package hk.eduhk.rich.database;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Blob;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.MimeMap;
import hk.eduhk.rich.BaseView;


@ManagedBean
@SessionScoped
@SuppressWarnings("serial")
public class DatabaseView extends BaseView
{
	private String genTable = null;	
	private List<String> tableNameList = null;
	private List<String> tableColumnList = null;
	private String selectedTable = null;	
	private String selectedTableColumn = null;	
	private String selectedTableOrder = null;
	private String columnValue = null;
	private Date columnDateValue = null;
	

	private String dataType = null;
	
	public DatabaseView()
	{
	}
	
	
	public List<String> getTableNameList()
	{
		if (tableNameList == null)
		{
			DatabaseDAO dao = DatabaseDAO.getInstance();
			tableNameList = dao.getTableNameList();
			tableNameList.add("CHRM_SAP_ACAD_PROF_OUTPUT_HDR@EBS");
			tableNameList.add("CHRM_SAP_RESEARCH_PROJECT_HDR@EBS");
			tableNameList.add("APPS.HKIEDKYRIE_PER_PEOPLE_EXTRA@EBS");
		}
		return tableNameList;
	}

	public List<String> getTableColumnList()
	{
		if (tableColumnList == null)
		{
			DatabaseDAO dao = DatabaseDAO.getInstance();
			tableColumnList = dao.getTableColumnList(selectedTable);
		}
		
		return tableColumnList;
	}
	
	public String getSelectedTable()
	{
		return selectedTable;
	}
	
	public void setSelectedTable(String selectedTable)
	{
		this.selectedTable = selectedTable;
		selectedTableColumn = null;	
		selectedTableOrder = null;
		columnValue = null;
		columnDateValue = null;
		dataType = null;
		tableColumnList = null;
	}
	
	
	public String getSelectedTableColumn()
	{
		return selectedTableColumn;
	}


	
	public void setSelectedTableColumn(String selectedTableColumn)
	{
		this.selectedTableColumn = selectedTableColumn;
		DatabaseDAO dao = DatabaseDAO.getInstance();
		dataType = dao.getTableColumnType(selectedTable, selectedTableColumn);
		columnValue = null;
		columnDateValue = null;
	}

	public String getSelectedTableOrder()
	{
		return selectedTableOrder;
	}


	
	public void setSelectedTableOrder(String selectedTableOrder)
	{
		this.selectedTableOrder = selectedTableOrder;
	}
	
	public String getColumnValue()
	{
		return columnValue;
	}


	
	public void setColumnValue(String columnValue)
	{
		this.columnValue = columnValue;
	}


	public Date getColumnDateValue()
	{
		return columnDateValue;
	}


	
	public void setColumnDateValue(Date columnDateValue)
	{
		this.columnDateValue = columnDateValue;
		if (columnDateValue != null) {
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");  
			columnValue = dateFormat.format(columnDateValue); 
		}
	}

	
	public String getDataType()
	{
		return dataType;
	}


	
	public void setDataType(String dataType)
	{
		this.dataType = dataType;
	}
		
	public String getGenTable()
	{
		if (selectedTable != null) {
			genTable = "";
			try
			{
				ResultSet rs = null;
				DatabaseDAO dao = DatabaseDAO.getInstance();
				rs = dao.getTableData(selectedTable, selectedTableColumn, columnValue, selectedTableOrder);
				XSSFWorkbook workbook = new XSSFWorkbook();
	            XSSFSheet sheet2 = workbook.createSheet("data");
	            
	            writeHeaderLine(rs, sheet2);
	            
	            writeDataLines(rs, workbook, sheet2);
	            ByteArrayOutputStream baos = new ByteArrayOutputStream();
	            workbook.write(baos);
	            workbook.close();
				
				XSSFSheet sheet = workbook.getSheet("data");
				
	            int rowCount = sheet.getLastRowNum() - sheet.getFirstRowNum();
	            //System.out.println("rowCount:"+rowCount);
	            genTable = "<table style='width: 100%; background: #000; color: #fff;'>";
	            for(int i=0; i<= rowCount; i++) {
	            	genTable += "<tr>";
	                int cellCount = sheet.getRow(i).getLastCellNum();
	                int cellWidth = 100/cellCount;
	                for(int j=0; j<cellCount; j++) {
	                	genTable += (i == 0)?"<th style='width: "+cellWidth+"%; border: 1px solid #fff; border-collapse: collapse;'>":"<td style='border: 1px solid #fff; border-collapse: collapse; word-break: break-all;'>";
	                    XSSFCell c1 = sheet.getRow(i).getCell(j);
	                    genTable += c1;
	                    genTable += (i == 0)?"</th>":"</td>";
	                }
	            	genTable += "</tr>";
	            }
	            genTable += "</table>";
	        }catch (SQLException e) {
	            e.printStackTrace();
	        }catch (IOException e) {
	            e.printStackTrace();
	        }
		}
		return genTable;
	}


	
	public void setGenTable(String genTable)
	{
		this.genTable = genTable;
	}


	public void exportData()
	{	
		try
		{
			ResultSet rs = null;
			DatabaseDAO dao = DatabaseDAO.getInstance();
			rs = dao.getTableData(selectedTable, selectedTableColumn, columnValue, selectedTableOrder);
			XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("data");
            
            writeHeaderLine(rs, sheet);
            
            writeDataLines(rs, workbook, sheet);
            
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            workbook.close();
			byte[] wbBytes = baos.toByteArray();
			// Set the response header
			String outputFileExt = "xlsx";
			String outputFileName = selectedTable+"_data.xlsx";
			FacesContext fCtx = FacesContext.getCurrentInstance();
			  ExternalContext eCtx = fCtx.getExternalContext();
			  eCtx.responseReset();
			  //eCtx.addResponseHeader("Cache-control", "no-cache");
			  //eCtx.addResponseHeader("Pragma", "no-cache");
			  
			  eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			  eCtx.setResponseHeader("Expires", "-1");
			  eCtx.setResponseHeader("Pragma", "private");
			  
			  eCtx.setResponseContentType(MimeMap.getInstance().get(outputFileExt));
			  eCtx.setResponseHeader("Content-Disposition", "attachment; filename="+outputFileName);

			  // Send the bytes to response OutputStream
			  OutputStream os = eCtx.getResponseOutputStream();
			  os.write(wbBytes);
			  fCtx.responseComplete();

        }catch (SQLException e) {
            e.printStackTrace();
        }catch (IOException e) {
            e.printStackTrace();
        }
		
		
	}	
	private void writeHeaderLine(ResultSet rs, XSSFSheet sheet) throws SQLException {
		ResultSetMetaData metadata = rs.getMetaData();
		int columnCount = metadata.getColumnCount();
		
        Row headerRow = sheet.createRow(0);
 
        
        
        for (int i = 1; i <= columnCount; i++) {
        	Cell headerCell = headerRow.createCell(i - 1);
        	headerCell.setCellValue(metadata.getColumnName(i));
        }
    }
	
	private void writeDataLines(ResultSet rs, XSSFWorkbook workbook, XSSFSheet sheet) throws SQLException {
		ResultSetMetaData metadata = rs.getMetaData();
		int columnCount = metadata.getColumnCount();
		int rowCount = 1;
 
        while (rs.next()) {
        	ResultSetMetaData rsmd = rs.getMetaData();
        	
        	Row row = sheet.createRow(rowCount++);
        	for (int i = 1; i <= columnCount; i++) {
        		int type = rsmd.getColumnType(i);
        		//System.out.println("type:"+type);
        		Cell cell = row.createCell(i - 1);
        		String s = "";
        		if (type == 2004) {
        			Blob blob = rs.getBlob(i);
        			if (blob != null) {
	        			byte[] bdata = blob.getBytes(1, (int) blob.length());
	        			s = new String(bdata);
        			}
        		}else {
        			s = rs.getString(i);
        		}
        		if (!Strings.isNullOrEmpty(s)) {
        			s = s.substring(0, Math.min(s.length(), 1000));
        		}
        		cell.setCellValue(s);
            }
        }
    }
}
