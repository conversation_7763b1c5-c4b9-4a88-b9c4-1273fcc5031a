package hk.eduhk.rich.database;

import java.io.UnsupportedEncodingException;
import java.sql.*;
import java.util.*;
import java.util.logging.Level;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.sql.rowset.serial.SerialBlob;
import javax.transaction.UserTransaction;

import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;


@SuppressWarnings("serial")
public class DatabaseDAO extends BaseDAO
{
	
	private static DatabaseDAO instance = null;

	
	public static synchronized DatabaseDAO getInstance()
	{
		if (instance == null) instance = new DatabaseDAO();
		return instance;
	}
	
	
	public List<String> getTableNameList()
	{
		List<String> objList = new ArrayList<String>();

		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		String query = "SELECT table_name FROM all_tables " + 
							"WHERE OWNER = 'RICH' ORDER BY table_name ";
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					objList.add(rs.getString("table_name"));
				}
			}
			catch (SQLException se)
			{
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}				
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<String> getTableColumnList(String tableName)
	{
		List<String> objList = new ArrayList<String>();
		String[] typeList = {"DATE", "VARCHAR2", "NUMBER", "CHAR"};
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		String query = "SELECT column_name, data_type FROM USER_TAB_COLUMNS " + 
							"WHERE table_name = ? ORDER BY column_id ";
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setString(1, tableName);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					if (Arrays.stream(typeList).anyMatch(rs.getString("data_type")::equals)) {
						objList.add(rs.getString("column_name"));
					}
				}
			}
			catch (SQLException se)
			{
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}				
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
		
	@SuppressWarnings("static-access")
	public String getTableColumnType(String tableName, String columnName)
	{
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		String value = "";
		String query = "SELECT data_type FROM USER_TAB_COLUMNS " + 
							"WHERE table_name = ? AND column_name = ? ";
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setString(1, tableName);
				pStmt.setString(2, columnName);
				ResultSet rs = pStmt.executeQuery();
				if (rs.next()) {
					value = rs.getString("data_type");
				}
			}
			catch (SQLException se)
			{
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}				
		}
		return value;
	}
	
	@SuppressWarnings("static-access")
	public ResultSet getTableData(String tableName, String tableColumn, String columnValue, String tableOrder) 
	{		
		SysParamDAO paramDAO = SysParamDAO.getInstance();
		String strMaxSize = paramDAO.getSysParamValueByCode(SysParam.PARAM_DOWNLOAD_MAX_RECORD);
		String dataType = (tableColumn!=null)?getTableColumnType(tableName, tableColumn):null;System.out.println("tableName:"+tableName+", tableColumn:"+tableColumn);
		PersistenceManager pm = PersistenceManager.getInstance(); 
		ResultSet rs = null;
		Connection conn = null;
		PreparedStatement pStmt = null;
		String condition = "";
		if (dataType != null && tableColumn != null) {
			if (!dataType.equals("DATE")) {
				condition = " WHERE " + tableColumn + " = ? ";
			}
			if (dataType.equals("DATE")) {
				condition = " WHERE TRUNC(" + tableColumn + ") = ? ";
			}
		}
		String orderBy = (tableOrder != null)?" ORDER BY " + tableOrder :"";
		String query = " SELECT * FROM " + tableName  + condition + orderBy;
		//System.out.println("query:"+query);
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				if (tableColumn != null) {
					if (dataType.equals("NUMBER")) {
						pStmt.setInt(1, Integer.parseInt(columnValue));
					}
					else if(dataType.equals("DATE")) {
						java.sql.Date sqlDate = java.sql.Date.valueOf(columnValue);
						pStmt.setDate(1, sqlDate);
					}
					else {
						pStmt.setString(1, columnValue);
					}
				}
				pStmt.setMaxRows(Integer.parseInt(strMaxSize));
				rs = pStmt.executeQuery();
			}
			catch (SQLException e) {
	            e.printStackTrace();
	        }finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return rs;
	}
	
	@SuppressWarnings("static-access")
	public int deleteData(String tableName) throws Exception
	{
		int row = 0;
		if (!Strings.isNullOrEmpty(tableName))
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			String query = "DELETE FROM " + tableName;
			//System.out.println("deleteData query:"+query);
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				row = pStmt.executeUpdate();
				//System.out.println("deleteData row:"+row);
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return row;
	}
	
	@SuppressWarnings("static-access")
	public int insertData(String fromTableName, String toTableName) throws Exception
	{
		int row = 0;
		if (!Strings.isNullOrEmpty(fromTableName) && !Strings.isNullOrEmpty(toTableName))
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			String query = "INSERT INTO " + toTableName +  " ( select * from " + fromTableName + ")";
			//System.out.println("insertData query:"+query);
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				row = pStmt.executeUpdate();
				//System.out.println("insertData row:"+row);
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return row;
	}
	
	@SuppressWarnings("static-access")
	public int updateData(String tableName, String setColName, String setValue, String whereColName, String whereValue) throws Exception
	{
		int row = 0;
		if (!Strings.isNullOrEmpty(tableName) && !Strings.isNullOrEmpty(setColName) && !Strings.isNullOrEmpty(whereColName))
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			String query = "UPDATE " + tableName + " SET " + setColName + " = ?, USERSTAMP = USER, TIMESTAMP = SYSDATE WHERE " + whereColName + " = ?";
			//System.out.println("query:"+query);
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setString(1, setValue);
				pStmt.setString(2, whereValue);
				row = pStmt.executeUpdate();
				//System.out.println("insertData row:"+row);
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return row;
	}
	
	@SuppressWarnings("static-access")
	public ResultSet getRiFullList() 
	{		
		PersistenceManager pm = PersistenceManager.getInstance(); 
		ResultSet rs = null;
		Connection conn = null;
		PreparedStatement pStmt = null;
		
		String query = " SELECT H.OUTPUT_NO, S.AUTHORSHIP_LIST,  "
				+ "    TITLE_JOUR_BOOK,  "
				+ "    OUTPUT_TITLE_CONTINUE, "
				+ "    (SELECT L1.DESCRIPTION "
				+ "        FROM RICH.RH_L_OUTPUT_TYPE_V L1, RICH.RH_L_OUTPUT_TYPE_V L2 "
				+ "       WHERE     L1.lookup_level = 1 "
				+ "             AND L1.lookup_code = L2.parent_lookup_code "
				+ "             AND H.sap_output_type = L2.lookup_code) "
				+ "        sap_output_cat, "
				+ "  (SELECT L2.description "
				+ "                    FROM RICH.RH_L_OUTPUT_TYPE_V L2 "
				+ "                   WHERE     L2.lookup_code = H.sap_output_type "
				+ "                         AND L2.lookup_level = 2) "
				+ "                    sap_output_type_desc, "
				+ "(SELECT L7.description "
				+ "                    FROM RICH.RH_L_RESEARCH_TYPE_V L7 "
				+ "                   WHERE     L7.lookup_code = H.sap_refered_journal "
				+ "                         AND L7.lookup_level = 1) "
				+ "                    sap_refered_journal_desc, "
				+ "   TITLE_PAPER_ART, "
				+ "   NAME_OTHER_EDITORS, "
				+ "   VOL_ISSUE, "
				+ "   PAGE_NUM, "
				+ "   PAGE_NUM_FROM, "
				+ "   PAGE_NUM_TO, "
				+ "   CITY, "
				+ "   FROM_MONTH, "
				+ "   FROM_YEAR, "
				+ "   TO_MONTH, "
				+ "   TO_YEAR, "
				+ "   PUBLISHER, "
				+ "   OTHER_DETAILS, "
				+ "   OTHER_DETAILS_CONTINUE, "
				+ "   LANGUAGE, "
				+ "   NAME_OTHER_POS , "
				+ "   IED_WORK_IND, "
				+ "   APA_CITATION, "
				+ "   ISSN, "
				+ "   ARTICLE_NUM, "
				+ "   DOI, "
				+ "   EISSN, "
				+ "   FULLTEXT_URL, "
				+ "   ISBN, "
				+ "   H.CREATION_TIME, "
				+ "   H.TIMESTAMP                                          "
				+ "FROM RICH.RH_P_ACAD_PROF_OUTPUT_HDR H "
				+ "    LEFT JOIN "
				+ "    (SELECT OUTPUT_NO, LISTAGG(AUTHORSHIP, ';') WITHIN GROUP (ORDER BY LINE_NO) AUTHORSHIP_LIST "
				+ "        FROM "
				+ "        ( "
				+ "            SELECT OUTPUT_NO, AUTHORSHIP_NAME || '[' || DEPT_CODE || ']'|| ' [' || L.DESCRIPTION || ']' AUTHORSHIP, LINE_NO, AUTHORSHIP_STAFF_NO  "
				+ "                FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL D "
				+ "                    LEFT JOIN RICH.RH_L_AUTHORSHIP_V L ON D.AUTHORSHIP_TYPE = L.LOOKUP_CODE "
				+ "                    LEFT JOIN (SELECT * FROM RICH.RH_P_STAFF_IDENTITY) S ON D.AUTHORSHIP_STAFF_NO = S.STAFF_NUMBER "
				+ "			WHERE D.DATA_LEVEL = 'P' "
				+ "        ) "
				+ "		where line_no < 139 "
				+ "        GROUP BY OUTPUT_NO) S ON H.OUTPUT_NO = S.OUTPUT_NO     "
				+ "WHERE DATA_LEVEL = 'P'                 "
				+ "ORDER BY H.OUTPUT_NO";
		System.out.println("query:"+query);
		// Execute the query

		try
		{
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			rs = pStmt.executeQuery();
		}
		catch (SQLException e) {
            e.printStackTrace();
        }finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return rs;
	}

}
	