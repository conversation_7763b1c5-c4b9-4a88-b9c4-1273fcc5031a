package hk.eduhk.rich.validator;

import java.io.Serializable;
import java.text.MessageFormat;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;

import org.apache.commons.validator.routines.CodeValidator;
import org.apache.commons.validator.routines.checkdigit.CheckDigitException;
import org.apache.commons.validator.routines.checkdigit.EAN13CheckDigit;
import org.apache.commons.validator.routines.checkdigit.ISBN10CheckDigit;
import org.apache.commons.validator.routines.checkdigit.ISSNCheckDigit;

import hk.eduhk.rich.BaseFacesValidator;


@FacesValidator("hk.eduhk.rich.validator.TextValidator")
public class TextValidator extends BaseFacesValidator
{
	public static final Integer MAX_LEN_150		= 150;
	public static final Integer MAX_LEN_200		= 200;
	public static final Integer MAX_LEN_500		= 500;
	public static final Integer MAX_LEN_1000		= 1000;

	public TextValidator()
	{
		super();
	}

	public void validateTitle1(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_200, component);
	}
	public void validateTitle2(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_200, component);
	}
	public void validateTitle3(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_200, component);
	}
	public void validateTitle4(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_200, component);
	}
	
	public void validateTitle_jour_book(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	public void validateOutput_title_continue(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	public void validateTitle_paper_art(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	public void validateName_other_editors(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	public void validateName_other_pos(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	
	public void validatePublisher(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	public void validateOther_details(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	public void validateOther_details_continue(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_500, component);
	}
	
	public void validateOther_da_dtl(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_150, component);
	}
	public void validatePatent_long_desc(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateMaxLength(obj, MAX_LEN_1000, component);
	}
}