action.activate=Activate
action.add.new.x=Add new {0}
action.back=Back
action.cancel=Cancel
action.clear=Clear
action.clone=Clone
action.create=Create
action.deactivate=Deactivate
action.delete=Delete
action.delete.x=Delete {0}
action.download=Download
action.duplicate=Duplicate
action.edit=Edit
action.edit.x=Edit {0}
action.execute=Execute
action.export=Export
action.generate=Generate
action.grant.access=Grant Access
action.import=Import
action.interrupt=Interrupt
action.login=Login
action.logout=Logout
action.manage=Manage
action.match=Match
action.new=New
action.new.x=New {0}
action.next=Next
action.no=No
action.ok=OK
action.prev=Prev.
action.print=Print
action.publish=Publish
action.assign.random.number=Assign Random Number
action.reload=Reload
action.request=Request
action.reset=Reset
action.reset.x=Reset {0}
action.save=Save
action.saveAndPublish=Save and Submit
action.saved=Saved
action.select=Select
action.submit=Submit
action.update=Update
action.update.x=Update {0}
action.upload=Upload
action.validate=Validate
action.view=View
action.yes=Yes

batch=Batch

category=Category

char.space=Space

date=Date

email=Email
email.addr=Email address

file=File
file.data=Data File

form=Form
form.remaining.characters=Characters remaining:

form.label.mc.view.agree=Agree
form.label.mc.view.disagree=Disagree
form.label.mc.view.sAgree=Strongly Agree
form.label.mc.view.sDisagree=Strongly Disagree
form.label.mc.view.na=N/A

form.ri.status.lv.m=Modified
form.ri.status.lv.p=Submitted
form.ri.status.lv.n=Modified
form.ri.status.lv.d=Submitted
form.ri.status.lv.c=CDCF Snapshot

lang=Language
lang.en=English
lang.zh=ä¸­æ
lang.zh_CN=ç®ä½ä¸­æ
lang.zh_HK=ç¹é«ä¸­æ

map=Map
map.marker=Marker

msg.err.access.denied=Access denied
msg.err.access.denied.x=Access denied. You are not allowed to access {0}
msg.err.account.lockout.x=Your account has been locked out due to too many login failures. Please try again after {0} minute(s).
msg.err.action.forbidden=Action forbidden.
msg.err.circular.reference=Circular reference detected
msg.err.data.collect.x=Failed to collect {0}.
msg.err.data.publish.x=Failed to submit {0}.
msg.err.data.save.x=Failed to save {0}.
msg.err.date.x.between.start.end={0} must be between start date and end date
msg.err.date.start.before.current=Start date cannot be before current date
msg.err.date.start.exceed.end=Start date cannot exceed end date
msg.err.download=Download failure
msg.err.duplicate.x= {0} cannot be duplicated
msg.err.exist.delete.x={0} exists. Please delete it if you would like to reuse the {0}
msg.err.exist.x={0} exists
msg.err.form.date.gt.end.date=This form is ended.
msg.err.form.date.gt.end.date.passed=The submission deadline has passed already.
msg.err.form.date.lt.start.date=This form is not yet started.
msg.err.form.delete.last=You are not allowed to delete the last copy.
msg.err.form.inactive=This form is inactive now.
msg.err.form.submitted=This form is submitted
msg.err.form.submitted.x=This form is submitted on {0}
msg.err.invalid.data=Invalid data.
msg.err.invalid.data.x=Invalid data in {0}
msg.err.invalid.data.format=Invalid data format.
msg.err.invalid.data.format.json=Invalid JSON
msg.err.invalid.data.format.x=Invalid data format "{0}"
msg.err.invalid.date=Invalid date
msg.err.invalid.date.time.x=Invalid date time format. Example: {0}
msg.err.invalid.parameter={0} is/are not valid email parameters.
msg.err.invalid.expiration.time=Invalid expiration time. The expiration time must be between 1 minute to {0}.
msg.err.invalid.file=Invalid file.
msg.err.invalid.file.column.count=Invalid column count.
msg.err.invalid.file.column.duplicate.x={0} column(s) has/have duplication
msg.err.invalid.file.column.missing.x={0} column(s) is/are missing
msg.err.invalid.file.ext=There must be a valid file extension ({0}) for the file to be uploaded.
msg.err.invalid.file.size=The file size exceeds the limit ({0}).
msg.err.invalid.file.upload=Upload failure. Please try again.
msg.err.invalid.file.upload.incomplete=Upload failure. Please select a file.
msg.err.invalid.time=Invalid time.
msg.err.invalid.time.format=Invalid time format.
msg.err.invalid.x=Invalid {0}.
msg.err.length.between=Length must be between {0} and {1}
msg.err.length.between.x=Length of {2} must be between {0} and {1}
msg.err.length.eq=Length must be {0}
msg.err.length.eq.x=Length of {1} must be {0}
msg.err.length.gt=Length exceeds {0}
msg.err.length.gt.x=Length of {1} exceeds {0}
msg.err.length.lt=Length is less than {0} 
msg.err.length.lt.x=Length of {1} is less than {0} 
msg.err.login.access.token.failure=Access authorization expired. Please login again.
msg.err.login.failure=Login failure. Incorrect user name or password
msg.err.login.failure.disabled=Login failure. Your account has been disabled.
msg.err.mandatory.x={0} is mandatory
msg.err.not.allowed={0} is not allowed
msg.err.not.available.form.list=No available form
msg.err.not.exist={0} does not exist
msg.err.not.role.exist={0} cannot be impersonated
msg.err.not.selected.x=No {0} selected
msg.err.number.of.error.x=You have {0} error(s)
msg.err.optimistic.lock=Concurrent update detected. Please refresh the page and try again.
msg.err.require.either=Either {0} or {1} is required
msg.err.require.field=This is a required field
msg.err.require.field.assessment.item=This assessment item is mandatory
msg.err.require.field.class=Class is mandatory
msg.err.require.field.date=Date is mandatory
msg.err.require.field.mandatory=This field is mandatory
msg.err.require.field.question=Question cannot be empty.  Please either turn off the language button or add question of the specific language.
msg.err.require.field.time=Time is mandatory
msg.err.require.field.topic=Topic is mandatory
msg.err.require.value=Value is required
msg.err.require.x={0} is required
msg.err.respondent.submit.exist=There is already respondent submission in the survey.
msg.err.select.atLeastOne=At least one must be selected.
msg.err.survey.started=The survey has been started.
msg.err.time.start.eq.end=Start time and End time cannot be the same
msg.err.time.start.exceed.end=Start time cannot exceed end time
msg.err.value.integer.between=Must be an integer between {0} and {1}
msg.err.value.integer.ge=Must be an integer greater than or equal to {0}
msg.err.value.integer.gt=Must be an integer greater than {0}
msg.err.value.integer.le=Must be an integer less than or equal to {0}
msg.err.value.integer.lt=Must be an integer less than {0}
msg.err.value.integer.must=Must be an integer
msg.err.value.number.between=Must be a number between {0} and {1}
msg.err.value.number.ge=Must be a number greater than or equal to {0}
msg.err.value.number.gt=Must be a number greater than {0}
msg.err.value.number.le=Must be a number less than or equal to {0}
msg.err.value.number.lt=Must be a number less than {0}
msg.err.value.number.must=Must be a number
msg.err.unexpected=Unexpected error encountered.
msg.err.update.ban.tchr=Please update teacher information in Banner.
msg.err.validation.data=Data validation error
msg.err.version.outdated=The version you are using is outdated. Please update the app to the latest version. 
msg.err.word.exceed.x={0} should not exceed {1} words.

msg.confirm.action.x=Are you sure to {0} {1}?
msg.confirm.delete.x=Are you sure to delete {0}?
msg.confirm.disable.x=Are you sure to disable {0}?
msg.confirm.generate.x=Are you sure to generate {0}?
msg.confirm.submit.no.change=Are you sure to submit Review Feedback? No change can be made after submission.
msg.confirm.delete.question.sheet=Are you sure to remove this additional question sheet? It will be removed from all surveys that are not started.

msg.info.form.submission.deadline.x=Submission Deadline: {0}

msg.success.action.x={0} is successfully {1}.
msg.success.clear.x={0} is successfully cleared.
msg.success.create.x={0} is successfully created.
msg.success.delete.x={0} is successfully deleted.
msg.success.duplicate.x={0} is successfully duplicated.
msg.success.import.x={0} is successfully imported.
msg.success.publish.x={0} is successfully submitted.
msg.success.save.x={0} is successfully saved.
msg.success.save.generate.x={0} is successfully saved and generated.
msg.success.save.publish.x={0} is successfully saved and submitted.
msg.success.send.reset.pw=An email has been sent to your email address. Please follow the instructions in the email to reset your account password.
msg.success.submit.x={0} is successfully submitted.
msg.success.update.x={0} is successfully updated.
msg.success.upload.x={0} is successfully uploaded.

school=School

template=Template

time=Time
time.duration.days.x={0} Days
time.duration.day.x={0} Day
time.duration.hours.x={0} Hours
time.duration.hour.x={0} Hour
time.duration.minutes.x={0} Minutes
time.duration.minute.x={0} Minute

user=User

val=Value
val.appropriate=Appropriate
val.excellent=Excellent
val.fair=Fair
val.good=Good
val.high=High
val.low=Low
val.moderate=Moderate
val.na=Not applicable
val.no=No
val.none=None
val.password=Password
val.poor=Poor
val.tooLong=Too Long
val.tooShort=Too Short
val.veryGood=Very Good
val.yes=Yes

kt.form.cpd=KT_CPD
kt.form.prof.conf=KT_PROF_CONF
kt.form.sem=KT_SEMINAR
kt.form.cnt.proj=KT_CNT_PROJ
kt.form.inn=KT_INNOVATION
kt.form.cons=KT_CONSULT
kt.form.prof.engmt=KT_PROF_ENGMT
kt.form.ip=KT_IP
kt.form.soc.engmt=KT_SOC_ENGMT
kt.form.staff.engmt=KT_STAFF_ENGMT
kt.form.ea=KT_EA
kt.form.startup=KT_STARTUP
kt.form.inv.award=KT_INV_AWARD

kt.form.label.act.code=Activity/ Project Code
kt.form.label.kt.dept=Department/Centre/Unit
kt.form.label.kt.fac=Faculty/Centre/Unit
kt.form.label.kt.pi=Principal Investigator/ Person in charge

kt.form.label.budget.holder=Budget Holder
kt.form.label.budget.approval=Budget Approval Sought for the Activity?
kt.form.label.budget.total=Total Approved Budget

kt.form.label.income=Income
kt.form.label.income.fo=Income (FO/ Budget Holder)
kt.form.label.income.fo.remark=Income (Remarks from FO/ Budget Holder)
kt.form.label.income.rdo=Income (RDO)

kt.form.label.expend=Expenditure (Direct Cost)
kt.form.label.expend.fo=Expenditure (Direct Cost) (FO/ Budget Holder)
kt.form.label.expend.fo.remark=Expenditure (Direct Cost) (Remarks from FO/ Budget Holder)
kt.form.label.expend.rdo=Expenditure (RDO)

kt.form.label.event.expend=Total Expenditure from the Event (HK$) 
kt.form.label.event.expend.fo=Total Expenditure from the Event (HK$) (FO) 
kt.form.label.event.expend.fo.remark=Total Expenditure from the Event (HK$) (Remarks from FO) 
kt.form.label.event.expend.rdo=Total Expenditure from the Event (HK$) (RDO) 

kt.form.label.eduhk.org=EdUHK Organizer?
kt.form.label.region=Local/ National/ International
kt.form.label.free=Free/ Chargeable (F/C)
kt.form.label.date.start=Start Date
kt.form.label.date.end=End Date
kt.form.label.total.day.act=Total Number of Activity Days
kt.form.label.total.day.rpt.yr.act=Total Number of Activity Days in the Reporting Year

kt.form.label.num.key=Number of Key Partners
kt.form.label.num.oth.stakeholder=Number of Other Stakeholders Benefited (e.g. professionals, workers)
kt.form.label.num.oth=Number of Other Participants (e.g. Parents)
kt.form.label.num.stu=Number of Student Participants
kt.form.label.num.sch=Number of Schools Benefited
kt.form.label.num.spe=Number of Speakers
kt.form.label.num.sub.ses=Number of Sub-sessions
kt.form.label.num.tea=Number of Teacher Participants
kt.form.label.num.pri=Number of Principal Participants

function.title.acadStaff.countRI=Count My Research Information
function.title.dept.countRI=Count Research Information

rae.selected.type.ns=Not Selected
rae.selected.type.s=Selected
rae.selected.type.dw1=Double-weighted 1
rae.selected.type.dwb1=Reserve for Double-weighted 1
rae.selected.type.dw2=Double-weighted 2
rae.selected.type.dwb2=Reserve for Double-weighted 2