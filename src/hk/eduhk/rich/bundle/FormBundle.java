package hk.eduhk.rich.bundle;

import java.util.Locale;
import java.util.ResourceBundle;

import javax.faces.context.FacesContext;


public class FormBundle extends UTF8ResourceBundle 
{
	
	private static final String BUNDLE_NAME = "Form"; 
	

	public FormBundle()
	{
		setParent(ResourceBundle.getBundle(PACKAGE_NAME + "." + BUNDLE_NAME, FacesContext.getCurrentInstance().getViewRoot().getLocale(), UTF8_CONTROL));
	}

	
	public static ResourceBundle getResourceBundle(Locale locale)
	{
		return ResourceBundle.getBundle(PACKAGE_NAME + "." + BUNDLE_NAME, locale, UTF8_CONTROL);
	}


}
