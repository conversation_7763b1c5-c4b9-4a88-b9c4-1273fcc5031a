package hk.eduhk.rich;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import javax.persistence.*;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.time.DateUtils;

/**
 * BasePersistenceObject is the base class for all Entity classes
 * It defines the control fields: creation date and timestamp
 *
 */
@MappedSuperclass
public abstract class BasePersistenceObject implements Serializable, Cloneable
{
	
	private static final long serialVersionUID = 1L;

	@Column(name = "creation_time")
	private Date creationDate;

	@Column(name = "timestamp")
	@Version
	private Date timestamp;
	

	public Date getCreationDate()
	{
		return creationDate;
	}


	public void setCreationDate(Date creationDate)
	{
		this.creationDate = (creationDate != null) ? DateUtils.truncate(creationDate, Calendar.SECOND) : null;
	}


	public Date getTimestamp()
	{
		return timestamp;
	}


	/**
	 * Set the timestamp of this object.
	 * The timestamp of this class is truncated to remove millisecond field.
	 * As TIMESTAMP column in some databases may not accept milliseconds, 
	 * this will cause OptimisticLockException in some situations. 
	 * @param timestamp
	 */
	public void setTimestamp(Date timestamp)
	{
		this.timestamp = (timestamp != null) ? DateUtils.truncate(timestamp, Calendar.SECOND) : null;
	}
	
	
	/**
	 * Return whether this entity is a new entity that does not exist in database.
	 * If the creation date is null, the entity is considered as new.
	 * 
	 * @return
	 */
	public boolean isNewEntity()
	{
		return (getCreationDate() == null);
	}
	

	@PrePersist
	protected void prePersist()
	{
		// Explicitly setCreationDate and setTimestamp to truncate the Date
		Date d = new Date();
		this.setTimestamp(d);
		this.setCreationDate(d);
	}


	@PostUpdate
	protected void postUpdate()
	{
		// Explicit truncation of Timestamp
		this.setTimestamp(this.getTimestamp());
	}
	

	/**
	 * Clone the control fields from the provided object.
	 * 
	 */
	public void cloneControlFields(BasePersistenceObject sourceObj)
	{
		if (sourceObj != null)
		{
			this.setCreationDate(sourceObj.getCreationDate());
			this.setTimestamp(sourceObj.getTimestamp());
		}
	}
	
	
	public Object clone()
	{
		Object cloneObj = null;
		
		try
		{
			cloneObj = BeanUtils.cloneBean(this);
		}
		catch (Exception e)
		{
			// Should not even be happened, 
			// as BasePersistenceObject implements Cloneable interface
		}
		
		return cloneObj;
	}
	
}