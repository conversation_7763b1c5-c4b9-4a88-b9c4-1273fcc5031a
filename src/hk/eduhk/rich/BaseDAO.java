package hk.eduhk.rich;

import java.io.Serializable;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceException;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.rich.util.PersistenceManager;


/**
 * BaseDAO defines a set of methods which provide basic persistence operations to database.
 * Those operations are based on Java Persistence API (JPA). 
 * Concrete DAO classes can acquire the operations by extending the implementation of this interface. 
 *
 */
@SuppressWarnings("serial")
public abstract class BaseDAO implements Serializable
{
	@PersistenceContext(unitName=PersistenceManager.EM_NAME)
	protected EntityManager em;	
	
	private static final long serialVersionUID = 1L;

	// Database limit, the query parameter List size cannot be too large.
	// Max possible value in Oracle 11g is 1000 
	public static final int MAX_PARAM_LIST_SIZE = 1000;
	
	public static final int MAX_BATCH_SIZE = 1000;
	
	@Inject 
    protected transient static PersistenceManager pm;
    
    // Logger object for the extended class
    protected Logger logger = null;
    
    
    protected BaseDAO()
    {
    	pm = PersistenceManager.getInstance();
    	logger = Logger.getLogger(this.getClass().getName());
    }

    
    /**
     * Get the EntityManager of Portal
     * @return
     */
    protected static EntityManager getEntityManager()
    {
    	return pm.getEntityManager(PersistenceManager.DATASOURCE_NAME);
    }

    
    /**
     * Get the EntityManager of Data warehouse
     * @return
     */
    protected EntityManager getDWEntityManager()
    {
    	return pm.getEntityManager(PersistenceManager.DATASOURCE_DW);
    }
    
    protected <V extends Object> List<V> getSafeList(List<V> list)
    {
    	return (list != null) ? list : Collections.emptyList();
    }    
    
    /**
     * Sort the entity list according to the key list ordering. 
     * 
     * @param entityList
     * @param keyCol
     * @param f Function of extracting the key from the entity
     */
    protected <K, V> void sortByKey(List<V> entityList, Collection<K> keyCol, Function<V, K> f)
    {
    	if (f == null) throw new NullPointerException("f cannot be null");
    	
    	if (CollectionUtils.isNotEmpty(entityList) && CollectionUtils.isNotEmpty(keyCol))
    	{
    		// Log the position of keys to idxMap
        	Integer idx = 0;
        	Map<K, Integer> idxMap = new HashMap<K, Integer>();
    		for (K key : keyCol) idxMap.put(key, idx++);
    		
    		// Number of padding null in the entity list
    		int nullNum = 0;
    		
    		// Order the entities according to the keys
    		for (int n=0;n<entityList.size();n++)
    		{
    			V obj = entityList.get(n);
    			int iter = 0;
    			
    			do
    			{
	    			// Lookup the key position in the map
	    			K key = (obj != null) ? f.apply(obj) : null;
	    			idx = (key != null) ? idxMap.get(key) : null;
	    			
	    			// The entity's key is not found in the map
	    			if (idx == null) break;
	    			
	    			// If the object is not in the right position
	    			// Swap it to the right position
	    			if (n != idx)
	    			{
	    				// The index is greater than the entity list
	    				// grow the entity list by padding null at the end
	    				if (idx >= entityList.size())
	    				{
	    					int nullReq = idx - entityList.size() + 1;
	    					for (int p=0;p<nullReq;p++) entityList.add(null);
	    					nullNum += nullReq;
	    				}
	    				
	    				V swapObj = entityList.get(idx);
	    				entityList.set(idx, obj);
	    				obj = swapObj;
	    			}
	    			
	    			// Otherwise, proceed to the next entity
	    			else break;
	    			
	    			iter++;
    			}
    			while (iter < (entityList.size()-n) && obj != null);
    			
    			// This line is essential
				entityList.set(n, obj);
    		}
    		
    		// Remove null from entityList
    		// if padding null is added in the former procedure
    		if (nullNum > 0)
    		{
    			idx = entityList.size()-1;
    			
    			do
    			{
    				V obj = entityList.get(idx);
    				if (obj == null)
    				{
    					entityList.remove(idx);
    					nullNum--;
    				}
    				
    				idx--;
    			}
    			while (idx >= 0 && nullNum > 0);
    		}
    	}
    }
    
    protected <T> T getEntity(Class<T> entityClass, Object keyId)
	{
		return (keyId != null) ? em.find(entityClass, keyId) : null;
	}
	
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public <T> T persistEntity(T obj)
	{
		if (obj != null) em.persist(obj);
		return obj;
	}

	
	public <T> T persistEntityWithTx(T obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				em.persist(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				
				if (e.getCause() != null && e.getCause() instanceof PersistenceException)
				{
					throw (PersistenceException) e.getCause();
				}
				else
				{
					throw new RuntimeException(e);
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public <T> T updateEntity(T obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public <T> T deleteEntity(Class<T> entityClass, Object key)
	{
		T obj = null;
		EntityManager em = null;
		UserTransaction utx = null;
		
		if (key != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				obj = em.find(entityClass, key);
				em.remove(obj);
				
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				
				if (e.getCause() != null && e.getCause() instanceof PersistenceException)
				{
					throw (PersistenceException) e.getCause();
				}
				else
				{
					throw new RuntimeException(e);
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}

	public void publishToSAP (int riNo, String publishToSAPSprocName) throws SQLException
	{
		Connection conn = null;
		CallableStatement cStmt = null;
		try{
			if (riNo > 0){
				conn = pm.getConnection();
				cStmt = conn.prepareCall("{CALL " + publishToSAPSprocName + "(?)}");
				cStmt.setInt(1, riNo);
				cStmt.execute();
			}
		}
		finally{
			PersistenceManager.close(cStmt);
			PersistenceManager.close(conn);
		}
	}
	
	public boolean hasSAP(String riTable, String riCol, int riNo) 
	{
		boolean result = false;
		List<String> objList = new ArrayList<String>();
		Connection conn = null;
		PreparedStatement stmt = null;
		try {
			String query = "SELECT * FROM "+riTable+"@EBS WHERE "+riCol+" = ? ";
			conn = pm.getConnection();
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, riNo);
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) objList.add(rs.getString(1));
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			PersistenceManager.close(stmt);
			PersistenceManager.close(conn);
		}
		if(!objList.isEmpty()) {
			result = true;
		}
		return result;
	}
	
	public String getSAPLog(String process_id) 
	{
		String result = "";
		Connection conn = null;
		PreparedStatement stmt = null;
		try {
			String query = "SELECT ERR_MESSAGE FROM RICH.RH_O_ERROR_LOG WHERE ERR_SEQ = (SELECT MAX(ERR_SEQ) FROM RICH.RH_O_ERROR_LOG WHERE PROCESS_ID = ?) "
							+ "	AND PROCESS_ID = ? ";
			conn = pm.getConnection();
			stmt = conn.prepareStatement(query);
			stmt.setString(1, process_id);
			stmt.setString(2, process_id);
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) result = rs.getString(1);
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			PersistenceManager.close(stmt);
			PersistenceManager.close(conn);
		}
		return result;
	}

	/*
	public static void main(String[] args)
	{
		List<SurvDistribution> distrList = new ArrayList<SurvDistribution>();
		
		
		List<Integer> distrIdList = Arrays.asList(new Integer[] {100, 172, 123, 221, 95, 77, 111});
		
		Function<SurvDistribution, Integer> f = SurvDistribution::getDistrId;
		
		for (Integer distrId : distrIdList)
		{
			SurvDistribution distr = new SurvDistribution();
			distr.setDistrId(distrId);
			distrList.add(distr);
		}
		
		distrList.remove(2);
		distrList.remove(1);

		
		Collections.shuffle(distrList);

		System.out.println("distrList before sort="+distrList.stream().map(d -> d.getDistrId()).collect(Collectors.toList()));
		
		BaseDAO.sortByKeyList(distrList, distrIdList, f);

		System.out.println("distrList after sort="+distrList.stream().map(d -> d.getDistrId()).collect(Collectors.toList()));

		//System.out.println(f.apply(distr));
	}
	*/
	
}
