package hk.eduhk.rich.email;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.util.ServiceLocator;

import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.Date;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.InternetAddress;


public class EmailAgent
{

	private String from;
	private String to;
	private String subject;
	private String content;
	private String cc;
	private String bcc;

	private SysParamDAO sysParamDAO = null;
	private Logger logger = Logger.getLogger(getClass().getName());

	private boolean debug = false;


	public EmailAgent()
	{
		sysParamDAO = SysParamCacheDAO.getInstance();
	}


	public void setFrom(String mfrom)
	{
		from = mfrom;
	}


	public void setTo(String mto)
	{
		to = mto;
	}


	public void setCc(String mcc)
	{
		cc = mcc;
	}


	public void setBcc(String mbcc)
	{
		bcc = mbcc;
	}


	public void setSubject(String subject)
	{
		this.subject = subject;
	}


	public void setContent(String content)
	{
		this.content = content;
	}


	public boolean send(String opt) throws Exception
	{
		if (!Constant.isLocalEnv()) 
		{
			String sessionJndi = sysParamDAO.getSysParamValueByCode(SysParam.PARAM_MAIL_SESSION_JNDI);
			ServiceLocator sl = ServiceLocator.getInstance();
			Session session = (Session) sl.lookup(sessionJndi);
			
			if (session != null)
			{
				try 
				{
					// create a message
					MimeMessage msg = new MimeMessage(session);
					msg.setFrom(new InternetAddress(from));
					
					if (!GenericValidator.isBlankOrNull(to))
					{
						msg.addRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
					}
					
					if (!GenericValidator.isBlankOrNull(cc))
					{
						msg.addRecipients(Message.RecipientType.CC, InternetAddress.parse(cc));
					}
	
					if (!GenericValidator.isBlankOrNull(bcc)) 
					{
						msg.addRecipients(Message.RecipientType.BCC, InternetAddress.parse(bcc));
					}
	
					msg.setSubject(subject);
					msg.setSentDate(new Date());
	
					if (opt != null) 
					{
						if (opt.equals("HTML")) 
						{
							msg.setContent(content, "text/html; charset=UTF-8");
						}
						else {
							msg.setText(content, "UTF-8");
						}
					}
					
					Transport.send(msg);
				}
				catch (Exception e) 
				{
					logger.log(Level.WARNING, "Cannot send email", e);
					throw e;
				}
			}
			else
			{
				throw new IllegalArgumentException("Cannot lookup mail session in JNDI");
			}
		}
		else
		{
			logger.log(Level.INFO, "Cannot send email in local environment");
		}

		return !Constant.isLocalEnv();
	}

}
