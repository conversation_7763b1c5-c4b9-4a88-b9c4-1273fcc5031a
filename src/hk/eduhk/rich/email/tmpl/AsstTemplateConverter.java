package hk.eduhk.rich.email.tmpl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.rich.entity.staff.Assistant;

public class AsstTemplateConverter extends TemplateConverter
{
	ObjectMapper objMapper = new ObjectMapper();
	
	public List<Map<String, Object>> convertParamList(Object... objs)
	{
		List<Map<String, Object>> paramList = new ArrayList<Map<String, Object>>();
		
		//param[0]
		Map<String, Object> asstMap = new LinkedHashMap<String, Object>();
		paramList.add(asstMap);
		
		if(ArrayUtils.isNotEmpty(objs)) 
		{
			for(Object obj : objs)
			{
				if(obj != null) 
				{
					if(obj instanceof Assistant)
					{
						Assistant asst = (Assistant)obj;
						
						if(asst != null)
						{
							asstMap.put("asstId", asst.getPk().getAssistant_id());
							asstMap.put("asstName", asst.getAssistant_name());
							asstMap.put("acadStaffName", asst.getAcadStaffName());
							if ("APPROVED".equals(asst.getStatus())) {
								asstMap.put("status", "accepted");
							}
							if ("REJECTED".equals(asst.getStatus())) {
								asstMap.put("status", "rejected");
							}
						}
					}
				}
			}
		}
		
		return paramList;
	}
}
