package hk.eduhk.rich.email.tmpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public abstract class TemplateConverter
{
	
	private static Map<String, TemplateConverter> converterMap = new HashMap<String, TemplateConverter>();
	
	public static TemplateConverter getTemplateConverter()
	{
		TemplateConverter obj = null;
			
		if (obj == null)
		{
			obj = new AsstTemplateConverter();
		}
		
		return obj;
	}
	
	public abstract List<Map<String, Object>> convertParamList(Object... objs);
}
