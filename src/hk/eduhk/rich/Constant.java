package hk.eduhk.rich;

import java.text.SimpleDateFormat;
import java.util.*;
import javax.faces.bean.ApplicationScoped;
import javax.faces.bean.ManagedBean;

import org.apache.commons.lang3.LocaleUtils;
import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;
import org.owasp.html.Sanitizers;

import hk.eduhk.rich.bundle.MessageBundle;


@ManagedBean(eager = true)
@ApplicationScoped
public class Constant
{
	
	public static final String ATTR_LOGIN_USER_ID		= "login.userId"; 
	public static final String ATTR_IMPERSONATE_USER_ID	= "impersonate.userId";
	
	public static final String ATTR_CURRENT_PERSON		= "current.person";
		
	public static final int AUTO_COMPLETE_RETURN_NUM = 10;
	
	public static final String DEFAULT_CHARSET 				= "UTF-8";
	public static final String DEFAULT_DATE_FORMAT 			= "dd/MM/yyyy";
	public static final String DEFAULT_DATE_FORMAT_FILE 	= "yyyyMMdd";
	public static final String DEFAULT_DATE_TIME_FORMAT_MM	= "dd/MM/yyyy HH:mm";
	public static final String DEFAULT_DATE_TIME_FORMAT		= "dd/MM/yyyy HH:mm:ss";
	public static final String DEFAULT_MONEY_FORMAT			= "###,##0.00";
	public static final String DEFAULT_TIME_FORMAT			= "HH:mm";
	public static final String DEFAULT_TIME24HOURS_PATTERN 	= "([01]?[0-9]|2[0-3]):[0-5][0-9]";
	
	public static final String DEFAULT_PHONE_PATTERN = "^(\\+)?(\\([0-9]+\\)-?)*[0-9]+(-[0-9]+)*";
	
	// Default Primefaces DataTable properties
	public static final String DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE = "(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})";
	public static final String DEFAULT_PAGINATOR_TEMPLATE = "{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}";
	public static final String DEFAULT_ROWS_PER_PAGE_TEMPLATE = "10,20,50";
	public static final String DEFAULT_ROWS_PER_PAGE_TEMPLATE_100 = "30,50,100";
	public static final String DEFAULT_ROWS_PER_PAGE_TEMPLATE_200 = "50,100,200";
	public static final String DEFAULT_ROWS_PER_PAGE_TEMPLATE_10_100 = "10,20,30,50,100";

	// The minimum date that can be supported by Excel
	public static Date EXCEL_MIN_DATE = null;
	
	public static final String DEFAULT_LANG 			= "en";
	public static final Locale DEFAULT_LOCALE			= LocaleUtils.toLocale(DEFAULT_LANG);
	
	public static final String EMPTY_JSON_ARRAY			= "[]";
	public static final String EMPTY_JSON_SET			= "{}";

	public static byte[] KEY_AES						= null;
	
	public static Map<String, String> languageNameMap 	= null;

	public static final PolicyFactory HTML_POLICY_NONE 	= new HtmlPolicyBuilder().toFactory();
	public static final PolicyFactory HTML_POLICY_LINKS = Sanitizers.LINKS.and(new HtmlPolicyBuilder().allowElements("br").toFactory());
	
	public static final String JNDI_AGG_SERVICE			= "java:global/rich/AggregationService";
	public static final String JNDI_DISTR_SERVICE		= "java:global/rich/DistributionService";
	public static final String JNDI_DISTR_SERVICE_SET 	= "java:global/rich/SETDistributionService";
		
	public static final String LOG_FILE_PROPS_LOCATION	= "/WEB-INF/logging.properties";
	public static final String LOG_FILE_PREFIX 			= "rich-log-";
	public static final String[] LOG_NAMESPACES			= new String[] {"hk.eduhk.rich"};
	
	public static final String LOGICAL_OP_AND	= "AND";
	public static final String LOGICAL_OP_OR	= "OR";
	
	public static final String PASSWORD_PEPPER = "3gc#59-P";
	
	public static boolean OS_WIN = false;
	public static boolean LOCAL_ENV = false;
	
	// These values are set at web.xml instead of here
	public static String LOCAL_USER_ID = null;
	
	
	static
	{
		try
		{
			EXCEL_MIN_DATE = new SimpleDateFormat(DEFAULT_DATE_FORMAT).parse("01/01/1900");
		}
		catch (Exception e)
		{
			
		}
	}
	
	
	public static Map<String, String> getLanguageNameMap()
	{
		if (languageNameMap == null)
		{
	        Locale locale = LocaleUtils.toLocale(Constant.DEFAULT_LANG);
	        ResourceBundle bundle = MessageBundle.getResourceBundle(locale);
			
			languageNameMap = new LinkedHashMap<String, String>();
			languageNameMap.put("en", bundle.getString("lang.en"));
			languageNameMap.put("zh_HK", bundle.getString("lang.zh"));
			languageNameMap = Collections.unmodifiableMap(languageNameMap);
		}
		
		return languageNameMap;
	}

	
	public static String getLocalUserId()
	{
		return LOCAL_USER_ID;
	}


	public static boolean isLocalEnv()
	{
		return LOCAL_ENV;
	}
	
	
	public static boolean isWindows()
	{
		return OS_WIN;
	}
	
	public String getDefaultDateFormat()
	{
		return DEFAULT_DATE_FORMAT;
	}	
}
