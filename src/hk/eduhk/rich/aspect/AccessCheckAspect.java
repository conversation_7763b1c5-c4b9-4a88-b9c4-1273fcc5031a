package hk.eduhk.rich.aspect;

import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.*;
import org.aspectj.lang.annotation.*;

import hk.eduhk.rich.BaseView;


@Aspect
public class AccessCheckAspect
{
	
	
   	@Around("execution(* hk.eduhk.rich.category.set.DistributionListView.updateDistrTime(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.DistributionListView.startSurvey(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.DistributionListView.pauseSurvey(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.DistributionListView.pauseSurvey(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.DistributionListView.newSupplForm(..)) || " +
			"execution(* hk.eduhk.rich.category.set.DistributionListView.removeSupplForm(..)) ")
	public Object distrAccessCheck(ProceedingJoinPoint joinPoint) throws Throwable 
    {
   		/*
		DistributionSelect instance = (DistributionSelect) joinPoint.getTarget();
    	
    	if (instance != null)
    	{
    		SurvDistribution distr = instance.getSelectedDistr();
	    	
	    	// Only conduct access check if the distribution has been persisted in database
	    	if (distr != null && distr.getDistrId() != null)
	    	{
	    		BaseView baseView = (BaseView) instance;
	    		String currentUserId = baseView.getCurrentUserId();
	    		
	    		// Query the distrId that the current user ID has access right to
	    		Integer distrId = new DistributionQueryChain(distr.getCatId())
								    			.filterByIdList(Arrays.asList(new Integer[] {distr.getDistrId()}))
								    			.authorized(currentUserId, SETAccessBuilder.ACS_TYPE_DISTR)
								    			.queryFirstId();
	    		
	    		// Unauthorized access
	    		if (distrId == null)
	    		{
	    			Logger logger = Logger.getLogger(instance.getClass().getName());
	    			logger.log(Level.WARNING, "Access denied (userId=" + currentUserId + ", " + "distrId=" + distr.getDistrId() + ")");
	    			
					String message = baseView.getResourceBundle().getString("msg.err.access.denied");
					FacesContext fCtx = FacesContext.getCurrentInstance();
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));

					// Terminate here. Do NOT proceed to the pointcut
					return null;
	    		}
	    	}
    	}
    	*/
    	
    	return joinPoint.proceed();
    }
	
	
   	@Around("execution(* hk.eduhk.rich.category.set.DistributionListView.assignSupplForm(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.DistributionListView.batchAssignSupplForm(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.SupplFormListView.gotoEditSupplForm(..)) || " +
   			"execution(* hk.eduhk.rich.category.set.SupplFormListView.update*(..)) || " +
    		"execution(* hk.eduhk.rich.category.set.SupplFormListView.delete*(..)) || " +
    		"execution(* hk.eduhk.rich.category.set.SupplFormCompEditView.saveCustomQuestions(..)) ")
    public Object supplFormAccessCheck(ProceedingJoinPoint joinPoint) throws Throwable 
    {
   		/*
   		SupplFormSelect instance = (SupplFormSelect) joinPoint.getTarget();
    	
    	if (instance != null)
    	{
	    	SurvSupplForm supplForm = instance.getSelectedSupplForm();
	    	
	    	// Only conduct access check if the supplementary form has been persisted in database
	    	if (supplForm != null && supplForm.getSupplFormId() != null)
	    	{
	    		BaseView baseView = (BaseView) instance;
	    		String currentUserId = baseView.getCurrentUserId();
	    				
	    		// Unauthorized access
	    		if (!StringUtils.equals(supplForm.getUserId(), currentUserId))
	    		{
	    			Logger logger = Logger.getLogger(instance.getClass().getName());
	    			logger.log(Level.WARNING, "Access denied (userId=" + currentUserId + ", " + "supplFormId=" + supplForm.getSupplFormId() + ", " + "supplFormUserId=" + supplForm.getUserId() + ")");
	    			
					String message = baseView.getResourceBundle().getString("msg.err.access.denied");
					FacesContext fCtx = FacesContext.getCurrentInstance();
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));

					// Terminate here. Do NOT proceed to the pointcut
					return null;
	    		}
	    	}
    	}
    	*/
    	
    	return joinPoint.proceed();
    }
    
}
