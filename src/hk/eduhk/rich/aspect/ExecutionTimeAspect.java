package hk.eduhk.rich.aspect;

import java.util.logging.Level;
import java.util.logging.Logger;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;

@Aspect
public class ExecutionTimeAspect
{
	
    @Around("execution(* hk.eduhk.rich.banner.BannerLookupDAO.*(..))")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable 
    {
    	
    	long t = System.currentTimeMillis();
    	Logger logger = null;
    	
    	if (joinPoint.getTarget() != null)
    	{
    		String className = joinPoint.getTarget().getClass().getName();
    		logger = Logger.getLogger(className);
    	}
    	
        //Default Object that we can use to return to the consumer
        Object returnObject = null;
        
        try 
        {
            returnObject = joinPoint.proceed();
        } 
        catch (Throwable throwable) 
        {
            //Here we can catch and modify any exceptions that are called
            //We could potentially not throw the exception to the caller and instead return "null" or a default object.
            throw throwable;
        }
        finally 
        {
            if (logger != null)
            {
            	//logger.log(Level.INFO,"Execution time=" + (System.currentTimeMillis()-t) + "ms");
            }
        }
        
        return returnObject;
    }
    
}
