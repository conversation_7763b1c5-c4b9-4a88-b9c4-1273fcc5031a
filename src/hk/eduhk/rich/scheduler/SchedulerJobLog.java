package hk.eduhk.rich.scheduler;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@Entity
@Table(name = "RH_O_SCHD_JOB_LOG")
public class SchedulerJobLog extends UserPersistenceObject
{

	private static final long serialVersionUID = 1L;
	

	@Id
	@SequenceGenerator(name = "schedulerJobLogSeqGen", sequenceName = "RH_O_SCHD_JOB_LOG_SEQ", allocationSize = 1)
	@GeneratedValue(strategy=GenerationType.SEQUENCE, generator = "schedulerJobLogSeqGen")
	@Column(name = "job_seq")
	private Integer jobSeq;
	
	@Column(name = "job_name", length = 200)
	private String jobName;
	
	@Column(name = "job_group", length = 200)
	private String jobGroup;
	
	@Column(name = "class_name", length = 250)
	private String className;
	
	@Column(name = "params", length = 200)
	private String parameters;
	
	@Column(name = "is_success")
	private Boolean success = null;
	
	@Column(name = "is_hidden")
	private boolean hidden = false;
	
	@Column(name = "message", length = 4000)
	private String message;


	public Integer getJobSeq()
	{
		return jobSeq;
	}

	
	public void setJobSeq(Integer jobSeq)
	{
		this.jobSeq = jobSeq;
	}

	
	public String getJobGroup()
	{
		return jobGroup;
	}

	
	public void setJobGroup(String jobGroup)
	{
		this.jobGroup = jobGroup;
	}

	
	public String getJobName()
	{
		return jobName;
	}

	
	public void setJobName(String jobName)
	{
		this.jobName = jobName;
	}


	public String getClassName()
	{
		return className;
	}


	public void setClassName(String className)
	{
		this.className = className;
	}


	public String getParameters()
	{
		return parameters;
	}


	public void setParameters(String parameters)
	{
		this.parameters = parameters;
	}


	public Boolean getSuccess()
	{
		return success;
	}


	public void setSuccess(Boolean success)
	{
		this.success = success;
	}
	
	
	public boolean isExecuting()
	{
		return (success == null);
	}
	

	public boolean isHidden()
	{
		return hidden;
	}

	
	public void setHidden(boolean hidden)
	{
		this.hidden = hidden;
	}
	
	
	public String getMessage()
	{
		return message;
	}


	public void setMessage(String message)
	{
		this.message = message;
	}

	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((jobSeq == null) ? 0 : jobSeq.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj) return true;
		if (obj == null) return false;
		if (getClass() != obj.getClass()) return false;
		SchedulerJobLog other = (SchedulerJobLog) obj;
		if (jobSeq == null)
		{
			if (other.jobSeq != null) return false;
		}
		else if (!jobSeq.equals(other.jobSeq)) return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SchedulerJobLog [jobSeq=" + jobSeq + ", jobName=" + jobName	+ ", jobGroup=" + jobGroup + ", className=" + className
				+ ", parameters=" + parameters + ", success=" + success + ", hidden=" + hidden + ", message=" + message + "]";
	}

}