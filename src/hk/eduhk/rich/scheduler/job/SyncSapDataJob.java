package hk.eduhk.rich.scheduler.job;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.database.DatabaseDAO;
import hk.eduhk.rich.scheduler.*;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * SyncSapDataJob 
 */
public class SyncSapDataJob extends CronJob
{ 
	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of SyncDataJob
	 */
	public SyncSapDataJob()
	{
		super();
	}


	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		String message = "";
		Boolean allSuccess = true;
		DatabaseDAO dao = DatabaseDAO.getInstance();
		try
		{
			int row = 0;
			row = dao.deleteData("RICH.RH_SAP_ACAD_PROF_OUTPUT_HDR");
			message = message + row + " rows has been deleted in RH_SAP_ACAD_PROF_OUTPUT_HDR.<br/>";
			row = dao.insertData("CHRM_SAP_ACAD_PROF_OUTPUT_HDR@EBS", "RICH.RH_SAP_ACAD_PROF_OUTPUT_HDR");
			message = message + row + " rows has been inserted into RH_SAP_ACAD_PROF_OUTPUT_HDR.<br/>";
			
			row = dao.deleteData("RICH.RH_SAP_RESEARCH_PROJECT_HDR");
			message = message + row + " rows has been deleted in RH_SAP_RESEARCH_PROJECT_HDR.<br/>";
			row = dao.insertData("CHRM_SAP_RESEARCH_PROJECT_HDR@EBS", "RICH.RH_SAP_RESEARCH_PROJECT_HDR");
			message = message + row + " rows has been inserted into RH_SAP_RESEARCH_PROJECT_HDR.<br/>";
			
			row = dao.deleteData("RICH.RH_SAP_AWARD_HDR");
			message = message + row + " rows has been deleted in RH_SAP_AWARD_HDR.<br/>";
			row = dao.insertData("APPS.HKIEDKYRIE_PER_PEOPLE_EXTRA@EBS", "RICH.RH_SAP_AWARD_HDR");
			message = message + row + " rows has been inserted into RH_SAP_AWARD_HDR.<br/>";
		}	
		catch (Exception e) {
			allSuccess = false;
			logger.log(Level.INFO, "", e);
		}		
		if(allSuccess)
		{
			
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}
		else
		{
			message = "Please refer detail errors in System Logging.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}

		setLogMessage(logMessage.toString());
	}

}