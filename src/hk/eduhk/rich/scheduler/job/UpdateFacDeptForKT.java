package hk.eduhk.rich.scheduler.job;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.time.Year;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.database.DatabaseDAO;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;
import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.scheduler.*;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * UpdateDataJob 
 */
public class UpdateFacDeptForKT extends CronJob
{ 
	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of SyncDataJob
	 */
	public UpdateFacDeptForKT()
	{
		super();
	}


	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		String message = "";
		Boolean allSuccess = true;
		List<KtForm> formList = null;
		FormDAO fDao = FormDAO.getInstance();
		try
		{
			int row = 0;
			
			
			if (formList == null) {
				formList = fDao.getKtFormList();
			}
			
			if (formList != null) {
				for (KtForm f:formList) {
					row += fDao.updateKTFacDeptMap(f.getForm_code());
				}
			}
			message = message + row + " rows has been updated.";
		}	
		catch (Exception e) {
			allSuccess = false;
			logger.log(Level.INFO, "", e);
		}		
		if(allSuccess)
		{
			
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}
		else
		{
			message = "Please refer detail errors in System Logging.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}

		setLogMessage(logMessage.toString());
	}

}