package hk.eduhk.rich.scheduler.job;

import java.io.FileOutputStream;
import java.net.ConnectException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.util.List;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.activation.*;
import javax.mail.*;
import javax.mail.internet.*;

import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.database.DatabaseDAO;
import hk.eduhk.rich.email.tmpl.AsstTemplateConverter;
import hk.eduhk.rich.entity.email.EmailService;
import hk.eduhk.rich.entity.staff.Assistant;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.scheduler.*;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * UpdateDataJob 
 */
public class SendRiFullListJob extends CronJob
{ 
	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of SyncDataJob
	 */
	public SendRiFullListJob()
	{
		super();
	}


	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		String message = "";
		Boolean allSuccess = true;
		SysParamDAO pdao = SysParamDAO.getInstance();
		try
		{
			// Create a workbook object
	        XSSFWorkbook workbook = new XSSFWorkbook();

	        // Access the first worksheet
	        XSSFSheet worksheet = workbook.createSheet("Sheet1");

	        
	        // Execute the SQL query
	        PersistenceManager pm = PersistenceManager.getInstance(); 
			ResultSet resultSet = null;
			Connection conn = null;
			PreparedStatement pStmt = null;
			
			String query = " SELECT H.OUTPUT_NO, S.AUTHORSHIP_LIST,  "
					+ "    TITLE_JOUR_BOOK,  "
					+ "    OUTPUT_TITLE_CONTINUE, "
					+ "    (SELECT L1.DESCRIPTION "
					+ "        FROM RICH.RH_L_OUTPUT_TYPE_V L1, RICH.RH_L_OUTPUT_TYPE_V L2 "
					+ "       WHERE     L1.lookup_level = 1 "
					+ "             AND L1.lookup_code = L2.parent_lookup_code "
					+ "             AND H.sap_output_type = L2.lookup_code) "
					+ "        sap_output_cat, "
					+ "  (SELECT L2.description "
					+ "                    FROM RICH.RH_L_OUTPUT_TYPE_V L2 "
					+ "                   WHERE     L2.lookup_code = H.sap_output_type "
					+ "                         AND L2.lookup_level = 2) "
					+ "                    sap_output_type_desc, "
					+ "(SELECT L7.description "
					+ "                    FROM RICH.RH_L_RESEARCH_TYPE_V L7 "
					+ "                   WHERE     L7.lookup_code = H.sap_refered_journal "
					+ "                         AND L7.lookup_level = 1) "
					+ "                    sap_refered_journal_desc, "
					+ "   TITLE_PAPER_ART, "
					+ "   NAME_OTHER_EDITORS, "
					+ "   VOL_ISSUE, "
					+ "   PAGE_NUM, "
					+ "   PAGE_NUM_FROM, "
					+ "   PAGE_NUM_TO, "
					+ "   CITY, "
					+ "   FROM_MONTH, "
					+ "   FROM_YEAR, "
					+ "   TO_MONTH, "
					+ "   TO_YEAR, "
					+ "   PUBLISHER, "
					+ "   OTHER_DETAILS, "
					+ "   OTHER_DETAILS_CONTINUE, "
					+ "   LANGUAGE, "
					+ "   NAME_OTHER_POS , "
					+ "   IED_WORK_IND, "
					+ "   APA_CITATION, "
					+ "   ISSN, "
					+ "   ARTICLE_NUM, "
					+ "   DOI, "
					+ "   EISSN, "
					+ "   FULLTEXT_URL, "
					+ "   ISBN, "
					+ "	  SDG_CODE, "
					+ "   H.CREATION_TIME, "
					+ "   H.TIMESTAMP                                          "
					+ "FROM RICH.RH_P_ACAD_PROF_OUTPUT_HDR H "
					+ "    LEFT JOIN "
					+ "    (SELECT OUTPUT_NO, LISTAGG(AUTHORSHIP, ';') WITHIN GROUP (ORDER BY LINE_NO) AUTHORSHIP_LIST "
					+ "        FROM "
					+ "        ( "
					+ "            SELECT OUTPUT_NO, AUTHORSHIP_NAME || '[' || DEPT_CODE || ']'|| ' [' || L.DESCRIPTION || ']' AUTHORSHIP, LINE_NO, AUTHORSHIP_STAFF_NO  "
					+ "                FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL D "
					+ "                    LEFT JOIN RICH.RH_L_AUTHORSHIP_V L ON D.AUTHORSHIP_TYPE = L.LOOKUP_CODE "
					+ "                    LEFT JOIN (SELECT * FROM RICH.RH_P_STAFF_IDENTITY) S ON D.AUTHORSHIP_STAFF_NO = S.STAFF_NUMBER "
					+ "			WHERE D.DATA_LEVEL = 'P' "
					+ "        ) "
					+ "		where line_no < 139 "
					+ "        GROUP BY OUTPUT_NO) S ON H.OUTPUT_NO = S.OUTPUT_NO     "
					+ "WHERE DATA_LEVEL = 'P'                 "
					+ "ORDER BY H.OUTPUT_NO";
			//System.out.println("query:"+query);
			// Execute the query

			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				resultSet = pStmt.executeQuery();
			
		        // Get the column count
		        int columnCount = resultSet.getMetaData().getColumnCount();
	
		        
		        // Create the header row
		        Row headerRow = worksheet.createRow(0);
		        for (int i = 1; i <= columnCount; i++) {
		            Cell cell = headerRow.createCell(i - 1);
		            cell.setCellValue(resultSet.getMetaData().getColumnName(i));
		        }
	
		        // Create the data rows
		        int rowNumber = 1;
		        while (resultSet.next()) {
		            Row dataRow = worksheet.createRow(rowNumber++);
		            for (int i = 1; i <= columnCount; i++) {
		                Cell cell = dataRow.createCell(i - 1);
		                cell.setCellValue(resultSet.getString(i));
		            }
		        }
	
		        // Auto-size the columns
		        for (int i = 0; i < columnCount; i++) {
		            worksheet.autoSizeColumn(i);
		        }
	
		        // Save the workbook to an Excel file
		        FileOutputStream outputStream = new FileOutputStream("full_ri_list.xlsx");
		        workbook.write(outputStream);
		        workbook.close();
		        outputStream.close();	
		        
		        SysParam passwordSysParam = pdao.getSysParamByCode("email.sendRi.from.pw");
				
		        // Send the Excel file as an email attachment
		        String to = pdao.getSysParamValueByCode("email.sendRi.to");
		        String from = pdao.getSysParamValueByCode("email.sendRi.from");
		        String cc = pdao.getSysParamValueByCode("email.sendRi.cc");
		        String title = pdao.getSysParamValueByCode("email.sendRi.title");
		        String content = pdao.getSysParamValueByCode("email.sendRi.content");
		        String host = "smtp.gmail.com";
		        String password = passwordSysParam.getDecryptedValue();
		        
		        //Getting the current date value
		        LocalDate currentdate = LocalDate.now();
		        //Getting the current month
		        Month currentMonth = currentdate.getMonth();
		        //getting the current year
		        int currentYear = currentdate.getYear();
		        
		        title = title + currentMonth + " " + currentYear;
		        
		        // Setup mail server
		        Properties properties = System.getProperties();
		        properties.put("mail.smtp.host", host);
		        properties.put("mail.smtp.port", "465");
		        properties.put("mail.smtp.ssl.enable", "true");
		        properties.put("mail.smtp.auth", "true");

		        
		     // creates a new session with an authenticator
	            Authenticator auth = new Authenticator() {
	                public PasswordAuthentication getPasswordAuthentication() {
	                    return new PasswordAuthentication(from, password);
	                }
	            };

	            Session session = Session.getInstance(properties, auth);
		        
		        MimeMessage emailMessage = new MimeMessage(session);
		        emailMessage.setFrom(new InternetAddress(from));
		        emailMessage.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
		        //emailMessage.addRecipient(Message.RecipientType.CC, new InternetAddress(cc));
		        
		        String[] ccList = cc.split(",");
		        InternetAddress[] recipientAddress = new InternetAddress[ccList.length];
		        int counter = 0;
		        for (String recipient : ccList) {
		            recipientAddress[counter] = new InternetAddress(recipient.trim());
		            counter++;
		        }
		        emailMessage.setRecipients(Message.RecipientType.CC, recipientAddress);
		        
		        
		        emailMessage.setSubject(title);
		        BodyPart messageBodyPart = new MimeBodyPart();
		        messageBodyPart.setText(content);
		        Multipart multipart = new MimeMultipart();
		        multipart.addBodyPart(messageBodyPart);
		        messageBodyPart = new MimeBodyPart();
		        DataSource source = new FileDataSource("full_ri_list.xlsx");
		        messageBodyPart.setDataHandler(new DataHandler(source));
		        messageBodyPart.setFileName("full_ri_list.xlsx");
		        multipart.addBodyPart(messageBodyPart);
		        emailMessage.setContent(multipart);
		        Transport.send(emailMessage);
			}
			catch (SQLException e) {
	            e.printStackTrace();
	        }finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}

		}	
		catch (Exception e) {
			allSuccess = false;
			logger.log(Level.INFO, "", e);
		}		
		if(allSuccess)
		{
			
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}
		else
		{
			message = "Please refer detail errors in System Logging.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}

		setLogMessage(logMessage.toString());
	}
	
	/*public void sendEmail(Assistant obj, String email, String emailTmpl) throws ConnectException, JsonProcessingException {
		EmailService emailService = new EmailService();
		AsstTemplateConverter tmplConverter = new AsstTemplateConverter();

		List<String> resList = emailService.sendEmail(emailTmpl, email, null);
		//index 0 is the response code; index 1 is the response details
		int resCode = Integer.parseInt(resList.get(0));
		String response = resList.get(1);
		if (resCode == 200)
		{
			logger.log(Level.INFO, response);
		}
		else
		{
			// Response code not equals to 200
			throw new ConnectException(); 
		}
    }*/

}