package hk.eduhk.rich.scheduler.job;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;

import org.primefaces.shaded.json.JSONArray;
import org.primefaces.shaded.json.JSONObject;

import com.google.api.client.http.ByteArrayContent;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpBackOffUnsuccessfulResponseHandler;
import com.google.api.client.http.HttpContent;
import com.google.api.client.http.HttpRequest;
import com.google.api.client.http.HttpResponse;
import com.google.api.client.http.HttpResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.util.ExponentialBackOff;

import hk.eduhk.rich.email.EmailAgent;
import hk.eduhk.rich.access.UnauthorizedException;
import hk.eduhk.rich.entity.importRI.ImportRIBatch;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIStore;
import hk.eduhk.rich.entity.importRI.ImportRIStore_PK;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.scheduler.*;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * PublishDataJob 
 */
public class GetHRECDataJob extends CronJob
{
	private Logger logger = Logger.getLogger(getClass().getName());

	private SysParamDAO sysParamDAO = SysParamDAO.getCacheInstance();
	private static String uploadSrc = "PROJECT_HREC";
	
	ExponentialBackOff exponentialBackOff = new ExponentialBackOff.Builder()
			   .setInitialIntervalMillis(1000)
			   .setMaxElapsedTimeMillis(60000)
			   .setMaxIntervalMillis(32000)
			   .setMultiplier(2)
			   .setRandomizationFactor(0.5)
			   .build();
	
	
	public HttpRequest backOff(HttpRequest request)
	{
		logger.log(Level.FINE, "Handle exponential backOff to request");
		return request.setUnsuccessfulResponseHandler(new HttpBackOffUnsuccessfulResponseHandler(exponentialBackOff));
	}

	/**
	 * Constructor of PublishDataJob
	 */
	public GetHRECDataJob()
	{
		super();
	}


	public void executeJob()
	{
		try
		{
			updateHRECData();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	
	private List<ImportRIStore> updateHRECData() throws Exception
	{
		JSONArray roleArray;
		List<ImportRIStore> storeList = null;
//		StringBuilder paraStringBuilder = new StringBuilder();
//	    paraStringBuilder.append("?");
//	    paraStringBuilder.append("id=HREC:_Rich&");
//	    paraStringBuilder.append("_format=json&");
//	    paraStringBuilder.append("USERNAME=" + sysParamDAO.getSysParamValueByCode(SysParam.PARAM_HREC_DATA_USERNAME) + "&");
//	    paraStringBuilder.append("PASSWORD=" + sysParamDAO.getSysParamValueByCode(SysParam.PARAM_HREC_DATA_PASSWORD));
	    
	    StringBuilder responseString = APIBuilder("runfunction.aspx");
//	    StringBuilder responseString = new StringBuilder();
//	    responseString.append("[ { \"Status\": \"Approved\", \"ProjectTitle\": \"Co-I Chair\", \"ProjectFrom\": \"13/12/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0030\", \"ProjectTo\": \"24/12/2024\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"test\", \"ProjectFrom\": \"03/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0029\", \"ProjectTo\": \"25/05/2021\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Test\", \"ProjectFrom\": \"20/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0026\", \"ProjectTo\": \"27/05/2021\", \"ProjectSummary\": \"11111111\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Co-I Chair\", \"ProjectFrom\": \"19/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0025\", \"ProjectTo\": \"31/05/2021\", \"ProjectSummary\": \"1234\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"delegate\", \"ProjectFrom\": \"04/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0024\", \"ProjectTo\": \"31/05/2021\", \"ProjectSummary\": \"1234\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Testing by OCIO\", \"ProjectFrom\": \"30/04/2021\", \"FundingTitle\": \"Testing Title\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0023\", \"ProjectTo\": \"31/05/2021\", \"ProjectSummary\": \"abcdefg\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Test Chair as Co-I\", \"ProjectFrom\": \"31/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0022\", \"ProjectTo\": \"31/05/2022\", \"ProjectSummary\": \"abc\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Co-I Chair\", \"ProjectFrom\": \"24/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0021\", \"ProjectTo\": \"28/05/2021\", \"ProjectSummary\": \"1234\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Delegate\", \"ProjectFrom\": \"03/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0019\", \"ProjectTo\": \"25/05/2021\", \"ProjectSummary\": \"1233\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"human\", \"ProjectFrom\": \"12/01/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"2020-2021-0017\", \"ProjectTo\": \"18/02/2021\", \"ProjectSummary\": \"human\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"artefacts\", \"ProjectFrom\": \"20/01/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0016\", \"ProjectTo\": \"24/02/2021\", \"ProjectSummary\": \"artefacts\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"delegate 20210111a\", \"ProjectFrom\": \"29/01/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0015\", \"ProjectTo\": \"31/12/2021\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"human\", \"ProjectFrom\": \"04/01/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0012\", \"ProjectTo\": \"29/01/2021\", \"ProjectSummary\": \"human\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Delegate Change 20201230\", \"ProjectFrom\": \"31/12/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0009\", \"ProjectTo\": \"31/12/2021\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Artefacts 20201230c\", \"ProjectFrom\": \"31/12/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"ppschung\", \"ReferenceNumber\": \"2020-2021-0008\", \"ProjectTo\": \"31/12/2023\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"artefacts\", \"ProjectFrom\": \"10/12/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0007\", \"ProjectTo\": \"31/12/2020\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct05\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Artefacts 20201230b\", \"ProjectFrom\": \"31/12/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"ppschung\", \"ReferenceNumber\": \"2020-2021-0005\", \"ProjectTo\": \"31/12/2022\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"only human\", \"ProjectFrom\": \"31/12/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2020-2021-0002\", \"ProjectTo\": \"30/12/2022\", \"ProjectSummary\": \"1234\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Application to test delegated amendment application\", \"ProjectFrom\": \"01/10/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"fhr-uat-uacct08\", \"ReferenceNumber\": \"2018-2019-0035\", \"ProjectTo\": \"31/10/2021\", \"ProjectSummary\": \"XXXXXXX\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct05\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"123\", \"ProjectFrom\": \"08/04/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct02\", \"ReferenceNumber\": \"2018-2019-0034\", \"ProjectTo\": \"15/10/2020\", \"ProjectSummary\": \"purpose 123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct05\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"123\", \"ProjectFrom\": \"01/05/2020\", \"FundingTitle\": \"funding test\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0033\", \"ProjectTo\": \"31/10/2020\", \"ProjectSummary\": \"Purpose of the Researchtesttest\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project_Student_20191106\", \"ProjectFrom\": \"29/11/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0028\", \"ProjectTo\": \"30/06/2020\", \"ProjectSummary\": \"To identify key priorities and barriers for research development in health librarianshipTo gain consensus on key questions to be included in a national survey for research development in health librarianship\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Achieving team synergy: The effect of the timing and the frequency of group interaction on group problem solving\", \"ProjectFrom\": \"17/10/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0026\", \"ProjectTo\": \"30/10/2020\", \"ProjectSummary\": \"test\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Test (Student)\", \"ProjectFrom\": \"24/10/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0025\", \"ProjectTo\": \"19/12/2019\", \"ProjectSummary\": \"Test\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Testing (Delegate)\", \"ProjectFrom\": \"09/10/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0024\", \"ProjectTo\": \"21/10/2020\", \"ProjectSummary\": \"Test\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Project 20191004 - Staff/Delegate - Regular - 2nd UAT\", \"ProjectFrom\": \"01/11/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0023\", \"ProjectTo\": \"31/10/2021\", \"ProjectSummary\": \"The study investigated the influence of environment and heredity on health status of primary school pupils in Jos north Local Government Area, Plateau state. Related literature were reviewed on the area by the study, descriptive survey research design method was used. The populace of the study comprised all primary schools in Jos north Local Government Area Plateau state; two hundred respondents were randomly selected from five schools in Jos north Local Government Area. The instrument used for the study was a structured questionnaire which was validated and tested for reliability. The test retest method was used and the coefficient of the reliability was 0.87.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Project 20191003 - Student - Regular - 2nd UAT\", \"ProjectFrom\": \"02/12/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0022\", \"ProjectTo\": \"30/09/2020\", \"ProjectSummary\": \"Schools are established to equip the youths with essential skills needed for functionality as useful and knowledgeable citizens of the country. However, examination of school records shows that students’ academic performances have remained for long very unimpressive. Obviously, this is not healthy for the growth of the country.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Test Test\", \"ProjectFrom\": \"16/10/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0021\", \"ProjectTo\": \"27/11/2019\", \"ProjectSummary\": \"PsychologyPsychologyPsychology\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Test\", \"ProjectFrom\": \"02/12/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0020\", \"ProjectTo\": \"21/12/2023\", \"ProjectSummary\": \"TestTestTest\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Achieving team synergy: The effect of the timing and the frequency of group interaction on group problem solving\", \"ProjectFrom\": \"03/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0019\", \"ProjectTo\": \"18/09/2019\", \"ProjectSummary\": \"Psychology\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"THE PROBLEMS FACING THE TEACHING AND LEARNING OF INTEGRATED SCIENCE IN JUNIOR SECONDARY SCHOOLS\", \"ProjectFrom\": \"21/10/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0018\", \"ProjectTo\": \"26/03/2020\", \"ProjectSummary\": \"In this research project, an attempt was made by the researcher to find out the role of supervision in the development of primary education in some selected schools within sokoto metropolis. Simple and sampling technique research designing was adopted in the study. The instrument was questionnaires for head masters and the teachers in the area of the study to obtain their responses on the subject matter. The responses obtain by the researchers indicate that one of the important thing also observed in the research is the nonpayment of supervisors allowances provided that encouraged the supervisors to perform their duty effectively. Therefore the researchers suggest that the ministry of education should ensure the training and retraining of school supervisors to keep them current in discharging their duties in the area of the study.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"abc\", \"ProjectFrom\": \"(unset)\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0017\", \"ProjectTo\": \"(unset)\", \"ProjectSummary\": \"abc\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project 20190911_Student\", \"ProjectFrom\": \"02/10/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0016\", \"ProjectTo\": \"30/08/2020\", \"ProjectSummary\": \"Despite some thirty years of social scientific research into fatherhood and masculinity, and the recent increase in the public and political ‘visibility’ of fathers, key researchers such as Lamb (2004), Morgan (2002) and Lewis (2000) continue to argue that our understanding of men’s experiences as fathers remains limited. “There are substantial gaps in our current knowledge about fatherhood” (Lewis, 2000). One such gap is in the relative lack of empirical insight into the experiences of working class fathers. In theoretical terms fatherhood is increasingly recognised as complex and dynamic, as an identity and a ‘practice’ which is played out in a range of social contexts and which is both enabled and constrained by (often-contradictory) social institutions and norms. More research is needed that attempts to chart the processes by which men perceive and negotiate their identity and activity as fathers. In addition, a growing recognition of the importance and ‘reality’ of post-divorce parenting has focused both academic and political attention on the roles, involvement and identity of fathers after divorce or separation.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Project 20190911_Staff\", \"ProjectFrom\": \"01/11/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"tmchan\", \"ReferenceNumber\": \"2018-2019-0015\", \"ProjectTo\": \"30/09/2021\", \"ProjectSummary\": \"‘Working At It’ An exploration of the perceptions and experiences of negotiating employment and caring responsibilities of fathers in post-divorce/separation co-parenting situations.Despite some thirty years of social scientific research into fatherhood and masculinity, and the recent increase in the public and political ‘visibility’ of fathers, key researchers such as Lamb (2004), Morgan (2002) and Lewis (2000) continue to argue that our understanding of men’s experiences as fathers remains limited. “There are substantial gaps in our current knowledge about fatherhood” (Lewis, 2000). One such gap is in the relative lack of empirical insight into the experiences of working class fathers. In theoretical terms fatherhood is increasingly recognised as complex and dynamic, as an identity and a ‘practice’ which is played out in a range of social contexts and which is both enabled and constrained by (often-contradictory) social institutions and norms. More research is needed that attempts to chart the processes by which men perceive and negotiate their identity and activity as fathers. In addition, a growing recognition of the importance and ‘reality’ of post-divorce parenting has focused both academic and political attention on the roles, involvement and identity of fathers after divorce or separation. My research will contribute to a growing sociology of 'family practice', building on existing fatherhood research and adding to the insightful and innovative work on post divorce parenting developed by sociologists such as Rosalind Edwards, Simon Duncan, Jane Ribbens McCarthy, Carol Smart and Judith Glover. In different ways such writers have sought to present a more accurate and grounded knowledge of family life together with a critical investigation into both contemporary parenting and, importantly, the social policy and legal frameworks which surround this. Their research emphasises the complex, often moral, dilemmas involved in making and re-making families (Ribbens McCarthy, Edwards & Gillies, 2003) and asserts the creativity of family members in such processes. Also offered is an arguably more constructive approach to divorce/separation PG Research Prospectus 2008 (DD/ph 18th Feb) 2 suggesting that it may provide a catalyst for thinking and acting differently about parenting and about gender roles. In this way it could be that divorced/separated fathers, together with many lone-mothers, have the potential (not necessarily by choice) to challenge the enduring gendered model for organising earning and caring, and are therefore sociologically and politically significant. My study seeks to investigate the practice and processes of negotiating employment and caring responsibilities for divorced or separated fathers who have regular physical care of their children. It will focus on the experiences and perceptions of fathers’ in relation to their roles and identity as fathers, their relationships with their children and their working lives.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Achieving team synergy\", \"ProjectFrom\": \"18/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0014\", \"ProjectTo\": \"13/09/2000\", \"ProjectSummary\": \"123\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Language development\", \"ProjectFrom\": \"19/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0012\", \"ProjectTo\": \"18/09/2020\", \"ProjectSummary\": \"12345\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project TEST Delegate\", \"ProjectFrom\": \"21/08/2019\", \"FundingTitle\": \"PPR\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0011\", \"ProjectTo\": \"17/08/2021\", \"ProjectSummary\": \"the Human Research Ethics Committee (HREC) receives for review any research protocols involving human participants submitted by Principal Investigators from the academic/research staff and students from Faculties other than Medicine and Dentistry; a\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project FGH\", \"ProjectFrom\": \"09/09/2019\", \"FundingTitle\": \"departmental development fund\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"mahocheung\", \"ReferenceNumber\": \"2018-2019-0010\", \"ProjectTo\": \"31/12/2019\", \"ProjectSummary\": \"the Human Research Ethics Committee (HREC) receives for review any research protocols involving human participants submitted by Principal Investigators from the academic/research staff and students from Faculties other than Medicine and Dentistry\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Project CDF\", \"ProjectFrom\": \"01/01/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"mahocheung\", \"ReferenceNumber\": \"2018-2019-0009\", \"ProjectTo\": \"31/08/2020\", \"ProjectSummary\": \"In line with the HKU Policy on Research Integrity, Principal Investigators (PIs) who are academic/research staff members or research students (MPhil/PhD) in Faculties other than Medicine and Dentistry should apply to the Human Research Ethics Committee (HREC) for ethical clearance for research involving human participants.  \", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Testing Application made by OCIO\", \"ProjectFrom\": \"02/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0008\", \"ProjectTo\": \"31/08/2020\", \"ProjectSummary\": \"In line with the HKU Policy on Research Integrity, Principal Investigators (PIs) who are academic/research staff members or research students (MPhil/PhD) in Faculties other than Medicine and Dentistry should apply to the Human Research Ethics Committee (HREC) for ethical clearance for research involving human participants.   \", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Crafting an Institutional Ethnography of ‘Entrepreneurial Universities’: Diverse Voices and Critical Insights from Hong Kong’s Academic Profession\", \"ProjectFrom\": \"11/08/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0006\", \"ProjectTo\": \"17/09/2019\", \"ProjectSummary\": \"This research aims to examine the diverse voices and critical insights of the academic profession inHong Kong’s research-intensive universities in response to the policies and practices ofentrepreneurial universities. It also aims to identify the institutional logics of Hong Kong’s research-intensive universities and illuminate the new reality and dynamics between entrepreneurialuniversities and the everyday life of academics.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Testing by OCIO\", \"ProjectFrom\": \"01/01/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0005\", \"ProjectTo\": \"30/08/2022\", \"ProjectSummary\": \"This proposed study aims to investigate the role of phonological correlations to the writingdevelopment through a cross-sectional study of writing errors and learning strategies of Chinese-character writing (hereafter CW) by non-Chinese (L2) leaners. L2 learners are drawn from those whouse an alphabet-based writing system and others who use an ideograph-based one. Their writing performances are collected through a writingto-dictation test and error patterns are examined againstlearners' level of L2 proficiencies and first language (L1) writing systems. Pre- and post-dictation, theStrategy Inventory for Language Learning (version 5.1) and the comprehensive strategy checklistsare used to identify L2 learners’ respective strategies.\", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Artistic Citizenship Development of Hong Kong Youth through Community Music Participation: A Case Study\", \"ProjectFrom\": \"01/09/2018\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0002\", \"ProjectTo\": \"30/11/2019\", \"ProjectSummary\": \"Tasks: Divergent and convergent thinking tasks will be presented to participants during Experiment 1 and 2. Divergent thinking tasks involve the generation of multiple ideas, e.g., idea generation task, and convergent thinking tasks involve generating the unique and novel solution to a problem, e.g., matchstick problems (see attached for sample tasks). \", \"Type\": \"Application\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Sample Application for Amendment of a student\", \"ProjectFrom\": \"03/09/2018\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"2018-2019-0001\", \"ProjectTo\": \"30/09/2019\", \"ProjectSummary\": \"Tasks: Divergent and convergent thinking tasks will be presented to participants during Experiment 1 and 2. Divergent thinking tasks involve the generation of multiple ideas, e.g., idea generation task, and convergent thinking tasks involve generating the unique and novel solution to a problem, e.g., matchstick problems (see attached for sample tasks). \", \"Type\": \"Application\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Sample Application for Exemption created by OCIO\", \"ProjectFrom\": \"01/07/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0001\", \"ProjectTo\": \"31/07/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \" Sentiment Strength Detection with A Context-Dependent Lexicon-Based Convolutional Neural Network\", \"ProjectFrom\": \"01/06/2019\", \"FundingTitle\": \"Internal Research Fund\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0002\", \"ProjectTo\": \"31/12/2020\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"A study of the Cantonese Bibles by Western Protestant Missionaries in the late-Qing and early-Republican era\", \"ProjectFrom\": \"(unset)\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0003\", \"ProjectTo\": \"(unset)\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"An Investigative Study on the Effect of Gender Difference on Academic Performance of Chemistry Students in Secondary School\", \"ProjectFrom\": \"02/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0006\", \"ProjectTo\": \"31/12/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"testing from OCIO\", \"ProjectFrom\": \"02/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0009\", \"ProjectTo\": \"28/09/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Kindness Interventions\", \"ProjectFrom\": \"19/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0010\", \"ProjectTo\": \"29/10/2021\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Values education in Kazakhstani schools\", \"ProjectFrom\": \"16/09/2019\", \"FundingTitle\": \"Start-up Research Grant\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0011\", \"ProjectTo\": \"27/09/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project 20190916_Staff\", \"ProjectFrom\": \"02/12/2019\", \"FundingTitle\": \"Department Fund\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"1234567\", \"ReferenceNumber\": \"E2018-2019-0013\", \"ProjectTo\": \"30/08/2021\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project 20190916b - Staff\", \"ProjectFrom\": \"30/10/2019\", \"FundingTitle\": \"xyz\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"peterchan\", \"ReferenceNumber\": \"E2018-2019-0014\", \"ProjectTo\": \"30/09/2022\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Project 20190916c -Staff\", \"ProjectFrom\": \"20/11/2019\", \"FundingTitle\": \"CRF\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0015\", \"ProjectTo\": \"16/06/2020\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"abc\", \"ProjectFrom\": \"20/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0016\", \"ProjectTo\": \"30/09/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Achieving team synergy: The effect of the timing and the frequency of group interaction on group problem solving\", \"ProjectFrom\": \"11/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0017\", \"ProjectTo\": \"24/09/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Test\", \"ProjectFrom\": \"02/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0018\", \"ProjectTo\": \"26/09/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"training04\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"abc\", \"ProjectFrom\": \"02/09/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0019\", \"ProjectTo\": \"30/09/2021\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Test Test\", \"ProjectFrom\": \"01/10/2019\", \"FundingTitle\": \"Start-Up Research Grant\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0020\", \"ProjectTo\": \"31/10/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser09\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Project 20191004 - Student - Exemption - 2nd UAT\", \"ProjectFrom\": \"17/12/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0022\", \"ProjectTo\": \"24/08/2020\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"training02\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"123\", \"ProjectFrom\": \"16/10/2019\", \"FundingTitle\": \"123\", \"TypeOfFunding\": \"Internal Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0023\", \"ProjectTo\": \"24/12/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Submitted\", \"ProjectTitle\": \"Test Project Duration\", \"ProjectFrom\": \"31/12/2019\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0025\", \"ProjectTo\": \"03/12/2019\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Project staff approve 20200717\", \"ProjectFrom\": \"24/07/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0031\", \"ProjectTo\": \"13/07/2022\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"852\", \"ProjectFrom\": \"30/09/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2018-2019-0035\", \"ProjectTo\": \"29/09/2021\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser08\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"test\", \"ProjectFrom\": \"11/11/2020\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"\", \"ReferenceNumber\": \"E2020-2021-0001\", \"ProjectTo\": \"25/02/2021\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"fhr-uat-uacct05\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Exemption check on chair message display\", \"ProjectFrom\": \"03/01/2022\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"External Funding\", \"CoInvestigators\": \"1234567\", \"ReferenceNumber\": \"E2020-2021-0002\", \"ProjectTo\": \"30/11/2023\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"staffuser11\" }, { \"Status\": \"Approved\", \"ProjectTitle\": \"Co-I Chair\", \"ProjectFrom\": \"11/05/2021\", \"FundingTitle\": \"\", \"TypeOfFunding\": \"No Funding\", \"CoInvestigators\": \"fhr-uat-uacct05\", \"ReferenceNumber\": \"E2020-2021-0004\", \"ProjectTo\": \"28/05/2021\", \"ProjectSummary\": \"\", \"Type\": \"Exemption\", \"PrincipalInvestigator\": \"fhr-uat-uacct02\" } ]");
	    if(responseString != null) {
	    	roleArray = new JSONArray(responseString.toString());
	    	if(roleArray != null) {
	    		storeList = new ArrayList<ImportRIStore>();
	    		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
	    		ImportRIBatch oldBatch = dao.getBatchByAreaCodeAndKey(uploadSrc,
	    				sysParamDAO.getSysParamValueByCode(SysParam.PARAM_HREC_DATA_BATCH_NAME));
	    		if(oldBatch != null) {
	    			dao.deleteBatch(oldBatch.getBatch_id());
					dao.runBuiltStoreStaff(oldBatch.getArea_code());
	    		}
	    		ImportRIBatch batch = new ImportRIBatch();
	    		batch.setArea_code(uploadSrc);
	    		batch.setBatch_key(sysParamDAO.getSysParamValueByCode(SysParam.PARAM_HREC_DATA_BATCH_NAME).trim());
	    		batch.setFile_name("none");
	    		batch.setRemarks("schedule job generate");
	    		batch.setFile_size(0);
	    		batch = dao.updateBatch(batch);
	    		Integer dataRowNum = 0;
	    		for(int i = 0 ; i < roleArray.length() ; ++i) {
	    			JSONObject data = roleArray.getJSONObject(i);
	    			dataRowNum++;
	    			ImportRIStore store = new ImportRIStore();
	    			ImportRIStore_PK pk = new ImportRIStore_PK();
	    			store.setRow_num(dataRowNum);
	    			pk.setArea_code(uploadSrc);
	    			pk.setBatch_id(batch.getBatch_id());
	    			store.setPk(pk);
	    			pk.setSource_id(data.getString("ReferenceNumber"));
	    			store.setCol_1(data.getString("PrincipalInvestigator"));
	    			store.setCol_2(data.getString("CoInvestigators"));
	    			store.setCol_3(data.getString("Type"));
	    			store.setCol_4(data.getString("Status"));
	    			store.setCol_5(data.getString("TypeOfFunding"));
	    			store.setCol_6(data.getString("FundingTitle"));
	    			store.setCol_7(data.getString("ProjectTitle"));
	    			store.setCol_91(data.getString("ProjectSummary"));
	    			store.setCol_8(data.getString("ProjectFrom"));
	    			store.setCol_9(data.getString("ProjectTo"));
	    			storeList.add(store);
	    			dao.updateStore(store);
	    		}
	    		batch.setRow_num(dataRowNum);
	    		batch = dao.updateBatch(batch);
	    		try {
	    			dao.runBuiltStoreStaff(uploadSrc);
	    		}
	    		catch (Exception e) {
	    			e.printStackTrace();
					throw new RuntimeException(e);
	    		}
	    	}
	    }
	    return storeList;
	}
	
	
	private StringBuilder APIBuilder(String apiURL) throws Exception
	{
		String responseLine;
		StringBuilder responseString;
		int responseCode = -1;
		
		try
		{
			//set json that is the parameters will send to web service
		    StringBuilder paraStringBuilder = new StringBuilder();
		    
		    //Create the request
			//API-Create Response Export
			GenericUrl url = new GenericUrl(sysParamDAO.getSysParamValueByCode(SysParam.PARAM_HREC_DATA_DOMAIN)+apiURL);
			url.put("id", "HREC:_Rich");
			url.put("_format", "json");
			url.put("USERNAME", sysParamDAO.getSysParamValueByCode(SysParam.PARAM_HREC_DATA_USERNAME));
			url.put("PASSWORD", sysParamDAO.getSysParamDecryptedValueByCode(SysParam.PARAM_HREC_DATA_PASSWORD));
			HttpTransport transport = new NetHttpTransport();
			HttpContent content = new ByteArrayContent("application/json", paraStringBuilder.toString().getBytes());
			HttpRequest request = transport.createRequestFactory().buildPostRequest(url, content);
			
			//Send request and Receive the response
			HttpResponse response = backOff(request).execute();
			responseCode = response.getStatusCode();
			
			try(BufferedReader br = new BufferedReader(new InputStreamReader(response.getContent(), "utf-8"))) {
	    		responseString = new StringBuilder();
	    		
    		    while ((responseLine = br.readLine()) != null) 
    		    {
    		    	responseString.append(responseLine.trim());
    		    }
	    	}
			
			if (responseCode == javax.servlet.http.HttpServletResponse.SC_OK)
			{
				return responseString;
			}
		}
		catch (Exception e)
		{
			// General exception handling for all QualtricsAction
			if (e instanceof HttpResponseException)
			{
				HttpResponseException httpre = (HttpResponseException) e;
				int statusCode = httpre.getStatusCode();
				String message = (httpre.getContent() != null) ? new JSONObject(httpre.getContent()).getJSONObject("meta").getJSONObject("error").getString("errorMessage") : "";
				logger.log(Level.WARNING, "Error: " + message, httpre);
				
				switch (statusCode)
				{	
					// Bad request. Missing auth token, or missing Content-Type, or Incorrect Json raw (incorrect param name), 
					// or Invalid libraryId
					case 400:
						throw new UnauthorizedException("Invalid credentials (Status code=" + statusCode + "). " + message);
					
					// Incorrect Qualtrics OAuth token
					case 401:
						throw new UnauthorizedException("Unauthorized OAuth token (Status code=" + statusCode + "). " + message);
						
					case 403:
						throw new UnauthorizedException("Forbidden operation (Status code=" + statusCode + "). " + message);
						
					default:
						throw new UnauthorizedException("Cannot connect to Pyramid (Status code=" + statusCode + "). " + message);
				}
			}
			else
			{
				e.printStackTrace();
				throw new RuntimeException(e);
			}
		}
		return null;
	}

}