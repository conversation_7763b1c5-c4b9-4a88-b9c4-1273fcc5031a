package hk.eduhk.rich.scheduler.job;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.time.Year;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.database.DatabaseDAO;
import hk.eduhk.rich.scheduler.*;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * UpdateDataJob 
 */
public class UpdateDataJob extends CronJob
{ 
	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of SyncDataJob
	 */
	public UpdateDataJob()
	{
		super();
	}


	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		String message = "";
		Boolean allSuccess = true;
		DatabaseDAO dao = DatabaseDAO.getInstance();
		try
		{
			int row = 0;
			int year = Year.now().getValue();
			row = dao.updateData("RICH.RH_Z_SYS_PARAM", "PARAM_VALUE", "30/06/"+year, "PARAM_CODE", "CENSUS_DATE");
			message = message + row + " rows has been updated in RH_Z_SYS_PARAM.<br/>WHERE PARAM_CODE = CENSUS_DATE";
		}	
		catch (Exception e) {
			allSuccess = false;
			logger.log(Level.INFO, "", e);
		}		
		if(allSuccess)
		{
			
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}
		else
		{
			message = "Please refer detail errors in System Logging.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}

		setLogMessage(logMessage.toString());
	}

}