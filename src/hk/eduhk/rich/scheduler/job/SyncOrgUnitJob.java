package hk.eduhk.rich.scheduler.job;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.banner.BanViewOrganizationUnit;
import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.scheduler.*;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * SyncDataJob 
 */
public class SyncOrgUnitJob extends CronJob
{ 
	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of SyncDataJob
	 */
	public SyncOrgUnitJob()
	{
		super();
	}


	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		String message = null;
		Boolean allSuccess = true;
		BannerLookupDAO bDao = BannerLookupDAO.getInstance();
		LookupValueDAO vDao = LookupValueDAO.getInstance();
		List<BanViewOrganizationUnit> banDataList = bDao.getViewBanOrganizationUnitList();
		List <LookupValue> newDataList = new ArrayList<LookupValue>();
		
		if (Constant.isLocalEnv())
			logMessage.append("(localhost) <br/><br/>");
		
		if (banDataList != null) {
			for (BanViewOrganizationUnit b:banDataList) {
				LookupValue v = new LookupValue();
				v.getPk().setLookup_type(b.getLookup_type());
				v.getPk().setLookup_code(b.getLookup_code());
				v.getPk().setLanguage(b.getLanguage());
				v.setDescription(b.getDescription());
				v.setEnabled_flag(b.getEnabled_flag());
				v.setParent_lookup_code(b.getParent_lookup_code());
				v.setPrint_order(b.getPrint_order());
				v.setUserstamp("RICH");
				newDataList.add(v);
			}
		}
		if (newDataList.size() > 0) {
			try
			{
				vDao.deleteLookupValueByLookupType("ORGANIZATION_UNIT_L1");
				vDao.deleteLookupValueByLookupType("ORGANIZATION_UNIT_L2");
				for (LookupValue d:newDataList) {
					vDao.updateLookupValue(d);
				}
			}
			catch (Exception e)
			{
				allSuccess = false;
				logger.log(Level.WARNING, "",e);
			}
			
		}
		if(allSuccess)
		{
			message = newDataList.size() + " row(s) has been inserted.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}
		else
		{
			message = "Please refer detail errors in System Logging.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/>" + message + " <br/>");
		}

		setLogMessage(logMessage.toString());	
	}

}