package hk.eduhk.rich.scheduler.job;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.logging.Level;
import java.util.logging.Logger;
import hk.eduhk.rich.scheduler.*;

import hk.eduhk.rich.util.PersistenceManager;

/**
 * PublishDataJob 
 */
public class PublishDataJob extends CronJob
{
	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of PublishDataJob
	 */
	public PublishDataJob()
	{
		super();
	}


	public void executeJob()
	{
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement stmt = null;
		try
		{
			conn = pm.getConnection();
			stmt = conn.prepareCall("{CALL RH_PUBLISH_DATA_P()}");
			stmt.execute();
		}	
		catch (Exception e) {
			logger.log(Level.INFO, "", e);
		}		
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
	}

}