package hk.eduhk.rich.scheduler;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.text.ParseException;

import javax.servlet.ServletContext;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.quartz.*;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.matchers.GroupMatcher;
import org.quartz.spi.MutableTrigger;


/**
 * SchedulerManager is a layer which encapsulates all the scheduler logic 
 * provided by Quartz in a single class. The configuration of Quartz 
 * scheduler is also done in this class.
 * 
 * SchedulerManager is a singleton class that only one instance exists 
 * in the web application.
 *
 */
public class SchedulerManager
{

	public static final String DEFAULT_JOB_GROUP = Scheduler.DEFAULT_GROUP;
	private static final String JOB_NOT_RUNNABLE_CRON_EXPRESSION = "0 0 0 * * ? 2100";

	public static final String PARAM_LOGGABLE 	= "loggable";
	public static final String PARAM_PARAMETERS = "parameters";

	private static SchedulerManager instance = null;

	private Scheduler schd = null;
	private ServletContext sCtx = null;
	private Logger logger = Logger.getLogger(getClass().getName());


	public static synchronized SchedulerManager getInstance(ServletContext sCtx)
	{
		if (instance == null) instance = new SchedulerManager(sCtx);
		return instance;
	}

	
	public static synchronized SchedulerManager getInstance()
	{
		return instance;
	}


	/**
	 * Constructor of SchedulerManager
	 * This cannot be called by other classes.
	 */
	private SchedulerManager(ServletContext sCtx)
	{
		this.sCtx = sCtx;
		
	    try
	    {
		    // Configure the scheduler    
			Properties props = new Properties();
			props.load(sCtx.getResourceAsStream("/WEB-INF/quartz.properties"));
			SchedulerFactory schedFact = new org.quartz.impl.StdSchedulerFactory(props);
			schd = schedFact.getScheduler();
	    }
	    catch (SchedulerException se)
	    {
			logger.log(Level.WARNING, "Cannot initialize scheduler", se);
	    }
	    catch (Exception e)
	    {
	    	e.printStackTrace();
	    }
	}
	
	
	public ServletContext getServletContext()
	{
		return sCtx;
	}


	public void start()
	{
		try
		{
			schd.start();
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "", se);
		}
	}


	public void startDelayed(int seconds)
	{
		try
		{
			schd.startDelayed(seconds);
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "", se);
		}
	}


	public void shutdown() 
	{
		try
		{
			// Wait for all executing jobs to complete and then shut down the scheduler
			schd.shutdown(true);
			
			schd = null;
			sCtx = null;
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "", se);
		}
	}
	

	/**
	 * Get the scheduled job list from the scheduler.
	 */
	public List<SchedulerJob> getJobList()
	{
		List<SchedulerJob> voList = new ArrayList<SchedulerJob> ();

		try
		{
			Set<JobKey> jobKeySet = schd.getJobKeys(GroupMatcher.jobGroupEquals(DEFAULT_JOB_GROUP));
			if (jobKeySet != null)
			{
				Iterator<JobKey> jobKeyIter = jobKeySet.iterator();
				while (jobKeyIter.hasNext())
				{
					JobKey jk = jobKeyIter.next();
					
					try
					{
						// Get the JobDetails class for the Class Name.
						JobDetail jd = schd.getJobDetail(jk);
						SchedulerJob jobObj = new SchedulerJob();
						jobObj.setJobGroup(jd.getKey().getGroup());
						jobObj.setJobName(jd.getKey().getName());
						jobObj.setClassName(jd.getJobClass().getName());
						jobObj.setDescription(jd.getDescription());
	
						// Get the loggable (additional parameter) from the JobDataMap
						JobDataMap jdm = jd.getJobDataMap();
						jobObj.setParameters((String) jdm.get(PARAM_PARAMETERS));
						jobObj.setLoggable((Boolean) jdm.get(PARAM_LOGGABLE));
						
						// Set the unique identifier in Quartz
						jobObj.setJobKey(jk);
	
						// Get the Trigger class for the Cron Expression, only take the information from the first trigger
						List<Trigger> jobTriggerList = (List<Trigger>) schd.getTriggersOfJob(jk);
						if (jobTriggerList != null && jobTriggerList.size() > 0)
						{
							for (Trigger jobTrigger : jobTriggerList)
							{
								if (jobTrigger instanceof CronTrigger)
								{
									CronTrigger ct = (CronTrigger) jobTrigger;
									jobObj.setCronExpression(ct.getCronExpression());
									break;
								}
							}
						}
	
						voList.add(jobObj);
					}
					catch (JobPersistenceException cnfe)
					{
						// Ignore this Exception, probably caused by ClassNotFoundException
						// The saved job class is not found by the Scheduler
						logger.log(Level.WARNING, "", cnfe);
					}
				}
			}
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "", se);
		}

		return voList;
	}

	
	public SchedulerJob getJob(String jobName)
	{
		SchedulerJob obj = null;
		
		if (jobName != null)
		{
			List<SchedulerJob> jobList = getJobList();
			
			// Find out the selected job in the list by the job name
			// Quartz API seems not providing method of getting a specific job
			// Iterating jobList is the only way to get the specific job
			for (SchedulerJob job : jobList)
			{
				if (StringUtils.equals(job.getJobName(), jobName))
				{
					obj = job;
					break;
				}
			}
		}
		
		return obj;
	}

	
	public SchedulerJob getFirstJobByClassName(String className)
	{
		SchedulerJob obj = null;
		
		if (className != null)
		{
			List<SchedulerJob> jobList = getJobList();
			
			// Find out the selected job in the list by the job name
			// Quartz API seems not providing method of getting a specific job
			// Iterating jobList is the only way to get the specific job
			for (SchedulerJob job : jobList)
			{
				if (StringUtils.equals(job.getClassName(), className))
				{
					obj = job;
					break;
				}
			}
		}
		
		return obj;
	}
	

	public void addJob(SchedulerJob vObj)
	{
		try
		{
			// Get the Class object from the job class name
			Class jobClass = null;
			try
			{
				if (vObj != null && vObj.getClassName() != null)
				  jobClass = Class.forName(vObj.getClassName());
			}
			catch (ClassNotFoundException cnfe)
			{
			}

			// Instantiate the JobBuilder and build the JobDetail
			JobKey jk = vObj.getJobKey();
			JobBuilder jb = JobBuilder.newJob(jobClass).withIdentity(jk);
			jb.storeDurably(true);
			jb.requestRecovery(true);
			JobDetail jd = jb.build();
			
			// JobDetail is interface which does not have setDescription method
			// Only the implementation class has it
			if (jd instanceof JobDetailImpl)
			{
				JobDetailImpl jdImpl = (JobDetailImpl) jd;
				jdImpl.setDescription(vObj.getDescription());
			}
						
			// Put the jobName in the JobDataMap for later retrieval (for logging).
			JobDataMap jdm = jd.getJobDataMap();
			jdm.put(PARAM_PARAMETERS, vObj.getParameters());
			jdm.put(PARAM_LOGGABLE, vObj.getLoggable());

			// Add the job to the scheduler.
			schd.addJob(jd, true);

			// Try parsing the provided cron expression
			MutableTrigger mt = null;
			if (!GenericValidator.isBlankOrNull(vObj.getCronExpression()))
			{
				try
				{
					mt = CronScheduleBuilder.cronSchedule(vObj.getCronExpression()).build();
					if (mt == null) throw new ParseException(null, 0);
				}
				
				// The provided cron expression is not parsable or is null.
				catch (ParseException pe)
				{
					mt = CronScheduleBuilder.cronSchedule(JOB_NOT_RUNNABLE_CRON_EXPRESSION).build();
				}
			}

			if (jd != null) 
			{
				List<Trigger> triggerList = (List<Trigger>) schd.getTriggersOfJob(vObj.getJobKey());
				if (triggerList != null)
				  for (Trigger trigger : triggerList)
					schd.unscheduleJob(trigger.getKey());
			}
			
			if (mt != null)
			{
				// Set the identity of the trigger
				mt.setJobKey(jk);
				mt.setKey(new TriggerKey(jk.getName(), jk.getGroup()));
				
				// Schedule the trigger 
				schd.scheduleJob(mt);
			}

			logger.log(Level.INFO, "Job Name=" + vObj.getJobName() + ", " +
									"Class Name=" + vObj.getClassName() + ", " +
									"Cron Exp=" + vObj.getCronExpression() + " is added to Scheduler.");
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot add job (jobName=" + vObj.getJobName() + ") to Scheduler", e);
		}	
	}



	public void deleteJob(SchedulerJob obj)
	{
		try
		{
			// Delete the job first if it is already here.
			schd.deleteJob(obj.getJobKey());
			logger.log(Level.INFO, "Job Name=" + obj.getJobName() + ", Class Name=" + obj.getClassName() + " is deleted from Scheduler.");
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "Cannot delete job (jobName=" + obj.getJobName() + ") from Scheduler", se);
		}		
	}
	
	
	/**
	 * Return whether the specified job is currently fired in the scheduler
	 * 
	 * @param obj
	 * @return
	 */
	public boolean isExecuting(SchedulerJob obj) 
	{
		boolean executing = false;
		
		if (obj != null)
		{
			try
			{
				List<JobExecutionContext> execList = schd.getCurrentlyExecutingJobs();
				if (execList != null)
				{
					for (JobExecutionContext exec : execList)
					{
						JobDetail jobDetail = exec.getJobDetail();
						JobKey jobKey = jobDetail.getKey();
						
						if (jobKey != null && jobKey.equals(obj.getJobKey()))
						{
							executing = true;
							break;
						}
					}
				}
			}
			catch (SchedulerException se)
			{
				logger.log(Level.WARNING, "", se);
			}
			
		}
		
		return executing;
	}
	


	/**
	 * Execute the job which associated with the given job ID. 
	 * A job must be first added to the scheduler before it can execute.
	 *
	 * @param jobName the job ID of the job.
	 */
	public boolean executeJob(SchedulerJob obj)
	{
		boolean isSuccess = false;

		if (obj != null)
		{
			try
			{
				// Execute the job immediately
				schd.triggerJob(obj.getJobKey());
				logger.log(Level.INFO, "Job Name=" + obj.getJobName() + " is executed.");
				isSuccess = true;
			}
			catch (SchedulerException se)
			{
				logger.log(Level.WARNING, "", se);
			}
		}

		return isSuccess;
	}


	/**
	 * Interrupt the running job which associated with the given job ID. 
	 *
	 * @param jobName the job ID of the job.
	 */
	public boolean interruptJob(SchedulerJob obj)
	{
		boolean isSuccess = false;

		try
		{
			schd.interrupt(obj.getJobKey());
			logger.log(Level.INFO, "Job Name=" + obj.getJobName() + " is interrupted.");
			isSuccess = true;
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "", se);
		}

		return isSuccess;
	}


	/**
	 * Check whether the given job is in the scheduler.
	 *
	 * @param jobName the job ID of the job.
	 * @return whether the given job is in the scheduler.
	 */
	public boolean isJobExist(SchedulerJob obj)
	{
		boolean isExist = false;
		try
		{
			if (obj != null)
			  isExist = schd.checkExists(obj.getJobKey());
		}
		catch (SchedulerException se)
		{
			logger.log(Level.WARNING, "", se);
		}		

		return isExist;
	}


	public Date getNextExecutionTime(String cronExp)
	{
		Date nextExecutionTime = null;

		try
		{
			CronExpression exp = new CronExpression(cronExp);
			nextExecutionTime = exp.getNextValidTimeAfter(new Date());
		}
		catch (Exception e)
		{
		}

		return nextExecutionTime;
	}


	public List<SchedulerJobLog> getSchedulerJobLogList(int num)
	{
		return SchedulerDAO.getInstance().getLatestSchedulerJobLogList(num);
	}


	public void updateSchedulerJobLog(SchedulerJobLog vObj) throws Exception
	{
		SchedulerDAO.getInstance().updateEntity(vObj); 
	}

}