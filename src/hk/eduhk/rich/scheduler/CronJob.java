package hk.eduhk.rich.scheduler;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.inject.Inject;

import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.rich.util.JPAUtils;


public abstract class CronJob extends SchedulableJob
{
	protected Logger logger = Logger.getLogger(this.getClass().getPackage().getName());
	
	
	private SchedulerJobLog logObj = null;

	// For logging to database
	private String logMessage = null;
	
	// Parameter Map
	private Map<String, String> paramMap = null;


	public CronJob()
	{
		super();
	}


	public void setLogMessage(String logMessage)
	{
		this.logMessage = logMessage;
	}


	public String getLogMessage()
	{
		return logMessage;
	}
	
		
	public String getParameter(String name)
	{
		if (paramMap == null)
		{
			String params = (String) getValue(SchedulerManager.PARAM_PARAMETERS);
			if (!GenericValidator.isBlankOrNull(params))
			{
				try
				{
					ObjectMapper objMapper = new ObjectMapper();
					TypeReference<HashMap<String,String>> typeRef = new TypeReference<HashMap<String,String>>() {};
					paramMap = objMapper.readValue(params, typeRef);
				}
				catch (Exception e)
				{
					logger.log(Level.WARNING, "Cannot parse parameters", e);
				}
			}
		}
		
		return (paramMap != null) ? paramMap.get(name) : null;
	}


	public boolean isLoggable()
	{
		Boolean b = (Boolean) getValue(SchedulerManager.PARAM_LOGGABLE);
		return (b != null) ? b.booleanValue() : true;
	}
	
	
	protected Boolean getJobSuccess()
	{
		return (logObj != null) ? (logObj.getSuccess() != null ? "Y".equals(logObj.getSuccess()): null) : null;
	}
	
	
	protected void setJobSuccess(boolean success)
	{
		if (logObj != null)
		{
			logObj.setSuccess(success ? true : false);
		}
	}


	public void execute()
	{
		SchedulerDAO daoInstance = SchedulerDAO.getInstance();
		logObj = new SchedulerJobLog();
		logObj.setJobGroup(getJobGroup());
		logObj.setJobName(getJobName());
		logObj.setClassName(getClass().getName());
		logObj.setParameters((String) getValue(SchedulerManager.PARAM_PARAMETERS));

		try
		{
			try
			{
				// Record the start time (creation_date)
				if (isLoggable()) logObj = daoInstance.updateSchedulerJobLog(logObj);

				executeJob();
				
				if (isInterrupted()) throw new InterruptedException("User interruption");

				// if logObj.getSuccess() != null, 
				// the success indicator is set from the implementation class
				if (logObj.getSuccess() == null) logObj.setSuccess(true);
				
				// Record the completion time (timestamp)
				logObj.setMessage(getLogMessage());
				if (isLoggable()) logObj = daoInstance.updateSchedulerJobLog(logObj);
			}
			catch (Exception e)
			{
				// Retrieve the whole stack trace from the Exception object
				if (e != null)
				{
					StringWriter sw = new StringWriter();
					e.printStackTrace(new PrintWriter(sw));
					String message = sw.toString();
					
					// Determine the maximum size of the that database field
					int maxLength = JPAUtils.getColumnLength(logObj.getClass(), "message");
					message = message.substring(0, Math.min(maxLength, message.length()));

					logObj.setSuccess(false);
					logObj.setMessage(message);
				}

				// Failure case, save the failure record even the loggable flag is set to false
				logObj = daoInstance.updateSchedulerJobLog(logObj);

				// Write the complete Exception to log file if it is not interrupted by user.
				if (!(e instanceof InterruptedException)) logger.log(Level.WARNING, "", e);
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Execution failure", e);
		}
	}


}