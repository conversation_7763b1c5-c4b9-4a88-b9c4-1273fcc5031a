package hk.eduhk.rich;

import java.lang.reflect.*;
import java.text.MessageFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.component.UIInput;
import javax.faces.context.FacesContext;
import javax.faces.validator.Validator;
import javax.faces.validator.ValidatorException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.rich.access.UserSessionView;
import hk.eduhk.rich.bundle.MessageBundle;
import hk.eduhk.rich.util.JSFUtils;


public abstract class BaseFacesValidator implements Validator
{
	
	protected static final Class[] VALIDATE_ARG_CLASSES = new Class[] {FacesContext.class, UIComponent.class, Object.class};
	
	protected static final Logger logger = Logger.getLogger(BaseFacesValidator.class.getName());
	
	protected UserSessionView userSessionView;

	
	// Used for validation in non-JSF environment
	private Locale locale;

	
	protected BaseFacesValidator()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		Map<String, Object> sessMap = (fCtx != null) ? fCtx.getExternalContext().getSessionMap() : null;
		userSessionView = (sessMap != null) ? (UserSessionView) sessMap.get(StringUtils.uncapitalize(UserSessionView.class.getSimpleName())) : null;
	}
	
	
	public Locale getLocale()
	{
		// If the locale is set, return it
		if (locale != null) return locale;
				
		// Otherwise, check whether any locale exist in FacesContext ViewRoot
		FacesContext fCtx = FacesContext.getCurrentInstance();			
		return (fCtx != null) ? fCtx.getViewRoot().getLocale() : Constant.DEFAULT_LOCALE;
	}

	
	public void setLocale(Locale locale)
	{
		this.locale = locale;
	}
	
	
	public ResourceBundle getResourceBundle()
	{
		ResourceBundle bundle = null;
		
		bundle = MessageBundle.getResourceBundle(getLocale());
		
		return bundle;
	}
	

	private String getContainerClientId(FacesContext fCtx, UIComponent component)
	{
		return  ":" + StringUtils.defaultString(component.getNamingContainer().getClientId());
	}
	
	
	protected void invokeValidation(String name, FacesContext fCtx, UIComponent comp, Object obj) throws ValidatorException
	{
		// Only perform validation if the field is marked as "required"
		// Ref: http://stackoverflow.com/questions/12819977/jsf-update-model-values-from-view-before-validation
		Object requiredObj = comp.getAttributes().get("validationRequired");
		Boolean validationRequired = (requiredObj != null) ? Boolean.valueOf(requiredObj.toString()) : null;
		if (validationRequired == null || validationRequired)
		{
			String methodName = null;
			
			try
			{
				methodName = "validate" + StringUtils.capitalize(name);
				Method method = findInheritedMethod(this.getClass(), methodName, VALIDATE_ARG_CLASSES);
				method.invoke(this, new Object[] {fCtx, comp, obj});
			}
			catch (Exception e)
			{
				if (e.getCause() != null && e.getCause() instanceof ValidatorException)
				{
					throw (ValidatorException) e.getCause();
				}
				else
				{
					// Unexpected exception thrown
					String message = "Cannot invoke method " + methodName + " from class " + this.getClass().getName();
					logger.log(Level.INFO, message, e);
				}
			}
		}
	}
	
	
	protected Method findInheritedMethod(Class cls, String methodName, Class[] args) throws NoSuchMethodException 
	{
		    if (cls == null)
		      throw new NoSuchMethodException("No class");
		    if (methodName == null)
		      throw new NoSuchMethodException("No method name");

		    Method method = null;
		    Method[] methods = cls.getDeclaredMethods();
		    for (int i = 0; i < methods.length && method == null; i++) {
		      if (methods[i].getName().equals(methodName))
		        method = methods[i];
		    }
		    if (method != null) {
		      return method;
		    } else
		      return findInheritedMethod(cls.getSuperclass(), methodName, args);
	}


	/**
	 * Get the value from the attribute objectName 
	 * <f:attribute name="objectName" value="xxx" />
	 * 
	 * @param component
	 * @return
	 */
	protected String getObjectName(UIComponent component)
	{
		return (String) component.getAttributes().get("objectName");
	}
	
	
	/**
	 * Get the refer Component by the attribute "referId" 
	 * 
	 * @param fCtx
	 * @param component
	 * @return
	 */
	protected UIComponent getReferComponent(UIComponent component)
	{
		UIComponent[] comps = getReferComponents(component);
		return (comps != null && comps.length > 0) ? comps[0] : null;
	}
	
	
	protected UIComponent[] getReferComponents(UIComponent component)
	{
		UIComponent[] comps = null;

		if (component != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String referIds = (String) component.getAttributes().get("referId");
			
			if (!GenericValidator.isBlankOrNull(referIds))
			{
				String containerId = getContainerClientId(fCtx, component);
				
				String[] referIdArray = referIds.split(" ");
				comps = new UIComponent[referIdArray.length];
				
				for (int n=0;n<referIdArray.length;n++)
				{
					String id = StringUtils.trim(referIdArray[n]);
					
					// Try search expression first
					String searchExp = containerId + ":" + StringUtils.trim(referIdArray[n]);
					comps[n] = fCtx.getViewRoot().findComponent(searchExp);
					
					if (comps[n] == null)
					{
						// This is for finding component 
						// which is wrapped in <ui:repeat>
						comps[n] = JSFUtils.findComponent(id);
						if (comps[n] == null)
						{
							logger.log(Level.WARNING, "Cannot find UIComponent (id=" + id + ")");
						}
					}
				}
			}
		}
		
		return comps;
	}
	
	
	protected Object getReferValue(UIComponent component)
	{
		Object[] objs = getReferValues(component);
		return (objs != null && objs.length > 0) ? objs[0] : null;
	}
	
	
	protected Object[] getReferValues(UIComponent component)
	{
		Object[] values = null;
		
		if (component != null)
		{
			// Get refer Component(s) from the UIComponent argument
			UIComponent[] comps = getReferComponents(component);
			if (comps != null && comps.length > 0)
			{
				// Extract the values from Components
				values = new Object[comps.length];
				for (int n=0;n<comps.length;n++)
				{
					if (comps[n] != null && comps[n] instanceof UIInput) 
					{
						values[n] = ((UIInput) comps[n]).getValue();
					}
				}
			}
		}
		
		return values;
	}
	
	
	protected Object getReferSubmittedValue(UIComponent component)
	{
		Object[] objs = getReferSubmittedValues(component);
		return (objs != null && objs.length > 0) ? objs[0] : null;
	}
	
	
	protected Object[] getReferSubmittedValues(UIComponent component)
	{
		Object[] values = null;
		
		if (component != null)
		{
			// Get refer Component(s) from the UIComponent argument
			UIComponent[] comps = getReferComponents(component);
			if (comps != null && comps.length > 0)
			{
				// Extract the values from Components
				values = new Object[comps.length];
				for (int n=0;n<comps.length;n++)
				{
					if (comps[n] != null && comps[n] instanceof UIInput) 
					{
						UIInput inputComp = (UIInput) comps[n];
						values[n] = inputComp.getSubmittedValue();
					}
				}
			}
		}
		
		return values;
	}
	
	
	@Override
	public void validate(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		// Get the attribute "validateField" 
		// to determine which field should be validated
		String validateField = (String) component.getAttributes().get("validateField");
		if (validateField != null)
		{
			String[] fields = validateField.split(" ");
			
			if (fields != null)
			{
				for (String field : fields)
				{
					field = StringUtils.trim(field);
					if (field.length() > 0) invokeValidation(field, fCtx, component, obj);
				}
			}
		}
	}
	
	
	/**
	 *  Check whether the input String is a valid JSON or not
	 *  
	 * @param valueObj
	 * @throws ValidatorException
	 */
	protected void validateJSON(Object valueObj) throws ValidatorException
	{
		try
		{
			String json = (String) valueObj;
			ObjectMapper mapper = new ObjectMapper();
			mapper.readTree(json);
		}
		catch (Exception e)
		{
			String msg = getResourceBundle().getString("msg.err.invalid.data.format.json");
			throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
		}
	}
	
	protected void validateMaxLength(Object valueObj, int maxLength, UIComponent component) throws ValidatorException
	{
		validateLength(valueObj, new Integer[] {null, maxLength}, component.getAttributes().get("label").toString());
	}
	
	protected void validateLength(Object valueObj, Integer[] range) throws ValidatorException
	{
		validateLength(valueObj, range, null);
	}
	
	
	protected void validateLength(Object valueObj, Integer[] range, String objName) throws ValidatorException
	{
		try
		{
			String value = valueObj.toString();
			/*if (range[0] != null && value.length() < range[0] || 
					range[1] != null && value.length() > range[1])
				{
					throw new StringIndexOutOfBoundsException();
				}*/
			byte[] valueBytes = value.getBytes("UTF-8");
			if (range[0] != null && valueBytes.length < range[0] || 
				range[1] != null && valueBytes.length > range[1])
			{
				throw new StringIndexOutOfBoundsException();
			}
		}
		catch (Exception e)
		{
			String msg = null;
			String param = (!GenericValidator.isBlankOrNull(objName)) ? ".x" : "";
			
			if (range[0] != null && range[1] != null)
			{
				if (range[0] != range[1])
				{
					msg = MessageFormat.format(getResourceBundle().getString("msg.err.length.between" + param), range[0], range[1], objName);
				}
				else
				{
					msg = MessageFormat.format(getResourceBundle().getString("msg.err.length.eq" + param), range[0], objName);
				}
			}
			else if (range[0] == null && range[1] != null)
			{
				msg = MessageFormat.format(getResourceBundle().getString("msg.err.length.gt" + param), range[1], objName);
			}
			else if (range[0] != null && range[1] == null)
			{
				msg = MessageFormat.format(getResourceBundle().getString("msg.err.length.lt" + param), range[0], objName);
			}
			
			throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
		}
	}


	protected void validateInt(Object valueObj, Integer[] range) throws ValidatorException
	{
		try
		{
			// Convert Object to int
			Integer value = null;
			if (valueObj instanceof String) value = Integer.parseInt((String) valueObj);
			  else value = ((Number) valueObj).intValue();
			
			if (value == null ||
				(range != null && range[0] != null && value < range[0]) ||
				(range != null && range[1] != null && value > range[1]))
			{
				throw new NumberFormatException();
			}
		}
		catch (Exception e)
		{
			if (range == null || (range[0] == null && range[1] == null))
			{
				String msg = getResourceBundle().getString("msg.err.value.integer.must");
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
			else if (range[0] != null && range[1] != null)
			{
				String msg = getResourceBundle().getString("msg.err.value.integer.between");
				msg = MessageFormat.format(msg, range[0], range[1]);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
			else if (range[0] == null && range[1] != null)
			{
				String msg = getResourceBundle().getString("msg.err.value.integer.le");
				msg = MessageFormat.format(msg, range[1]);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
			else if (range[0] != null && range[1] == null)
			{
				String msg = getResourceBundle().getString("msg.err.value.integer.ge");
				msg = MessageFormat.format(msg, range[0]);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
		}
	}
	

	protected void validateRange(Object valueObj, Number[] range) throws ValidatorException
	{
		// Convert Object to Comparable<Number>
		Comparable<Number> value = (valueObj instanceof Number && valueObj instanceof Comparable) ? (Comparable<Number>) valueObj : null;
		
		if (value == null)
		{
			String msg = getResourceBundle().getString("msg.err.value.number.must");
			throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
		}
		
		if (range[0] != null && range[1] == null)
		{
			if (value.compareTo(range[0]) < 0)
			{
				String msg = getResourceBundle().getString("msg.err.value.number.ge");
				msg = MessageFormat.format(msg, range[0]);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
		}
		else if (range[0] == null && range[1] != null)
		{
			if (value.compareTo(range[1]) > 0)
			{
				String msg = getResourceBundle().getString("msg.err.value.number.le");
				msg = MessageFormat.format(msg, range[1]);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
		}
		else if (range[0] != null && range[1] != null)
		{
			if (value.compareTo(range[0]) < 0 || value.compareTo(range[1]) > 0)
			{
				String msg = getResourceBundle().getString("msg.err.value.number.between");
				msg = MessageFormat.format(msg, range[0], range[1]);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
		}
	}
	
	
	protected void validateEmpty(Object valueObj) throws ValidatorException
	{
		validateEmpty(valueObj, null);
	}
	
	
	protected void validateEmpty(Object valueObj, String objName) throws ValidatorException
	{
		try
		{
			if (valueObj instanceof String)
			{
				if (((String) valueObj).length() == 0)
				{
					throw new NullPointerException();
				}
			}
			else if (valueObj instanceof Collection)
			{
				Collection col = (Collection) valueObj;
				if (col.isEmpty()) throw new NullPointerException();
			}
			else if (valueObj == null)
			{
				throw new NullPointerException();
			}
		}
		catch (Exception e)
		{
			String msg = null;
			
			if (objName != null)
			{
				msg = MessageFormat.format(getResourceBundle().getString("msg.err.require.x"), objName);
			}
			else
			{
				msg = getResourceBundle().getString("msg.err.require.value");
			}
			throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
		}
	}

	
}
