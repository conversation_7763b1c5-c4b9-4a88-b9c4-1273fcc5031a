package hk.eduhk.rich.cv;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.validator.GenericValidator;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.RowEditEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.UnselectEvent;
import org.primefaces.model.file.UploadedFile;

import com.google.common.base.Strings;
import com.google.common.collect.Iterables;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.staff.Assistant;
import hk.eduhk.rich.entity.staff.AssistantDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffInfo;
import hk.eduhk.rich.entity.staff.StaffProfileDisplay;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;

@ManagedBean(name = "cvDisplayView")
@ViewScoped
@SuppressWarnings("serial")
public class CvDisplayView extends BaseView
{
	private static Logger logger = Logger.getLogger(StaffProfileDisplay.class.getName());
	
	private Boolean showOutput;
	private Boolean showProject;
	private Boolean showAward;
	private Boolean showPatent;
	private Boolean showSubCatOutput;
	private Boolean showPhoto;
	private Boolean showKt;
	private Boolean showFundingSourceProject;
	private Boolean showEmploymentHistory;
	


	private String showWebsite;
	private String citation;
	private String cvStyle;
	private String websiteLink;
	private String homepageURL;
	//all tab items
	private List<StaffProfileDisplay> displayList;
	//all tab items where showInd = 'Y'
	private List<StaffProfileDisplay> displayOrderList;
	private StaffIdentity sIdentity = null;
	private String paramPid;
	private Boolean hasAccessRight = false;
	
	String[] tabItems = {"TAB_OUTPUT", "TAB_PROJECT", "TAB_AWARD", "TAB_PATENT","TAB_KT","TAB_EMPLOYMENT"};
	
	public void checkValid() throws IOException 
	{
		if (getHasAccessRight() == false) {
			String message = "You don't have access right.";	
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
	}	
	
	public Boolean getHasAccessRight()
	{
		StaffIdentity tmpStaff = getsIdentity();
		if (tmpStaff != null) {
			if (tmpStaff.getCn().equals(getCurrentUserId())) {
				hasAccessRight = true;
				this.paramPid = String.valueOf(tmpStaff.getPid());
			}
			if (paramPid != null) {
				Set<String> roleSet = AccessDAO.getCacheInstance().getRoleIdSetByUserId(getLoginUserId());
				if(roleSet.contains("rdoAdmin"))
					hasAccessRight = true;
				AssistantDAO aDao = AssistantDAO.getInstance();
				List<Assistant> asstList = aDao.getAsstListByAcadStaffAndStatus(Integer.parseInt(paramPid), "APPROVED");
				for (Assistant a:asstList) {
					if (a.getPk().getAssistant_id().equals(getCurrentUserId())) {
						hasAccessRight = true;
					}
				}
			}
		}
		return hasAccessRight;
	}


	public String getParamPid()
	{
		if (Strings.isNullOrEmpty(paramPid)) {
			StaffDAO dao = StaffDAO.getInstance();
			StaffIdentity sIdentity = dao.getStaffDetailsByUserId(getCurrentUserId());
			this.paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
		}
		return paramPid;
	}


	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}


	public StaffIdentity getsIdentity()
	{
		if (sIdentity == null) {
			StaffDAO dao = StaffDAO.getInstance();
			if (paramPid != null) {
				sIdentity = dao.getStaffDetailsByPid(Integer.parseInt(paramPid));
			}else {
				sIdentity = dao.getStaffDetailsByUserId(getCurrentUserId());
				if (paramPid == null) {
					paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
				}
			}
		}
		return sIdentity;
	}

	public String getWebsiteLink()
	{
		if (getShowWebsite()!= null) {
			if (showWebsite.equals("CVPAGE")) {
				websiteLink = getCvLink();
			}else if (showWebsite.equals("HOMEPAGE")) {
				websiteLink = getHomepageURL();
			}else {
				websiteLink = "N/A";
			}
		}else {
			websiteLink = "N/A";
		}
		return websiteLink;
	}

	
	public void setWebsiteLink(String websiteLink)
	{
		this.websiteLink = websiteLink;
	}

	
	public String getHomepageURL()
	{
		if (homepageURL == null) {
			homepageURL = getHomepageLink();
		}
		return homepageURL;
	}

	
	public void setHomepageURL(String homepageURL)
	{
		this.homepageURL = homepageURL;
	}

	public String getCvLink()
	{
		String cvLink = "";
		if (sIdentity != null) {
			SysParamDAO sDao = SysParamDAO.getInstance();
			String cvUrl = sDao.getSysParamValueByCode("BASE_URL_CV");
			cvLink = cvUrl + sIdentity.getPid()+"&name="+sIdentity.getFullname();
		}
		return cvLink;
	}
	
	public String getHomepageLink()
	{
		String homepageLink = "";
		if (getParamPid() != null) {
			CvDAO dao = CvDAO.getInstance();
			StaffInfo sInfo = dao.getStaffInfo(Integer.valueOf(paramPid));
			if (sInfo != null) {
				homepageLink = sInfo.getUrl();
			}
		}
		return homepageLink;
	}
	
	
	public Boolean getShowOutput()
	{
		if (showOutput == null) {
			showOutput = false;
			displayList = getDisplayList();
			for (StaffProfileDisplay d:displayList) {
				if (d.getPk().getItemCode().equals(tabItems[0])) {
					showOutput = (d.getShowInd().equals("Y"))?true:false;
				}
			}
		}
		return showOutput;
	}

	
	public void setShowOutput(Boolean showOutput)
	{
		this.showOutput = showOutput;
		//displayOrderList = null;
		if (getDisplayOrderList() != null) {
			if (showOutput == false) {
				displayOrderList.removeIf((StaffProfileDisplay d) ->  tabItems[0].equals((d.getPk().getItemCode())));
			}else {
				List<StaffProfileDisplay> tmpList = displayOrderList.stream()
																	.filter(y -> tabItems[0].equals(y.getPk().getItemCode()))
																	.collect(Collectors.toList());
				if (tmpList.isEmpty()) {
					List<StaffProfileDisplay> tmpDisplayList = displayList.stream()
																		.filter(y -> tabItems[0].equals(y.getPk().getItemCode()))
																		.collect(Collectors.toList());
					displayOrderList.add(tmpDisplayList.get(0));
				}
			}
		}
	}

	
	public Boolean getShowProject()
	{
		if (showProject == null) {
			showProject = false;
			displayList = getDisplayList();
			for (StaffProfileDisplay d:displayList) {
				if (d.getPk().getItemCode().equals(tabItems[1])) {
					showProject = (d.getShowInd().equals("Y"))?true:false;
				}
			}
		}
		return showProject;
	}

	
	public void setShowProject(Boolean showProject)
	{
		this.showProject = showProject;
		//displayOrderList = null;
		if (getDisplayOrderList() != null) {
			if (showProject == false) {
				displayOrderList.removeIf((StaffProfileDisplay d) ->  tabItems[1].equals((d.getPk().getItemCode())));
			}else {
				List<StaffProfileDisplay> tmpList = displayOrderList.stream()
																	.filter(y -> tabItems[1].equals(y.getPk().getItemCode()))
																	.collect(Collectors.toList());
				if (tmpList.isEmpty()) {
					List<StaffProfileDisplay> tmpDisplayList = displayList.stream()
																		.filter(y -> tabItems[1].equals(y.getPk().getItemCode()))
																		.collect(Collectors.toList());
					displayOrderList.add(tmpDisplayList.get(0));
				}
			}
		}
	}

	
	public Boolean getShowAward()
	{
		if (showAward == null) {
			showAward = false;
			displayList = getDisplayList();
			for (StaffProfileDisplay d:displayList) {
				if (d.getPk().getItemCode().equals(tabItems[2])) {
					showAward = (d.getShowInd().equals("Y"))?true:false;
				}
			}
		}
		return showAward;
	}

	
	public void setShowAward(Boolean showAward)
	{
		this.showAward = showAward;
		//displayOrderList = null;
		if (getDisplayOrderList() != null) {
			if (showAward == false) {
				displayOrderList.removeIf((StaffProfileDisplay d) ->  tabItems[2].equals((d.getPk().getItemCode())));
			}else {
				List<StaffProfileDisplay> tmpList = displayOrderList.stream()
																	.filter(y -> tabItems[2].equals(y.getPk().getItemCode()))
																	.collect(Collectors.toList());
				if (tmpList.isEmpty()) {
					List<StaffProfileDisplay> tmpDisplayList = displayList.stream()
																		.filter(y -> tabItems[2].equals(y.getPk().getItemCode()))
																		.collect(Collectors.toList());
					displayOrderList.add(tmpDisplayList.get(0));
				}
			}
		}
	}

	
	public Boolean getShowPatent()
	{
		if (showPatent == null) {
			showPatent = false;
			displayList = getDisplayList();
			for (StaffProfileDisplay d:displayList) {
				if (d.getPk().getItemCode().equals(tabItems[3])) {
					showPatent = (d.getShowInd().equals("Y"))?true:false;
				}
			}
		}
		return showPatent;
	}

	
	public void setShowPatent(Boolean showPatent)
	{
		this.showPatent = showPatent;
		//displayOrderList = null;
		if (getDisplayOrderList() != null) {
			if (showPatent == false) {
				displayOrderList.removeIf((StaffProfileDisplay d) ->  tabItems[3].equals((d.getPk().getItemCode())));
			}else {
				List<StaffProfileDisplay> tmpList = displayOrderList.stream()
																	.filter(y -> tabItems[3].equals(y.getPk().getItemCode()))
																	.collect(Collectors.toList());
				if (tmpList.isEmpty()) {
					List<StaffProfileDisplay> tmpDisplayList = displayList.stream()
																		.filter(y -> tabItems[3].equals(y.getPk().getItemCode()))
																		.collect(Collectors.toList());
					displayOrderList.add(tmpDisplayList.get(0));
				}
			}
		}
	}
	
	public Boolean getShowEmploymentHistory()
	{
		if (showEmploymentHistory == null) {
			showEmploymentHistory = false;
			displayList = getDisplayList();
			for (StaffProfileDisplay d:displayList) {
				if (d.getPk().getItemCode().equals(tabItems[5])) {
					showEmploymentHistory = (d.getShowInd().equals("Y"))?true:false;
				}
			}
		}
		return showEmploymentHistory;
	}

	
	public void setShowEmploymentHistory(Boolean showEmploymentHistory)
	{
		this.showEmploymentHistory = showEmploymentHistory;
		//displayOrderList = null;
		if (getDisplayOrderList() != null) {
			if (showEmploymentHistory == false) {
				displayOrderList.removeIf((StaffProfileDisplay d) ->  tabItems[5].equals((d.getPk().getItemCode())));
			}else {
				List<StaffProfileDisplay> tmpList = displayOrderList.stream()
																	.filter(y -> tabItems[5].equals(y.getPk().getItemCode()))
																	.collect(Collectors.toList());
				if (tmpList.isEmpty()) {
					List<StaffProfileDisplay> tmpDisplayList = displayList.stream()
																		.filter(y -> tabItems[5].equals(y.getPk().getItemCode()))
																		.collect(Collectors.toList());
					displayOrderList.add(tmpDisplayList.get(0));
				}
			}
		}
	} 

	
	
	public Boolean getShowSubCatOutput()
	{
		if (showSubCatOutput == null && getParamPid() != null) {
			showSubCatOutput = false;
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "CAT_OUTPUT");
			if (obj != null) {
				showSubCatOutput = (obj.getShowInd().equals("Y"))?true:false;
			}
		}
		return showSubCatOutput;
	}

	
	public void setShowSubCatOutput(Boolean showSubCatOutput)
	{
		this.showSubCatOutput = showSubCatOutput;
	}

	
	public Boolean getShowPhoto()
	{
		if (showPhoto == null && getParamPid() != null) {
			showPhoto = true;
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_PHOTO");
			if (obj != null) {
				showPhoto = (obj.getShowInd().equals("Y"))?true:false;
			}
		}
		return showPhoto;
	}

	
	public void setShowPhoto(Boolean showPhoto)
	{
		this.showPhoto = showPhoto;
	}

	
	public Boolean getShowKt()
	{
		if (showKt == null) {
			showKt = false;
			displayList = getDisplayList();
			for (StaffProfileDisplay d:displayList) {
				if (d.getPk().getItemCode().equals(tabItems[4])) {
					showKt = (d.getShowInd().equals("Y"))?true:false;
				}
			}
		}
		return showKt;
	}

	
	public void setShowKt(Boolean showKt)
	{
		this.showKt = showKt;
		//displayOrderList = null;
		if (getDisplayOrderList() != null) {
			if (showKt == false) {
				displayOrderList.removeIf((StaffProfileDisplay d) ->  tabItems[4].equals((d.getPk().getItemCode())));
			}else {
				List<StaffProfileDisplay> tmpList = displayOrderList.stream()
																	.filter(y -> tabItems[4].equals(y.getPk().getItemCode()))
																	.collect(Collectors.toList());
				if (tmpList.isEmpty()) {
					List<StaffProfileDisplay> tmpDisplayList = displayList.stream()
																		.filter(y -> tabItems[4].equals(y.getPk().getItemCode()))
																		.collect(Collectors.toList());
					displayOrderList.add(tmpDisplayList.get(0));
				}
			}
		}
	}

	public Boolean getShowFundingSourceProject()
	{
		if (showFundingSourceProject == null && getParamPid() != null) {
			showFundingSourceProject = false;
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_FUNDING");
			if (obj != null) {
				showFundingSourceProject = (obj.getShowInd().equals("Y"))?true:false;
			}
		}
		return showFundingSourceProject;
	}

	
	public void setShowFundingSourceProject(Boolean showFundingSourceProject)
	{
		this.showFundingSourceProject = showFundingSourceProject;
	}

    
    
	public String getShowWebsite()
	{
		if (showWebsite == null && getParamPid() != null) {
			showWebsite = "CVPAGE";
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_WEBSITE");
			if (obj != null) {
				if (obj.getShowInd().equals("N")) {
					showWebsite = "NA";
				}else {
					showWebsite= obj.getDisplayType();
				}
			}
		}
		return showWebsite;
	}

	
	public void setShowWebsite(String showWebsite)
	{
		this.showWebsite = showWebsite;
	}

	
	public String getCitation()
	{
		if (citation == null && getParamPid() != null) {
			citation = "APA";
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_CITATION");
			if (obj != null) {
				citation= obj.getDisplayType();
			}
		}
		return citation;
	}

	
	
	public String getCvStyle()
	{
		if (cvStyle == null && getParamPid() != null) {
			cvStyle = "1";
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "CV_STYLE");
			if (obj != null) {
				cvStyle= obj.getDisplayType();
			}
		}
		return cvStyle;
	}

	
	public void setCvStyle(String cvStyle)
	{
		this.cvStyle = cvStyle;
	}

	public void setCitation(String citation)
	{
		this.citation = citation;
	}

	public List<StaffProfileDisplay> getDisplayList(){
		if (displayList == null && getParamPid() != null) {
			CvDAO cvDao = CvDAO.getInstance();
			int pid = Integer.valueOf(paramPid);
			displayList = cvDao.getStaffProfileDisplayList(pid, "TAB");
			for(String item:tabItems) {
				if (!displayList.stream().anyMatch(o -> o.getPk().getItemCode().equals(item))){
					StaffProfileDisplay tmp = new StaffProfileDisplay();
					tmp.getPk().setPid(pid);
					tmp.getPk().setItemCode(item);
					tmp.setItemType("TAB");
					tmp.setShowInd("N");
					displayList.add(tmp);
				}
			}
		}
		return displayList;
	}

	
	public void setDisplayList(List<StaffProfileDisplay> displayList)
	{
		this.displayList = displayList;
	}

	
	public List<StaffProfileDisplay> getDisplayOrderList()
	{
		if (displayOrderList == null && getParamPid() != null) {
			displayOrderList = new ArrayList<StaffProfileDisplay>();
			displayList = getDisplayList();
			for(StaffProfileDisplay d:displayList) {
				if (getShowOutput() && d.getPk().getItemCode().equals(tabItems[0])){
					displayOrderList.add(d);
				}
				if (getShowProject() && d.getPk().getItemCode().equals(tabItems[1])) {
					displayOrderList.add(d);
				}
				if (getShowAward() && d.getPk().getItemCode().equals(tabItems[2])) {
					displayOrderList.add(d);
				}
				if (getShowPatent() && d.getPk().getItemCode().equals(tabItems[3])) {
					displayOrderList.add(d);
				}
				if (getShowKt() && d.getPk().getItemCode().equals(tabItems[4])) {
					displayOrderList.add(d);
				}
				
				if (getShowEmploymentHistory() && d.getPk().getItemCode().equals(tabItems[5])) {
					displayOrderList.add(d);
				}
			}
		}
		return displayOrderList;
	}

	
	public void setDisplayOrderList(List<StaffProfileDisplay> displayOrderList)
	{
		this.displayOrderList = displayOrderList;
	}
    
    public String updateStaffProfileDisplayList()
    {
    	String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (paramPid != null) {
    		int pid = Integer.valueOf(paramPid);
			try {
	    		CvDAO cvDao = CvDAO.getInstance();
	    		displayOrderList = getDisplayOrderList();
	    		displayList = getDisplayList();
	    		if (displayOrderList != null && !displayOrderList.isEmpty()) {
		    		for (int i = 0; i < displayOrderList.size(); i++) {
		    			StaffProfileDisplay updateObj = displayOrderList.get(i);
		    			updateObj.setDisplayOrder(i);
		    			updateObj.setShowInd("Y");
		    			updateObj.setUserstamp(getLoginUserId());
		    			updateObj = cvDao.updateStaffProfileDisplay(updateObj);
		    		}
		    		displayList.removeAll(displayOrderList);
	    		}
	    		for (StaffProfileDisplay d:displayList) {
	    			d.setDisplayOrder(99);
	    			d.setShowInd("N");
	    			d.setUserstamp(getLoginUserId());
	    			d = cvDao.updateStaffProfileDisplay(d);
	    		}
	    		displayList = null;
	    		
	    		//update Sub-category of Outputs Information
	    		StaffProfileDisplay subCatOutput = cvDao.getStaffProfileDisplay(pid, "CAT_OUTPUT");
	    		if (subCatOutput == null) {
	    			subCatOutput = new StaffProfileDisplay();
	    			subCatOutput.getPk().setPid(pid);
	    			subCatOutput.getPk().setItemCode("CAT_OUTPUT");
	    			subCatOutput.setItemType("CAT");
	    		}
	    		subCatOutput.setShowInd(showSubCatOutput?"Y":"N");
	    		subCatOutput.setUserstamp(getLoginUserId());
	    		subCatOutput = cvDao.updateStaffProfileDisplay(subCatOutput);
	    		
	    		//update photo
	    		StaffProfileDisplay photoObj = cvDao.getStaffProfileDisplay(pid, "DATA_PHOTO");
	    		if (photoObj == null) {
	    			photoObj = new StaffProfileDisplay();
	    			photoObj.getPk().setPid(pid);
	    			photoObj.getPk().setItemCode("DATA_PHOTO");
	    			photoObj.setItemType("DATA");
	    		}
	    		photoObj.setShowInd(showPhoto?"Y":"N");
	    		photoObj.setUserstamp(getLoginUserId());
	    		photoObj = cvDao.updateStaffProfileDisplay(photoObj);
	    		
	    		//update fundingSourceProject
	    		/*StaffProfileDisplay fundingSourceProject = cvDao.getStaffProfileDisplay(pid, "DATA_FUNDING");
	    		if (fundingSourceProject == null) {
	    			fundingSourceProject = new StaffProfileDisplay();
	    			fundingSourceProject.getPk().setPid(pid);
	    			fundingSourceProject.getPk().setItemCode("DATA_FUNDING");
	    			fundingSourceProject.setItemType("DATA");
	    		}
	    		fundingSourceProject.setShowInd(showFundingSourceProject?"Y":"N");
	    		fundingSourceProject.setUserstamp(getLoginUserId());
	    		fundingSourceProject = cvDao.updateStaffProfileDisplay(fundingSourceProject);*/
	    		
	    		//update website
	    		StaffProfileDisplay websiteObj = cvDao.getStaffProfileDisplay(pid, "DATA_WEBSITE");
	    		if (websiteObj == null) {
	    			websiteObj = new StaffProfileDisplay();
	    			websiteObj.getPk().setPid(pid);
	    			websiteObj.getPk().setItemCode("DATA_WEBSITE");
	    			websiteObj.setItemType("DATA");
	    		}
	    		websiteObj.setShowInd(!showWebsite.equals("NA")?"Y":"N");
	    		websiteObj.setDisplayType(showWebsite);
	    		websiteObj.setUserstamp(getLoginUserId());
	    		websiteObj = cvDao.updateStaffProfileDisplay(websiteObj);
	    		
	    		if (showWebsite.equals("HOMEPAGE") && getHomepageURL() != null) {
    				if (!homepageURL.equals(getHomepageLink())) {
	    				StaffInfo sInfo = cvDao.getStaffInfo(pid);
		    			if (sInfo != null) {
		    				sInfo.setUrl(homepageURL);
		    				StaffDAO sDao = StaffDAO.getInstance();
		    				sDao.updateStaffInfo(sInfo);
		    			}
	    			}
	    		}
	    		
	    		//update citation
	    		StaffProfileDisplay citationObj = cvDao.getStaffProfileDisplay(pid, "DATA_CITATION");
	    		if (citationObj == null) {
	    			citationObj = new StaffProfileDisplay();
	    			citationObj.getPk().setPid(pid);
	    			citationObj.getPk().setItemCode("DATA_CITATION");
	    			citationObj.setItemType("DATA");
	    		}
	    		citationObj.setShowInd("Y");
	    		citationObj.setDisplayType(citation);
	    		citationObj.setUserstamp(getLoginUserId());
	    		citationObj = cvDao.updateStaffProfileDisplay(citationObj);
	    		
	    		//update cv style
	    		StaffProfileDisplay cvStyleObj = cvDao.getStaffProfileDisplay(pid, "CV_STYLE");
	    		if (cvStyleObj == null) {
	    			cvStyleObj = new StaffProfileDisplay();
	    			cvStyleObj.getPk().setPid(pid);
	    			cvStyleObj.getPk().setItemCode("CV_STYLE");
	    			cvStyleObj.setItemType("STYLE");
	    		}
	    		cvStyleObj.setShowInd("Y");
	    		cvStyleObj.setDisplayType(cvStyle);
	    		cvStyleObj.setUserstamp(getLoginUserId());
	    		cvStyleObj = cvDao.updateStaffProfileDisplay(cvStyleObj);
	    		
	    		message = "msg.success.update.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Profile Preferences");
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (OptimisticLockException ole)
			{
				message = bundle.getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
	    	catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
				message = bundle.getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
    	}
    	return redirect("manageProfile")+"&pid="+paramPid;
    }
}
