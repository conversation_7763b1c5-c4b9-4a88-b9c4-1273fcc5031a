package hk.eduhk.rich.cv;

import java.util.ArrayList;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.*;

import org.omnifaces.util.selectitems.SelectItemsUtils;
import org.primefaces.component.orderlist.OrderList;
import org.primefaces.component.picklist.PickList;
import org.primefaces.model.DualListModel;

import hk.eduhk.rich.entity.staff.StaffProfileDisplay;


@FacesConverter("cvOrderListConverter")
public class CvOrderListConverter implements Converter
{
	
	@Override
	public String getAsString(FacesContext context, UIComponent component, Object value) {
		
	    String id = (value instanceof StaffProfileDisplay) ? ((StaffProfileDisplay) value).getPk().getItemCode() : null;
	    return (id != null) ? id : null;
	}
	
	@Override
	public Object getAsObject(FacesContext arg0, UIComponent arg1, String value) {
		Object ret = null;
		
		Object list = ((OrderList) arg1).getValue();
		ArrayList<StaffProfileDisplay> al = (ArrayList<StaffProfileDisplay>) list;
		
        for (Object o : al) {
            String id = "" + ((StaffProfileDisplay) o).getPk().getItemCode();
            if (value.equals(id)) {
                ret = o;
                break;
            }
        }

		return ret;
	}
    
}
