package hk.eduhk.rich.cv;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Reader;
import java.math.BigInteger;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.SQLException;
import java.text.DateFormatSymbols;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.imageio.ImageIO;

import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.http.client.HttpClient;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.docx4j.XmlUtils;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.omnifaces.util.Faces;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTabStop;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTabJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.primefaces.model.DefaultStreamedContent;

import com.google.common.base.Strings;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.BaseRI;
import hk.eduhk.rich.entity.BaseRIFull;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.award.*;
import hk.eduhk.rich.entity.employment.EmploymentHistory;
import hk.eduhk.rich.entity.employment.EmploymentHistory_edit;
import hk.eduhk.rich.entity.employment.employmentHistoryDao;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;
import hk.eduhk.rich.entity.form.KtFormSummary;
import hk.eduhk.rich.entity.patent.Patent;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.project.*;
import hk.eduhk.rich.entity.publication.*;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;

@ManagedBean(name = "cvView")
@ViewScoped
@SuppressWarnings("serial")
public class CvView extends ManageRIView
{
	private String paramPid;
	
	private List<CdcfRptPeriod> cdcfPeriodList = null;
	private List<FundSource> fundSource_list;
	
	private List<CdcfRptPeriod> selectedCdcfPeriodList;
	private String[] selectedCdcfPeriods;
	private String[] selectedOutputTypes;
	private String[] selectedFundingSources;
	private Date selectedStartDate;
	private Date selectedEndDate;
	private String remarks;
	
	private InternetUserInfo iUserInfo = null;
	private List<InternetUserInfo> iUserInfoList = null;
	private StaffIdentity sIdentity = null;
	private StaffInfo sInfo = null;
	
	private OutputHeader_P selectedOutputHeader_p;
	private List<OutputDetails_P> outputList;
	private List<Award> awardList = null;
	private List<Patent> patentList = null;
	private List<KtFormSummary> ktList = null;
	private List<ProjectDetails_P> projectList = null;
	private List<String> selectedOutputTypeList = null;
	private List<String> selectedKtTypeList = null;
	
	private List<String> displayOrderList;
	
	private List<EmploymentHistory_edit> employmentHistList_edit = null;
	
	private String citation;
	private String cvStyle ;
	private String websiteLink ;
	private String showWebsite ;
	private Boolean showPhoto;
	
	//Export my data options
	private String[] selectedOptions;
	private boolean selectAllExportData;
	private List<SelectItem> exportDataList;
	private String[] exportDataValues = {"Photo", "Contact","Personal Profile","Career Overview","Research Interests","Teaching Interests","External Appointments","Other Activities","Research Outputs","Projects","Prizes and Awards","Patents","KT Activities"};
	private String[] exportDataKeys = {"photo","contact","profile","job","researchInt","teachingInt","ExtAppt","othAct","output","project","award","patent","kt"};

	private SysParamDAO sDao = SysParamDAO.getInstance();
	private PublicationDAO pDao = PublicationDAO.getInstance();
	private FormDAO fDao = FormDAO.getInstance();
	private CdcfRptDAO cdcfRptDao = CdcfRptDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	
	public String getParamPid()
	{
		return paramPid;
	}
	
	public void setParamPid(String paramPid)
	{	
		this.paramPid = paramPid;
	}
	
	
	public List<EmploymentHistory_edit> getEmploymentHistList_edit()
	{
		if (employmentHistList_edit == null) {
			employmentHistoryDao dao = employmentHistoryDao.getInstance();
			int user_pid = Integer.parseInt(paramPid);
			employmentHistList_edit = dao.getEmploymentHistory_edit(user_pid);
		}
		return employmentHistList_edit;
	}
	
	public void setEmploymentHistList_edit(List<EmploymentHistory_edit> employmentHistList_edit)
	{
		this.employmentHistList_edit = employmentHistList_edit;
	}

	public String getStaffNo() throws IOException
	{		
		String staffNo = null;
		if (paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			staffNo = dao.getStaffNo(convertIntoNumeric(paramPid));
			//System.out.println("staffNo:"+staffNo);
		}
		return staffNo;
	}


	public StaffIdentity getsIdentity() throws IOException {
		if (sIdentity == null) {
			CvDAO dao = CvDAO.getInstance();
			sIdentity = dao.getStaffIdentity(getStaffNo());
		}
		return sIdentity;
	}

	public StaffInfo getsInfo() throws IOException {
		if (sInfo == null && paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			sInfo = dao.getStaffInfo(convertIntoNumeric(paramPid));
			if (sInfo != null) {
				if (sInfo.getResearch_interest()!=null) {
					sInfo.setResearch_interest(sInfo.getResearch_interest().replace("&#160;"," "));
				}
				if (sInfo.getTeaching_interest()!=null) {
					sInfo.setTeaching_interest(sInfo.getTeaching_interest().replace("&#160;"," "));
				}
				if (sInfo.getExt_appt()!=null) {
					sInfo.setExt_appt(sInfo.getExt_appt().replace("&#160;"," "));
				}
				if (sInfo.getProfile()!=null) {
					sInfo.setProfile(sInfo.getProfile().replace("&#160;"," "));
				}
				//website
				if (getShowWebsite()!= null) {
					if (showWebsite.equals("CVPAGE")) {
						websiteLink = "https://rich.eduhk.hk/web/person.xhtml?pid=" + sInfo.getPid();
						if (getsIdentity() != null) {
							websiteLink += "&amp;name=" + sIdentity.getFullname();
						}
					}
					if (showWebsite.equals("HOMEPAGE")) {
						if (sInfo.getUrl() != null) {
							if (sInfo.getUrl().contains("http://") || sInfo.getUrl().contains("https://")) {
								websiteLink = sInfo.getUrl();
							}else {
								websiteLink = "http://"+sInfo.getUrl();
							}
						}
					}
				}
				//photo
				if (getShowPhoto() != null) {
					if (!showPhoto) {
						sInfo.setPhotoFile("");
					}
				}
				/*sInfo.setResearch_interest(sInfo.getResearch_interest().replaceAll("\\<.*?>",""));
				sInfo.setTeaching_interest(sInfo.getTeaching_interest().replaceAll("\\<.*?>",""));
				sInfo.setExt_appt(sInfo.getExt_appt().replaceAll("\\<.*?>",""));
				sInfo.setProfile(sInfo.getProfile().replaceAll("\\<.*?>",""));*/
				
			}
		}
		return sInfo;	
	}
	
	public String getShowWebsite()
	{
		if (showWebsite == null && getParamPid() != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_WEBSITE");
			if (obj != null) {
				if (obj.getShowInd().equals("N")) {
					showWebsite = null;
				}else {
					showWebsite= obj.getDisplayType();
				}
			}
		}
		return showWebsite;
	}
	
	public void setShowWebsite(String showWebsite)
	{
		this.showWebsite = showWebsite;
	}
	
	public InternetUserInfo getiUserInfo() throws IOException {
		if (iUserInfo == null && paramPid != null) {
			List<InternetUserInfo>tmpList = getiUserInfoList();
			String phone = "";
			String fax = "";
			String email = "";
			if (tmpList != null) {
				if (tmpList.size() > 0) {
					iUserInfo = tmpList.get(0);
					phone = (iUserInfo.getPhone() != "" && iUserInfo.getPhone() != null)?"(852) " + iUserInfo.getPhone():"";
					fax = (iUserInfo.getFax() != "" && iUserInfo.getFax() != null)?"(852) " + iUserInfo.getFax():"";
					email = (iUserInfo.getEmail() != "" && iUserInfo.getEmail() != null)?iUserInfo.getEmail():"";
				}
				for (InternetUserInfo t : tmpList) {
					if (t.getPhone() != "" && t.getPhone() != null) {
						if (!phone.contains(t.getPhone())) {
							if (phone != "") {
								phone += " / ";
							}
							phone += "(852) "+t.getPhone();
						}
					}
					if (t.getFax() != "" && t.getFax() != null) {
						if (!fax.contains(t.getFax())) {
							if (fax != "") {
								fax += " / ";
							}
							fax += "(852) "+t.getFax();
						}
					}
					if (t.getEmail() != "" && t.getEmail() != null) {
						if (!email.contains(t.getEmail())) {
							if (email != "") {
								email += " / ";
							}
							email += t.getEmail();
						}
					}
				}
			}
			iUserInfo.setPhone(phone);
			iUserInfo.setFax(fax);
			iUserInfo.setEmail(email);
		}
		return iUserInfo;	
	}	
	
	public List<InternetUserInfo> getiUserInfoList() throws IOException {
		if (iUserInfoList == null && paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			iUserInfoList = dao.getInternetUserInfo(convertIntoNumeric(paramPid));
			if (iUserInfoList == null) {
				redirectToPure();
			}
		}
		return iUserInfoList;
	}
	

	
	public List<KtFormSummary> getKtList() throws IOException
	{
		if (ktList == null && paramPid != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "TAB_KT");
			boolean display = (obj.getShowInd().equals("Y"))?true:false;
			if (display) {
				ktList = fDao.getKtFormSummaryListByStaff(getStaffNo(), "P", null, null, 1, null);
			}
		}
		return ktList;
	}

	
	public void setKtList(List<KtFormSummary> ktList)
	{
		this.ktList = ktList;
	}
	
	public String getKtFormName(String formCode) {
		String result = "";
		KtForm f = fDao.getKtForm(formCode);
		if (f != null) {
			result = f.getForm_full_desc();
		}
		return result;
	}
	
	public List<String> getSelectedKtTypeList() throws SQLException, IOException
	{
		if (selectedKtTypeList == null && paramPid != null) {
			selectedKtTypeList = new ArrayList<>();
			if (getKtList() != null) {
				HashSet unique=new HashSet();
				for(KtFormSummary d:ktList) {
					if (unique.add(d.getFormCode())) {
						selectedKtTypeList.add(d.getFormCode());
					}
				}
			}
		}
		return selectedKtTypeList;
	}
	
	public String getSelectedKtDesc(String code) throws SQLException, IOException
	{
		String result = "";
		if (getKtList() != null) {
			for(KtFormSummary d:ktList) {
				if (d.getFormCode().equals(code) && "Y".equals(d.getDisplay_ind())) {
					result += "<span class='cv_dot'></span> " + d.getTitle() + "</br>";
					Date startDate = d.getStartDate();
					if (startDate != null) {
						String startDateStr = new SimpleDateFormat("yyyy-MM-dd").format(startDate);
						result += "Date: ";
						result += startDateStr;
					}
					Date endDate = d.getEndDate();
					if (endDate != null) {
						String endDateStr = new SimpleDateFormat("yyyy-MM-dd").format(endDate);
						result += " - ";
						result += endDateStr + "</br>";
					}else {
						result += "</br>";
					}
					
				}
			}
		}
		
		return result;
	}
	
	
	public List<OutputDetails_P> getOutputList() throws IOException
	{
		if (outputList == null) {
			outputList = pDao.getOutputDetails_P_byStaffNo(getStaffNo(), "P");
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			for (OutputDetails_P p:outputList) {
				if (Strings.isNullOrEmpty(p.getOutputHeader_p().getSap_output_type())) {
					p.getOutputHeader_p().setSap_output_type("0");
				}
				List<OutputType> tmpList = lvTwoList.stream()
														.filter(y -> p.getOutputHeader_p().getSap_output_type().equals(y.getPk().getLookup_code()))
														.collect(Collectors.toList());
				if (!tmpList.isEmpty()) {
					p.getOutputHeader_p().setOutput_lookup_code(tmpList.get(0).getParent_lookup_code());
				}else {
					p.getOutputHeader_p().setOutput_lookup_code("0");
				}
			}
		}
		return outputList;
	}
	
	
	public void setOutputList(List<OutputDetails_P> outputList)
	{
		this.outputList = outputList;
	}

	public List<String> getSelectedOutputTypeList() throws SQLException, IOException
	{
		if (selectedOutputTypeList == null && paramPid != null) {
			selectedOutputTypeList = new ArrayList<>();
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			if (getOutputList() != null) {
				for(OutputDetails_P p:outputList) {
					List<OutputType> tmpList = lvTwoList.stream()
							.filter(y -> y.getPk().getLookup_code().equals(p.getOutputHeader_p().getSap_output_type()))
							.collect(Collectors.toList());
					if (!tmpList.isEmpty()) {
						String parentLookupCode = tmpList.get(0).getParent_lookup_code();
						if (!selectedOutputTypeList.contains(parentLookupCode))
							selectedOutputTypeList.add(parentLookupCode);
					}
				}
				Collections.sort(selectedOutputTypeList);
			}
		}
		return selectedOutputTypeList;
	}

	public String getSelectedOutputCatDesc(String code) throws SQLException, IOException
	{
		String result = "";
		if (code != null) {
			List<OutputType> lvOneList = pDao.getOutputTypeList(1);
			List<OutputType> tmpList = lvOneList.stream()
					.filter(y -> y.getPk().getLookup_code().equals(code))
					.collect(Collectors.toList());
			result = (!tmpList.isEmpty())?tmpList.get(0).getDescription():"";
		}
		return result;
	}	
	
	public String getSelectedOutputDesc(String code) throws SQLException, IOException
	{
		String result = "";
		
		if (code != null && paramPid != null) {
			CvDAO cvDao = CvDAO.getInstance();
			int pid = convertIntoNumeric(paramPid);
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "CAT_OUTPUT");
			//get show subcat in output
			boolean showSubCat = (obj.getShowInd().equals("Y"))?true:false;
			List<OutputType> outputSubCatList = getOutputSubCatList(code);
			List<String> subCatStringList = new ArrayList<>();
			for (OutputType o:outputSubCatList) {
				subCatStringList.add(o.getPk().getLookup_code());
				//if show subcat in output
				if (showSubCat) {
					result += getSelectedOutputSubcatDesc(o.getPk().getLookup_code(), o.getDescription());
				}
			}
			//if not show subcat in output
			if (!showSubCat) {
				outputList = getOutputList();
				String strWithinYear = sDao.getSysParamValueByCode(SysParam.PARAM_PERSONAL_WITHIN_YEAR_PUBLICATION);
				int withinYear = Year.now().getValue() - Integer.valueOf(strWithinYear);
				Date date= new Date();
				Calendar cal = Calendar.getInstance();
				cal.setTime(date);
				int currentMonth = cal.get(Calendar.MONTH);
				int currentYear = Year.now().getValue();
				
				
				for(OutputDetails_P p:outputList) {
					boolean valid = true;
					if (p.getOutputHeader_p().getSap_output_type() != null && "Y".equals(p.getOutputDetails_q().getDisplay_ind())) {
						//check in subcat list
						if (subCatStringList.contains(p.getOutputHeader_p().getSap_output_type())) {
							//check from Submit date
							if (p.getOutputHeader_p().getFrom_year() != null) {
								if (p.getOutputHeader_p().getFrom_year() < withinYear) {
									valid = false;
								}
							}
							//check to Submit date
							/*if (p.getOutputHeader_p().getTo_year() != null) {
								if (p.getOutputHeader_p().getTo_year() > currentYear) {
									valid = false;
								}else {
									if (p.getOutputHeader_p().getTo_month() != null && p.getOutputHeader_p().getTo_year() == currentYear) {
										if (p.getOutputHeader_p().getTo_month() > currentMonth) {
											valid = false;
										}
									}
								}	
							}*/
							
							
							if (valid) {
								selectedOutputHeader_p = p.getOutputHeader_p();
								genCitation();
								result += "<span class='cv_dot'></span> " + selectedOutputHeader_p.getApa_html() + "</br>";
								if(selectedOutputHeader_p.getSdg_code() != null)
										result +=  "SDGs infomation: " + selectedOutputHeader_p.getSdg_info() + "</br>";
							}
						}
					}	
				}		
			}
			
			if (!result.isEmpty()) {
				result = "<span class='cv_right_selected_output_content_title'> "+getSelectedOutputCatDesc(code)+"</span><br/>" + result;
			}
		}
		return result;
	}	

	public String getSelectedOutputSubcatDesc(String code, String desc) throws SQLException, IOException
	{
		String result = "";
		if (code != null && paramPid != null) {
			if (getOutputList() != null) {
				String strWithinYear = sDao.getSysParamValueByCode(SysParam.PARAM_PERSONAL_WITHIN_YEAR_PUBLICATION);
				int withinYear = Year.now().getValue() - Integer.valueOf(strWithinYear);
				Date date= new Date();
				Calendar cal = Calendar.getInstance();
				cal.setTime(date);
				int currentMonth = cal.get(Calendar.MONTH) + 1;
				int currentYear = Year.now().getValue();
				
				for(OutputDetails_P p:outputList) {
					boolean valid = true;
					if (p.getOutputHeader_p().getSap_output_type() != null && "Y".equals(p.getOutputDetails_q().getDisplay_ind())) {
						//check type
						if (code.equals(p.getOutputHeader_p().getSap_output_type())) {
							//check from Submit date
							if (p.getOutputHeader_p().getFrom_year() != null) {
								if (p.getOutputHeader_p().getFrom_year() < withinYear) {
									valid = false;
								}
							}
							//check to Submit date
							/*if (p.getOutputHeader_p().getTo_year() != null) {
								if (p.getOutputHeader_p().getTo_year() > currentYear) {
									valid = false;
								}
								if (p.getOutputHeader_p().getTo_month() != null && p.getOutputHeader_p().getTo_year() == currentYear) {
									if (p.getOutputHeader_p().getTo_month() > currentMonth) {
										valid = false;
									}
								}
							}*/
							if (valid) {
								selectedOutputHeader_p = p.getOutputHeader_p();
								genCitation();
								result += "<span class='cv_dot'></span> " + selectedOutputHeader_p.getApa_html() + "</br>";
							}
						}
					}			
				}
				if (!result.isEmpty()) {
					result = "<span style='color:#e18905; font-size:14px; font-weight:700;'><i class='fas fa-caret-right'></i> "+desc+"</span><br/>" + result;
				}
			}
		}
		return result;
	}
	
	public List<OutputType> getOutputSubCatList(String code)
	{
		List<OutputType> subcatList = new ArrayList<OutputType>();
		List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
		List<OutputType> tmpLvTwoList = lvTwoList.stream()
				.filter(y -> y.getParent_lookup_code().equals(code))
				.collect(Collectors.toList());
		subcatList.addAll(tmpLvTwoList);
		return subcatList;
	}
	
	public List<ProjectDetails_P> getProjectList() throws SQLException, IOException
	{
		if (projectList == null && paramPid != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "TAB_PROJECT");
			boolean display = (obj.getShowInd().equals("Y"))?true:false;
			if (display) {
				ProjectDAO dao = ProjectDAO.getInstance();
				projectList = dao.getProjectDetails_P_byStaffNo(getStaffNo(), "P");
				projectList = projectList.stream()
						.filter(y -> "Y".equals(y.getProjectDetails_q().getConsent_ind()))
						.filter(y -> "Y".equals(y.getProjectDetails_q().getDisplay_ind()))
						.collect(Collectors.toList());
				for (ProjectDetails_P p:projectList) {
					if (!Strings.isNullOrEmpty(p.getProjectHeader_p().getProject_summary()))
						p.getProjectHeader_p().setProject_summary(p.getProjectHeader_p().getProject_summary().replaceAll("(\r\n|\n)", "<br/>"));
					if (!Strings.isNullOrEmpty(p.getProjectHeader_p().getProject_summary_2()))
						p.getProjectHeader_p().setProject_summary_2(p.getProjectHeader_p().getProject_summary_2().replaceAll("(\r\n|\n)", "<br/>"));
				}
			}
		}
		return projectList;
	}

	//0: name; 1: role; 2: staff no.
	public String getPrincipalInvestigators(String projctNo, String staffNo) throws SQLException
	{
		String investigators = "";
		String staffName = "";
		String staffRole = "";
		boolean isPI = false;
		if (projctNo != null) {
			ProjectDAO dao = ProjectDAO.getInstance();
			List<ProjectDetails_P> resultList = dao.getInvestigators(Integer.valueOf(projctNo));
			
			int count = 1;
			for (ProjectDetails_P i:resultList) {
				String tmpStaffNo = (i.getInvestigator_staff_no() == null)?"":i.getInvestigator_staff_no();
				String tmpStaffRole = (i.getInvestigator_type() == null)?"":i.getInvestigator_type();
				
				if (tmpStaffNo.equals(staffNo)) {
					staffName = i.getInvestigator_name();
					staffRole = dao.getProjectCapacityDesc(i.getInvestigator_type());
				}
				if (tmpStaffRole.equals("PRINCIPAL INVESTIGATOR")) {
					investigators += i.getInvestigator_name();
					investigators += (count < resultList.size())?", ":"";
					if (tmpStaffNo.equals(staffNo)) {
						isPI = true;
					}
				}
				count++;
			}
			if (investigators .length() > 1 && investigators.charAt(investigators.length() - 2) == ','){
				investigators = investigators.substring(0,  investigators.length() - 2);
			}
			
			if (isPI == false) {
				investigators += " (" + staffName + " as " + staffRole + ")";
			}
		}
		return investigators;
	}		
	
	public List<Award> getAwardList() throws SQLException, IOException
	{
		if (awardList == null && paramPid != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "TAB_AWARD");
			boolean display = (obj.getShowInd().equals("Y"))?true:false;
			if (display) {
				AwardDAO dao = AwardDAO.getInstance();
				awardList = dao.getCVAwardList(pid, getStaffNo(), null, null);
			}
		}
		return awardList;
	}
	
	public List<Patent> getPatentList() throws SQLException, IOException
	{
		if (patentList == null && paramPid != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "TAB_PATENT");
			boolean display = (obj.getShowInd().equals("Y"))?true:false;
			if (display) {
				PatentDAO dao = PatentDAO.getInstance();
				patentList = dao.getRIList(pid, getStaffNo(), null);
			}
		}
		return patentList;
	}	
	
	@PostConstruct
	public void init(){
		StaffDAO sdao = StaffDAO.getInstance();
		StaffIdentity sIdentity = sdao.getStaffDetailsByUserId(getCurrentUserId());
		this.paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
	}
	
	public void redirectToPure() throws IOException
	{
		String code = "PURE_HOME_URL";
		String url = "";
		SysParamDAO dao = SysParamDAO.getInstance();
		SysParam tmp = dao.getSysParamByCode(code);
		if (tmp != null) {
			url = tmp.getValue();
		}
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		//HttpServletRequest httpReq = (HttpServletRequest) eCtx.getRequest();
		eCtx.redirect(url);
	}
	
	public void checkValidStaff() throws IOException 
	{
		boolean valid = true;
		if (Strings.isNullOrEmpty(paramPid)) {
			valid = false;
		}
		if (valid) {
			Pattern pattern = Pattern.compile("-?\\d+(\\.\\d+)?");
			if (!pattern.matcher(paramPid).matches()) {
				valid = false;
			}
		}
		if (valid) {
			if (getStaffNo() == null || getsInfo() == null) {
				valid = false;
			}
		}
		if (valid == false) {
			redirectToPure();
		}
	}
	
	public Boolean longContent(String value) {
		Boolean result = false;
		String code = "MAX_CHAR";
		SysParamDAO dao = SysParamDAO.getInstance();
		SysParam tmp = dao.getSysParamByCode(code);
		int max = Integer.parseInt(tmp.getValue());
		if (value.length() > max) {
			result = true;
		}
		return result;
	}

	public List<String> getDisplayOrderList()
	{
		if (displayOrderList == null && paramPid != null) {
			displayOrderList =  new ArrayList<>();
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			List<StaffProfileDisplay> orderList = cvDao.getStaffProfileDisplayList(pid, "TAB");
			for (StaffProfileDisplay o:orderList) {
				if (o.getIsShow()) {
					String itemCode = o.getPk().getItemCode().toLowerCase();
					String item = itemCode.substring(itemCode.lastIndexOf("_")+1);
					displayOrderList.add(item+getCvStyle()+".xhtml");
				}
			}
		}
		
	
		return displayOrderList;
	}

	
	public String getWebsiteLink()
	{
		if (getShowWebsite()!= null) {
			if (showWebsite.equals("CVPAGE")) {
				websiteLink = getCvLink();
			}else if (showWebsite.equals("HOMEPAGE")) {
				websiteLink = getHomepageLink();
			}else {
				websiteLink = "N/A";
			}
		}else {
			websiteLink = "N/A";
		}
		return websiteLink;
	}

	
	public void setWebsiteLink(String websiteLink)
	{
		this.websiteLink = websiteLink;
	}

	
	public String getCvStyle()
	{
		if (cvStyle == null && getParamPid() != null) {
			cvStyle = "1";
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "CV_STYLE");

			if (obj != null) {
				cvStyle = obj.getDisplayType();
			}
		}
		return cvStyle;
	}

	
	public void setCvStyle(String cvStyle)
	{
		this.cvStyle = cvStyle;
	}

	public String getCvStylePage()
	{
		String cvStylePage = "";
		if (getCvStyle() != null) {
			cvStylePage = "../resources/component/cv/style"+cvStyle+".xhtml";
		}else {
			cvStylePage = "../resources/component/cv/style1.xhtml";
		}
		return cvStylePage;
	}
	
	public String getCvLink()
	{
		String cvLink = "";
		if (sIdentity != null) {
			SysParamDAO sDao = SysParamDAO.getInstance();
			String cvUrl = sDao.getSysParamValueByCode("BASE_URL_CV");
			cvLink = cvUrl + sIdentity.getPid()+"&amp;name="+sIdentity.getFullname();
		}
		return cvLink;
	}
	
	public String getHomepageLink()
	{
		String homepageLink = "";
		if (sInfo != null) {
			homepageLink = sInfo.getUrl();
		}
		return homepageLink;
	}
	
	public Boolean getShowPhoto()
	{
		if (showPhoto == null && getParamPid() != null) {
			showPhoto = true;
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_PHOTO");
			if (obj != null) {
				showPhoto = (obj.getShowInd().equals("Y"))?true:false;
			}
		}
		return showPhoto;
	}

	
	public void setShowPhoto(Boolean showPhoto)
	{
		this.showPhoto = showPhoto;
	}
	
	public String getFullMonth(int month, Locale locale) {
	    return new DateFormatSymbols(locale).getShortMonths()[month-1];
	}
	
	public String getShortMonth(int month, Locale locale) {
	    return new DateFormatSymbols(locale).getMonths()[month-1];
	}
	
	public String getCitation()
	{
		if (citation == null) {			
			citation = "APA";
			if (paramPid != null) {
				int pid = convertIntoNumeric(paramPid);
				CvDAO cvDao = CvDAO.getInstance();
				StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_CITATION");
				if (obj != null) {
					citation= obj.getDisplayType();
				}
			}
		}
		return citation;
	}

	
	public void setCitation(String citation)
	{
		this.citation = citation;
	}
	
	public void genCitation() throws IOException 
	{
		if (!GenericValidator.isBlankOrNull(selectedOutputHeader_p.getSap_output_type())){
			String result = "";
			if (!"0".equals(selectedOutputHeader_p.getSap_output_type())) {
				if ("APA".equals(getCitation())) {
					result = getAPA(selectedOutputHeader_p);
				}
				if ("MLA".equals(getCitation())) {
					result = getMLA(selectedOutputHeader_p);
				}
				if ("Chicago".equals(getCitation())) {
					result = getChicago(selectedOutputHeader_p);
				}
				if (getsInfo() != null) {
					OutputAddl_P addl = pDao.getOutputAddl_P(selectedOutputHeader_p.getPk().getOutput_no(), "P", sInfo.getStaff_number());
					if (addl != null) {
						if(addl.getCustom_citation() != null) {
							result = addl.getCustom_citation();
							result = result.replaceAll("</p><p>","</br>");
							result = result.replaceAll("<p>","");
							result = result.replaceAll("</p>","");
						}
					}
				}
				selectedOutputHeader_p.setApa_html(result);
				selectedOutputHeader_p.setApa_citation(result.replaceAll("\\<[^>]*>",""));
			}
		}
	}
	
	public void exportMyDataInPdf() throws Exception {
    	StringBuilder htmlCode = new StringBuilder();
    	if(sInfo == null) {
    		sInfo = getsInfo();
    	}
    	
    	if(iUserInfo == null) {
    		iUserInfo = getiUserInfo();
    	}
    	
    	//HTML Head
       	htmlCode.append("<!DOCTYPE html>" +
				"<html>" + 
					"<head>" + 
						"<title>The Education University of Hong Kong</title>" + 
						"<meta charset = 'utf-8'/> " + 
						"<meta name='author' content='The Education University of Hong Kong'/>" + 
						"<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css'/>" + 
						"<link rel='stylesheet' href='https://github.com/googlefonts/noto-cjk/raw/main/Sans/Variable/TTF/Subset/NotoSansTC-VF.ttf'/>" + 
						"<style>" + 
			            "@font-face {" + 
					         "font-family: 'Noto Sans TC';" + 
					         "font-style: regular;" +
					         "font-weight: 400;" + 
					         "font-display: swap;" +
					         "src: url(https://github.com/googlefonts/noto-cjk/raw/main/Sans/Variable/TTF/Subset/NotoSansTC-VF.ttf);}" +
						"#header {position: running(header); margin-top: 15px;}" +
						".timeTable {border:1px solid black;width:100%;}" + 
						".timeTable th{border:1px solid #666666;}" + 
						".timeTable td{border:1px solid #666666;}" + 
						".timeTable {text-align: center;}" + 
						".chineseChar {font-family: 'Noto Sans TC';  line-height: 1.2;}" + 
						".remarks {font-family: 'Noto Sans TC';  text-decoration: none; margin: 0; line-height: 1.5;}" + 
						".category {color:#000000; border-bottom: 1px solid black; }" +
						"@page{margin-left: 10px;; margin-right: 10px; padding-left: 0; padding-right: 0;}" +
						".page-number:after {content: 'Page ' counter(page) ' of ' counter(pages); }"+
						"</style>" + 
					"</head>");
       	
    	//Report Header
    	htmlCode.append("<body>");
    	String imageCode = "";
    	String nameCode = "";
    	if (selectedOptions == null) {
    		selectedOptions = new String[0];
    	}
    	if (ArrayUtils.contains(selectedOptions, "photo") && !Strings.isNullOrEmpty(sInfo.getPhotoFile())) {
        	imageCode = "<td colspan='1'><img src='data:image/jpg;base64," + sInfo.getPhotoFile().substring(0, sInfo.getPhotoFile().length() - 2) + "' width = '64px' height = '64px'/></td>";
    	}
    	if (!Strings.isNullOrEmpty(imageCode)){
    		nameCode = "<td colspan='2' class = 'chineseChar'>" + iUserInfo.getTitle() + " " + iUserInfo.getFullname() + " " + iUserInfo.getChinesename() + "</td>";
    	}else {
    		nameCode = "<td colspan='3' class = 'chineseChar'>" + iUserInfo.getTitle() + " " + iUserInfo.getFullname() + " " + iUserInfo.getChinesename() + "</td>";
    	}
    	
    	htmlCode.append("<table width = '100%'>" +
				"<colgroup>" + 
					"<col width = '10%'><col width = '60%'><col width = '30%'>" +
				"</colgroup>" + 
				"<tr>" + imageCode + nameCode + "</tr>");
    	
    	for(InternetUserInfo internetUserInfo: iUserInfoList) {
    		htmlCode.append("<tr><td colspan='2'>" + internetUserInfo.getPost() + " - " + internetUserInfo.getDeptdesc() + "</td></tr>");
    	}
    	
    	htmlCode.append("</table></td>" + 
						"</table><br/>");
    	
    	String htmlCodeString = exportDataBySelectedOptionsForPdf(htmlCode);

       	Document htmlDocument = Jsoup.parse(htmlCodeString);
    	htmlDocument.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
    	
    	File report = new File("cv.pdf");
    	OutputStream output = null;
    	
	    try (OutputStream os = new FileOutputStream(report)) {
	    	
    	    PdfRendererBuilder builder = new PdfRendererBuilder();
    	    builder.toStream(os);
    	    builder.withW3cDocument(new W3CDom().fromJsoup(htmlDocument), "/");
    	    builder.run();
    	    
    	    //Render Report to the client window
			ExternalContext context =  FacesContext.getCurrentInstance().getExternalContext();
			
			context.responseReset();
			
			context.setResponseContentType("application/pdf");
			context.setResponseContentLength((int) report.length());
			
			context.setResponseHeader("Content-Disposition", "inline; filename=\"" + report.getName() + "\""); 
			context.setResponseHeader("Cache-Control", "private, must-revalidate");
			context.setResponseHeader("Expires", "-1");
			context.setResponseHeader("Pragma", "private");
			
			output = context.getResponseOutputStream();
	        Files.copy(report.toPath(), output);

	        os.flush();
	        os.close();
	        output.flush();
	        output.close();
			
			FacesContext.getCurrentInstance().responseComplete();
	    }
	}
	
	public void exportMyDataInDocx() throws Exception {
    	StringBuilder htmlCode = new StringBuilder();
    	if(sInfo == null) {
    		sInfo = getsInfo();
    	}
    	
    	if(iUserInfo == null) {
    		iUserInfo = getiUserInfo();
    	}
    	
    	//HTML Head
       	htmlCode.append("<!DOCTYPE html>" +
				"<html>" + 
					"<head>" + 
					"</head>");
       	
    	//Report Header
    	htmlCode.append("<body>");
    	String imageCode = "";
    	String nameCode = "";
    	if (selectedOptions == null) {
    		selectedOptions = new String[0];
    	}
    	if (ArrayUtils.contains(selectedOptions, "photo") && !Strings.isNullOrEmpty(sInfo.getPhotoFile())) {
        	imageCode = "<td colspan='1'><img src='data:image/jpg;base64," + sInfo.getPhotoFile().substring(0, sInfo.getPhotoFile().length() - 2) + "' width = '64px' height = '64px'/></td>";
    	}
    	if (!Strings.isNullOrEmpty(imageCode)){
    		nameCode = "<td colspan='2' class = 'chineseChar'>" + iUserInfo.getTitle() + " " + iUserInfo.getFullname() + " " + iUserInfo.getChinesename() + "</td>";
    	}else {
    		nameCode = "<td colspan='3' class = 'chineseChar'>" + iUserInfo.getTitle() + " " + iUserInfo.getFullname() + " " + iUserInfo.getChinesename() + "</td>";
    	}
 
    	htmlCode.append("<table width = '100%'>" +
							"<colgroup>" + 
								"<col width = '10%'><col width = '60%'><col width = '30%'>" +
							"</colgroup>" + 
							"<tr>" + imageCode + nameCode + "</tr>");
    	
    	for(InternetUserInfo internetUserInfo: iUserInfoList) {
    		htmlCode.append("<tr><td colspan='2'>" + internetUserInfo.getPost() +" - "+ internetUserInfo.getDeptdesc() + "</td></tr>");
    	}
    	
    	htmlCode.append("</table></td>" + 
						"</table>");
    	
    	String htmlCodeString = exportDataBySelectedOptionsForDocx(htmlCode);
       	Document htmlDocument = Jsoup.parse(htmlCodeString);
    	htmlDocument.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    	
    	File cvDocx = new File("cv.docx");

		WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();
        XHTMLImporterImpl XHTMLImporter = new XHTMLImporterImpl(wordMLPackage);

		wordMLPackage.getMainDocumentPart().getContent().addAll(XHTMLImporter.convert(htmlDocument.outerHtml().replace("nbsp", "#160"), null));
		wordMLPackage.save(cvDocx);
		FileInputStream is = new FileInputStream(cvDocx);
		Faces.sendFile(is, "cv.docx", false);
		is.close();
	}
	
	public String exportDataBySelectedOptionsForDocx(StringBuilder htmlCode) throws Exception{	
		List<CdcfRptPeriod> selectedCdcfPeriodList = getSelectedCdcfPeriodList();
		for(int i = 0; i < selectedOptions.length; i++) {			
			//export Contact data
			if(selectedOptions[i].equals("contact")){ //Contact
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000; '><b><u>Contact</u></b></td></tr>");
	        	
	        	if(sInfo.getOrcid() != null) {
					htmlCode.append("<tr><td colspan = '1' style = 'padding-top: 8px;'><b>ORCID: </b></td>" + 
										"<td colspan = '2' style = 'padding-top: 8px;'> " + sInfo.getOrcid() + " </td></tr>");
	        	}
	        	
	        	if(!iUserInfo.getPhone().equals("")) {
	        		htmlCode.append("<tr><td colspan = '1'><b>Phone: </b></td>" + 
	        							"<td colspan = '2'>" + iUserInfo.getPhone() + "</td></tr>");
	        	}
	        	
	        	if(!iUserInfo.getEmail().equals("")) {
	        		htmlCode.append("<tr><td colspan = '1'><b>Email: </b></td>" + 
							"<td colspan = '2'>" + iUserInfo.getEmail() + "</td></tr>");
	        	}
	        	
	        	if(!iUserInfo.getFax().equals("")){
	        		htmlCode.append("<tr><td colspan = '1'><b>Fax: </b></td>" + 
							"<td colspan = '2'>" + iUserInfo.getFax() + "</td></tr>");
	        	}

	        	htmlCode.append("<tr><td colspan = '1'><b>Address: </b></td>" + 
						"<td colspan = '2'>" + "10 Lo Ping Road, Tai Po, New Territories, Hong Kong" + "</td></tr>");
	        	
	        	if(!getWebsiteLink().equals("N/A")) {
		        	htmlCode.append("<tr><td colspan = '1'><b>Website: </b></td>" + 
							"<td colspan = '2'><a href = '" + websiteLink.split("&")[0] + "' target = '_blank' style = 'text-decoration: none;'><u>" + websiteLink + "</u></a></td></tr>");
	        	}
	        	
	        	if(sInfo.getScopusId() != null) {
		        	htmlCode.append("<tr><td colspan = '1'><b>Scopus Id: </b></td>" + 
							"<td colspan = '2'>" + sInfo.getScopusId() + "</td></tr>");
	        	}
	        	
	        	htmlCode.append("</table><br/>");
	        	
			}else if(selectedOptions[i].equals("profile")) { //Personal Profile
				String profileCode = (!Strings.isNullOrEmpty(sInfo.getProfile()))?sInfo.getProfile():"";
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000; '><b><u>Personal Profile</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + profileCode + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("job")) { //Career Overview
				if(employmentHistList_edit == null) {
					employmentHistList_edit = getEmploymentHistList_edit();
				}
				
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000;'><b><u>Career Overview</u></b></td></tr>");
	        	
	        	SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
	        	for(EmploymentHistory_edit  history: employmentHistList_edit) {
	        		String parseFromDate = (history.getFrom_date() != null)? dateFormatter.format(history.getFrom_date()): "";
	        		String parseToDate = (history.getTo_date() != null) ? dateFormatter.format(history.getTo_date()): "Present";
	        		if(history.getCompany() != null) {
	        			htmlCode.append("<tr><td colspan='3' class = 'chineseChar'>" + history.getCompany() + "</td></tr>");
	        		}
	        		htmlCode.append("<tr class = 'chineseChar'><td colspan='3'>" + history.getJob_title() + "</td></tr>" + 
	        						"<tr><td colspan='3' class = 'chineseChar'>" + parseFromDate + " - " + parseToDate + "</td></tr>");
	        		htmlCode.append("<tr><td></td></tr>");
	        	}
				        	
	        	htmlCode.append("</table>");
			}else if(selectedOptions[i].equals("researchInt")) { // Research Interest
				String researchIntCode = (!Strings.isNullOrEmpty(sInfo.getResearch_interest()))?sInfo.getResearch_interest():"";
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000; '><b><u>Research Interests</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + researchIntCode + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("teachingInt")) { //Teaching Interest
				String teachingIntCode = (!Strings.isNullOrEmpty(sInfo.getTeaching_interest()))?sInfo.getTeaching_interest():"";
				
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000; '><b><u>Teaching Interests</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + teachingIntCode + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("ExtAppt")) { //External Appointment
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000; '><b><u>External Appointments</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + sInfo.getExt_appt()+ "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("othAct")) { //Other Activities
				String othActCode = (!Strings.isNullOrEmpty(sInfo.getOth_activity()))?sInfo.getOth_activity():""; 
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000;'><b><u>Other Activities</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + othActCode + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("output")) {// Research Outputs
				List<Publication> outputList = pDao.getResearchOutputBySelectedOutputTypes(selectedOutputTypes, iUserInfo.getStaff_number());
				if (outputList != null) {
					List<Publication> parentCatList = pDao.getParentOutputTypesList();
					//List<Publication> filteredList = (List<Publication>) filteredByCDCFGenDate(outputList, selectedCdcfPeriodList, "Output");
					List<Publication> filteredList = filterOutputListByRIDate(outputList);
					
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style = 'color:#000000;'><b><u>Research Outputs</u></b></td></tr>");
					
		        	int listIndex = 0;
		        	boolean isHeaderAppend = false;
					for(Publication parentCat: parentCatList) {
						while(listIndex < filteredList.size()) {
							if(parentCat.getSapOutputType().equals(filteredList.get(listIndex).getParentLookupCode())) {
								if(isHeaderAppend == false) {
									htmlCode.append("<tr style = 'font-size: 12px;'><td colspan='3'>" + 
											parentCat.getSapOutputTypeDesc() + "</td></tr>");
									isHeaderAppend = true;
								}
								
								if(listIndex == 0 || !filteredList.get(listIndex - 1).getSapOutputTypeDesc().equals(filteredList.get(listIndex).getSapOutputTypeDesc())) {
									htmlCode.append("<tr style = 'color: #444444; font-size: 12px;'><td colspan='3' class = 'chineseChar'>" + 
											"<span style = 'font-size: 8px;'>&#9654;</span> " + 
											filteredList.get(listIndex).getSapOutputTypeDesc() + "</td></tr>");
								}
								
								htmlCode.append("<tr style = 'color: #444444; font-size: 12px;'><td colspan='3' class = 'chineseChar'>" + 
										"<span style = 'font-size: 8px;'>&#9632;</span> " + 
										filteredList.get(listIndex).getApaCitation() + "</td></tr>");
								
								listIndex++;
							}else if(isHeaderAppend == true){
								htmlCode.append("<tr><td></br></td></tr>");
								isHeaderAppend = false;
								break;
							}else {
								break;
							}
							
						}
					}
					
					htmlCode.append("</table></br>");
				}
			} else if(selectedOptions[i].equals("project")) { //Projects	    	
				projectList = getProjectList();
				if (projectList != null) {
					List<ProjectDetails_P> filteredList = filterProjectListByFundSource();
					filteredList = filterProjectListByRIDate(filteredList);
					
					List<Integer> projectIdList = getProjectIdList(filteredList);
		        	List<ProjectSummary> projectSummaryList = projDao.getProjectSummaryByProjectNo(projectIdList);
		        	//projectSummaryList = (List<ProjectSummary>) filteredByCDCFGenDate(projectSummaryList, selectedCdcfPeriodList, "Project");
					
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style = 'color:#000000; '><b><u>Projects</u></b></td></tr>");
		        	
		        	for(ProjectSummary project: projectSummaryList) {
		        		htmlCode.append("<tr><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + project.getProjTitle() + "</td></tr>" +
		        							"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>" + project.getProject_summary() + "</td></tr>" + 
		        							"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>" + project.getProject_summary_2() + "</td></tr>" + 
		        							"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>Project Start Year: " + project.getFromYear() + 
		        							", Principal Investigator (s): " + getPrincipalInvestigators(Integer.toString(project.getProjectNo()), iUserInfo.getStaff_number()) + 
		        						"</td></tr>");   
		        		
						htmlCode.append("<tr><td></br></td></tr>");
		        	}
		        	
		        	htmlCode.append("</table></br>");
				}
			}else if(selectedOptions[i].equals("award")) { //Prizes and Awards
		    	awardList = getAwardList();
		    	if (awardList != null) {
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style = 'color:#000000; '><b><u>Prizes and Awards</u></b></td></tr>");
		        	
		    		//List<Award> filteredAwardList = (List<Award>) filteredByCDCFGenDate(awardList, selectedCdcfPeriodList, "Award");
		        	List<Award>filteredAwardList = filterAwardListByRIDate(awardList);
		        	for(Award award: filteredAwardList) {  
		        		htmlCode.append("<tr class = 'chineseChar' style = ''><td colspan='3'>" + award.getAwardName() + "</td></tr>");
		        		htmlCode.append("<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>");
		        		if(award.getFullDesc() != null) {htmlCode.append(award.getFullDesc());} else {htmlCode.append("<br/>");}
		        		htmlCode.append("</td></tr>");
		        		htmlCode.append("<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'> Date of receipt: ");
		        		if(award.getAwardDay() != null) {htmlCode.append(award.getAwardDay() + "/");}
		        		if(award.getAwardMonth() != null) { htmlCode.append(award.getAwardMonth() + "/");}
		        		if(award.getAwardYear() != null) {htmlCode.append(award.getAwardYear());}
		        		htmlCode.append(" Conferred By: " + award.getOrgName()  + "</td></tr>");
		        		htmlCode.append("<tr><td><br/></td></tr>");
		        	}
		        	
		        	htmlCode.append("</table></br>");
		    	}
			}else if(selectedOptions[i].equals("patent")) { //Patents
				patentList = getPatentList();
				if (patentList != null) {
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style = 'color:#000000;'><b><u>Patents</u></b></td></tr>");
		        	
		        	List<Patent> filteredList = filterPatentListByRIDate(patentList);
		        	//filteredList = (List<Patent>) filteredByCDCFGenDate(filteredList, selectedCdcfPeriodList, "Patent");
		        	
		        	for(Patent patent: filteredList) {
		        		htmlCode.append("<tr><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + patent.getPatentName() + "</td></tr>" +
								"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>" + patent.getShortDesc() + " - " + 
								patent.getPatentGranted()  + "/" + patent.getPatentType() + 
								"</br></td></tr>");  
		        	}
		        	htmlCode.append("</table></br>");
				}
			}else if(selectedOptions[i].equals("kt")) { //KT Activities
				List<KtFormSummary> ktFormSummaryList = fDao.getKtFormSummaryList(iUserInfo.getStaff_number(), selectedStartDate, selectedEndDate, selectedCdcfPeriodList);
				if (ktFormSummaryList != null) {
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style = 'color:#000000;'><b><u>KT Activities</u></b></td></tr>");
					SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
					
					for(int j = 0; j < ktFormSummaryList.size(); j++) {
						if(j == 0 || !ktFormSummaryList.get(j - 1).getFormCode().equals(ktFormSummaryList.get(j).getFormCode())) {
							if(j > 0) {
								htmlCode.append("<tr><td></br></td></tr>");
							}
							htmlCode.append("<tr><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + 
											getKtFormName(ktFormSummaryList.get(j).getFormCode()) + "</td></tr>");
						}
						htmlCode.append("<tr style = 'color:#444444'><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + 
										"<span style = 'font-size: 8px;'>&#9632;</span> " + 
										ktFormSummaryList.get(j).getTitle() + "</td></tr>" + 
										"<tr style = 'color:#444444;'><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>Date: " + 
										dateFormatter.format(ktFormSummaryList.get(j).getStartDate()));
						
						if(ktFormSummaryList.get(j).getEndDate() != null) {
							htmlCode.append(" - "  + dateFormatter.format(ktFormSummaryList.get(j).getEndDate()));
						}
						
						htmlCode.append("</td></tr>");
					}
					
		        	htmlCode.append("</table><br>");
				}
			}
		}
    	if(remarks.length() > 0) {
        	htmlCode.append("<table width = '100%'>" +
								"<colgroup>" + 
									"<col width = '15%'><col width = '55%'><col width = '30%'>" +
								"</colgroup>" + 
								"<tr><td colspan='3' class = 'category'><b>Remarks</b></td></tr>" + 
								"<tr><td colspan='3' class = 'chineseChar'>" + remarks + "</td></tr>" + 
							"</table>");      	
    	}

		return htmlCode.toString();
	}
	
	public String exportDataBySelectedOptionsForPdf(StringBuilder htmlCode) throws Exception{	
		List<CdcfRptPeriod> selectedCdcfPeriodList = getSelectedCdcfPeriodList();
		for(int i = 0; i < selectedOptions.length; i++) {			
			//export Contact data
			if(selectedOptions[i].equals("contact")){ //Contact
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style = 'color:#000000; '><b><u>Contact</u></b></td></tr>");
	        	
	        	if(sInfo.getOrcid() != null) {
					htmlCode.append("<tr><td colspan = '1' style = 'padding-top: 8px;'><b>ORCID: </b></td>" + 
										"<td colspan = '2' style = 'padding-top: 8px;'> " + sInfo.getOrcid() + " </td></tr>");
	        	}
	        	
	        	if(!iUserInfo.getPhone().equals("")) {
	        		htmlCode.append("<tr><td colspan = '1'><b>Phone: </b></td>" + 
	        							"<td colspan = '2'>" + iUserInfo.getPhone() + "</td></tr>");
	        	}
	        	
	        	if(!iUserInfo.getEmail().equals("")) {
	        		htmlCode.append("<tr><td colspan = '1'><b>Email: </b></td>" + 
							"<td colspan = '2'>" + iUserInfo.getEmail() + "</td></tr>");
	        	}
	        	
	        	if(!iUserInfo.getFax().equals("")){
	        		htmlCode.append("<tr><td colspan = '1'><b>Fax: </b></td>" + 
							"<td colspan = '2'>" + iUserInfo.getFax() + "</td></tr>");
	        	}

	        	htmlCode.append("<tr><td colspan = '1'><b>Address: </b></td>" + 
						"<td colspan = '2'>" + "10 Lo Ping Road, Tai Po, New Territories, Hong Kong" + "</td></tr>");
	        	
	        	if(!getWebsiteLink().equals("N/A")) {
		        	htmlCode.append("<tr><td colspan = '1'><b>Website: </b></td>" + 
							"<td colspan = '2'><a href = '" + websiteLink.split("&")[0] + "' target = '_blank' style = 'text-decoration: none;'><u>" + websiteLink + "</u></a></td></tr>");
	        	}
	        	
	        	if(sInfo.getScopusId() != null) {
		        	htmlCode.append("<tr><td colspan = '1'><b>Scopus Id: </b></td>" + 
							"<td colspan = '2'>" + sInfo.getScopusId() + "</td></tr>");
	        	}
	        	
	        	htmlCode.append("</table><br/>");
	        	
			}else if(selectedOptions[i].equals("profile")) { //Personal Profile
				String profileCode = (!Strings.isNullOrEmpty(sInfo.getProfile()))?sInfo.getProfile():"";
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style='color:#000000; '><b><u>Personal Profile</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + profileCode + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("job")) { //Career Overview
				if(employmentHistList_edit == null) {
					employmentHistList_edit = getEmploymentHistList_edit();
				}
				
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style='color:#000000; '><b><u>Career Overview</u></b></td></tr>");
	        	
	        	SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
	        	for(EmploymentHistory_edit  history: employmentHistList_edit) {
	        		String parseFromDate = (history.getFrom_date() != null)? dateFormatter.format(history.getFrom_date()): "";
	        		String parseToDate = (history.getTo_date() != null) ? dateFormatter.format(history.getTo_date()): "Present";
	        		if(history.getCompany() != null) {
	        			htmlCode.append("<tr><td colspan='3' style = 'font-size: 12px;color: #444444;' class = 'chineseChar'>" + history.getCompany() + "</td></tr>");
	        		}
	        		htmlCode.append("<tr style = 'font-size: 12px;'><td colspan='3' class = 'chineseChar'>" + history.getJob_title() + "</td></tr>" + 
	        						"<tr><td colspan='3' style = 'font-size: 12px;color: #444444;' class = 'chineseChar'>" + parseFromDate + " - " + parseToDate + "</td></tr>");
	        		htmlCode.append("<tr><td></td></tr>");
	        	}
				        	
	        	htmlCode.append("</table>");
			}else if(selectedOptions[i].equals("researchInt")) { // Research Interest
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style='color:#000000; '><b><u>Research Interest</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + sInfo.getResearch_interest() + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("teachingInt")) { //Teaching Interest
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style='color:#000000; '><b><u>Teaching Interest</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + sInfo.getTeaching_interest() + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("ExtAppt")) { //External Appointment
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style='color:#000000; '><b><u>External Appointment</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + sInfo.getExt_appt()+ "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("othAct")) { //Other Activities
				String othActCode = (!Strings.isNullOrEmpty(sInfo.getOth_activity()))?sInfo.getOth_activity():""; 
	        	htmlCode.append("<table width = '100%'>" +
									"<colgroup>" + 
										"<col width = '15%'><col width = '55%'><col width = '30%'>" +
									"</colgroup>" + 
									"<tr><td colspan='3' style='color:#000000; '><b><u>Other Activities</u></b></td></tr>" +
									"<tr><td colspan='3' class = 'chineseChar'>" + othActCode + "</td></tr></table></br>");
			}else if(selectedOptions[i].equals("output")) {// Research Outputs
				List<Publication> outputList = pDao.getResearchOutputBySelectedOutputTypes(selectedOutputTypes, iUserInfo.getStaff_number());
				if (outputList != null) {
					List<Publication> parentCatList = pDao.getParentOutputTypesList();
					//List<Publication> filteredList = (List<Publication>) filteredByCDCFGenDate(outputList, selectedCdcfPeriodList, "Output");
					List<Publication> filteredList = filterOutputListByRIDate(outputList);
					
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style='color:#000000; '><b><u>Research Outputs</u></b></td></tr>");
					
		        	int listIndex = 0;
		        	boolean isHeaderAppend = false;
					for(Publication parentCat: parentCatList) {
						while(listIndex < filteredList.size()) {
							if(parentCat.getSapOutputType().equals(filteredList.get(listIndex).getParentLookupCode())) {
								if(isHeaderAppend == false) {
									htmlCode.append("<tr style = 'font-size: 12px;'><td colspan='3'>" + 
											parentCat.getSapOutputTypeDesc() + "</td></tr>");
									isHeaderAppend = true;
								}
								
								if(listIndex == 0 || !filteredList.get(listIndex - 1).getSapOutputTypeDesc().equals(filteredList.get(listIndex).getSapOutputTypeDesc())) {
									htmlCode.append("<tr style = 'color: #444444; font-size: 12px;'><td colspan='3' class = 'chineseChar'>" + 
											"<span style = 'font-size: 8px;'>&#9654;</span> " + 
											filteredList.get(listIndex).getSapOutputTypeDesc() + "</td></tr>");
								}
								
								htmlCode.append("<tr style = 'color: #444444; font-size: 12px;'><td colspan='3' class = 'chineseChar'>" + 
										"<span style = 'font-size: 8px;'>&#9632;</span> " + 
										filteredList.get(listIndex).getApaCitation() + "</td></tr>");
								
								listIndex++;
							}else if(isHeaderAppend == true){
								htmlCode.append("<tr><td></br></td></tr>");
								isHeaderAppend = false;
								break;
							}else {
								break;
							}
							
						}
					}
					
					htmlCode.append("</table></br>");
				}
			} else if(selectedOptions[i].equals("project")) { //Projects	    	
				projectList = getProjectList();
				if (projectList != null) {
					List<ProjectDetails_P> filteredList = filterProjectListByFundSource();
					filteredList = filterProjectListByRIDate(filteredList);
					
					List<Integer> projectIdList = getProjectIdList(filteredList);
		        	List<ProjectSummary> projectSummaryList = projDao.getProjectSummaryByProjectNo(projectIdList);
		        	//projectSummaryList = (List<ProjectSummary>) filteredByCDCFGenDate(projectSummaryList, selectedCdcfPeriodList, "Project");
					
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style='color:#000000; '><b><u>Projects</u></b></td></tr>");
		        	
		        	for(ProjectSummary project: projectSummaryList) {
		        		htmlCode.append("<tr><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + project.getProjTitle() + "</td></tr>" +
		        							"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>" + project.getProject_summary() + "</td></tr>" + 
		        							"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>" + project.getProject_summary_2() + "</td></tr>" + 
		        							"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>Project Start Year: " + project.getFromYear() + 
		        							", Principal Investigator (s): " + getPrincipalInvestigators(Integer.toString(project.getProjectNo()), iUserInfo.getStaff_number()) + 
		        						"</td></tr>" + 
		        						"<tr><td></br></td></tr>");        		
		        	}
		        	
		        	htmlCode.append("</table></br>");
				}
			}else if(selectedOptions[i].equals("award")) { //Prizes and Awards
		    	awardList = getAwardList();
		    	if (awardList != null) {
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style='color:#000000; '><b><u>Prizes and Awards</u></b></td></tr>");
		        	
		    		//List<Award> filteredAwardList = (List<Award>) filteredByCDCFGenDate(awardList, selectedCdcfPeriodList, "Award");
		        	List<Award> filteredAwardList = filterAwardListByRIDate(awardList);
		        	for(Award award: filteredAwardList) {  
		        		htmlCode.append("<tr class = 'chineseChar' style = ''><td colspan='3'>" + award.getAwardName() + "</td></tr>");
		        		htmlCode.append("<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>");
		        		if(award.getFullDesc() != null) {htmlCode.append(award.getFullDesc());} else {htmlCode.append("<br/>");}
		        		htmlCode.append("</td></tr>");
		        		htmlCode.append("<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'> Date of receipt: ");
		        		if(award.getAwardDay() != null) {htmlCode.append(award.getAwardDay() + "/");}
		        		if(award.getAwardMonth() != null) { htmlCode.append(award.getAwardMonth() + "/");}
		        		if(award.getAwardYear() != null) {htmlCode.append(award.getAwardYear());}
		        		htmlCode.append(" Conferred By: " + award.getOrgName()  + "</td></tr>");
		        		htmlCode.append("<tr><td><br/></td></tr>");
		        	}
		        	
		        	htmlCode.append("</table></br>");
		    	}
			}else if(selectedOptions[i].equals("patent")) { //Patents
				patentList = getPatentList();
				if (patentList != null) {
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style='color:#000000; '><b><u>Patents</u></b></td></tr>");
		        	
		        	List<Patent> filteredList = filterPatentListByRIDate(patentList);
		        	//filteredList = (List<Patent>) filteredByCDCFGenDate(filteredList, selectedCdcfPeriodList, "Patent");
		        	
		        	for(Patent patent: filteredList) {
		        		htmlCode.append("<tr><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + patent.getPatentName() + "</td></tr>" +
								"<tr class = 'chineseChar' style = 'color: #444444; font-size: 12px;'><td colspan='3'>" + patent.getShortDesc() + " - " + 
								patent.getPatentGranted()  + "/" + patent.getPatentType() + 
								"</td></tr>");  
		        	}
		        	htmlCode.append("</table></br>");
				}
			}else if(selectedOptions[i].equals("kt")) { //KT Activities
				List<KtFormSummary> ktFormSummaryList = fDao.getKtFormSummaryList(iUserInfo.getStaff_number(), selectedStartDate, selectedEndDate, selectedCdcfPeriodList);
				if (ktFormSummaryList != null) {
		        	htmlCode.append("<table width = '100%'>" +
										"<colgroup>" + 
											"<col width = '15%'><col width = '55%'><col width = '30%'>" +
										"</colgroup>" + 
										"<tr><td colspan='3' style='color:#000000; '><b><u>KT Activities</u></b></td></tr>");
					SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
					
					for(int j = 0; j < ktFormSummaryList.size(); j++) {
						if(j == 0 || !ktFormSummaryList.get(j - 1).getFormCode().equals(ktFormSummaryList.get(j).getFormCode())) {
							if(j > 0) {
								htmlCode.append("<tr><td></br></td></tr>");
							}
							htmlCode.append("<tr><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + 
											getKtFormName(ktFormSummaryList.get(j).getFormCode()) + "</td></tr>");
						}
						htmlCode.append("<tr style = 'color:#444444'><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>" + 
										"<span style = 'font-size: 8px;'>&#9632;</span> " + 
										ktFormSummaryList.get(j).getTitle() + "</td></tr>" + 
										"<tr style = 'color:#444444;'><td colspan='3' class = 'chineseChar' style = 'font-size: 12px;'>Date: " + 
										dateFormatter.format(ktFormSummaryList.get(j).getStartDate()));
						
						if(ktFormSummaryList.get(j).getEndDate() != null) {
							htmlCode.append(" - "  + dateFormatter.format(ktFormSummaryList.get(j).getEndDate()));
						}
						
						htmlCode.append("</td></tr>");
					}
					
		        	htmlCode.append("</table></br>");
				}
			}
		}
    	if(remarks.length() > 0) {
        	htmlCode.append("<table width = '100%'>" +
								"<colgroup>" + 
									"<col width = '15%'><col width = '55%'><col width = '30%'>" +
								"</colgroup>" + 
								"<tr><td colspan='3' class = 'category'><b>Remarks</b></td></tr>" + 
								"<tr><td colspan='3' class = 'chineseChar'>" + remarks + "</td></tr>" + 
							"</table>");      	
    	}

		return htmlCode.toString();
	}
	
	public File getPhotoFile() throws Exception {
		String base64Data = sInfo.getPhotoFile().substring(0, sInfo.getPhotoFile().length() - 2);

		byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
		ByteArrayInputStream bis = new ByteArrayInputStream(decodedBytes);
		BufferedImage image = ImageIO.read(bis);
		bis.close();

		File photoFile = new File("photoFile.png");
		ImageIO.write(image, "png", photoFile); 
		
		return photoFile;
	}
	
	public String getSpace(int spaceNo) {
		String spaceString = "";
		for(int i = 0; i < spaceNo; i++) {
			spaceString += "&#160;";
		}
		
		return spaceString;
	}
	
	public List<CdcfRptPeriod> getSelectedCdcfPeriodList()
	{
		if (getSelectedCdcfPeriods() != null) {
			List<String> tmpList = Arrays.asList(selectedCdcfPeriods);
			List<Integer> tmpIntegerList = new ArrayList<>();
			for(int i = 0; i < tmpList.size(); i++) {
				tmpIntegerList.add(Integer.valueOf(tmpList.get(i)));
			}
			if (tmpIntegerList.size() > 0) {
				selectedCdcfPeriodList = cdcfRptDao.getCdcfRptPeriodListByPeriodIds(tmpIntegerList);
			}
		}
		return selectedCdcfPeriodList;
	}
	
	
	public void setSelectedCdcfPeriodList(List<CdcfRptPeriod> selectedCdcfPeriodList)
	{
		this.selectedCdcfPeriodList = selectedCdcfPeriodList;
	}

	public List<? extends BaseRI> filteredByCDCFGenDate(List<? extends BaseRI> objectList, List<CdcfRptPeriod> selectedCdcfPeriodList, String classType){
		if(selectedCdcfPeriodList == null) {
			return objectList;
		}
		if(selectedCdcfPeriodList.size() > 0) {
		List filteredList = null;
		
		if(classType.equals("Output")) {
			filteredList = new ArrayList<Publication>();
		}if(classType.equals("Project")) {
			filteredList = new ArrayList<ProjectSummary>();
		}else if(classType.equals("Award")) {
			filteredList = new ArrayList<Award>();
		}else if(classType.equals("Patent")) {
			filteredList = new ArrayList<Patent>();
		}
		
		for(int i = 0; i < objectList.size(); i++) {
			if(objectList.get(i).getCDCFGenDate() != null) {
				for(CdcfRptPeriod period: selectedCdcfPeriodList) {
					if(period.getDate_from().getTime() <= objectList.get(i).getCDCFGenDate().getTime() && period.getDate_to().getTime() >= objectList.get(i).getCDCFGenDate().getTime()) {
						filteredList.add(objectList.get(i));
						break;
					}
				}
			}
		}
		
		return filteredList;
		}
		return objectList;
	}
	
	public List<Award> filterAwardListByRIDate(List<Award> objectList){
		if(getFilteredStartDate() == null && getFilteredEndDate() == null) {
			return objectList;
		}
		
		List<Award> filteredList = new ArrayList<Award>();	
	
		for(Award award: objectList) {
			int year = (award.getAwardYear() == null)? 0 : Integer.parseInt(award.getAwardYear());
			int month = (award.getAwardMonth() == null)? -1: Integer.parseInt(award.getAwardMonth()) - 1; 
			int date = (award.getAwardDay() == null)? 0: Integer.parseInt(award.getAwardDay());
			
			Calendar awardDate = Calendar.getInstance();
			if(year > 0) {awardDate.set(Calendar.YEAR, year);}
			if(month >= 0) {awardDate.set(Calendar.MONTH, month);}
			if(date > 0) {awardDate.set(Calendar.DATE, date);}
			
			if(getFilteredStartDate() != null && getFilteredEndDate() != null) {
				if (getFilteredStartDate().compareTo(awardDate.getTime()) < 1 && getFilteredEndDate().compareTo(awardDate.getTime()) > -1) {
					filteredList.add(award);
				}
			}
			if(getFilteredStartDate() != null && getFilteredEndDate() == null) {
				if (getFilteredStartDate().compareTo(awardDate.getTime()) < 1) {
					filteredList.add(award);
				}
			}
			if(getFilteredStartDate() == null && getFilteredEndDate() != null) {
				if (getFilteredEndDate().compareTo(awardDate.getTime()) > -1) {
					filteredList.add(award);
				}
			}
		}
		
		return filteredList;
	}	
	
	public List<ProjectDetails_P> filterProjectListByRIDate(List<ProjectDetails_P> objectList){
		if(getFilteredStartDate() == null && getFilteredEndDate() == null) {
			return objectList;
		}
		
		List<ProjectDetails_P> filteredList = new ArrayList<ProjectDetails_P>();
		for(ProjectDetails_P obj: objectList) {
			ProjectHeader_P header = obj.getProjectHeader_p();
			
			int startYear = (header.getFrom_year() != null)? header.getFrom_year(): 0;
			int startMonth = (header.getFrom_month() != null)? header.getFrom_month() - 1: -1;
			int startDay = (header.getFrom_day() != null)? header.getFrom_day(): 0;
			
			int endYear = (header.getTo_year() != null)? header.getTo_year(): 0;
			int endMonth = (header.getTo_month() != null)? header.getTo_month(): -1;
			int endDay = (header.getTo_day() != null)? header.getTo_day(): 0;
			
			Calendar startDate = Calendar.getInstance();
			Calendar endDate = Calendar.getInstance();
			
			startDate.clear();
			endDate.clear();
			
			if(startYear > 0) {startDate.set(Calendar.YEAR, startYear);}
			if(startMonth >= 0) {startDate.set(Calendar.MONTH, startMonth);}
			if(startDay > 0) {startDate.set(Calendar.DATE, startDay);}
			if(endYear > 0) {endDate.set(Calendar.YEAR, endYear);}
			if(endMonth >= 0) {endDate.set(Calendar.MONTH, endMonth);}
			if(endDay > 0) {endDate.set(Calendar.DATE, endDay);}
			
			if(getFilteredStartDate() != null && getFilteredEndDate() != null) {
				if ((getFilteredStartDate().compareTo(startDate.getTime()) < 1 && getFilteredEndDate().compareTo(startDate.getTime()) > -1) ||
						(getFilteredStartDate().compareTo(endDate.getTime()) < 1 && getFilteredEndDate().compareTo(endDate.getTime()) > -1)) {
					filteredList.add(obj);
				}
			}
			if(getFilteredStartDate() != null && getFilteredEndDate() == null) {
				if (getFilteredStartDate().compareTo(startDate.getTime()) < 1 || getFilteredStartDate().compareTo(endDate.getTime()) < 1) {
					filteredList.add(obj);
				}
			}
			if(getFilteredStartDate() == null && getFilteredEndDate() != null) {
				if (getFilteredEndDate().compareTo(startDate.getTime()) > -1 || getFilteredEndDate().compareTo(endDate.getTime()) > -1) {
					filteredList.add(obj);
				}
			}
		}
		
		return filteredList;
	}
	
	public List<Integer> getProjectIdList(List<ProjectDetails_P> objList){
		List<Integer> projectIdList = new ArrayList<Integer>();
		
		for(ProjectDetails_P project: objList) {
			projectIdList.add(project.getPk().getProject_no());
		}
		
		return projectIdList;
	}
	
	public List<ProjectDetails_P> filterProjectListByFundSource() {
		if(selectedFundingSources.length == 0) {
			return projectList;
		}
		
		List<ProjectDetails_P> filteredList = new ArrayList<ProjectDetails_P>();
		
		for(ProjectDetails_P project: projectList) {
			ProjectHeader_P header = project.getProjectHeader_p();
			for(int i = 0; i < selectedFundingSources.length; i++) {
				if(header.getSap_funding_source().equals(selectedFundingSources[i])) {
					filteredList.add(project);
					break;
				}
			}
		}
		
		return filteredList;
	}
	
	public List<Patent> filterPatentListByRIDate(List<Patent> objList)
	{
		if(getFilteredStartDate() == null && getFilteredEndDate() == null) {
			return objList;
		}
		
		List<Patent> filteredList = new ArrayList<Patent>();	
	
		for(Patent patent: patentList) {
			int year = (patent.getPatentYear() == null)? 0 : Integer.parseInt(patent.getPatentYear());
			int month = (patent.getPatentMonth() == null)? 0 : Integer.parseInt(patent.getPatentMonth()) - 1; 
			int date = (patent.getPatentDay() == null)? 0 : Integer.parseInt(patent.getPatentDay());;
			
			Calendar patentDate = Calendar.getInstance();
			patentDate.clear();
			if(year > 0) {patentDate.set(Calendar.YEAR, year);}
			if(month >= 0) {patentDate.set(Calendar.MONTH, month);}
			if(date > 0) {patentDate.set(Calendar.DATE, date);}
			
			if(getFilteredStartDate() != null && getFilteredEndDate() != null) {
				if (getFilteredStartDate().compareTo(patentDate.getTime()) < 1 && getFilteredEndDate().compareTo(patentDate.getTime()) > -1) {
					filteredList.add(patent);
				}
			}
			if(getFilteredStartDate() != null && getFilteredEndDate() == null) {
				if (getFilteredStartDate().compareTo(patentDate.getTime()) < 1) {
					filteredList.add(patent);
				}
			}
			if(getFilteredStartDate() == null && getFilteredEndDate() != null) {
				if (getFilteredEndDate().compareTo(patentDate.getTime()) > -1) {
					filteredList.add(patent);
				}
			}
		}
		
		return filteredList;
	}
	
	public List<Publication> filterOutputListByRIDate(List<Publication> objList)
	{	
		if(getFilteredStartDate() == null && getFilteredEndDate() == null) {
			return objList;
		}
		
		List<Publication> filteredList = new ArrayList<Publication>();
		
		for(Publication publication: objList) {
			Calendar startDate = Calendar.getInstance();
			Calendar endDate = Calendar.getInstance();
			startDate.clear();
			endDate.clear();
			
			if(publication.getFromYear() != null) {startDate.set(Calendar.YEAR, Integer.parseInt(publication.getFromYear()));}
			if(publication.getFromMonth() != null) {startDate.set(Calendar.MONTH, Integer.parseInt(publication.getFromMonth()));}
			//if(publication.getToYear() != null) {endDate.set(Calendar.YEAR, Integer.parseInt(publication.getToYear()));}
			//if(publication.getToMonth() != null) {endDate.set(Calendar.MONTH, Integer.parseInt(publication.getToMonth()));}

			if(getFilteredStartDate() != null && getFilteredEndDate() != null) {
				if (getFilteredStartDate().compareTo(startDate.getTime()) < 1 && getFilteredEndDate().compareTo(startDate.getTime()) > -1) {
					filteredList.add(publication);
				}
			}
			if(getFilteredStartDate() != null && getFilteredEndDate() == null) {
				if (getFilteredStartDate().compareTo(startDate.getTime()) < 1) {
					filteredList.add(publication);
				}
			}
			if(getFilteredStartDate() == null && getFilteredEndDate() != null) {
				if (getFilteredEndDate().compareTo(startDate.getTime()) > -1) {
					filteredList.add(publication);
				}
			}
		}
		
		return filteredList;
	}
	
	public void goToExportMyDataWithRGC() throws Exception {
		FacesContext.getCurrentInstance().getExternalContext().redirect("exportDataWithRGC.xhtml");
	}

	public int convertIntoNumeric(String xVal)
	{
	 try
	  { 
	     return Integer.parseInt(xVal);
	  }
	 catch(Exception ex) 
	  {
	     return 0; 
	  }
	}

	
	public String[] getSelectedOptions()
	{
		return selectedOptions;
	}

	
	public void setSelectedOptions(String[] selectedOptions)
	{
		this.selectedOptions = selectedOptions;
	}

	
	public List<CdcfRptPeriod> getCdcfPeriodList()
	{
		if (cdcfPeriodList == null) {
			List<CdcfRptPeriod> tmpCdcfPeriodList = cdcfRptDao.getCdcfRptPeriodList();
			cdcfPeriodList = tmpCdcfPeriodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return cdcfPeriodList;
	}

	
	public void setCdcfPeriodList(List<CdcfRptPeriod> cdcfPeriodList)
	{
		this.cdcfPeriodList = cdcfPeriodList;
	}

	
	public String[] getSelectedCdcfPeriods()
	{
		return selectedCdcfPeriods;
	}

	
	public void setSelectedCdcfPeriods(String[] selectedCdcfPeriods)
	{
		this.selectedCdcfPeriods = selectedCdcfPeriods;
	}

	
	public String[] getSelectedOutputTypes()
	{
		return selectedOutputTypes;
	}

	
	public void setSelectedOutputTypes(String[] selectedOutputTypes)
	{
		this.selectedOutputTypes = selectedOutputTypes;
	}

	public Date getFilteredStartDate()
	{
		Date startDate = selectedStartDate;
		if (getSelectedCdcfPeriodList() != null) {
			if (selectedCdcfPeriodList.size() > 0) {
				Date minDate = selectedCdcfPeriodList.stream().map(u -> u.getDate_from()).min(Date::compareTo).get();
				if (startDate == null) {
					startDate = minDate;
				}else {
					if (startDate.compareTo(minDate) > 0) {
						startDate = minDate;
					}
				}
			}
		}
		return startDate;
	}
	
	public Date getFilteredEndDate()
	{
		Date endDate = selectedEndDate;
		if (getSelectedCdcfPeriodList() != null) {
			if (selectedCdcfPeriodList.size() > 0) {
				Date maxDate = selectedCdcfPeriodList.stream().map(u -> u.getDate_to()).max(Date::compareTo).get();
				if (endDate == null) {
					endDate = maxDate;
				}else {
					if (endDate.compareTo(maxDate) < 1) {
						endDate = maxDate;
					}
				}
			}
		}
		return endDate;
	}
	
	public Date getSelectedStartDate()
	{
		
		return selectedStartDate;
	}

	
	public void setSelectedStartDate(Date selectedStartDate)
	{
		this.selectedStartDate = selectedStartDate;
	}

	
	public Date getSelectedEndDate()
	{
		return selectedEndDate;
	}

	
	public void setSelectedEndDate(Date selectedEndDate)
	{
		this.selectedEndDate = selectedEndDate;
	}

	
	public String[] getSelectedFundingSources()
	{
		return selectedFundingSources;
	}

	
	public void setSelectedFundingSources(String[] selectedFundingSources)
	{
		this.selectedFundingSources = selectedFundingSources;
	}

	
	public List<FundSource> getFundSource_list()
	{
		if (fundSource_list == null) {
			fundSource_list = projDao.getFundSourceList(1);
		}
		return fundSource_list;
	}

	
	public void setFundSource_list(List<FundSource> fundSource_list)
	{
		this.fundSource_list = fundSource_list;
	}

	
	public String getRemarks()
	{
		return remarks;
	}

	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}
	
	public void onSelectAllExportData() {
		if (selectAllExportData) {
			selectedOptions = exportDataKeys;
		}else {
			selectedOptions = new String[0];
		}
    }

	
	
	public boolean isSelectAllExportData()
	{
		return selectAllExportData;
	}

	
	public void setSelectAllExportData(boolean selectAllExportData)
	{
		this.selectAllExportData = selectAllExportData;
	}

	public List<SelectItem> getExportDataList()
	{
		if (exportDataList == null) {
			exportDataList = new ArrayList<SelectItem>();
			for (int i = 0; i < exportDataValues.length; i++) {
				SelectItem option = new SelectItem(exportDataKeys[i], exportDataValues[i]);
				exportDataList.add(option);
			}
		}
		return exportDataList;
	}

	
	public void setExportDataList(List<SelectItem> exportDataList)
	{
		this.exportDataList = exportDataList;
	}
}
