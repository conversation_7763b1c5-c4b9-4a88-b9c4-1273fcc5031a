package hk.eduhk.rich.cv;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.el.ValueExpression;
import javax.faces.application.FacesMessage;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.model.structure.PageDimensions;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.wml.Body;
import org.docx4j.wml.ObjectFactory;
import org.docx4j.wml.SectPr;
import org.docx4j.wml.SectPr.PgMar;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.omnifaces.util.Faces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.datatable.DataTableBase;
import org.primefaces.event.ReorderEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;

import com.google.common.base.Strings;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;

import hk.eduhk.rich.entity.Authorship;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.award.Award;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.employment.EmploymentHistory_edit;
import hk.eduhk.rich.entity.employment.employmentHistoryDao;
import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.project.ProjectHeader_P;
import hk.eduhk.rich.entity.publication.OutputAddl_P;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.staff.InternetUserInfo;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffInfo;
import hk.eduhk.rich.entity.staff.StaffProfileDisplay;

@ManagedBean(name = "cvRGCTemplateView")
@ViewScoped
@SuppressWarnings("serial")
public class CvRGCTemplateView extends ManageRIView implements Serializable{
	
	private static Logger logger = Logger.getLogger(CvRGCTemplateView.class.getName());
	private String paramPid;
	
	private StaffIdentity sIdentity = null;
	private InternetUserInfo iUserInfo = null;
	private StaffInfo sInfo = null;
	private StaffCv savedOption = null;
	private List<EmploymentHistory_edit> employmentHistList_edit = null;
	private List<EmploymentHistory_edit> currentJobInfoList = null;
	private List<String> journalTypeList = null;
	
	private List<ProjectDetails_P> projectList = null;
	private HashMap<String, String> fundSourceMap = null;
	private List<OutputDetails_P> outputList = null;
	private HashMap<String, String> outputTypeMap = null;
	private HashMap<String, String> authorshipTypeMap = null;
	private List<Award> awardList = null;
	private String other;
	private boolean sectionAB = false;
	private boolean awardReorder = false;
	
	private String citation;
	
	private List<ProjectDetails_P> selectedProject;
	private List<ProjectDetails_P> filteredProject;
	private List<OutputDetails_P> selectedPublication;
	private List<OutputDetails_P> filteredPublication;
	private List<OutputDetails_P> selectedPublicationA;
	private List<OutputDetails_P> selectedPublicationB;
	private List<Award> selectedAward;
	private List<Award> filteredAward;
	private List<Award> orderedAward;
	
	private StaffDAO sdao = StaffDAO.getInstance();
	private CvDAO cdao = CvDAO.getInstance();
	private PublicationDAO pDao = PublicationDAO.getInstance();
		
	public String getParamPid(){
		return paramPid;
	}

	public void setParamPid(String paramPid){
		this.paramPid = paramPid;
	}

	public StaffIdentity getsIdentity(){
		return sIdentity;
	}
	
	public void setsIdentity(StaffIdentity sIdentity){
		this.sIdentity = sIdentity;
	}

	public InternetUserInfo getiUserInfo() throws IOException {
		if (iUserInfo == null && paramPid != null) {
			List<InternetUserInfo>tmpList = getiUserInfoList();
			String email = "";
			if (tmpList != null) {
				iUserInfo = tmpList.get(0);
				email = (iUserInfo.getEmail() != "" && iUserInfo.getEmail() != null)?iUserInfo.getEmail():"";
			}
			
			for (InternetUserInfo t : tmpList) {

				if (t.getEmail() != "" && t.getEmail() != null) {
					if (!email.contains(t.getEmail())) {
						if (email != "") {
							email += " / ";
						}
						email += t.getEmail();
					}
				}
			}
			
			if(email.equals("")) {
				email += "Not available";
			}
			
			iUserInfo.setEmail(email);
		}
		
		return iUserInfo;	
	}	
	
	public void setiUserInfo(InternetUserInfo iUserInfo){
		this.iUserInfo = iUserInfo;
	}

	public StaffInfo getsInfo() throws IOException {
		if (sInfo == null && paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			sInfo = dao.getStaffInfo(Integer.parseInt(paramPid));
			if (sInfo != null) {
				if (sInfo.getResearch_interest()!=null) {
	    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replace("( 69 , 69 , 69 )","( 0 , 0 , 0 )")));
	    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<ul>","<ul style=\"margin: 0px\">")));
	    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<ol>","<ol style=\"margin: 0px\">")));
	    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<p><br /></p>","<br/>")));
					sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<p>","")));
					sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("</p>","<br/>")));

				}
				if (sInfo.getTeaching_interest()!=null) {
					//System.out.println(sInfo.getPid());
					sInfo.setTeaching_interest(sInfo.getTeaching_interest().replace("&#160;"," "));
					sInfo.setTeaching_interest(sInfo.getTeaching_interest().replaceAll("<p><br/></p>","<br/>"));
					sInfo.setTeaching_interest(sInfo.getTeaching_interest().replaceAll("<p>",""));
					sInfo.setTeaching_interest(sInfo.getTeaching_interest().replaceAll("</p>",""));
				}
				if (sInfo.getExt_appt()!=null) {
					sInfo.setExt_appt(sInfo.getExt_appt().replace("&#160;"," "));
					sInfo.setExt_appt(sInfo.getExt_appt().replaceAll("<p><br /></p>","<br/>"));
					sInfo.setExt_appt(sInfo.getExt_appt().replaceAll("<p>",""));
					sInfo.setExt_appt(sInfo.getExt_appt().replaceAll("</p>",""));
				}
				if (sInfo.getProfile()!=null) {
					sInfo.setProfile(sInfo.getProfile().replace("&#160;"," "));
					sInfo.setProfile(sInfo.getProfile().replaceAll("<p><br /></p>","<br>"));
					sInfo.setProfile(sInfo.getProfile().replaceAll("<p>",""));
					sInfo.setProfile(sInfo.getProfile().replaceAll("</p>","<br>"));
				}
				
			}
		}
		return sInfo;	
	}

	public void setsInfo(StaffInfo sInfo){
		this.sInfo = sInfo;
	}
	
	public StaffCv getSavedOption(){
		return savedOption;
	}

	public void setSavedOption(StaffCv savedOption){
		this.savedOption = savedOption;
	}

	public List<EmploymentHistory_edit> getEmploymentHistList_edit(){
		if (employmentHistList_edit == null) {
			employmentHistoryDao dao = employmentHistoryDao.getInstance();
			int user_pid = Integer.parseInt(paramPid);
			employmentHistList_edit = dao.getEmploymentHistory_edit(user_pid);			
		}
		
		if(currentJobInfoList == null) {
			currentJobInfoList = new ArrayList<>();
			for(EmploymentHistory_edit history: employmentHistList_edit) {
				if(history.getIs_current() == 'Y') {
					currentJobInfoList.add(history);
				}				
			}
			
			for(EmploymentHistory_edit history: currentJobInfoList) {
				employmentHistList_edit.remove(history);
			}
		}
		
		return employmentHistList_edit;
	}

	public void setEmploymentHistList_edit(List<EmploymentHistory_edit> employmentHistList_edit){
		this.employmentHistList_edit = employmentHistList_edit;
	}
	
	public List<EmploymentHistory_edit> getCurrentJobInfoList(){	
		return currentJobInfoList;
	}
	
	public void setCurrentJobInfo(List<EmploymentHistory_edit> currentJobInfoList){
		this.currentJobInfoList = currentJobInfoList;
	}
	
	
	public List<String> getJournalTypeList()
	{
		if (journalTypeList == null) {
			journalTypeList = new ArrayList<String>();
			List<OutputType> tmpJournalTypeList = new ArrayList<OutputType>();
			List<OutputType> lvOneList = pDao.getOutputTypeList(1);
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			for (OutputType o:lvOneList) {
				if(o.getDescription().equals("Journal Publications") || o.getDescription().equals("Conference Papers")) {
					tmpJournalTypeList.add(o);
				}
				List<OutputType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				tmpJournalTypeList.addAll(tmpLvTwoList);
			}
			
			for(OutputType o: tmpJournalTypeList) {
				journalTypeList.add(o.getPk().getLookup_code());
			}
		}
			
		return journalTypeList;
	}

	public void setOutputTypeList(List<String> journalTypeList){
		this.journalTypeList = journalTypeList;
	}

	public List<ProjectDetails_P> getProjectList() throws SQLException, IOException{
		if (projectList == null && paramPid != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "TAB_PROJECT");
			boolean display = (obj.getShowInd().equals("Y"))?true:false;
			if (display) {
				ProjectDAO dao = ProjectDAO.getInstance();
				projectList = dao.getProjectDetails_P_byStaffNo(getStaffNo(), "P");
				projectList = projectList.stream()
						.filter(y -> "Y".equals(y.getProjectDetails_q().getConsent_ind()))
						.filter(y -> "Y".equals(y.getProjectDetails_q().getDisplay_ind()))
						.collect(Collectors.toList());
				for (ProjectDetails_P p:projectList) {
					if (!Strings.isNullOrEmpty(p.getProjectHeader_p().getProject_summary()))
						p.getProjectHeader_p().setProject_summary(p.getProjectHeader_p().getProject_summary().replaceAll("(\r\n|\n)", "<br/>"));
					if (!Strings.isNullOrEmpty(p.getProjectHeader_p().getProject_summary_2()))
						p.getProjectHeader_p().setProject_summary_2(p.getProjectHeader_p().getProject_summary_2().replaceAll("(\r\n|\n)", "<br/>"));
				}
			}
		}	
		return projectList;
	}

	public void setProjectList(List<ProjectDetails_P> projectList){
		this.projectList = projectList;
	}

	public HashMap<String, String> getFundSourceMap(){
		if(fundSourceMap == null) {
			ProjectDAO projDao = ProjectDAO.getInstance();
			List<FundSource> fundSourceList = projDao.getFundSourceList(1);
			fundSourceMap = new HashMap<String, String>();
			for(FundSource current: fundSourceList) {
				fundSourceMap.put(current.getPk().getLookup_code(), current.getDescription());
			}
		}
		
		return fundSourceMap;
	}
	
	public void setFundSourceMap(HashMap<String, String> fundSourceMap){
		this.fundSourceMap = fundSourceMap;
	}
	
	public List<OutputDetails_P> getOutputList() throws Exception{
	
		if(outputList == null) {
			outputList = pDao.getOutputDetails_P_byStaffNo(getStaffNo(), "P");
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			
			for (OutputDetails_P p:outputList) {
				if (Strings.isNullOrEmpty(p.getOutputHeader_p().getSap_output_type())) {
					p.getOutputHeader_p().setSap_output_type("0");
				}
				List<OutputType> tmpList = lvTwoList.stream()
														.filter(y -> p.getOutputHeader_p().getSap_output_type().equals(y.getPk().getLookup_code()))
														.collect(Collectors.toList());
				if (!tmpList.isEmpty()) {
					p.getOutputHeader_p().setOutput_lookup_code(tmpList.get(0).getParent_lookup_code());
				}else {
					p.getOutputHeader_p().setOutput_lookup_code("0");
				}
				p.getOutputHeader_p().setApa_citation(genCitation(p));
			}
		}

		return outputList;
	}
	
	public void setOutputList(List<OutputDetails_P> outputList){
		this.outputList = outputList;
	}

	public HashMap<String, String> getOutputTypeMap(){
		if(outputTypeMap == null) {
			List<OutputType> outputTypeList = pDao.getOutputTypeList(2);
			outputTypeMap = new HashMap<String, String>();
			for(OutputType type: outputTypeList) {
				outputTypeMap.put(type.getPk().getLookup_code(), type.getDescription());
			}
		}
		
		return outputTypeMap;
	}
	
	public void setOutputTypeMap(HashMap<String, String> outputTypeMap){
		this.outputTypeMap = outputTypeMap;
	}

	public HashMap<String, String> getAuthorshipTypeMap(){
		if(authorshipTypeMap == null) {		
			List<Authorship> authorshipTypeList = pDao.getAuthorshipList(1);
			authorshipTypeMap = new HashMap<String, String>();
			for(Authorship authorship: authorshipTypeList) {
				authorshipTypeMap.put(authorship.getPk().getLookup_code(), authorship.getDescription());
			}
		}
		
		return authorshipTypeMap;
	}

	public void setAuthorshipTypeMap(HashMap<String, String> authorshipTypeMap){
		this.authorshipTypeMap = authorshipTypeMap;
	}

	public List<Award> getAwardList() throws SQLException, IOException{
		if (awardList == null && paramPid != null) {
			int pid = convertIntoNumeric(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "TAB_AWARD");
			boolean display = (obj.getShowInd().equals("Y"))?true:false;
			if (display) {
				AwardDAO dao = AwardDAO.getInstance();
				awardList = dao.getCVAwardList(pid, getStaffNo(), null, null);
			}
		}
		return awardList;
	}

	public void setAwardList(List<Award> awardList){
		this.awardList = awardList;
	}

	public String getOther(){
		return other;
	}

	public void setOther(String other){
		this.other = other;
	}

	public List<ProjectDetails_P> getSelectedProject(){
		return selectedProject;
	}
	
	public void setSelectedProject(List<ProjectDetails_P> selectedProject){
		this.selectedProject = selectedProject;
	}
	
	public List<ProjectDetails_P> getFilteredProject(){
		return filteredProject;
	}

	public void setFilteredProject(List<ProjectDetails_P> filteredProject){
		this.filteredProject = filteredProject;
	}

	public List<OutputDetails_P> getSelectedPublication(){
		return selectedPublication;
	}

	public void setSelectedPublication(List<OutputDetails_P> selectedPublication){
		this.selectedPublication = selectedPublication;		
	}

	public List<OutputDetails_P> getFilteredPublication(){
		return filteredPublication;
	}
	
	public void setFilteredPublication(List<OutputDetails_P> filteredPublication){
		this.filteredPublication = filteredPublication;
	}
	
	public List<OutputDetails_P> getSelectedPublicationA(){
		return selectedPublicationA;
	}

	public void setSelectedPublicationA(List<OutputDetails_P> selectedPublicationA){
		this.selectedPublicationA = selectedPublicationA;
	}

	public List<OutputDetails_P> getSelectedPublicationB(){
		return selectedPublicationB;
	}
	
	public void setSelectedPublicationB(List<OutputDetails_P> selectedPublicationB){
		this.selectedPublicationB = selectedPublicationB;
	}

	public List<Award> getFilteredAward(){
		return filteredAward;
	}

	public void setFilteredAward(List<Award> filteredAward){
		this.filteredAward = filteredAward;
	}
	
	public List<Award> getSelectedAward(){
		return selectedAward;
	}

	public void setSelectedAward(List<Award> selectedAward){
		this.selectedAward = selectedAward;
	}

	
	public List<Award> getOrderedAward()
	{
		return orderedAward;
	}

	
	public void setOrderedAward(List<Award> orderedAward)
	{
		this.orderedAward = orderedAward;
	}

	@PostConstruct
	public void init() throws Exception {
		if(paramPid == null) {
			sIdentity = sdao.getStaffDetailsByUserId(getCurrentUserId());
			this.paramPid = (sIdentity != null)? String.valueOf(sIdentity.getPid()):"";
		}
		if(selectedPublicationA == null) {
			selectedPublicationA = new ArrayList<>();
		}
		if(selectedPublicationB == null) {
			selectedPublicationB = new ArrayList<>();
		}
		if(savedOption == null) {
			savedOption = cdao.getStaffCv(Integer.valueOf(paramPid));
		}
		
		
		if(savedOption != null) {
			restoreSavedOption();
		}
		
	}
	
	public void restoreSavedOption() throws Exception {
		if(savedOption.getProject() != null) {
			String[] projectId = savedOption.getProject().split("/");
			List<Integer> idList = new ArrayList<>();
			
			if (projectId.length == 1)
				idList.add(Integer.valueOf(projectId[0]));
			else {
			
				for(int i = 0; i < projectId.length - 2; i++) 	
					idList.add(Integer.valueOf(projectId[i]));
				
	
				if(projectId[projectId.length - 2].charAt(0) < '0' || projectId[projectId.length - 2].charAt(0) > '9') {
					DataTable selectedProjectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedProjTable"); 
					DataTable projectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:projectTable"); 
					SortOrder sortOrder = null;
					String field = projectId[projectId.length - 2];
					String order = projectId[projectId.length - 1];
					
					if(order.equals("ASCENDING")) 
						sortOrder = SortOrder.ASCENDING;
					else 
						sortOrder = SortOrder.DESCENDING;
					
					

					if(field.equalsIgnoreCase("parseProjectDate_edit(datarow,true)"))
						field = "parseProjectDate_edit(datarow,true)";
					
						
					SortMeta sortBy = SortMeta.builder()
						                .field(field)
						                .order(sortOrder)
						                .build();
					
					//selectedProjectTable.setSortBy(sortBy);
					projectTable.setSortBy(sortBy);

				}else {
					
;					
					/*if(projectId[projectId.length - 2].equalsIgnoreCase("parseProjectDate_edit(datarow,true)"))
						idList.add(Integer.valueOf(projectId[projectId.length - 2]));
					else
						idList.add(Integer.valueOf(projectId[projectId.length - 2])); */
					idList.add(Integer.valueOf(projectId[projectId.length - 2]));
					idList.add(Integer.valueOf(projectId[projectId.length - 1]));
				}
				
			}
			
			
			if(selectedProject == null) 
				selectedProject = new ArrayList<>();
			selectedProject = cdao.getProjectDetails_PByIdList(idList, "P", sIdentity.getStaff_number());

		}
		
		
		if(savedOption.getOutputA() != null) {
			
			String[] outputId = savedOption.getOutputA().split("/");
			List<Integer> idList = new ArrayList<>();
			
			//When it has only one selected records
			if (outputId.length == 1) 
				idList.add(Integer.valueOf(outputId[0]));
			else {
					for(int i = 0; i < outputId.length - 2; i++) 		
						idList.add(Integer.valueOf(outputId[i]));
				
				
				if(outputId[outputId.length - 2].charAt(0) < '0' || outputId[outputId.length - 2].charAt(0) > '9') {
					DataTable selectedPublicationATable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedPublicationATable"); 
					SortOrder sortOrder = null;
					String field = outputId[outputId.length - 2];
					String order = outputId[outputId.length - 1];
					
					if(order.equals("ASCENDING")) 
						sortOrder = SortOrder.ASCENDING;
					else 
						sortOrder = SortOrder.DESCENDING;
					
					
					SortMeta sortBy = SortMeta.builder()
						                .field(field)
						                .order(sortOrder)
						                .build();
					
					selectedPublicationATable.setSortBy(sortBy);
				}else {
					idList.add(Integer.valueOf(outputId[outputId.length - 2]));
					idList.add(Integer.valueOf(outputId[outputId.length - 1]));
				}
			}
			
			
			selectedPublicationA = cdao.getOutputDetails_PByIds(idList);
			
			if(selectedPublication == null) 
				selectedPublication = new ArrayList<>();
			
			selectedPublication.addAll(selectedPublicationA);
		}
		
		if(savedOption.getOutputB() != null) {
			String[] outputId = savedOption.getOutputB().split("/");
			List<Integer> idList = new ArrayList<>();
			
			
			if (outputId.length == 1) 
				idList.add(Integer.valueOf(outputId[0]));
			else {
				
				for(int i = 0; i < outputId.length - 2; i++) 			
					idList.add(Integer.valueOf(outputId[i]));
			
				
				if(outputId[outputId.length - 2].charAt(0) < '0' || outputId[outputId.length - 2].charAt(0) > '9') {
					DataTable selectedPublicationATable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedPublicationBTable"); 
					SortOrder sortOrder = null;
					String field = outputId[outputId.length - 2];
					String order = outputId[outputId.length - 1];
					
					if(order.equals("ASCENDING"))
						sortOrder = SortOrder.ASCENDING;
					else 
						sortOrder = SortOrder.DESCENDING;
					
					
		
					
					SortMeta sortBy = SortMeta.builder()
						                .field(field)
						                .order(sortOrder)
						                .build();
					
					selectedPublicationATable.setSortBy(sortBy);
				}else {
					idList.add(Integer.valueOf(outputId[outputId.length - 2]));
					idList.add(Integer.valueOf(outputId[outputId.length - 1]));
				}
			}


			selectedPublicationB = cdao.getOutputDetails_PByIds(idList);
			
			if(selectedPublication == null) 
				selectedPublication = new ArrayList<>();
			
			selectedPublication.addAll(selectedPublicationB);
		}

		if(savedOption.getAward() != null) 
			selectedAward = getselectedAwardList();
		
		other = savedOption.getOther();
	}
	
	public List<Award> getselectedAwardList() throws SQLException{
		
		String[] awardId = savedOption.getAward().split("/");
		List<Integer> idList = new ArrayList<>();
		
		
		if (awardId.length == 1) 
			idList.add(Integer.valueOf(awardId[0]));
		else {
			for(int i = 0; i < awardId.length - 2; i++) 
				idList.add(Integer.valueOf(awardId[i]));
			
			
			if(awardId[awardId.length - 2].charAt(0) < '0' || awardId[awardId.length - 2].charAt(0) > '9') {
				DataTable selectedAwardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedAwardTable"); 
				DataTable awardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:awardTable"); 
				SortOrder sortOrder = null;
				String field = awardId[awardId.length - 2];
				String order = awardId[awardId.length - 1];
				
				if(order.equals("ASCENDING")) 
					sortOrder = SortOrder.ASCENDING;
				else 
					sortOrder = SortOrder.DESCENDING;
				
				
				SortMeta sortBy = SortMeta.builder()
					                .field(field)
					                .order(sortOrder)
					                .build();
				
				selectedAwardTable.setSortBy(sortBy);
				awardTable.setSortBy(sortBy);
			}else {
				idList.add(Integer.valueOf(awardId[awardId.length - 2]));
				idList.add(Integer.valueOf(awardId[awardId.length - 1]));
			}
		}
		if(selectedAward == null) 
			selectedAward = new ArrayList<>();
		
		selectedAward = cdao.getAwardListByIdList(idList);

		return selectedAward;
	}
	
	public List<InternetUserInfo> getiUserInfoList() throws IOException {
		CvDAO dao = CvDAO.getInstance();
		List <InternetUserInfo>iUserInfoList = dao.getInternetUserInfo(Integer.parseInt(paramPid));
		return iUserInfoList;
	}	
	
	public String parseJobDate(Date date) {
		if (date != null) {
			return parseJobDate(date, "MM/yyyy");
		}else {
			return "";
		}
	}
	
	public String parseJobDate(Date date, String format) {
		String result = "";
		if (date != null) {
			SimpleDateFormat dateFormatter = new SimpleDateFormat(format);
			result = dateFormatter.format(date);	
		}
		return result;	
	}
		
	public String getStaffNo() throws IOException{		
		String staffNo = null;
		if (paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			staffNo = dao.getStaffNo(convertIntoNumeric(paramPid));
		}
		return staffNo;
	}
	
	public void onProjectSelect(SelectEvent<ProjectDetails_P> event) {
		printMessage(FacesMessage.SEVERITY_INFO, "Selected a Project.", "");
	}
	
	public void onProjectUnselect(UnselectEvent<ProjectDetails_P> event) {
		selectedProject.remove(event.getObject());
		printMessage(FacesMessage.SEVERITY_INFO, "Unselected a Project.", "");
	}
	
	public void selectAllProject(ToggleSelectEvent event) {
		if(event.isSelected()) {
			if( ! (filteredProject != null && filteredProject.size() > 0))
				selectedProject = projectList;			
			printMessage(FacesMessage.SEVERITY_INFO, "Selected " + selectedProject.size() +  " Projects", "");
		}
		else {
			if( filteredProject != null && filteredProject.size() > 0) {
				selectedProject.remove(filteredProject);
				printMessage(FacesMessage.SEVERITY_INFO, "Unselected " + filteredProject.size() +  " Projects", "");
			}
					
			else {
				selectedProject = new ArrayList<>();
				DataTable projectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:projectTable"); 
				projectTable.setSelectedRowKeys(null);
				printMessage(FacesMessage.SEVERITY_INFO, "Unselected all Projects.", "");
			}
		}
	}
	
	
	
	
	public void onPublicationSelect(SelectEvent<OutputDetails_P> event) {
		String section = groupSelectedPublication(event.getObject());
		if(section.equals("A")) {
			if(selectedPublicationA == null) {
				selectedPublicationA = new ArrayList<>();
			}
			
			if(selectedPublicationA.size() >= 5) {
				selectedPublication.remove(event.getObject());
				undoSelectionInPublicationTable();
				printMessage(FacesMessage.SEVERITY_ERROR, "Cannot select more than 5 publications in the recent five years.", "");
			}else {
				selectedPublicationA.add(event.getObject());
				selectedPublicationA = sortPublicationList(selectedPublicationA);
				printMessage(FacesMessage.SEVERITY_INFO, "Selected a publication in the recent five years.", "");
			}
		}else if(section.equals("B")) {
			if(selectedPublicationB == null) {
				selectedPublicationB = new ArrayList<>();
			}
			
			if(selectedPublicationB.size() >= 5) {
				selectedPublication.remove(event.getObject());
				undoSelectionInPublicationTable();
				printMessage(FacesMessage.SEVERITY_ERROR, "Cannot select more than 5 publications beyond the recent five years.", "");
			}else{
				selectedPublicationB.add(event.getObject());
				selectedPublicationB = sortPublicationList(selectedPublicationB);
				printMessage(FacesMessage.SEVERITY_INFO, "Selected a publication beyond the recent five years.", "");
			}
			
		}
	}
	
	public void undoSelectionInPublicationTable() {
		
		DataTable publicationTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:publicationTable"); 
		Set<String> rowKeySet = new HashSet<String>();
		for(OutputDetails_P output: selectedPublication) {
			rowKeySet.add(Integer.toString(output.getPk().getOutput_no()));
		}
		
		publicationTable.setSelectedRowKeys(rowKeySet);
	}
	
	public void onPublicationUnselect(UnselectEvent<OutputDetails_P> event) {
		
		String section = groupSelectedPublication(event.getObject());
		OutputDetails_P selectedPublication_pre ;
		
		if(section.equals("A")) {
			selectedPublication_pre = selectedPublicationA.stream().filter(t -> t.getPk().getOutput_no().equals(event.getObject().getPk().getOutput_no())).collect(Collectors.toList()).get(0);
			selectedPublicationA.remove(selectedPublication_pre);
		}
		else if (section.equals("B")) {
			selectedPublication_pre = selectedPublicationB.stream().filter(t -> t.getPk().getOutput_no().equals(event.getObject().getPk().getOutput_no())).collect(Collectors.toList()).get(0);
			selectedPublicationB.remove(selectedPublication_pre);
		}
		printMessage(FacesMessage.SEVERITY_INFO, "Unselected a Publication.", "");
	}
	
	public void resetPublicationList() {
		try {
			selectedPublication = new ArrayList<>();
			selectedPublicationA = new ArrayList<>();
			selectedPublicationB = new ArrayList<>();
			
			// Success message
			printMessage(FacesMessage.SEVERITY_INFO, "Unselect all Publications.", "");
		}
		catch (Exception e)
		{
			printMessage(FacesMessage.SEVERITY_INFO, "Cannot unselect all Publications.", "");
			logger.log(Level.WARNING, "Cannot unselect all the Publications, pid: " + paramPid, e);
		}
	}
	
	public void onAwardSelect(SelectEvent<Award> event) {
		//selectedAward.add(event.getObject());
		printMessage(FacesMessage.SEVERITY_INFO, "Selected a Prize and Award.", "");
	}
	
	public void onAwardUnselect(UnselectEvent<Award> event) {
		selectedAward.remove(event.getObject());
		printMessage(FacesMessage.SEVERITY_INFO, "Unselected a Prize and Award.", "");
	}
	
	public void selectAllAward(ToggleSelectEvent event) {
		if(event.isSelected()) {
			if(filteredAward != null && filteredAward.size() > 0) {
				selectedAward = filteredAward;
			}else{
				selectedAward = awardList;
			}
		
			printMessage(FacesMessage.SEVERITY_INFO, "Selected " + selectedAward.size() + " Prizes and Awards.", "");
		}else {
			
			if(filteredAward != null && filteredAward.size() > 0) {
				selectedAward.remove(filteredAward);
			}
			else {			
				selectedAward = new ArrayList<>();
				DataTable awardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:awardTable"); 
				awardTable.setSelectedRowKeys(null);
				printMessage(FacesMessage.SEVERITY_INFO, "Unselected all Prizes and Awards.", "");
			}
		}
	}
	
	public void printMessage(Severity severity, String summary, String content) {
		FacesMessage message = new FacesMessage(severity, summary, content);
		FacesContext.getCurrentInstance().addMessage(null, message);
	}
	
	public int convertIntoNumeric(String string) {
		return Integer.parseInt(string);
	}
	
	//Export PDF file
	public void exportCvInPdf() throws Exception{
		StringBuilder htmlCode = new StringBuilder();

    	//HTML Head
       	htmlCode.append("<!DOCTYPE html>" +
				"<html>" + 
					"<head>" + 
						"<title>The Education University of Hong Kong</title>" + 
						"<meta charset = 'utf-8'/> " + 
						"<link href='https://fonts.googleapis.com/css2?family=Noto+Serif+HK:wght@400;700&display=swap' rel='stylesheet'>" + 
						"<style>" +
						//Noto Serif HK for Chinese wordings
						".font {font-family: 'Noto Serif HK'; font-size:14px; line-height: 1.1;}" + 
						//".font {font-size: 15px; line-height: 1.1;}" + 
						"@page{margin-top:22mm; margin-bottom:22mm; margin-left:22mm; margin-right:22mm; padding:0;}" + 
						"body: {margin:0; padding:0;}" + 
						"</style>" +
					"</head><body style = 'font'>");
       	
       	//HTML Body
    	//Report Header
    	htmlCode.append("<body class = 'font'>" +
							"<div>" + 
								"<table width='100%'>" + 
									"<tr>" + 
										"<td style='text-align: center; font-weight: bold;'>Curriculum Vitae</td>" +
									"</tr>" + 
								"</table>" + 
							"<br>" + 
  							"</div>");
    	
    	//Name
    	htmlCode.append("<table width='100%' style = 'padding-bottom: 10px;'>" + 
							"<tr><td><span style='font-weight:bold;text-decoration:underline;'>Name:</span>" + 
							    "<span style='color: #000000;'> " + sIdentity.getSurname()+" " +sIdentity.getOthername()  + "</span></td></tr>" + 
						"</table>");
    	
    	//Email Address
    	htmlCode.append("<table width='100%' style = 'padding-bottom: 10px;'>" + 
							"<tr><td><span style='font-weight: bold;text-decoration: underline;'>Email Address:</span>" + 
							    "<span style='color: #000000;'> " + iUserInfo.getEmail() + "</span></td></tr>" + 
						"</table>");
    	
    	//Academic Qualifications 
    	htmlCode.append("<table width='100%' style = 'padding-bottom: 10px;'>" + 
				"<tr><td><span style='font-weight: bold;text-decoration: underline;'>Academic Qualifications</span></td></tr>" + 
				"<tr><td style='color: #000000;padding-left: 0px;'>" + sInfo.getProfile() + "</td></tr>" + 
			"</table></br>");
    	
    	//Career Overview
    	//Previous Academic Positions Held
    	if (employmentHistList_edit.size() > 0) {
	    	htmlCode.append("<table width='100%'>" + 
							"<colgroup>" + 
								"<col width = '20%'><col width = '80%'>" +
							"</colgroup>" +
							"<tr><td colspan = '2'><span style='font-weight: bold;text-decoration: underline;'>Previous Academic Positions Held</span></td></tr>");
	    	
	    	for(EmploymentHistory_edit history: employmentHistList_edit) {
	    		if (parseJobDate(history.getFrom_date(), "yyyy").equals(parseJobDate(history.getTo_date(), "yyyy"))) {
	    			htmlCode.append("<tr><td style='color: #000000; padding-left: 0 px; padding-top: 0px;' colspan='1' >" + parseJobDate(history.getFrom_date(), "yyyy") + "</td>" + 
	    			        "<td style='color: #000000;padding-left: 0 px; padding-bottom: 0px;' colspan='1'> " + history.getJob_title() +  ", " + history.getCompany() + "</td></tr>");   	
	    		}else{
	    			if(history.getTo_date() != null) {
	    			htmlCode.append("<tr><td style='color: #000000;padding-left: 0 px; padding-top: 0px;' colspan='1'>" + parseJobDate(history.getFrom_date(), "yyyy") + " - " +  parseJobDate(history.getTo_date(), "yyyy") + "</td>" + 
					        "<td style='color: #000000;padding-left: 0 px; padding-bottom: 0px;margin-top: 0px;' colspan='1'> " + history.getJob_title() +  ", " + history.getCompany() + "</td></tr>");   
	    			}
	    			else {
	    				htmlCode.append("<tr><td style='color: #000000;padding-left: 0 px; padding-top: 0px;' colspan='1'>" + parseJobDate(history.getFrom_date(), "yyyy") + "</td>" + 
						        "<td style='color: #000000;margin-top: 0px;padding-left: 0 px; padding-bottom: 0px;' colspan='1'> " + history.getJob_title() +  ", " + history.getCompany() + "</td></tr>");   
	    			}
	    		}
	    	}
	    			
			htmlCode.append("</table></br>");
    	}
    	
		//Current position
    	htmlCode.append("<table width='100%'>" + 
							"<colgroup>" + 
								"<col width = '20%'><col width = '80%'>" +
							"</colgroup>" +
							"<tr><td colspan='2'><span style='font-weight: bold;text-decoration: underline;'>Present Academic Position</span></td></tr>");
							for(EmploymentHistory_edit currentJobInfo: currentJobInfoList) {
		    					htmlCode.append("<tr style = 'color: #000000'><td colspan = '1' style='padding-left: 0 px; padding-top: 0px;'>" + parseJobDate(currentJobInfo.getFrom_date(), "yyyy") + " - Present</td>" +
    					        "<td colspan = '1'  style='padding-left: 0 px; padding-bottom: 0px;'>" + currentJobInfo.getJob_title() + ", " + currentJobInfo.getCompany() + "</td></tr>");
							}

    	htmlCode.append("</table></br>");
 
		
		//Research Interests
    	if (sInfo.getResearch_interest() != null) {
	    	htmlCode.append("<table width='100%'>" + 
								"<tr><td style='font-weight: bold;text-decoration: underline;'>Research Interests</td></tr>" + 
								"<tr><td style='color: #000000;'>" + sInfo.getResearch_interest() + "</td></tr>" + 
					        "</table><br/>");  
    	}
    	
    	//Projects
    	if(selectedProject != null) {
    		if (selectedProject.size() > 0) {
    		sortSelectedProject();
	    	htmlCode.append(
	    					"<table width='100%' cellspacing = '0'>" + 
								"<colgroup>" + 
									"<col width = '45%'><col width = '15%'><col width = '25%'><col width = '15%'>" +
								"</colgroup>" +
								"<tr><td colspan = '4'><span style='font-weight: bold; text-decoration: underline;'>Relevant Research Grant Records</span></td></tr>" + 
								"<tr style = 'color: #000000;'>" + 
	    							"<td style = 'border: 1px solid;padding:5px;' colspan = '1'>Project Title</td>" +
	    							"<td style = 'border: 1px solid;padding:5px;' colspan = '1'>Period</td>" +
	    							"<td style = 'border: 1px solid;padding:5px;' colspan = '1'>Funding Scheme</td>" +
	    							"<td style = 'border: 1px solid;padding:5px;' colspan = '1'>Funding Amount (HKD)</td>" +
	    						"</tr>");
	    	
	    	for(ProjectDetails_P project: selectedProject) {
	    		String title = "";
	    		title += (project.getProjectHeader_p().getTitle_1() == null)?"":project.getProjectHeader_p().getTitle_1();
	    		title += (project.getProjectHeader_p().getTitle_2() == null)?"":project.getProjectHeader_p().getTitle_2();
	    		title += (project.getProjectHeader_p().getTitle_3() == null)?"":project.getProjectHeader_p().getTitle_3();
	    		title += (project.getProjectHeader_p().getTitle_4() == null)?"":project.getProjectHeader_p().getTitle_4();
	    		title += (project.getInvestigator_type() == null)?"":(" (" + convertRole(project.getInvestigator_type()) + ")");
	    		
	    		htmlCode.append("<tr style = 'color: #000000'>" + 
	    							"<td style = 'border: 1px solid;padding:5px; border-collapse: collapse;'>" + title + "</td>" +
	    							"<td style = 'border: 1px solid;padding:5px; border-collapse: collapse;'>" + parseProjectDate(project, false) + "</td>" + 
	    							"<td style = 'border: 1px solid;padding:5px; border-collapse: collapse;'>" + fundSourceMap.get(project.getProjectHeader_p().getSap_funding_source()) + "</td>");
	    		
	    		if(project.getProjectHeader_p().getSap_grant_amt() != null) {
	    			htmlCode.append("<td style = 'border: 1px solid;padding:5px; border-collapse: collapse;'>" + rephraseFundAmount(project.getProjectHeader_p().getSap_grant_amt()) + "</td>");
	    		}else {
	    			htmlCode.append("<td style = 'border: 1px solid;padding:5px; border-collapse: collapse;'>Not applicable</td>");
	    		}
	    		
	    	}
	
		    htmlCode.append("</tr></table><br/>");
    		}
    	}
	    
	    //Publication 
    	if (selectedPublicationA.size() > 0 || selectedPublicationB.size() > 0) {
	    htmlCode.append("<br/><div style='padding-bottom:10px;'><span style='font-weight: bold; text-decoration: underline;'>Publication Records</span></div>"); 
	    
	    if (selectedPublicationA.size() > 0) {
		    htmlCode.append("<div style='padding-bottom:10px;' ><span style='font-weight: bold;'>Section A: Five most representative publications in the recent five years:</span></div>" + 
					"<table width='100%' cellspacing = '1' style = 'font-size:16px;'>"); 
		   int i = 1; 
		    for(OutputDetails_P output: selectedPublicationA) {
		    	htmlCode.append("<tr style='vertical-align:top'><td style='padding-left: 0px;padding-top: 0px;'>"+i+".</td><td>" + genCitation(output) + "</td></tr>");
		    	i++;
		    }
		    htmlCode.append("</table></br>");
	    }
	   
	    
	    if (selectedPublicationB.size() > 0) {
		    htmlCode.append("<div style='padding-bottom:10px;'><span style='font-weight: bold;'>Section B: Five most representative publications beyond the recent five years:</span></div>" + 
	    					"<table width='100%' cellspacing = '1' style = 'font-size:16px;'>"); 
		    int i = 1; 
		    for(OutputDetails_P output: selectedPublicationB) {
		    	htmlCode.append("<tr style='vertical-align:top'><td style='padding-left: 0px;padding-top: 0px;'>"+i+".</td><td>" + genCitation(output) + "</td></tr>");
		    	i++;
		    }
		    htmlCode.append("</table></br>");
		    }
    	}
    	
    	if (orderedAward != null)
    		selectedAward = getOrderedAward();
    	
	    //Prizes and awards
	    if(selectedAward.size() > 0) {
	    	
	    	
	    	sortSelectedAward();
		    htmlCode.append("<div style='font-weight: bold;'><u>Research-related Prizes and Awards</u></div>"+ 
							"<table width='100%' style = 'font-size:16px;'>"); 
		    
		    for(Award award: selectedAward) {
		    	htmlCode.append("<tr style = 'color: #000000;vertical-align:top;text-align:left;'>" + 
						"<td width = '10%'>" + award.getAwardYear()+ "</td>" +
						"<td width = '90%'>" + award.getAwardName()+ ", " + award.getOrgName() + "</td>" +
					"</tr>"); 
		    }
		    
		    htmlCode.append("</table></br>");
	    }
	    
	    //other
	    if(other.length() > 0) {
	    	String htmlOther = new String(other);
	    	htmlOther = htmlOther.replace("\n", "<br/>");
		    htmlCode.append("<div><span  style='font-weight: bold; text-decoration: underline;'>Others</span></span></div>"+ 
							"<table width='100%'>" + 
								"<tr style = 'color: #000000;' class = 'chineseChar'>" + 
									"<td width = '100%'>" + htmlOther + "</td>" +
								"</tr>" + 
							"</table>" + 
							"<br>");
	    }
       	
	    
	    htmlCode.append("</body></html>");
			    				
    	Document htmlDocument = Jsoup.parse(htmlCode.toString());
    	htmlDocument.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
    	
    	File pdf = new File("cv.pdf");
    	OutputStream output = null;
    	
	    try (OutputStream os = new FileOutputStream(pdf)) {
    	    PdfRendererBuilder builder = new PdfRendererBuilder();
    	    builder.toStream(os);
    	    builder.withW3cDocument(new W3CDom().fromJsoup(htmlDocument), "/");
    	    builder.run();
    	    
    	    //Render Report to the client window
			ExternalContext context =  FacesContext.getCurrentInstance().getExternalContext();
			
			context.responseReset();
			
			context.setResponseContentType("application/pdf");
			context.setResponseContentLength((int) pdf.length());
			
			context.setResponseHeader("Content-Disposition", "inline; filename=\"" + pdf.getName() + "\""); 
			context.setResponseHeader("Cache-Control", "private, must-revalidate");
			context.setResponseHeader("Expires", "-1");
			context.setResponseHeader("Pragma", "private");
			
			output = context.getResponseOutputStream();
	        Files.copy(pdf.toPath(), output);

	        os.flush();
	        os.close();
	        output.flush();
	        output.close();
			
			FacesContext.getCurrentInstance().responseComplete();
	    }
	}
	
	//Export Word File
	
	public void exportCvInDocx() throws Exception {
		StringBuilder htmlCode = new StringBuilder();
		
    	//HTML Head
       	htmlCode.append("<!DOCTYPE html>" +
				"<html>" + 
					"<head>" + 
						"<title>The Education University of Hong Kong</title>" + 
						"<meta charset = 'utf-8'/> " + 
						"<style>" + 
						"body {font-size:16px;line-height:1;}" + 
						".page-number:after {content: 'Page ' counter(page) ' of ' counter(pages); font-size:16px;}"+
						"</style>" + 
					"</head>");
       	
       	//HTML Body
    	//Report Header
    	htmlCode.append("<body>" +
							"<div>" + 
								"<table width='100%'>" + 
									"<tr>" + 
										"<td style='text-align: center; font-weight: bold; padding-bottom:10px; '>Curriculum Vitae</td>" +
									"</tr>" + 
								"</table>" + 
  							"</div>");
    	
    	//Name
    	htmlCode.append("<table width='100%'>" + 
    						"<tr><td><span style='font-weight: bold;'><u>Name:</u></span>" + 
							    "<span style='color: #000000;'> " + sIdentity.getSurname() +" "+ sIdentity.getOthername()+ "</span></td></tr>" + 
						"</table>");
    	
    	//Email Address
    	htmlCode.append("<table width='100%'>" + 
    						"<tr><td><span style='font-weight: bold;'><u>Email:</u></span>" + 
							    "<span style='color: #000000;'> " + iUserInfo.getEmail() + "</span></tr>" + 
						"</table>");
    	
    	//Academic Qualifications 
    	htmlCode.append("<table width='100%' style= 'justify-content: left;'>" +
    						"<tr><td><u style='font-weight: bold; padding-bottom:10px;'>Academic Qualifications</u></td></tr>" + 
							"<tr><td style='color: #000000;'>" + sInfo.getProfile() + "</td></tr>" + 
						"</table>");
    	
    	//Career Overview
    	htmlCode.append("<table width='100%'>" + "<colgroup>" + 
										"<col width = '20%'><col width = '80%'>" +
										"</colgroup>" +
				    					"<tr><td colspan='2'><u style='font-weight: bold; padding-bottom:10px;'>Previous Academic Positions Held</u></td></tr>");
    	
    	for(EmploymentHistory_edit history: employmentHistList_edit) {
    		if (parseJobDate(history.getFrom_date(), "yyyy").equals(parseJobDate(history.getTo_date(), "yyyy"))) {
    			htmlCode.append("<tr style='color: #000000;'><td colspan = '1' style = 'padding-left:0px;padding-top:0px'>" + parseJobDate(history.getFrom_date(), "yyyy") + "</td>" + 
			        	"<td colspan='1'>" + history.getJob_title() + ", " + history.getCompany() + "</td></tr>");   			
    		}else {
    			if ( history.getTo_date() != null){
    				htmlCode.append("<tr style='color: #000000;'><td colspan = '1' style = 'padding-left:0px;padding-top:0px'>" + parseJobDate(history.getFrom_date(), "yyyy") + " - " +  parseJobDate(history.getTo_date(), "yyyy") + "</td>" + 
    			        	"<td colspan='1'>" + history.getJob_title() + ", " + history.getCompany() + "</td></tr>");   
    			}
    			else
    			{
    				htmlCode.append("<tr style='color: #000000;'><td colspan = '1' style = 'padding-left:0px;padding-top:0px'>" + parseJobDate(history.getFrom_date(), "yyyy") + "</td>" + 
    			        	"<td colspan='1'>" + history.getJob_title() + ", " + history.getCompany() + "</td></tr>"); 
    			}
    						
    		}			
    	}
    			
		htmlCode.append("</table>");
		
		//Current position
    	htmlCode.append("<table width='100%'>" + 
							    			"<colgroup>" + 
											"<col width = '20%'><col width = '80%'>" +
										"</colgroup>" +
			    						"<tr><td colspan = '2'><u style='font-weight: bold;padding-bottom:10px;'>Present Academic Position</u></td></tr>");
    						for(EmploymentHistory_edit currentJobInfo: currentJobInfoList) {
		    					htmlCode.append("<tr class = 'chineseChar' style='color: #000000;><td colspan = '0'></td><td colspan = '1' style = 'padding-left:0px;padding-top:0px'>" 
		    							+ parseJobDate(currentJobInfo.getFrom_date(), "yyyy") + " - Present</td>" + 
		    							"<td colspan = '1'>"  +currentJobInfo.getJob_title() + ", " 
		    							+ currentJobInfo.getCompany() + "</td></tr>"); 
    						}
    	
    	htmlCode.append("</table>");
		
		//Research Interest
    	if (sInfo.getResearch_interest() != null) {
	    	htmlCode.append("<table width='100%'>" + 
								"<tr><td style='font-weight: bold; padding-bottom:10px;'><u>Research Interests</u></td></tr>" + 
								"<tr><td style='color: #000000;'>" + sInfo.getResearch_interest() + "\n</td></tr>" + 
					        "</table>");  
    	}
    	//Projects
    	if(selectedProject.size() > 0) {
    		sortSelectedProject();
	    	htmlCode.append("<table width='100%' cellspacing = '0' style = 'line-height:1.2; border-collapse: collapse;'>" + 
								"<colgroup>" + 
									"<col width = '45%'><col width = '15%'><col width = '20%'><col width = '15%'>" +
								"</colgroup>" +
	    						"<tr><td style='font-weight: bold;padding-bottom:10px;' colspan ='4'><u>Relevant Research Grant Records</u></td></tr>" + 
								"<tr style = 'color: #000000;'>" + 
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px' colspan ='1'>Project Title</td>" +
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px' colspan ='1'>Period</td>" +
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px' colspan ='1'>Funding Scheme</td>" +
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px' colspan ='1'>Funding Amount (HKD)</td>" +
	    						"</tr>");
	    	
	    	for(ProjectDetails_P project: selectedProject) {
	    		String title = "";
	    		title += (project.getProjectHeader_p().getTitle_1() == null)?"":project.getProjectHeader_p().getTitle_1();
	    		title += (project.getProjectHeader_p().getTitle_2() == null)?"":project.getProjectHeader_p().getTitle_2();
	    		title += (project.getProjectHeader_p().getTitle_3() == null)?"":project.getProjectHeader_p().getTitle_3();
	    		title += (project.getProjectHeader_p().getTitle_4() == null)?"":project.getProjectHeader_p().getTitle_4();
	    		title += (project.getInvestigator_type() == null)?"":(" (" + convertRole(project.getInvestigator_type()) + ")");
	    		
	    		htmlCode.append("<tr style = 'color: #000000'>" + 
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px'>" + title + "</td>" +
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px'>" + parseProjectDate(project, false) + "</td>" + 
	    							"<td style = 'border: 1px solid; border-collapse: collapse;padding:5px'>" + fundSourceMap.get(project.getProjectHeader_p().getSap_funding_source()) + "</td>");
	    		
	    		if(project.getProjectHeader_p().getSap_grant_amt() != null) {
	    			htmlCode.append("<td style = 'border: 1px solid; border-collapse: collapse;'>" + rephraseFundAmount(project.getProjectHeader_p().getSap_grant_amt()) + "</td>");
	    		}else {
	    			htmlCode.append("<td style = 'border: 1px solid; border-collapse: collapse;'>Not applicable</td>");
	    		}
	    		
	    		htmlCode.append("</tr>");
	    	}
	
		    htmlCode.append("</table>");
    	}
	    
	    //Publication 
    	if (selectedPublicationA.size() > 0 || selectedPublicationB.size() > 0) {
		    htmlCode.append("<table width='100%' cellspacing = '1'>" + 
							"<tr><td colspan = '2'><u style='font-weight: bold;padding-bottom:10px;'>Publication Records</u></td></tr></table>");

			    if (selectedPublicationA.size() > 0) {
				    htmlCode.append("<table width='100%' cellspacing = '1'>" +
				    					"<tr><td colspan = '2' style = 'font-weight:bold;padding-bottom:10px;'>Section A: Five most representative publications in the recent five years</td></tr>");
				    int i = 1;
				    for(OutputDetails_P output: selectedPublicationA) {
				    	htmlCode.append("<tr style='vertical-align:top'><td style = 'padding-left:0px;padding-top:0px' >"+i+".</td><td>" 
				    			+ genCitation(output) + "</td></tr>");
				    	i++;
				    }
				    htmlCode.append("</table>");
			    }
		    
		    
		    if (selectedPublicationB.size() > 0) {
			    htmlCode.append("<table width='100%' cellspacing = '1'>" + 
								"<tr style='vertical-align:top'><td colspan = '2' style = 'font-weight:bold;padding-bottom:10px;'>Section B: Five most representative publications beyond the recent five years</td></tr>");
			    int i = 1;
			    for(OutputDetails_P output: selectedPublicationB) {
			    	htmlCode.append("<tr><td style = 'padding-left:0px;padding-top:0px' >"+i+".</td><td>" + genCitation(output) + "</td></tr>");
			    	i++;
			    }
			    htmlCode.append("</table>");
		    }
    	}
    	
    	if (orderedAward != null)
    		selectedAward = getOrderedAward();
    	
	    //Prizes and awards
	    if(selectedAward.size() > 0) {

	    	sortSelectedAward();
	    	
		    htmlCode.append("<table width='100%'>" + 
								"<tr><td colspan = '3' style = 'font-weight:bold;padding-bottom:10px;'><u>Research-related Prizes and Awards</u></td></tr>"); 
		    
		    for(Award award: selectedAward) {
		    	htmlCode.append("<tr style = 'color: #000000;vertical-align:top; text-align:left;'>" + 
						"<td width = '10%'>" + award.getAwardYear() + "</td>" +
						"<td width = '90%'>" + award.getAwardName() + ", " + award.getOrgName() + "</td>" +
					"</tr>"); 
		    }
		    
		    htmlCode.append("</table>");
	    }
	    
	    //other
	    if(other.length() > 0) {
	    	String htmlOther = new String(other);
	    	htmlOther = htmlOther.replace("\n", "<br/>");
		    htmlCode.append("<table width='100%'>" + 
								"<tr><td><u  style = 'font-weight:bold;padding-bottom:10px;'>Others</u></td></tr>" + 
								"<tr style = 'color: #000000;'>" + 
									"<td width = '100%'>" + htmlOther + "</td>" +
								"</tr>" + 
							"</table>"); 
	    }
	    
       	Document htmlDocument = Jsoup.parse(htmlCode.toString());
    	htmlDocument.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
    	
    	File cvDocx = new File("cv.docx");

		WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage(
				org.docx4j.model.structure.PageSizePaper.valueOf("A4"), true);
        XHTMLImporterImpl XHTMLImporter = new XHTMLImporterImpl(wordMLPackage);

        //Set page margin
    	Body body = wordMLPackage.getMainDocumentPart().getJaxbElement().getBody();
    	PageDimensions page = new PageDimensions();
    	PgMar pageMargin = page.getPgMar();  
    	pageMargin.setTop(BigInteger.valueOf(1420));
    	pageMargin.setBottom(BigInteger.valueOf(1420));
    	pageMargin.setLeft(BigInteger.valueOf(1420));
    	pageMargin.setRight(BigInteger.valueOf(1420));

    	ObjectFactory objectFactory = new ObjectFactory();
    	SectPr sectPr = objectFactory.createSectPr();   
    	body.setSectPr(sectPr);                           
    	sectPr.setPgMar(pageMargin); 
    	
		wordMLPackage.getMainDocumentPart().getContent().addAll(XHTMLImporter.convert(htmlDocument.outerHtml().replace("nbsp", "#160"), null));
		wordMLPackage.save(cvDocx);
		FileInputStream is = new FileInputStream(cvDocx);
		Faces.sendFile(is, "cv.docx", false);
		is.close();
	}
	
	
	public String parseProjectDate(ProjectDetails_P project, boolean forSorting) {
		ProjectHeader_P headerP = project.getProjectHeader_p();
		SimpleDateFormat dateFormatter = null;
		String dateString = "";
		
		if(forSorting == true) {
			dateFormatter = new SimpleDateFormat("yyyy/MM/dd");
		}else if(headerP.getFrom_day() == null || headerP.getTo_day() == null) {
			dateFormatter = new SimpleDateFormat("MM/yyyy");
		}else {
				dateFormatter = new SimpleDateFormat("dd/MM/yyyy");	
		}
		
		Calendar fromDate = Calendar.getInstance();
		fromDate.set(Calendar.DATE, (headerP.getFrom_day() == null)?1:headerP.getFrom_day());
		fromDate.set(Calendar.MONTH, headerP.getFrom_month() - 1);
		fromDate.set(Calendar.YEAR, headerP.getFrom_year());
		
		Calendar toDate = Calendar.getInstance();
		toDate .set(Calendar.DATE, (headerP.getTo_day() == null)?1:headerP.getTo_day());
		toDate .set(Calendar.MONTH, headerP.getTo_month() - 1);
		toDate .set(Calendar.YEAR, headerP.getTo_year());
		
		//dateString = dateFormatter.format(fromDate.getTime());
		dateString = dateFormatter.format(fromDate.getTime()) + " - <br/>" + dateFormatter.format(toDate.getTime());
		
		return dateString;
	}
	
	public Date parseProjectDate_edit(ProjectDetails_P project, boolean period_from) throws ParseException {
		ProjectHeader_P headerP = project.getProjectHeader_p();
		Date real_date;
		DateFormat df = new SimpleDateFormat("d/M/yyyy"); 
		
		String string_date = "";
		
		if(period_from) {
			string_date = (headerP.getFrom_day().SIZE == 0 || headerP.getFrom_day() == null  )?"1/": (headerP.getFrom_day()+"/");
			string_date += headerP.getFrom_month() +"/" + headerP.getFrom_year();
		}
		else {
			string_date = (headerP.getTo_day().SIZE == 0 || headerP.getTo_day() == null  )?"1/": (headerP.getTo_day()+"/");
			string_date += headerP.getTo_month() +"/" + headerP.getTo_year();
		}
		
		real_date = df.parse(string_date);
		
		return real_date;
	}
	
	
	
	
	public String groupSelectedPublication(OutputDetails_P publication) {
		String section = "";
		
		int year = -1;
		if(publication.getOutputHeader_p().getTo_year() == null) {
			year = publication.getOutputHeader_p().getFrom_year();
		}else{
			year = publication.getOutputHeader_p().getTo_year();
		}
		
		Calendar calendar = Calendar.getInstance();
		int currentYear = calendar.get(Calendar.YEAR);
		if(currentYear - year < 5) {
			section = "A";
		}else{
			section = "B";
		}

		return section;
	}
	
	public String rephraseFundAmount(Double fundAmount) {
		int intFundAmount = fundAmount.intValue();
		String parsedFundAmount;
		if(intFundAmount == fundAmount) {
			parsedFundAmount = String.format("$%,d", intFundAmount);
		}else {
			parsedFundAmount = String.format("$%,.2f", fundAmount);
		}
		
		return parsedFundAmount;
	}
	
	public List<OutputDetails_P> sortPublicationList(List<OutputDetails_P> publicationList){
		Collections.sort(publicationList, new Comparator<OutputDetails_P>(){
			   public int compare(OutputDetails_P p1, OutputDetails_P p2){
			      int p1Year = (p1.getOutputHeader_p().getTo_year() == null)?p1.getOutputHeader_p().getFrom_year():p1.getOutputHeader_p().getTo_year();
			      int p2Year = (p2.getOutputHeader_p().getTo_year() == null)?p2.getOutputHeader_p().getFrom_year():p2.getOutputHeader_p().getTo_year();
			      
			      return p2Year - p1Year;
			   }
		});
		return publicationList;
	}
	
	/*public String userNameInCitation(String language) throws Exception {
		if(sIdentity == null) {
			sIdentity = getsIdentity();
		}
		
		String nameInCitation = "";
		if(language.equals("Eng")) {
			String[] otherName = sIdentity.getOthername().split(" ");
			
			nameInCitation += sIdentity.getSurname() + ",";
			for(int i = 0; i < otherName.length; i++) {
				if(i != otherName.length - 1) {
					nameInCitation += " ";
				}		
				nameInCitation += otherName[i].charAt(0) + ".";
			}
		}else if(language.equals("Chi")) {
			nameInCitation += sIdentity.getChinesename();
		}
		
		return nameInCitation;
	}*/
	
	/*public String parseCitation(OutputDetails_P output) throws Exception {
		String parsedCitation = "";
		if(output != null) {
			String citation = output.getOutputHeader_p().getApa_citation();
			List<String> splitCitation = splitCitation(citation);
			
			//Bold the name of the staff member concerned in the citation.
			String authorNamePart = splitCitation.get(0);
			String nameInCitation  = null;
			if(authorNamePart.matches("[\\u4E00-\\u9FA5]+")) {
				nameInCitation = userNameInCitation("Chi");
			}else {
				nameInCitation = userNameInCitation("Eng");
			}
			
			int staffNameStartIndex = authorNamePart.indexOf(nameInCitation);
			System.out.println(staffNameStartIndex == -1);
			if(staffNameStartIndex == -1) {//Staff Name not found.
				parsedCitation += authorNamePart;
			}else {
				if(staffNameStartIndex > 0) {
					parsedCitation += authorNamePart.substring(0, staffNameStartIndex);
				}
				
				parsedCitation += "<b>" +authorNamePart.substring(staffNameStartIndex, staffNameStartIndex + nameInCitation.length()) + "</b>";
				if(staffNameStartIndex + nameInCitation.length() <= authorNamePart.length()){
					parsedCitation += authorNamePart.substring(staffNameStartIndex + nameInCitation.length(), authorNamePart.length());	
				}
			}
			
			//Italicize the journal title for journal publications
			String otherPart = splitCitation.get(1);
			if(journalTypeList == null) {
				journalTypeList = getJournalTypeList();
			}
			if(journalTypeList.contains(output.getOutputHeader_p().getSap_output_type())){
				String journalTitle = output.getOutputHeader_p().getTitle_jour_book();
				int titleStartIndex = otherPart.indexOf(journalTitle);
				parsedCitation += otherPart.substring(0, titleStartIndex);
				parsedCitation += "<i>" + otherPart.substring(titleStartIndex, titleStartIndex + journalTitle.length()) + "</i>";
				parsedCitation += otherPart.substring(titleStartIndex + journalTitle.length(), otherPart.length());
			}else {
				parsedCitation += otherPart;
			}
		}
		
		return parsedCitation;
	}*/
	
	/*public List<String> splitCitation(String citation) throws Exception{
		List<String> splitCitation = new ArrayList<>();
		if(citation != null) {
			String leftBracket = getUTF8String("（");
			for(int i = 0; i < citation.length(); i++) {
				if(citation.charAt(i) == '(' || citation.substring(i, i + 1).equals(leftBracket)) {
					String authorNamePart = citation.substring(0, i);
					String otherPart = citation.substring(i, citation.length() - 1);
					splitCitation.add(authorNamePart);
					splitCitation.add(otherPart);
					break;
				}
			}
		}
		
		return splitCitation;
	}*/
	
	public String getUTF8String(String string) throws Exception {
		String unicode = string;
		byte[] bytes = unicode.getBytes();
		String utf8 = new String(bytes, "UTF-8");
		
		return utf8;
	}
		
	public void saveCurrentOption() throws IOException{

		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
			if(savedOption == null) {
				savedOption = new StaffCv();
			}
			
			savedOption.setPid(Integer.valueOf(paramPid));
			if(selectedProject != null) {
				if (selectedProject.size() > 0) {
					String projectArray ="";
					for(int i = 0; i < selectedProject.size(); i++) {
						projectArray += Integer.toString(selectedProject.get(i).getProjectHeader_p().getPk().getProject_no());
						if(i != selectedProject.size() - 1) {
							projectArray += "/";
						}
					}
				
				DataTable selectedProjectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedProjTable"); 
				DataTable projectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:projectTable"); 

				if(selectedProjectTable.getActiveSortMeta().values().size() > 0) {
					for(SortMeta sortMeta: selectedProjectTable.getActiveSortMeta().values()) {
						String sortField = sortMeta.getField();
						String sortOrder = sortMeta.getOrder().toString();
						projectArray += "/" + sortField + "/" + sortOrder;
	 				}
				}else if(projectTable.getActiveSortMeta().values().size() > 0) {
					for(SortMeta sortMeta: projectTable.getActiveSortMeta().values()) {
					String sortField = sortMeta.getField();
					String sortOrder = sortMeta.getOrder().toString();
					projectArray += "/" + sortField + "/" + sortOrder;
					}
				}
				
				savedOption.setProject(projectArray);
				}
			}
			
			if(selectedPublicationA.size() != 0) {
				String publicationAArray = "";
				for(int i = 0; i < selectedPublicationA.size(); i++) {
					publicationAArray += selectedPublicationA.get(i).getOutputHeader_p().getPk().getOutput_no();
					if(i != selectedPublicationA.size() - 1) {
						publicationAArray += "/";
					}
				}
				
				if ( ! sectionAB ) {
					DataTable selectedPublicationATable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedPublicationATable");
					if(selectedPublicationATable.getActiveSortMeta().values().size() > 0) {
						for(SortMeta sortMeta: selectedPublicationATable.getActiveSortMeta().values()) {
							String sortField = sortMeta.getField();
							String sortOrder = sortMeta.getOrder().toString();
							publicationAArray += "/" + sortField + "/" + sortOrder;
		 				}
					}
				}

				savedOption.setOutputA(publicationAArray);
			} 
			else {
				//reset the OutputA array
				savedOption.setOutputA("");
			}
			
			
			
			if(selectedPublicationB.size() != 0) {
				String publicationBArray = "";
				for(int i = 0; i < selectedPublicationB.size(); i++) {
					publicationBArray += selectedPublicationB.get(i).getOutputHeader_p().getPk().getOutput_no();
					if(i != selectedPublicationB.size() - 1) {
						publicationBArray += "/";
					}
				}
				if ( ! sectionAB ) {
					DataTable selectedPublicationBTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedPublicationBTable");
					if(selectedPublicationBTable.getActiveSortMeta().values().size() > 0) {
						for(SortMeta sortMeta: selectedPublicationBTable.getActiveSortMeta().values()) {
							String sortField = sortMeta.getField();
							String sortOrder = sortMeta.getOrder().toString();
							publicationBArray += "/" + sortField + "/" + sortOrder;
		 				}
					}
				}
				
				savedOption.setOutputB(publicationBArray);
			}
			else 
				savedOption.setOutputB("");
			
			if (orderedAward != null) {
				selectedAward = orderedAward;
			}
			
			if(selectedAward != null) {
				String awardArray = "";
				if (orderedAward != null) {
					selectedAward = orderedAward;
				}

				for(int i = 0; i < selectedAward.size(); i++) {
					
					awardArray += selectedAward.get(i).getRiNo();
					if(i != selectedAward.size() - 1) {
						awardArray += "/";
					}
				}

				if (! awardReorder ) {
					DataTable selectedAwardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedAwardTable"); 
					DataTable awardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:awardTable"); 
					if(selectedAwardTable.getActiveSortMeta().values().size() > 0) {
						for(SortMeta sortMeta: selectedAwardTable.getActiveSortMeta().values()) {
							String sortField = sortMeta.getField();
							String sortOrder = sortMeta.getOrder().toString();
							awardArray += "/" + sortField + "/" + sortOrder;
		 				}
					}
					
					/*else if(awardTable.getActiveSortMeta().values().size() > 0) {
						for(SortMeta sortMeta: awardTable.getActiveSortMeta().values()) {
							String sortField = sortMeta.getField();
							String sortOrder = sortMeta.getOrder().toString();
							awardArray += "/" + sortField + "/" + sortOrder;
						}
					}*/
				}

				savedOption.setAward(awardArray);
			}
			
	    	savedOption.setOther(other);
	    	savedOption.setUserstamp(getLoginUserId());
	    	cdao.updateStaffCv(savedOption);
	    	// Success message
			String message = "msg.success.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "The CV");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
		}
		catch (Exception e)
		{
			// Failed message
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "The CV");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		fCtx.getExternalContext().redirect("createMyCV.xhtml");
	}

	public void deleteSavedOption() throws IOException {
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
			cdao.deleteStaffCv(savedOption);
			savedOption = new StaffCv();
			selectedProject = new ArrayList<>();
			selectedPublication = new ArrayList<>();
			selectedPublicationA = new ArrayList<>();
			selectedPublicationB = new ArrayList<>();
			selectedAward = new ArrayList<>();
			other = "";
			// Success message
			String message = "msg.success.delete.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "The CV");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot delete the saved CV, pid: " + paramPid, e);
		}
		fCtx.getExternalContext().redirect("createMyCV.xhtml");
		//printMessage(FacesMessage.SEVERITY_INFO, "Deleted saved selections.", "");
	}
	
	
    public void onRowReorder(String dataFrom) {

    	DataTable selectedPublicationATable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(dataFrom);
    	selectedPublicationATable.reset();
    	
    	if ("dataForm:selectedAwardTable".equals(dataFrom)) {
    		awardReorder = true;
    	}
    	else {
    		sectionAB = true;
    	}
    	orderedAward = selectedAward;
    	//printMessage(FacesMessage.SEVERITY_INFO, "Row Moved From: " + event.getFromIndex() + " To: " + event.getToIndex(), "");
    }

	
	public void sortSelectedProject() {
		DataTable selectedProjectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedProjTable"); 
		DataTable projectTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:projectTable"); 
		for(SortMeta sortMeta: selectedProjectTable.getActiveSortMeta().values()) {
			String sortField = sortMeta.getField();
			String sortOrder = sortMeta.getOrder().toString();
			
			if(sortField.equals("projectHeader_p.title_1") && sortOrder.equals("ASCENDING")) {	//Sort by Project Title
				selectedProject = sortProjectByTitleAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.title_1") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByTitleDesc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.from_year") && sortOrder.equals("ASCENDING")) {  //Sort by Period
				selectedProject = sortProjectByPeriodAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.from_year") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByPeriodDesc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_funding_source") && sortOrder.equals("ASCENDING")) { //Sort by funding source
				selectedProject = sortProjectByFundingSourceAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_funding_source") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByFundingSourceDesc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_grant_amt") && sortOrder.equals("ASCENDING")) { //Sort by funding amount
				selectedProject = sortProjectByFundingAmountAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_grant_amt") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByFundingAmountDesc(selectedProject);
				return;
			}
		}
		
		for(SortMeta sortMeta: projectTable.getActiveSortMeta().values()) {
			String sortField = sortMeta.getField();
			String sortOrder = sortMeta.getOrder().toString();
			
			if(sortField.equals("projectHeader_p.title_1") && sortOrder.equals("ASCENDING")) {	//Sort by Project Title
				selectedProject = sortProjectByTitleAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.title_1") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByTitleDesc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.from_year") && sortOrder.equals("ASCENDING")) {  //Sort by Period
				selectedProject = sortProjectByPeriodAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.from_year") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByPeriodDesc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_funding_source") && sortOrder.equals("ASCENDING")) { //Sort by funding source
				selectedProject = sortProjectByFundingSourceAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_funding_source") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByFundingSourceDesc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_grant_amt") && sortOrder.equals("ASCENDING")) { //Sort by funding amount
				selectedProject = sortProjectByFundingAmountAsc(selectedProject);
				return;
			}else if(sortField.equals("projectHeader_p.sap_grant_amt") && sortOrder.equals("DESCENDING")) {
				selectedProject = sortProjectByFundingAmountDesc(selectedProject);
				return;
			}
		}
	}
	
	public List<ProjectDetails_P> sortProjectByTitleAsc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   String p1Title = "";
				   String p2Title = "";
				   p1Title += (p1.getProjectHeader_p().getTitle_1() == null)?"":p1.getProjectHeader_p().getTitle_1();
				   p1Title += (p1.getProjectHeader_p().getTitle_2() == null)?"":p1.getProjectHeader_p().getTitle_2();
				   p1Title += (p1.getProjectHeader_p().getTitle_3() == null)?"":p1.getProjectHeader_p().getTitle_3();
				   p1Title += (p1.getProjectHeader_p().getTitle_4() == null)?"":p1.getProjectHeader_p().getTitle_4();
				   p1Title += (p1.getInvestigator_type() == null)?"":(" (" + convertRole(p1.getInvestigator_type()) + ")"); 
				   
				   p2Title += (p2.getProjectHeader_p().getTitle_1() == null)?"":p2.getProjectHeader_p().getTitle_1();
				   p2Title += (p2.getProjectHeader_p().getTitle_2() == null)?"":p2.getProjectHeader_p().getTitle_2();
				   p2Title += (p2.getProjectHeader_p().getTitle_3() == null)?"":p2.getProjectHeader_p().getTitle_3();
				   p2Title += (p2.getProjectHeader_p().getTitle_4() == null)?"":p2.getProjectHeader_p().getTitle_4();
				   p2Title += (p2.getInvestigator_type() == null)?"":(" (" + convertRole(p2.getInvestigator_type()) + ")"); 
			      
			      return p1Title.compareTo(p2Title);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByTitleDesc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   String p1Title = "";
				   String p2Title = "";
				   p1Title += (p1.getProjectHeader_p().getTitle_1() == null)?"":p1.getProjectHeader_p().getTitle_1();
				   p1Title += (p1.getProjectHeader_p().getTitle_2() == null)?"":p1.getProjectHeader_p().getTitle_2();
				   p1Title += (p1.getProjectHeader_p().getTitle_3() == null)?"":p1.getProjectHeader_p().getTitle_3();
				   p1Title += (p1.getProjectHeader_p().getTitle_4() == null)?"":p1.getProjectHeader_p().getTitle_4();
				   p1Title += (p1.getInvestigator_type() == null)?"":(" (" + convertRole(p1.getInvestigator_type()) + ")"); 
				   
				   p2Title += (p2.getProjectHeader_p().getTitle_1() == null)?"":p2.getProjectHeader_p().getTitle_1();
				   p2Title += (p2.getProjectHeader_p().getTitle_2() == null)?"":p2.getProjectHeader_p().getTitle_2();
				   p2Title += (p2.getProjectHeader_p().getTitle_3() == null)?"":p2.getProjectHeader_p().getTitle_3();
				   p2Title += (p2.getProjectHeader_p().getTitle_4() == null)?"":p2.getProjectHeader_p().getTitle_4();
				   p2Title += (p2.getInvestigator_type() == null)?"":(" (" + convertRole(p2.getInvestigator_type()) + ")"); 
			      
			      return p2Title.compareTo(p1Title);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByPeriodAsc(List<ProjectDetails_P> projectList){

		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   String p1Period = parseProjectDate(p1, true);
				   String p2Period = parseProjectDate(p2, true);
			      return p1Period.compareTo(p2Period);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByPeriodDesc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   String p1Period = parseProjectDate(p1, true);
				   String p2Period = parseProjectDate(p2, true);
			      
			      return p2Period.compareTo(p1Period);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByFundingSourceAsc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   String p1FundingSource = p1.getProjectHeader_p().getSap_funding_source();
				   String p2FundingSource = p2.getProjectHeader_p().getSap_funding_source();
			      
			      return p1FundingSource.compareTo(p2FundingSource);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByFundingSourceDesc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   String p1FundingSource = p1.getProjectHeader_p().getSap_funding_source();
				   String p2FundingSource = p2.getProjectHeader_p().getSap_funding_source();

			      
			      return p2FundingSource.compareTo(p1FundingSource);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByFundingAmountAsc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   Double p1FundingAmount = p1.getProjectHeader_p().getSap_grant_amt();
				   Double p2FundingAmount = p2.getProjectHeader_p().getSap_grant_amt();
			      
			       return Double.compare(p1FundingAmount, p2FundingAmount);
			   }
		});
		return projectList;
	}
	
	public List<ProjectDetails_P> sortProjectByFundingAmountDesc(List<ProjectDetails_P> projectList){
		Collections.sort(projectList, new Comparator<ProjectDetails_P>(){
			   public int compare(ProjectDetails_P p1, ProjectDetails_P p2){
				   Double p1FundingAmount = p1.getProjectHeader_p().getSap_grant_amt();
				   Double p2FundingAmount = p2.getProjectHeader_p().getSap_grant_amt();
			      
			       return Double.compare(p2FundingAmount, p1FundingAmount);
			   }
		});
		return projectList;
	}
	
	public void sortSelectedAward() {
		DataTable selectedAwardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:selectedAwardTable"); 
		DataTable awardTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("dataForm:awardTable"); 
		for(SortMeta sortMeta: selectedAwardTable.getActiveSortMeta().values()) {
			String sortField = sortMeta.getField();
			String sortOrder = sortMeta.getOrder().toString();
			
			if(sortField.equals("awardName") && sortOrder.equals("ASCENDING")) {	//Sort by Award Name
				selectedAward = sortAwardByAwardNameAsc(selectedAward);
				return;
			}else if(sortField.equals("awardName") && sortOrder.equals("DESCENDING")) {
				selectedAward = sortAwardByAwardNameDesc(selectedAward);
				return;
			}else if(sortField.equals("orgName") && sortOrder.equals("ASCENDING")) {
				selectedAward = sortAwardByOrgNameAsc(selectedAward);
				return;
			}else if(sortField.equals("orgName") && sortOrder.equals("DESCENDING")) {
				selectedAward = sortAwardByOrgNameDesc(selectedAward);
				return;
			}else if(sortField.equals("awardYear") && sortOrder.equals("ASCENDING")) {
				selectedAward = sortAwardByAwardYearAsc(selectedAward);
				return;
			}else if(sortField.equals("awardYear") && sortOrder.equals("DESCENDING")) {
				selectedAward = sortAwardByAwardYearDesc(selectedAward);
				return;
			}
		}
		
		for(SortMeta sortMeta: awardTable.getActiveSortMeta().values()) {
			String sortField = sortMeta.getField();
			String sortOrder = sortMeta.getOrder().toString();
			
			if(sortField.equals("awardName") && sortOrder.equals("ASCENDING")) {	//Sort by Award Name
				selectedAward = sortAwardByAwardNameAsc(selectedAward);
				return;
			}else if(sortField.equals("awardName") && sortOrder.equals("DESCENDING")) {
				selectedAward = sortAwardByAwardNameDesc(selectedAward);
				return;
			}else if(sortField.equals("orgName") && sortOrder.equals("ASCENDING")) {
				selectedAward = sortAwardByOrgNameAsc(selectedAward);
				return;
			}else if(sortField.equals("orgName") && sortOrder.equals("DESCENDING")) {
				selectedAward = sortAwardByOrgNameDesc(selectedAward);
				return;
			}else if(sortField.equals("awardYear") && sortOrder.equals("ASCENDING")) {
				selectedAward = sortAwardByAwardYearAsc(selectedAward);
				return;
			}else if(sortField.equals("awardYear") && sortOrder.equals("DESCENDING")) {
				selectedAward = sortAwardByAwardYearDesc(selectedAward);
				return;
			}
		}
	}
	
	public List<Award> sortAwardByAwardNameAsc(List<Award> awardList){
		Collections.sort(awardList, new Comparator<Award>(){
			   public int compare(Award a1, Award a2){
				   String a1AwardName = a1.getAwardName();
				   String a2AwardName = a2.getAwardName();
			      
			       return a1AwardName.compareTo(a2AwardName);
			   }
		});
		return awardList;
	}
	
	public List<Award> sortAwardByAwardNameDesc(List<Award> awardList){
		Collections.sort(awardList, new Comparator<Award>(){
			   public int compare(Award a1, Award a2){
				   String a1AwardName = a1.getAwardName();
				   String a2AwardName = a2.getAwardName();
			      
			       return a2AwardName.compareTo(a1AwardName);
			   }
		});
		return awardList;
	}
	
	public List<Award> sortAwardByOrgNameAsc(List<Award> awardList){
		Collections.sort(awardList, new Comparator<Award>(){
			   public int compare(Award a1, Award a2){
				   String a1OrgName = a1.getAwardName();
				   String a2OrgName = a2.getAwardName();
			      
			       return a1OrgName.compareTo(a2OrgName);
			   }
		});
		return awardList;
	}
	
	public List<Award> sortAwardByOrgNameDesc(List<Award> awardList){
		Collections.sort(awardList, new Comparator<Award>(){
			   public int compare(Award a1, Award a2){
				   String a1OrgName = a1.getAwardName();
				   String a2OrgName = a2.getAwardName();
			      
			       return a2OrgName.compareTo(a1OrgName);
			   }
		});
		return awardList;
	}
	
	public List<Award> sortAwardByAwardYearAsc(List<Award> awardList){
		Collections.sort(awardList, new Comparator<Award>(){
			   public int compare(Award a1, Award a2){
				   String a1AwardYear = a1.getAwardYear();
				   String a2AwardYear = a2.getAwardYear();
			      
			       return a1AwardYear.compareTo(a2AwardYear);
			   }
		});
		return awardList;
	}
	
	public List<Award> sortAwardByAwardYearDesc(List<Award> awardList){
		Collections.sort(awardList, new Comparator<Award>(){
			   public int compare(Award a1, Award a2){
				   String a1AwardYear = a1.getAwardYear();
				   String a2AwardYear = a2.getAwardYear();
			      
			       return a2AwardYear.compareTo(a1AwardYear);
			   }
		});
		return awardList;
	}
	
	public String convertRole(String value) {
		String result = "";
		if (value != null) {
			switch(value){
			    case "PRINCIPAL COORDINATOR" :
			    	result = "PC";
			       break; 
			    case "PRINCIPAL INVESTIGATOR" :
			    	result = "PI";
			       break; 
			    case "CO-PRINCIPAL INVESTIGATOR" :
			    	result = "Co-PI";
			       break; 
			    case "CO-INVESTIGATOR" :
			    	result = "Co-I";
			       break; 
			    case "TEAM MEMBER" :
			    	result = "TM";
			       break; 
			    default : 
			    	result = "";
			}
		}
		return result;
	}
	
	public String genCitation(OutputDetails_P output) 
	{
		String result = "";
		if (!GenericValidator.isBlankOrNull(output.getOutputHeader_p().getSap_output_type())){
			if (!"0".equals(output.getOutputHeader_p().getSap_output_type())) {
				if ("APA".equals(getCitation())) {
					result = getAPA(output.getOutputHeader_p());
				}
				if ("MLA".equals(getCitation())) {
					result = getMLA(output.getOutputHeader_p());
				}
				if ("Chicago".equals(getCitation())) {
					result = getChicago(output.getOutputHeader_p());
				}
				if (staffDetail != null && output.getOutputHeader_p().getPk().getOutput_no() != null) {
					OutputAddl_P addl = pDao.getOutputAddl_P(output.getOutputHeader_p().getPk().getOutput_no(), "P", staffDetail.getStaff_number());
					if (addl != null) {
						if(addl.getCustom_citation() != null) {
							result = addl.getCustom_citation();
							if (!Strings.isNullOrEmpty(result)) {
								result = result.replaceAll("</p><p>","</br>");
								result = result.replaceAll("<p>","");
								result = result.replaceAll("</p>","");
							}
						}
					}
				}

				/*output.getOutputHeader_p().setApa_html(result);
				if (!Strings.isNullOrEmpty(result)) {
					result = result.replaceAll("\\<[^>]*>","");
					result = StringEscapeUtils.unescapeHtml4(result);
				}
				output.getOutputHeader_p().setApa_citation(result);		*/	
			}
		}
		result = escapeInvalidXmlCharacters(result);
		return result;
	}
	
	public String removeHtml(String value) {
		String result = "";
		if (!Strings.isNullOrEmpty(value)) {
			result = value.replaceAll("\\<[^>]*>","");
			result = StringEscapeUtils.unescapeHtml4(result);
		}
		return result;
	}
	
	public String removeCitationHtml(OutputDetails_P output) {
		String value = genCitation(output);
		String result = "";
		if (!Strings.isNullOrEmpty(value)) {
			result = value.replaceAll("\\<[^>]*>","");
			result = StringEscapeUtils.unescapeHtml4(result);
		}
		return result;
	}
	
	public String getCitation()
	{
		if (citation == null) {			
			citation = "APA";
			if (!Strings.isNullOrEmpty(getParamPid())) {
				int pid = Integer.valueOf(paramPid);
				CvDAO cvDao = CvDAO.getInstance();
				StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_CITATION");
				if (obj != null) {
					citation= obj.getDisplayType();
				}
			}
		}
		return citation;
	}
	
	
}