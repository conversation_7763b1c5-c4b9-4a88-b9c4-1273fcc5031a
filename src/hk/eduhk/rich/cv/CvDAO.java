package hk.eduhk.rich.cv;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.award.Award;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.util.PersistenceManager;

public class CvDAO extends BaseDAO
{

	private static CvDAO instance = null;


	public static synchronized CvDAO getInstance()
	{
		if (instance == null) instance = new CvDAO();
		return instance;
	}
	
	
	public static CvDAO getCacheInstance()
	{
		return CvDAO.getInstance();
	}
	
	public String getStaffNo(int pid)
	{
		List<StaffIdentity> objList = null;
		EntityManager em = null;
		String staffNo = null;
		try
		{
			em = getEntityManager();
			String query = "SELECT obj FROM StaffIdentity obj WHERE obj.pid = :pid ORDER BY obj.staff_number DESC ";
			TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
			q.setParameter("pid", pid);
			q.setMaxResults(1);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		if (objList != null && !objList.isEmpty()) {
			staffNo = objList.get(0).getStaff_number();
		}
		return staffNo;
	}		
	
	public StaffIdentity getStaffIdentity(String staffNo)
	{
		StaffIdentity obj = null;
		
		if (!GenericValidator.isBlankOrNull(staffNo))
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(StaffIdentity.class, staffNo);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public StaffInfo getStaffInfo(int pid)
	{
		StaffInfo obj = null;
		
		if (pid > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(StaffInfo.class, pid);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public List<InternetUserInfo> getInternetUserInfo(int pid)
	{
		List<InternetUserInfo> objList = null;
		
		if (pid > 0)
		{
			EntityManager em = null;
			String query = "SELECT obj FROM InternetUserInfo obj WHERE obj.pid = :pid ORDER BY obj.displayorder, obj.eorecord";
			try
			{
				em = getEntityManager();
				TypedQuery<InternetUserInfo> q = em.createQuery(query, InternetUserInfo.class);
				q.setParameter("pid", pid);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return objList;
	}
	
	public List<InternetUserInfo> getInternetUserInfoByStaffNo(String staffNo)
	{
		List<InternetUserInfo> objList = null;
		
		if (StringUtils.isNotBlank(staffNo))
		{
			EntityManager em = null;
			String query = "SELECT obj FROM InternetUserInfo obj WHERE obj.staff_number = :staffNo ORDER BY obj.displayorder, obj.eorecord";
			try
			{
				em = getEntityManager();
				TypedQuery<InternetUserInfo> q = em.createQuery(query, InternetUserInfo.class);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return objList;
	}
	
	public StaffProfileDisplay getStaffProfileDisplay(int pid, String itemCode)
	{
		List<StaffProfileDisplay> objList = null;
		EntityManager em = null;
		try
		{
			em = getEntityManager();
			String query = "SELECT obj FROM StaffProfileDisplay obj WHERE obj.pk.pid = :pid AND obj.pk.itemCode = :itemCode ORDER BY obj.displayOrder, obj.pk.itemCode ";
			TypedQuery<StaffProfileDisplay> q = em.createQuery(query, StaffProfileDisplay.class);
			q.setParameter("pid", pid);
			q.setParameter("itemCode", itemCode);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public List<StaffProfileDisplay> getStaffProfileDisplayList(int pid, String type)
	{
		List<StaffProfileDisplay> objList = null;
		EntityManager em = null;
		try
		{
			em = getEntityManager();
			String query = "SELECT obj FROM StaffProfileDisplay obj WHERE obj.pk.pid = :pid AND obj.itemType = :type ORDER BY obj.displayOrder, obj.pk.itemCode ";
			TypedQuery<StaffProfileDisplay> q = em.createQuery(query, StaffProfileDisplay.class);
			q.setParameter("pid", pid);
			q.setParameter("type", type);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public StaffProfileDisplay updateStaffProfileDisplay(StaffProfileDisplay obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public StaffCv getStaffCv(int pid)
	{
		StaffCv obj = null;
		
		if (pid > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(StaffCv.class, pid);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public StaffCv updateStaffCv(StaffCv obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public List<OutputDetails_P> getOutputDetails_PByIds(List<Integer> idList){
		
		List<OutputDetails_P> objList = null;
		EntityManager em = null;	
		String where = "";
		String orderby = "";
		try
		{
			em = getEntityManager();	
			if (!idList.isEmpty()) {
				for (int i = 0; i < idList.size(); i++) {
					if (i == 0) {
						where += " AND ( ";
						orderby +=" ORDER BY CASE ";
					}
					where += " obj.pk.output_no IN :riNos"+i;
					orderby += " WHEN obj.pk.output_no = :riNos"+i + " THEN "+ i;
					if (i == idList.size() - 1) {
						where += " ) ";
						orderby += " ELSE "+i + " END ";
					}else {
						where += " OR ";
					}
				}

				String query = "SELECT obj FROM OutputDetails_P obj WHERE obj.pk.line_no = :line_no " + where + "and obj.outputHeader_p.pk.data_level = :data_level " +
								orderby;
						//Remove Order		" ORDER BY obj.outputHeader_p.from_year DESC, obj.outputHeader_p.from_month DESC, obj.outputHeader_p.name_other_pos, obj.outputHeader_p.title_jour_book";			
				TypedQuery<OutputDetails_P> q = em.createQuery(query, OutputDetails_P.class);

				for (int i = 0; i < idList.size(); i++) {
					q.setParameter("riNos"+i, idList.get(i));
				}

				q.setParameter("line_no", 1);
				q.setParameter("data_level", "P");

				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<ProjectDetails_P> getProjectDetails_PByIdList(List<Integer> idList, String data_level, String staffId){
		List<ProjectDetails_P> objList = null;
		EntityManager em = null;	
		String where = "";
		try
		{
			em = getEntityManager();		
			for (int i = 0; i < idList.size(); i++) {
				if (i == 0) {
					where += " AND ( ";
				}
				where += " obj.pk.project_no = :project_no"+i;
				if (i == idList.size() - 1) {
					where += " ) ";
				}else {
					where += " OR ";
				}
			}
			String query = "SELECT obj FROM ProjectDetails_P obj WHERE obj.pk.data_level = :data_level AND obj.investigator_staff_no = :investigator_staff_no" + where + "ORDER BY obj.pk.line_no";			
			TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
			for (int i = 0; i < idList.size(); i++) {
				q.setParameter("project_no"+i, idList.get(i));
			}

			q.setParameter("data_level", data_level);
			q.setParameter("investigator_staff_no", staffId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	@SuppressWarnings("static-access")
	public List<Award> getAwardListByIdList(List<Integer> idList) throws SQLException{
		List<Award> voList = new ArrayList<Award>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		String where = "";
		String orderby = "";
    	
		try
		{
			conn = pm.getConnection();
			for (int i = 0; i < idList.size(); i++) {
				if (i == 0) {
					where += " AND ( ";
					orderby +=" ORDER BY CASE ";
				}
				where 	+= " H.AWARD_NO = '" + idList.get(i) + "' ";
				orderby += " WHEN H.AWARD_NO = "+idList.get(i) + " THEN "+ i;
				if (i == idList.size() - 1) {
					orderby += " ELSE "+ (i+1) + " END ";
					where += " ) ";
				}else {
					where += " OR ";
				}
			}
			String query = "SELECT RECIPIENT_STAFF_NO, AWARD_NAME, ORG_NAME, AWARD_DAY, AWARD_MONTH, AWARD_YEAR, SHORT_DESC, FULL_DESC, H.AWARD_NO FROM RICH.RH_P_AWARD_HDR H LEFT JOIN RICH.RH_P_AWARD_DTL D ON H.AWARD_NO = D.AWARD_NO AND H.DATA_LEVEL = D.DATA_LEVEL "
					+ " LEFT JOIN RICH.RH_Q_AWARD_DTL QD ON H.AWARD_NO = QD.AWARD_NO AND QD.STAFF_NO = D.RECIPIENT_STAFF_NO "	
					+  "WHERE H.DATA_LEVEL = 'P' AND QD.DISPLAY_IND = 'Y'  AND D.LINE_NO = 1 AND QD.CONSENT_IND = 'Y' " + where;
			
			query 	+= orderby;
			//query += " ORDER BY H.AWARD_YEAR DESC, H.AWARD_MONTH DESC, H.AWARD_DAY DESC";
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			while (rs.next())
			{
				Award vObj = new Award();
				vObj.setStaffNumber(rs.getString("RECIPIENT_STAFF_NO"));
				vObj.setAwardName(rs.getString("AWARD_NAME"));
				vObj.setOrgName(rs.getString("ORG_NAME"));
				vObj.setAwardDay(rs.getString("AWARD_DAY"));
				vObj.setAwardMonth(rs.getString("AWARD_MONTH"));
				vObj.setAwardYear(rs.getString("AWARD_YEAR"));
				vObj.setShortDesc(rs.getString("SHORT_DESC"));
				vObj.setFullDesc(rs.getString("FULL_DESC"));
				vObj.setRiNo(rs.getInt("AWARD_NO"));
			
				voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return voList;
	}
	
	public void deleteStaffCv(StaffCv obj)
	{
		if (obj != null) {
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				obj = em.find(StaffCv.class, obj.getPid());
				em.remove(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}	
	
	public void testing(File obj) throws Exception, IOException {

	}
}
