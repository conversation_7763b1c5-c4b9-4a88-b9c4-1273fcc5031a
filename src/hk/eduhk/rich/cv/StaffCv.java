package hk.eduhk.rich.cv;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_U_STAFF_CV")
public class StaffCv extends UserPersistenceObject{
	
	@Id
	private int pid;
	
	@Column(name = "OUTPUT_A")
	private String outputA;
	
	@Column(name = "OUTPUT_B")
	private String outputB;
	
	@Column(name = "PROJECT")
	private String project;
	
	@Column(name = "AWARD")
	private String award;
	
	@Column(name = "OTHER")
	private String other;

	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}

	
	public String getOutputA()
	{
		return outputA;
	}

	
	public void setOutputA(String outputA)
	{
		this.outputA = outputA;
	}

	
	public String getOutputB()
	{
		return outputB;
	}

	
	public void setOutputB(String outputB)
	{
		this.outputB = outputB;
	}

	
	public String getProject()
	{
		return project;
	}

	
	public void setProject(String project)
	{
		this.project = project;
	}

	
	public String getAward()
	{
		return award;
	}

	
	public void setAward(String award)
	{
		this.award = award;
	}

	
	public String getOther()
	{
		return other;
	}

	
	public void setOther(String other)
	{
		this.other = other;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((award == null) ? 0 : award.hashCode());
		result = prime * result + ((other == null) ? 0 : other.hashCode());
		result = prime * result + ((outputA == null) ? 0 : outputA.hashCode());
		result = prime * result + ((outputB == null) ? 0 : outputB.hashCode());
		result = prime * result + pid;
		result = prime * result + ((project == null) ? 0 : project.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffCv other = (StaffCv) obj;
		if (award == null)
		{
			if (other.award != null)
				return false;
		}
		else if (!award.equals(other.award))
			return false;
		if (this.other == null)
		{
			if (other.other != null)
				return false;
		}
		else if (!this.other.equals(other.other))
			return false;
		if (outputA == null)
		{
			if (other.outputA != null)
				return false;
		}
		else if (!outputA.equals(other.outputA))
			return false;
		if (outputB == null)
		{
			if (other.outputB != null)
				return false;
		}
		else if (!outputB.equals(other.outputB))
			return false;
		if (pid != other.pid)
			return false;
		if (project == null)
		{
			if (other.project != null)
				return false;
		}
		else if (!project.equals(other.project))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "StaffCv [pid=" + pid + ", outputA=" + outputA + ", outputB=" + outputB + ", project=" + project
				+ ", award=" + award + ", other=" + other + "]";
	}
	

}
