package hk.eduhk.rich.export;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import hk.eduhk.rich.entity.award.GenAwardXmlView;
import hk.eduhk.rich.entity.form.GenKtActXmlView;

@WebServlet(name = "service", urlPatterns = { "/service" })
public class ExportPureKtAct extends HttpServlet {
 

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static final Logger logger = Logger.getLogger(ExportPureKtAct.class.getName());
	

	public ExportPureKtAct() {
		super();
	}
	
	protected void doGet(HttpServletRequest request,
            HttpServletResponse response) throws ServletException, IOException {

		response.setContentType("text/xml");
		response.setCharacterEncoding("UTF-8");
		 
  		PrintWriter writer = response.getWriter();
   		GenKtActXmlView genKtActXmlView = new GenKtActXmlView();
   		writer.println(genKtActXmlView.createKtActXmlFile());
   		writer.close();	            
    }
 
	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		doGet(request, response);
	}
	

}