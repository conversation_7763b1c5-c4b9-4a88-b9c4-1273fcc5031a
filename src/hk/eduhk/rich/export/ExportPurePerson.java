package hk.eduhk.rich.export;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import hk.eduhk.rich.entity.staff.GenPersonXmlView;

@WebServlet(name = "service", urlPatterns = { "/service" })
public class ExportPurePerson extends HttpServlet {
 

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static final Logger logger = Logger.getLogger(ExportPurePerson.class.getName());
	

	public ExportPurePerson() {
		super();
	}
	
	protected void doGet(HttpServletRequest request,
            HttpServletResponse response) throws ServletException, IOException {

		response.setContentType("text/xml");
		response.setCharacterEncoding("UTF-8");
		 
  		PrintWriter writer = response.getWriter();
   		GenPersonXmlView genPersonXmlView = new GenPersonXmlView();
   		writer.println(genPersonXmlView.createPersonXmlFile());
   		writer.close();	            
    }
 
	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		doGet(request, response);
	}
	

}