package hk.eduhk.rich;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;

import org.apache.commons.lang3.StringUtils;


public abstract class BaseConverter implements Converter
{
	
	protected String getContainerClientId(FacesContext fCtx, UIComponent component)
	{
		return  ":" + StringUtils.defaultString(component.getNamingContainer().getClientId());
	}
	
	
	/**
	 * Get the refer Component by the attribute "referId" 
	 * 
	 * @param fCtx
	 * @param component
	 * @return
	 */
	protected UIComponent getReferComponent(FacesContext fCtx, UIComponent component)
	{
		String referId = (String) component.getAttributes().get("referId");
		String containerId = getContainerClientId(fCtx, component);
		return fCtx.getViewRoot().findComponent(containerId + ":" + referId);
	}


}
