package hk.eduhk.rich.param;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.persistence.*;

import org.apache.commons.codec.binary.Base64;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.UserPersistenceObject;


@Entity
@Table(name = "RH_Z_SYS_PARAM")
@SuppressWarnings("serial")
public class SysParam extends UserPersistenceObject
{
	
	// AUTH
	public static final String PARAM_ACCT_RESET_TOKEN_LIFETIME	= "account.reset.token.lifetime";
	public static final String PARAM_ACCT_LOCKOUT_DURATION		= "account.lockout.duration";
	public static final String PARAM_ACCT_LOCKOUT_THRESHOLD		= "account.lockout.threshold";
	public static final String PARAM_ACCT_MAP					= "ACCOUNT.MAP";
	
	public static final String PARAM_JWT_LIFETIME_MINUTE		= "jwt.lifetime.minute";
	public static final String PARAM_JWT_SECRET					= "jwt.secret";
	
	public static final String PARAM_LDAP_BASE						= "ldap.base";
	public static final String PARAM_LDAP_CREDENTIAL				= "ldap.credential";
	public static final String PARAM_LDAP_FILTER_USER				= "ldap.filter.user";
	public static final String PARAM_LDAP_HOST						= "ldap.host";

	public static final String PARAM_SSO_LOGOUT_URL					= "SSO_LOGOUT_URL";
	
	// EMAIL
	public static final String PARAM_MAIL_SESSION_JNDI				= "mail.session.jndi";
	public static final String PARAM_EMAIL_REROUTE					= "email.reroute";
	public static final String PARAM_EMAIL_SERVICE_ENDPOINT			= "email.service.endpoint";
	public static final String PARAM_EMAIL_SERVICE_TOKEN			= "email.service.token";
	
	//EMAIL TMPL
	public static final String PARAM_EMAIL_TMPL_ASST_REQUEST			= "email.tmpl.asst.request";
	public static final String PARAM_EMAIL_TMPL_ASST_ACK				= "email.tmpl.asst.ack";
	public static final String PARAM_EMAIL_TMPL_RI_FULL_LIST			= "email.tmpl.job.ri.fullList";
	
	// FORM
	public static final String PARAM_FORM_AUTOSAVE_INTERVAL		= "form.autosave.interval";
	
	//RAE
	public static final String PARAM_RAE_FILE_SUP_DOC				="rae_sup_doc";
	public static final String PARAM_RAE_FILE_P10					="rae_p10";
	public static final String PARAM_RAE_FILE_P12					="rae_p12";
	public static final String PARAM_RAE_FILE_FULL_VER				="rae_full_ver";
	public static final String PARAM_RAE_FILE_TOC_ATT				="rae_toc_att";
	public static final String PARAM_RAE_FILE_TOC_ATT_OTH			="rae_toc_att_oth";
	public static final String PARAM_RAE_FILE_SCW					="rae_scw";
	public static final String PARAM_RAE_FILE_ADD_INFO				="rae_add_info";
		
	// SYSTEM	
	public static final String PARAM_DOWNLOAD_MAX_RECORD 			= "DOWNLOAD_MAX_RECORD";
	public static final String PARAM_FILE_PATH						= "FILE_PATH";
	public static final String PARAM_FILE_PATH_LOCAL				= "FILE_PATH_LOCAL";
	public static final String PARAM_LOG_FILE_PATH					= "SYS_LOG_FILE_PATH";
	public static final String PARAM_LOG_FILE_PATH_LOCAL			= "SYS_LOG_FILE_PATH_LOCAL";
	public static final String PARAM_BROWSER_SUPPORT				= "BROWSER_SUPPORT";
	public static final String PARAM_MAX_AUTHOR_LIST_LENGTH			= "MAX_AUTHOR_LIST_LENGTH";
	public static final String PARAM_PLATFORM						= "PLATFORM";
	
	//OTHER
	public static final String PARAM_APA_EDITED_BOOK				="APA_EDITED_BOOK";
	public static final String PARAM_SAP_OUTPUT_TYPES				="SAP_OUTPUT_TYPES";
	public static final String PARAM_OUTPUT_PRIORITY				="OUTPUT_PRIORITY";
	public static final String PARAM_OUTPUT_ORDER					="OUTPUT_ORDER";
	public static final String PARAM_OUTPUT_ORDER_SUB				="OUTPUT_ORDER_SUB";
	
	public static final String PARAM_PERSONAL_WITHIN_YEAR_PUBLICATION					="PERSONAL_WITHIN_YEAR_PUBLICATION";
	public static final String PARAM_PERSONAL_NUM_RETRIEVE_PUBLICATION					="PERSONAL_NUM_RETRIEVE_PUBLICATION";
	public static final String PARAM_PERSONAL_TO_FIX_YEAR_PROJECT						="PERSONAL_TO_FIX_YEAR_PROJECT";
	public static final String PARAM_PERSONAL_NUM_RETRIEVE_PROJECT						="PERSONAL_NUM_RETRIEVE_PROJECT";
	
	//KT Form
	public static final String PARAM_KT_FORM_CPD					="KT_CPD";
	public static final String PARAM_KT_FORM_PROF_CONF				="KT_PROF_CONF";
	public static final String PARAM_KT_FORM_SEM					="KT_SEMINAR";
	public static final String PARAM_KT_FORM_CNT_PROJ				="KT_CNT_PROJ";
	public static final String PARAM_KT_FORM_INN					="KT_INNOVATION";
	public static final String PARAM_KT_FORM_CONS					="KT_CONSULT";
	public static final String PARAM_KT_FORM_PROF_ENGMT				="KT_PROF_ENGMT";
	public static final String PARAM_KT_FORM_IP						="KT_IP";
	public static final String PARAM_KT_FORM_SOC_ENGMT				="KT_SOC_ENGMT";
	public static final String PARAM_KT_FORM_STAFF_ENGMT			="KT_STAFF_ENGMT";
	public static final String PARAM_KT_FORM_EA						="KT_EA";
	public static final String PARAM_KT_FORM_STARTUP				="KT_STARTUP";
	public static final String PARAM_KT_FORM_INV_AWARD				="KT_INV_AWARD";
	
	//KT Form Class
	public static final String PARAM_KT_FORM_CLASS_CPD					="ktFormCPD_p";
	public static final String PARAM_KT_FORM_CLASS_PROF_CONF			="ktFormProfConf_p";
	public static final String PARAM_KT_FORM_CLASS_SEM					="ktFormSem_p";
	public static final String PARAM_KT_FORM_CLASS_CNT_PROJ				="ktFormCntProj_p";
	public static final String PARAM_KT_FORM_CLASS_INN					="ktFormInn_p";
	public static final String PARAM_KT_FORM_CLASS_CONS					="ktFormCons_p";
	public static final String PARAM_KT_FORM_CLASS_PROF_ENGMT			="ktFormProfEngmt_p";
	public static final String PARAM_KT_FORM_CLASS_IP					="ktFormIP_p";
	public static final String PARAM_KT_FORM_CLASS_SOC_ENGMT			="ktFormSocEngmt_p";
	public static final String PARAM_KT_FORM_CLASS_STAFF_ENGMT			="ktFormStaffEngmt_p";
	public static final String PARAM_KT_FORM_CLASS_EA					="ktFormEA_p";
	public static final String PARAM_KT_FORM_CLASS_STARTUP				="ktFormStartup_p";
	public static final String PARAM_KT_FORM_CLASS_INV_AWARD			="ktFormInvAward_p";
	
	
	
	// SCHEDULER
	public static final String PARAM_HREC_DATA_DOMAIN						="HREC_DATA_DOMAIN";
	public static final String PARAM_HREC_DATA_USERNAME						="HREC_DATA_USERNAME";
	public static final String PARAM_HREC_DATA_PASSWORD						="HREC_DATA_PASSWORD";
	public static final String PARAM_HREC_DATA_BATCH_NAME					="HREC_DATA_BATCH_NAME";
	
	//Special Character Department
	public static final Object PARAM_SPECIAL_DEPT_STR 						= " OR S.DEPARTMENT_CODE LIKE 'VP(AC)&Pr%' OR S.DEPARTMENT_CODE LIKE 'C&I%'  ) ";	
	public static final Object PARAM_SPECIAL_LOOKUP_STR 						= " OR LOOKUP_CODE LIKE 'VP(AC)&Pr%' OR LOOKUP_CODE LIKE 'C&I%'  ) ";
	
	public static Logger logger = Logger.getLogger(SysParam.class.toString());
		
	@Id
	@Column(name = "param_code", length = 50)
	private String code;
	
	@Column(name = "param_value", length = 4000)
	private String value;
	
	@Column(name = "param_desc", length = 4000)
	private String description;
	
	@Column(name = "param_group", length = 30)
	private String group;
	
	@Column(name = "is_encrypted")
	private boolean encrypted;
	
	@Transient
	private String decryptedValue = null;
	
	@Transient
	private boolean decryptedValueClear = false;
	
	@Transient
	private boolean changed = false;
	
	private static IvParameterSpec ivParamSpec = new IvParameterSpec(new byte[16]);
	
	
	public String getCode()
	{
		return code;
	}


	public void setCode(String code)
	{
		this.code = code;
	}


	public String getValue()
	{
		return value;
	}
	
	
	public void setValue(String value)
	{
		this.value = value;
	}


	public String getDescription()
	{
		return description;
	}


	public void setDescription(String description)
	{
		this.description = description;
	}
	
	
	// Intended to be called in sysParamEdit.xhtml only
	public void clearValue()
	{
		this.value = null;
	}
	

	public String getGroup()
	{
		return group;
	}


	public void setGroup(String group)
	{
		this.group = group;
	}


	public boolean isEncrypted()
	{
		return encrypted;
	}


	public void setEncrypted(boolean encrypted)
	{
		this.encrypted = encrypted;
	}


	public String getDecryptedValue()
	{
		if (encrypted && !decryptedValueClear && decryptedValue == null && value != null)
		{
			// Decrypt the value
			try
			{
				SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
				Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.DECRYPT_MODE, spec, ivParamSpec);
				
				byte[] decryptData = cipher.doFinal(new Base64().decode(value.getBytes("UTF-8")));
				decryptedValue = new String(decryptData, "UTF-8");			
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Decryption is failed", e);
			}
		}
		
		return (!encrypted) ? value : decryptedValue;
	}


	public void setDecryptedValue(String decryptedValue)
	{
		this.decryptedValue = decryptedValue;
		
		if (encrypted && decryptedValue != null)
		{
			// Encrypt the value
			try
			{
				SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
				Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.ENCRYPT_MODE, spec, ivParamSpec);
				
				byte[] encryptData = cipher.doFinal(decryptedValue.getBytes("UTF-8"));
				this.value = new String(new Base64().encode(encryptData), "UTF-8");
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Encryption is failed", e);
			}
		}
		else
		{
			this.value = decryptedValue;
		}
	}
	
	
	// Intended to be called in sysParamEdit.xhtml only
	public void clearDecryptedValue()
	{
		decryptedValue = null;
		decryptedValueClear = true;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((code == null) ? 0 : code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SysParam other = (SysParam) obj;
		if (code == null) {
			if (other.code != null)
				return false;
		}
		else if (!code.equals(other.code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SysParam [code=" + code + ", value=" + value + ", description=" + description + ", group=" + group
				+ ", encrypted=" + encrypted + "]";
	}
			
	
}
