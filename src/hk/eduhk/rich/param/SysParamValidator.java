package hk.eduhk.rich.param;

import java.text.MessageFormat;

import javax.faces.application.FacesMessage;
import javax.faces.component.*;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;

import hk.eduhk.rich.BaseFacesValidator;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.util.JPAUtils;


@FacesValidator("hk.eduhk.rich.param.SysParamValidator")
public class SysParamValidator extends BaseFacesValidator
{
	
	public static final Integer[] VALID_LENGTH_CODE 		= {1, JPAUtils.getColumnLength(SysParam.class, "code")};
	public static final Integer[] VALID_LENGTH_DESCRIPTION	= {0, JPAUtils.getColumnLength(SysParam.class, "description")};
	public static final Integer[] VALID_LENGTH_GROUP 		= {1, JPAUtils.getColumnLength(SysParam.class, "group")};
	public static final Integer[] VALID_LENGTH_VALUE	 	= {0, JPAUtils.getColumnLength(SysParam.class, "value")};
	
	
	public SysParamValidator()
	{
		super();
	}
	
	
	public void validateCode(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_CODE);
		
		if (obj != null)
		{
			try
			{
				String code = (String) obj;
				if (code != null)
				{
					SysParamDAO dao = SysParamDAO.getInstance();
					SysParam dbObj = dao.getSysParamByCode(code);
					if (dbObj != null) throw new IllegalArgumentException();
				}
			}
			catch (Exception e)
			{
				String msg = MessageFormat.format(userSessionView.getResourceBundle().getString("msg.err.duplicate.x"), obj);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
		}
	}

	
	public void validateDescription(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_DESCRIPTION);
	}

	
	public void validateGroup(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_GROUP);
	}
	
	
	public void validateValue(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_VALUE);
	}
	
	
}
