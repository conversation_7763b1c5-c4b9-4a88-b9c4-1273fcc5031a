package hk.eduhk.rich.logging;

import java.io.PrintStream;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * The <tt>LogPrintStream</tt> intercepts messages from PrintStream and
 * redirect these messages to the destined objLogger.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LogPrintStream extends PrintStream
{
	// default objLevel of logging
	private static final Level DEFAULT_LEVEL = Level.FINE;
	
	private PrintStream os;
	private Logger logger;
	private Level level;
	
	/**
	 * Initalize a LogPrintStream that wraps an OutputStream and uses logger for logging.
	 *
	 * @param objPrintStream the OutputStream that is wrapped by the LogPrintStream.
	 * @param objLogger the objLogger for logging.
	 */
	public LogPrintStream(PrintStream objPrintStream, Logger objLogger)
	{
		this(objPrintStream, objLogger, DEFAULT_LEVEL);
	}
	
	
	/**
	 * Initalize a LogPrintStream that wraps an OutputStream and uses logger for logging.
	 *
	 * @param objPrintStream the OutputStream that is wrapped by the LogPrintStream.
	 * @param objLogger the objLogger for logging.
	 * @param objLevel the message objLevel that the intercepted message should be published.
	 */
	public LogPrintStream(PrintStream objPrintStream, Logger logger, Level level)
	{
		super(objPrintStream);
		setLogger(logger);
		setLevel(level);
	}
	
	
	/**
	 * Set the message level that the intercepted message should be published.
	 *
	 * @param objLevel the message level that the intercepted message should be published.
	 */
	public void setLevel(Level level)
	{
		this.level = level;
	}

	
	/**
	 * Get the message level that the intercepted message should be published.
	 *
	 * @return the message level that the intercepted message should be published.
	 */
	public Level getLevel()
	{
		return level;
	}

	
	/**
	 * Set the logger that is used for logging in this Stream.
	 *
	 * @param objLogger the logger that is used for logging in this Stream.
	 */
	public synchronized void setLogger(Logger logger)
	{
		this.logger = logger;
	}

	
	/**
	 * Get the logger that is used for logging in this Stream.
	 *
	 * @return the logger that is used for logging in this Stream.
	 */
	public Logger getLogger()
	{
		return logger;
	}


	/**
	 * Get the wrapped PrintStream.
	 *
	 * @return the wrapper PrintStream.
	 */
	public PrintStream getPrintStream()
	{
		return os;
	}
	

	/**
	 * Override the original method of println. The message will be published by the logger.
	 *
	 * @param strMessage the input message.
	 */	
	public void println(String strMessage)
	{
		logger.log(level, strMessage);
	}
	

	/**
	 * Override the original method of println. The message will be published by the logger.
	 *
	 * @param strMessage the input message.
	 */	
	public void print(String strMessage)
	{
		logger.log(level, strMessage);
	}
}
