package hk.eduhk.rich.logging;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.util.Date;
import java.util.logging.Formatter;
import java.util.logging.LogRecord;

/**
 * Print a brief summary of the LogRecord in a human readable
 * format. The summary will typically be 1 or 2 lines. This class 
 * is just for retaining the original log format. If no customization
 * of log format is needed, this class can be replaced by SimpleFormatter.
 *
 * <p>
 * The logging format is prededined as: [{0,date} {0,time}][{1}][{2}][{3}]: {4}
 * <ul>
 * <li> {0} DateTime
 * <li> {1} Level
 * <li> {2} Class Name
 * <li> {3} Method Name
 * <li> {4} Thread ID
 * <li> {5} Log Message
 * </ul>
 *
 * <AUTHOR> Ng
 * @version 1.0, 09/26/05
 */
public class LogFormatter extends Formatter 
{

	public static final String PATTERN_LONG		= "[{0,date,yyyy/MM/dd} {0,time,HH:mm:ss}][{1}][{2}][{3}][{4}]: {5}";
	public static final String PATTERN_DEFAULT	= "[{0,date,yyyy/MM/dd} {0,time,HH:mm:ss}][{1}][{4}]: {5}";
	
	Date m_objDate = new Date();
    
    private String m_objFormat = PATTERN_DEFAULT;
    private MessageFormat m_objFormatter;

    private Object m_objArgs[] = new Object[6];

    // Line separator string.  This is the value of the line.separator
    // property at the moment that the SimpleFormatter was created.
    private String m_strLineSeparator = System.getProperty("line.separator");


	public LogFormatter()
	{
		this(PATTERN_LONG);
	}
	
	
	public LogFormatter(String objFormat)
	{
		super();
		setFormat(objFormat);
	}
	

    /**
     * Format the given LogRecord.
     * @param record the log record to be formatted.
     * @return a formatted log record
     */
    public synchronized String format(LogRecord record)
    {	
		// Initialize the m_objFormatter.
		if (m_objFormatter == null) setFormat(m_objFormat);
		
		// Minimize memory allocations here.
		m_objDate.setTime(record.getMillis());
		
		// DateTime
		m_objArgs[0] = m_objDate;
		
		// Level name
		m_objArgs[1] = record.getLevel().getLocalizedName();
			
		// Class name
		if (record.getSourceClassName() != null)
		{	
			m_objArgs[2] = record.getSourceClassName();
		}
		else 
		{
			m_objArgs[2] = record.getLoggerName();
		}
		
		// Method name
		if (record.getSourceMethodName() != null)
		{
			m_objArgs[3] = record.getSourceMethodName();
		}
		
		// Thread ID
		m_objArgs[4] = new Integer(record.getThreadID());

		// Log message
		m_objArgs[5] = formatMessage(record);

		// formatting
		StringBuffer objBuffer = new StringBuffer(64);
		objBuffer = m_objFormatter.format(m_objArgs, objBuffer, null);
		objBuffer.append(m_strLineSeparator);
		
		if (record.getThrown() != null)
		{
		    try 
		    {
		        StringWriter objsw = new StringWriter();
		        PrintWriter objpw = new PrintWriter(objsw);
		        record.getThrown().printStackTrace(objpw);
		        objpw.close();
				objBuffer.append(objsw.toString());
		    } 
		    catch (Exception ex) 
		    {
		    }
		}
		return objBuffer.toString();
    }
    
    
    /**
     * Set the log format of this m_objFormatter.
     *
     * @param objFormat the log format
     */
    public void setFormat(String objFormat)
    {
    	synchronized (this)
    	{
    		m_objFormat = objFormat;
    		
    		if (m_objFormatter == null)
    		{
    			m_objFormatter = new MessageFormat(m_objFormat);
    		}
    		else
    		{
    			m_objFormatter.applyPattern(m_objFormat);
    		}
    	}
    }
    
    
    /**
     * Get the log format of this m_objFormatter.
     *
     * @return the log format of this m_objFormatter.
     */
    public String getFormat()
    {
    	return m_objFormat;
    }
}
