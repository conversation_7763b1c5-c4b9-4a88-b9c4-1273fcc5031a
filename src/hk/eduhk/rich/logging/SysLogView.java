package hk.eduhk.rich.logging;

import java.io.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.io.IOUtils;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.util.MimeMap;


@ManagedBean
@ViewScoped
public class SysLogView extends BaseView
{
	
	private static final long serialVersionUID = 1L;

	
	private List<Map<String, Object>> sysLogList;
	
	private String sysLogLoc;
	private FileFilter sysLogFileFilter;
	private List<Event> events;
	
	private Map<String, Object> selectedFileName;
	
	private String sortBy;
	private String sortOrder;
	
	
	public SysLogView()
	{
		this.sortBy = "name";
		this.sortOrder = "descending";
		
		sysLogLoc = SysLogFileHandler.getSysLogFilePath();
		sysLogFileFilter = new SysLogFileFilter(sysLogLoc, Constant.LOG_FILE_PREFIX);
	}
	
	
	public Map<String, Object> getSelectedFileName()
	{
		return selectedFileName;
	}


	public void setSelectedFileName(Map<String, Object> selectedFileName)
	{
		this.selectedFileName = selectedFileName;
	}


	public String getSortBy()
	{
		return sortBy;
	}

	
	public void setSortBy(String sortBy)
	{
		this.sortBy = sortBy;
	}

	
	public String getSortOrder()
	{
		return sortOrder;
	}

	
	public void setSortOrder(String sortOrder)
	{
		this.sortOrder = sortOrder;
	}


	public List<Map<String, Object>> getSysLogList()
	{
		if (sysLogList == null)
		{
			File dir = new File(sysLogLoc);
			if (dir.isDirectory() && dir.exists())
			{
				File[] logFiles = dir.listFiles(sysLogFileFilter);
				if (logFiles != null)
				{
					sysLogList = new ArrayList<Map<String, Object>>();
					for (int n=0;n<logFiles.length;n++)
					{
						Map<String, Object> logEntry = new HashMap<String, Object>();
						logEntry.put("name", logFiles[n].getName());
						logEntry.put("size", logFiles[n].length());
						logEntry.put("lastModified", logFiles[n].lastModified());
						sysLogList.add(logEntry);
					}
				}
			}
		}

		return sysLogList;
	}
	
	
	public void downloadSysLog() throws IOException
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
	    ExternalContext eCtx = fCtx.getExternalContext();

		// Check whether the input name is valid 
	    String fileName = (String) selectedFileName.get("name");
		File f = new File(sysLogLoc, fileName);
		boolean accept = sysLogFileFilter.accept(f);

		if (accept)
		{
			int idx = f.getName().indexOf(".");
			String zipFileName = fileName.substring(0, idx) + ".zip";
			
			// Some JSF component library or some Filter might have set some headers in the buffer beforehand. We want to get rid of them, else it may collide.
		    eCtx.responseReset(); 
		    eCtx.setResponseContentType(MimeMap.getInstance().get("zip"));
		    eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\"" + zipFileName + "\"");
		    
		    eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
		    eCtx.setResponseHeader("Expires", "-1");
		    eCtx.setResponseHeader("Pragma", "private");
			
		    InputStream is = null;
			ZipOutputStream zo = null;

		    try
		    {
		    	is = new BufferedInputStream(new FileInputStream(f));
		    	zo = new ZipOutputStream(eCtx.getResponseOutputStream());
				ZipEntry ze = new ZipEntry(f.getName());
				zo.putNextEntry(ze);
			    IOUtils.copy(is, zo);
		    }
		    finally
		    {
		    	IOUtils.closeQuietly(zo);
		    	IOUtils.closeQuietly(is);
		    }
		
		    // Important! Otherwise JSF will attempt to render the response which obviously will fail since it's already written with a file and closed.
		    fCtx.responseComplete(); 
		}

	}
	
	
	public void reloadSysLogList()
	{
		this.sysLogList = null;
	}
	
	public List<Event> getEvents() {
		events = new ArrayList<>();
        events.add(new Event("Phase II", "2021-10", "#9C27B0", "v2.0"));
        events.add(new Event("Phase I", "2021-06", "#673AB7", "v1.0"));
        return events;
    }
	
	public static class Event {
        String status;
        String date;
        String color;
        String ver;

        public Event() {
        }

        public Event(String status, String date, String color, String ver) {
            this.status = status;
            this.date = date;
            this.color = color;
            this.ver = ver;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }

        public String getVer() {
            return ver;
        }

        public void setVer(String ver) {
            this.ver = ver;
        }
    }
	
}
