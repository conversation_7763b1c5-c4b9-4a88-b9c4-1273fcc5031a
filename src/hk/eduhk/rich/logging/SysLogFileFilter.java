package hk.eduhk.rich.logging;

import java.io.File;
import java.io.FileFilter;
import java.io.Serializable;


public class SysLogFileFilter implements FileFilter, Serializable
{
	
	private final String separator = System.getProperty("file.separator");
	
	private String location;
	private String prefix;
	

	public SysLogFileFilter(String location, String prefix)
	{
		if (location != null)
		{
			this.location = location;
			this.location = this.location.replace("/", separator);
			this.location = this.location.replace("\\", separator);
		}
		
		this.prefix = prefix;
	}
	
	
	@Override
	public boolean accept(File pathname)
	{
		return (pathname.getParent().equals(location) && pathname.getName().startsWith(prefix));
	}
	
}
