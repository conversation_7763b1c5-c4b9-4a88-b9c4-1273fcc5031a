package hk.eduhk.rich.logging;

import java.io.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;


/**
 * System Log File Handler
 */
 public class SysLogFileHandler extends LogFileHandler
{
	 
	 public static final int SYS_LOG_FILE_EXPIRY_DAY = 60;
	 
	 
	/**
	 * Initializes a LogFileHandler with default settings.
	 */
	public SysLogFileHandler() throws IOException, SecurityException
	{
		super();
	}


	/**
	 * Initializes a LogFileHandler with the file name pattern.
	 *
	 * @param strPattern the name pattern of the output file.
	 */
	public SysLogFileHandler(String strPattern) throws IOException, SecurityException
	{
		super(strPattern);
	}
	

	/**
	 * Initializes a LogFileHandler with the file name pattern, and the append mode.
	 *
	 * @param strPattern the name pattern of the output file.
	 * @param blnAppend specifies append mode.
	 */
	public SysLogFileHandler(String strPattern, boolean blnAppend) throws IOException, SecurityException
	{
		super(strPattern, blnAppend);
	}
	
	
	/**
	 * Initializes a LogFileHandler with the file name pattern, and the file rotation triggering policy.
	 *
	 * @param strPattern the name pattern of the output file.
	 * @param objPolicy the file rotation triggering policy.
	 */
	public SysLogFileHandler(String strPattern, TriggeringPolicy objPolicy) throws IOException, SecurityException
	{
		super(strPattern, objPolicy);
	}
	
	
	/**
	 * Initializes a LogFileHandler with the file name pattern, and the file rotation triggering policy,
	 * and the append mode.
	 *
	 * @param strPattern the name pattern of the output file.
	 * @param objPolicy the file rotation triggering policy.
	 * @param blnAppend specifies append mode.
	 */
	public SysLogFileHandler(String strPattern, TriggeringPolicy objPolicy, boolean blnAppend) 
		throws IOException, SecurityException
	{
		super(strPattern, objPolicy, blnAppend);
	}

	 		
	/**
	 * Purge old log files during file rotation
	 */
	@Override
	protected void purgeExpiryLogFiles()
	{
		// Get system parameters
		//SysParamDAO paramDAO = SysParamDAO.getInstance();
		//int expiryDay = paramDAO.getSysParamIntByCode(SysParam.PARAM_SYS_LOG_FILE_EXPIRY);
		
		int expiryDay = SYS_LOG_FILE_EXPIRY_DAY;
		String fileLoc = getSysLogFilePath();
		String filePrefix = Constant.LOG_FILE_PREFIX;

		// Find the expired files from the log file directory
		File logFileDir = new File(fileLoc);
		if (logFileDir.exists() && logFileDir.isDirectory())
		{
			// Get the expiry date (time = 0:00:00).
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.SECOND, 0);
			cal.set(Calendar.MINUTE, 0);
			cal.set(Calendar.HOUR, 0);
			cal.set(Calendar.AM_PM, 0);
			cal.add(Calendar.DATE, -expiryDay);
			Date expiryDate = cal.getTime();
			
			SysLogFileFilter filter = new SysLogFileFilter(fileLoc, filePrefix);
			File[] logFiles = logFileDir.listFiles(filter);
			Logger logger = null;
			
			if (logFiles != null && logFiles.length > 0)
			{
				for (File logFile : logFiles)
				{
					// Delete the file and log the file name
					if (logFile.lastModified() <= expiryDate.getTime())
					{
						if (logger == null) logger = Logger.getLogger(this.getClass().getName());
						logger.log(Level.INFO, "Log file to be deleted=" + logFile.getName());
						logFile.delete();
					}
				}
			}
		}
	}
	
	
	public static String getSysLogFilePath()
	{
		String code = Constant.isLocalEnv() ? SysParam.PARAM_LOG_FILE_PATH_LOCAL : SysParam.PARAM_LOG_FILE_PATH;
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		return paramDAO.getSysParamValueByCode(code);
	}
		
}