package hk.eduhk.rich.logging;

import java.io.File;
import java.util.Calendar;

/**
 * <tt>Triggering policy based on time</tt>.
 * <p>
 * This class is immutable. Once the instance is created, there is no way to modify
 * the triggering period of the instance.
 *
 * <AUTHOR>
 * @version 1.0
 */

public class TimeBasedTriggeringPolicy implements TriggeringPolicy
{

	public static final int PERIOD_MINUTE = 0;		// mainly for testing purpose.
	public static final int PERIOD_HOUR   = 1;
	public static final int PERIOD_DAY    = 24;
	public static final int PERIOD_WEEK   = 168;
	public static final int PERIOD_MONTH  = 720;
	public static final int PERIOD_YEAR   = 8760;
	
	private int period;
	private long nextCheckTime;


	/**
	 * Initialize a TimeBasedTriggeringPolicy instance with a default rotation period.
	 * The default rotation period is a day.
	 */
	public TimeBasedTriggeringPolicy()
	{
		this(PERIOD_DAY);
	}
	
	
	/**
	 * Initialize a TimeBasedTriggeringPolicy instance with given period.
	 *
	 * @param period the triggering period of this instance.
	 * @exception IllegalArgumentException if the period value is illegal.
	 */
	public TimeBasedTriggeringPolicy(int period)
	{
		// Argument check
		switch (period)
		{
			case PERIOD_MINUTE:
			case PERIOD_HOUR:
			case PERIOD_DAY:
			case PERIOD_WEEK:
			case PERIOD_MONTH:
			case PERIOD_YEAR:
			 	break;
			
			default:
			 	throw new IllegalArgumentException("Illegal period.");
		}
		
		// Initialization.
		this.period = period;
		nextCheckTime = nextTriggeringTime(System.currentTimeMillis());
	}


	/**
	 * Get the triggering period of this policy.
	 *
	 * @return the triggering period of this policy.
	 */
	public int getPeriod()
	{
		return period;
	}
	
	
	/**
	 * Check whether the active log file should be rotated or not.
	 * 
	 * @param f The currently active log file. 
	 * @return whether the active log file should be rotated or not.
	 */
    public boolean isTriggered(File f)
    {
    	boolean isTriggered = false;
    	long intCurrentTime = System.currentTimeMillis();
    	
    	if (f != null && intCurrentTime >= nextCheckTime)
    	{
    		isTriggered = true;
    		nextCheckTime = nextTriggeringTime(intCurrentTime);
    	}
    	
    	return isTriggered;
    }
 
 
 	/**
 	 * Get the next triggering time by the input time.
 	 *
 	 * @param inputTime The input time.
 	 * @return the next triggering time of event.
 	 */
    private long nextTriggeringTime(long inputTime)
    {
		Calendar objCal = Calendar.getInstance();
		objCal.setTimeInMillis(inputTime);
    	
    	switch (period)
    	{   		
    		case PERIOD_MINUTE:
		    	objCal.set(Calendar.MILLISECOND, 0);
		     	objCal.set(Calendar.SECOND, 0);
		     	objCal.add(Calendar.MINUTE, 1);
    		 	break;
    		
    		case PERIOD_HOUR:
		    	objCal.set(Calendar.MILLISECOND, 0);
		     	objCal.set(Calendar.SECOND, 0);
		     	objCal.set(Calendar.MINUTE, 0);
    		 	objCal.add(Calendar.HOUR, 1);
    		 	break;
    		
    		case PERIOD_DAY:
		     	objCal.set(Calendar.MILLISECOND, 0);
		    	objCal.set(Calendar.SECOND, 0);
		    	objCal.set(Calendar.MINUTE, 0);
		    	objCal.set(Calendar.HOUR, 0);
    			objCal.set(Calendar.AM_PM, 0);
    			objCal.add(Calendar.DATE, 1);
    			break;

			case PERIOD_WEEK:
	      		objCal.set(Calendar.DAY_OF_WEEK, objCal.getFirstDayOfWeek());
	      		objCal.set(Calendar.MILLISECOND, 0);
	      		objCal.set(Calendar.SECOND, 0);
		    	objCal.set(Calendar.MINUTE, 0);
	      		objCal.set(Calendar.HOUR, 0);
    			objCal.set(Calendar.AM_PM, 0);
	      	 	objCal.add(Calendar.WEEK_OF_YEAR, 1);
				break;
    		
    		case PERIOD_MONTH:
		    	objCal.set(Calendar.MILLISECOND, 0);
		    	objCal.set(Calendar.SECOND, 0);
		    	objCal.set(Calendar.MINUTE, 0);
		    	objCal.set(Calendar.HOUR, 0);
    			objCal.set(Calendar.AM_PM, 0);
		    	objCal.set(Calendar.DATE, 1);
    			objCal.add(Calendar.MONTH, 1);
    			break;
    		
    		case PERIOD_YEAR:
			    objCal.set(Calendar.MILLISECOND, 0);
			    objCal.set(Calendar.SECOND, 0);
			    objCal.set(Calendar.MINUTE, 0);
			    objCal.set(Calendar.HOUR, 0);
    			objCal.set(Calendar.AM_PM, 0);
			    objCal.set(Calendar.DATE, 1);
	    		objCal.set(Calendar.MONTH, 0);
	    		objCal.add(Calendar.YEAR, 1);
	    		break;
    	}
    	
    	return objCal.getTimeInMillis();
    }
    
    
    public TimeBasedTriggeringPolicy clone()
    {
    	return new TimeBasedTriggeringPolicy(period);
    }
    
}