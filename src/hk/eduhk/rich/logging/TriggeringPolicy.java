package hk.eduhk.rich.logging;

import java.io.File;

/**
 * Triggering policy for file handler.
 * <p>
 * The <tt>TrriggeringPolicy</tt> is an interface that has only a method. This method
 * indicates whether the file should be triggered for file rotation.
 *
 * <AUTHOR>
 * @version 1.0, 09/27/05
 */

public interface TriggeringPolicy extends Cloneable
{
	
	/**
	 * Check whether the active log file should be rotated or not.
	 * 
	 * @param objFile The currently active log file. 
	 * @return whether the active log file should be rotated or not.
	 */
    boolean isTriggered(File objFile);
    
    TriggeringPolicy clone();
    
}