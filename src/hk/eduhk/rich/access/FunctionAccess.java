package hk.eduhk.rich.access;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "access")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class FunctionAccess implements Serializable
{
	
	@XmlElement(name = "authorizer")
	private Authorizer authorizer;

	@XmlElement(name = "url")
	private List<String> urlList;
	
	
	public Authorizer getAuthorizer()
	{
		return authorizer;
	}

	
	public void setAuthorizer(Authorizer authorizer)
	{
		this.authorizer = authorizer;
	}

		
	public List<String> getUrlList()
	{
		return urlList;
	}

	
	public void setUrlList(List<String> urlList)
	{
		this.urlList = urlList;
	}


	@Override
	public String toString()
	{
		return "FunctionAccess [authorizer=" + authorizer + ", urlList=" + urlList + "]";
	}

	
}
