package hk.eduhk.rich.access;

import java.io.Serializable;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "role")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Role implements Serializable
{

	@XmlElement(name = "id")
	private String roleId;
	
	@XmlElement(name = "name")
	private String name;
	
	@XmlElement(name = "description")
	private String description;

	
	public String getRoleId()
	{
		return roleId;
	}

	
	public void setRoleId(String roleId)
	{
		this.roleId = roleId;
	}

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((roleId == null) ? 0 : roleId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Role other = (Role) obj;
		if (roleId == null)
		{
			if (other.roleId != null)
				return false;
		}
		else if (!roleId.equals(other.roleId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "Role [roleId=" + roleId + ", name=" + name + ", description=" + description + "]";
	}

}
