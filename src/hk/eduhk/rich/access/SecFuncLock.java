package hk.eduhk.rich.access;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;

import javax.persistence.Table;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_Z_SEC_FUNC_LOCK")
@SuppressWarnings("serial")
public class SecFunc<PERSON>ock extends UserPersistenceObject
{
	@EmbeddedId
	private SecFuncLockPK pk = new SecFuncLockPK();

	@Column(name = "LOCK_STATUS")
	private String lock_status;
	
	@Column(name = "LOCK_MSG")
	private String lock_msg;

	@Column(name = "FAC")
	private String fac;
	
	@Column(name = "DEPT")
	private String dept;
	
	public SecFuncLockPK getPk()
	{
		return pk;
	}

	
	public void setPk(SecFuncLockPK pk)
	{
		this.pk = pk;
	}

	
	public String getLock_status()
	{
		return lock_status;
	}

	
	public void setLock_status(String lock_status)
	{
		this.lock_status = lock_status;
	}

	
	public String getLock_msg()
	{
		return lock_msg;
	}

	
	public void setLock_msg(String lock_msg)
	{
		this.lock_msg = lock_msg;
	}


	
	public String getFac()
	{
		return fac;
	}


	
	public void setFac(String fac)
	{
		this.fac = fac;
	}


	
	public String getDept()
	{
		return dept;
	}


	
	public void setDept(String dept)
	{
		this.dept = dept;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SecFuncLock other = (SecFuncLock) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SecFuncLock [pk=" + pk + ", lock_status=" + lock_status + ", lock_msg=" + lock_msg + ", fac=" + fac
				+ ", dept=" + dept + "]";
	}

}
