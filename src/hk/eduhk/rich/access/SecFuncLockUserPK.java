package hk.eduhk.rich.access;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class SecFuncLockUserPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="LOCK_CODE", length = 30)
	private String lock_code;

	@Column(name="FUNC_ID", length = 50)
	private String func_id;

	@Column(name="USER_ID", length = 20)
	private String user_id;

	
	public String getLock_code()
	{
		return lock_code;
	}

	
	public void setLock_code(String lock_code)
	{
		this.lock_code = lock_code;
	}

	
	public String getFunc_id()
	{
		return func_id;
	}

	
	public void setFunc_id(String func_id)
	{
		this.func_id = func_id;
	}

	
	public String getUser_id()
	{
		return user_id;
	}

	
	public void setUser_id(String user_id)
	{
		this.user_id = user_id;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((func_id == null) ? 0 : func_id.hashCode());
		result = prime * result + ((lock_code == null) ? 0 : lock_code.hashCode());
		result = prime * result + ((user_id == null) ? 0 : user_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SecFuncLockUserPK other = (SecFuncLockUserPK) obj;
		if (func_id == null)
		{
			if (other.func_id != null)
				return false;
		}
		else if (!func_id.equals(other.func_id))
			return false;
		if (lock_code == null)
		{
			if (other.lock_code != null)
				return false;
		}
		else if (!lock_code.equals(other.lock_code))
			return false;
		if (user_id == null)
		{
			if (other.user_id != null)
				return false;
		}
		else if (!user_id.equals(other.user_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SecFuncLockUserPK [lock_code=" + lock_code + ", func_id=" + func_id + ", user_id=" + user_id + "]";
	}



}
