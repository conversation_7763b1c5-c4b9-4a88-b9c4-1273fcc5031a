package hk.eduhk.rich.access;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Level;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.banner.BanPerson;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean
@SessionScoped
@SuppressWarnings("serial")
public class UserSessionView extends BaseView
{
	
	private static String sourceVersion = null;
	
	public static UserSessionView getCurrentInstance() 
	{
		UserSessionView instance = null;

		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (fCtx != null)
		{
			ExternalContext eCtx = fCtx.getExternalContext();
			Map<String, Object> sessionMap = eCtx.getSessionMap();
			
			String attr = StringUtils.uncapitalize(UserSessionView.class.getSimpleName());
			instance = (UserSessionView) (sessionMap != null ? sessionMap.get(attr) : null);
			
			// Manually instantiate a UserSessionView instance
			// if it has not yet existed in session
			if (instance == null)
			{
				instance = new UserSessionView();
				sessionMap.put(attr, instance);
			}
		}
		
		return instance;
	}

	
	public UserSessionView()
	{
		getLogger().log(Level.FINE, "A new user session is created for user " + getUserId());
	}
	
	
	/**
	 * Return the actual user ID
	 */
	public String getLoginUserId()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		Map<String, Object> sessionMap = fCtx.getExternalContext().getSessionMap();
		return (String) sessionMap.get(Constant.ATTR_LOGIN_USER_ID);
	}
	
	
	/**
	 * Return the imperonsate user ID. 
	 * This method returns null if impersonation is not activated
	 */
	public String getImpersonateUserId()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		Map<String, Object> sessionMap = fCtx.getExternalContext().getSessionMap();
		return (String) sessionMap.get(Constant.ATTR_IMPERSONATE_USER_ID);
	}
	
	
	public String getUserId()
	{
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		return eCtx.getRemoteUser();
	}

	
	public BanPerson getPerson()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		Map<String, Object> sessionMap = fCtx.getExternalContext().getSessionMap();
		return (BanPerson) sessionMap.get(Constant.ATTR_CURRENT_PERSON);
	}
	
	
	public boolean isLogin()
	{
		return !GenericValidator.isBlankOrNull(getUserId());
	}

	
	public String logout()
	{
		try
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			ExternalContext eCtx = fCtx.getExternalContext();
			
			String userId = eCtx.getRemoteUser();
			getLogger().log(Level.INFO, "Logout user="+ userId);
			
			
			if (!GenericValidator.isBlankOrNull(userId))
			{
				// Invalidate session
				eCtx.invalidateSession();
				eCtx.getSession(true);
				
				// Redirect to SSO logout page
				String redirectURL = SysParamDAO.getInstance().getSysParamValueByCode(SysParam.PARAM_SSO_LOGOUT_URL);
				if (!GenericValidator.isBlankOrNull(redirectURL))
				{
					eCtx.redirect(redirectURL);
				}
				else
				{
					getLogger().log(Level.INFO, "Logout link is not defined in system parameter");
				}
			}
		}
		catch (Exception e)
		{
			getLogger().log(Level.WARNING, "", e);
		}
		
		return "";
	}
	
	private synchronized void readVersionProperties()
	{
		FacesContext fCtx =  FacesContext.getCurrentInstance();
		ServletContext sCtx = (ServletContext) fCtx.getExternalContext().getContext();
		
		try
		(
			InputStream is = sCtx.getResourceAsStream("/WEB-INF/version.properties")
		)
		{
			Properties versionProps = new Properties();
			versionProps.load(is);
			
			if (versionProps != null)
			{
				String param = "scm.version";
				String version = (versionProps != null) ? versionProps.getProperty("scm.version") : null; 
				sourceVersion = (!StringUtils.equals(version, "${" + param + "}")) ? version : null;
			}
		}
		catch (IOException ioe)
		{
			getLogger().log(Level.WARNING, "Cannot load version.properties", ioe);
		}
		
		sourceVersion = StringUtils.defaultString(sourceVersion);
	}
	

	/**
	 * Return the version from the source control management system (e.g. SVN) 
	 * This method returns "-" if the version is not defined in version.properties
	 * 
	 * @return
	 */
	
	public String getSourceVersion()
	{
		if (sourceVersion == null) readVersionProperties();
		return sourceVersion;
	}
	
}
