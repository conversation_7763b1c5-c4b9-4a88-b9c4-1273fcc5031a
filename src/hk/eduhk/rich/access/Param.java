package hk.eduhk.rich.access;

import java.io.Serializable;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "param")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Param implements Serializable
{

	@XmlElement(name = "name")
	private String name;
	
	@XmlElement(name = "value")
	private String value;

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}

	
	public String getValue()
	{
		return value;
	}

	
	public void setValue(String value)
	{
		this.value = value;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Param other = (Param) obj;
		if (name == null)
		{
			if (other.name != null)
				return false;
		}
		else if (!name.equals(other.name))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "Param [name=" + name + ", value=" + value + "]";
	}
	
}
