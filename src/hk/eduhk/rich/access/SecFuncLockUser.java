package hk.eduhk.rich.access;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;

import javax.persistence.Table;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_Z_SEC_FUNC_LOCK_USER")
@SuppressWarnings("serial")
public class SecFunc<PERSON>ock<PERSON>ser extends UserPersistenceObject
{
	@EmbeddedId
	private SecFuncLockUserPK pk = new SecFuncLockUserPK();

	
	@Column(name = "LOCK_STATUS")
	private String lock_status;
	
	@Column(name = "REMARKS")
	private String remarks;

	
	public SecFuncLockUserPK getPk()
	{
		return pk;
	}

	
	public void setPk(SecFuncLockUserPK pk)
	{
		this.pk = pk;
	}

	
	public String getLock_status()
	{
		return lock_status;
	}

	
	public void setLock_status(String lock_status)
	{
		this.lock_status = lock_status;
	}

	
	public String getRemarks()
	{
		return remarks;
	}

	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SecFuncLockUser other = (SecFuncLockUser) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SecFuncLockUser [pk=" + pk + ", lock_status=" + lock_status + ", remarks=" + remarks + "]";
	}

	

}
