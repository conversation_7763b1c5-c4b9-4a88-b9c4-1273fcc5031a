package hk.eduhk.rich.access;

import java.io.Serializable;
import javax.persistence.*;


@Embeddable
@SuppressWarnings("serial")
public class UserRolePK implements Serializable
{
	
	@Column(name = "user_id", length = 30)
	private String userId;
	
	@Column(name = "role_id", length = 20)
	private String roleId;

	
	public String getUserId()
	{
		return userId;
	}

	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}

	
	public String getRoleId()
	{
		return roleId;
	}

	
	public void setRoleId(String roleId)
	{
		this.roleId = roleId;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((roleId == null) ? 0 : roleId.hashCode());
		result = prime * result + ((userId == null) ? 0 : userId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserRolePK other = (UserRolePK) obj;
		if (roleId == null)
		{
			if (other.roleId != null)
				return false;
		}
		else if (!roleId.equals(other.roleId))
			return false;
		if (userId == null)
		{
			if (other.userId != null)
				return false;
		}
		else if (!userId.equals(other.userId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UserRolePK [userId=" + userId + ", roleId=" + roleId + "]";
	}
	
}
