package hk.eduhk.rich.access;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;


@Entity
@Table(name = "RH_Z_USER_ROLE")
@SuppressWarnings("serial")
public class UserRole extends UserPersistenceObject
{
	
	@EmbeddedId
	private UserRolePK pk = null;
	
	@Transient
	private Role role = null;
		
	@Transient
	private StaffIdentity staff = null;
	
	public UserRolePK getPk()
	{
		if (pk == null) pk = new UserRolePK();
		return pk;
	}

	
	public void setPk(UserRolePK pk)
	{
		this.pk = pk;
	}

	
	public String getUserId()
	{
		return getPk().getUserId();
	}
	
	
	public void setUserId(String userId)
	{
		getPk().setUserId(userId);
	}

	
	public String getRoleId()
	{
		return getPk().getRoleId();
	}
	
	
	public void setRoleId(String userId)
	{
		getPk().setRoleId(userId);
		
		// Remove the related instance variable
		this.role = null;
	}
	
	
	public Role getRole()
	{
		if (role == null && getRoleId() != null)
		{
			role = AccessControl.getInstance().getRole(getRoleId());
		}
		
		return role;
	}
		

	
	public StaffIdentity getStaff()
	{
		if (staff == null && getUserId() != null)
		{
			StaffDAO dao = StaffDAO.getInstance();
			staff = dao.getStaffDetailsByUserId(getUserId());
		}
		return staff;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserRole other = (UserRole) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UserRole [pk=" + pk + "]";
	}
	
}
