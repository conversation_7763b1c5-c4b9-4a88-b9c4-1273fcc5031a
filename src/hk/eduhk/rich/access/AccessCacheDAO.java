package hk.eduhk.rich.access;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.validator.GenericValidator;
import org.ehcache.Cache;

import hk.eduhk.rich.cache.AppCache;


@SuppressWarnings("serial")
public class AccessCacheDAO extends AccessDAO
{

	private static AccessCacheDAO instance = null;

	private transient Cache<String, Set> userRoleCache;
	
	private static final Logger logger = Logger.getLogger(AccessCacheDAO.class.getName());
	
	// logging
	private static final Level LVL_CACHE_HIT 	= Level.FINEST;
	private static final Level LVL_QUERY_DB 	= Level.FINER;

	
	public static synchronized AccessCacheDAO getInstance()
	{
		if (instance == null) instance = new AccessCacheDAO();
		return instance;
	}

	
	private Cache<String, Set> getUserRoleCache()
	{
		if (userRoleCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			userRoleCache = appCache.getCache(AppCache.CACHE_USER_ROLE, String.class, Set.class);
		}
		
		return userRoleCache;
	}
	

	@Override
	public Set<String> getRoleIdSetByUserId(String userId)
	{
		Set<String> objSet = null;
		
		if (!GenericValidator.isBlankOrNull(userId))
		{
			objSet = (getUserRoleCache() != null) ? getUserRoleCache().get(userId) : null;
			
			// Retrieve from database if could not find in cache
			if (objSet == null)
			{
				objSet = super.getRoleIdSetByUserId(userId);
				if (getUserRoleCache() != null && objSet != null) getUserRoleCache().put(userId, objSet);
			}
			else
			{
				if (logger.isLoggable(Level.FINEST)) logger.log(Level.FINEST, "Cache hit for userId=" + userId);
			}
		}
		
		return (objSet != null) ? objSet : Collections.EMPTY_SET;
	}
	
	
	@Override
	public void updateUserRoles(String userId, Collection<Role> roleCol, String modifier) throws Exception
	{
		super.updateUserRoles(userId, roleCol, modifier);
		
		if (getUserRoleCache() != null)
		{
			getUserRoleCache().remove(userId);
		}
	}
		
}
