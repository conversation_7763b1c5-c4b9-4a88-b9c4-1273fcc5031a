package hk.eduhk.rich.access;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class ImpersonationView extends BaseView 
{
	
	private String redirectPage = null; 
	private String impersonateUserId; 
	
	
	public ImpersonationView()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		redirectPage = eCtx.getRequestContextPath() + "/user/dashboard.xhtml";
	}

	
	public String getImpersonateUserId()
	{
		return impersonateUserId;
	}

	
	public void setImpersonateUserId(String impersonateUserId)
	{
		this.impersonateUserId = StringUtils.lowerCase(impersonateUserId);
	}
	
	
	public boolean isImpersonationActive()
	{
		String imUserId = UserSessionView.getCurrentInstance().getImpersonateUserId();
		String userId = getLoginUserId();
		return (imUserId != null && !StringUtils.equals(imUserId, userId));
	}
	
	
	private void setSessionImpersonateUserId(String userId)
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			Map<String, Object> sessionMap = fCtx.getExternalContext().getSessionMap();
			sessionMap.put(Constant.ATTR_IMPERSONATE_USER_ID, userId);
		}
	}
	
	
	public List<String> completeUserId(String query)
	{
		//FormDAO dao = FormDAO.getInstance();
		//return dao.getAutoCompleteFormUserId(query + "%", 10);
		
		return Collections.EMPTY_LIST;
	}
	
	
	public void activateImpersonation() throws IOException
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (!GenericValidator.isBlankOrNull(getImpersonateUserId()))
		{
			String loginUserId = UserSessionView.getCurrentInstance().getLoginUserId();
			
			if (!StringUtils.equals(impersonateUserId, loginUserId))
			{
				setSessionImpersonateUserId(impersonateUserId);
				
				// Redirect to dashboard
				FacesContext.getCurrentInstance().getExternalContext().redirect(redirectPage);
			}
			else
			{
				String message = "You are currently login as " + loginUserId;
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		else
		{
			String msg = MessageFormat.format(getResourceBundle().getString("msg.err.require.x"), getResourceBundle().getString("user"));
			fCtx.addMessage("form:userId", new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			fCtx.getPartialViewContext().getRenderIds().add("form:userIdMsg");
		}
	}
	
	
	public void deactivateImpersonation() throws IOException
	{
		setImpersonateUserId(null);
		
		// Remote the impersonate user ID
		FacesContext fCtx = FacesContext.getCurrentInstance();
		Map<String, Object> sessionMap = fCtx.getExternalContext().getSessionMap();
		sessionMap.remove(Constant.ATTR_IMPERSONATE_USER_ID);
						
		// Redirect to dashboard
		fCtx.getExternalContext().redirect(redirectPage);
	}

	
}
