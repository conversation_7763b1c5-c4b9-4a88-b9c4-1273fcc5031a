package hk.eduhk.rich.access;

import java.text.MessageFormat;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.component.*;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;
import javax.naming.NamingException;

import hk.eduhk.rich.BaseFacesValidator;


@FacesValidator("hk.eduhk.rich.access.LDAPUserValidator")
public class LDAPUserValidator extends BaseFacesValidator
{
		
	private static Logger logger = Logger.getLogger(LDAPUserValidator.class.getName());
	
	
	public LDAPUserValidator()
	{
		super();
	}
	
	
	public void validateUserId(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		if (obj != null)
		{
			try
			{
				String userId = (String) obj;
				if (userId != null)
				{
					LDAPService service = LDAPService.getInstance();
					LDAPUser ldapUser = service.getLDAPUserByUsername(userId);
					if (ldapUser == null) throw new IllegalArgumentException();
				}
			}
			catch (IllegalArgumentException iae)
			{
				String msg = MessageFormat.format(userSessionView.getResourceBundle().getString("msg.err.not.exist"), 
												  userSessionView.getResourceBundle().getString("user"));
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
			catch (NamingException ne)
			{
				String msg = "Cannot connect to LDAP";
				logger.log(Level.WARNING, msg, ne);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
			catch (Exception e)
			{
				// Not throwing ValidatorException, for other exception
				// Probably cannot connect to LDAP
				logger.log(Level.WARNING, "", e);
			}
		}
	}
	
}
