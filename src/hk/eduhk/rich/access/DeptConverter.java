package hk.eduhk.rich.access;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.FacesConverter;

import org.omnifaces.util.selectitems.SelectItemsUtils;

import hk.eduhk.rich.BaseConverter;
import hk.eduhk.rich.entity.staff.PureDept;


@FacesConverter("hk.eduhk.rich.access.DeptConverter")
public class DeptConverter extends BaseConverter
{
	
	@Override
	public Object getAsObject(FacesContext fCtx, UIComponent component, String value) 
	{
		// Find the selected Role object by roleId from <f:selectItems> element
		return SelectItemsUtils.findValueByStringConversion(fCtx, component, value, this);
	}
	
	
	@Override
	public String getAsString(FacesContext fCtx, UIComponent component, Object value) 
	{
		return (value != null && value instanceof PureDept) ? ((PureDept) value).getDepartment_code() : null;
	}	
	
}
