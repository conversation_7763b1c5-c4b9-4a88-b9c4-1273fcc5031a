package hk.eduhk.rich.access;

import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.naming.NamingException;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.importRI.ImportRICA;
import hk.eduhk.rich.entity.staff.PureDept;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.entity.staff.SecDataUser_PK;
import hk.eduhk.rich.entity.staff.StaffProfileDisplay;
import hk.eduhk.rich.util.JPAUtils;


@SuppressWarnings("serial")
public class AccessDAO extends BaseDAO
{
	
	private static AccessDAO instance = null;

	private static final Level LVL_EXEC_TIME 	= Level.FINEST;
	private static final Level LVL_QUERY 		= Level.FINEST;

	
	public static synchronized AccessDAO getInstance()
	{
		if (instance == null) instance = new AccessDAO();
		return instance;
	}
	
	
	public static AccessDAO getCacheInstance()
	{
		return AccessCacheDAO.getInstance();
	}
	
	
	public List<String> getUserIdList()
	{
		List<String> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT DISTINCT obj.pk.userId " +
						   "FROM UserRole obj " +
						   "ORDER BY obj.pk.userId ";
			
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public SecDataUser getSecDataUser(SecDataUser_PK pk)
	{
		List<SecDataUser> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj FROM SecDataUser obj " +
						   "WHERE obj.pk = :pk ";
			
			em = getEntityManager();
			TypedQuery<SecDataUser> q = em.createQuery(query, SecDataUser.class);
			q.setParameter("pk", pk);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
				
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	public void deleteSecDataUser(String data_type) throws Exception
	{
		if (data_type != "")
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM SecDataUser obj WHERE obj.pk.data_type = :data_type ");
				q.setParameter("data_type", data_type);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete SecDataUser (data_type=" + data_type + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteUserRole(String roleId) throws Exception
	{
		if (roleId != "")
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM UserRole obj WHERE obj.pk.roleId = :roleId ");
				q.setParameter("roleId", roleId);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete UserRole (roleId=" + roleId + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public SecDataUser updateSecDataUser(SecDataUser obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public UserRole updateUserRole(UserRole obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public UserRole getUserRoleByUserId(String userId, String roleId)
	{
		List<UserRole> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj FROM UserRole obj " +
						   "WHERE obj.pk.userId = :userId AND obj.pk.roleId = :roleId";
			
			em = getEntityManager();
			TypedQuery<UserRole> q = em.createQuery(query, UserRole.class);
			q.setParameter("userId", userId);
			q.setParameter("roleId", roleId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
				
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	public List<UserRole> getUserRoleListByUserId(String userId)
	{
		List<UserRole> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj FROM UserRole obj " +
						   "WHERE obj.pk.userId = :userId ";
			
			em = getEntityManager();
			TypedQuery<UserRole> q = em.createQuery(query, UserRole.class);
			q.setParameter("userId", userId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
				
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<PureDept> getDeptList()
	{
		List<PureDept> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM PureDept obj ORDER BY obj.department_name";
		try
		{
			em = getEntityManager();
			TypedQuery<PureDept> q = em.createQuery(query, PureDept.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<SecDataUser> getDataCodeByUserId(String userId, String dataType) {
		List<SecDataUser> objList = null;
		
		if (userId != null)
		{
			EntityManager em = null;		
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM SecDataUser obj "
						+ " WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";		
				TypedQuery<SecDataUser> q = em.createQuery(query, SecDataUser.class);
				q.setParameter("userId", userId);
				q.setParameter("dataType", dataType);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<SecDataUser> getSecDataUserList(String dataType) {
		List<SecDataUser> objList = null;
		
		if (dataType != null)
		{
			EntityManager em = null;		
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM SecDataUser obj "
						+ " WHERE obj.pk.data_type = :dataType ORDER BY obj.pk.user_id ";		
				TypedQuery<SecDataUser> q = em.createQuery(query, SecDataUser.class);
				q.setParameter("dataType", dataType);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public Set<String> getRoleIdSetByUserId(String userId)
	{
		Set<String> objSet = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj.pk.roleId " +
						   "FROM UserRole obj " +
						   "WHERE obj.pk.userId = :userId ";
			
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			q.setParameter("userId", userId);
			List<String> objList = q.getResultList();
			objSet = (objList != null && objList.size() > 0) ? new HashSet<String>(objList) : Collections.EMPTY_SET;
		}
		finally
		{
			pm.close(em);
		}
				
		return (objSet != null) ? objSet : Collections.EMPTY_SET;
	}
	

	public void updateUserRoles(String userId, Collection<Role> roleCol, String modifier) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(roleCol))
				{
					// Extract roleId List from Collection<Role>
					List<String> roleIdList = roleCol.stream().map(Role::getRoleId).collect(Collectors.toList());
					
					// Query the existing UserRoles of the user
					String selectQuery = "SELECT obj.pk.roleId FROM UserRole obj " + 
							   			 "WHERE obj.pk.userId = :userId ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					List<String> dbRoleIdList = tq.getResultList();
					
					// Remove UserRoles from database
					Collection<String> removeRoleIdCol = CollectionUtils.removeAll(dbRoleIdList, roleIdList);
					if (CollectionUtils.isNotEmpty(removeRoleIdCol))
					{
						Query q = em.createQuery("DELETE FROM UserRole obj " +
												 "WHERE obj.pk.userId = :userId " +
												 "AND obj.pk.roleId IN :roleIdList ");
						
						q.setParameter("userId", userId);
						q.setParameter("roleIdList", new ArrayList<String>(removeRoleIdCol));
						q.executeUpdate();
					}
					
					// Perist UserRole which are not in database
					for (Role role : roleCol)
					{
						if (!dbRoleIdList.contains(role.getRoleId()))
						{
							UserRole obj = new UserRole();
							obj.setUserId(userId);
							obj.setRoleId(role.getRoleId());
							obj.setUserstamp(modifier);
							em.persist(obj);
						}
					}
				}
				
				// Remove all UserRoles from the user
				else
				{
					Query q = em.createQuery("DELETE FROM UserRole obj WHERE obj.pk.userId = :userId ");
					q.setParameter("userId", userId);
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public SecFuncLock getSecFuncLock(String lock_code, String func_id, String lock_grp)
	{
		List<SecFuncLock> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM SecFuncLock obj WHERE obj.pk.lock_code = :lock_code AND obj.pk.func_id = :func_id AND obj.pk.lock_grp = :lock_grp ";

		try
		{
			em = getEntityManager();
			TypedQuery<SecFuncLock> q = em.createQuery(query, SecFuncLock.class);
			q.setParameter("lock_code", lock_code);
			q.setParameter("func_id", func_id);
			q.setParameter("lock_grp", lock_grp);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null;
	}
	
	public SecFuncLock updateSecFuncLock(SecFuncLock obj) {
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public SecFuncLockUser getSecFuncLockUser(String lockCode, String funcId, String userId)
	{
		List<SecFuncLockUser> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM SecFuncLockUser obj WHERE obj.pk.lock_code = :lockCode AND obj.pk.func_id = :funcId AND obj.pk.user_id = :userId";
		try
		{
			em = getEntityManager();
			TypedQuery<SecFuncLockUser> q = em.createQuery(query, SecFuncLockUser.class);
			q.setParameter("lockCode", lockCode);
			q.setParameter("funcId", funcId);
			q.setParameter("userId", userId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null;
	}
	
	public List<SecFuncLockUser> getSecFuncLockUserList(String lockCode, String funcId)
	{
		List<SecFuncLockUser> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM SecFuncLockUser obj WHERE obj.pk.lock_code = :lockCode AND obj.pk.func_id = :funcId ORDER BY obj.pk.user_id";
		try
		{
			em = getEntityManager();
			TypedQuery<SecFuncLockUser> q = em.createQuery(query, SecFuncLockUser.class);
			q.setParameter("lockCode", lockCode);
			q.setParameter("funcId", funcId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public void deleteSecFuncLockUser(SecFuncLockUser obj)
	{
		if (obj != null) {
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				obj = em.find(SecFuncLockUser.class, obj.getPk());
				em.remove(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}	
	
	public SecFuncLockUser updateSecFuncLockUser(SecFuncLockUser obj) {
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void updateUserDepts(String userId, Collection<PureDept> deptCol, String modifier, Boolean isSelectedAll) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(deptCol))
				{
					// Extract deptId List from Collection<PureDept>
					List<String> deptIdList = deptCol.stream().map(PureDept::getDepartment_code).collect(Collectors.toList());
					
					// Query the existing Depts of the user
					String selectQuery = "SELECT obj.pk.data_code FROM SecDataUser obj " + 
							   			 "WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					tq.setParameter("dataType", "D");
					List<String> dbDeptIdList = tq.getResultList();
					
					if (isSelectedAll) {
						Query q = em.createQuery("DELETE FROM SecDataUser obj " +
											 "WHERE obj.pk.user_id = :userId " +
											 " AND obj.pk.data_type = :dataType ");
		
						q.setParameter("userId", userId);
						q.setParameter("dataType", "D");
						q.executeUpdate();
						
						SecDataUser obj = new SecDataUser();
						obj.setUser_id(userId);
						obj.getPk().setData_type("D");
						obj.setData_code("ALL_DATA");
						obj.setUserstamp(modifier);
						em.persist(obj);
					}else {
						// Remove Depts from database
						Collection<String> removeDeptIdCol = CollectionUtils.removeAll(dbDeptIdList, deptIdList);
						if (CollectionUtils.isNotEmpty(removeDeptIdCol))
						{
							Query q = em.createQuery("DELETE FROM SecDataUser obj " +
													 "WHERE obj.pk.user_id = :userId " +
													 " AND obj.pk.data_type = :dataType " +
													 "AND obj.pk.data_code IN :deptIdList ");
							
							q.setParameter("userId", userId);
							q.setParameter("dataType", "D");
							q.setParameter("deptIdList", new ArrayList<String>(removeDeptIdCol));
							q.executeUpdate();
						}
						
						// Perist Depts which are not in database
						for (PureDept dept : deptCol)
						{
							if (!dbDeptIdList.contains(dept.getDepartment_code()))
							{
								SecDataUser obj = new SecDataUser();
								obj.setUser_id(userId);
								obj.getPk().setData_type("D");
								obj.setData_code(dept.getDepartment_code());
								obj.setUserstamp(modifier);
								em.persist(obj);
							}
						}
					}
				}
				
				// Remove all Depts from the user
				else
				{
					Query q = em.createQuery("DELETE FROM SecDataUser obj WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ");
					q.setParameter("userId", userId);
					q.setParameter("dataType", "D");
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void updateUserCas(String userId, Collection<ImportRICA> caCol, String modifier) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(caCol))
				{
					// Extract caId List from Collection<ImportRICA>
					List<String> caIdList = caCol.stream().map(ImportRICA::getArea_code).collect(Collectors.toList());
					
					// Query the existing Cas of the user
					String selectQuery = "SELECT obj.pk.data_code FROM SecDataUser obj " + 
							   			 "WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					tq.setParameter("dataType", "CA");
					List<String> dbCaIdList = tq.getResultList();
					
					// Remove Cas from database
					Collection<String> removeCaIdCol = CollectionUtils.removeAll(dbCaIdList, caIdList);
					if (CollectionUtils.isNotEmpty(removeCaIdCol))
					{
						Query q = em.createQuery("DELETE FROM SecDataUser obj " +
												 "WHERE obj.pk.user_id = :userId " +
												 " AND obj.pk.data_type = :dataType " +
												 "AND obj.pk.data_code IN :caIdList ");
						
						q.setParameter("userId", userId);
						q.setParameter("dataType", "CA");
						q.setParameter("caIdList", new ArrayList<String>(removeCaIdCol));
						q.executeUpdate();
					}
					
					// Perist Cas which are not in database
					for (ImportRICA ca : caCol)
					{
						if (!dbCaIdList.contains(ca.getArea_code()))
						{
							SecDataUser obj = new SecDataUser();
							obj.setUser_id(userId);
							obj.getPk().setData_type("CA");
							obj.setData_code(ca.getArea_code());
							obj.setUserstamp(modifier);
							em.persist(obj);
						}
					}
					
				}
				
				// Remove all Cas from the user
				else
				{
					Query q = em.createQuery("DELETE FROM SecDataUser obj WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ");
					q.setParameter("userId", userId);
					q.setParameter("dataType", "CA");
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void updateUserKtds(String userId, Collection<String> ktdCol, String modifier, Boolean isSelectedAll) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(ktdCol))
				{
					// Query the existing Ktds of the user
					String selectQuery = "SELECT obj.pk.data_code FROM SecDataUser obj " + 
							   			 "WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					tq.setParameter("dataType", "KTD");
					List<String> dbKtdIdList = tq.getResultList();
					if (isSelectedAll) {
						Query q = em.createQuery("DELETE FROM SecDataUser obj " +
											 "WHERE obj.pk.user_id = :userId " +
											 " AND obj.pk.data_type = :dataType ");
		
						q.setParameter("userId", userId);
						q.setParameter("dataType", "KTD");
						q.executeUpdate();
						
						SecDataUser obj = new SecDataUser();
						obj.setUser_id(userId);
						obj.getPk().setData_type("KTD");
						obj.setData_code("ALL_DATA");
						obj.setUserstamp(modifier);
						em.persist(obj);
					}else {
						// Remove Ktds from database
						Collection<String> removeKtdIdCol = CollectionUtils.removeAll(dbKtdIdList, ktdCol);
						if (CollectionUtils.isNotEmpty(removeKtdIdCol))
						{
							Query q = em.createQuery("DELETE FROM SecDataUser obj " +
													 "WHERE obj.pk.user_id = :userId " +
													 " AND obj.pk.data_type = :dataType " +
													 "AND obj.pk.data_code IN :ktdIdList ");
							
							q.setParameter("userId", userId);
							q.setParameter("dataType", "KTD");
							q.setParameter("ktdIdList", new ArrayList<String>(removeKtdIdCol));
							q.executeUpdate();
						}
						
						// Perist Ktds which are not in database
						for (String ktd : ktdCol)
						{
							if (!dbKtdIdList.contains(ktd))
							{
								SecDataUser obj = new SecDataUser();
								obj.setUser_id(userId);
								obj.getPk().setData_type("KTD");
								obj.setData_code(ktd);
								obj.setUserstamp(modifier);
								em.persist(obj);
							}
						}
					}
				}
				
				// Remove all Ktds from the user
				else
				{
					Query q = em.createQuery("DELETE FROM SecDataUser obj WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ");
					q.setParameter("userId", userId);
					q.setParameter("dataType", "KTD");
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void updateUserRpts(String userId, Collection<String> rptCol, String modifier, Boolean isSelectedAll) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(rptCol))
				{
					// Query the existing Rpts of the user
					String selectQuery = "SELECT obj.pk.data_code FROM SecDataUser obj " + 
							   			 "WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					tq.setParameter("dataType", "RPT");
					List<String> dbRptIdList = tq.getResultList();
					if (isSelectedAll) {
						Query q = em.createQuery("DELETE FROM SecDataUser obj " +
											 "WHERE obj.pk.user_id = :userId " +
											 " AND obj.pk.data_type = :dataType ");
		
						q.setParameter("userId", userId);
						q.setParameter("dataType", "RPT");
						q.executeUpdate();
						
						SecDataUser obj = new SecDataUser();
						obj.setUser_id(userId);
						obj.getPk().setData_type("RPT");
						obj.setData_code("ALL_DATA");
						obj.setUserstamp(modifier);
						em.persist(obj);
					}else {
						// Remove Rpts from database
						Collection<String> removeRptIdCol = CollectionUtils.removeAll(dbRptIdList, rptCol);
						if (CollectionUtils.isNotEmpty(removeRptIdCol))
						{
							Query q = em.createQuery("DELETE FROM SecDataUser obj " +
													 "WHERE obj.pk.user_id = :userId " +
													 " AND obj.pk.data_type = :dataType " +
													 "AND obj.pk.data_code IN :rptIdList ");
							
							q.setParameter("userId", userId);
							q.setParameter("dataType", "RPT");
							q.setParameter("rptIdList", new ArrayList<String>(removeRptIdCol));
							q.executeUpdate();
						}
						
						// Perist Rpts which are not in database
						for (String rpt : rptCol)
						{
							if (!dbRptIdList.contains(rpt))
							{
								SecDataUser obj = new SecDataUser();
								obj.setUser_id(userId);
								obj.getPk().setData_type("RPT");
								obj.setData_code(rpt);
								obj.setUserstamp(modifier);
								em.persist(obj);
							}
						}
					}
				}
				
				// Remove all Rpts from the user
				else
				{
					Query q = em.createQuery("DELETE FROM SecDataUser obj WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ");
					q.setParameter("userId", userId);
					q.setParameter("dataType", "RPT");
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	
	public void updateUserUoa(String userId, Collection<String> uoaCol, String modifier, Boolean isSelectedAll) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(uoaCol))
				{
					// Query the existing Rpts of the user
					String selectQuery = "SELECT obj.pk.data_code FROM SecDataUser obj " + 
							   			 "WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					tq.setParameter("dataType", "UOA");
					List<String> dbUoaIdList = tq.getResultList();
					if (isSelectedAll) {
						Query q = em.createQuery("DELETE FROM SecDataUser obj " +
											 "WHERE obj.pk.user_id = :userId " +
											 " AND obj.pk.data_type = :dataType ");
		
						q.setParameter("userId", userId);
						q.setParameter("dataType", "UOA");
						q.executeUpdate();
						
						SecDataUser obj = new SecDataUser();
						obj.setUser_id(userId);
						obj.getPk().setData_type("UOA");
						obj.setData_code("ALL_DATA");
						obj.setUserstamp(modifier);
						em.persist(obj);
					}else {
						// Remove Rpts from database
						Collection<String> removeUoaIdCol = CollectionUtils.removeAll(dbUoaIdList, uoaCol);
						if (CollectionUtils.isNotEmpty(removeUoaIdCol))
						{
							Query q = em.createQuery("DELETE FROM SecDataUser obj " +
													 "WHERE obj.pk.user_id = :userId " +
													 " AND obj.pk.data_type = :dataType " +
													 "AND obj.pk.data_code IN :uoaIdList ");
							
							q.setParameter("userId", userId);
							q.setParameter("dataType", "UOA");
							q.setParameter("uoaIdList", new ArrayList<String>(removeUoaIdCol));
							q.executeUpdate();
						}
						
						// Perist Rpts which are not in database
						for (String uoa : uoaCol)
						{
							if (!dbUoaIdList.contains(uoa))
							{
								SecDataUser obj = new SecDataUser();
								obj.setUser_id(userId);
								obj.getPk().setData_type("UOA");
								obj.setData_code(uoa);
								obj.setUserstamp(modifier);
								em.persist(obj);
							}
						}
					}
				}
				
				// Remove all UOA from the user
				else
				{
					Query q = em.createQuery("DELETE FROM SecDataUser obj WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ");
					q.setParameter("userId", userId);
					q.setParameter("dataType", "UOA");
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	
	
	public List<UserRole> getUserInfoList()
	{
		List<UserRole> objList = null;
		EntityManager em = null;
		
		try
		{
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT obj FROM UserRole obj " +
					   	"ORDER BY obj.pk.roleId, obj.pk.userId ");
			
			// Query execution
			em = getEntityManager();
			TypedQuery<UserRole> q = em.createQuery(buf.toString(), UserRole.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		return objList;
	}	
}
	