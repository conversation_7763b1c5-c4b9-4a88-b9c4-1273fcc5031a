package hk.eduhk.rich.access;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.EmptyFileException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.model.file.UploadedFile;

import hk.eduhk.rich.util.MimeMap;
import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.importRI.ImportRICA;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.rae.RaeUOA;
import hk.eduhk.rich.entity.rae.RaeUOADAO;
import hk.eduhk.rich.entity.staff.PureDept;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.entity.staff.SecDataUser_PK;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class DataAccessView extends BaseView
{

	private List<String> userIdList = null;
	private List<PureDept> deptList = null;
	private List<ImportRICA> caList = null;
	private List<LookupValue> ktdList = null;
	private List<LookupValue> rptList = null;
	private List<RaeUOA> uoaList = null;
	
	private List <SecDataUser> secDataUserList = null;
	
	private String selectedUserId = null;	
	private Set<PureDept> selectedDeptSet = null;
	private Set<ImportRICA> selectedCaSet = null;
	private Set<String> selectedKtdSet = null;
	private Set<String> selectedRptSet = null;
	private Set<String>	selectedUoaSet = null;
	
	private UploadedFile file;
	
	private AccessDAO getAccessDAO()
	{
		return AccessCacheDAO.getInstance();
	}

	
	public List<String> getUserIdList()
	{
		if (userIdList == null)
		{
			// Only allow internal users
			AccessDAO dao = AccessDAO.getInstance();
			userIdList = dao.getUserIdList();
		}
		
		return userIdList;
	}
	
	public List<SecDataUser> getSecDataUserList()
	{
		if (secDataUserList == null) {
			AccessDAO dao = AccessDAO.getInstance();
			secDataUserList = dao.getSecDataUserList("D");
		}
		return secDataUserList;
	}


	
	public void setSecDataUserList(List<SecDataUser> secDataUserList)
	{
		this.secDataUserList = secDataUserList;
	}


	public String getSelectedUserId()
	{
		return selectedUserId;
	}

	
	public void setSelectedUserId(String selectedUserId)
	{
		this.selectedUserId = selectedUserId;
		
		if (!GenericValidator.isBlankOrNull(selectedUserId))
		{
			// Dept
			List<SecDataUser> userDeptList = getAccessDAO().getDataCodeByUserId(selectedUserId, "D");
			List<SecDataUser> allData = userDeptList.stream()
					.filter(y -> y.getPk().getData_code().equals("ALL_DATA"))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(allData)) {
				selectedDeptSet = (CollectionUtils.isNotEmpty(userDeptList)) 
									? userDeptList.stream().map(SecDataUser::getDept).collect(Collectors.toSet())
									: new HashSet<PureDept>(getDeptList().size());
			}else {
				selectedDeptSet = new HashSet<PureDept>(getDeptList().size());
				selectAll();
			}
			
			// Ca
			List<SecDataUser> userCaList = getAccessDAO().getDataCodeByUserId(selectedUserId, "CA");
			selectedCaSet = (CollectionUtils.isNotEmpty(userCaList)) 
								? userCaList.stream().map(SecDataUser::getCa).collect(Collectors.toSet())
								: new HashSet<ImportRICA>(getCaList().size());
			
			// Ktd				
			List<SecDataUser> userKtdList = getAccessDAO().getDataCodeByUserId(selectedUserId, "KTD");
			List<SecDataUser> allKtdData = userKtdList.stream()
					.filter(y -> y.getPk().getData_code().equals("ALL_DATA"))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(allKtdData)) {
				selectedKtdSet = (CollectionUtils.isNotEmpty(userKtdList)) 
									? userKtdList.stream().map(SecDataUser::getData_code).collect(Collectors.toSet())
									: new HashSet<String>(getKtdList().size());
			}else {
				selectedKtdSet = new HashSet<String>(getKtdList().size());
				selectAllKtd();
			}
			
			// Rpt		
			
			List<SecDataUser> userRptList = getAccessDAO().getDataCodeByUserId(selectedUserId, "RPT");
			List<SecDataUser> allRptData = userRptList.stream()
					.filter(y -> y.getPk().getData_code().equals("ALL_DATA"))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(allRptData)) {
				selectedRptSet = (CollectionUtils.isNotEmpty(userRptList)) 
									? userRptList.stream().map(SecDataUser::getData_code).collect(Collectors.toSet())
									: new HashSet<String>(getKtdList().size());
			}else {
				selectedRptSet = new HashSet<String>(getRptList().size());
				selectAllRpt();
			}
			
			
			//UOA	
			List<SecDataUser> userUoaList = getAccessDAO().getDataCodeByUserId(selectedUserId, "UOA");
			List<SecDataUser> allUoaData = userRptList.stream()
					.filter(y -> y.getPk().getData_code().equals("ALL_DATA"))
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(allUoaData)) {
				selectedUoaSet = (CollectionUtils.isNotEmpty(userUoaList)) 
									? userUoaList.stream().map(SecDataUser::getData_code).collect(Collectors.toSet())
									: new HashSet<String>(getKtdList().size());
			}else {
				selectedUoaSet = new HashSet<String>(getUoaList().size());
				selectAllUoa();
			}
			
		}
		else
		{
			selectedDeptSet = null;
			selectedCaSet = null;
			selectedKtdSet = null;
			selectedRptSet = null;
			selectedUoaSet = null;
		}
	}

	
	public Set<PureDept> getSelectedDepts()
	{
		return selectedDeptSet;
	}

	
	public void setSelectedDepts(Set<PureDept> selectedDeptSet)
	{
		this.selectedDeptSet = selectedDeptSet;
	}
	
	
	
	public Set<ImportRICA> getSelectedCas()
	{
		return selectedCaSet;
	}


	
	public void setSelectedCas(Set<ImportRICA> selectedCaSet)
	{
		this.selectedCaSet = selectedCaSet;
	}

	
	public Set<String> getSelectedKtds()
	{
		if(selectedKtdSet == null) {
			AccessDAO dao = AccessDAO.getInstance();
			List<SecDataUser> secDataUserList = dao.getDataCodeByUserId(selectedUserId, "KTD");
			selectedKtdSet = secDataUserList.stream().
					map(SecDataUser -> SecDataUser.getPk().getData_code()).collect(Collectors.toSet());
		}
		return selectedKtdSet;
	}


	
	public void setSelectedKtds(Set<String> selectedKtdSet)
	{
		this.selectedKtdSet = selectedKtdSet;
	}

	public Set<String> getSelectedRpts()
	{
		if(selectedRptSet == null) {
			AccessDAO dao = AccessDAO.getInstance();
			List<SecDataUser> secDataUserList = dao.getDataCodeByUserId(selectedUserId, "RPT");
			selectedRptSet = secDataUserList.stream().
					map(SecDataUser -> SecDataUser.getPk().getData_code()).collect(Collectors.toSet());
		}
		return selectedRptSet;
	}


	
	public void setSelectedRpts(Set<String> selectedRptSet)
	{
		this.selectedRptSet = selectedRptSet;
	}
	
	
	
	
	public Set<String> getSelectedUoa()
	{
		if(selectedUoaSet == null) {
			AccessDAO dao = AccessDAO.getInstance();
			List<SecDataUser> secDataUserList = dao.getDataCodeByUserId(selectedUserId, "UOA");
			selectedUoaSet = secDataUserList.stream().
					map(SecDataUser -> SecDataUser.getPk().getData_code()).collect(Collectors.toSet());
			
			
		}
		return selectedUoaSet;
	}


	
	public void setSelectedUoa(Set<String> selectedUoaSet)
	{
		this.selectedUoaSet = selectedUoaSet;
	}
	
	
	
	
	
	public List<PureDept> getDeptList()
	{
		if (deptList == null)
		{
			AccessDAO dao = AccessDAO.getInstance();
			deptList = dao.getDeptList();
		}
		return deptList;
	}
	
	
	public List<ImportRICA> getCaList()
	{
		if (caList == null)
		{
			ImportRIDAO dao = ImportRIDAO.getInstance();
			caList = dao.getImportRICAList(null);
		}
		return caList;
	}

	
	
	public List<LookupValue> getKtdList()
	{
		if(ktdList == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			ktdList = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			ktdList.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y"));
		}
		return ktdList;
	}

	public List<LookupValue> getRptList()
	{
		if(rptList == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			rptList = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			rptList.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y"));
		}
		return rptList;
	}
	
	
	
	public List<RaeUOA> getUoaList()
	{
		if(uoaList == null) {
			RaeUOADAO dao = RaeUOADAO.getInstance();
			uoaList = dao.getRaeUOAList();
			
		}
		
		return uoaList;
	}


	public void selectAll()
	{
		if (selectedUserId != null)
		{
			selectedDeptSet.addAll(getDeptList());
		}
	}
	
	
	public void selectAllCa()
	{
		if (selectedUserId != null)
		{
			selectedCaSet.addAll(getCaList());
		}
	}
	
	public void selectAllKtd()
	{
		if (selectedUserId != null)
		{
			List<String> ktdCodeList = getKtdList().stream().
					map(LookupValue -> LookupValue.getPk().getLookup_code()).collect(Collectors.toList());
			selectedKtdSet.addAll(ktdCodeList);
		}
	}
	
	public void selectAllRpt()
	{
		if (selectedUserId != null)
		{
			List<String> rptCodeList = getRptList().stream().
					map(LookupValue -> LookupValue.getPk().getLookup_code()).collect(Collectors.toList());
			selectedRptSet.addAll(rptCodeList);
		}
	}
	
	
	public void selectAllUoa()
	{
		if (selectedUserId != null)
		{
			List<String> uoaCodeList = getUoaList().stream().
					map(RaeUOA -> RaeUOA.getUoaCode()).collect(Collectors.toList());
			selectedUoaSet.addAll(uoaCodeList);
		}
	}
	
	
	
	public void unselectAll()
	{
		if (selectedUserId != null)
		{
			selectedDeptSet.clear();
		}
	}
	
	public void unselectAllCa()
	{
		if (selectedUserId != null)
		{
			selectedCaSet.clear();
		}
	}
	
	public void unselectAllKtd()
	{
		if (selectedUserId != null)
		{
			selectedKtdSet.clear();
		}
	}
	
	public void unselectAllRpt()
	{
		if (selectedUserId != null)
		{
			selectedRptSet.clear();
		}
	}
	
	public void unselectAllUoa()
	{
		if (selectedUserId != null)
		{
			selectedUoaSet.clear();
		}
	}
	
	
	
	public Boolean isSelectedAll()
	{
		if (CollectionUtils.isNotEmpty(selectedDeptSet)) {
			if (selectedDeptSet.size() == getDeptList().size()) {
				return true;
			}
		}
		return false;
	}
	
	public Boolean isSelectedAllKtd()
	{
		if (CollectionUtils.isNotEmpty(selectedKtdSet)) {
			if (selectedKtdSet.size() == getKtdList().size()) {
				return true;
			}
		}
		return false;
	}
	
	public Boolean isSelectedAllRpt()
	{
		if (CollectionUtils.isNotEmpty(selectedRptSet)) {
			if (selectedRptSet.size() == getRptList().size()) {
				return true;
			}
		}
		return false;
	}
	
	
	public Boolean isSelectedAllUoa()
	{
		if (CollectionUtils.isNotEmpty(selectedUoaSet)) {
			if (selectedUoaSet.size() == getRptList().size()) {
				return true;
			}
		}
		return false;
	}
	
	
	public String updateUserDepts()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (selectedUserId != null)
		{
			try
			{
				//getReomoteUser() will get impersonate userId
				//String creator = fCtx.getExternalContext().getRemoteUser();
				String creator = getLoginUserId();
				getAccessDAO().updateUserDepts(getSelectedUserId(), selectedDeptSet, creator, isSelectedAll());
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.update.x"), getSelectedUserId());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				// User may be removed. Reload the user list
				userIdList = null;
				if (!getUserIdList().contains(getSelectedUserId()))
				{
					setSelectedUserId(null);
				}
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		String destUrl = redirect("dataAccess");
		return redirect(destUrl);
	}
	
	public String updateUserCas()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (selectedUserId != null)
		{
			try
			{
				//getReomoteUser() will get impersonate userId
				//String creator = fCtx.getExternalContext().getRemoteUser();
				String creator = getLoginUserId();
				getAccessDAO().updateUserCas(getSelectedUserId(), selectedCaSet, creator);
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.update.x"), getSelectedUserId());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				// User may be removed. Reload the user list
				userIdList = null;
				if (!getUserIdList().contains(getSelectedUserId()))
				{
					setSelectedUserId(null);
				}
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		String destUrl = redirect("dataAccess");
		return redirect(destUrl);
	}
	
	public String updateUserKtds()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (selectedUserId != null)
		{
			try
			{
				//getReomoteUser() will get impersonate userId
				//String creator = fCtx.getExternalContext().getRemoteUser();
				String creator = getLoginUserId();
				getAccessDAO().updateUserKtds(getSelectedUserId(), selectedKtdSet, creator, isSelectedAllKtd());
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.update.x"), getSelectedUserId());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				// User may be removed. Reload the user list
				userIdList = null;
				if (!getUserIdList().contains(getSelectedUserId()))
				{
					setSelectedUserId(null);
				}
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		String destUrl = redirect("dataAccess");
		return redirect(destUrl);
	}
	
	public String updateUserRpts()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (selectedUserId != null)
		{
			try
			{
				//getReomoteUser() will get impersonate userId
				//String creator = fCtx.getExternalContext().getRemoteUser();
				String creator = getLoginUserId();
				getAccessDAO().updateUserRpts(getSelectedUserId(), selectedRptSet, creator, isSelectedAllRpt());
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.update.x"), getSelectedUserId());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				// User may be removed. Reload the user list
				userIdList = null;
				if (!getUserIdList().contains(getSelectedUserId()))
				{
					setSelectedUserId(null);
				}
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		String destUrl = redirect("dataAccess");
		return redirect(destUrl);
	}
	
	
	public String updateUserUoa()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (selectedUserId != null)
		{
			try
			{
				//getReomoteUser() will get impersonate userId
				//String creator = fCtx.getExternalContext().getRemoteUser();
				String creator = getLoginUserId();
				getAccessDAO().updateUserUoa(getSelectedUserId(), selectedUoaSet, creator, isSelectedAllUoa());
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.update.x"), getSelectedUserId());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				// User may be removed. Reload the user list
				userIdList = null;
				if (!getUserIdList().contains(getSelectedUserId()))
				{
					setSelectedUserId(null);
				}
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		String destUrl = redirect("dataAccess");
		return redirect(destUrl);
	}
	
	
	
	
	
	public void download()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (secDataUserList != null){
			Workbook wb = null;			
			long t = System.currentTimeMillis();
			try
			{
				int rowCount = secDataUserList.size();
		    	// Create the Excel Workbook
		    	// Use SXSSF model if the batch is large in size 
		    	// to reduce the memory footprint consumed by POI api
		    	if (rowCount > 5000)
		    	{
			    	SXSSFWorkbook swb = new SXSSFWorkbook(1000);
			    	swb.setCompressTempFiles(true);
			    	wb = swb;
		    	}
		    	
		    	// Still use standard XSSF model for small batch,
		    	// as it supports autoSizeColumn
		    	else wb = new XSSFWorkbook();
		    	
		    	createDataSheet(wb, secDataUserList);
		    	//Sheet sheet = wb.createSheet(batch.getBatchKey());
		    	
		    	getLogger().log(Level.INFO, "Time spent on generating excel with " + rowCount + " rows=" + (System.currentTimeMillis() - t) + "ms");
				
			}
			catch (Exception e) 
	    	{
				String message = "Cannot create the target report in excel format.";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				
				getLogger().log(Level.SEVERE, message, e);
				return;
			}
			
			
	    	try 
	    	{
	    		// Get the byte array of the Workbook
		    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
				wb.write(baos);
				
				// Dispose of temporary files backing this workbook on disk
				if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
				
				wb.close();
				byte[] wbBytes = baos.toByteArray();
				
				// Set the response header
				
				  ExternalContext eCtx = fCtx.getExternalContext();
				  eCtx.responseReset();
				  //eCtx.addResponseHeader("Cache-control", "no-cache");
				  //eCtx.addResponseHeader("Pragma", "no-cache");
				  
				  eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
				  eCtx.setResponseHeader("Expires", "-1");
				  eCtx.setResponseHeader("Pragma", "private");
				  String outputFileExt = "xlsx";
				  eCtx.setResponseContentType(MimeMap.getInstance().get(outputFileExt));
				  eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\"dataExport.xlsx\"");
	
				  // Send the bytes to response OutputStream
				  OutputStream os = eCtx.getResponseOutputStream();
				  os.write(wbBytes);
				  fCtx.responseComplete();
			}
			catch (IOException e) 
	    	{
				String message = "Cannot send Workbook bytes to response OutputStream ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				
				getLogger().log(Level.SEVERE, message, e);
				return;
			}
		}
	}
	
	private void createDataSheet(Workbook workbook, List<SecDataUser> resultList)
	{
		int numOfRows = 0;
			
    	Sheet sheet = workbook.createSheet("Data");
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	String[] headerArray = {"User ID","User Name","User Dept.","Access Dept.","Access Dept. Name"};
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		// Create data rows
		for(SecDataUser r:resultList) {
			int i = 0;
			row = sheet.createRow(sheet.getLastRowNum()+1);
			
			cell = row.createCell(i++);
    		cell.setCellValue(r.getUser_id());
    		
    		cell = row.createCell(i++);
    		cell.setCellValue(r.getStaff().getFullname());
    		
    		cell = row.createCell(i++);
    		cell.setCellValue(r.getStaff().getDept_code());
    		
    		cell = row.createCell(i++);
			cell.setCellValue("ALL_DATA".equals(r.getData_code())?"All":r.getData_code());
    		
    		cell = row.createCell(i++);
    		if (r.getDept() != null) {
    			cell.setCellValue(r.getDept().getDepartment_name());
    		}
    		else {
    			cell.setCellValue("");
    		}
    		numOfRows ++;
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	public void upload() throws Exception {
		if (file == null || file.getSize() == 0) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, 
                "Error", "Please select a non-empty file to upload.");
            FacesContext.getCurrentInstance().addMessage(null, message);
            return;
        }
        
        if (!file.getFileName().toLowerCase().endsWith(".xlsx")) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Error", "Only Excel (.xlsx) files are supported.");
            FacesContext.getCurrentInstance().addMessage(null, message);
            return;
        }

        try {
            processExcelUpload(file);
            FacesMessage message = new FacesMessage("Successful", 
                file.getFileName() + " was processed successfully.");
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (EmptyFileException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Error", "The uploaded file is empty.");
            FacesContext.getCurrentInstance().addMessage(null, message);
        } catch (IOException e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Error", "Failed to process the file. Please check the format.");
            FacesContext.getCurrentInstance().addMessage(null, message);
            getLogger().log(Level.SEVERE, "File processing error", e);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR,
                "Error", "An unexpected error occurred: " + e.getMessage());
            FacesContext.getCurrentInstance().addMessage(null, message);
            getLogger().log(Level.SEVERE, "Unexpected error", e);
        }
    }
    
    // Getter and setter for file
    public UploadedFile getFile() {
        return file;
    }
    
    public void setFile(UploadedFile file) {
        this.file = file;
    }
    
    public void processExcelUpload(UploadedFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            
            // Skip header row
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // Read data from Excel
                String userId = getStringCellValue(row.getCell(0)); // userId column
                String roleId = getStringCellValue(row.getCell(1)); // roleId column
                String dataType = getStringCellValue(row.getCell(2)); // dataType column
                String dataCodes = getStringCellValue(row.getCell(3)); // dataCode column (comma-separated)
                
                if (i == 1) {
                	AccessDAO.getInstance().deleteUserRole(roleId);
                	AccessDAO.getInstance().deleteSecDataUser(dataType);
                }
                // Save UserRole
                saveUserRole(userId, roleId);
                
                // Save SecDataUser records for each department
                saveDepartmentAccess(userId, dataType, dataCodes);
            }
        }
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((int) cell.getNumericCellValue());
            default:
                return null;
        }
    }

    private void saveUserRole(String userId, String roleId) {
        // Check if the record already exists
        UserRolePK pk = new UserRolePK();
        pk.setUserId(userId.trim());
        pk.setRoleId(roleId.trim());
        
        UserRole existing = AccessDAO.getInstance().getUserRoleByUserId(userId, roleId);
        if (existing == null) {
            UserRole userRole = new UserRole();
            userRole.setPk(pk);
            AccessDAO.getInstance().updateUserRole(userRole);
        }
    }

    private void saveDepartmentAccess(String userId, String dataType, String dataCodes) {
        if (dataCodes == null || dataCodes.isEmpty()) return;
        
        // Split comma-separated departments and remove duplicates
        Set<String> departments = new HashSet<>(
            Arrays.asList(dataCodes.split("\\s*,\\s*"))
        );
        
        for (String department : departments) {
            if (department.isEmpty()) continue;
            
            SecDataUser_PK pk = new SecDataUser_PK();
            pk.setUser_id(userId.trim());
            pk.setData_type(dataType.trim());
            pk.setData_code(department.trim());
            
            // Check if the record already exists
            SecDataUser existing = AccessDAO.getInstance().getSecDataUser(pk);
            if (existing == null) {
                SecDataUser secDataUser = new SecDataUser();
                secDataUser.setPk(pk);
                AccessDAO.getInstance().updateSecDataUser(secDataUser);
            }
        }
    }
}
