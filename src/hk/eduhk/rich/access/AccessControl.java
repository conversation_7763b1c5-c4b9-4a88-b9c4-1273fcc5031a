package hk.eduhk.rich.access;

import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.xml.bind.*;
import javax.xml.bind.annotation.*;


/**
 * A collection of Function.
 * All available functions in AMIS are put in this class. 
 * Access control is relied on this class.
 * 
 * <AUTHOR>
 */
@XmlRootElement(name = "access-control")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class AccessControl implements Serializable
{
	
	private static AccessControl instance = null;
	private static Logger logger = Logger.getLogger(AccessControl.class.getName());
	
	
	@XmlElement(name = "role")
	private List<Role> roleList;
	
	@XmlElement(name = "function")
	private List<Function> functionList;
	
	private Map<String, Role> roleMap;
	private Map<String, Function> functionMap;
	
	
	protected AccessControl()
	{
	}
	
	
	public static AccessControl getInstance()
	{
		return instance;
	}
	
	
	public static void parseAccessFile(InputStream is)
	{
		// Unmarshal the WEB-INF/access-control.xml to Java objects
		try 
		{
			JAXBContext jCtx = JAXBContext.newInstance(AccessControl.class);
			Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
			instance = (AccessControl) jaxbUnmarshaller.unmarshal(is);
			
			logger.log(Level.CONFIG, "AccessControl.instance="+instance);
		}
		catch (JAXBException e) 
		{
			e.printStackTrace();
		}
	}

	
	public List<Role> getRoleList()
	{
		return (roleList != null) ? roleList : Collections.EMPTY_LIST;
	}
	

	public Role getRole(String roleId)
	{
		return (roleId != null && getRoleMap() != null) ? roleMap.get(roleId) : null; 
	}
	
	
	/**
	 * Get the Role mapping (roleId -> Role object)
	 * @return
	 */
	public Map<String, Role> getRoleMap()
	{
		// Create the roleMap by the functionList
		if (roleMap == null) 
		{
			roleMap = new HashMap<String, Role>();
		
			if (roleList != null)
			{
				for (Role role : roleList)
				{
					roleMap.put(role.getRoleId(), role);
				}
			}
			
			roleMap = Collections.unmodifiableMap(roleMap);
		}
		
		return roleMap;
	}


	public List<Function> getFunctionList()
	{
		return (functionList != null) ? functionList : Collections.EMPTY_LIST;
	}


	public Function getFunction(String funcId)
	{
		return (funcId != null && getFunctionMap() != null) ? functionMap.get(funcId) : null; 
	}
	

	/**
	 * Get the Function instance if the target access url is defined in the Function
	 * @param url Target access url
	 * @return Function instance if the target access url is defined in the Function
	 */
	public List<Function> getFunctionListByMatchedUrl(String url)
	{
		return functionList.stream().filter(f -> f.containsURL(url)).collect(Collectors.toList());
	}
	

	/**
	 * Get the function mapping (funcId -> Function object)
	 * @return
	 */
	public Map<String, Function> getFunctionMap()
	{
		// Create the functionMap by the functionList
		if (functionMap == null) 
		{
			functionMap = new HashMap<String, Function>();
		
			if (functionList != null)
			{
				for (Function function : functionList)
				{
					functionMap.put(function.getFuncId(), function);
				}
			}
			
			functionMap = Collections.unmodifiableMap(functionMap);
		}
		
		return functionMap;
	}
	
	
	@Override
	public String toString()
	{
		return "AccessControl [functionList=" + functionList + "]";
	}
	
 
	/*
	// Unit Test Code
	public static void main(String args[]) throws Exception
	{
		java.io.File f = new java.io.File("D:/workspace/survey/WebContent/WEB-INF/access-control.xml");
		JAXBContext jCtx = JAXBContext.newInstance(AccessControl.class);
		
		Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
		AccessControl acsControl = (AccessControl) jaxbUnmarshaller.unmarshal(f);	
		
		System.out.println("acsControl="+acsControl);
	}
	*/
	
}
