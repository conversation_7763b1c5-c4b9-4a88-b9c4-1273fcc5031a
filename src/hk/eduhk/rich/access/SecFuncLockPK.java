package hk.eduhk.rich.access;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
public class SecFuncLockPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="LOCK_CODE", length = 30)
	private String lock_code;

	@Column(name="FUNC_ID", length = 50)
	private String func_id;

	@Column(name="LOCK_GRP", length = 10)
	private String lock_grp;

	
	public String getLock_code()
	{
		return lock_code;
	}

	
	public void setLock_code(String lock_code)
	{
		this.lock_code = lock_code;
	}

	
	public String getFunc_id()
	{
		return func_id;
	}

	
	public void setFunc_id(String func_id)
	{
		this.func_id = func_id;
	}

	
	public String getLock_grp()
	{
		return lock_grp;
	}

	
	public void setLock_grp(String lock_grp)
	{
		this.lock_grp = lock_grp;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((func_id == null) ? 0 : func_id.hashCode());
		result = prime * result + ((lock_code == null) ? 0 : lock_code.hashCode());
		result = prime * result + ((lock_grp == null) ? 0 : lock_grp.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SecFuncLockPK other = (SecFuncLockPK) obj;
		if (func_id == null)
		{
			if (other.func_id != null)
				return false;
		}
		else if (!func_id.equals(other.func_id))
			return false;
		if (lock_code == null)
		{
			if (other.lock_code != null)
				return false;
		}
		else if (!lock_code.equals(other.lock_code))
			return false;
		if (lock_grp == null)
		{
			if (other.lock_grp != null)
				return false;
		}
		else if (!lock_grp.equals(other.lock_grp))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SecFuncLockPK [lock_code=" + lock_code + ", func_id=" + func_id + ", lock_grp=" + lock_grp + "]";
	}

	

}
