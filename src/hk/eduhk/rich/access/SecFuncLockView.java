package hk.eduhk.rich.access;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.RowEditEvent;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.banner.BanPerson;
import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.staff.PureDept;
import hk.eduhk.rich.entity.staff.StaffDAO;


@ManagedBean(name = "secFuncLockView")
@ViewScoped
@SuppressWarnings("serial")
public class SecFuncLockView extends BaseView
{
	private static Logger logger = Logger.getLogger(SecFuncLock.class.getName());
	
	private int paramTabIndex;
	private String paramLockCode;
	private String paramFuncId;
	private SecFuncLock selectedSecFuncLock;
	private SecFuncLock selectedSecFuncLockRaeResearcher;
	private SecFuncLock selectedSecFuncLockRaeUoaAdmin;
	private SecFuncLockUser selectedSecFuncLockUser;
	private SecFuncLockUser removeSecFuncLockUser;
	private List<SecFuncLockUser> secFuncLockUserList  = null;
	private List<LookupValue> deptList  = null;
	private List<LookupValue> selectedDepts;
	
	public int getParamTabIndex()
	{
		return paramTabIndex;
	}

	public void setParamTabIndex(int paramTabIndex)
	{
		this.paramTabIndex = paramTabIndex;
	}

	
	public String getParamFuncId()
	{
		return paramFuncId;
	}

	
	public void setParamFuncId(String paramFuncId)
	{
		this.paramFuncId = paramFuncId;
	}

	public String getParamLockCode()
	{
		return paramLockCode;
	}

	
	public void setParamLockCode(String paramLockCode)
	{
		this.paramLockCode = paramLockCode;
	}

	public void setLockCode(String value)
	{
		if (!Strings.isNullOrEmpty(value)) {
			paramLockCode = value;
		}
	}
	
	public SecFuncLock getSelectedSecFuncLock() {
		if (selectedSecFuncLock == null) {
			AccessDAO dao = AccessDAO.getInstance();
			if ("EXCLUSIVE_KT".equals(getParamLockCode())) {
    			selectedSecFuncLock = new SecFuncLock();
    			selectedSecFuncLock.setLock_status("N");
    		}else {
    			selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_RI", "MANAGE_RI", "ALL");
    		}
		}
		return selectedSecFuncLock;
	}

	
	public void setSelectedSecFuncLock(SecFuncLock selectedSecFuncLock)
	{
		this.selectedSecFuncLock = selectedSecFuncLock;
	}
	
	
	
	public SecFuncLock getSelectedSecFuncLockRaeResearcher()
	{
		if (selectedSecFuncLockRaeResearcher == null) {
			AccessDAO dao = AccessDAO.getInstance();
			selectedSecFuncLockRaeResearcher = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", "RESEARCHER");
		}
		return selectedSecFuncLockRaeResearcher;
	}

	
	public void setSelectedSecFuncLockRaeResearcher(SecFuncLock selectedSecFuncLockRaeResearcher)
	{
		this.selectedSecFuncLockRaeResearcher = selectedSecFuncLockRaeResearcher;
	}

	
	public SecFuncLock getSelectedSecFuncLockRaeUoaAdmin()
	{
		if (selectedSecFuncLockRaeUoaAdmin == null) {
			AccessDAO dao = AccessDAO.getInstance();
			selectedSecFuncLockRaeUoaAdmin = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", "UOAADMIN");
		}
		return selectedSecFuncLockRaeUoaAdmin;
	}

	
	public void setSelectedSecFuncLockRaeUoaAdmin(SecFuncLock selectedSecFuncLockRaeUoaAdmin)
	{
		this.selectedSecFuncLockRaeUoaAdmin = selectedSecFuncLockRaeUoaAdmin;
	}

	public SecFuncLockUser getSelectedSecFuncLockUser()
	{
		if (selectedSecFuncLockUser == null) {
			selectedSecFuncLockUser = new SecFuncLockUser();
		}
		return selectedSecFuncLockUser;
	}

	
	public void setSelectedSecFuncLockUser(SecFuncLockUser selectedSecFuncLockUser)
	{
		this.selectedSecFuncLockUser = selectedSecFuncLockUser;
	}


	public void clearRaeForm(String lock_grp) {
		if (selectedSecFuncLock != null) {
			AccessDAO dao = AccessDAO.getInstance();
			if ("RESEARCHER".equals(lock_grp)) {
				selectedSecFuncLockRaeResearcher = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", "RESEARCHER");
				selectedSecFuncLockRaeResearcher.setLock_msg("");
			}
			if ("UOAADMIN".equals(lock_grp)) {
				selectedSecFuncLockRaeUoaAdmin = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", "UOAADMIN");
				selectedSecFuncLockRaeUoaAdmin.setLock_msg("");
			}
		}
	}
	
	public void clearForm() {
		if (selectedSecFuncLock != null) {
			AccessDAO dao = AccessDAO.getInstance();
			if ("EXCLUSIVE_KT".equals(getParamLockCode())) {
    			selectedSecFuncLock = new SecFuncLock();
    		}else {
    			selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_RI", "MANAGE_RI", "ALL");
    			
    		}
			selectedSecFuncLock.setLock_msg("");
		}
	}
	
	public void updateRaeSecFuncLock(String lock_grp)
	{
		if ("RESEARCHER".equals(lock_grp)) {
			selectedSecFuncLock = selectedSecFuncLockRaeResearcher;
		}
		if ("UOAADMIN".equals(lock_grp)) {
			selectedSecFuncLock = selectedSecFuncLockRaeUoaAdmin;
		}
		updateSecFuncLock();
	}
	
    public String updateSecFuncLock() 
    {
    	String message;
    	String destUrl = "";
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedSecFuncLock != null) {
    		try {
    			Boolean isNew = (selectedSecFuncLock.getCreator() == null)?true:false;
    			selectedSecFuncLock.setUserstamp(getLoginUserId());
    			AccessDAO dao = AccessDAO.getInstance();
				dao.updateSecFuncLock(selectedSecFuncLock);
				message = (isNew)?"msg.success.create.x":"msg.success.update.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Exclusive Use ");
	    		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	    		if ("MANAGE_RAE".equals(selectedSecFuncLock.getPk().getLock_code())) {
	    			destUrl = redirect("manageExRae") + "&id=0";
	    		}else {
	    			destUrl = redirect("manageExclusiveUse") + "&id=0";
	    		}
	    		
    		}catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("Department");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    	return destUrl;
    }
  
    public void reloadSecFuncLockUserList(){
    	secFuncLockUserList = null;
    }   
    
    public List<SecFuncLockUser> getSecFuncLockUserList()
    {
    	if (secFuncLockUserList == null) {
    		AccessDAO dao = AccessDAO.getInstance();
    		if ("EXCLUSIVE_KT".equals(getParamLockCode())) {
    			secFuncLockUserList = dao.getSecFuncLockUserList("EXCLUSIVE_KT", "MANAGE_KT_ACT");
    		}else {
    			secFuncLockUserList = dao.getSecFuncLockUserList("EXCLUSIVE_RI", "MANAGE_RI");
    		}
    	}
		return secFuncLockUserList;
    }
    

	public String getUserName(String userId) {
    	BannerLookupDAO lookupDAO = BannerLookupDAO.getInstance();
    	BanPerson selectedPerson = lookupDAO.getActivePersonByUserId(userId);
    	String userName = (selectedPerson!=null)?selectedPerson.getName():"";
		return userName;
    }
    
    public String getUserDept(String userId) {
    	BannerLookupDAO lookupDAO = BannerLookupDAO.getInstance();
    	BanPerson selectedPerson = lookupDAO.getActivePersonByUserId(userId);
    	String userName = (selectedPerson!=null)?selectedPerson.getDepartment():"";
		return userName;
    }   
    
    //get old sec func lock user
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		AccessDAO dao = AccessDAO.getInstance();
    		if ("EXCLUSIVE_KT".equals(getParamLockCode())) {
    			removeSecFuncLockUser = dao.getSecFuncLockUser("EXCLUSIVE_KT", "MANAGE_KT_ACT", (String)event.getOldValue());
    		}
    		if ("EXCLUSIVE_RI".equals(getParamLockCode())) {
    			removeSecFuncLockUser = dao.getSecFuncLockUser("EXCLUSIVE_RI", "MANAGE_RI", (String)event.getOldValue());
    		}
    		if ("EXCLUSIVE_RAE".equals(getParamLockCode())) {
    			removeSecFuncLockUser = dao.getSecFuncLockUser("EXCLUSIVE_RAE", "MANAGE_RAE", (String)event.getOldValue());
    		}
    	}
    }
    
    public void onRowEdit(RowEditEvent<SecFuncLockUser> event) {
	       Boolean isDuplicateKey = false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
	       
			//Check user id is unique
			int count = 0;
			for (SecFuncLockUser d: secFuncLockUserList){
				if (event.getObject().getPk().getUser_id().equals(d.getPk().getUser_id())) {
					count++;
				}
			}
			if (count > 1) {
				isDuplicateKey = true;
			}
			
			if (isDuplicateKey) {
				String param = "User ID";
				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
			
			//Check data			
			if (event.getObject().getPk().getUser_id().isEmpty()) {
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "User ID cannot be null", ""));
    		}
			
			if (event.getObject().getPk().getUser_id().length() > 20) {
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "User ID is too long ", ""));
			}   		
			
			if (event.getObject().getRemarks().length() > 1000) {
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Remarks is too long ", ""));
			}   
			
			//Update extended access user
 		if (fCtx.getMessageList().isEmpty()) {
 			selectedSecFuncLockUser = event.getObject();
 			updateSecFuncLockUser();
 			if (removeSecFuncLockUser != null) {
 				AccessDAO dao = AccessDAO.getInstance();
 				dao.deleteSecFuncLockUser(removeSecFuncLockUser);
 				removeSecFuncLockUser = null;
 			}
 			secFuncLockUserList = null;
 		}     
    }
    
    public void onRowCancel(RowEditEvent<SecFuncLockUser> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "User ID: "+String.valueOf(event.getObject().getPk().getUser_id()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }   
    
    public void onAddNew() {
    	SecFuncLockUser newRow = new SecFuncLockUser();
    	if ("EXCLUSIVE_KT".equals(getParamLockCode())) {
    		newRow.getPk().setFunc_id("MANAGE_KT_ACT");
        	newRow.getPk().setLock_code("EXCLUSIVE_KT");
		}
    	if ("EXCLUSIVE_RI".equals(getParamLockCode())) {
			newRow.getPk().setFunc_id("MANAGE_RI");
			newRow.getPk().setLock_code("EXCLUSIVE_RI");
		}
    	newRow.setLock_status("N");
    	secFuncLockUserList.add(0, newRow);
    }
    
	
	public void deleteSecFuncLockUser() {
		System.out.println("selectedSecFuncLockUser:"+selectedSecFuncLockUser);
		if (selectedSecFuncLockUser != null) {
			try {
				if (selectedSecFuncLockUser.getPk().getUser_id() != "") {
					AccessDAO dao = AccessDAO.getInstance();
					dao.deleteSecFuncLockUser(selectedSecFuncLockUser);
					String message = "msg.success.delete.x";
					message = MessageFormat.format(getResourceBundle().getString(message), selectedSecFuncLockUser.getPk().getUser_id());
		    		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
		    		FacesContext.getCurrentInstance().addMessage(null, msg);
		    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
				}
	    		secFuncLockUserList.remove(selectedSecFuncLockUser);
	    		selectedSecFuncLockUser = null;
			}
			catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedSecFuncLockUser.getPk().getUser_id());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
		}
	}
	
    public String updateSecFuncLockUser() {
    	String message;
    	String destUrl = "";
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedSecFuncLockUser != null) {
    		try {
    			Boolean isNew = (selectedSecFuncLockUser.getCreator() == null)?true:false;
    			selectedSecFuncLockUser.setUserstamp(getLoginUserId());
    			AccessDAO dao = AccessDAO.getInstance();
				dao.updateSecFuncLockUser(selectedSecFuncLockUser);
				message = (isNew)?"msg.success.create.x":"msg.success.update.x";
				message = MessageFormat.format(getResourceBundle().getString(message), selectedSecFuncLockUser.getPk().getUser_id());
	    		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	    		destUrl = redirect("manageExclusiveUse") + "&id=1";
    		}catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("User ID");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    	return destUrl;
    }

	
	@SuppressWarnings("null")
	public List<LookupValue> getDeptList()
	{
		if (deptList == null) {
			LookupValueDAO vDao = LookupValueDAO.getInstance();
			List<LookupValue> deptLv2List = vDao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			List<LookupValue> deptLv1List = vDao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			deptList = new ArrayList<LookupValue>();
			if (deptLv1List != null && deptLv2List != null) {
				for (LookupValue d1:deptLv1List) {
					int i = 0;
					SelectItemGroup fac = new SelectItemGroup(d1.getDescription());
					SelectItem[] depts = new SelectItem[deptLv2List.size()];
					depts[i] = new SelectItem(d1.getPk().getLookup_code(), d1.getDescription());
					deptList.add(d1);
					for (LookupValue d2:deptLv2List) {
						if (d2.getParent_lookup_code().equals(d1.getPk().getLookup_code())) {
							depts[i] = new SelectItem(d2.getPk().getLookup_code(), d2.getDescription());
							deptList.add(d2);
							i++;
						}	
					}
					fac.setSelectItems(depts);
				}
			}
			
		}
		return deptList;
	}

	
	public void setDeptList(List<LookupValue> deptList)
	{
		this.deptList = deptList;
	}

	
	public List<LookupValue> getSelectedDepts()
	{
		return selectedDepts;
	}

	
	public void setSelectedDepts(List<LookupValue> selectedDepts)
	{
		this.selectedDepts = selectedDepts;
	}
    
	public SecFuncLock getExRi(String lock_grp)
	{
		SecFuncLock exRi;
		AccessDAO dao = AccessDAO.getInstance();
		exRi = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", lock_grp);
		return exRi;
	}
	
	public SecFuncLock getExKtByDept(String dept)
	{
		SecFuncLock exKt;
		AccessDAO dao = AccessDAO.getInstance();
		exKt = dao.getSecFuncLock("EXCLUSIVE_KT", "MANAGE_KT_ACT", dept);
		return exKt;
	}
	
	@SuppressWarnings("unused")
	public String getSelectedDeptBtnMsg() {
        if (hasSelectedDepts()) {
            int size = this.selectedDepts.size();
            //set selectedSecFuncLock
            if (size == 1) {
            	selectedSecFuncLock = getExKtByDept(this.selectedDepts.get(0).getPk().getLookup_code());
            }else if (size > 1) {
            	//check lock_status and lock_msg are unique
            	HashSet<String> unique_lock_status=new HashSet<String>();
            	HashSet<String> unique_lock_msg=new HashSet<String>();
            	Boolean is_unique_lock_status = true;
            	Boolean is_unique_lock_msg = true;
            	selectedSecFuncLock = null;
            	getSelectedSecFuncLock();
            	for (int i = 0; i < size; i++) {
            		SecFuncLock tmp = getExKtByDept(this.selectedDepts.get(i).getPk().getLookup_code());
            		if (tmp == null) {
            			is_unique_lock_status = false;
            			is_unique_lock_msg = false;
            		}else {
            			selectedSecFuncLock.setLock_status(tmp.getLock_status());
                		selectedSecFuncLock.setLock_msg(tmp.getLock_msg());
            			if (unique_lock_status.add(tmp.getLock_status()) && i > 0) {
            				is_unique_lock_status = false;
            			}
            			if (unique_lock_msg.add(tmp.getLock_msg()) && i > 0) {
            				is_unique_lock_msg = false;
            			}
            		}
            	}
            	if (is_unique_lock_status == false) {
            		selectedSecFuncLock.setLock_status("N");
            	}
            	if (is_unique_lock_msg == false) {
            		selectedSecFuncLock.setLock_msg("");
            	}
            }else {
            	selectedSecFuncLock = null;
            }
            if (selectedSecFuncLock == null) {
            	getSelectedSecFuncLock();
            }
            return size > 1 ? size + " faculties/departments selected" : "1 faculty/department selected";
        }

        return "Please select";
    }
	
	public boolean hasSelectedDepts() {
        return this.selectedDepts != null && !this.selectedDepts.isEmpty();
    }
	
	public String updateSelectedDepts() 
	{
		String message;
    	String destUrl = "";
    	SecFuncLock exKt;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedSecFuncLock != null && selectedDepts != null) {
    		try {
    			for (LookupValue d:selectedDepts) {
    				AccessDAO dao = AccessDAO.getInstance();
    				exKt = dao.getSecFuncLock("EXCLUSIVE_KT", "MANAGE_KT_ACT", d.getPk().getLookup_code());
    				if (exKt == null) {
    					exKt = new SecFuncLock();
    					exKt.getPk().setLock_code("EXCLUSIVE_KT");
    					exKt.getPk().setFunc_id("MANAGE_KT_ACT");
    					exKt.getPk().setLock_grp(d.getPk().getLookup_code());
    					exKt.setDept(d.getPk().getLookup_code());
    				}
    				exKt.setLock_status(selectedSecFuncLock.getLock_status());
					exKt.setLock_msg(selectedSecFuncLock.getLock_msg());
					exKt.setUserstamp(getLoginUserId());
    				dao.updateSecFuncLock(exKt);
    			}
				message = "msg.success.update.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Exclusive Use ");
	    		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	    		destUrl = redirect("manageExKt") + "&id=0";
    		}catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("Department");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
		return destUrl;
    }
}
