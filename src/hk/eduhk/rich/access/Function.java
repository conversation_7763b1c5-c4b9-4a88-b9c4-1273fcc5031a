package hk.eduhk.rich.access;

import java.io.Serializable;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.bind.annotation.*;

import hk.eduhk.rich.access.authorizer.FunctionAuthorizer;


@XmlRootElement(name = "function")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Function implements Serializable
{

	private static Logger logger = Logger.getLogger(Function.class.getName());

	@XmlElement(name = "id")
	private String funcId;
	
	@XmlElement(name = "name")
	private String name;
	
	@XmlElement(name = "description")
	private String description;
	
	@XmlElement(name = "entry-url")
	private String entryUrl;
	
	@XmlElement(name = "access")
	private FunctionAccess access;
	
	private transient FunctionAuthorizer authorizer = null;
	
	
	public Function()
	{
	}


	public String getFuncId()
	{
		return funcId;
	}


	public void setFuncId(String funcId)
	{
		this.funcId = funcId;
	}

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}


	public String getDescription()
	{
		return description;
	}


	public void setDescription(String description)
	{
		this.description = description;
	}


	public String getEntryUrl()
	{
		return entryUrl;
	}


	public void setEntryUrl(String entryUrl)
	{
		this.entryUrl = entryUrl;
	}
	

	public FunctionAuthorizer getFunctionAuthorizer()
	{
		if (authorizer == null)
		{
			authorizer = getAccess().getAuthorizer().getFunctionAuthorizer();
		}
		
		return authorizer;
	}
	
	
	public boolean isAuthorized(String userId)
	{
		return (getFunctionAuthorizer() != null) ? getFunctionAuthorizer().isAuthorized(getFuncId(), userId) : false;
	}
	
	
	public FunctionAccess getAccess()
	{
		return access;
	}

	
	public void setAccess(FunctionAccess access)
	{
		this.access = access;
	}

	
	public List<String> getUrlList()
	{
		return (access != null) ? access.getUrlList() : null;
	}

	
	public boolean containsURL(String url)
	{
		return (getUrlList() != null) ? getUrlList().contains(url) : false;
	}


	@Override
	public String toString()
	{
		return "Function [funcId=" + funcId + ", name=" + name + ", description=" + description + ", entryUrl="
				+ entryUrl + ", access=" + access + "]";
	}

}
