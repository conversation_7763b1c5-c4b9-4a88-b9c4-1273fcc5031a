package hk.eduhk.rich.access.authorizer;

import java.util.*;
import java.util.logging.Level; 
import java.util.logging.Logger;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.rich.access.AccessDAO;


public class FunctionRoleAccessAuthorizer extends AbstractFunctionAuthorizer
{

	public static final String PARAM_AUTH_ROLES = "authorizedRoles";
	
	private List<String> roleList;
	
	private static Logger logger = Logger.getLogger(FunctionRoleAccessAuthorizer.class.getName());
	
	
	public FunctionRoleAccessAuthorizer()
	{
	}

	
	public List<String> getAuthorizedRoleList()
	{
		if (roleList == null)
		{
			// Parse parameter authorizedRoles
			String[] roles = null;
			String paramGroups = getParamValue(PARAM_AUTH_ROLES);
			
			if (paramGroups != null)
			{
				roles = paramGroups.split(",");
				for (int n=0;n<roles.length;n++) roles[n] = roles[n].trim();
				roleList = Collections.unmodifiableList(Arrays.asList(roles));
			}	
		}
		
		return (roleList != null) ? roleList : Collections.EMPTY_LIST;
	}
	

	@Override
	public boolean isAuthorized(String funcId, String userId)
	{
		return CollectionUtils.containsAny(getAuthorizedRoleList(), AccessDAO.getCacheInstance().getRoleIdSetByUserId(userId));
	}

	
}
