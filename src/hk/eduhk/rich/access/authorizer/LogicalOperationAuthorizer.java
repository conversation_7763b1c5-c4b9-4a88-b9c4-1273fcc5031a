package hk.eduhk.rich.access.authorizer;

import java.util.*;
import java.util.logging.Level; 
import java.util.logging.Logger;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.Constant;


public class LogicalOperationAuthorizer extends AbstractFunctionAuthorizer
{
	
	public static final String PARAM_AUTHORIZERS 	= "authorizers";
	public static final String PARAM_OPEREATOR 		= "operator";
	
	
	private String operator = null;
	private List<FunctionAuthorizer> funcAuthorizerList;
	
	
	private static Logger logger = Logger.getLogger(LogicalOperationAuthorizer.class.getName());
	
	
	public LogicalOperationAuthorizer(String operator, List<FunctionAuthorizer> funcAuthorizerList)
	{
		this.operator = StringUtils.upperCase(operator);
		this.funcAuthorizerList = funcAuthorizerList;
	}
	

	@Override
	public boolean isAuthorized(String funcId, String userId)
	{
		boolean authorized = false;
		
		// Iterate each FunctionAuthorizer using the specified operator
		if (CollectionUtils.isNotEmpty(funcAuthorizerList))
		{
			authIter:
			for (int n=0;n<funcAuthorizerList.size();n++)
			{
				FunctionAuthorizer funcAuthorizer = funcAuthorizerList.get(n);
				boolean funcAuthorized = funcAuthorizer.isAuthorized(funcId, userId);
				
				if (n == 0)
				{ 
					authorized = funcAuthorized;
				}
				else
				{
					switch (operator)
					{
						case Constant.LOGICAL_OP_AND:
							authorized = authorized & funcAuthorized;
							if (!authorized) break authIter;
							break;
							
							
						case Constant.LOGICAL_OP_OR:
							authorized = authorized | funcAuthorized;
							break;
					}
				}
			}
		}
		
		return authorized;
	}
	
}
