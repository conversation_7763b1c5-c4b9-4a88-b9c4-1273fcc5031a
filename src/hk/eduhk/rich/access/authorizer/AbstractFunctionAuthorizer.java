package hk.eduhk.rich.access.authorizer;

import java.util.*;

import hk.eduhk.rich.access.Param;


public abstract class AbstractFunctionAuthorizer implements FunctionAuthorizer
{
	
	private Map<String, String> paramMap;
	
	
	@Override
	public void initParamMap(List<Param> paramList)
	{
		if (paramMap == null)
		{
			paramMap = new HashMap<String, String>();
		}
		else 
		{
			paramMap.clear();
		}
		
		if (paramList != null)
		{
			for (Param param : paramList)
			{
				paramMap.put(param.getName(), param.getValue());
			}
		}
	}
	

	@Override
	public String getParamValue(String paramName)
	{
		return (paramMap != null) ? paramMap.get(paramName) : null;
	}

}
