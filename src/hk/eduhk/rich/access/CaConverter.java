package hk.eduhk.rich.access;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.FacesConverter;

import org.omnifaces.util.selectitems.SelectItemsUtils;

import hk.eduhk.rich.BaseConverter;
import hk.eduhk.rich.entity.importRI.ImportRICA;
import hk.eduhk.rich.entity.staff.PureDept;


@FacesConverter("hk.eduhk.rich.access.CaConverter")
public class CaConverter extends BaseConverter
{
	
	@Override
	public Object getAsObject(FacesContext fCtx, UIComponent component, String value) 
	{
		// Find the selected Role object by roleId from <f:selectItems> element
		return SelectItemsUtils.findValueByStringConversion(fCtx, component, value, this);
	}
	
	
	@Override
	public String getAsString(FacesContext fCtx, UIComponent component, Object value) 
	{
		return (value != null && value instanceof ImportRICA) ? ((ImportRICA) value).getArea_code() : null;
	}	
	
}
