package hk.eduhk.rich.access.menu;

import java.util.*;
import javax.faces.bean.*;
import javax.faces.context.FacesContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;
import org.primefaces.model.menu.DefaultMenuItem;
import org.primefaces.model.menu.DefaultMenuModel;
import org.primefaces.model.menu.DefaultSubMenu;
import org.primefaces.model.menu.MenuModel;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.Function;


@ManagedBean(name="funcMenuView")
@ViewScoped
@SuppressWarnings("serial")
public class FunctionMenuView extends BaseView
{
	
	private MenuModel model;
	
	
	public MenuModel getUserMenuModel()
	{
		if (model == null)
		{
			model = new DefaultMenuModel();
			
			// Get current userId
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String userId = fCtx.getExternalContext().getRemoteUser();
			String contextPath = fCtx.getExternalContext().getRequestContextPath();
			
			DefaultSubMenu subMenu = null;
			AccessMenu acsMenu = AccessMenu.getInstance();
			
			// Iterate MenuGroup
			List<MenuGroup> groupList = acsMenu.getMenuGroupList();
			if (!CollectionUtils.isEmpty(groupList))
			{
				for (MenuGroup group : groupList)
				{
					subMenu = null;
					
					// Iterate MenuItem from the MenuGroup
					List<MenuItem> itemList = group.getMenuItemList();
					if (!CollectionUtils.isEmpty(itemList))
					{
						for (MenuItem item : itemList)
						{
							Function function = item.getFunction();
							if (function != null && function.isAuthorized(userId))
							{
								if (subMenu == null)
								{
									String groupName =  group.getName();
									subMenu = DefaultSubMenu.builder().label(groupName).build();
									model.getElements().add(subMenu);
								}
								
								String itemName = function.getName();
								DefaultMenuItem menuItem = DefaultMenuItem.builder()
																		.value(itemName)
																		.url(contextPath + function.getEntryUrl())
																		.build();
								subMenu.getElements().add(menuItem);
							}
						}
					}
				}
			}		
			
			// User Account sub menu
			subMenu = DefaultSubMenu.builder().label("User Account").build();
			model.getElements().add(subMenu);

			// Logout menu item
			DefaultMenuItem menuItem = DefaultMenuItem.builder()
													.value("Logout")
													.url(contextPath + "/user/signout.xhtml")
													.build();
			subMenu.getElements().add(menuItem);
		}
		
		return model;
	}
	
	
	public void setUserMenuModel(MenuModel model)
	{
		this.model = model;
	}
		
	
}
