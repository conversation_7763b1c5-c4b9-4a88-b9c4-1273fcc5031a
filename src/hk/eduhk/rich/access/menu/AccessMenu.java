package hk.eduhk.rich.access.menu;

import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.xml.bind.*;
import javax.xml.bind.annotation.*;


/**
 * A collection of Access Menu Groups.
 * Access control is relied on this class.
 * 
 * <AUTHOR>
 */
@XmlRootElement(name = "access-menu")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class AccessMenu implements Serializable
{
	
	private static AccessMenu instance = null;
	private static Logger logger = Logger.getLogger(AccessMenu.class.getName());
	
	
	@XmlElement(name = "menu-group")
	private List<MenuGroup> menuGroupList;
		
	
	protected AccessMenu()
	{
	}
	
	
	public static AccessMenu getInstance()
	{
		return instance;
	}
	
	
	public static void parseAccessFile(InputStream is)
	{
		// Unmarshal the WEB-INF/access-control.xml to Java objects
		try 
		{
			JAXBContext jCtx = JAXBContext.newInstance(AccessMenu.class);
			Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
			instance = (AccessMenu) jaxbUnmarshaller.unmarshal(is);
			
			logger.log(Level.CONFIG, "AccessMenu.instance="+instance);
		}
		catch (JAXBException e) 
		{
			e.printStackTrace();
		}
	}
		
	
	public List<MenuGroup> getMenuGroupList()
	{
		return menuGroupList;
	}

	
	public void setMenuGroupList(List<MenuGroup> menuGroupList)
	{
		this.menuGroupList = menuGroupList;
	}


	@Override
	public String toString()
	{
		return "AccessMenu [menuGroupList=" + menuGroupList + "]";
	}
	

	/*
	// Unit Test Code
	public static void main(String args[]) throws Exception
	{
		String s = "����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������";
		
		System.out.println(s.length());
		
		java.io.File f = new java.io.File("D:/workspace/survey/WebContent/WEB-INF/access-menu.xml");
		JAXBContext jCtx = JAXBContext.newInstance(AccessMenu.class);
		
		Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
		AccessMenu accessMenu = (AdminMenu) jaxbUnmarshaller.unmarshal(f);	
		
		System.out.println("AccessMenu="+accessMenu);
	}
	*/
	
}
