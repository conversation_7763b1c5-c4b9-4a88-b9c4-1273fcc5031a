package hk.eduhk.rich.access.menu;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "menu-item")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class MenuGroup implements Serializable
{
	
	@XmlAttribute(name = "name")
	private String name;
	
	@XmlAttribute(name = "color")
	private String color;	
	
	@XmlAttribute(name = "menu")
	private String menu;	
	
	@XmlElement(name = "menu-item")
	private List<MenuItem> menuItemList;

	
	public String getName()
	{
		return name;
	}


	public void setName(String name)
	{
		this.name = name;
	}

	
	
	public String getColor()
	{
		return color;
	}


	
	public void setColor(String color)
	{
		this.color = color;
	}


	
	public String getMenu()
	{
		return menu;
	}


	
	public void setMenu(String menu)
	{
		this.menu = menu;
	}


	public List<MenuItem> getMenuItemList()
	{
		return menuItemList;
	}

	
	public void setMenuItemList(List<MenuItem> menuItemList)
	{
		this.menuItemList = menuItemList;
	}


	@Override
	public String toString()
	{
		return "MenuGroup [name=" + name + ", color=" + color + ", menu=" + menu + ", menuItemList=" + menuItemList
				+ "]";
	}

	
}
