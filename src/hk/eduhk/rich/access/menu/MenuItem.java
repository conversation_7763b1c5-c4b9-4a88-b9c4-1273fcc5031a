package hk.eduhk.rich.access.menu;

import java.io.Serializable;
import javax.xml.bind.annotation.*;

import hk.eduhk.rich.access.AccessControl;
import hk.eduhk.rich.access.Function;


@XmlRootElement(name = "menu-item")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class MenuItem implements Serializable
{
	
	@XmlAttribute(name = "function-id")
	private String funcId;

	@XmlAttribute(name = "icon")
	private String icon;

	@XmlAttribute(name = "icon-style")
	private String iconStyle;
	
	
	public String getFuncId()
	{
		return funcId;
	}

	
	public void setFuncId(String funcId)
	{
		this.funcId = funcId;
	}


	public Function getFunction()
	{
		AccessControl acsControl = AccessControl.getInstance();
		return (acsControl != null) ? acsControl.getFunction(getFuncId()) : null;
	}
	
	
	public String getIcon()
	{
		return icon;
	}

	
	public void setIcon(String icon)
	{
		this.icon = icon;
	}

	
	public String getIconStyle()
	{
		return iconStyle;
	}

	
	public void setIconStyle(String iconStyle)
	{
		this.iconStyle = iconStyle;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((funcId == null) ? 0 : funcId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MenuItem other = (MenuItem) obj;
		if (funcId == null)
		{
			if (other.funcId != null)
				return false;
		}
		else if (!funcId.equals(other.funcId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "MenuItem [funcId=" + funcId + ", icon=" + icon + ", iconStyle=" + iconStyle + "]";
	}

	
}
