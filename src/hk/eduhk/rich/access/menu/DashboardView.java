package hk.eduhk.rich.access.menu;

import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.Function;
import hk.eduhk.rich.access.UserRole;
import hk.eduhk.rich.access.UserSessionView;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.award.AwardDetails_P;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentDetails_P;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.staff.Assistant;
import hk.eduhk.rich.entity.staff.AssistantDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name="dashboardView")
@ViewScoped
@SuppressWarnings("serial")
public class DashboardView extends BaseView
{

	private Map<MenuGroup, List<MenuItem>> menuItemMap;
	private Map<String,Function> funcMap = new HashMap<String,Function>();
	private Boolean acadStaff;
	private List<UserRole> userRoleList;
	
	public Map<MenuGroup, List<MenuItem>> getMenuItemMap()
	{
		if(menuItemMap == null)
		{
			menuItemMap = new LinkedHashMap<MenuGroup,List<MenuItem>>();
			
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String userId = fCtx.getExternalContext().getRemoteUser();
			
			AccessMenu acsMenu = AccessMenu.getInstance();
			
			List<MenuGroup> groupList = acsMenu.getMenuGroupList();

			if (!CollectionUtils.isEmpty(groupList))
			{
				for (MenuGroup group : groupList)
				{
					List<MenuItem> itemList = group.getMenuItemList();
					
					if (!CollectionUtils.isEmpty(itemList))
					{
						for (MenuItem item : itemList)
						{
							Function function = item.getFunction();
							if (function != null && function.isAuthorized(userId))
							{
								List<MenuItem> menuItemList = menuItemMap.get(group);
								if(menuItemList == null){
									menuItemMap.put(group, new ArrayList<MenuItem>());
								}
								menuItemMap.get(group).add(item);
								
								funcMap.put(item.getFuncId(),function);
							}
						}
					}
				}
			}
			
		}
		
		return menuItemMap;
	}

	
	public void setMenuItemMap(Map<MenuGroup, List<MenuItem>> menuItemMap)
	{
		this.menuItemMap = menuItemMap;
	}


	
	public Map<String, Function> getFuncMap()
	{
		return funcMap;
	}


	
	public void setFuncMap(Map<String, Function> funcMap)
	{
		this.funcMap = funcMap;
	}
	
	
	public String getImportRICount(String funcId) {
		String funcName = getFuncMap().get(funcId).getName();
		if(!funcName.equals("Import RI")) return null;
		else {
			Integer sum = 0;
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			StaffDAO sDao = StaffDAO.getCacheInstance();
			StaffIdentity s = sDao.getStaffDetailsByUserId(getCurrentUserId());
			if (s != null) {
				String staffId = s.getStaff_number();
				Integer outputCount = dao.getNumberOfImportRIOutput(staffId);
				Integer projectCount = dao.getNumberOfImportRIProject(staffId);
				Integer awardCount = dao.getNumberOfImportRIAward(staffId);
				Integer patentCount = dao.getNumberOfImportRIPatent(staffId);
				sum = outputCount + projectCount + awardCount + patentCount;
			}
			return sum != 0? sum.toString():null;
		}
	}
	
	public String getAsstRequestedCount(String funcId) {
		String funcName = getFuncMap().get(funcId).getName();
		if(!funcName.equals("Manage Assistant Right")) return null;
		else {
			AssistantDAO dao = AssistantDAO.getInstance();
			StaffDAO sDao = StaffDAO.getCacheInstance();
			StaffIdentity s = sDao.getStaffDetailsByUserId(getCurrentUserId());
			if (s != null) {
				List<Assistant> asstList = dao.getAsstListByAcadStaffAndStatus(s.getPid(), "REQUESTED");
				Integer sum = asstList.size();
				return sum != 0? sum.toString():null;
			}else {
				return null;
			}
		}
	}
	
	public String getUnconfirmRICount(String funcId) {
		String funcName = getFuncMap().get(funcId).getName();
		if(!funcName.equals("Consent RI")) return null;
		else {
			int countRI = 0;
			StaffDAO sDao = StaffDAO.getCacheInstance();
			StaffIdentity s = sDao.getStaffDetailsByUserId(getCurrentUserId());
			if (s != null) {
				//count output
				PublicationDAO pDao = PublicationDAO.getInstance();
				List<OutputDetails_P> outputList = pDao.getOutputDetails_P_byStaffNo_consent(s.getStaff_number(), "M");
				countRI += (outputList != null)?outputList.size():0;
				/*for (OutputDetails_P o:outputList) {
					if (o.getOutputDetails_q() != null) {
						if ("U".equals(o.getOutputDetails_q().getConsent_ind()) && o.getOutputHeader_q().getPublish_freq() > 0) {
							countRI++;
						}
					}
				}*/
				//count project
				ProjectDAO projDao = ProjectDAO.getInstance();
				List<ProjectDetails_P> projectList = projDao.getProjectDetails_P_byStaffNo_consent(s.getStaff_number(), "M");
				countRI += (projectList != null)?projectList.size():0;
				/*for (ProjectDetails_P o:projectList) {
					if (o.getProjectDetails_q() != null) {
						if ("U".equals(o.getProjectDetails_q().getConsent_ind()) && o.getProjectHeader_q().getPublish_freq() > 0) {
							countRI++;
						}
					}
				}*/
				//count award
				AwardDAO awardDao = AwardDAO.getInstance();
				List<AwardDetails_P> awardList = awardDao.getAwardDetails_P_byStaffNo_consent(s.getStaff_number(), "M");
				countRI += (awardList != null)?awardList.size():0;
				/*for (AwardDetails_P o:awardList) {
					if (o.getAwardDetails_q() != null) {
						if ("U".equals(o.getAwardDetails_q().getConsent_ind()) && o.getAwardHeader_q().getPublish_freq() > 0) {
							countRI++;
						}
					}
				}*/
				//count patent
				PatentDAO patDao = PatentDAO.getInstance();
				List<PatentDetails_P> patentList = patDao.getPatentDetails_P_byStaffNo_consent(s.getStaff_number(), "M");
				countRI += (patentList != null)?patentList.size():0;
				/*for (PatentDetails_P o:patentList) {
					if (o.getPatentDetails_q() != null) {
						if ("U".equals(o.getPatentDetails_q().getConsent_ind()) && o.getPatentHeader_q().getPublish_freq() > 0) {
							countRI++;
						}
					}
				}*/
			}
			return countRI != 0?String.valueOf(countRI):null;
		}
	}
	
	public String getSysParamDesc(String code)
	{
		SysParamDAO sDAO = SysParamDAO.getInstance();
		String dclDesc = sDAO.getSysParamValueByCode(code);
		return dclDesc;
	}

	public List<UserRole> getUserRoleList(){
		if (userRoleList == null) {
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String userId = fCtx.getExternalContext().getRemoteUser();
			AccessDAO aDao = AccessDAO.getInstance();
			userRoleList = aDao.getUserRoleListByUserId(userId);
			//getLogger().log(Level.WARNING, userId+":"+userRoleList);
		}
		return userRoleList;
	}
	
	
	public void setUserRoleList(List<UserRole> userRoleList)
	{
		this.userRoleList = userRoleList;
	}


	public Boolean getAcadStaff()
	{
		if (acadStaff == null) {
			acadStaff = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("acadStaff"))
					.collect(Collectors.toList());
			acadStaff = (!tmpList.isEmpty())?true:false;
			//getLogger().log(Level.WARNING, acadStaff+":"+tmpList);
		}
		return acadStaff;
	}


	
	public void setAcadStaff(Boolean acadStaff)
	{
		this.acadStaff = acadStaff;
	}
}
