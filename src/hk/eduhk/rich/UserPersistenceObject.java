package hk.eduhk.rich;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import javax.persistence.*;


/**
 * UserPersistenceObject is the base class for Entity classes which need auditing 
 * in SRR sub-module. It defines the control fields: creator and userstamp
 *
 */
@MappedSuperclass
public abstract class UserPersistenceObject extends BasePersistenceObject
{
	
	private static final long serialVersionUID = 1L;

	@Column(name = "creator", length = 100)
	private String creator;

	@Column(name = "userstamp", length = 100)
	private String userstamp;
	

	public String getCreator()
	{
		return creator;
	}

	
	public void setCreator(String creator)
	{
		this.creator = creator;
	}


	public String getUserstamp()
	{
		return userstamp;
	}


	public void setUserstamp(String userstamp)
	{
		this.userstamp = userstamp;
		if (creator == null) creator = userstamp;
	}


	/**
	 * Clone the control fields from the provided object.
	 * 
	 */
	public void cloneControlFields(UserPersistenceObject sourceObj)
	{
		super.cloneControlFields(sourceObj);
		if (sourceObj != null) this.setCreator(sourceObj.getCreator());
	}
	
	public Date riStringToDate(Integer day, Integer month, Integer year, boolean start, Date startDate) throws ParseException
	{
		String dateStr = (day != null)?day+"/"+month+"/"+year:"01/"+month+"/"+year;
		Date date = new SimpleDateFormat("dd/MM/yyyy").parse("01/01/1900"); 
		if (month != null && year != null) {
			date = new SimpleDateFormat("dd/MM/yyyy").parse(dateStr); 
		}
		if (start == false) {
			if (month != null && year != null) {
				if (day == null) {
					date = (getLastDayOfMonth(date));
				}
			}else {
				date = startDate;
			}
			
		}
		return date;
	}
	
	public Date getLastDayOfMonth(Date date)
	{
		Calendar calendar = Calendar.getInstance();  
        calendar.setTime(date);  

        calendar.add(Calendar.MONTH, 1);  
        calendar.set(Calendar.DAY_OF_MONTH, 1);  
        calendar.add(Calendar.DATE, -1); 
        Date lastDayOfMonth = calendar.getTime();  
        return lastDayOfMonth;
	}
	
}