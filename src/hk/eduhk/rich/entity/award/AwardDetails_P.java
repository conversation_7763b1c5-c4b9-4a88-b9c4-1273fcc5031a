package hk.eduhk.rich.entity.award;

import java.sql.SQLException;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.project.ProjectDAO;

@Entity
@Table(name = "RH_P_AWARD_DTL")
public class AwardDetails_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(AwardDetails_P.class.toString());
	
	@EmbeddedId
	private AwardDetails_P_PK pk = new AwardDetails_P_PK();
	
	@Column(name = "recipient_name")
	private String recipient_name;

	@Column(name = "non_ied_staff_flag")
	private String flag;	
	
	@Column(name = "recipient_staff_no")
	private String staff_no;

	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "award_no", referencedColumnName = "award_no", nullable = false, insertable = false, updatable = false)
	private AwardHeader_Q awardHeader_q;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "award_no", referencedColumnName = "award_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", nullable = false, insertable = false, updatable = false)
	})
	private AwardHeader_P awardHeader_p;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "award_no", referencedColumnName = "award_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "recipient_staff_no", referencedColumnName = "staff_no", nullable = false, insertable = false, updatable = false)
	})
	private AwardDetails_Q awardDetails_q;
	
	public AwardDetails_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(AwardDetails_P_PK pk)
	{
		this.pk = pk;
	}


	public AwardHeader_Q getAwardHeader_q()
	{
		if (awardHeader_q != null) {
			try {
				awardHeader_q.getAward_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					AwardDAO dao = AwardDAO.getInstance();
					awardHeader_q = dao.getAwardHeader_Q(getPk().getAward_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return awardHeader_q;
	}

	public AwardHeader_P getAwardHeader_p()
	{
		if (awardHeader_p != null) {
			try {
				awardHeader_p.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					AwardDAO dao = AwardDAO.getInstance();
					awardHeader_p = dao.getAwardHeader_P(getPk().getAward_no(), getPk().getData_level());
				}
				else
				{
					throw re;
				}
			}
		}
		return awardHeader_p;
	}

	public AwardDetails_Q getAwardDetails_q()
	{
		if (awardDetails_q != null) {
			try {
				awardDetails_q.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					AwardDAO dao = AwardDAO.getInstance();
					awardDetails_q = dao.getAwardDetails_Q1(getPk().getAward_no(), getStaff_no());
				}
				else if (re.getClass().getName().equals("javax.persistence.EntityNotFoundException"))
				{
					// Handle case where AwardDetails_Q entity doesn't exist
					AwardDAO dao = AwardDAO.getInstance();
					awardDetails_q = dao.getAwardDetails_Q1(getPk().getAward_no(), getStaff_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return awardDetails_q;
	}
	public String getRecipient_name()
	{
		return recipient_name;
	}

	
	public void setRecipient_name(String recipient_name)
	{
		this.recipient_name = recipient_name;
	}

	
	public String getFlag()
	{
		return flag;
	}

	
	public void setFlag(String flag)
	{
		this.flag = flag;
	}

	
	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AwardDetails_P other = (AwardDetails_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "AwardDetails_P [pk=" + pk + ", recipient_name=" + recipient_name + ", flag=" + flag + ", staff_no="
				+ staff_no + "]";
	}



}
