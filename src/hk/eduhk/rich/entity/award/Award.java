package hk.eduhk.rich.entity.award;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import hk.eduhk.rich.entity.BaseRIFull;

@SuppressWarnings("serial")
public class Award extends BaseRIFull
{

	private String staffFullname;
	private String staffDept;
	private String awardName;
	private String orgName;
	private String awardDay;
	private String awardMonth;
	private String awardYear;
	private String fullDesc;
	private String shortDesc;
	private String IEdWorkInd;
	//private int riNo;
	
	// for RH-ADM-006 Research Information Listing
	private String recipient_list;
	private String awardDate;
	
	private String cdcf_selected_ind;

	public Award()
	{
	}


	public String getAwardName()
	{
		return awardName;
	}


	public void setAwardName(String awardName)
	{
		this.awardName = awardName;
	}


	public String getOrgName()
	{
		return orgName;
	}


	public void setOrgName(String orgName)
	{
		this.orgName = orgName;
	}


	public String getAwardDay()
	{
		return awardDay;
	}


	public void setAwardDay(String awardDay)
	{
		this.awardDay = awardDay;
	}


	public String getAwardMonth()
	{
		return awardMonth;
	}


	public void setAwardMonth(String awardMonth)
	{
		this.awardMonth = awardMonth;
	}


	public String getAwardYear()
	{
		return awardYear;
	}


	public void setAwardYear(String awardYear)
	{
		this.awardYear = awardYear;
	}


	public String getFullDesc()
	{
		return fullDesc;
	}


	public void setFullDesc(String fullDesc)
	{
		this.fullDesc = fullDesc;
	}


	public String getShortDesc()
	{
		return shortDesc;
	}


	public void setShortDesc(String shortDesc)
	{
		this.shortDesc = shortDesc;
	}


	public String getIEdWorkInd()
	{
		return IEdWorkInd;
	}


	public void setIEdWorkInd(String IEdWorkInd)
	{
		this.IEdWorkInd = IEdWorkInd;
	}


	public String toHTMLString()
	{
		return "";
	}


	public String toString()
	{
		return "pid=" + getPid() + ", " + "riNo=" + getRINo() + ", " + "staffNumber="
				+ StringUtils.defaultString(getStaffNumber()) + ", " + "orgName="
				+ StringUtils.defaultString(getOrgName()) + ", " + "fullDesc="
				+ StringUtils.defaultString(getFullDesc()) + ", " + "shortDesc="
				+ StringUtils.defaultString(getShortDesc());
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + "\"" + JSONObject.escape("pid") + "\":" + "\"" + JSONObject.escape(String.valueOf(getPid()))
				+ "\"," + "\"" + JSONObject.escape("RINo") + "\":" + "\"" + JSONObject.escape(String.valueOf(getRINo()))
				+ "\"," + "\"" + JSONObject.escape("staffNumber") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getStaffNumber())) + "\"," + "\""
				+ JSONObject.escape("awardName") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getAwardName())) + "\"," + "\""
				+ JSONObject.escape("orgName") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getOrgName())) + "\"," + "\""
				+ JSONObject.escape("awardDay") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getAwardDay())) + "\"," + "\""
				+ JSONObject.escape("awardMonth") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getAwardMonth())) + "\"," + "\""
				+ JSONObject.escape("awardYear") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getAwardYear())) + "\"," + "\""
				+ JSONObject.escape("fullDesc") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getFullDesc())) + "\"," + "\""
				+ JSONObject.escape("shortDesc") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getShortDesc())) + "\"," + "\""
				+ JSONObject.escape("IEdWorkInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getIEdWorkInd())) + "\"");

		strBuf.append(",\"" + JSONObject.escape("publishStatus") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getPublishStatus())) + "\"" + ",\""
				+ JSONObject.escape("lastModifiedBy") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getLastModifiedBy())) + "\"" + ",\""
				+ JSONObject.escape("lastModifiedDate") + "\":"
				+ (getLastModifiedDate() != null ? "new Date(" + getLastModifiedDate().getTime() + ")" : "\"\"") + ",\""
				+ JSONObject.escape("lastPublishedBy") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getLastPublishedBy())) + "\"" + ",\""
				+ JSONObject.escape("lastPublishedDate") + "\":"
				+ (getLastPublishedDate() != null ? "new Date(" + getLastPublishedDate().getTime() + ")" : "\"\"")
				+ ",\"" + JSONObject.escape("instDisplayInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getInstDisplayInd())) + "\"" + ",\""
				+ JSONObject.escape("instVerifiedInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getInstVerifiedInd())) + "\"" + ",\""
				+ JSONObject.escape("instVerifiedDate") + "\":"
				+ (getInstVerifiedDate() != null ? "new Date(" + getInstVerifiedDate().getTime() + ")" : "\"\"") + ",\""
				+ JSONObject.escape("CDCFStatus") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getCDCFStatus())) + "\"" + ",\""
				+ JSONObject.escape("CDCFGenInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getCDCFGenInd())) + "\"" + ",\""
				+ JSONObject.escape("CDCFGenDate") + "\":"
				+ (getCDCFGenDate() != null ? "new Date(" + getCDCFGenDate().getTime() + ")" : "\"\"") + ",\""
				+ JSONObject.escape("CDCFProcessInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getCDCFProcessInd())) + "\"" + ",\""
				+ JSONObject.escape("CDCFProcessDate") + "\":"
				+ (getCDCFProcessDate() != null ? "new Date(" + getCDCFProcessDate().getTime() + ")" : "\"\"") + ",\""
				+ JSONObject.escape("CDCFSelectedInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getCDCFSelectedInd())) + "\"" + ",\""
				+ JSONObject.escape("CDCFChangedInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getCDCFChangedInd())) + "\"" + ",\""
				+ JSONObject.escape("bulletinInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getBulletinInd())) + "\"" + ",\""
				+ JSONObject.escape("remarks") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getRemarks())) + "\"");

		strBuf.append(",\"" + JSONObject.escape("consentInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getConsentInd())) + "\"" + ",\""
				+ JSONObject.escape("displayInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getDisplayInd())) + "\"" + ",\""
				+ JSONObject.escape("creatorInd") + "\":" + "\""
				+ JSONObject.escape(StringUtils.defaultString(getCreatorInd())) + "\"");

		if (getRIDetailList() != null)
		{
			strBuf.append(",");
			strBuf.append("\"" + JSONObject.escape("detailList") + "\":" + JSONArray.toJSONString(getRIDetailList()));
		}

		if (getRIAttachmentList() != null)
		{
			strBuf.append(",");
			strBuf.append(
					"\"" + JSONObject.escape("attachmentList") + "\":" + JSONArray.toJSONString(getRIAttachmentList()));
		}

		strBuf.append("}");

		return strBuf.toString();
	}
	

	public int getRiNo()
	{
		return riNo;
	}


	
	public void setRiNo(int riNo)
	{
		this.riNo = riNo;
	}


	public void setStaffFullname(String staffFullname)
	{
		this.staffFullname = staffFullname;
	}


	public String getStaffFullname()
	{
		return staffFullname;
	}


	public void setStaffDept(String staffDept)
	{
		this.staffDept = staffDept;
	}


	public String getStaffDept()
	{
		return staffDept;
	}

	
	
	public String getRecipient_list()
	{
		return recipient_list;
	}


	
	public void setRecipient_list(String recipient_list)
	{
		this.recipient_list = recipient_list;
	}


	public String getAwardDate()
	{
		return awardDate;
	}


	
	public void setAwardDate(String awardDate)
	{
		this.awardDate = awardDate;
	}
	
	
	public String getCdcf_selected_ind()
	{
		return cdcf_selected_ind;
	}


	
	public void setCdcf_selected_ind(String cdcf_selected_ind)
	{
		this.cdcf_selected_ind = cdcf_selected_ind;
	}

}