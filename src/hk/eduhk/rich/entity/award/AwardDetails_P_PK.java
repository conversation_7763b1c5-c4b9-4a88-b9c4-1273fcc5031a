package hk.eduhk.rich.entity.award;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class AwardDetails_P_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="award_no")
	private Integer award_no;
	
	@Column(name="data_level")
	private String data_level;	
	
	@Column(name="line_no")
	private Integer line_no;

	
	public Integer getAward_no()
	{
		return award_no;
	}

	
	public void setAward_no(Integer award_no)
	{
		this.award_no = award_no;
	}

	
	
	public String getData_level()
	{
		return data_level;
	}


	
	public void setData_level(String data_level)
	{
		this.data_level = data_level;
	}


	public int getLine_no()
	{
		return line_no;
	}

	
	public void setLine_no(Integer line_no)
	{
		this.line_no = line_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((award_no == null) ? 0 : award_no.hashCode());
		result = prime * result + ((data_level == null) ? 0 : data_level.hashCode());
		result = prime * result + ((line_no == null) ? 0 : line_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AwardDetails_P_PK other = (AwardDetails_P_PK) obj;
		if (award_no == null)
		{
			if (other.award_no != null)
				return false;
		}
		else if (!award_no.equals(other.award_no))
			return false;
		if (data_level == null)
		{
			if (other.data_level != null)
				return false;
		}
		else if (!data_level.equals(other.data_level))
			return false;
		if (line_no == null)
		{
			if (other.line_no != null)
				return false;
		}
		else if (!line_no.equals(other.line_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "AwardDetails_P_PK [award_no=" + award_no + ", data_level=" + data_level + ", line_no=" + line_no + "]";
	}


	
}
