package hk.eduhk.rich.entity.award;

import java.util.logging.Logger;

import javax.persistence.*;


@Entity
@Table(name = "RH_PURE_AWARD_HDR_V")
public class AwardHeader
{
	public static Logger logger = Logger.getLogger(AwardHeader.class.toString());
	
	@Id
	@Column(name = "award_no")
	private int award_no;	
	
	@Column(name = "award_name")
	private String award_name;

	@Column(name = "description")
	private String description;

	@Column(name = "org_name")
	private String org_name;

	@Column(name = "award_year")
	private Integer award_year;
	
	@Column(name = "award_month")
	private Integer award_month;		
	
	@Column(name = "award_day")
	private Integer award_day;		
	
	@Column(name = "visibility")
	private String visibility;

	
	public int getAward_no()
	{
		return award_no;
	}

	
	public void setAward_no(int award_no)
	{
		this.award_no = award_no;
	}

	
	public String getAward_name()
	{
		return award_name;
	}

	
	public void setAward_name(String award_name)
	{
		this.award_name = award_name;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getOrg_name()
	{
		return org_name;
	}

	
	public void setOrg_name(String org_name)
	{
		this.org_name = org_name;
	}

	
	public Integer getAward_year()
	{
		return award_year;
	}

	
	public void setAward_year(Integer award_year)
	{
		this.award_year = award_year;
	}

	
	public Integer getAward_month()
	{
		return award_month;
	}

	
	public void setAward_month(Integer award_month)
	{
		this.award_month = award_month;
	}

	
	public Integer getAward_day()
	{
		return award_day;
	}

	
	public void setAward_day(Integer award_day)
	{
		this.award_day = award_day;
	}

	
	public String getVisibility()
	{
		return visibility;
	}

	
	public void setVisibility(String visibility)
	{
		this.visibility = visibility;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + award_no;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AwardHeader other = (AwardHeader) obj;
		if (award_no != other.award_no)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "AwardHeader [award_no=" + award_no + ", award_name=" + award_name + ", description=" + description
				+ ", org_name=" + org_name + ", award_year=" + award_year + ", award_month=" + award_month
				+ ", award_day=" + award_day + ", visibility=" + visibility + "]";
	}

	

}
