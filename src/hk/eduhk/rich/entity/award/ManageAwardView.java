package hk.eduhk.rich.entity.award;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.importRI.ImportRIAwardV;
import hk.eduhk.rich.entity.importRI.ImportRIAwardV_PK;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIStatus;
import hk.eduhk.rich.entity.patent.PatentDetails_Q;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_Q;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.view.ImportRIAward;


@ManagedBean(name = "manageAwardView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageAwardView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(Award.class.getName());
	
	private List<AwardDetails_P> awardList;
	private List<AwardDetails_P> awardDetails_p_list;
	
	private String riCreatorStaffNo;
	private Boolean isCreator;
	private Boolean isContributor;
	private Boolean canDelete;
	private Boolean hasSAP;
	private Boolean hasError;
	private Boolean saved;
	
	private AwardDetails_Q selectedAwardDetails_q;
	private AwardHeader_P selectedAwardHeader_p;
	private AwardHeader_Q selectedAwardHeader_q;
	private List<String> dayList;
	private List<SelectItem> cdcfStatusList;
	
	private AwardDAO awardDao = AwardDAO.getInstance();
	
	private ImportRIAward awardPanel;
	private ImportRIAwardV selectedImportAward;

	public void checkValid(ComponentSystemEvent event) throws IOException
	{
		paramDataLevel = getParamDataLevel();
		String message = "";
		if (!getIsRdoAdmin()) {
			if ((getIsCreator() == false && getIsContributor() == false) || !"M".equals(paramDataLevel)) {
				message = "You don't have access right.";	
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, getLoginUserId()+" doesn't have access right");
			}
		}
	}
	
	public Boolean hasAccessRight() 
	{
		Boolean result = false;
		if ("M".equals(paramDataLevel)){
			if (getIsCreator() || getIsContributor()) {
				result = true;
			}
		}
		if ("P".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		if ("C".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		return result;
	}
	
	public Boolean canViewRIList() 
	{
		Boolean result = false;
		if (staffDetail == null) {
			if (getIsAsst() || getIsRdoAdmin()) {
				staffDetail = getStaffDetail(getParamPid(), null, true);
				//check is Former Staff
				staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
				if (staffPastDetail != null) {
					staffDetail = new StaffIdentity();
					staffDetail.setPid(staffPastDetail.getPid());
					staffDetail.setStaff_number(staffPastDetail.getStaff_number());
					staffDetail.setCn(staffPastDetail.getCn());
					staffDetail.setStaffType("F");
				}
			}else {
				staffDetail = getStaffDetail(null, null, false);
			}
		}
		if (staffDetail != null) {
			if (Strings.isNullOrEmpty(paramPid))
				paramPid = String.valueOf(staffDetail.getPid());
			if (getIsAsst() || getIsRdoAdmin() || getCurrentUserId().equals(staffDetail.getCn())){
					result = true;
			}
		}
		return result;
	}
	
	public Boolean getCanDelete()
	{
		if (canDelete == null) {
			canDelete = false;
			if (getIsCreator() && !Strings.isNullOrEmpty(paramNo)) {
				/*if (getSelectedAwardHeader_q() != null) {
					if ("CDCF_PENDING".equals(selectedAwardHeader_q.getCdcf_status())) {
						AwardHeader_P selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedAwardHeader_p != null) {
							canDelete = true;
						}
					}
				}*/
				AwardHeader_P selectedAwardHeader_p_c = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
				if ("M".equals(getParamDataLevel())) {
					if (selectedAwardHeader_p_c == null) {
						AwardHeader_P selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedAwardHeader_p != null) {
							canDelete = true;
						}
					}
				}else {
					if (getHasSAP() == false) {
						canDelete = true;
					}
				}
			}
		}
		return canDelete;
	}

	
	public void setCanDelete(Boolean canDelete)
	{
		this.canDelete = canDelete;
	}
	
	public Boolean getHasSAP()
	{
		if (hasSAP == null) {
			hasSAP = false;
			hasSAP = awardDao.hasSAP("APPS.HKIEDKYRIE_PER_PEOPLE_EXTRA", "PERSON_EXTRA_INFO_ID", Integer.valueOf(paramNo));
		}
		return hasSAP;
	}


	
	public void setHasSAP(Boolean hasSAP)
	{
		this.hasSAP = hasSAP;
	}
	
	public Boolean getIsCreator()
	{
		if (isCreator == null) {
			isCreator = false;
			selectedAwardDetails_q = getSelectedAwardDetails_q();
			if (selectedAwardDetails_q != null) {
				isCreator = ("Y".equals(selectedAwardDetails_q.getCreator_ind()))?true:false;
				if (!Strings.isNullOrEmpty(getParamNo())) {
					AwardDetails_Q currentCreator = awardDao.getAwardDetails_Q_creator(Integer.valueOf(getParamNo()));
					if (isCreator && currentCreator != null) {
						if (currentCreator.getPk().getStaff_no().equals(selectedAwardDetails_q.getPk().getStaff_no())) {
							isCreator = true;
						}else {
							isCreator = false;
						}
					}
				}
			}
			if(paramNo == null) {
				isCreator = true;
			}
			if (getIsRdoAdmin()) {
				isCreator = true;
			}
		}
		return isCreator;
	}
	
	public void setIsCreator(Boolean isCreator)
	{
		this.isCreator = isCreator;
	}

	public Boolean getIsContributor()
	{
		if (isContributor == null) {
			isContributor = false;
			selectedAwardDetails_q = getSelectedAwardDetails_q();
			if (selectedAwardDetails_q != null) {
				isContributor = ("N".equals(selectedAwardDetails_q.getCreator_ind()))?true:false;
			}
		}
		return isContributor;
	}

	public void setIsContributor(Boolean isContributor)
	{
		this.isContributor = isContributor;
	}	
	

	public Boolean getHasError()
	{
		if (hasError == null) {
			hasError = false;
		}
		return hasError;
	}

	public void setHasError(Boolean hasError)
	{
		this.hasError = hasError;
	}
	
	
	public Boolean getSaved()
	{
		if (saved == null) {
			saved = false;
		}
		return saved;
	}

	
	public void setSaved(Boolean saved)
	{
		this.saved = saved;
	}

	public List<SelectItem> getCdcfStatusList()
	{
		cdcfStatusList = new ArrayList<SelectItem>();
		
		cdcfStatusList.add(optionPending);
		if ("CDCF_PENDING".equals(selectedAwardHeader_q.getCdcf_status()))
				cdcfStatusList.add(optionProcessed);
		if (!"CDCF_PENDING".equals(selectedAwardHeader_q.getCdcf_status()))
			cdcfStatusList.add(optionGenerated);
		cdcfStatusList.add(optionNotSelected);
		cdcfStatusList.add(optionSpecial);
		return cdcfStatusList;
	}
	
	public void setCdcfStatusList(List<SelectItem> cdcfStatusList)
	{
		this.cdcfStatusList = cdcfStatusList;
	}
	
	public List<AwardDetails_P> getAwardList()
	{
		if (awardList == null && canViewRIList()) {
			awardList = awardDao.getAwardDetails_P_byStaffNo(staffDetail.getStaff_number(), "M");
			if (!Strings.isNullOrEmpty(getParamConsent())) {
				if (!"all".equals(paramConsent)) {
					awardList = awardList.stream()
							.filter(y -> paramConsent.equals(y.getAwardDetails_q().getConsent_ind()) && y.getAwardHeader_q().getPublish_freq() > 0)
							.collect(Collectors.toList());
				}
			}else {
				awardList.removeIf(y -> "N".equals(y.getAwardDetails_q().getCreator_ind()) && y.getAwardHeader_q().getPublish_freq() < 1);
			}
		}
		return awardList;
	}
	
	public long getTotalCount() {
		if (getAwardList() != null) {
			return awardList.stream().count();
		}else {
			return 0;
		}	
    }
	
	public List<AwardDetails_P> getAwardDetails_p_list()
	{
		if (awardDetails_p_list == null) {
			awardDetails_p_list = new ArrayList<AwardDetails_P>();
			if (!Strings.isNullOrEmpty(paramNo)) {
				awardDetails_p_list = awardDao.getAwardDetails_P(Integer.valueOf(paramNo), getParamDataLevel());
				for (AwardDetails_P d:awardDetails_p_list) {
					if ("F".equals(d.getFlag())){
						StaffPast tmp = staffDao.getPastStaffDetailsByStaffNo(d.getStaff_no());
						if (tmp != null) {
							d.setRecipient_name(tmp.getFullname_display());
						}else {
							d.setRecipient_name("");
						}
					}
					if ("N".equals(d.getFlag())){
						StaffIdentity tmp = staffDao.getStaffDetailsByStaffNo(d.getStaff_no());
						if (tmp != null) {
							d.setRecipient_name(tmp.getFullname_display());
						}else {
							d.setRecipient_name("");
						}
					}
				}
			}
			else if (getSelectedImportAward() != null) {
				List<String> cnList = Arrays.asList(selectedImportAward.getRecipient_id_list().split(";"));
		    	for(String cn : cnList) {
		    		StaffIdentity staff = null;
					AwardDetails_P tmp = new AwardDetails_P();
			    	tmp.getPk().setData_level("M");
			    	tmp.setFlag("F");
			    	for(StaffIdentity id : getStaffNameList()) {
			    		if(id.getCn().equals(cn)) {
			    			tmp.setFlag("N");
			    			staff = id;
			    			break;
			    		}
			    	}
			    	if(staff != null) {
				    	tmp.setStaff_no(staff.getStaff_number());
				    	awardDetails_p_list.add(tmp);
			    	}
		    	}
			}
			else {
				if (getIsAsst() || getIsRdoAdmin()) {
					staffDetail = getStaffDetail(getParamPid(), null, true);
					//check is Former Staff
					staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
					if (staffPastDetail != null) {
						staffDetail = new StaffIdentity();
						staffDetail.setPid(staffPastDetail.getPid());
						staffDetail.setStaff_number(staffPastDetail.getStaff_number());
						staffDetail.setCn(staffPastDetail.getCn());
						staffDetail.setStaffType("F");
					}
				}else {
					staffDetail = getStaffDetail(null, null, false);
				}
				AwardDetails_P tmp = new AwardDetails_P();
				if (staffDetail != null) {
			    	tmp.getPk().setData_level("M");
			    	if ("F".equals(staffDetail.getStaffType())){
			    		tmp.setFlag("F");
			    	}else {
			    		tmp.setFlag("N");
			    	}
			    	tmp.setStaff_no(String.valueOf(staffDetail.getStaff_number()));
				}
		    	awardDetails_p_list.add(tmp);
			}
		}
		return awardDetails_p_list;
	}

	public void setAwardDetails_p_list(List<AwardDetails_P> awardDetails_p_list)
	{
		this.awardDetails_p_list = awardDetails_p_list;
	}
	
	public AwardHeader_P getSelectedAwardHeader_p() throws ParseException
	{
		if (selectedAwardHeader_p == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), getParamDataLevel());
				selectedAwardHeader_p = (selectedAwardHeader_p != null)?selectedAwardHeader_p:new AwardHeader_P();
			}else {
				selectedAwardHeader_p = new AwardHeader_P();
				if(getSelectedImportAward() != null) {
					selectedAwardHeader_p.setAward_name(selectedImportAward.getAward_name());
					selectedAwardHeader_p.setOrg_name(selectedImportAward.getOrg_name());
					selectedAwardHeader_p.setFull_desc(selectedImportAward.getFull_desc());
					if(StringUtils.isNotBlank(selectedImportAward.getAward_day()))
						selectedAwardHeader_p.setAward_day(Integer.parseInt(selectedImportAward.getAward_day()));
					selectedAwardHeader_p.setAward_month(Integer.parseInt(selectedImportAward.getAward_month()));
					selectedAwardHeader_p.setAward_year(Integer.parseInt(selectedImportAward.getAward_year()));
				}
			}
			if (selectedAwardHeader_p.getAward_year() == null) {
				selectedAwardHeader_p.setAward_year(getCurrentYear());
			}
		}
		return selectedAwardHeader_p;
	}

	
	public void setSelectedAwardHeader_p(AwardHeader_P selectedAwardHeader_p)
	{
		this.selectedAwardHeader_p = selectedAwardHeader_p;
	}
	
	public AwardHeader_Q getSelectedAwardHeader_q()
	{
		if (selectedAwardHeader_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedAwardHeader_q= awardDao.getAwardHeader_Q(Integer.valueOf(paramNo));
				selectedAwardHeader_q = (selectedAwardHeader_q != null)?selectedAwardHeader_q:new AwardHeader_Q();
			}else {
				selectedAwardHeader_q = new AwardHeader_Q();
			}
			if ("Y".equals(selectedAwardHeader_q.getInst_verified_ind()) && selectedAwardHeader_q.getInst_verified_date() == null) {
				selectedAwardHeader_q.setInst_verified_date(getCurrentDate());
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getInst_display_ind())) {
				selectedAwardHeader_q.setInst_display_ind("Y");
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getInst_verified_ind())) {
				selectedAwardHeader_q.setInst_verified_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getCdcf_status())) {
				selectedAwardHeader_q.setCdcf_status("CDCF_PENDING");
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getCdcf_gen_ind())) {
				selectedAwardHeader_q.setCdcf_gen_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getCdcf_processed_ind())) {
				selectedAwardHeader_q.setCdcf_processed_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getCdcf_selected_ind())) {
				selectedAwardHeader_q.setCdcf_selected_ind("U");
			}
			if (Strings.isNullOrEmpty(selectedAwardHeader_q.getCdcf_changed_ind())) {
				selectedAwardHeader_q.setCdcf_changed_ind("N");
			}
			if (selectedAwardHeader_q.getPublish_freq() == null) {
				selectedAwardHeader_q.setPublish_freq(0);
			}
		}
		return selectedAwardHeader_q;
	}


	
	public void setSelectedAwardHeader_q(AwardHeader_Q selectedAwardHeader_q)
	{
		this.selectedAwardHeader_q = selectedAwardHeader_q;
	}

	public void setDisplyRI()
	{
		if (selectedAwardDetails_q != null) {
			if (!"Y".equals(selectedAwardDetails_q.getConsent_ind()))
				selectedAwardDetails_q.setDisplay_ind("N");
		}
	}
	
	public AwardDetails_Q getSelectedAwardDetails_q()
	{
		if (getIsAsst() || getIsRdoAdmin()) {
			staffDetail = getStaffDetail(getParamPid(), null, true);
			//check is Former Staff
			staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
			if (staffPastDetail != null) {
				staffDetail = new StaffIdentity();
				staffDetail.setPid(staffPastDetail.getPid());
				staffDetail.setStaff_number(staffPastDetail.getStaff_number());
				staffDetail.setCn(staffPastDetail.getCn());
				staffDetail.setStaffType("F");
			}
		}else {
			staffDetail = getStaffDetail(null, null, false);
		}
		if (staffDetail != null && selectedAwardDetails_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				List<AwardDetails_Q> tmp = awardDao.getAwardDetails_Q(Integer.valueOf(paramNo), staffDetail.getStaff_number());
				selectedAwardDetails_q = (!tmp.isEmpty())?tmp.get(0):null;
			}
			else if(getSelectedImportAward() != null) {
				riCreatorStaffNo = getSelectedImportAward().getPk().getStaff_number();
				selectedAwardDetails_q = new AwardDetails_Q();
				selectedAwardDetails_q.getPk().setStaff_no(getSelectedImportAward().getPk().getStaff_number());
				selectedAwardDetails_q.setCreator_ind("Y");
				selectedAwardDetails_q.setDisplay_ind("Y");
				selectedAwardDetails_q.setConsent_ind("Y");
			}
			if (selectedAwardDetails_q == null) {
				riCreatorStaffNo = staffDetail.getStaff_number();
				selectedAwardDetails_q = new AwardDetails_Q();
				selectedAwardDetails_q.getPk().setStaff_no(staffDetail.getStaff_number());
				selectedAwardDetails_q.setCreator_ind("Y");
				selectedAwardDetails_q.setDisplay_ind("Y");
				selectedAwardDetails_q.setConsent_ind("Y");
			}
		}
		return selectedAwardDetails_q;
	}


	
	public void setSelectedAwardDetails_q(AwardDetails_Q selectedAwardDetails_q)
	{
		this.selectedAwardDetails_q = selectedAwardDetails_q;
	}


	
	public List<String> getDayList()
	{
		int month = (selectedAwardHeader_p.getAward_month() != null)?selectedAwardHeader_p.getAward_month():1;
		int year = (selectedAwardHeader_p.getAward_year() != null)?selectedAwardHeader_p.getAward_year():Calendar.getInstance().get(Calendar.YEAR);
		YearMonth yearMonthObject = YearMonth.of(year, month);
		int daysInMonth = yearMonthObject.lengthOfMonth();
		dayList = new ArrayList<>();
		for (int d = 1; d <= daysInMonth; d++) {
			dayList.add(String.valueOf(d));
		}
		return dayList;
	}


	public void moveColumnUp(int idx) throws SQLException
	{
		if (!getAwardDetails_p_list().isEmpty())
		{
			if (idx > 0)
			{
				AwardDetails_P tmp = awardDetails_p_list.get(idx-1);
				awardDetails_p_list.set(idx-1, awardDetails_p_list.get(idx));
				awardDetails_p_list.set(idx, tmp);
			}
		}
	}

	
	public void moveColumnDown(int idx) throws SQLException
	{
		if (getAwardDetails_p_list() != null)
		{
			if (idx+1 < awardDetails_p_list.size())
			{
				AwardDetails_P tmp = awardDetails_p_list.get(idx+1);
				awardDetails_p_list.set(idx+1, awardDetails_p_list.get(idx));
				awardDetails_p_list.set(idx, tmp);
			}
		}
	}	
	
	public void updateRowStaffNum(int idx) throws SQLException
	{
		if (awardDetails_p_list != null)
		{
			if (idx > -1)
			{
				AwardDetails_P tmp = awardDetails_p_list.get(idx);
				String staffNum = getPastStaffNumByStaffName(tmp.getRecipient_name());
				tmp.setStaff_no(staffNum);
				awardDetails_p_list.set(idx, tmp);
			}
		}
	}
	
	public void deleteRow(int idx) throws SQLException
	{
		if (!getAwardDetails_p_list().isEmpty())
		{
			if (idx < awardDetails_p_list.size()) awardDetails_p_list.remove(idx);
		}
	}
	public void addRow() throws SQLException
	{
		AwardDetails_P p = new AwardDetails_P();
		p.getPk().setAward_no(selectedAwardHeader_q.getAward_no());
		p.getPk().setData_level(getParamDataLevel());
		p.setFlag("N");
		if (!getAwardDetails_p_list().isEmpty())
		{
			awardDetails_p_list.add(p);
		}else {
			awardDetails_p_list = new ArrayList<AwardDetails_P>();
			awardDetails_p_list.add(p);
		}
	}
	
	public void save() throws Exception
	{
		if (selectedAwardHeader_p != null && selectedAwardHeader_q != null && selectedAwardDetails_q != null) {
			hasError = false;
			if ("M".equals(getParamDataLevel())) {
				save_m(false);
			}
			if ("P".equals(getParamDataLevel())) {
				save_p();
			}
			if ("C".equals(getParamDataLevel())) {
				save_c();
			}
		}
	}
	
	//save and publish
	public void saveAndPublishForm() throws IOException
	{
		if (selectedAwardHeader_p != null && selectedAwardHeader_q != null && selectedAwardDetails_q != null) {
			hasError = false;
			save_m(true);
			publish();
		}
	}
		
	//M level
	public void save_m(boolean doPublish)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		saved = false;
		try {
			selectedAwardHeader_q.setPublish_status("MODIFIED");
			selectedAwardHeader_q.setLast_modified_by(getLoginUserId());
			selectedAwardHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
			
			selectedAwardHeader_p.getPk().setData_level("M");
			
			selectedAwardHeader_p.setUserstamp(getLoginUserId());
			selectedAwardHeader_q.setUserstamp(getLoginUserId());
			selectedAwardDetails_q.setUserstamp(getLoginUserId());
			
			//Record still can be saved even there is error
			validateRequiredField();
			
			if (validateAwardDetails_P(staffDetail.getStaff_number())) {
				//Update P, Q
				selectedAwardHeader_q = awardDao.updateEntity(selectedAwardHeader_q);

				int currentAwardNo = selectedAwardHeader_q.getAward_no();
				
				selectedAwardDetails_q.getPk().setAward_no(currentAwardNo);
				selectedAwardDetails_q = awardDao.updateEntity(selectedAwardDetails_q);

				selectedAwardHeader_p.getPk().setAward_no(currentAwardNo);
				selectedAwardHeader_p = awardDao.updateEntity(selectedAwardHeader_p);
				
				//delete all recipient in M levels
				awardDao.deleteAllRecipient(currentAwardNo, "M");

				//Update award name list
				int line_no = 1;
				for(AwardDetails_P p:awardDetails_p_list) {
					p.getPk().setAward_no(currentAwardNo);
					p.getPk().setData_level("M");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getFlag().equals("N") && p.getStaff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setRecipient_name(nList.get(0));
						}
					}
					//set past staff details
					if (p.getFlag().equals("F") && p.getStaff_no() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setRecipient_name(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					awardDao.updateEntity(p);
					
					//Create record in awardDetails_q if has staff number
					if (p.getStaff_no() != null) {
						AwardDetails_Q tmpDetailsQ = awardDao.getAwardDetails_Q1(currentAwardNo, p.getStaff_no());
						if (tmpDetailsQ.getPk().getAward_no() == null) {
							AwardDetails_Q newDetailsQ = new AwardDetails_Q();
							newDetailsQ.getPk().setAward_no(currentAwardNo);
							newDetailsQ.getPk().setStaff_no(p.getStaff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							awardDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;		
				}
				//awardDetails_p_list = null;
				// update importStatus if it is imported
				updateSelectedImportStatus(currentAwardNo);
				
				// Success message
				String message = "msg.success.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				saved = true;
				if (!doPublish) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageAward_edit.xhtml?pid="+paramPid+"&no="+selectedAwardHeader_p.getPk().getAward_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  award (award_no=" + selectedAwardHeader_q.getAward_no() + ", staff_no="+ selectedAwardDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  award (award_no=" + selectedAwardHeader_q.getAward_no() + ", staff_no="+ selectedAwardDetails_q.getPk().getStaff_no() + ")", e);
		}
			
	}
	
	//P level
	public void save_p() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
				if ("Y".equals(selectedAwardHeader_q.getInst_verified_ind()) && selectedAwardHeader_q.getInst_verified_date() == null) {
					selectedAwardHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedAwardHeader_q.getCdcf_status())) {
					selectedAwardHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedAwardHeader_q.getCdcf_status())) {
					selectedAwardHeader_q.setCdcf_processed_ind("Y");
					selectedAwardHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedAwardHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedAwardHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedAwardHeader_q.getCdcf_status())) {
					selectedAwardHeader_q.setCdcf_processed_ind("Y");
					selectedAwardHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedAwardHeader_q.setCdcf_selected_ind("N");
				}
				selectedAwardHeader_q.setUserstamp(getLoginUserId());
				
				//Update Header Q
				selectedAwardHeader_q = awardDao.updateEntity(selectedAwardHeader_q);

				int currentAwardtNo = selectedAwardHeader_q.getAward_no();
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentAwardtNo);
				

				// Success message
				String message;
				if ("CDCF_PROCESSED".equals(selectedAwardHeader_q.getCdcf_status())) {
					takeSnapshot();
					awardDao.publishToSAP(selectedAwardHeader_q.getAward_no(), "RH_UPLOAD_AWARD_P");
					message = "msg.success.save.generate.x";
				}else {
					message = "msg.success.save.x";
				}
				message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  award (award_no=" + selectedAwardHeader_q.getAward_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  award (award_no=" + selectedAwardHeader_q.getAward_no() + ")", e);
		}
	}	
	
	//C level
	public void save_c() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
			validateRequiredField();
			
			if (validateAwardDetails_P(getRealRiCreatorStaffNo()) && !getHasError()) {
				if ("Y".equals(selectedAwardHeader_q.getInst_verified_ind()) && selectedAwardHeader_q.getInst_verified_date() == null) {
					selectedAwardHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedAwardHeader_q.getCdcf_status())) {
					selectedAwardHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedAwardHeader_q.getCdcf_status())) {
					selectedAwardHeader_q.setCdcf_processed_ind("Y");
					selectedAwardHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedAwardHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedAwardHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedAwardHeader_q.getCdcf_status())) {
					selectedAwardHeader_q.setCdcf_processed_ind("Y");
					selectedAwardHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedAwardHeader_q.setCdcf_selected_ind("N");
				}
				
				selectedAwardHeader_q.setUserstamp(getLoginUserId());
				
				//Update P, Q
				selectedAwardHeader_q = awardDao.updateEntity(selectedAwardHeader_q);
				
				selectedAwardHeader_p.setUserstamp(getLoginUserId());
				selectedAwardHeader_p = awardDao.updateEntity(selectedAwardHeader_p);
				
				awardDao.deleteAllRecipient(selectedAwardHeader_q.getAward_no(), "C");
				int line_no = 1;
				for (AwardDetails_P p:awardDetails_p_list) {
					p.getPk().setData_level("C");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getFlag().equals("N") && p.getStaff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setRecipient_name(nList.get(0));
						}
					}
					//set past staff details
					if (p.getFlag().equals("F") && p.getStaff_no() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setRecipient_name(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					awardDao.updateEntity(p);
					
					//Create record in awardDetails_q if has staff number
					if (p.getStaff_no() != null) {
						AwardDetails_Q tmpDetailsQ = awardDao.getAwardDetails_Q1(selectedAwardHeader_p.getPk().getAward_no(), p.getStaff_no());
						if (tmpDetailsQ.getPk().getAward_no() == null) {
							AwardDetails_Q newDetailsQ = new AwardDetails_Q();
							newDetailsQ.getPk().setAward_no(selectedAwardHeader_p.getPk().getAward_no());
							newDetailsQ.getPk().setStaff_no(p.getStaff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							awardDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;
				}
				
				int currentAwardtNo = selectedAwardHeader_q.getAward_no();
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentAwardtNo);
				
				// Success message
				String message;
				if ("CDCF_GENERATED".equals(selectedAwardHeader_q.getCdcf_status()) || "CDCF_PROCESSED".equals(selectedAwardHeader_q.getCdcf_status())) {
					awardDao.publishToSAP(selectedAwardHeader_q.getAward_no(), "RH_UPLOAD_AWARD_P");
					message = "msg.success.save.generate.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageAward_edit.xhtml?no="+currentAwardtNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageAward_edit.xhtml?no="+currentAwardtNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}
				
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  award (award_no=" + selectedAwardHeader_q.getAward_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  award (award_no=" + selectedAwardHeader_q.getAward_no() + ")", e);
		}
	}	
	
	public void publish()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (selectedAwardHeader_p != null && selectedAwardHeader_q != null && selectedAwardDetails_q != null) {
			try {
				//Get C level header
				if (getParamNo() != null) {
					AwardHeader_P selectedAwardHeader_p_c = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
					if (selectedAwardHeader_p_c != null) {
						selectedAwardHeader_q.setCdcf_changed_ind("Y");
					}
				}
				selectedAwardHeader_q.setPublish_status("PUBLISHED");
				int publishFreq = (selectedAwardHeader_q.getPublish_freq() != null)?selectedAwardHeader_q.getPublish_freq()+1:1;
				selectedAwardHeader_q.setPublish_freq(publishFreq);
				selectedAwardHeader_q.setLast_modified_by(getLoginUserId());
				selectedAwardHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
				selectedAwardHeader_q.setLast_published_by(getLoginUserId());
				selectedAwardHeader_q.setLast_published_date(Timestamp.from(Instant.now()));
				
				selectedAwardDetails_q.setUserstamp(getLoginUserId());
				
				//if (validateAwardDetails_P(awardDetails_p_list, staffDetail.getStaff_number())) {
				if (!getHasError()) {	
					//Update P, Q
					selectedAwardHeader_q = awardDao.updateEntity(selectedAwardHeader_q);

					int currentAwardNo = selectedAwardHeader_q.getAward_no();
					
					selectedAwardDetails_q.getPk().setAward_no(currentAwardNo);
					selectedAwardDetails_q = awardDao.updateEntity(selectedAwardDetails_q);

					selectedAwardHeader_p.getPk().setAward_no(currentAwardNo);
					selectedAwardHeader_p = awardDao.updateEntity(selectedAwardHeader_p);
					
					AwardHeader_P selectedAwardHeader_p2 = selectedAwardHeader_p;
					selectedAwardHeader_p2.getPk().setData_level("P");
					selectedAwardHeader_p2.setUserstamp(getLoginUserId());
					AwardHeader_P awardHeader_p_publish = awardDao.getAwardHeader_P(selectedAwardHeader_p.getPk().getAward_no(), "P");
					if (awardHeader_p_publish != null) {
						awardDao.deleteEntity(AwardHeader_P.class, awardHeader_p_publish.getPk());
						//selectedAwardHeader_p2 = awardDao.updateEntity(selectedAwardHeader_p2);
						//java.sql.Timestamp sqlDate = new java.sql.Timestamp(awardHeader_p_publish.getCreationDate().getTime());
						//awardDao.updateCreateDetails(awardHeader_p_publish.getPk().getAward_no(), awardHeader_p_publish.getCreator(), sqlDate);
					}
					selectedAwardHeader_p2 = awardDao.updateEntity(selectedAwardHeader_p2);

					//delete all recipient in M and P levels
					//awardDao.deleteAllRecipient(currentAwardNo, "M");
					awardDao.deleteAllRecipient(currentAwardNo, "P");
					//Update award name list
					int line_no = 1;
					for(AwardDetails_P p:awardDetails_p_list) {
						/*p.getPk().setAward_no(currentAwardNo);
						p.getPk().setData_level("M");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getFlag().equals("N") && p.getStaff_no() != null) {
							staffNameList = getStaffNameList();
							List<String> nList = staffNameList.stream()
									.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
									.map(x -> x.getFullname() + " " + x.getChinesename())
									.collect(Collectors.toList());
							if (!nList.isEmpty()) {
								p.setRecipient_name(nList.get(0));
							}
						}
						//set past staff details
						if (p.getFlag().equals("F") && p.getStaff_no() != null) {
							staffPastList = getStaffPastList();
							List<String> fList = staffPastList.stream()
									.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
									.map(x -> x.getFullname())
									.collect(Collectors.toList());
							if (!fList.isEmpty()) {
								p.setRecipient_name(fList.get(0));
							}
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						awardDao.updateEntity(p);*/
						AwardDetails_P p2 = p;
						p2.getPk().setData_level("P");
						awardDao.updateEntity(p2);
						//Create record in awardDetails_q if has staff number
						if (p.getStaff_no() != null) {
							AwardDetails_Q tmpDetailsQ = awardDao.getAwardDetails_Q1(currentAwardNo, p.getStaff_no());
							if (tmpDetailsQ.getPk().getAward_no() == null) {
								AwardDetails_Q newDetailsQ = new AwardDetails_Q();
								newDetailsQ.getPk().setAward_no(currentAwardNo);
								newDetailsQ.getPk().setStaff_no(p.getStaff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								awardDao.updateEntity(newDetailsQ);
							}
						}
						line_no++;		
					}
					//awardDetails_p_list = null;

					// Success message
					String message = "msg.success.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}else {
					// Failed message
					String message = "msg.err.data.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				}
				if (saved) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageAward_edit.xhtml?pid="+paramPid+"&no="+selectedAwardHeader_p.getPk().getAward_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit award (award_no=" + selectedAwardHeader_q.getAward_no() + ", staff_no="+ selectedAwardDetails_q.getPk().getStaff_no() + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit award (award_no=" + selectedAwardHeader_q.getAward_no() + ", staff_no="+ selectedAwardDetails_q.getPk().getStaff_no() + ")", e);
			}
		}
	}
	
	public void submitConsent()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		if ( selectedAwardDetails_q != null) {
			try {
				selectedAwardDetails_q.setUserstamp(getLoginUserId());
				selectedAwardDetails_q = awardDao.updateEntity(selectedAwardDetails_q);
					
					// Success message
					String message = "msg.success.submit.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Consent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedAwardDetails_q + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedAwardDetails_q + ")", e);
			}
		}
	}	
	
	

	public String takeSnapshot()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String destUrl = "";
		if (selectedAwardHeader_p != null && awardDetails_p_list != null && selectedAwardHeader_q != null) {
			try {
				int currentRiNo = selectedAwardHeader_p.getPk().getAward_no();
				AwardHeader_P selectedAwardHeader_p_c = awardDao.getAwardHeader_P(currentRiNo, "C");
				if (selectedAwardHeader_p_c != null) {
					awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p_c.getPk());
				}
				selectedAwardHeader_p_c = selectedAwardHeader_p;
				selectedAwardHeader_p_c.getPk().setData_level("C");
				selectedAwardHeader_p_c.setUserstamp(getLoginUserId());
				selectedAwardHeader_p_c.setCreator(getLoginUserId());
				selectedAwardHeader_p = awardDao.updateEntity(selectedAwardHeader_p_c);
				
				awardDao.deleteAllRecipient(currentRiNo, "C");
				for (AwardDetails_P p:awardDetails_p_list) {
					AwardDetails_P p2 = p;
					p2.getPk().setData_level("C");
					p2.setCreator(getLoginUserId());
					p2.setUserstamp(getLoginUserId());
					awardDao.updateEntity(p2);
				}
				
				if ("CDCF_GENERATED".equals(selectedAwardHeader_q.getCdcf_status())) {
					awardDao.publishToSAP(selectedAwardHeader_q.getAward_no(), "RH_UPLOAD_AWARD_P");
				}
				
				// Success message
				String message = "msg.success.create.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Snapshot");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				destUrl = redirect("manageAward_edit") + "&no=" + currentRiNo + "&dataLevel=C";	
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Award No.:" + selectedAwardHeader_p.getPk().getAward_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Award No.:" + selectedAwardHeader_p.getPk().getAward_no() + ")", e);
			}
		}
		return redirect(destUrl);
	}	
	
	//Delete all levels
	public String deleteForm() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (!Strings.isNullOrEmpty(paramNo)) {
			try {
				//Get C level header
				AwardHeader_P selectedAwardHeader_p_c = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
				
				//In M level page
				/*if ("M".equals(getParamDataLevel())) {
					if (selectedAwardHeader_p_c == null) {
						//P
						selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "P");
						if (selectedAwardHeader_p != null) {
							awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p.getPk());
						}
						//M
						AwardHeader_P selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedAwardHeader_p != null) {
							awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p.getPk());
						}
						
						//M and P levels
						awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "M");
						awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "P");
						
						//Header Q and Details Q
						awardDao.deleteEntity(AwardHeader_Q.class, Integer.valueOf(paramNo));
						awardDao.deleteAwardDetails_Q(Integer.valueOf(paramNo));
							
						// Success message
						String message = "msg.success.delete.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}else {
						String message = "Award is already generated to SAP, so it can not be deleted. ";
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					}
				}*/
				//In P or C level page
				/*if ("P".equals(getParamDataLevel()) || "C".equals(getParamDataLevel())){
					//C
					if (selectedAwardHeader_p_c != null) {
						awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p_c.getPk());
					}
					//P
					selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "P");
					if (selectedAwardHeader_p != null) {
						awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p.getPk());
					}
					//M
					AwardHeader_P selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "M");
					if (selectedAwardHeader_p != null) {
						awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p.getPk());
					}
					
					//M and P and C levels
					awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "M");
					awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "P");
					awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "C");
					
					//Header Q and Details Q
					awardDao.deleteEntity(AwardHeader_Q.class, Integer.valueOf(paramNo));
					awardDao.deleteAwardDetails_Q(Integer.valueOf(paramNo));
					
					// Success message
					String message = "msg.success.delete.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}*/
				//C
				if (selectedAwardHeader_p_c != null) {
					awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p_c.getPk());
				}
				//P
				selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "P");
				if (selectedAwardHeader_p != null) {
					awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p.getPk());
				}
				//M
				AwardHeader_P selectedAwardHeader_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "M");
				if (selectedAwardHeader_p != null) {
					awardDao.deleteEntity(AwardHeader_P.class, selectedAwardHeader_p.getPk());
				}
				
				//M and P and C levels
				awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "M");
				awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "P");
				awardDao.deleteAllRecipient(Integer.valueOf(paramNo), "C");
				
				//Header Q and Details Q
				awardDao.deleteEntity(AwardHeader_Q.class, Integer.valueOf(paramNo));
				awardDao.deleteAwardDetails_Q(Integer.valueOf(paramNo));
				
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Prize/Award");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete award (No.: " + selectedAwardHeader_q.getAward_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete award (No.: " + selectedAwardHeader_q.getAward_no() + ")", e);
			}
		}
		String destUrl = redirect("manageAward");
		if(paramPid != null)
			destUrl += "&pid=" + paramPid;
		return redirect(destUrl);
	}
	
	public String getRealRiCreatorStaffNo()
	{
		AwardDetails_Q tmp = awardDao.getAwardDetails_Q_creator(Integer.valueOf(paramNo));
		if (tmp != null) {
			return tmp.getPk().getStaff_no();
		}
		return null;
	}
	
	public String getRiCreatorStaffNo()
	{
		if (Strings.isNullOrEmpty(riCreatorStaffNo)) {
			AwardDetails_Q tmp = awardDao.getAwardDetails_Q_creator(Integer.valueOf(paramNo));
			riCreatorStaffNo = (tmp!= null)?tmp.getPk().getStaff_no():getCurrentUserId();
		}
		return riCreatorStaffNo;
	}

	
	public void setRiCreatorStaffNo(String riCreatorStaffNo)
	{
		this.riCreatorStaffNo = riCreatorStaffNo;
	}

	public boolean isRiCreator(String staff_no) 
	{
		boolean result = false;
		String realRiCreatorStaffNo = ("M".equals(getParamDataLevel()))? getRiCreatorStaffNo():getRealRiCreatorStaffNo();
		if (!Strings.isNullOrEmpty(staff_no) && !Strings.isNullOrEmpty(realRiCreatorStaffNo)) {
			result = (realRiCreatorStaffNo.equals(staff_no))?true:false;
		}
		return result;
	}	
	
	public boolean checkSnapshotExists() 
	{
		boolean result = false;
		AwardHeader_P tmp = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
		if (tmp != null) {
			result = true;
		}
		return result;	
	}

	//validate name list
	public boolean validateAwardDetails_P(String staff_no){
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		boolean yourself = false;
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		int countAuthor = 0;
		HashSet unique=new HashSet();
		if (!Strings.isNullOrEmpty(staff_no)) {
			for (AwardDetails_P p:awardDetails_p_list) {
				int lineNo = countAuthor + 1;
				
				
				//get staff details
				if (p.getFlag().equals("N")) {
					if (p.getStaff_no()== null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Recipient (no. "+lineNo+")");
						allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Recipient (no. "+lineNo+")")+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getStaff_no());
						if (s != null) {
							p.setRecipient_name(s.getFullname_save());
						}	
					}
				}
				//get past staff details
				if (p.getFlag().equals("F")) {
					if (p.getRecipient_name() == null) {
						result = false;
						message = "Recipient (no. "+lineNo+") is not correct.";
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getStaff_no());
						if (sp != null) {
							//p.setStaff_no(sp.getStaff_number());
							//p.setRecipient_name(sp.getFullname_save());
						}else {
							result = false;
							//message = MessageFormat.format(getResourceBundle().getString(errMessage), "Recipient (no. "+lineNo+")");
							message = "Recipient (no. "+lineNo+") is not correct.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
				}
				
				//check name is not null
				if (p.getFlag().equals("Y") || p.getFlag().equals("S")) {
					if (Strings.isNullOrEmpty(p.getRecipient_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Recipient (no. "+lineNo+")");
						allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Recipient (no. "+lineNo+")")+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						byte[] nameBytes = p.getRecipient_name().getBytes(StandardCharsets.UTF_8);
						if (nameBytes.length > 80) {
							result = false;
							message = "Recipient (no. "+lineNo+") is too long.";
							allMessage += "- Recipient (no. "+lineNo+") is too long.<br/>";
							fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
						}else {
							p.setStaff_no(null);
						}	
					}
				}
				
				//check duplicate
				if (!unique.add(p.getStaff_no()) && p.getStaff_no() != null){
					result = false;
					if (!Strings.isNullOrEmpty(p.getRecipient_name())) {
						message = "Recipient - Staff ("+p.getRecipient_name()+") cannot be duplicated.";
						allMessage += "- Recipient - Staff ("+p.getRecipient_name()+") cannot be duplicated.<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						message = "Recipient cannot be duplicated.";
						allMessage += "- Recipient cannot be duplicated.<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
				}
				
				if (!Strings.isNullOrEmpty(p.getStaff_no())) {
					if (staff_no.equals(p.getStaff_no())) {
						yourself = true;
					}
				}
				
				countAuthor++;
			}
		}
		if (yourself == false && "M".equals(getParamDataLevel())) {		
			result = false;
			message = "You must be one of the recipient.";
			allMessage += "- You must be one of the recipient.<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (yourself == false && "M".equals(getParamDataLevel()) == false) {		
			result = false;
			message = "Creator must be one of the recipient.";
			allMessage += "- You must be one of the recipient.<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
		
	//validate mandatory field	
	public Boolean validateRequiredField() {
		boolean result = true;
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (Strings.isNullOrEmpty(selectedAwardHeader_p.getAward_name())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Prize/Award");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Prize/Award")+"<br/>";
			fCtx.addMessage("editForm:award_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedAwardHeader_p.setAward_name(selectedAwardHeader_p.getAward_name().trim());
		}
		if (Strings.isNullOrEmpty(selectedAwardHeader_p.getOrg_name())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Organization Conferring the Prize/Award");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Organization Conferring the Prize/Award")+"<br/>";
			fCtx.addMessage("editForm:award_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedAwardHeader_p.setOrg_name(selectedAwardHeader_p.getOrg_name().trim());
		}
		if (selectedAwardHeader_p.getAward_month()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Receipt - Month");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Receipt - Month")+"<br/>";
			fCtx.addMessage("editForm:awardMonth", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedAwardHeader_p.getAward_year()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Receipt - Year");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Receipt - Year")+"<br/>";
			fCtx.addMessage("editForm:awardYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}

	public ImportRIAward getAwardPanel()
	{
		if(awardPanel == null) {
			awardPanel = new ImportRIAward();
		}
		return awardPanel;
	}

	public void ignoreAward() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedAward = dao.getStatusByPK(getAwardPanel().getSelectedIgnoreAward());
		if(selectedAward != null) {
			selectedAward.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedAward);
			getAwardPanel().setAwardList(null);
		}
	}

	
	public ImportRIAwardV getSelectedImportAward()
	{
		if(selectedImportAward == null && getParamArea_code() != null
				&& getParamSource_id() != null && getParamStaff_number() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			ImportRIAwardV_PK pk = new ImportRIAwardV_PK();
			pk.setArea_code(getParamArea_code());
			pk.setSource_id(getParamSource_id());
			pk.setStaff_number(getParamStaff_number());
			selectedImportAward = dao.getImportRIAwardByPK(pk);
		}
		
		return selectedImportAward;
	}
}


