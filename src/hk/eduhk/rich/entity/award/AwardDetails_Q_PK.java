package hk.eduhk.rich.entity.award;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class AwardDetails_Q_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="award_no")
	private Integer award_no;
	
	@Column(name="staff_no")
	private String staff_no;

	
	public Integer getAward_no()
	{
		return award_no;
	}

	
	public void setAward_no(Integer award_no)
	{
		this.award_no = award_no;
	}

	
	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + award_no;
		result = prime * result + ((staff_no == null) ? 0 : staff_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AwardDetails_Q_PK other = (AwardDetails_Q_PK) obj;
		if (award_no != other.award_no)
			return false;
		if (staff_no == null)
		{
			if (other.staff_no != null)
				return false;
		}
		else if (!staff_no.equals(other.staff_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "AwardDetails_Q_PK [award_no=" + award_no + ", staff_no=" + staff_no + "]";
	}	

	

}
