package hk.eduhk.rich.entity.award;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.view.RISearchPanel;
import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.project.ProjectSummary;
import hk.eduhk.rich.entity.publication.OutputDetails_Q;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;

@SuppressWarnings("serial")
public class AwardDAO extends BaseDAO
{

	private static AwardDAO instance = null;


	public static synchronized AwardDAO getInstance()
	{
		if (instance == null) instance = new AwardDAO();
		return instance;
	}
	
	
	public static AwardDAO getCacheInstance()
	{
		return AwardDAO.getInstance();
	}
	
	public List<Award> getCVAwardList(int pid, String staffNo, String startDate, String endDate) throws SQLException
	{
		List<Award> voList = new ArrayList<Award>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
    
		try
		{
			conn = pm.getConnection();
			String query = "SELECT RECIPIENT_STAFF_NO, AWARD_NAME, ORG_NAME, AWARD_DAY, AWARD_MONTH, AWARD_YEAR, SHORT_DESC, FULL_DESC, H.AWARD_NO FROM RICH.RH_P_AWARD_HDR H LEFT JOIN RICH.RH_P_AWARD_DTL D ON H.AWARD_NO = D.AWARD_NO AND H.DATA_LEVEL = D.DATA_LEVEL "
					+ " LEFT JOIN RICH.RH_Q_AWARD_DTL QD ON H.AWARD_NO = QD.AWARD_NO AND QD.STAFF_NO = D.RECIPIENT_STAFF_NO "	
					+ " WHERE D.RECIPIENT_STAFF_NO = '"+staffNo+"' AND H.DATA_LEVEL = 'P' AND QD.DISPLAY_IND = 'Y' AND QD.CONSENT_IND = 'Y' ";
			
			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				query += " AND TO_DATE(NVL(H.AWARD_MONTH, '"+startMonthStr+"') || '/' || H.AWARD_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')";
			}
			query += " ORDER BY H.AWARD_YEAR DESC, H.AWARD_MONTH DESC, H.AWARD_DAY DESC";
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();

			while (rs.next())
			{
				Award vObj = new Award();
				vObj.setStaffNumber(rs.getString("RECIPIENT_STAFF_NO"));
				vObj.setAwardName(rs.getString("AWARD_NAME"));
				vObj.setOrgName(rs.getString("ORG_NAME"));
				vObj.setAwardDay(rs.getString("AWARD_DAY"));
				vObj.setAwardMonth(rs.getString("AWARD_MONTH"));
				vObj.setAwardYear(rs.getString("AWARD_YEAR"));
				vObj.setShortDesc(rs.getString("SHORT_DESC"));
				vObj.setFullDesc(rs.getString("FULL_DESC"));
				vObj.setRiNo(rs.getInt("AWARD_NO"));
				

				voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return voList;
	}
	
	public List<Award> getRIList(int pid, String staffNo, List<String> riNoList, boolean byStaff) throws SQLException
	{
		//20210127
		//if (searchObj == null) searchObj = new Award();
		List<Award> voList = new ArrayList<Award>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
    
		try
		{
			conn = pm.getConnection();

			String query = this.getRetrieveRIQuery(pid, staffNo, riNoList, byStaff);
			logger.log(Level.FINEST, "the search award query string is: " + query);

			//System.out.println("get award: " + query);      
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();

			while (rs.next())
			{
				Award vObj = new Award();
				vObj.setPid(rs.getInt("pid"));
				vObj.setStaffNumber(rs.getString("staff_number"));
				vObj.setStaffFullname(rs.getString("fullname"));
				vObj.setStaffDept(rs.getString("dept_code"));        
				vObj.setRINo(rs.getInt("award_no"));
				vObj.setAwardName(rs.getString("award_name"));
				vObj.setOrgName(rs.getString("org_name"));
				vObj.setAwardDay(rs.getString("award_day"));
				vObj.setAwardMonth(rs.getString("award_month"));
				vObj.setAwardYear(rs.getString("award_year"));
				vObj.setShortDesc(rs.getString("short_desc"));
				vObj.setFullDesc(rs.getString("full_desc"));
				vObj.setIEdWorkInd(rs.getString("ied_work_ind"));
				vObj.setCreator(rs.getString("creator"));
				vObj.setCreationDate(rs.getTimestamp("creation_time"));

				// HDR Status 
				vObj.setPublishStatus(rs.getString("publish_status"));
				vObj.setLastModifiedDate(rs.getTimestamp("last_modified_date"));
				vObj.setLastModifiedBy(rs.getString("last_modified_by"));
				vObj.setLastPublishedDate(rs.getTimestamp("last_published_date"));
				vObj.setLastPublishedBy(rs.getString("last_published_by"));
				vObj.setInstDisplayInd(rs.getString("inst_display_ind"));
				vObj.setInstVerifiedInd(rs.getString("inst_verified_ind"));
				vObj.setInstVerifiedDate(rs.getTimestamp("inst_verified_date"));
				vObj.setCDCFStatus(rs.getString("cdcf_status"));
				vObj.setCDCFGenInd(rs.getString("cdcf_gen_ind"));
				vObj.setCDCFGenDate(rs.getTimestamp("cdcf_gen_date"));
				vObj.setCDCFProcessInd(rs.getString("cdcf_processed_ind"));
				vObj.setCDCFProcessDate(rs.getTimestamp("cdcf_processed_date"));
				vObj.setCDCFSelectedInd(rs.getString("cdcf_selected_ind"));
				vObj.setCDCFChangedInd(rs.getString("cdcf_changed_ind"));
				vObj.setBulletinInd(rs.getString("bulletin_ind"));
				vObj.setRemarks(rs.getString("remarks"));
				vObj.setPublishFreq(rs.getInt("publish_freq"));

				// DTL Status
				vObj.setCreatorInd(rs.getString("creator_ind"));
				vObj.setDisplayInd(rs.getString("display_ind"));
				vObj.setConsentInd(rs.getString("consent_ind"));

				// Virtual field
				vObj.setCreatorPid(rs.getInt("creator_pid"));
				vObj.setCreatorName(rs.getString("creator_name"));
				vObj.setConcatNames(rs.getString("concat_names"));

				voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return voList;
	}
	
	private String getRetrieveRIQuery(int pid, String staffNo, List<String> riNoList, boolean byStaff)
	{
		boolean retrieveByRINoList = (riNoList != null && riNoList.size() > 0);

    // Check if the list by staff or by RI, when by staff, a special string
    // "BY_STAFF" is add at the end of the array list
    /*boolean byStaff = false;
    
    if(retrieveByRINoList && "BY_STAFF".equals(riNoList.get(riNoList.size()-1))) 
    {
        byStaff = true;
        // remove special code after checked   
        riNoList.remove(riNoList.size()-1);        
    }*/
    
		StringBuffer sqlBuf = new StringBuffer();

		sqlBuf.append("SELECT RI.* " +
									"FROM " +
									"( " +
									"  SELECT I.pid, I.fullname, i.dept_code, I.staff_number, C.creator_pid, c.creator_name, N.concat_names, H.*, " +
									"	 QH.publish_status, QH.last_modified_date, QH.last_modified_by, " +
									"	 QH.last_published_date, QH.last_published_by, " +
									"	 QH.inst_display_ind, QH.inst_verified_ind, QH.inst_verified_date, " +
									"	 QH.cdcf_status, QH.cdcf_gen_ind, QH.cdcf_gen_date, " +
									"	 QH.cdcf_processed_ind, QH.cdcf_processed_date, QH.cdcf_selected_ind, " +
									"	 QH.cdcf_changed_ind, QH.bulletin_ind, QH.remarks, QH.publish_freq, " +
									"	 QD.display_ind, QD.creator_ind, QD.consent_ind, D.line_no " +
									"  FROM " + this.getDataTableHDR() + " H, " + this.getDataTableDTL() + " D, " +
									"	 " + getStatusTableHDR() + " QH, " + getStatusTableDTL() + " QD, " +
									"	 (SELECT pid, fullname, dept_code, staff_number from RH_P_STAFF_IDENTITY UNION SELECT PID, FULLNAME, NULL, STAFF_NUMBER FROM RH_P_STAFF_EMPLOYMENT_PAST) I, " +
									"	 ( " +
									"		 SELECT award_no, pid AS creator_pid, fullname as creator_name FROM " + 
									"		 (" + 
									"			 SELECT award_no, pid, fullname, " + 
									"			 ROW_NUMBER() OVER (PARTITION BY award_no ORDER BY pid) AS idx, " + 
									"			 COUNT(*) OVER (PARTITION BY award_no) AS cnt " +                   
									"			 FROM " + getStatusTableDTL() + " iqd, (SELECT staff_number, PID, fullname  FROM RH_P_STAFF_IDENTITY  UNION  SELECT STAFF_NUMBER, PID, fullname FROM RH_P_STAFF_EMPLOYMENT_PAST) ii " + 
									"			 WHERE iqd.staff_no = ii.staff_number AND pid is not null AND creator_ind='Y' " + 
									"			) " +
									"			WHERE idx = cnt " +
									"  ) C, " + // C captures the creator pid
                  "	 ( " +
									"		  SELECT award_no, SUBSTR(SYS_CONNECT_BY_PATH(name, '; '), 2) AS concat_names " +
									"		  FROM " +
									"		  ( " +
									"				SELECT award_no, line_no, " +
									"				CASE " +
                  "					WHEN NVL(non_ied_staff_flag,'X') = 'S' THEN d.recipient_name || ' [Student]' " +
									"					WHEN dept_code IS NULL THEN d.recipient_name " +
									"					ELSE i.fullname || ' [' || i.dept_code || ']' " +
									"				END AS name, " +
									"		    COUNT (*) OVER (PARTITION BY award_no) AS cnt " +
									"				FROM " + this.getDataTableDTL() + " D, RH_P_STAFF_IDENTITY I " +
									"				WHERE D.recipient_staff_no = I.staff_number (+) " +
									"		  ) " +
									"		  WHERE line_no = cnt " +
									"		  START WITH line_no = 1 " +
									"		  CONNECT BY PRIOR line_no = line_no - 1 AND PRIOR award_no = award_no " +
									"	 ) N	" + // N captures the concat names of the RI
									"  WHERE H.award_no = D.award_no (+) " +
									"	 AND H.award_no = QH.award_no (+) " +
									"	 AND D.award_no = QD.award_no (+) " +
									"	 AND D.award_no = C.award_no (+) " +
									"	 AND D.award_no = N.award_no (+) " +
									" 	 AND H.data_level = D.data_level " +
									" 	 AND H.data_level = 'P' " +
									"	 AND D.recipient_staff_no = QD.staff_no (+) " +
									"  AND d.recipient_staff_no = I.staff_number (+) " +
									" AND QD.CONSENT_IND <> 'N' AND QD.DISPLAY_IND <> 'N' " +
									") RI " +
									"WHERE 1=1 ");
		//20210127
		//if (riNo > 0) sqlBuf.append("AND award_no=" + riNo + " ");
		if (pid > 0) sqlBuf.append("AND pid=" + pid + " ");
		if (!GenericValidator.isBlankOrNull(staffNo)) sqlBuf.append("AND staff_number='" + escapeSql(staffNo) + "' ");
		//20210127
		/*if (!GenericValidator.isBlankOrNull(searchObj.getPublishStatus())) sqlBuf.append("AND publish_status='" + escapeSql(searchObj.getPublishStatus()) + "' ");

		if (searchObj.getCreatorPid() > 0)
		{
			sqlBuf.append("AND pid=" + searchObj.getCreatorPid() + " " +
										"AND (creator_ind = 'Y' OR publish_freq >= 1) ");
		}*/

		if (retrieveByRINoList)
		{
			sqlBuf.append("AND award_no IN (");
			sqlBuf.append(getSQLIdSet(getDataTableHDR(), "award_no", riNoList, false));
			sqlBuf.append(")");
      //if(!byStaff) sqlBuf.append("AND creator_ind = 'Y' ");
      if(!byStaff) {
        // retrieve first ied staff
        sqlBuf.append("AND line_no = (SELECT MIN(LINE_NO) FROM " + this.getDataTableDTL() +" SH WHERE SH.AWARD_NO = RI.AWARD_NO AND NON_IED_STAFF_FLAG IN ('N','F') )");
      }
      if(byStaff) sqlBuf.append("AND pid is not null ");                    
    }
		//20210127
		/*if (searchObj instanceof BaseRIFull)
		{
			BaseRIFull fullSearch = (BaseRIFull) searchObj;
      if (!GenericValidator.isBlankOrNull(fullSearch.getDisplayInd())) sqlBuf.append("AND display_ind='" + escapeSql(fullSearch.getDisplayInd()) + "' ");
			if (!GenericValidator.isBlankOrNull(fullSearch.getConsentInd())) sqlBuf.append("AND consent_ind='" + escapeSql(fullSearch.getConsentInd()) + "' ");
			if (!GenericValidator.isBlankOrNull(fullSearch.getCreatorInd())) sqlBuf.append("AND creator_ind='" + escapeSql(fullSearch.getCreatorInd()) + "' ");
		}*/

		// Order by the actual order of pid in the List.
		if (retrieveByRINoList && !byStaff)
		{
			sqlBuf.append("ORDER BY DECODE(award_no");
			for (int n=0;n<riNoList.size();n++) sqlBuf.append("," + riNoList.get(n) + "," + n);
			sqlBuf.append(",9999999)");
		}

		// Default order.
		else
		{
			sqlBuf.append("ORDER BY fullname, award_year desc, award_month desc, short_desc ");
		}

		//System.out.println("awardList: " + sqlBuf);
		return sqlBuf.toString();
	}	
	
	protected String getDataTableHDR()
	{
		return "RH_P_AWARD_HDR";
	}
	
	protected String getDataTableDTL()
	{
		return "RH_P_AWARD_DTL";
	}


	protected String getStatusTableHDR()
	{
		return "RH_Q_AWARD_HDR";
	}


	protected String getStatusTableDTL()
	{
		return "RH_Q_AWARD_DTL";
	}	
	
	public static String escapeSql(String str)
	{
         if (str == null) {
                return null;
          }
         return StringUtils.replace(str, "'", "''");
	}	
	
	public static String getSQLIdSet(String tableName, String id, Collection idCol, boolean withQuoteChar)
	{
		StringBuffer queryBuf = new StringBuffer();
		StringBuffer idBuf = new StringBuffer();

		if (idCol != null)
		{
			int numOfIds = idCol.size();

			int n = 0;
			Iterator idIter = idCol.iterator();
			while (idIter.hasNext())
			{
				n++;
				String Id = (String) idIter.next();
				if (idBuf.length() > 0) idBuf.append(",");
				if (withQuoteChar) idBuf.append("'");
				idBuf.append(escapeSql(Id));
				if (withQuoteChar) idBuf.append("'");

				// Construct a sub-query for every 500 Ids.
				if (n % 500 == 0 || n == numOfIds)
				{
					if (queryBuf.length() > 0) queryBuf.append(" UNION ");
					queryBuf.append("SELECT DISTINCT " + id + " FROM " + tableName + " WHERE " + id + " IN (" + idBuf + ")");
					idBuf.delete(0, idBuf.length());
				}
			}
		}

		return queryBuf.toString();
	}	
	
	public List<AwardHeader> getAwardList()
	{
		List<AwardHeader> objList = null;
		

			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				
				String query = "SELECT obj FROM AwardHeader obj ORDER BY obj.award_no ";
				
				TypedQuery<AwardHeader> q = em.createQuery(query, AwardHeader.class);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		
		
		return objList;
	}
	
	public List<AwardDetails> getAwardDetails(int award_no){
		List<AwardDetails> objList = new ArrayList<AwardDetails>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		if (award_no > 0) {
			try
			{
				String query = "SELECT * FROM RH_PURE_AWARD_DTL_V WHERE award_no = ? ORDER BY display_order";
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setInt(1, award_no);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					AwardDetails d = new AwardDetails();
					d.setAward_no(rs.getInt("award_no"));
					d.setPerson_source_id(rs.getString("person_source_id"));
					d.setRole(rs.getString("role"));
					d.setExternal_ind(rs.getString("external_ind"));
					d.setFirst_name(rs.getString("first_name"));
					d.setLast_name(rs.getString("last_name"));
					objList.add(d);
				}
				
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
	
	return objList;		
	}
	
	public List<AwardDetails_P> getAwardDetails_P_byStaffNo(String staff_no, String data_level)
	{
		List<AwardDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AwardDetails_P obj WHERE obj.staff_no = :staff_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.award_no DESC";			
			TypedQuery<AwardDetails_P> q = em.createQuery(query, AwardDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<AwardDetails_P> getAwardDetails_P_byStaffNo_consent(String staff_no, String data_level)
	{
		List<AwardDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AwardDetails_P obj WHERE obj.staff_no = :staff_no AND obj.pk.data_level = :data_level "+ 
									" AND obj.awardHeader_q.publish_freq > :publish_freq  AND obj.awardDetails_q.consent_ind = :consent_ind ORDER BY obj.pk.award_no DESC";			
			TypedQuery<AwardDetails_P> q = em.createQuery(query, AwardDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			q.setParameter("publish_freq", 0);
			q.setParameter("consent_ind", "U");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<AwardDetails_P> getAwardDetails_P(Integer award_no, String data_level)
	{
		List<AwardDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AwardDetails_P obj WHERE obj.pk.award_no = :award_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.line_no";			
			TypedQuery<AwardDetails_P> q = em.createQuery(query, AwardDetails_P.class);
			q.setParameter("award_no", award_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	

	public List<AwardDetails_Q> getAwardDetails_Q(Integer award_no, String staff_no)
	{
		List<AwardDetails_Q> objList = null;
		EntityManager em = null;		
		String awardNo = (award_no > 0)? " AND obj.pk.award_no = :award_no ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AwardDetails_Q obj WHERE obj.pk.staff_no = :staff_no " + awardNo;			
			TypedQuery<AwardDetails_Q> q = em.createQuery(query, AwardDetails_Q.class);
			if (award_no > 0) {
				q.setParameter("award_no", award_no);
			}
			q.setParameter("staff_no", staff_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<AwardHeader_P> getAwardHeader_P_list(List<Integer> awardNoList)
	{
		List<AwardHeader_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AwardHeader_P obj WHERE obj.pk.award_no IN :awardNoList ORDER BY obj.pk.award_no ";			
			TypedQuery<AwardHeader_P> q = em.createQuery(query, AwardHeader_P.class);
			q.setParameter("awardNoList", awardNoList);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public AwardHeader_P getAwardHeader_P (Integer award_no, String data_level) 
	{
		List<AwardHeader_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AwardHeader_P obj WHERE obj.pk.award_no = :award_no AND obj.pk.data_level = :data_level";			
			TypedQuery<AwardHeader_P> q = em.createQuery(query, AwardHeader_P.class);
			q.setParameter("award_no", award_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	
	
	public AwardHeader_Q getAwardHeader_Q (Integer award_no) 
	{
		AwardHeader_Q obj = null;
		if (award_no > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = pm.getEntityManager();
				obj = em.find(AwardHeader_Q.class, award_no);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public AwardDetails_Q getAwardDetails_Q1 (Integer award_no, String staffNo) 
	{
		List<AwardDetails_Q> objList = null;
		EntityManager em = null;
		if (award_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM AwardDetails_Q obj WHERE obj.pk.award_no = :award_no AND obj.pk.staff_no = :staffNo";			
				TypedQuery<AwardDetails_Q> q = em.createQuery(query, AwardDetails_Q.class);
				q.setParameter("award_no", award_no);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):new AwardDetails_Q();
	}	

	public AwardDetails_Q getAwardDetails_Q_creator (Integer award_no) 
	{
		List<AwardDetails_Q> objList = null;
		EntityManager em = null;
		if (award_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM AwardDetails_Q obj WHERE obj.pk.award_no = :award_no AND obj.creator_ind = :creator_ind";			
				TypedQuery<AwardDetails_Q> q = em.createQuery(query, AwardDetails_Q.class);
				q.setParameter("award_no", award_no);
				q.setParameter("creator_ind", "Y");
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public void deleteAllRecipient(int award_no, String data_level) throws Exception
	{
		if (award_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM AwardDetails_P obj WHERE obj.pk.award_no = :award_no AND obj.pk.data_level = :data_level ");
				q.setParameter("award_no", award_no);
				q.setParameter("data_level", data_level);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete recipients (award_no=" + award_no + ", data_level="+ data_level + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteAwardDetails_Q(int award_no) throws Exception
	{
		if (award_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM AwardDetails_Q obj WHERE obj.pk.award_no = :award_no ");
				q.setParameter("award_no", award_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete AwardDetails_Q (award_no=" + award_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public List<Award> getAwardListByIds(List<Integer> idList, RISearchPanel searchPanel) throws SQLException
	{
		List<Award> voList = new ArrayList<Award>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && searchPanel != null) {
			String listingType = searchPanel.getListingType();
			String dataLevel = searchPanel.getViewType();
			String sortCol = searchPanel.getSortCol();
			String sortOrder = searchPanel.getSortOrder();
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
				
				    sqlBuf.append(
				    		" SELECT PH.AWARD_NO, " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" (CASE WHEN STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME ELSE EXSTAFF.FULLNAME END) AS STAFF_NAME, " + 
				    		" PD.RECIPIENT_STAFF_NO, " + 
				    		" STAFF.DEPT_CODE AS DEPARTMENT, " );
				    }
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.RECIPIENT_LIST"+i+" AS RECIPIENT_LIST"+i+", ");
				    }
				    sqlBuf.append(
				    		// 2.x
				    		" (CASE WHEN IED_WORK_IND = 'Y' THEN 'Yes' WHEN IED_WORK_IND = 'N' THEN 'No' ELSE '' END) AS IED_WORK_IND, " + 
				    		// 4.x
				    		" AWARD_NAME, " + 
				    		" ORG_NAME, " + 
				    		" SHORT_DESC, " + 
				    		" FULL_DESC, " + 
				    		" (CASE WHEN AWARD_DAY IS NOT NULL THEN (AWARD_DAY || '/') ELSE '' END) || AWARD_MONTH || '/' || AWARD_YEAR AS AWARDDATE, " +
				    		// manageInstituteRI
				    		" CDCF_STATUS, " +
				    		// add for sorting
				    		" AWARD_YEAR, " +
				    		" AWARD_MONTH, " +
				    		" AWARD_DAY, " +
				    		" (CASE WHEN CDCF_SELECTED_IND = 'Y' THEN 'Yes' WHEN CDCF_SELECTED_IND = 'N' THEN 'No' ELSE '' END) AS CDCF_SELECTED_IND " + 
				    		" FROM RH_P_AWARD_HDR PH  " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_P_AWARD_DTL PD ON (PD.AWARD_NO = PH.AWARD_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " );
				    }
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_AWARD_HDR QH ON QH.AWARD_NO = PH.AWARD_NO  " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_Q_AWARD_DTL QD ON (QD.AWARD_NO = PH.AWARD_NO AND QD.STAFF_NO = PD.RECIPIENT_STAFF_NO)  " + 
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.RECIPIENT_STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.RECIPIENT_STAFF_NO "); 
				    }
				    sqlBuf.append(
				    		" LEFT JOIN ( " + 
				    		" SELECT PH.AWARD_NO, " );
		    		for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" LISTAGG( " + 
					    		" CASE WHEN (PD.RECIPIENT_STAFF_NO IS NOT NULL OR PD.RECIPIENT_NAME IS NOT NULL) " + 
					    		" AND (LINE_NO <= " + i*30 + 
					    		" AND LINE_NO > " + (i*30 - 30) + " ) " +
					    		" THEN ( " + 
					    		" (CASE WHEN PD.RECIPIENT_STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
					    		" WHEN PD.RECIPIENT_STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
					    		" ELSE PD.RECIPIENT_NAME END) " + 
					    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " + 
					    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END) " + 
					    		" ) ");
					    if(i == listNum) {
						    sqlBuf.append(
						    		" WHEN LINE_NO = (" + i*30 +
						    		")+1 THEN '...' ");}
					    sqlBuf.append(
					    		" ELSE '' END , '<br/>') " + 
					    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS RECIPIENT_LIST" + i + " ");
					    if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM RH_P_AWARD_HDR PH  " + 
				    		" LEFT JOIN RH_P_AWARD_DTL PD ON (PD.AWARD_NO = PH.AWARD_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " +  
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.RECIPIENT_STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.RECIPIENT_STAFF_NO  " + 
				    		" WHERE PH.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PH.AWARD_NO " + 
				    		" ) TMP ON PH.AWARD_NO = TMP.AWARD_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
		    		if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
		    			sqlBuf.append(
		    				" AND PD.RECIPIENT_STAFF_NO IS NOT NULL" );
		    		}
				    sqlBuf.append(
				    		" AND PH.AWARD_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
				
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
					logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						Award vObj = new Award();
						vObj.setRINo(rs.getInt("AWARD_NO"));
						if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
							vObj.setStaffFullname(rs.getString("STAFF_NAME"));
							vObj.setStaffNumber(rs.getString("RECIPIENT_STAFF_NO"));
							vObj.setStaffDept(rs.getString("DEPARTMENT"));
						}
						String recipient_list = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("RECIPIENT_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									recipient_list += "<br/>" ;
								recipient_list += authListSeg;
							}
						}
						vObj.setRecipient_list(recipient_list);
						// 2.x
						vObj.setIEdWorkInd(rs.getString("IED_WORK_IND"));
						// 4.x
						vObj.setAwardName(rs.getString("AWARD_NAME"));
						vObj.setOrgName(rs.getString("ORG_NAME"));
						vObj.setShortDesc(rs.getString("SHORT_DESC"));
						vObj.setFullDesc(rs.getString("FULL_DESC"));
						vObj.setAwardDate(rs.getString("AWARDDATE"));
						// manageInstituteRI
						vObj.setCDCFStatus(rs.getString("CDCF_STATUS"));
						// for sorting
						vObj.setAwardYear(rs.getString("AWARD_YEAR"));
						vObj.setAwardMonth(rs.getString("AWARD_MONTH"));
						vObj.setAwardDay(rs.getString("AWARD_DAY"));
						vObj.setCdcf_selected_ind(rs.getString("CDCF_SELECTED_IND"));
						
						voList.add(vObj);
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			if(voList != null && !voList.isEmpty()) {
				if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_RI_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Award::getRINo));
					else
						voList.sort(Comparator.comparing(Award::getRINo).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
			    	Comparator<Award> nameComp = new Comparator<Award>() {
			    		@Override public int compare(final Award record1, final Award record2) {
			    		    int c = 0;
			    		    if (c == 0 && record1.getStaffFullname() != null && record2.getStaffFullname() != null)
			    		    	c = record1.getStaffFullname().compareTo(record2.getStaffFullname());
			    		    else if (record1.getStaffFullname() == null) return 1;
			    		    else if (record2.getStaffFullname() == null) return -1;
			    		    if (c == 0 && record1.getAwardYear() != null && record2.getAwardYear() != null)
			    		       c = Integer.valueOf(record1.getAwardYear()).compareTo(Integer.valueOf(record2.getAwardYear()));
			    		    if (c == 0 && record1.getAwardMonth() != null && record2.getAwardMonth() != null)
			    		       c = Integer.valueOf(record1.getAwardMonth()).compareTo(Integer.valueOf(record2.getAwardMonth()));
			    		    if (c == 0 && record1.getAwardDay() != null && record2.getAwardDay() != null)
			    		       c = Integer.valueOf(record1.getAwardDay()).compareTo(Integer.valueOf(record2.getAwardDay()));
			    		    return c;
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, nameComp);
					else
						Collections.sort(voList, nameComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NO_VALUE)) {
			    	if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Award::getRINo, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Award::getRINo, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NAME_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Award::getAwardName, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Award::getAwardName, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CONTRIBUTOR_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Award::getRecipient_list, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Award::getRecipient_list, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_FROM_DATE_VALUE)) {
					Comparator<Award> awardDayComp = new Comparator<Award>() {
			    		@Override public int compare(final Award record1, final Award record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getAwardYear() == null ? "1000" : record1.getAwardYear();
				    		    String record1Month = record1.getAwardMonth() == null ? "1" : record1.getAwardMonth();
				    		    String record1Day = record1.getAwardDay() == null ? "1" : record1.getAwardDay();
				    		    String record1NullLast = record1.getAwardDay() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse(record1Day + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getAwardYear() == null ? "1000" : record2.getAwardYear();
				    		    String record2Month = record2.getAwardMonth() == null ? "1" : record2.getAwardMonth();
				    		    String record2Day = record2.getAwardDay() == null ? "1" : record2.getAwardDay();
				    		    String record2NullLast = record2.getAwardDay() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse(record2Day + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getAwardListByIds having non number award date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, awardDayComp);
					else
						Collections.sort(voList, awardDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_TO_DATE_VALUE)) {
					Comparator<Award> awardDayComp = new Comparator<Award>() {
			    		@Override public int compare(final Award record1, final Award record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getAwardYear() == null ? "1000" : record1.getAwardYear();
				    		    String record1Month = record1.getAwardMonth() == null ? "1" : record1.getAwardMonth();
				    		    String record1Day = record1.getAwardDay() == null ? "1" : record1.getAwardDay();
				    		    String record1NullLast = record1.getAwardDay() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse(record1Day + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getAwardYear() == null ? "1000" : record2.getAwardYear();
				    		    String record2Month = record2.getAwardMonth() == null ? "1" : record2.getAwardMonth();
				    		    String record2Day = record2.getAwardDay() == null ? "1" : record2.getAwardDay();
				    		    String record2NullLast = record2.getAwardDay() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse(record2Day + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getAwardListByIds having non number award date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, awardDayComp);
					else
						Collections.sort(voList, awardDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CDCF_STAT_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Award::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Award::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
			}
		}
		    return voList;
	}
	
	
	public List<Integer> getAwardNoListByDept(List<String> deptList, String data_level) throws SQLException
	{
		List<Integer> voList = new ArrayList<Integer>();
		
		if(deptList != null && data_level != null) {
			if (deptList.size() > 0) {
				PersistenceManager pm = PersistenceManager.getInstance();
				Connection conn = null;
				PreparedStatement pStmt = null;
			try
			{
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
			
			    sqlBuf.append(
			    		" SELECT DISTINCT PD.AWARD_NO " );
			    sqlBuf.append(
			    		" FROM RH_P_AWARD_DTL PD  " +
			    		" LEFT JOIN RH_P_STAFF_IDENTITY_DEPT STAFF_DEPT ON (PD.RECIPIENT_STAFF_NO = STAFF_DEPT.STAFF_NUMBER) " );
			    sqlBuf.append(
			    		" WHERE  " + 
			    		" PD.DATA_LEVEL = '" + data_level + "' ");
			    sqlBuf.append(
			    		" AND STAFF_DEPT.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
	
				while (rs.next())
				{
					voList.add(rs.getInt("AWARD_NO"));
				}
			}
			
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			}
		}
		return voList;
	}
	
	public List<AwardDetails_P> getAwardDetails_P_byRiNo(List<List<Integer>> riNosParts, String data_level)
	{
		List<AwardDetails_P> objList = null;
		EntityManager em = null;	
		String where = "";
		try
		{
			em = getEntityManager();	
			if (!riNosParts.isEmpty()) {
				for (int i = 0; i < riNosParts.size(); i++) {
					if (i == 0) {
						where += " AND ( ";
					}
					where += " obj.pk.award_no IN :riNos"+i;
					if (i == riNosParts.size() - 1) {
						where += " ) ";
					}else {
						where += " OR ";
					}
				}
				
				String query = "SELECT obj FROM AwardDetails_P obj WHERE obj.pk.data_level = :data_level AND obj.pk.line_no = :line_no " + where +
									" ORDER BY obj.awardHeader_p.award_year DESC, obj.awardHeader_p.award_month DESC ";			
				TypedQuery<AwardDetails_P> q = em.createQuery(query, AwardDetails_P.class);
				if (!riNosParts.isEmpty()) {
					for (int i = 0; i < riNosParts.size(); i++) {
						q.setParameter("riNos"+i, riNosParts.get(i));
					}
				}
				q.setParameter("data_level", data_level);
				q.setParameter("line_no", 1);
				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<Summary> getAwardSummaryCountList(String staffNo, String dataLevel, String startDate, String endDate, List<String> deptList){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (deptList != null) {
				selectDept =  "SD.DEPT_CODE_EACH, ";
			}
			sqlBuf.append("SELECT RP.PERIOD_ID, RP.PERIOD_DESC, " + selectDept + "COUNT(DISTINCT(H.AWARD_NO)) AS C FROM RICH.RH_P_AWARD_HDR H"
					+ "    LEFT JOIN RICH.RH_P_AWARD_DTL D ON H.AWARD_NO = D.AWARD_NO AND H.DATA_LEVEL = D.DATA_LEVEL"
					+ "    LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT SD ON D.RECIPIENT_STAFF_NO = SD.STAFF_NUMBER "
					+ "	   LEFT JOIN RICH.RH_Q_AWARD_DTL QD ON H.AWARD_NO = QD.AWARD_NO "
					+ "    LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON "
					+ "        TO_DATE(NVL(H.AWARD_MONTH, '01') || '/' || H.AWARD_YEAR, 'MM/YYYY') BETWEEN  "
					+ "        TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY')");
			sqlBuf.append(" WHERE H.DATA_LEVEL = '"+dataLevel+"' AND QD.CONSENT_IND = 'Y' ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND D.RECIPIENT_STAFF_NO = '"+staffNo+"' ");
			}

			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(H.AWARD_MONTH, '"+startMonthStr+"') || '/' || H.AWARD_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE_EACH "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, SD.DEPT_CODE_EACH ");
			}else {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC ");
			}
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next())
				{
					Summary obj = new Summary();
					obj.setPeriod_id(rs.getString("PERIOD_ID"));
					obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
					obj.setCount(rs.getString("C"));
					if (deptList != null) {
						obj.setFacDept(rs.getString("DEPT_CODE_EACH"));
					}
					objList.add(obj);						
				}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<AwardReport> getAwardReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		List<AwardReport> objList = new ArrayList<AwardReport>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(  "select distinct a.*, max(pdc.line_no) over (partition by pdc.award_no) as NO_RECIPIENTS  " + 
					" from ( " + 
					"        SELECT PH.AWARD_NO, " + 
					"        CONCAT(CONCAT(S.LAST_NAME, ', '),S.FIRST_NAME) AS EMPLOYEE_NAME, " + 
					"        S.EMPLOYEE_NUMBER, " + 
					"        S.DEPARTMENT_CODE, " + 
					"        S.DCC,  " + 
					"        S.DCC_PERCENTAGE, " + 
					"        S.STAFF_GRADE, " + 
					"        TO_DATE(NVL(PH.AWARD_DAY, '1') || '/' ||PH.AWARD_MONTH || '/' ||PH.AWARD_YEAR, 'DD/MM/YYYY') AS AWARD_DATE, " + 
					"        PH.AWARD_NAME, " + 
					"        PH.ORG_NAME, " + 
					"        QH.CDCF_STATUS " + 
					"        from RICH.RH_P_AWARD_DTL PD " + 
					"        LEFT JOIN RICH.RH_P_AWARD_HDR PH ON PD.AWARD_NO = PH.AWARD_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL " + 
					"        LEFT JOIN RICH.RH_Q_AWARD_DTL QD ON PD.AWARD_NO = QD.AWARD_NO  " + 
					"        LEFT JOIN RICH.RH_Q_AWARD_HDR QH ON PD.AWARD_NO = QH.AWARD_NO  " + 
					"        RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.RECIPIENT_STAFF_NO  ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QD.CONSENT_IND = 'Y' AND QH.CDCF_STATUS != 'CDCF_PENDING'  "
					+ " AND TO_DATE(NVL(PH.AWARD_MONTH, '01') || '/' || PH.AWARD_YEAR, 'MM/YYYY') BETWEEN  "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (deptList != null) {
				
				
				sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
				
				/*
				sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				
				List<String> specialDeptList = deptList.stream().filter(o->o.contains("&")).collect(Collectors.toList());
				
				if(specialDeptList != null) {
					sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					
					sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
					
					for(String specialDept :specialDeptList)
						sqlBuf.append(" OR S.DEPARTMENT_CODE LIKE '"+specialDept+"%' " ) ; 
					
					sqlBuf.append(")");
				}
				else
					sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				*/
			}
			sqlBuf.append("	GROUP BY PH.AWARD_NO, " + 
					"        CONCAT(CONCAT(S.LAST_NAME, ', '),S.FIRST_NAME),  " + 
					"        S.EMPLOYEE_NUMBER,S.DEPARTMENT_CODE, S.DCC,DCC_PERCENTAGE, S.STAFF_GRADE, TO_DATE(NVL(PH.AWARD_DAY, '1') || '/' ||PH.AWARD_MONTH || '/' ||PH.AWARD_YEAR, 'DD/MM/YYYY'), PH.AWARD_NAME, PH.ORG_NAME, " + 
					"        QH.CDCF_STATUS "
					+ "	ORDER BY CONCAT(CONCAT(S.LAST_NAME, ', '),S.FIRST_NAME), S.DEPARTMENT_CODE, S.DCC, S.STAFF_GRADE, TO_DATE(NVL(PH.AWARD_DAY, '1') || '/' ||PH.AWARD_MONTH || '/' ||PH.AWARD_YEAR, 'DD/MM/YYYY'), PH.AWARD_NAME, PH.ORG_NAME ");
			sqlBuf.append(" ) a  inner join RICH.RH_P_AWARD_DTL pdc on pdc.AWARD_NO = a.AWARD_NO AND pdc.DATA_LEVEL = 'C' ");
			
			//logger.log(Level.FINEST, sqlBuf.toString());
			//System.out.println("SQL :" + sqlBuf.toString() );
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			
			
			while (rs.next())
			{
				AwardReport obj = new AwardReport();
				obj.setAward_no(rs.getInt("AWARD_NO"));
				obj.setEmployee_name(rs.getString("EMPLOYEE_NAME"));
				obj.setStaff_number(rs.getString("EMPLOYEE_NUMBER"));
				obj.setDept(rs.getString("DEPARTMENT_CODE"));
				obj.setDcc(rs.getInt("DCC"));
				obj.setDcc_percentage(rs.getDouble("DCC_PERCENTAGE"));
				obj.setStaff_grade(rs.getString("STAFF_GRADE"));
				obj.setGranted_date(rs.getDate("AWARD_DATE"));
				obj.setAward_name(rs.getString("AWARD_NAME"));
				obj.setOrg_name(rs.getString("ORG_NAME"));
				obj.setCdcf_status(rs.getString("CDCF_STATUS"));
				obj.setNo_recipients(rs.getInt("NO_RECIPIENTS"));
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objList;
	}
	
	/*public void updateCreateDetails(Integer award_no, String creator, Timestamp creationTime) throws Exception
	{
		if (award_no != null)
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			UserTransaction utx = null;
			try
			{
				utx = pm.getUserTransaction();
				utx.begin();			
				conn = pm.getConnection();
				
				String sql = "UPDATE RH_P_AWARD_HDR SET creator = ?, creation_time = ? WHERE award_no = ? AND data_level = ?";
				pStmt = conn.prepareStatement(sql);
				pStmt.setString(1, creator);
				pStmt.setTimestamp(2, creationTime);	
				pStmt.setInt(3, award_no);
				pStmt.setString(4, "P");
				pStmt.executeQuery();
				utx.commit();
			}
			catch (Exception e)
			{
				throw e;
			}			
			finally
			{
				pm.close(em);
			}	
		}
	}*/
}
