package hk.eduhk.rich.entity.award;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import hk.eduhk.rich.entity.BaseRIFull;

@SuppressWarnings("serial")
public class AwardReport
{

	private Integer award_no;
	private String staff_number;
	private String employee_name;
	private String dept;
	
	private Integer dcc;
	private Double dcc_percentage;
	
	private String staff_grade;
	private Date granted_date;
	private String award_name;
	private String org_name;
	private String cdcf_status;
	private Integer no_recipients;
	
	
	
	
	public String getEmployee_name()
	{
		return employee_name;
	}
	
	public void setEmployee_name(String employee_name)
	{
		this.employee_name = employee_name;
	}
	
	public String getDept()
	{
		return dept;
	}
	
	public void setDept(String dept)
	{
		this.dept = dept;
	}
	
	public Integer getDcc()
	{
		return dcc;
	}
	
	public void setDcc(Integer dcc)
	{
		this.dcc = dcc;
	}
	
	public String getStaff_grade()
	{
		return staff_grade;
	}
	
	public void setStaff_grade(String staff_grade)
	{
		this.staff_grade = staff_grade;
	}
	
	public Date getGranted_date()
	{
		return granted_date;
	}
	
	public void setGranted_date(Date granted_date)
	{
		this.granted_date = granted_date;
	}
	
	public String getAward_name()
	{
		return award_name;
	}
	
	public void setAward_name(String award_name)
	{
		this.award_name = award_name;
	}
	
	public String getOrg_name()
	{
		return org_name;
	}
	
	public void setOrg_name(String org_name)
	{
		this.org_name = org_name;
	}
	

	
	public Integer getAward_no()
	{
		return award_no;
	}

	
	public void setAward_no(Integer award_no)
	{
		this.award_no = award_no;
	}

	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public Double getDcc_percentage()
	{
		return dcc_percentage;
	}

	
	public void setDcc_percentage(Double dcc_percentage)
	{
		this.dcc_percentage = dcc_percentage;
	}

	
	public String getCdcf_status()
	{
		return cdcf_status;
	}

	
	public void setCdcf_status(String cdcf_status)
	{
		this.cdcf_status = cdcf_status;
	}
	
	

	
	public Integer getNo_recipients()
	{
		return no_recipients;
	}

	
	public void setNo_recipients(Integer no_recipients)
	{
		this.no_recipients = no_recipients;
	}

	@Override
	public String toString()
	{
		return "AwardReport [award_no=" + award_no + ", staff_number=" + staff_number + ", employee_name="
				+ employee_name + ", dept=" + dept + ", dcc=" + dcc + ", dcc_percentage=" + dcc_percentage
				+ ", staff_grade=" + staff_grade + ", granted_date=" + granted_date + ", award_name=" + award_name
				+ ", org_name=" + org_name + ", cdcf_status=" + cdcf_status + ", no_recipients=" + no_recipients + "]";
	}

}