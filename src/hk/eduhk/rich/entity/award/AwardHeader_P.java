package hk.eduhk.rich.entity.award;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_AWARD_HDR")
public class AwardHeader_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(AwardHeader_P.class.toString());
	
	@EmbeddedId
	private AwardHeader_P_PK pk = new AwardHeader_P_PK();
	
	@Column(name = "award_name")
	private String award_name;

	@Column(name = "org_name")
	private String org_name;	
	
	@Column(name = "short_desc")
	private String short_desc;

	@Column(name = "full_desc")
	private String full_desc;

	@Column(name = "award_year")
	private Integer award_year;
	
	@Column(name = "award_month")
	private Integer award_month;		
	
	@Column(name = "award_day")
	private Integer award_day;		
	
	@Column(name = "ied_work_ind")
	private String ied_work_ind;


	public AwardHeader_P_PK getPk()
	{
		return pk;
	}


	
	public void setPk(AwardHeader_P_PK pk)
	{
		this.pk = pk;
	}


	public String getAward_name()
	{
		return award_name;
	}

	
	public void setAward_name(String award_name)
	{
		this.award_name = award_name;
	}

	
	public String getOrg_name()
	{
		return org_name;
	}

	
	public void setOrg_name(String org_name)
	{
		this.org_name = org_name;
	}

	
	public String getShort_desc()
	{
		return short_desc;
	}

	
	public void setShort_desc(String short_desc)
	{
		this.short_desc = short_desc;
	}

	
	public String getFull_desc()
	{
		return full_desc;
	}

	
	public void setFull_desc(String full_desc)
	{
		this.full_desc = full_desc;
	}

	
	public Integer getAward_year()
	{
		return award_year;
	}

	
	public void setAward_year(Integer award_year)
	{
		this.award_year = award_year;
	}

	
	public Integer getAward_month()
	{
		return award_month;
	}

	
	public void setAward_month(Integer award_month)
	{
		this.award_month = award_month;
	}

	
	public Integer getAward_day()
	{
		return award_day;
	}

	
	public void setAward_day(Integer award_day)
	{
		this.award_day = award_day;
	}

	
	public String getIed_work_ind()
	{
		return ied_work_ind;
	}

	
	public void setIed_work_ind(String ied_work_ind)
	{
		this.ied_work_ind = ied_work_ind;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AwardHeader_P other = (AwardHeader_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "AwardHeader_P [pk=" + pk + ", award_name=" + award_name + ", org_name=" + org_name + ", short_desc="
				+ short_desc + ", full_desc=" + full_desc + ", award_year=" + award_year + ", award_month="
				+ award_month + ", award_day=" + award_day + ", ied_work_ind=" + ied_work_ind + "]";
	}


}
