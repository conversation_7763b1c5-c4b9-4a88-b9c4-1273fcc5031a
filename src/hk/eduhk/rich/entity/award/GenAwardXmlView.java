package hk.eduhk.rich.entity.award;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;


import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.io.IOUtils;
import org.jdom2.*;
import org.jdom2.output.*;
import org.jdom2.output.support.*;
import org.jdom2.transform.JDOMResult;
import org.jdom2.transform.JDOMSource;

import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.util.MimeMap;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.BaseView;


@SuppressWarnings("serial")
@ManagedBean(name = "genAwardXmlView")
@ViewScoped
public class GenAwardXmlView extends BaseView
{
	private Logger logger = Logger.getLogger(getClass().getName());
	public String createAwardXmlFile()
	{		
		String xmlString = "";
		try
		{
			Document doc=new Document();
			//Root Element
			Namespace ns1 = Namespace.getNamespace("v1.prize-sync.pure.atira.dk");	
			Namespace ns2 = Namespace.getNamespace("v3", "v3.commons.pure.atira.dk");
			
			Element root=new Element("prizes", ns1);
			root.addNamespaceDeclaration(ns2);
			doc.setRootElement(root);
					
			AwardDAO dao = AwardDAO.getInstance();
			List<AwardHeader> awardList = dao.getAwardList();
			for(AwardHeader a:awardList) {
				//Prize Element
				Element prize=new Element("prize", ns1);
				prize.setAttribute("id", "a"+a.getAward_no());
				prize.setAttribute("type", "prize");
				
				//Prize title
				Element title=new Element("title", ns1);
				title.addContent(a.getAward_name());
				prize.addContent(title);
				
				//Prize description
				Element description=new Element("description", ns1);
				description.addContent(a.getDescription());
				prize.addContent(description);
				
				//grantingOrganisations
				Element grantingOrganisations=new Element("grantingOrganisations", ns1);
				Element organisation=new Element("organisation", ns1);
				organisation.setAttribute("lookupId", "a"+a.getAward_no()+"_org1");
				organisation.setAttribute("origin", "external");
				Element organisation_name=new Element("name", ns1);
				organisation_name.addContent(a.getOrg_name());
				organisation.addContent(organisation_name);
				grantingOrganisations.addContent(organisation);
				prize.addContent(grantingOrganisations);
				
				//get award details
				List<AwardDetails> details = dao.getAwardDetails(a.getAward_no());
				
				//Receivers of the prize
				Element receivers=new Element("receiversOfPrize", ns1);
				int countReceiver = 1;
				for(AwardDetails d:details) {
					Element receiver=new Element("receiverOfPrize", ns1);
					receiver.setAttribute("id", "a" + a.getAward_no()+"_reciever"+countReceiver);
				
					Element person=new Element("person", ns1);
					
					//If external receiver, show firstName and ListName
					if (d.getExternal_ind().equals("Y")) {
						person.setAttribute("origin", "external");
						
						//First name
						Element receiver_firstName=new Element("firstName", ns1);
						receiver_firstName.addContent(d.getFirst_name());
						person.addContent(receiver_firstName);
						
						//Last name
						Element receiver_lastName=new Element("lastName", ns1);
						receiver_lastName.addContent(d.getLast_name());
						person.addContent(receiver_lastName);
					}else {
						person.setAttribute("lookupId", d.getPerson_source_id());
					}
					
					Element personRole=new Element("personRole", ns1);
					personRole.addContent(d.getRole());
					receiver.addContent(person);
					receiver.addContent(personRole);
					receivers.addContent(receiver);
					countReceiver++;
				}
				
				prize.addContent(receivers);
				
				//managedBy
				Element managedBy =new Element("managedBy", ns1);
				managedBy.setAttribute("lookupId", "u00001");
				prize.addContent(managedBy);
				
				//Prize awarded date
				Element awardedDate =new Element("awardedDate", ns1);
				
				Element year =new Element("year", ns2);
				Element month =new Element("month", ns2);
				Element day =new Element("day", ns2);
				
				if(a.getAward_year() != null) {
					year.addContent(a.getAward_year().toString());
					awardedDate.addContent(year);
				}
				if(a.getAward_month() != null) {
					month.addContent(a.getAward_month().toString());
					awardedDate.addContent(month);
				}
				if(a.getAward_day() != null) {
					day.addContent(a.getAward_day().toString());
					awardedDate.addContent(day);
				}
				prize.addContent(awardedDate);
				
				//visibility
				Element visibility = new Element("visibility", ns1);
				visibility.addContent(a.getVisibility());
				prize.addContent(visibility);
				
				//Add in the root Element
				root.addContent(prize);		
			}
			xmlString = transform(doc);
		}
		catch (TransformerFactoryConfigurationError | TransformerException e)
		{
			logger.log(Level.WARNING, "Cannot generate xml", e);
		}
		return xmlString;

		
		//return new XMLOutputter().outputString(doc);
		/*String fileName = "pureAward.xml";
		XMLOutputter outter=new XMLOutputter(Format.getPrettyFormat(), XMLOUTPUT);
		outter.output(doc, new FileWriter("D:"+File.separator+fileName));*/
	}
	
	private String transform(Document sourceDoc) throws TransformerException {
        JDOMSource source = new JDOMSource(sourceDoc);
        StreamResult result = new StreamResult(new StringWriter());

        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
        transformer.transform(source, result);

        return result.getWriter().toString();
    }
	
	private String getXmlFilePath()
	{
		String code = Constant.isLocalEnv() ? SysParam.PARAM_FILE_PATH_LOCAL : SysParam.PARAM_FILE_PATH;
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		return paramDAO.getSysParamValueByCode(code);
	}
	
	public String getSysParamValue(String code) {
		String value = "";
		SysParamDAO sysParamDao = SysParamDAO.getInstance();
		SysParam tmp = sysParamDao.getSysParamByCode(code);
		if (tmp != null) {
			value = tmp.getValue();
		}
		return value;
	}
	
	public String skipInValidXMLChars(String in) {
        StringBuffer out = new StringBuffer();
        char current;

        if (in == null || ("".equals(in))) return "";
        for (int i = 0; i < in.length(); i++) {
            current = in.charAt(i);
            if ((current == 0x9) ||
                (current == 0xA) ||
                (current == 0xD) ||
                ((current >= 0x20) && (current <= 0xD7FF)) ||
                ((current >= 0xE000) && (current <= 0xFFFD)) ||
                ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }  
	
	public static final XMLOutputProcessor XMLOUTPUT = new AbstractXMLOutputProcessor() {
	    @Override
	    protected void printDeclaration(final Writer out, final FormatStack fstack) throws IOException {
	        write(out, "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?> ");
	        write(out, fstack.getLineSeparator());
	    }
	};
	
}
