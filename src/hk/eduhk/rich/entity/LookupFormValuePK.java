package hk.eduhk.rich.entity;

import java.io.Serializable;

import javax.persistence.Column;

public class LookupFormValuePK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "form_code")
	private String  formCode;
	
	@Column(name = "lookup_type")
	private String lookupType;

	
	public String getFormCode()
	{
		return formCode;
	}

	
	public void setFormCode(String formCode)
	{
		this.formCode = formCode;
	}

	
	public String getLookupType()
	{
		return lookupType;
	}

	
	public void setLookupType(String lookupType)
	{
		this.lookupType = lookupType;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((formCode == null) ? 0 : formCode.hashCode());
		result = prime * result + ((lookupType == null) ? 0 : lookupType.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupFormValuePK other = (LookupFormValuePK) obj;
		if (formCode == null)
		{
			if (other.formCode != null)
				return false;
		}
		else if (!formCode.equals(other.formCode))
			return false;
		if (lookupType == null)
		{
			if (other.lookupType != null)
				return false;
		}
		else if (!lookupType.equals(other.lookupType))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupFormValuePK [formCode=" + formCode + ", lookupType=" + lookupType + "]";
	}
	
}
