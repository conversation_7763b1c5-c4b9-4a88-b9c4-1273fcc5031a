package hk.eduhk.rich.entity;

import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import hk.eduhk.rich.UserPersistenceObject;

@SuppressWarnings("serial")
@Entity
@Table(name = "RH_S_JNL_RANK")
public class JournalRank extends UserPersistenceObject{
	
	@Id
	@Column(name = "RANK_ID")
	private int rank_id;
	
	@Column(name = "TITLE")
	private String title;
	
	@Column(name = "ISSN_1")
	private String issn_1;
	
	@Column(name = "ISSN_2")
	private String issn_2;
	
	@Column(name = "ISSN_3")
	private String issn_3;
	
	@Column(name = "RANK")
	private String rank;
	
	@Column(name = "JCR")
	private String jcr;
	
	@Column(name = "SJR")
	private String sjr;

	@Column(name = "YEAR")
	private String year;
	
	
	
	public int getRank_id()
	{
		return rank_id;
	}


	
	public void setRank_id(int rank_id)
	{
		this.rank_id = rank_id;
	}


	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getIssn_1()
	{
		return issn_1;
	}

	
	public void setIssn_1(String issn_1)
	{
		this.issn_1 = issn_1;
	}

	
	public String getIssn_2()
	{
		return issn_2;
	}

	
	public void setIssn_2(String issn_2)
	{
		this.issn_2 = issn_2;
	}

	
	public String getIssn_3()
	{
		return issn_3;
	}

	
	public void setIssn_3(String issn_3)
	{
		this.issn_3 = issn_3;
	}

	
	public String getRank()
	{
		return rank;
	}

	
	public void setRank(String rank)
	{
		this.rank = rank;
	}

	
	public String getJcr()
	{
		return jcr;
	}

	
	public void setJcr(String jcr)
	{
		this.jcr = jcr;
	}

	
	public String getSjr()
	{
		return sjr;
	}

	
	public void setSjr(String sjr)
	{
		this.sjr = sjr;
	}


	
	public String getYear()
	{
		return year;
	}


	
	public void setYear(String year)
	{
		this.year = year;
	}


	


	@Override
	public int hashCode()
	{
		return Objects.hash(rank_id);
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		JournalRank other = (JournalRank) obj;
		return rank_id == other.rank_id;
	}



	@Override
	public String toString()
	{
		return "JournalRank [title=" + title + ", issn_1=" + issn_1 + ", issn_2=" + issn_2 + ", issn_3=" + issn_3
				+ ", rank=" + rank + ", jcr=" + jcr + ", sjr=" + sjr + ", year=" + year + "]";
	}


}
