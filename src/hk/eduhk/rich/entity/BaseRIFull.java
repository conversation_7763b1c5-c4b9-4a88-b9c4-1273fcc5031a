package hk.eduhk.rich.entity;

/**
 * BaseRIDetail is an abstract class which contains all status fields in RI
 * status table (e.g. RI_Q_ACAD_PROF_OUTPUT_HDR) and RI detail status table
 * (e.g. RI_Q_ACAD_PROF_OUTPUT_DTL)
 */
public class BaseRIFull extends BaseRI
{

	private String displayInd;
	private String creatorInd;
	private String consentInd;

	public void setDisplayInd(String displayInd)
	{
		this.displayInd = displayInd;
	}


	public String getDisplayInd()
	{
		return displayInd;
	}


	public void setCreatorInd(String creatorInd)
	{
		this.creatorInd = creatorInd;
	}


	public String getCreatorInd()
	{
		return creatorInd;
	}


	public void setConsentInd(String consentInd)
	{
		this.consentInd = consentInd;
	}


	public String getConsentInd()
	{
		return consentInd;
	}

}