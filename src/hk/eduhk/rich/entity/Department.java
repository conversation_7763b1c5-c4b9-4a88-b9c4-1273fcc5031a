package hk.eduhk.rich.entity;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_Q_PK;


@Entity
@Table(name = "RH_P_DEPARTMENT_V")
public class Department
{
	public static Logger logger = Logger.getLogger(Department.class.toString());
	
	@Id
	@Column(name = "DEPARTMENT_CODE")
	private String departmentCode;
	
	@Column(name = "DEPARTMENT_NAME")
	private String departmentName;	
	
	@Column(name = "DEPARTMENT_ABBR")
	private String departmentAbbr;
	
	@Column(name = "DEPARTMENT_CHI_NAME")
	private String departmentChiName;
	
	@Column(name = "DATE_FROM")
	private String dateFrom;
	
	@Column(name = "DATE_TO")
	private String dateTo;
	
	@Column(name = "NUM_ACAD_STAFF")
	private String numAcadStaff;
	
	@Column(name = "URL")
	private String url;
	
	@Column(name = "NUM_PROJECT")
	private String numProject;
	
	@Column(name = "NUM_PUBLICATION")
	private String numPublication;

	
	public String getDepartmentCode()
	{
		return departmentCode;
	}

	
	public void setDepartmentCode(String departmentCode)
	{
		this.departmentCode = departmentCode;
	}

	
	public String getDepartmentName()
	{
		return departmentName;
	}

	
	public void setDepartmentName(String departmentName)
	{
		this.departmentName = departmentName;
	}

	
	public String getDepartmentAbbr()
	{
		return departmentAbbr;
	}

	
	public void setDepartmentAbbr(String departmentAbbr)
	{
		this.departmentAbbr = departmentAbbr;
	}

	
	public String getDepartmentChiName()
	{
		return departmentChiName;
	}

	
	public void setDepartmentChiName(String departmentChiName)
	{
		this.departmentChiName = departmentChiName;
	}

	
	public String getDateFrom()
	{
		return dateFrom;
	}

	
	public void setDateFrom(String dateFrom)
	{
		this.dateFrom = dateFrom;
	}

	
	public String getDateTo()
	{
		return dateTo;
	}

	
	public void setDateTo(String dateTo)
	{
		this.dateTo = dateTo;
	}

	
	public String getNumAcadStaff()
	{
		return numAcadStaff;
	}

	
	public void setNumAcadStaff(String numAcadStaff)
	{
		this.numAcadStaff = numAcadStaff;
	}

	
	public String getUrl()
	{
		return url;
	}

	
	public void setUrl(String url)
	{
		this.url = url;
	}

	
	public String getNumProject()
	{
		return numProject;
	}

	
	public void setNumProject(String numProject)
	{
		this.numProject = numProject;
	}

	
	public String getNumPublication()
	{
		return numPublication;
	}

	
	public void setNumPublication(String numPublication)
	{
		this.numPublication = numPublication;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((departmentCode == null) ? 0 : departmentCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Department other = (Department) obj;
		if (departmentCode == null)
		{
			if (other.departmentCode != null)
				return false;
		}
		else if (!departmentCode.equals(other.departmentCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "Department [departmentCode=" + departmentCode + ", departmentName=" + departmentName
				+ ", departmentAbbr=" + departmentAbbr + ", departmentChiName=" + departmentChiName + ", dateFrom="
				+ dateFrom + ", dateTo=" + dateTo + ", numAcadStaff=" + numAcadStaff + ", url=" + url + ", numProject="
				+ numProject + ", numPublication=" + numPublication + "]";
	}


	

	

}
