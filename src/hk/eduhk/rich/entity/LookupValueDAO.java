package hk.eduhk.rich.entity;

import java.util.*;
import java.util.logging.Level;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;



@SuppressWarnings("serial")
public class LookupValueDAO extends BaseDAO
{

	private static LookupValueDAO instance = null;


	public static synchronized LookupValueDAO getInstance()
	{
		if (instance == null) instance = new LookupValueDAO();
		return instance;
	}
	
	
	public static LookupValueDAO getCacheInstance()
	{
		return LookupValueDAO.getInstance();
	}	
	
	public List<LookupValue> getLookupValueFullList() 
	{
		List<LookupValue> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM LookupValue obj ORDER BY obj.pk.lookup_type, obj.pk.language, obj.enabled_flag, obj.print_order ";			
			TypedQuery<LookupValue> q = em.createQuery(query, LookupValue.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<LookupValue> getLookupValueList(String lookup_type, String language, String enabled_flag) 
	{
		List<LookupValue> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM LookupValue obj WHERE obj.pk.lookup_type = :lookup_type AND obj.pk.language = :language AND obj.enabled_flag = :enabled_flag ORDER BY obj.print_order ";			
			TypedQuery<LookupValue> q = em.createQuery(query, LookupValue.class);
			q.setParameter("lookup_type", lookup_type);
			q.setParameter("language", language);
			q.setParameter("enabled_flag", enabled_flag);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<LookupValue> getLookupValueList(List<String> lookupTypeList, String language, String enabled_flag) 
	{
		List<LookupValue> objList = null;
		
		if(CollectionUtils.isNotEmpty(lookupTypeList) && language!=null && enabled_flag!=null) 
		{
			EntityManager em = null;		
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM LookupValue obj WHERE obj.pk.lookup_type in :lookupTypeList AND obj.pk.language = :language AND obj.enabled_flag = :enabled_flag ORDER BY obj.print_order ";			
				TypedQuery<LookupValue> q = em.createQuery(query, LookupValue.class);
				q.setParameter("lookupTypeList", lookupTypeList);
				q.setParameter("language", language);
				q.setParameter("enabled_flag", enabled_flag);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (objList!=null) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<String> getDeptListByFac(String fac) 
	{
		List<String> objList = null;
		EntityManager em = null;
		if(fac != null) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj.pk.lookup_code FROM LookupValue obj "
						+ "WHERE obj.pk.lookup_type = :lookup_type "
						+ "AND obj.pk.language = :language "
						+ "AND obj.enabled_flag = :enabled_flag "
						+ "AND obj.parent_lookup_code = :fac "
						+ "ORDER BY obj.print_order ";			
				TypedQuery<String> q = em.createQuery(query, String.class);
				q.setParameter("lookup_type", "ORGANIZATION_UNIT_L2");
				q.setParameter("language", "US");
				q.setParameter("enabled_flag", "Y");
				q.setParameter("fac", fac);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public LookupValue getLookupValue(String lookup_type, String lookup_code, String language) 
	{
		List<LookupValue> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM LookupValue obj WHERE obj.pk.lookup_type = :lookup_type AND obj.pk.language = :language AND obj.pk.lookup_code = :lookup_code ";			
			TypedQuery<LookupValue> q = em.createQuery(query, LookupValue.class);
			q.setParameter("lookup_type", lookup_type);
			q.setParameter("language", language);
			q.setParameter("lookup_code", lookup_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public LookupValue updateLookupValue(LookupValue obj) 
	{
		return updateEntity(obj);
	}
	
	public void deleteLookupValue(String lookup_type, String lookup_code, String language) throws Exception
	{
		if (!Strings.isNullOrEmpty(lookup_type) && !Strings.isNullOrEmpty(lookup_code) && !Strings.isNullOrEmpty(language))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM LookupValue obj WHERE obj.pk.lookup_type = :lookup_type AND obj.pk.lookup_code = :lookup_code AND obj.pk.language = :language");
				q.setParameter("lookup_type", lookup_type);
				q.setParameter("lookup_code", lookup_code);
				q.setParameter("language", language);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete LookupValue", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteLookupValueByLookupType(String lookup_type) throws Exception
	{
		if (!Strings.isNullOrEmpty(lookup_type))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM LookupValue obj WHERE obj.pk.lookup_type = :lookup_type ");
				q.setParameter("lookup_type", lookup_type);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete LookupValue by lookup_type", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public List<LookupFormValue> getLookupFormValueList(List<String> formCodeList)
	{
		List<LookupFormValue> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();	
			String query = "SELECT obj FROM LookupFormValue obj "+
							"LEFT JOIN FETCH obj.form form "+
							"WHERE obj.pk.formCode in :formCodeList";
			TypedQuery<LookupFormValue> q = em.createQuery(query, LookupFormValue.class);
			q.setParameter("formCodeList", formCodeList);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
}