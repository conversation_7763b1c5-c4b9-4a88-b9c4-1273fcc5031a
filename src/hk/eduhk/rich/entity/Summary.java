package hk.eduhk.rich.entity;

import java.sql.Timestamp;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.persistence.Column;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;


@SuppressWarnings("serial")
public class Summary 
{

	private String period_id; 
	private String period_desc; 
	private String count;
	private String weighting;
	private String form_code;
	private String facDept;
	private String fac;
	
	public String getPeriod_id()
	{
		return period_id;
	}



	
	public void setPeriod_id(String period_id)
	{
		this.period_id = period_id;
	}



	
	public String getPeriod_desc()
	{
		return period_desc;
	}



	
	public void setPeriod_desc(String period_desc)
	{
		this.period_desc = period_desc;
	}



	
	public String getCount()
	{
		return count;
	}



	
	public void setCount(String count)
	{
		this.count = count;
	}



	
	public String getWeighting()
	{
		return weighting;
	}



	
	public void setWeighting(String weighting)
	{
		this.weighting = weighting;
	}



	
	public String getForm_code()
	{
		return form_code;
	}




	
	public void setForm_code(String form_code)
	{
		this.form_code = form_code;
	}




	
	public String getFacDept()
	{
		return facDept;
	}




	
	public void setFacDept(String facDept)
	{
		this.facDept = facDept;
	}




	
	public String getFac()
	{
		return fac;
	}




	
	public void setFac(String fac)
	{
		this.fac = fac;
	}




	@Override
	public String toString()
	{
		return "Summary [period_id=" + period_id + ", period_desc=" + period_desc + ", count=" + count + ", weighting="
				+ weighting + ", form_code=" + form_code + ", facDept=" + facDept + ", fac=" + fac + "]";
	}



	
}