package hk.eduhk.rich.entity.query;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.SetValuedMap;

// Copy from Survey GenericQueryChain
public interface GenericQueryChain
{
	
	public static final String VAL_NA 		= "-";	// Not applicable
	public static final String VAL_WILDCARD = "*";	// Wildcard
	
	public <T> GenericQueryChain filterByIdList(List<T> idList);

	public List<? extends Object> queryIdList();

}
