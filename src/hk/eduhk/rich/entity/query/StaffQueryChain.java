package hk.eduhk.rich.entity.query;

import java.io.Serializable;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.collections4.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.util.JPAUtils;
import hk.eduhk.rich.util.PersistenceManager;


@SuppressWarnings("serial")
public class StaffQueryChain implements GenericQueryChain, Serializable
{
	public static final String SELECT_ALL_VALUE = "all";
	public static final String YES_VALUE = "Y";
	public static final String NO_VALUE = "N";
	public static final String EMPTY_VALUE = "empty";
	
	// Chained query
	private String query = null;

	private static Logger logger = Logger.getLogger(StaffQueryChain.class.getName());

	private static final Level LVL_EXEC_TIME 	= Level.FINEST;
	private static final Level LVL_QUERY 		= Level.FINEST;

	
	public StaffQueryChain()
	{
		// Set the base query
		this.query = " SELECT STAFF.STAFF_NUMBER  " +
				" FROM RH_P_STAFF_IDENTITY STAFF " +
				" WHERE 1=1 ";
	}
	
	
	/**
	 * Private constructor 
	 * 
	 * @param catId
	 * @param query
	 */
	private StaffQueryChain(String query)
	{
		this.query = query;
	}
	
	
	public StaffQueryChain staffName(String staffName)
	{
		String newQuery = query;
		if(staffName != null) {
			newQuery += " AND UPPER(STAFF.FULLNAME) LIKE UPPER('%" + 
					 PersistenceManager.escapeSql(staffName)+"%') ";
		}
		return new StaffQueryChain(newQuery);
	}
	
	
	public StaffQueryChain staffRank(List<String> staffRank)
	{
		String newQuery = query;
		if(!CollectionUtils.isEmpty(staffRank)) {
			newQuery += " AND ( " ;
			int orInd = staffRank.size() - 1;
			for(int i = 0 ; i < staffRank.size() ; ++i) {
				newQuery +=	" ','||STAFF.RANK_CODE||',' LIKE '%," + 
							PersistenceManager.escapeSql(staffRank.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) ";
		}
		return new StaffQueryChain(newQuery);
	}
	
	
	public StaffQueryChain isAcadStaff(String acadStaff) {
		String newQuery = query;
		if(acadStaff != null) {
			if(acadStaff.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " STAFF.ACAD_STAFF_IND IS NOT NULL " ;
			else if(!acadStaff.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 "STAFF.ACAD_STAFF_IND = '" + PersistenceManager.escapeSql(acadStaff) + "' ";

			// do nothing when empty
		}
		return new StaffQueryChain(newQuery);
	}
	
	
	public StaffQueryChain department(List<String> department) {
		String newQuery = query;
		if(!CollectionUtils.isEmpty(department)) {
			String staffNo = "STAFF.STAFF_NUMBER";
			newQuery += " AND ( " ;
			int orInd = department.size() - 1;
			for(int i = 0 ; i < department.size() ; ++i) {
				newQuery +=	" ','||STAFF.DEPT_CODE||',' LIKE '%," + 
							PersistenceManager.escapeSql(department.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) ";
		}
		return new StaffQueryChain(newQuery);
	}
	
	public StaffQueryChain end() {
		String newQuery = query;
		newQuery += " AND 1=0 "; 
		return new StaffQueryChain(newQuery);
	}
	
	@Override
	public <T extends Object> StaffQueryChain filterByIdList(List<T> idList)
	{
		Class cls = CollectionUtils.isNotEmpty(idList) ? idList.get(0).getClass() : null;
		String newQuery = query + "AND " + JPAUtils.convertQueryParamCol("STAFF.STAFF_NUMBER", idList, cls) + " ";
		return new StaffQueryChain(newQuery);
	}
	
	
	@Override
	public List<String> queryIdList()
	{
		List<String> resultList = new ArrayList<String>();
		long t = System.currentTimeMillis();
		
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		logger.log(LVL_QUERY, "query=" + query);
		
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) resultList.add(rs.getString(1));
			}
			catch (SQLException se)
			{
				logger.log(Level.WARNING, "", se);
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution time=" + (System.currentTimeMillis()-t) + "ms");
		return resultList;
	}


	@Override
	public String toString()
	{
		return "StaffQueryChain [query=" + query + "]";
	}
	
	
	

	
}
