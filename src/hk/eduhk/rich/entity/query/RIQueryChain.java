package hk.eduhk.rich.entity.query;

import java.io.Serializable;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.collections4.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.util.JPAUtils;
import hk.eduhk.rich.util.PersistenceManager;


@SuppressWarnings("serial")
public class RIQueryChain implements GenericQueryChain, Serializable
{
	public static final String RI_TYPE_OUTPUT = "Output";
	public static final String RI_TYPE_PROJECT = "Project";
	public static final String RI_TYPE_AWARD = "Award";
	public static final String RI_TYPE_PATENT = "Patent";
	public static final String RI_TYPE_RAE	  = "RAE Output";
	
	
	public static final String SELECT_ALL_VALUE = "all";
	public static final String YES_VALUE = "Y";
	public static final String NO_VALUE = "N";
	public static final String EMPTY_VALUE = "empty";
	
	public static final String RICH_EDITION_VALUE = "P";
	public static final String CDCF_SNAPSHOT_VALUE = "C";
	
	private String riType = null;
	private String searchNo = null;

	// Chained query
	private String query = null;

	private static Logger logger = Logger.getLogger(RIQueryChain.class.getName());

	private static final Level LVL_EXEC_TIME 	= Level.FINEST;
	private static final Level LVL_QUERY 		= Level.FINEST;

	
	public RIQueryChain(String riType)
	{
		if (riType == null) throw new NullPointerException("riType cannot be null");
		
		this.riType = riType;
		
		// Set the base query
		if(riType.equals(RI_TYPE_OUTPUT)) {
			this.query = "SELECT DISTINCT PH.OUTPUT_NO FROM RH_P_ACAD_PROF_OUTPUT_HDR PH " +
					"LEFT JOIN RH_P_ACAD_PROF_OUTPUT_DTL PD ON (PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +
					"LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_HDR QH ON QH.OUTPUT_NO = PH.OUTPUT_NO " +
					"LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_DTL QD ON (QD.OUTPUT_NO = PH.OUTPUT_NO AND QD.STAFF_NO = PD.AUTHORSHIP_STAFF_NO) " +
					"LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO " +
					"LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO " +
					"WHERE 1=1 ";
			searchNo = "PH.OUTPUT_NO";
		}
		else if(riType.equals(RI_TYPE_PROJECT)) {
			this.query = "SELECT DISTINCT PH.PROJECT_NO FROM RH_P_RESEARCH_PROJECT_HDR PH " +
					"LEFT JOIN RH_P_RESEARCH_PROJECT_DTL PD ON (PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +
					"LEFT JOIN RH_Q_RESEARCH_PROJECT_HDR QH ON QH.PROJECT_NO = PH.PROJECT_NO " +
					"LEFT JOIN RH_Q_RESEARCH_PROJECT_DTL QD ON (QD.PROJECT_NO = PH.PROJECT_NO AND QD.STAFF_NO = PD.INVESTIGATOR_STAFF_NO) " +
					"LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " +
					"LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " +
					"WHERE 1=1 ";
			searchNo = "PH.PROJECT_NO";
		}
		else if(riType.equals(RI_TYPE_AWARD)) {
			this.query = "SELECT DISTINCT PH.AWARD_NO FROM RH_P_AWARD_HDR PH " +
					"LEFT JOIN RH_P_AWARD_DTL PD ON (PD.AWARD_NO = PH.AWARD_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +
					"LEFT JOIN RH_Q_AWARD_HDR QH ON QH.AWARD_NO = PH.AWARD_NO " +
					"LEFT JOIN RH_Q_AWARD_DTL QD ON (QD.AWARD_NO = PH.AWARD_NO AND QD.STAFF_NO = PD.RECIPIENT_STAFF_NO) " +
					"LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.RECIPIENT_STAFF_NO " +
					"LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.RECIPIENT_STAFF_NO " +
					"WHERE 1=1 ";
			searchNo = "PH.AWARD_NO";
		}
		else if(riType.equals(RI_TYPE_PATENT)) {
			this.query = "SELECT DISTINCT PH.PATENT_NO FROM RH_P_PATENT_HDR PH " +
					"LEFT JOIN RH_P_PATENT_DTL PD ON (PD.PATENT_NO = PH.PATENT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +
					"LEFT JOIN RH_Q_PATENT_HDR QH ON QH.PATENT_NO = PH.PATENT_NO " +
					"LEFT JOIN RH_Q_PATENT_DTL QD ON (QD.PATENT_NO = PH.PATENT_NO AND QD.STAFF_NO = PD.INVENTOR_STAFF_NO) " +
					"LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVENTOR_STAFF_NO " +
					"LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVENTOR_STAFF_NO " +
					"WHERE 1=1 ";
			searchNo = "PH.PATENT_NO";
		}
		else if(riType.equals(RI_TYPE_RAE)) {
			this.query = "SELECT DISTINCT PS.output_no FROM RH_RAE_OUTPUT_STATUS PS " +
					"INNER JOIN RH_RAE_STAFF RAE_STAFF on PS.STAFF_NUMBER = RAE_STAFF.STAFF_NUMBER " +
					"LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = RAE_STAFF.STAFF_NUMBER " +
					"LEFT JOIN RH_RAE_OUTPUT_HDR PH on PH.output_no = PS.output_no " +
					"WHERE 1=1 ";
			searchNo = "PS.output_no";
		}
		else {
			throw new NullPointerException("Unknown riType");
		}
		
	}
	
	
	/**
	 * Private constructor 
	 * 
	 * @param catId
	 * @param query
	 */
	private RIQueryChain(String riType, String query)
	{
		if(riType.equals(RI_TYPE_OUTPUT)) {
			searchNo = "PH.OUTPUT_NO";
		}
		else if(riType.equals(RI_TYPE_PROJECT)) {
			searchNo = "PH.PROJECT_NO";
		}
		else if(riType.equals(RI_TYPE_AWARD)) {
			searchNo = "PH.AWARD_NO";
		}
		else if(riType.equals(RI_TYPE_PATENT)) {
			searchNo = "PH.PATENT_NO";
		}
		else if(riType.equals(RI_TYPE_RAE)) {
			searchNo = "PS.OUTPUT_NO";
		}
		else throw new NullPointerException("Unknown riType");
		this.riType = riType;
		this.query = query;
	}
	
	
	public RIQueryChain staffName(String staffName)
	{
		String newQuery = query;
		if(staffName != null) {
			String staffNo = "STAFF.STAFF_NUMBER";
//			newQuery += " AND (UPPER(STAFF.FULLNAME) LIKE UPPER('%" + 
//					 PersistenceManager.escapeSql(staffName)+"%') " +
//					 " AND " + staffNo + " IS NOT NULL) ";
			newQuery += " AND (UPPER(STAFF.FULLNAME) = UPPER('" + 
					 PersistenceManager.escapeSql(staffName)+"') " +
					 " AND " + staffNo + " IS NOT NULL) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain staffRank(List<String> staffRank)
	{
		String newQuery = query;
		if(!CollectionUtils.isEmpty(staffRank)) {
			String staffNo = "STAFF.STAFF_NUMBER";
			newQuery += " AND (( " ;
			int orInd = staffRank.size() - 1;
			for(int i = 0 ; i < staffRank.size() ; ++i) {
				newQuery +=	" ','||STAFF.RANK_CODE||',' LIKE '%," + 
							PersistenceManager.escapeSql(staffRank.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) AND " + staffNo + " IS NOT NULL) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain uoaList(List<String> uoaList)
	{
		String newQuery = query;
		if(!CollectionUtils.isEmpty(uoaList)) {

			newQuery += " AND ( " ;
			int orInd = uoaList.size() - 1;
			for(int i = 0 ; i < uoaList.size() ; ++i) {
				newQuery +=	" ','||RAE_STAFF.uoa_code||',' LIKE '%," + 
							PersistenceManager.escapeSql(uoaList.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain sdgList (String sdgList) {
		
		String newQuery = query;
		String sdgQuery = "";
		

		if(sdgList != null) {
			StringBuilder finalStringb =new StringBuilder();
			newQuery += " AND (";
			if (sdgList.contains(",")) {
				for (String sdg : Arrays.asList(PersistenceManager.escapeSql(sdgList).split(","))) 
					sdgQuery += "OR PH.SDG_CODE LIKE '%" +sdg+ "%' ";
				
			
				newQuery += sdgQuery.replaceFirst("OR", "");
			}
			else
				newQuery += "PH.SDG_CODE LIKE '%" +PersistenceManager.escapeSql(sdgList)+ "%' ";
			newQuery += " ) ";
		}
		// do nothing when empty
		return new RIQueryChain(riType, newQuery);
	}
	
	
	
	
	public RIQueryChain isAcadStaff(String acadStaff) {
		String newQuery = query;
		if(acadStaff != null) {
			if(acadStaff.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND ( " + 
						 " STAFF.ACAD_STAFF_IND IS NOT NULL " +
						 " AND STAFF.STAFF_NUMBER IS NOT NULL) ";
			else if(!acadStaff.equals(EMPTY_VALUE)) 
				newQuery += " AND ( " + 
						 "STAFF.ACAD_STAFF_IND = '" + PersistenceManager.escapeSql(acadStaff) + "' " +
						 "AND STAFF.STAFF_NUMBER IS NOT NULL) ";

			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain department(List<String> department) {
		String newQuery = query;
		if(!CollectionUtils.isEmpty(department)) {
			String staffNo = "STAFF.STAFF_NUMBER";
			newQuery += " AND (( " ;
			int orInd = department.size() - 1;
			for(int i = 0 ; i < department.size() ; ++i) {
				newQuery +=	" ','||STAFF.DEPT_CODE||',' LIKE '%," + 
							PersistenceManager.escapeSql(department.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) AND " + staffNo + " IS NOT NULL) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	

	public RIQueryChain exStaffName(String exStaffName)
	{
		String newQuery = query;
		if(exStaffName != null) {
			String staffNo = "EXSTAFF.STAFF_NUMBER";
//			newQuery += " AND (UPPER(EXSTAFF.FULLNAME) LIKE UPPER('%" + 
//					 PersistenceManager.escapeSql(exStaffName)+"%') " +
//					 " AND " + staffNo + " IS NOT NULL) ";
			newQuery += " AND (UPPER(EXSTAFF.FULLNAME) = UPPER('" + 
					 PersistenceManager.escapeSql(exStaffName)+"') " +
					 " AND " + staffNo + " IS NOT NULL) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain formStaffNum(String formStaffNum)
	{
		String newQuery = query;
		if(formStaffNum != null) {
			newQuery += " AND EXSTAFF.STAFF_NUMBER = '" + 
					 PersistenceManager.escapeSql(formStaffNum)+"' ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain riNo(String riNo)
	{
		String newQuery = query;
		if(riNo != null) {
			newQuery += " AND " +
					JPAUtils.convertQueryParamCol(searchNo, riNo.split(","), String.class)+" ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain outputType(String... outputType)
	{
		String newQuery = query;
		if(riType.equals(RI_TYPE_OUTPUT) && !ArrayUtils.isEmpty(outputType)) {
			newQuery += " AND " +
					JPAUtils.convertQueryParamCol("PH.SAP_OUTPUT_TYPE", outputType, String.class)+" ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain dataLevel(String dataLevel)
	{
		String newQuery = query;
		if(dataLevel != null) {
			newQuery += " AND PH.DATA_LEVEL = '" + 
					 PersistenceManager.escapeSql(dataLevel)+"' ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain keyword(String keyword)
	{
		String newQuery = query;
		if(riType.equals(RI_TYPE_PROJECT) && keyword != null) {
			newQuery += " AND REGEXP_LIKE(UPPER(PH.KEYWORDS), UPPER('" +
					keyword.replace(",", "|") + "')) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain smartSearch(String smartSearch)
	{
		String newQuery = query;
		if(smartSearch != null) {
			if(riType.equals(RI_TYPE_PROJECT)) {
				newQuery += " AND (" +
						"REGEXP_LIKE(UPPER(" +
						"PH.TITLE_1||' '||PH.TITLE_2||' '||PH.TITLE_3||' '||PH.TITLE_4)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.PROJECT_SUMMARY)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						")";
			}
			else if(riType.equals(RI_TYPE_OUTPUT)) {
				newQuery += " AND (" +
						"REGEXP_LIKE(UPPER(" +
						"PH.TITLE_JOUR_BOOK||' '||PH.OUTPUT_TITLE_CONTINUE)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.TITLE_PAPER_ART)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.APA_CITATION)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.OTHER_DETAILS||' '||PH.OTHER_DETAILS_CONTINUE)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						")";
			}
			else if(riType.equals(RI_TYPE_AWARD)) {
				newQuery += " AND (" +
						"REGEXP_LIKE(UPPER(" +
						"PH.AWARD_NAME)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.ORG_NAME)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.SHORT_DESC)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.FULL_DESC)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						")";
			}
			else if(riType.equals(RI_TYPE_PATENT)) {
				newQuery += " AND (" +
						"REGEXP_LIKE(UPPER(" +
						"PH.PATENT_NAME)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.SHORT_DESC)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						" OR " +
						"REGEXP_LIKE(UPPER(" +
						"PH.FULL_DESC)" +
						", UPPER('" +
						smartSearch.replace(",", "|") + "')) " +
						")";
			}
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain journalName(String journalName)
	{
		String newQuery = query;
		if(riType.equals(RI_TYPE_OUTPUT) && journalName != null) {
			newQuery += " AND UPPER(PH.TITLE_PAPER_ART) LIKE(UPPER('%" +
					PersistenceManager.escapeSql(journalName) + "%') ) ";
		}
		return new RIQueryChain(riType, newQuery);
	}

	public RIQueryChain cdcfStatus(String cdcfStatus) {
		String newQuery = query;
		if(cdcfStatus != null) {
			if(cdcfStatus.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " QH.CDCF_STATUS IS NOT NULL ";
			else if(!cdcfStatus.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " QH.CDCF_STATUS = '" + PersistenceManager.escapeSql(cdcfStatus) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain insDecIndicator(String insDecIndicator) {
		String newQuery = query;
		if(insDecIndicator != null) {
			if(insDecIndicator.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " QH.INST_VERIFIED_IND IS NOT NULL ";
			else if(!insDecIndicator.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " QH.INST_VERIFIED_IND = '" + PersistenceManager.escapeSql(insDecIndicator) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain consentIndicator(String consentIndicator) {
		String newQuery = query;
		if(consentIndicator != null) {
			if(consentIndicator.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " QD.CONSENT_IND IS NOT NULL ";
			else if(!consentIndicator.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " QD.CONSENT_IND = '" + PersistenceManager.escapeSql(consentIndicator) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain chgAfterProcess(String chgAfterProcess) {
		String newQuery = query;
		if(chgAfterProcess != null) {
			if(chgAfterProcess.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " QH.CDCF_CHANGED_IND IS NOT NULL ";
			else if(!chgAfterProcess.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " QH.CDCF_CHANGED_IND = '" + PersistenceManager.escapeSql(chgAfterProcess) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain singleFac (String faculty) {
		String newQuery = query;
		if(faculty != null) {
			if(faculty.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " RAE_STAFF.COORD_FACULTY IS NOT NULL ";
			else if(!faculty.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " RAE_STAFF.COORD_FACULTY = '" + PersistenceManager.escapeSql(faculty) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain researchType (String type) {
		String newQuery = query;
		if(type != null) {
			if(type.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.OUTPUT_TYPE IS NOT NULL ";
			else if(!type.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.OUTPUT_TYPE = '" + PersistenceManager.escapeSql(type) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain selectType (String type) {
		String newQuery = query;
		if(type != null) {
			if(type.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PS.SEL_TYPE IS NOT NULL ";
			else if(!type.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PS.SEL_TYPE = '" + PersistenceManager.escapeSql(type) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain infoCompleted (String completed) {
		String newQuery = query;
		if(completed != null) {
			if(completed.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PS.INFO_COMP IS NOT NULL ";
			else if(!completed.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PS.INFO_COMP = '" + PersistenceManager.escapeSql(completed) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain outputStatus (String status) {
		String newQuery = query;
		if(status != null) {
			if(status.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code IS NOT NULL ";
			else if(!status.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code = '" + PersistenceManager.escapeSql(status) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain panelStatus (String status) {
		String newQuery = query;
		if(status != null) {
			if(status.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code_panel IS NOT NULL ";
			else if(!status.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code_panel = '" + PersistenceManager.escapeSql(status) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	
	public RIQueryChain fullStatus (String status) {
		String newQuery = query;
		if(status != null) {
			if(status.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code_full_ver IS NOT NULL ";
			else if(!status.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code_full_ver = '" + PersistenceManager.escapeSql(status) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain otherStatus (String status) {
		String newQuery = query;
		if(status != null) {
			if(status.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code_oth_info IS NOT NULL ";
			else if(!status.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_code_oth_info = '" + PersistenceManager.escapeSql(status) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain ineligibleStatus (String status) {
		String newQuery = query;
		if(status != null) {
			if(status.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_ineligible IS NOT NULL ";
			else if(!status.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.rae_status_ineligible = '" + PersistenceManager.escapeSql(status) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	public RIQueryChain citationChk (String check) {
		String newQuery = query;
		if(check != null) {
			if(check.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.citation_chk_code IS NOT NULL ";
			else if(!check.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.citation_chk_code = '" + PersistenceManager.escapeSql(check) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	
	public RIQueryChain copyrightClear (String clearance) {
		String newQuery = query;
		if(clearance != null) {
			if(clearance.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.copyright_clr_code IS NOT NULL ";
			else if(!clearance.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.copyright_clr_code = '" + PersistenceManager.escapeSql(clearance) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	
	
	
	public RIQueryChain riDate(Date fromDate, Date toDate,String viewType) {
		
		
		String newQuery = query;
		if(fromDate != null || toDate != null) {
			DateFormat dmyFormat = new SimpleDateFormat("dd/MM/yyyy");
			DateFormat myFormat = new SimpleDateFormat("MM/yyyy");
			if(fromDate == null) {
				fromDate = new GregorianCalendar(1,1,1).getTime();
			}
			if(toDate == null) {
				toDate = new GregorianCalendar(9000,12,31).getTime();
			}
			
			if(riType.equals(RI_TYPE_OUTPUT)) {
				newQuery += " AND PH.OUTPUT_NO IN ( " +
						" SELECT DISTINCT OUTPUT_NO FROM ( " + 
//						Both from and to date is using From, it is normal and correct
						" SELECT OUTPUT_NO, NVL(FROM_MONTH,'1')||'/'||NVL(FROM_YEAR,'9001') AS FROMDATE, " +
						" NVL(FROM_MONTH,'12')||'/'||NVL(FROM_YEAR,'9001') AS TODATE FROM " +
						"(SELECT * FROM RH_P_ACAD_PROF_OUTPUT_HDR WHERE DATA_LEVEL = '"+viewType+"' )) " +
						" WHERE NOT (" +
						" (TO_DATE(TODATE, 'MM/YYYY') < TO_DATE('" + myFormat.format(fromDate) + "', 'MM/YYYY')) " +
						" OR " +
						" (TO_DATE(FROMDATE, 'MM/YYYY') > TO_DATE('" + myFormat.format(toDate) + "', 'MM/YYYY')) " +
						" ) " +
						" ) ";
			}
			else if(riType.equals(RI_TYPE_PROJECT)) {
				newQuery += " AND PH.PROJECT_NO IN ( " +
						" SELECT DISTINCT PROJECT_NO FROM ( " + 
						" SELECT PROJECT_NO, NVL(FROM_DAY,'1')||'/'||FROM_MONTH||'/'||FROM_YEAR AS FROMDATE, " +
						" NVL(TO_DAY, TO_CHAR(LAST_DAY(TO_DATE(TO_MONTH||'/'||TO_YEAR, 'MM/YYYY')),'DD')) " +
						" ||'/'||TO_MONTH||'/'||TO_YEAR AS TODATE FROM " +
						" (SELECT * FROM RH_P_RESEARCH_PROJECT_HDR WHERE DATA_LEVEL = '"+viewType+"' )) " +
						" WHERE NOT (" +
						" (TO_DATE(TODATE, 'DD/MM/YYYY') < TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" OR " +
						" (TO_DATE(FROMDATE, 'DD/MM/YYYY') > TO_DATE('" + dmyFormat.format(toDate) + "', 'DD/MM/YYYY')) " +
						" ) " +
						" ) ";
			}
			else if(riType.equals(RI_TYPE_AWARD)) {
				newQuery += " AND PH.AWARD_NO IN ( " +
						" SELECT DISTINCT AWARD_NO FROM ( " + 
						" SELECT AWARD_NO, NVL(AWARD_DAY,'1')||'/'||AWARD_MONTH||'/'||AWARD_YEAR AS FROMDATE, " +
						" NVL(AWARD_DAY, TO_CHAR(LAST_DAY(TO_DATE(AWARD_MONTH||'/'||AWARD_YEAR, 'MM/YYYY')),'DD')) " +
						" ||'/'||AWARD_MONTH||'/'||AWARD_YEAR AS TODATE FROM " +
						" (SELECT * FROM RH_P_AWARD_HDR WHERE DATA_LEVEL = '"+viewType+"' )) " +
						" WHERE NOT (" +
						" (TO_DATE(TODATE, 'DD/MM/YYYY') < TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" OR " +
						" (TO_DATE(FROMDATE, 'DD/MM/YYYY') > TO_DATE('" + dmyFormat.format(toDate) + "', 'DD/MM/YYYY')) " +
						" ) " +
						" ) ";
			}
			else if(riType.equals(RI_TYPE_PATENT)) {
				newQuery += " AND PH.PATENT_NO IN ( " +
						" SELECT DISTINCT PATENT_NO FROM ( " + 
						" SELECT PATENT_NO, NVL(PATENT_DAY,'1')||'/'||PATENT_MONTH||'/'||PATENT_YEAR AS FROMDATE, " +
						" NVL(PATENT_DAY, TO_CHAR(LAST_DAY(TO_DATE(PATENT_MONTH||'/'||PATENT_YEAR, 'MM/YYYY')),'DD')) " +
						" ||'/'||PATENT_MONTH||'/'||PATENT_YEAR AS TODATE FROM " +
						" (SELECT * FROM RH_P_PATENT_HDR WHERE DATA_LEVEL = '" +viewType+"' )) " +
						" WHERE NOT (" +
						" (TO_DATE(TODATE, 'DD/MM/YYYY') < TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" OR " +
						" (TO_DATE(FROMDATE, 'DD/MM/YYYY') > TO_DATE('" + dmyFormat.format(toDate) + "', 'DD/MM/YYYY')) " +
						" ) " +
						" ) ";
			}
			
			else 	if(riType.equals(RI_TYPE_RAE)) {
				newQuery += " AND PS.OUTPUT_NO IN ( " +
						" SELECT DISTINCT OUTPUT_NO FROM ( " + 
						" SELECT OUTPUT_NO, NVL(FROM_MONTH,'1')||'/'||NVL(FROM_YEAR,'9001') AS FROMDATE, " +
						" NVL(TO_MONTH,'1')||'/'||NVL(TO_YEAR,'9001') AS TODATE FROM RH_RAE_OUTPUT_HDR ) " +
						" WHERE NOT (" +
						" (TO_DATE(TODATE, 'MM/YYYY') < TO_DATE('" + myFormat.format(fromDate) + "', 'MM/YYYY')) " +
						" OR " +
						" (TO_DATE(FROMDATE, 'MM/YYYY') > TO_DATE('" + myFormat.format(toDate) + "', 'MM/YYYY')) " +
						" ) " +
						" ) ";
			}
			
			
			//System.out.println("Date Period: "+newQuery);
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain riFirstPublish(Date fromDate, Date toDate) {
		String newQuery = query;
		if(fromDate != null || toDate != null) {
			DateFormat dmyFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date toDateTmr = tmrDate(toDate);;
			if(fromDate == null) {
				fromDate = new GregorianCalendar(1,1,1).getTime();
			}
			if(toDateTmr == null) {
				toDateTmr = new GregorianCalendar(9000,12,31).getTime();
			}
			
			newQuery += " AND ( " +
						" (PH.CREATION_TIME <= TO_DATE('" + dmyFormat.format(toDateTmr) + "', 'DD/MM/YYYY')) " +
						" AND " + 
						" (PH.CREATION_TIME >= TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" ) ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain riLastPublish(Date fromDate, Date toDate) {
		String newQuery = query;
		if(fromDate != null || toDate != null) {
			DateFormat dmyFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date toDateTmr = tmrDate(toDate);;
			if(fromDate == null) {
				fromDate = new GregorianCalendar(1,1,1).getTime();
			}
			if(toDateTmr == null) {
				toDateTmr = new GregorianCalendar(9000,12,31).getTime();
			}
			
			newQuery += " AND ( " +
						" (QH.LAST_PUBLISHED_DATE <= TO_DATE('" + dmyFormat.format(toDateTmr) + "', 'DD/MM/YYYY')) " +
						" AND " + 
						" (QH.LAST_PUBLISHED_DATE >= TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" ) ";

		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain fundingSrc(String... fundingSrc)
	{
		String newQuery = query;
		if(riType.equals(RI_TYPE_PROJECT) && !ArrayUtils.isEmpty(fundingSrc)) {
			newQuery += " AND " +
					JPAUtils.convertQueryParamCol("PH.SAP_FUNDING_SOURCE", fundingSrc, String.class)+" ";
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain enhTLInHighEdu(String enhTLInHighEdu) {
		String newQuery = query;
		if(riType.equals(RI_TYPE_OUTPUT) && enhTLInHighEdu != null) {
			if(enhTLInHighEdu.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " PH.IS_ENH_HIGH_EDU IS NOT NULL ";
			else if(!enhTLInHighEdu.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " PH.IS_ENH_HIGH_EDU = '" + PersistenceManager.escapeSql(enhTLInHighEdu) + "' ";
			// do nothing when empty
		}
		return new RIQueryChain(riType, newQuery);
	}
	
	public RIQueryChain end() {
		String newQuery = query;
		newQuery += " AND 1=0 "; 
		return new RIQueryChain(riType, newQuery);
	}
	
	
	@Override
	public <T extends Object> RIQueryChain filterByIdList(List<T> idList)
	{
		Class cls = CollectionUtils.isNotEmpty(idList) ? idList.get(0).getClass() : null;
		String newQuery = query + "AND " + JPAUtils.convertQueryParamCol("PH."+searchNo, idList, cls) + " ";
		return new RIQueryChain(riType, newQuery);
	}
	
	
	@Override
	public List<Integer> queryIdList()
	{
		List<Integer> resultList = new ArrayList<Integer>();
		long t = System.currentTimeMillis();
		
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		//logger.log(LVL_QUERY, "query=" + query);
		//System.out.println("query:"+query);
		
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) resultList.add(rs.getInt(1));
			}
			catch (SQLException se)
			{
				logger.log(Level.WARNING, "", se);
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		
		//logger.log(LVL_EXEC_TIME, "Execution time=" + (System.currentTimeMillis()-t) + "ms");
		return resultList;
	}


	@Override
	public String toString()
	{
		return "RIQueryChain [riType=" + riType + ", searchNo=" + searchNo + ", query=" + query + "]";
	}
	
	private Date tmrDate(Date date) {
		if(date != null) {
			Date toDateTmr = new Date();
			Calendar c = Calendar.getInstance(); 
			c.setTime(date); 
			c.add(Calendar.DATE, 1);
			toDateTmr = c.getTime();
			return toDateTmr;
		}
		else return null;
	}
	
	

	
}
