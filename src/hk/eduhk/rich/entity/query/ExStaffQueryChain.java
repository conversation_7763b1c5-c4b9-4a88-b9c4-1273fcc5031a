package hk.eduhk.rich.entity.query;

import java.io.Serializable;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.collections4.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.util.JPAUtils;
import hk.eduhk.rich.util.PersistenceManager;


@SuppressWarnings("serial")
public class ExStaffQueryChain implements GenericQueryChain, Serializable
{
	public static final String SELECT_ALL_VALUE = "all";
	public static final String YES_VALUE = "Y";
	public static final String NO_VALUE = "N";
	public static final String EMPTY_VALUE = "empty";
	
	// Chained query
	private String query = null;

	private static Logger logger = Logger.getLogger(ExStaffQueryChain.class.getName());

	private static final Level LVL_EXEC_TIME 	= Level.FINEST;
	private static final Level LVL_QUERY 		= Level.FINEST;

	
	public ExStaffQueryChain()
	{
		// Set the base query
		this.query = " SELECT EXSTAFF.STAFF_NUMBER  " +
				" FROM RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF " +
				" WHERE 1=1 ";
	}
	
	
	/**
	 * Private constructor 
	 * 
	 * @param catId
	 * @param query
	 */
	private ExStaffQueryChain(String query)
	{
		this.query = query;
	}
	

	public ExStaffQueryChain exStaffName(String exStaffName)
	{
		String newQuery = query;
		if(exStaffName != null) {
			newQuery += " AND UPPER(EXSTAFF.FULLNAME) LIKE UPPER('%" + 
					 PersistenceManager.escapeSql(exStaffName)+"%') ";
		}
		return new ExStaffQueryChain(newQuery);
	}
	
	public ExStaffQueryChain formStaffNum(String formStaffNum)
	{
		String newQuery = query;
		if(formStaffNum != null) {
			newQuery += " AND EXSTAFF.STAFF_NUMBER = '" + 
					 PersistenceManager.escapeSql(formStaffNum)+"' ";
		}
		return new ExStaffQueryChain(newQuery);
	}
	
	
	@Override
	public <T extends Object> ExStaffQueryChain filterByIdList(List<T> idList)
	{
		Class cls = CollectionUtils.isNotEmpty(idList) ? idList.get(0).getClass() : null;
		String newQuery = query + "AND " + JPAUtils.convertQueryParamCol("EXSTAFF.STAFF_NUMBER", idList, cls) + " ";
		return new ExStaffQueryChain(newQuery);
	}
	
	
	@Override
	public List<String> queryIdList()
	{
		List<String> resultList = new ArrayList<String>();
		long t = System.currentTimeMillis();
		
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		logger.log(LVL_QUERY, "query=" + query);
		
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) resultList.add(rs.getString(1));
			}
			catch (SQLException se)
			{
				logger.log(Level.WARNING, "", se);
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution time=" + (System.currentTimeMillis()-t) + "ms");
		return resultList;
	}


	@Override
	public String toString()
	{
		return "ExStaffQueryChain [query=" + query + "]";
	}
	
	
	

	
}
