package hk.eduhk.rich.entity.query;

import java.io.Serializable;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.apache.commons.collections4.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.util.JPAUtils;
import hk.eduhk.rich.util.PersistenceManager;


@SuppressWarnings("serial")
public class KTQueryChain implements GenericQueryChain, Serializable
{
	public static final String SELECT_ALL_VALUE = "all";
	public static final String YES_VALUE = "Y";
	public static final String NO_VALUE = "N";
	public static final String EMPTY_VALUE = "empty";
	
	public static final String RICH_EDITION_VALUE = "P";
	public static final String CDCF_SNAPSHOT_VALUE = "C";
	
	private String ktType = null;
	private String searchNo = "form_no";

	// Chained query
	private String query = null;

	private static Logger logger = Logger.getLogger(KTQueryChain.class.getName());

	private static final Level LVL_EXEC_TIME 	= Level.FINEST;
	private static final Level LVL_QUERY 		= Level.FINEST;

	
	public KTQueryChain(String ktType)
	{
		if (ktType == null) throw new NullPointerException("ktType cannot be null");
		
		this.ktType = ktType;
		
		// Set the base query
		if(ktType != null) {
			this.query = "SELECT DISTINCT PH.FORM_NO FROM RH_P_"+ ktType +"_HDR PH " +
					"LEFT JOIN RH_P_KT_FORM_DTL PD ON (PD.FORM_NO = PH.FORM_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +
					"LEFT JOIN RH_Q_KT_FORM_HDR QH ON (QH.FORM_NO = PH.FORM_NO AND ((PH.DATA_LEVEL IN ('M', 'P') AND " +
					" QH.DATA_LEVEL = 'M') OR (PH.DATA_LEVEL IN ('N', 'D') AND QH.DATA_LEVEL = 'N'))) " +
					"LEFT JOIN RH_Q_KT_FORM_STATE QS ON QS.FORM_NO = PH.FORM_NO " +
					"LEFT JOIN RH_Q_KT_FORM_DTL QD ON (QD.FORM_NO = PH.FORM_NO AND QD.STAFF_NO = PD.STAFF_NO) " +
					"LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.STAFF_NO " +
					"LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.STAFF_NO " +
					"WHERE 1=1 ";
		}
		
	}
	
	
	/**
	 * Private constructor 
	 * 
	 * @param catId
	 * @param query
	 */
	private KTQueryChain(String ktType, String query)
	{
		this.ktType = ktType;
		this.query = query;
	}
	
	
	public KTQueryChain staffName(String staffName)
	{
		String newQuery = query;
		if(staffName != null) {
			String staffNo = "STAFF.STAFF_NUMBER";
//			newQuery += " AND (UPPER(STAFF.FULLNAME) LIKE UPPER('%" + 
//					 PersistenceManager.escapeSql(staffName)+"%') " +
//					 " AND " + staffNo + " IS NOT NULL) ";
			newQuery += " AND (UPPER(STAFF.FULLNAME) = UPPER('" + 
					 PersistenceManager.escapeSql(staffName)+"') " +
					 " AND " + staffNo + " IS NOT NULL) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	
	public KTQueryChain staffRank(List<String> staffRank)
	{
		String newQuery = query;
		if(!CollectionUtils.isEmpty(staffRank)) {
			String staffNo = "STAFF.STAFF_NUMBER";
			newQuery += " AND (( " ;
			int orInd = staffRank.size() - 1;
			for(int i = 0 ; i < staffRank.size() ; ++i) {
				newQuery +=	" ','||STAFF.RANK_CODE||',' LIKE '%," + 
							PersistenceManager.escapeSql(staffRank.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) AND " + staffNo + " IS NOT NULL) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	
	public KTQueryChain isAcadStaff(String acadStaff) {
		String newQuery = query;
		if(acadStaff != null) {
			if(acadStaff.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND ( " + 
						 " STAFF.ACAD_STAFF_IND IS NOT NULL " +
						 " AND STAFF.STAFF_NUMBER IS NOT NULL) ";
			else if(!acadStaff.equals(EMPTY_VALUE)) 
				newQuery += " AND ( " + 
						 "STAFF.ACAD_STAFF_IND = '" + PersistenceManager.escapeSql(acadStaff) + "' " +
						 "AND STAFF.STAFF_NUMBER IS NOT NULL) ";

			// do nothing when empty
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	
	public KTQueryChain department(List<String> department) {
		String newQuery = query;
		if(!CollectionUtils.isEmpty(department)) {
			String staffNo = "STAFF.STAFF_NUMBER";
			newQuery += " AND (( " ;
			int orInd = department.size() - 1;
			for(int i = 0 ; i < department.size() ; ++i) {
				newQuery +=	" ','||STAFF.DEPT_CODE||',' LIKE '%," + 
							PersistenceManager.escapeSql(department.get(i)) + ",%' ";
				if(i != orInd)
					newQuery += " OR ";
			}
			newQuery +=	" ) AND " + staffNo + " IS NOT NULL) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	

	public KTQueryChain exStaffName(String exStaffName)
	{
		String newQuery = query;
		if(exStaffName != null) {
			String staffNo = "EXSTAFF.STAFF_NUMBER";
//			newQuery += " AND (UPPER(EXSTAFF.FULLNAME) LIKE UPPER('%" + 
//					 PersistenceManager.escapeSql(exStaffName)+"%') " +
//					 " AND " + staffNo + " IS NOT NULL) ";
			newQuery += " AND (UPPER(EXSTAFF.FULLNAME) = UPPER('" + 
					 PersistenceManager.escapeSql(exStaffName)+"') " +
					 " AND " + staffNo + " IS NOT NULL) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain formStaffNum(String formStaffNum)
	{
		String newQuery = query;
		if(formStaffNum != null) {
			newQuery += " AND EXSTAFF.STAFF_NUMBER = '" + 
					 PersistenceManager.escapeSql(formStaffNum)+"' ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain formNo(String formNo)
	{
		String newQuery = query;
		if(formNo != null) {
			newQuery += " AND " +
					JPAUtils.convertQueryParamCol("PH."+searchNo, formNo.split(","), String.class)+" ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain dataLevel(String dataLevel)
	{
		String newQuery = query;
		if(dataLevel != null) {
			newQuery += " AND PH.DATA_LEVEL = '" + 
					 PersistenceManager.escapeSql(dataLevel)+"' ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain title(String title)
	{
		String newQuery = query;
		if(title != null) {
			newQuery += " AND UPPER(PH.TITLE) LIKE(UPPER('%" +
					PersistenceManager.escapeSql(title) + "%') ) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}

	public KTQueryChain cdcfStatus(String cdcfStatus) {
		String newQuery = query;
		if(cdcfStatus != null) {
			if(cdcfStatus.equals(SELECT_ALL_VALUE)) 
				newQuery += " AND " + 
						 " QH.CDCF_STATUS IS NOT NULL ";
			else if(!cdcfStatus.equals(EMPTY_VALUE)) 
				newQuery += " AND " + 
						 " QH.CDCF_STATUS = '" + PersistenceManager.escapeSql(cdcfStatus) + "' ";
			// do nothing when empty
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain ktDate(Date fromDate, Date toDate) {
		String newQuery = query;
		if(fromDate != null || toDate != null) {
			DateFormat dmyFormat = new SimpleDateFormat("dd/MM/yyyy");
			if(fromDate == null) {
				fromDate = new GregorianCalendar(1,1,1).getTime();
			}
			if(toDate == null) {
				toDate = new GregorianCalendar(9000,12,31).getTime();
			}
			
			if(ktType != null) {
				newQuery += " AND PH.FORM_NO IN ( " +
						" SELECT DISTINCT FORM_NO FROM ( " +
						" SELECT FORM_NO, NVL(START_DATE,TO_DATE('1/1/1000', 'DD/MM/YYYY')) as DATESTART" +
						", NVL(END_DATE, START_DATE) as DATEEND " +
						" FROM (SELECT * FROM RH_P_" + ktType + "_HDR WHERE DATA_LEVEL NOT IN ('M', 'N'))) " +
						" WHERE NOT (" +
						" (DATEEND < TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" OR " +
						" (DATESTART > TO_DATE('" + dmyFormat.format(toDate) + "', 'DD/MM/YYYY')) " +
						" ) " +
						" ) ";
			}
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain facDept(List<String> facDeptList, String viewType) {
		String newQuery = query;
		if(!CollectionUtils.isEmpty(facDeptList)) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<String> allFacList = l1List.stream().map(LookupValue -> LookupValue.getPk()
									.getLookup_code()).collect(Collectors.toList());
			List<String> facList = new ArrayList<String>(allFacList);
			facList.retainAll(facDeptList);
			List<String> deptList = new ArrayList<String>(facDeptList);
			deptList.removeAll(facList);
			
			if(!viewType.equals("D")) {
				for(String fac : facList) {
					List<String> allDeptInFac = dao.getDeptListByFac(fac);
					deptList.addAll(allDeptInFac);
				}
				if(!CollectionUtils.isEmpty(deptList)) {
					String staffNo = "STAFF.STAFF_NUMBER";
					newQuery += " AND (( " ;
					int orInd = deptList.size() - 1;
					for(int i = 0 ; i < deptList.size() ; ++i) {
						newQuery +=	" ','||STAFF.DEPT_CODE||',' LIKE '%," + 
									PersistenceManager.escapeSql(deptList.get(i)) + ",%' ";
						if(i != orInd)
							newQuery += " OR ";
					}
					newQuery +=	" ) AND " + staffNo + " IS NOT NULL) ";
				}
			}
			else {
				newQuery += " AND ( " ;
				if(!facList.isEmpty()) {
					newQuery += " (PH.FAC IN (";
					for(int i = 0 ; i < facList.size() ; ++i) {
						newQuery += "'"+PersistenceManager.escapeSql(facList.get(i))+"'";
						if(i != facList.size() - 1) newQuery += ",";
					}
					newQuery += " ) AND PH.DEPT IS NULL ) ";
					if(!deptList.isEmpty()) newQuery += " OR ";
				}
				if(!deptList.isEmpty()) {
					newQuery += JPAUtils.convertQueryParamCol("PH.DEPT", deptList, String.class) + " ";
				}
				newQuery +=	" ) ";
			}
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain riFirstPublish(Date fromDate, Date toDate) {
		String newQuery = query;
		if(fromDate != null || toDate != null) {
			DateFormat dmyFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date toDateTmr = tmrDate(toDate);;
			if(fromDate == null) {
				fromDate = new GregorianCalendar(1,1,1).getTime();
			}
			if(toDateTmr == null) {
				toDateTmr = new GregorianCalendar(9000,12,31).getTime();
			}
			
			newQuery += " AND ( " +
						" (PH.CREATION_TIME <= TO_DATE('" + dmyFormat.format(toDateTmr) + "', 'DD/MM/YYYY')) " +
						" AND " + 
						" (PH.CREATION_TIME >= TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" ) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain riLastPublish(Date fromDate, Date toDate) {
		String newQuery = query;
		if(fromDate != null || toDate != null) {
			DateFormat dmyFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date toDateTmr = tmrDate(toDate);;
			if(fromDate == null) {
				fromDate = new GregorianCalendar(1,1,1).getTime();
			}
			if(toDateTmr == null) {
				toDateTmr = new GregorianCalendar(9000,12,31).getTime();
			}
			
			newQuery += " AND ( " +
						" (QH.LAST_PUBLISHED_DATE <= TO_DATE('" + dmyFormat.format(toDateTmr) + "', 'DD/MM/YYYY')) " +
						" AND " + 
						" (QH.LAST_PUBLISHED_DATE >= TO_DATE('" + dmyFormat.format(fromDate) + "', 'DD/MM/YYYY')) " +
						" ) ";
		}
		return new KTQueryChain(ktType, newQuery);
	}
	
	public KTQueryChain end() {
		String newQuery = query;
		newQuery += " AND 1=0 "; 
		return new KTQueryChain(ktType, newQuery);
	}
	
	
	@Override
	public <T extends Object> KTQueryChain filterByIdList(List<T> idList)
	{
		Class cls = CollectionUtils.isNotEmpty(idList) ? idList.get(0).getClass() : null;
		String newQuery = query + "AND " + JPAUtils.convertQueryParamCol("PH."+searchNo, idList, cls) + " ";
		return new KTQueryChain(ktType, newQuery);
	}
	
	
	@Override
	public List<Integer> queryIdList()
	{
		List<Integer> resultList = new ArrayList<Integer>();
		long t = System.currentTimeMillis();
		
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		logger.log(LVL_QUERY, "query=" + query);
		
		// Execute the query
		if (!GenericValidator.isBlankOrNull(query))
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) resultList.add(rs.getInt(1));
			}
			catch (SQLException se)
			{
				logger.log(Level.WARNING, "", se);
				throw new RuntimeException(se);
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution time=" + (System.currentTimeMillis()-t) + "ms");
		return resultList;
	}


	@Override
	public String toString()
	{
		return "RIQueryChain [ktType=" + ktType + ", searchNo=" + searchNo + ", query=" + query + "]";
	}
	
	private Date tmrDate(Date date) {
		if(date != null) {
			Date toDateTmr = new Date();
			Calendar c = Calendar.getInstance(); 
			c.setTime(date); 
			c.add(Calendar.DATE, 1);
			toDateTmr = c.getTime();
			return toDateTmr;
		}
		else return null;
	}
	
	

	
}
