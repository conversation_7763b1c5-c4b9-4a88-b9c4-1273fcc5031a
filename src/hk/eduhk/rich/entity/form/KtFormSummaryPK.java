package hk.eduhk.rich.entity.form;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class KtFormSummaryPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="form_no")
	private Integer formNo;
	
	@Column(name="data_level")
	private String dataLevel;

	
	public Integer getFormNo()
	{
		return formNo;
	}

	
	public void setFormNo(Integer formNo)
	{
		this.formNo = formNo;
	}

	
	public String getDataLevel()
	{
		return dataLevel;
	}

	
	public void setDataLevel(String dataLevel)
	{
		this.dataLevel = dataLevel;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dataLevel == null) ? 0 : dataLevel.hashCode());
		result = prime * result + ((formNo == null) ? 0 : formNo.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormSummaryPK other = (KtFormSummaryPK) obj;
		if (dataLevel == null)
		{
			if (other.dataLevel != null)
				return false;
		}
		else if (!dataLevel.equals(other.dataLevel))
			return false;
		if (formNo == null)
		{
			if (other.formNo != null)
				return false;
		}
		else if (!formNo.equals(other.formNo))
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "KtFormSummaryPK [formNo=" + formNo + ", dataLevel=" + dataLevel + "]";
	}
	
}
