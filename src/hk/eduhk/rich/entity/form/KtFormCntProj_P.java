package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_CNT_PROJ_HDR")
public class KtFormCntProj_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_CNT_PROJ";
	
	public static Logger logger = Logger.getLogger(KtFormCntProj_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "act_code")
	private String act_code;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "principal_inves")
	private String principal_inves;
	
	@Column(name = "fund_src")
	private String fund_src;
	
	@Column(name = "budget")
	private Double  budget;
	
	@Column(name = "income_rpt_unit")
	private Double  income_rpt_unit;
	
	@Column(name = "income_fo")
	private Double  income_fo;
	
	@Column(name = "income_fo_rem")
	private String  income_fo_rem;
	
	@Column(name = "income_rdo")
	private Double  income_rdo;
	
	@Column(name = "income_grant")
	private String  income_grant;
	
	@Column(name = "num_key_partner")
	private Integer  num_key_partner;
	
	@Column(name = "num_teacher")
	private Integer  num_teacher;
	
	@Column(name = "num_principal")
	private Integer  num_principal;
	
	@Column(name = "num_stakeholder")
	private Integer  num_stakeholder;

	@Column(name = "num_school")
	private Integer  num_school;
	
	@Column(name = "num_stu")
	private Integer  num_stu;
	
	@Column(name = "num_ext_prof")
	private Integer  num_ext_prof;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;

	@Column(name = "research_element")
	private String  research_element;
	
	@Column(name = "ownership_ip_right")
	private String  ownership_ip_right;
	
	@Column(name = "support_fo")
	private String  support_fo;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	@Transient
	private String[] ownership_ip_right_array = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public String getAct_code()
	{
		return act_code;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public String getDept()
	{
		return dept;
	}

	
	public Date getStart_date()
	{
		return start_date;
	}

	
	public Date getEnd_date()
	{
		return end_date;
	}

	
	public String getPrincipal_inves()
	{
		return principal_inves;
	}

	
	public String getFund_src()
	{
		return fund_src;
	}

	
	public Double getBudget()
	{
		return budget;
	}

	
	public Double getIncome_rpt_unit()
	{
		return income_rpt_unit;
	}

	
	public Double getIncome_fo()
	{
		return income_fo;
	}

	
	public String getIncome_fo_rem()
	{
		return income_fo_rem;
	}

	
	public Double getIncome_rdo()
	{
		return income_rdo;
	}

	
	public String getIncome_grant()
	{
		return income_grant;
	}

	
	public Integer getNum_key_partner()
	{
		return num_key_partner;
	}

	
	public Integer getNum_teacher()
	{
		return num_teacher;
	}

	
	public Integer getNum_principal()
	{
		return num_principal;
	}

	
	public Integer getNum_stakeholder()
	{
		return num_stakeholder;
	}

	
	public Integer getNum_school()
	{
		return num_school;
	}

	
	public Integer getNum_stu()
	{
		return num_stu;
	}

	
	public Integer getNum_ext_prof()
	{
		return num_ext_prof;
	}

	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}

	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}

	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}

	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public void setDept(String dept)
	{
		this.dept = dept;
	}

	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}

	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}

	
	public void setPrincipal_inves(String principal_inves)
	{
		this.principal_inves = principal_inves;
	}

	
	public void setFund_src(String fund_src)
	{
		this.fund_src = fund_src;
	}

	
	public void setBudget(Double budget)
	{
		this.budget = budget;
	}

	
	public void setIncome_rpt_unit(Double income_rpt_unit)
	{
		this.income_rpt_unit = income_rpt_unit;
	}

	
	public void setIncome_fo(Double income_fo)
	{
		this.income_fo = income_fo;
	}

	
	public void setIncome_fo_rem(String income_fo_rem)
	{
		this.income_fo_rem = income_fo_rem;
	}

	
	public void setIncome_rdo(Double income_rdo)
	{
		this.income_rdo = income_rdo;
	}

	
	public void setIncome_grant(String income_grant)
	{
		this.income_grant = income_grant;
	}

	public void setNum_key_partner(Integer num_key_partner)
	{
		this.num_key_partner = num_key_partner;
	}

	
	public void setNum_teacher(Integer num_teacher)
	{
		this.num_teacher = num_teacher;
	}

	
	public void setNum_principal(Integer num_principal)
	{
		this.num_principal = num_principal;
	}

	
	public void setNum_stakeholder(Integer num_stakeholder)
	{
		this.num_stakeholder = num_stakeholder;
	}

	
	public void setNum_school(Integer num_school)
	{
		this.num_school = num_school;
	}

	
	public void setNum_stu(Integer num_stu)
	{
		this.num_stu = num_stu;
	}

	
	public void setNum_ext_prof(Integer num_ext_prof)
	{
		this.num_ext_prof = num_ext_prof;
	}

	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}

	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}

	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}
	
	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}
	
	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}


	
	public String getResearch_element()
	{
		return research_element;
	}


	
	public void setResearch_element(String research_element)
	{
		this.research_element = research_element;
	}


	
	public String getOwnership_ip_right()
	{
		return ownership_ip_right;
	}


	
	public void setOwnership_ip_right(String ownership_ip_right)
	{
		this.ownership_ip_right = ownership_ip_right;
	}


	
	public String getSupport_fo()
	{
		return support_fo;
	}


	
	public void setSupport_fo(String support_fo)
	{
		this.support_fo = support_fo;
	}


	
	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}


	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}


	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}


	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}


	public String[] getOwnership_ip_right_array()
	{
		if (ownership_ip_right_array == null) {
			if(!GenericValidator.isBlankOrNull(getOwnership_ip_right()))
			{
				ownership_ip_right = ownership_ip_right.replace(" ", "");
				ownership_ip_right_array = ownership_ip_right.split(",");
			}else {
				ownership_ip_right_array = new String[0];
			}
		}
		return ownership_ip_right_array;
	}


	
	public void setOwnership_ip_right_array(String[] ownership_ip_right_array)
	{
		this.ownership_ip_right_array = ownership_ip_right_array;
		if (ownership_ip_right_array != null) {
			ownership_ip_right = "";
			for (int i = 0; i < ownership_ip_right_array.length; i++) {
				ownership_ip_right += ownership_ip_right_array[i];
				if (i < ownership_ip_right_array.length-1) {
					ownership_ip_right += ",";
				}
			}
		}
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormCntProj_P other = (KtFormCntProj_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormCntProj_P [pk=" + pk + ", title=" + title + ", act_code=" + act_code + ", fac=" + fac + ", dept="
				+ dept + ", start_date=" + start_date + ", end_date=" + end_date + ", principal_inves="
				+ principal_inves + ", fund_src=" + fund_src + ", budget=" + budget + ", income_rpt_unit="
				+ income_rpt_unit + ", income_fo=" + income_fo + ", income_fo_rem=" + income_fo_rem + ", income_rdo="
				+ income_rdo + ", income_grant=" + income_grant + ", num_key_partner=" + num_key_partner
				+ ", num_teacher=" + num_teacher + ", num_principal=" + num_principal + ", num_stakeholder="
				+ num_stakeholder + ", num_school=" + num_school + ", num_stu=" + num_stu + ", num_ext_prof="
				+ num_ext_prof + ", remarks_staff=" + remarks_staff + ", remarks_dept=" + remarks_dept + ", remarks_kt="
				+ remarks_kt + ", research_element=" + research_element + ", ownership_ip_right=" + ownership_ip_right
				+ ", support_fo=" + support_fo + "]";
	}

}
