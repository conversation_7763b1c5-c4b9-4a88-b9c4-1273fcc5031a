package hk.eduhk.rich.entity.form;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;


import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.io.IOUtils;
import org.jdom2.*;
import org.jdom2.output.*;
import org.jdom2.output.support.*;
import org.jdom2.transform.JDOMResult;
import org.jdom2.transform.JDOMSource;

import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.util.MimeMap;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.BaseView;


@SuppressWarnings("serial")
@ManagedBean(name = "genKtActXmlView")
@ViewScoped
public class GenKtActXmlView extends BaseView
{
	private Logger logger = Logger.getLogger(getClass().getName());
	public String createKtActXmlFile()
	{		
		String xmlString = "";
		try
		{
			Document doc=new Document();
			//Root Element
			Namespace ns1 = Namespace.getNamespace("v1.unified.activity.pure.atira.dk");	
			Namespace ns2 = Namespace.getNamespace("ns2", "v3.commons.pure.atira.dk");
			
			Element root=new Element("activities", ns1);
			root.addNamespaceDeclaration(ns2);
			doc.setRootElement(root);
					
			FormDAO dao = FormDAO.getInstance();
			List<PureKtFormHeader> headerList = dao.getPureKtFormHeaderList();
			for(PureKtFormHeader h:headerList) {
				//Activities Element
				Element otherActivity=new Element("otherActivity", ns1);
				otherActivity.setAttribute("id", "kt"+h.getForm_no());
				otherActivity.setAttribute("type", "otheractivity/"+h.getForm_code());
				
				//otherActivity description
				Element description=new Element("description", ns1);
				description.addContent(h.getAct_desc());
				otherActivity.addContent(description);
				
				//otherActivity managedBy
				Element managedBy=new Element("managedBy", ns1);
				managedBy.setAttribute("lookupId", "u00001");
				//managedBy.addContent("u00001");
				otherActivity.addContent(managedBy);
				
				//otherActivity period
				Element period=new Element("period", ns1);
				
				//startDate
				Element startDate =new Element("startdate", ns2);
				Element startDateYear =new Element("year", ns2);
				Element startDateMonth =new Element("month", ns2);
				Element startDateDay =new Element("day", ns2);
				
				if (h.getStart_date() != null) {
					LocalDate localDate = h.getStart_date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					Integer startYear = localDate.getYear();
					Integer startMonth = localDate.getMonthValue();
					Integer startDay = localDate.getDayOfMonth();
					startDateYear.addContent(startYear.toString());
					startDateMonth.addContent(startMonth.toString());
					startDateDay.addContent(startDay.toString());
					startDate.addContent(startDateYear);
					startDate.addContent(startDateMonth);
					startDate.addContent(startDateDay);
					period.addContent(startDate);
				}
				
				//endDate
				Element endDate =new Element("enddate", ns2);
				Element endDateYear =new Element("year", ns2);
				Element endDateMonth =new Element("month", ns2);
				Element endDateDay =new Element("day", ns2);

				if (h.getEnd_date() != null) {
					LocalDate localDate = h.getEnd_date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					Integer endYear = localDate.getYear();
					Integer endMonth = localDate.getMonthValue();
					Integer endDay = localDate.getDayOfMonth();
					endDateYear.addContent(endYear.toString());
					endDateMonth.addContent(endMonth.toString());
					endDateDay.addContent(endDay.toString());
					endDate.addContent(endDateYear);
					endDate.addContent(endDateMonth);
					endDate.addContent(endDateDay);
					period.addContent(endDate);
				}
				
				otherActivity.addContent(period);
				
				//visibility
				Element visibility = new Element("visibility", ns1);
				visibility.addContent("Public");
				otherActivity.addContent(visibility);
				
				//otherActivity title
				Element title=new Element("title", ns1);
				title.addContent(h.getAct_title());
				otherActivity.addContent(title);
				
				//otherActivity personAssociations
				Element personAssociations=new Element("personAssociations", ns1);
				int countExPerson = 1;
				if (h.getFormDetails() != null) {
					for(PureKtFormDetails d:h.getFormDetails()) {
						if (d != null) {
							Element person=new Element("person", ns1);
							if ("Y".equals(d.getExternal_ind())) {
								//external participant
								person.setAttribute("id", "kt" + h.getForm_no()+"_participant"+countExPerson);
								Element personOrigin=new Element("person", ns1);
								personOrigin.setAttribute("origin", "external");
								//firstName
								Element firstName=new Element("firstName", ns1);
								firstName.addContent(d.getFirst_name());
								personOrigin.addContent(firstName);
								//lastName
								Element lastName=new Element("lastName", ns1);
								lastName.addContent(d.getLast_name());
								personOrigin.addContent(lastName);
								person.addContent(personOrigin);
								//role
								Element role=new Element("role", ns1);
								role.addContent(d.getRole());
								person.addContent(role);
								countExPerson++;
							}else {
								//internal participant
								person.setAttribute("id", d.getPerson_source_id());
								Element personLookupId=new Element("person", ns1);
								personLookupId.setAttribute("lookupId", d.getPerson_source_id());
								person.addContent(personLookupId);
								//role
								Element role=new Element("role", ns1);
								role.addContent(d.getRole());
								person.addContent(role);
							}
							personAssociations.addContent(person);
						}
					}
				}
				otherActivity.addContent(personAssociations);
				
				//otherActivity organisations
				Element organisations=new Element("organisations", ns1);
				Element organisation=new Element("organisation", ns1);
				organisation.setAttribute("origin", "external");
				organisation.setAttribute("lookupId", "kt"+h.getForm_no()+"_org1");
				Element organisationName=new Element("name", ns1);
				organisationName.addContent("The Education University of Hong Kong");
				organisation.addContent(organisationName);
				organisations.addContent(organisation);
				otherActivity.addContent(organisations);
				/*
				//grantingOrganisations
				Element grantingOrganisations=new Element("grantingOrganisations", ns1);
				Element organisation=new Element("organisation", ns1);
				organisation.setAttribute("lookupId", "a"+a.getAward_no()+"_org1");
				organisation.setAttribute("origin", "external");
				Element organisation_name=new Element("name", ns1);
				organisation_name.addContent(a.getOrg_name());
				organisation.addContent(organisation_name);
				grantingOrganisations.addContent(organisation);
				prize.addContent(grantingOrganisations);
				
				//get award details
				List<AwardDetails> details = dao.getAwardDetails(a.getAward_no());
				
				//Receivers of the prize
				Element receivers=new Element("receiversOfPrize", ns1);
				int countReceiver = 1;
				for(AwardDetails d:details) {
					Element receiver=new Element("receiverOfPrize", ns1);
					receiver.setAttribute("id", "a" + a.getAward_no()+"_reciever"+countReceiver);
				
					Element person=new Element("person", ns1);
					
					//If external receiver, show firstName and ListName
					if (d.getExternal_ind().equals("Y")) {
						person.setAttribute("origin", "external");
						
						//First name
						Element receiver_firstName=new Element("firstName", ns1);
						receiver_firstName.addContent(d.getFirst_name());
						person.addContent(receiver_firstName);
						
						//Last name
						Element receiver_lastName=new Element("lastName", ns1);
						receiver_lastName.addContent(d.getLast_name());
						person.addContent(receiver_lastName);
					}else {
						person.setAttribute("lookupId", d.getPerson_source_id());
					}
					
					Element personRole=new Element("personRole", ns1);
					personRole.addContent(d.getRole());
					receiver.addContent(person);
					receiver.addContent(personRole);
					receivers.addContent(receiver);
					countReceiver++;
				}
				
				prize.addContent(receivers);
				
				//managedBy
				Element managedBy =new Element("managedBy", ns1);
				managedBy.setAttribute("lookupId", "u00001");
				prize.addContent(managedBy);
				
				//Prize awarded date
				Element awardedDate =new Element("awardedDate", ns1);
				
				Element year =new Element("year", ns2);
				Element month =new Element("month", ns2);
				Element day =new Element("day", ns2);
				
				if(a.getAward_year() != null) {
					year.addContent(a.getAward_year().toString());
					awardedDate.addContent(year);
				}
				if(a.getAward_month() != null) {
					month.addContent(a.getAward_month().toString());
					awardedDate.addContent(month);
				}
				if(a.getAward_day() != null) {
					day.addContent(a.getAward_day().toString());
					awardedDate.addContent(day);
				}
				prize.addContent(awardedDate);
				*/
				
				
				//Add in the root Element
				root.addContent(otherActivity);		
			}
			xmlString = transform(doc);
		}
		catch (TransformerFactoryConfigurationError | TransformerException e)
		{
			logger.log(Level.WARNING, "Cannot generate xml", e);
		}
		return xmlString;

		
		//return new XMLOutputter().outputString(doc);
		/*String fileName = "pureAward.xml";
		XMLOutputter outter=new XMLOutputter(Format.getPrettyFormat(), XMLOUTPUT);
		outter.output(doc, new FileWriter("D:"+File.separator+fileName));*/
	}
	
	private String transform(Document sourceDoc) throws TransformerException {
        JDOMSource source = new JDOMSource(sourceDoc);
        StreamResult result = new StreamResult(new StringWriter());

        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
        transformer.transform(source, result);

        return result.getWriter().toString();
    }
	
	private String getXmlFilePath()
	{
		String code = Constant.isLocalEnv() ? SysParam.PARAM_FILE_PATH_LOCAL : SysParam.PARAM_FILE_PATH;
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		return paramDAO.getSysParamValueByCode(code);
	}
	
	public String getSysParamValue(String code) {
		String value = "";
		SysParamDAO sysParamDao = SysParamDAO.getInstance();
		SysParam tmp = sysParamDao.getSysParamByCode(code);
		if (tmp != null) {
			value = tmp.getValue();
		}
		return value;
	}
	
	public String skipInValidXMLChars(String in) {
        StringBuffer out = new StringBuffer();
        char current;

        if (in == null || ("".equals(in))) return "";
        for (int i = 0; i < in.length(); i++) {
            current = in.charAt(i);
            if ((current == 0x9) ||
                (current == 0xA) ||
                (current == 0xD) ||
                ((current >= 0x20) && (current <= 0xD7FF)) ||
                ((current >= 0xE000) && (current <= 0xFFFD)) ||
                ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }  
	
	public static final XMLOutputProcessor XMLOUTPUT = new AbstractXMLOutputProcessor() {
	    @Override
	    protected void printDeclaration(final Writer out, final FormatStack fstack) throws IOException {
	        write(out, "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?> ");
	        write(out, fstack.getLineSeparator());
	    }
	};
	
}
