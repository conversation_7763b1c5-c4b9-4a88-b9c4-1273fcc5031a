package hk.eduhk.rich.entity.form;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.param.SysParam;

@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_FORM_DTL")
public class KtFormDetails_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(KtFormDetails_P.class.toString());
	
	@EmbeddedId
	private KtFormDetails_P_PK pk = new KtFormDetails_P_PK();

	@Column(name = "non_ied_staff_flag")
	private String flag;	
	
	@Column(name = "pid")
	private Integer pid;
	
	@Column(name = "staff_no")
	private String staff_no;
	
	@Column(name = "name")
	private String name;

	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false)
	private KtFormState_Q ktFormState_q = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormHeader_Q ktFormHeader_q = null;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "staff_no", referencedColumnName = "staff_no", insertable = false, updatable = false)
	})
	private KtFormDetails_Q ktFormDetails_q = null;
	
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormCPD_P ktFormCPD_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormProfConf_P ktFormProfConf_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormSem_P ktFormSem_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormCntProj_P ktFormCntProj_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormInn_P ktFormInn_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormCons_P ktFormCons_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormProfEngmt_P ktFormProfEngmt_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormIP_P ktFormIP_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormSocEngmt_P ktFormSocEngmt_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormStaffEngmt_P ktFormStaffEngmt_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormEA_P ktFormEA_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormStartup_P ktFormStartup_p = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "form_no", referencedColumnName = "form_no", insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", insertable = false, updatable = false)
	})
	private KtFormInvAward_P ktFormInvAward_p = null;
	
	public KtFormDetails_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(KtFormDetails_P_PK pk)
	{
		this.pk = pk;
	}

	
	public String getFlag()
	{
		return flag;
	}

	
	public void setFlag(String flag)
	{
		this.flag = flag;
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}

	
	public String getName()
	{
		return name;
	}


	
	public void setName(String name)
	{
		this.name = name;
	}


	public KtFormHeader_Q getKtFormHeader_q()
	{
		if (ktFormHeader_q != null) {
			try {
				ktFormHeader_q.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					ktFormHeader_q = dao.getKtFormHeader_Q(getPk().getForm_no(), getPk().getData_level());
				}
				else
				{
					throw re;
				}
			}
		}
		return ktFormHeader_q;
	}



	public KtFormDetails_Q getKtFormDetails_q()
	{
		if (ktFormDetails_q != null) {
			try {
				ktFormDetails_q.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					ktFormDetails_q = dao.getKtFormDetails_Q(getPk().getForm_no(), getStaff_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return ktFormDetails_q;
	}


	public KtFormState_Q getKtFormState_q()
	{
		if (ktFormState_q != null) {
			try {
				ktFormState_q.getForm_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					ktFormState_q = dao.getKtFormState_Q(getPk().getForm_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return ktFormState_q;
	}


	
	public void setKtFormState_q(KtFormState_Q ktFormState_q)
	{
		this.ktFormState_q = ktFormState_q;
	}


	public KtFormCPD_P getKtFormCPD_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_CPD.equals(getKtFormState_q().getForm_code())){
				if (ktFormCPD_p != null) {
					try {
						ktFormCPD_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormCPD_p = dao.getKtFormCPD_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormCPD_p;
	}


	public void setKtFormCPD_p(KtFormCPD_P ktFormCPD_p)
	{
		this.ktFormCPD_p = ktFormCPD_p;
	}
	
	
	public KtFormProfConf_P getKtFormProfConf_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_PROF_CONF.equals(getKtFormState_q().getForm_code())){
				if (ktFormProfConf_p != null) {
					try {
						ktFormProfConf_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormProfConf_p = dao.getKtFormProfConf_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormProfConf_p;
	}


	
	public void setKtFormProfConf_p(KtFormProfConf_P ktFormProfConf_p)
	{
		this.ktFormProfConf_p = ktFormProfConf_p;
	}


	
	public KtFormSem_P getKtFormSem_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_SEM.equals(getKtFormState_q().getForm_code())){
				if (ktFormSem_p != null) {
					try {
						ktFormSem_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormSem_p = dao.getKtFormSem_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormSem_p;
	}


	
	public void setKtFormSem_p(KtFormSem_P ktFormSem_p)
	{
		this.ktFormSem_p = ktFormSem_p;
	}


	
	public KtFormCntProj_P getKtFormCntProj_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_CNT_PROJ.equals(getKtFormState_q().getForm_code())){
				if (ktFormCntProj_p != null) {
					try {
						ktFormCntProj_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormCntProj_p = dao.getKtFormCntProj_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormCntProj_p;
	}


	
	public void setKtFormCntProj_p(KtFormCntProj_P ktFormCntProj_p)
	{
		this.ktFormCntProj_p = ktFormCntProj_p;
	}


	
	public KtFormInn_P getKtFormInn_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_INN.equals(getKtFormState_q().getForm_code())){
				if (ktFormInn_p != null) {
					try {
						ktFormInn_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormInn_p = dao.getKtFormInn_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormInn_p;
	}


	
	public void setKtFormInn_p(KtFormInn_P ktFormInn_p)
	{
		this.ktFormInn_p = ktFormInn_p;
	}


	
	public KtFormCons_P getKtFormCons_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_CONS.equals(getKtFormState_q().getForm_code())){
				if (ktFormCons_p != null) {
					try {
						ktFormCons_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormCons_p = dao.getKtFormCons_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormCons_p;
	}


	
	public void setKtFormCons_p(KtFormCons_P ktFormCons_p)
	{
		this.ktFormCons_p = ktFormCons_p;
	}


	
	public KtFormProfEngmt_P getKtFormProfEngmt_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_PROF_ENGMT.equals(getKtFormState_q().getForm_code())){
				if (ktFormProfEngmt_p != null) {
					try {
						ktFormProfEngmt_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormProfEngmt_p = dao.getKtFormProfEngmt_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormProfEngmt_p;
	}


	
	public void setKtFormProfEngmt_p(KtFormProfEngmt_P ktFormProfEngmt_p)
	{
		this.ktFormProfEngmt_p = ktFormProfEngmt_p;
	}


	
	public KtFormIP_P getKtFormIP_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_IP.equals(getKtFormState_q().getForm_code())){
				if (ktFormIP_p != null) {
					try {
						ktFormIP_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormIP_p = dao.getKtFormIP_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormIP_p;
	}


	
	public void setKtFormIP_p(KtFormIP_P ktFormIP_p)
	{
		this.ktFormIP_p = ktFormIP_p;
	}


	
	public KtFormSocEngmt_P getKtFormSocEngmt_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_SOC_ENGMT.equals(getKtFormState_q().getForm_code())){
				if (ktFormSocEngmt_p != null) {
					try {
						ktFormSocEngmt_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormSocEngmt_p = dao.getKtFormSocEngmt_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormSocEngmt_p;
	}


	
	public void setKtFormSocEngmt_p(KtFormSocEngmt_P ktFormSocEngmt_p)
	{
		this.ktFormSocEngmt_p = ktFormSocEngmt_p;
	}


	
	public KtFormStaffEngmt_P getKtFormStaffEngmt_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_STAFF_ENGMT.equals(getKtFormState_q().getForm_code())){
				if (ktFormStaffEngmt_p != null) {
					try {
						ktFormStaffEngmt_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormStaffEngmt_p = dao.getKtFormStaffEngmt_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormStaffEngmt_p;
	}


	
	public void setKtFormStaffEngmt_p(KtFormStaffEngmt_P ktFormStaffEngmt_p)
	{
		this.ktFormStaffEngmt_p = ktFormStaffEngmt_p;
	}


	
	public KtFormEA_P getKtFormEA_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_EA.equals(getKtFormState_q().getForm_code())){
				if (ktFormEA_p != null) {
					try {
						ktFormEA_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormEA_p = dao.getKtFormEA_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormEA_p;
	}


	
	public void setKtFormEA_p(KtFormEA_P ktFormEA_p)
	{
		this.ktFormEA_p = ktFormEA_p;
	}


	
	public KtFormStartup_P getKtFormStartup_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_STARTUP.equals(getKtFormState_q().getForm_code())){
				if (ktFormStartup_p != null) {
					try {
						ktFormStartup_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormStartup_p = dao.getKtFormStartup_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormStartup_p;
	}


	
	public void setKtFormStartup_p(KtFormStartup_P ktFormStartup_p)
	{
		this.ktFormStartup_p = ktFormStartup_p;
	}


	
	public KtFormInvAward_P getKtFormInvAward_p()
	{
		if (getKtFormState_q() != null) {
			if (SysParam.PARAM_KT_FORM_INV_AWARD.equals(getKtFormState_q().getForm_code())){
				if (ktFormInvAward_p != null) {
					try {
						ktFormInvAward_p.getPk();
					}catch (RuntimeException re)
					{
						if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
						{
							FormDAO dao = FormDAO.getInstance();
							ktFormInvAward_p = dao.getKtFormInvAward_P(getPk().getForm_no(), getPk().getData_level(), null, null, 0);
						}
						else
						{
							throw re;
						}
					}
				}
			}
		}
		return ktFormInvAward_p;
	}


	
	public void setKtFormInvAward_p(KtFormInvAward_P ktFormInvAward_p)
	{
		this.ktFormInvAward_p = ktFormInvAward_p;
	}


	public void setKtFormHeader_q(KtFormHeader_Q ktFormHeader_q)
	{
		this.ktFormHeader_q = ktFormHeader_q;
	}


	
	public void setKtFormDetails_q(KtFormDetails_Q ktFormDetails_q)
	{
		this.ktFormDetails_q = ktFormDetails_q;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormDetails_P other = (KtFormDetails_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormDetails_P [pk=" + pk + ", flag=" + flag + ", pid=" + pid + ", staff_no=" + staff_no + ", name="
				+ name + ", ktFormState_q=" + ktFormState_q + ", ktFormHeader_q=" + ktFormHeader_q
				+ ", ktFormDetails_q=" + ktFormDetails_q + "]";
	}


	

}
