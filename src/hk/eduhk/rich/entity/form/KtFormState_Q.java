package hk.eduhk.rich.entity.form;

import java.util.Date;
import java.sql.Timestamp;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@Entity
@Table(name = "RH_Q_KT_FORM_STATE")
public class KtFormState_Q extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(KtFormState_Q.class.toString());
	
	@Id
	@GeneratedValue(generator = "ktSeq")
	@SequenceGenerator(name = "ktSeq", sequenceName = "RH_RI_NO_SEQ", allocationSize = 1)
	@Column(name = "form_no")
	private int form_no;	
	
	@Column(name = "form_code")
	private String form_code;
	
	@Column(name = "creation_data_level")
	private String creation_data_level;

	@Column(name = "period_id")
	private Integer periodId;
	
	public int getForm_no()
	{
		return form_no;
	}

	
	public void setForm_no(int form_no)
	{
		this.form_no = form_no;
	}

	
	public String getForm_code()
	{
		return form_code;
	}

	
	public void setForm_code(String form_code)
	{
		this.form_code = form_code;
	}

	
	public String getCreation_data_level()
	{
		return creation_data_level;
	}

	
	public void setCreation_data_level(String creation_data_level)
	{
		this.creation_data_level = creation_data_level;
	}


	
	public Integer getPeriodId()
	{
		return periodId;
	}

	
	public void setPeriodId(Integer periodId)
	{
		this.periodId = periodId;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((creation_data_level == null) ? 0 : creation_data_level.hashCode());
		result = prime * result + form_no;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormState_Q other = (KtFormState_Q) obj;
		if (creation_data_level == null)
		{
			if (other.creation_data_level != null)
				return false;
		}
		else if (!creation_data_level.equals(other.creation_data_level))
			return false;
		if (form_no != other.form_no)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormState_Q [form_no=" + form_no + ", form_code=" + form_code + ", creation_data_level="
				+ creation_data_level + ", periodId=" + periodId + "]";
	}	
}
