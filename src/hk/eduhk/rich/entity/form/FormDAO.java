package hk.eduhk.rich.entity.form;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.operation.UploadForm;
import hk.eduhk.rich.entity.operation.UploadFormPK;
import hk.eduhk.rich.entity.operation.UploadStatus;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.util.JPAUtils;
import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.view.KTSearchPanel;
import hk.eduhk.rich.view.CDCFSearchPanel;

@SuppressWarnings("serial")
public class FormDAO extends BaseDAO
{

	private static FormDAO instance = null;


	public static synchronized FormDAO getInstance()
	{
		if (instance == null) instance = new FormDAO();
		return instance;
	}
	
	
	public static FormDAO getCacheInstance()
	{
		return FormDAO.getInstance();
	}	
	
	
	public List<KtForm> getKtFormList()
	{
		List<KtForm> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtForm obj ORDER BY obj.display_order";			
			TypedQuery<KtForm> q = em.createQuery(query, KtForm.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	
	@SuppressWarnings("static-access")
	public int updateKTFacDeptMap(String tableName) throws Exception
	{
		int row = 0;
		if (!Strings.isNullOrEmpty(tableName) )
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			
			String query =	  "UPDATE RICH.RH_P_"+ tableName + "_HDR T1 "
							+ "SET T1.FAC = (SELECT DEPT.FAC "
							+ "              FROM RICH.RH_Z_FAC_DEPT_V DEPT "
							+ "              WHERE T1.DEPT = DEPT.FAC_DEPT) "
							+ "WHERE T1.DEPT IS NOT NULL AND EXISTS (SELECT 1 "
							+ "              FROM RICH.RH_Z_FAC_DEPT_V DEPT "
							+ "              WHERE T1.DEPT = DEPT.FAC_DEPT AND T1.FAC <> DEPT.FAC)";
			
			//System.out.println("query:"+query);
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				row = pStmt.executeUpdate();
				//System.out.println("insertData row:"+row);
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return row;
	}
	
	
	public List<KtForm> getKtFormListWithCon(Boolean enabled)
	{
		List<KtForm> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtForm obj WHERE obj.is_enabled = :enabled ORDER BY obj.display_order";			
			TypedQuery<KtForm> q = em.createQuery(query, KtForm.class);
			q.setParameter("enabled", enabled);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	
	public List<KtFormDetails_P> getKtFormSubList(String form_code, String staff_no, String data_level, Date start_date, Date end_date, Integer periodId)
	{
		List<KtFormDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String where = "";
			String className = "";
			String dateSql = "";
			if (!Strings.isNullOrEmpty(form_code)) {
				switch(form_code) {
					case SysParam.PARAM_KT_FORM_CPD:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_CPD;
						break;
					case SysParam.PARAM_KT_FORM_PROF_CONF:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_PROF_CONF;
						break;
					case SysParam.PARAM_KT_FORM_SEM:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_SEM;
						break;
					case SysParam.PARAM_KT_FORM_CNT_PROJ:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_CNT_PROJ;
						break;
					case SysParam.PARAM_KT_FORM_INN:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_INN;
						break;
					case SysParam.PARAM_KT_FORM_CONS:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_CONS;
						break;
					case SysParam.PARAM_KT_FORM_PROF_ENGMT:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_PROF_ENGMT;
						break;
					case SysParam.PARAM_KT_FORM_IP:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_IP;
						break;
					case SysParam.PARAM_KT_FORM_SOC_ENGMT:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_SOC_ENGMT;
						break;
					case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_STAFF_ENGMT;
						break;
					case SysParam.PARAM_KT_FORM_EA:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_EA;
						break;
					case SysParam.PARAM_KT_FORM_STARTUP:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_STARTUP;
						break;
					case SysParam.PARAM_KT_FORM_INV_AWARD:
						className = "obj."+SysParam.PARAM_KT_FORM_CLASS_INV_AWARD;
						break;
				}
			}
			if (periodId > 1) {
				//dateSql = " AND ((NVL("+className+".start_date, '1900-01-01') > :start_date AND NVL("+className+".end_date, '2999-01-01') < :end_date))";
				dateSql += " AND (("+className+".start_date >= :start_date AND "+className+".start_date <= :end_date) ";
				dateSql += " OR ("+className+".end_date >= :start_date AND "+className+".end_date <= :end_date) ";
				dateSql += " OR ("+className+".end_date >= :start_date AND "+className+".start_date <= :start_date) ";
				//dateSql += " OR ("+className+".start_date is null AND "+className+".end_date <= :end_date) ";
				if (SysParam.PARAM_KT_FORM_INV_AWARD.equals(form_code)) {
					dateSql += ")";
				}else {
					dateSql += " OR ("+className+".start_date is null AND "+className+".end_date >= :start_date ) ";
					dateSql += " OR ("+className+".start_date <= :end_date AND "+className+".end_date is null)) ";
				}
			}
			if (!Strings.isNullOrEmpty(staff_no)) {
				where += " AND obj.staff_no = :staff_no ";
			}
			if (!Strings.isNullOrEmpty(form_code)) {
				where += " AND obj.ktFormState_q.form_code = :form_code ";
			}
			where += dateSql;
			String query = "SELECT obj FROM KtFormDetails_P obj WHERE obj.pk.data_level = :data_level " + where + " ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormDetails_P> q = em.createQuery(query, KtFormDetails_P.class);
			if (!Strings.isNullOrEmpty(form_code)) {
				q.setParameter("form_code", form_code);
			}
			q.setParameter("data_level", data_level);
			if (!Strings.isNullOrEmpty(staff_no)) {
				q.setParameter("staff_no", staff_no);
			}
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : new ArrayList<KtFormDetails_P>());
	}
	
	
	public List<KtFormDetails_P> getKtFormDetails_PList(String staffNum, String dataLevel)
	{
		List<KtFormDetails_P> objList = null;
		EntityManager em = null;
		
		if(!GenericValidator.isBlankOrNull(staffNum) && !GenericValidator.isBlankOrNull(dataLevel)) 
		{
			try 
			{
				em = getEntityManager();
				String query = "SELECT obj FROM KtFormDetails_P obj WHERE obj.staff_no = :staffNum AND obj.pk.data_level = :dataLevel ORDER BY obj.pk.form_no desc ";
				TypedQuery<KtFormDetails_P> q = em.createQuery(query, KtFormDetails_P.class);
				q.setParameter("staffNum", staffNum);
				q.setParameter("dataLevel", dataLevel);
				objList = q.getResultList();
			}
			finally 
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList : Collections.EMPTY_LIST);
	}
	
	public List<Integer> getUnimportedPLevelKTFormNoListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<Integer> voList = new ArrayList<Integer>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(deptList != null && formCode != null) {
			try
			{
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
			
			    sqlBuf.append(
			    		" SELECT DISTINCT PD.FORM_NO " );
			    sqlBuf.append(
			    		" FROM RH_P_KT_FORM_DTL PD  " +
			    		" LEFT JOIN RH_Q_KT_FORM_HDR QH ON QH.FORM_NO = PD.FORM_NO " + 
			    		" LEFT JOIN RH_Q_KT_FORM_STATE QS ON QS.FORM_NO = PD.FORM_NO " + 
			    		" LEFT JOIN RH_P_STAFF_IDENTITY_DEPT STAFF_DEPT ON (PD.STAFF_NO = STAFF_DEPT.STAFF_NUMBER) " );
			    sqlBuf.append(
			    		" WHERE  " + 
			    		" PD.DATA_LEVEL = 'P' " +
			    		" AND QS.FORM_CODE = '" + formCode + "' ");
			    sqlBuf.append(
			    		" AND STAFF_DEPT.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			    sqlBuf.append(
			    		" AND PD.FORM_NO NOT IN ( " + 
			    		" SELECT DISTINCT PD.FORM_NO " +
			    		" FROM RH_P_KT_FORM_DTL PD  " +
			    		" LEFT JOIN RH_Q_KT_FORM_HDR QH ON QH.FORM_NO = PD.FORM_NO " + 
			    		" LEFT JOIN RH_Q_KT_FORM_STATE QS ON QS.FORM_NO = PD.FORM_NO " + 
			    		" WHERE  " + 
			    		" PD.DATA_LEVEL = 'N' " +
			    		" AND QS.FORM_CODE = '" + formCode + "') ");
			
				//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
			    //logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
	
				while (rs.next())
				{
					voList.add(rs.getInt("FORM_NO"));
				}
			}
			
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return voList;
	}
	
	public List<KtFormCPD_P> getUnimportedPLevelKTCPDListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormCPD_P> rtnList = new ArrayList<KtFormCPD_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormCPD_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormCntProj_P> getUnimportedPLevelKTCntProjListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormCntProj_P> rtnList = new ArrayList<KtFormCntProj_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormCntProj_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormCons_P> getUnimportedPLevelKTConsListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormCons_P> rtnList = new ArrayList<KtFormCons_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormCons_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormInn_P> getUnimportedPLevelKTInnListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormInn_P> rtnList = new ArrayList<KtFormInn_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormInn_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormIP_P> getUnimportedPLevelKTIPListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormIP_P> rtnList = new ArrayList<KtFormIP_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormIP_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormProfConf_P> getUnimportedPLevelKTProfConfListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormProfConf_P> rtnList = new ArrayList<KtFormProfConf_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormProfConf_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormProfEngmt_P> getUnimportedPLevelKTProfEngmtListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormProfEngmt_P> rtnList = new ArrayList<KtFormProfEngmt_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormProfEngmt_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormSem_P> getUnimportedPLevelKTSemListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormSem_P> rtnList = new ArrayList<KtFormSem_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormSem_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormSocEngmt_P> getUnimportedPLevelKTSocEngmtListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormSocEngmt_P> rtnList = new ArrayList<KtFormSocEngmt_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormSocEngmt_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormStaffEngmt_P> getUnimportedPLevelKTStaffEngmtListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormStaffEngmt_P> rtnList = new ArrayList<KtFormStaffEngmt_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormStaffEngmt_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormEA_P> getUnimportedPLevelKTEAListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormEA_P> rtnList = new ArrayList<KtFormEA_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormEA_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormInvAward_P> getUnimportedPLevelKTInvAwardListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormInvAward_P> rtnList = new ArrayList<KtFormInvAward_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormInvAward_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormStartup_P> getUnimportedPLevelKTStartupListByDept(List<String> deptList, String formCode) throws SQLException
	{
		List<KtFormStartup_P> rtnList = new ArrayList<KtFormStartup_P>();
		List<Integer> formNoList = getUnimportedPLevelKTFormNoListByDept(deptList, formCode);
		if(!formNoList.isEmpty()) {
			for(Integer formNo : formNoList) {
				rtnList.add(getKtFormStartup_P(formNo, "P", null, null, 0));
			}
		}
		return rtnList;
	}
	
	public List<KtFormCPD_P> getImportedKTCPDListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormCPD_P> objList = null;
		EntityManager em = null;
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCPD_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormCPD_P> q = em.createQuery(query, KtFormCPD_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormCntProj_P> getImportedKTCntProjListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormCntProj_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCntProj_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormCntProj_P> q = em.createQuery(query, KtFormCntProj_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormCons_P> getImportedKTConsListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormCons_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCons_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormCons_P> q = em.createQuery(query, KtFormCons_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormInn_P> getImportedKTInnListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormInn_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormInn_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormInn_P> q = em.createQuery(query, KtFormInn_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormIP_P> getImportedKTIPListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormIP_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormIP_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormIP_P> q = em.createQuery(query, KtFormIP_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormProfConf_P> getImportedKTProfConfListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormProfConf_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormProfConf_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormProfConf_P> q = em.createQuery(query, KtFormProfConf_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormProfEngmt_P> getImportedKTProfEngmtListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormProfEngmt_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormProfEngmt_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormProfEngmt_P> q = em.createQuery(query, KtFormProfEngmt_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormSem_P> getImportedKTSemListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormSem_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormSem_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormSem_P> q = em.createQuery(query, KtFormSem_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormSocEngmt_P> getImportedKTSocEngmtListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormSocEngmt_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormSocEngmt_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormSocEngmt_P> q = em.createQuery(query, KtFormSocEngmt_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormStaffEngmt_P> getImportedKTStaffEngmtListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormStaffEngmt_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormStaffEngmt_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormStaffEngmt_P> q = em.createQuery(query, KtFormStaffEngmt_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormEA_P> getImportedKTEAListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormEA_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormEA_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormEA_P> q = em.createQuery(query, KtFormEA_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormInvAward_P> getImportedKTInvAwardListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormInvAward_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormInvAward_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormInvAward_P> q = em.createQuery(query, KtFormInvAward_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormStartup_P> getImportedKTStartupListByFacAndDept(String fac, String dept) throws SQLException
	{
		List<KtFormStartup_P> objList = null;
		EntityManager em = null;	
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormStartup_P obj "
					+ "WHERE obj.pk.data_level = :dataLevel ";
			if(fac == null) query += "AND obj.fac IS NULL ";
			else			query +=  "AND obj.fac = :fac ";
			if(dept == null) query += "AND obj.dept IS NULL ";
			else			query +=  "AND obj.dept = :dept ";
			query += "ORDER BY obj.pk.form_no desc";			
			TypedQuery<KtFormStartup_P> q = em.createQuery(query, KtFormStartup_P.class);
			if(fac != null) q.setParameter("fac", fac);
			if(dept != null) q.setParameter("dept", dept);
			q.setParameter("dataLevel", "N");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	
	public KtForm getKtForm(String formCode)
	{
		KtForm obj = null;		
		if (!Strings.isNullOrEmpty(formCode))
		{
			EntityManager em = null;
			try
			{
				em = getEntityManager();
				obj = em.find(KtForm.class, formCode);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public KtForm updateKtForm(KtForm obj) 
	{
		return updateEntity(obj);
	}
	
	public void deleteKtForm(String formCode)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			KtForm obj = em.find(KtForm.class, formCode);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public KtFormHeader_Q getKtFormHeader_Q (Integer form_no, String data_level) 
	{
		List<KtFormHeader_Q> objList = null;
		if (form_no > 0)
		{
			EntityManager em = null;
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormHeader_Q obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.form_no, obj.pk.data_level";			
				TypedQuery<KtFormHeader_Q> q = em.createQuery(query, KtFormHeader_Q.class);
				q.setParameter("form_no", form_no);
				q.setParameter("data_level", data_level);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormDetails_P> getKtFormDetails_P (Integer form_no, String data_level) 
	{
		List<KtFormDetails_P> objList = null;
		EntityManager em = null;
		if (form_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormDetails_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.line_no";			
				TypedQuery<KtFormDetails_P> q = em.createQuery(query, KtFormDetails_P.class);
				q.setParameter("form_no", form_no);
				q.setParameter("data_level", data_level);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public KtFormState_Q getKtFormState_Q (Integer form_no) 
	{
		List<KtFormState_Q> objList = null;
		EntityManager em = null;
		if (form_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormState_Q obj WHERE obj.form_no = :form_no ";			
				TypedQuery<KtFormState_Q> q = em.createQuery(query, KtFormState_Q.class);
				q.setParameter("form_no", form_no);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormState_Q> getKtFormState_QByPeriodId(String dataLevel, Integer periodId)
	{
		List<KtFormState_Q> objList = null;
		
		if(dataLevel!=null && periodId!=null) 
		{
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormState_Q obj WHERE obj.creation_data_level = :dataLevel "+
								"AND obj.periodId = :periodId";			
				TypedQuery<KtFormState_Q> q = em.createQuery(query, KtFormState_Q.class);
				q.setParameter("dataLevel", dataLevel);
				q.setParameter("periodId", periodId);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList))?objList:Collections.EMPTY_LIST;
	}
	
	
	public List<KtFormState_Q> getKtFormState_QList(List<Integer> formNumList)
	{
		List<KtFormState_Q> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormState_Q obj WHERE "+ JPAUtils.convertQueryParamList("obj.form_no", formNumList, Integer.class);			
				TypedQuery<KtFormState_Q> q = em.createQuery(query, KtFormState_Q.class);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public KtFormDetails_Q getKtFormDetails_Q (Integer form_no, String staffNo) 
	{
		List<KtFormDetails_Q> objList = null;
		EntityManager em = null;
		if (form_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormDetails_Q obj WHERE obj.pk.form_no = :form_no AND obj.pk.staff_no = :staffNo";			
				TypedQuery<KtFormDetails_Q> q = em.createQuery(query, KtFormDetails_Q.class);
				q.setParameter("form_no", form_no);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	
	public List<KtFormDetails_Q> getKtFormDetails_QListByStaffNo(String staffNo)
	{
		List<KtFormDetails_Q> objList = null;
		
		EntityManager em = null;
		
		if(!GenericValidator.isBlankOrNull(staffNo)) 
		{
			try 
			{
				em = getEntityManager();
				String query = "SELECT obj FROM KtFormDetails_Q obj WHERE obj.pk.staff_no = :staffNo ";
				TypedQuery<KtFormDetails_Q> q = em.createQuery(query, KtFormDetails_Q.class);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public KtFormCPD_P getKtFormCPD_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormCPD_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCPD_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormCPD_P> q = em.createQuery(query, KtFormCPD_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormCPD_P> getKtFormCPD_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormCPD_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormCPD_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormCPD_P> q = em.createQuery(query, KtFormCPD_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<KtFormCPD_P> getKtFormCPD_P_list (String data_level, Date startDate, Date endDate) 
	{
		List<KtFormCPD_P> objList = null;
		EntityManager em = null;		
		try
		{
			String query2 = "";
			if (startDate != null && endDate != null) {
				query2 = " AND (obj.start_date BETWEEN :startDate AND :endDate OR obj.end_date BETWEEN :startDate AND :endDate) ";
			}
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCPD_P obj WHERE obj.pk.data_level = :data_level" + query2;			
			TypedQuery<KtFormCPD_P> q = em.createQuery(query, KtFormCPD_P.class);
			q.setParameter("data_level", data_level);
			if (startDate != null && endDate != null) {
				q.setParameter("startDate", startDate);
				q.setParameter("endDate", endDate);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public KtFormProfConf_P getKtFormProfConf_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormProfConf_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormProfConf_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormProfConf_P> q = em.createQuery(query, KtFormProfConf_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	
	
	public List<KtFormProfConf_P> getKtFormProfConf_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormProfConf_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormProfConf_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormProfConf_P> q = em.createQuery(query, KtFormProfConf_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormSem_P getKtFormSem_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormSem_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormSem_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			TypedQuery<KtFormSem_P> q = em.createQuery(query, KtFormSem_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormSem_P> getKtFormSem_PList(String dataLevel, List<Integer> formNumList)
	{
		List<KtFormSem_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormSem_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				TypedQuery<KtFormSem_P> q = em.createQuery(query, KtFormSem_P.class);
				q.setParameter("dataLevel", dataLevel);
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormCntProj_P getKtFormCntProj_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormCntProj_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCntProj_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormCntProj_P> q = em.createQuery(query, KtFormCntProj_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}		
	
	public List<KtFormCntProj_P> getKtFormCntProj_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormCntProj_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormCntProj_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormCntProj_P> q = em.createQuery(query, KtFormCntProj_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public KtFormInn_P getKtFormInn_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormInn_P> objList = null;
		EntityManager em = null;
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormInn_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormInn_P> q = em.createQuery(query, KtFormInn_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormInn_P> getKtFormInn_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormInn_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormInn_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormInn_P> q = em.createQuery(query, KtFormInn_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public KtFormCons_P getKtFormCons_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormCons_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormCons_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormCons_P> q = em.createQuery(query, KtFormCons_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormCons_P> getKtFormCons_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormCons_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormCons_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormCons_P> q = em.createQuery(query, KtFormCons_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormProfEngmt_P getKtFormProfEngmt_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormProfEngmt_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormProfEngmt_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormProfEngmt_P> q = em.createQuery(query, KtFormProfEngmt_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormProfEngmt_P> getKtFormProfEngmt_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormProfEngmt_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormProfEngmt_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormProfEngmt_P> q = em.createQuery(query, KtFormProfEngmt_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public KtFormIP_P getKtFormIP_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormIP_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormIP_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormIP_P> q = em.createQuery(query, KtFormIP_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormIP_P> getKtFormIP_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId)
	{
		List<KtFormIP_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormIP_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormIP_P> q = em.createQuery(query, KtFormIP_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormSocEngmt_P getKtFormSocEngmt_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormSocEngmt_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormSocEngmt_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormSocEngmt_P> q = em.createQuery(query, KtFormSocEngmt_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormSocEngmt_P> getKtFormSocEngmt_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormSocEngmt_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormSocEngmt_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormSocEngmt_P> q = em.createQuery(query, KtFormSocEngmt_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormStaffEngmt_P getKtFormStaffEngmt_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormStaffEngmt_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormStaffEngmt_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";		
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormStaffEngmt_P> q = em.createQuery(query, KtFormStaffEngmt_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormStaffEngmt_P> getKtFormStaffEngmt_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormStaffEngmt_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormStaffEngmt_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormStaffEngmt_P> q = em.createQuery(query, KtFormStaffEngmt_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormEA_P getKtFormEA_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormEA_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormEA_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
				dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
				dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormEA_P> q = em.createQuery(query, KtFormEA_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormEA_P> getKtFormEA_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormEA_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormEA_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date ) ";
					dateSql += " OR (obj.start_date is null AND obj.end_date >= :start_date ) ";
					dateSql += " OR (obj.start_date <=:end_date AND obj.end_date is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormEA_P> q = em.createQuery(query, KtFormEA_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormStartup_P getKtFormStartup_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormStartup_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormStartup_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			TypedQuery<KtFormStartup_P> q = em.createQuery(query, KtFormStartup_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormStartup_P> getKtFormStartup_PList(String dataLevel, List<Integer> formNumList)
	{
		List<KtFormStartup_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormStartup_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				TypedQuery<KtFormStartup_P> q = em.createQuery(query, KtFormStartup_P.class);
				q.setParameter("dataLevel", dataLevel);
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormInvAward_P getKtFormInvAward_P (Integer form_no, String data_level, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormInvAward_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtFormInvAward_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level";			
			String dateSql = "";
			if (periodId > 1) {
				dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
				dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date )) ";
				query += dateSql;
			}
			query += " ORDER BY obj.pk.form_no ";
			TypedQuery<KtFormInvAward_P> q = em.createQuery(query, KtFormInvAward_P.class);
			q.setParameter("form_no", form_no);
			q.setParameter("data_level", data_level);
			if (periodId > 1) {
				q.setParameter("start_date", start_date);
				q.setParameter("end_date", end_date);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<KtFormInvAward_P> getKtFormInvAward_PList(String dataLevel, List<Integer> formNumList, Date start_date, Date end_date, Integer periodId) 
	{
		List<KtFormInvAward_P> objList = null;
		EntityManager em = null;
		
		if(CollectionUtils.isNotEmpty(formNumList)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormInvAward_P obj WHERE obj.pk.data_level = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNumList, Integer.class);
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.start_date >= :start_date AND obj.start_date <= :end_date) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.end_date <= :end_date ) ";
					dateSql += " OR (obj.end_date >= :start_date AND obj.start_date <= :start_date )) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.form_no ";
				TypedQuery<KtFormInvAward_P> q = em.createQuery(query, KtFormInvAward_P.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormDetails_Q getKtFormDetails_Q_creator (Integer form_no, String staff_no) 
	{
		List<KtFormDetails_Q> objList = null;
		EntityManager em = null;
		if (form_no > 0) {
			try
			{
				String filterStaffNo = "";
				if (staff_no != null) {
					filterStaffNo = " AND obj.pk.staff_no = :staff_no";
				}
				em = getEntityManager();		
				String query = "SELECT obj FROM KtFormDetails_Q obj WHERE obj.pk.form_no = :form_no AND obj.creator_ind = :creator_ind " + filterStaffNo + " ORDER BY obj.pk.form_no DESC";			
				TypedQuery<KtFormDetails_Q> q = em.createQuery(query, KtFormDetails_Q.class);
				q.setParameter("form_no", form_no);
				q.setParameter("creator_ind", "Y");
				if (staff_no != null) {
					q.setParameter("staff_no", staff_no);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public void deleteKtFormDetails_P(int form_no, String data_level) throws Exception
	{
		if (form_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();

				Query q = em.createQuery("DELETE FROM KtFormDetails_P obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level ");
				q.setParameter("form_no", form_no);
				q.setParameter("data_level", data_level);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete KtFormDetails_P (form_no=" + form_no + ", data_level="+ data_level + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteKtFormHeader_Q(int form_no, String data_level) throws Exception
	{
		if (form_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM KtFormHeader_Q obj WHERE obj.pk.form_no = :form_no AND obj.pk.data_level = :data_level ");
				q.setParameter("form_no", form_no);
				q.setParameter("data_level", data_level);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete KtFormHeader_Q (form_no=" + form_no + ", data_level="+ data_level + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteKtFormDetails_Q(int form_no) throws Exception
	{
		if (form_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM KtFormDetails_Q obj WHERE obj.pk.form_no = :form_no ");
				q.setParameter("form_no", form_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete KtFormDetails_Q (form_no=" + form_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}

	public List<KtActivity> geCDCFListByIds(List<Integer> idList, CDCFSearchPanel searchPanel) throws SQLException
	{
		List<KtActivity> voList = new ArrayList<KtActivity>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && searchPanel != null) {
			String dataLevel = searchPanel.getViewType();
			String sortCol = searchPanel.getSortCol();
			String sortOrder = searchPanel.getSortOrder();
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
					
				    sqlBuf.append(
				    		" SELECT PH.FORM_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.AUTHOR_LIST"+i+" AS AUTHOR_LIST"+i+", ");
				    }
				    sqlBuf.append(
				    		// Common
				    		" PH.DATA_LEVEL, " + 
				    		" PH.START_DATE, " + 
				    		" PH.END_DATE, " + 
				    		" PH.FAC, " + 
				    		" PH.DEPT, "); 
				    
				    ktActivitiesCol(sqlBuf, searchPanel.getKtType());
				    
				    sqlBuf.append(
				    		" PH.CREATOR," +
				    		" PH.CREATION_TIME," +
				    		" PH.USERSTAMP," +
				    		" PH.TIMESTAMP" +
				    		" FROM RH_P_" + searchPanel.getKtType() + "_HDR PH " );
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_KT_FORM_HDR QH ON (QH.FORM_NO = PH.FORM_NO AND ((PH.DATA_LEVEL IN ('M', 'P') AND " + 
				    		" QH.DATA_LEVEL = 'M') OR (PH.DATA_LEVEL IN ('N', 'D') AND QH.DATA_LEVEL = 'N'))) "+
				    		" LEFT JOIN RH_Q_KT_FORM_STATE QS ON QS.FORM_NO = PH.FORM_NO ");
				    sqlBuf.append(
				    		" LEFT JOIN ( " + 
				    		" SELECT PH.FORM_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" LISTAGG( " + 
					    		" CASE WHEN (PD.STAFF_NO IS NOT NULL OR PD.NAME IS NOT NULL) " + 
					    		" AND (LINE_NO <= " + i*30 + 
					    		" AND LINE_NO > " + (i*30 - 30) + " ) " +
					    		" THEN ( " + 
					    		" (CASE WHEN PD.STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
					    		" WHEN PD.STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
					    		" ELSE PD.NAME END) " + 
					    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " + 
					    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END) " +
					    		" ) ");
					    if(i == listNum) {
						    sqlBuf.append(
						    		" WHEN LINE_NO = (" + i*30 +
						    		")+1 THEN '...' ");}
					    sqlBuf.append(
					    		" ELSE '' END , '<br/>') " + 
					    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS AUTHOR_LIST" + i + " ");
					    if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM RH_P_" + searchPanel.getKtType() + "_HDR PH  " + 
				    		" LEFT JOIN RH_P_KT_FORM_DTL PD ON (PD.FORM_NO = PH.FORM_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " +  
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.STAFF_NO  " + 
				    		" WHERE PH.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PH.FORM_NO " + 
				    		" ) TMP ON PH.FORM_NO = TMP.FORM_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
				    sqlBuf.append(
				    		" AND PH.FORM_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
				    //logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						KtActivity vObj = new KtActivity();
						vObj.setForm_no(rs.getInt("FORM_NO"));
						String authorList = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									authorList += "<br/>" ;
								authorList += authListSeg;
							}
						}
						vObj.setAuthorList(authorList);
						vObj.setData_level(rs.getString("data_level"));
						vObj.setStart_date(rs.getDate("start_date"));
						vObj.setEnd_date(rs.getDate("end_date"));
						vObj.setFac(rs.getString("fac"));
						vObj.setDept(rs.getString("dept"));
						
						ktActivitiesSet(vObj, searchPanel.getKtType(), rs);
						
						vObj.setTimestamp(rs.getDate("timestamp"));
						vObj.setCreationDate(rs.getDate("creation_time"));
						vObj.setUserstamp(rs.getString("userstamp"));
						vObj.setCreator(rs.getString("creator"));
						
						voList.add(vObj);
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
			if(voList != null && !voList.isEmpty()) {
				if(sortCol.equals(KTSearchPanel.SORT_COL_DEFAULT_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(KTSearchPanel.SORT_COL_KT_NO_VALUE)) {
			    	if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(KTSearchPanel.SORT_COL_FROM_DATE_VALUE)) {
			    	Comparator<KtActivity> fromDayComp = new Comparator<KtActivity>() {
			    		@Override public int compare(final KtActivity record1, final KtActivity record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String nullYear =  "1000" ;
				    		    String nullMonth = "1" ;
				    		    String nullDay = "1";
				    		    String nullNullLast = "00" ;
				    		    java.util.Date nullDate = format.parse(nullDay + "/" + nullMonth + "/" + nullYear + " " + nullNullLast);
				    		    java.util.Date record1Date = nullDate;
				    		    java.util.Date record2Date = nullDate;
			    				if(record1.getStart_date() != null) {
			    					record1Date = record1.getStart_date();
			    				}
			    				if(record2.getStart_date() != null) {
			    					record2Date = record2.getStart_date();
			    				}
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getKtActivityListByIds having non number from date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, fromDayComp);
					else
						Collections.sort(voList, fromDayComp.reversed());
			    }
				else if(sortCol.equals(KTSearchPanel.SORT_COL_TO_DATE_VALUE)) {
					Comparator<KtActivity> toDayComp = new Comparator<KtActivity>() {
			    		@Override public int compare(final KtActivity record1, final KtActivity record2) {
			    			try {
			    				SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String nullYear =  "1000" ;
				    		    String nullMonth = "1" ;
				    		    String nullDay = "1";
				    		    String nullNullLast = "00" ;
				    		    java.util.Date nullDate = format.parse(nullDay + "/" + nullMonth + "/" + nullYear + " " + nullNullLast);
				    		    java.util.Date record1Date = nullDate;
				    		    java.util.Date record2Date = nullDate;
			    				if(record1.getEnd_date() != null) {
			    					record1Date = record1.getEnd_date();
			    				}
			    				if(record2.getEnd_date() != null) {
			    					record2Date = record2.getEnd_date();
			    				}
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getKtActivityListByIds having non number to date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, toDayComp);
					else
						Collections.sort(voList, toDayComp.reversed());
			    }
			}
		}
		
		return voList;
	}
	
	public List<KtActivity> getKtActivityListByIds(List<Integer> idList, KTSearchPanel searchPanel) throws SQLException
	{
		List<KtActivity> voList = new ArrayList<KtActivity>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && searchPanel != null) {
			String dataLevel = searchPanel.getViewType();
			String sortCol = searchPanel.getSortCol();
			String sortOrder = searchPanel.getSortOrder();
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
					
				    sqlBuf.append(
				    		" SELECT PH.FORM_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.AUTHOR_LIST"+i+" AS AUTHOR_LIST"+i+", ");
				    }
				    sqlBuf.append(
				    		// Common
				    		" PH.DATA_LEVEL, " + 
				    		" PH.START_DATE, " + 
				    		" PH.END_DATE, " + 
				    		" PH.FAC, " + 
				    		" PH.DEPT, "); 
				    
				    ktActivitiesCol(sqlBuf, searchPanel.getKtType());
				    
				    sqlBuf.append(
				    		" PH.CREATOR," +
				    		" PH.CREATION_TIME," +
				    		" PH.USERSTAMP," +
				    		" PH.TIMESTAMP" +
				    		" FROM RH_P_" + searchPanel.getKtType() + "_HDR PH " );
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_KT_FORM_HDR QH ON (QH.FORM_NO = PH.FORM_NO AND ((PH.DATA_LEVEL IN ('M', 'P') AND " + 
				    		" QH.DATA_LEVEL = 'M') OR (PH.DATA_LEVEL IN ('N', 'D') AND QH.DATA_LEVEL = 'N'))) "+
				    		" LEFT JOIN RH_Q_KT_FORM_STATE QS ON QS.FORM_NO = PH.FORM_NO ");
				    sqlBuf.append(
				    		" LEFT JOIN ( " + 
				    		" SELECT PH.FORM_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" LISTAGG( " + 
					    		" CASE WHEN (PD.STAFF_NO IS NOT NULL OR PD.NAME IS NOT NULL) " + 
					    		" AND (LINE_NO <= " + i*30 + 
					    		" AND LINE_NO > " + (i*30 - 30) + " ) " +
					    		" THEN ( " + 
					    		" (CASE WHEN PD.STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
					    		" WHEN PD.STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
					    		" ELSE PD.NAME END) " + 
					    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " + 
					    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END) " +
					    		" ) ");
					    if(i == listNum) {
						    sqlBuf.append(
						    		" WHEN LINE_NO = (" + i*30 +
						    		")+1 THEN '...' ");}
					    sqlBuf.append(
					    		" ELSE '' END , '<br/>') " + 
					    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS AUTHOR_LIST" + i + " ");
					    if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM RH_P_" + searchPanel.getKtType() + "_HDR PH  " + 
				    		" LEFT JOIN RH_P_KT_FORM_DTL PD ON (PD.FORM_NO = PH.FORM_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " +  
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.STAFF_NO  " + 
				    		" WHERE PH.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PH.FORM_NO " + 
				    		" ) TMP ON PH.FORM_NO = TMP.FORM_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
				    sqlBuf.append(
				    		" AND PH.FORM_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
				    //logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						KtActivity vObj = new KtActivity();
						vObj.setForm_no(rs.getInt("FORM_NO"));
						String authorList = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									authorList += "<br/>" ;
								authorList += authListSeg;
							}
						}
						vObj.setAuthorList(authorList);
						vObj.setData_level(rs.getString("data_level"));
						vObj.setStart_date(rs.getDate("start_date"));
						vObj.setEnd_date(rs.getDate("end_date"));
						vObj.setFac(rs.getString("fac"));
						vObj.setDept(rs.getString("dept"));
						
						ktActivitiesSet(vObj, searchPanel.getKtType(), rs);
						
						vObj.setTimestamp(rs.getDate("timestamp"));
						vObj.setCreationDate(rs.getDate("creation_time"));
						vObj.setUserstamp(rs.getString("userstamp"));
						vObj.setCreator(rs.getString("creator"));
						
						voList.add(vObj);
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
			if(voList != null && !voList.isEmpty()) {
				if(sortCol.equals(KTSearchPanel.SORT_COL_DEFAULT_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(KTSearchPanel.SORT_COL_KT_NO_VALUE)) {
			    	if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(KtActivity::getForm_no, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(KTSearchPanel.SORT_COL_FROM_DATE_VALUE)) {
			    	Comparator<KtActivity> fromDayComp = new Comparator<KtActivity>() {
			    		@Override public int compare(final KtActivity record1, final KtActivity record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String nullYear =  "1000" ;
				    		    String nullMonth = "1" ;
				    		    String nullDay = "1";
				    		    String nullNullLast = "00" ;
				    		    java.util.Date nullDate = format.parse(nullDay + "/" + nullMonth + "/" + nullYear + " " + nullNullLast);
				    		    java.util.Date record1Date = nullDate;
				    		    java.util.Date record2Date = nullDate;
			    				if(record1.getStart_date() != null) {
			    					record1Date = record1.getStart_date();
			    				}
			    				if(record2.getStart_date() != null) {
			    					record2Date = record2.getStart_date();
			    				}
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getKtActivityListByIds having non number from date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, fromDayComp);
					else
						Collections.sort(voList, fromDayComp.reversed());
			    }
				else if(sortCol.equals(KTSearchPanel.SORT_COL_TO_DATE_VALUE)) {
					Comparator<KtActivity> toDayComp = new Comparator<KtActivity>() {
			    		@Override public int compare(final KtActivity record1, final KtActivity record2) {
			    			try {
			    				SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String nullYear =  "1000" ;
				    		    String nullMonth = "1" ;
				    		    String nullDay = "1";
				    		    String nullNullLast = "00" ;
				    		    java.util.Date nullDate = format.parse(nullDay + "/" + nullMonth + "/" + nullYear + " " + nullNullLast);
				    		    java.util.Date record1Date = nullDate;
				    		    java.util.Date record2Date = nullDate;
			    				if(record1.getEnd_date() != null) {
			    					record1Date = record1.getEnd_date();
			    				}
			    				if(record2.getEnd_date() != null) {
			    					record2Date = record2.getEnd_date();
			    				}
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getKtActivityListByIds having non number to date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, toDayComp);
					else
						Collections.sort(voList, toDayComp.reversed());
			    }
			}
		}
		
		return voList;
	}
	
	private StringBuffer ktActivitiesCol(StringBuffer strBuf, String ktType) {
		StringBuffer rtnBuf = strBuf;
		if(ktType.equals("KT_CPD")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.PI, " +
					"PH.FUND_SRC, " +
					"PH.ACT_CODE, " +
					"PH.ORGANIZER, " +
					"PH.FREE_CHARGE, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_RDO, " + 
					"PH.EXPND_RPT_UNIT, " + 
					"PH.EXPND_FO, " + 
					"PH.EXPND_FO_REM, " + 
					"PH.EXPND_RDO, " + 
					"PH.NUM_KEY_PARTNER, " + 
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_STU_CONTACT_HR, " + 
					"PH.STAFF_MAN_DAY, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_CNT_PROJ")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.ACT_CODE, " +
					"PH.PRINCIPAL_INVES, " +
					"PH.FUND_SRC, " +
					"PH.BUDGET, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_GRANT, " + 
					"PH.INCOME_RDO, " + 
					"PH.NUM_KEY_PARTNER, " + 
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_STAKEHOLDER, " + 
					"PH.NUM_SCHOOL, " + 
					"PH.NUM_STU, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_CONSULT")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.ACT_CODE, " +
					"PH.PRINCIPAL_INVES, " +
					"PH.FUND_SRC, " +
					"PH.FUND_SRC_TYPE, " +
					"PH.FUND_SRC_ORG, " +
					"PH.EDUHK_ROLE, " +
					"PH.BUDGET, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.CUMU_INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_GRANT, " + 
					"PH.INCOME_RDO, " + 
					"PH.NUM_KEY_PARTNER, " + 
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_STAKEHOLDER, " + 
					"PH.NUM_SCHOOL, " + 
					"PH.NUM_ORG, " + 
					"PH.NUM_ADV_BODY, " + 
					"PH.NUM_STU, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_EA")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.NUM_SUBSESSIONS, " +
					"PH.PI, " +
					"PH.ACT_CODE, " +
					"PH.ACT_CAT, " +
					"PH.FUND_SRC, " +
					"PH.FREE_CHARGE, " +
					"PH.REGION, " +
					"PH.CT_PERSON, " +
					"PH.NUM_KEY_PARTNER, " +
					"PH.NUM_SPEAKERS, " +
					"PH.NUM_STU_UG, " +
					"PH.NUM_STU_PG, " +
					"PH.NUM_STAFF, " +
					"PH.NUM_ALUMNI, " + 
					"PH.NUM_OTHER, " +
					"PH.NUM_PAX, " +
					"PH.NUM_EXT_PROF, " +
					"PH.ACT_TYPE, " + 
					"PH.CREDIT_BEARING, " +
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_INNOVATION")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.ACT_CODE, " +
					"PH.PRINCIPAL_INVES, " +
					"PH.FUND_SRC, " +
					"PH.BUDGET, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_GRANT, " + 
					"PH.INCOME_RDO, " + 
					"PH.NUM_KEY_PARTNER, " + 
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_STAKEHOLDER, " + 
					"PH.NUM_SCHOOL, " + 
					"PH.NUM_STU, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_INV_AWARD")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.EVENT_NAME, " +
					"PH.ACT_CODE, " +
					"PH.NAME_PI, " +
					"PH.NAME_OTHER, " +
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_IP")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.CAT, " +
					"PH.SOFTWARE_LIC, " +
					"PH.STAFF_NAME, " +
					"PH.INCOME_CAT, " +
					"PH.INCOME_NAME, " +
					"PH.ORG_TYPE, " +
					"PH.INCOME_RPT_UNIT, " +
					"PH.NUM_EXT_PROF, " +
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_PROF_CONF")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.NUM_SUBSESSIONS, " +
					"PH.PI, " +
					"PH.ACT_CODE, " +
					"PH.EVENT_TYPE, " +
					"PH.ORGANIZER, " +
					"PH.REGION, " +
					"PH.CT_PERSON, " +
					"PH.TARGET_PAX, " +
					"PH.FREE_CHARGE, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_RDO, " + 
					"PH.EXPND_RPT_UNIT, " + 
					"PH.EXPND_FO, " + 
					"PH.EXPND_FO_REM, " + 
					"PH.EXPND_RDO, " + 
					"PH.NUM_KEY_PARTNER, " +
					"PH.NUM_SPEAKERS, " +
					"PH.NUM_PRESENTATION, " +
					"PH.NUM_TEACHER, " +
					"PH.NUM_PRINCIPAL, " +
					"PH.NUM_OTHER, " +
					"PH.NUM_PAX, " +
					"PH.STAFF_MAN_DAY, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_PROF_ENGMT")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.COND_STAFF, " +
					"PH.ORGANIZER, " +
					"PH.CHECK_ENGAGE, " +
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_LEADER, " + 
					"PH.NUM_OTHER, " + 
					"PH.NUM_PAX_BEN, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		if(ktType.equals("KT_SEMINAR")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.ACT_CODE, " +
					"PH.ORGANIZER, " +
					"PH.CT_PERSON, " +
					"PH.TARGET_PAX, " +
					"PH.FREE_CHARGE, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_RDO, " + 
					"PH.EXPND_RPT_UNIT, " + 
					"PH.EXPND_FO, " + 
					"PH.EXPND_FO_REM, " + 
					"PH.EXPND_RDO, " + 
					"PH.NUM_KEY_PARTNER, " + 
					"PH.NUM_PAX, " + 
					"PH.CHECK_PAX, " + 
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_OTHER, " + 
					"PH.STAFF_MAN_DAY, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		if(ktType.equals("KT_SOC_ENGMT")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.ACT_CODE, " +
					"PH.NUM_SUBSESSIONS, " +
					"PH.PI, " +
					"PH.ORGANIZER, " +
					"PH.STAFF_NAME, " +
					"PH.FREE_CHARGE, " +
					"PH.EVENT_TYPE, " +
					"PH.PERFORM, " +
					"PH.INCOME_RPT_UNIT, " + 
					"PH.INCOME_FO, " + 
					"PH.INCOME_FO_REM, " + 
					"PH.INCOME_GRANT, " + 
					"PH.INCOME_RDO, " + 
					"PH.EXPND_RPT_UNIT, " + 
					"PH.EXPND_FO, " + 
					"PH.EXPND_FO_REM, " + 
					"PH.EXPND_RDO, " + 
					"PH.NUM_KEY_PARTNER, " + 
					"PH.NUM_SPEAKERS, " +
					"PH.NUM_TEACHER, " + 
					"PH.NUM_PRINCIPAL, " + 
					"PH.NUM_OTHER, " + 
					"PH.NUM_EXT_PROF, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		else if(ktType.equals("KT_STAFF_ENGMT")) {
			rtnBuf.append("PH.STAFF_NAME, " +
					"PH.EXT_BODY_NAME, " +
					"PH.REGION, " +
					"PH.COUNTRY, " +
					"PH.EXT_BODY_NATURE, " +
					"PH.POST_ENGAGED, " +
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		if(ktType.equals("KT_STARTUP")) {
			rtnBuf.append("PH.TITLE, " +
					"PH.ACT_CODE, " +
					"PH.ORGANIZER, " +
					"PH.REGION, " +
					"PH.CT_PERSON, " +
					"PH.NUM_STU_UG, " +
					"PH.NUM_STU_PG, " +
					"PH.NUM_STAFF, " + 
					"PH.NUM_ALUMNI, " + 
					"PH.ACT_TYPE, " + 
					"PH.REMARKS_STAFF, " + 
					"PH.REMARKS_DEPT, " + 
					"PH.REMARKS_KT, ");
		}
		return rtnBuf;
	}
	
	private KtActivity ktActivitiesSet(KtActivity ktAct, String ktType, ResultSet rs) throws SQLException {
		KtActivity rtnAct = ktAct;
		if(ktType.equals("KT_CPD")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setPi(rs.getString("pi"));
			rtnAct.setFund_src(rs.getString("fund_src"));
			rtnAct.setOrganizer(rs.getString("organizer"));
			rtnAct.setFree_charge(rs.getString("free_charge"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setExpnd_rpt_unit(rs.getString("expnd_rpt_unit"));
			rtnAct.setExpnd_fo(rs.getString("expnd_fo"));
			rtnAct.setExpnd_fo_rem(rs.getString("expnd_fo_rem"));
			rtnAct.setExpnd_rdo(rs.getString("expnd_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_stu_contact_hr(rs.getString("num_stu_contact_hr"));
			rtnAct.setStaff_man_day(rs.getString("staff_man_day"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_CNT_PROJ")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setPrincipal_inves(rs.getString("principal_inves"));
			rtnAct.setFund_src(rs.getString("fund_src"));
			rtnAct.setBudget(rs.getString("budget"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_grant(rs.getString("income_grant"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_stakeholder(rs.getString("num_stakeholder"));
			rtnAct.setNum_school(rs.getString("num_school"));
			rtnAct.setNum_stu(rs.getString("num_stu"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_CONSULT")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setPrincipal_inves(rs.getString("principal_inves"));
			rtnAct.setFund_src(rs.getString("fund_src"));
			rtnAct.setFund_src_type(rs.getString("fund_src_type"));
			rtnAct.setFund_src_org(rs.getString("fund_src_org"));
			rtnAct.setEduhk_role(rs.getString("eduhk_role"));
			rtnAct.setBudget(rs.getString("budget"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setCumu_income_fo(rs.getString("cumu_income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_grant(rs.getString("income_grant"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_stakeholder(rs.getString("num_stakeholder"));
			rtnAct.setNum_school(rs.getString("num_school"));
			rtnAct.setNum_org(rs.getString("num_org"));
			rtnAct.setNum_adv_body(rs.getString("num_adv_body"));
			rtnAct.setNum_stu(rs.getString("num_stu"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_EA")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setNum_subsessions(rs.getString("num_subsessions"));
			rtnAct.setPi(rs.getString("pi"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setAct_cat(rs.getString("act_cat"));
			rtnAct.setFund_src(rs.getString("fund_src"));
			rtnAct.setFree_charge(rs.getString("free_charge"));
			rtnAct.setRegion(rs.getString("region"));
			rtnAct.setCt_person(rs.getString("ct_person"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_speakers(rs.getString("num_speakers"));
			rtnAct.setNum_stu_ug(rs.getString("num_stu_ug"));
			rtnAct.setNum_stu_pg(rs.getString("num_stu_pg"));
			rtnAct.setNum_staff(rs.getString("num_staff"));
			rtnAct.setNum_alumni(rs.getString("num_alumni"));
			rtnAct.setNum_other(rs.getString("num_other"));
			rtnAct.setNum_pax(rs.getString("num_pax"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setAct_type(rs.getString("act_type"));
			rtnAct.setCredit_bearing(rs.getString("credit_bearing"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_INNOVATION")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setPrincipal_inves(rs.getString("principal_inves"));
			rtnAct.setFund_src(rs.getString("fund_src"));
			rtnAct.setBudget(rs.getString("budget"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_grant(rs.getString("income_grant"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_stakeholder(rs.getString("num_stakeholder"));
			rtnAct.setNum_school(rs.getString("num_school"));
			rtnAct.setNum_stu(rs.getString("num_stu"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_INV_AWARD")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setEvent_name(rs.getString("event_name"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setName_pi(rs.getString("name_pi"));
			rtnAct.setName_other(rs.getString("name_other"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_IP")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setCat(rs.getString("cat"));
			rtnAct.setSoftware_lic(rs.getString("software_lic"));
			rtnAct.setStaff_name(rs.getString("staff_name"));
			rtnAct.setIncome_cat(rs.getString("income_cat"));
			rtnAct.setIncome_name(rs.getString("income_name"));
			rtnAct.setOrg_type(rs.getString("org_type"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_PROF_CONF")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setNum_subsessions(rs.getString("num_subsessions"));
			rtnAct.setPi(rs.getString("pi"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setOrganizer(rs.getString("organizer"));
			rtnAct.setRegion(rs.getString("region"));
			rtnAct.setCt_person(rs.getString("ct_person"));
			rtnAct.setTarget_pax(rs.getString("target_pax"));
			rtnAct.setFree_charge(rs.getString("free_charge"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setExpnd_rpt_unit(rs.getString("expnd_rpt_unit"));
			rtnAct.setExpnd_fo(rs.getString("expnd_fo"));
			rtnAct.setExpnd_fo_rem(rs.getString("expnd_fo_rem"));
			rtnAct.setExpnd_rdo(rs.getString("expnd_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_speakers(rs.getString("num_speakers"));
			rtnAct.setNum_presentation(rs.getString("num_presentation"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_other(rs.getString("num_other"));
			rtnAct.setNum_pax(rs.getString("num_pax"));
			rtnAct.setStaff_man_day(rs.getString("staff_man_day"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
			rtnAct.setEvent_type(rs.getString("event_type"));
		}
		else if(ktType.equals("KT_PROF_ENGMT")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setCond_staff(rs.getString("cond_staff"));
			rtnAct.setOrganizer(rs.getString("organizer"));
			rtnAct.setCheck_engage(rs.getString("check_engage"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_leader(rs.getString("num_leader"));
			rtnAct.setNum_other(rs.getString("num_other"));
			rtnAct.setNum_pax_ben(rs.getString("num_pax_ben"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		if(ktType.equals("KT_SEMINAR")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setOrganizer(rs.getString("organizer"));
			rtnAct.setCt_person(rs.getString("ct_person"));
			rtnAct.setTarget_pax(rs.getString("target_pax"));
			rtnAct.setFree_charge(rs.getString("free_charge"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setExpnd_rpt_unit(rs.getString("expnd_rpt_unit"));
			rtnAct.setExpnd_fo(rs.getString("expnd_fo"));
			rtnAct.setExpnd_fo_rem(rs.getString("expnd_fo_rem"));
			rtnAct.setExpnd_rdo(rs.getString("expnd_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_pax(rs.getString("num_pax"));
			rtnAct.setCheck_pax(rs.getString("check_pax"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_other(rs.getString("num_other"));
			rtnAct.setStaff_man_day(rs.getString("staff_man_day"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		if(ktType.equals("KT_SOC_ENGMT")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setNum_subsessions(rs.getString("num_subsessions"));
			rtnAct.setPi(rs.getString("pi"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setOrganizer(rs.getString("organizer"));
			rtnAct.setStaff_name(rs.getString("staff_name"));
			rtnAct.setFree_charge(rs.getString("free_charge"));
			rtnAct.setEvent_type(rs.getString("event_type"));
			rtnAct.setPerform(rs.getString("perform"));
			rtnAct.setIncome_rpt_unit(rs.getString("income_rpt_unit"));
			rtnAct.setIncome_fo(rs.getString("income_fo"));
			rtnAct.setIncome_fo_rem(rs.getString("income_fo_rem"));
			rtnAct.setIncome_grant(rs.getString("income_grant"));
			rtnAct.setIncome_rdo(rs.getString("income_rdo"));
			rtnAct.setExpnd_rpt_unit(rs.getString("expnd_rpt_unit"));
			rtnAct.setExpnd_fo(rs.getString("expnd_fo"));
			rtnAct.setExpnd_fo_rem(rs.getString("expnd_fo_rem"));
			rtnAct.setExpnd_rdo(rs.getString("expnd_rdo"));
			rtnAct.setNum_key_partner(rs.getString("num_key_partner"));
			rtnAct.setNum_speakers(rs.getString("num_speakers"));
			rtnAct.setNum_teacher(rs.getString("num_teacher"));
			rtnAct.setNum_principal(rs.getString("num_principal"));
			rtnAct.setNum_other(rs.getString("num_other"));
			rtnAct.setNum_ext_prof(rs.getString("num_ext_prof"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		else if(ktType.equals("KT_STAFF_ENGMT")) {
			rtnAct.setStaff_name(rs.getString("staff_name"));
			rtnAct.setExt_body_name(rs.getString("ext_body_name"));
			rtnAct.setRegion(rs.getString("region"));
			rtnAct.setCountry(rs.getString("country"));
			rtnAct.setExt_body_nature(rs.getString("ext_body_nature"));
			rtnAct.setPost_engaged(rs.getString("post_engaged"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		if(ktType.equals("KT_STARTUP")) {
			rtnAct.setTitle(rs.getString("title"));
			rtnAct.setAct_code(rs.getString("act_code"));
			rtnAct.setOrganizer(rs.getString("organizer"));
			rtnAct.setRegion(rs.getString("region"));
			rtnAct.setCt_person(rs.getString("ct_person"));
			rtnAct.setNum_stu_ug(rs.getString("num_stu_ug"));
			rtnAct.setNum_stu_pg(rs.getString("num_stu_pg"));
			rtnAct.setNum_staff(rs.getString("num_staff"));
			rtnAct.setNum_alumni(rs.getString("num_alumni"));
			rtnAct.setAct_type(rs.getString("act_type"));
			rtnAct.setRemarks_staff(rs.getString("remarks_staff"));
			rtnAct.setRemarks_dept(rs.getString("remarks_dept"));
			rtnAct.setRemarks_kt(rs.getString("remarks_kt"));
		}
		return rtnAct;
	}


	public Map<Integer, String> getAuthorListMap(List<Integer> idList, String formCode) throws SQLException
	{
		Map<Integer, String> rtnMap = new HashMap<Integer, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && formCode != null) {
			String dataLevel = "P";
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
					
				    sqlBuf.append(
				    		" SELECT PH.FORM_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.AUTHOR_LIST"+i+" AS AUTHOR_LIST"+i+", ");
				    }
				    
				    sqlBuf.append(
				    		" PH.CREATOR," +
				    		" PH.CREATION_TIME," +
				    		" PH.USERSTAMP," +
				    		" PH.TIMESTAMP" +
				    		" FROM RH_P_" + formCode + "_HDR PH " );
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_KT_FORM_HDR QH ON (QH.FORM_NO = PH.FORM_NO AND ((PH.DATA_LEVEL IN ('M', 'P') AND " + 
				    		" QH.DATA_LEVEL = 'M') OR (PH.DATA_LEVEL IN ('N', 'D') AND QH.DATA_LEVEL = 'N'))) "+
				    		" LEFT JOIN RH_Q_KT_FORM_STATE QS ON QS.FORM_NO = PH.FORM_NO ");
				    sqlBuf.append(
				    		" LEFT JOIN ( " + 
				    		" SELECT PH.FORM_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" LISTAGG( " + 
					    		" CASE WHEN (PD.STAFF_NO IS NOT NULL OR PD.NAME IS NOT NULL) " + 
					    		" AND (LINE_NO <= " + i*30 + 
					    		" AND LINE_NO > " + (i*30 - 30) + " ) " +
					    		" THEN ( " + 
					    		" (CASE WHEN PD.STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
					    		" WHEN PD.STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
					    		" ELSE PD.NAME END) " + 
					    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " + 
					    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END) " +
					    		" ) ");
					    if(i == listNum) {
						    sqlBuf.append(
						    		" WHEN LINE_NO = (" + i*30 +
						    		")+1 THEN '...' ");}
					    sqlBuf.append(
					    		" ELSE '' END , '<br/>') " + 
					    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS AUTHOR_LIST" + i + " ");
					    if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM RH_P_" + formCode + "_HDR PH  " + 
				    		" LEFT JOIN RH_P_KT_FORM_DTL PD ON (PD.FORM_NO = PH.FORM_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " +  
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.STAFF_NO  " + 
				    		" WHERE PH.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PH.FORM_NO " + 
				    		" ) TMP ON PH.FORM_NO = TMP.FORM_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
				    sqlBuf.append(
				    		" AND PH.FORM_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
				    //logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						String authorList = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									authorList += "<br/>" ;
								authorList += authListSeg;
							}
						}
						rtnMap.put(rs.getInt("FORM_NO"), authorList);
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		
		return rtnMap;
	}

	public List<PureKtFormHeader> getPureKtFormHeaderList()
	{
		List<PureKtFormHeader> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PureKtFormHeader obj ORDER BY form_no ";			
			TypedQuery<PureKtFormHeader> q = em.createQuery(query, PureKtFormHeader.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public PureKtFormHeader getPureKtFormHeader(Integer form_no)
	{
		List<PureKtFormHeader> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PureKtFormHeader obj WHERE obj.form_no = :form_no ORDER BY obj.display_order ";			
			TypedQuery<PureKtFormHeader> q = em.createQuery(query, PureKtFormHeader.class);
			q.setParameter("form_no", form_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<PureKtFormDetails> getPureKtFormDetailsList(Integer form_no)
	{
		List<PureKtFormDetails> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PureKtFormDetails obj WHERE obj.pk.form_no = :form_no ORDER BY obj.pk.display_order ";			
			TypedQuery<PureKtFormDetails> q = em.createQuery(query, PureKtFormDetails.class);
			q.setParameter("form_no", form_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<KtFormSummary> getKtFormSummaryListByPid(String pid, String dataLevel)
	{
		List<KtFormSummary> objList = null;
		EntityManager em = null;
		
		if(!GenericValidator.isBlankOrNull(pid) && !GenericValidator.isBlankOrNull(dataLevel)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormSummary obj WHERE obj.pid = :pid "+
								"AND obj.pk.dataLevel = :dataLevel ";
				TypedQuery<KtFormSummary> q = em.createQuery(query, KtFormSummary.class);
				q.setParameter("pid", pid);
				q.setParameter("dataLevel", dataLevel);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<KtFormSummary> getKtFormSummaryListByStaff(String staffNum, String dataLevel, Date start_date, Date end_date, Integer periodId, String formCode)
	{
		List<KtFormSummary> objList = null;
		EntityManager em = null;
		
		if(!GenericValidator.isBlankOrNull(staffNum) && !GenericValidator.isBlankOrNull(dataLevel)) 
		{
			try 
			{
				em = getEntityManager();
				
				String query = 	"SELECT obj FROM KtFormSummary obj WHERE obj.staffNo = :staffNo "+
								"AND obj.pk.dataLevel = :dataLevel ";
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.startDate >= :start_date AND obj.startDate <= :end_date) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.endDate <= :end_date ) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.startDate <= :start_date ) ";
					dateSql += " OR (obj.startDate is null AND obj.endDate >= :start_date ) ";
					dateSql += " OR (obj.startDate <=:end_date AND obj.endDate is null)) ";
					query += dateSql;
				}
				if (formCode != null) {
					query += " AND obj.formCode = :formCode )";
				}
				query += " ORDER BY obj.ktForm.display_order, obj.startDate, obj.endDate, obj.title ";
				TypedQuery<KtFormSummary> q = em.createQuery(query, KtFormSummary.class);
				q.setParameter("staffNo", staffNum);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				if (formCode != null) {
					q.setParameter("formCode", formCode);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<KtFormSummary> getKtFormSummaryListByDataLevel(String dataLevel, Date start_date, Date end_date, Integer periodId)
	{
		List<KtFormSummary> objList = null;
		EntityManager em = null;
		
		if(!GenericValidator.isBlankOrNull(dataLevel)) 
		{
			try 
			{
				em = getEntityManager();
				
				String query = "SELECT obj FROM KtFormSummary obj WHERE obj.pk.dataLevel = :dataLevel ";
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.startDate >= :start_date AND obj.startDate <= :end_date) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.endDate <= :end_date ) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.startDate <= :start_date ) ";
					dateSql += " OR (obj.startDate is null AND obj.endDate >= :start_date ) ";
					dateSql += " OR (obj.startDate <=:end_date AND obj.endDate is null)) ";
					query += dateSql;
				}
				query += " ORDER BY obj.pk.formNo ";
				TypedQuery<KtFormSummary> q = em.createQuery(query, KtFormSummary.class);
				q.setParameter("dataLevel", dataLevel);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<KtFormSummary> getKtFormSummaryListByFacDept(String fac, String dept, String dataLevel, String formCode, Date start_date, Date end_date, Integer periodId)
	{
		List<KtFormSummary> objList = null;
		EntityManager em = null;
		if(!GenericValidator.isBlankOrNull(dataLevel) && (!GenericValidator.isBlankOrNull(fac) || !GenericValidator.isBlankOrNull(dept))) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormSummary obj WHERE obj.pk.dataLevel = :dataLevel ";
				
				if(!GenericValidator.isBlankOrNull(fac)) query = query+"AND obj.fac = :fac ";
				if(!GenericValidator.isBlankOrNull(dept)) query = query+"AND obj.dept = :dept ";
				if(!GenericValidator.isBlankOrNull(formCode)) query = query+"AND obj.formCode = :formCode ";
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.startDate >= :start_date AND obj.startDate <= :end_date) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.endDate <= :end_date ) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.startDate <= :start_date ) ";
					dateSql += " OR (obj.startDate is null AND obj.endDate >= :start_date ) ";
					dateSql += " OR (obj.startDate <=:end_date AND obj.endDate is null)) ";
					query += dateSql;
				}
				query = query +"ORDER BY obj.pk.formNo desc";
				
				TypedQuery<KtFormSummary> q = em.createQuery(query, KtFormSummary.class);
				
				q.setParameter("dataLevel", dataLevel);
				if(!GenericValidator.isBlankOrNull(fac)) q.setParameter("fac", fac);
				if(!GenericValidator.isBlankOrNull(dept)) q.setParameter("dept", dept);
				if(!GenericValidator.isBlankOrNull(formCode)) q.setParameter("formCode", formCode);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<KtFormSummary> getKtFormSummaryListByFacsAndDepts(List<String> facs, List<String> depts, String dataLevel, String formCode, Date start_date, Date end_date, Integer periodId)
	{
		List<KtFormSummary> objList = null;
		EntityManager em = null;
		if(!GenericValidator.isBlankOrNull(dataLevel) && (facs.size() > 0 || depts.size() > 0 )) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormSummary obj WHERE obj.pk.dataLevel = :dataLevel ";
				
				if(facs != null) {
					if (facs.size() > 0)
						query = query+" AND obj.fac IN :facs ";
				}
				if(depts != null) {
					if (depts.size() > 0)
						query = query+" AND obj.dept IN :depts ";
				}
				if(!GenericValidator.isBlankOrNull(formCode)) query = query+"AND obj.formCode = :formCode ";
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.startDate >= :start_date AND obj.startDate <= :end_date) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.endDate <= :end_date ) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.startDate <= :start_date ) ";
					dateSql += " OR (obj.startDate is null AND obj.endDate >= :start_date ) ";
					dateSql += " OR (obj.startDate <=:end_date AND obj.endDate is null)) ";
					query += dateSql;
				}
				query = query +"ORDER BY obj.pk.formNo ";
				
				TypedQuery<KtFormSummary> q = em.createQuery(query, KtFormSummary.class);
				
				q.setParameter("dataLevel", dataLevel);
				if(facs != null) {
					if (facs.size() > 0)
						q.setParameter("facs", facs);
				}
				if(depts != null) {
					if (depts.size() > 0)
						q.setParameter("depts", depts);
				}
				if(!GenericValidator.isBlankOrNull(formCode)) q.setParameter("formCode", formCode);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<KtFormSummary> getKtFormSummaryListByDepts(List<String> depts, String dataLevel, String formCode, Date start_date, Date end_date, Integer periodId)
	{
		List<KtFormSummary> objList = null;
		EntityManager em = null;
		if(!GenericValidator.isBlankOrNull(dataLevel) && (depts != null)) 
		{
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM KtFormSummary obj WHERE obj.pk.dataLevel = :dataLevel ";
				
				if(depts != null) query = query+"AND obj.dept IN :depts ";
				if(!GenericValidator.isBlankOrNull(formCode)) query = query+"AND obj.formCode = :formCode ";
				String dateSql = "";
				if (periodId > 1) {
					dateSql += " AND ((obj.startDate >= :start_date AND obj.startDate <= :end_date) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.endDate <= :end_date ) ";
					dateSql += " OR (obj.endDate >= :start_date AND obj.startDate <= :start_date ) ";
					dateSql += " OR (obj.startDate is null AND obj.endDate >= :start_date ) ";
					dateSql += " OR (obj.startDate <=:end_date AND obj.endDate is null)) ";
					query += dateSql;
				}
				query = query +"ORDER BY obj.pk.formNo ";
				
				TypedQuery<KtFormSummary> q = em.createQuery(query, KtFormSummary.class);
				
				q.setParameter("dataLevel", dataLevel);
				if(depts != null) q.setParameter("depts", depts);
				if(!GenericValidator.isBlankOrNull(formCode)) q.setParameter("formCode", formCode);
				if (periodId > 1) {
					q.setParameter("start_date", start_date);
					q.setParameter("end_date", end_date);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.EMPTY_LIST;
	}
	
	public KtFormHeader_Q updateKtFormHeader_Q(KtFormHeader_Q obj)
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormHeader_Q header = em.find(KtFormHeader_Q.class, obj.getPk());
				if(header!=null) 
				{
					em.remove(header);
				}
				
				obj = em.merge(obj);
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	public KtFormState_Q updateKtFormState_Q(KtFormState_Q obj)
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();

				if(obj.getForm_no()>0) 
				{
					KtFormState_Q o = em.find(KtFormState_Q.class, obj.getForm_no());
					if(o==null) 
					{
						obj = em.merge(obj);
					}
				}
				else
				{
					obj = em.merge(obj);
				}
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormHeader_QList(List<Integer> formNoList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(formNoList) && dataLevel!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				Query q = em.createQuery(	"DELETE FROM KtFormHeader_Q obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", formNoList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
			
		}
	}
	
	public void deleteKtFormState_QList(List<Integer> formNoList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(formNoList) && dataLevel!=null) 
		{
			
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				//System.out.println("formNoList = "+formNoList);
				Query q = em.createQuery(	"DELETE FROM KtFormState_Q obj "+
											"WHERE obj.creation_data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.form_no", formNoList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	public void deleteKtFormList(Map<String, List<Integer>> objMap, String dataLevel)
	{
		if(MapUtils.isNotEmpty(objMap) && dataLevel!=null) 
		{
			Set<String> keySet = objMap.keySet();
			
			for(String formCode : keySet)
			{
				List<Integer> formNoList = objMap.get(formCode);
				
				if(CollectionUtils.isNotEmpty(formNoList)) 
				{
					deleteKtFormHeader_QList(formNoList, dataLevel);
					deleteKtFormState_QList(formNoList, dataLevel);
					
					if(StringUtils.equals(formCode, KtFormIP_P.REPORT_FORM_CODE)) 
					{
						deleteKtFormIpList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormCntProj_P.REPORT_FORM_CODE))
					{
						deleteKtFormCntProjList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormInn_P.REPORT_FORM_CODE))
					{
						deleteKtFormInnList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormCons_P.REPORT_FORM_CODE))
					{
						deleteKtFormConsList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormEA_P.REPORT_FORM_CODE))
					{
						deleteKtFormEAList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormStartup_P.REPORT_FORM_CODE))
					{
						deleteKtFormStartupList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormInvAward_P.REPORT_FORM_CODE))
					{
						deleteKtFormInvAwardList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormSocEngmt_P.REPORT_FORM_CODE))
					{
						deleteKtFormSocEngmtList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormCPD_P.REPORT_FORM_CODE))
					{
						deleteKtFormCPDList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormProfConf_P.REPORT_FORM_CODE))
					{
						deleteKtFormProfConfList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormSem_P.REPORT_FORM_CODE))
					{
						deleteKtFormSemList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormProfEngmt_P.REPORT_FORM_CODE))
					{
						deleteKtFormProfEngmtList(formNoList, dataLevel);
					}
					else if(StringUtils.equals(formCode, KtFormStaffEngmt_P.REPORT_FORM_CODE))
					{
						deleteKtFormStaffEngmtList(formNoList, dataLevel);
					}
				}
			}
		}
	}
	
	
	public void deleteKtFormIpList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				Query q = em.createQuery(	"DELETE FROM KtFormIP_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}

	public KtFormIP_P updateKtFormIp(KtFormIP_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				KtFormIP_P o = em.find(KtFormIP_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				em.merge(obj);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormCntProjList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();

				Query q = em.createQuery(	"DELETE FROM KtFormCntProj_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormCntProj_P updateKtFormCntProj(KtFormCntProj_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormCntProj_P o = em.find(KtFormCntProj_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				em.merge(obj);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormInnList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();
				Query q = em.createQuery(	"DELETE FROM KtFormInn_P obj "+
						"WHERE obj.pk.data_level = :dataLevel "+
						"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormInn_P updateKtFormInn(KtFormInn_P obj)
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormInn_P o = em.find(KtFormInn_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);				
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormConsList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();

				Query q = em.createQuery(	"DELETE FROM KtFormCons_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormCons_P updateKtFormCons(KtFormCons_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormCons_P o = em.find(KtFormCons_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormEAList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();


				Query q = em.createQuery(	"DELETE FROM KtFormEA_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	public KtFormEA_P updateKtFormEA(KtFormEA_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormEA_P o = em.find(KtFormEA_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormStartupList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();

				Query q = em.createQuery(	"DELETE FROM KtFormStartup_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormStartup_P updateKtFormStartup(KtFormStartup_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormStartup_P o = em.find(KtFormStartup_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormInvAwardList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();


				Query q = em.createQuery(	"DELETE FROM KtFormInvAward_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormInvAward_P updateKtFormInvAward(KtFormInvAward_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormInvAward_P o = em.find(KtFormInvAward_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormSocEngmtList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();
				
				Query q = em.createQuery(	"DELETE FROM KtFormSocEngmt_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormSocEngmt_P updateKtFormSocEngmt(KtFormSocEngmt_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormSocEngmt_P o = em.find(KtFormSocEngmt_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormCPDList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();
				
				Query q = em.createQuery(	"DELETE FROM KtFormCPD_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormCPD_P updateKtFormCPD(KtFormCPD_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormCPD_P o = em.find(KtFormCPD_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormProfConfList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();
				
				Query q = em.createQuery(	"DELETE FROM KtFormProfConf_P obj "+
						"WHERE obj.pk.data_level = :dataLevel "+
						"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormProfConf_P updateKtFormProfConf(KtFormProfConf_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormProfConf_P o = em.find(KtFormProfConf_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormSemList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();

				Query q = em.createQuery(	"DELETE FROM KtFormSem_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormSem_P updateKtFormSem(KtFormSem_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormSem_P o = em.find(KtFormSem_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormProfEngmtList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();

				Query q = em.createQuery(	"DELETE FROM KtFormProfEngmt_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormProfEngmt_P updateKtFormProfEngmt(KtFormProfEngmt_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormProfEngmt_P o = em.find(KtFormProfEngmt_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteKtFormStaffEngmtList(List<Integer> objList, String dataLevel) 
	{
		if(CollectionUtils.isNotEmpty(objList) && dataLevel !=null) 
		{
			try 
			{
				em = getEntityManager();

				Query q = em.createQuery(	"DELETE FROM KtFormStaffEngmt_P obj "+
											"WHERE obj.pk.data_level = :dataLevel "+
											"AND "+JPAUtils.convertQueryParamList("obj.pk.form_no", objList, Integer.class));
				q.setParameter("dataLevel", dataLevel);
				
				q.executeUpdate();
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public KtFormStaffEngmt_P updateKtFormStaffEngmt(KtFormStaffEngmt_P obj) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				KtFormStaffEngmt_P o = em.find(KtFormStaffEngmt_P.class, obj.getPk());
				if(o!=null) 
				{
					em.remove(o);
				}
				
				obj = em.merge(obj);
				
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public List<UploadForm> getUploadFormList(Integer periodId, String dataLevel)
	{
		List<UploadForm> objList = null;
		
		if(periodId != null && periodId > 0 && dataLevel!=null) 
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM UploadForm obj WHERE obj.pk.periodId = :periodId AND obj.pk.dataLevel = :dataLevel";			
				TypedQuery<UploadForm> q = em.createQuery(query, UploadForm.class);
				q.setParameter("periodId", periodId);
				q.setParameter("dataLevel", dataLevel);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (objList == null) ? Collections.EMPTY_LIST : objList;
	}
	
	public List<UploadForm> getUploadFormListByFormNo(List<Integer> formNoList, String dataLevel)
	{
		List<UploadForm> objList = null;
		
		if(CollectionUtils.isNotEmpty(formNoList) && dataLevel!=null) 
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();		
				String query = 	"SELECT obj FROM UploadForm obj WHERE obj.pk.dataLevel = :dataLevel "+
								"AND "+JPAUtils.convertQueryParamList("obj.pk.formNo", formNoList, Integer.class);	
				TypedQuery<UploadForm> q = em.createQuery(query, UploadForm.class);
				q.setParameter("dataLevel", dataLevel);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (objList == null) ? Collections.EMPTY_LIST : objList;
	}
	
	public List<UploadStatus> getUploadStatusList()
	{
		List<UploadStatus> objList = null;
		
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM UploadStatus obj";			
			TypedQuery<UploadStatus> q = em.createQuery(query, UploadStatus.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null)? objList : Collections.EMPTY_LIST; 
	}
	
	public UploadStatus updateUploadStatus(UploadStatus obj, List<Integer> formNoList, String dataLevel) 
	{
		if(obj!=null) 
		{
			EntityManager em = null;
			
			try 
			{
				em = getEntityManager();
				
				UploadStatus uploadStatus = em.find(UploadStatus.class, obj.getPk());
				if(uploadStatus!=null) 
				{
					Query q = em.createQuery(	"DELETE FROM UploadForm obj WHERE obj.pk.periodId = :periodId ");
					q.setParameter("periodId", obj.getPk().getPeriodId());
					q.executeUpdate();
					
					em.remove(uploadStatus);
				}
				
				em.persist(obj);
				
				if(CollectionUtils.isNotEmpty(formNoList)) 
				{
					for(Integer formNo : formNoList)
					{
						UploadForm uploadForm = new UploadForm();
						UploadFormPK pk = new UploadFormPK();
						pk.setType(obj.getPk().getType());
						pk.setPeriodId(obj.getPk().getPeriodId());
						pk.setDataLevel(dataLevel);
						pk.setFormNo(formNo);
						uploadForm.setPk(pk);
						em.merge(uploadForm);
					}
					
					
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public List<KtFormSummary> getKtFormSummaryList(String staffNo, Date riStartDate, Date riEndDate, List<CdcfRptPeriod> cdcfGenDateFilterList){
		List<KtFormSummary> ktFormSummaryList = new ArrayList<KtFormSummary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			SimpleDateFormat dateFormatter = new SimpleDateFormat("dd-MMM-yyyy");
	
			sqlBuf.append("SELECT DISTINCT FSV.FORM_NO, FSV.FORM_CODE, FSV.TITLE, FSV.START_DATE, FSV.END_DATE, QH.CDCF_GEN_DATE FROM RH_KT_FORM_SUMMARY_V FSV " + 
						  "LEFT JOIN RH_Q_KT_FORM_HDR QH ON QH.FORM_NO = FSV.FORM_NO " + 
						  "WHERE FSV.DISPLAY_IND = 'Y' " + 
						  "AND FSV.DATA_LEVEL  = 'P' " +
						  "AND FSV.STAFF_NO ='" + staffNo + "' " );
			
			
			if(riStartDate != null) {
				sqlBuf.append("AND FSV.START_DATE >= '" + dateFormatter.format(riStartDate) + "' ");
			}
			
			if(riEndDate != null) {
				sqlBuf.append("AND FSV.END_DATE <= '" + dateFormatter.format(riEndDate) + "' ");
			}
			
			if(cdcfGenDateFilterList != null && cdcfGenDateFilterList.size() > 0) {
				sqlBuf.append("AND ( ");
				for(CdcfRptPeriod period: cdcfGenDateFilterList) {
					sqlBuf.append("(QH.CDCF_GEN_DATE >= '" + dateFormatter.format(period.getDate_from()) + "' AND " + 
								  "QH.CDCF_GEN_DATE <= '" + dateFormatter.format(period.getDate_to()) + "') OR ");
				}
				
				sqlBuf.delete(sqlBuf.length() - 4, sqlBuf.length() - 1);
				sqlBuf.append(" ) ");
			}
			
			//System.out.println(sqlBuf.toString());
			sqlBuf.append("ORDER BY FSV.FORM_CODE, FSV.FORM_NO ");

			logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();

			
			while (rs.next()){
				KtFormSummary ktFormSummary = new KtFormSummary();
				ktFormSummary.setFormCode(rs.getString("FORM_CODE"));
				ktFormSummary.setTitle(rs.getString("TITLE"));
				ktFormSummary.setStartDate(rs.getTimestamp("START_DATE"));
				ktFormSummary.setEndDate(rs.getTimestamp("END_DATE"));
				
				ktFormSummaryList.add(ktFormSummary);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(em);
		}
		
		return ktFormSummaryList;
	}
	
	public List<Summary> getKtSummaryCountList(String staffNo, String dataLevel, String startDate, String endDate, List<String> facList, List<String> deptList, String form_code){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (facList != null && deptList != null) {
				selectDept = "H.FAC AS F, H.DEPT AS D, ";
			}
			if (facList != null && deptList == null) {
				selectDept = "H.FAC AS F, ";
			}
			if (facList == null && deptList != null) {
				selectDept = "H.DEPT AS D, ";
			}
			
			sqlBuf.append("SELECT RP.PERIOD_ID, RP.PERIOD_DESC, H.FORM_CODE, " + selectDept + "COUNT(DISTINCT(H.FORM_NO)) AS C FROM RICH.RH_KT_FORM_SUMMARY_V H"
					+ "    LEFT JOIN RICH.RH_Z_KT_RPT_PERIOD RP ON "
					+ "        H.START_DATE BETWEEN RP.DATE_FROM AND RP.DATE_TO OR "
					+ "        H.END_DATE BETWEEN RP.DATE_FROM AND RP.DATE_TO  ");
			sqlBuf.append(" WHERE H.DATA_LEVEL = '"+dataLevel+"' ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND H.STAFF_NO = '"+staffNo+"' ");
			}
			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				sqlBuf.append(" AND H.START_DATE BETWEEN TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			
			if (facList != null && deptList != null) {
				sqlBuf.append(" AND (H.FAC IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) AND H.DEPT IS NULL) ");
				sqlBuf.append(" OR H.DEPT IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, H.FORM_CODE, H.FAC, H.DEPT "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, H.FORM_CODE, H.FAC, H.DEPT ");
			}
			
			if (facList == null && deptList != null) {
				sqlBuf.append(" AND H.DEPT IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, H.FORM_CODE, H.DEPT "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, H.FORM_CODE, H.DEPT ");
			}
			
			if (facList != null && deptList == null) {
				sqlBuf.append(" AND (H.FAC IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) AND H.DEPT IS NULL) ");
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, H.FORM_CODE, H.FAC "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, H.FORM_CODE, H.FAC ");
			}
			
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, H.FORM_CODE "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, H.FORM_CODE ");
			}
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next())
				{
					Summary obj = new Summary();
					obj.setPeriod_id(rs.getString("PERIOD_ID"));
					obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
					obj.setCount(rs.getString("C"));
					obj.setForm_code(rs.getString("FORM_CODE"));
					if (deptList != null) {
						obj.setFacDept(rs.getString("D"));
					}
					if (facList != null) {
						obj.setFac(rs.getString("F"));
					}
					objList.add(obj);						
				}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
}