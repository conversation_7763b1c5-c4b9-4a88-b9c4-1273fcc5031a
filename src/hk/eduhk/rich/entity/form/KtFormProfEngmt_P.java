package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_PROF_ENGMT_HDR")
public class KtFormProfEngmt_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_PROF_ENGMT";
	
	public static Logger logger = Logger.getLogger(KtFormProfEngmt_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "cond_staff")
	private String cond_staff;
	
	@Column(name = "organizer")
	private String organizer;
	
	@Column(name = "check_engage")
	private String check_engage;
	
	@Column(name = "num_teacher")
	private Integer  num_teacher;
	
	@Column(name = "num_principal")
	private Integer  num_principal;
	
	@Column(name = "num_leader")
	private Integer  num_leader;

	@Column(name = "num_other")
	private Integer  num_other;
	
	@Column(name = "num_pax_ben")
	private Integer  num_pax_ben;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;

	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public String getDept()
	{
		return dept;
	}

	
	public Date getStart_date()
	{
		return start_date;
	}

	
	public Date getEnd_date()
	{
		return end_date;
	}

	
	public String getCond_staff()
	{
		return cond_staff;
	}

	
	public String getOrganizer()
	{
		return organizer;
	}

	
	public String getCheck_engage()
	{
		return check_engage;
	}

	
	public Integer getNum_teacher()
	{
		return num_teacher;
	}

	
	public Integer getNum_principal()
	{
		return num_principal;
	}

	
	public Integer getNum_leader()
	{
		return num_leader;
	}

	
	public Integer getNum_other()
	{
		return num_other;
	}

	
	public Integer getNum_pax_ben()
	{
		return num_pax_ben;
	}

	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}

	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}

	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}

	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public void setDept(String dept)
	{
		this.dept = dept;
	}

	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}

	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}

	
	public void setCond_staff(String cond_staff)
	{
		this.cond_staff = cond_staff;
	}

	
	public void setOrganizer(String organizer)
	{
		this.organizer = organizer;
	}

	
	public void setCheck_engage(String check_engage)
	{
		this.check_engage = check_engage;
	}

	
	public void setNum_teacher(Integer num_teacher)
	{
		this.num_teacher = num_teacher;
	}

	
	public void setNum_principal(Integer num_principal)
	{
		this.num_principal = num_principal;
	}

	
	public void setNum_leader(Integer num_leader)
	{
		this.num_leader = num_leader;
	}

	
	public void setNum_other(Integer num_other)
	{
		this.num_other = num_other;
	}

	
	public void setNum_pax_ben(Integer num_pax_ben)
	{
		this.num_pax_ben = num_pax_ben;
	}

	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}

	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}

	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}

	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}
	
	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}

	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}


	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}


	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}


	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormProfEngmt_P other = (KtFormProfEngmt_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormProfEngmt_P [pk=" + pk + ", title=" + title + ", fac=" + fac + ", dept=" + dept + ", start_date="
				+ start_date + ", end_date=" + end_date + ", cond_staff=" + cond_staff + ", organizer=" + organizer
				+ ", check_engage=" + check_engage + ", num_teacher=" + num_teacher + ", num_principal=" + num_principal
				+ ", num_leader=" + num_leader + ", num_other=" + num_other + ", num_pax_ben=" + num_pax_ben
				+ ", remarks_staff=" + remarks_staff + ", remarks_dept=" + remarks_dept + ", remarks_kt=" + remarks_kt
				+ "]";
	}


}
