package hk.eduhk.rich.entity.form;

import java.io.IOException;
import java.io.Serializable;
import java.util.Base64;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


@Embeddable
public class KtFormHeader_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="form_no")
	private Integer form_no;
	
	@Column(name="data_level")
	private String data_level;	
	
	
	// JSON processing
	private static final ObjectMapper objMapper = new ObjectMapper().setSerializationInclusion(Include.NON_NULL);
	private static final TypeReference<KtFormHeader_PK> typeRef = new TypeReference<KtFormHeader_PK>() {};
	
	// Logging
	public static Logger logger = Logger.getLogger(KtFormHeader_PK.class.toString());


	public Integer getForm_no()
	{
		return form_no;
	}



	
	public void setForm_no(Integer form_no)
	{
		this.form_no = form_no;
	}



	public String getData_level()
	{
		return data_level;
	}


	
	public void setData_level(String data_level)
	{
		this.data_level = data_level;
	}




	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((data_level == null) ? 0 : data_level.hashCode());
		result = prime * result + ((form_no == null) ? 0 : form_no.hashCode());
		return result;
	}




	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormHeader_PK other = (KtFormHeader_PK) obj;
		if (data_level == null)
		{
			if (other.data_level != null)
				return false;
		}
		else if (!data_level.equals(other.data_level))
			return false;
		if (form_no == null)
		{
			if (other.form_no != null)
				return false;
		}
		else if (!form_no.equals(other.form_no))
			return false;
		return true;
	}




	@Override
	public String toString()
	{
		return "KtFormHeader_P_PK [form_no=" + form_no + ", data_level=" + data_level + "]";
	}

	@JsonIgnore
	public String getAsKeyString()
	{
		String strKey = "";
		
		try
		{
			// Convert instance to JSON representation
			String json = objMapper.writeValueAsString(this);
			strKey = json;
			
			// Encode JSON as base64 encoding
			Base64.Encoder encoder = Base64.getEncoder();
			strKey = encoder.encodeToString(json.getBytes());
		}
		catch (IOException ioe)
		{
			logger.log(Level.WARNING, "Cannot convert instance to base64 (instance=" + this + ")", ioe.getMessage());
		}
		
		return strKey;
	}
	
	
	@JsonIgnore
	public KtFormHeader_PK getAsKeyObject(String strKey)
	{
		KtFormHeader_PK pk = null;
		
		if (!GenericValidator.isBlankOrNull(strKey))
		{
			try
			{
				// Convert base64 encoding to JSON representation
				Base64.Decoder decoder = Base64.getDecoder();
				String json = new String(decoder.decode(strKey));
				
				// Convert JSON representation to instance
				pk = objMapper.readValue(json, typeRef);
			}
			catch (IOException ioe)
			{
				logger.log(Level.WARNING, "Cannot convert base64 to instance (strKey=" + strKey + ")", ioe.getMessage());
			}
		}
		
		return pk;
	}


	
}
