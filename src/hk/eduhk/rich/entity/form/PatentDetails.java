package hk.eduhk.rich.entity.form;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.entity.BaseRI;

@SuppressWarnings("serial")
public class PatentDetails
{
	private String name;
	private String num;
	private String date;
	private String country;

	public PatentDetails()
	{
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	
	public String getNum()
	{
		return num;
	}

	
	public void setNum(String num)
	{
		this.num = num;
	}

	
	public String getDate()
	{
		return date;
	}

	
	public void setDate(String date)
	{
		this.date = date;
	}

	
	public String getCountry()
	{
		return country;
	}

	
	public void setCountry(String country)
	{
		this.country = country;
	}

	@Override
	public String toString()
	{
		return "PatentDetails [name=" + name + ", num=" + num + ", date=" + date + ", country=" + country + "]";
	}

}