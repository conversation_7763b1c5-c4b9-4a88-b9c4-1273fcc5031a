package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_CPD_HDR")
public class KtFormCPD_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_CPD";
	
	public static Logger logger = Logger.getLogger(KtFormCPD_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "act_code")
	private String act_code;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "organizer")
	private String organizer;

	@Column(name = "free_charge")
	private String free_charge;
	
	@Column(name = "income_rpt_unit")
	private Double  income_rpt_unit;
	
	@Column(name = "income_fo")
	private Double  income_fo;
	
	@Column(name = "income_fo_rem")
	private String  income_fo_rem;
	
	@Column(name = "income_rdo")
	private Double  income_rdo;
	
	@Column(name = "expnd_rpt_unit")
	private Double  expnd_rpt_unit;
	
	@Column(name = "expnd_fo")
	private Double  expnd_fo;
	
	@Column(name = "expnd_fo_rem")
	private String  expnd_fo_rem;
	
	@Column(name = "expnd_rdo")
	private Double  expnd_rdo;
	
	@Column(name = "num_key_partner")
	private Integer  num_key_partner;
	
	@Column(name = "num_teacher")
	private Integer  num_teacher;
	
	@Column(name = "num_principal")
	private Integer  num_principal;
	
	@Column(name = "num_stu_contact_hr")
	private Integer  num_stu_contact_hr;
	
	@Column(name = "staff_man_day")
	private Integer  staff_man_day;
	
	@Column(name = "num_ext_prof")
	private Integer  num_ext_prof;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;
	
	@Column(name = "research_element")
	private String  research_element;
	
	@Column(name = "crse_prog")
	private String  crse_prog;
	
	@Column(name = "ent_element")
	private String  ent_element;
	
	@Column(name = "act_mode")
	private String  act_mode;
	
	@Column(name = "eduhk_org")
	private String  eduhk_org;
	
	@Column(name = "region")
	private String region;
	
	@Column(name = "budget_approval")
	private String  budget_approval;
	
	@Column(name = "budget")
	private Double  budget;
	
	@Column(name = "num_other")
	private Integer  num_other;
	
	@Column(name = "num_school")
	private Integer  num_school;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	@Column(name = "pi")
	private String  pi;
	
	@Column(name = "fund_src")
	private String  fund_src;
	
	@Transient
	private String regionValue = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}


	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}


	
	public String getTitle()
	{
		return title;
	}



	
	public void setTitle(String title)
	{
		this.title = title;
	}



	
	public String getAct_code()
	{
		return act_code;
	}



	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}



	
	public String getFac()
	{
		return fac;
	}



	
	public void setFac(String fac)
	{
		this.fac = fac;
	}



	
	public String getDept()
	{
		return dept;
	}



	
	public void setDept(String dept)
	{
		this.dept = dept;
	}



	
	
	public Date getStart_date()
	{
		return start_date;
	}



	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}



	
	public Date getEnd_date()
	{
		return end_date;
	}



	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}



	public String getOrganizer()
	{
		return organizer;
	}

	
	public void setOrganizer(String organizer)
	{
		this.organizer = organizer;
	}

	
	public String getFree_charge()
	{
		return free_charge;
	}

	
	public void setFree_charge(String free_charge)
	{
		this.free_charge = free_charge;
	}

	
	

	
	
	public Double getIncome_rpt_unit()
	{
		return income_rpt_unit;
	}



	
	public void setIncome_rpt_unit(Double income_rpt_unit)
	{
		this.income_rpt_unit = income_rpt_unit;
	}



	
	public Double getIncome_fo()
	{
		return income_fo;
	}



	
	public void setIncome_fo(Double income_fo)
	{
		this.income_fo = income_fo;
	}



	
	public String getIncome_fo_rem()
	{
		return income_fo_rem;
	}



	
	public void setIncome_fo_rem(String income_fo_rem)
	{
		this.income_fo_rem = income_fo_rem;
	}



	
	public Double getIncome_rdo()
	{
		return income_rdo;
	}



	
	public void setIncome_rdo(Double income_rdo)
	{
		this.income_rdo = income_rdo;
	}



	public Double getExpnd_rpt_unit()
	{
		return expnd_rpt_unit;
	}

	
	public void setExpnd_rpt_unit(Double expnd_rpt_unit)
	{
		this.expnd_rpt_unit = expnd_rpt_unit;
	}

	
	public Double getExpnd_fo()
	{
		return expnd_fo;
	}

	
	public void setExpnd_fo(Double expnd_fo)
	{
		this.expnd_fo = expnd_fo;
	}

	
	public String getExpnd_fo_rem()
	{
		return expnd_fo_rem;
	}

	
	public void setExpnd_fo_rem(String expnd_fo_rem)
	{
		this.expnd_fo_rem = expnd_fo_rem;
	}

	
	public Double getExpnd_rdo()
	{
		return expnd_rdo;
	}

	
	public void setExpnd_rdo(Double expnd_rdo)
	{
		this.expnd_rdo = expnd_rdo;
	}

	
	public Integer getNum_key_partner()
	{
		return num_key_partner;
	}

	
	public void setNum_key_partner(Integer num_key_partner)
	{
		this.num_key_partner = num_key_partner;
	}

	
	public Integer getNum_teacher()
	{
		return num_teacher;
	}

	
	public void setNum_teacher(Integer num_teacher)
	{
		this.num_teacher = num_teacher;
	}

	
	public Integer getNum_principal()
	{
		return num_principal;
	}

	
	public void setNum_principal(Integer num_principal)
	{
		this.num_principal = num_principal;
	}

	
	public Integer getNum_stu_contact_hr()
	{
		return num_stu_contact_hr;
	}

	
	public void setNum_stu_contact_hr(Integer num_stu_contact_hr)
	{
		this.num_stu_contact_hr = num_stu_contact_hr;
	}

	
	public Integer getStaff_man_day()
	{
		return staff_man_day;
	}

	
	public void setStaff_man_day(Integer staff_man_day)
	{
		this.staff_man_day = staff_man_day;
	}

	
	public Integer getNum_ext_prof()
	{
		return num_ext_prof;
	}

	
	public void setNum_ext_prof(Integer num_ext_prof)
	{
		this.num_ext_prof = num_ext_prof;
	}

	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}

	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}

	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}

	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}

	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}

	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}
	
	
	
	public String getResearch_element()
	{
		return research_element;
	}



	
	public void setResearch_element(String research_element)
	{
		this.research_element = research_element;
	}



	
	public String getCrse_prog()
	{
		return crse_prog;
	}



	
	public void setCrse_prog(String crse_prog)
	{
		this.crse_prog = crse_prog;
	}



	
	public String getEnt_element()
	{
		return ent_element;
	}



	
	public void setEnt_element(String ent_element)
	{
		this.ent_element = ent_element;
	}



	
	public String getAct_mode()
	{
		return act_mode;
	}



	
	public void setAct_mode(String act_mode)
	{
		this.act_mode = act_mode;
	}



	
	public String getEduhk_org()
	{
		return eduhk_org;
	}



	
	public void setEduhk_org(String eduhk_org)
	{
		this.eduhk_org = eduhk_org;
	}



	
	public String getRegion()
	{
		return region;
	}



	
	public void setRegion(String region)
	{
		this.region = region;
	}



	
	public String getBudget_approval()
	{
		return budget_approval;
	}



	
	public void setBudget_approval(String budget_approval)
	{
		this.budget_approval = budget_approval;
	}



	
	public Double getBudget()
	{
		return budget;
	}



	
	public void setBudget(Double budget)
	{
		this.budget = budget;
	}



	
	public Integer getNum_other()
	{
		return num_other;
	}



	
	public void setNum_other(Integer num_other)
	{
		this.num_other = num_other;
	}



	
	public Integer getNum_school()
	{
		return num_school;
	}



	
	public void setNum_school(Integer num_school)
	{
		this.num_school = num_school;
	}



	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}
	
	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}




	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}



	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}



	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}



	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}


	public String getRegionValue()
	{
		if(regionValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getRegion()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_REGION", getRegion(), "US");
				
				if(obj!=null) regionValue = obj.getDescription();
			}
		}
		return regionValue;
	}


	
	public void setRegionValue(String regionValue)
	{
		this.regionValue = regionValue;
	}
	
	
	public String getPi()
	{
		return pi;
	}



	
	public void setPi(String pi)
	{
		this.pi = pi;
	}



	
	public String getFund_src()
	{
		return fund_src;
	}



	
	public void setFund_src(String fund_src)
	{
		this.fund_src = fund_src;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormCPD_P other = (KtFormCPD_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "KtFormCPD_P [pk=" + pk + ", title=" + title + ", act_code=" + act_code + ", fac=" + fac + ", dept="
				+ dept + ", start_date=" + start_date + ", end_date=" + end_date + ", organizer=" + organizer
				+ ", free_charge=" + free_charge + ", income_rpt_unit=" + income_rpt_unit + ", income_fo=" + income_fo
				+ ", income_fo_rem=" + income_fo_rem + ", income_rdo=" + income_rdo + ", expnd_rpt_unit="
				+ expnd_rpt_unit + ", expnd_fo=" + expnd_fo + ", expnd_fo_rem=" + expnd_fo_rem + ", expnd_rdo="
				+ expnd_rdo + ", num_key_partner=" + num_key_partner + ", num_teacher=" + num_teacher
				+ ", num_principal=" + num_principal + ", num_stu_contact_hr=" + num_stu_contact_hr + ", staff_man_day="
				+ staff_man_day + ", num_ext_prof=" + num_ext_prof + ", remarks_staff=" + remarks_staff
				+ ", remarks_dept=" + remarks_dept + ", remarks_kt=" + remarks_kt + ", research_element="
				+ research_element + ", crse_prog=" + crse_prog + ", ent_element=" + ent_element + ", act_mode="
				+ act_mode + ", eduhk_org=" + eduhk_org + ", region=" + region + ", budget_approval=" + budget_approval
				+ ", budget=" + budget + ", num_other=" + num_other + ", num_school=" + num_school + ", num_proj_day="
				+ num_proj_day + ", num_proj_day_in_yr=" + num_proj_day_in_yr + ", pi=" + pi + ", fund_src=" + fund_src
				+ ", regionValue=" + regionValue + "]";
	}
}
