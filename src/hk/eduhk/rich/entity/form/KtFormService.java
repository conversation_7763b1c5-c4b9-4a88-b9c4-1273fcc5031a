package hk.eduhk.rich.entity.form;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.ejb.LocalBean;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.entity.operation.UploadForm;
import hk.eduhk.rich.entity.operation.UploadStatus;

@LocalBean
@Stateless
public class KtFormService
{
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public Map<String, List<Object>> updateKtFormList(	UploadStatus uploadStatus, String dataLevel, 
														Map<String, List<Object>> importMap, String userId, String publishStatus)
	{
		Map<String, List<Object>> objMap = new HashMap<String, List<Object>>();
		FormDAO dao = FormDAO.getCacheInstance();
		
		Set<Integer> batchFormNoSet = new HashSet<Integer>();
		
		if(uploadStatus!=null && dataLevel!=null) 
		{	
			//Get Existing Upload Form List of selected period
			List<UploadForm> existingUploadFormList = dao.getUploadFormList(uploadStatus.getPk().getPeriodId(), dataLevel);
			List<Integer> preRemoveFormNumList = existingUploadFormList.stream().map(f->f.getPk().getFormNo()).collect(Collectors.toList());
			
			List<Integer> uploadedFormNoList = new ArrayList<Integer>();
			
			if(MapUtils.isNotEmpty(importMap)) 
			{
				Set<String> rptFormCodeSet = importMap.keySet();
				
				for(String rptFormCode : rptFormCodeSet)
				{
					List<Object> objList = importMap.get(rptFormCode);
					
					if(CollectionUtils.isNotEmpty(objList)) 
					{
						if(StringUtils.equals(rptFormCode, KtFormIP_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormIP_P formObj = (KtFormIP_P) obj;
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormIp(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj.setUserstamp(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormCntProj_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormCntProj_P formObj = (KtFormCntProj_P) obj;
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormCntProj(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormInn_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormInn_P formObj = (KtFormInn_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormInn(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormCons_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormCons_P formObj = (KtFormCons_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormCons(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormEA_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormEA_P formObj = (KtFormEA_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormEA(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormStartup_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormStartup_P formObj = (KtFormStartup_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormStartup(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormInvAward_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormInvAward_P formObj = (KtFormInvAward_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormInvAward(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormSocEngmt_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormSocEngmt_P formObj = (KtFormSocEngmt_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormSocEngmt(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormCPD_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormCPD_P formObj = (KtFormCPD_P) obj;
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormCPD(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormProfConf_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormProfConf_P formObj = (KtFormProfConf_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormProfConf(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormSem_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormSem_P formObj = (KtFormSem_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormSem(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormProfEngmt_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormProfEngmt_P formObj = (KtFormProfEngmt_P) obj;
								
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormProfEngmt(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						else if(StringUtils.equals(rptFormCode, KtFormStaffEngmt_P.REPORT_FORM_CODE)) 
						{
							for(Object obj : objList)
							{
								KtFormStaffEngmt_P formObj = (KtFormStaffEngmt_P) obj;
								
								
								KtFormState_Q formStateObj = new KtFormState_Q();
								formStateObj.setCreation_data_level(dataLevel);
								formStateObj.setForm_code(rptFormCode);
								formStateObj.setPeriodId(uploadStatus.getPk().getPeriodId());
								if(formObj.getPk().getForm_no()!=null) formStateObj.setForm_no(formObj.getPk().getForm_no());
								formStateObj = dao.updateKtFormState_Q(formStateObj);
								
								formObj.getPk().setData_level(dataLevel);
								formObj.getPk().setForm_no(formStateObj.getForm_no());
								formObj.setUserstamp(userId);
								formObj = dao.updateKtFormStaffEngmt(formObj);
								
								KtFormHeader_Q formHeaderObj = new KtFormHeader_Q();
								formHeaderObj.getPk().setForm_no(formObj.getPk().getForm_no());
								formHeaderObj.getPk().setData_level(dataLevel);
								formHeaderObj.setPublish_status(publishStatus);
								formHeaderObj.setLast_modified_date((new Timestamp(new Date().getTime())));
								formHeaderObj.setLast_modified_by(userId);
								formHeaderObj = dao.updateKtFormHeader_Q(formHeaderObj);
								
								uploadedFormNoList.add(formHeaderObj.getPk().getForm_no());
							}
						}
						
						
					}
				}
				
				
			}
			uploadStatus = dao.updateUploadStatus(uploadStatus, uploadedFormNoList, dataLevel);
			
			
			if(CollectionUtils.isNotEmpty(uploadedFormNoList))
				preRemoveFormNumList.removeAll(uploadedFormNoList);
			
			if(CollectionUtils.isNotEmpty(preRemoveFormNumList))
			{
				List<UploadForm> retainList = dao.getUploadFormListByFormNo(preRemoveFormNumList, dataLevel);
				List<Integer> retainFormNoList = retainList.stream().map(r->r.getPk().getFormNo()).collect(Collectors.toList());
				preRemoveFormNumList.removeAll(retainFormNoList);
				
				//Remove the related form
				if(CollectionUtils.isNotEmpty(preRemoveFormNumList)) 
				{
					List<KtFormState_Q> preRemoveFormStateList = dao.getKtFormState_QList(preRemoveFormNumList);
					
					Map<String, List<Integer>> preRemoveFormMap = new HashMap<String, List<Integer>>();
					for(KtFormState_Q state : preRemoveFormStateList)
					{
						List<Integer> formNoList = preRemoveFormMap.get(state.getForm_code());
						if(formNoList == null) 
						{
							formNoList = new ArrayList<Integer>();
						}
						formNoList.add(state.getForm_no());
						preRemoveFormMap.put(state.getForm_code(), formNoList);
					}
					
					dao.deleteKtFormList(preRemoveFormMap, dataLevel);
					
				}
			}
				
			
			
		}
		
		return objMap;
	}
}
