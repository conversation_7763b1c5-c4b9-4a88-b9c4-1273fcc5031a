package hk.eduhk.rich.entity.form;

import java.sql.Timestamp;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.persistence.Column;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.BaseRIFull;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;

@SuppressWarnings("serial")
public class KtActivity extends UserPersistenceObject
{
	private int form_no;
	private String authorList;
	private String data_level;
	private String fac;
	private String dept;
	private Date start_date;
	private Date end_date;
	private String title;
	private String act_code;
	private String organizer;
	private String free_charge;
	private String income_rpt_unit;
	private String income_fo;
	private String income_fo_rem;
	private String income_rdo;
	private String expnd_rpt_unit;
	private String expnd_fo;
	private String expnd_fo_rem;
	private String expnd_rdo;
	private String num_key_partner;
	private String num_teacher;
	private String num_principal;
	private String num_stu_contact_hr;
	private String staff_man_day;
	private String num_ext_prof;
	private String remarks_staff;
	private String remarks_dept;
	private String remarks_kt;
	private String principal_inves;
	private String fund_src;
	private String budget;
	private String income_grant;
	private String num_stakeholder;
	private String num_school;
	private String num_stu;
	private String fund_src_type;
	private String fund_src_org;
	private String eduhk_role;
	private String cumu_income_fo;
	private String num_org;
	private String num_adv_body;
	private String region;
	private String ct_person;
	private String num_stu_ug;
	private String num_stu_pg;
	private String num_staff;
	private String num_alumni;
	private String act_type;
	private String credit_bearing;
	private String event_name;
	private String name_pi;
	private String name_other;
	private String cat;
	private String software_lic;
	private String staff_name;
	private String income_cat;
	private String income_name;
	private String org_type;
	private String target_pax;
	private String num_presentation;
	private String num_pax;
	private String cond_staff;
	private String check_engage;
	private String num_leader;
	private String num_other;
	private String num_pax_ben;
	private String check_pax;
	private String event_type;
	private String perform;
	private String num_attendee;
	private String ext_body_name;
	private String country;
	private String ext_body_nature;
	private String post_engaged;
	private String act_cat;
	private String num_subsessions;
	private String pi;
	private String num_speakers;
	
	public KtActivity()
	{
	}
	
	public int getForm_no()
	{
		return form_no;
	}

	
	public void setForm_no(int form_no)
	{
		this.form_no = form_no;
	}
	
	
	public String getAuthorList()
	{
		return authorList;
	}

	
	public void setAuthorList(String authorList)
	{
		this.authorList = authorList;
	}

	public String getData_level()
	{
		return data_level;
	}

	
	public void setData_level(String data_level)
	{
		this.data_level = data_level;
	}


	public String getFac()
	{
		return fac;
	}


	
	public void setFac(String fac)
	{
		this.fac = fac;
	}


	
	public String getDept()
	{
		return dept;
	}


	
	public void setDept(String dept)
	{
		this.dept = dept;
	}


	
	public Date getStart_date()
	{
		return start_date;
	}


	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}


	
	public Date getEnd_date()
	{
		return end_date;
	}


	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}

	
	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getAct_code()
	{
		return act_code;
	}

	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}

	
	public String getOrganizer()
	{
		return organizer;
	}

	
	public void setOrganizer(String organizer)
	{
		this.organizer = organizer;
	}

	
	public String getFree_charge()
	{
		return free_charge;
	}

	
	public void setFree_charge(String free_charge)
	{
		this.free_charge = free_charge;
	}

	public String getIncome_rpt_unit()
	{
		return income_rpt_unit;
	}


	
	public void setIncome_rpt_unit(String income_rpt_unit)
	{
		this.income_rpt_unit = income_rpt_unit;
	}


	
	public String getIncome_fo()
	{
		return income_fo;
	}


	
	public void setIncome_fo(String income_fo)
	{
		this.income_fo = income_fo;
	}


	
	public String getIncome_fo_rem()
	{
		return income_fo_rem;
	}


	
	public void setIncome_fo_rem(String income_fo_rem)
	{
		this.income_fo_rem = income_fo_rem;
	}


	
	public String getIncome_rdo()
	{
		return income_rdo;
	}


	
	public void setIncome_rdo(String income_rdo)
	{
		this.income_rdo = income_rdo;
	}


	
	public String getExpnd_rpt_unit()
	{
		return expnd_rpt_unit;
	}


	
	public void setExpnd_rpt_unit(String expnd_rpt_unit)
	{
		this.expnd_rpt_unit = expnd_rpt_unit;
	}


	
	public String getExpnd_fo()
	{
		return expnd_fo;
	}


	
	public void setExpnd_fo(String expnd_fo)
	{
		this.expnd_fo = expnd_fo;
	}


	
	public String getExpnd_fo_rem()
	{
		return expnd_fo_rem;
	}


	
	public void setExpnd_fo_rem(String expnd_fo_rem)
	{
		this.expnd_fo_rem = expnd_fo_rem;
	}


	
	public String getExpnd_rdo()
	{
		return expnd_rdo;
	}


	
	public void setExpnd_rdo(String expnd_rdo)
	{
		this.expnd_rdo = expnd_rdo;
	}


	
	public String getNum_key_partner()
	{
		return num_key_partner;
	}


	
	public void setNum_key_partner(String num_key_partner)
	{
		this.num_key_partner = num_key_partner;
	}


	
	public String getNum_teacher()
	{
		return num_teacher;
	}


	
	public void setNum_teacher(String num_teacher)
	{
		this.num_teacher = num_teacher;
	}


	
	public String getNum_principal()
	{
		return num_principal;
	}


	
	public void setNum_principal(String num_principal)
	{
		this.num_principal = num_principal;
	}


	
	public String getNum_stu_contact_hr()
	{
		return num_stu_contact_hr;
	}


	
	public void setNum_stu_contact_hr(String num_stu_contact_hr)
	{
		this.num_stu_contact_hr = num_stu_contact_hr;
	}


	
	public String getStaff_man_day()
	{
		return staff_man_day;
	}


	
	public void setStaff_man_day(String staff_man_day)
	{
		this.staff_man_day = staff_man_day;
	}


	
	public String getNum_ext_prof()
	{
		return num_ext_prof;
	}


	
	public void setNum_ext_prof(String num_ext_prof)
	{
		this.num_ext_prof = num_ext_prof;
	}


	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}


	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}


	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}


	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}


	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}


	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}

	
	public String getPrincipal_inves()
	{
		return principal_inves;
	}

	
	public void setPrincipal_inves(String principal_inves)
	{
		this.principal_inves = principal_inves;
	}

	
	public String getFund_src()
	{
		return fund_src;
	}

	
	public void setFund_src(String fund_src)
	{
		this.fund_src = fund_src;
	}

	
	public String getBudget()
	{
		return budget;
	}

	
	public void setBudget(String budget)
	{
		this.budget = budget;
	}

	
	public String getIncome_grant()
	{
		return income_grant;
	}

	
	public void setIncome_grant(String income_grant)
	{
		this.income_grant = income_grant;
	}

	
	public String getNum_stakeholder()
	{
		return num_stakeholder;
	}

	
	public void setNum_stakeholder(String num_stakeholder)
	{
		this.num_stakeholder = num_stakeholder;
	}

	
	public String getNum_school()
	{
		return num_school;
	}

	
	public void setNum_school(String num_school)
	{
		this.num_school = num_school;
	}

	
	public String getNum_stu()
	{
		return num_stu;
	}

	
	public void setNum_stu(String num_stu)
	{
		this.num_stu = num_stu;
	}

	
	public String getFund_src_type()
	{
		return fund_src_type;
	}

	
	public void setFund_src_type(String fund_src_type)
	{
		this.fund_src_type = fund_src_type;
	}

	
	public String getFund_src_org()
	{
		return fund_src_org;
	}

	
	public void setFund_src_org(String fund_src_org)
	{
		this.fund_src_org = fund_src_org;
	}

	
	public String getEduhk_role()
	{
		return eduhk_role;
	}

	
	public void setEduhk_role(String eduhk_role)
	{
		this.eduhk_role = eduhk_role;
	}

	
	public String getCumu_income_fo()
	{
		return cumu_income_fo;
	}

	
	public void setCumu_income_fo(String cumu_income_fo)
	{
		this.cumu_income_fo = cumu_income_fo;
	}

	
	public String getNum_org()
	{
		return num_org;
	}

	
	public void setNum_org(String num_org)
	{
		this.num_org = num_org;
	}

	
	public String getNum_adv_body()
	{
		return num_adv_body;
	}

	
	public void setNum_adv_body(String num_adv_body)
	{
		this.num_adv_body = num_adv_body;
	}

	
	public String getRegion()
	{
		return region;
	}

	
	public void setRegion(String region)
	{
		this.region = region;
	}

	
	public String getCt_person()
	{
		return ct_person;
	}

	
	public void setCt_person(String ct_person)
	{
		this.ct_person = ct_person;
	}

	
	public String getNum_stu_ug()
	{
		return num_stu_ug;
	}

	
	public void setNum_stu_ug(String num_stu_ug)
	{
		this.num_stu_ug = num_stu_ug;
	}

	
	public String getNum_stu_pg()
	{
		return num_stu_pg;
	}

	
	public void setNum_stu_pg(String num_stu_pg)
	{
		this.num_stu_pg = num_stu_pg;
	}

	
	public String getNum_staff()
	{
		return num_staff;
	}

	
	public void setNum_staff(String num_staff)
	{
		this.num_staff = num_staff;
	}

	
	public String getNum_alumni()
	{
		return num_alumni;
	}

	
	public void setNum_alumni(String num_alumni)
	{
		this.num_alumni = num_alumni;
	}

	
	public String getAct_type()
	{
		return act_type;
	}

	
	public void setAct_type(String act_type)
	{
		this.act_type = act_type;
	}

	
	public String getCredit_bearing()
	{
		return credit_bearing;
	}

	
	public void setCredit_bearing(String credit_bearing)
	{
		this.credit_bearing = credit_bearing;
	}

	
	public String getEvent_name()
	{
		return event_name;
	}

	
	public void setEvent_name(String event_name)
	{
		this.event_name = event_name;
	}

	
	public String getName_pi()
	{
		return name_pi;
	}

	
	public void setName_pi(String name_pi)
	{
		this.name_pi = name_pi;
	}

	
	public String getName_other()
	{
		return name_other;
	}

	
	public void setName_other(String name_other)
	{
		this.name_other = name_other;
	}

	
	public String getCat()
	{
		return cat;
	}

	
	public void setCat(String cat)
	{
		this.cat = cat;
	}

	
	public String getSoftware_lic()
	{
		return software_lic;
	}

	
	public void setSoftware_lic(String software_lic)
	{
		this.software_lic = software_lic;
	}

	
	public String getStaff_name()
	{
		return staff_name;
	}

	
	public void setStaff_name(String staff_name)
	{
		this.staff_name = staff_name;
	}

	
	public String getIncome_cat()
	{
		return income_cat;
	}

	
	public void setIncome_cat(String income_cat)
	{
		this.income_cat = income_cat;
	}

	
	public String getIncome_name()
	{
		return income_name;
	}

	
	public void setIncome_name(String income_name)
	{
		this.income_name = income_name;
	}

	
	public String getOrg_type()
	{
		return org_type;
	}

	
	public void setOrg_type(String org_type)
	{
		this.org_type = org_type;
	}

	
	public String getTarget_pax()
	{
		return target_pax;
	}

	
	public void setTarget_pax(String target_pax)
	{
		this.target_pax = target_pax;
	}

	
	public String getNum_presentation()
	{
		return num_presentation;
	}

	
	public void setNum_presentation(String num_presentation)
	{
		this.num_presentation = num_presentation;
	}

	
	public String getNum_pax()
	{
		return num_pax;
	}

	
	public void setNum_pax(String num_pax)
	{
		this.num_pax = num_pax;
	}

	
	public String getCond_staff()
	{
		return cond_staff;
	}

	
	public void setCond_staff(String cond_staff)
	{
		this.cond_staff = cond_staff;
	}

	
	public String getCheck_engage()
	{
		return check_engage;
	}

	
	public void setCheck_engage(String check_engage)
	{
		this.check_engage = check_engage;
	}

	
	public String getNum_leader()
	{
		return num_leader;
	}

	
	public void setNum_leader(String num_leader)
	{
		this.num_leader = num_leader;
	}

	
	public String getNum_other()
	{
		return num_other;
	}

	
	public void setNum_other(String num_other)
	{
		this.num_other = num_other;
	}

	
	public String getNum_pax_ben()
	{
		return num_pax_ben;
	}

	
	public void setNum_pax_ben(String num_pax_ben)
	{
		this.num_pax_ben = num_pax_ben;
	}

	
	public String getCheck_pax()
	{
		return check_pax;
	}

	
	public void setCheck_pax(String check_pax)
	{
		this.check_pax = check_pax;
	}

	
	public String getEvent_type()
	{
		return event_type;
	}

	
	public void setEvent_type(String event_type)
	{
		this.event_type = event_type;
	}

	
	public String getPerform()
	{
		return perform;
	}

	
	public void setPerform(String perform)
	{
		this.perform = perform;
	}

	
	public String getNum_attendee()
	{
		return num_attendee;
	}

	
	public void setNum_attendee(String num_attendee)
	{
		this.num_attendee = num_attendee;
	}

	
	
	public String getExt_body_name()
	{
		return ext_body_name;
	}

	
	public void setExt_body_name(String ext_body_name)
	{
		this.ext_body_name = ext_body_name;
	}

	public String getCountry()
	{
		return country;
	}

	
	public void setCountry(String country)
	{
		this.country = country;
	}

	
	public String getExt_body_nature()
	{
		return ext_body_nature;
	}

	
	public void setExt_body_nature(String ext_body_nature)
	{
		this.ext_body_nature = ext_body_nature;
	}

	
	public String getPost_engaged()
	{
		return post_engaged;
	}

	
	public void setPost_engaged(String post_engaged)
	{
		this.post_engaged = post_engaged;
	}

	
	public String getAct_cat()
	{
		return act_cat;
	}

	
	public void setAct_cat(String act_cat)
	{
		this.act_cat = act_cat;
	}

	
	public String getNum_subsessions()
	{
		return num_subsessions;
	}

	
	public void setNum_subsessions(String num_subsessions)
	{
		this.num_subsessions = num_subsessions;
	}

	
	public String getPi()
	{
		return pi;
	}

	
	public void setPi(String pi)
	{
		this.pi = pi;
	}

	
	public String getNum_speakers()
	{
		return num_speakers;
	}

	
	public void setNum_speakers(String num_speakers)
	{
		this.num_speakers = num_speakers;
	}

	public static String removeTailChar(String inString, char inChar)
	{
		String outString = (inString != null) ? inString.trim() : "";

		if (outString.charAt(outString.length() - 1) == inChar)
		{
			outString = outString.substring(0, outString.length() - 1);
		}

		return outString;
	}


	// remove all html tags from a string
	public static String removeHTML(String htmlString)
	{
		// Remove HTML tag from java String
		String noHTMLString = htmlString.replaceAll("\\<.*?\\>", "");

		// Remove Carriage return from java String
		noHTMLString = noHTMLString.replaceAll("<br/>", " ");

		// Remove New line from java string and replace html break
		noHTMLString = noHTMLString.replaceAll("\n", " ");
		noHTMLString = noHTMLString.replaceAll("\r", " ");

		// noHTMLString = noHTMLString.replaceAll("\'", "&#39;");
		// noHTMLString = noHTMLString.replaceAll("\"", "&quot;");
		return noHTMLString;
	}


	public static String getMonth(int month)
	{
		return new DateFormatSymbols(Locale.US).getMonths()[month - 1];
	}


	private String escape(String raw)
	{
		String escaped = raw;
		escaped = escaped.replace("\\", "\\\\");
		escaped = escaped.replace("\"", "\\\"");
		escaped = escaped.replace("\b", "\\b");
		escaped = escaped.replace("\f", "\\f");
		escaped = escaped.replace("\n", "\\n");
		escaped = escaped.replace("\r", "\\r");
		escaped = escaped.replace("\t", "\\t");
		// TODO: escape other non-printing characters using uXXXX notation
		return escaped;
	}
}