package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_CONSULT_HDR")
public class KtFormCons_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_CONSULT";
	
	public static Logger logger = Logger.getLogger(KtFormCons_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "act_code")
	private String act_code;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "principal_inves")
	private String principal_inves;
	
	@Column(name = "fund_src")
	private String fund_src;
	
	@Column(name = "fund_src_type")
	private String fund_src_type;
	
	@Column(name = "fund_src_org")
	private String fund_src_org;
	
	@Column(name = "eduhk_role")
	private String eduhk_role;
	
	@Column(name = "budget")
	private Double  budget;
	
	@Column(name = "income_rpt_unit")
	private Double  income_rpt_unit;
	
	@Column(name = "cumu_income_fo")
	private Double  cumu_income_fo;
	
	@Column(name = "income_fo")
	private Double  income_fo;
	
	@Column(name = "income_fo_rem")
	private String  income_fo_rem;
	
	@Column(name = "income_rdo")
	private Double  income_rdo;
	
	@Column(name = "income_grant")
	private String  income_grant;
	
	@Column(name = "num_key_partner")
	private Integer  num_key_partner;
	
	@Column(name = "num_teacher")
	private Integer  num_teacher;
	
	@Column(name = "num_principal")
	private Integer  num_principal;
	
	@Column(name = "num_stakeholder")
	private Integer  num_stakeholder;

	@Column(name = "num_school")
	private Integer  num_school;
	
	@Column(name = "num_stu")
	private Integer  num_stu;
	
	@Column(name = "num_org")
	private Integer  num_org;
	
	@Column(name = "num_adv_body")
	private Integer  num_adv_body;
	
	@Column(name = "num_ext_prof")
	private Integer  num_ext_prof;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;

	@Column(name = "research_element")
	private String  research_element;
	
	@Column(name = "edu_crse")
	private String  edu_crse;
	
	@Column(name = "hk_fund")
	private String  hk_fund;
	
	@Column(name = "hk_gov_fund")
	private String  hk_gov_fund;
	
	@Column(name = "hk_pri_fund")
	private String  hk_pri_fund;
	
	@Column(name = "joint_proj")
	private String  joint_proj;
	
	@Column(name = "name_coor")
	private String  name_coor;
	
	@Column(name = "fund_src_coor")
	private String  fund_src_coor;
	
	@Column(name = "proj_title_coor")
	private String  proj_title_coor;
	
	@Column(name = "pi_coor")
	private String  pi_coor;
	
	@Column(name = "support_fo")
	private String  support_fo;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	@Transient
	private String hk_fundValue = null;
	
	@Transient
	private String fundSrcTypeValue = null;
	
	@Transient
	private String fundSrcOrgValue = null;
	
	@Transient
	private String eduhkRoleValue = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public String getAct_code()
	{
		return act_code;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public String getDept()
	{
		return dept;
	}

	
	public Date getStart_date()
	{
		return start_date;
	}

	
	public Date getEnd_date()
	{
		return end_date;
	}

	
	public String getPrincipal_inves()
	{
		return principal_inves;
	}

	
	public String getFund_src()
	{
		return fund_src;
	}

	
	public String getFund_src_type()
	{
		return fund_src_type;
	}

	
	public String getFund_src_org()
	{
		return fund_src_org;
	}

	
	public String getEduhk_role()
	{
		return eduhk_role;
	}

	
	public Double getBudget()
	{
		return budget;
	}

	
	public Double getIncome_rpt_unit()
	{
		return income_rpt_unit;
	}

	
	public Double getCumu_income_fo()
	{
		return cumu_income_fo;
	}

	
	public Double getIncome_fo()
	{
		return income_fo;
	}

	
	public String getIncome_fo_rem()
	{
		return income_fo_rem;
	}

	
	public Double getIncome_rdo()
	{
		return income_rdo;
	}

	
	public String getIncome_grant()
	{
		return income_grant;
	}

	
	public Integer getNum_key_partner()
	{
		return num_key_partner;
	}

	
	public Integer getNum_teacher()
	{
		return num_teacher;
	}

	
	public Integer getNum_principal()
	{
		return num_principal;
	}

	
	public Integer getNum_stakeholder()
	{
		return num_stakeholder;
	}

	
	public Integer getNum_school()
	{
		return num_school;
	}

	
	public Integer getNum_stu()
	{
		return num_stu;
	}

	
	public Integer getNum_org()
	{
		return num_org;
	}

	
	public Integer getNum_adv_body()
	{
		return num_adv_body;
	}

	
	public Integer getNum_ext_prof()
	{
		return num_ext_prof;
	}

	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}

	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}

	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}

	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public void setDept(String dept)
	{
		this.dept = dept;
	}

	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}

	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}

	
	public void setPrincipal_inves(String principal_inves)
	{
		this.principal_inves = principal_inves;
	}

	
	public void setFund_src(String fund_src)
	{
		this.fund_src = fund_src;
	}

	
	public void setFund_src_type(String fund_src_type)
	{
		this.fund_src_type = fund_src_type;
	}

	
	public void setFund_src_org(String fund_src_org)
	{
		this.fund_src_org = fund_src_org;
	}

	
	public void setEduhk_role(String eduhk_role)
	{
		this.eduhk_role = eduhk_role;
	}

	
	public void setBudget(Double budget)
	{
		this.budget = budget;
	}

	
	public void setIncome_rpt_unit(Double income_rpt_unit)
	{
		this.income_rpt_unit = income_rpt_unit;
	}

	
	public void setCumu_income_fo(Double cumu_income_fo)
	{
		this.cumu_income_fo = cumu_income_fo;
	}

	
	public void setIncome_fo(Double income_fo)
	{
		this.income_fo = income_fo;
	}

	
	public void setIncome_fo_rem(String income_fo_rem)
	{
		this.income_fo_rem = income_fo_rem;
	}

	
	public void setIncome_rdo(Double income_rdo)
	{
		this.income_rdo = income_rdo;
	}

	
	public void setIncome_grant(String income_grant)
	{
		this.income_grant = income_grant;
	}

	
	public void setNum_key_partner(Integer num_key_partner)
	{
		this.num_key_partner = num_key_partner;
	}

	
	public void setNum_teacher(Integer num_teacher)
	{
		this.num_teacher = num_teacher;
	}

	
	public void setNum_principal(Integer num_principal)
	{
		this.num_principal = num_principal;
	}

	
	public void setNum_stakeholder(Integer num_stakeholder)
	{
		this.num_stakeholder = num_stakeholder;
	}

	
	public void setNum_school(Integer num_school)
	{
		this.num_school = num_school;
	}

	
	public void setNum_stu(Integer num_stu)
	{
		this.num_stu = num_stu;
	}

	
	public void setNum_org(Integer num_org)
	{
		this.num_org = num_org;
	}

	
	public void setNum_adv_body(Integer num_adv_body)
	{
		this.num_adv_body = num_adv_body;
	}

	
	public void setNum_ext_prof(Integer num_ext_prof)
	{
		this.num_ext_prof = num_ext_prof;
	}

	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}

	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}

	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}
	
	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}
	
	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}
	
	
	
	public String getHk_fundValue()
	{
		if(hk_fundValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getHk_fund()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_CONS_HK_FUND", getHk_fund(), "US");
				
				if(obj!=null) hk_fundValue = obj.getDescription();
			}
		}
		return hk_fundValue;
	}


	
	public void setHk_fundValue(String hk_fundValue)
	{
		this.hk_fundValue = hk_fundValue;
	}


	public void setFundSrcTypeValue(String fundSrcTypeValue)
	{
		this.fundSrcTypeValue = fundSrcTypeValue;
	}


	
	public void setFundSrcOrgValue(String fundSrcOrgValue)
	{
		this.fundSrcOrgValue = fundSrcOrgValue;
	}


	
	public void setEduhkRoleValue(String eduhkRoleValue)
	{
		this.eduhkRoleValue = eduhkRoleValue;
	}


	public String getFundSrcTypeValue()
	{
		if(fundSrcTypeValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getFund_src_type()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_CONS_FUND_SRC", getFund_src_type(), "US");
				
				if(obj!=null) fundSrcTypeValue = obj.getDescription();
			}
		}
		return fundSrcTypeValue;
	}



	public String getFundSrcOrgValue()
	{
		if(fundSrcOrgValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getFund_src_org()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_CONS_ORG_TYPE", getFund_src_org(), "US");
				
				if(obj!=null) fundSrcOrgValue = obj.getDescription();
			}
		}
		
		return fundSrcOrgValue;
	}


	
	public String getEduhkRoleValue()
	{
		if(eduhkRoleValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getEduhk_role()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_CONS_ROLE", getEduhk_role(), "US");
				
				if(obj!=null) eduhkRoleValue = obj.getDescription();
			}
		}
		
		return eduhkRoleValue;
	}


	
	public String getResearch_element()
	{
		return research_element;
	}


	
	public void setResearch_element(String research_element)
	{
		this.research_element = research_element;
	}


	
	public String getEdu_crse()
	{
		return edu_crse;
	}


	
	public void setEdu_crse(String edu_crse)
	{
		this.edu_crse = edu_crse;
	}


	
	public String getHk_fund()
	{
		return hk_fund;
	}


	
	public void setHk_fund(String hk_fund)
	{
		this.hk_fund = hk_fund;
	}


	
	public String getHk_gov_fund()
	{
		return hk_gov_fund;
	}


	
	public void setHk_gov_fund(String hk_gov_fund)
	{
		this.hk_gov_fund = hk_gov_fund;
	}


	
	public String getHk_pri_fund()
	{
		return hk_pri_fund;
	}


	
	public void setHk_pri_fund(String hk_pri_fund)
	{
		this.hk_pri_fund = hk_pri_fund;
	}


	
	public String getJoint_proj()
	{
		return joint_proj;
	}


	
	public void setJoint_proj(String joint_proj)
	{
		this.joint_proj = joint_proj;
	}


	
	public String getName_coor()
	{
		return name_coor;
	}


	
	public void setName_coor(String name_coor)
	{
		this.name_coor = name_coor;
	}


	
	public String getFund_src_coor()
	{
		return fund_src_coor;
	}


	
	public void setFund_src_coor(String fund_src_coor)
	{
		this.fund_src_coor = fund_src_coor;
	}


	
	public String getProj_title_coor()
	{
		return proj_title_coor;
	}


	
	public void setProj_title_coor(String proj_title_coor)
	{
		this.proj_title_coor = proj_title_coor;
	}


	
	public String getPi_coor()
	{
		return pi_coor;
	}


	
	public void setPi_coor(String pi_coor)
	{
		this.pi_coor = pi_coor;
	}


	
	public String getSupport_fo()
	{
		return support_fo;
	}


	
	public void setSupport_fo(String support_fo)
	{
		this.support_fo = support_fo;
	}


	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}


	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}


	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}


	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormCons_P other = (KtFormCons_P) obj;
		return Objects.equals(pk, other.pk);
	}


	@Override
	public String toString()
	{
		return "KtFormCons_P [pk=" + pk + ", title=" + title + ", act_code=" + act_code + ", fac=" + fac + ", dept="
				+ dept + ", start_date=" + start_date + ", end_date=" + end_date + ", principal_inves="
				+ principal_inves + ", fund_src=" + fund_src + ", fund_src_type=" + fund_src_type + ", fund_src_org="
				+ fund_src_org + ", eduhk_role=" + eduhk_role + ", budget=" + budget + ", income_rpt_unit="
				+ income_rpt_unit + ", cumu_income_fo=" + cumu_income_fo + ", income_fo=" + income_fo
				+ ", income_fo_rem=" + income_fo_rem + ", income_rdo=" + income_rdo + ", income_grant=" + income_grant
				+ ", num_key_partner=" + num_key_partner + ", num_teacher=" + num_teacher + ", num_principal="
				+ num_principal + ", num_stakeholder=" + num_stakeholder + ", num_school=" + num_school + ", num_stu="
				+ num_stu + ", num_org=" + num_org + ", num_adv_body=" + num_adv_body + ", num_ext_prof=" + num_ext_prof
				+ ", remarks_staff=" + remarks_staff + ", remarks_dept=" + remarks_dept + ", remarks_kt=" + remarks_kt
				+ ", research_element=" + research_element + ", edu_crse=" + edu_crse + ", hk_fund=" + hk_fund
				+ ", hk_gov_fund=" + hk_gov_fund + ", hk_pri_fund=" + hk_pri_fund + ", joint_proj=" + joint_proj
				+ ", name_coor=" + name_coor + ", fund_src_coor=" + fund_src_coor + ", proj_title_coor="
				+ proj_title_coor + ", pi_coor=" + pi_coor + ", support_fo=" + support_fo + ", hk_fundValue="
				+ hk_fundValue + ", fundSrcTypeValue=" + fundSrcTypeValue + ", fundSrcOrgValue=" + fundSrcOrgValue
				+ ", eduhkRoleValue=" + eduhkRoleValue + "]";
	}



}
