package hk.eduhk.rich.entity.form;

import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.publication.PublicationDAO;

@Entity
@Table(name = "RH_KT_FORM_SUMMARY_V" )
public class KtFormSummary
{
	public static Logger logger = Logger.getLogger(KtFormSummary.class.toString());
	
	@EmbeddedId
	private KtFormSummaryPK pk = new KtFormSummaryPK();
	
	@Column(name = "form_code")
	private String formCode;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "kt_desc")
	private String desc;
	
	@Column(name = "start_date")
	private Date startDate;
	
	@Column(name = "end_date")
	private Date endDate;
	
	@Column(name = "staff_no")
	private String staffNo;
	
	@Column(name = "pid")
	private Integer pid;
	
	@Column(name = "display_ind")
	private String display_ind;

	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "form_code", referencedColumnName = "form_code", insertable = false, updatable = false)
	private KtForm ktForm = null;
	
	public KtFormSummaryPK getPk()
	{
		return pk;
	}

	
	public void setPk(KtFormSummaryPK pk)
	{
		this.pk = pk;
	}

	
	public String getFormCode()
	{
		return formCode;
	}

	
	public void setFormCode(String formCode)
	{
		this.formCode = formCode;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public String getDept()
	{
		return dept;
	}

	
	public void setDept(String dept)
	{
		this.dept = dept;
	}

	
	
	public String getTitle()
	{
		return title;
	}


	
	public void setTitle(String title)
	{
		this.title = title;
	}


	
	public String getDesc()
	{
		return desc;
	}


	
	public void setDesc(String desc)
	{
		this.desc = desc;
	}


	
	public Date getStartDate()
	{
		return startDate;
	}


	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}


	
	public Date getEndDate()
	{
		return endDate;
	}


	
	public void setEndDate(Date endDate)
	{
		this.endDate = endDate;
	}


	public String getStaffNo()
	{
		return staffNo;
	}

	
	public void setStaffNo(String staffNo)
	{
		this.staffNo = staffNo;
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}
	

	
	
	public String getDisplay_ind()
	{
		return display_ind;
	}


	
	public void setDisplay_ind(String display_ind)
	{
		this.display_ind = display_ind;
	}


	public KtForm getKtForm()
	{
		if (ktForm != null) {
			try {
				ktForm.getForm_code();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					ktForm = dao.getKtForm(getFormCode());
				}
				else
				{
					throw re;
				}
			}
		}
		return ktForm;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormSummary other = (KtFormSummary) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormSummary [pk=" + pk + ", formCode=" + formCode + ", fac=" + fac + ", dept=" + dept + ", title="
				+ title + ", desc=" + desc + ", startDate=" + startDate + ", endDate=" + endDate + ", staffNo="
				+ staffNo + ", pid=" + pid + ", display_ind=" + display_ind + ", ktForm=" + ktForm + "]";
	}




}
