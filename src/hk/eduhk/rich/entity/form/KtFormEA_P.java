package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_EA_HDR")
public class KtFormEA_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_EA";
	
	public static Logger logger = Logger.getLogger(KtFormEA_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "act_code")
	private String act_code;
	
	@Column(name = "act_cat")
	private String act_cat;
	
	@Column(name = "act_type")
	private String act_type;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "free_charge")
	private String free_charge;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "fund_src")
	private String fund_src;
	
	@Column(name = "organizer")
	private String organizer;

	@Column(name = "region")
	private String region;
	
	@Column(name = "ct_person")
	private String ct_person;
	
	@Column(name = "num_stu_ug")
	private Integer  num_stu_ug;
	
	@Column(name = "num_stu_pg")
	private Integer  num_stu_pg;
	
	@Column(name = "num_staff")
	private Integer  num_staff;
	
	@Column(name = "num_alumni")
	private Integer  num_alumni;
	
	@Column(name = "num_other")
	private Integer  num_other;
	
	@Column(name = "num_pax")
	private Integer  num_pax;
	
	@Column(name = "num_key_partner")
	private Integer  num_key_partner;
	
	@Column(name = "num_ext_prof")
	private Integer  num_ext_prof;

	@Column(name = "credit_bearing")
	private String credit_bearing;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;
	
	@Column(name = "ent_element")
	private String  ent_element;
	
	@Column(name = "team_name")
	private String  team_name;
	
	@Column(name = "museum")
	private String  museum;
	
	@Column(name = "eduhk_org")
	private String  eduhk_org;
	
	@Column(name = "act_mode")
	private String  act_mode;
	
	@Column(name = "budget_holder")
	private String  budget_holder;
	
	@Column(name = "budget_approval")
	private String  budget_approval;
	
	@Column(name = "budget")
	private Double  budget;
	
	@Column(name = "income_rpt_unit")
	private Double  income_rpt_unit;
	
	@Column(name = "income_fo")
	private Double  income_fo;
	
	@Column(name = "income_fo_rem")
	private String  income_fo_rem;
	
	@Column(name = "income_rdo")
	private Double  income_rdo;
	
	@Column(name = "expnd_rpt_unit")
	private Double  expnd_rpt_unit;
	
	@Column(name = "expnd_fo")
	private Double  expnd_fo;
	
	@Column(name = "expnd_fo_rem")
	private String  expnd_fo_rem;
	
	@Column(name = "expnd_rdo")
	private Double  expnd_rdo;
	
	@Column(name = "target_pax")
	private String  target_pax;
	
	@Column(name = "num_teacher")
	private Integer  num_teacher;
	
	@Column(name = "num_principal")
	private Integer  num_principal;
	
	@Column(name = "num_school")
	private Integer  num_school;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	@Column(name = "num_subsessions")
	private Integer  num_subsessions;
	
	@Column(name = "pi")
	private String  pi;
	
	@Column(name = "num_speakers")
	private Integer  num_speakers;
	
	@Transient
	private String[] act_type_array = null;
	
	@Transient
	private String actTypeValue = null;
	
	@Transient
	private String actCatValue = null;
	
	@Transient
	private String regionValue = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}


	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}



	
	public String getTitle()
	{
		return title;
	}



	
	public void setTitle(String title)
	{
		this.title = title;
	}



	
	public String getAct_code()
	{
		return act_code;
	}



	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}



	public String getAct_cat()
	{
		return act_cat;
	}



	
	public void setAct_cat(String act_cat)
	{
		this.act_cat = act_cat;
	}



	public String getAct_type()
	{
		return act_type;
	}



	
	public void setAct_type(String act_type)
	{
		this.act_type = act_type;
	}



	
	public String getFac()
	{
		return fac;
	}



	
	public void setFac(String fac)
	{
		this.fac = fac;
	}



	
	public String getDept()
	{
		return dept;
	}



	
	public void setDept(String dept)
	{
		this.dept = dept;
	}



	
	
	public String getFree_charge()
	{
		return free_charge;
	}



	
	public void setFree_charge(String free_charge)
	{
		this.free_charge = free_charge;
	}



	public Date getStart_date()
	{
		return start_date;
	}



	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}



	
	public Date getEnd_date()
	{
		return end_date;
	}



	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}



	
	
	public String getFund_src()
	{
		return fund_src;
	}



	
	public void setFund_src(String fund_src)
	{
		this.fund_src = fund_src;
	}



	public String getOrganizer()
	{
		return organizer;
	}



	
	public void setOrganizer(String organizer)
	{
		this.organizer = organizer;
	}



	
	public String getRegion()
	{
		return region;
	}



	
	public void setRegion(String region)
	{
		this.region = region;
	}



	
	public String getCt_person()
	{
		return ct_person;
	}



	
	public void setCt_person(String ct_person)
	{
		this.ct_person = ct_person;
	}



	
	public Integer getNum_stu_ug()
	{
		return num_stu_ug;
	}



	
	public void setNum_stu_ug(Integer num_stu_ug)
	{
		this.num_stu_ug = num_stu_ug;
	}



	
	public Integer getNum_stu_pg()
	{
		return num_stu_pg;
	}



	
	public void setNum_stu_pg(Integer num_stu_pg)
	{
		this.num_stu_pg = num_stu_pg;
	}



	
	public Integer getNum_staff()
	{
		return num_staff;
	}



	
	public void setNum_staff(Integer num_staff)
	{
		this.num_staff = num_staff;
	}



	
	public Integer getNum_alumni()
	{
		return num_alumni;
	}



	
	public void setNum_alumni(Integer num_alumni)
	{
		this.num_alumni = num_alumni;
	}


	public Integer getNum_other()
	{
		return num_other;
	}



	
	public void setNum_other(Integer num_other)
	{
		this.num_other = num_other;
	}



	
	public Integer getNum_pax()
	{
		return num_pax;
	}



	
	public void setNum_pax(Integer num_pax)
	{
		this.num_pax = num_pax;
	}



	
	public Integer getNum_key_partner()
	{
		return num_key_partner;
	}



	
	public void setNum_key_partner(Integer num_key_partner)
	{
		this.num_key_partner = num_key_partner;
	}



	
	public Integer getNum_ext_prof()
	{
		return num_ext_prof;
	}



	
	public void setNum_ext_prof(Integer num_ext_prof)
	{
		this.num_ext_prof = num_ext_prof;
	}



	public String getCredit_bearing()
	{
		return credit_bearing;
	}



	
	public void setCredit_bearing(String credit_bearing)
	{
		this.credit_bearing = credit_bearing;
	}



	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}



	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}



	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}



	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}



	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}



	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}


	
	public String getEnt_element()
	{
		return ent_element;
	}



	
	public void setEnt_element(String ent_element)
	{
		this.ent_element = ent_element;
	}



	
	public String getTeam_name()
	{
		return team_name;
	}



	
	public void setTeam_name(String team_name)
	{
		this.team_name = team_name;
	}



	
	public String getMuseum()
	{
		return museum;
	}



	
	public void setMuseum(String museum)
	{
		this.museum = museum;
	}



	
	public String getEduhk_org()
	{
		return eduhk_org;
	}



	
	public void setEduhk_org(String eduhk_org)
	{
		this.eduhk_org = eduhk_org;
	}

	
	public String getAct_mode()
	{
		return act_mode;
	}



	
	public void setAct_mode(String act_mode)
	{
		this.act_mode = act_mode;
	}



	public String getBudget_holder()
	{
		return budget_holder;
	}



	
	public void setBudget_holder(String budget_holder)
	{
		this.budget_holder = budget_holder;
	}



	
	public String getBudget_approval()
	{
		return budget_approval;
	}



	
	public void setBudget_approval(String budget_approval)
	{
		this.budget_approval = budget_approval;
	}


	
	
	public Double getBudget()
	{
		return budget;
	}



	
	public void setBudget(Double budget)
	{
		this.budget = budget;
	}



	public Double getIncome_fo()
	{
		return income_fo;
	}



	
	public void setIncome_fo(Double income_fo)
	{
		this.income_fo = income_fo;
	}



	
	public String getIncome_fo_rem()
	{
		return income_fo_rem;
	}



	
	public void setIncome_fo_rem(String income_fo_rem)
	{
		this.income_fo_rem = income_fo_rem;
	}



	
	public Double getIncome_rdo()
	{
		return income_rdo;
	}



	
	public void setIncome_rdo(Double income_rdo)
	{
		this.income_rdo = income_rdo;
	}



	
	public Double getExpnd_rpt_unit()
	{
		return expnd_rpt_unit;
	}



	
	public void setExpnd_rpt_unit(Double expnd_rpt_unit)
	{
		this.expnd_rpt_unit = expnd_rpt_unit;
	}



	
	public Double getExpnd_fo()
	{
		return expnd_fo;
	}



	
	public void setExpnd_fo(Double expnd_fo)
	{
		this.expnd_fo = expnd_fo;
	}



	
	public String getExpnd_fo_rem()
	{
		return expnd_fo_rem;
	}



	
	public void setExpnd_fo_rem(String expnd_fo_rem)
	{
		this.expnd_fo_rem = expnd_fo_rem;
	}



	
	public Double getExpnd_rdo()
	{
		return expnd_rdo;
	}



	
	public void setExpnd_rdo(Double expnd_rdo)
	{
		this.expnd_rdo = expnd_rdo;
	}



	
	public String getTarget_pax()
	{
		return target_pax;
	}



	
	public void setTarget_pax(String target_pax)
	{
		this.target_pax = target_pax;
	}



	
	public Integer getNum_teacher()
	{
		return num_teacher;
	}



	
	public void setNum_teacher(Integer num_teacher)
	{
		this.num_teacher = num_teacher;
	}



	
	public Integer getNum_principal()
	{
		return num_principal;
	}



	
	public void setNum_principal(Integer num_principal)
	{
		this.num_principal = num_principal;
	}



	
	public Integer getNum_school()
	{
		return num_school;
	}



	
	public void setNum_school(Integer num_school)
	{
		this.num_school = num_school;
	}


	public Double getIncome_rpt_unit()
	{
		return income_rpt_unit;
	}



	
	public void setIncome_rpt_unit(Double income_rpt_unit)
	{
		this.income_rpt_unit = income_rpt_unit;
	}



	public String getActTypeValue()
	{
		if(actTypeValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getAct_type()))
			{
				String[] actTypes = act_type.split(",");
				List<String> actTypeList = Arrays.asList(actTypes);
				
				for(String actTypeCode : actTypeList)
				{
					LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_EA_ACT_TYPE", actTypeCode, "US");
					
					if(obj!=null) 
					{
						if(actTypeValue == null) 
							actTypeValue = obj.getDescription();
						else
							actTypeValue = actTypeValue+","+obj.getDescription();
					}
				}
				
			}
		}
		return actTypeValue;
	}


	public void setActTypeValue(String actTypeValue)
	{
		this.actTypeValue = actTypeValue;
	}
	
	
	
	public String getActCatValue()
	{
		if(actCatValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getAct_cat()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_EA_ACT_CAT", getAct_cat(), "US");
				
				if(obj!=null) actCatValue = obj.getDescription();
			}
		}
		return actCatValue;
	}



	
	public void setActCatValue(String actCatValue)
	{
		this.actCatValue = actCatValue;
	}



	public String[] getAct_type_array()
	{
		if (act_type_array == null) {
			if(!GenericValidator.isBlankOrNull(getAct_type()))
			{
				act_type = act_type.replace(" ", "");
				act_type_array = act_type.split(",");
			}else {
				act_type_array = new String[0];
			}
		}
		return act_type_array;
	}


	public void setAct_type_array(String[] act_type_array)
	{
		this.act_type_array = act_type_array;
		if (act_type_array != null) {
			act_type = "";
			for (int i = 0; i < act_type_array.length; i++) {
				act_type += act_type_array[i];
				if (i < act_type_array.length-1) {
					act_type += ",";
				}
			}
		}
	}



	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}

	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}

	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}



	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}



	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}



	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}

	
	public Integer getNum_subsessions()
	{
		return num_subsessions;
	}



	
	public void setNum_subsessions(Integer num_subsessions)
	{
		this.num_subsessions = num_subsessions;
	}



	
	public String getPi()
	{
		return pi;
	}



	
	public void setPi(String pi)
	{
		this.pi = pi;
	}



	
	public Integer getNum_speakers()
	{
		return num_speakers;
	}



	
	public void setNum_speakers(Integer num_speakers)
	{
		this.num_speakers = num_speakers;
	}



	public String getRegionValue()
	{
		if(regionValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getRegion()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_REGION", getRegion(), "US");
				
				if(obj!=null) regionValue = obj.getDescription();
			}
		}
		return regionValue;
	}


	
	public void setRegionValue(String regionValue)
	{
		this.regionValue = regionValue;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormEA_P other = (KtFormEA_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "KtFormEA_P [pk=" + pk + ", title=" + title + ", act_code=" + act_code + ", act_cat=" + act_cat
				+ ", act_type=" + act_type + ", fac=" + fac + ", dept=" + dept + ", free_charge=" + free_charge
				+ ", start_date=" + start_date + ", end_date=" + end_date + ", fund_src=" + fund_src + ", organizer="
				+ organizer + ", region=" + region + ", ct_person=" + ct_person + ", num_stu_ug=" + num_stu_ug
				+ ", num_stu_pg=" + num_stu_pg + ", num_staff=" + num_staff + ", num_alumni=" + num_alumni
				+ ", num_other=" + num_other + ", num_pax=" + num_pax + ", num_key_partner=" + num_key_partner
				+ ", num_ext_prof=" + num_ext_prof + ", credit_bearing=" + credit_bearing + ", remarks_staff="
				+ remarks_staff + ", remarks_dept=" + remarks_dept + ", remarks_kt=" + remarks_kt + ", ent_element="
				+ ent_element + ", team_name=" + team_name + ", museum=" + museum + ", eduhk_org=" + eduhk_org
				+ ", act_mode=" + act_mode + ", budget_holder=" + budget_holder + ", budget_approval=" + budget_approval
				+ ", budget=" + budget + ", income_rpt_unit=" + income_rpt_unit + ", income_fo=" + income_fo
				+ ", income_fo_rem=" + income_fo_rem + ", income_rdo=" + income_rdo + ", expnd_rpt_unit="
				+ expnd_rpt_unit + ", expnd_fo=" + expnd_fo + ", expnd_fo_rem=" + expnd_fo_rem + ", expnd_rdo="
				+ expnd_rdo + ", target_pax=" + target_pax + ", num_teacher=" + num_teacher + ", num_principal="
				+ num_principal + ", num_school=" + num_school + ", num_proj_day=" + num_proj_day
				+ ", num_proj_day_in_yr=" + num_proj_day_in_yr + ", num_subsessions=" + num_subsessions + ", pi=" + pi
				+ ", num_speakers=" + num_speakers + ", act_type_array=" + Arrays.toString(act_type_array)
				+ ", actTypeValue=" + actTypeValue + ", actCatValue=" + actCatValue + ", regionValue=" + regionValue
				+ "]";
	}


}
