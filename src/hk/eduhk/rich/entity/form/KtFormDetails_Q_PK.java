package hk.eduhk.rich.entity.form;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class KtFormDetails_Q_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="form_no")
	private Integer form_no;
	
	@Column(name="staff_no")
	private String staff_no;


	
	public Integer getForm_no()
	{
		return form_no;
	}


	
	public void setForm_no(Integer form_no)
	{
		this.form_no = form_no;
	}


	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((form_no == null) ? 0 : form_no.hashCode());
		result = prime * result + ((staff_no == null) ? 0 : staff_no.hashCode());
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormDetails_Q_PK other = (KtFormDetails_Q_PK) obj;
		if (form_no == null)
		{
			if (other.form_no != null)
				return false;
		}
		else if (!form_no.equals(other.form_no))
			return false;
		if (staff_no == null)
		{
			if (other.staff_no != null)
				return false;
		}
		else if (!staff_no.equals(other.staff_no))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "KtFormDetails_Q_PK [form_no=" + form_no + ", staff_no=" + staff_no + "]";
	}


}
