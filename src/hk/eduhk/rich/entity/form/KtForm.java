package hk.eduhk.rich.entity.form;

import java.util.logging.Logger;
import java.util.List;
import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.form.*;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptSum;

@SuppressWarnings("serial")
@Entity
@Table(name = "RH_Z_KT_FORM")
public class KtForm extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(KtForm.class.toString());
	
	@Id
	@Column(name = "form_code")
	private String form_code;
	
	@Column(name = "form_short_desc")
	private String form_short_desc;
	
	@Column(name = "form_full_desc")
	private String form_full_desc;
	
	@Column(name = "form_brief")
	private String form_brief;
	
	@Column(name = "note_1")
	private String note_1;
	
	@Column(name = "note_2")
	private String note_2;

	@Column(name = "display_order")
	private Integer  display_order;
	
	@Column(name = "is_enabled")
	private Boolean is_enabled = false;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "form", cascade = {CascadeType.ALL})
	private List<KtRptSum> sum;
	
	@Transient
	private Integer countTotal = 0;
	
	@Transient
	private Integer countWaitConsent = 0;
	
	public String getForm_code()
	{
		return form_code;
	}

	
	public void setForm_code(String form_code)
	{
		this.form_code = form_code;
	}

	
	public String getForm_short_desc()
	{
		return form_short_desc;
	}

	
	public void setForm_short_desc(String form_short_desc)
	{
		this.form_short_desc = form_short_desc;
	}

	
	public String getForm_full_desc()
	{
		return form_full_desc;
	}

	
	public void setForm_full_desc(String form_full_desc)
	{
		this.form_full_desc = form_full_desc;
	}

	
	
	public String getForm_brief()
	{
		return form_brief;
	}


	
	public void setForm_brief(String form_brief)
	{
		this.form_brief = form_brief;
	}


	public String getNote_1()
	{
		return note_1;
	}

	
	public void setNote_1(String note_1)
	{
		this.note_1 = note_1;
	}

	
	public String getNote_2()
	{
		return note_2;
	}

	
	public void setNote_2(String note_2)
	{
		this.note_2 = note_2;
	}

	
	public Integer getDisplay_order()
	{
		return display_order;
	}

	
	public void setDisplay_order(Integer display_order)
	{
		this.display_order = display_order;
	}


	
	public Boolean getIs_enabled()
	{
		return is_enabled;
	}


	
	public void setIs_enabled(Boolean is_enabled)
	{
		this.is_enabled = is_enabled;
	}


	
	public List<KtRptSum> getSum()
	{
		if (sum != null)
		{
			try
			{
				sum.size();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					KtRptDAO dao = KtRptDAO.getInstance();
					sum = dao.getKtRptSumList(getForm_code());
				}
				else
				{
					throw re;
				}
			}
		}
		return sum;
	}


	
	public void setSum(List<KtRptSum> sum)
	{
		this.sum = sum;
	}


	
	public Integer getCountTotal()
	{
		return countTotal;
	}


	
	public void setCountTotal(Integer countTotal)
	{
		this.countTotal = countTotal;
	}


	
	public Integer getCountWaitConsent()
	{
		return countWaitConsent;
	}


	
	public void setCountWaitConsent(Integer countWaitConsent)
	{
		this.countWaitConsent = countWaitConsent;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((form_code == null) ? 0 : form_code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtForm other = (KtForm) obj;
		if (form_code == null)
		{
			if (other.form_code != null)
				return false;
		}
		else if (!form_code.equals(other.form_code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtForm [form_code=" + form_code + ", form_short_desc=" + form_short_desc + ", form_full_desc="
				+ form_full_desc + ", form_brief=" + form_brief + ", note_1=" + note_1 + ", note_2=" + note_2
				+ ", display_order=" + display_order + ", is_enabled=" + is_enabled + "]";
	}
}
