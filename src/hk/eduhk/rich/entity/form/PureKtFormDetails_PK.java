package hk.eduhk.rich.entity.form;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class PureKtFormDetails_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="form_no")
	private Integer form_no;
	
	@Column(name = "display_order")
	private Integer  display_order;

	
	public Integer getForm_no()
	{
		return form_no;
	}

	
	public void setForm_no(Integer form_no)
	{
		this.form_no = form_no;
	}

	
	public Integer getDisplay_order()
	{
		return display_order;
	}

	
	public void setDisplay_order(Integer display_order)
	{
		this.display_order = display_order;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((display_order == null) ? 0 : display_order.hashCode());
		result = prime * result + ((form_no == null) ? 0 : form_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PureKtFormDetails_PK other = (PureKtFormDetails_PK) obj;
		if (display_order == null)
		{
			if (other.display_order != null)
				return false;
		}
		else if (!display_order.equals(other.display_order))
			return false;
		if (form_no == null)
		{
			if (other.form_no != null)
				return false;
		}
		else if (!form_no.equals(other.form_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PureKtFormDetails_PK [form_no=" + form_no + ", display_order=" + display_order + "]";
	}




}
