package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_STARTUP_HDR")
public class KtFormStartup_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_STARTUP";
	
	public static Logger logger = Logger.getLogger(KtFormStartup_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "act_code")
	private String act_code;
	
	@Column(name = "act_type")
	private String act_type;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "organizer")
	private String organizer;

	@Column(name = "region")
	private String region;
	
	@Column(name = "ct_person")
	private String ct_person;
	
	@Column(name = "num_stu_ug")
	private Integer  num_stu_ug;
	
	@Column(name = "num_stu_pg")
	private Integer  num_stu_pg;
	
	@Column(name = "num_staff")
	private Integer  num_staff;
	
	@Column(name = "num_alumni")
	private Integer  num_alumni;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;
	
	@Transient
	private List<String> act_type_list;
	
	@Transient
	private String actTypeValue = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}


	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}



	
	public String getTitle()
	{
		return title;
	}



	
	public void setTitle(String title)
	{
		this.title = title;
	}



	
	public String getAct_code()
	{
		return act_code;
	}



	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}



	
	public String getAct_type()
	{
		return act_type;
	}



	
	public void setAct_type(String act_type)
	{
		this.act_type = act_type;
	}



	
	public String getFac()
	{
		return fac;
	}



	
	public void setFac(String fac)
	{
		this.fac = fac;
	}



	
	public String getDept()
	{
		return dept;
	}



	
	public void setDept(String dept)
	{
		this.dept = dept;
	}



	
	public Date getStart_date()
	{
		return start_date;
	}



	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}



	
	public Date getEnd_date()
	{
		return end_date;
	}



	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}



	
	public String getOrganizer()
	{
		return organizer;
	}



	
	public void setOrganizer(String organizer)
	{
		this.organizer = organizer;
	}



	
	public String getRegion()
	{
		return region;
	}



	
	public void setRegion(String region)
	{
		this.region = region;
	}



	
	public String getCt_person()
	{
		return ct_person;
	}



	
	public void setCt_person(String ct_person)
	{
		this.ct_person = ct_person;
	}



	
	public Integer getNum_stu_ug()
	{
		return num_stu_ug;
	}



	
	public void setNum_stu_ug(Integer num_stu_ug)
	{
		this.num_stu_ug = num_stu_ug;
	}



	
	public Integer getNum_stu_pg()
	{
		return num_stu_pg;
	}



	
	public void setNum_stu_pg(Integer num_stu_pg)
	{
		this.num_stu_pg = num_stu_pg;
	}



	
	public Integer getNum_staff()
	{
		return num_staff;
	}



	
	public void setNum_staff(Integer num_staff)
	{
		this.num_staff = num_staff;
	}



	
	public Integer getNum_alumni()
	{
		return num_alumni;
	}



	
	public void setNum_alumni(Integer num_alumni)
	{
		this.num_alumni = num_alumni;
	}



	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}



	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}



	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}



	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}



	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}



	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}



	
	public List<String> getAct_type_list()
	{
		if(act_type_list == null && getAct_type() != null) {
			act_type_list = new ArrayList<String>(Arrays.asList(act_type.split(",")));
		}
		return act_type_list;
	}



	
	public void setAct_type_list(List<String> act_type_list)
	{
		this.act_type_list = act_type_list;
	}
	
	
	public String getActTypeValue()
	{
		if(actTypeValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getAct_type()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_STARTUP_ACT_TYPE", getAct_type(), "US");
				
				if(obj!=null) actTypeValue = obj.getDescription();
			}
		}
		return actTypeValue;
	}


	public void setActTypeValue(String actTypeValue)
	{
		this.actTypeValue = actTypeValue;
	}
	
	
	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormStartup_P other = (KtFormStartup_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "KtFormEA_P [pk=" + pk + ", title=" + title + ", act_code=" + act_code + ", act_type=" + act_type
				+ ", fac=" + fac + ", dept=" + dept + ", start_date=" + start_date + ", end_date=" + end_date
				+ ", organizer=" + organizer + ", region=" + region + ", ct_person=" + ct_person + ", num_stu_ug="
				+ num_stu_ug + ", num_stu_pg=" + num_stu_pg + ", num_staff=" + num_staff + ", num_alumni=" + num_alumni
				+ ", remarks_staff=" + remarks_staff + ", remarks_dept="
				+ remarks_dept + ", remarks_kt=" + remarks_kt + "]";
	}


	
}
