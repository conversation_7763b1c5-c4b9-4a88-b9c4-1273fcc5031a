package hk.eduhk.rich.entity.form;

import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;


@Entity
@Table(name = "RH_PURE_KT_FORM_HDR_V")
public class PureKtFormHeader
{
	public static Logger logger = Logger.getLogger(PureKtFormHeader.class.toString());
	
	@Id
	@Column(name = "form_no")
	private int form_no;	
	
	@Column(name = "form_code")
	private String form_code;
	
	@Column(name = "acty_title")
	private String act_title;

	@Column(name = "acty_desc")
	private String act_desc;

	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "formHeader", cascade = {CascadeType.ALL})
	private List<PureKtFormDetails> formDetails;
	
	public int getForm_no()
	{
		return form_no;
	}

	
	public void setForm_no(int form_no)
	{
		this.form_no = form_no;
	}

	
	public String getForm_code()
	{
		return form_code;
	}

	
	public void setForm_code(String form_code)
	{
		this.form_code = form_code;
	}

	
	public String getAct_title()
	{
		return act_title;
	}

	
	public void setAct_title(String act_title)
	{
		this.act_title = act_title;
	}

	
	public String getAct_desc()
	{
		return act_desc;
	}

	
	public void setAct_desc(String act_desc)
	{
		this.act_desc = act_desc;
	}

	
	public Date getStart_date()
	{
		return start_date;
	}

	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}

	
	public Date getEnd_date()
	{
		return end_date;
	}

	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}


	
	public List<PureKtFormDetails> getFormDetails()
	{
		if (formDetails != null) {
			try
			{
				formDetails.size();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					formDetails = dao.getPureKtFormDetailsList(getForm_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return formDetails;
	}


	
	public void setFormDetails(List<PureKtFormDetails> formDetails)
	{
		this.formDetails = formDetails;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + form_no;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PureKtFormHeader other = (PureKtFormHeader) obj;
		if (form_no != other.form_no)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtActHeader [form_no=" + form_no + ", form_code=" + form_code + ", act_title=" + act_title
				+ ", act_desc=" + act_desc + ", start_date=" + start_date + ", end_date=" + end_date + "]";
	}

	

}
