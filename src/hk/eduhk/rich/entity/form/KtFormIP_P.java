package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_IP_HDR")
public class KtFormIP_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_IP";
	
	public static Logger logger = Logger.getLogger(KtFormIP_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "cat")
	private String cat;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "software_lic")
	private String software_lic;

	@Column(name = "staff_name")
	private String staff_name;
	
	@Column(name = "income_cat")
	private String  income_cat;
	
	@Column(name = "income_name")
	private String income_name;
	
	@Column(name = "org_type")
	private String org_type;
	
	@Column(name = "income_rpt_unit")
	private Double  income_rpt_unit;
	
	@Column(name = "num_ext_prof")
	private Integer  num_ext_prof;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;

	@Column(name = "hk_lic")
	private String  hk_lic;
	
	@Column(name = "hk_gov")
	private String  hk_gov;
	
	@Column(name = "hk_pri")
	private String  hk_pri;
	
	@Column(name = "amt_upfront")
	private Double  amt_upfront;
	
	@Column(name = "amt_royalty")
	private Double  amt_royalty;
	
	@Column(name = "debit_note")
	private String  debit_note;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	@Transient
	private String catValue = null;
	
	@Transient
	private String incomeCatValue = null;
	
	@Transient
	private String orgTypeValue = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public String getCat()
	{
		return cat;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public String getDept()
	{
		return dept;
	}

	
	public Date getStart_date()
	{
		return start_date;
	}

	
	public Date getEnd_date()
	{
		return end_date;
	}

	
	public String getSoftware_lic()
	{
		return software_lic;
	}

	
	public String getStaff_name()
	{
		return staff_name;
	}

	
	public String getIncome_cat()
	{
		return income_cat;
	}

	
	public String getIncome_name()
	{
		return income_name;
	}

	
	public String getOrg_type()
	{
		return org_type;
	}

	
	public Double getIncome_rpt_unit()
	{
		return income_rpt_unit;
	}

	
	public Integer getNum_ext_prof()
	{
		return num_ext_prof;
	}

	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}

	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}

	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}

	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public void setCat(String cat)
	{
		this.cat = cat;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public void setDept(String dept)
	{
		this.dept = dept;
	}

	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}

	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}

	
	public void setSoftware_lic(String software_lic)
	{
		this.software_lic = software_lic;
	}

	
	public void setStaff_name(String staff_name)
	{
		this.staff_name = staff_name;
	}

	
	public void setIncome_cat(String income_cat)
	{
		this.income_cat = income_cat;
	}

	
	public void setIncome_name(String income_name)
	{
		this.income_name = income_name;
	}

	
	public void setOrg_type(String org_type)
	{
		this.org_type = org_type;
	}

	
	public void setIncome_rpt_unit(Double income_rpt_unit)
	{
		this.income_rpt_unit = income_rpt_unit;
	}

	
	public void setNum_ext_prof(Integer num_ext_prof)
	{
		this.num_ext_prof = num_ext_prof;
	}

	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}

	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}

	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}
	
	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}
	
	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}
	
	
	public String getCatValue()
	{
		if(catValue == null) 
		{
			if(!GenericValidator.isBlankOrNull(getCat()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_IP_CAT", getCat(), "US");
				
				if(obj!=null) catValue = obj.getDescription();
			}
		}
		return catValue;
	}

	
	public void setCatValue(String catValue)
	{
		this.catValue = catValue;
	}

	
	public String getIncomeCatValue()
	{
		if(incomeCatValue == null) 
		{
			if(!GenericValidator.isBlankOrNull(getIncome_cat()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_IP_FUND_SRC", getIncome_cat(), "US");
				
				if(obj!=null) incomeCatValue = obj.getDescription();
			}
		}
		return incomeCatValue;
	}

	
	public void setIncomeCatValue(String incomeCatValue)
	{
		this.incomeCatValue = incomeCatValue;
	}

	
	public String getOrgTypeValue()
	{
		if(orgTypeValue == null) 
		{
			if(!GenericValidator.isBlankOrNull(getOrg_type()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_IP_ORG_TYPE", getOrg_type(), "US");
				
				if(obj!=null) orgTypeValue = obj.getDescription();
			}
		}
		return orgTypeValue;
	}

	
	public void setOrgTypeValue(String orgTypeValue)
	{
		this.orgTypeValue = orgTypeValue;
	}


	
	public String getHk_lic()
	{
		return hk_lic;
	}


	
	public void setHk_lic(String hk_lic)
	{
		this.hk_lic = hk_lic;
	}


	
	public String getHk_gov()
	{
		return hk_gov;
	}


	
	public void setHk_gov(String hk_gov)
	{
		this.hk_gov = hk_gov;
	}


	
	public String getHk_pri()
	{
		return hk_pri;
	}


	
	public void setHk_pri(String hk_pri)
	{
		this.hk_pri = hk_pri;
	}


	
	public Double getAmt_upfront()
	{
		return amt_upfront;
	}


	
	public void setAmt_upfront(Double amt_upfront)
	{
		this.amt_upfront = amt_upfront;
	}


	
	public Double getAmt_royalty()
	{
		return amt_royalty;
	}


	
	public void setAmt_royalty(Double amt_royalty)
	{
		this.amt_royalty = amt_royalty;
	}


	
	public String getDebit_note()
	{
		return debit_note;
	}


	
	public void setDebit_note(String debit_note)
	{
		this.debit_note = debit_note;
	}


	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}


	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}


	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}


	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormIP_P other = (KtFormIP_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormIP_P [pk=" + pk + ", title=" + title + ", cat=" + cat + ", fac=" + fac + ", dept=" + dept
				+ ", start_date=" + start_date + ", end_date=" + end_date + ", software_lic=" + software_lic
				+ ", staff_name=" + staff_name + ", income_cat=" + income_cat + ", income_name=" + income_name
				+ ", org_type=" + org_type + ", income_rpt_unit=" + income_rpt_unit + ", num_ext_prof=" + num_ext_prof
				+ ", remarks_staff=" + remarks_staff + ", remarks_dept=" + remarks_dept + ", remarks_kt=" + remarks_kt
				+ ", hk_lic=" + hk_lic + ", hk_gov=" + hk_gov + ", hk_pri=" + hk_pri + ", amt_upfront=" + amt_upfront
				+ ", amt_royalty=" + amt_royalty + ", debit_note=" + debit_note + "]";
	}

}
