package hk.eduhk.rich.entity.form;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.staff.AssistantPK;

@Entity
@Table(name = "RH_Q_KT_FORM_DTL")
public class KtFormDetails_Q extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(KtFormDetails_Q.class.toString());
	
	@EmbeddedId
	private KtFormDetails_Q_PK pk = new KtFormDetails_Q_PK();
	
	@Column(name = "creator_ind")
	private String creator_ind;

	@Column(name = "display_ind")
	private String display_ind;	
	
	@Column(name = "consent_ind")
	private String consent_ind;

	
	public KtFormDetails_Q_PK getPk()
	{
		return pk;
	}

	
	public void setPk(KtFormDetails_Q_PK pk)
	{
		this.pk = pk;
	}

	
	public String getCreator_ind()
	{
		return creator_ind;
	}

	
	public void setCreator_ind(String creator_ind)
	{
		this.creator_ind = creator_ind;
	}

	
	public String getDisplay_ind()
	{
		return display_ind;
	}

	
	public void setDisplay_ind(String display_ind)
	{
		this.display_ind = display_ind;
	}

	
	public String getConsent_ind()
	{
		return consent_ind;
	}

	
	public void setConsent_ind(String consent_ind)
	{
		this.consent_ind = consent_ind;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormDetails_Q other = (KtFormDetails_Q) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormDetails_Q [pk=" + pk + ", creator_ind=" + creator_ind + ", display_ind=" + display_ind
				+ ", consent_ind=" + consent_ind + "]";
	}


}
