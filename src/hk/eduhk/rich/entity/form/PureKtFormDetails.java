package hk.eduhk.rich.entity.form;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.report.KtRptDAO;


@Entity
@Table(name = "RH_PURE_KT_FORM_DTL_V")
public class PureKtFormDetails
{
	public static Logger logger = Logger.getLogger(PureKtFormDetails.class.toString());
	
	@EmbeddedId
	private PureKtFormDetails_PK pk = new PureKtFormDetails_PK();
	
	@Column(name="person_source_id")
	private String person_source_id;	
	
	@Column(name = "role")
	private String role;
	
	@Column(name = "last_name")
	private String last_name;

	@Column(name = "first_name")
	private String first_name;

	@Column(name = "external_ind")
	private String external_ind;

	

	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "form_no", referencedColumnName = "form_no", nullable = false, insertable = false, updatable = false)
	private PureKtFormHeader formHeader;
	
	public PureKtFormDetails_PK getPk()
	{
		return pk;
	}

	
	public void setPk(PureKtFormDetails_PK pk)
	{
		this.pk = pk;
	}

	
	public String getRole()
	{
		return role;
	}

	
	public void setRole(String role)
	{
		this.role = role;
	}

	
	public String getLast_name()
	{
		return last_name;
	}

	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}

	
	public String getFirst_name()
	{
		return first_name;
	}

	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}

	
	public String getExternal_ind()
	{
		return external_ind;
	}

	
	public void setExternal_ind(String external_ind)
	{
		this.external_ind = external_ind;
	}


	public String getPerson_source_id()
	{
		return person_source_id;
	}


	
	public void setPerson_source_id(String person_source_id)
	{
		this.person_source_id = person_source_id;
	}


	public PureKtFormHeader getFormHeader()
	{
		if (formHeader != null) 
		{
			try
			{
				formHeader.getForm_no();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					formHeader = dao.getPureKtFormHeader(getPk().getForm_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return formHeader;
	}


	
	public void setFormHeader(PureKtFormHeader formHeader)
	{
		this.formHeader = formHeader;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PureKtFormDetails other = (PureKtFormDetails) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PureKtFormDetails [pk=" + pk + ", person_source_id=" + person_source_id + ", role=" + role
				+ ", last_name=" + last_name + ", first_name=" + first_name + ", external_ind=" + external_ind
				+ ", formHeader=" + formHeader + "]";
	}

}
