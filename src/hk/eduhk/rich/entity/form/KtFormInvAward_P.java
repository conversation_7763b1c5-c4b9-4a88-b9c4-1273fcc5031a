package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_INV_AWARD_HDR")
public class KtFormInvAward_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_INV_AWARD";
	
	public static Logger logger = Logger.getLogger(KtFormInvAward_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();

	@Column(name = "title")
	private String title;
	
	@Column(name = "act_code")
	private String act_code;
	
	@Column(name = "event_name")
	private String event_name;
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "name_pi")
	private String name_pi;

	@Column(name = "name_other")
	private String name_other;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;
	
	@Column(name = "region")
	private String region;
	
	@Column(name = "num_pat_filed")
	private Integer  num_pat_filed;
	
	@Column(name = "num_pat_granted")
	private Integer  num_pat_granted;
	
	@Column(name = "ip_name")
	private String ip_name;
	
	@Column(name = "award_title")
	private String award_title;
	
	@Column(name = "award_type")
	private String award_type;
	
	@Column(name = "patent_filed_name")
	private String patent_filed_name;
	
	@Column(name = "patent_filed_num")
	private String patent_filed_num;
	
	@Column(name = "patent_filed_date")
	private String patent_filed_date;
	
	@Column(name = "patent_filed_country")
	private String patent_filed_country;
	
	@Column(name = "patent_granted_name")
	private String patent_granted_name;
	
	@Column(name = "patent_granted_num")
	private String patent_granted_num;
	
	@Column(name = "patent_granted_date")
	private String patent_granted_date;
	
	@Column(name = "patent_granted_country")
	private String patent_granted_country;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}


	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}



	
	public String getTitle()
	{
		return title;
	}



	
	public void setTitle(String title)
	{
		this.title = title;
	}



	
	public String getAct_code()
	{
		return act_code;
	}



	
	public void setAct_code(String act_code)
	{
		this.act_code = act_code;
	}



	
	public String getEvent_name()
	{
		return event_name;
	}



	
	public void setEvent_name(String event_name)
	{
		this.event_name = event_name;
	}



	
	public String getFac()
	{
		return fac;
	}



	
	public void setFac(String fac)
	{
		this.fac = fac;
	}



	
	public String getDept()
	{
		return dept;
	}



	
	public void setDept(String dept)
	{
		this.dept = dept;
	}



	
	public Date getStart_date()
	{
		return start_date;
	}



	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}



	
	public Date getEnd_date()
	{
		return end_date;
	}



	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}



	
	public String getName_pi()
	{
		return name_pi;
	}



	
	public void setName_pi(String name_pi)
	{
		this.name_pi = name_pi;
	}



	
	public String getName_other()
	{
		return name_other;
	}



	
	public void setName_other(String name_other)
	{
		this.name_other = name_other;
	}



	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}



	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}



	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}



	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}



	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}



	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}
	
	
	
	public String getRegion()
	{
		return region;
	}



	
	public void setRegion(String region)
	{
		this.region = region;
	}



	
	public Integer getNum_pat_filed()
	{
		return num_pat_filed;
	}



	
	public void setNum_pat_filed(Integer num_pat_filed)
	{
		this.num_pat_filed = num_pat_filed;
	}



	
	public Integer getNum_pat_granted()
	{
		return num_pat_granted;
	}



	
	public void setNum_pat_granted(Integer num_pat_granted)
	{
		this.num_pat_granted = num_pat_granted;
	}



	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}



	
	
	public String getIp_name()
	{
		return ip_name;
	}



	
	public void setIp_name(String ip_name)
	{
		this.ip_name = ip_name;
	}



	
	public String getAward_title()
	{
		return award_title;
	}



	
	public void setAward_title(String award_title)
	{
		this.award_title = award_title;
	}



	
	public String getAward_type()
	{
		return award_type;
	}



	
	public void setAward_type(String award_type)
	{
		this.award_type = award_type;
	}



	
	public String getPatent_filed_name()
	{
		return patent_filed_name;
	}



	
	public void setPatent_filed_name(String patent_filed_name)
	{
		this.patent_filed_name = patent_filed_name;
	}



	
	public String getPatent_filed_num()
	{
		return patent_filed_num;
	}



	
	public void setPatent_filed_num(String patent_filed_num)
	{
		this.patent_filed_num = patent_filed_num;
	}



	
	public String getPatent_filed_date()
	{
		return patent_filed_date;
	}



	
	public void setPatent_filed_date(String patent_filed_date)
	{
		this.patent_filed_date = patent_filed_date;
	}



	
	public String getPatent_filed_country()
	{
		return patent_filed_country;
	}



	
	public void setPatent_filed_country(String patent_filed_country)
	{
		this.patent_filed_country = patent_filed_country;
	}



	
	public String getPatent_granted_name()
	{
		return patent_granted_name;
	}



	
	public void setPatent_granted_name(String patent_granted_name)
	{
		this.patent_granted_name = patent_granted_name;
	}



	
	public String getPatent_granted_num()
	{
		return patent_granted_num;
	}



	
	public void setPatent_granted_num(String patent_granted_num)
	{
		this.patent_granted_num = patent_granted_num;
	}



	
	public String getPatent_granted_date()
	{
		return patent_granted_date;
	}



	
	public void setPatent_granted_date(String patent_granted_date)
	{
		this.patent_granted_date = patent_granted_date;
	}



	
	public String getPatent_granted_country()
	{
		return patent_granted_country;
	}



	
	public void setPatent_granted_country(String patent_granted_country)
	{
		this.patent_granted_country = patent_granted_country;
	}



	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}



	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}



	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}



	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}



	@Override
	public String toString()
	{
		return "KtFormInvAward_P [pk=" + pk + ", title=" + title + ", act_code=" + act_code + ", event_name="
				+ event_name + ", fac=" + fac + ", dept=" + dept + ", start_date=" + start_date + ", end_date="
				+ end_date + ", name_pi=" + name_pi + ", name_other=" + name_other + ", remarks_staff=" + remarks_staff
				+ ", remarks_dept=" + remarks_dept + ", remarks_kt=" + remarks_kt + ", region=" + region
				+ ", num_pat_filed=" + num_pat_filed + ", num_pat_granted=" + num_pat_granted + ", ip_name=" + ip_name
				+ ", award_title=" + award_title + ", award_type=" + award_type + ", patent_filed_name="
				+ patent_filed_name + ", patent_filed_num=" + patent_filed_num + ", patent_filed_date="
				+ patent_filed_date + ", patent_filed_country=" + patent_filed_country + ", patent_granted_name="
				+ patent_granted_name + ", patent_granted_num=" + patent_granted_num + ", patent_granted_date="
				+ patent_granted_date + ", patent_granted_country=" + patent_granted_country + "]";
	}

}
