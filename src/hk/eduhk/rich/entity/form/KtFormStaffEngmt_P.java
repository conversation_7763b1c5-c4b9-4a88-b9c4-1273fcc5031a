package hk.eduhk.rich.entity.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_P_KT_STAFF_ENGMT_HDR")
public class KtFormStaffEngmt_P extends UserPersistenceObject
{
	public static final String REPORT_FORM_CODE = "KT_STAFF_ENGMT";
	
	public static Logger logger = Logger.getLogger(KtFormStaffEngmt_P.class.toString());
	
	@EmbeddedId
	private KtFormHeader_PK pk = new KtFormHeader_PK();
	
	@Column(name = "fac")
	private String fac;
	
	@Column(name = "dept")
	private String dept;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Column(name = "ext_body_name")
	private String ext_body_name;
	
	@Column(name = "ext_body_nature")
	private String ext_body_nature;
	
	@Column(name = "staff_name")
	private String staff_name;
	
	@Column(name = "region")
	private String region;
	
	@Column(name = "country")
	private String country;
	
	@Column(name = "post_engaged")
	private String post_engaged;
	
	@Column(name = "remarks_staff")
	private String  remarks_staff;
	
	@Column(name = "remarks_dept")
	private String  remarks_dept;
	
	@Column(name = "remarks_kt")
	private String  remarks_kt;
	
	@Column(name = "admin_tech")
	private String  admin_tech;
	
	@Column(name = "num_proj_day")
	private Integer  num_proj_day;
	
	@Column(name = "num_proj_day_in_yr")
	private Integer  num_proj_day_in_yr;
	
	@Transient
	private String extBodyNatureValue = null;
	
	@Transient
	private String regionValue = null;
	
	public KtFormHeader_PK getPk()
	{
		return pk;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public String getDept()
	{
		return dept;
	}

	
	public Date getStart_date()
	{
		return start_date;
	}

	
	public Date getEnd_date()
	{
		return end_date;
	}

	
	public String getExt_body_name()
	{
		return ext_body_name;
	}

	
	public String getStaff_name()
	{
		return staff_name;
	}

	
	public String getRegion()
	{
		return region;
	}

	
	public String getCountry()
	{
		return country;
	}

	
	public String getPost_engaged()
	{
		return post_engaged;
	}

	
	public String getRemarks_staff()
	{
		return remarks_staff;
	}

	
	public String getRemarks_dept()
	{
		return remarks_dept;
	}

	
	public String getRemarks_kt()
	{
		return remarks_kt;
	}

	
	public void setPk(KtFormHeader_PK pk)
	{
		this.pk = pk;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public void setDept(String dept)
	{
		this.dept = dept;
	}

	
	public void setStart_date(Date start_date)
	{
		this.start_date = start_date;
	}

	
	public void setEnd_date(Date end_date)
	{
		this.end_date = end_date;
	}

	
	public void setExt_body_name(String ext_body_name)
	{
		this.ext_body_name = ext_body_name;
	}

	
	
	public String getExt_body_nature()
	{
		return ext_body_nature;
	}


	
	public void setExt_body_nature(String ext_body_nature)
	{
		this.ext_body_nature = ext_body_nature;
	}


	public void setStaff_name(String staff_name)
	{
		this.staff_name = staff_name;
	}

	
	public void setRegion(String region)
	{
		this.region = region;
	}

	
	public void setCountry(String country)
	{
		this.country = country;
	}

	
	public void setPost_engaged(String post_engaged)
	{
		this.post_engaged = post_engaged;
	}

	
	public void setRemarks_staff(String remarks_staff)
	{
		this.remarks_staff = remarks_staff;
	}

	
	public void setRemarks_dept(String remarks_dept)
	{
		this.remarks_dept = remarks_dept;
	}

	
	public void setRemarks_kt(String remarks_kt)
	{
		this.remarks_kt = remarks_kt;
	}
	
	public String getStart_dateStr() {
		String rtnStr = "";
		if(start_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(start_date);
		}
		return rtnStr;
	}
	
	public String getEnd_dateStr() {
		String rtnStr = "";
		if(end_date != null) {
			DateFormat dateFormat = new SimpleDateFormat("MM/yyyy");
			rtnStr = dateFormat.format(end_date);
		}
		return rtnStr;
	}

	
	public String getExtBodyNatureValue()
	{
		if(extBodyNatureValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getExt_body_nature()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_STAFF_ENGMT_NATURE", getExt_body_nature(), "US");
				
				if(obj!=null) extBodyNatureValue = obj.getDescription();
			}
		}
		return extBodyNatureValue;
	}


	public void setExtBodyNatureValue(String extBodyNatureValue)
	{
		this.extBodyNatureValue = extBodyNatureValue;
	}


	
	
	public String getRegionValue()
	{
		if(regionValue==null) 
		{
			if(!GenericValidator.isBlankOrNull(getRegion()))
			{
				LookupValue obj = LookupValueDAO.getCacheInstance().getLookupValue("KT_REGION", getRegion(), "US");
				
				if(obj!=null) regionValue = obj.getDescription();
			}
		}
		return regionValue;
	}


	
	public void setRegionValue(String regionValue)
	{
		this.regionValue = regionValue;
	}


	public String getAdmin_tech()
	{
		return admin_tech;
	}


	
	public void setAdmin_tech(String admin_tech)
	{
		this.admin_tech = admin_tech;
	}


	
	public Integer getNum_proj_day()
	{
		return num_proj_day;
	}


	
	public void setNum_proj_day(Integer num_proj_day)
	{
		this.num_proj_day = num_proj_day;
	}


	
	public Integer getNum_proj_day_in_yr()
	{
		return num_proj_day_in_yr;
	}


	
	public void setNum_proj_day_in_yr(Integer num_proj_day_in_yr)
	{
		this.num_proj_day_in_yr = num_proj_day_in_yr;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtFormStaffEngmt_P other = (KtFormStaffEngmt_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtFormStaffEngmt_P [pk=" + pk + ", fac=" + fac + ", dept=" + dept + ", start_date=" + start_date
				+ ", end_date=" + end_date + ", ext_body_name=" + ext_body_name + ", ext_body_nature=" + ext_body_nature
				+ ", staff_name=" + staff_name + ", region=" + region + ", country=" + country + ", post_engaged="
				+ post_engaged + ", remarks_staff=" + remarks_staff + ", remarks_dept=" + remarks_dept + ", remarks_kt="
				+ remarks_kt + ", admin_tech=" + admin_tech + ", num_proj_day=" + num_proj_day + ", num_proj_day_in_yr="
				+ num_proj_day_in_yr + ", extBodyNatureValue=" + extBodyNatureValue + ", regionValue=" + regionValue
				+ "]";
	}



}
