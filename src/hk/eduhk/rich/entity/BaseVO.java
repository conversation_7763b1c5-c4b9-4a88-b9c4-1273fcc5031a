package hk.eduhk.rich.entity;

import java.io.Serializable;
import java.lang.reflect.*;
import java.sql.Timestamp;
import java.util.*;

import org.apache.commons.beanutils.BeanUtils;

import org.json.simple.JSONAware;
import org.json.simple.JSONObject;


public abstract class BaseVO implements Serializable, JSONAware
{

	private String createdBy;
	private Timestamp creationDate;
	private String userstamp;
	private Timestamp timestamp;


	private static Map typeClassMap = new HashMap();
	static
	{
		typeClassMap.put(Byte.TYPE, Byte.class);
		typeClassMap.put(Double.TYPE, Double.class);
		typeClassMap.put(Float.TYPE, Float.class);
		typeClassMap.put(Integer.TYPE, Integer.class);
		typeClassMap.put(Long.TYPE, Long.class);
		typeClassMap.put(Short.TYPE, Short.class);
		typeClassMap.put(String.class, String.class);
	}


	public void setCreatedBy(String createdBy)
	{
		this.createdBy = createdBy;
	}
	
	public String getCreatedBy()
	{
		return createdBy;
	}

	public void setCreationDate(Date creationDate)
	{
		this.creationDate = (creationDate != null) ? new Timestamp(creationDate.getTime()) : null;
	}
	
	public Timestamp getCreationDate()
	{
		return creationDate;
	}

	public void setUserstamp(String userstamp)
	{
		this.userstamp = userstamp;
	}
	
	public String getUserstamp()
	{
		return userstamp;
	}

	public void setTimestamp(Date timestamp)
	{
		this.timestamp = (timestamp != null) ? new Timestamp(timestamp.getTime()) : null;
	}
	
	public Timestamp getTimestamp()
	{
		return timestamp;
	}


	/**
	 * Populate this value object by the provided JSON object.
	 */
	public void populateByJSON(JSONObject jsonObj)
	{
		try
		{
			
			//System.out.println("populateByJSON");
			BeanUtils.populate(this, jsonObj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		/*
		*/

		/*
		Class[] classArray = new Class[1];
		Object[] valueArray = new Object[1];

		Iterator nameIter = jsonObj.keySet().iterator();
		while (nameIter.hasNext())
		{
			try
			{
				// Invoke the setting if available.
				String name = (String) nameIter.next();
				String methodName = "set" + StringUtils.capitalize(name);
				Class argType = null;

				// Only accept one argument in the setter, and find out the type of the argument
				// If field not found, use String class by default.
				try
				{
					Field f = this.getClass().getDeclaredField(name);
					argType = f.getType();
				}
				catch (NoSuchFieldException nsfe)
				{
					argType = String.class;
				}

				// Get the corresponding setter method.
				classArray[0] = argType;
				Method m = this.getClass().getMethod(methodName, classArray);

				if (m != null)
				{
					// Cast the value from JSONObject to the corresponding argument type.
					classArray[0] = String.class;
					valueArray[0] = jsonObj.get(name);
					Class objClass = (Class) typeClassMap.get(argType);
					Constructor c = objClass.getDeclaredConstructor(classArray);
					valueArray[0] = c.newInstance(valueArray);

					// Invoke the setter method.
					m.invoke(this, valueArray);
				}
			}
			catch (Exception e)
			{
				// Ignore
			}
		}
		*/
	}


	public String toJSONString()
	{
		return "";
	} 

}