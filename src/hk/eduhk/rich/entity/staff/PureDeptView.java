package hk.eduhk.rich.entity.staff;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;

@ManagedBean(name = "pureDeptView")
@ViewScoped
@SuppressWarnings("serial")
public class PureDeptView extends BaseView
{
	private static Logger logger = Logger.getLogger(PureDept.class.getName());
	private List<PureDept> pureDeptList = null;
	private PureDept selectedPureDept;
	private PureDept removeDept;
	private String paramDeptCode;

	
	public String getParamDeptCode()
	{
		return paramDeptCode;
	}


	
	public void setParamDeptCode(String paramDeptCode)
	{
		this.paramDeptCode = paramDeptCode;
		if (paramDeptCode != null) {
			StaffDAO dao = StaffDAO.getInstance();
			selectedPureDept = dao.getPureDept(paramDeptCode);
		}
	}

	public void reloadPureDeptList()
	{
		pureDeptList = null;
	}
	
	public List<PureDept> getPureDeptList()
	{
		if (pureDeptList == null)
		{
			StaffDAO dao = StaffDAO.getInstance();
			pureDeptList = dao.getPureDeptList();
		}
		return pureDeptList;
	}
	
	
	public PureDept getSelectedPureDept()
	{
		// To avoid NullPointerException
		if (selectedPureDept == null)
			selectedPureDept = new PureDept();
		return selectedPureDept;
	}

	
	public void setSelectedPureDept(PureDept selectedPureDept)
	{
		this.selectedPureDept = selectedPureDept;
	}

	public void onRowEdit(RowEditEvent<PureDept> event) {
	       Boolean isDuplicateKey = false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";

			//Check department code is unique
			int count = 0;
			for (PureDept d: pureDeptList){
				if (event.getObject().getDepartment_code().equals(d.getDepartment_code())) {
					count++;
				}
			}
			if (count > 1) {
				isDuplicateKey = true;
			}
			
			if (isDuplicateKey) {
				String param = "Department code";
				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
			
			//Check data
			if (event.getObject().getDepartment_code().isEmpty()) {
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Department code cannot be null", ""));
    		}
    		
			if (event.getObject().getDepartment_name().length() > 240) {
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Department name is too long ", ""));
			}
			
			if (event.getObject().getPure_source_id().length() > 50) {
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "PURE Source ID is too long ", ""));
			}  			
			
			if (event.getObject().getParent_source_id().length() > 50) {
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "PURE Parent ID is too long ", ""));
			}   			
			
			//Update department
    		if (fCtx.getMessageList().isEmpty()) {
    			selectedPureDept = event.getObject();
    			updatePureDept();
    			if (removeDept != null) {
    				StaffDAO dao = StaffDAO.getInstance();
			    	dao.deletePureDept(removeDept.getDepartment_code());
    			}
        		pureDeptList = null;
    		} 
    }
	
	public void onRowCancel(RowEditEvent<PureDept> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getDepartment_code()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();
    }
 
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		StaffDAO dao = StaffDAO.getInstance();
    		removeDept =  dao.getPureDept((String)event.getOldValue());
    	}
    }       
    
    public void onAddNew() {
    	PureDept newDept = new PureDept();
    	pureDeptList.add(0, newDept);
    }
    
    public void updatePureDept() {
    	String message;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedPureDept != null) {
    		try {
    			Boolean isNew = (selectedPureDept.getCreator() == null)?true:false;
		    	selectedPureDept.setUserstamp(getLoginUserId());
		    	StaffDAO dao = StaffDAO.getInstance();
				dao.updatePureDept(selectedPureDept);
				message = (isNew)?"msg.success.create.x":"msg.success.update.x";
				message = MessageFormat.format(getResourceBundle().getString(message), selectedPureDept.getDepartment_code());
				
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    		}catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("Department");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}

    	}
    	
    }
    
    public String updateForm() {
    	
    	updatePureDept();
    	return redirect("manageDepartment");
    }
    
    public void deletePureDept() {
    	if (selectedPureDept != null) {
    		try {
    			if (selectedPureDept.getDepartment_code() != null) {
			    	StaffDAO dao = StaffDAO.getInstance();
			    	dao.deletePureDept(selectedPureDept.getDepartment_code());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedPureDept.getDepartment_code());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
    			pureDeptList.remove(selectedPureDept);
    			selectedPureDept = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedPureDept.getDepartment_code());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
    
	public String gotoEditPage() throws UnsupportedEncodingException
	{		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("manageDepartment_edit")+
				   "&deptCode=" + selectedPureDept.getDepartment_code() +
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	public String gotoNewPage() throws UnsupportedEncodingException
	{		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("manageDepartment_edit")+
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
}
