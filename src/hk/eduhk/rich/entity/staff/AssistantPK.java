package hk.eduhk.rich.entity.staff;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
public class AssistantPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="PID")
	private int pid;

	@Column(name="ASSISTANT_ID")
	private String assistant_id;

	
	public int getPid()
	{
		return pid;
	}


	
	public void setPid(int pid)
	{
		this.pid = pid;
	}


	public String getAssistant_id()
	{
		return assistant_id;
	}

	
	public void setAssistant_id(String assistant_id)
	{
		this.assistant_id = assistant_id;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((assistant_id == null) ? 0 : assistant_id.hashCode());
		result = prime * result + pid;
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AssistantPK other = (AssistantPK) obj;
		if (assistant_id == null)
		{
			if (other.assistant_id != null)
				return false;
		}
		else if (!assistant_id.equals(other.assistant_id))
			return false;
		if (pid != other.pid)
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "AssistantPK [pid=" + pid + ", assistant_id=" + assistant_id + "]";
	}


}
