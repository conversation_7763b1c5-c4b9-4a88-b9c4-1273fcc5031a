package hk.eduhk.rich.entity.staff;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_P_STAFF_IDENTITY")
@SuppressWarnings("serial")
public class StaffIdentity extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(StaffIdentity.class.toString());
		
	@Id
	@Column(name = "staff_number")
	private String staff_number;

	@Column(name = "pid")
	private int pid;

	@Column(name = "cn")
	private String cn;

	@Column(name = "title")
	private String title;

	@Column(name = "surname")
	private String surname;

	@Column(name = "othername")
	private String othername;

	@Column(name = "fullname")
	private String fullname;

	@Column(name = "chinesename")
	private String chinesename;

	@Column(name = "email")
	private String email;

	@Column(name = "acad_staff_ind")
	private String acad_staff_ind;

	@Column(name = "dept_code")
	private String dept_code;

	@Column(name = "rank_code")
	private String rank_code;

	@Column(name = "rich_person_ind")
	private String rich_person_ind;

	@Column(name = "cost_centre_code")
	private String cost_centre_code;

	@Column(name = "rae_staff_ind")
	private String rae_staff_ind;

	@Column(name = "acad_quali")
	private String acad_quali;

	@Column(name = "gender")
	private String gender;
	
	@Transient
	private String staffType = null;
	
	@Transient
	private String fullname_display;
	
	@Transient
	private String fullname_save;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "pid", referencedColumnName = "pid", nullable = false, insertable = false, updatable = false)
	private StaffInfo staffInfo;
	
	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}
	
	public StaffInfo getStaffInfo()
	{
		if (staffInfo != null) {
			try {
				staffInfo.getStaff_number();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					StaffDAO dao = StaffDAO.getInstance();
					staffInfo = dao.getStaffProfileByStaffNo(getStaff_number());
				}
				else
				{
					throw re;
				}
			}
		}
		return staffInfo;
	}
	
	
	public String getCn()
	{
		return cn;
	}

	
	public void setCn(String cn)
	{
		this.cn = cn;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getSurname()
	{
		return surname;
	}

	
	public void setSurname(String surname)
	{
		this.surname = surname;
	}

	
	public String getOthername()
	{
		return othername;
	}

	
	public void setOthername(String othername)
	{
		this.othername = othername;
	}

	
	public String getFullname()
	{
		return fullname;
	}

	
	public void setFullname(String fullname)
	{
		this.fullname = fullname;
	}

	
	public String getChinesename()
	{
		return chinesename;
	}

	
	public void setChinesename(String chinesename)
	{
		this.chinesename = chinesename;
	}

	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}

	
	public String getAcad_staff_ind()
	{
		return acad_staff_ind;
	}

	
	public void setAcad_staff_ind(String acad_staff_ind)
	{
		this.acad_staff_ind = acad_staff_ind;
	}

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getRank_code()
	{
		return rank_code;
	}

	
	public void setRank_code(String rank_code)
	{
		this.rank_code = rank_code;
	}

	
	public String getRich_person_ind()
	{
		return rich_person_ind;
	}

	
	public void setRich_person_ind(String rich_person_ind)
	{
		this.rich_person_ind = rich_person_ind;
	}

	
	public String getCost_centre_code()
	{
		return cost_centre_code;
	}

	
	public void setCost_centre_code(String cost_centre_code)
	{
		this.cost_centre_code = cost_centre_code;
	}

	
	public String getRae_staff_ind()
	{
		return rae_staff_ind;
	}

	
	public void setRae_staff_ind(String rae_staff_ind)
	{
		this.rae_staff_ind = rae_staff_ind;
	}

	
	public String getAcad_quali()
	{
		return acad_quali;
	}

	
	public void setAcad_quali(String acad_quali)
	{
		this.acad_quali = acad_quali;
	}

	
	public String getGender()
	{
		return gender;
	}

	
	public void setGender(String gender)
	{
		this.gender = gender;
	}


	
	public String getStaffType()
	{
		return staffType;
	}


	
	public void setStaffType(String staffType)
	{
		this.staffType = staffType;
	}

	public void setFullname_save(String fullname_save)
	{
		this.fullname_save = fullname_save;
	}


	public String getFullname_save()
	{
		if (fullname_save == null) {
			if (getSurname() != null) {
				fullname_save = getSurname();
			}
			if (getOthername() != null) {
				String otherName = getOthername();
				otherName = capitalizeFully(otherName);
				fullname_save = fullname_save + ", " + otherName;
			}
		}
		return fullname_save;
	}
	
	public String getFullname_display()
	{
		if (fullname_display == null) {
			if (getFullname_save() != null) {
				fullname_display = getFullname_save();
				if (getChinesename() != null) {
					fullname_display = fullname_display +" " + getChinesename();
				}
			}
		}
		return fullname_display;
	}

	
	public void setFullname_display(String fullname_display)
	{
		this.fullname_display = fullname_display;
	}
	
	private String capitalizeFully(String str) {
	    StringBuilder sb = new StringBuilder();
	    boolean cnl = true; // <-- capitalize next letter.
	    for (char c : str.toCharArray()) {
	        if (cnl && Character.isLetter(c)) {
	            sb.append(Character.toUpperCase(c));
	            cnl = false;
	        } else {
	            sb.append(Character.toLowerCase(c));
	        }
	        if (Character.isWhitespace(c)) {
	            cnl = true;
	        }
	    }
	    return sb.toString();
	}
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((staff_number == null) ? 0 : staff_number.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffIdentity other = (StaffIdentity) obj;
		if (staff_number == null)
		{
			if (other.staff_number != null)
				return false;
		}
		else if (!staff_number.equals(other.staff_number))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "StaffIdentity [staff_number=" + staff_number + ", pid=" + pid + ", cn=" + cn + ", title=" + title
				+ ", surname=" + surname + ", othername=" + othername + ", fullname=" + fullname + ", chinesename="
				+ chinesename + ", email=" + email + ", acad_staff_ind=" + acad_staff_ind + ", dept_code=" + dept_code
				+ ", rank_code=" + rank_code + ", rich_person_ind=" + rich_person_ind + ", cost_centre_code="
				+ cost_centre_code + ", rae_staff_ind=" + rae_staff_ind + ", acad_quali=" + acad_quali + ", gender="
				+ gender + "]";
	}




}
