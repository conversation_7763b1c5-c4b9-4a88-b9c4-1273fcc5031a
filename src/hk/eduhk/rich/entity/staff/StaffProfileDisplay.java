package hk.eduhk.rich.entity.staff;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_U_STAFF_PROFILE_DISPLAY")
@SuppressWarnings("serial")
public class StaffProfileDisplay extends UserPersistenceObject
{
	@EmbeddedId
	private StaffProfileDisplayPK pk = new StaffProfileDisplayPK();
	
	@Column(name = "ITEM_TYPE")
	private String itemType;
	
	@Column(name = "SHOW_IND")
	private String showInd;
	
	@Column(name = "DISPLAY_ORDER")
	private int displayOrder;
	
	@Column(name = "DISPLAY_TYPE")
	private String displayType;
	
	@Transient
	private String itemName;
	
	@Transient
	private String itemIcon;
	
	@Transient
	private Boolean isShow;

	public StaffProfileDisplayPK getPk()
	{
		return pk;
	}

	
	public void setPk(StaffProfileDisplayPK pk)
	{
		this.pk = pk;
		this.itemName = null;
		this.itemIcon = null;
	}

	
	public String getItemType()
	{
		return itemType;
	}

	
	public void setItemType(String itemType)
	{
		this.itemType = itemType;
	}

	
	public String getShowInd()
	{
		return showInd;
	}

	
	public void setShowInd(String showInd)
	{
		this.showInd = showInd;
		this.isShow = null;
	}

	
	
	public int getDisplayOrder()
	{
		return displayOrder;
	}


	
	public void setDisplayOrder(int displayOrder)
	{
		this.displayOrder = displayOrder;
	}


	
	public String getDisplayType()
	{
		return displayType;
	}


	
	public void setDisplayType(String displayType)
	{
		this.displayType = displayType;
	}


	public String getItemName()
	{
		if (pk.getItemCode().equals("TAB_OUTPUT")) {
			itemName = "Research Outputs";
		}
		if (pk.getItemCode().equals("TAB_PROJECT")) {
			itemName = "Projects";
		}
		if (pk.getItemCode().equals("TAB_AWARD")) {
			itemName = "Prizes and Awards";
		}
		if (pk.getItemCode().equals("TAB_PATENT")) {
			itemName = "Patents";
		}
		if (pk.getItemCode().equals("TAB_KT")) {
			itemName = "KT Activities";
		}
		if (pk.getItemCode().equals("TAB_EMPLOYMENT")) {
			itemName = "Career Overview";
		}
		return itemName;
	}

	
	public void setItemName(String itemName)
	{
		this.itemName = itemName;
	}


	
	public void setItemIcon(String itemIcon)
	{
		this.itemIcon = itemIcon;
	}


	public String getItemIcon()
	{
		if (pk.getItemCode().equals("TAB_OUTPUT")) {
			itemIcon = "fa-chart-pie";
		}
		if (pk.getItemCode().equals("TAB_PROJECT")) {
			itemIcon = "fa-project-diagram";
		}
		if (pk.getItemCode().equals("TAB_AWARD")) {
			itemIcon = "fa-trophy";
		}
		if (pk.getItemCode().equals("TAB_PATENT")) {
			itemIcon = "fa-award";
		}
		if (pk.getItemCode().equals("TAB_KT")) {
			itemIcon = "fa-chalkboard-user";
		}
		if (pk.getItemCode().equals("TAB_EMPLOYMENT")) {
			itemIcon = "fa-history";
		}
		return itemIcon;
	}
	
	public Boolean getIsShow()
	{
		if (showInd.equals("Y")){
			isShow = true;
		}else {
			isShow = false;
		}
		return isShow;
	}

	public void setIsShow(Boolean isShow)
	{
		this.isShow = isShow;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffProfileDisplay other = (StaffProfileDisplay) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "StaffProfileDisplay [pk=" + pk + ", itemType=" + itemType + ", showInd=" + showInd + ", displayOrder="
				+ displayOrder + ", displayType=" + displayType + "]";
	}



}