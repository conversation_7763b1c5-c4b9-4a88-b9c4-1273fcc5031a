package hk.eduhk.rich.entity.staff;

import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;

@Entity
@Table(name = "RH_Z_PURE_MAPPING")
@SuppressWarnings("serial")
public class PureMap extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PureMap.class.toString());
	
	@Id
	@Column(name = "PURE_ID")
	private Integer pure_id;		
	
	@Column(name = "PID")
	private Integer pid;	
	
	@Column(name = "PURE_UUID")
	private String pure_uuid;

	@Column(name = "PURE_EMAIL")
	private String pure_email;

	@Column(name = "PURE_LAST_NAME")
	private String pure_last_name;
	
	@Column(name = "PURE_FIRST_NAME")
	private String pure_first_name;
	
	@Column(name = "PURE_SOURCE_ID")
	private String pure_source_id;

	@Column(name = "PURE_VISIBILITY")
	private String pure_visibility;

	
	public Integer getPure_id()
	{
		return pure_id;
	}

	
	public void setPure_id(Integer pure_id)
	{
		this.pure_id = pure_id;
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public String getPure_uuid()
	{
		return pure_uuid;
	}

	
	public void setPure_uuid(String pure_uuid)
	{
		this.pure_uuid = pure_uuid;
	}

	
	public String getPure_email()
	{
		return pure_email;
	}

	
	public void setPure_email(String pure_email)
	{
		this.pure_email = pure_email;
	}

	
	public String getPure_last_name()
	{
		return pure_last_name;
	}

	
	public void setPure_last_name(String pure_last_name)
	{
		this.pure_last_name = pure_last_name;
	}

	
	public String getPure_first_name()
	{
		return pure_first_name;
	}

	
	public void setPure_first_name(String pure_first_name)
	{
		this.pure_first_name = pure_first_name;
	}

	
	public String getPure_source_id()
	{
		return pure_source_id;
	}

	
	public void setPure_source_id(String pure_source_id)
	{
		this.pure_source_id = pure_source_id;
	}

	
	public String getPure_visibility()
	{
		return pure_visibility;
	}

	
	public void setPure_visibility(String pure_visibility)
	{
		this.pure_visibility = pure_visibility;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pure_id == null) ? 0 : pure_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PureMap other = (PureMap) obj;
		if (pure_id == null)
		{
			if (other.pure_id != null)
				return false;
		}
		else if (!pure_id.equals(other.pure_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PureMap [pure_id=" + pure_id + ", pid=" + pid + ", pure_uuid=" + pure_uuid + ", pure_email="
				+ pure_email + ", pure_last_name=" + pure_last_name + ", pure_first_name=" + pure_first_name
				+ ", pure_source_id=" + pure_source_id + ", pure_visibility=" + pure_visibility + "]";
	}



}
