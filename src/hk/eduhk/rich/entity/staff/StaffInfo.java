package hk.eduhk.rich.entity.staff;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;

@Entity
@Table(name = "RH_P_STAFF_INFO")
@SuppressWarnings("serial")
public class StaffInfo extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(StaffInfo.class.toString());
		
	@Id
	@Column(name = "pid")
	private int pid;

	@Column(name = "staff_number")
	private String staff_number;

	@Column(name = "photo")
	private byte[] photo;

	@Column(name = "acad_staff_ind")
	private String acad_staff_ind;

	@Column(name = "profile")
	private String profile;

	@Column(name = "research_interest")
	private String research_interest;

	@Column(name = "ext_appt")
	private String ext_appt;

	@Column(name = "oth_activity")
	private String oth_activity;
	
	@Column(name = "url")
	private String url;

	@Column(name = "teaching_interest")
	private String teaching_interest;

	@Column(name = "orcid")
	private String orcid;

	@Column(name = "scopus_id_1")
	private String scopus_id_1;

	@Column(name = "scopus_id_2")
	private String scopus_id_2;

	@Column(name = "researcherid")
	private String researcherid;
	
	@Column(name = "sdg_code")
	private String sdg_code;
	
	@Transient
	private List<String> sdg_list;
	
	@Transient
	private String sdg_info;
	
	@Transient
	private String photoFile;
	
	@Transient
	private String scopusId;
	
	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}

	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public byte[] getPhoto()
	{
		return photo;
	}

	
	public void setPhoto(byte[] photo)
	{
		this.photo = photo;
	}
	
	

	
	
	public String getSdg_code()
	{
		return sdg_code;
	}


	
	public void setSdg_code(String sdg_code)
	{
		this.sdg_code = sdg_code;
	}
	
	public String getSdg_info()
	{
		if (getSdg_list() != null) {
			List<String> sdg_info_list = new ArrayList<String>();
			
			for(String sdg_code : sdg_list)
				sdg_info_list.add(sdg_code + " - " +LookupValueDAO.getInstance().getLookupValue("SDG",sdg_code,"US").getDescription()) ;
			
			sdg_info = String.join("<br/>", sdg_info_list);
		}
		return sdg_info;
	}



	
	public void setSdg_info(String sdg_info)
	{
		this.sdg_info = sdg_info;
	}


	
	public List<String> getSdg_list()
	{
		if (sdg_code != null && sdg_list == null )
			sdg_list =  Stream.of(getSdg_code().split(",")).collect(Collectors.toList());
		
		return sdg_list;
	}


	
	public void setSdg_list(List<String> sdg_list)
	{
		this.sdg_list = sdg_list;
	}


	public String getAcad_staff_ind()
	{
		return acad_staff_ind;
	}

	
	public void setAcad_staff_ind(String acad_staff_ind)
	{
		this.acad_staff_ind = acad_staff_ind;
	}

	
	public String getProfile()
	{
		return profile;
	}

	
	public void setProfile(String profile)
	{
		this.profile = profile;
	}

	
	public String getResearch_interest()
	{
		return research_interest;
	}

	
	public void setResearch_interest(String research_interest)
	{
		this.research_interest = research_interest;
	}

	
	public String getExt_appt()
	{
		return ext_appt;
	}

	
	public void setExt_appt(String ext_appt)
	{
		this.ext_appt = ext_appt;
	}

	public String getOth_activity()
	{
		return oth_activity;
	}

	public void setOth_activity(String oth_activity)
	{
		this.oth_activity = oth_activity;
	}


	public String getUrl()
	{
		return url;
	}

	
	public void setUrl(String url)
	{
		this.url = url;
	}

	
	public String getTeaching_interest()
	{
		return teaching_interest;
	}

	
	public void setTeaching_interest(String teaching_interest)
	{
		this.teaching_interest = teaching_interest;
	}

	
	public String getOrcid()
	{
		return orcid;
	}

	
	public void setOrcid(String orcid)
	{
		this.orcid = orcid;
	}

	
	public String getScopus_id_1()
	{
		return scopus_id_1;
	}

	
	public void setScopus_id_1(String scopus_id_1)
	{
		this.scopus_id_1 = scopus_id_1;
	}

	
	public String getScopus_id_2()
	{
		return scopus_id_2;
	}

	
	public void setScopus_id_2(String scopus_id_2)
	{
		this.scopus_id_2 = scopus_id_2;
	}

	
	public String getResearcherid()
	{
		return researcherid;
	}

	
	public void setResearcherid(String researcherid)
	{
		this.researcherid = researcherid;
	}

	public String getScopusId()
	{
		String scopusId = (getScopus_id_1()!=null && getScopus_id_2()==null)?getScopus_id_1():null;
		scopusId = (getScopus_id_1()!=null && getScopus_id_2()!=null)?getScopus_id_1()+", "+getScopus_id_2():scopusId;
		scopusId = (getScopus_id_1()==null && getScopus_id_2()!=null)?getScopus_id_2():scopusId;
		return scopusId;
	}
	
	public String getPhotoFile()
	{
		if (photoFile == null) {
			if (getPhoto() != null) {
				photoFile = Base64.getEncoder().encodeToString(getPhoto());
			}
		}
		return photoFile;
	}

	
	
	public void setPhotoFile(String photoFile)
	{
		this.photoFile = photoFile;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + pid;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffInfo other = (StaffInfo) obj;
		if (pid != other.pid)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "StaffInfo [pid=" + pid + ", staff_number=" + staff_number + ", photo=" + Arrays.toString(photo)
				+ ", acad_staff_ind=" + acad_staff_ind + ", profile=" + profile + ", research_interest="
				+ research_interest + ", ext_appt=" + ext_appt + ", oth_activity=" + oth_activity + ", url=" + url
				+ ", teaching_interest=" + teaching_interest + ", orcid=" + orcid + ", scopus_id_1=" + scopus_id_1
				+ ", scopus_id_2=" + scopus_id_2 + ", researcherid=" + researcherid + ", sdg_code=" + sdg_code
				+ ", sdg_list=" + sdg_list + ", sdg_info=" + sdg_info + ", photoFile=" + photoFile + ", scopusId="
				+ scopusId + "]";
	}


	

}
