package hk.eduhk.rich.entity.staff;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessControl;
import hk.eduhk.rich.access.Role;
import hk.eduhk.rich.entity.importRI.ImportRICA;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;


@Entity
@Table(name = "RH_Z_SEC_DATA_USER")
public class SecDataUser extends UserPersistenceObject
{
	public static final String allData = "ALL_DATA";
	
	public static Logger logger = Logger.getLogger(SecDataUser.class.toString());
	
	@EmbeddedId
	private SecDataUser_PK pk = new SecDataUser_PK();

	@Transient
	private StaffIdentity staff = null;
	
	@Transient
	private PureDept dept = null;
	
	@Transient
	private ImportRICA ca = null;
	
	public SecDataUser_PK getPk()
	{
		return pk;
	}

	public void setPk(SecDataUser_PK pk)
	{
		this.pk = pk;
	}

	public String getUser_id()
	{
		return getPk().getUser_id();
	}
	
	
	public void setUser_id(String user_id)
	{
		getPk().setUser_id(user_id);
	}

	
	public String getData_code()
	{
		return getPk().getData_code();
	}

	
	public void setData_code(String data_code)
	{
		getPk().setData_code(data_code);
	}
	
	
	public StaffIdentity getStaff()
	{
		if (staff == null && getUser_id() != null)
		{
			StaffDAO dao = StaffDAO.getInstance();
			staff = dao.getStaffDetailsByUserId(getUser_id());
		}
		return staff;
	}

	public PureDept getDept()
	{
		if (dept == null && getData_code() != null)
		{
			StaffDAO dao = StaffDAO.getInstance();
			dept = dao.getPureDept(getData_code());
		}
		
		return dept;
	}
	
	public ImportRICA getCa()
	{
		if (ca == null && getData_code() != null)
		{
			ImportRIDAO dao = ImportRIDAO.getInstance();
			ca = dao.getCAByPK(getData_code());
		}
		
		return ca;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SecDataUser other = (SecDataUser) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SecDataUser [pk=" + pk + "]";
	}



	

}
