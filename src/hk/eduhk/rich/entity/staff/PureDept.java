package hk.eduhk.rich.entity.staff;

import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;

@Entity
@Table(name = "RH_P_DEPARTMENT")
@SuppressWarnings("serial")
public class PureDept extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PureDept.class.toString());
	
	@Id
	@Column(name = "department_code")
	private String department_code;	
	
	@Column(name = "department_name")
	private String department_name;		
	
	@Column(name = "department_abbr")
	private String department_abbr;

	@Column(name = "department_chi_name")
	private String department_chi_name;

	@Column(name = "url")
	private String url;
	
	@Column(name = "pure_source_id")
	private String pure_source_id;
	
	@Column(name = "parent_source_id")
	private String parent_source_id;

	@Column(name = "date_from")
	private Date date_from;
	
	@Column(name = "date_to")
	private Date date_to;
	
	
	
	public String getDepartment_name()
	{
		return department_name;
	}


	public void setDepartment_name(String department_name)
	{
		this.department_name = department_name;
	}


	public String getDepartment_code()
	{
		return department_code;
	}

	
	public void setDepartment_code(String department_code)
	{
		this.department_code = department_code;
	}

	
	public String getDepartment_abbr()
	{
		return department_abbr;
	}

	
	public void setDepartment_abbr(String department_abbr)
	{
		this.department_abbr = department_abbr;
	}

	
	public String getDepartment_chi_name()
	{
		return department_chi_name;
	}

	
	public void setDepartment_chi_name(String department_chi_name)
	{
		this.department_chi_name = department_chi_name;
	}

	
	public String getUrl()
	{
		return url;
	}

	
	public void setUrl(String url)
	{
		this.url = url;
	}

	
	public String getPure_source_id()
	{
		return pure_source_id;
	}

	
	public void setPure_source_id(String pure_source_id)
	{
		this.pure_source_id = pure_source_id;
	}

	
	public String getParent_source_id()
	{
		return parent_source_id;
	}

	
	public void setParent_source_id(String parent_source_id)
	{
		this.parent_source_id = parent_source_id;
	}


	
	public Date getDate_from()
	{
		return date_from;
	}


	
	public void setDate_from(Date date_from)
	{
		this.date_from = date_from;
	}


	
	public Date getDate_to()
	{
		return date_to;
	}


	
	public void setDate_to(Date date_to)
	{
		this.date_to = date_to;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((department_code == null) ? 0 : department_code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PureDept other = (PureDept) obj;
		if (department_code == null)
		{
			if (other.department_code != null)
				return false;
		}
		else if (!department_code.equals(other.department_code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PureDept [department_code=" + department_code + ", department_name=" + department_name
				+ ", department_abbr=" + department_abbr + ", department_chi_name=" + department_chi_name + ", url="
				+ url + ", pure_source_id=" + pure_source_id + ", parent_source_id=" + parent_source_id + "]";
	}


}
