package hk.eduhk.rich.entity.staff;

import java.net.ConnectException;
import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.persistence.OptimisticLockException;

import org.apache.commons.collections.CollectionUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.UserRole;
import hk.eduhk.rich.banner.BanPerson;
import hk.eduhk.rich.banner.BannerLookupDAO;
import hk.eduhk.rich.email.tmpl.AsstTemplateConverter;
import hk.eduhk.rich.entity.email.EmailService;

@ManagedBean(name = "asstView")
@ViewScoped
@SuppressWarnings("serial")
public class AssistantView extends BaseView
{
	private static Logger logger = Logger.getLogger(Assistant.class.getName());
	private List<StaffIdentity> acadStaffList = null;
	private List<Assistant> acadStaffListByAsst = null;
	private List<Assistant> approvedAcadStaffListByAsst = null;
	private List<Assistant> asstListByAcadStaff = null;
	private Assistant selectedAsst;
	private Assistant selectedRecord;
	private String selectedAcadStaff;
	private String paramPid;
	
	private ObjectMapper objMapper = new ObjectMapper();
	AssistantDAO dao = AssistantDAO.getInstance();
	
	public List<StaffIdentity> getAcadStaffList()
	{
		if (acadStaffList == null) {
			StaffDAO dao = StaffDAO.getInstance();
			acadStaffList = dao.getAcadStaffList("Y");
		}
		return acadStaffList;
	}

	public List<Assistant> getAcadStaffListByAsst()
	{
		if (acadStaffListByAsst == null) {
			acadStaffListByAsst = dao.getAcadStaffListByAsstId(getCurrentUserId(), "");
		}
		return acadStaffListByAsst;
	}

	public List<Assistant> getApprovedAcadStaffListByAsst()
	{
		if (approvedAcadStaffListByAsst == null) {
			approvedAcadStaffListByAsst = dao.getAcadStaffListByAsstId(getCurrentUserId(), "APPROVED");
		}
		return approvedAcadStaffListByAsst;
	}
	
	public List<Assistant> getAsstListByAcadStaff()
	{
		if (asstListByAcadStaff == null) {
			AssistantDAO dao = AssistantDAO.getInstance();
			asstListByAcadStaff = dao.getAsstListByAcadStaff(Integer.valueOf(getParamPid()));
		}
		return asstListByAcadStaff;
	}

	public String getSelectedAcadStaff()
	{
		return selectedAcadStaff;
	}

	public void setSelectedAcadStaff(String selectedAcadStaff)
	{
		this.selectedAcadStaff = selectedAcadStaff;
	}

	
	public Assistant getSelectedAsst()
	{
		// To avoid NullPointerException
		if (selectedAsst == null) {
			selectedAsst = new Assistant();
		}
		return selectedAsst;
	}

	
	public void setSelectedAsst(Assistant selectedAsst)
	{
		this.selectedAsst = selectedAsst;
	}

	public Assistant getSelectedRecord()
	{
		return selectedRecord;
	}

	
	public void setSelectedRecord(Assistant selectedRecord)
	{
		this.selectedRecord = selectedRecord;
	}
	

	public String getAcadStaffName(String value)
	{
		String result;
		StaffDAO dao = StaffDAO.getInstance();
		StaffIdentity staff = dao.getStaffDetailsByPid(Integer.parseInt(value));
		result = (staff != null)?staff.getFullname():"";
		return result;
	}
	
	//asst request, send to acad staff
	public String updateRequestAsst()
	{
		String message;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedAcadStaff != null) {
    		//check duplicate
    		for (Assistant a:acadStaffListByAsst) {
    			if (selectedAcadStaff.equals(String.valueOf(a.getPk().getPid()))) {
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), "Request ");
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    		}
    		if (fCtx.getMessageList().isEmpty()) {
	    		try {
	    			String asstName = getUserName(getCurrentUserId());
	    			
	    			Assistant tmpAsst = new Assistant();
	    			tmpAsst.getPk().setPid(Integer.parseInt(selectedAcadStaff));
	    			tmpAsst.getPk().setAssistant_id(getCurrentUserId());
	    			tmpAsst.setAssistant_name(asstName);
	    			tmpAsst.setAccess_right("EDIT_PUBLISH");
	    			tmpAsst.setStatus("REQUESTED");
	    			tmpAsst.setUserstamp(getLoginUserId());
	    			AssistantDAO dao = AssistantDAO.getInstance();
	    			dao.updateAsst(tmpAsst);
	    			
	    			//send email
	    			StaffDAO staffDao = StaffDAO.getInstance();
	    			StaffIdentity s = staffDao.getStaffDetailsByPid(Integer.parseInt(selectedAcadStaff));
	    			tmpAsst.setAcadStaffName(s.getFullname());
	    			SysParamDAO pdao = SysParamDAO.getCacheInstance();
	    			String emailTmpl = pdao.getSysParamValueByCode(SysParam.PARAM_EMAIL_TMPL_ASST_REQUEST);
	    			sendEmail(tmpAsst, s.getEmail(), emailTmpl);
	    			
	    			message = "msg.success.create.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Request");
	    			FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
		    		FacesContext.getCurrentInstance().addMessage(null, msg);
		    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	    		}
	    		catch (IllegalStateException ise)
	    		{
	    			logger.log(Level.WARNING, "", ise);
	    			String param = bundle.getString("Request ");
	    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
	    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
	    		}
	    		catch (OptimisticLockException ole)
	    		{
	    			message = bundle.getString("msg.err.optimistic.lock");
	    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
	    		}
	        	catch (Exception e)
	    		{
	    			logger.log(Level.WARNING, "", e);
	    			message = bundle.getString("msg.err.unexpected");
	    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
	    		}
    		}
    	}
		return redirect("asstRequest");
	}
	//acad staff add asst
	public String updateAppForm() throws ConnectException, JsonProcessingException
	{
		String message;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedAsst != null) {
    		
    		//check assistant id is null
    		if (selectedAsst.getPk().getAssistant_id() == null) {
    			message = "Please enter Assistant ID";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		
    		//check assistant name
    		String asstName = getUserName(selectedAsst.getPk().getAssistant_id());
    		if (asstName.equals("")) {
    			message = selectedAsst.getPk().getAssistant_id() + " is not a valid id";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		selectedAsst.setAssistant_name(asstName);
    		
    		//check valid academic staff
			StaffDAO staffDao = StaffDAO.getInstance();
			StaffIdentity s = null;
			if(getParamPid() == null)
				s = staffDao.getStaffDetailsByUserId(getCurrentUserId());
			else
				s = staffDao.getStaffDetailsByPid(Integer.parseInt(getParamPid()));
			if (s != null) {
				selectedAsst.getPk().setPid(s.getPid());
			}else {
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "You are not academic staff", ""));
			}
			
    		//check duplicate assistant
    		for (Assistant a:asstListByAcadStaff) {
    			if (selectedAsst.getPk().getAssistant_id().equals(a.getPk().getAssistant_id())){
    					message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), "Assistant ");
	    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    		}
    		
    		if (fCtx.getMessageList().isEmpty()) {
    			selectedAsst.setStatus("APPROVED");
    			updateAsst(selectedAsst, true);
    			updateAsstUserRole(selectedAsst, true);
    		}
			
    	}
		return redirect("manageAsst.xhtml?pid="+paramPid);
	}	
	
	public String approveAsst(Assistant obj) {
		if (obj != null) {
			obj.setStatus("APPROVED");
			updateAsst(obj, true);
			updateAsstUserRole(obj, true);
		}
		return redirect("manageAsst.xhtml?pid="+paramPid);
	}
	
	public String enableAsst(Assistant obj) {
		if (obj != null) {
			obj.setStatus("APPROVED");
			updateAsst(obj, false);
			updateAsstUserRole(obj, true);
		}
		return redirect("manageAsst.xhtml?pid="+paramPid);
	}	
	
	public String disableAsst(Assistant obj) {
		if (obj != null) {
			obj.setStatus("DISABLED");
			updateAsst(obj, false);
			updateAsstUserRole(obj, false);
		}
		return redirect("manageAsst.xhtml?pid="+paramPid);
	}	
	
	public String rejectAsst(Assistant obj) {
		if (obj != null) {
			obj.setStatus("REJECTED");
			updateAsst(obj, true);
			updateAsstUserRole(obj, false);
		}
		return redirect("manageAsst.xhtml?pid="+paramPid);
	}		
	
	public String removeAsst(Assistant obj) {
		if (obj != null) {
			deleteAsst(obj);
			updateAsstUserRole(obj, false);
		}
		return redirect("manageAsst.xhtml?pid="+paramPid);
	}	
	
	//Apply for assistant page
	public String removeAsstRequest(Assistant obj) {
		if (obj != null) {
			deleteAsst(obj);
		}
		return redirect("asstRequest");
	}	
	
	public void updateAsst(Assistant obj, boolean sentEmail) {
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (obj != null) {
			try {
				obj.setAccess_right("EDIT_PUBLISH");
				obj.setUserstamp(getLoginUserId());
				AssistantDAO dao = AssistantDAO.getInstance();
				dao.updateAsst(obj);
				
    			//send email
				if (sentEmail) {
					StaffDAO staffDao = StaffDAO.getInstance();
					StaffIdentity s = staffDao.getStaffDetailsByPid(obj.getPk().getPid());
					if (s != null) {
						obj.setAcadStaffName(s.getFullname());
					}
	    			SysParamDAO pdao = SysParamDAO.getCacheInstance();
	    			String emailTmpl = pdao.getSysParamValueByCode(SysParam.PARAM_EMAIL_TMPL_ASST_ACK);
	    			sendEmail(obj, obj.getPk().getAssistant_id()+"@eduhk.hk", emailTmpl);
				}
				message = "msg.success.action.x";
				message = MessageFormat.format(getResourceBundle().getString(message), obj.getAssistant_name(), obj.getStatus().toLowerCase());
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (IllegalStateException ise)
			{
				logger.log(Level.WARNING, "", ise);
				String param = bundle.getString(obj.getPk().getAssistant_id());
				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
			catch (OptimisticLockException ole)
			{
				message = bundle.getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
	    	catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
				message = bundle.getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
    	}
	}
	
	public void deleteAsst(Assistant obj) {
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
		if (obj != null) {
			try {
				String asst = obj.getAssistant_name();
				dao.deleteAsst(obj);
				message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), asst);
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	    		FacesContext.getCurrentInstance().addMessage(null, msg);
	    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (OptimisticLockException ole)
			{
				message = bundle.getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
	    	catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
				message = bundle.getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
	}
	
    public String getUserName(String userId) {
    	BannerLookupDAO lookupDAO = BannerLookupDAO.getInstance();
    	BanPerson selectedPerson = lookupDAO.getActivePersonByUserId(userId);
    	String userName = (selectedPerson!=null)?selectedPerson.getName():"";
		return userName;
    }
    
    public void sendEmail(Assistant obj, String email, String emailTmpl) throws ConnectException, JsonProcessingException {
		EmailService emailService = new EmailService();
		AsstTemplateConverter tmplConverter = new AsstTemplateConverter();
		String json;
		json = objMapper.writeValueAsString(tmplConverter.getTemplateConverter().convertParamList(obj));
		List<String> resList = emailService.sendEmail(emailTmpl, email, json);
		//index 0 is the response code; index 1 is the response details
		int resCode = Integer.parseInt(resList.get(0));
		String response = resList.get(1);
		if (resCode == 200)
		{
			logger.log(Level.INFO, response);
		}
		else
		{
			// Response code not equals to 200
			throw new ConnectException(); 
		}
    }
    
    public void updateAsstUserRole(Assistant obj, Boolean isAsst) {
    	AccessDAO aDao = AccessDAO.getInstance();
		UserRole oldUser = aDao.getUserRoleByUserId(obj.getPk().getAssistant_id(), "asst");
    	if (isAsst) {
    		if (oldUser == null) {
    			UserRole newUser = new UserRole();
            	newUser.setUserId(obj.getPk().getAssistant_id());
            	newUser.setRoleId("asst");
            	newUser.setUserstamp(getLoginUserId());
        		dao.updateEntity(newUser);
    		}
    	}else {
    		approvedAcadStaffListByAsst = dao.getAcadStaffListByAsstId(obj.getPk().getAssistant_id(), "APPROVED");
    		if (CollectionUtils.isEmpty(approvedAcadStaffListByAsst)) {
				if (oldUser != null) {
					dao.deleteEntity(UserRole.class, oldUser.getPk());
				}
    		}
    	}
    	
    }
    
    public String getParamPid()
	{
		if (paramPid == null) {
			StaffDAO dao = StaffDAO.getInstance();
			StaffIdentity sIdentity = dao.getStaffDetailsByUserId(getCurrentUserId());
			this.paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
		}
		return paramPid;
	}


	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}
}


