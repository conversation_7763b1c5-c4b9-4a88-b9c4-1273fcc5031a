package hk.eduhk.rich.entity.staff;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_P_STAFF_EMPLOYMENT_PAST")
@SuppressWarnings("serial")
public class StaffPast extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(StaffPast.class.toString());
		
	@Id
	@Column(name = "staff_number")
	private String staff_number;

	@Column(name = "pid")
	private int pid;

	@Column(name = "cn")
	private String cn;

	@Column(name = "title")
	private String title;

	@Column(name = "surname")
	private String surname;

	@Column(name = "othername")
	private String othername;

	@Column(name = "fullname")
	private String fullname;

	@Column(name = "chinesename")
	private String chinesename;

	@Column(name = "department_code")
	private String dept_code;

	@Column(name = "rank_code")
	private String rank_code;

	@Column(name = "gender")
	private String gender;

	@Transient
	private String fullname_display;
	
	@Transient
	private String fullname_save;
	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}

	
	public String getCn()
	{
		return cn;
	}

	
	public void setCn(String cn)
	{
		this.cn = cn;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getSurname()
	{
		return surname;
	}

	
	public void setSurname(String surname)
	{
		this.surname = surname;
	}

	
	public String getOthername()
	{
		return othername;
	}

	
	public void setOthername(String othername)
	{
		this.othername = othername;
	}

	
	public String getFullname()
	{
		return fullname;
	}

	
	public void setFullname(String fullname)
	{
		this.fullname = fullname;
	}

	
	public String getChinesename()
	{
		return chinesename;
	}

	
	public void setChinesename(String chinesename)
	{
		this.chinesename = chinesename;
	}

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getRank_code()
	{
		return rank_code;
	}

	
	public void setRank_code(String rank_code)
	{
		this.rank_code = rank_code;
	}

	
	public String getGender()
	{
		return gender;
	}

	
	public void setGender(String gender)
	{
		this.gender = gender;
	}


	
	public String getFullname_display()
	{
		if (fullname_display == null) {
			if (getFullname_save() != null) {
				fullname_display = getFullname_save();
				if (getChinesename() != null) {
					fullname_display = fullname_display +" " + getChinesename();
				}
			}
		}
		return fullname_display;
	}

	
	public void setFullname_display(String fullname_display)
	{
		this.fullname_display = fullname_display;
	}


	
	public void setFullname_save(String fullname_save)
	{
		this.fullname_save = fullname_save;
	}


	public String getFullname_save()
	{
		if (fullname_save == null) {
			if (getSurname() != null) {
				fullname_save = getSurname();
			}
			if (getOthername() != null) {
				String otherName = getOthername();
				otherName = capitalizeFully(otherName);
				fullname_save = fullname_save + ", " + otherName;
			}
		}
		return fullname_save;
	}
	
	private String capitalizeFully(String str) {
	    StringBuilder sb = new StringBuilder();
	    boolean cnl = true; // <-- capitalize next letter.
	    for (char c : str.toCharArray()) {
	        if (cnl && Character.isLetter(c)) {
	            sb.append(Character.toUpperCase(c));
	            cnl = false;
	        } else {
	            sb.append(Character.toLowerCase(c));
	        }
	        if (Character.isWhitespace(c)) {
	            cnl = true;
	        }
	    }
	    return sb.toString();
	}
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((staff_number == null) ? 0 : staff_number.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffPast other = (StaffPast) obj;
		if (staff_number == null)
		{
			if (other.staff_number != null)
				return false;
		}
		else if (!staff_number.equals(other.staff_number))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "StaffPast [staff_number=" + staff_number + ", pid=" + pid + ", cn=" + cn + ", title=" + title
				+ ", surname=" + surname + ", othername=" + othername + ", fullname=" + fullname + ", chinesename="
				+ chinesename + ", dept_code=" + dept_code + ", rank_code=" + rank_code + ", gender=" + gender
				+ ", fullname_display=" + fullname_display + ", fullname_save=" + fullname_save + "]";
	}

}
