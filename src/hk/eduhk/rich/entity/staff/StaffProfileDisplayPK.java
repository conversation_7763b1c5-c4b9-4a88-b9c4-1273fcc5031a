package hk.eduhk.rich.entity.staff;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import org.json.simple.JSONObject;
import hk.eduhk.rich.entity.BaseVO;


@Embeddable
public class StaffProfileDisplayPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="PID")
	private int pid;
	
	@Column(name="ITEM_CODE")
	private String itemCode;

	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}

	
	public String getItemCode()
	{
		return itemCode;
	}

	
	public void setItemCode(String itemCode)
	{
		this.itemCode = itemCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((itemCode == null) ? 0 : itemCode.hashCode());
		result = prime * result + pid;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffProfileDisplayPK other = (StaffProfileDisplayPK) obj;
		if (itemCode == null)
		{
			if (other.itemCode != null)
				return false;
		}
		else if (!itemCode.equals(other.itemCode))
			return false;
		if (pid != other.pid)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "StaffProfileDisplayPK [pid=" + pid + ", itemCode=" + itemCode + "]";
	}

  
}