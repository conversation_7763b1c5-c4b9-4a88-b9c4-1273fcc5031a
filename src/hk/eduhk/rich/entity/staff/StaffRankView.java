package hk.eduhk.rich.entity.staff;

import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;

@ManagedBean(name = "staffRankView")
@ViewScoped
@SuppressWarnings("serial")
public class StaffRankView extends BaseView
{
	private static Logger logger = Logger.getLogger(StaffRank.class.getName());
	private List<StaffRank> rankList = null;
	private StaffRank selectedRank;
	private StaffRank removeRank;

	
	public void reloadRankList() {
		rankList = null;
	}
	
	public List<StaffRank> getRankList()
	{
		if (rankList == null)
		{
			StaffDAO dao = StaffDAO.getInstance();
			rankList = dao.getRankList();
		}
		return rankList;
	}
	
	
	public StaffRank getSelectedRank()
	{
		return selectedRank;
	}

	
	public void setSelectedRank(StaffRank selectedRank)
	{
		this.selectedRank = selectedRank;
	}

	public void onRowEdit(RowEditEvent<StaffRank> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
        	try {
        		StaffDAO dao = StaffDAO.getInstance();

    			//Check rank code is unique
    			int count = 0;
    			for (StaffRank r: rankList){
    				if (event.getObject().getRank_code().equals(r.getRank_code())) {
    					count++;
    				}
    			}
    			if (count > 1) {
    				isDuplicateKey = true;
    			}
    			
    			if (isDuplicateKey) {
    				String param = "Rank code";
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    			
    			////Check data
    			if (event.getObject().getRank_code().isEmpty()) {
        			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Rank code cannot be null", ""));
        		}
     
    			if (event.getObject().getRank_code().length() > 20) {
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Rank code is too long", ""));
    			}
    			
    			if (event.getObject().getRank_full().length() > 80) {
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Rank description is too long", ""));
    			}
    			
    			//Update rank
        		if (fCtx.getMessageList().isEmpty()) {
        			event.getObject().setUserstamp(getLoginUserId());
        			dao.updateRank(event.getObject());
        			if (removeRank != null) {
         				dao.deleteRank(removeRank.getRank_code());
         				removeRank = null;
         			}
        			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
        			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getRank_code());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedRank = null;
	        		rankList = null;
        		}
        	}
        	catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("Rank");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
	            
	        
    }
	
	public void onRowCancel(RowEditEvent<StaffRank> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getRank_code()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();

 
    }
    
    public void onAddNew() {
    	StaffRank newRank = new StaffRank();
    	rankList.add(0, newRank);
    }
   
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		StaffDAO dao = StaffDAO.getInstance();
    		removeRank =  dao.getRankByCode((String)event.getOldValue());
    	}
    }    
    
    public void deleteRank() {
    	if (selectedRank != null) {
    		try {
    			if (selectedRank.getRank_code() != null) {
			    	StaffDAO dao = StaffDAO.getInstance();
			    	dao.deleteRank(selectedRank.getRank_code());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedRank.getRank_code());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		rankList.remove(selectedRank);
		        selectedRank = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedRank.getRank_code());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
}
