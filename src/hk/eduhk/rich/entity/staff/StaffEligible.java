package hk.eduhk.rich.entity.staff;

import java.util.Date;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_S_ELIGIBLE_STAFF_LIST")
@SuppressWarnings("serial")
public class StaffEligible extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(StaffEligible.class.toString());
		
	@EmbeddedId
	private StaffEligible_PK pk = new StaffEligible_PK();

	@Column(name = "REPORTING_PERIOD")
	private String reporting_period;
	
	@Column(name = "ASSIGNMENT_NUMBER")
	private String assignment_number;
	
	@Column(name = "HUSBAND_LAST_NAME")
	private String husband_last_name;
	
	@Column(name = "TITLE")
	private String title;
	
	@Column(name = "LAST_NAME")
	private String last_name;
	
	@Column(name = "FIRST_NAME")
	private String first_name;
	
	@Column(name = "CHINESE_NAME")
	private String chinese_name;
	
	@Column(name = "DEPARTMENT_CODE")
	private String dept_code;
	
	@Column(name = "POST_RANK_CODE")
	private String post_rank_code;
	
	@Column(name = "STAFF_GRADE")
	private String staff_grade;
	
	@Column(name = "DCC_PERCENTAGE")
	private Double dcc_percentage;
	
	@Column(name = "TERMS_OF_APPOINTMENT")
	private String terms_of_appointment;
	
	@Column(name = "POST_TYPE")
	private String post_type;
	
	@Column(name = "LAST_TERMINATION_DATE")
	private Date last_termination_date;
	
	@Column(name = "PRINCIPAL_ASSIGNMENT_FLAG")
	private String principal_assignment_flag;
	
	@Column(name = "MAPPED_DEPT")
	private String mapped_dept;

	@Column(name = "START_PERIOD")
	private String start_period;
	
	@Column(name = "END_PERIOD")
	private String end_period;
	
	@Column(name = "REMARKS")
	private String remarks;
	
	public StaffEligible_PK getPk()
	{
		return pk;
	}

	
	public void setPk(StaffEligible_PK pk)
	{
		this.pk = pk;
	}

	
	public String getReporting_period()
	{
		return reporting_period;
	}

	
	public void setReporting_period(String reporting_period)
	{
		this.reporting_period = reporting_period;
	}

	
	public String getAssignment_number()
	{
		return assignment_number;
	}

	
	public void setAssignment_number(String assignment_number)
	{
		this.assignment_number = assignment_number;
	}

	
	
	public String getTitle()
	{
		return title;
	}


	
	public void setTitle(String title)
	{
		this.title = title;
	}


	public String getHusband_last_name()
	{
		return husband_last_name;
	}

	
	public void setHusband_last_name(String husband_last_name)
	{
		this.husband_last_name = husband_last_name;
	}

	
	public String getLast_name()
	{
		return last_name;
	}

	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}

	
	public String getFirst_name()
	{
		return first_name;
	}

	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}

	
	public String getChinese_name()
	{
		return chinese_name;
	}

	
	public void setChinese_name(String chinese_name)
	{
		this.chinese_name = chinese_name;
	}

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getPost_rank_code()
	{
		return post_rank_code;
	}

	
	public void setPost_rank_code(String post_rank_code)
	{
		this.post_rank_code = post_rank_code;
	}

	
	public String getStaff_grade()
	{
		return staff_grade;
	}

	
	public void setStaff_grade(String staff_grade)
	{
		this.staff_grade = staff_grade;
	}

	
	public Double getDcc_percentage()
	{
		return dcc_percentage;
	}

	
	public void setDcc_percentage(Double dcc_percentage)
	{
		this.dcc_percentage = dcc_percentage;
	}

	
	public String getTerms_of_appointment()
	{
		return terms_of_appointment;
	}

	
	public void setTerms_of_appointment(String terms_of_appointment)
	{
		this.terms_of_appointment = terms_of_appointment;
	}

	
	public String getPost_type()
	{
		return post_type;
	}

	
	public void setPost_type(String post_type)
	{
		this.post_type = post_type;
	}

	
	public Date getLast_termination_date()
	{
		return last_termination_date;
	}

	
	public void setLast_termination_date(Date last_termination_date)
	{
		this.last_termination_date = last_termination_date;
	}

	
	public String getPrincipal_assignment_flag()
	{
		return principal_assignment_flag;
	}

	
	public void setPrincipal_assignment_flag(String principal_assignment_flag)
	{
		this.principal_assignment_flag = principal_assignment_flag;
	}

	
	public String getMapped_dept()
	{
		return mapped_dept;
	}

	
	public void setMapped_dept(String mapped_dept)
	{
		this.mapped_dept = mapped_dept;
	}


	
	public String getStart_period()
	{
		return start_period;
	}


	
	public void setStart_period(String start_period)
	{
		this.start_period = start_period;
	}


	
	public String getEnd_period()
	{
		return end_period;
	}


	
	public void setEnd_period(String end_period)
	{
		this.end_period = end_period;
	}


	
	public String getRemarks()
	{
		return remarks;
	}


	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffEligible other = (StaffEligible) obj;
		return Objects.equals(pk, other.pk);
	}


	@Override
	public String toString()
	{
		return "StaffEligible [pk=" + pk + ", reporting_period=" + reporting_period + ", assignment_number="
				+ assignment_number + ", husband_last_name=" + husband_last_name + ", title=" + title + ", last_name="
				+ last_name + ", first_name=" + first_name + ", chinese_name=" + chinese_name + ", dept_code="
				+ dept_code + ", post_rank_code=" + post_rank_code + ", staff_grade=" + staff_grade
				+ ", dcc_percentage=" + dcc_percentage + ", terms_of_appointment=" + terms_of_appointment
				+ ", post_type=" + post_type + ", last_termination_date=" + last_termination_date
				+ ", principal_assignment_flag=" + principal_assignment_flag + ", mapped_dept=" + mapped_dept
				+ ", start_period=" + start_period + ", end_period=" + end_period + ", remarks=" + remarks + "]";
	}
}
