package hk.eduhk.rich.entity.staff;

import java.net.ConnectException;
import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;

@ManagedBean(name = "pureMapView")
@ViewScoped
@SuppressWarnings("serial")
public class PureMapView extends BaseView
{
	private static Logger logger = Logger.getLogger(PureMap.class.getName());
	private List<PureMap> pureMapList = null;
	private PureMap selectedPureMap;
	private PureMap removePureMap;
	
	StaffDAO dao = StaffDAO.getInstance();

	public void reloadList() {
		pureMapList = null;
	}
	
	public List<PureMap> getPureMapList()
	{
		if (pureMapList == null) {
			pureMapList = dao.getPureMapList();
		}
		return pureMapList;
	}

	
	public void setPureMapList(List<PureMap> pureMapList)
	{
		this.pureMapList = pureMapList;
	}

	
	public PureMap getSelectedPureMap()
	{
		return selectedPureMap;
	}

	
	public void setSelectedPureMap(PureMap selectedPureMap)
	{
		this.selectedPureMap = selectedPureMap;
	}
	
	public void onRowEdit(RowEditEvent<PureMap> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
     	try {

 			//Check uuid code is unique
 			int count = 0;
 			for (PureMap r: pureMapList){
 				if (event.getObject().getPure_id().equals(r.getPure_id())) {
 					count++;
 				}
 			}
 			if (count > 1) {
 				isDuplicateKey = true;
 			}
 			
 			if (isDuplicateKey) {
 				String param = "Pure ID";
 				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
 				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 			}

 			
 			//Update pure map
     		if (fCtx.getMessageList().isEmpty()) {
     			event.getObject().setUserstamp(getLoginUserId());
     			dao.updatePureMap(event.getObject());
     			if (removePureMap != null) {
      				dao.deletePureMap(removePureMap.getPure_id());
      				removePureMap = null;
      			}
     			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
     			message = MessageFormat.format(getResourceBundle().getString(message), "Pure ID: "+event.getObject().getPure_id());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedPureMap = null;
	        		pureMapList = null;
     		}
     	}
     	catch (IllegalStateException ise)
 		{
 			logger.log(Level.WARNING, "", ise);
 			String param = bundle.getString("Pure Map");
 			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
 		catch (OptimisticLockException ole)
 		{
 			message = bundle.getString("msg.err.optimistic.lock");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
     	catch (Exception e)
 		{
 			logger.log(Level.WARNING, "", e);
 			message = bundle.getString("msg.err.unexpected");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
	            
	        
 }
	
	public void onRowCancel(RowEditEvent<PureMap> event) {
     FacesMessage msg = new FacesMessage("Edit Cancelled", "Pure ID: "+String.valueOf(event.getObject().getPure_id()));
     FacesContext.getCurrentInstance().addMessage(null, msg);
 }

 public void onCellEdit(CellEditEvent event) {
     Object oldValue = event.getOldValue();
     Object newValue = event.getNewValue();


 }
 
 public void onAddNew() {
 	PureMap newObj = new PureMap();
 	pureMapList.add(0, newObj);
 }

 public void keyChangedListener(ValueChangeEvent event) {
 	if (event.getOldValue() != null) {
 		removePureMap =  dao.getPureMapById((Integer)event.getOldValue());
 	}
 }    
 
 public void deletePureMap() {
 	if (selectedPureMap != null) {
 		try {
 			if (selectedPureMap.getPure_id() != null) {
			    	dao.deletePureMap(selectedPureMap.getPure_id());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), "Pure ID: "+selectedPureMap.getPure_id());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
 			}
     		pureMapList.remove(selectedPureMap);
		        selectedPureMap = null;
 		}
 		catch(IllegalArgumentException e){
 			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), "Pure ID: "+selectedPureMap.getPure_id());
 			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
 	}
 }
}


