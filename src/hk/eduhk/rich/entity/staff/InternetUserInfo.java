package hk.eduhk.rich.entity.staff;

import java.util.Arrays;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_P_INTERNET_USER_INFO")
@SuppressWarnings("serial")
public class InternetUserInfo extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(InternetUserInfo.class.toString());
		
	@Id
	@SequenceGenerator(name = "rh_p_seq", sequenceName = "RH_P_SEQ", allocationSize = 1)
	@GeneratedValue(generator = "rh_p_seq")
	@Column(name = "info_seq")
	private int info_seq;
	
	@Column(name = "pid")
	private int pid;
	
	@Column(name = "staff_number")
	private String staff_number;

	@Column(name = "deptcode")
	private String deptcode;

	@Column(name = "phone")
	private String phone;

	@Column(name = "fax")
	private String fax;

	@Column(name = "email")
	private String email;

	@Column(name = "deptdesc")
	private String deptdesc;

	@Column(name = "post")
	private String post;

	@Column(name = "post_internal")
	private String post_internal;

	@Column(name = "rankcode_internal")
	private String rankcode_internal;

	@Column(name = "acad_staff_ind")
	private String acad_staff_ind;

	@Column(name = "title")
	private String title;

	@Column(name = "surname_internal")
	private String surname_internal;

	@Column(name = "othername_internal")
	private String othername_internal;

	@Column(name = "chinesename")
	private String chinesename;

	@Column(name = "fullname")
	private String fullname;

	@Column(name = "eorecord")
	private String eorecord;

	@Column(name = "displayorder")
	private int displayorder;

	
	
	public int getInfo_seq()
	{
		return info_seq;
	}


	
	public void setInfo_seq(int info_seq)
	{
		this.info_seq = info_seq;
	}


	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}

	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public String getDeptcode()
	{
		return deptcode;
	}

	
	public void setDeptcode(String deptcode)
	{
		this.deptcode = deptcode;
	}

	
	public String getPhone()
	{
		return phone;
	}

	
	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	
	public String getFax()
	{
		return fax;
	}

	
	public void setFax(String fax)
	{
		this.fax = fax;
	}

	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}

	
	public String getDeptdesc()
	{
		return deptdesc;
	}

	
	public void setDeptdesc(String deptdesc)
	{
		this.deptdesc = deptdesc;
	}

	
	public String getPost()
	{
		return post;
	}

	
	public void setPost(String post)
	{
		this.post = post;
	}

	
	public String getPost_internal()
	{
		return post_internal;
	}

	
	public void setPost_internal(String post_internal)
	{
		this.post_internal = post_internal;
	}

	
	public String getRankcode_internal()
	{
		return rankcode_internal;
	}

	
	public void setRankcode_internal(String rankcode_internal)
	{
		this.rankcode_internal = rankcode_internal;
	}

	
	public String getAcad_staff_ind()
	{
		return acad_staff_ind;
	}

	
	public void setAcad_staff_ind(String acad_staff_ind)
	{
		this.acad_staff_ind = acad_staff_ind;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getSurname_internal()
	{
		return surname_internal;
	}

	
	public void setSurname_internal(String surname_internal)
	{
		this.surname_internal = surname_internal;
	}

	
	public String getOthername_internal()
	{
		return othername_internal;
	}

	
	public void setOthername_internal(String othername_internal)
	{
		this.othername_internal = othername_internal;
	}

	
	public String getChinesename()
	{
		return chinesename;
	}

	
	public void setChinesename(String chinesename)
	{
		this.chinesename = chinesename;
	}

	
	public String getFullname()
	{
		return fullname;
	}

	
	public void setFullname(String fullname)
	{
		this.fullname = fullname;
	}

	
	public String getEorecord()
	{
		return eorecord;
	}

	
	public void setEorecord(String eorecord)
	{
		this.eorecord = eorecord;
	}

	
	public int getDisplayorder()
	{
		return displayorder;
	}

	
	public void setDisplayorder(int displayorder)
	{
		this.displayorder = displayorder;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + info_seq;
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		InternetUserInfo other = (InternetUserInfo) obj;
		if (info_seq != other.info_seq)
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "InternetUserInfo [info_seq=" + info_seq + ", pid=" + pid + ", staff_number=" + staff_number
				+ ", deptcode=" + deptcode + ", phone=" + phone + ", fax=" + fax + ", email=" + email + ", deptdesc="
				+ deptdesc + ", post=" + post + ", post_internal=" + post_internal + ", rankcode_internal="
				+ rankcode_internal + ", acad_staff_ind=" + acad_staff_ind + ", title=" + title + ", surname_internal="
				+ surname_internal + ", othername_internal=" + othername_internal + ", chinesename=" + chinesename
				+ ", fullname=" + fullname + ", eorecord=" + eorecord + ", displayorder=" + displayorder + "]";
	}






}
