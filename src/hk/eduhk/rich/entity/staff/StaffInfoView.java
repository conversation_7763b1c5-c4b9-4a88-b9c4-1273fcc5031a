package hk.eduhk.rich.entity.staff;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Date;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.persistence.OptimisticLockException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.commons.validator.routines.ISSNValidator;
import org.apache.commons.validator.routines.UrlValidator;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.RowEditEvent;
import org.primefaces.model.file.UploadedFile;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.employment.EmploymentHistory;
import hk.eduhk.rich.entity.employment.EmploymentHistory_edit;
import hk.eduhk.rich.entity.employment.Staff_info_hist_pk;
import hk.eduhk.rich.entity.employment.employmentHistoryDao;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.researcher.researcherInfoDao;

@ManagedBean(name = "staffInfoView")
@ViewScoped
@SuppressWarnings("serial")
public class StaffInfoView extends BaseView
{
	private static Logger logger = Logger.getLogger(StaffRank.class.getName());
	
	private List<Assistant> asstList;

	
	private InternetUserInfo iUserInfo = null;
	private List<InternetUserInfo> iUserInfoList = null;
	private StaffIdentity sIdentity = null;
	private StaffInfo sInfo = null;
	private UploadedFile file;

	private Boolean hasAccessRight = false;
	private Boolean showPhoto;
	private String showWebsite;
	private String websiteLink;
	private String cvLink;
	private String homepageLink;
	private Date currentDate;
	
	

	private String paramTab;
	private String paramPid;
	private int paramJobSeq;
	


	private List<EmploymentHistory> employmentHistList = null;
	private List<EmploymentHistory> selectedEmployHistList = null;	

	private List<EmploymentHistory_edit> employmentHistList_edit = null;
	private EmploymentHistory_edit selectedEmploymentHist_edit;
	
	
	public Date getCurrentDate()
	{
		Date nowdate = new Date();  
		currentDate = nowdate;
		return currentDate;
	}

	
	public void setCurrentDate(Date currentDate)
	{
		this.currentDate = currentDate;
	}

	
	public int getParamJobSeq()
	{
		return paramJobSeq;
	}

	
	public void setParamJobSeq(int paramJobSeq)
	{
		this.paramJobSeq = paramJobSeq;
		Staff_info_hist_pk pk = new Staff_info_hist_pk();
		pk.setJob_seq(paramJobSeq);
		pk.setPid(Integer.parseInt(paramPid));
		
		employmentHistoryDao dao = employmentHistoryDao.getInstance();
		selectedEmploymentHist_edit = dao.getSelectedEmploymentHistory(pk);
	}

	
	public List<EmploymentHistory> getSelectedEmployHistList()
	{
		return selectedEmployHistList;
	}

	
	public void setSelectedEmployHistList(List<EmploymentHistory> selectedEmployHistList)
	{
		this.selectedEmployHistList = selectedEmployHistList;
	}
	
	public EmploymentHistory_edit getSelectedEmploymentHist_edit()
	{
		if (selectedEmploymentHist_edit == null)
		{
			selectedEmploymentHist_edit = new EmploymentHistory_edit();
		}
		
		return selectedEmploymentHist_edit;
	}
	
	public void deleteSelectedEmploymentHist() throws IOException
	{
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String redirectLink = "manageInfo.xhtml?tab=1";
		
		try {
			employmentHistoryDao.getInstance().deleteEmploymentHistory(selectedEmploymentHist_edit.getPk());
			ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
	    	eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot delete Career Overview (PID.: " + selectedEmploymentHist_edit.getPk().getPid() + " Job Seq : " + selectedEmploymentHist_edit.getPk().getJob_seq()+")", e);
		}	
	}

	
	public void setSelectedEmploymentHist_edit(EmploymentHistory_edit selectedEmploymentHist_edit)
	{
		this.selectedEmploymentHist_edit = selectedEmploymentHist_edit;
	}


	public void checkValid() throws IOException 
	{
		if (getHasAccessRight() == false) {
			String message = "You don't have access right.";	
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
	}	
	
	public Boolean getHasAccessRight()
	{
		StaffIdentity tmpStaff = getsIdentity();
		if (tmpStaff != null) {
			if (tmpStaff.getCn().equals(getCurrentUserId())) {
				hasAccessRight = true;
				this.paramPid = String.valueOf(tmpStaff.getPid());
			}
			if (paramPid != null) {
				Set<String> roleSet = AccessDAO.getCacheInstance().getRoleIdSetByUserId(getLoginUserId());
				if(roleSet.contains("rdoAdmin"))
					hasAccessRight = true;
				AssistantDAO aDao = AssistantDAO.getInstance();
				List<Assistant> asstList = aDao.getAsstListByAcadStaffAndStatus(Integer.parseInt(paramPid),"APPROVED");
				for (Assistant a:asstList) {
					if (a.getPk().getAssistant_id().equals(getCurrentUserId())) {
						hasAccessRight = true;
					}
				}
			}
		}
		return hasAccessRight;
	}

	public UploadedFile getFile() {
        return file;
    }

    public void setFile(UploadedFile file) {
        this.file = file;
    }
    

    
    public void handleFileUpload(FileUploadEvent event) {
    	//System.out.println("NAME:"+event.getFile().getFileName());
        FacesMessage message = new FacesMessage("Successful", event.getFile().getFileName() + " is uploaded.");
        FacesContext.getCurrentInstance().addMessage(null, message);
    }
    
	public String getParamPid()
	{
		if (paramPid == null) {
			StaffDAO dao = StaffDAO.getInstance();
			StaffIdentity sIdentity = dao.getStaffDetailsByUserId(getCurrentUserId());
			this.paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
		}
		return paramPid;
	}


	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}


	
	public String getParamTab()
	{
		if (Strings.isNullOrEmpty(paramTab)) {
			this.paramTab = "0";
		}
		return paramTab;
	}


	
	public void setParamTab(String paramTab)
	{
		this.paramTab = paramTab;
	}


	public StaffIdentity getsIdentity()
	{
		if (sIdentity == null) {
			StaffDAO dao = StaffDAO.getInstance();
			if (paramPid != null) {
				sIdentity = dao.getStaffDetailsByPid(Integer.parseInt(paramPid));
			}else {
				sIdentity = dao.getStaffDetailsByUserId(getCurrentUserId());
				if (paramPid == null) {
					paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
				}
			}
		}
		return sIdentity;
	}

	public InternetUserInfo getiUserInfo() throws IOException {
		if (iUserInfo == null && paramPid != null) {
			List<InternetUserInfo>tmpList = getiUserInfoList();
			String phone = "";
			String fax = "";
			String email = "";
			if (tmpList != null) {
				if (tmpList.size() > 0) {
					iUserInfo = tmpList.get(0);
					phone = (iUserInfo.getPhone() != "" && iUserInfo.getPhone() != null)?"(852) " + iUserInfo.getPhone():"";
					fax = (iUserInfo.getFax() != "" && iUserInfo.getFax() != null)?"(852) " + iUserInfo.getFax():"";
					email = (iUserInfo.getEmail() != "" && iUserInfo.getEmail() != null)?iUserInfo.getEmail():"";
				
					for (InternetUserInfo t : tmpList) {
						if (t.getPhone() != "" && t.getPhone() != null) {
							if (!phone.contains(t.getPhone())) {
								if (phone != "") {
									phone += " / ";
								}
								phone += "(852) "+t.getPhone();
							}
						}
						if (t.getFax() != "" && t.getFax() != null) {
							if (!fax.contains(t.getFax())) {
								if (fax != "") {
									fax += " / ";
								}
								fax += "(852) "+t.getFax();
							}
						}
						if (t.getEmail() != "" && t.getEmail() != null) {
							if (!email.contains(t.getEmail())) {
								if (email != "") {
									email += " / ";
								}
								email += t.getEmail();
							}
						}
					}
					
					iUserInfo.setPhone(phone);
					iUserInfo.setFax(fax);
					iUserInfo.setEmail(email);
				}
			}
		}
		return iUserInfo;	
	}	
	
	public List<InternetUserInfo> getiUserInfoList() throws IOException {
		if (iUserInfoList == null && paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			iUserInfoList = dao.getInternetUserInfo(Integer.valueOf(paramPid));
		}
		return iUserInfoList;	
	}
	
	

	public String getStaffPost() throws IOException {
		String value = "";
		iUserInfoList = getiUserInfoList();
		if (iUserInfoList != null) {
			for (InternetUserInfo i:iUserInfoList) {
				value += i.getPost() + ", <span style='color:#ff5722'>" +i.getDeptdesc() + "</span><br/>";
			}
		}
		return value;
	}
	
	public StaffInfo getsInfo() throws IOException {
		if (sInfo == null && paramPid != null) {
			CvDAO dao = CvDAO.getInstance();
			sInfo = dao.getStaffInfo(Integer.valueOf(paramPid));
			if (sInfo != null) {
				if (sInfo.getResearch_interest()!=null) {
					sInfo.setResearch_interest(sInfo.getResearch_interest().replace("&nbsp;"," "));
				}
				if (sInfo.getTeaching_interest()!=null) {
					sInfo.setTeaching_interest(sInfo.getTeaching_interest().replace("&nbsp;"," "));
				}
				if (sInfo.getExt_appt()!=null) {
					sInfo.setExt_appt(sInfo.getExt_appt().replace("&nbsp;"," "));
				}
				if (sInfo.getProfile()!=null) {
					sInfo.setProfile(sInfo.getProfile().replace("&nbsp;"," "));
				}
			}
			/*sInfo.setResearch_interest(sInfo.getResearch_interest().replaceAll("\\<.*?>",""));
			sInfo.setTeaching_interest(sInfo.getTeaching_interest().replaceAll("\\<.*?>",""));
			sInfo.setExt_appt(sInfo.getExt_appt().replaceAll("\\<.*?>",""));
			sInfo.setProfile(sInfo.getProfile().replaceAll("\\<.*?>",""));*/
			
		}
		return sInfo;	
	}

	
	public void setsInfo(StaffInfo sInfo)
	{
		this.sInfo = sInfo;
	}

	
	
	public String getCvLink()
	{
		if (cvLink == null && sIdentity != null) {
			SysParamDAO sDao = SysParamDAO.getInstance();
			String cvUrl = sDao.getSysParamValueByCode("BASE_URL_CV");
			cvLink = cvUrl + sIdentity.getPid()+"&amp;name="+sIdentity.getFullname();
		}
		return cvLink;
	}

	
	public void setCvLink(String cvLink)
	{
		this.cvLink = cvLink;
	}

	
	public String getHomepageLink()
	{
		if (homepageLink == null && sInfo != null) {
			homepageLink = sInfo.getUrl();
		}
		return homepageLink;
	}

	
	public void setHomepageLink(String homepageLink)
	{
		this.homepageLink = homepageLink;
	}

	public String getWebsiteLink()
	{
		if (getShowWebsite()!= null) {
			if (showWebsite.equals("CVPAGE")) {
				websiteLink = getCvLink();
			}else if (showWebsite.equals("HOMEPAGE")) {
				websiteLink = getHomepageLink();
			}else {
				websiteLink = "N/A";
			}
		}else {
			websiteLink = "N/A";
		}
		return websiteLink;
	}

	
	public void setWebsiteLink(String websiteLink)
	{
		this.websiteLink = websiteLink;
	}

	public List<Assistant> getAsstList()
	{
		if (asstList == null) {
			AssistantDAO dao = AssistantDAO.getInstance();
			asstList = dao.getAsstListByAcadStaff(Integer.parseInt(paramPid));
		}
		return asstList;
	}
	
	public Boolean getShowPhoto()
	{
		if (showPhoto == null && getParamPid() != null) {
			showPhoto = true;
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_PHOTO");
			if (obj != null) {
				showPhoto = (obj.getShowInd().equals("Y"))?true:false;
			}
		}
		return showPhoto;
	}

	public void setShowPhoto(Boolean showPhoto)
	{
		this.showPhoto = showPhoto;
	}
	
	public String getShowWebsite()
	{
		if (showWebsite == null && getParamPid() != null) {
			showWebsite = "CVPAGE";
			int pid = Integer.valueOf(paramPid);
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_WEBSITE");
			if (obj != null) {
				if (obj.getShowInd().equals("N")) {
					showWebsite = "NA";
				}else {
					showWebsite= obj.getDisplayType();
				}
			}
		}
		return showWebsite;
	}

	
	public void setShowWebsite(String showWebsite)
	{
		this.showWebsite = showWebsite;
	}
	
	public void updateInfo()
	{
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	CvDAO cvDao = CvDAO.getInstance();
    	StaffDAO dao = StaffDAO.getInstance();
		if (sInfo != null) {
			try {
				if (validateUrl() && validateOrcid()) {
					if (sInfo.getPhotoFile() != null) {
						String[] base64ImageArray = sInfo.getPhotoFile().split(",");
						if (base64ImageArray.length > 1) {
							String base64Image = base64ImageArray[1];
							if (base64Image != "") {
								byte[] decodedImg = Base64.getDecoder().decode(base64Image.getBytes(StandardCharsets.UTF_8));
								sInfo.setPhoto(decodedImg);
							}
						}
					}
					if (sInfo.getResearcherid() != null) {
						sInfo.setResearcherid(StringUtils.capitalize(sInfo.getResearcherid()));
					}
					
					sInfo.setUrl(homepageLink);
					sInfo.setUserstamp(getLoginUserId());
					
					//photo
		    		StaffProfileDisplay photoObj = cvDao.getStaffProfileDisplay(sInfo.getPid(), "DATA_PHOTO");
		    		if (photoObj == null) {
		    			photoObj = new StaffProfileDisplay();
		    			photoObj.getPk().setPid(sInfo.getPid());
		    			photoObj.getPk().setItemCode("DATA_PHOTO");
		    			photoObj.setItemType("DATA");
		    		}
		    		photoObj.setShowInd(showPhoto?"Y":"N");
		    		photoObj.setUserstamp(getLoginUserId());
		    		
					//website
		    		StaffProfileDisplay websiteObj = cvDao.getStaffProfileDisplay(sInfo.getPid(), "DATA_WEBSITE");
		    		if (websiteObj == null) {
		    			websiteObj = new StaffProfileDisplay();
		    			websiteObj.getPk().setPid(sInfo.getPid());
		    			websiteObj.getPk().setItemCode("DATA_WEBSITE");
		    			websiteObj.setItemType("DATA");
		    		}
		    		websiteObj.setShowInd(!showWebsite.equals("NA")?"Y":"N");
		    		websiteObj.setDisplayType(showWebsite);
		    		websiteObj.setUserstamp(getLoginUserId());
		    		
		    		if (sInfo.getProfile()!=null) {
						sInfo.setProfile(StringEscapeUtils.unescapeHtml4(sInfo.getProfile().replace("&nbsp;"," ")));
					}
		    		if (sInfo.getResearch_interest()!=null) {
		    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replace("( 69 , 69 , 69 )","( 0 , 0 , 0 )")));
		    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<ul>","<ul style=\"margin: 0px\">")));
		    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<ol>","<ol style=\"margin: 0px\">")));
		    			sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<p><br /></p>","<br/>")));
						sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("<p>","")));
						sInfo.setResearch_interest(StringEscapeUtils.unescapeHtml4(sInfo.getResearch_interest().replaceAll("</p>","<br/>")));
						
					}
					if (sInfo.getTeaching_interest()!=null) {
						sInfo.setTeaching_interest(StringEscapeUtils.unescapeHtml4(sInfo.getTeaching_interest().replace("&nbsp;"," ")));
					}
					if (sInfo.getExt_appt()!=null) {
						sInfo.setExt_appt(StringEscapeUtils.unescapeHtml4(sInfo.getExt_appt().replace("&nbsp;"," ")));
					}
					if (sInfo.getOth_activity()!=null) {
						sInfo.setOth_activity(StringEscapeUtils.unescapeHtml4(sInfo.getOth_activity().replace("&nbsp;"," ")));
					}
					
					if(sInfo.getSdg_list() != null) 
						sInfo.setSdg_code(String.join(",", sInfo.getSdg_list()));
					
		    		//Update form
		    		dao.updateStaffInfo(sInfo);
		    		photoObj = cvDao.updateStaffProfileDisplay(photoObj);
		    		websiteObj = cvDao.updateStaffProfileDisplay(websiteObj);
					
					message = "msg.success.update.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Your Info.");
					FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
		    		FacesContext.getCurrentInstance().addMessage(null, msg);
		    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
		    		
		    		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageInfo.xhtml?pid="+sInfo.getPid();
			    	eCtx.redirect(redirectLink);
				}
			}
			catch (OptimisticLockException ole)
			{
				message = bundle.getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
	    	catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
				message = bundle.getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
	}
	
	public Boolean validateUrl() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		sInfo.setUrl(homepageLink);
		if (StringUtils.isNotBlank(sInfo.getUrl())) {
			String[] schemes = {"http","https"}; // DEFAULT schemes = "http", "https", "ftp"
			UrlValidator urlValidator = new UrlValidator(schemes);
			if (urlValidator.isValid(sInfo.getUrl()) == false) {
				result = false;
				message = "Homepage URL is not valid.";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		return result;
	}
	
	public Boolean validateOrcid() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (StringUtils.isNotBlank(sInfo.getOrcid())) {
			String orcid = sInfo.getOrcid().replaceAll("-", "");
        	String last = takeLast(orcid, 1);
        	String checkValue = removeLastCharacter(orcid);
        	String checkDegit = generateCheckDigit(checkValue);
        	if (!last.equals(checkDegit)) {
        		result = false;
				message = "ORCID is not valid.";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
        	}
		}else {
			result = false;
			message = "ORCID is mandatory.";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		return result;
	}
	
	public String getPureProfileUrl() {
		String url = null;
		SysParamDAO sDao = SysParamDAO.getInstance();
		String pureProfileUrl = sDao.getSysParamValueByCode("PURE_PROFILE_URL");
		if (getsIdentity() != null) {
			String otherName = sIdentity.getOthername().toLowerCase();
			otherName = otherName.replaceAll(" ", "-");
			otherName = otherName.replaceAll(",", "");
			String surname = sIdentity.getSurname().toLowerCase();
			String chineseName = (sIdentity.getChinesename() != null)?sIdentity.getChinesename():"";
			url = pureProfileUrl + otherName + chineseName + "-" + surname;
		}
		return url;
	}
	
	/** 
	  * Generates check digit as per ISO 7064 11,2. 
	  * 
	  */ 
	public String generateCheckDigit(String baseDigits) { 
	    int total = 0; 
	    for (int i = 0; i < baseDigits.length(); i++) { 
	        int digit = Character.getNumericValue(baseDigits.charAt(i)); 
	        total = (total + digit) * 2; 
	    } 
	    int remainder = total % 11; 
	    int result = (12 - remainder) % 11; 
	    return result == 10 ? "X" : String.valueOf(result); 
	}
	
	public static String takeLast(String value, int count) {
	    if (value == null || value.trim().length() == 0) return "";
	    if (count < 1) return "";

	    if (value.length() > count) {
	        return value.substring(value.length() - count);
	    } else {
	        return value;
	    }
	}
	
	public static String removeLastCharacter(String str) {
		   String result = Optional.ofNullable(str)
		   .filter(sStr -> sStr.length() != 0)
		   .map(sStr -> sStr.substring(0, sStr.length() - 1))
		   .orElse(str);
		   return result;
		}
	
	public List<EmploymentHistory> getEmploymentHistList() {
		if (employmentHistList == null) {
			employmentHistoryDao dao = employmentHistoryDao.getInstance();
			int user_pid = Integer.parseInt(paramPid);
			employmentHistList = dao.getEmploymentHistory(user_pid);
			

		}
		return employmentHistList;
	}
	public void setEmploymentHistList(List<EmploymentHistory> employmentHistList)
	{
		this.employmentHistList = employmentHistList;
	}


	public List<EmploymentHistory_edit> getEmploymentHistList_edit()
	{
		if (employmentHistList_edit == null) {
			employmentHistoryDao dao = employmentHistoryDao.getInstance();
			int user_pid = Integer.parseInt(paramPid);
			employmentHistList_edit = dao.getEmploymentHistory_edit(user_pid);
		}
		return employmentHistList_edit;
	}
	
	public void setEmploymentHistList_edit(List<EmploymentHistory_edit> employmentHistList_edit)
	{
		this.employmentHistList_edit = employmentHistList_edit;
	}
	
	
	public String gotoEditHistPage() throws UnsupportedEncodingException
	{		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("employmentHistory_edit")+
				   "&pid=" + selectedEmploymentHist_edit.getPk().getPid() +
				   "&job_seq=" + selectedEmploymentHist_edit.getPk().getJob_seq()+
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	public String gotoNewPage() throws UnsupportedEncodingException
	{		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("employmentHistory_edit")+
				"&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	
	//Move the data from selected Career Overview data to RH_P_STAFF_INFO_HIST
	public void insertSelectedHist () {
		String message;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	try {
	        if (selectedEmployHistList != null && selectedEmployHistList.size() > 0) {
	            boolean result;
	            
	            for (int i = 0; i < selectedEmployHistList.size(); i++) {
	            	Staff_info_hist_pk pk_obj = new Staff_info_hist_pk();
	            	EmploymentHistory_edit new_obj = new EmploymentHistory_edit();
	            	
	            	pk_obj.setPid(selectedEmployHistList.get(i).getPid());
	            	pk_obj.setJob_seq(findMaxjob_seq()+i+1);
	            	
	            	new_obj.setPk(pk_obj);
	            	new_obj.setCompany("The Education University of Hong Kong");
	            	new_obj.setJob_title(selectedEmployHistList.get(i).getRank_full());
	            	new_obj.setFrom_date(selectedEmployHistList.get(i).getEff_from());
	            	if (selectedEmployHistList.get(i).getEff_to().before(getCurrentDate())) {
		            	new_obj.setTo_date(selectedEmployHistList.get(i).getEff_to());
		            	new_obj.setIs_current('N');	
	            	}else {
	            		new_obj.setIs_current('Y');	
	            	}
	            	
	            	employmentHistoryDao dao = employmentHistoryDao.getInstance();
	            	result = dao.UpdateEmploymentHistory(new_obj);
	            	
	                message = new_obj.getJob_title() +  "is successfully copied. ";
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	            }
	        }
    	}
	        catch (IllegalStateException ise){
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("employmentHistList_edit");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole){
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    }
	
	//To return the max job_seq in a specific pid
	public int findMaxjob_seq() {
		
		List<Integer> job_seq_list = new ArrayList<Integer>();
		int maxNumber = 0;
		//System.out.println(maxNumber);
		//System.out.println(getEmploymentHistList_edit().size());
		if (getEmploymentHistList_edit().size() != 0)
		{
			for (int i = 0; i < getEmploymentHistList_edit().size(); i++) {
				
				job_seq_list.add(getEmploymentHistList_edit().get(i).getPk().getJob_seq());
			}
			maxNumber = job_seq_list.stream().mapToInt(v->v).max().orElseThrow(NoSuchElementException::new);
		}
		System.out.println(maxNumber);

		return maxNumber;
	}
	
	//Update or create new Career Overview by button
	public void updateSelectedHist () {
		String message;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	try {
	        if (selectedEmploymentHist_edit != null) {
	            boolean result;
	            boolean isNew = (selectedEmploymentHist_edit.getPk().getJob_seq() == null)?true:false;
	            employmentHistoryDao dao = employmentHistoryDao.getInstance();
	            
	            
	            if (isNew)
	            {
	            	selectedEmploymentHist_edit.getPk().setPid(Integer.valueOf(getParamPid()));
	            	selectedEmploymentHist_edit.getPk().setJob_seq(findMaxjob_seq()+1);
	            	selectedEmploymentHist_edit.setCreator(getLoginUserId());
	            	//selectedEmploymentHist_edit.setCreator("RICH");
	            }
	            
	            if (!Strings.isNullOrEmpty(selectedEmploymentHist_edit.getJob_details())) {
	            	String tmpDetails = selectedEmploymentHist_edit.getJob_details();
	            	tmpDetails = tmpDetails.replaceAll("<script>", "＜script＞");
	            	tmpDetails = tmpDetails.replaceAll("<p><br /></p>", "<br /><br />");
	            	tmpDetails = tmpDetails.replaceAll("<p>", "");
	            	tmpDetails = tmpDetails.replaceAll("</p>", "");
	            	selectedEmploymentHist_edit.setJob_details(tmpDetails);
	            }
	            selectedEmploymentHist_edit.setUserstamp(getLoginUserId());
	            result = dao.UpdateEmploymentHistory(selectedEmploymentHist_edit);
	            
                message = "msg.success.update.x";
    			message = MessageFormat.format(getResourceBundle().getString(message), selectedEmploymentHist_edit.getJob_title());
        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
        		FacesContext.getCurrentInstance().addMessage(null, msg);
        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	       
	        }
    	}
	        catch (IllegalStateException ise){
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("employmentHistList_edit");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole){
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    }
	
	
    public String deleteEmpHistory() {
    	if (selectedEmploymentHist_edit != null) {
    		String responseStr = selectedEmploymentHist_edit.getJob_title() +" at "+ selectedEmploymentHist_edit.getCompany();
    		try {
    			if (selectedEmploymentHist_edit.getPk().getJob_seq() != null) {
    				
    				employmentHistoryDao dao = employmentHistoryDao.getInstance();   				
			    	dao.deleteEmploymentHistory(selectedEmploymentHist_edit.getPk());
			    	
			        String message = "msg.success.delete.x";
			       
	    			message = MessageFormat.format(getResourceBundle().getString(message),responseStr);
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
    			employmentHistList_edit.remove(selectedEmploymentHist_edit);
    			selectedEmploymentHist_edit = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), responseStr);
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    	String redirectLink = "manageInfo.xhtml?tab=1";
    	return redirect(redirectLink); 
    }
	
	public boolean empHistDateValid() {
		boolean result = false;
		if (selectedEmploymentHist_edit.getTo_date() != null) {
			if (selectedEmploymentHist_edit.getFrom_date().compareTo(selectedEmploymentHist_edit.getTo_date()) < 1) {
				result= true;
			}
		}else {
			result= true;
		}
		return result;
	}
	
	
    public String updateEmpHistoryForm() {
    	if(empHistDateValid()) {
	    	updateSelectedHist();
	    	String redirectLink = "manageInfo.xhtml?tab=1";
	    	return redirect(redirectLink); 
    	}
		String message = "Date To must be later than Date From";
		FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		return "";
	
    }
	
	
    public String insertHroHist() {
    	
    	insertSelectedHist();
    	String redirectLink = "manageInfo.xhtml?tab=1";
    	return redirect(redirectLink); 
    }
	
}
