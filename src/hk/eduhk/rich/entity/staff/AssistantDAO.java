package hk.eduhk.rich.entity.staff;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.access.SecFuncLockUser;
import hk.eduhk.rich.util.PersistenceManager;

public class AssistantDAO extends BaseDAO
{

	private static AssistantDAO instance = null;


	public static synchronized AssistantDAO getInstance()
	{
		if (instance == null) instance = new AssistantDAO();
		return instance;
	}
	
	
	public static AssistantDAO getCacheInstance()
	{
		return AssistantDAO.getInstance();
	}	
	
	public List<Assistant> getAcadStaffListByAsstId(String asstId, String status)
	{
		List<Assistant> objList = null;
		EntityManager em = null;		
		String whereClause = (status != "")?" AND obj.status = :status ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Assistant obj WHERE obj.pk.assistant_id = :asstId "+ whereClause +" ORDER BY obj.creationDate DESC";			
			TypedQuery<Assistant> q = em.createQuery(query, Assistant.class);
			q.setParameter("asstId", asstId);
			if (whereClause != "") {
				q.setParameter("status", status);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<Assistant> getAsstListByAcadStaff(int pid)
	{
		List<Assistant> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Assistant obj WHERE obj.pk.pid = :pid ORDER BY obj.creationDate DESC";			
			TypedQuery<Assistant> q = em.createQuery(query, Assistant.class);
			q.setParameter("pid", pid);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<Assistant> getAsstListByAcadStaffAndStatus(int pid, String status)
	{
		List<Assistant> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Assistant obj WHERE obj.pk.pid = :pid AND obj.status = :status ORDER BY obj.assistant_name";			
			TypedQuery<Assistant> q = em.createQuery(query, Assistant.class);
			q.setParameter("pid", pid);
			q.setParameter("status", status);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public Assistant updateAsst(Assistant obj) 
	{
		return updateEntity(obj);
	}
	
	public void deleteAsst(Assistant obj)
	{
		if (obj != null) {
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				obj = em.find(Assistant.class, obj.getPk());
				em.remove(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}	
}