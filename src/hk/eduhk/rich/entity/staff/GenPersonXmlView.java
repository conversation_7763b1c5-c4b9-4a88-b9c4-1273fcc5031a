package hk.eduhk.rich.entity.staff;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;


import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.stream.StreamResult;

import org.jdom2.*;
import org.jdom2.output.*;
import org.jdom2.output.support.*;
import org.jdom2.transform.JDOMSource;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.util.MimeMap;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PurePersonPatent;
import hk.eduhk.rich.BaseView;


@SuppressWarnings("serial")
@ManagedBean(name = "genPersonXmlView")
@ViewScoped
public class GenPersonXmlView extends BaseView
{
	private Logger logger = Logger.getLogger(getClass().getName());

	public String createPersonXmlFile() throws IOException
	{		
		String xmlString = "";
		PatentDAO pDao = PatentDAO.getInstance();
		StaffDAO dao = StaffDAO.getInstance();
		try
		{
			Document doc=new Document();
			
			//Root Element
			Namespace ns1 = Namespace.getNamespace("v1.unified-person-sync.pure.atira.dk");	
			Namespace ns2 = Namespace.getNamespace("v3", "v3.commons.pure.atira.dk");
			Element root=new Element("persons", ns1);
			root.addNamespaceDeclaration(ns2);
			doc.setRootElement(root);
			
			List<PurePerson> personList = dao.getPurePerson();
			Namespace rootNS = root.getNamespace();
			for(PurePerson p:personList) {
				//Person Element
				Element person=new Element("person", rootNS);
				person.setAttribute("id", p.getPure_source_id());
				
				//name
				Element childName = new Element("name", rootNS);
				Element firstname = new Element("firstname", ns2);
				if(p.getChinesename_e() != null) {
					childName.addContent(firstname.addContent(p.getOthername_e() +"\u2004" + p.getChinesename_e()));
				}
				else {
					childName.addContent(firstname.addContent(p.getOthername_e()));
				}
				Element lastname = new Element("lastname", ns2);
				childName.addContent(lastname.addContent(p.getSurname_e()));
				person.addContent(childName); 
				
				//gender
				Element childGender = new Element("gender", rootNS);
				childGender.addContent(p.getGender());
				person.addContent(childGender);
				
				//photos
				Element childPhotos = new Element("photos", rootNS);
				if (p.getPhoto() != null && getShowPhoto(p.getPid())) {
					Element personPhoto = new Element("personPhoto", rootNS);
					personPhoto.setAttribute("id", p.getPure_source_id()+"_personPhotoAssoc1");
					childPhotos.addContent(personPhoto);
					Element classification = new Element("classification", rootNS).addContent("portrait");
					personPhoto.addContent(classification);
					
					Element data = new Element("data", rootNS);
					personPhoto.addContent(data);
	
					Element dataByte = new Element("byte", rootNS);
					data.addContent(dataByte);
					Element base64EncodedString = new Element("base64EncodedString", rootNS);
					base64EncodedString.addContent(p.getPhotoFile());
					Element mimeType = new Element("mimeType", rootNS);
					mimeType.addContent("image/jpg");
					Element fileName = new Element("fileName", rootNS);
					fileName.addContent(p.getSurname_e()+"_"+p.getOthername_e()+".jpg");
					
					dataByte.addContent(base64EncodedString);
					dataByte.addContent(mimeType);
					dataByte.addContent(fileName);
					person.addContent(childPhotos);
				}
				
				//organisationAssociations
				Element childOrgAsso = new Element("organisationAssociations", rootNS);
				List<PurePersonDept> deptList = dao.getPurePersonDept(p.getPure_source_id());
				int countDept = 1;
				for(PurePersonDept d:deptList) {
					//staffOrganisationAssociation
					Element staffOrgAsso = new Element("staffOrganisationAssociation", rootNS);
					staffOrgAsso.setAttribute("id", p.getPure_source_id()+"_staffOrganisationAssociation"+countDept);
					
					//phoneNumbers
					if (d.getPhone() != null) {
						Element phoneNumbers = new Element("phoneNumbers", rootNS);
						Element classifiedPhoneNumber = new Element("classifiedPhoneNumber", ns2);
						classifiedPhoneNumber.setAttribute("id", p.getPure_source_id()+"_personStaffAssoc1PhoneNumber1");
						classifiedPhoneNumber.addContent(new Element("classification", ns2).addContent("phone"));
						classifiedPhoneNumber.addContent(new Element("value", ns2).addContent(d.getPhone()));
						phoneNumbers.addContent(classifiedPhoneNumber);
						staffOrgAsso.addContent(phoneNumbers);
					}
					
					//emails
					if (d.getEmail() != null) {
						Element emails = new Element("emails", rootNS);
						Element classifiedEmail = new Element("classifiedEmail", ns2);
						classifiedEmail.setAttribute("id", p.getPure_source_id()+"_classifiedEmail");
						classifiedEmail.addContent(new Element("classification", ns2).addContent("email"));
						classifiedEmail.addContent(new Element("value", ns2).addContent(d.getEmail()));
						emails.addContent(classifiedEmail);
						staffOrgAsso.addContent(emails);
					}
					
					//webAddresses
					String websiteLink = getWebsiteLink(p);
					if (websiteLink != null) {
						Element webAddresses = new Element("webAddresses", rootNS);
						Element classifiedWebAddress = new Element("classifiedWebAddress", ns2);
						classifiedWebAddress.setAttribute("id", p.getPure_source_id()+"_classifiedWebAddress1");
						classifiedWebAddress.addContent(new Element("classification", ns2).addContent("web"));
						Element classifiedWebAddress_value = new Element("value", ns2);
						Element classifiedWebAddress_value_text = new Element("text", ns2);
						classifiedWebAddress_value_text.setAttribute("lang", "en");
						classifiedWebAddress_value_text.setAttribute("country", "GB");
						classifiedWebAddress_value_text.addContent(websiteLink);
						classifiedWebAddress_value.addContent(classifiedWebAddress_value_text);
						classifiedWebAddress.addContent(classifiedWebAddress_value);
						webAddresses.addContent(classifiedWebAddress);
						staffOrgAsso.addContent(webAddresses);
					}
					
					//organisation
					Element organisation = new Element("organisation", rootNS);
					organisation.addContent(new Element("source_id", ns2).addContent(d.getDept_source_id()));
					staffOrgAsso.addContent(organisation);
					
					//period
					Element period = new Element("period", rootNS);
					period.addContent(new Element("startDate", ns2).addContent(d.getStart_date()));
					if (d.getEnd_date() != null)
						period.addContent(new Element("endDate", ns2).addContent(d.getEnd_date()));
					staffOrgAsso.addContent(period);
					
				
					
					//staffType
					Element staffType = new Element("staffType", rootNS);
					staffType.addContent(d.getStaff_type());
					staffOrgAsso.addContent(staffType);
					
					//jobDescription
					Element jobDescription = new Element("jobDescription", rootNS);
					Element jobDescription_text = new Element("text", ns2);
					jobDescription_text.setAttribute("lang", "en");
					jobDescription_text.addContent(d.getPost());
					jobDescription.addContent(jobDescription_text);
					staffOrgAsso.addContent(jobDescription);
					
					childOrgAsso.addContent(staffOrgAsso);
					countDept++;
				}
				
				
				person.addContent(childOrgAsso);
				
				
				//profileInformation
				Element childProfileInfo = new Element("profileInformation", rootNS);
		
				//profile
				if (p.getProfile() != null) {
					String content = p.getProfile();
					content = skipInValidXMLChars(content);
					content = removeEmptyTag(content);
					content = content.replaceAll("</?span[^>]*>", "");
					content = content.replace("&nbsp;", " ");
					Element info = new Element("personCustomField", rootNS);
					info.setAttribute("id", p.getPure_source_id()+"_personCustomField1");
					info.addContent(new Element("typeClassification", rootNS).addContent("personal_profile"));
					Element info_value = new Element("value", rootNS);
					Element info_value_text = new Element("text", ns2);
					info_value_text.setAttribute("lang", "en");
					info_value_text.setAttribute("country", "GB");
					info_value_text.addContent(content);
					info_value.addContent(info_value_text);
					info.addContent(info_value);
					childProfileInfo.addContent(info);
				}
				
				//research interest
				if (p.getResearch_interest() != null) {
					String content = p.getResearch_interest();
					content = skipInValidXMLChars(content);
					content = removeEmptyTag(content);
					content = content.replaceAll("</?span[^>]*>", "");
					content = content.replace("&nbsp;", " ");
					Element info = new Element("personCustomField", rootNS);
					info.setAttribute("id", p.getPure_source_id()+"_personCustomField2");
					info.addContent(new Element("typeClassification", rootNS).addContent("researchinterests"));
					Element info_value = new Element("value", rootNS);
					Element info_value_text = new Element("text", ns2);
					info_value_text.setAttribute("lang", "en");
					info_value_text.setAttribute("country", "GB");
					info_value_text.addContent(content);
					info_value.addContent(info_value_text);
					info.addContent(info_value);
					childProfileInfo.addContent(info);		
				}
				
				//teaching interest
				if (p.getTeaching_interest() != null) {
					String content = p.getTeaching_interest();
					content = skipInValidXMLChars(content);
					content = removeEmptyTag(content);
					content = content.replaceAll("</?span[^>]*>", "");
					content = content.replace("&nbsp;", " ");
					Element info = new Element("personCustomField", rootNS);
					info.setAttribute("id", p.getPure_source_id()+"_personCustomField3");
					info.addContent(new Element("typeClassification", rootNS).addContent("teaching"));
					Element info_value = new Element("value", rootNS);
					Element info_value_text = new Element("text", ns2);
					info_value_text.setAttribute("lang", "en");
					info_value_text.setAttribute("country", "GB");
					info_value_text.addContent(content);
					info_value.addContent(info_value_text);
					info.addContent(info_value);
					childProfileInfo.addContent(info);		
				}
				
				//ext app
				if (p.getExt_appt() != null) {
					String content = p.getExt_appt();
					content = skipInValidXMLChars(content);
					content = removeEmptyTag(content);
					content = content.replaceAll("</?span[^>]*>", "");
					content = content.replace("&nbsp;", " ");
					Element info = new Element("personCustomField", rootNS);
					info.setAttribute("id", p.getPure_source_id()+"_personCustomField4");
					info.addContent(new Element("typeClassification", rootNS).addContent("external_appointment"));
					Element info_value = new Element("value", rootNS);
					Element info_value_text = new Element("text", ns2);
					info_value_text.setAttribute("lang", "en");
					info_value_text.setAttribute("country", "GB");
					info_value_text.addContent(content);
					info_value.addContent(info_value_text);
					info.addContent(info_value);
					childProfileInfo.addContent(info);		
				}
				

				
				
				//orcid || scopus_id_1 || scopus_id_2 || researcherid
				if (p.getOrcid() != null || p.getScopus_id_1() != null || p.getScopus_id_2() != null || p.getResearcherid() != null) {
					Element info = new Element("personCustomField", rootNS);
					info.setAttribute("id", p.getPure_source_id()+"_personCustomField5");
					info.addContent(new Element("typeClassification", rootNS).addContent("professionalinformation"));
					Element info_value = new Element("value", rootNS);
					Element info_value_text = new Element("text", ns2);
					info_value_text.setAttribute("lang", "en");
					info_value_text.setAttribute("country", "GB");
					String value = "";
					if (p.getScopus_id_1() != null && p.getScopus_id_2() != null)
						value += "Scopus ID: <a href='"+getSysParamValue("BASE_URL_SCOPUS")+p.getScopus_id_1()+"'>"+p.getScopus_id_1()+"</a>, <a href='"+getSysParamValue("BASE_URL_SCOPUS")+p.getScopus_id_2()+"'>"+p.getScopus_id_2()+"</a><br/><br/>";
					if (p.getScopus_id_1() != null && p.getScopus_id_2() == null)
						value += "Scopus ID: <a href='"+getSysParamValue("BASE_URL_SCOPUS")+p.getScopus_id_1()+"'>"+p.getScopus_id_1()+"</a><br/><br/>";
					if (p.getScopus_id_1() == null && p.getScopus_id_2() != null)
						value += "Scopus ID: <a href='"+getSysParamValue("BASE_URL_SCOPUS")+p.getScopus_id_2()+"'>"+p.getScopus_id_2()+"</a><br/><br/>";
					if (p.getOrcid() != null)
						value += "ORCID: <a href='"+getSysParamValue("BASE_URL_ORCID")+p.getOrcid()+"'>"+p.getOrcid()+"</a><br/><br/>";
					if (p.getResearcherid() != null)
						value += "ResearcherID: <a href='"+getSysParamValue("BASE_URL_RESEARCHERID")+p.getResearcherid()+"'>"+p.getResearcherid()+"</a><br/><br/>";
					info_value_text.addContent(value);
					info_value.addContent(info_value_text);
					info.addContent(info_value);
					childProfileInfo.addContent(info);		
				}
				
				//patent
				List<PurePersonPatent> patentList = pDao.getPersonPatentList(p.getPure_source_id());
				if (!patentList.isEmpty()) {
					Element info = new Element("personCustomField", rootNS);
					info.setAttribute("id", p.getPure_source_id()+"_personCustomField6");
					info.addContent(new Element("typeClassification", rootNS).addContent("patents"));
					Element info_value = new Element("value", rootNS);
					Element info_value_text = new Element("text", ns2);
					info_value_text.setAttribute("lang", "en");
					info_value_text.setAttribute("country", "GB");
					String value = "";
					for(PurePersonPatent ppp:patentList) {
						if (value != "") {
							value += "<br/>";
						}
						value +="<strong>"+ppp.getPatent_name()+"</strong><br/><span style=\"font-size:smaller;\">"+ppp.getShort_desc()+"<br/>";
						
						if("G".equals(ppp.getPatent_granted())) {
							value +="Date of grant: ";
						} 
						else if("A".equals(ppp.getPatent_granted())) {
							value +="Date of application: ";
						}
						else {
							value +="Date: ";
						}
									
						value += ppp.getPatent_day()+"/"+ppp.getPatent_month()+"/"+ppp.getPatent_year()+"</span>";
						value +="<br/>";
					}
					info_value_text.addContent(value);
					info_value.addContent(info_value_text);
					info.addContent(info_value);
					childProfileInfo.addContent(info);		
				}
				
				
				person.addContent(childProfileInfo);
				
				
				//links
				Element childLinks = new Element("links", rootNS);
				/*if (p.getUrl() != null) {
					Element link = new Element("link", ns2);
					link.setAttribute("id", p.getPure_source_id()+"_link1");
					link.addContent(new Element("url", ns2).addContent(p.getUrl()));
					link.addContent(new Element("type", ns2).addContent("personal"));
					Element link_desc = new Element("description", ns2);
					Element link_desc_text = new Element("text", ns2);
					link_desc_text.setAttribute("lang", "en");
					link_desc_text.setAttribute("country", "GB");
					link_desc_text.addContent("Personal Website");
					link_desc.addContent(link_desc_text);
					link.addContent(link_desc);
					childLinks.addContent(link);
					person.addContent(childLinks);
				}
				*/
				
				
				
				//keywords SDG	
				if (p.getSdg_code() != null && p.getSdg_code().length() != 0) {
					Element keywords = new Element("keywords", ns1);
					Element logicalGroup = new Element("logicalGroup", ns2);
					logicalGroup.setAttribute("logicalName", "sustainabledevelopmentgoals");
					Element structuredKeywords = new Element("structuredKeywords", ns2);
					for (String sdg:p.getSdg_list()) {
						Element structuredKeyword = new Element("structuredKeyword", ns2);
						String sdg_str = sdg.toLowerCase().replace(" ", "_");
						sdg_str = sdg_str.replace("-", "_");
						structuredKeyword.setAttribute("classification", "/dk/atira/pure/sustainabledevelopmentgoals/"+sdg_str);
						structuredKeywords.addContent(structuredKeyword);
					}
					logicalGroup.addContent(structuredKeywords);
					keywords.addContent(logicalGroup);
					person.addContent(keywords);
				}
				
				
				//scopus id 1/2
				Element childScopus = new Element("personIds", rootNS);
				int countScopusId = 1;
				if (p.getScopus_id_1() != null) {
					String scopusId = p.getScopus_id_1();
					Element scopusId_item = new Element("id", ns2);
					scopusId_item.setAttribute("type", "scopusauthor");
					scopusId_item.setAttribute("id", p.getPure_source_id()+"_id"+countScopusId);
					scopusId_item.addContent(scopusId);
					childScopus.addContent(scopusId_item);
					countScopusId++;
				}
				if (p.getScopus_id_2() != null) {
					String scopusId = p.getScopus_id_2();
					Element scopusId_item = new Element("id", ns2);
					scopusId_item.setAttribute("type", "scopusauthor");
					scopusId_item.setAttribute("id", p.getPure_source_id()+"_id"+countScopusId);
					scopusId_item.addContent(scopusId);
					childScopus.addContent(scopusId_item);
				}
				person.addContent(childScopus);
				

				
				
				//orcid
				Element childOrcid = new Element("orcId", rootNS);
				if (p.getOrcid() != null) {
					childOrcid.addContent(p.getOrcid());
					person.addContent(childOrcid);
				}

				 
				
				//visibility
				Element childVisibility = new Element("visibility", rootNS);
				childVisibility.addContent(p.getVisibility());
				person.addContent(childVisibility);
				
				
				//profiled
				Element childProfiled = new Element("profiled", rootNS);
				childProfiled.addContent("false");
				person.addContent(childProfiled);
				
				//workflow
				Element childWorkflow = new Element("workflow", rootNS);
				childWorkflow.addContent("forApproval");
				person.addContent(childWorkflow);
				
				
				//Add in the root Element
				root.addContent(person);			
	
			}
			
			xmlString = transform(doc);
			xmlString = removeEmptyTag(xmlString);
			xmlString = xmlString.replace("&#158;", "");
			
		}
		catch (TransformerFactoryConfigurationError | TransformerException e)
		{
			logger.log(Level.WARNING, "Cannot generate xml", e);
		}
		return xmlString;
		
	}
	
	private String transform(Document sourceDoc) throws TransformerException {
        JDOMSource source = new JDOMSource(sourceDoc);
        StreamResult result = new StreamResult(new StringWriter());

        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
        transformer.transform(source, result);

        return result.getWriter().toString();
    }	
	
	private String getXmlFilePath()
	{
		String code = Constant.isLocalEnv() ? SysParam.PARAM_FILE_PATH_LOCAL : SysParam.PARAM_FILE_PATH;
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		return paramDAO.getSysParamValueByCode(code);
	}
	
	public String getSysParamValue(String code) {
		String value = "";
		SysParamDAO sysParamDao = SysParamDAO.getInstance();
		SysParam tmp = sysParamDao.getSysParamByCode(code);
		if (tmp != null) {
			value = tmp.getValue();
		}
		return value;
	}
	
	public String skipInValidXMLChars(String in) {
        StringBuffer out = new StringBuffer();
        char current;

        if (in == null || ("".equals(in))) return "";
        for (int i = 0; i < in.length(); i++) {
            current = in.charAt(i);
            if ((current == 0x9) ||
                (current == 0xA) ||
                (current == 0xD) ||
                ((current >= 0x20) && (current <= 0xD7FF)) ||
                ((current >= 0xE000) && (current <= 0xFFFD)) ||
                ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }  
	

	
	public static final XMLOutputProcessor XMLOUTPUT = new AbstractXMLOutputProcessor() {
	    @Override
	    protected void printDeclaration(final Writer out, final FormatStack fstack) throws IOException {
	        write(out, "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?> ");
	        write(out, fstack.getLineSeparator());
	    }
	};
	
	public String removeEmptyTag(String value) {
		Pattern emptyValueTag = Pattern.compile("\\s*<\\w+/>");
	    Pattern emptyTagMultiLine = Pattern.compile("\\s*<\\w+>\n*\\s*</\\w+>");

	    value = emptyValueTag.matcher(value).replaceAll("");
	    while (value.length() != (value = emptyTagMultiLine.matcher(value).replaceAll("")).length()) {
	    }
	    return value;
	}
	
	public String getScopusId(String scopusId1, String scopusId2)
	{
		String scopusId = (scopusId1!=null && scopusId2==null)?scopusId1:null;
		scopusId = (scopusId1!=null && scopusId2!=null)?scopusId1+", "+scopusId2:scopusId;
		scopusId = (scopusId1==null && scopusId2!=null)?scopusId2:scopusId;
		return scopusId;
	}
	
	public Boolean getShowPhoto(int pid)
	{
		boolean showPhoto = true;
		if (pid > 0) {
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_PHOTO");
			if (obj != null) {
				showPhoto = (obj.getShowInd().equals("Y"))?true:false;
			}
		}
		return showPhoto;
	}

	public String getShowWebsite(int pid)
	{
		String showWebsite = "CVPAGE";
		if (pid > 0) {
			CvDAO cvDao = CvDAO.getInstance();
			StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_WEBSITE");
			if (obj != null) {
				if (obj.getShowInd().equals("N")) {
					showWebsite = null;
				}else {
					showWebsite= obj.getDisplayType();
				}
			}
		}
		return showWebsite;
	}
	
	public String getWebsiteLink(PurePerson p) 
	{
		String showWebsite = getShowWebsite(p.getPid());
		String websiteLink = null;
		if (showWebsite!= null) {
			if (showWebsite.equals("CVPAGE")) {
				websiteLink = getSysParamValue("BASE_URL_CV")+p.getPid()+"&name="+p.getSurname_e()+"-"+p.getOthername_e();
			}
			if (showWebsite.equals("HOMEPAGE")) {
				if (p.getUrl() != null) {
					if (p.getUrl().contains("http://") || p.getUrl().contains("https://")) {
						websiteLink = p.getUrl();
					}else {
						websiteLink = "http://"+p.getUrl();
					}
				}
			}
		}
		return websiteLink;
		
	}
}
