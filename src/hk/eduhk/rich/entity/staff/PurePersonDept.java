package hk.eduhk.rich.entity.staff;

import java.util.Arrays;
import java.util.Base64;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

//RH_PURE_PERSON_DEPT_V
public class PurePersonDept
{
	public static Logger logger = Logger.getLogger(PurePersonDept.class.toString());
	
	private String pure_source_id;	
	
	private int pid;

	private String deptcode;

	private String dept_source_id;
	
	private String post;
	
	private String email;

	private String phone;	
	
	private String fax;		
	
	private String start_date;		
	
	private String end_date;		
	
	private String staff_type;		
	
	private Integer displayOrder;		
	
	private String eoRecord;

	
	public String getPure_source_id()
	{
		return pure_source_id;
	}

	
	public void setPure_source_id(String pure_source_id)
	{
		this.pure_source_id = pure_source_id;
	}

	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}

	
	public String getDeptcode()
	{
		return deptcode;
	}

	
	public void setDeptcode(String deptcode)
	{
		this.deptcode = deptcode;
	}

	
	
	public String getDept_source_id()
	{
		return dept_source_id;
	}


	
	public void setDept_source_id(String dept_source_id)
	{
		this.dept_source_id = dept_source_id;
	}


	public String getPost()
	{
		return post;
	}

	
	public void setPost(String post)
	{
		this.post = post;
	}

	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}

	
	public String getPhone()
	{
		return phone;
	}

	
	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	
	public String getFax()
	{
		return fax;
	}

	
	public void setFax(String fax)
	{
		this.fax = fax;
	}

	
	public String getStart_date()
	{
		return start_date;
	}

	
	public void setStart_date(String start_date)
	{
		this.start_date = start_date;
	}

	
	public String getEnd_date()
	{
		return end_date;
	}

	
	public void setEnd_date(String end_date)
	{
		this.end_date = end_date;
	}

	
	public String getStaff_type()
	{
		return staff_type;
	}

	
	public void setStaff_type(String staff_type)
	{
		this.staff_type = staff_type;
	}

	
	public Integer getDisplayOrder()
	{
		return displayOrder;
	}

	
	public void setDisplayOrder(Integer displayOrder)
	{
		this.displayOrder = displayOrder;
	}

	
	public String getEoRecord()
	{
		return eoRecord;
	}

	
	public void setEoRecord(String eoRecord)
	{
		this.eoRecord = eoRecord;
	}


	@Override
	public String toString()
	{
		return "PurePersonDept [pure_source_id=" + pure_source_id + ", pid=" + pid + ", deptcode=" + deptcode
				+ ", dept_source_id=" + dept_source_id + ", post=" + post + ", email=" + email + ", phone=" + phone
				+ ", fax=" + fax + ", start_date=" + start_date + ", end_date=" + end_date + ", staff_type="
				+ staff_type + ", displayOrder=" + displayOrder + ", eoRecord=" + eoRecord + "]";
	}


}
