package hk.eduhk.rich.entity.staff;

import java.io.*;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DateUtil;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.data.DataMatrix;
import hk.eduhk.rich.data.DataMatrixExtractor;
import hk.eduhk.rich.entity.importRI.ImportRIBatch;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIStore;
import hk.eduhk.rich.entity.importRI.ImportRIStore_PK;

@ManagedBean(name = "staffEligibleView")
@ViewScoped
@SuppressWarnings("serial")
public class StaffEligibleView extends BaseView
{
	private static Logger logger = Logger.getLogger(StaffEligible.class.getName());
	private List<StaffEligible> staffEligibleList = null;
	private StaffEligible selectedStaff;
	
	private transient UploadedFile uploadedFile = null;
	
	private StaffDAO dao = StaffDAO.getInstance();
	
	public void reloadStaffList() {
		staffEligibleList = null;
		getStaffEligibleList();
	}
	
	public List<StaffEligible> getStaffEligibleList()
	{
		if (staffEligibleList == null)
		{
			staffEligibleList = dao.getStaffEligibleList();
		}
		return staffEligibleList;
	}

	public String getFileName()
	{
		String result = "staff_eligible_list";
		if (getStaffEligibleList() != null) {
			if (staffEligibleList.size() > 0) {
				String periodString = staffEligibleList.get(0).getReporting_period();
				if (periodString.length() > 1) {
					String toYear = periodString.substring(periodString.length() - 2);
					String fromYear = String.valueOf(Integer.valueOf(toYear)-1);
					result = fromYear + "-" + toYear + "_" + result;
				}
			}
		}
		return result;
		
	}
	
	public StaffEligible getSelectedStaff()
	{
		return selectedStaff;
	}

	
	public void setSelectedStaff(StaffEligible selectedStaff)
	{
		this.selectedStaff = selectedStaff;
	}
    
    public void deleteStaff() {
    	if (selectedStaff != null) {
    		try {
    			if (selectedStaff.getPk().getEmployee_number() != null && selectedStaff.getPk().getDcc() != null) {
			    	dao.deleteStaffEligible(selectedStaff.getPk().getEmployee_number(), selectedStaff.getPk().getDcc());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedStaff.getLast_name() + " " + selectedStaff.getFirst_name());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		staffEligibleList.remove(selectedStaff);
		        selectedStaff = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedStaff.getLast_name() + " " + selectedStaff.getFirst_name());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
			catch (Exception e)
			{
				String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedStaff.getLast_name() + " " + selectedStaff.getFirst_name());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
    	}else {
    		String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedStaff.getLast_name() + " " + selectedStaff.getFirst_name());
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    	}
    }
    
    
    
	public UploadedFile getUploadedFile()
	{
		return uploadedFile;
	}

	
	public void setUploadedFile(UploadedFile uploadedFile)
	{
		this.uploadedFile = uploadedFile;
	}

	
	public List<String> getUploadSrcHeaders() {

		List<String> rtnList = new ArrayList<String>();
		rtnList.add("Reporting Period");
		rtnList.add("Employee Number");
		rtnList.add("Assignment Number");
		rtnList.add("Title");
		rtnList.add("Husband Last Name");
		rtnList.add("Last Name");
		rtnList.add("First Name");
		rtnList.add("Chinese Name");
		rtnList.add("Department Code");
		rtnList.add("Post Rank Code");
		rtnList.add("Staff Grade");
		rtnList.add("DCC");
		rtnList.add("DCC Percentage");
		rtnList.add("Terms Of Appointment");
		rtnList.add("Post Type");
		rtnList.add("Last Termination Date");
		rtnList.add("Principal Assignment Flag");
		rtnList.add("Mapped Dept.");
		rtnList.add("Start Period");
		rtnList.add("End Period");
		rtnList.add("Remarks");
		return rtnList;
		
	}
	public String upload() throws Exception {
		FacesContext fCtx = FacesContext.getCurrentInstance();

		String destUrl = redirect("manageEligibleStaff");
		String filename = null;
		SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy", Locale.ENGLISH);
		
		if (getUploadedFile() != null && 
			getUploadedFile().getContent() != null &&
			getUploadedFile().getContent().length > 0)
		{

			// Validate the file type
			filename = getUploadedFile().getFileName();

	        String extension = FilenameUtils.getExtension(filename);
        
			if (!"xlsx".equalsIgnoreCase(extension) && !"xls".equalsIgnoreCase(extension))
			{
				String message = "Invalid file. The file must be in Excel format (xlsx or xls).";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
			// Get the Sheet
			DataMatrixExtractor extractor = new DataMatrixExtractor(uploadedFile);
			DataMatrix dataMatrix = null;
			
			try
			{
				dataMatrix = extractor.getDataMatrix();
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e.getCause());
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
			
				// Validate the header
				if (dataMatrix != null)
				{
					// Get uploaded File headers
					List<String> colHeaderList = null;
					List<String> srcHeaderList = getUploadSrcHeaders();
					int rowNum = dataMatrix.getLastRowNum();
		
		    		// Create the mapping table between header name and column number
		    		// using the 1st row
		    		if (rowNum > 0)
		    		{
		    			colHeaderList = new ArrayList<String>();
		    			for(String header : dataMatrix.getRowValueList(0)) {
			    			if(!StringUtils.isBlank(header)) colHeaderList.add(header);
			    		}
		    		}
		    		
		    		
		    		boolean headerError = false;
		    		if(colHeaderList != null && colHeaderList.size() != 0)
		    		{
		    			if(colHeaderList.size() != srcHeaderList.size()) 
		    				headerError = true;
		    			for (int colIdx=0;colIdx<colHeaderList.size();colIdx++)
		    			{
		    				if(colHeaderList.get(colIdx) == null || !colHeaderList.get(colIdx).equals(srcHeaderList.get(colIdx)))
		    				{
		    					headerError = true;
		    					break;
		    				}
		    			}
		    		} else headerError = true;
		    		
		    		if(headerError)
		    		{
						String message = "The headers of the excel file do not match.";
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
						fCtx.getExternalContext().getFlash().setKeepMessages(true);
						return destUrl;
		    		}
	
		    		Integer dataRowNum = 0;
		    		List<StaffEligible> newStaffEligibleList = new ArrayList<StaffEligible>();
		    		
		    		// Starts from the 2nd row, as the 1st row is header row
		    		for (int rowIdx=1;rowIdx<=rowNum;rowIdx++)
		    		{
		    			if (dataMatrix.isEmptyRow(rowIdx)) continue;
		    			dataRowNum++;
		    			StaffEligible obj = new StaffEligible();
		    			StaffEligible_PK pk = new StaffEligible_PK();
		    			for (int colIdx=0;colIdx<colHeaderList.size();colIdx++)
		    			{
		    				String strCellValue = dataMatrix.getValue(rowIdx, colIdx);
		    				if ("NULL".equals(strCellValue)) {
		    					strCellValue = null;
		    				}
		    				switch(colIdx+1) {
		    					case 1 :
		    						obj.setReporting_period(strCellValue);
		    						break;
		    					case 2 :
		    						pk.setEmployee_number(strCellValue);
		    						break;
		    					case 3 :
		    						obj.setAssignment_number(strCellValue);
		    						break;
		    					case 4 :
		    						obj.setTitle(strCellValue);
		    						break;
		    					case 5 :
		    						obj.setHusband_last_name(strCellValue);
		    						break;	
		    					case 6 :
		    						obj.setLast_name(strCellValue);
		    						break;
		    					case 7 :
		    						obj.setFirst_name(strCellValue);
		    						break;
		    					case 8 :
		    						obj.setChinese_name(strCellValue);
		    						break;
		    					case 9 :
		    						obj.setDept_code(strCellValue);
		    						break;
		    					case 10 :
		    						obj.setPost_rank_code(strCellValue);
		    						break;
		    					case 11 :
		    						obj.setStaff_grade(strCellValue);
		    						break;
		    					case 12 :
		    						pk.setDcc(Integer.valueOf(strCellValue));
		    						break;
		    					case 13 :
		    						obj.setDcc_percentage(Double.parseDouble(strCellValue));
		    						break;
		    					case 14 :
		    						obj.setTerms_of_appointment(strCellValue);
		    						break;
		    					case 15 :
		    						obj.setPost_type(strCellValue);
		    						break;
		    					case 16 :
		    						Date last_terminatoin_date=null; 
		    						if (!Strings.isNullOrEmpty(strCellValue)){
			    						if (!strCellValue.matches("\\d{2}-[a-zA-Z]{3}-\\d{4}")) {
			    							last_terminatoin_date = DateUtil.getJavaDate(Double.parseDouble(strCellValue));
			    						}else {
			    							last_terminatoin_date =sdf.parse(strCellValue);  
			    						}
		    						}
		    						obj.setLast_termination_date(last_terminatoin_date);
		    						break;
		    					case 17 :
		    						obj.setPrincipal_assignment_flag(strCellValue);
		    						break;
		    					case 18 :
		    						obj.setMapped_dept(strCellValue);
		    						break;
		    					case 19 :
		    						obj.setStart_period(strCellValue);
		    						break;
		    					case 20 :
		    						obj.setEnd_period(strCellValue);
		    						break;
		    					case 21 :
		    						obj.setRemarks(strCellValue);
		    						break;
		    				}
		    			}
		    			obj.setPk(pk);
		    			newStaffEligibleList.add(obj);
		    		}
		    		
		    		//delete list
		    		dao.deleteAllStaffEligible();
		    		
		    		//insert list
		    		for (StaffEligible d:newStaffEligibleList) {
		    			dao.updateStaffEligible(d);
		    		}
		    			
		    		String message = "The eligible staff list has been uploaded successfully";
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
				}
				else
				{
					String message = getResourceBundle().getString("msg.err.unexpected");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
					return destUrl;
				}
				
			}
			else {
				String message = "Upload File cannot be null";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
		
		return destUrl;
	}
	
	private StreamedContent file;

    public void FileDownloadView() {
        file = DefaultStreamedContent.builder()
                .name("staff_list_template.xlsx")
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .stream(() -> FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream("/resources/template/staff_list_template.xlsx"))
                .build();
    }

    public StreamedContent getFile() {
        return file;
    }

		
}
