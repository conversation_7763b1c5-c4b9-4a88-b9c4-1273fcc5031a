package hk.eduhk.rich.entity.staff;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_Z_ACAD_STAFF_RANK")
@SuppressWarnings("serial")
public class StaffRank extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(StaffRank.class.toString());
	
	@Id
	@Column(name = "rank_code")
	private String rank_code;	
	
	@Column(name = "rank_full")
	private String rank_full;



	public String getRank_code()
	{
		return rank_code;
	}

	
	public void setRank_code(String rank_code)
	{
		this.rank_code = rank_code;
	}

	
	public String getRank_full()
	{
		return rank_full;
	}

	
	public void setRank_full(String rank_full)
	{
		this.rank_full = rank_full;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((rank_code == null) ? 0 : rank_code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffRank other = (StaffRank) obj;
		if (rank_code == null)
		{
			if (other.rank_code != null)
				return false;
		}
		else if (!rank_code.equals(other.rank_code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "StaffRank [rank_code=" + rank_code + ", rank_full=" + rank_full + "]";
	}
}
