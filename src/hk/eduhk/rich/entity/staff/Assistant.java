package hk.eduhk.rich.entity.staff;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;

import javax.persistence.Table;
import javax.persistence.Transient;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_U_STAFF_ASSISTANT")
@SuppressWarnings("serial")
public class Assistant extends UserPersistenceObject
{
	@EmbeddedId
	private AssistantPK pk = new AssistantPK();

	@Column(name = "ASSISTANT_NAME")
	private String assistant_name;
	
	@Column(name = "ACCESS_RIGHT")
	private String access_right;

	@Column(name = "STATUS")
	private String status;
	
	@Transient
	private String acadStaffName = null;
	

	public String getAcadStaffName()
	{
		return acadStaffName;
	}


	public void setAcadStaffName(String acadStaffName)
	{
		this.acadStaffName = acadStaffName;
	}


	public AssistantPK getPk()
	{
		return pk;
	}

	
	public void setPk(AssistantPK pk)
	{
		this.pk = pk;
	}

	
	public String getAssistant_name()
	{
		return assistant_name;
	}

	
	public void setAssistant_name(String assistant_name)
	{
		this.assistant_name = assistant_name;
	}

	
	public String getAccess_right()
	{
		return access_right;
	}

	
	public void setAccess_right(String access_right)
	{
		this.access_right = access_right;
	}

	
	public String getStatus()
	{
		return status;
	}

	
	public void setStatus(String status)
	{
		this.status = status;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Assistant other = (Assistant) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "Assistant [pk=" + pk + ", assistant_name=" + assistant_name + ", access_right=" + access_right
				+ ", status=" + status + "]";
	}


}
