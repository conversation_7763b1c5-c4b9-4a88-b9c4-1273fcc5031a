package hk.eduhk.rich.entity.staff;

import java.io.Serializable;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.publication.DisciplinaryArea;


@Embeddable
public class StaffEligible_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "EMPLOYEE_NUMBER")
	private String employee_number;	
	
	@Column(name = "DCC")
	private Integer dcc;

	
	public String getEmployee_number()
	{
		return employee_number;
	}

	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}

	
	public Integer getDcc()
	{
		return dcc;
	}

	
	public void setDcc(Integer dcc)
	{
		this.dcc = dcc;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(dcc, employee_number);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StaffEligible_PK other = (StaffEligible_PK) obj;
		return Objects.equals(dcc, other.dcc) && Objects.equals(employee_number, other.employee_number);
	}


	@Override
	public String toString()
	{
		return "StaffEligible_PK [employee_number=" + employee_number + ", dcc=" + dcc + "]";
	}

}
