package hk.eduhk.rich.entity.staff;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.Department;
import hk.eduhk.rich.entity.FacDept;
import hk.eduhk.rich.entity.publication.OutputHeader_P;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.util.PersistenceManager;

public class StaffDAO extends BaseDAO
{

	private static StaffDAO instance = null;


	public static synchronized StaffDAO getInstance()
	{
		if (instance == null) instance = new StaffDAO();
		return instance;
	}
	
	
	public static StaffDAO getCacheInstance()
	{
		return StaffDAO.getInstance();
	}
	
	public List<StaffPast> getPastStaffList()
	{
		List<StaffPast> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM StaffPast obj ORDER BY obj.fullname ";			
			TypedQuery<StaffPast> q = em.createQuery(query, StaffPast.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public StaffPast getPastStaffDetailsByPid(int pid)
	{
		List<StaffPast> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM StaffPast obj WHERE obj.pid = :pid ORDER BY obj.fullname ";			
			TypedQuery<StaffPast> q = em.createQuery(query, StaffPast.class);
			q.setParameter("pid", pid);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public StaffPast getPastStaffDetailsByStaffName(String staffName, boolean hasChinese)
	{
		List<StaffPast> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "";
			if (hasChinese) {
				query = "SELECT obj FROM StaffPast obj WHERE REPLACE(UPPER(SURNAME || ' ' ||OTHERNAME|| ' ' ||chinesename), ',', '') = :staffName ORDER BY obj.staff_number DESC ";			
			}else {
				query = "SELECT obj FROM StaffPast obj WHERE REPLACE(UPPER(SURNAME || ' ' ||OTHERNAME), ',', '') = :staffName ORDER BY obj.staff_number DESC ";			
			}
			TypedQuery<StaffPast> q = em.createQuery(query, StaffPast.class);
			q.setParameter("staffName", staffName);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public StaffPast getPastStaffDetailsByStaffNo(String staffNo)
	{
		List<StaffPast> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM StaffPast obj WHERE obj.staff_number = :staffNo ORDER BY obj.fullname ";			
			TypedQuery<StaffPast> q = em.createQuery(query, StaffPast.class);
			q.setParameter("staffNo", staffNo);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public List<StaffIdentity> getAcadStaffList(String acadStaffInd)
	{
		List<StaffIdentity> objList = null;
		EntityManager em = null;		
		String whereClause  = "";
		if (acadStaffInd != null) {
			whereClause = " WHERE obj.acad_staff_ind = :acadStaffInd ";
		}
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM StaffIdentity obj " + whereClause + " ORDER BY obj.fullname ";			
			TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
			if (acadStaffInd != null) {
				q.setParameter("acadStaffInd", acadStaffInd);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	
	public StaffIdentity getStaffDetailsByUserId(String userId)
	{
		List<StaffIdentity> objList = null;
		
		if (userId != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM StaffIdentity obj WHERE obj.cn = :userId";		
				TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
				q.setParameter("userId", userId);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}	
	
	public StaffIdentity getStaffDetailsByPid(int pid)
	{
		List<StaffIdentity> objList = null;
		
		if (pid > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM StaffIdentity obj WHERE obj.pid = :pid";		
				TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
				q.setParameter("pid", pid);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public StaffIdentity getStaffDetailsByStaffNo(String staffNo)
	{
		List<StaffIdentity> objList = null;
		
		if (staffNo != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM StaffIdentity obj WHERE obj.staff_number = :staffNo";		
				TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public StaffInfo getStaffProfileByPid(int pid)
	{
		List<StaffInfo> objList = null;
		
		if (pid > 0l)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM StaffInfo obj WHERE obj.pid = :pid";		
				TypedQuery<StaffInfo> q = em.createQuery(query, StaffInfo.class);
				q.setParameter("pid", pid);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}	
	
	public StaffInfo getStaffProfileByStaffNo(String staffNo)
	{
		List<StaffInfo> objList = null;
		
		if (staffNo != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM StaffInfo obj WHERE obj.staff_number = :staffNo";		
				TypedQuery<StaffInfo> q = em.createQuery(query, StaffInfo.class);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public List<PurePerson> getPurePerson()
	{
		List<PurePerson> objList = null;	
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PurePerson obj ORDER BY obj.pure_source_id ";			
			TypedQuery<PurePerson> q = em.createQuery(query, PurePerson.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PurePersonDept> getPurePersonDept(String pure_source_id)
	{
		List<PurePersonDept> objList = new ArrayList<PurePersonDept>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		try
		{
			String query = "SELECT * FROM RH_PURE_PERSON_DEPT_V WHERE PURE_SOURCE_ID = ? ORDER BY displayorder, eorecord, post ";
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			pStmt.setString(1, pure_source_id);
			ResultSet rs = pStmt.executeQuery();
			while (rs.next()) {
				PurePersonDept d = new PurePersonDept();
				d.setPure_source_id(rs.getString("PURE_SOURCE_ID"));
				d.setPid(rs.getInt("pid"));
				d.setDeptcode(rs.getString("deptcode"));
				d.setDept_source_id(rs.getString("DEPT_SOURCE_ID"));
				d.setPost(rs.getString("post"));
				d.setEmail(rs.getString("email"));
				d.setPhone(rs.getString("phone"));
				d.setFax(rs.getString("fax"));
				d.setStart_date(rs.getString("start_date"));
				d.setEnd_date(rs.getString("end_date"));
				d.setStaff_type(rs.getString("staff_type"));
				d.setDisplayOrder(rs.getInt("displayorder"));
				d.setEoRecord(rs.getString("eorecord"));
				objList.add(d);
			}			
		}
		catch (SQLException se)
		{
			se.printStackTrace();
		}
		finally
		{
			PersistenceManager.close(pStmt);
			PersistenceManager.close(conn);
		}
		return objList;		
	}
	
	public List<StaffRank> getRankList()
	{
		List<StaffRank> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM StaffRank obj ORDER BY obj.rank_full";
		try
		{
			em = getEntityManager();
			TypedQuery<StaffRank> q = em.createQuery(query, StaffRank.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<String> getEmploymentRankList()
	{
		List<String> objList = null;

		EntityManager em = null;
		String query = "SELECT distinct obj.rank_code FROM StaffEmployment obj";
		try
		{
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public StaffRank getRankByCode(String code)
	{
		StaffRank obj = null;
		
		if (code != "")
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(StaffRank.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public StaffRank updateRank(StaffRank obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteRank(String code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			StaffRank obj = em.find(StaffRank.class, code);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public PureMap updatePureMap(PureMap obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deletePureMap(Integer pure_id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			PureMap obj = em.find(PureMap.class, pure_id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public PureMap getPureMapById(Integer pure_id)
	{
		PureMap obj = null;
		
		if (pure_id != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(PureMap.class, pure_id);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public List<PureMap> getPureMapList()
	{
		List<PureMap> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM PureMap obj ORDER BY obj.pure_id";
		try
		{
			em = getEntityManager();
			TypedQuery<PureMap> q = em.createQuery(query, PureMap.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PureDept> getPureDeptList()
	{
		List<PureDept> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM PureDept obj ORDER BY obj.department_code";
		try
		{
			em = getEntityManager();
			TypedQuery<PureDept> q = em.createQuery(query, PureDept.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public PureDept getPureDept(String code)
	{
		PureDept obj = null;
		EntityManager em = null;
		if (code != "") {
			try
			{
				em = getEntityManager();
				obj = em.find(PureDept.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;	
	}
	
	public PureDept updatePureDept(PureDept obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deletePureDept(String code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (code != "") {
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				PureDept obj = em.find(PureDept.class, code);
				em.remove(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}	
	
	public StaffInfo updateStaffInfo(StaffInfo obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public List<Department> getDepartmentList() 
	{
		List<Department> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Department obj ";			
			TypedQuery<Department> q = em.createQuery(query, Department.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<Department> getDepartmentListHavingData() 
	{
		List<Department> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Department obj WHERE (numAcadStaff != 0 OR " +
							" numProject != 0 OR numPublication != 0) AND obj.departmentCode != 'OTHERS' " +
							" ORDER BY obj.departmentName ";			
			TypedQuery<Department> q = em.createQuery(query, Department.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<Department> getDepartmentListFac() 
	{
		List<Department> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Department obj WHERE departmentName LIKE 'Faculty%' ";			
			TypedQuery<Department> q = em.createQuery(query, Department.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<Department> getDepartmentListDept() 
	{
		List<Department> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Department obj WHERE departmentName NOT LIKE 'Faculty%' ";			
			TypedQuery<Department> q = em.createQuery(query, Department.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public Department getDepartmentByCode(String deptCode)
	{
		Department obj = null;		
		if (deptCode != null)
		{
			EntityManager em = null;
			try
			{
				em = getEntityManager();
				obj = em.find(Department.class, deptCode);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public List<StaffIdentity> getAcadStaffMapByIdList(List<String> idList)
	{
		List<StaffIdentity> objList = null;
		EntityManager em = null;
		if(!CollectionUtils.isEmpty(idList)) {
			objList = new ArrayList<StaffIdentity>();
			List<List<String>> idListPatch = new ArrayList<List<String>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			try
			{
				em = getEntityManager();	
				for(List<String> list : idListPatch) {
					String query = " SELECT obj FROM StaffIdentity obj "
							+ " WHERE obj.staff_number IN :idList "
							+ " ORDER BY obj.fullname ";			
					TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
					q.setParameter("idList", list);
					objList.addAll(q.getResultList());
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public List<StaffIdentity> getAcadStaffMapByPidList(List<Integer> idList)
	{
		List<StaffIdentity> objList = null;
		EntityManager em = null;
		if(!CollectionUtils.isEmpty(idList)) {
			try
			{
				em = getEntityManager();	
				
				String query = " SELECT obj FROM StaffIdentity obj "
						+ " WHERE obj.pid IN :idList "
						+ " ORDER BY obj.fullname ";			
				TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
				q.setParameter("idList", idList);
				objList = q.getResultList();
				
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public List<StaffPast> getPastStaffMapByIdList(List<String> idList)
	{
		List<StaffPast> objList = null;
		EntityManager em = null;
		if(!CollectionUtils.isEmpty(idList)) {
			objList = new ArrayList<StaffPast>();
			List<List<String>> idListPatch = new ArrayList<List<String>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			try
			{
				em = getEntityManager();	
				for(List<String> list : idListPatch) {
					String query = " SELECT obj FROM StaffPast obj "
							+ " WHERE obj.staff_number IN :idList "
							+ " ORDER BY obj.fullname ";			
					TypedQuery<StaffPast> q = em.createQuery(query, StaffPast.class);
					q.setParameter("idList", list);
					objList.addAll(q.getResultList());
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public Map<String, StaffInfo> getStaffInfoMapByIdList(List<String> idList)
	{
		Map<String, StaffInfo> objMap = new HashMap<String, StaffInfo>();
		List<StaffInfo> objList = null;
		EntityManager em = null;	
		if(!CollectionUtils.isEmpty(idList)) {
			objList = new ArrayList<StaffInfo>();
			List<List<String>> idListPatch = new ArrayList<List<String>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			try
			{
				em = getEntityManager();	
				for(List<String> list : idListPatch) {
					String query = " SELECT obj FROM StaffInfo obj "
							+ " WHERE obj.staff_number IN :idList ";			
					TypedQuery<StaffInfo> q = em.createQuery(query, StaffInfo.class);
					q.setParameter("idList", list);
					objList.addAll(q.getResultList());
					if(objList != null) {
						for (StaffInfo info : objList) {
							objMap.put(info.getStaff_number(), info);
						}
					}
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return objMap;
	}
	
	public List<String> getDataCodeByUserId(String userId) {
		List<SecDataUser> objList = null;
		List<String> rtnList = null;
		
		if (userId != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM SecDataUser obj "
						+ " WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";		
				TypedQuery<SecDataUser> q = em.createQuery(query, SecDataUser.class);
				q.setParameter("userId", userId);
				q.setParameter("dataType", "D");
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
			if(CollectionUtils.isNotEmpty(objList)){
				rtnList = new ArrayList<String>();
				for (SecDataUser data : objList) {
					rtnList.add(data.getPk().getData_code());
				}
			}
		}
		
		return (CollectionUtils.isNotEmpty(rtnList) ? rtnList : null); 
	}
	
	public List<String> getDataCodeByUserId(String userId, String dataType) {
		List<SecDataUser> objList = null;
		List<String> rtnList = null;
		
		if (userId != null && dataType != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM SecDataUser obj "
						+ " WHERE obj.pk.user_id = :userId AND obj.pk.data_type = :dataType ";		
				TypedQuery<SecDataUser> q = em.createQuery(query, SecDataUser.class);
				q.setParameter("userId", userId);
				q.setParameter("dataType", dataType);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
			if(CollectionUtils.isNotEmpty(objList)){
				rtnList = new ArrayList<String>();
				for (SecDataUser data : objList) {
					rtnList.add(data.getPk().getData_code());
				}
			}
		}
		
		return (CollectionUtils.isNotEmpty(rtnList) ? rtnList : null); 
	}
	
	public List<String> getStaffNameListByCNList(List<String> cnList)
	{
		List<String> objList = null;
		EntityManager em = null;
		if(!CollectionUtils.isEmpty(cnList)) {
			try
			{
				em = getEntityManager();		
				String query = " SELECT obj.fullname FROM StaffIdentity obj "
						+ " WHERE obj.cn IN :cnList "
						+ " ORDER BY obj.fullname ";			
				TypedQuery<String> q = em.createQuery(query, String.class);
				q.setParameter("cnList", cnList);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	

	public FacDept getFacDeptByCode(String code)
	{
		FacDept obj = null;
		EntityManager em = null;
		if (code != "") {
			try
			{
				em = getEntityManager();
				obj = em.find(FacDept.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;	
	}
	
	public List<FacDept> getFacDeptByFac(String fac) {
		List<FacDept> objList = null;
		
		if (!Strings.isNullOrEmpty(fac))
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM FacDept obj "
						+ " WHERE obj.fac = :fac ORDER BY obj.print_order ";		
				TypedQuery<FacDept> q = em.createQuery(query, FacDept.class);
				q.setParameter("fac", fac);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<StaffEligible> getStaffEligibleList()
	{
		List<StaffEligible> objList = null;
		EntityManager em = null;		

		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM StaffEligible obj ORDER BY obj.last_name, obj.first_name, obj.dept_code ";			
			TypedQuery<StaffEligible> q = em.createQuery(query, StaffEligible.class);

			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public void deleteStaffEligible(String employee_number, Integer dcc) throws Exception
	{
		if (!Strings.isNullOrEmpty(employee_number) && dcc != null)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM StaffEligible obj WHERE obj.pk.employee_number = :employee_number and obj.pk.dcc = :dcc");
				q.setParameter("employee_number", employee_number);
				q.setParameter("dcc", dcc);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete StaffEligible (employee_number=" + employee_number + ", dcc="+dcc+")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteAllStaffEligible() throws Exception
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			
			Query q = em.createQuery("DELETE FROM StaffEligible obj ");
			q.executeUpdate();
			
			utx.commit();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot delete Staff Eligible List", e);
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}

	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public StaffEligible updateStaffEligible(StaffEligible obj)
	{
		return updateEntity(obj);
	}
}