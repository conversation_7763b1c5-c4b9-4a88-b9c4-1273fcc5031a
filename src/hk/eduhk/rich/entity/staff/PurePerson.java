package hk.eduhk.rich.entity.staff;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;

@Entity
@Table(name = "RH_PURE_PERSON_V")
@SuppressWarnings("serial")
public class PurePerson
{
	public static Logger logger = Logger.getLogger(PurePerson.class.toString());
	
	@Id
	@Column(name = "PURE_SOURCE_ID")
	private String pure_source_id;	
	
	@Column(name = "pid")
	private int pid;

	@Column(name = "title")
	private String title;

	@Column(name = "surname_e")
	private String surname_e;

	@Column(name = "othername_e")
	private String othername_e;
	
	@Column(name = "chinesename_e")
	private String chinesename_e;
	
	@Column(name = "gender")
	private String gender;
	
	@Column(name = "orcid")
	private String orcid;
	
	@Column(name = "scopus_id_1")
	private String scopus_id_1;
	
	@Column(name = "scopus_id_2")
	private String scopus_id_2;

	@Column(name = "researcherid")
	private String researcherid;
	
	@Column(name = "photo")
	private byte[] photo;

	@Column(name = "profile")
	private String profile;
	
	@Column(name = "research_interest")
	private String research_interest;
	
	@Column(name = "teaching_interest")
	private String teaching_interest;
	
	@Column(name = "ext_appt")
	private String ext_appt;
	
	@Column(name = "url")
	private String url;
	
	@Column(name = "email")
	private String email;

	@Column(name = "visibility")
	private String visibility;
	
	@Column(name = "sdg_code")
	private String sdg_code;
	
	
	@Transient
	private List<String> sdg_list;
	
	@Transient
	private String photoFile;
	
	public String getPure_source_id()
	{
		return pure_source_id;
	}

	
	public void setPure_source_id(String pure_source_id)
	{
		this.pure_source_id = pure_source_id;
	}

	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}
	
	
	public String getSdg_code()
	{
		return sdg_code;
	}


	
	public void setSdg_code(String sdg_code)
	{
		this.sdg_code = sdg_code;
	}


	public List<String> getSdg_list()
	{
		sdg_list = new ArrayList<String>();
		if (sdg_code != null && sdg_list.size() == 0) {
			List <String> sdg_code_list =  Stream.of(getSdg_code().split(",")).collect(Collectors.toList());

			for(String i_sdg_index : sdg_code_list) {
				sdg_list.add(LookupValueDAO.getInstance().getLookupValue("SDG",i_sdg_index,"US").getShort_desc()) ;
			}
			
			
		}
		
		return sdg_list;
	}


	
	public void setSdg_list(List<String> sdg_list)
	{
		this.sdg_list = sdg_list;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getSurname_e()
	{
		return surname_e;
	}

	
	public void setSurname_e(String surname_e)
	{
		this.surname_e = surname_e;
	}

	
	public String getOthername_e()
	{
		return othername_e;
	}

	
	public void setOthername_e(String othername_e)
	{
		this.othername_e = othername_e;
	}

	
	public String getChinesename_e()
	{
		return chinesename_e;
	}

	
	public void setChinesename_e(String chinesename_e)
	{
		this.chinesename_e = chinesename_e;
	}

	
	public String getGender()
	{
		return gender;
	}

	
	public void setGender(String gender)
	{
		this.gender = gender;
	}

	
	public String getOrcid()
	{
		return orcid;
	}

	
	public void setOrcid(String orcid)
	{
		this.orcid = orcid;
	}

	
	public String getScopus_id_1()
	{
		return scopus_id_1;
	}

	
	public void setScopus_id_1(String scopus_id_1)
	{
		this.scopus_id_1 = scopus_id_1;
	}

	
	public String getScopus_id_2()
	{
		return scopus_id_2;
	}

	
	public void setScopus_id_2(String scopus_id_2)
	{
		this.scopus_id_2 = scopus_id_2;
	}

	
	public String getResearcherid()
	{
		return researcherid;
	}

	
	public void setResearcherid(String researcherid)
	{
		this.researcherid = researcherid;
	}

	
	public byte[] getPhoto()
	{
		return photo;
	}

	
	public void setPhoto(byte[] photo)
	{
		this.photo = photo;
	}

	
	public String getProfile()
	{
		return profile;
	}

	
	public void setProfile(String profile)
	{
		this.profile = profile;
	}

	
	public String getResearch_interest()
	{
		return research_interest;
	}

	
	public void setResearch_interest(String research_interest)
	{
		this.research_interest = research_interest;
	}

	
	public String getTeaching_interest()
	{
		return teaching_interest;
	}

	
	public void setTeaching_interest(String teaching_interest)
	{
		this.teaching_interest = teaching_interest;
	}

	
	public String getExt_appt()
	{
		return ext_appt;
	}

	
	public void setExt_appt(String ext_appt)
	{
		this.ext_appt = ext_appt;
	}

	
	public String getUrl()
	{
		return url;
	}

	
	public void setUrl(String url)
	{
		this.url = url;
	}

	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}

	
	public String getVisibility()
	{
		return visibility;
	}


	
	public void setVisibility(String visibility)
	{
		this.visibility = visibility;
	}


	public String getPhotoFile()
	{
		if (photoFile == null) {
			if (getPhoto() != null) {
				photoFile = Base64.getEncoder().encodeToString(getPhoto());
			}
		}
		return photoFile;
	}
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pure_source_id == null) ? 0 : pure_source_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PurePerson other = (PurePerson) obj;
		if (pure_source_id == null)
		{
			if (other.pure_source_id != null)
				return false;
		}
		else if (!pure_source_id.equals(other.pure_source_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PurePerson [pure_source_id=" + pure_source_id + ", pid=" + pid + ", title=" + title + ", surname_e="
				+ surname_e + ", othername_e=" + othername_e + ", chinesename_e=" + chinesename_e + ", gender=" + gender
				+ ", orcid=" + orcid + ", scopus_id_1=" + scopus_id_1 + ", scopus_id_2=" + scopus_id_2
				+ ", researcherid=" + researcherid + ", photo=" + Arrays.toString(photo) + ", profile=" + profile
				+ ", research_interest=" + research_interest + ", teaching_interest=" + teaching_interest
				+ ", ext_appt=" + ext_appt + ", url=" + url + ", email=" + email + ", visibility=" + visibility
				+ ", sdg_code=" + sdg_code + ", sdg_list=" + sdg_list + ", photoFile=" + photoFile + "]";
	}

	
}
