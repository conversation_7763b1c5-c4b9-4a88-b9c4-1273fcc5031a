package hk.eduhk.rich.entity.staff;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.publication.DisciplinaryArea;


@Embeddable
public class SecDataUser_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "USER_ID")
	private String user_id;	
	
	@Column(name = "DATA_TYPE")
	private String data_type;
	
	@Column(name = "DATA_CODE")
	private String data_code;

	
	public String getUser_id()
	{
		return user_id;
	}

	
	public void setUser_id(String user_id)
	{
		this.user_id = user_id;
	}

	
	public String getData_type()
	{
		return data_type;
	}

	
	public void setData_type(String data_type)
	{
		this.data_type = data_type;
	}

	
	public String getData_code()
	{
		return data_code;
	}

	
	public void setData_code(String data_code)
	{
		this.data_code = data_code;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((data_code == null) ? 0 : data_code.hashCode());
		result = prime * result + ((data_type == null) ? 0 : data_type.hashCode());
		result = prime * result + ((user_id == null) ? 0 : user_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SecDataUser_PK other = (SecDataUser_PK) obj;
		if (data_code == null)
		{
			if (other.data_code != null)
				return false;
		}
		else if (!data_code.equals(other.data_code))
			return false;
		if (data_type == null)
		{
			if (other.data_type != null)
				return false;
		}
		else if (!data_type.equals(other.data_type))
			return false;
		if (user_id == null)
		{
			if (other.user_id != null)
				return false;
		}
		else if (!user_id.equals(other.user_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SecDataUser_PK [user_id=" + user_id + ", data_type=" + data_type + ", data_code=" + data_code + "]";
	}

	
	


	

}
