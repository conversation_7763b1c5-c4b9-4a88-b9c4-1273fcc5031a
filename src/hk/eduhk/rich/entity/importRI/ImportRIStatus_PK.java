package hk.eduhk.rich.entity.importRI;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@Embeddable
public class ImportRIStatus_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "AREA_CODE")
	private String area_code;	
	
	@Column(name = "SOURCE_ID")
	private String source_id;
	
	@Column(name = "STAFF_NUMBER")
	private String staff_number;

	
	public String getArea_code()
	{
		return area_code;
	}

	
	public void setArea_code(String area_code)
	{
		this.area_code = area_code;
	}

	
	public String getSource_id()
	{
		return source_id;
	}

	
	public void setSource_id(String source_id)
	{
		this.source_id = source_id;
	}


	
	public String getStaff_number()
	{
		return staff_number;
	}


	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((area_code == null) ? 0 : area_code.hashCode());
		result = prime * result + ((source_id == null) ? 0 : source_id.hashCode());
		result = prime * result + ((staff_number == null) ? 0 : staff_number.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIStatus_PK other = (ImportRIStatus_PK) obj;
		if (area_code == null)
		{
			if (other.area_code != null)
				return false;
		}
		else if (!area_code.equals(other.area_code))
			return false;
		if (source_id == null)
		{
			if (other.source_id != null)
				return false;
		}
		else if (!source_id.equals(other.source_id))
			return false;
		if (staff_number == null)
		{
			if (other.staff_number != null)
				return false;
		}
		else if (!staff_number.equals(other.staff_number))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRIStatus_PK [area_code=" + area_code + ", source_id=" + source_id + ", staff_number="
				+ staff_number + "]";
	}



	



	


}
