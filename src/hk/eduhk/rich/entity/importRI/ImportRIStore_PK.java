package hk.eduhk.rich.entity.importRI;

import java.io.Serializable;

import javax.persistence.*;


@Embeddable
public class ImportRIStore_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "AREA_CODE")
	private String area_code;	
	
	@Column(name = "SOURCE_ID")
	private String source_id;
	
	@Column(name = "BATCH_ID")        
	private Integer batch_id;

	
	public String getArea_code()
	{
		return area_code;
	}

	
	public void setArea_code(String area_code)
	{
		this.area_code = area_code;
	}

	
	public String getSource_id()
	{
		return source_id;
	}

	
	public void setSource_id(String source_id)
	{
		this.source_id = source_id;
	}

	
	public Integer getBatch_id()
	{
		return batch_id;
	}


	
	public void setBatch_id(Integer batch_id)
	{
		this.batch_id = batch_id;
	}


	

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((area_code == null) ? 0 : area_code.hashCode());
		result = prime * result + ((batch_id == null) ? 0 : batch_id.hashCode());
		result = prime * result + ((source_id == null) ? 0 : source_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIStore_PK other = (ImportRIStore_PK) obj;
		if (area_code == null)
		{
			if (other.area_code != null)
				return false;
		}
		else if (!area_code.equals(other.area_code))
			return false;
		if (batch_id == null)
		{
			if (other.batch_id != null)
				return false;
		}
		else if (!batch_id.equals(other.batch_id))
			return false;
		if (source_id == null)
		{
			if (other.source_id != null)
				return false;
		}
		else if (!source_id.equals(other.source_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRIStore_PK [area_code=" + area_code + ", source_id=" + source_id + ", batch_id=" + batch_id + "]";
	}

	


}
