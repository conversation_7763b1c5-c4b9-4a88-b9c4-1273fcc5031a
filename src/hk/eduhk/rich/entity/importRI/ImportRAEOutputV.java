package hk.eduhk.rich.entity.importRI;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PublicationDAO;


@Entity
@Table(name = "RH_IMPORT_RAE_V")
public class ImportRAEOutputV
{
	public static Logger logger = Logger.getLogger(ImportRAEOutputV.class.toString());
	
	@Id
	@Column(name = "OUTPUT_NO")
	private Integer output_no;
	
	@Column(name = "STAFF_NO")
	private String staff_no;    
	
	@Column(name = "APA_CITATION")     
	private String apa_citation;
	
	@Column(name = "SAP_OUTPUT_TYPE")  
	private String sap_output_type;
	
	@Column(name = "SAP_OUTPUT_TYPE_DESC")  
	private String sap_output_type_desc;
	
	@Column(name = "SAP_REFERED_JOURNAL")
	private String sap_refered_journal;
	
	@Column(name = "LANGUAGE")         
	private String language;
	                               
	@Column(name = "TITLE_OF_OUTPUT")  
	private String title_of_output;
	
	@Column(name = "TITLE_PAPER_ART")  
	private String title_paper_art;           
	                                
	@Column(name = "TOTAL_NO_OF_AUTHOR")  
	private String total_no_of_author;
	
	@Column(name = "NAME_OTHER_POS")             
	private String name_other_pos;        

	@Column(name = "VOL_ISSUE")        
	private String vol_issue;
	                                   
	@Column(name = "PAGE_NUM")         
	private String page_num;
	
	@Column(name = "FROM_MONTH")       
	private String from_month;
	                                   
	@Column(name = "FROM_YEAR")        
	private String from_year;
	
	@Column(name = "TO_MONTH")       
	private String to_month;
	                                   
	@Column(name = "TO_YEAR")        
	private String to_year;
	
	@Column(name = "PUBLISHER")        
	private String publisher;
	
	@Column(name = "ISBN")             
	private String isbn;
	
	@Column(name = "ISSN")             
	private String issn;
	
	@Column(name = "EISSN")             
	private String eissn;
	
	@Column(name = "DOI")             
	private String doi;
	
	@Column(name = "FULLTEXT_URL")             
	private String fulltext_url;

	@Column(name = "ARTICLE_NUM")             
	private String article_num;
	
	@Column(name = "CITY")             
	private String city;
	                                   	
	public String getSap_output_type()
	{
		if(sap_output_type == null) return " ";
		else return sap_output_type;
	}
	
	public void setSap_output_type(String sap_output_type)
	{
		this.sap_output_type = sap_output_type;
	}

	
	public String getTitle_paper_art()
	{
		if(title_paper_art == null) return " ";
		else return title_paper_art;
	}

	
	public void setTitle_paper_art(String title_paper_art)
	{
		this.title_paper_art = title_paper_art;
	}

	
	public String getVol_issue()
	{
		if(vol_issue == null) return " ";
		else return vol_issue;
	}

	
	public void setVol_issue(String vol_issue)
	{
		this.vol_issue = vol_issue;
	}

	
	public String getPage_num()
	{
		if(page_num == null) return " ";
		else return page_num;
	}

	
	public void setPage_num(String page_num)
	{
		this.page_num = page_num;
	}
	
	

	
	
	
	public String getSap_refered_journal()
	{
		return sap_refered_journal;
	}

	
	public void setSap_refered_journal(String sap_refered_journal)
	{
		this.sap_refered_journal = sap_refered_journal;
	}

	public String getFrom_month()
	{
		if(from_month == null) return " ";
		else return from_month;
	}

	
	public void setFrom_month(String from_month)
	{
		this.from_month = from_month;
	}

	
	public String getFrom_year()
	{
		if(from_year == null) return " ";
		else return from_year;
	}

	
	public void setFrom_year(String from_year)
	{
		this.from_year = from_year;
	}

	
	public String getPublisher()
	{
		if(publisher == null) return "publisher ";
		else return publisher;
	}

	
	public void setPublisher(String publisher)
	{
		this.publisher = publisher;
	}

	
	public String getLanguage()
	{
		if(language == null) return " ";
		else return language;
	}

	
	public void setLanguage(String language)
	{
		this.language = language;
	}

	
	public String getApa_citation()
	{
		if(apa_citation == null) return " ";
		else return apa_citation;
	}

	
	public void setApa_citation(String apa_citation)
	{
		this.apa_citation = apa_citation;
	}

	
	public String getIssn()
	{
		if(issn == null) return " ";
		else return issn;
	}

	
	public void setIssn(String issn)
	{
		this.issn = issn;
	}

	


	
	public String getFulltext_url()
	{
		return fulltext_url;
	}


	
	public void setFulltext_url(String fulltext_url)
	{
		this.fulltext_url = fulltext_url;
	}


	
	public String getDoi()
	{
		return doi;
	}


	
	public void setDoi(String doi)
	{
		this.doi = doi;
	}
	
	public String getArticle_num()
	{
		return article_num;
	}


	
	public void setArticle_num(String article_num)
	{
		this.article_num = article_num;
	}


	
	public Integer getOutput_no()
	{
		return output_no;
	}


	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}


	
	public String getStaff_no()
	{
		return staff_no;
	}


	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	
	public String getSap_output_type_desc()
	{
		return sap_output_type_desc;
	}


	
	public void setSap_output_type_desc(String sap_output_type_desc)
	{
		this.sap_output_type_desc = sap_output_type_desc;
	}


	
	public String getTitle_of_output()
	{
		return title_of_output;
	}


	
	public void setTitle_of_output(String title_of_output)
	{
		this.title_of_output = title_of_output;
	}


	
	public String getTotal_no_of_author()
	{
		return total_no_of_author;
	}


	
	public void setTotal_no_of_author(String total_no_of_author)
	{
		this.total_no_of_author = total_no_of_author;
	}


	
	public String getName_other_pos()
	{
		return name_other_pos;
	}


	
	public void setName_other_pos(String name_other_pos)
	{
		this.name_other_pos = name_other_pos;
	}


	
	public String getTo_month()
	{
		return to_month;
	}


	
	public void setTo_month(String to_month)
	{
		this.to_month = to_month;
	}


	
	public String getTo_year()
	{
		return to_year;
	}


	
	public void setTo_year(String to_year)
	{
		this.to_year = to_year;
	}


	
	public String getIsbn()
	{
		return isbn;
	}


	
	public void setIsbn(String isbn)
	{
		this.isbn = isbn;
	}


	
	public String getEissn()
	{
		return eissn;
	}


	
	public void setEissn(String eissn)
	{
		this.eissn = eissn;
	}
	
	public String getCity()
	{
		return city;
	}

	
	public void setCity(String city)
	{
		this.city = city;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((output_no == null) ? 0 : output_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRAEOutputV other = (ImportRAEOutputV) obj;
		if (output_no == null)
		{
			if (other.output_no != null)
				return false;
		}
		else if (!output_no.equals(other.output_no))
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "ImportRAEOutputV [output_no=" + output_no + ", staff_no=" + staff_no + ", apa_citation=" + apa_citation
				+ ", sap_output_type=" + sap_output_type + ", sap_output_type_desc=" + sap_output_type_desc
				+ ", sap_refered_journal=" + sap_refered_journal + ", language=" + language + ", title_of_output="
				+ title_of_output + ", title_paper_art=" + title_paper_art + ", total_no_of_author="
				+ total_no_of_author + ", name_other_pos=" + name_other_pos + ", vol_issue=" + vol_issue + ", page_num="
				+ page_num + ", from_month=" + from_month + ", from_year=" + from_year + ", to_month=" + to_month
				+ ", to_year=" + to_year + ", publisher=" + publisher + ", isbn=" + isbn + ", issn=" + issn + ", eissn="
				+ eissn + ", doi=" + doi + ", fulltext_url=" + fulltext_url + ", article_num=" + article_num + ", city="
				+ city + "]";
	}

}
