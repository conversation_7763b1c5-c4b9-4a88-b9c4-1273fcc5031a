package hk.eduhk.rich.entity.importRI;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.entity.project.ProjectDAO;


@Entity
@Table(name = "RH_IMPORT_AWARD_V")
public class ImportRIAwardV
{
	public static Logger logger = Logger.getLogger(ImportRIAwardV.class.toString());
	
	@EmbeddedId
	private ImportRIAwardV_PK pk;
	
	@Column(name = "AREA_DESCRIPTION")
	private String area_description;
	                                   
	@Column(name = "PID")              
	private String pid;
	                                   
	@Column(name = "RECIPIENT_NAME")  
	private String recipient_name;
    
	@Column(name = "RECIPIENT_ID")  
	private String recipient_id;
	                                   
	@Column(name = "AWARD_NAME")  
	private String award_name;
	
	@Column(name = "ORG_NAME")  
	private String org_name;
	
	@Column(name = "FULL_DESC")  
	private String full_desc;
	
	@Column(name = "AWARD_DAY")  
	private String award_day;
	
	@Column(name = "AWARD_MONTH")  
	private String award_month;
	                                   
	@Column(name = "AWARD_YEAR")         
	private String award_year;
	
	@Column(name = "IMPORT_STATUS")    
	private String import_status;
	
	@Column(name = "RECIPIENT_ID_LIST")    
	private String recipient_id_list;


	
	public ImportRIAwardV_PK getPk()
	{
		return pk;
	}


	
	public void setPk(ImportRIAwardV_PK pk)
	{
		this.pk = pk;
	}


	
	public String getArea_description()
	{
		return area_description;
	}


	
	public void setArea_description(String area_description)
	{
		this.area_description = area_description;
	}


	
	public String getPid()
	{
		return pid;
	}


	
	public void setPid(String pid)
	{
		this.pid = pid;
	}


	
	public String getRecipient_name()
	{
		return recipient_name;
	}


	
	public void setRecipient_name(String recipient_name)
	{
		this.recipient_name = recipient_name;
	}


	
	public String getRecipient_id()
	{
		return recipient_id;
	}


	
	public void setRecipient_id(String recipient_id)
	{
		this.recipient_id = recipient_id;
	}


	
	public String getAward_name()
	{
		return award_name;
	}


	
	public void setAward_name(String award_name)
	{
		this.award_name = award_name;
	}


	
	public String getOrg_name()
	{
		return org_name;
	}


	
	public void setOrg_name(String org_name)
	{
		this.org_name = org_name;
	}


	
	public String getFull_desc()
	{
		return full_desc;
	}


	
	public void setFull_desc(String full_desc)
	{
		this.full_desc = full_desc;
	}


	
	public String getAward_day()
	{
		return award_day;
	}


	
	public void setAward_day(String award_day)
	{
		this.award_day = award_day;
	}


	
	public String getAward_month()
	{
		return award_month;
	}


	
	public void setAward_month(String award_month)
	{
		this.award_month = award_month;
	}


	
	public String getAward_year()
	{
		return award_year;
	}


	
	public void setAward_year(String award_year)
	{
		this.award_year = award_year;
	}


	
	public String getImport_status()
	{
		return import_status;
	}


	
	public void setImport_status(String import_status)
	{
		this.import_status = import_status;
	}


	
	public String getRecipient_id_list()
	{
		return recipient_id_list;
	}



	
	public void setRecipient_id_list(String recipient_id_list)
	{
		this.recipient_id_list = recipient_id_list;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIAwardV other = (ImportRIAwardV) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "ImportRIAwardV [pk=" + pk + ", area_description=" + area_description + ", pid=" + pid
				+ ", recipient_name=" + recipient_name + ", recipient_id=" + recipient_id + ", award_name=" + award_name
				+ ", org_name=" + org_name + ", full_desc=" + full_desc + ", award_day="
				+ award_day + ", award_month=" + award_month + ", award_year=" + award_year + ", import_status="
				+ import_status + ", recipient_id_list=" + recipient_id_list + "]";
	}
	


}
