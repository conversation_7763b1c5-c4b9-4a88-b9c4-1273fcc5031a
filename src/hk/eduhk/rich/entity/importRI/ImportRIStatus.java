package hk.eduhk.rich.entity.importRI;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_D_IMPORT_STATUS")
public class ImportRIStatus extends UserPersistenceObject
{
	public static final String statusNew = "NEW";
	public static final String statusIgnore = "IGNORE";
	
	public static Logger logger = Logger.getLogger(ImportRIStatus.class.toString());
	
	@EmbeddedId     
	private ImportRIStatus_PK pk;
	                                  
	@Column(name = "PID")        
	private Integer pid;
	                                  
	@Column(name = "IMPORT_STATUS")  
	private String import_status;
	                                  
	@Column(name = "IGNORE_REASON")           
	private String ignore_reason;
	                                  
	@Column(name = "RI_NO")           
	private Integer ri_no;

	
	public ImportRIStatus_PK getPk()
	{
		return pk;
	}

	
	public void setPk(ImportRIStatus_PK pk)
	{
		this.pk = pk;
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public String getImport_status()
	{
		return import_status;
	}

	
	public void setImport_status(String import_status)
	{
		this.import_status = import_status;
	}

	
	public String getIgnore_reason()
	{
		return ignore_reason;
	}

	
	public void setIgnore_reason(String ignore_reason)
	{
		this.ignore_reason = ignore_reason;
	}

	
	public Integer getRi_no()
	{
		return ri_no;
	}

	
	public void setRi_no(Integer ri_no)
	{
		this.ri_no = ri_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIStatus other = (ImportRIStatus) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRIStatus [pk=" + pk + ", pid=" + pid + ", import_status=" + import_status + ", ignore_reason="
				+ ignore_reason + ", ri_no=" + ri_no + "]";
	}
	                                  
	






	


}
