package hk.eduhk.rich.entity.importRI;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.util.PersistenceManager;

@SuppressWarnings("serial")
public class ImportRIDAO extends BaseDAO
{

	private static ImportRIDAO instance = null;


	public static synchronized ImportRIDAO getInstance()
	{
		if (instance == null) instance = new ImportRIDAO();
		return instance;
	}
	
	
	public static ImportRIDAO getCacheInstance()
	{
		return ImportRIDAO.getInstance();
	}
	
	public List<ImportRIOutputV> getNewImportRIOutputV(String staffId)
	{
		List<ImportRIOutputV> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRIOutputV obj WHERE obj.import_status = 'NEW' " +
							//" AND obj.pk.staff_number = :staffId";
							" AND obj.pid = :staffId " +
							" ORDER BY obj.from_year DESC, obj.from_month DESC NULLS LAST";	
			TypedQuery<ImportRIOutputV> q = em.createQuery(query, ImportRIOutputV.class);
			q.setParameter("staffId", staffId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ImportRAEOutputV> getImportRAEOutputV(String staffNumber)
	{
		List<ImportRAEOutputV> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRAEOutputV obj WHERE obj.staff_no = :staffNumber order by obj.from_year desc , obj.from_month desc";
			TypedQuery<ImportRAEOutputV> q = em.createQuery(query, ImportRAEOutputV.class);
			q.setParameter("staffNumber", staffNumber);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	
	public List<ImportRIProjectV> getNewImportRIProjectV(String staffId)
	{
		List<ImportRIProjectV> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRIProjectV obj WHERE obj.import_status = 'NEW' " +
							//" AND obj.pk.staff_number = :staffId";		
							" AND obj.pid = :staffId " +
							" ORDER BY obj.from_year DESC, obj.from_month DESC NULLS LAST, obj.from_day DESC NULLS LAST";	
			TypedQuery<ImportRIProjectV> q = em.createQuery(query, ImportRIProjectV.class);
			q.setParameter("staffId", staffId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ImportRIAwardV> getNewImportRIAwardV(String staffId)
	{
		List<ImportRIAwardV> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRIAwardV obj WHERE obj.import_status = 'NEW' " +
							//" AND obj.pk.staff_number = :staffId";		
							" AND obj.pid = :staffId " +
							" ORDER BY obj.award_year DESC, obj.award_month DESC NULLS LAST, obj.award_day DESC NULLS LAST";	
			TypedQuery<ImportRIAwardV> q = em.createQuery(query, ImportRIAwardV.class);
			q.setParameter("staffId", staffId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ImportRIPatentV> getNewImportRIPatentV(String staffId)
	{
		List<ImportRIPatentV> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRIPatentV obj WHERE obj.import_status = 'NEW' " +
							//" AND obj.pk.staff_number = :staffId";		
							" AND obj.pid = :staffId " +
							" ORDER BY obj.patent_year DESC, obj.patent_month DESC NULLS LAST, obj.patent_day DESC NULLS LAST";	
			TypedQuery<ImportRIPatentV> q = em.createQuery(query, ImportRIPatentV.class);
			q.setParameter("staffId", staffId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public Integer getNumberOfImportRIOutput(String staffId) {
		Long obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT COUNT(*) FROM ImportRIOutputV obj WHERE obj.import_status = 'NEW' " +
							" AND obj.pk.staff_number = :staffId";			
			TypedQuery<Long> q = em.createQuery(query, Long.class);
			q.setParameter("staffId", staffId);
			obj = q.getSingleResult();
		}
		finally
		{
			pm.close(em);
		}
		return obj != null?obj.intValue():null;
	}
	
	public Integer getNumberOfImportRIProject(String staffId) {
		Long obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT COUNT(*) FROM ImportRIProjectV obj WHERE obj.import_status = 'NEW' " +
							" AND obj.pk.staff_number = :staffId";			
			TypedQuery<Long> q = em.createQuery(query, Long.class);
			q.setParameter("staffId", staffId);
			obj = q.getSingleResult();
		}
		finally
		{
			pm.close(em);
		}
		return obj != null?obj.intValue():0;
	}
	
	public Integer getNumberOfImportRIAward(String staffId) {
		Long obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT COUNT(*) FROM ImportRIAwardV obj WHERE obj.import_status = 'NEW' " +
							" AND obj.pk.staff_number = :staffId";			
			TypedQuery<Long> q = em.createQuery(query, Long.class);
			q.setParameter("staffId", staffId);
			obj = q.getSingleResult();
		}
		finally
		{
			pm.close(em);
		}
		return obj != null?obj.intValue():0;
	}
	
	public Integer getNumberOfImportRIPatent(String staffId) {
		Long obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT COUNT(*) FROM ImportRIPatentV obj WHERE obj.import_status = 'NEW' " +
							" AND obj.pk.staff_number = :staffId";			
			TypedQuery<Long> q = em.createQuery(query, Long.class);
			q.setParameter("staffId", staffId);
			obj = q.getSingleResult();
		}
		finally
		{
			pm.close(em);
		}
		return obj != null?obj.intValue():0;
	}
	
	public List<ImportRICA> getImportRICAList(List<String> areaCode)
	{
		List<ImportRICA> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRICA obj WHERE 1=1 ";
			if(areaCode != null)		
				query += " AND obj.area_code IN :areaCode";	
			TypedQuery<ImportRICA> q = em.createQuery(query, ImportRICA.class);
			if(areaCode != null)
				q.setParameter("areaCode", areaCode);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ImportRIBatch> getImportRIBatchList(String areaCode)
	{
		List<ImportRIBatch> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ImportRIBatch obj WHERE 1=1 ";
			if(areaCode != null)		
				query += " AND obj.area_code = :areaCode";	
			TypedQuery<ImportRIBatch> q = em.createQuery(query, ImportRIBatch.class);
			q.setParameter("areaCode", areaCode);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ImportRIStore> getImportRIStoreList(ImportRIBatch batch)
	{
		List<ImportRIStore> objList = null;
		EntityManager em = null;		
		if(batch != null) {
			try
			{
				String areaCode = batch.getArea_code();
				Integer batchId = batch.getBatch_id();
				
				em = getEntityManager();		
				String query = "SELECT obj FROM ImportRIStore obj WHERE 1=1 "	
								+ " AND obj.pk.area_code = :areaCode"	
								+ " AND obj.pk.batch_id = :batchId";	
				TypedQuery<ImportRIStore> q = em.createQuery(query, ImportRIStore.class);
				q.setParameter("areaCode", areaCode);
				q.setParameter("batchId", batchId);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public ImportRIOutputV getImportRIOutputByPK(ImportRIOutputV_PK pk)
	{
		ImportRIOutputV obj = null;
		
		if (pk != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(ImportRIOutputV.class, pk);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIProjectV getImportRIProjectByPK(ImportRIProjectV_PK pk)
	{
		ImportRIProjectV obj = null;
		
		if (pk != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(ImportRIProjectV.class, pk);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIAwardV getImportRIAwardByPK(ImportRIAwardV_PK pk)
	{
		ImportRIAwardV obj = null;
		
		if (pk != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(ImportRIAwardV.class, pk);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIPatentV getImportRIPatentByPK(ImportRIPatentV_PK pk)
	{
		ImportRIPatentV obj = null;
		
		if (pk != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(ImportRIPatentV.class, pk);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIStatus getStatusByPK(ImportRIStatus_PK pk)
	{
		ImportRIStatus obj = null;
		
		if (pk != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(ImportRIStatus.class, pk);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIBatch getBatchByPK(Integer pk)
	{
		ImportRIBatch obj = null;
		EntityManager em = null;
		if(pk != null) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM ImportRIBatch obj WHERE obj.batch_id = :pk ";			
				TypedQuery<ImportRIBatch> q = em.createQuery(query, ImportRIBatch.class);
				q.setParameter("pk", pk);
				obj = q.getSingleResult();
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public ImportRIBatch getBatchByAreaCodeAndKey(String areaCode, String key)
	{
		ImportRIBatch obj = null;
		EntityManager em = null;
		if(areaCode != null && key != null) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM ImportRIBatch obj WHERE obj.area_code = :areaCode AND "
						+ "obj.batch_key = :key";			
				TypedQuery<ImportRIBatch> q = em.createQuery(query, ImportRIBatch.class);
				q.setParameter("areaCode", areaCode);
				q.setParameter("key", key);
				obj = q.getSingleResult();
			}
			catch(NoResultException e) {
				obj = null;
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public ImportRICA getCAByPK(String pk)
	{
		ImportRICA obj = null;
		EntityManager em = null;
		if(pk != null) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM ImportRICA obj WHERE obj.area_code = :pk ";			
				TypedQuery<ImportRICA> q = em.createQuery(query, ImportRICA.class);
				q.setParameter("pk", pk);
				obj = q.getSingleResult();
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public ImportRIStatus updateStatus(ImportRIStatus obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIBatch updateBatch(ImportRIBatch obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public ImportRIStore updateStore(ImportRIStore obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void runBuiltStoreStaff(String area_code) throws SQLException
	{
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(!StringUtils.isBlank(area_code)) {
			try
			{
				conn = pm.getConnection();
	
				String query = " begin " + 
						" RICH.RH_BUILT_STORE_STAFF_P('" + area_code + "'); " + 
						" end;";
	
				//System.out.println("get award: " + query);      
				pStmt = conn.prepareStatement(query);
				pStmt.executeQuery();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
	}
	
	public void deleteBatch(Integer batch_id) throws Exception
	{
		if (batch_id != null)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM ImportRIBatch obj WHERE obj.batch_id = :batch_id ");
				q.setParameter("batch_id", batch_id);
				q.executeUpdate();
				
				q = em.createQuery("DELETE FROM ImportRIStore obj WHERE obj.pk.batch_id = :batch_id ");
				q.setParameter("batch_id", batch_id);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete batch (batch_id=" + batch_id + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
}
