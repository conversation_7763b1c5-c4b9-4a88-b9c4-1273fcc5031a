package hk.eduhk.rich.entity.importRI;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PublicationDAO;


@Entity
@Table(name = "RH_IMPORT_OUTPUT_V")
public class ImportRIOutputV
{
	public static Logger logger = Logger.getLogger(ImportRIOutputV.class.toString());
	
	@EmbeddedId
	private ImportRIOutputV_PK pk;
	
	@Column(name = "AREA_DESCRIPTION")
	private String area_description;
	                                   
	@Column(name = "PID")              
	private String pid;
	                                   
	@Column(name = "AUTHORSHIP_NAME")  
	private String authorship_name;
	                                   
	@Column(name = "TITLE_JOUR_BOOK")  
	private String title_jour_book;
	                                   
	@Column(name = "OUTPUT_CATEGORY")  
	private String output_category;
	                                   
	@Column(name = "SAP_OUTPUT_TYPE")  
	private String sap_output_type;
	                                   
	@Column(name = "TITLE_PAPER_ART")  
	private String title_paper_art;
	                                   
	@Column(name = "VOL_ISSUE")        
	private String vol_issue;
	                                   
	@Column(name = "PAGE_NUM")         
	private String page_num;
	                                   
	@Column(name = "PAGE_NUM_FROM")    
	private String page_num_from;
	                                   
	@Column(name = "PAGE_NUM_TO")      
	private String page_num_to;
	                                   
	@Column(name = "CITY")             
	private String city;
	                                   
	@Column(name = "FROM_MONTH")       
	private String from_month;
	                                   
	@Column(name = "FROM_YEAR")        
	private String from_year;
	                                   
	@Column(name = "PUBLISHER")        
	private String publisher;
	                                   
	@Column(name = "LANGUAGE")         
	private String language;
	                                   
	@Column(name = "APA_CITATION")     
	private String apa_citation;
	                                   
	@Column(name = "ISSN")             
	private String issn;
	
	@Column(name = "PRINT_ISBN")             
	private String print_isbn;
	
	@Column(name = "ELECTRONIC_ISBN")             
	private String electronic_isbn;
	
	@Column(name = "FULLTEXT_URL")             
	private String fulltext_url;
	
	@Column(name = "DOI")             
	private String doi;
	
	@Column(name = "NAME_OTHER_EDITORS")             
	private String name_other_editors;
	
	@Column(name = "ARTICLE_NUM")             
	private String article_num;
	                                   
	@Column(name = "LAST_UPDATE_DATE") 
	private String last_update_date;
	                                   
	@Column(name = "IMPORT_STATUS")    
	private String import_status;
	
	@Column(name = "CONTRIBUTOR_ID_LIST")    
	private String contributor_id_list;
	
	private static List<OutputType> outputTypeList;

	
	public ImportRIOutputV_PK getPk()
	{
		return pk;
	}

	
	public void setPk(ImportRIOutputV_PK pk)
	{
		this.pk = pk;
	}

	
	public String getArea_description()
	{
		if(area_description == null) return " ";
		else return area_description;
	}

	
	public void setArea_description(String area_description)
	{
		this.area_description = area_description;
	}

	
	public String getPid()
	{
		if(pid == null) return " ";
		else return pid;
	}

	
	public void setPid(String pid)
	{
		this.pid = pid;
	}

	
	public String getAuthorship_name()
	{
		if(authorship_name == null) return " ";
		else return authorship_name;
	}

	
	public void setAuthorship_name(String authorship_name)
	{
		this.authorship_name = authorship_name;
	}

	
	public String getTitle_jour_book()
	{
		if(title_jour_book == null) return " ";
		else return title_jour_book;
	}

	
	public void setTitle_jour_book(String title_jour_book)
	{
		this.title_jour_book = title_jour_book;
	}

	
	public String getOutput_category()
	{
		if(output_category == null) return " ";
		else return output_category;
	}
	
	
	public String getOutput_category_desc()
	{
		String returnStr = " ";
		if(output_category == null || 
				getOutputTypeList() == null ||
				getOutputTypeList().isEmpty()) return returnStr;
		else {
			for(OutputType o : getOutputTypeList()) {
				if(o.getPk().getLookup_code().equals(output_category)) {
					returnStr = o.getDescription();
					break;
				}
			}
			return returnStr;
		}
	}

	
	public void setOutput_category(String output_category)
	{
		this.output_category = output_category;
	}

	
	public String getSap_output_type()
	{
		if(sap_output_type == null) return " ";
		else return sap_output_type;
	}

	
	public void setSap_output_type(String sap_output_type)
	{
		this.sap_output_type = sap_output_type;
	}

	
	public String getTitle_paper_art()
	{
		if(title_paper_art == null) return " ";
		else return title_paper_art;
	}

	
	public void setTitle_paper_art(String title_paper_art)
	{
		this.title_paper_art = title_paper_art;
	}

	
	public String getVol_issue()
	{
		if(vol_issue == null) return " ";
		else return vol_issue;
	}

	
	public void setVol_issue(String vol_issue)
	{
		this.vol_issue = vol_issue;
	}

	
	public String getPage_num()
	{
		if(page_num == null) return " ";
		else return page_num;
	}

	
	public void setPage_num(String page_num)
	{
		this.page_num = page_num;
	}

	
	public String getPage_num_from()
	{
		if(page_num_from == null) return " ";
		else return page_num_from;
	}

	
	public void setPage_num_from(String page_num_from)
	{
		this.page_num_from = page_num_from;
	}

	
	public String getPage_num_to()
	{
		if(page_num_to == null) return " ";
		else return page_num_to;
	}

	
	public void setPage_num_to(String page_num_to)
	{
		this.page_num_to = page_num_to;
	}

	
	public String getCity()
	{
		if(city == null) return " ";
		else return city;
	}

	
	public void setCity(String city)
	{
		this.city = city;
	}

	
	public String getFrom_month()
	{
		if(from_month == null) return " ";
		else return from_month;
	}

	
	public void setFrom_month(String from_month)
	{
		this.from_month = from_month;
	}

	
	public String getFrom_year()
	{
		if(from_year == null) return " ";
		else return from_year;
	}

	
	public void setFrom_year(String from_year)
	{
		this.from_year = from_year;
	}

	
	public String getPublisher()
	{
		if(publisher == null) return "publisher ";
		else return publisher;
	}

	
	public void setPublisher(String publisher)
	{
		this.publisher = publisher;
	}

	
	public String getLanguage()
	{
		if(language == null) return " ";
		else return language;
	}

	
	public void setLanguage(String language)
	{
		this.language = language;
	}

	
	public String getApa_citation()
	{
		if(apa_citation == null) return " ";
		else return apa_citation;
	}

	
	public void setApa_citation(String apa_citation)
	{
		this.apa_citation = apa_citation;
	}

	
	public String getIssn()
	{
		if(issn == null) return " ";
		else return issn;
	}

	
	public void setIssn(String issn)
	{
		this.issn = issn;
	}

	
	public String getLast_update_date()
	{
		if(last_update_date == null) return " ";
		else return last_update_date;
	}

	
	public void setLast_update_date(String last_update_date)
	{
		this.last_update_date = last_update_date;
	}

	
	public String getImport_status()
	{
		if(import_status == null) return " ";
		else return import_status;
	}

	
	public void setImport_status(String import_status)
	{
		this.import_status = import_status;
	}
	
	
	public String getContributor_id_list()
	{
		return contributor_id_list;
	}


	
	public void setContributor_id_list(String contributor_id_list)
	{
		this.contributor_id_list = contributor_id_list;
	}


	
	public String getPrint_isbn()
	{
		return print_isbn;
	}


	
	public void setPrint_isbn(String print_isbn)
	{
		this.print_isbn = print_isbn;
	}


	
	public String getElectronic_isbn()
	{
		return electronic_isbn;
	}


	
	public void setElectronic_isbn(String electronic_isbn)
	{
		this.electronic_isbn = electronic_isbn;
	}


	
	public String getFulltext_url()
	{
		return fulltext_url;
	}


	
	public void setFulltext_url(String fulltext_url)
	{
		this.fulltext_url = fulltext_url;
	}


	
	public String getDoi()
	{
		return doi;
	}


	
	public void setDoi(String doi)
	{
		this.doi = doi;
	}


	
	public String getName_other_editors()
	{
		return name_other_editors;
	}


	
	public void setName_other_editors(String name_other_editors)
	{
		this.name_other_editors = name_other_editors;
	}


	
	public String getArticle_num()
	{
		return article_num;
	}


	
	public void setArticle_num(String article_num)
	{
		this.article_num = article_num;
	}


	public List<OutputType> getOutputTypeList()
	{
		if(outputTypeList == null) {
			PublicationDAO dao = PublicationDAO.getCacheInstance();
			outputTypeList = dao.getOutputTypeList(2);
		}
		return outputTypeList;
	}

	public String getFirstISBN() {
		if(print_isbn != null) {
			String[] isbnArr = print_isbn.split(", ");
			return isbnArr[0];
		}
		else if (electronic_isbn != null) {
			String[] isbnArr = electronic_isbn.split(", ");
			return isbnArr[0];
		}
		else return null;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIOutputV other = (ImportRIOutputV) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRIOutputV [pk=" + pk + ", area_description=" + area_description + ", pid=" + pid
				+ ", authorship_name=" + authorship_name + ", title_jour_book=" + title_jour_book + ", output_category="
				+ output_category + ", sap_output_type=" + sap_output_type + ", title_paper_art=" + title_paper_art
				+ ", vol_issue=" + vol_issue + ", page_num=" + page_num + ", page_num_from=" + page_num_from
				+ ", page_num_to=" + page_num_to + ", city=" + city + ", from_month=" + from_month + ", from_year="
				+ from_year + ", publisher=" + publisher + ", language=" + language + ", apa_citation=" + apa_citation
				+ ", issn=" + issn + ", print_isbn=" + print_isbn + ", electronic_isbn=" + electronic_isbn
				+ ", fulltext_url=" + fulltext_url + ", doi=" + doi + ", name_other_editors=" + name_other_editors
				+ ", article_num=" + article_num + ", last_update_date=" + last_update_date + ", import_status="
				+ import_status + ", contributor_id_list=" + contributor_id_list + "]";
	}


}
