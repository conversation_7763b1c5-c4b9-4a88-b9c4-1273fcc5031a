package hk.eduhk.rich.entity.importRI;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.patent.PatentCountry;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentType;
import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.entity.project.ProjectDAO;


@Entity
@Table(name = "RH_IMPORT_PATENT_V")
public class ImportRIPatentV
{
	public static Logger logger = Logger.getLogger(ImportRIPatentV.class.toString());
	
	@EmbeddedId
	private ImportRIPatentV_PK pk;
	
	@Column(name = "AREA_DESCRIPTION")
	private String area_description;
	                                   
	@Column(name = "PID")              
	private String pid;
	                                   
	@Column(name = "INVENTOR_NAME")  
	private String inventor_name;
    
	@Column(name = "INVENTOR_ID")  
	private String inventor_id;
	                                   
	@Column(name = "PATENT_GRANTED")  
	private String patent_granted;
	                                   
	@Column(name = "PATENT_NAME")  
	private String patent_name;
    
	@Column(name = "SERIAL_NO")  
	private String serial_no;
	                                   
	@Column(name = "COUNTRY")  
	private String country;
	
	@Column(name = "FULL_DESC")  
	private String full_desc;
	
	@Column(name = "PATENT_DAY")  
	private String patent_day;
	
	@Column(name = "PATENT_MONTH")  
	private String patent_month;
	                                   
	@Column(name = "PATENT_YEAR")         
	private String patent_year;
	
	@Column(name = "IMPORT_STATUS")    
	private String import_status;
	
	@Column(name = "INVENTOR_ID_LIST")    
	private String inventor_id_list;

	private static List<PatentCountry> countryList;
	
	
	public ImportRIPatentV_PK getPk()
	{
		return pk;
	}


	
	public void setPk(ImportRIPatentV_PK pk)
	{
		this.pk = pk;
	}


	
	public String getArea_description()
	{
		return area_description;
	}


	
	public void setArea_description(String area_description)
	{
		this.area_description = area_description;
	}


	
	public String getPid()
	{
		return pid;
	}


	
	public void setPid(String pid)
	{
		this.pid = pid;
	}


	
	public String getInventor_name()
	{
		return inventor_name;
	}


	
	public void setInventor_name(String inventor_name)
	{
		this.inventor_name = inventor_name;
	}


	
	public String getInventor_id()
	{
		return inventor_id;
	}


	
	public void setInventor_id(String inventor_id)
	{
		this.inventor_id = inventor_id;
	}


	
	public String getPatent_granted()
	{
		return patent_granted;
	}


	
	public void setPatent_granted(String patent_granted)
	{
		this.patent_granted = patent_granted;
	}


	
	public String getPatent_name()
	{
		return patent_name;
	}


	
	public void setPatent_name(String patent_name)
	{
		this.patent_name = patent_name;
	}


	
	public String getSerial_no()
	{
		return serial_no;
	}


	
	public void setSerial_no(String serial_no)
	{
		this.serial_no = serial_no;
	}


	
	public String getCountry()
	{
		return country;
	}


	
	public void setCountry(String country)
	{
		this.country = country;
	}


	
	public String getFull_desc()
	{
		return full_desc;
	}


	
	public void setFull_desc(String full_desc)
	{
		this.full_desc = full_desc;
	}


	
	public String getPatent_day()
	{
		return patent_day;
	}


	
	public void setPatent_day(String patent_day)
	{
		this.patent_day = patent_day;
	}


	
	public String getPatent_month()
	{
		return patent_month;
	}


	
	public void setPatent_month(String patent_month)
	{
		this.patent_month = patent_month;
	}


	
	public String getPatent_year()
	{
		return patent_year;
	}


	
	public void setPatent_year(String patent_year)
	{
		this.patent_year = patent_year;
	}


	
	public String getImport_status()
	{
		return import_status;
	}


	
	public void setImport_status(String import_status)
	{
		this.import_status = import_status;
	}
	
	
	
	public String getInventor_id_list()
	{
		return inventor_id_list;
	}



	
	public void setInventor_id_list(String inventor_id_list)
	{
		this.inventor_id_list = inventor_id_list;
	}



	public String getPatent_granted_str() {
		if(getPatent_granted() != null) {
			if(getPatent_granted().equals("A")) return "Patent application";
			else if(getPatent_granted().equals("G")) return "Patent granted";
			else return "";
		}
		else return "";
	}
	
	public List<PatentCountry> getCountryList()
	{
		if(countryList == null) {
			PatentDAO dao = PatentDAO.getCacheInstance();
			countryList = dao.getPatentCountryList(2);
		}
		return countryList;
	}
	
	public String getCountry_desc()
	{
		String returnStr = " ";
		if(country == null || 
				getCountryList() == null ||
						getCountryList().isEmpty()) return returnStr;
		else {
			for(PatentCountry f : getCountryList()) {
				if(f.getPk().getLookup_code().equals(country)) {
					returnStr = f.getDescription();
					break;
				}
			}
			return returnStr;
		}
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIPatentV other = (ImportRIPatentV) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "ImportRIPatentV [pk=" + pk + ", area_description=" + area_description + ", pid=" + pid
				+ ", inventor_name=" + inventor_name + ", inventor_id=" + inventor_id + ", patent_granted="
				+ patent_granted + ", patent_name=" + patent_name + ", serial_no=" + serial_no + ", country=" + country
				+ ", full_desc=" + full_desc + ", patent_day=" + patent_day + ", patent_month=" + patent_month
				+ ", patent_year=" + patent_year + ", import_status=" + import_status + ", inventor_id_list="
				+ inventor_id_list + "]";
	}
	


}
