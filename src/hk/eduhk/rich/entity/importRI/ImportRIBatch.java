package hk.eduhk.rich.entity.importRI;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_D_CA_BATCH")
public class ImportRIBatch extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(ImportRIBatch.class.toString());
	
	@Id
	@SequenceGenerator(name = "batchIdGen", sequenceName = "RH_D_CA_BATCH_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "batchIdGen")
	@Column(name = "BATCH_ID")
	private Integer batch_id;
	
	@Column(name = "BATCH_KEY")
	private String batch_key;

	@Column(name = "AREA_CODE")
	private String area_code;	
	
	@Column(name = "REMARKS")
	private String remarks;

	@Column(name = "FILE_SIZE")
	private Integer file_size;

	@Column(name = "FILE_NAME")
	private String file_name;
	
	@Column(name = "ROW_NUM")
	private Integer row_num;

	
	public Integer getBatch_id()
	{
		return batch_id;
	}

	
	public void setBatch_id(Integer batch_id)
	{
		this.batch_id = batch_id;
	}

	
	public String getBatch_key()
	{
		return batch_key;
	}

	
	public void setBatch_key(String batch_key)
	{
		this.batch_key = batch_key;
	}

	
	public String getArea_code()
	{
		return area_code;
	}

	
	public void setArea_code(String area_code)
	{
		this.area_code = area_code;
	}

	
	public String getRemarks()
	{
		return remarks;
	}

	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}

	
	public Integer getFile_size()
	{
		return file_size;
	}

	
	public void setFile_size(Integer file_size)
	{
		this.file_size = file_size;
	}

	
	public String getFile_name()
	{
		return file_name;
	}

	
	public void setFile_name(String file_name)
	{
		this.file_name = file_name;
	}

	
	public Integer getRow_num()
	{
		return row_num;
	}

	
	public void setRow_num(Integer row_num)
	{
		this.row_num = row_num;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((batch_id == null) ? 0 : batch_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIBatch other = (ImportRIBatch) obj;
		if (batch_id == null)
		{
			if (other.batch_id != null)
				return false;
		}
		else if (!batch_id.equals(other.batch_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRIBatch [batch_id=" + batch_id + ", batch_key=" + batch_key + ", area_code=" + area_code
				+ ", remarks=" + remarks + ", file_size=" + file_size + ", file_name=" + file_name + ", row_num="
				+ row_num + "]";
	}

	


}
