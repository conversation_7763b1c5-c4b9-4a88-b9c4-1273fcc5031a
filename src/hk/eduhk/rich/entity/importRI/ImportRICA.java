package hk.eduhk.rich.entity.importRI;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_D_CA")
public class ImportRICA extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(ImportRICA.class.toString());
	
	@Id
	@Column(name = "AREA_CODE")
	private String area_code;
	
	@Column(name = "GRP_CODE")
	private String grp_code;

	@Column(name = "DESCRIPTION")
	private String description;	
	
	@Column(name = "INSTRUCTION")
	private String instruction;

	@Column(name = "TITLE")
	private String title;

	@Column(name = "METADATA_XML")
	private String metadata_xml;

	
	public String getArea_code()
	{
		return area_code;
	}

	
	public void setArea_code(String area_code)
	{
		this.area_code = area_code;
	}

	
	public String getGrp_code()
	{
		return grp_code;
	}

	
	public void setGrp_code(String grp_code)
	{
		this.grp_code = grp_code;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getInstruction()
	{
		return instruction;
	}

	
	public void setInstruction(String instruction)
	{
		this.instruction = instruction;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}


	
	public String getMetadata_xml()
	{
		return metadata_xml;
	}


	
	public void setMetadata_xml(String metadata_xml)
	{
		this.metadata_xml = metadata_xml;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((area_code == null) ? 0 : area_code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRICA other = (ImportRICA) obj;
		if (area_code == null)
		{
			if (other.area_code != null)
				return false;
		}
		else if (!area_code.equals(other.area_code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRICA [area_code=" + area_code + ", grp_code=" + grp_code + ", description=" + description
				+ ", instruction=" + instruction + ", title=" + title + ", metadata_xml=" + metadata_xml + "]";
	}



	


}
