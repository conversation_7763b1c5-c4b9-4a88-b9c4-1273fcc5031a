package hk.eduhk.rich.entity.importRI;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_D_STORE_01")
public class ImportRIStore extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(ImportRIStore.class.toString());
	
	@EmbeddedId     
	private ImportRIStore_PK pk;
	                                  
	@Column(name = "ROW_NUM")         
	private Integer row_num;
	                                  
	@Column(name = "CONTRIBUTOR_ID")  
	private String contributor_id;
	                                  
	@Column(name = "COL_1")           
	private String col_1;
	                                  
	@Column(name = "COL_2")           
	private String col_2;
	                                  
	@Column(name = "COL_3")           
	private String col_3;
	                                  
	@Column(name = "COL_4")           
	private String col_4;
	                                  
	@Column(name = "COL_5")           
	private String col_5;
	                                  
	@Column(name = "COL_6")           
	private String col_6;
	                                  
	@Column(name = "COL_7")           
	private String col_7;
	                                  
	@Column(name = "COL_8")           
	private String col_8;
	                                  
	@Column(name = "COL_9")           
	private String col_9;
	                                  
	@Column(name = "COL_10")          
	private String col_10;
	                                  
	@Column(name = "COL_11")          
	private String col_11;
	                                  
	@Column(name = "COL_12")          
	private String col_12;
	                                  
	@Column(name = "COL_13")          
	private String col_13;
	                                  
	@Column(name = "COL_14")          
	private String col_14;
	                                  
	@Column(name = "COL_15")          
	private String col_15;
	                                  
	@Column(name = "COL_16")          
	private String col_16;
	                                  
	@Column(name = "COL_17")          
	private String col_17;
	                                  
	@Column(name = "COL_18")          
	private String col_18;
	                                  
	@Column(name = "COL_19")          
	private String col_19;
	                                  
	@Column(name = "COL_20")          
	private String col_20;
	                                  
	@Column(name = "COL_21")          
	private String col_21;
	                                  
	@Column(name = "COL_22")          
	private String col_22;
	                                  
	@Column(name = "COL_23")          
	private String col_23;
	                                  
	@Column(name = "COL_24")          
	private String col_24;
	                                  
	@Column(name = "COL_25")          
	private String col_25;
	                                  
	@Column(name = "COL_26")          
	private String col_26;
	                                  
	@Column(name = "COL_27")          
	private String col_27;
	                                  
	@Column(name = "COL_28")          
	private String col_28;
	                                  
	@Column(name = "COL_29")          
	private String col_29;
	                                  
	@Column(name = "COL_30")          
	private String col_30;
	                                  
	@Column(name = "COL_31")          
	private String col_31;
	                                  
	@Column(name = "COL_32")          
	private String col_32;
	                                  
	@Column(name = "COL_33")          
	private String col_33;
	                                  
	@Column(name = "COL_34")          
	private String col_34;
	                                  
	@Column(name = "COL_35")          
	private String col_35;
	                                  
	@Column(name = "COL_36")          
	private String col_36;
	                                  
	@Column(name = "COL_37")          
	private String col_37;
	                                  
	@Column(name = "COL_38")          
	private String col_38;
	                                  
	@Column(name = "COL_39")          
	private String col_39;
	                                  
	@Column(name = "COL_40")          
	private String col_40;
	                                  
	@Column(name = "COL_41")          
	private String col_41;
	                                  
	@Column(name = "COL_42")          
	private String col_42;
	                                  
	@Column(name = "COL_43")          
	private String col_43;
	                                  
	@Column(name = "COL_44")          
	private String col_44;
	                                  
	@Column(name = "COL_45")          
	private String col_45;
	                                  
	@Column(name = "COL_46")          
	private String col_46;
	                                  
	@Column(name = "COL_47")          
	private String col_47;
	                                  
	@Column(name = "COL_48")          
	private String col_48;
	                                  
	@Column(name = "COL_49")          
	private String col_49;
	                                  
	@Column(name = "COL_50")          
	private String col_50;
	                                  
	@Column(name = "COL_51")          
	private String col_51;
	                                  
	@Column(name = "COL_52")          
	private String col_52;
	                                  
	@Column(name = "COL_53")          
	private String col_53;
	                                  
	@Column(name = "COL_54")          
	private String col_54;
	                                  
	@Column(name = "COL_55")          
	private String col_55;
	                                  
	@Column(name = "COL_56")          
	private String col_56;
	                                  
	@Column(name = "COL_57")          
	private String col_57;
	                                  
	@Column(name = "COL_58")          
	private String col_58;
	                                  
	@Column(name = "COL_59")          
	private String col_59;
	                                  
	@Column(name = "COL_60")          
	private String col_60;
	                                  
	@Column(name = "COL_61")          
	private String col_61;
	                                  
	@Column(name = "COL_62")          
	private String col_62;
	                                  
	@Column(name = "COL_63")          
	private String col_63;
	                                  
	@Column(name = "COL_64")          
	private String col_64;
	                                  
	@Column(name = "COL_65")          
	private String col_65;
	                                  
	@Column(name = "COL_66")          
	private String col_66;
	                                  
	@Column(name = "COL_67")          
	private String col_67;
	                                  
	@Column(name = "COL_68")          
	private String col_68;
	                                  
	@Column(name = "COL_69")          
	private String col_69;
	                                  
	@Column(name = "COL_70")          
	private String col_70;
	                                  
	@Column(name = "COL_71")          
	private String col_71;
	                                  
	@Column(name = "COL_72")          
	private String col_72;
	                                  
	@Column(name = "COL_73")          
	private String col_73;
	                                  
	@Column(name = "COL_74")          
	private String col_74;
	                                  
	@Column(name = "COL_75")          
	private String col_75;
	                                  
	@Column(name = "COL_76")          
	private String col_76;
	                                  
	@Column(name = "COL_77")          
	private String col_77;
	                                  
	@Column(name = "COL_78")          
	private String col_78;
	                                  
	@Column(name = "COL_79")          
	private String col_79;
	                                  
	@Column(name = "COL_80")          
	private String col_80;
	                                  
	@Column(name = "COL_81")          
	private String col_81;
	                                  
	@Column(name = "COL_82")          
	private String col_82;
	                                  
	@Column(name = "COL_83")          
	private String col_83;
	                                  
	@Column(name = "COL_84")          
	private String col_84;
	                                  
	@Column(name = "COL_85")          
	private String col_85;
	                                  
	@Column(name = "COL_86")          
	private String col_86;
	                                  
	@Column(name = "COL_87")          
	private String col_87;
	                                  
	@Column(name = "COL_88")          
	private String col_88;
	                                  
	@Column(name = "COL_89")          
	private String col_89;
	                                  
	@Column(name = "COL_90")          
	private String col_90;
	                                  
	@Column(name = "COL_91")          
	private String col_91;
	                                  
	@Column(name = "COL_92")          
	private String col_92;
	                                  
	@Column(name = "COL_93")          
	private String col_93;
	                                  
	@Column(name = "COL_94")          
	private String col_94;
	                                  
	@Column(name = "COL_95")          
	private String col_95;
	                                  
	@Column(name = "COL_96")          
	private String col_96;
	                                  
	@Column(name = "COL_97")          
	private String col_97;
	                                  
	@Column(name = "COL_98")          
	private String col_98;
	                                  
	@Column(name = "COL_99")          
	private String col_99;
	                                  
	@Column(name = "COL_100")         
	private String col_100;

	
	public ImportRIStore_PK getPk()
	{
		return pk;
	}

	
	public void setPk(ImportRIStore_PK pk)
	{
		this.pk = pk;
	}

	
	public Integer getRow_num()
	{
		return row_num;
	}

	
	public void setRow_num(Integer row_num)
	{
		this.row_num = row_num;
	}

	
	public String getContributor_id()
	{
		return contributor_id;
	}

	
	public void setContributor_id(String contributor_id)
	{
		this.contributor_id = contributor_id;
	}

	
	public String getCol_1()
	{
		return col_1;
	}

	
	public void setCol_1(String col_1)
	{
		this.col_1 = col_1;
	}

	
	public String getCol_2()
	{
		return col_2;
	}

	
	public void setCol_2(String col_2)
	{
		this.col_2 = col_2;
	}

	
	public String getCol_3()
	{
		return col_3;
	}

	
	public void setCol_3(String col_3)
	{
		this.col_3 = col_3;
	}

	
	public String getCol_4()
	{
		return col_4;
	}

	
	public void setCol_4(String col_4)
	{
		this.col_4 = col_4;
	}

	
	public String getCol_5()
	{
		return col_5;
	}

	
	public void setCol_5(String col_5)
	{
		this.col_5 = col_5;
	}

	
	public String getCol_6()
	{
		return col_6;
	}

	
	public void setCol_6(String col_6)
	{
		this.col_6 = col_6;
	}

	
	public String getCol_7()
	{
		return col_7;
	}

	
	public void setCol_7(String col_7)
	{
		this.col_7 = col_7;
	}

	
	public String getCol_8()
	{
		return col_8;
	}

	
	public void setCol_8(String col_8)
	{
		this.col_8 = col_8;
	}

	
	public String getCol_9()
	{
		return col_9;
	}

	
	public void setCol_9(String col_9)
	{
		this.col_9 = col_9;
	}

	
	public String getCol_10()
	{
		return col_10;
	}

	
	public void setCol_10(String col_10)
	{
		this.col_10 = col_10;
	}

	
	public String getCol_11()
	{
		return col_11;
	}

	
	public void setCol_11(String col_11)
	{
		this.col_11 = col_11;
	}

	
	public String getCol_12()
	{
		return col_12;
	}

	
	public void setCol_12(String col_12)
	{
		this.col_12 = col_12;
	}

	
	public String getCol_13()
	{
		return col_13;
	}

	
	public void setCol_13(String col_13)
	{
		this.col_13 = col_13;
	}

	
	public String getCol_14()
	{
		return col_14;
	}

	
	public void setCol_14(String col_14)
	{
		this.col_14 = col_14;
	}

	
	public String getCol_15()
	{
		return col_15;
	}

	
	public void setCol_15(String col_15)
	{
		this.col_15 = col_15;
	}

	
	public String getCol_16()
	{
		return col_16;
	}

	
	public void setCol_16(String col_16)
	{
		this.col_16 = col_16;
	}

	
	public String getCol_17()
	{
		return col_17;
	}

	
	public void setCol_17(String col_17)
	{
		this.col_17 = col_17;
	}

	
	public String getCol_18()
	{
		return col_18;
	}

	
	public void setCol_18(String col_18)
	{
		this.col_18 = col_18;
	}

	
	public String getCol_19()
	{
		return col_19;
	}

	
	public void setCol_19(String col_19)
	{
		this.col_19 = col_19;
	}

	
	public String getCol_20()
	{
		return col_20;
	}

	
	public void setCol_20(String col_20)
	{
		this.col_20 = col_20;
	}

	
	public String getCol_21()
	{
		return col_21;
	}

	
	public void setCol_21(String col_21)
	{
		this.col_21 = col_21;
	}

	
	public String getCol_22()
	{
		return col_22;
	}

	
	public void setCol_22(String col_22)
	{
		this.col_22 = col_22;
	}

	
	public String getCol_23()
	{
		return col_23;
	}

	
	public void setCol_23(String col_23)
	{
		this.col_23 = col_23;
	}

	
	public String getCol_24()
	{
		return col_24;
	}

	
	public void setCol_24(String col_24)
	{
		this.col_24 = col_24;
	}

	
	public String getCol_25()
	{
		return col_25;
	}

	
	public void setCol_25(String col_25)
	{
		this.col_25 = col_25;
	}

	
	public String getCol_26()
	{
		return col_26;
	}

	
	public void setCol_26(String col_26)
	{
		this.col_26 = col_26;
	}

	
	public String getCol_27()
	{
		return col_27;
	}

	
	public void setCol_27(String col_27)
	{
		this.col_27 = col_27;
	}

	
	public String getCol_28()
	{
		return col_28;
	}

	
	public void setCol_28(String col_28)
	{
		this.col_28 = col_28;
	}

	
	public String getCol_29()
	{
		return col_29;
	}

	
	public void setCol_29(String col_29)
	{
		this.col_29 = col_29;
	}

	
	public String getCol_30()
	{
		return col_30;
	}

	
	public void setCol_30(String col_30)
	{
		this.col_30 = col_30;
	}

	
	public String getCol_31()
	{
		return col_31;
	}

	
	public void setCol_31(String col_31)
	{
		this.col_31 = col_31;
	}

	
	public String getCol_32()
	{
		return col_32;
	}

	
	public void setCol_32(String col_32)
	{
		this.col_32 = col_32;
	}

	
	public String getCol_33()
	{
		return col_33;
	}

	
	public void setCol_33(String col_33)
	{
		this.col_33 = col_33;
	}

	
	public String getCol_34()
	{
		return col_34;
	}

	
	public void setCol_34(String col_34)
	{
		this.col_34 = col_34;
	}

	
	public String getCol_35()
	{
		return col_35;
	}

	
	public void setCol_35(String col_35)
	{
		this.col_35 = col_35;
	}

	
	public String getCol_36()
	{
		return col_36;
	}

	
	public void setCol_36(String col_36)
	{
		this.col_36 = col_36;
	}

	
	public String getCol_37()
	{
		return col_37;
	}

	
	public void setCol_37(String col_37)
	{
		this.col_37 = col_37;
	}

	
	public String getCol_38()
	{
		return col_38;
	}

	
	public void setCol_38(String col_38)
	{
		this.col_38 = col_38;
	}

	
	public String getCol_39()
	{
		return col_39;
	}

	
	public void setCol_39(String col_39)
	{
		this.col_39 = col_39;
	}

	
	public String getCol_40()
	{
		return col_40;
	}

	
	public void setCol_40(String col_40)
	{
		this.col_40 = col_40;
	}

	
	public String getCol_41()
	{
		return col_41;
	}

	
	public void setCol_41(String col_41)
	{
		this.col_41 = col_41;
	}

	
	public String getCol_42()
	{
		return col_42;
	}

	
	public void setCol_42(String col_42)
	{
		this.col_42 = col_42;
	}

	
	public String getCol_43()
	{
		return col_43;
	}

	
	public void setCol_43(String col_43)
	{
		this.col_43 = col_43;
	}

	
	public String getCol_44()
	{
		return col_44;
	}

	
	public void setCol_44(String col_44)
	{
		this.col_44 = col_44;
	}

	
	public String getCol_45()
	{
		return col_45;
	}

	
	public void setCol_45(String col_45)
	{
		this.col_45 = col_45;
	}

	
	public String getCol_46()
	{
		return col_46;
	}

	
	public void setCol_46(String col_46)
	{
		this.col_46 = col_46;
	}

	
	public String getCol_47()
	{
		return col_47;
	}

	
	public void setCol_47(String col_47)
	{
		this.col_47 = col_47;
	}

	
	public String getCol_48()
	{
		return col_48;
	}

	
	public void setCol_48(String col_48)
	{
		this.col_48 = col_48;
	}

	
	public String getCol_49()
	{
		return col_49;
	}

	
	public void setCol_49(String col_49)
	{
		this.col_49 = col_49;
	}

	
	public String getCol_50()
	{
		return col_50;
	}

	
	public void setCol_50(String col_50)
	{
		this.col_50 = col_50;
	}

	
	public String getCol_51()
	{
		return col_51;
	}

	
	public void setCol_51(String col_51)
	{
		this.col_51 = col_51;
	}

	
	public String getCol_52()
	{
		return col_52;
	}

	
	public void setCol_52(String col_52)
	{
		this.col_52 = col_52;
	}

	
	public String getCol_53()
	{
		return col_53;
	}

	
	public void setCol_53(String col_53)
	{
		this.col_53 = col_53;
	}

	
	public String getCol_54()
	{
		return col_54;
	}

	
	public void setCol_54(String col_54)
	{
		this.col_54 = col_54;
	}

	
	public String getCol_55()
	{
		return col_55;
	}

	
	public void setCol_55(String col_55)
	{
		this.col_55 = col_55;
	}

	
	public String getCol_56()
	{
		return col_56;
	}

	
	public void setCol_56(String col_56)
	{
		this.col_56 = col_56;
	}

	
	public String getCol_57()
	{
		return col_57;
	}

	
	public void setCol_57(String col_57)
	{
		this.col_57 = col_57;
	}

	
	public String getCol_58()
	{
		return col_58;
	}

	
	public void setCol_58(String col_58)
	{
		this.col_58 = col_58;
	}

	
	public String getCol_59()
	{
		return col_59;
	}

	
	public void setCol_59(String col_59)
	{
		this.col_59 = col_59;
	}

	
	public String getCol_60()
	{
		return col_60;
	}

	
	public void setCol_60(String col_60)
	{
		this.col_60 = col_60;
	}

	
	public String getCol_61()
	{
		return col_61;
	}

	
	public void setCol_61(String col_61)
	{
		this.col_61 = col_61;
	}

	
	public String getCol_62()
	{
		return col_62;
	}

	
	public void setCol_62(String col_62)
	{
		this.col_62 = col_62;
	}

	
	public String getCol_63()
	{
		return col_63;
	}

	
	public void setCol_63(String col_63)
	{
		this.col_63 = col_63;
	}

	
	public String getCol_64()
	{
		return col_64;
	}

	
	public void setCol_64(String col_64)
	{
		this.col_64 = col_64;
	}

	
	public String getCol_65()
	{
		return col_65;
	}

	
	public void setCol_65(String col_65)
	{
		this.col_65 = col_65;
	}

	
	public String getCol_66()
	{
		return col_66;
	}

	
	public void setCol_66(String col_66)
	{
		this.col_66 = col_66;
	}

	
	public String getCol_67()
	{
		return col_67;
	}

	
	public void setCol_67(String col_67)
	{
		this.col_67 = col_67;
	}

	
	public String getCol_68()
	{
		return col_68;
	}

	
	public void setCol_68(String col_68)
	{
		this.col_68 = col_68;
	}

	
	public String getCol_69()
	{
		return col_69;
	}

	
	public void setCol_69(String col_69)
	{
		this.col_69 = col_69;
	}

	
	public String getCol_70()
	{
		return col_70;
	}

	
	public void setCol_70(String col_70)
	{
		this.col_70 = col_70;
	}

	
	public String getCol_71()
	{
		return col_71;
	}

	
	public void setCol_71(String col_71)
	{
		this.col_71 = col_71;
	}

	
	public String getCol_72()
	{
		return col_72;
	}

	
	public void setCol_72(String col_72)
	{
		this.col_72 = col_72;
	}

	
	public String getCol_73()
	{
		return col_73;
	}

	
	public void setCol_73(String col_73)
	{
		this.col_73 = col_73;
	}

	
	public String getCol_74()
	{
		return col_74;
	}

	
	public void setCol_74(String col_74)
	{
		this.col_74 = col_74;
	}

	
	public String getCol_75()
	{
		return col_75;
	}

	
	public void setCol_75(String col_75)
	{
		this.col_75 = col_75;
	}

	
	public String getCol_76()
	{
		return col_76;
	}

	
	public void setCol_76(String col_76)
	{
		this.col_76 = col_76;
	}

	
	public String getCol_77()
	{
		return col_77;
	}

	
	public void setCol_77(String col_77)
	{
		this.col_77 = col_77;
	}

	
	public String getCol_78()
	{
		return col_78;
	}

	
	public void setCol_78(String col_78)
	{
		this.col_78 = col_78;
	}

	
	public String getCol_79()
	{
		return col_79;
	}

	
	public void setCol_79(String col_79)
	{
		this.col_79 = col_79;
	}

	
	public String getCol_80()
	{
		return col_80;
	}

	
	public void setCol_80(String col_80)
	{
		this.col_80 = col_80;
	}

	
	public String getCol_81()
	{
		return col_81;
	}

	
	public void setCol_81(String col_81)
	{
		this.col_81 = col_81;
	}

	
	public String getCol_82()
	{
		return col_82;
	}

	
	public void setCol_82(String col_82)
	{
		this.col_82 = col_82;
	}

	
	public String getCol_83()
	{
		return col_83;
	}

	
	public void setCol_83(String col_83)
	{
		this.col_83 = col_83;
	}

	
	public String getCol_84()
	{
		return col_84;
	}

	
	public void setCol_84(String col_84)
	{
		this.col_84 = col_84;
	}

	
	public String getCol_85()
	{
		return col_85;
	}

	
	public void setCol_85(String col_85)
	{
		this.col_85 = col_85;
	}

	
	public String getCol_86()
	{
		return col_86;
	}

	
	public void setCol_86(String col_86)
	{
		this.col_86 = col_86;
	}

	
	public String getCol_87()
	{
		return col_87;
	}

	
	public void setCol_87(String col_87)
	{
		this.col_87 = col_87;
	}

	
	public String getCol_88()
	{
		return col_88;
	}

	
	public void setCol_88(String col_88)
	{
		this.col_88 = col_88;
	}

	
	public String getCol_89()
	{
		return col_89;
	}

	
	public void setCol_89(String col_89)
	{
		this.col_89 = col_89;
	}

	
	public String getCol_90()
	{
		return col_90;
	}

	
	public void setCol_90(String col_90)
	{
		this.col_90 = col_90;
	}

	
	public String getCol_91()
	{
		return col_91;
	}

	
	public void setCol_91(String col_91)
	{
		this.col_91 = col_91;
	}

	
	public String getCol_92()
	{
		return col_92;
	}

	
	public void setCol_92(String col_92)
	{
		this.col_92 = col_92;
	}

	
	public String getCol_93()
	{
		return col_93;
	}

	
	public void setCol_93(String col_93)
	{
		this.col_93 = col_93;
	}

	
	public String getCol_94()
	{
		return col_94;
	}

	
	public void setCol_94(String col_94)
	{
		this.col_94 = col_94;
	}

	
	public String getCol_95()
	{
		return col_95;
	}

	
	public void setCol_95(String col_95)
	{
		this.col_95 = col_95;
	}

	
	public String getCol_96()
	{
		return col_96;
	}

	
	public void setCol_96(String col_96)
	{
		this.col_96 = col_96;
	}

	
	public String getCol_97()
	{
		return col_97;
	}

	
	public void setCol_97(String col_97)
	{
		this.col_97 = col_97;
	}

	
	public String getCol_98()
	{
		return col_98;
	}

	
	public void setCol_98(String col_98)
	{
		this.col_98 = col_98;
	}

	
	public String getCol_99()
	{
		return col_99;
	}

	
	public void setCol_99(String col_99)
	{
		this.col_99 = col_99;
	}

	
	public String getCol_100()
	{
		return col_100;
	}

	
	public void setCol_100(String col_100)
	{
		this.col_100 = col_100;
	}
	
	public String getContributorIDWithoutEmail() {
		if(contributor_id != null) return contributor_id.replace("@eduhk.hk", "");
		else return null;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIStore other = (ImportRIStore) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ImportRIStore [pk=" + pk + ", row_num=" + row_num + ", contributor_id="
				+ contributor_id + ", col_1=" + col_1 + ", col_2=" + col_2 + ", col_3=" + col_3 + ", col_4=" + col_4
				+ ", col_5=" + col_5 + ", col_6=" + col_6 + ", col_7=" + col_7 + ", col_8=" + col_8 + ", col_9=" + col_9
				+ ", col_10=" + col_10 + ", col_11=" + col_11 + ", col_12=" + col_12 + ", col_13=" + col_13
				+ ", col_14=" + col_14 + ", col_15=" + col_15 + ", col_16=" + col_16 + ", col_17=" + col_17
				+ ", col_18=" + col_18 + ", col_19=" + col_19 + ", col_20=" + col_20 + ", col_21=" + col_21
				+ ", col_22=" + col_22 + ", col_23=" + col_23 + ", col_24=" + col_24 + ", col_25=" + col_25
				+ ", col_26=" + col_26 + ", col_27=" + col_27 + ", col_28=" + col_28 + ", col_29=" + col_29
				+ ", col_30=" + col_30 + ", col_31=" + col_31 + ", col_32=" + col_32 + ", col_33=" + col_33
				+ ", col_34=" + col_34 + ", col_35=" + col_35 + ", col_36=" + col_36 + ", col_37=" + col_37
				+ ", col_38=" + col_38 + ", col_39=" + col_39 + ", col_40=" + col_40 + ", col_41=" + col_41
				+ ", col_42=" + col_42 + ", col_43=" + col_43 + ", col_44=" + col_44 + ", col_45=" + col_45
				+ ", col_46=" + col_46 + ", col_47=" + col_47 + ", col_48=" + col_48 + ", col_49=" + col_49
				+ ", col_50=" + col_50 + ", col_51=" + col_51 + ", col_52=" + col_52 + ", col_53=" + col_53
				+ ", col_54=" + col_54 + ", col_55=" + col_55 + ", col_56=" + col_56 + ", col_57=" + col_57
				+ ", col_58=" + col_58 + ", col_59=" + col_59 + ", col_60=" + col_60 + ", col_61=" + col_61
				+ ", col_62=" + col_62 + ", col_63=" + col_63 + ", col_64=" + col_64 + ", col_65=" + col_65
				+ ", col_66=" + col_66 + ", col_67=" + col_67 + ", col_68=" + col_68 + ", col_69=" + col_69
				+ ", col_70=" + col_70 + ", col_71=" + col_71 + ", col_72=" + col_72 + ", col_73=" + col_73
				+ ", col_74=" + col_74 + ", col_75=" + col_75 + ", col_76=" + col_76 + ", col_77=" + col_77
				+ ", col_78=" + col_78 + ", col_79=" + col_79 + ", col_80=" + col_80 + ", col_81=" + col_81
				+ ", col_82=" + col_82 + ", col_83=" + col_83 + ", col_84=" + col_84 + ", col_85=" + col_85
				+ ", col_86=" + col_86 + ", col_87=" + col_87 + ", col_88=" + col_88 + ", col_89=" + col_89
				+ ", col_90=" + col_90 + ", col_91=" + col_91 + ", col_92=" + col_92 + ", col_93=" + col_93
				+ ", col_94=" + col_94 + ", col_95=" + col_95 + ", col_96=" + col_96 + ", col_97=" + col_97
				+ ", col_98=" + col_98 + ", col_99=" + col_99 + ", col_100=" + col_100 + "]";
	}






	


}
