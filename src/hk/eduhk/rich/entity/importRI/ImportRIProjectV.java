package hk.eduhk.rich.entity.importRI;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;


@Entity
@Table(name = "RH_IMPORT_PROJECT_V")
public class ImportRIProjectV
{
	public static Logger logger = Logger.getLogger(ImportRIProjectV.class.toString());
	
	@EmbeddedId
	private ImportRIProjectV_PK pk;
	
	@Column(name = "AREA_DESCRIPTION")
	private String area_description;
	                                   
	@Column(name = "PID")              
	private String pid;
	                                   
	@Column(name = "INVESTIGATOR_NAME")  
	private String investigator_name;
    
	@Column(name = "INVESTIGATOR_ID")  
	private String investigator_id;
	                                   
	@Column(name = "OTHER_CONTRIBUTORS_ID")  
	private String other_contributors_id;
	                                   
	@Column(name = "SAP_FUNDING_SOURCE")  
	private String sap_funding_source;
    
	@Column(name = "FUNDING_BODY")  
	private String funding_body;
	                                   
	@Column(name = "TITLE_1")  
	private String title_1;
	
	@Column(name = "TITLE_2")  
	private String title_2;
	
	@Column(name = "TITLE_3")  
	private String title_3;
	
	@Column(name = "TITLE_4")  
	private String title_4;
	                                   
	@Column(name = "PROJECT_SUMMARY")         
	private String project_summary;
	                                   
	@Column(name = "PROJECT_SUMMARY_2")    
	private String project_summary_2;
	                                   
	@Column(name = "FROM_DAY")             
	private String from_day;
	                                   
	@Column(name = "FROM_MONTH")       
	private String from_month;
	                                   
	@Column(name = "FROM_YEAR")        
	private String from_year;
	
	@Column(name = "TO_DAY")             
	private String to_day;
	                                   
	@Column(name = "TO_MONTH")       
	private String to_month;
	                                   
	@Column(name = "TO_YEAR")        
	private String to_year;
	
	@Column(name = "IMPORT_STATUS")    
	private String import_status;
	
	@Transient
	private String other_contributors_name;
	
	private static List<FundSource> fundSourceList;

	
	
	public ImportRIProjectV_PK getPk()
	{
		return pk;
	}


	
	public void setPk(ImportRIProjectV_PK pk)
	{
		this.pk = pk;
	}


	public String getArea_description()
	{
		if(area_description == null) return " ";
		else return area_description;
	}

	
	public void setArea_description(String area_description)
	{
		this.area_description = area_description;
	}

	
	public String getPid()
	{
		if(pid == null) return " ";
		else return pid;
	}

	
	public void setPid(String pid)
	{
		this.pid = pid;
	}

	
	
	public String getInvestigator_name()
	{
		return investigator_name;
	}


	
	public void setInvestigator_name(String investigator_name)
	{
		this.investigator_name = investigator_name;
	}

	
	
	public String getInvestigator_id()
	{
		return investigator_id;
	}



	
	public void setInvestigator_id(String investigator_id)
	{
		this.investigator_id = investigator_id;
	}



	public String getOther_contributors_id()
	{
		return other_contributors_id;
	}


	
	public void setOther_contributors_id(String other_contributors_id)
	{
		this.other_contributors_id = other_contributors_id;
	}


	
	public String getSap_funding_source()
	{
		return sap_funding_source;
	}
	
	
	public String getSap_funding_source_desc()
	{
		String returnStr = " ";
		if(sap_funding_source == null || 
				getFundSourceList() == null ||
						getFundSourceList().isEmpty()) return returnStr;
		else {
			for(FundSource f : getFundSourceList()) {
				if(f.getPk().getLookup_code().equals(sap_funding_source)) {
					returnStr = f.getDescription();
					break;
				}
			}
			return returnStr;
		}
	}


	
	public void setSap_funding_source(String sap_funding_source)
	{
		this.sap_funding_source = sap_funding_source;
	}

	
	
	public String getFunding_body()
	{
		return funding_body;
	}



	
	public void setFunding_body(String funding_body)
	{
		this.funding_body = funding_body;
	}



	public String getTitle_1()
	{
		return title_1;
	}


	
	public void setTitle_1(String title_1)
	{
		this.title_1 = title_1;
	}


	
	public String getTitle_2()
	{
		return title_2;
	}


	
	public void setTitle_2(String title_2)
	{
		this.title_2 = title_2;
	}


	
	public String getTitle_3()
	{
		return title_3;
	}


	
	public void setTitle_3(String title_3)
	{
		this.title_3 = title_3;
	}


	
	public String getTitle_4()
	{
		return title_4;
	}


	
	public void setTitle_4(String title_4)
	{
		this.title_4 = title_4;
	}


	
	public String getProject_summary()
	{
		return project_summary;
	}


	
	public void setProject_summary(String project_summary)
	{
		this.project_summary = project_summary;
	}


	
	public String getProject_summary_2()
	{
		return project_summary_2;
	}


	
	public void setProject_summary_2(String project_summary_2)
	{
		this.project_summary_2 = project_summary_2;
	}


	
	public String getFrom_day()
	{
		return from_day;
	}


	
	public void setFrom_day(String from_day)
	{
		this.from_day = from_day;
	}


	
	public String getFrom_month()
	{
		return from_month;
	}


	
	public void setFrom_month(String from_month)
	{
		this.from_month = from_month;
	}


	
	public String getFrom_year()
	{
		return from_year;
	}


	
	public void setFrom_year(String from_year)
	{
		this.from_year = from_year;
	}


	
	public String getTo_day()
	{
		return to_day;
	}


	
	public void setTo_day(String to_day)
	{
		this.to_day = to_day;
	}


	
	public String getTo_month()
	{
		return to_month;
	}


	
	public void setTo_month(String to_month)
	{
		this.to_month = to_month;
	}


	
	public String getTo_year()
	{
		return to_year;
	}


	
	public void setTo_year(String to_year)
	{
		this.to_year = to_year;
	}


	public String getImport_status()
	{
		if(import_status == null) return " ";
		else return import_status;
	}

	
	public void setImport_status(String import_status)
	{
		this.import_status = import_status;
	}
	
	
	public List<FundSource> getFundSourceList()
	{
		if(fundSourceList == null) {
			ProjectDAO dao = ProjectDAO.getCacheInstance();
			fundSourceList = dao.getFundSourceList(1);
		}
		return fundSourceList;
	}


	
	public String getOther_contributors_name()
	{
		if(other_contributors_name == null && other_contributors_id != null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			String[] idArr = other_contributors_id.split(";");
			List<String> cnList = Arrays.asList(idArr);
			List<String> staffNameList = dao.getStaffNameListByCNList(cnList);
			other_contributors_name = String.join("; ", staffNameList);
		}
		return other_contributors_name;
	}
	
	public String getTitle() {
		String title_1_Str = title_1 != null? title_1 : "";
		String title_2_Str = title_2 != null? title_2 : "";
		String title_3_Str = title_3 != null? title_3 : "";
		String title_4_Str = title_4 != null? title_4 : "";
		return title_1_Str + title_2_Str + title_3_Str + title_4_Str;
	}
	
	public String getSummary() {
		return project_summary + project_summary_2;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ImportRIProjectV other = (ImportRIProjectV) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "ImportRIProjectV [pk=" + pk + ", area_description=" + area_description + ", pid=" + pid
				+ ", investigator_name=" + investigator_name + ", investigator_id=" + investigator_id
				+ ", other_contributors_id=" + other_contributors_id + ", sap_funding_source=" + sap_funding_source
				+ ", funding_body=" + funding_body + ", title_1=" + title_1 + ", title_2=" + title_2 + ", title_3="
				+ title_3 + ", title_4=" + title_4 + ", project_summary=" + project_summary + ", project_summary_2="
				+ project_summary_2 + ", from_day=" + from_day + ", from_month=" + from_month + ", from_year="
				+ from_year + ", to_day=" + to_day + ", to_month=" + to_month + ", to_year=" + to_year
				+ ", import_status=" + import_status + "]";
	}


	
	



	


}
