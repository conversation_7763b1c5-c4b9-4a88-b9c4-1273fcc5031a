package hk.eduhk.rich.entity.operation;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import java.util.logging.Logger;

import hk.eduhk.rich.UserPersistenceObject;

@SuppressWarnings("serial")
@Entity
@Table(name = "RH_O_UPLOAD_STATUS")
public class UploadStatus extends UserPersistenceObject
{
	public static final String UPLOAD_TYPE_KT_ACT = "KT";
	
	public static Logger logger = Logger.getLogger(UploadStatus.class.toString());
	
	@EmbeddedId
	private UploadStatusPK pk = new UploadStatusPK();
	
	@Column(name = "file_name")
	private String fileName;
	
	@Column(name = "remarks")
	private String remarks;

	
	public UploadStatusPK getPk()
	{
		return pk;
	}

	
	public void setPk(UploadStatusPK pk)
	{
		this.pk = pk;
	}


	public String getFileName()
	{
		return fileName;
	}

	
	public void setFileName(String fileName)
	{
		this.fileName = fileName;
	}

	
	public String getRemarks()
	{
		return remarks;
	}

	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UploadStatus other = (UploadStatus) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UploadStatus [pk=" + pk + ", fileName=" + fileName + ", remarks=" + remarks + "]";
	}
}
