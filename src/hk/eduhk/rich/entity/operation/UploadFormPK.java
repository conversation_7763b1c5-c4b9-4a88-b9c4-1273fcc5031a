package hk.eduhk.rich.entity.operation;

import java.io.Serializable;

import javax.persistence.Column;

public class UploadFormPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "type")
	private String type;
	
	@Column(name = "period_id")
	private Integer periodId;
	
	@Column(name="form_no")
	private Integer formNo;
	
	@Column(name="data_level")
	private String dataLevel;

	
	public String getType()
	{
		return type;
	}

	
	public void setType(String type)
	{
		this.type = type;
	}

	
	public Integer getPeriodId()
	{
		return periodId;
	}

	
	public void setPeriodId(Integer periodId)
	{
		this.periodId = periodId;
	}


	
	public Integer getFormNo()
	{
		return formNo;
	}


	
	public void setFormNo(Integer formNo)
	{
		this.formNo = formNo;
	}


	
	public String getDataLevel()
	{
		return dataLevel;
	}


	
	public void setDataLevel(String dataLevel)
	{
		this.dataLevel = dataLevel;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dataLevel == null) ? 0 : dataLevel.hashCode());
		result = prime * result + ((formNo == null) ? 0 : formNo.hashCode());
		result = prime * result + ((periodId == null) ? 0 : periodId.hashCode());
		result = prime * result + ((type == null) ? 0 : type.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UploadFormPK other = (UploadFormPK) obj;
		if (dataLevel == null)
		{
			if (other.dataLevel != null)
				return false;
		}
		else if (!dataLevel.equals(other.dataLevel))
			return false;
		if (formNo == null)
		{
			if (other.formNo != null)
				return false;
		}
		else if (!formNo.equals(other.formNo))
			return false;
		if (periodId == null)
		{
			if (other.periodId != null)
				return false;
		}
		else if (!periodId.equals(other.periodId))
			return false;
		if (type == null)
		{
			if (other.type != null)
				return false;
		}
		else if (!type.equals(other.type))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UploadFormPK [type=" + type + ", periodId=" + periodId + ", formNo=" + formNo + ", dataLevel="
				+ dataLevel + "]";
	}
}
