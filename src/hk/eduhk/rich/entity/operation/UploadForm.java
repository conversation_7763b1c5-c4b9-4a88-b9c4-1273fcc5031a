package hk.eduhk.rich.entity.operation;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_O_UPLOAD_FORM")
public class UploadForm implements Serializable
{
	
	public static Logger logger = Logger.getLogger(UploadForm.class.toString());
	
	@EmbeddedId
	private UploadFormPK pk = new UploadFormPK();

	
	public UploadFormPK getPk()
	{
		return pk;
	}

	
	public void setPk(UploadFormPK pk)
	{
		this.pk = pk;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UploadForm other = (UploadForm) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UploadForm [pk=" + pk + "]";
	}
	
	
}
