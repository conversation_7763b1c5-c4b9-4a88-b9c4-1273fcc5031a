package hk.eduhk.rich.entity.operation;

import java.io.Serializable;

import javax.persistence.Column;

public class UploadStatusPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "type")
	private String type;
	
	@Column(name = "period_id")
	private Integer periodId;

	
	public String getType()
	{
		return type;
	}

	
	public void setType(String type)
	{
		this.type = type;
	}

	
	public Integer getPeriodId()
	{
		return periodId;
	}

	
	public void setPeriodId(Integer periodId)
	{
		this.periodId = periodId;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((periodId == null) ? 0 : periodId.hashCode());
		result = prime * result + ((type == null) ? 0 : type.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UploadStatusPK other = (UploadStatusPK) obj;
		if (periodId == null)
		{
			if (other.periodId != null)
				return false;
		}
		else if (!periodId.equals(other.periodId))
			return false;
		if (type == null)
		{
			if (other.type != null)
				return false;
		}
		else if (!type.equals(other.type))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UploadStatusPK [type=" + type + ", periodId=" + periodId + "]";
	}
}
