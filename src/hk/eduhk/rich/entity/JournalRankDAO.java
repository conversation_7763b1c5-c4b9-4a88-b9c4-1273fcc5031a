package hk.eduhk.rich.entity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;

import org.apache.commons.collections.CollectionUtils;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.util.PersistenceManager;

@SuppressWarnings("serial")
public class JournalRankDAO extends BaseDAO{
	private static JournalRankDAO instance = null;
	
	public static JournalRankDAO getInstance() {
		if(instance == null) {
			instance = new JournalRankDAO();
		}
		return instance;
	}
	
	public List<JournalRank> getJournalRankList(){
		List<JournalRank> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM JournalRank obj ";
			TypedQuery<JournalRank> q = em.createQuery(query, JournalRank.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList:null;
	}
	
	
	
	public List<JournalRank> getJournalRankListByIssn(String issn , String eissn){
		List<JournalRank> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM JournalRank obj where 1=1 AND year = '2022' " ;
			
			if(issn != null && ! issn.isEmpty() ) 
				query += " AND ( obj.issn_1 = :issn or obj.issn_2 = :issn or obj.issn_3 = :issn )  ";
			
			TypedQuery<JournalRank> q = em.createQuery(query, JournalRank.class);
			
			if(issn != null && ! issn.isEmpty() ) 
				q.setParameter("issn", issn);
			
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList:null;
	}
	
	public void clearOldRecord() throws Exception {
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
    
		try{
			conn = pm.getConnection();
			String query = "DELETE FROM RH_S_JNL_RANK";
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
	}
	
	public JournalRank updateJournalRank(JournalRank obj) {
		return updateEntity(obj);
	}

}
