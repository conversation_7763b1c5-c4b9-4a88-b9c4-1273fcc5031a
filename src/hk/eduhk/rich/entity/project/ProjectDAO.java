package hk.eduhk.rich.entity.project;

import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.view.PreRptView;
import hk.eduhk.rich.view.RISearchPanel;
import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;

@SuppressWarnings("serial")
public class ProjectDAO extends BaseDAO
{

	private static ProjectDAO instance = null;


	public static synchronized ProjectDAO getInstance()
	{
		if (instance == null) instance = new ProjectDAO();
		return instance;
	}
	
	
	public static ProjectDAO getCacheInstance()
	{
		return ProjectDAO.getInstance();
	}
	
	public List<ProjectSummary> getRecentProjectSummaryListByStaffNumber(String staffNumber) throws SQLException
	{
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		List<ProjectSummary> voList = new ArrayList<ProjectSummary>();
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();    
		try
		{
			conn = pm.getConnection();
      StringBuffer query = new StringBuffer();
      
      String strFixToYear = paramDAO.getSysParamValueByCode(SysParam.PARAM_PERSONAL_TO_FIX_YEAR_PROJECT);
      int fixToYear = Integer.parseInt(strFixToYear);
      String strNumOfRetrieved = paramDAO.getSysParamValueByCode(SysParam.PARAM_PERSONAL_NUM_RETRIEVE_PROJECT);
			int numOfRetrieved = Integer.parseInt(strNumOfRetrieved);
      
      query.append("SELECT project_no, title, proj_summary || (CASE WHEN LENGTH(proj_summary) = ? THEN ? ELSE '' END) proj_summary, from_year " +
		        " FROM (SELECT H.project_no, H.title_1 || ' ' || H.title_2 || ' ' || H.title_3 || ' ' || H.title_4 title, " +
		        "SUBSTR(H.project_summary, 0, ?) proj_summary,  " +
		        "H.from_year  " +
		        " FROM RH_P_RESEARCH_PROJECT_HDR H, RH_P_RESEARCH_PROJECT_DTL D, RH_Q_RESEARCH_PROJECT_DTL QD " +
				" WHERE QD.DISPLAY_IND <> 'N' " +
				" AND QD.CONSENT_IND <> 'N' " +
				" AND D.project_no = QD.project_no " +
				" AND D.investigator_staff_no = QD.staff_no " +
				" AND H.project_no = D.project_no " +
				" AND H.data_level = D.data_level " +
				" AND H.data_level = 'P' " +
				" AND D.investigator_staff_no = '" + escapeSql(staffNumber) + "' " +
				" AND H.to_year >= " + fixToYear + " ");

        query.append ("ORDER BY from_year DESC NULLS LAST, TITLE_1)");

			StringBuffer sqlBuf = new StringBuffer();
			sqlBuf.append("SELECT * FROM " +
        "  (SELECT a.*, ROWNUM rnum  FROM " +
        "  ("+query+") a " +
        "   WHERE ROWNUM <= " + numOfRetrieved + " ) " +
        "   WHERE rnum >= 1");
      		
			logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
      
      int i = 0;
      pStmt.setInt(++i, 500);
      pStmt.setString(++i, " . . .");
      pStmt.setInt(++i, 500);
      	//System.out.println("query: " + query);      
			ResultSet rs = pStmt.executeQuery();
			while (rs.next())
			{
				ProjectSummary vObj = new ProjectSummary();
		        vObj.setRINo(rs.getInt("project_no"));
		        vObj.setProjTitle(rs.getString("title"));
		        vObj.setProjSummary(rs.getString("proj_summary"));
		        vObj.setFromYear(rs.getString("from_year"));       
		        voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}

    return voList;
	}
	
	public List<ProjectDetails_P> getInvestigators(int projectNo) throws SQLException
	{
		List<ProjectDetails_P> objList = null;
		if (projectNo > 0)
		{
			EntityManager em = null;
			try
			{
				em = getEntityManager();
				
				String query ="SELECT obj FROM ProjectDetails_P obj WHERE obj.pk.project_no = :project_no AND obj.pk.data_level = :data_level ";
				
				TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
				q.setParameter("project_no", projectNo);
				q.setParameter("data_level", "P");
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}	
	
	public static String escapeSql(String str)
	{
         if (str == null) {
                return null;
          }
         return StringUtils.replace(str, "'", "''");
	}	
	
	public List<ProjectHeader> getProjectList()
	{
		List<ProjectHeader> objList = null;
		

			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				
				String query = "SELECT DISTINCT obj FROM ProjectHeader obj ORDER BY obj.project_no ";
				
				TypedQuery<ProjectHeader> q = em.createQuery(query, ProjectHeader.class);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		
		
		return objList;
	}
	
	public List<ProjectDetails> getPureProjectDetails(int project_no){
		List<ProjectDetails> objList = new ArrayList<ProjectDetails>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		if (project_no > 0) {
			try
			{
				String query = "SELECT * FROM RH_PURE_PROJECT_DTL_V WHERE project_no = ? ORDER BY display_order";
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setInt(1, project_no);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					ProjectDetails d = new ProjectDetails();
					d.setPerson_source_id(rs.getString("person_source_id"));
					d.setExternal_ind(rs.getString("external_ind"));
					d.setFirst_name(rs.getString("first_name"));
					d.setLast_name(rs.getString("last_name"));
					d.setInvestigator_type(rs.getString("investigator_type"));
					objList.add(d);
				}
				
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
	
	return objList;		
	}
	
	public String getProjectCapacityDesc(String code) {
		String value = "";
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try 
	    {
			String query = "SELECT description FROM RICH.RH_L_PROJECT_CAPACITY_V WHERE lookup_level = ? AND lookup_code = ? ORDER BY print_order DESC";
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			pStmt.setInt(1, 1);
			pStmt.setString(2, code);
			ResultSet rs = pStmt.executeQuery();
			while (rs.next()) {
				value = rs.getString("description");
			}
			
	    }
		catch (SQLException e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		finally 
	    {
				pm.close(pStmt);
				pm.close(conn);
	    }
	   return value;
	}	
	
	public ProjectHeader_P getProjectHeader_P (Integer project_no, String data_level) 
	{
		List<ProjectHeader_P> objList = null;
		EntityManager em = null;	
		if (project_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM ProjectHeader_P obj WHERE obj.pk.project_no = :project_no AND obj.pk.data_level = :data_level";			
				TypedQuery<ProjectHeader_P> q = em.createQuery(query, ProjectHeader_P.class);
				q.setParameter("project_no", project_no);
				q.setParameter("data_level", data_level);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	
	
	public ProjectHeader_Q getProjectHeader_Q (Integer project_no) 
	{
		ProjectHeader_Q obj = null;
		if (project_no > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = pm.getEntityManager();
				obj = em.find(ProjectHeader_Q.class, project_no);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	public List<ProjectDetails_P> getProjectDetails_P(Integer project_no, String data_level)
	{
		List<ProjectDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			
			String query = "SELECT obj FROM ProjectDetails_P obj WHERE obj.pk.project_no = :project_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.line_no";			
			TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
			q.setParameter("project_no", project_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	public ProjectDetails_Q getProjectDetails_Q1 (Integer project_no, String staffNo) 
	{
		List<ProjectDetails_Q> objList = null;
		EntityManager em = null;
		if (project_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM ProjectDetails_Q obj WHERE obj.pk.project_no = :project_no AND obj.pk.staff_no = :staffNo";			
				TypedQuery<ProjectDetails_Q> q = em.createQuery(query, ProjectDetails_Q.class);
				q.setParameter("project_no", project_no);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):new ProjectDetails_Q();
	}		

	public List<ProjectDetails_P> getProjectDetails_P_byStaffNo(String staff_no, String data_level)
	{
		List<ProjectDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ProjectDetails_P obj WHERE obj.investigator_staff_no = :staff_no AND obj.pk.data_level = :data_level "
					+ " ORDER BY obj.projectHeader_p.from_year desc, obj.projectHeader_p.from_month desc, obj.projectHeader_p.title_1 ";			
			TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ProjectDetails_P> getProjectDetails_P_byStaffNo_FundingSource(String staff_no, String data_level, List<String> sap_funding_source)
	{
		List<ProjectDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ProjectDetails_P obj WHERE obj.investigator_staff_no = :staff_no AND obj.pk.data_level = :data_level AND obj.projectHeader_p.sap_funding_source IN :sap_funding_source "
					+ " ORDER BY obj.projectHeader_p.from_year desc, obj.projectHeader_p.from_month desc, obj.projectHeader_p.title_1 ";			
			TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			q.setParameter("sap_funding_source", sap_funding_source);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ProjectDetails_P> getProjectDetails_P_byStaffNo_consent(String staff_no, String data_level)
	{
		List<ProjectDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ProjectDetails_P obj WHERE obj.investigator_staff_no = :staff_no AND obj.pk.data_level = :data_level "
					+ " AND obj.projectHeader_q.publish_freq > :publish_freq  AND obj.projectDetails_q.consent_ind = :consent_ind ORDER BY obj.projectHeader_p.from_year desc, obj.projectHeader_p.from_month desc, obj.projectHeader_p.title_1 ";			
			TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			q.setParameter("publish_freq", 0);
			q.setParameter("consent_ind", "U");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<ProjectDetails_Q> getProjectDetails_Q(Integer project_no, String staff_no)
	{
		List<ProjectDetails_Q> objList = null;
		EntityManager em = null;		
		String projectNo = (project_no > 0)? " AND obj.pk.project_no = :project_no ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ProjectDetails_Q obj WHERE obj.pk.staff_no = :staff_no " + projectNo;			
			TypedQuery<ProjectDetails_Q> q = em.createQuery(query, ProjectDetails_Q.class);
			if (project_no > 0) {
				q.setParameter("project_no", project_no);
			}
			q.setParameter("staff_no", staff_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public ProjectDetails_Q getProjectDetails_Q_creator (Integer project_no) 
	{
		List<ProjectDetails_Q> objList = null;
		EntityManager em = null;
		if (project_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM ProjectDetails_Q obj WHERE obj.pk.project_no = :project_no AND obj.creator_ind = :creator_ind";			
				TypedQuery<ProjectDetails_Q> q = em.createQuery(query, ProjectDetails_Q.class);
				q.setParameter("project_no", project_no);
				q.setParameter("creator_ind", "Y");
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<PartnerCountry> getPartnerCountryList(int lookup_level) 
	{
		List<PartnerCountry> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PartnerCountry obj "
					+ "WHERE obj.pk.lookup_level = :lookup_level "
					+ "AND obj.enabled_flag = :enabled_flag " + parent_lookup_code 
					+ " ORDER BY obj.print_order ";			
			TypedQuery<PartnerCountry> q = em.createQuery(query, PartnerCountry.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	

	public List<ProjectSummary> getProjectListByIds(List<Integer> idList, RISearchPanel searchPanel) throws SQLException
	{
		List<ProjectSummary> voList = new ArrayList<ProjectSummary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && searchPanel != null) {
			String listingType = searchPanel.getListingType();
			String dataLevel = searchPanel.getViewType();
			String sortCol = searchPanel.getSortCol();
			String sortOrder = searchPanel.getSortOrder();
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				List<String> selectedSDG  = searchPanel.getSelectedSDGList();
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
				
				    sqlBuf.append(
				    		" SELECT PH.PROJECT_NO, SDG_CODE, ") ;
				    		
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" (CASE WHEN STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME ELSE EXSTAFF.FULLNAME END) AS STAFF_NAME, " + 
				    		" PD.INVESTIGATOR_STAFF_NO, " + 
				    		" STAFF.DEPT_CODE AS DEPARTMENT, " );
				    }
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.PRIN_INVES_AUTHOR_LIST"+i+" AS PRIN_INVES_AUTHOR_LIST"+i+", " +
			    				" TMP.CO_PRIN_INVES_AUTHOR_LIST"+i+" AS CO_PRIN_INVES_AUTHOR_LIST"+i+", " +
								" TMP.CO_INVES_AUTHOR_LIST"+i+" AS CO_INVES_AUTHOR_LIST"+i+", " +
								" TMP.OTHER_AUTHOR_LIST"+i+" AS OTHER_AUTHOR_LIST"+i+", ");
				    }
				    sqlBuf.append(
				    		// 2.x
				    		" CASE WHEN IED_WORK_IND = 'Y' THEN 'Yes' WHEN IED_WORK_IND = 'N' THEN 'No' ELSE '' END AS IED_WORK_IND, " + 
				    		" CASE WHEN PROJ_TYPE = 'R' THEN 'Research Related' WHEN PROJ_TYPE = 'N' THEN 'Not Research Related' ELSE '' END AS PROJ_TYPE, " + 
				    		" ACTIVITY_CODE, " + 
				    		" CASE WHEN TECH_PROJ = 'Y' THEN 'Yes' WHEN TECH_PROJ = 'N' THEN 'No' ELSE '' END AS TECH_PROJ, " + 
				    		" RGC_PROJ_NUM, " + 
				    		// 4.x
				    		" TITLE_1 || ' ' || TITLE_2 || ' ' || TITLE_3 || ' ' || TITLE_4 AS TITLE, " + 
				    		" PROJECT_SUMMARY, " + 
				    		" PROJECT_SUMMARY_2, " + 
				    		" (CASE WHEN FROM_DAY IS NOT NULL THEN (FROM_DAY || '/') ELSE '' END) || FROM_MONTH || '/' || FROM_YEAR AS FROMDATE, " + 
				    		" (CASE WHEN TO_DAY IS NOT NULL THEN (TO_DAY || '/') ELSE '' END) || TO_MONTH || '/' || TO_YEAR AS TODATE, " + 
				    		" ((TO_YEAR-FROM_YEAR)*12 + (TO_MONTH-FROM_MONTH)) AS DURATION_MONTH, " +
				    		" TRUNC(((TO_YEAR-FROM_YEAR)*12 + (TO_MONTH-FROM_MONTH))/12)||'yr '|| " +
				    		" MOD(((TO_YEAR-FROM_YEAR)*12 + (TO_MONTH-FROM_MONTH)), 12)||'m' as DURATION_YEAR, " +
				    		" CASE WHEN COLLAB_PARTNER_UGC = 'Y' THEN 'Yes' WHEN COLLAB_PARTNER_UGC = 'N' THEN 'No' ELSE '' END AS COLLAB_PARTNER_UGC, " + 
				    		" CASE WHEN COLLAB_PARTNER_NON_INS = 'Y' THEN 'Yes' WHEN COLLAB_PARTNER_NON_INS = 'N' THEN 'No' ELSE '' END AS COLLAB_PARTNER_NON_INS, " + 
				    		" CONCATENATED_INV_NAME, " + 
				    		" NAME_OF_COLLABORATIVE_PARTNER, " + 
				    		" COLLABORATIVE_PARTNER_CITY, " + 
				    		" COLLABORATIVE_PARTNER_COUNTRY, " + 
				    		" COLLAB_OUTPUT_TITLE, " + 
				    		" COLLAB_OUTPUT_TYPE, " + 
				    		" COLLAB_OUTPUT_INST, " + 
				    		" COLLAB_OUTPUT_DATE, " + 
				    		" CASE WHEN FUNDED_PROJ = 'Y' THEN 'Yes' WHEN FUNDED_PROJ = 'N' THEN 'No' ELSE '' END AS FUNDED_PROJ, " + 
				    		" FUNDING_BODY, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_FUND_SOURCE_V WHERE LOOKUP_CODE = SAP_FUNDING_SOURCE) AS SAP_FUNDING_SOURCE, " + 
				    		" SAP_GRANT_AMT, " + 
				    		" FUNDING_ORG, " + 
				    		" FUNDING_OTHERS, " + 
				    		" CASE WHEN ROLE_INST = 'C' THEN 'Coordinating Institution' WHEN ROLE_INST = 'P' THEN 'Participating Institution' ELSE '' END AS ROLE_INST, " + 
				    		" CASE WHEN ROLE_INST_FOR_T690 = 'C' THEN 'Coordinating Institution' WHEN ROLE_INST_FOR_T690 = 'P' THEN 'Participating Institution' ELSE '' END AS ROLE_INST_FOR_T690, " + 
				    		" RELEASED_VAL, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_CODE AND LOOKUP_LEVEL = 1) AS SCH_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_DTL_CODE AND LOOKUP_LEVEL = 2) AS SCH_DTL_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_CODE AND LOOKUP_LEVEL = 1) AS DA_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_DTL_CODE AND LOOKUP_LEVEL = 2) AS DA_DTL_CODE, " + 
				    		" OTHER_DA_DTL, " + 
				    		" KEYWORDS, " +
				    		// manageInstituteRI
				    		" CDCF_STATUS, " +
				    		" FROM_YEAR, " +
				    		" FROM_MONTH, " +
				    		" FROM_DAY, " +
				    		" TO_YEAR, " +
				    		" TO_MONTH, " +
				    		" TO_DAY, " +
				    		" (CASE WHEN CDCF_SELECTED_IND = 'Y' THEN 'Yes' WHEN CDCF_SELECTED_IND = 'N' THEN 'No' ELSE '' END) AS CDCF_SELECTED_IND " + 
				    		" FROM RH_P_RESEARCH_PROJECT_HDR PH " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_P_RESEARCH_PROJECT_DTL PD ON (PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " );
				    }
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_RESEARCH_PROJECT_HDR QH ON QH.PROJECT_NO = PH.PROJECT_NO   " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_Q_RESEARCH_PROJECT_DTL QD ON (QD.PROJECT_NO = PH.PROJECT_NO AND QD.STAFF_NO = PD.INVESTIGATOR_STAFF_NO) " + 
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO "); 
				    }
				    sqlBuf.append(
				    		" LEFT JOIN ( " +
				    		" SELECT PD.PROJECT_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
				    	sqlBuf.append(
				    		createAuthorList("PRINCIPAL INVESTIGATOR", "PRIN_INVES_AUTHOR_LIST",i,listNum) + ", " +
				    		createAuthorList("CO-PRINCIPAL INVESTIGATOR", "CO_PRIN_INVES_AUTHOR_LIST",i,listNum) + ", " +
				    		createAuthorList("CO-INVESTIGATOR", "CO_INVES_AUTHOR_LIST",i,listNum) + ", " +
				    		createAuthorList("COLLABORATOR', 'TEAM MEMBER', '9999", "OTHER_AUTHOR_LIST",i,listNum)
				    	);
				    	if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM (SELECT DENSE_RANK() OVER (PARTITION BY PD.PROJECT_NO, PD.INVESTIGATOR_TYPE ORDER BY LINE_NO) AS DEN, PD.*" +
				    		" FROM RH_P_RESEARCH_PROJECT_DTL PD WHERE PD.DATA_LEVEL = '"+ dataLevel +"' ) PD " +
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " +
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " +
				    		" WHERE PD.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PD.PROJECT_NO " +
				    		" ) TMP ON PH.PROJECT_NO = TMP.PROJECT_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
		    		if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
		    			sqlBuf.append(
		    				" AND PD.INVESTIGATOR_STAFF_NO IS NOT NULL" );
		    		}
				    sqlBuf.append(
				    		" AND PH.PROJECT_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
				
					//logger.log(Level.FINEST, sqlBuf.toString());
				    //System.out.println(sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						ProjectSummary vObj = new ProjectSummary();
						vObj.setProjectNo(rs.getInt("PROJECT_NO"));
						if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
							vObj.setStaffName(rs.getString("STAFF_NAME"));
							vObj.setStaffNumber(rs.getString("INVESTIGATOR_STAFF_NO"));
							vObj.setDepartment(rs.getString("DEPARTMENT"));
						}
						String prin_inves_author_list = "";
						String co_prin_inves_author_list = "";
						String co_inves_author_list = "";
						String other_author_list = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("PRIN_INVES_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									prin_inves_author_list += "<br/>" ;
								prin_inves_author_list += authListSeg;
							}
							authListSeg = rs.getString("CO_PRIN_INVES_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									co_prin_inves_author_list += "<br/>" ;
								co_prin_inves_author_list += authListSeg;
							}
							authListSeg = rs.getString("CO_INVES_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									co_inves_author_list += "<br/>" ;
								co_inves_author_list += authListSeg;
							}
							authListSeg = rs.getString("OTHER_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									other_author_list += "<br/>" ;
								other_author_list += authListSeg;
							}
						}
						vObj.setPrin_inves_author_list(prin_inves_author_list);
						vObj.setCo_prin_inves_author_list(co_prin_inves_author_list);
						vObj.setCo_inves_author_list(co_inves_author_list);
						vObj.setOther_author_list(other_author_list);
						// 2.x
						vObj.setIed_work_ind(rs.getString("IED_WORK_IND"));
						vObj.setProject_type(rs.getString("PROJ_TYPE"));
						vObj.setActivity_code(rs.getString("ACTIVITY_CODE"));
						vObj.setTech_proj(rs.getString("TECH_PROJ"));
						vObj.setRgc_proj_num(rs.getString("RGC_PROJ_NUM"));
						
						// 4.x
						vObj.setProjTitle(rs.getString("TITLE"));
						vObj.setProject_summary(rs.getString("PROJECT_SUMMARY"));
						vObj.setProject_summary_2(rs.getString("PROJECT_SUMMARY_2"));
						vObj.setFromdate(rs.getString("FROMDATE"));
						vObj.setTodate(rs.getString("TODATE"));
						vObj.setDurationMonth(rs.getString("DURATION_MONTH"));
						String durationYearStr = rs.getString("DURATION_YEAR");
						/*if(StringUtils.isNoneBlank(durationYearStr)) {
							if(durationYearStr.equals("0yr 0m"))
								durationYearStr = "0yr 1m";
						}*/
						vObj.setDurationYear(durationYearStr);
						vObj.setCollab_partner_ugc(rs.getString("COLLAB_PARTNER_UGC"));
						vObj.setCollab_partner_non_ins(rs.getString("COLLAB_PARTNER_NON_INS"));
						vObj.setConcatenated_inv_name(rs.getString("CONCATENATED_INV_NAME"));
						vObj.setName_of_collaborative_partner(rs.getString("NAME_OF_COLLABORATIVE_PARTNER"));
						vObj.setCollaborative_partner_city(rs.getString("COLLABORATIVE_PARTNER_CITY"));
						vObj.setCollaborative_partner_country(rs.getString("COLLABORATIVE_PARTNER_COUNTRY"));
						vObj.setCollab_output_title(rs.getString("COLLAB_OUTPUT_TITLE"));
						vObj.setCollab_output_type(rs.getString("COLLAB_OUTPUT_TYPE"));
						vObj.setCollab_output_inst(rs.getString("COLLAB_OUTPUT_INST"));
						vObj.setCollab_output_date(rs.getString("COLLAB_OUTPUT_DATE"));
						vObj.setFunded_proj(rs.getString("FUNDED_PROJ"));
						vObj.setFunding_body(rs.getString("FUNDING_BODY"));
						vObj.setSap_funding_source(rs.getString("SAP_FUNDING_SOURCE"));
						vObj.setSap_grant_amt(rs.getString("SAP_GRANT_AMT"));
						vObj.setFunding_org(rs.getString("FUNDING_ORG"));
						vObj.setFunding_others(rs.getString("FUNDING_OTHERS"));
						vObj.setCollabT630(rs.getString("ROLE_INST"));
						vObj.setCollabT690(rs.getString("ROLE_INST_FOR_T690"));
						vObj.setReleased_val(rs.getString("RELEASED_VAL"));
						vObj.setSchCode(rs.getString("SCH_CODE"));
						vObj.setSchDtlCode(rs.getString("SCH_DTL_CODE"));
						vObj.setDaCode(rs.getString("DA_CODE"));
						vObj.setDaDtlCode(rs.getString("DA_DTL_CODE"));
						vObj.setOtherDaDtl(rs.getString("OTHER_DA_DTL"));
						vObj.setKeyword(rs.getString("KEYWORDS"));
						// manageInstituteRI
						vObj.setSdg_str(rs.getString("SDG_CODE"));
						vObj.setCDCFStatus(rs.getString("CDCF_STATUS"));
						vObj.setFromYear(rs.getString("FROM_YEAR"));
						vObj.setFromMonth(rs.getString("FROM_MONTH"));
						vObj.setFromDay(rs.getString("FROM_DAY"));
						vObj.setToYear(rs.getString("TO_YEAR"));
						vObj.setToMonth(rs.getString("TO_MONTH"));
						vObj.setToDay(rs.getString("TO_DAY"));
						vObj.setCdcf_selected_ind(rs.getString("CDCF_SELECTED_IND"));
						
					
						
						
						if (!CollectionUtils.isEmpty(selectedSDG))
						{
							Boolean sdgPass = false;
							
							// Only for handling the string issue of 1 is not 11
							for(String sdg : selectedSDG) {

								if(Arrays.asList(vObj.getSdg_str().split(",")).contains(sdg))
									sdgPass = true;
							}
							
							if(sdgPass)
								voList.add(vObj);
						}
						else
							voList.add(vObj);
							
						
					}
					
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			if(voList != null && !voList.isEmpty()) {
				if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_RI_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(ProjectSummary::getProjectNo));
					else
						voList.sort(Comparator.comparing(ProjectSummary::getProjectNo).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
			    	Comparator<ProjectSummary> nameComp = new Comparator<ProjectSummary>() {
			    		@Override public int compare(final ProjectSummary record1, final ProjectSummary record2) {
			    		    int c = 0;
			    		    if (c == 0 && record1.getStaffName() != null && record2.getStaffName() != null)
			    		    	c = record1.getStaffName().compareTo(record2.getStaffName());
			    		    else if (record1.getStaffName() == null) return 1;
			    		    else if (record2.getStaffName() == null) return -1;
			    		    if (c == 0 && record1.getFromYear() != null && record2.getFromYear() != null)
			    		       c = Integer.valueOf(record1.getFromYear()).compareTo(Integer.valueOf(record2.getFromYear()));
			    		    if (c == 0 && record1.getFromMonth() != null && record2.getFromMonth() != null)
			    		       c = Integer.valueOf(record1.getFromMonth()).compareTo(Integer.valueOf(record2.getFromMonth()));
			    		    if (c == 0 && record1.getFromDay() != null && record2.getFromDay() != null)
			    		       c = Integer.valueOf(record1.getFromDay()).compareTo(Integer.valueOf(record2.getFromDay()));
			    		    return c;
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, nameComp);
					else
						Collections.sort(voList, nameComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NO_VALUE)) {
			    	if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(ProjectSummary::getProjectNo, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(ProjectSummary::getProjectNo, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NAME_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(ProjectSummary::getProjTitle, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(ProjectSummary::getProjTitle, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CONTRIBUTOR_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(ProjectSummary::getPrin_inves_author_list, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(ProjectSummary::getPrin_inves_author_list, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_FROM_DATE_VALUE)) {
					Comparator<ProjectSummary> fromDayComp = new Comparator<ProjectSummary>() {
			    		@Override public int compare(final ProjectSummary record1, final ProjectSummary record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getFromYear() == null ? "1000" : record1.getFromYear();
				    		    String record1Month = record1.getFromMonth() == null ? "1" : record1.getFromMonth();
				    		    String record1Day = record1.getFromDay() == null ? "1" : record1.getFromDay();
				    		    String record1NullLast = record1.getFromDay() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse(record1Day + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getFromYear() == null ? "1000" : record2.getFromYear();
				    		    String record2Month = record2.getFromMonth() == null ? "1" : record2.getFromMonth();
				    		    String record2Day = record2.getFromDay() == null ? "1" : record2.getFromDay();
				    		    String record2NullLast = record2.getFromDay() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse(record2Day + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getProjectListByIds having non number from date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, fromDayComp);
					else
						Collections.sort(voList, fromDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_TO_DATE_VALUE)) {
					Comparator<ProjectSummary> toDayComp = new Comparator<ProjectSummary>() {
			    		@Override public int compare(final ProjectSummary record1, final ProjectSummary record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getToYear() == null ? "1000" : record1.getToYear();
				    		    String record1Month = record1.getToMonth() == null ? "1" : record1.getToMonth();
				    		    String record1Day = record1.getToDay() == null ? "1" : record1.getToDay();
				    		    String record1NullLast = record1.getToDay() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse(record1Day + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getToYear() == null ? "1000" : record2.getToYear();
				    		    String record2Month = record2.getToMonth() == null ? "1" : record2.getToMonth();
				    		    String record2Day = record2.getToDay() == null ? "1" : record2.getToDay();
				    		    String record2NullLast = record2.getToDay() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse(record2Day + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getProjectListByIds having non number to date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, toDayComp);
					else
						Collections.sort(voList, toDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CDCF_STAT_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(ProjectSummary::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(ProjectSummary::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
			}
		}
		    return voList;
	}
	
	private String createAuthorList(String investigatorType, String colName, int i, int listNum) {
		String rtnStr =
	    		" LISTAGG( " + 
	    		" CASE WHEN PD.INVESTIGATOR_TYPE IN ('" + investigatorType + "') THEN( " +
	    		" CASE WHEN (PD.INVESTIGATOR_STAFF_NO IS NOT NULL OR PD.INVESTIGATOR_NAME IS NOT NULL) " + 
	    		" AND (DEN <= (" + i*30 + ") AND DEN > " + (i*30 - 30) + " ) " +
	    		" THEN ( " + 
	    		" (CASE WHEN PD.INVESTIGATOR_STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
	    		" WHEN PD.INVESTIGATOR_STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
	    		" ELSE PD.INVESTIGATOR_NAME END) " + 
	    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " +
	    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END) ";
		if(colName.equals("OTHER_AUTHOR_LIST"))
	    	rtnStr += " ||' '||'['||(SELECT DESCRIPTION FROM RH_L_PROJECT_CAPACITY_V WHERE LOOKUP_CODE = PD.INVESTIGATOR_TYPE)||']' " ;
	    rtnStr +=
	    		" ) ";
		if(i == listNum) {
			rtnStr +=
		    		" WHEN DEN = (" + i*30 +
		    		")+1 THEN '...' ";}
		rtnStr +=
	    		" ELSE '' END ) END , '<br/>') " + 
	    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS " + colName + i + " "  ;
		return rtnStr;
	}
		
	
	public List<ProjectSummary> getProjectListByIds(List<Integer> idList) throws SQLException
	{
		List<ProjectSummary> voList = new ArrayList<ProjectSummary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty()) {
			String dataLevel = "P";
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
				
				    sqlBuf.append(
				    		" SELECT PH.PROJECT_NO, ") ;
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.PRIN_INVES_AUTHOR_LIST"+i+" AS PRIN_INVES_AUTHOR_LIST"+i+", " +
			    				" TMP.CO_PRIN_INVES_AUTHOR_LIST"+i+" AS CO_PRIN_INVES_AUTHOR_LIST"+i+", " +
								" TMP.CO_INVES_AUTHOR_LIST"+i+" AS CO_INVES_AUTHOR_LIST"+i+", " +
								" TMP.OTHER_AUTHOR_LIST"+i+" AS OTHER_AUTHOR_LIST"+i+", ");
				    }
				    sqlBuf.append(
				    		// 2.x
				    		" CASE WHEN IED_WORK_IND = 'Y' THEN 'Yes' WHEN IED_WORK_IND = 'N' THEN 'No' ELSE '' END AS IED_WORK_IND, " + 
				    		" CASE WHEN PROJ_TYPE = 'R' THEN 'Research Related' WHEN PROJ_TYPE = 'N' THEN 'Not Research Related' ELSE '' END AS PROJ_TYPE, " + 
				    		" ACTIVITY_CODE, " + 
				    		" CASE WHEN TECH_PROJ = 'Y' THEN 'Yes' WHEN TECH_PROJ = 'N' THEN 'No' ELSE '' END AS TECH_PROJ, " + 
				    		" RGC_PROJ_NUM, " + 
				    		// 4.x
				    		" TITLE_1 || ' ' || TITLE_2 || ' ' || TITLE_3 || ' ' || TITLE_4 AS TITLE, " + 
				    		" PROJECT_SUMMARY, " + 
				    		" PROJECT_SUMMARY_2, " + 
				    		" (CASE WHEN FROM_DAY IS NOT NULL THEN (FROM_DAY || '/') ELSE '' END) || FROM_MONTH || '/' || FROM_YEAR AS FROMDATE, " + 
				    		" (CASE WHEN TO_DAY IS NOT NULL THEN (TO_DAY || '/') ELSE '' END) || TO_MONTH || '/' || TO_YEAR AS TODATE, " + 
				    		" ((TO_YEAR-FROM_YEAR)*12 + (TO_MONTH-FROM_MONTH)) AS DURATION_MONTH, " +
				    		" TRUNC(((TO_YEAR-FROM_YEAR)*12 + (TO_MONTH-FROM_MONTH))/12)||'yr '|| " +
				    		" MOD(((TO_YEAR-FROM_YEAR)*12 + (TO_MONTH-FROM_MONTH)), 12)||'m' as DURATION_YEAR, " +
				    		" CASE WHEN COLLAB_PARTNER_UGC = 'Y' THEN 'Yes' WHEN COLLAB_PARTNER_UGC = 'N' THEN 'No' ELSE '' END AS COLLAB_PARTNER_UGC, " + 
				    		" CASE WHEN COLLAB_PARTNER_NON_INS = 'Y' THEN 'Yes' WHEN COLLAB_PARTNER_NON_INS = 'N' THEN 'No' ELSE '' END AS COLLAB_PARTNER_NON_INS, " + 
				    		" CONCATENATED_INV_NAME, " + 
				    		" NAME_OF_COLLABORATIVE_PARTNER, " + 
				    		" COLLABORATIVE_PARTNER_CITY, " + 
				    		" COLLABORATIVE_PARTNER_COUNTRY, " + 
				    		" COLLAB_OUTPUT_TITLE, " + 
				    		" COLLAB_OUTPUT_TYPE, " + 
				    		" COLLAB_OUTPUT_INST, " + 
				    		" COLLAB_OUTPUT_DATE, " + 
				    		" CASE WHEN FUNDED_PROJ = 'Y' THEN 'Yes' WHEN FUNDED_PROJ = 'N' THEN 'No' ELSE '' END AS FUNDED_PROJ, " + 
				    		" FUNDING_BODY, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_FUND_SOURCE_V WHERE LOOKUP_CODE = SAP_FUNDING_SOURCE) AS SAP_FUNDING_SOURCE, " + 
				    		" SAP_GRANT_AMT, " + 
				    		" FUNDING_ORG, " + 
				    		" FUNDING_OTHERS, " + 
				    		" CASE WHEN ROLE_INST = 'C' THEN 'Coordinating Institution' WHEN ROLE_INST = 'P' THEN 'Participating Institution' ELSE '' END AS ROLE_INST, " + 
				    		" CASE WHEN ROLE_INST_FOR_T690 = 'C' THEN 'Coordinating Institution' WHEN ROLE_INST_FOR_T690 = 'P' THEN 'Participating Institution' ELSE '' END AS ROLE_INST_FOR_T690, " + 
				    		" RELEASED_VAL, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_CODE AND LOOKUP_LEVEL = 1) AS SCH_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_DTL_CODE AND LOOKUP_LEVEL = 2) AS SCH_DTL_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_CODE AND LOOKUP_LEVEL = 1) AS DA_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_DTL_CODE AND LOOKUP_LEVEL = 2) AS DA_DTL_CODE, " + 
				    		" OTHER_DA_DTL, " + 
				    		" KEYWORDS, " +
				    		// manageInstituteRI
				    		" CDCF_STATUS, " +
				    		" FROM_YEAR, " +
				    		" FROM_MONTH, " +
				    		" FROM_DAY, " +
				    		" TO_YEAR, " +
				    		" TO_MONTH, " +
				    		" TO_DAY, " +
				    		" (CASE WHEN CDCF_SELECTED_IND = 'Y' THEN 'Yes' WHEN CDCF_SELECTED_IND = 'N' THEN 'No' ELSE '' END) AS CDCF_SELECTED_IND " + 
				    		" FROM RH_P_RESEARCH_PROJECT_HDR PH" );
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_RESEARCH_PROJECT_HDR QH ON QH.PROJECT_NO = PH.PROJECT_NO   " );
				    sqlBuf.append(
				    		" LEFT JOIN ( " +
				    		" SELECT PD.PROJECT_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
				    	sqlBuf.append(
				    		createAuthorList("PRINCIPAL INVESTIGATOR", "PRIN_INVES_AUTHOR_LIST",i,listNum) + ", " +
				    		createAuthorList("CO-PRINCIPAL INVESTIGATOR", "CO_PRIN_INVES_AUTHOR_LIST",i,listNum) + ", " +
				    		createAuthorList("CO-INVESTIGATOR", "CO_INVES_AUTHOR_LIST",i,listNum) + ", " +
				    		createAuthorList("COLLABORATOR', 'TEAM MEMBER', '9999", "OTHER_AUTHOR_LIST",i,listNum)
				    	);
				    	if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM (SELECT DENSE_RANK() OVER (PARTITION BY PD.PROJECT_NO, PD.INVESTIGATOR_TYPE ORDER BY LINE_NO) AS DEN, PD.*" +
				    		" FROM RH_P_RESEARCH_PROJECT_DTL PD WHERE PD.DATA_LEVEL = '"+ dataLevel +"' ) PD " +
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " +
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " +
				    		" WHERE PD.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PD.PROJECT_NO " +
				    		" ) TMP ON PH.PROJECT_NO = TMP.PROJECT_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
				    sqlBuf.append(
				    		" AND PH.PROJECT_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
				
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
					//logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						ProjectSummary vObj = new ProjectSummary();
						vObj.setProjectNo(rs.getInt("PROJECT_NO"));
						String prin_inves_author_list = "";
						String co_prin_inves_author_list = "";
						String co_inves_author_list = "";
						String other_author_list = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("PRIN_INVES_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									prin_inves_author_list += "<br/>" ;
								prin_inves_author_list += authListSeg;
							}
							authListSeg = rs.getString("CO_PRIN_INVES_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									co_prin_inves_author_list += "<br/>" ;
								co_prin_inves_author_list += authListSeg;
							}
							authListSeg = rs.getString("CO_INVES_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									co_inves_author_list += "<br/>" ;
								co_inves_author_list += authListSeg;
							}
							authListSeg = rs.getString("OTHER_AUTHOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									other_author_list += "<br/>" ;
								other_author_list += authListSeg;
							}
						}
						vObj.setPrin_inves_author_list(prin_inves_author_list);
						vObj.setCo_prin_inves_author_list(co_prin_inves_author_list);
						vObj.setCo_inves_author_list(co_inves_author_list);
						vObj.setOther_author_list(other_author_list);
						// 2.x
						vObj.setIed_work_ind(rs.getString("IED_WORK_IND"));
						vObj.setProject_type(rs.getString("PROJ_TYPE"));
						vObj.setActivity_code(rs.getString("ACTIVITY_CODE"));
						vObj.setTech_proj(rs.getString("TECH_PROJ"));
						vObj.setRgc_proj_num(rs.getString("RGC_PROJ_NUM"));
						
						// 4.x
						vObj.setProjTitle(rs.getString("TITLE"));
						vObj.setProject_summary(rs.getString("PROJECT_SUMMARY"));
						vObj.setProject_summary_2(rs.getString("PROJECT_SUMMARY_2"));
						vObj.setFromdate(rs.getString("FROMDATE"));
						vObj.setTodate(rs.getString("TODATE"));
						vObj.setDurationMonth(rs.getString("DURATION_MONTH"));
						String durationYearStr = rs.getString("DURATION_YEAR");
						/*if(StringUtils.isNoneBlank(durationYearStr)) {
							if(durationYearStr.equals("0yr 0m"))
								durationYearStr = "0yr 1m";
						}*/
						vObj.setDurationYear(durationYearStr);
						vObj.setCollab_partner_ugc(rs.getString("COLLAB_PARTNER_UGC"));
						vObj.setCollab_partner_non_ins(rs.getString("COLLAB_PARTNER_NON_INS"));
						vObj.setConcatenated_inv_name(rs.getString("CONCATENATED_INV_NAME"));
						vObj.setName_of_collaborative_partner(rs.getString("NAME_OF_COLLABORATIVE_PARTNER"));
						vObj.setCollaborative_partner_city(rs.getString("COLLABORATIVE_PARTNER_CITY"));
						vObj.setCollaborative_partner_country(rs.getString("COLLABORATIVE_PARTNER_COUNTRY"));
						vObj.setCollab_output_title(rs.getString("COLLAB_OUTPUT_TITLE"));
						vObj.setCollab_output_type(rs.getString("COLLAB_OUTPUT_TYPE"));
						vObj.setCollab_output_inst(rs.getString("COLLAB_OUTPUT_INST"));
						vObj.setCollab_output_date(rs.getString("COLLAB_OUTPUT_DATE"));
						vObj.setFunded_proj(rs.getString("FUNDED_PROJ"));
						vObj.setFunding_body(rs.getString("FUNDING_BODY"));
						vObj.setSap_funding_source(rs.getString("SAP_FUNDING_SOURCE"));
						vObj.setSap_grant_amt(rs.getString("SAP_GRANT_AMT"));
						vObj.setFunding_org(rs.getString("FUNDING_ORG"));
						vObj.setFunding_others(rs.getString("FUNDING_OTHERS"));
						vObj.setCollabT630(rs.getString("ROLE_INST"));
						vObj.setCollabT690(rs.getString("ROLE_INST_FOR_T690"));
						vObj.setReleased_val(rs.getString("RELEASED_VAL"));
						vObj.setSchCode(rs.getString("SCH_CODE"));
						vObj.setSchDtlCode(rs.getString("SCH_DTL_CODE"));
						vObj.setDaCode(rs.getString("DA_CODE"));
						vObj.setDaDtlCode(rs.getString("DA_DTL_CODE"));
						vObj.setOtherDaDtl(rs.getString("OTHER_DA_DTL"));
						vObj.setKeyword(rs.getString("KEYWORDS"));
						// manageInstituteRI
						vObj.setCDCFStatus(rs.getString("CDCF_STATUS"));
						vObj.setFromYear(rs.getString("FROM_YEAR"));
						vObj.setFromMonth(rs.getString("FROM_MONTH"));
						vObj.setFromDay(rs.getString("FROM_DAY"));
						vObj.setToYear(rs.getString("TO_YEAR"));
						vObj.setToMonth(rs.getString("TO_MONTH"));
						vObj.setToDay(rs.getString("TO_DAY"));
						vObj.setCdcf_selected_ind(rs.getString("CDCF_SELECTED_IND"));
						
						voList.add(vObj);
						
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		    return voList;
	}
	
	public String getFundSourceName(String lookup_code) 
	{
		List<FundSource> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM FundSource obj WHERE obj.pk.lookup_code = :lookup_code ORDER BY obj.print_order ";			
			TypedQuery<FundSource> q = em.createQuery(query, FundSource.class);
			q.setParameter("lookup_code", lookup_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0).getDescription():"";
	}	
	
	public List<FundSource> getFundSourceList(int lookup_level) 
	{
		List<FundSource> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM FundSource obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<FundSource> q = em.createQuery(query, FundSource.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<Capacity> getCapacityList(int lookup_level) 
	{
		List<Capacity> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Capacity obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<Capacity> q = em.createQuery(query, Capacity.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public void deleteAllInvestigators(int project_no, String data_level) throws Exception
	{
		if (project_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM ProjectDetails_P obj WHERE obj.pk.project_no = :project_no AND obj.pk.data_level = :data_level ");
				q.setParameter("project_no", project_no);
				q.setParameter("data_level", data_level);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete investigators (project_no=" + project_no + ", data_level="+ data_level + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteProjectDetails_Q(int project_no) throws Exception
	{
		if (project_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM ProjectDetails_Q obj WHERE obj.pk.project_no = :project_no ");
				q.setParameter("project_no", project_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete ProjectDetails_Q (project_no=" + project_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public List<ProjectSummary> getProjectSummaryByProjectNo(List<Integer> projectNoList){
		List<ProjectSummary> projectSummaryList = new ArrayList<ProjectSummary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(projectNoList != null && projectNoList.size() > 0) {
			try {
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
		
				sqlBuf.append("SELECT PH.PROJECT_NO, " + 
							  "PH.TITLE_1 || ' ' || PH.TITLE_2 || ' ' || PH.TITLE_3 || ' ' || PH.TITLE_4 AS TITLE , " + 
							  "PH.PROJECT_SUMMARY, PH.PROJECT_SUMMARY_2, " + 
							  "PH.FROM_YEAR, " + 
							  "QH.CDCF_GEN_DATE " + 
							  "FROM RH_P_RESEARCH_PROJECT_HDR PH " + 
							  "LEFT JOIN RH_Q_RESEARCH_PROJECT_HDR QH ON QH.PROJECT_NO = PH.PROJECT_NO " + 
							  "WHERE PH.DATA_LEVEL = 'P' AND ( ");
	
					for(Integer projectNo: projectNoList) {
						sqlBuf.append("PH.PROJECT_NO = " + Integer.toString(projectNo) + " OR ");
					}
				    
					sqlBuf = sqlBuf.delete(sqlBuf.length() - 4, sqlBuf.length() - 1);
					sqlBuf.append(" ) ");
					
					logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
					
					while (rs.next())
					{
						ProjectSummary projectSummary = new ProjectSummary();
						projectSummary.setProjectNo(rs.getInt("PROJECT_NO"));
						projectSummary.setProjTitle(rs.getString("TITLE"));
						projectSummary.setProject_summary(rs.getString("PROJECT_SUMMARY"));
						projectSummary.setProject_summary_2(rs.getString("PROJECT_SUMMARY_2"));
						projectSummary.setCDCFGenDate(rs.getTimestamp("CDCF_GEN_DATE"));
						projectSummary.setFromYear(rs.getString("FROM_YEAR"));
						
						projectSummaryList.add(projectSummary);						
					}
			}catch (Exception e){
				throw new RuntimeException(e);
			}finally{
				pm.close(em);
			}
		}
		
		return projectSummaryList;
	}
	
	public List<Summary> getProjectSummaryCountList(String staffNo, String dataLevel, String startDate, String endDate, List<String> deptList, boolean isTotal, List<String> fundSrcList, String fundedProj, String projType, String tech, String funding_body){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (isTotal == false && deptList != null) {
				selectDept = "SD.DEPT_CODE_EACH, ";
			}
			sqlBuf.append("SELECT RP.PERIOD_ID, RP.PERIOD_DESC, " + selectDept + "COUNT(DISTINCT(PH.PROJECT_NO)) AS C FROM RICH.RH_P_RESEARCH_PROJECT_HDR PH"
					+ "    LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_DTL D ON PH.PROJECT_NO = D.PROJECT_NO AND PH.DATA_LEVEL = D.DATA_LEVEL"
					+ "    LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT SD ON D.INVESTIGATOR_STAFF_NO = SD.STAFF_NUMBER "
					+ "    LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON "
					+ "        TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') BETWEEN TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY') OR "
					+ "			TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') BETWEEN TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY') OR "
					+ " 		(TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') <= TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') >= TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY')) ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"'  ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND D.INVESTIGATOR_STAFF_NO = '"+staffNo+"' ");
			}
			if (!Strings.isNullOrEmpty(fundedProj)) {
				sqlBuf.append(" AND PH.FUNDED_PROJ = '"+fundedProj+"' ");
			}
			if (!Strings.isNullOrEmpty(projType)) {
				if ("N".equals(projType)) {
					sqlBuf.append(" AND (PH.PROJ_TYPE = '"+projType+"' OR PH.PROJ_TYPE ='T' OR PH.PROJ_TYPE IS NULL) ");
				}else {
					sqlBuf.append(" AND PH.PROJ_TYPE = '"+projType+"' ");
				}
			}
			if (!Strings.isNullOrEmpty(tech)) {
				sqlBuf.append(" AND PH.TECH_PROJ = '"+tech+"' ");
			}
			if (!Strings.isNullOrEmpty(funding_body)) {
				//sqlBuf.append(" AND H.FUNDING_BODY = '"+funding_body+"' ");
				sqlBuf.append(" AND PH.sap_funding_source IN (SELECT LOOKUP_CODE FROM RICH.RH_L_FUND_SOURCE_V WHERE DESCRIPTION LIKE '"+funding_body+" - %') ");
			}
			if (fundSrcList != null) {
				sqlBuf.append(" AND PH.sap_funding_source IN ( '" +
						fundSrcList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			
			if (!Strings.isNullOrEmpty(startDate) && Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_MONTH, '"+startMonthStr+"') || '/' || PH.FROM_YEAR, 'MM/YYYY') >= "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY')");
			}
			
			if (Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') <= "
						+ "TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			
			
			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				String endMonthStr = endDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_MONTH, '"+startMonthStr+"') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (isTotal == false && deptList != null) {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE_EACH "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, SD.DEPT_CODE_EACH ");
			}else {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC ");
			}
			//System.out.println("PROJECT CUR:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Summary obj = new Summary();
				obj.setPeriod_id(rs.getString("PERIOD_ID"));
				obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
				obj.setCount(rs.getString("C"));
				if (isTotal == false && deptList != null) {
					obj.setFacDept(rs.getString("DEPT_CODE_EACH"));
				}
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<Summary> getProjectSummaryCountNewList(String staffNo, String dataLevel, String startDate, String endDate, List<String> deptList, boolean isTotal, List<String> fundSrcList, String fundedProj, String projType, String tech, String funding_body){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (isTotal == false && deptList != null) {
				selectDept = "SD.DEPT_CODE_EACH, ";
			}
			
			
			sqlBuf.append("SELECT RP.PERIOD_ID, RP.PERIOD_DESC, " + selectDept + "COUNT(DISTINCT(H.PROJECT_NO)) AS C FROM RICH.RH_P_RESEARCH_PROJECT_HDR H"
					+ "    LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_DTL D ON H.PROJECT_NO = D.PROJECT_NO AND H.DATA_LEVEL = D.DATA_LEVEL"
					+ "    LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT SD ON D.INVESTIGATOR_STAFF_NO = SD.STAFF_NUMBER "
					+ "    LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON "
					+ "        TO_DATE(NVL(H.FROM_MONTH, '01') || '/' || H.FROM_YEAR, 'MM/YYYY') BETWEEN  "
					+ "        TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') ");
			sqlBuf.append(" WHERE H.DATA_LEVEL = '"+dataLevel+"'  ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND D.INVESTIGATOR_STAFF_NO = '"+staffNo+"' ");
			}
			if (!Strings.isNullOrEmpty(fundedProj)) {
				sqlBuf.append(" AND H.FUNDED_PROJ = '"+fundedProj+"' ");
			}
			if (!Strings.isNullOrEmpty(projType)) {
				if ("N".equals(projType)) {
					sqlBuf.append(" AND (H.PROJ_TYPE = '"+projType+"' OR H.PROJ_TYPE ='T' OR H.PROJ_TYPE IS NULL) ");
				}else {
					sqlBuf.append(" AND H.PROJ_TYPE = '"+projType+"' ");
				}
			}
			if (!Strings.isNullOrEmpty(tech)) {
				sqlBuf.append(" AND H.TECH_PROJ = '"+tech+"' ");
			}
			if (!Strings.isNullOrEmpty(funding_body)) {
				//sqlBuf.append(" AND H.FUNDING_BODY = '"+funding_body+"' ");
				sqlBuf.append(" AND H.sap_funding_source IN (SELECT LOOKUP_CODE FROM RICH.RH_L_FUND_SOURCE_V WHERE DESCRIPTION LIKE '"+funding_body+" - %') ");
			}
			if (fundSrcList != null) {
				sqlBuf.append(" AND H.sap_funding_source IN ( '" +
						fundSrcList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			
			if (!Strings.isNullOrEmpty(startDate) && Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(H.FROM_MONTH, '"+startMonthStr+"') || '/' || H.FROM_YEAR, 'MM/YYYY') >= "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY')");
			}
			
			if (Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				sqlBuf.append(" AND TO_DATE(NVL(H.FROM_MONTH, '01') || '/' || H.FROM_YEAR, 'MM/YYYY') <= "
						+ "TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			
			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(H.FROM_MONTH, '"+startMonthStr+"') || '/' || H.FROM_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (isTotal == false && deptList != null) {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE_EACH "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, SD.DEPT_CODE_EACH ");
			}else {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC ");
			}
			//System.out.println("Project New:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Summary obj = new Summary();
				obj.setPeriod_id(rs.getString("PERIOD_ID"));
				obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
				obj.setCount(rs.getString("C"));
				if (isTotal == false && deptList != null) {
					obj.setFacDept(rs.getString("DEPT_CODE_EACH"));
				}
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<ProjectReport> getPastYearProjectReportList(String dataLevel, String startDate, String endDate, Integer periodId, List<String> deptList, List<String> fundSrcList){
		List<ProjectReport> objList = new ArrayList<ProjectReport>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT PERIOD_ID, PERIOD_DESC, DEPT_CODE, COUNT(DISTINCT(PROJECT_NO)) AS C, SUM(AMT) AS A FROM ("
					+ " SELECT RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO, MAX(PH.SAP_GRANT_AMT) AS AMT FROM RICH.RH_PS_RESEARCH_PROJECT_HDR PH   "
					+ " INNER JOIN  (   Select PROJECT_NO, " + 
					"                       INVESTIGATOR_STAFF_NO," + 
					"                       LINE_NO" + 
					"                from (" + 
					"                    SELECT  PROJECT_NO, " + 
					"                            INVESTIGATOR_STAFF_NO," + 
					"                            RANK() OVER (PARTITION BY PROJECT_NO ORDER BY LINE_NO) AS staff_rank," + 
					"                            LINE_NO" + 
					"                    FROM RICH.RH_P_RESEARCH_PROJECT_DTL INNER JOIN RICH.rh_elig_acad_staff_v STT ON STT.EMPLOYEE_NUMBER = INVESTIGATOR_STAFF_NO " + 
					"                    WHERE DATA_LEVEL = '"+dataLevel+ 
					"  '                  AND NON_IED_STAFF_FLAG != 'Y' " + 
					"                    AND INVESTIGATOR_TYPE = 'PRINCIPAL INVESTIGATOR' " + 
					"                ) WHERE staff_rank = 1 ) PD ON PH.PROJECT_NO = PD.PROJECT_NO "
					+ " LEFT JOIN RICH.RH_ELIG_ACAD_STAFF_V SD ON PD.INVESTIGATOR_STAFF_NO = SD.EMPLOYEE_NUMBER "
					+ " LEFT JOIN RICH.RH_QS_RESEARCH_PROJECT_HDR QH ON PH.PROJECT_NO = QH.PROJECT_NO AND PH.PERIOD_ID = QH.PERIOD_ID "
					+ " LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') BETWEEN TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY') OR "
					+ " TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') BETWEEN TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY') OR "
					+ " (TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') <= TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') >= TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY')) ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND ( QH.CDCF_STATUS = 'CDCF_GENERATED' OR QH.CDCF_STATUS = 'CDCF_SPEC') AND  RP.PERIOD_ID = "+periodId+" ");


			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' ||  NVL(PH.FROM_MONTH, '"+startMonthStr+"') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND TO_DATE('"+endDate+"', 'DD/MM/YYYY')");
			}
			if (fundSrcList != null) {
				sqlBuf.append(" AND PH.sap_funding_source IN ( '" +
						fundSrcList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			//if (deptList != null) {
			//	sqlBuf.append(" AND SD.DEPT_CODE IN ( '" +
			 //   		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			//}
			sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO  "
					+ "	ORDER BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO) "
					+" GROUP BY PERIOD_ID, PERIOD_DESC, DEPT_CODE "
					+ "	ORDER BY PERIOD_ID, PERIOD_DESC, DEPT_CODE ");
		
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectReport obj = new ProjectReport();
				obj.setDept(rs.getString("DEPT_CODE"));
				obj.setNum(rs.getInt("C"));
				obj.setValue(rs.getDouble("A"));
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<ProjectReport> getProjectReportList(String dataLevel, String startDate, String endDate, Integer periodId, List<String> deptList, List<String> fundSrcList){
		List<ProjectReport> objList = new ArrayList<ProjectReport>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT PERIOD_ID, PERIOD_DESC, DEPT_CODE, COUNT(DISTINCT(PROJECT_NO)) AS C, SUM(AMT) AS A FROM ("
					+ "SELECT RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO, MAX(PH.SAP_GRANT_AMT) AS AMT FROM RICH.RH_P_RESEARCH_PROJECT_HDR PH   "
					+ " INNER JOIN  (   Select PROJECT_NO, " + 
					"                       INVESTIGATOR_STAFF_NO," + 
					"                       LINE_NO" + 
					"                from (" + 
					"                    SELECT  PROJECT_NO, " + 
					"                            INVESTIGATOR_STAFF_NO," + 
					"                            RANK() OVER (PARTITION BY PROJECT_NO ORDER BY LINE_NO) AS staff_rank," + 
					"                            LINE_NO" + 
					"                    FROM RICH.RH_P_RESEARCH_PROJECT_DTL INNER JOIN RICH.rh_elig_acad_staff_v STT ON STT.EMPLOYEE_NUMBER = INVESTIGATOR_STAFF_NO " + 
					"                    WHERE DATA_LEVEL = '"+dataLevel+ 
					"'                    AND NON_IED_STAFF_FLAG != 'Y' " + 
					"                    AND INVESTIGATOR_TYPE = 'PRINCIPAL INVESTIGATOR' " + 
					"                ) WHERE staff_rank = 1 ) PD ON PH.PROJECT_NO = PD.PROJECT_NO "
					+ "LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_DTL PD ON PH.PROJECT_NO = PD.PROJECT_NO AND PH.DATA_LEVEL = PD.DATA_LEVEL "
					+ "LEFT JOIN RICH.RH_ELIG_ACAD_STAFF_V SD ON PD.INVESTIGATOR_STAFF_NO = SD.EMPLOYEE_NUMBER "
					+ "LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON PH.PROJECT_NO = QH.PROJECT_NO "
					+ "LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') BETWEEN TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY') OR "
					+ "TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') BETWEEN TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY') OR "
					+ "(TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') <= TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY') >= TO_DATE(NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'MM/YYYY')) ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND ( QH.CDCF_STATUS = 'CDCF_GENERATED' OR QH.CDCF_STATUS = 'CDCF_SPEC') AND RP.PERIOD_ID = "+periodId+" ");


			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' ||  NVL(PH.FROM_MONTH, '"+startMonthStr+"') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND TO_DATE('"+endDate+"', 'DD/MM/YYYY')");
			}
			if (fundSrcList != null) {
				sqlBuf.append(" AND PH.sap_funding_source IN ( '" +
						fundSrcList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
		//	if (deptList != null) {
			//	sqlBuf.append(" AND SD.DEPT_CODE IN ( '" +
			//    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
		//	}
			sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO  "
					+ "	ORDER BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO) "
					+" GROUP BY PERIOD_ID, PERIOD_DESC, DEPT_CODE "
					+ "	ORDER BY PERIOD_ID, PERIOD_DESC, DEPT_CODE ");
		
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectReport obj = new ProjectReport();
				obj.setDept(rs.getString("DEPT_CODE"));
				obj.setNum(rs.getInt("C"));
				obj.setValue(rs.getDouble("A"));
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		return objList;
	}
	
	public List<ProjectReport> getProjectReportNewList(String dataLevel, String startDate, String endDate, Integer periodId, List<String> deptList, List<String> fundSrcList){
		List<ProjectReport> objList = new ArrayList<ProjectReport>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT PERIOD_ID, PERIOD_DESC, DEPT_CODE, COUNT(DISTINCT(PROJECT_NO)) AS C, SUM(AMT) AS A FROM ("
					+ " SELECT RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO, MAX(PH.SAP_GRANT_AMT) AS AMT FROM RICH.RH_P_RESEARCH_PROJECT_HDR PH   "
					+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_DTL PD ON PH.PROJECT_NO = PD.PROJECT_NO AND PH.DATA_LEVEL = PD.DATA_LEVEL "
					+ " LEFT JOIN RICH.RH_ELIG_ACAD_STAFF_V SD ON PD.INVESTIGATOR_STAFF_NO = SD.EMPLOYEE_NUMBER "
					+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON PH.PROJECT_NO = QH.PROJECT_NO "
					+ " RIGHT JOIN (SELECT PROJECT_NO, DATA_LEVEL, MIN(LINE_NO) AS LINE_NO FROM RICH.RH_P_RESEARCH_PROJECT_DTL INNER JOIN RICH.rh_elig_acad_staff_v STT ON STT.EMPLOYEE_NUMBER = INVESTIGATOR_STAFF_NO "
					+ "            WHERE DATA_LEVEL = '"+dataLevel+"' AND INVESTIGATOR_TYPE = 'PRINCIPAL INVESTIGATOR' AND INVESTIGATOR_STAFF_NO IS NOT NULL "
					+ "            GROUP BY (PROJECT_NO, DATA_LEVEL))T2 "
					+ "            ON PD.PROJECT_NO = T2.PROJECT_NO AND PD.LINE_NO = T2.LINE_NO AND PD.DATA_LEVEL = T2.DATA_LEVEL "
					+ " LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY')");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND ( QH.CDCF_STATUS  = 'CDCF_GENERATED' OR QH.CDCF_STATUS  = 'CDCF_SPEC' ) AND RP.PERIOD_ID = "+periodId+" ");


			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' ||  NVL(PH.FROM_MONTH, '"+startMonthStr+"') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND TO_DATE('"+endDate+"', 'DD/MM/YYYY')");
			}
			if (fundSrcList != null) {
				sqlBuf.append(" AND PH.sap_funding_source IN ( '" +
						fundSrcList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			/*
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			*/
			sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO  "
					+ "	ORDER BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO) "
					+" GROUP BY PERIOD_ID, PERIOD_DESC, DEPT_CODE "
					+ "	ORDER BY PERIOD_ID, PERIOD_DESC, DEPT_CODE ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectReport obj = new ProjectReport();
				obj.setDept(rs.getString("DEPT_CODE"));
				obj.setNum(rs.getInt("C"));
				obj.setValue(rs.getDouble("A"));
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<ProjectReport> getPastYearProjectReportNewList(String dataLevel, String startDate, String endDate, Integer periodId, List<String> deptList, List<String> fundSrcList){
		List<ProjectReport> objList = new ArrayList<ProjectReport>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT PERIOD_ID, PERIOD_DESC, DEPT_CODE, COUNT(DISTINCT(PROJECT_NO)) AS C, SUM(AMT) AS A FROM ("
					+ " SELECT RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO, MAX(PH.SAP_GRANT_AMT) AS AMT FROM RICH.RH_PS_RESEARCH_PROJECT_HDR PH   "
					+ " LEFT JOIN RICH.RH_PS_RESEARCH_PROJECT_DTL PD ON PH.PROJECT_NO = PD.PROJECT_NO AND PH.DATA_LEVEL = PD.DATA_LEVEL AND PH.PERIOD_ID = PD.PERIOD_ID "
					+ " LEFT JOIN RICH.RH_ELIG_ACAD_STAFF_V SD ON PD.INVESTIGATOR_STAFF_NO = SD.EMPLOYEE_NUMBER "
					+ " LEFT JOIN RICH.RH_QS_RESEARCH_PROJECT_HDR QH ON PH.PROJECT_NO = QH.PROJECT_NO AND PH.PERIOD_ID = PH.PERIOD_ID  "
					+ " RIGHT JOIN (SELECT PROJECT_NO, DATA_LEVEL, PERIOD_ID, MIN(LINE_NO) AS LINE_NO FROM RICH.RH_PS_RESEARCH_PROJECT_DTL INNER JOIN RICH.rh_elig_acad_staff_v STT ON STT.EMPLOYEE_NUMBER = INVESTIGATOR_STAFF_NO "
					+ "            WHERE DATA_LEVEL = '"+dataLevel+"' AND PERIOD_ID = "+periodId
					+ "				AND INVESTIGATOR_TYPE = 'PRINCIPAL INVESTIGATOR' AND INVESTIGATOR_STAFF_NO IS NOT NULL "
					+ "            GROUP BY (PROJECT_NO, DATA_LEVEL, PERIOD_ID))T2 "
					+ "            ON PD.PROJECT_NO = T2.PROJECT_NO AND PD.LINE_NO = T2.LINE_NO AND PD.DATA_LEVEL = T2.DATA_LEVEL AND PD.PERIOD_ID = T2.PERIOD_ID "
					+ " LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY')");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"'AND ( QH.CDCF_STATUS  = 'CDCF_GENERATED' OR QH.CDCF_STATUS  = 'CDCF_SPEC' )    AND RP.PERIOD_ID = "+periodId+" ");

			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' ||  NVL(PH.FROM_MONTH, '"+startMonthStr+"') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND TO_DATE('"+endDate+"', 'DD/MM/YYYY')");
			}
			if (fundSrcList != null) {
				sqlBuf.append(" AND PH.sap_funding_source IN ( '" +
						fundSrcList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO  "
					+ "	ORDER BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE, PH.PROJECT_NO) "
					+" GROUP BY PERIOD_ID, PERIOD_DESC, DEPT_CODE "
					+ "	ORDER BY PERIOD_ID, PERIOD_DESC, DEPT_CODE ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectReport obj = new ProjectReport();
				obj.setDept(rs.getString("DEPT_CODE"));
				obj.setNum(rs.getInt("C"));
				obj.setValue(rs.getDouble("A"));
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	/*public void updateCreateDetails(Integer project_no, String creator, Timestamp creationTime) throws Exception
	{
		if (project_no != null)
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			UserTransaction utx = null;
			try
			{
				utx = pm.getUserTransaction();
				utx.begin();			
				conn = pm.getConnection();
				
				String sql = "UPDATE RH_P_RESEARCH_PROJECT_HDR SET creator = ?, creation_time = ? WHERE project_no = ? AND data_level = ?";
				pStmt = conn.prepareStatement(sql);
				pStmt.setString(1, creator);
				pStmt.setTimestamp(2, creationTime);	
				pStmt.setInt(3, project_no);
				pStmt.setString(4, "P");
				pStmt.executeQuery();
				utx.commit();
			}
			catch (Exception e)
			{
				throw e;
			}			
			finally
			{
				pm.close(em);
			}	
		}
	}*/
	

	
	public List<ProjectDetails_P> getProjectDetails_P_byRiNo(List<List<Integer>> riNosParts, String data_level)
	{
		List<ProjectDetails_P> objList = null;
		EntityManager em = null;	
		String where = "";
		try
		{
			em = getEntityManager();	
			if (!riNosParts.isEmpty()) {
				for (int i = 0; i < riNosParts.size(); i++) {
					if (i == 0) {
						where += " AND ( ";
					}
					where += " obj.pk.project_no IN :riNos"+i;
					if (i == riNosParts.size() - 1) {
						where += " ) ";
					}else {
						where += " OR ";
					}
				}
				
				String query = "SELECT obj FROM ProjectDetails_P obj WHERE obj.pk.data_level = :data_level AND obj.pk.line_no = :line_no " + where +
									" ORDER BY obj.projectHeader_p.from_year DESC, obj.projectHeader_p.from_month DESC";			
				TypedQuery<ProjectDetails_P> q = em.createQuery(query, ProjectDetails_P.class);
				if (!riNosParts.isEmpty()) {
					for (int i = 0; i < riNosParts.size(); i++) {
						q.setParameter("riNos"+i, riNosParts.get(i));
					}
				}
				q.setParameter("data_level", data_level);
				q.setParameter("line_no", 1);
				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<Integer> getProjectNoListByDept(List<String> deptList, String data_level, List<String> fundingSourceList) throws SQLException
	{
		List<Integer> voList = new ArrayList<Integer>();
		
		if(deptList != null && data_level != null) {
			if (deptList.size() > 0) {
			PersistenceManager pm = PersistenceManager.getInstance();
			Connection conn = null;
			PreparedStatement pStmt = null;
			try
			{
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
			
			    sqlBuf.append(
			    		" SELECT DISTINCT PD.PROJECT_NO " );
			    sqlBuf.append(
			    		" FROM RH_P_RESEARCH_PROJECT_DTL PD  " +
			    		" LEFT JOIN RH_P_RESEARCH_PROJECT_HDR PH ON (PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +	
			    		" LEFT JOIN RH_P_STAFF_IDENTITY_DEPT STAFF_DEPT ON (PD.INVESTIGATOR_STAFF_NO = STAFF_DEPT.STAFF_NUMBER) " );
			    sqlBuf.append(
			    		" WHERE  " + 
			    		" PD.DATA_LEVEL = '" + data_level + "' ");
			    sqlBuf.append(
			    		" AND STAFF_DEPT.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			    if (fundingSourceList != null) {
				    sqlBuf.append(
				    		" AND PH.SAP_FUNDING_SOURCE IN ( '" +
				    		fundingSourceList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			    }
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
	
				while (rs.next())
				{
					voList.add(rs.getInt("PROJECT_NO"));
				}
			}
			
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			}
		}
		return voList;
	}
	
	public Map<String, Map<String, Map<String, Map<String, Double>>>> getCRD003ReportList(String dataLevel, 
			String startDate, String endDate, List<String> facList, 
			List<String> deptList, Set<String> fundTypeList){
		Map<String, Map<String, Map<String, Map<String, Double>>>> objMap = new LinkedHashMap<String, Map<String, Map<String, Map<String, Double>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT "
					+ " DEPT1.LOOKUP_CODE AS FAC_CODE, "
					+ " DEPT2.LOOKUP_CODE AS DEPARTMENT_CODE, "
					+ " FUNDTYPE.LOOKUP_CODE AS FUND_TYPE, "
					+ " SUM(PH.RELEASED_VAL) AS AMOUNT, "
					+ " COUNT(PH.PROJECT_NO) AS WEIGHTING "
					+ " FROM RICH.RH_P_RESEARCH_PROJECT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON PD.PROJECT_NO = QH.PROJECT_NO "
					+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUNDTYPE ON FUNDTYPE.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT2 ON DEPT2.LOOKUP_CODE = S.DEPARTMENT_CODE "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT1 ON DEPT1.LOOKUP_CODE = DEPT2.PARENT_LOOKUP_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND PD.LINE_NO = 1 "
					+ " AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') ");
			if (fundTypeList != null) {
				sqlBuf.append(" AND FUNDTYPE.LOOKUP_CODE IN ( '" +
						fundTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (facList != null) {
				sqlBuf.append(" AND DEPT1.LOOKUP_CODE IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND DEPT2.LOOKUP_CODE IN ( '" +
						deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY "
					+ " DEPT1.LOOKUP_CODE, "
					+ " DEPT2.LOOKUP_CODE, "
					+ " FUNDTYPE.LOOKUP_CODE "
					+ " ORDER BY DEPT1.LOOKUP_CODE, DEPT2.LOOKUP_CODE ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fac = rs.getString("FAC_CODE");
				String dept = rs.getString("DEPARTMENT_CODE");
				String fundType = rs.getString("FUND_TYPE");
				if(fundType.equals("830") || fundType.equals("880") || fundType.equals("1570"))
					fundType = "900";
				Double no = rs.getDouble("WEIGHTING");
				Double amount = rs.getDouble("AMOUNT");
				
				if(objMap.get(fac) == null) {
					objMap.put(fac, new HashMap<String, Map<String, Map<String, Double>>>());
				}
				if(objMap.get(fac).get(fundType) == null) {
					objMap.get(fac).put(fundType, new HashMap<String, Map<String, Double>>());
				}
				if(objMap.get(fac).get(fundType).get(dept) == null) {
					objMap.get(fac).get(fundType).put(dept, new HashMap<String, Double>());
				}
				if(objMap.get(fac).get(fundType).get(dept).get("No.") == null) objMap.get(fac).get(fundType).get(dept).put("No.", 0.0);
				if(objMap.get(fac).get(fundType).get(dept).get("Amount") == null) objMap.get(fac).get(fundType).get(dept).put("Amount", 0.0);
				objMap.get(fac).get(fundType).get(dept).put("No.", objMap.get(fac).get(fundType).get(dept).get("No.") + no);
				objMap.get(fac).get(fundType).get(dept).put("Amount", objMap.get(fac).get(fundType).get(dept).get("Amount") + amount);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, Map<String, Double>>>> getCRD004ReportList(String dataLevel, 
			String startDate, String endDate, List<String> facList, 
			List<String> deptList, Set<String> fundTypeList){
		Map<String, Map<String, Map<String, Map<String, Double>>>> objMap = new LinkedHashMap<String, Map<String, Map<String, Map<String, Double>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT "
					+ " DEPT1.LOOKUP_CODE AS FAC_CODE, "
					+ " DEPT2.LOOKUP_CODE AS DEPARTMENT_CODE, "
					+ " S.LAST_NAME, S.FIRST_NAME,"
					+ " (S.LAST_NAME || '/' || S.FIRST_NAME || '/' || S.DEPARTMENT_CODE || '/' || S.POST_RANK_CODE) AS STAFF_NAME, "
					+ " FUNDTYPE.LOOKUP_CODE AS FUND_TYPE, "
					+ " SUM(PH.RELEASED_VAL) AS AMOUNT, "
					+ " COUNT(PH.PROJECT_NO) AS WEIGHTING "
					+ " FROM RICH.RH_P_RESEARCH_PROJECT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON PD.PROJECT_NO = QH.PROJECT_NO "
					+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUNDTYPE ON FUNDTYPE.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT2 ON DEPT2.LOOKUP_CODE = S.DEPARTMENT_CODE "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT1 ON DEPT1.LOOKUP_CODE = DEPT2.PARENT_LOOKUP_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') ");
			if (fundTypeList != null) {
				sqlBuf.append(" AND FUNDTYPE.LOOKUP_CODE IN ( '" +
						fundTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (facList != null) {
				sqlBuf.append(" AND DEPT1.LOOKUP_CODE IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND DEPT2.LOOKUP_CODE IN ( '" +
						deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY "
					+ " DEPT1.LOOKUP_CODE, "
					+ " DEPT2.LOOKUP_CODE, S.LAST_NAME, S.FIRST_NAME, "
					+ " (S.LAST_NAME || '/' || S.FIRST_NAME || '/' || S.DEPARTMENT_CODE || '/' || S.POST_RANK_CODE), "
					+ " FUNDTYPE.LOOKUP_CODE "
					+ " ORDER BY DEPT1.LOOKUP_CODE, DEPT2.LOOKUP_CODE, S.LAST_NAME, S.FIRST_NAME ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fac = rs.getString("FAC_CODE");
				String fundType = rs.getString("FUND_TYPE");
				if(fundType.equals("830") || fundType.equals("880") || fundType.equals("1570"))
					fundType = "900";
				String name = rs.getString("STAFF_NAME");
				Double no = rs.getDouble("WEIGHTING");
				Double amount = rs.getDouble("AMOUNT");
				
				if(objMap.get(fac) == null) {
					objMap.put(fac, new LinkedHashMap<String, Map<String, Map<String, Double>>>());
				}
				if(objMap.get(fac).get(name) == null) {
					objMap.get(fac).put(name, new HashMap<String, Map<String, Double>>());
				}
				if(objMap.get(fac).get(name).get(fundType) == null) {
					objMap.get(fac).get(name).put(fundType, new HashMap<String, Double>());
				}
				if(objMap.get(fac).get(name).get(fundType).get("No.") == null) objMap.get(fac).get(name).get(fundType).put("No.", 0.0);
				if(objMap.get(fac).get(name).get(fundType).get("Amount") == null) objMap.get(fac).get(name).get(fundType).put("Amount", 0.0);
				objMap.get(fac).get(name).get(fundType).put("No.", objMap.get(fac).get(name).get(fundType).get("No.") + no);
				objMap.get(fac).get(name).get(fundType).put("Amount", objMap.get(fac).get(name).get(fundType).get("Amount") + amount);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public List<ProjectSummary> getCHRMProjReportList(String dataLevel, String startDate, String endDate){
		List<ProjectSummary> objList = new ArrayList<ProjectSummary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(
		    		" SELECT DISTINCT PH.PROJECT_NO, " +
		    		" CASE WHEN CDCF_STATUS = 'CDCF_GENERATED' THEN 'Count' ELSE 'Not Count' END AS DCC_STAT, " +
		    		" TO_CHAR(PH.CENSUS_DATE, 'DD-FMMon-YYYY') AS CENSUS_DATE, " +
		    		" ACTIVITY_CODE, " + 
		    		" TITLE_1, " + 
		    		" TITLE_2, " + 
		    		" TITLE_3, " + 
		    		" TITLE_4, " + 
		    		" PROJECT_SUMMARY, " + 
		    		" PROJECT_SUMMARY_2, " + 
		    		" ROLE_INST, " +
		    		" RELEASED_VAL, " + 
		    		" SAP_GRANT_AMT, " + 
		    		" FROM_DAY, " + 
		    		" FROM_MONTH, " + 
		    		" FROM_YEAR, " + 
		    		" TO_DAY, " + 
		    		" TO_MONTH, " + 
		    		" TO_YEAR, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_FUND_SOURCE_V WHERE LOOKUP_CODE = SAP_FUNDING_SOURCE) AS SAP_FUNDING_SOURCE, " + 
		    		" KEY_RESEARCH_AREAS, " + 
		    		" OTHER_KEY_RESEARCH_AREAS, " + 
		    		" NAME_OF_COLLABORATIVE_PARTNER, " + 
		    		" COLLABORATIVE_PARTNER_COUNTRY, " + 
		    		" COLLABORATIVE_PARTNER_CITY, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_CODE AND LOOKUP_LEVEL = 1) AS SCH_CODE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_DTL_CODE AND LOOKUP_LEVEL = 2) AS SCH_DTL_CODE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_RESEARCH_AREA_V WHERE LOOKUP_CODE = RS_CODE AND LOOKUP_LEVEL = 1) AS RS_CODE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_RESEARCH_AREA_V WHERE LOOKUP_CODE = RS_DTL_CODE AND LOOKUP_LEVEL = 2) AS RS_DTL_CODE, " + 
		    		"  MAX(PD.LINE_NO) OVER (PARTITION BY PD.PROJECT_NO, PD.DATA_LEVEL) AS TOTAL_NO_OF_INVESTIGATOR, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_CODE AND LOOKUP_LEVEL = 1) AS DA_CODE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_DTL_CODE AND LOOKUP_LEVEL = 2) AS DA_DTL_CODE, " + 
		    		" OTHER_DA_DTL, " + 
		    		" PROJ_TYPE, " + 
		    		" COLLAB_OUTPUT_TITLE, " + 
		    		" COLLAB_OUTPUT_TYPE, " + 
		    		" COLLAB_OUTPUT_INST, " + 
		    		" COLLAB_OUTPUT_DATE, " + 
		    		" COLLAB_PARTNER_UGC, " + 
		    		" COLLAB_PARTNER_NON_INS, " + 
		    		" QH.COLLAB_T630, " + 
		    		" QH.COLLAB_T690, " + 
		    		" ROLE_INST_FOR_T690 ");
		    		sqlBuf.append(" FROM RICH.RH_P_RESEARCH_PROJECT_HDR PH "
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_DTL PD ON (PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS IN ('CDCF_GENERATED', 'CDCF_NOT_SEL', 'CDCF_SPEC' )  "
					+ " AND ( TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " OR TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN " 
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " OR (TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') < TO_DATE('" + startDate + "', 'DD/MM/YYYY') "
					+ " AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') > TO_DATE('" + endDate + "', 'DD/MM/YYYY')) ) ");
			sqlBuf.append("	ORDER BY PH.PROJECT_NO ");
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectSummary vObj = new ProjectSummary();
				vObj.setProjectNo(rs.getInt("PROJECT_NO"));
				vObj.setDccStat(rs.getString("DCC_STAT"));
				vObj.setCensusDate(rs.getString("CENSUS_DATE"));
				vObj.setActivity_code(rs.getString("ACTIVITY_CODE"));
				vObj.setTitle1(rs.getString("TITLE_1"));
				vObj.setTitle2(rs.getString("TITLE_2"));
				vObj.setTitle3(rs.getString("TITLE_3"));
				vObj.setTitle4(rs.getString("TITLE_4"));
				vObj.setProject_summary(rs.getString("PROJECT_SUMMARY"));
				vObj.setProject_summary_2(rs.getString("PROJECT_SUMMARY_2"));
				vObj.setRoleInstT630(rs.getString("ROLE_INST"));
				vObj.setReleased_val(rs.getString("RELEASED_VAL"));
				vObj.setSap_grant_amt(rs.getString("SAP_GRANT_AMT"));
				vObj.setFromYear(rs.getString("FROM_YEAR"));
				vObj.setFromMonth(rs.getString("FROM_MONTH"));
				vObj.setFromDay(rs.getString("FROM_DAY"));
				vObj.setToYear(rs.getString("TO_YEAR"));
				vObj.setToMonth(rs.getString("TO_MONTH"));
				vObj.setToDay(rs.getString("TO_DAY"));
				vObj.setSap_funding_source(rs.getString("SAP_FUNDING_SOURCE"));
				vObj.setKeyResearchAreas(rs.getString("KEY_RESEARCH_AREAS"));
				vObj.setOtherKeyResearchAreas(rs.getString("OTHER_KEY_RESEARCH_AREAS"));
				vObj.setName_of_collaborative_partner(rs.getString("NAME_OF_COLLABORATIVE_PARTNER"));
				vObj.setCollaborative_partner_country(rs.getString("COLLABORATIVE_PARTNER_COUNTRY"));
				vObj.setCollaborative_partner_city(rs.getString("COLLABORATIVE_PARTNER_CITY"));
				vObj.setSchCode(rs.getString("SCH_CODE"));
				vObj.setSchDtlCode(rs.getString("SCH_DTL_CODE"));
				vObj.setRsCode(rs.getString("RS_CODE"));
				vObj.setRsDtlCode(rs.getString("RS_DTL_CODE"));
				vObj.setTotalNoOfInvestigator(rs.getString("TOTAL_NO_OF_INVESTIGATOR"));
				vObj.setDaCode(rs.getString("DA_CODE"));
				vObj.setDaDtlCode(rs.getString("DA_DTL_CODE"));
				vObj.setOtherDaDtl(rs.getString("OTHER_DA_DTL"));
				vObj.setProject_type(rs.getString("PROJ_TYPE"));
				vObj.setCollab_output_title(rs.getString("COLLAB_OUTPUT_TITLE"));
				vObj.setCollab_output_type(rs.getString("COLLAB_OUTPUT_TYPE"));
				vObj.setCollab_output_inst(rs.getString("COLLAB_OUTPUT_INST"));
				vObj.setCollab_output_date(rs.getString("COLLAB_OUTPUT_DATE"));
				vObj.setCollab_partner_ugc(rs.getString("COLLAB_PARTNER_UGC"));
				vObj.setCollab_partner_non_ins(rs.getString("COLLAB_PARTNER_NON_INS"));
				vObj.setCollabT630(rs.getString("COLLAB_T630"));
				vObj.setCollabT690(rs.getString("COLLAB_T690"));
				vObj.setRoleInstT690(rs.getString("ROLE_INST_FOR_T690"));
				List<ProjectDetails_P> pDtlList = getProjectDetails_P(rs.getInt("PROJECT_NO"), dataLevel);
				vObj.setpDtlList(pDtlList);
				vObj.setNoPDtl(pDtlList.size());
				
				
				objList.add(vObj);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public Map<String, Map<String, List<ProjectSummary>>> getCHRMProj002ReportList(String dataLevel, String startDate, String endDate,String selectedDept){
		Map<String, Map<String, List<ProjectSummary>>> objMap = new LinkedHashMap<String, Map<String, List<ProjectSummary>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			//System.out.println("selectedDept:" + selectedDept);
			sqlBuf.append(
		    		"  SELECT DISTINCT * FROM ( SELECT  PD.PROJECT_NO, " +
		    		" CASE WHEN DEPT.DEPARTMENT_NAME IS NOT NULL THEN DEPT.DEPARTMENT_NAME || '('||S.DEPARTMENT_CODE || ')' " + 
		    		" ELSE S.DEPARTMENT_CODE END AS DEPARTMENT_NAME, " +
		    		" ( NVL2(S.HUSBAND_LAST_NAME,S.HUSBAND_LAST_NAME || ' ','') || S.LAST_NAME || ' ' || S.FIRST_NAME || '_' || S.CHINESE_NAME ) AS STAFF_NAME, " +
		    		" TITLE_1 || ' ' || TITLE_2 || ' ' || TITLE_3 || ' ' || TITLE_4 AS TITLE, " +
		    		" PROJ_TYPE, " + 
		    		" PROJECT_SUMMARY || ' ' || PROJECT_SUMMARY_2 AS PROJECT_SUMMARY, " + 
		    		" (SELECT NVL(DESCRIPTION,'') FROM RH_L_FUND_SOURCE_V WHERE LOOKUP_CODE = SAP_FUNDING_SOURCE) AS SAP_FUNDING_SOURCE, " +
		    		" FROM_YEAR || '/' || FROM_MONTH AS FROM_YEAR, " +
		    		" TO_YEAR || '/' || TO_MONTH AS TO_YEAR, " +
		    		" NAME_OF_COLLABORATIVE_PARTNER, " + 
		    		" COLLABORATIVE_PARTNER_COUNTRY, " + 
		    		" COLLAB_OUTPUT_TYPE, " + 
		    		" COLLAB_OUTPUT_INST, " + 
		    		" CDCF_STATUS,"
		    		+ "COLLAB_T630, "
		    		+ "COLLAB_T690  ");
		    		sqlBuf.append(" FROM RICH.RH_P_RESEARCH_PROJECT_DTL PD "
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) "
		    		+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
		    		+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' "
					+ "AND (CDCF_STATUS = '"+PreRptView.CDCF_GENERATED+"' OR CDCF_STATUS = '"+PreRptView.CDCF_NOT_SEL+"' OR CDCF_STATUS = '"+PreRptView.CDCF_SPEC+"' ) ");
		
			if (!selectedDept.equals("All"))
					sqlBuf.append( " AND S.DEPARTMENT_CODE = '"+selectedDept+"'" );
			sqlBuf.append( " AND ( TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " OR TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN " 
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " OR (TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') < TO_DATE('" + startDate + "', 'DD/MM/YYYY') "
					+ " AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') > TO_DATE('" + endDate + "', 'DD/MM/YYYY')) ) ");
			sqlBuf.append(" ) ORDER BY DEPARTMENT_NAME, STAFF_NAME ,CDCF_STATUS DESC, TITLE  ");
			
			//System.out.println("sqlBuf:"+sqlBuf.toString());
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectSummary vObj = new ProjectSummary();
				vObj.setProjTitle(rs.getString("TITLE"));
				vObj.setProject_type(rs.getString("PROJ_TYPE"));
				vObj.setCDCFStatus(rs.getString("CDCF_STATUS"));
				vObj.setProject_summary(rs.getString("PROJECT_SUMMARY"));
				vObj.setSap_funding_source(rs.getString("SAP_FUNDING_SOURCE"));
				vObj.setFromYear(rs.getString("FROM_YEAR"));
				vObj.setToYear(rs.getString("TO_YEAR"));
				vObj.setCollabT630(rs.getString("COLLAB_T630"));
				vObj.setCollabT690(rs.getString("COLLAB_T690"));
				
				
				List<ProjectDetails_P> pDtlList = getProjectDetails_P(rs.getInt("PROJECT_NO"), dataLevel);
				
				vObj.setpDtlList(pDtlList);
				vObj.setNoPDtl(pDtlList.size());
				vObj.setName_of_collaborative_partner(rs.getString("NAME_OF_COLLABORATIVE_PARTNER"));
				vObj.setCollaborative_partner_country(rs.getString("COLLABORATIVE_PARTNER_COUNTRY"));
				vObj.setCollab_output_type(rs.getString("COLLAB_OUTPUT_TYPE"));
				vObj.setCollab_output_inst(rs.getString("COLLAB_OUTPUT_INST"));
				
				String dept = rs.getString("DEPARTMENT_NAME");
				String name = rs.getString("STAFF_NAME");
				
				if(objMap.get(dept) == null) {
					objMap.put(dept, new LinkedHashMap<String, List<ProjectSummary>>());
				}
				if(objMap.get(dept).get(name) == null) {
					objMap.get(dept).put(name, new ArrayList<ProjectSummary>());
				}
				objMap.get(dept).get(name).add(vObj);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Map<String, List<Double>>> getCHRMProj005CurrReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		Map<String, Map<String, List<Double>>> objMap = new LinkedHashMap<String, Map<String, List<Double>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append( "  WITH rankedProjectList AS ( " + 
					"    SELECT * from ( " + 
					"    select PD.PROJECT_NO,PD.DATA_LEVEL, S.DEPARTMENT_CODE, " + 
					"    ROW_NUMBER() OVER (PARTITION BY PD.PROJECT_NO,PD.DATA_LEVEL ORDER BY PD.line_no) AS rn " + 
					"    from  RICH.RH_P_RESEARCH_PROJECT_DTL PD  " + 
					"    Inner JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO  " + 
					"    AND INVESTIGATOR_TYPE= 'PRINCIPAL INVESTIGATOR' AND NON_IED_STAFF_FLAG  IN('N','F'))  " + 
					"    WHERE rn = 1" + 
					" ) " +
					
		    		" SELECT " +
		    		" PD.DEPARTMENT_CODE, " +
		    		" FUND.DESCRIPTION AS SAP_FUNDING_SOURCE, " +
		    		" FUND.PRINT_ORDER, " +
		    		" SUM(1) AS NUM_PROJECT, " + 
		    		" SUM(PH.SAP_GRANT_AMT) AS SAP_GRANT_AMT ");
		    		sqlBuf.append(" FROM  rankedProjectList PD " 
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) "
		    		+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUND ON FUND.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS IN ('CDCF_GENERATED', 'CDCF_SPEC' ) "
					+ "   AND QH.COLLAB_T630 = 'Y'  "
					+ "     AND ( ( TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') " + 
					"    BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY'))" + 
					"  " + 
					"    OR (TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY')  " + 
					"    BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY')) " + 
					"        " + 
					"    OR (TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') < TO_DATE( '"+startDate+"' , 'DD/MM/YYYY') " + 
					"        AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') > TO_DATE( '"+endDate+"' , 'DD/MM/YYYY')) )" );
			/*if (deptList != null) {
				sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
			}
			*/
			sqlBuf.append("	GROUP BY "
						+ " PD.DEPARTMENT_CODE, "
						+ " FUND.DESCRIPTION, " 
						+ " FUND.PRINT_ORDER " 
						+ " ORDER BY FUND.PRINT_ORDER ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fund = rs.getString("SAP_FUNDING_SOURCE");
				String dept = rs.getString("DEPARTMENT_CODE");
				
				Double numProj = rs.getDouble("NUM_PROJECT");
				Double amt = rs.getDouble("SAP_GRANT_AMT");
				
				if(objMap.get(fund) == null) {
					objMap.put(fund, new LinkedHashMap<String, List<Double>>());
				}
				if(objMap.get(fund).get(dept) == null) {
					objMap.get(fund).put(dept, new ArrayList<Double>());
				}
				objMap.get(fund).get(dept).add(numProj);
				objMap.get(fund).get(dept).add(amt);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Map<String, List<Double>>> getCHRMProj005NewReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		Map<String, Map<String, List<Double>>> objMap = new LinkedHashMap<String, Map<String, List<Double>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append( "  WITH rankedProjectList AS ( " + 
					"    SELECT * from ( " + 
					"    select PD.PROJECT_NO,PD.DATA_LEVEL, S.DEPARTMENT_CODE, " + 
					"    ROW_NUMBER() OVER (PARTITION BY PD.PROJECT_NO,PD.DATA_LEVEL ORDER BY PD.line_no) AS rn " + 
					"    from  RICH.RH_P_RESEARCH_PROJECT_DTL PD  " + 
					"    Inner JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO  " + 
					"    AND INVESTIGATOR_TYPE= 'PRINCIPAL INVESTIGATOR' AND NON_IED_STAFF_FLAG  IN('N','F'))  " + 
					"    WHERE rn = 1" + 
					" ) " +
					
		    		" SELECT " +
		    		" PD.DEPARTMENT_CODE, " +
		    		" FUND.DESCRIPTION AS SAP_FUNDING_SOURCE, " +
		    		" FUND.PRINT_ORDER, " +
		    		" SUM(1) AS NUM_PROJECT, " + 
		    		" SUM(PH.SAP_GRANT_AMT) AS SAP_GRANT_AMT ");
		    		sqlBuf.append(" FROM  rankedProjectList PD " 
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) "
		    		+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUND ON FUND.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS IN ('CDCF_GENERATED' , 'CDCF_SPEC' ) "
					+ " AND QH.COLLAB_T630 = 'Y' "
					+"  AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN " + 
		    		 " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') " );
			/*if (deptList != null) { 
				sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
			}*/
			sqlBuf.append("	GROUP BY "
						+ " PD.DEPARTMENT_CODE, "
						+ " FUND.DESCRIPTION, " 
						+ " FUND.PRINT_ORDER " 
						+ " ORDER BY FUND.PRINT_ORDER ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fund = rs.getString("SAP_FUNDING_SOURCE");
				String dept = rs.getString("DEPARTMENT_CODE");
				
				Double numProj = rs.getDouble("NUM_PROJECT");
				Double amt = rs.getDouble("SAP_GRANT_AMT");
				
				if(objMap.get(fund) == null) {
					objMap.put(fund, new LinkedHashMap<String, List<Double>>());
				}
				if(objMap.get(fund).get(dept) == null) {
					objMap.get(fund).put(dept, new ArrayList<Double>());
				}
				objMap.get(fund).get(dept).add(numProj);
				objMap.get(fund).get(dept).add(amt);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, List<Double>>>> getCHRMProj010ReportList(String dataLevel, String startDate, String endDate, List<String> deptList,String selectedDept){
		Map<String, Map<String, Map<String, List<Double>>>> objMap = new LinkedHashMap<String, Map<String, Map<String, List<Double>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append( "SELECT  STAFF_NAME, " + 
					"        DEPARTMENT_CODE, " + 
					"        SAP_FUNDING_SOURCE, " + 
					"        PRINT_ORDER, " + 
					"        SUM( SAP_GRANT_AMT) AS SAP_GRANT_AMT, " + 
					"        COUNT(DISTINCT PROJECT_NO) AS NUM_PROJECT, " + 
					"        NEW, LINE_NO  " + 
					" FROM (  	SELECT  DISTINCT (S.LAST_NAME || ' ' || S.FIRST_NAME || '_' || S.EMPLOYEE_NUMBER ) AS STAFF_NAME, " + 
					"        	S.DEPARTMENT_CODE," + 
					"        	S.EMPLOYEE_NUMBER FROM RICH.RH_S_ELIGIBLE_STAFF_LIST_V S WHERE 1 = 1 "
					);
					
				    if (! selectedDept.equals("All") )
				    	sqlBuf.append("AND S.DEPARTMENT_CODE = '"+selectedDept+"' ") ;
					
					
			sqlBuf.append(	" ) A LEFT JOIN ( SELECT DISTINCT PD.INVESTIGATOR_STAFF_NO,  " +

		    		" FUND.DESCRIPTION AS SAP_FUNDING_SOURCE, " +
		    		" FUND.PRINT_ORDER, " +
		    		"  PH.PROJECT_NO, " + 
		    		"  PH.SAP_GRANT_AMT, "+
		    		"   CASE WHEN TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || NVL(PH.FROM_YEAR,9999), 'DD/MM/YYYY')  " + 
		    		"     BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY')  THEN 'YES' ELSE NULL END AS NEW,"
		    				+ " CASE WHEN PD.LINE_NO = 1 THEN 1  " + 
		    				"                     ELSE PD.PROJECT_NO END AS LINE_NO  ");
		    		sqlBuf.append(" FROM RICH.RH_P_RESEARCH_PROJECT_DTL PD "
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO  AND PH.DATA_LEVEL = PD.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON QH.PROJECT_NO = PH.PROJECT_NO "
					+ " RIGHT JOIN  RICH.RH_S_ELIGIBLE_STAFF_LIST_V S  ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO  " 
		    		+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUND ON FUND.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE  "
		    		+ " WHERE PD.INVESTIGATOR_TYPE = 'PRINCIPAL INVESTIGATOR' AND PD.DATA_LEVEL = 'C' "
		    		+ " AND  FUND.ENABLED_FLAG = 'Y' AND QH.CDCF_STATUS = 'CDCF_GENERATED' AND QH.COLLAB_T630 = 'Y' "
					+ " AND ( TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " OR TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN " 
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " OR (TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') < TO_DATE('" + startDate + "', 'DD/MM/YYYY') "
					+ " AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') > TO_DATE('" + endDate + "', 'DD/MM/YYYY')) )) B"
					+ "  ON A.EMPLOYEE_NUMBER = B.INVESTIGATOR_STAFF_NO ");

			sqlBuf.append(" GROUP BY STAFF_NAME, DEPARTMENT_CODE,SAP_FUNDING_SOURCE,PRINT_ORDER,NEW,LINE_NO " + 
					   	"   ORDER BY DEPARTMENT_CODE, STAFF_NAME , NEW ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fund = rs.getString("SAP_FUNDING_SOURCE");
				String name = rs.getString("STAFF_NAME") + "_"+rs.getString("LINE_NO");
				String dept = rs.getString("DEPARTMENT_CODE");
				String newProj = rs.getString("NEW");
				//String lineNO = rs.getString("LINE_NO");
				
				Double numProj = rs.getDouble("NUM_PROJECT");
				Double amt = rs.getDouble("SAP_GRANT_AMT");
				if(objMap.get(dept) == null) {
					objMap.put(dept, new LinkedHashMap<String, Map<String, List<Double>>>());
				}
				if(objMap.get(dept).get(name) == null) {
					objMap.get(dept).put(name, new LinkedHashMap<String, List<Double>>());
				}
				
				
				if(objMap.get(dept).get(name).get(fund) == null) {
					objMap.get(dept).get(name).put(fund, new ArrayList<Double>());
				}
				
				// WHEN new Project, would have 4 items in a list
				
				objMap.get(dept).get(name).get(fund).add(numProj);
				objMap.get(dept).get(name).get(fund).add(amt);

				if(newProj != null) {
					objMap.get(dept).get(name).get(fund).add(0.0);
					objMap.get(dept).get(name).get(fund).add(0.0);
				}
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+ objMap );
		return objMap;
	}
	
	public Map<String, List<ProjectSummary>> getCHRMProj013ReportList(String dataLevel, String startDate, String endDate, List<String> deptList , String selectedDept){
		Map<String, List<ProjectSummary>> objMap = new LinkedHashMap<String, List<ProjectSummary>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(
		    		" SELECT DISTINCT PH.PROJECT_NO, " +
		    		" (DCC.LOOKUP_TYPE || ' - ' || DCC.DESCRIPTION) AS DEPARTMENT_NAME, " +
		    		" (CASE WHEN TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN " + 
		    		" TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') " +
		    		" THEN 1 ELSE 0 END) AS NEW_RPT, " +
		    		" TITLE_1 || ' ' || TITLE_2 || ' ' || TITLE_3 || ' ' || TITLE_4 AS TITLE, " + 
		    		" ( PH.SAP_GRANT_AMT )  AS  SAP_GRANT_AMT, " +
		    		" ( PH.RELEASED_VAL  )  AS  RELEASED_VAL, " +
		    		" FROM_YEAR || '/' || FROM_MONTH || '/' || FROM_DAY AS FROM_YEAR, " +
		    		" TO_YEAR || '/' || TO_MONTH || '/' || TO_DAY AS TO_YEAR, " +
		    		" FUND.DESCRIPTION AS SAP_FUNDING_SOURCE, " +
		    		" FUND.PRINT_ORDER, " +
		    		" PH.ACTIVITY_CODE  ");
		    		sqlBuf.append(" FROM ( select PDL.*,  " + 
		    				"		    	ROW_NUMBER() OVER ( PARTITION BY PDL.PROJECT_NO , PDL.DATA_LEVEL ORDER BY LINE_NO) AS REAL_LINE_NO " +  
		    				"		    	from RH_P_RESEARCH_PROJECT_DTL PDL " + 
		    				"		    	RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V VS ON PDL.INVESTIGATOR_STAFF_NO = VS.EMPLOYEE_NUMBER ) PD  "
		    		+ " LEFT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) "
		    		+ " LEFT JOIN RICH.RH_L_DCC_V DCC ON S.DCC = DCC.LOOKUP_CODE "
		    		+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUND ON FUND.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND PD.REAL_LINE_NO = 1 AND QH.COLLAB_T630 = 'Y' "
					+ " AND ( ( TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') " + 
					"    BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY'))" + 
					"  " + 
					"    OR (TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY')  " + 
					"    BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY')) " + 
					"        " + 
					"    OR (TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') < TO_DATE( '"+startDate+"' , 'DD/MM/YYYY') " + 
					"        AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') > TO_DATE( '"+endDate+"' , 'DD/MM/YYYY')) )" );
			
			if (selectedDept.equals("All") ) {
				if (deptList != null) {
	
					sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
				}
			}
			else {
				sqlBuf.append("AND S.DEPARTMENT_CODE = '"+selectedDept+"' ") ;
			}
			sqlBuf.append(" ORDER BY (DCC.LOOKUP_TYPE || ' - ' || DCC.DESCRIPTION), FUND.PRINT_ORDER ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				ProjectSummary vObj = new ProjectSummary();
				vObj.setProjectNo(rs.getInt("PROJECT_NO"));
				vObj.setDccStat(rs.getString("NEW_RPT"));
				vObj.setProjTitle(rs.getString("TITLE"));
				vObj.setSap_funding_source(rs.getString("SAP_FUNDING_SOURCE"));
				vObj.setFromYear(rs.getString("FROM_YEAR"));
				vObj.setToYear(rs.getString("TO_YEAR"));
				List<ProjectDetails_P> pDtlList = getProjectDetails_P(rs.getInt("PROJECT_NO"), dataLevel);
				vObj.setpDtlList(pDtlList);
				vObj.setNoPDtl(pDtlList.size());
				vObj.setSap_grant_amt(rs.getString("SAP_GRANT_AMT"));
				vObj.setReleased_val(rs.getString("RELEASED_VAL"));
				vObj.setActivity_code(rs.getString("ACTIVITY_CODE"));
				
				
				
				String dcc = rs.getString("DEPARTMENT_NAME");
				
				if(objMap.get(dcc) == null) {
					objMap.put(dcc, new ArrayList<ProjectSummary>());
				}
				objMap.get(dcc).add(vObj);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objMap;
	}
	
	public Map<String, List<String>> getCHRMProj014ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		Map<String, List<String>> objMap = new LinkedHashMap<String, List<String>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			
			String periodId = " TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN " + 
		    		" TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'DD/MM/YYYY') ";

			sqlBuf.append(
		    		" SELECT S.DCC || '_' || FUND.FUND_CODE || '_' || FUND.FUND_PROJ_TYPE || '_' || PH.ROLE_INST || '_' || " +
		    		" (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END ) || '_' || " +
		    		" (CASE WHEN TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN " +
		    		" TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND TO_DATE('" + endDate + "', 'DD/MM/YYYY') " +
		    		" THEN 1 ELSE 2 END) AS KEYARR, " +
		    		" LISTAGG (PH.PROJECT_NO, ',') WITHIN GROUP (ORDER BY PH.PROJECT_NO) AS PROJECT_NO, " +
		    		" SUM(CASE WHEN " + periodId +
		    		" THEN ( S.DCC_PERCENTAGE/100 ) ELSE 0 END) AS NEW_RPT_NO, " +
		    		" SUM(CASE WHEN " + periodId + 
		    		" THEN PH.RELEASED_VAL * (S.DCC_PERCENTAGE/100) ELSE 0 END) AS NEW_RPT_VAL, " +
		    		" SUM(CASE WHEN " + periodId + 
		    		" THEN PH.SAP_GRANT_AMT * (S.DCC_PERCENTAGE/100) ELSE 0 END) AS NEW_RPT_REL, " +
		    		" SUM(S.DCC_PERCENTAGE/100) AS CURR_RPT_NO, " +
		    		" SUM(PH.RELEASED_VAL * (S.DCC_PERCENTAGE/100)) AS CURR_RPT_VAL, " +
		    		" SUM(PH.SAP_GRANT_AMT * (S.DCC_PERCENTAGE/100)) AS CURR_RPT_REL ");
		    		sqlBuf.append(" FROM  ( select PDL.* , " + 
		    				"    ROW_NUMBER() OVER ( PARTITION BY PDL.PROJECT_NO,PDL.DATA_LEVEL ORDER BY LINE_NO) AS REAL_LINE_NO" + 
		    				"    from RH_P_RESEARCH_PROJECT_DTL PDL" + 
		    				"    RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V VS ON PDL.INVESTIGATOR_STAFF_NO = VS.EMPLOYEE_NUMBER  AND PDL.LINE_NO =1 ) PD  "
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) "
		    		+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
		    		+ " LEFT JOIN RICH.RH_L_DCC_V DCC ON S.DCC = DCC.LOOKUP_CODE "
		    		+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUND ON FUND.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE " );
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS = 'CDCF_GENERATED'  AND  PD.REAL_LINE_NO = 1 "
					+ " AND QH.COLLAB_T630 = 'Y' AND FUND.ATTRIBUTE3 != 'N' AND FUND.ATTRIBUTE4 != 'N' "
					+ "     AND ( ( TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') " + 
					"    BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY'))" + 
					"  " + 
					"    OR (TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY')  " + 
					"    BETWEEN  TO_DATE('"+startDate+"', 'DD/MM/YYYY') AND  TO_DATE('"+endDate+"', 'DD/MM/YYYY')) " + 
					"        " + 
					"    OR (TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') < TO_DATE( '"+startDate+"' , 'DD/MM/YYYY') " + 
					"        AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') > TO_DATE( '"+endDate+"' , 'DD/MM/YYYY')) )"
					);
			
			/*
			if (deptList != null) {
				sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
			}*/
			
			sqlBuf.append(" GROUP BY S.DCC, FUND.FUND_CODE, FUND.FUND_PROJ_TYPE, PH.ROLE_INST, (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END ), "
					+ " (CASE WHEN TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " THEN 1 ELSE 2 END) "
					+ " ORDER BY S.DCC, FUND.FUND_CODE, FUND.FUND_PROJ_TYPE, PH.ROLE_INST, (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END ), "
					+ " (CASE WHEN TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					+ " THEN 1 ELSE 2 END) ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String keyArr = rs.getString("KEYARR");
				
				String projNo = rs.getString("PROJECT_NO");
				String newRptNo = rs.getString("NEW_RPT_NO");
				String newRptVal = rs.getString("NEW_RPT_VAL") == null ? "0" : rs.getString("NEW_RPT_VAL");
				String newRptRel = rs.getString("NEW_RPT_REL") == null ? "0" : rs.getString("NEW_RPT_REL");
				String currRptNo = rs.getString("CURR_RPT_NO");
				String currRptVal = rs.getString("CURR_RPT_VAL") == null ? "0" : rs.getString("CURR_RPT_VAL");
				String currRptRel = rs.getString("CURR_RPT_REL") == null ? "0" : rs.getString("CURR_RPT_REL");
				
				if(objMap.get(keyArr) == null) {
					objMap.put(keyArr, new ArrayList<String>());
				}
				
				
				
				String [] projNoArr = projNo.split(",");	
				Set<String> projNoSet = new HashSet<>(Arrays.asList(projNoArr));
				objMap.get(keyArr).add(String.join(",", projNoSet));
				
				objMap.get(keyArr).add(newRptNo);
				objMap.get(keyArr).add(newRptVal);
				objMap.get(keyArr).add(newRptRel);
				objMap.get(keyArr).add(currRptNo);
				objMap.get(keyArr).add(currRptVal);
				objMap.get(keyArr).add(currRptRel);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, List<String>> getCHRMProj016ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		Map<String, List<String>> objMap = new LinkedHashMap<String, List<String>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(
		    		" SELECT DISTINCT PH.PROJECT_NO," +
		    		" TO_CHAR(CENSUS_DATE, 'YYYYMMDD') AS CENSUS_DATE, " +
		    		" TITLE_1 || ' ' || TITLE_2 || ' ' || TITLE_3 || ' ' || TITLE_4 AS TITLE, " + 
		    		" COLLABORATIVE_PARTNER_COUNTRY, " +
		    		" NAME_OF_COLLABORATIVE_PARTNER, " +
		    		" (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END ) AS FROM_YEAR, " +
		    		" (CASE WHEN TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') BETWEEN " +
		    		" TO_DATE('" + startDate + "', 'DD/MM/YYYY') AND TO_DATE('" + endDate + "', 'DD/MM/YYYY') " +
		    		" THEN 1 ELSE 2 END) AS TO_YEAR, " +
		    		" CASE WHEN PH.ROLE_INST_FOR_T690 = 'C' THEN 'Coordinating Institution' WHEN ROLE_INST_FOR_T690 = 'P' THEN 'Participating Institution' ELSE '' END AS ROLE_INST_FOR_T690, " +
		    		" RELEASED_VAL, " +
		    		" SAP_GRANT_AMT ");
		    		sqlBuf.append(" FROM RICH.RH_P_RESEARCH_PROJECT_DTL PD "
		    		+ " LEFT JOIN RICH.RH_P_RESEARCH_PROJECT_HDR PH ON PD.PROJECT_NO = PH.PROJECT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
		    		+ " LEFT JOIN RICH.RH_Q_RESEARCH_PROJECT_HDR QH ON (QH.PROJECT_NO = PH.PROJECT_NO) "
		    		//+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
		    		//+ " LEFT JOIN RICH.RH_L_DCC_V DCC ON S.DCC = DCC.LOOKUP_CODE "
		    		+ " LEFT JOIN RICH.RH_L_FUND_SOURCE_V FUND ON FUND.LOOKUP_CODE = PH.SAP_FUNDING_SOURCE ");
		    		//+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS IN ( 'CDCF_GENERATED', 'CDCF_SPEC')  "
					+ " AND PD.LINE_NO = 1 AND QH.COLLAB_T690 = 'Y' "
					+ " AND TO_DATE(NVL(PH.TO_DAY, '01') || '/' || NVL(PH.TO_MONTH, '01') || '/' || PH.TO_YEAR, 'DD/MM/YYYY') >= "
					+ " TO_DATE('" + startDate + "', 'DD/MM/YYYY') "
					+ " AND TO_DATE(NVL(PH.FROM_DAY, '01') || '/' || NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') <= "
					+ " TO_DATE('" + endDate + "', 'DD/MM/YYYY') "
					);
			/*if (deptList != null) {
				sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +
						deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}*/
			sqlBuf.append(" ORDER BY CENSUS_DATE, PROJECT_NO ");
		//	System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			String oldCensus = "";
			Integer serialNo = 1;
			while (rs.next())
			{
				
				String projNo = rs.getString("PROJECT_NO");
				String hei = "EdUHK";
				String censusDate = rs.getString("CENSUS_DATE") == null ? "" : rs.getString("CENSUS_DATE");;
				if(oldCensus.equals(censusDate)) serialNo++;
				else {
					oldCensus = censusDate;
				}
				String serialStr = "";
				if(serialNo >= 100) serialStr = serialNo.toString();
				else if (serialNo >= 10) serialStr = "0" + serialNo.toString();
				else serialStr = "00" + serialNo.toString();
				String serial = (censusDate.equals("") ? "" : censusDate.substring(0, 4)) + "_" + serialStr;
				String title = rs.getString("TITLE");
				String country = rs.getString("COLLABORATIVE_PARTNER_COUNTRY");
				String inst = rs.getString("NAME_OF_COLLABORATIVE_PARTNER");
				String fromYear = rs.getString("FROM_YEAR");
				String toYear = rs.getString("TO_YEAR");
				String roleInst = rs.getString("ROLE_INST_FOR_T690");
				String rel = rs.getString("RELEASED_VAL") == null ? "NULL" : rs.getString("RELEASED_VAL");
				String val = rs.getString("SAP_GRANT_AMT") == null ? "NULL" : rs.getString("SAP_GRANT_AMT");
				String remarks = "";
				
				if(objMap.get(projNo) == null) {
					objMap.put(projNo, new ArrayList<String>());
				}
				objMap.get(projNo).add(projNo);
				objMap.get(projNo).add(hei);
				objMap.get(projNo).add(censusDate);
				objMap.get(projNo).add(serial);
				objMap.get(projNo).add(title);
				objMap.get(projNo).add(country);
				objMap.get(projNo).add(inst);
				objMap.get(projNo).add(fromYear);
				objMap.get(projNo).add(toYear);
				objMap.get(projNo).add(roleInst);
				objMap.get(projNo).add(rel);
				objMap.get(projNo).add(val);
				objMap.get(projNo).add(remarks);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public String getEligibleFullNme(Integer projNo, Integer lineNo, String dataLevel) {
		String rtnString = "";
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(
		    		" SELECT DISTINCT (CASE WHEN S.EMPLOYEE_NUMBER IS NOT NULL THEN " +
		    		" ( NVL2(S.HUSBAND_LAST_NAME,S.HUSBAND_LAST_NAME || ' ','') || S.LAST_NAME || ' ' || S.FIRST_NAME || "
		    		+ " (CASE WHEN S.CHINESE_NAME IS NOT NULL THEN (' ' || S.CHINESE_NAME) ELSE '' END) " + 
		    		" || ' [' || S.DEPARTMENT_CODE || ']') "
		    		+ " WHEN STAFF.STAFF_NUMBER IS NOT NULL THEN (STAFF.SURNAME || ' ' || "
		    		+ " STAFF.OTHERNAME || (CASE WHEN STAFF.CHINESENAME IS NOT NULL THEN (' ' || STAFF.CHINESENAME) ELSE '' END)) "
		    		+ " || (CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN (' [' || STAFF.DEPT_CODE || ']') ELSE '' END) "
		    		+ " WHEN EXSTAFF.STAFF_NUMBER IS NOT NULL THEN ( EXSTAFF.SURNAME || ' ' || "
		    		+ " EXSTAFF.OTHERNAME || (CASE WHEN EXSTAFF.CHINESENAME IS NOT NULL THEN (' ' || EXSTAFF.CHINESENAME) ELSE '' END)) "
		    		+ " || (CASE WHEN EXSTAFF.DEPARTMENT_CODE IS NOT NULL THEN (' [' || EXSTAFF.DEPARTMENT_CODE || ']') ELSE '' END) "
		    		+ " ELSE PD.INVESTIGATOR_NAME END) AS STAFF_NAME ");
		    		sqlBuf.append(" FROM RICH.RH_P_RESEARCH_PROJECT_DTL PD "
		    		+ " LEFT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.INVESTIGATOR_STAFF_NO "
    				+ " LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO " 
		    		+ " LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVESTIGATOR_STAFF_NO "
		    		+ " WHERE PD.PROJECT_NO = "+projNo+""+" AND PD.LINE_NO = "+lineNo+""+" AND PD.DATA_LEVEL = '"+dataLevel+"'");

			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				rtnString = rs.getString("STAFF_NAME");
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return rtnString;
	}
	
	public Map<String, String> getInvestigatorTypeList(){
		Map<String, String> objMap = new LinkedHashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT LOOKUP_CODE, DESCRIPTION FROM RH_L_PROJECT_CAPACITY_V "
					+ " ORDER BY PRINT_ORDER ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("LOOKUP_CODE"), rs.getString("DESCRIPTION"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, String> getFundTypeList(){
		Map<String, String> objMap = new LinkedHashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT LOOKUP_CODE, DESCRIPTION FROM RH_L_FUND_SOURCE_V "
					+ " WHERE ENABLED_FLAG = 'Y' ORDER BY PRINT_ORDER ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("LOOKUP_CODE"), rs.getString("DESCRIPTION"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public void projectSnapshot(CdcfRptPeriod period, String userId) throws Exception
	{
		if (period != null)
		{
			Connection conn = null;
			CallableStatement cStmt = null;
			try{
				DateFormat df = new SimpleDateFormat("dd/MM/yyyy", Locale.ENGLISH);
				conn = pm.getConnection();
				cStmt = conn.prepareCall("{CALL RH_PROJECT_SNAPSHOT_P (?,?,?,?)}");
				cStmt.setInt(1, period.getPeriod_id());
				cStmt.setString(2, df.format(period.getDate_from()));
				cStmt.setString(3, df.format(period.getDate_to()));
				cStmt.setString(4, userId);
				cStmt.execute();
				
			}
			finally{
				PersistenceManager.close(cStmt);
				PersistenceManager.close(conn);
			}
		}
	}
	
}
