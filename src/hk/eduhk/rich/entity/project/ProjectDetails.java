package hk.eduhk.rich.entity.project;


import java.util.logging.Logger;



public class ProjectDetails{
	
	public static Logger logger = Logger.getLogger(ProjectDetails.class.toString());
	
	private int project_no;	
	
	private String person_source_id;	
	
	private String staff_number;
	
	private String last_name;
	
	private String first_name;
	
	private String investigator_type;
	
	private String external_ind;

	
	public int getProject_no()
	{
		return project_no;
	}

	
	public void setProject_no(int project_no)
	{
		this.project_no = project_no;
	}

	
	public String getPerson_source_id()
	{
		return person_source_id;
	}

	
	public void setPerson_source_id(String person_source_id)
	{
		this.person_source_id = person_source_id;
	}

	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public String getLast_name()
	{
		return last_name;
	}

	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}

	
	public String getFirst_name()
	{
		return first_name;
	}

	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}

	
	public String getInvestigator_type()
	{
		return investigator_type;
	}

	
	public void setInvestigator_type(String investigator_type)
	{
		this.investigator_type = investigator_type;
	}

	
	public String getExternal_ind()
	{
		return external_ind;
	}

	
	public void setExternal_ind(String external_ind)
	{
		this.external_ind = external_ind;
	}



	@Override
	public String toString()
	{
		return "ProjectDetails [project_no=" + project_no + ", person_source_id=" + person_source_id + ", staff_number="
				+ staff_number + ", last_name=" + last_name + ", first_name=" + first_name + ", investigator_type="
				+ investigator_type + ", external_ind=" + external_ind + "]";
	}


	
	
}
