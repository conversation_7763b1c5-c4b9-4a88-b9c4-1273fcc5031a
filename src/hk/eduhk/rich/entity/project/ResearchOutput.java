package hk.eduhk.rich.entity.project;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.entity.BaseRI;

@SuppressWarnings("serial")
public class ResearchOutput
{
	private String outputTitle;
	private String outputType;
	private String outputInst;
	private Date outputDate;

	public ResearchOutput()
	{
	}

	
	public String getOutputTitle()
	{
		return outputTitle;
	}

	
	public void setOutputTitle(String outputTitle)
	{
		this.outputTitle = outputTitle;
	}

	
	public String getOutputType()
	{
		return outputType;
	}

	
	public void setOutputType(String outputType)
	{
		this.outputType = outputType;
	}

	
	public String getOutputInst()
	{
		return outputInst;
	}

	
	public void setOutputInst(String outputInst)
	{
		this.outputInst = outputInst;
	}

	


	
	public Date getOutputDate()
	{
		return outputDate;
	}


	
	public void setOutputDate(Date outputDate)
	{
		this.outputDate = outputDate;
	}


	@Override
	public String toString()
	{
		return "ResearchOutput [outputTitle=" + outputTitle + ", outputType=" + outputType + ", outputInst=" + outputInst
				+ ", outputDate=" + outputDate + "]";
	}



}