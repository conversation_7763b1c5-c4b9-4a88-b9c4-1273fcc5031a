package hk.eduhk.rich.entity.project;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.*;

import hk.eduhk.rich.entity.LookupValueDAO;


@Entity
@Table(name = "RH_PURE_PROJECT_HDR_V")
public class ProjectHeader
{
	public static Logger logger = Logger.getLogger(ProjectHeader.class.toString());
	
	@Id
	@Column(name = "project_no")
	private int project_no;	
	
	@Column(name = "project_type")
	private String project_type;

	@Column(name = "title")
	private String title;

	@Column(name = "project_summary")
	private String project_summary;

	@Column(name = "project_summary_2")
	private String project_summary_2;
	
	@Column(name = "start_date")
	private String start_date;		
	
	@Column(name = "end_date")
	private String end_date;		
	
	@Column(name = "keywords")
	private String keywords;
	
	@Column(name = "funding_source")
	private String funding_source;
	
	@Column(name = "sdg_code")
	private String sdg_code;
	
	
	@Transient
	private List<String> sdg_list;

	
	public int getProject_no()
	{
		return project_no;
	}

	
	public void setProject_no(int project_no)
	{
		this.project_no = project_no;
	}


	
	public String getSdg_code()
	{
		return sdg_code;
	}


	
	public void setSdg_code(String sdg_code)
	{
		this.sdg_code = sdg_code;
	}


	public List<String> getSdg_list()
	{
		sdg_list = new ArrayList<String>();
		if (sdg_code != null && sdg_list.size() == 0) {
			List <String> sdg_code_list =  Stream.of(getSdg_code().split(",")).collect(Collectors.toList());

			for(String i_sdg_index : sdg_code_list) {
				sdg_list.add(LookupValueDAO.getInstance().getLookupValue("SDG",i_sdg_index,"US").getShort_desc()) ;
			}
			
			
		}
		
		return sdg_list;
	}


	
	public void setSdg_list(List<String> sdg_list)
	{
		this.sdg_list = sdg_list;
	}



	
	
	public String getProject_type()
	{
		return project_type;
	}


	
	public void setProject_type(String project_type)
	{
		this.project_type = project_type;
	}


	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getProject_summary()
	{
		return project_summary;
	}

	
	public void setProject_summary(String project_summary)
	{
		this.project_summary = project_summary;
	}

	
	public String getproject_summary_2()
	{
		return project_summary_2;
	}

	
	public void setproject_summary_2(String project_summary_2)
	{
		this.project_summary_2 = project_summary_2;
	}

	
	public String getStart_date()
	{
		return start_date;
	}

	
	public void setStart_date(String start_date)
	{
		this.start_date = start_date;
	}

	
	public String getEnd_date()
	{
		return end_date;
	}

	
	public void setEnd_date(String end_date)
	{
		this.end_date = end_date;
	}

	
	public String getKeywords()
	{
		return keywords;
	}

	
	public void setKeywords(String keywords)
	{
		this.keywords = keywords;
	}

	
	public String getFunding_source()
	{
		return funding_source;
	}

	
	public void setFunding_source(String funding_source)
	{
		this.funding_source = funding_source;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + project_no;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProjectHeader other = (ProjectHeader) obj;
		if (project_no != other.project_no)
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "ProjectHeader [project_no=" + project_no + ", project_type=" + project_type + ", title=" + title
				+ ", project_summary=" + project_summary + ", project_summary_2=" + project_summary_2 + ", start_date="
				+ start_date + ", end_date=" + end_date + ", keywords=" + keywords + ", funding_source="
				+ funding_source + ", sdg_code=" + sdg_code + ", sdg_list=" + sdg_list + "]";
	}
}
