package hk.eduhk.rich.entity.project;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class ProjectHeader_P_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="project_no")
	private Integer project_no;
	
	@Column(name="data_level")
	private String data_level;	

	
	
	public Integer getProject_no()
	{
		return project_no;
	}


	
	public void setProject_no(Integer project_no)
	{
		this.project_no = project_no;
	}


	public String getData_level()
	{
		return data_level;
	}

	
	public void setData_level(String data_level)
	{
		this.data_level = data_level;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((data_level == null) ? 0 : data_level.hashCode());
		result = prime * result + ((project_no == null) ? 0 : project_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProjectHeader_P_PK other = (ProjectHeader_P_PK) obj;
		if (data_level == null)
		{
			if (other.data_level != null)
				return false;
		}
		else if (!data_level.equals(other.data_level))
			return false;
		if (project_no == null)
		{
			if (other.project_no != null)
				return false;
		}
		else if (!project_no.equals(other.project_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ResearchHeader_P_PK [project_no=" + project_no + ", data_level=" + data_level + "]";
	}


}
