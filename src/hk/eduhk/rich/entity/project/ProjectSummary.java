package hk.eduhk.rich.entity.project;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.entity.BaseRI;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PublicationDAO;

@SuppressWarnings("serial")
public class ProjectSummary extends BaseRI
{
	private String projTitle;
	private String projSummary;
	private String fromYear;
	private String keyword;
	private int riNo;
	
	// for RH-ADM-006 Research Information Listing
	private int projectNo;
	private String staffName;
	private String department;
	private String prin_inves_author_list;
	private String co_prin_inves_author_list;
	private String co_inves_author_list;
	private String other_author_list;
	private String ied_work_ind;
	private String project_type;
	private String activity_code;
	private String tech_proj;
	private String rgc_proj_num;
	private String project_summary;
	private String project_summary_2;
	private String fromMonth;
	private String fromDay;
	private String fromdate;
	private Date fromdateDate;
	private String toYear;
	private String toMonth;
	private String toDay;
	private String todate;
	private Date todateDate;
	private String durationMonth;
	private String durationYear;
	private String collab_partner_ugc;
	private String collab_partner_non_ins;
	private String concatenated_inv_name;
	private String name_of_collaborative_partner;
	private String collaborative_partner_city;
	private String collaborative_partner_country;
	private String collab_output_title;
	private String collab_output_type;
	private String collab_output_inst;
	private String collab_output_date;
	private String funding_body;
	private String sap_funding_source;
	private String sap_grant_amt;
	private String funding_org;
	private String funding_others;
	private String released_val;
	private String schCode;
	private String schDtlCode;
	private String daCode;
	private String daDtlCode;
	private String otherDaDtl;
	
	private String funded_proj;
	
	private String cdcf_selected_ind;
	
	private String collaborativePartnerStr;
	private String collaborativePartnerCountryStr;
	private String collaborativeOutputStr;
	
	// RH-RPT-002
	private String dccStat;
	private String censusDate;
	private String title1;
	private String title2;
	private String title3;
	private String title4;
	private String keyResearchAreas;
	private String otherKeyResearchAreas;
	private String rsCode;
	private String rsDtlCode;
	private String totalNoOfInvestigator;
	private List<ProjectDetails_P> pDtlList;
	private int noPDtl;
	private String roleInstT630;
	private String roleInstT690;
	
	private String sdg_str;
	private String sdg_code;
	
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	
	public ProjectSummary()
	{
	}
	
	public String generateConcatProjectSummary() {
		
		if(!StringUtils.isEmpty(getProject_summary())) {
			if(!StringUtils.isEmpty(getProject_summary_2()))
				return getProject_summary() + " " + getProject_summary_2();
			else
				return getProject_summary();
		}
		return "";
	}
	
	public String getCollaborativePartnerStr() {
		if(collaborativePartnerStr == null) {
			if(!StringUtils.isEmpty(getName_of_collaborative_partner())) {
				collaborativePartnerStr = "";
				ProjectDAO dao = ProjectDAO.getCacheInstance();
				List<PartnerCountry> countryList = dao.getPartnerCountryList(1);
				Map<String, String> countryMap = new HashMap<String, String>();
				for (PartnerCountry country : countryList) {
					countryMap.put(country.getPk().getLookup_code(), country.getDescription());
				}
				List<String> collabPartnerNameList = Arrays.asList(getName_of_collaborative_partner().split("_",-1));
				List<String> collabPartnerCountryList = null;
				if(getCollaborative_partner_country() != null)
					collabPartnerCountryList = Arrays.asList(getCollaborative_partner_country().split("_",-1));
				else
					collabPartnerCountryList = Arrays.asList(new String[collabPartnerNameList.size()]);
				List<String> collabPartnerCityList = null;
				if(getCollaborative_partner_city() != null)
					collabPartnerCityList = Arrays.asList(getCollaborative_partner_city().split("_",-1));
				else
					collabPartnerCityList = Arrays.asList(new String[collabPartnerNameList.size()]);
				int listLength = collabPartnerNameList.size();
				for(int i = 0 ; i < listLength ; ++i) {
					collaborativePartnerStr += collabPartnerNameList.get(i) + ", ";
					collaborativePartnerStr += collabPartnerCityList.get(i) + ", ";
					String countryString = countryMap.get(collabPartnerCountryList.get(i));
					if(countryString != null) collaborativePartnerStr += countryString;
					else collaborativePartnerStr += collabPartnerCountryList.get(i);
					if(i != listLength - 1)
						collaborativePartnerStr += "<br/>";
				}
			}
		}
		return collaborativePartnerStr;
	}
	
	public String getCollaborativePartnerCountryStr(Boolean doc) {
		if(collaborativePartnerCountryStr == null) {
			if(!StringUtils.isEmpty(getName_of_collaborative_partner())) {
				collaborativePartnerCountryStr = "";
				ProjectDAO dao = ProjectDAO.getCacheInstance();
				List<PartnerCountry> countryList = dao.getPartnerCountryList(1);
				Map<String, String> countryMap = new HashMap<String, String>();
				for (PartnerCountry country : countryList) {
					countryMap.put(country.getPk().getLookup_code(), country.getDescription());
				}
				
				
				
				List<String> collabPartnerCountryList = null;
				if(getCollaborative_partner_country() != null)
					collabPartnerCountryList = Arrays.asList(getCollaborative_partner_country().split("_",-1));
				
				if (doc)
					for(String country : collabPartnerCountryList) 
						collaborativePartnerCountryStr += country + "_" ;
					
				else {
					for(String country : collabPartnerCountryList) {
						
						//input country code instead of full getDescription
						String countryString = countryMap.get(country);
						if(countryString != null) collaborativePartnerCountryStr += countryString +"_";
						else collaborativePartnerCountryStr += country + "_" ;
				}
					
					
					//collaborativePartnerCountryStr += country + "_" ;
				}
				if(StringUtils.isNotBlank(collaborativePartnerCountryStr))
					collaborativePartnerCountryStr = collaborativePartnerCountryStr.substring(0, collaborativePartnerCountryStr.length()-1);
			}
		}
		return collaborativePartnerCountryStr;
	}
	
	public void setCollaborativePartnerStr(String collaborativePartnerStr)
	{
		this.collaborativePartnerStr = collaborativePartnerStr;
	}

	public String getCollaborativeOutputStr() {
		if(collaborativeOutputStr == null) {
			if(!StringUtils.isEmpty(getCollab_output_title())) {
				collaborativeOutputStr = "";
				PublicationDAO dao = PublicationDAO.getCacheInstance();
				List<OutputType> outputTypeList = dao.getOutputTypeList(2);
				Map<String, String> outputTypeMap = new HashMap<String, String>();
				for (OutputType outputType : outputTypeList) {
					outputTypeMap.put(outputType.getPk().getLookup_code(), outputType.getDescription());
				}
				List<String> collabOutputTitleList = Arrays.asList(getCollab_output_title().split("_",-1));
				List<String> collabOutputDateList = Arrays.asList(getCollab_output_date().split("_",-1));
				List<String> collabOutputTypeList = null;
				if(getCollab_output_type() != null)
					collabOutputTypeList = Arrays.asList(getCollab_output_type().split("_",-1));
				else
					collabOutputTypeList = Arrays.asList(new String[collabOutputDateList.size()]);
				List<String> collabOutputInstList = null;
				if(getCollab_output_inst() != null)
					collabOutputInstList = Arrays.asList(getCollab_output_inst().split("_",-1));
				else
					collabOutputInstList = Arrays.asList(new String[collabOutputDateList.size()]);
				int listLength = collabOutputDateList.size();
				int titleLength = collabOutputTitleList.size();
				if(listLength == titleLength)
					for(int i = 0 ; i < listLength ; ++i) {
						collaborativeOutputStr += collabOutputTitleList.get(i) + " - ";
						String outputTypeString = outputTypeMap.get(collabOutputTypeList.get(i));
						if(outputTypeString != null) collaborativeOutputStr += outputTypeString + " ";
						else collaborativeOutputStr += collabOutputTypeList.get(i) + " ";
						collaborativeOutputStr += collabOutputInstList.get(i) + " ";
						collaborativeOutputStr += collabOutputDateList.get(i);
						if(i != listLength - 1)
							collaborativeOutputStr += "<br/>";
					}
				else collaborativeOutputStr = getCollab_output_title() 
						+ getCollab_output_type() + getCollab_output_inst() +
						getCollab_output_date();
			}
		}
		return collaborativeOutputStr;
	}
	
	
	
	
	public String getSdg_str()
	{
		return sdg_str;
	}

	
	public void setSdg_str(String sdg_str)
	{
		this.sdg_str = sdg_str;
	}

	public void setCollaborativeOutputStr(String collaborativeOutputStr)
	{
		this.collaborativeOutputStr = collaborativeOutputStr;
	}

	public void setProjTitle(String projTitle)
	{
		this.projTitle = projTitle;
	}


	public String getProjTitle()
	{
		return projTitle;
	}


	public void setProjSummary(String projSummary)
	{
		this.projSummary = projSummary;
	}


	public String getProjSummary()
	{
		return projSummary;
	}


	public void setFromYear(String fromYear)
	{
		this.fromYear = fromYear;
	}


	public String getFromYear()
	{
		return fromYear;
	}


	public void setKeyword(String keyword)
	{
		this.keyword = keyword;
	}


	public String getKeyword()
	{
		return keyword;
	}

	public int getRiNo()
	{
		return getRINo();
	}

	public void setRiNo(int riNo)
	{
		this.riNo = riNo;
	}
	
	public int getProjectNo()
	{
		return getRINo();
	}


	public void setProjectNo(int projectNo)
	{
		this.projectNo = projectNo;
		setRINo(projectNo);
	}
	
	
	public String getStaffName()
	{
		return staffName;
	}

	
	public void setStaffName(String staffName)
	{
		this.staffName = staffName;
	}

	public String getDepartment()
	{
		return department;
	}

	
	public void setDepartment(String department)
	{
		this.department = department;
	}

	
	public String getPrin_inves_author_list()
	{
		return prin_inves_author_list;
	}

	
	public void setPrin_inves_author_list(String prin_inves_author_list)
	{
		this.prin_inves_author_list = prin_inves_author_list;
	}

	
	public String getCo_prin_inves_author_list()
	{
		return co_prin_inves_author_list;
	}

	
	public void setCo_prin_inves_author_list(String co_prin_inves_author_list)
	{
		this.co_prin_inves_author_list = co_prin_inves_author_list;
	}

	
	public String getCo_inves_author_list()
	{
		return co_inves_author_list;
	}

	
	public void setCo_inves_author_list(String co_inves_author_list)
	{
		this.co_inves_author_list = co_inves_author_list;
	}

	
	public String getOther_author_list()
	{
		return other_author_list;
	}

	
	public void setOther_author_list(String other_author_list)
	{
		this.other_author_list = other_author_list;
	}

	
	public String getIed_work_ind()
	{
		return ied_work_ind;
	}

	
	public void setIed_work_ind(String ied_work_ind)
	{
		this.ied_work_ind = ied_work_ind;
	}

	
	public String getProject_type()
	{
		return project_type;
	}

	
	public void setProject_type(String project_type)
	{
		this.project_type = project_type;
	}

	
	public String getActivity_code()
	{
		return activity_code;
	}

	
	public void setActivity_code(String activity_code)
	{
		this.activity_code = activity_code;
	}

	
	
	
	public String getTech_proj()
	{
		return tech_proj;
	}

	
	public void setTech_proj(String tech_proj)
	{
		this.tech_proj = tech_proj;
	}

	public String getRgc_proj_num()
	{
		return rgc_proj_num;
	}

	
	public void setRgc_proj_num(String rgc_proj_num)
	{
		this.rgc_proj_num = rgc_proj_num;
	}

	public String getProject_summary()
	{
		return project_summary;
	}

	
	public void setProject_summary(String project_summary)
	{
		this.project_summary = project_summary;
	}

	
	public String getProject_summary_2()
	{
		return project_summary_2;
	}

	
	public void setProject_summary_2(String project_summary_2)
	{
		this.project_summary_2 = project_summary_2;
	}

	
	public String getFromdate()
	{
		return fromdate;
	}

	
	public void setFromdate(String fromdate)
	{
		this.fromdate = fromdate;
	}

	
	public Date getFromdateDate() {
		if (fromdateDate == null) {
			String fromDate = "";
			fromDate += fromDay != null ? fromDay : "1";
			fromDate += "/" + fromMonth;
			fromDate += "/" + fromYear;
			try {
				fromdateDate = new SimpleDateFormat("dd/MM/yyyy").parse(fromDate);  
			}
			catch (Exception e){
				logger.log(Level.WARNING, "Cannot getFromdateDate ", e);
			}
		}
		return fromdateDate;
	}
	
	public String getTodate()
	{
		return todate;
	}

	
	public void setTodate(String todate)
	{
		this.todate = todate;
	}
	
	public Date getTodateDate() {
		if (todateDate == null) {
			String toDate = "";
			toDate += toDay != null ? toDay : "1";
			toDate += "/" + toMonth;
			toDate += "/" + toYear;
			try {
				todateDate = new SimpleDateFormat("dd/MM/yyyy").parse(toDate);  
			}
			catch (Exception e){
				logger.log(Level.WARNING, "Cannot getTodateDate ", e);
			}
		}
		return fromdateDate;
	}

	
	public String getCollab_partner_ugc()
	{
		return collab_partner_ugc;
	}

	
	public void setCollab_partner_ugc(String collab_partner_ugc)
	{
		this.collab_partner_ugc = collab_partner_ugc;
	}

	
	public String getCollab_partner_non_ins()
	{
		return collab_partner_non_ins;
	}

	
	public void setCollab_partner_non_ins(String collab_partner_non_ins)
	{
		this.collab_partner_non_ins = collab_partner_non_ins;
	}

	
	public String getConcatenated_inv_name()
	{
		return concatenated_inv_name;
	}

	
	public void setConcatenated_inv_name(String concatenated_inv_name)
	{
		this.concatenated_inv_name = concatenated_inv_name;
	}

	
	public String getName_of_collaborative_partner()
	{
		return name_of_collaborative_partner;
	}

	
	public void setName_of_collaborative_partner(String name_of_collaborative_partner)
	{
		this.name_of_collaborative_partner = name_of_collaborative_partner;
	}

	
	public String getCollaborative_partner_city()
	{
		return collaborative_partner_city;
	}

	
	public void setCollaborative_partner_city(String collaborative_partner_city)
	{
		this.collaborative_partner_city = collaborative_partner_city;
	}

	
	public String getCollaborative_partner_country()
	{
		return collaborative_partner_country;
	}

	
	public void setCollaborative_partner_country(String collaborative_partner_country)
	{
		this.collaborative_partner_country = collaborative_partner_country;
	}

	
	public String getCollab_output_title()
	{
		return collab_output_title;
	}

	
	public void setCollab_output_title(String collab_output_title)
	{
		this.collab_output_title = collab_output_title;
	}

	
	public String getCollab_output_type()
	{
		return collab_output_type;
	}
	
	public String getCollab_output_typeStr() {
		PublicationDAO dao = PublicationDAO.getCacheInstance();
		List<OutputType> outputTypeList = dao.getOutputTypeList(2);
		Map<String, String> outputTypeMap = new HashMap<String, String>();
		for (OutputType outputType : outputTypeList) {
			outputTypeMap.put(outputType.getPk().getLookup_code(), outputType.getDescription());
		}
		List<String> collabOutputTypeList = null;
		String Collab_output_typeStr = "";
		if(getCollab_output_type() != null) {
			collabOutputTypeList = Arrays.asList(getCollab_output_type().split("_",-1));
			int listLength = collabOutputTypeList.size();
			for(int i = 0 ; i < listLength ; ++i) {
				Collab_output_typeStr += outputTypeMap.get(collabOutputTypeList.get(i));
				if(i != listLength - 1)
					Collab_output_typeStr += "_";
			}
		}
		return Collab_output_typeStr;
	}

	
	public void setCollab_output_type(String collab_output_type)
	{
		this.collab_output_type = collab_output_type;
	}

	
	public String getCollab_output_inst()
	{
		return collab_output_inst;
	}

	
	public void setCollab_output_inst(String collab_output_inst)
	{
		this.collab_output_inst = collab_output_inst;
	}

	
	public String getCollab_output_date()
	{
		return collab_output_date;
	}

	
	public void setCollab_output_date(String collab_output_date)
	{
		this.collab_output_date = collab_output_date;
	}

	
	public String getFunding_body()
	{
		return funding_body;
	}

	
	public void setFunding_body(String funding_body)
	{
		this.funding_body = funding_body;
	}

	
	public String getSap_funding_source()
	{
		return sap_funding_source;
	}

	
	public void setSap_funding_source(String sap_funding_source)
	{
		this.sap_funding_source = sap_funding_source;
	}

	
	public String getSap_grant_amt()
	{
		return sap_grant_amt;
	}

	
	public void setSap_grant_amt(String sap_grant_amt)
	{
		this.sap_grant_amt = sap_grant_amt;
	}

	
	public String getFunding_org()
	{
		return funding_org;
	}

	
	public void setFunding_org(String funding_org)
	{
		this.funding_org = funding_org;
	}

	
	public String getFunding_others()
	{
		return funding_others;
	}

	
	public void setFunding_others(String funding_others)
	{
		this.funding_others = funding_others;
	}

	
	public String getReleased_val()
	{
		return released_val;
	}

	
	public void setReleased_val(String released_val)
	{
		this.released_val = released_val;
	}

	
	public String getSchCode()
	{
		return schCode;
	}

	
	public void setSchCode(String schCode)
	{
		this.schCode = schCode;
	}

	
	public String getSchDtlCode()
	{
		return schDtlCode;
	}

	
	public void setSchDtlCode(String schDtlCode)
	{
		this.schDtlCode = schDtlCode;
	}

	
	public String getDaCode()
	{
		return daCode;
	}

	
	public void setDaCode(String daCode)
	{
		this.daCode = daCode;
	}

	
	public String getDaDtlCode()
	{
		return daDtlCode;
	}

	
	public void setDaDtlCode(String daDtlCode)
	{
		this.daDtlCode = daDtlCode;
	}

	
	public String getOtherDaDtl()
	{
		return otherDaDtl;
	}

	
	public void setOtherDaDtl(String otherDaDtl)
	{
		this.otherDaDtl = otherDaDtl;
	}

	
	public String getFromMonth()
	{
		return fromMonth;
	}

	
	public void setFromMonth(String fromMonth)
	{
		this.fromMonth = fromMonth;
	}

	
	public String getFromDay()
	{
		return fromDay;
	}

	
	public void setFromDay(String fromDay)
	{
		this.fromDay = fromDay;
	}

	
	public String getToYear()
	{
		return toYear;
	}

	
	public void setToYear(String toYear)
	{
		this.toYear = toYear;
	}

	
	public String getToMonth()
	{
		return toMonth;
	}

	
	public void setToMonth(String toMonth)
	{
		this.toMonth = toMonth;
	}

	
	public String getToDay()
	{
		return toDay;
	}

	
	public void setToDay(String toDay)
	{
		this.toDay = toDay;
	}
	

	
	public String getFunded_proj()
	{
		return funded_proj;
	}

	
	public void setFunded_proj(String funded_proj)
	{
		this.funded_proj = funded_proj;
	}
	
	public String getCdcf_selected_ind()
	{
		return cdcf_selected_ind;
	}


	
	public void setCdcf_selected_ind(String cdcf_selected_ind)
	{
		this.cdcf_selected_ind = cdcf_selected_ind;
	}

	
	public String getDurationMonth()
	{
		return durationMonth;
	}

	
	public void setDurationMonth(String durationMonth)
	{
		this.durationMonth = durationMonth;
	}

	
	public String getDurationYear()
	{
		return durationYear;
	}

	
	public void setDurationYear(String durationYear)
	{
		this.durationYear = durationYear;
	}

	
	public String getDccStat()
	{
		return dccStat;
	}

	
	public void setDccStat(String dccStat)
	{
		this.dccStat = dccStat;
	}

	
	public String getCensusDate()
	{
		return censusDate;
	}

	
	public void setCensusDate(String censusDate)
	{
		this.censusDate = censusDate;
	}

	
	public String getTitle1()
	{
		return title1;
	}

	
	public void setTitle1(String title1)
	{
		this.title1 = title1;
	}

	
	public String getTitle2()
	{
		return title2;
	}

	
	public void setTitle2(String title2)
	{
		this.title2 = title2;
	}

	
	public String getTitle3()
	{
		return title3;
	}

	
	public void setTitle3(String title3)
	{
		this.title3 = title3;
	}

	
	public String getTitle4()
	{
		return title4;
	}

	
	public void setTitle4(String title4)
	{
		this.title4 = title4;
	}

	
	public String getKeyResearchAreas()
	{
		return keyResearchAreas;
	}

	
	public void setKeyResearchAreas(String keyResearchAreas)
	{
		this.keyResearchAreas = keyResearchAreas;
	}

	
	public String getOtherKeyResearchAreas()
	{
		return otherKeyResearchAreas;
	}

	
	public void setOtherKeyResearchAreas(String otherKeyResearchAreas)
	{
		this.otherKeyResearchAreas = otherKeyResearchAreas;
	}

	
	public String getRsCode()
	{
		return rsCode;
	}

	
	public void setRsCode(String rsCode)
	{
		this.rsCode = rsCode;
	}

	
	public String getRsDtlCode()
	{
		return rsDtlCode;
	}

	
	public void setRsDtlCode(String rsDtlCode)
	{
		this.rsDtlCode = rsDtlCode;
	}

	
	public String getTotalNoOfInvestigator()
	{
		return totalNoOfInvestigator;
	}

	
	public void setTotalNoOfInvestigator(String totalNoOfInvestigator)
	{
		this.totalNoOfInvestigator = totalNoOfInvestigator;
	}

	
	public List<ProjectDetails_P> getpDtlList()
	{
		return pDtlList;
	}

	
	public void setpDtlList(List<ProjectDetails_P> pDtlList)
	{
		this.pDtlList = pDtlList;
	}

	
	public int getNoPDtl()
	{
		return noPDtl;
	}

	
	public void setNoPDtl(int noPDtl)
	{
		this.noPDtl = noPDtl;
	}

	
	public String getRoleInstT630()
	{
		return roleInstT630;
	}

	
	public void setRoleInstT630(String roleInstT630)
	{
		this.roleInstT630 = roleInstT630;
	}

	
	public String getRoleInstT690()
	{
		return roleInstT690;
	}

	
	public void setRoleInstT690(String roleInstT690)
	{
		this.roleInstT690 = roleInstT690;
	}

	
	public String getSdg_code()
	{
		sdg_code = sdg_str;
		return sdg_code;
	}

	
	public void setSdg_code(String sdg_code)
	{
		this.sdg_code = sdg_code;
	}

	@Override
	public String toString()
	{
		return "ProjectSummary [projTitle=" + projTitle + ", projSummary=" + projSummary + ", fromYear=" + fromYear
				+ ", keyword=" + keyword+", sdg_str=" + sdg_str + ", sdg_code="+sdg_code+"riNo=" + getRINo() + "]";
	}



	
	
}