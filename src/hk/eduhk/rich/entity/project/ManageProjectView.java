package hk.eduhk.rich.entity.project;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.entity.EduSector;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIProjectV;
import hk.eduhk.rich.entity.importRI.ImportRIProjectV_PK;
import hk.eduhk.rich.entity.importRI.ImportRIStatus;
import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_Q;
import hk.eduhk.rich.entity.staff.Assistant;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.view.ImportRIProject;


@ManagedBean(name = "manageProjectView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageProjectView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(ProjectSummary.class.getName());
	
	private String riCreatorStaffNo;
	private Boolean isCreator;
	private Boolean isContributor;
	private Boolean canDelete;
	private Boolean hasSAP;
	private Boolean hasError;
	private Boolean saved;
	
	private List<ProjectDetails_P> projectList;
	
	private ProjectDetails_Q selectedProjectDetails_q;
	private ProjectHeader_P selectedProjectHeader_p;
	private ProjectHeader_Q selectedProjectHeader_q;
	private List<ProjectDetails_P> projectDetails_p_list;
	
	private List<Capacity> capacity_list;
	private List<FundSource> fundSource_list;
	private List<FundSource> fundSource_cat_list  = new ArrayList<FundSource>();
	private List<FundSource> fundSource_subCat_list  = new ArrayList<FundSource>();
	private List<PartnerCountry> partnerCountry_list;
	private List<ProjectOverseas> projectOverseas_list;
	private List<ResearchOutput> researchOutput_list;
	private List<SelectItem> cdcfStatusList;
	private List<LookupValue> sdgList;
	
	private List<String> fromDayList;
	private List<String> toDayList;
	
	private SysParamDAO sDao = SysParamDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private LookupValueDAO lookupDao = LookupValueDAO.getInstance();
	
	private ImportRIProject projectPanel;
	private ImportRIProjectV selectedImportProject;
	
	public void checkValid(ComponentSystemEvent event) throws IOException
	{
		paramDataLevel = getParamDataLevel();
		String message = "";
		if (!getIsRdoAdmin()) {
			if ((getIsCreator() == false && getIsContributor() == false) || !"M".equals(paramDataLevel)) {
				message = "You don't have access right.";	
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, getLoginUserId()+" doesn't have access right");
			}
		}
	}
	
	public Boolean hasAccessRight() 
	{
		Boolean result = false;
		if ("M".equals(paramDataLevel)){
			if (getIsCreator() || getIsContributor()) {
				result = true;
			}
		}
		if ("P".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		if ("C".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		return result;
	}
	
	
	public List<LookupValue> getSdgList()
	{
		if(sdgList == null) {
			sdgList = lookupDao.getLookupValueList ("SDG","US","Y");
		}
		return sdgList;
	}

	
	public void setSdgList(List<LookupValue> sdgList)
	{
		this.sdgList = sdgList;
	}
	
	public Boolean canViewRIList() 
	{
		Boolean result = false;
		if (staffDetail == null) {
			if (getIsAsst() || getIsRdoAdmin()) {
				staffDetail = getStaffDetail(getParamPid(), null, true);
				//check is Former Staff
				staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
				if (staffPastDetail != null) {
					staffDetail = new StaffIdentity();
					staffDetail.setPid(staffPastDetail.getPid());
					staffDetail.setStaff_number(staffPastDetail.getStaff_number());
					staffDetail.setCn(staffPastDetail.getCn());
					staffDetail.setStaffType("F");
				}
			}else {
				staffDetail = getStaffDetail(null, null, false);
			}
		}
		if (staffDetail != null) {
			if (Strings.isNullOrEmpty(paramPid))
				paramPid = String.valueOf(staffDetail.getPid());
			if (getIsAsst() || getIsRdoAdmin() || getCurrentUserId().equals(staffDetail.getCn())){
					result = true;
			}
		}
		return result;
	}
	
	public Boolean getCanDelete()
	{
		if (canDelete == null) {
			canDelete = false;
			if (getIsCreator() && !Strings.isNullOrEmpty(paramNo)) {
				/*if (getSelectedProjectHeader_q() != null) {
					if ("CDCF_PENDING".equals(selectedProjectHeader_q.getCdcf_status())) {
						ProjectHeader_P selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedProjectHeader_p != null) {
							canDelete = true;
						}
					}
				}*/
				ProjectHeader_P selectedProjectHeader_p_c = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "C");
				if ("M".equals(getParamDataLevel())) {
					if (selectedProjectHeader_p_c == null) {
						ProjectHeader_P selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedProjectHeader_p != null) {
							canDelete = true;
						}
					}
				}else {
					if (getHasSAP() == false) {
						canDelete = true;
					}
				}
			}
		}
		return canDelete;
	}

	
	public void setCanDelete(Boolean canDelete)
	{
		this.canDelete = canDelete;
	}
	
	public String checkSAPError()
	{
		String errMessage = projDao.getSAPLog("RH_UPLOAD_PROJECT_P");
		if (errMessage.contains(paramNo) == false) {
			return errMessage;
		}
		return null;
	}
	
	public Boolean getHasSAP()
	{
		if (hasSAP == null) {
			hasSAP = false;
			hasSAP = projDao.hasSAP("CHRM_SAP_RESEARCH_PROJECT_HDR", "PROJECT_NO", Integer.valueOf(paramNo));
		}
		return hasSAP;
	}


	
	public void setHasSAP(Boolean hasSAP)
	{
		this.hasSAP = hasSAP;
	}
	
	public Boolean getIsCreator()
	{
		if (isCreator == null) {
			isCreator = false;
			selectedProjectDetails_q = getSelectedProjectDetails_q();
			if (selectedProjectDetails_q != null) {
				isCreator = ("Y".equals(selectedProjectDetails_q.getCreator_ind()))?true:false;
				if (!Strings.isNullOrEmpty(getParamNo())) {
					ProjectDetails_Q currentCreator = projDao.getProjectDetails_Q_creator(Integer.valueOf(getParamNo()));
					if (isCreator && currentCreator != null) {
						if (currentCreator.getPk().getStaff_no().equals(selectedProjectDetails_q.getPk().getStaff_no())) {
							isCreator = true;
						}else {
							isCreator = false;
						}
					}
				}
				
			}
			if(paramNo == null) {
				isCreator = true;
			}
			if (getIsRdoAdmin()) {
				isCreator = true;
			}
		}
		return isCreator;
	}
	
	public void setIsCreator(Boolean isCreator)
	{
		this.isCreator = isCreator;
	}

	public Boolean getIsContributor()
	{
		if (isContributor == null) {
			isContributor = false;
			selectedProjectDetails_q = getSelectedProjectDetails_q();
			if (selectedProjectDetails_q != null) {
				isContributor = ("N".equals(selectedProjectDetails_q.getCreator_ind()))?true:false;
			}
		}
		return isContributor;
	}

	public void setIsContributor(Boolean isContributor)
	{
		this.isContributor = isContributor;
	}	
	
	public Boolean getHasError()
	{
		if (hasError == null) {
			hasError = false;
		}
		return hasError;
	}

	public void setHasError(Boolean hasError)
	{
		this.hasError = hasError;
	}
	public Boolean getSaved()
	{
		if (saved == null) {
			saved = false;
		}
		return saved;
	}

	
	public void setSaved(Boolean saved)
	{
		this.saved = saved;
	}
	public List<ProjectDetails_P> getProjectList()
	{
		if (projectList == null && canViewRIList()) {
			projectList = projDao.getProjectDetails_P_byStaffNo(staffDetail.getStaff_number(), "M");
			if (!Strings.isNullOrEmpty(getParamConsent())) {
				if (!"all".equals(paramConsent)) {
					projectList = projectList.stream()
							.filter(y -> paramConsent.equals(y.getProjectDetails_q().getConsent_ind()) && y.getProjectHeader_q().getPublish_freq() > 0)
							.collect(Collectors.toList());
				}
			}else {
				projectList.removeIf(y -> "N".equals(y.getProjectDetails_q().getCreator_ind()) && y.getProjectHeader_q().getPublish_freq() < 1);
			}
		}
		return projectList;
	}

	public long getTotalCount() {
		if (getProjectList() != null) {
			return projectList.stream().count();
		}else {
			return 0;
		}	
    }
	
	public void setDisplyRI()
	{
		if (selectedProjectDetails_q != null) {
			if (!"Y".equals(selectedProjectDetails_q.getConsent_ind()))
				selectedProjectDetails_q.setDisplay_ind("N");
		}
	}
	
	public ProjectDetails_Q getSelectedProjectDetails_q()
	{
		if (getIsAsst() || getIsRdoAdmin()) {
			staffDetail = getStaffDetail(getParamPid(), null, true);
			//check is Former Staff
			staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
			if (staffPastDetail != null) {
				staffDetail = new StaffIdentity();
				staffDetail.setPid(staffPastDetail.getPid());
				staffDetail.setStaff_number(staffPastDetail.getStaff_number());
				staffDetail.setCn(staffPastDetail.getCn());
				staffDetail.setStaffType("F");
			}
		}else {
			staffDetail = getStaffDetail(null, null, false);
		}
		if (staffDetail != null && selectedProjectDetails_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				List<ProjectDetails_Q> tmp = projDao.getProjectDetails_Q(Integer.valueOf(paramNo), staffDetail.getStaff_number());
				selectedProjectDetails_q = (!tmp.isEmpty())?tmp.get(0):null;
			}
			else if(getSelectedImportProject() != null) {
				riCreatorStaffNo = getSelectedImportProject().getPk().getStaff_number();
				selectedProjectDetails_q = new ProjectDetails_Q();
				selectedProjectDetails_q.getPk().setStaff_no(getSelectedImportProject().getPk().getStaff_number());
				selectedProjectDetails_q.setCreator_ind("Y");
				selectedProjectDetails_q.setDisplay_ind("Y");
				selectedProjectDetails_q.setConsent_ind("Y");
			}
			if (selectedProjectDetails_q == null) {
				riCreatorStaffNo = staffDetail.getStaff_number();
				selectedProjectDetails_q = new ProjectDetails_Q();
				selectedProjectDetails_q.getPk().setStaff_no(staffDetail.getStaff_number());
				selectedProjectDetails_q.setCreator_ind("Y");
				selectedProjectDetails_q.setDisplay_ind("Y");
				selectedProjectDetails_q.setConsent_ind("Y");
				
			}
		}
		return selectedProjectDetails_q;
	}


	
	public void setSelectedProjectDetails_q(ProjectDetails_Q selectedProjectDetails_q)
	{
		this.selectedProjectDetails_q = selectedProjectDetails_q;
	}
	
	public ProjectHeader_P getSelectedProjectHeader_p()
	{
		if (selectedProjectHeader_p == null) {
			if (!Strings.isNullOrEmpty(getParamNo())) {
				selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), getParamDataLevel());
				selectedProjectHeader_p = (selectedProjectHeader_p != null)?selectedProjectHeader_p:new ProjectHeader_P();
			}else {
				selectedProjectHeader_p = new ProjectHeader_P();
				if(getSelectedImportProject() != null) {
					selectedProjectHeader_p.setSap_funding_source(selectedImportProject.getSap_funding_source());
					selectedProjectHeader_p.setFunding_body(selectedImportProject.getFunding_body());
					selectedProjectHeader_p.setTitle_1(selectedImportProject.getTitle_1());
					selectedProjectHeader_p.setTitle_2(selectedImportProject.getTitle_2());
					selectedProjectHeader_p.setTitle_3(selectedImportProject.getTitle_3());
					selectedProjectHeader_p.setTitle_4(selectedImportProject.getTitle_4());
					selectedProjectHeader_p.setProject_summary(selectedImportProject.getProject_summary());
					selectedProjectHeader_p.setProject_summary_2(selectedImportProject.getProject_summary_2());
					if(StringUtils.isNotBlank(selectedImportProject.getFrom_day()))
						selectedProjectHeader_p.setFrom_day(Integer.parseInt(selectedImportProject.getFrom_day()));
					selectedProjectHeader_p.setFrom_month(Integer.parseInt(selectedImportProject.getFrom_month()));
					selectedProjectHeader_p.setFrom_year(Integer.parseInt(selectedImportProject.getFrom_year()));
					if(StringUtils.isNotBlank(selectedImportProject.getTo_day()))
						selectedProjectHeader_p.setTo_day(Integer.parseInt(selectedImportProject.getTo_day()));
					selectedProjectHeader_p.setTo_month(Integer.parseInt(selectedImportProject.getTo_month()));
					selectedProjectHeader_p.setTo_year(Integer.parseInt(selectedImportProject.getTo_year()));
				}
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getCollab_partner_ugc())) {
				selectedProjectHeader_p.setCollab_partner_ugc("Y");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getCollab_partner_non_ins())) {
				selectedProjectHeader_p.setCollab_partner_non_ins("Y");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getFunded_proj())) {
				selectedProjectHeader_p.setFunded_proj("Y");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_body()) && StringUtils.isNotBlank(selectedProjectHeader_p.getSap_funding_source())) {
				if (getFundSource_list() != null) {
					for (FundSource fs:fundSource_list) {
						if (fs.getPk().getLookup_code().equals(selectedProjectHeader_p.getSap_funding_source())) {
							String catArray[] = fs.getDescription().split(" - ");
							if (catArray.length > 0) {
								String cat = catArray[0].trim();
								selectedProjectHeader_p.setFunding_body(cat);
								break;
							}
						}
					}
				}
			}
			if (selectedProjectHeader_p.getFrom_year() == null) {
				selectedProjectHeader_p.setFrom_year(getCurrentYear());
			}
			if (selectedProjectHeader_p.getTo_year() == null) {
				selectedProjectHeader_p.setTo_year(getCurrentYear());
			}
		}
		return selectedProjectHeader_p;
	}


	
	public void setSelectedProjectHeader_p(ProjectHeader_P selectedProjectHeader_p)
	{
		this.selectedProjectHeader_p = selectedProjectHeader_p;
	}


	
	public ProjectHeader_Q getSelectedProjectHeader_q()
	{
		if (selectedProjectHeader_q == null) {
			if (!Strings.isNullOrEmpty(getParamNo())) {
				selectedProjectHeader_q = projDao.getProjectHeader_Q(Integer.valueOf(paramNo));
				selectedProjectHeader_q = (selectedProjectHeader_q != null)?selectedProjectHeader_q:new ProjectHeader_Q();
			}else {
				selectedProjectHeader_q = new ProjectHeader_Q();
			}
			if ("Y".equals(selectedProjectHeader_q.getInst_verified_ind()) && selectedProjectHeader_q.getInst_verified_date() == null) {
				selectedProjectHeader_q.setInst_verified_date(getCurrentDate());
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getInst_display_ind())) {
				selectedProjectHeader_q.setInst_display_ind("Y");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getInst_verified_ind())) {
				selectedProjectHeader_q.setInst_verified_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getCdcf_status())) {
				selectedProjectHeader_q.setCdcf_status("CDCF_PENDING");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getCdcf_gen_ind())) {
				selectedProjectHeader_q.setCdcf_gen_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getCdcf_processed_ind())) {
				selectedProjectHeader_q.setCdcf_processed_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getCdcf_selected_ind())) {
				selectedProjectHeader_q.setCdcf_selected_ind("U");
			}
			if (Strings.isNullOrEmpty(selectedProjectHeader_q.getCdcf_changed_ind())) {
				selectedProjectHeader_q.setCdcf_changed_ind("N");
			}
			if (selectedProjectHeader_q.getPublish_freq() == null) {
				selectedProjectHeader_q.setPublish_freq(0);
			}
		}
		return selectedProjectHeader_q;
	}
	
	public void setSelectedProjectHeader_q(ProjectHeader_Q selectedProjectHeader_q)
	{
		this.selectedProjectHeader_q = selectedProjectHeader_q;
	}


	public List<ProjectDetails_P> getProjectDetails_p_list()
	{
		if (projectDetails_p_list == null) {
			projectDetails_p_list = new ArrayList<ProjectDetails_P>();
			if (!Strings.isNullOrEmpty(getParamNo())) {
				projectDetails_p_list = projDao.getProjectDetails_P(Integer.valueOf(paramNo), getParamDataLevel());
				for (ProjectDetails_P d:projectDetails_p_list) {
					if ("F".equals(d.getNon_ied_staff_flag())){
						StaffPast tmp = staffDao.getPastStaffDetailsByStaffNo(d.getInvestigator_staff_no());
						if (tmp != null) {
							d.setInvestigator_name(tmp.getFullname_display());
						}else {
							d.setInvestigator_name("");
						}
					}
					if ("N".equals(d.getNon_ied_staff_flag())){
						StaffIdentity tmp = staffDao.getStaffDetailsByStaffNo(d.getInvestigator_staff_no());
						if (tmp != null) {
							d.setInvestigator_name(tmp.getFullname_display());
						}else {
							d.setInvestigator_name("");
						}
					}
				}
			}else if (getSelectedImportProject() != null) {
				ProjectDetails_P tmp = new ProjectDetails_P();
				StaffIdentity staff = null;
		    	tmp.getPk().setData_level("M");
		    	tmp.setNon_ied_staff_flag("F");
		    	for(StaffIdentity id : getStaffNameList()) {
		    		if(id.getCn().equals(selectedImportProject.getInvestigator_id())) {
		    			tmp.setNon_ied_staff_flag("N");
		    			staff = id;
		    			break;
		    		}
		    	}
		    	if(staff != null) {
			    	tmp.setInvestigator_staff_no(String.valueOf(staff.getStaff_number()));
			    	tmp.setInvestigator_type("PRINCIPAL INVESTIGATOR");
			    	projectDetails_p_list.add(tmp);
		    	}
		    	List<String> cnList = Arrays.asList(selectedImportProject.getOther_contributors_id().split(";"));
		    	for(String cn : cnList) {
		    		staff = null;
		    		for(StaffIdentity id : getStaffNameList()) {
		    			if(id.getCn().equals(cn)) {
		    				staff = id;
		    				break;
		    			}
		    		}
			    	if(staff != null) {
			    		tmp = new ProjectDetails_P();
				    	tmp.getPk().setData_level("M");
			    		tmp.setNon_ied_staff_flag("N");
			    		tmp.setInvestigator_staff_no(staff.getStaff_number());
			    		tmp.setInvestigator_type("CO-INVESTIGATOR");
				    	projectDetails_p_list.add(tmp);
			    	}
		    	}
			}
			else {
				if (getIsAsst() || getIsRdoAdmin()) {
					staffDetail = getStaffDetail(getParamPid(), null, true);
					//check is Former Staff
					staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
					if (staffPastDetail != null) {
						staffDetail = new StaffIdentity();
						staffDetail.setPid(staffPastDetail.getPid());
						staffDetail.setStaff_number(staffPastDetail.getStaff_number());
						staffDetail.setCn(staffPastDetail.getCn());
						staffDetail.setStaffType("F");
					}
				}else {
					staffDetail = getStaffDetail(null, null, false);
				}
				ProjectDetails_P tmp = new ProjectDetails_P();
				if (staffDetail != null) {
			    	tmp.getPk().setData_level("M");
			    	if ("F".equals(staffDetail.getStaffType())){
			    		tmp.setNon_ied_staff_flag("F");
			    	}else {
			    		tmp.setNon_ied_staff_flag("N");
			    	}
			    	tmp.setInvestigator_staff_no(String.valueOf(staffDetail.getStaff_number()));
			    	tmp.setInvestigator_type("PRINCIPAL INVESTIGATOR");
				}
		    	projectDetails_p_list.add(tmp);
			}
		}
		return projectDetails_p_list;
	}

	public void setProjectDetails_p_list(List<ProjectDetails_P> projectDetails_p_list)
	{
		this.projectDetails_p_list = projectDetails_p_list;
	}
	
	public List<String> getFromDayList()
	{
		int month = (selectedProjectHeader_p.getFrom_month() != null)?selectedProjectHeader_p.getFrom_month():1;
		int year = (selectedProjectHeader_p.getFrom_year() != null)?selectedProjectHeader_p.getFrom_year():Calendar.getInstance().get(Calendar.YEAR);
		YearMonth yearMonthObject = YearMonth.of(year, month);
		int daysInMonth = yearMonthObject.lengthOfMonth();
		fromDayList = new ArrayList<>();
		for (int d = 1; d <= daysInMonth; d++) {
			fromDayList.add(String.valueOf(d));
		}
		return fromDayList;
	}

	public List<String> getToDayList()
	{
		int month = (selectedProjectHeader_p.getTo_month() != null)?selectedProjectHeader_p.getTo_month():1;
		int year = (selectedProjectHeader_p.getTo_year() != null)?selectedProjectHeader_p.getTo_year():Calendar.getInstance().get(Calendar.YEAR);
		YearMonth yearMonthObject = YearMonth.of(year, month);
		int daysInMonth = yearMonthObject.lengthOfMonth();
		toDayList = new ArrayList<>();
		for (int d = 1; d <= daysInMonth; d++) {
			toDayList.add(String.valueOf(d));
		}
		return toDayList;
	}
	
	public void resetDAList()
	{	
		String sch_dtl_code = selectedProjectHeader_p.getSch_dtl_code();
		if (!Strings.isNullOrEmpty(sch_dtl_code)) {
			String eduParentLookupCode = getEduSectorParentLookupCode(sch_dtl_code);
			if (!Strings.isNullOrEmpty(eduParentLookupCode)) {
				disAreaList = null;
				disAreaList = getDisAreaList();

				if ("3".equals(eduParentLookupCode)){
					disAreaList.removeIf(y -> !"EDU1000".equals(y.getParent_lookup_code()) && !"EDU1000".equals(y.getPk().getLookup_code()));
				}else {
					disAreaList.removeIf(y -> "EDU1000".equals(y.getParent_lookup_code()) || "EDU1000".equals(y.getPk().getLookup_code()));
				}
			}
		}
	}
	
	public List<ProjectOverseas> getProjectOverseas_list() throws SQLException
	{
		if (projectOverseas_list == null) {
			projectOverseas_list = new ArrayList<ProjectOverseas>();
			
			if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getName_of_collaborative_partner()) 
					&& !Strings.isNullOrEmpty(selectedProjectHeader_p.getCollaborative_partner_city()) 
					&& !Strings.isNullOrEmpty(selectedProjectHeader_p.getCollaborative_partner_country())) {
				String institution[] = selectedProjectHeader_p.getName_of_collaborative_partner().split("_",-1);
				String city[] = selectedProjectHeader_p.getCollaborative_partner_city().split("_",-1);
				String country[] = selectedProjectHeader_p.getCollaborative_partner_country().split("_",-1);
				for (int i = 0; i < institution.length; i++) {
					ProjectOverseas tmp = new ProjectOverseas();
					tmp.setInstitution(institution[i]);
					tmp.setCity(city[i]);
					tmp.setCountry(country[i]);
					projectOverseas_list.add(tmp);
				}
			}
			if ("Y".equals(selectedProjectHeader_p.getCollab_partner_non_ins()) && projectOverseas_list.isEmpty()) {
				addOverseasRow();
			}
		}
		return projectOverseas_list;
	}

	
	public void setProjectOverseas_list(List<ProjectOverseas> projectOverseas_list)
	{
		this.projectOverseas_list = projectOverseas_list;
	}
	public List<Capacity> getCapacity_list()
	{
		if (capacity_list == null) {
			capacity_list = projDao.getCapacityList(1);
		}
		return capacity_list;
	}

	
	public void setCapacity_list(List<Capacity> capacity_list)
	{
		this.capacity_list = capacity_list;
	}

	public List<FundSource> getFundSource_list()
	{
		if (fundSource_list == null) {
			fundSource_list = projDao.getFundSourceList(1);
		}
		return fundSource_list;
	}

	
	public void setFundSource_list(List<FundSource> fundSource_list)
	{
		this.fundSource_list = fundSource_list;
	}
	
	public List<FundSource> getFundSource_cat_list()
	{
		if (fundSource_cat_list.isEmpty()) {
			fundSource_list = getFundSource_list();
			HashSet unique=new HashSet();
			for (FundSource f:fundSource_list) {
				String catArray[] = f.getDescription().split(" - ");
				if (catArray.length > 0) {
					String cat = catArray[0].trim();
					if (unique.add(cat)) {
						FundSource tmp = new FundSource();
						tmp.getPk().setLookup_code(cat);
						tmp.setDescription(cat.replace("Govt", "Government"));
						fundSource_cat_list.add(tmp);
					}
				}
				
			}
		}
		return fundSource_cat_list;
	}

	
	public void setFundSource_cat_list(List<FundSource> fundSource_cat_list)
	{
		this.fundSource_cat_list = fundSource_cat_list;
	}
	
	public List<FundSource> getFundSource_subCat_list()
	{
		if (fundSource_subCat_list.isEmpty()) {
			if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_body())) {
				String code = selectedProjectHeader_p.getFunding_body();
				fundSource_list = getFundSource_list();
				HashSet unique=new HashSet();
				for (FundSource f:fundSource_list) {
					String catArray[] = f.getDescription().split(" - ");
					if (catArray.length > 0) {
						String cat = catArray[0].trim();
						if (cat.equals(code)){
							fundSource_subCat_list.add(f);
						}
					}
				}
			}
		}
		return fundSource_subCat_list;
	}

	public void setFundSource_subCat_list(List<FundSource> fundSource_subCat_list)
	{
		this.fundSource_subCat_list = fundSource_subCat_list;
	}

	public void resetFundSourceSubCatList()
	{
		fundSource_subCat_list  = new ArrayList<FundSource>();
	}
	
	public Boolean requireFundingOrg(String value) 
	{
		Boolean result = false;
		if (!Strings.isNullOrEmpty(value)) {
			List<FundSource> tmpList = fundSource_subCat_list.stream()
					.filter(y -> value.equals(y.getPk().getLookup_code()))
					.collect(Collectors.toList());
			if (!tmpList.isEmpty()) {
				long count = tmpList.get(0).getDescription().chars().filter(ch -> ch == '^').count();
				if (count > 1) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean requireFundingName(String value) 
	{
		Boolean result = false;
		if (!Strings.isNullOrEmpty(value)) {
			List<FundSource> tmpList = fundSource_subCat_list.stream()
					.filter(y -> value.equals(y.getPk().getLookup_code()))
					.collect(Collectors.toList());
			if (!tmpList.isEmpty()) {
				long count = tmpList.get(0).getDescription().chars().filter(ch -> ch == '^').count();
				if (count > 0) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public void resetFundingDetails(String value)
	{
		if ("N".equals(value)) {
			selectedProjectHeader_p.setFunding_body(null);
			selectedProjectHeader_p.setSap_funding_source(null);
			selectedProjectHeader_p.setFunding_others(null);
			selectedProjectHeader_p.setFunding_org(null);
			selectedProjectHeader_p.setSap_grant_amt(null);
			selectedProjectHeader_p.setReleased_val(null);
		}
	}
	

	public List<PartnerCountry> getPartnerCountry_list()
	{
		if (partnerCountry_list == null) {
			partnerCountry_list = projDao.getPartnerCountryList(1);
		}
		return partnerCountry_list;
	}

	
	public void setPartnerCountry_list(List<PartnerCountry> partnerCountry_list)
	{
		this.partnerCountry_list = partnerCountry_list;
	}

	public List<SelectItem> getCdcfStatusList()
	{
		cdcfStatusList = new ArrayList<SelectItem>();
		
		cdcfStatusList.add(optionPending);
		if ("CDCF_PENDING".equals(selectedProjectHeader_q.getCdcf_status()))
				cdcfStatusList.add(optionProcessed);
		if (!"CDCF_PENDING".equals(selectedProjectHeader_q.getCdcf_status()))
			cdcfStatusList.add(optionGenerated);
		cdcfStatusList.add(optionNotSelected);
		cdcfStatusList.add(optionSpecial);
		return cdcfStatusList;
	}
	
	public void setCdcfStatusList(List<SelectItem> cdcfStatusList)
	{
		this.cdcfStatusList = cdcfStatusList;
	}
	
	public List<ResearchOutput> getResearchOutput_list() throws ParseException
	{
		if (researchOutput_list == null) {
			researchOutput_list = new ArrayList<ResearchOutput>();
			if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getCollab_output_title()) 
					&& !Strings.isNullOrEmpty(selectedProjectHeader_p.getCollab_output_type()) 
					&& !Strings.isNullOrEmpty(selectedProjectHeader_p.getCollab_output_inst())
					&& !Strings.isNullOrEmpty(selectedProjectHeader_p.getCollab_output_date())) {
				DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				String outputTitleArray[] = selectedProjectHeader_p.getCollab_output_title().split("_",-1);
				String outputTypeArray[] = selectedProjectHeader_p.getCollab_output_type().split("_",-1);
				String outputInstArray[] = selectedProjectHeader_p.getCollab_output_inst().split("_",-1);
				String outputDateArray[] = selectedProjectHeader_p.getCollab_output_date().split("_",-1);
				for (int i = 0; i< outputTitleArray.length; i++) {
					ResearchOutput tmp = new ResearchOutput();
					tmp.setOutputTitle(outputTitleArray[i]);
					tmp.setOutputType(outputTypeArray[i]);
					tmp.setOutputInst(outputInstArray[i]);
					tmp.setOutputDate(dateFormat.parse(outputDateArray[i]));
					researchOutput_list.add(tmp);
				}
			}
		}
		return researchOutput_list;
	}

	
	public void setResearchOutput_list(List<ResearchOutput> researchOutput_list)
	{
		this.researchOutput_list = researchOutput_list;
	}

	public void moveColumnUp(int idx) throws SQLException
	{
		if (!getProjectDetails_p_list().isEmpty())
		{
			if (idx > 0)
			{
				ProjectDetails_P tmp = projectDetails_p_list.get(idx-1);
				projectDetails_p_list.set(idx-1, projectDetails_p_list.get(idx));
				projectDetails_p_list.set(idx, tmp);
			}
		}
	}
	public void moveOutputColumnUp(int idx) throws SQLException, ParseException
	{
		if (!getResearchOutput_list().isEmpty())
		{
			if (idx > 0)
			{
				ResearchOutput tmp = researchOutput_list.get(idx-1);
				researchOutput_list.set(idx-1, researchOutput_list.get(idx));
				researchOutput_list.set(idx, tmp);
			}
		}
	}
	public void moveOverseasColumnUp(int idx) throws SQLException
	{
		if (!getProjectOverseas_list().isEmpty())
		{
			if (idx > 0)
			{
				ProjectOverseas tmp = projectOverseas_list.get(idx-1);
				projectOverseas_list.set(idx-1, projectOverseas_list.get(idx));
				projectOverseas_list.set(idx, tmp);
			}
		}
	}
	
	public void moveColumnDown(int idx) throws SQLException
	{
		if (getProjectDetails_p_list() != null)
		{
			if (idx+1 < projectDetails_p_list.size())
			{
				ProjectDetails_P tmp = projectDetails_p_list.get(idx+1);
				projectDetails_p_list.set(idx+1, projectDetails_p_list.get(idx));
				projectDetails_p_list.set(idx, tmp);
			}
		}
	}	
	public void moveOutputColumnDown(int idx) throws SQLException, ParseException
	{
		if (getResearchOutput_list() != null)
		{
			if (idx+1 < researchOutput_list.size())
			{
				ResearchOutput tmp = researchOutput_list.get(idx+1);
				researchOutput_list.set(idx+1, researchOutput_list.get(idx));
				researchOutput_list.set(idx, tmp);
			}
		}
	}	
	public void moveOverseasColumnDown(int idx) throws SQLException
	{
		if (getProjectOverseas_list() != null)
		{
			if (idx+1 < projectOverseas_list.size())
			{
				ProjectOverseas tmp = projectOverseas_list.get(idx+1);
				projectOverseas_list.set(idx+1, projectOverseas_list.get(idx));
				projectOverseas_list.set(idx, tmp);
			}
		}
	}	
	
	public void updateRowStaffNum(int idx) throws SQLException
	{
		if (projectDetails_p_list != null)
		{
			if (idx > -1)
			{
				ProjectDetails_P tmp = projectDetails_p_list.get(idx);
				String staffNum = getPastStaffNumByStaffName(tmp.getInvestigator_name());
				tmp.setInvestigator_staff_no(staffNum);
				projectDetails_p_list.set(idx, tmp);
			}
		}
	}
	
	public void deleteRow(int idx) throws SQLException
	{
		if (!getProjectDetails_p_list().isEmpty())
		{
			if (idx < projectDetails_p_list.size()) projectDetails_p_list.remove(idx);
		}
	}
	public void deleteOutputRow(int idx) throws SQLException, ParseException
	{
		if (!getResearchOutput_list().isEmpty())
		{
			if (idx < researchOutput_list.size()) researchOutput_list.remove(idx);
		}
	}
	public void deleteOverseasRow(int idx) throws SQLException
	{
		if (!getProjectOverseas_list().isEmpty())
		{
			if (idx < projectOverseas_list.size()) projectOverseas_list.remove(idx);
		}
	}
	public void addRow() throws SQLException
	{
		ProjectDetails_P p = new ProjectDetails_P();
		p.getPk().setProject_no(selectedProjectHeader_q.getProject_no());
		p.getPk().setData_level(getParamDataLevel());
		p.setNon_ied_staff_flag("N");
		if (!getProjectDetails_p_list().isEmpty())
		{
			projectDetails_p_list.add(p);
		}else {
			projectDetails_p_list = new ArrayList<ProjectDetails_P>();
			projectDetails_p_list.add(p);
		}
	}
	public void addOutputRow() throws SQLException, ParseException
	{
		ResearchOutput p = new ResearchOutput();
		if (!getResearchOutput_list().isEmpty())
		{
			researchOutput_list.add(p);
		}else {
			researchOutput_list = new ArrayList<ResearchOutput>();
			researchOutput_list.add(p);
		}
	}
	public void addOverseasRow() throws SQLException
	{
		ProjectOverseas p = new ProjectOverseas();
		if (!getProjectOverseas_list().isEmpty())
		{	
			projectOverseas_list.add(p);
		}else {
			projectOverseas_list = new ArrayList<ProjectOverseas>();
			projectOverseas_list.add(p);
		}
	}
	
	public Boolean isOtherDisArea() {
		Boolean result = false;
		if (selectedProjectHeader_p != null) {
			String lookup_code = selectedProjectHeader_p.getDa_dtl_code();
			if (lookup_code != null) {
				result = (lookup_code.contains("OTHER"))?true:false;
			}
		}
		return result;
	}
	public void save() throws Exception
	{
		if (selectedProjectHeader_p != null && selectedProjectHeader_q != null && selectedProjectDetails_q != null) {
			hasError = false;
			if ("M".equals(getParamDataLevel())) {
				save_m(false);
			}
			if ("P".equals(getParamDataLevel())) {
				save_p();
			}
			if ("C".equals(getParamDataLevel())) {
				save_c();
			}
		}
	}
	
	//save and publish
	public void saveAndPublishForm() throws IOException
	{
		if (selectedProjectHeader_p != null && selectedProjectHeader_q != null && selectedProjectDetails_q != null) {
			hasError = false;
			save_m(true);
			publish();
		}
	}
		
	//M level
	public void save_m(boolean doPublish)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		saved = false;
		try {
				selectedProjectHeader_q.setPublish_status("MODIFIED");
				selectedProjectHeader_q.setLast_modified_by(getLoginUserId());
				selectedProjectHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
				
				selectedProjectHeader_p.getPk().setData_level("M");
				
				//other UGC Funded == 'N'
				if (selectedProjectHeader_p.getCollab_partner_ugc().equals("N")) {
					selectedProjectHeader_p.setRole_inst("X");
				}
				//overseas == 'N'
				if (selectedProjectHeader_p.getCollab_partner_non_ins().equals("N")) {
					projectOverseas_list = null;
					selectedProjectHeader_p.setRole_inst_for_t690("X");
				}
				
				//trim funding_others
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_others())) {
					selectedProjectHeader_p.setFunding_others(selectedProjectHeader_p.getFunding_others().trim());
				}
				
				//trim funding_org
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_org())) {
					selectedProjectHeader_p.setFunding_org(selectedProjectHeader_p.getFunding_org().trim());
				}
				
				//set edu code
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getSch_dtl_code())) {
					eduSectorList = getEduSectorList();
					List<EduSector> tmpEduSectorList = eduSectorList.stream()
							.filter(y -> y.getPk().getLookup_code().equals(selectedProjectHeader_p.getSch_dtl_code()))
							.collect(Collectors.toList());
					if (!tmpEduSectorList.isEmpty()) {
						if (tmpEduSectorList.get(0).getParent_lookup_code().length() > 0) {
							selectedProjectHeader_p.setSch_code(tmpEduSectorList.get(0).getParent_lookup_code().substring(0,1));
						}
					}
				}
				
				//set da code
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getDa_dtl_code())) {
					selectedProjectHeader_p.setDa_code(getDisAreaParentLookupCode(selectedProjectHeader_p.getDa_dtl_code()));
					/*disAreaList = getDisAreaList();
					List<DisciplinaryArea> tmpDisAreaList = disAreaList.stream()
							.filter(y -> y.getPk().getLookup_code().equals(selectedProjectHeader_p.getDa_dtl_code()))
							.collect(Collectors.toList());
					if (!tmpDisAreaList.isEmpty()) {
						if (tmpDisAreaList.get(0).getParent_lookup_code().length() > 0) {
							selectedProjectHeader_p.setDa_code(resetLookUpCode(tmpDisAreaList.get(0).getParent_lookup_code()));
						}
					}*/
				}
				selectedProjectHeader_p.setUserstamp(getLoginUserId());
				selectedProjectHeader_q.setUserstamp(getLoginUserId());
				selectedProjectDetails_q.setUserstamp(getLoginUserId());
				
				
				
				
				
				if(selectedProjectHeader_p.getSdg_code_list() != null) 
					selectedProjectHeader_p.setSdg_code(String.join(",", selectedProjectHeader_p.getSdg_code_list()));
				
				//Record still can be saved even there is error
				validateRequiredField();
				validateLength();
				validateFunding(); 
				validateProjectDate();
				validateDisArea();
				
				if (validateProjectDetails_P(staffDetail.getStaff_number()) && convertResearchOutput() && convertOverseas()) {
					//Update P, Q
					selectedProjectHeader_q = projDao.updateEntity(selectedProjectHeader_q);
	
					int currentProjectNo = selectedProjectHeader_q.getProject_no();
					
					selectedProjectDetails_q.getPk().setProject_no(currentProjectNo);
					selectedProjectDetails_q = projDao.updateEntity(selectedProjectDetails_q);
	
					selectedProjectHeader_p.getPk().setProject_no(currentProjectNo);
					selectedProjectHeader_p = projDao.updateEntity(selectedProjectHeader_p);
					
					//delete all investigators in M levels
					projDao.deleteAllInvestigators(currentProjectNo, "M");
	
					//Update paoject name list
					int line_no = 1;
					for(ProjectDetails_P p:projectDetails_p_list) {
						p.getPk().setProject_no(currentProjectNo);
						p.getPk().setData_level("M");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getNon_ied_staff_flag().equals("N") && p.getInvestigator_staff_no() != null) {
							staffNameList = getStaffNameList();
							List<String> nList = staffNameList.stream()
									.filter(x -> x.getStaff_number().equals(p.getInvestigator_staff_no()))
									.map(x -> x.getFullname_save())
									.collect(Collectors.toList());
							if (!nList.isEmpty()) {
								p.setInvestigator_name(nList.get(0));
							}
						}
						//set past staff details
						if (p.getNon_ied_staff_flag().equals("F") && p.getInvestigator_staff_no() != null) {
							staffPastList = getStaffPastList();
							List<String> fList = staffPastList.stream()
									.filter(x -> x.getStaff_number().equals(p.getInvestigator_staff_no()))
									.map(x -> x.getFullname_save())
									.collect(Collectors.toList());
							if (!fList.isEmpty()) {
								p.setInvestigator_name(fList.get(0));
							}
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						projDao.updateEntity(p);
						
						//Create record in ProjectDetails_q if has staff number
						if (p.getInvestigator_staff_no() != null) {
							ProjectDetails_Q tmpDetailsQ = projDao.getProjectDetails_Q1(currentProjectNo, p.getInvestigator_staff_no());
							if (tmpDetailsQ.getPk().getProject_no() == null) {
								ProjectDetails_Q newDetailsQ = new ProjectDetails_Q();
								newDetailsQ.getPk().setProject_no(currentProjectNo);
								newDetailsQ.getPk().setStaff_no(p.getInvestigator_staff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								projDao.updateEntity(newDetailsQ);
							}
						}
						line_no++;		
					}
				// update importStatus if it is imported
				updateSelectedImportStatus(currentProjectNo);
					
				// Success message
				String message = "msg.success.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Project");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				saved = true;
				
				if (!doPublish) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageProject_edit.xhtml?pid="+paramPid+"&no="+selectedProjectHeader_p.getPk().getProject_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Project");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Project");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  project (project_no=" + selectedProjectHeader_q.getProject_no() + ", staff_no="+ selectedProjectDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Project");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  project (project_no=" + selectedProjectHeader_q.getProject_no() + ", staff_no="+ selectedProjectDetails_q.getPk().getStaff_no() + ")", e);
		}
	}
	//P level
	public void save_p() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
				if ("Y".equals(selectedProjectHeader_q.getInst_verified_ind()) && selectedProjectHeader_q.getInst_verified_date() == null) {
					selectedProjectHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedProjectHeader_q.getCdcf_status())) {
					selectedProjectHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedProjectHeader_q.getCdcf_status())) {
					selectedProjectHeader_q.setCdcf_processed_ind("Y");
					selectedProjectHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedProjectHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedProjectHeader_q.getCdcf_status())) {
					selectedProjectHeader_q.setCdcf_processed_ind("Y");
					selectedProjectHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedProjectHeader_q.setCdcf_selected_ind("N");
				}
				selectedProjectHeader_q.setUserstamp(getLoginUserId());
				
				//Update Header Q
				selectedProjectHeader_q = projDao.updateEntity(selectedProjectHeader_q);

				int currentProjectNo = selectedProjectHeader_q.getProject_no();
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentProjectNo);
				
				// Success message
				String message;
				if ("CDCF_PROCESSED".equals(selectedProjectHeader_q.getCdcf_status())||"CDCF_NOT_SEL".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedProjectHeader_q.getCdcf_status())) {
					takeSnapshot();
					boolean publishToSAP = true;
					String failSAPMsg = null;
					if (publishToSAP) {
						projDao.publishToSAP(selectedProjectHeader_q.getProject_no(), "RH_UPLOAD_PROJECT_P");
						failSAPMsg = checkSAPError();
					}
					
					//Fail SAP message
					if (failSAPMsg != null)
					{
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, failSAPMsg, ""));
					}else {
						message = "msg.success.save.generate.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Project");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}
					
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageProject_edit.xhtml?no="+currentProjectNo+"&dataLevel=P";
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Project");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}
				
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  project (project_no=" + selectedProjectHeader_q.getProject_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  project (project_no=" + selectedProjectHeader_q.getProject_no() + ")", e);
		}
	}	
	
	//C level
	public void save_c() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		boolean publishToSAP = false;
		try {
			validateRequiredField();
			validateLength();
			validateFunding(); 
			validateProjectDate();
			validateDisArea();
			
			if (validateProjectDetails_P(getRealRiCreatorStaffNo()) && convertResearchOutput() && convertOverseas() && !getHasError()) {
				if ("CDCF_GENERATED".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_PROCESSED".equals(selectedProjectHeader_q.getCdcf_status())
						|| "CDCF_NOT_SEL".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedProjectHeader_q.getCdcf_status())) {
					publishToSAP = true;
				}
				if ("Y".equals(selectedProjectHeader_q.getInst_verified_ind()) && selectedProjectHeader_q.getInst_verified_date() == null) {
					selectedProjectHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedProjectHeader_q.getCdcf_status())) {
					selectedProjectHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedProjectHeader_q.getCdcf_status())) {
					selectedProjectHeader_q.setCdcf_processed_ind("Y");
					selectedProjectHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedProjectHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedProjectHeader_q.getCdcf_status())) {
					selectedProjectHeader_q.setCdcf_processed_ind("Y");
					selectedProjectHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedProjectHeader_q.setCdcf_selected_ind("N");
				}
				
				selectedProjectHeader_q.setUserstamp(getLoginUserId());
				
				//Update P, Q
				selectedProjectHeader_q = projDao.updateEntity(selectedProjectHeader_q);
				
				selectedProjectHeader_p.setUserstamp(getLoginUserId());
				
				//trim funding_others
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_others())) {
					selectedProjectHeader_p.setFunding_others(selectedProjectHeader_p.getFunding_others().trim());
				}
				
				//trim funding_org
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_org())) {
					selectedProjectHeader_p.setFunding_org(selectedProjectHeader_p.getFunding_org().trim());
				}
				
				//sdg
				if(selectedProjectHeader_p.getSdg_code_list() != null) 
					selectedProjectHeader_p.setSdg_code(String.join(",", selectedProjectHeader_p.getSdg_code_list()));
				
				if (publishToSAP) {
					String census_date = sDao.getSysParamValueByCode("CENSUS_DATE");
					selectedProjectHeader_p.setCensus_date(stringToDate(census_date));
				}
				selectedProjectHeader_p = projDao.updateEntity(selectedProjectHeader_p);
				
				projDao.deleteAllInvestigators(selectedProjectHeader_q.getProject_no(), "C");
				int line_no = 1;
				for (ProjectDetails_P p:projectDetails_p_list) {
					p.getPk().setData_level("C");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getNon_ied_staff_flag().equals("N") && p.getInvestigator_staff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getInvestigator_staff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setInvestigator_name(nList.get(0));
						}
					}
					//set past staff details
					if (p.getNon_ied_staff_flag().equals("F") && p.getInvestigator_staff_no() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getStaff_number().equals(p.getInvestigator_staff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setInvestigator_name(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					projDao.updateEntity(p);
					
					//Create record in ProjectDetails_q if has staff number
					if (p.getInvestigator_staff_no() != null) {
						ProjectDetails_Q tmpDetailsQ = projDao.getProjectDetails_Q1(selectedProjectHeader_p.getPk().getProject_no(), p.getInvestigator_staff_no());
						if (tmpDetailsQ.getPk().getProject_no() == null) {
							ProjectDetails_Q newDetailsQ = new ProjectDetails_Q();
							newDetailsQ.getPk().setProject_no(selectedProjectHeader_p.getPk().getProject_no());
							newDetailsQ.getPk().setStaff_no(p.getInvestigator_staff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							projDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;
				}
				
				int currentProjectNo = selectedProjectHeader_q.getProject_no();
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentProjectNo);
				
				// Success message
				String message;
				if (publishToSAP) {
					projDao.publishToSAP(selectedProjectHeader_q.getProject_no(), "RH_UPLOAD_PROJECT_P");
					String failSAPMsg = checkSAPError();
					if (failSAPMsg != null) {
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, failSAPMsg, ""));
					}else {
						message = "msg.success.save.generate.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Project");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageProject_edit.xhtml?no="+currentProjectNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Project");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageProject_edit.xhtml?no="+currentProjectNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}
				
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Project");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  project (project_no=" + selectedProjectHeader_q.getProject_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  project (project_no=" + selectedProjectHeader_q.getProject_no() + ")", e);
		}
	}	
	
	public void publish()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (selectedProjectHeader_p != null && selectedProjectHeader_q != null && selectedProjectDetails_q != null) {
			try {
				//Get C level header
				if (getParamNo() != null) {
					ProjectHeader_P selectedProjectHeader_p_c = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "C");
					if (selectedProjectHeader_p_c != null) {
						selectedProjectHeader_q.setCdcf_changed_ind("Y");
					}
				}
				selectedProjectHeader_q.setPublish_status("PUBLISHED");
				int publishFreq = (selectedProjectHeader_q.getPublish_freq() != null)?selectedProjectHeader_q.getPublish_freq()+1:1;
				selectedProjectHeader_q.setPublish_freq(publishFreq);
				selectedProjectHeader_q.setLast_modified_by(getLoginUserId());
				selectedProjectHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
				selectedProjectHeader_q.setLast_published_by(getLoginUserId());
				selectedProjectHeader_q.setLast_published_date(Timestamp.from(Instant.now()));
				
				selectedProjectHeader_p.getPk().setData_level("M");

				//other UGC Funded == 'N'
				if (selectedProjectHeader_p.getCollab_partner_non_ins().equals("N")) {
					selectedProjectHeader_p.setRole_inst("X");
				}
				//overseas == 'N'
				if (selectedProjectHeader_p.getCollab_partner_non_ins().equals("N")) {
					projectOverseas_list = null;
					selectedProjectHeader_p.setRole_inst_for_t690("X");
				}
				//set edu code
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getSch_dtl_code())) {
					eduSectorList = getEduSectorList();
					List<EduSector> tmpEduSectorList = eduSectorList.stream()
							.filter(y -> y.getPk().getLookup_code().equals(selectedProjectHeader_p.getSch_dtl_code()))
							.collect(Collectors.toList());
					if (!tmpEduSectorList.isEmpty()) {
						if (tmpEduSectorList.get(0).getParent_lookup_code().length() > 0) {
							selectedProjectHeader_p.setSch_code(tmpEduSectorList.get(0).getParent_lookup_code().substring(0,1));
						}
					}
				}

				//set da code
				if (!Strings.isNullOrEmpty(selectedProjectHeader_p.getDa_dtl_code())) {
					selectedProjectHeader_p.setDa_code(getDisAreaParentLookupCode(selectedProjectHeader_p.getDa_dtl_code()));
					/*disAreaList = getDisAreaList();
					List<DisciplinaryArea> tmpDisAreaList = disAreaList.stream()
							.filter(y -> y.getPk().getLookup_code().equals(selectedProjectHeader_p.getDa_dtl_code()))
							.collect(Collectors.toList());
					if (!tmpDisAreaList.isEmpty()) {
						if (tmpDisAreaList.get(0).getParent_lookup_code().length() > 0) {
							selectedProjectHeader_p.setDa_code(resetLookUpCode(tmpDisAreaList.get(0).getParent_lookup_code()));
						}
					}*/
				}
				selectedProjectHeader_p.setTotal_no_of_investigator(projectDetails_p_list.size());
				selectedProjectHeader_p.setUserstamp(getLoginUserId());
				
				selectedProjectDetails_q.setUserstamp(getLoginUserId());

				//if (validateProjectDetails_P(staffDetail.getStaff_number()) && convertResearchOutput(researchOutput_list) && convertOverseas(projectOverseas_list) && validateFunding() && validateProjectDate()) {
				if (!getHasError()) {
					//Update P, Q
					selectedProjectHeader_q = projDao.updateEntity(selectedProjectHeader_q);

					int currentProjectNo = selectedProjectHeader_q.getProject_no();
					
					selectedProjectDetails_q.getPk().setProject_no(currentProjectNo);
					selectedProjectDetails_q = projDao.updateEntity(selectedProjectDetails_q);

					selectedProjectHeader_p.getPk().setProject_no(currentProjectNo);
					selectedProjectHeader_p = projDao.updateEntity(selectedProjectHeader_p);

					ProjectHeader_P selectedProjectHeader_p2 = selectedProjectHeader_p;
					selectedProjectHeader_p2.getPk().setData_level("P");
					selectedProjectHeader_p2.setUserstamp(getLoginUserId());
					ProjectHeader_P projectHeader_p_publish = projDao.getProjectHeader_P(selectedProjectHeader_p.getPk().getProject_no(), "P");
					if (projectHeader_p_publish != null) {
						projDao.deleteEntity(ProjectHeader_P.class, projectHeader_p_publish.getPk());
						//selectedProjectHeader_p2 = projDao.updateEntity(selectedProjectHeader_p2);
						//java.sql.Timestamp sqlDate = new java.sql.Timestamp(projectHeader_p_publish.getCreationDate().getTime());
						//projDao.updateCreateDetails(projectHeader_p_publish.getPk().getProject_no(), projectHeader_p_publish.getCreator(), sqlDate);
					}
					selectedProjectHeader_p2 = projDao.updateEntity(selectedProjectHeader_p2);
					

					//delete all investigators in M and P levels
					//projDao.deleteAllInvestigators(currentProjectNo, "M");
					projDao.deleteAllInvestigators(currentProjectNo, "P");

					//Update Project name list
					int line_no = 1;
					for(ProjectDetails_P p:projectDetails_p_list) {
						/*p.getPk().setProject_no(currentProjectNo);
						p.getPk().setData_level("M");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getNon_ied_staff_flag().equals("N") && p.getInvestigator_staff_no() != null) {
							staffNameList = getStaffNameList();
							List<String> nList = staffNameList.stream()
									.filter(x -> x.getStaff_number().equals(p.getInvestigator_staff_no()))
									.map(x -> x.getFullname() + " " + x.getChinesename())
									.collect(Collectors.toList());
							if (!nList.isEmpty()) {
								p.setInvestigator_name(nList.get(0));
							}
						}
						//set past staff details
						if (p.getNon_ied_staff_flag().equals("F") && p.getInvestigator_staff_no() != null) {
							staffPastList = getStaffPastList();
							List<String> fList = staffPastList.stream()
									.filter(x -> x.getStaff_number().equals(p.getInvestigator_staff_no()))
									.map(x -> x.getFullname())
									.collect(Collectors.toList());
							if (!fList.isEmpty()) {
								p.setInvestigator_name(fList.get(0));
							}
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						projDao.updateEntity(p);*/
						ProjectDetails_P p2 = p;
						p2.getPk().setData_level("P");
						projDao.updateEntity(p2);
						
						//Create record in ProjectDetails_q if has staff number
						if (p.getInvestigator_staff_no() != null) {
							ProjectDetails_Q tmpDetailsQ = projDao.getProjectDetails_Q1(currentProjectNo, p.getInvestigator_staff_no());
							if (tmpDetailsQ.getPk().getProject_no() == null) {
								ProjectDetails_Q newDetailsQ = new ProjectDetails_Q();
								newDetailsQ.getPk().setProject_no(currentProjectNo);
								newDetailsQ.getPk().setStaff_no(p.getInvestigator_staff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								projDao.updateEntity(newDetailsQ);
							}
						}
						line_no++;		
					}
					//projectDetails_p_list = null;
					
					// update importStatus if it is imported
					//updateSelectedImportStatus(currentProjectNo);
					
					// Success message
					String message = "msg.success.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Project");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}else {
					// Failed message
					String message = "msg.err.data.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Project");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				}
				
				if (saved) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageProject_edit.xhtml?pid="+paramPid+"&no="+selectedProjectHeader_p.getPk().getProject_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit Project (Project_no=" + selectedProjectHeader_q.getProject_no() + ", staff_no="+ selectedProjectDetails_q.getPk().getStaff_no() + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit Project (Project_no=" + selectedProjectHeader_q.getProject_no() + ", staff_no="+ selectedProjectDetails_q.getPk().getStaff_no() + ")", e);
			}
		}				
	}
	
	public void submitConsent()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		if (selectedProjectDetails_q != null) {
			try {
				selectedProjectDetails_q.setUserstamp(getLoginUserId());
				selectedProjectDetails_q = projDao.updateEntity(selectedProjectDetails_q);
					
				// Success message
				String message = "msg.success.submit.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Consent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedProjectDetails_q + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedProjectDetails_q + ")", e);
			}
		}
	}
	
	public String takeSnapshot()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String destUrl = "";
		boolean publishToSAP = false;
		String failSAPMsg = null;
		if (getSelectedProjectHeader_p() != null && getProjectDetails_p_list() != null && getSelectedProjectHeader_q() != null) {
			try {
				if ("CDCF_GENERATED".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_NOT_SEL".equals(selectedProjectHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedProjectHeader_q.getCdcf_status())) {
					publishToSAP = true;
				}
				int currentProjectNo = selectedProjectHeader_p.getPk().getProject_no();
				ProjectHeader_P selectedProjectHeader_p_c = projDao.getProjectHeader_P(currentProjectNo, "C");
				if (selectedProjectHeader_p_c != null) {
					projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p_c.getPk());
				}
				selectedProjectHeader_p_c = selectedProjectHeader_p;
				selectedProjectHeader_p_c.getPk().setData_level("C");
				selectedProjectHeader_p_c.setUserstamp(getLoginUserId());
				selectedProjectHeader_p_c.setCreator(getLoginUserId());
				if (publishToSAP) {
					String census_date = sDao.getSysParamValueByCode("CENSUS_DATE");
					selectedProjectHeader_p_c.setCensus_date(stringToDate(census_date));
				}
				selectedProjectHeader_p = projDao.updateEntity(selectedProjectHeader_p_c);
				
				projDao.deleteAllInvestigators(currentProjectNo, "C");
				for (ProjectDetails_P p:projectDetails_p_list) {
					ProjectDetails_P p2 = p;
					p2.getPk().setData_level("C");
					p2.setCreator(getLoginUserId());
					p2.setUserstamp(getLoginUserId());
					projDao.updateEntity(p2);
				}
				
				
				if (publishToSAP) {
					projDao.publishToSAP(selectedProjectHeader_q.getProject_no(), "RH_UPLOAD_PROJECT_P");
					failSAPMsg = checkSAPError();
				}
				
				//Fail SAP message
				if (failSAPMsg != null)
				{
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, failSAPMsg, ""));
				}
				
				// Success message
				String message = "msg.success.create.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Snapshot");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				destUrl = redirect("manageProject_edit") + "&no=" + currentProjectNo + "&dataLevel=C";	
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Project No.:" + selectedProjectHeader_p.getPk().getProject_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Project No.:" + selectedProjectHeader_p.getPk().getProject_no() + ")", e);
			}
		}
		return redirect(destUrl);
	}	
	
	//Delete all levels
	public String deleteForm() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (!Strings.isNullOrEmpty(paramNo)) {
			try {
				//Get C level header
				ProjectHeader_P selectedProjectHeader_p_c = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "C");
				
				//In M level page
				/*if ("M".equals(getParamDataLevel())) {
					if (selectedProjectHeader_p_c == null) {
						//P
						selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "P");
						if (selectedProjectHeader_p != null) {
							projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p.getPk());
						}
						//M
						ProjectHeader_P selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedProjectHeader_p != null) {
							projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p.getPk());
						}
						
						//M and P levels
						projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "M");
						projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "P");
						
						//Header Q and Details Q
						projDao.deleteEntity(ProjectHeader_Q.class, Integer.valueOf(paramNo));
						projDao.deleteProjectDetails_Q(Integer.valueOf(paramNo));
							
						// Success message
						String message = "msg.success.delete.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Project");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}else {
						String message = "Project is already generated to SAP, so it can not be deleted. ";
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					}
				}*/
				//In P or C level page
				/*if ("P".equals(getParamDataLevel()) || "C".equals(getParamDataLevel())){
					//C
					if (selectedProjectHeader_p_c != null) {
						projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p_c.getPk());
					}
					//P
					selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "P");
					if (selectedProjectHeader_p != null) {
						projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p.getPk());
					}
					//M
					ProjectHeader_P selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "M");
					if (selectedProjectHeader_p != null) {
						projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p.getPk());
					}
					
					//M and P and C levels
					projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "M");
					projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "P");
					projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "C");
					
					//Header Q and Details Q
					projDao.deleteEntity(ProjectHeader_Q.class, Integer.valueOf(paramNo));
					projDao.deleteProjectDetails_Q(Integer.valueOf(paramNo));
					
					// Success message
					String message = "msg.success.delete.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Project");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}*/
				//C
				if (selectedProjectHeader_p_c != null) {
					projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p_c.getPk());
				}
				//P
				selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "P");
				if (selectedProjectHeader_p != null) {
					projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p.getPk());
				}
				//M
				ProjectHeader_P selectedProjectHeader_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "M");
				if (selectedProjectHeader_p != null) {
					projDao.deleteEntity(ProjectHeader_P.class, selectedProjectHeader_p.getPk());
				}
				
				//M and P and C levels
				projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "M");
				projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "P");
				projDao.deleteAllInvestigators(Integer.valueOf(paramNo), "C");
				
				//Header Q and Details Q
				projDao.deleteEntity(ProjectHeader_Q.class, Integer.valueOf(paramNo));
				projDao.deleteProjectDetails_Q(Integer.valueOf(paramNo));
				
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Project");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete (No.: " + selectedProjectHeader_q.getProject_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete (No.: " + selectedProjectHeader_q.getProject_no() + ")", e);
			}
		}
		String destUrl = redirect("manageProject");
		if(paramPid != null)
			destUrl += "&pid=" + paramPid;
		return redirect(destUrl);
	}
	
	public String getRealRiCreatorStaffNo()
	{
		ProjectDetails_Q tmp = projDao.getProjectDetails_Q_creator(Integer.valueOf(paramNo));
		if (tmp != null) {
			return tmp.getPk().getStaff_no();
		}
		return null;
	}
	
	public String getRiCreatorStaffNo()
	{
		if (Strings.isNullOrEmpty(riCreatorStaffNo)) {
			ProjectDetails_Q tmp = projDao.getProjectDetails_Q_creator(Integer.valueOf(paramNo));
			riCreatorStaffNo = (tmp!= null)?tmp.getPk().getStaff_no():getCurrentUserId();
		}
		return riCreatorStaffNo;
	}

	
	public void setRiCreatorStaffNo(String riCreatorStaffNo)
	{
		this.riCreatorStaffNo = riCreatorStaffNo;
	}

	public boolean isRiCreator(String staff_no) 
	{
		boolean result = false;
		String realRiCreatorStaffNo = ("M".equals(getParamDataLevel()))? getRiCreatorStaffNo():getRealRiCreatorStaffNo();
		if (!Strings.isNullOrEmpty(staff_no) && !Strings.isNullOrEmpty(realRiCreatorStaffNo)) {
			result = (realRiCreatorStaffNo.equals(staff_no))?true:false;
		}
		return result;
	}	
	
	public boolean checkSnapshotExists() 
	{
		boolean result = false;
		ProjectHeader_P tmp = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "C");
		if (tmp != null) {
			result = true;
		}
		return result;	
	}
	
	public ImportRIProject getProjectPanel()
	{
		if(projectPanel == null) {
			projectPanel = new ImportRIProject();
			projectPanel.setParamPid(getParamPid());
		}
		return projectPanel;
	}


	public void ignoreProject() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedProject = dao.getStatusByPK(getProjectPanel().getSelectedIgnoreProject());
		if(selectedProject != null) {
			selectedProject.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedProject);
			getProjectPanel().setProjectList(null);
		}
	}

	
	public ImportRIProjectV getSelectedImportProject()
	{
		if(selectedImportProject == null && getParamArea_code() != null
				&& getParamSource_id() != null && getParamStaff_number() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			ImportRIProjectV_PK pk = new ImportRIProjectV_PK();
			pk.setArea_code(getParamArea_code());
			pk.setSource_id(getParamSource_id());
			pk.setStaff_number(getParamStaff_number());
			selectedImportProject = dao.getImportRIProjectByPK(pk);
		}
		
		return selectedImportProject;
	}
	
	//validate name list
	public boolean validateProjectDetails_P(String staff_no)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String errMessage = "msg.err.mandatory.x";
		String message;
		boolean result = true;
		boolean yourself = false;
		int countPI = 0;
		int countPI2 = 0;
		String concatName = "";
		int maxPI = 31;
		HashSet unique=new HashSet();
		if (!Strings.isNullOrEmpty(staff_no)) {
			for (ProjectDetails_P p:projectDetails_p_list) {
				int lineNo = countPI2 + 1;
				if("PRINCIPAL INVESTIGATOR".equals(p.getInvestigator_type())) {
					countPI++;
				}
				
					
				//get staff details
				if (p.getNon_ied_staff_flag().equals("N")) {
					if (Strings.isNullOrEmpty(p.getInvestigator_staff_no())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Investigator (no. "+lineNo+")");
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					}else {
						StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getInvestigator_staff_no());
						if (s != null) {
							p.setInvestigator_name(s.getFullname_save());
						}
					}
				}
				//get past staff details
				if (p.getNon_ied_staff_flag().equals("F")) {
					if (p.getInvestigator_name() == null) {
						result = false;
						message = "Investigator (no. "+lineNo+") is not correct.";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					}else {
						StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getInvestigator_staff_no());
						if (sp != null) {
							//p.setInvestigator_staff_no(sp.getStaff_number());
							//p.setInvestigator_name(sp.getFullname_save());
						}else {
							result = false;
							//message = MessageFormat.format(getResourceBundle().getString(errMessage), "Investigator (no. "+lineNo+")");
							message = "Investigator (no. "+lineNo+") is not correct.";
							fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
						}
					}
				}
				
				//check name is not null
				if (p.getNon_ied_staff_flag().equals("Y") || p.getNon_ied_staff_flag().equals("S")) {
					if (Strings.isNullOrEmpty(p.getInvestigator_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Investigator (no. "+lineNo+")");
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					}else {
						byte[] nameBytes = p.getInvestigator_name().getBytes(StandardCharsets.UTF_8);
						if (nameBytes.length > 80) {
							result = false;
							message = "Investigator (no. "+lineNo+") is too long.";
							fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
						}else {
							p.setInvestigator_person_id(null);
							p.setInvestigator_staff_no(null);
							p.setInvestigator_assignment_id(null);
						}	
					}
				}
				
				//check duplicate
				if (!unique.add(p.getInvestigator_staff_no()) && p.getInvestigator_staff_no() != null){
					result = false;
					if (!Strings.isNullOrEmpty(p.getInvestigator_name())) {
						message = "Investigator - Staff ("+p.getInvestigator_name()+") cannot be duplicated.";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					}else {
						message = "Investigator cannot be duplicated.";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					}
				}
				
				if (countPI2 < maxPI) {
					String tmpStaffNo = (p.getInvestigator_staff_no() == null)?", ":" (" + p.getInvestigator_staff_no() + "), ";
					concatName += p.getInvestigator_name() + tmpStaffNo + p.getInvestigator_type() + "; ";
				}
				if (countPI2 == maxPI) {
					concatName += "*";
				}
				
				if (!Strings.isNullOrEmpty(p.getInvestigator_staff_no())) {
					if (staff_no.equals(p.getInvestigator_staff_no())) {
						yourself = true;
					}
				}
				
				countPI2++;
			}
		}
		if (yourself == false && "M".equals(getParamDataLevel())) {		
			result = false;
			message = "You must be one of the members in Investigator.";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}
		if (yourself == false && "M".equals(getParamDataLevel()) == false) {		
			result = false;
			message = "Creator must be one of the members in Investigator.";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}
		if (countPI < 1) {		
			result = false;
			message = "There must be at least one Principal Investigator.";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}
		if (selectedProjectHeader_p != null) {
			if (!concatName.isEmpty()) {
				if (concatName.length() > 900) {
					concatName = StringUtils.substring(concatName, 0, 900);
					concatName += "*";
				}
			}
			selectedProjectHeader_p.setTotal_no_of_investigator(countPI2);
			selectedProjectHeader_p.setConcatenated_inv_name(concatName);
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	//validation
	public boolean convertOverseas() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		String institution = "";
		String city = "";
		String country = "";
		if (projectOverseas_list != null && "Y".equals(selectedProjectHeader_p.getCollab_partner_non_ins())) {
			int i = 0;
			for (ProjectOverseas o:projectOverseas_list) {		
				if (o.getInstitution() != null) {
					o.setInstitution(o.getInstitution().replace("_", "＿"));
				}
				if (o.getCity() != null) {
					o.setCity(o.getCity().replace("_", "＿"));
				}
				if (o.getCountry() != null) {
					o.setCountry(o.getCountry().replace("_", "＿"));
				}
				institution += o.getInstitution();
				city += o.getCity();
				country += o.getCountry();
				
				if(++i < projectOverseas_list.size()) {
					institution += "_";
					city += "_";
					country += "_";
				}
			}
		}
		if (institution.length() > 4000 || city.length() > 4000 || country.length() > 4000) {
			result = false;
			message = "There are too many overseas institutions.";
			fCtx.addMessage("editForm:overseasTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}else {
			selectedProjectHeader_p.setName_of_collaborative_partner(institution);
			selectedProjectHeader_p.setCollaborative_partner_city(city);
			selectedProjectHeader_p.setCollaborative_partner_country(country);
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	//validation
	public boolean convertResearchOutput() {
		String message;
		boolean result = true;
		String outputTitle = "";
		String outputType = "";
		String outputInst = "";
		String outputDate = "";
		DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
		if (researchOutput_list != null && "Y".equals(selectedProjectHeader_p.getCollab_partner_non_ins())) {
			int i = 0;
			for (ResearchOutput o:researchOutput_list) {
				if (o.getOutputTitle() != null) {
					o.setOutputTitle(o.getOutputTitle().replace("_", "＿"));
				}
				if (o.getOutputType() != null) {
					o.setOutputType(o.getOutputType().replace("_", "＿"));
				}
				if (o.getOutputInst() != null) {
					o.setOutputInst(o.getOutputInst().replace("_", "＿"));
				}
				
				outputTitle += o.getOutputTitle();
				outputType += o.getOutputType();
				outputInst += o.getOutputInst();
				outputDate += dateFormat.format(o.getOutputDate());
				
				if(++i < researchOutput_list.size()) {
					outputTitle += "_";
					outputType += "_";
					outputInst += "_";
					outputDate += "_";
				}
			}
		}
		if (outputTitle.length() > 4000 || outputInst.length() > 4000) {
			result = false;
			FacesContext fCtx = FacesContext.getCurrentInstance();
			message = "There are too many research outputs.";
			fCtx.addMessage("editForm:outputTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}else {
			selectedProjectHeader_p.setCollab_output_title(outputTitle);
			selectedProjectHeader_p.setCollab_output_type(outputType);
			selectedProjectHeader_p.setCollab_output_inst(outputInst);
			selectedProjectHeader_p.setCollab_output_date(outputDate);
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}

	//validation
	public boolean validateFunding() {
		String message;
		boolean result = true;
		if (selectedProjectHeader_p != null) {
			if (selectedProjectHeader_p.getSap_grant_amt() != null && selectedProjectHeader_p.getReleased_val() != null) {
				if (selectedProjectHeader_p.getSap_grant_amt() < selectedProjectHeader_p.getReleased_val()) {
					result = false;
					FacesContext fCtx = FacesContext.getCurrentInstance();
					message = "Actual Amount cannot greater than Funding Amount.";
					fCtx.addMessage("editForm:released_val", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;	
	}
	
	public boolean validateDisArea()
	{	
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message;
		if (selectedProjectHeader_p != null) {
			if (selectedProjectHeader_p.getSch_dtl_code() != null && selectedProjectHeader_p.getDa_dtl_code() != null) {
				String eduParentLookupCode = getEduSectorParentLookupCode(selectedProjectHeader_p.getSch_dtl_code());
				String disAreaParentLookupCode = getDisAreaParentLookupCode(selectedProjectHeader_p.getDa_dtl_code());
				if ("3".equals(eduParentLookupCode) && !"EDU".equals(disAreaParentLookupCode)) {
					result = false;
				}
				if (!"3".equals(eduParentLookupCode) && "EDU".equals(disAreaParentLookupCode)) {
					result = false;
				}
			}
		}
		if (!result) {
			hasError = true;
			message = "Sector and Disciplinary Area of the Output are not matched.";
			fCtx.addMessage("editForm:da_dtl_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}
		return result;
	}
	
	public boolean validateLength()
	{	
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message;
		if (selectedProjectHeader_p != null) {
			//Project Title 1
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getTitle_1()) == false) {
				byte[] bytes = selectedProjectHeader_p.getTitle_1().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 200) {
					result = false;
					message = "1st line of Project Title is too long.";
					fCtx.addMessage("editForm:title1", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedProjectHeader_p.setTitle_1(selectedProjectHeader_p.getTitle_1().trim());
				}
			}
			//Project Title 2
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getTitle_2()) == false) {
				byte[] bytes = selectedProjectHeader_p.getTitle_2().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 200) {
					result = false;
					message = "2nd line of Project Title is too long.";
					fCtx.addMessage("editForm:title2", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedProjectHeader_p.setTitle_2(selectedProjectHeader_p.getTitle_2().trim());
				}
			}
			//Project Title 3
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getTitle_3()) == false) {
				byte[] bytes = selectedProjectHeader_p.getTitle_3().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 200) {
					result = false;
					message = "3rd line of Project Title is too long.";
					fCtx.addMessage("editForm:title3", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedProjectHeader_p.setTitle_3(selectedProjectHeader_p.getTitle_3().trim());
				}
			}
			//Project Title 4
			if (Strings.isNullOrEmpty(selectedProjectHeader_p.getTitle_4()) == false) {
				byte[] bytes = selectedProjectHeader_p.getTitle_4().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 200) {
					result = false;
					message = "4th line of Project Title is too long.";
					fCtx.addMessage("editForm:title4", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedProjectHeader_p.setTitle_4(selectedProjectHeader_p.getTitle_4().trim());
				}
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	
	//validation
	public boolean validateProjectDate() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String message;
		boolean result = true;
		if (selectedProjectHeader_p != null) {
			if (selectedProjectHeader_p.getFrom_year() != null && selectedProjectHeader_p.getTo_year() != null) {
				if (selectedProjectHeader_p.getFrom_year() > selectedProjectHeader_p.getTo_year()) {
					message = "Commencement Date cannot be greater than (Expected) Completion Date.";
					fCtx.addMessage("editForm:fromYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					result = false;
				}else if (selectedProjectHeader_p.getFrom_year().equals(selectedProjectHeader_p.getTo_year())){
					if (selectedProjectHeader_p.getFrom_month() != null && selectedProjectHeader_p.getTo_month() != null) {
						if (selectedProjectHeader_p.getFrom_month() > selectedProjectHeader_p.getTo_month()) {
							message = "Commencement Date cannot be greater than (Expected) Completion Date.";
							fCtx.addMessage("editForm:fromYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
							result = false;
						}else if (selectedProjectHeader_p.getFrom_month().equals(selectedProjectHeader_p.getTo_month())){
							if (selectedProjectHeader_p.getFrom_day() != null && selectedProjectHeader_p.getTo_day() != null) {
								if (selectedProjectHeader_p.getFrom_day() > selectedProjectHeader_p.getTo_day()) {
									message = "Commencement Date cannot be greater than (Expected) Completion Date.";
									fCtx.addMessage("editForm:fromYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
									fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
									result = false;
								}
							}
						}
					}
				}
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;	
	}
	
	//validate mandatory field	
	public Boolean validateRequiredField() {
		boolean result = true;
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		//Generation Information
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getProj_type())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Project Type");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Project Type")+"<br/>";
			fCtx.addMessage("editForm:project_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getTech_proj())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Is it an Innovation/Technology (創科) related project?");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Is it an Innovation/Technology (創科) related project?")+"<br/>";
			fCtx.addMessage("editForm:tech_proj", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getRgc_proj_num()) && "RGC".equals(selectedProjectHeader_p.getFunding_body())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Project number of the RGC funded project(s)");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Project number of the RGC funded project(s)")+"<br/>";
			fCtx.addMessage("editForm:rgc_proj_num", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		//Project Information
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getTitle_1())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "1st line of Project Title");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "1st line of Project Title")+"<br/>";
			fCtx.addMessage("editForm:title1", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedProjectHeader_p.setTitle_1(selectedProjectHeader_p.getTitle_1().trim());
		}
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getProject_summary())) {
			if (getIsRdoAdmin() == false) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Brief Description/Summary of Project");
				allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Brief Description/Summary of Project")+"<br/>";
				fCtx.addMessage("editForm:project_summary", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}else {
			selectedProjectHeader_p.setProject_summary(selectedProjectHeader_p.getProject_summary().trim());
		}
		if (selectedProjectHeader_p.getFrom_month()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Commencement Date - Month");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Commencement Date - Month")+"<br/>";
			fCtx.addMessage("editForm:fromMonth", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedProjectHeader_p.getFrom_year()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Commencement Date - Year");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Commencement Date - Year")+"<br/>";
			fCtx.addMessage("editForm:fromYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedProjectHeader_p.getTo_month()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "(Expected) Commencement Date - Month");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "(Expected) Commencement Date - Month")+"<br/>";
			fCtx.addMessage("editForm:toMonth", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedProjectHeader_p.getTo_year()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "(Expected) Commencement Date - Year");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "(Expected) Commencement Date - Year")+"<br/>";
			fCtx.addMessage("editForm:toYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		if (projectOverseas_list != null && "Y".equals(selectedProjectHeader_p.getCollab_partner_non_ins())) {
			int lineNo = 1;
			for (ProjectOverseas o:projectOverseas_list) {
				if (Strings.isNullOrEmpty(o.getInstitution())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Collaborative Partner(s) (Institution) (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Collaborative Partner(s) (Institution) (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:overseasTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (Strings.isNullOrEmpty(o.getCity())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Collaborative Partner(s) (City) (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Collaborative Partner(s) (City) (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:overseasTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (Strings.isNullOrEmpty(o.getCountry())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Collaborative Partner(s) (Country) (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Collaborative Partner(s) (Country) (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:overseasTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				lineNo++;
			}
		}
		
		if (researchOutput_list != null && "Y".equals(selectedProjectHeader_p.getCollab_partner_non_ins())) {
			int lineNo = 1;
			for (ResearchOutput o:researchOutput_list) {
				if (Strings.isNullOrEmpty(o.getOutputTitle())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Title (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Title (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:outputTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (Strings.isNullOrEmpty(o.getOutputType())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Research output type (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Research output type (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:outputTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (Strings.isNullOrEmpty(o.getOutputInst())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Name (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Name (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:outputTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (o.getOutputDate()==null) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Actual / Expected date (no. "+lineNo+")");
					allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Details of Research Output(s) - Actual / Expected date (no. "+lineNo+")")+"<br/>";
					fCtx.addMessage("editForm:outputTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			lineNo++;
		}
		
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_body()) && "Y".equals(selectedProjectHeader_p.getFunded_proj())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Body");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Body")+"<br/>";
			fCtx.addMessage("editForm:funding_body", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getSap_funding_source()) && "Y".equals(selectedProjectHeader_p.getFunded_proj())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Source");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Source")+"<br/>";
			fCtx.addMessage("editForm:sap_funding_source", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//other funding
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_others()) && "Y".equals(selectedProjectHeader_p.getFunded_proj()) && requireFundingName(selectedProjectHeader_p.getSap_funding_source())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Funding");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Funding")+"<br/>";
			fCtx.addMessage("editForm:funding_others", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//other funding
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getFunding_org()) && "Y".equals(selectedProjectHeader_p.getFunded_proj()) && requireFundingOrg(selectedProjectHeader_p.getSap_funding_source())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Organization");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Organization")+"<br/>";
			fCtx.addMessage("editForm:funding_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedProjectHeader_p.getSap_grant_amt()==null && "Y".equals(selectedProjectHeader_p.getFunded_proj())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Amount");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Funding Amount")+"<br/>";
			fCtx.addMessage("editForm:sap_grant_amt", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		/*if (selectedProjectHeader_p.getReleased_val()==null && "Y".equals(selectedProjectHeader_p.getFunded_proj())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Actual Amount");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Actual Amount")+"<br/>";
			fCtx.addMessage("editForm:released_val", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}*/
		
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getSch_dtl_code())  && getIsRdoAdmin() == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Sector of the Project");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Sector of the Project")+"<br/>";
			fCtx.addMessage("editForm:sch_dtl_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getDa_dtl_code())  && getIsRdoAdmin() == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Disciplinary Area of the Output");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Disciplinary Area of the Output")+"<br/>";
			fCtx.addMessage("editForm:da_dtl_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedProjectHeader_p.getOther_da_dtl()) && isOtherDisArea()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Other Disciplinary Area");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Other Disciplinary Area")+"<br/>";
			fCtx.addMessage("editForm:other_da_dtl", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
}


