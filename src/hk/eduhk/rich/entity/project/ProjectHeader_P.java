package hk.eduhk.rich.entity.project;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;

@Entity
@Table(name = "RH_P_RESEARCH_PROJECT_HDR")
public class ProjectHeader_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(ProjectHeader_P.class.toString());
	
	@EmbeddedId
	private ProjectHeader_P_PK pk = new ProjectHeader_P_PK();
	
	@Column(name = "title_1")
	private String title_1;

	@Column(name = "title_2")
	private String title_2;	
	
	@Column(name = "title_3")
	private String title_3;

	@Column(name = "title_4")
	private String title_4;
	
	@Column(name = "project_summary")
	private String project_summary;
	
	@Column(name = "project_summary_2")
	private String project_summary_2;
	
	@Column(name = "from_day")
	private Integer from_day;
	
	@Column(name = "from_month")
	private Integer from_month;
	
	@Column(name = "from_year")
	private Integer from_year;
	
	@Column(name = "to_day")
	private Integer to_day;
	
	@Column(name = "to_month")
	private Integer to_month;
	
	@Column(name = "to_year")
	private Integer to_year;
	
	@Column(name = "sap_funding_source")
	private String sap_funding_source;
	
	@Column(name = "user_select")
	private String user_select;
	
	@Column(name = "census_date")
	private Date census_date;
	
	@Column(name = "role_inst")
	private String role_inst;
	
	@Column(name = "released_val")
	private Double released_val;
	
	@Column(name = "sap_grant_amt")
	private Double sap_grant_amt;
	
	@Column(name = "sch_code")
	private String sch_code;
	
	@Column(name = "sch_dtl_code")
	private String sch_dtl_code;
	
	@Column(name = "rs_code")
	private String rs_code;
	
	@Column(name = "rs_dtl_code")
	private String rs_dtl_code;
	
	@Column(name = "total_no_of_investigator")
	private Integer total_no_of_investigator;
	
	@Column(name = "concatenated_inv_name")
	private String concatenated_inv_name;
	
	@Column(name = "activity_code")
	private String activity_code;
	
	@Column(name = "key_research_areas")
	private String key_research_areas;
	
	@Column(name = "other_key_research_areas")
	private String other_key_research_areas;
	
	@Column(name = "name_of_collaborative_partner")
	private String name_of_collaborative_partner;
	
	@Column(name = "collaborative_partner_country")
	private String collaborative_partner_country;
	
	@Column(name = "collaborative_partner_city")
	private String collaborative_partner_city;
	
	@Column(name = "da_code")
	private String da_code;
	
	@Column(name = "da_dtl_code")
	private String da_dtl_code;
	
	@Column(name = "other_da_dtl")
	private String other_da_dtl;

	@Column(name = "ied_work_ind")
	private String ied_work_ind;

	@Column(name = "keywords")
	private String keywords;

	@Column(name = "proj_type")
	private String proj_type;

	@Column(name = "collab_output_title")
	private String collab_output_title;

	@Column(name = "collab_output_type")
	private String collab_output_type;

	@Column(name = "collab_output_inst")
	private String collab_output_inst;

	@Column(name = "collab_output_date")
	private String collab_output_date;

	@Column(name = "collab_partner_ugc")
	private String collab_partner_ugc;

	@Column(name = "collab_partner_non_ins")
	private String collab_partner_non_ins;

	@Column(name = "role_inst_for_t690")
	private String role_inst_for_t690;

	@Column(name = "funding_body")
	private String funding_body;

	@Column(name = "funding_others")
	private String funding_others;

	@Column(name = "funding_org")
	private String funding_org;

	@Column(name = "funded_proj")
	private String funded_proj;
	
	@Column(name = "tech_proj")
	private String tech_proj;
	
	@Column(name = "rgc_proj_num")
	private String rgc_proj_num;
	
	@Column(name = "sdg_code")
	private String sdg_code;
	
	@Transient
	private String sdg_info;
	
	@Transient
	private List<String> sdg_code_list;
	
	

	public String getSdg_code()
	{
		return sdg_code;
	}


	
	public void setSdg_code(String sdg_code)
	{
		this.sdg_code = sdg_code;
	}

	
	
	public List<String> getSdg_code_list()
	{
		if (sdg_code != null && sdg_code_list == null )
			sdg_code_list =  Stream.of(getSdg_code().split(",")).collect(Collectors.toList());
		
		return sdg_code_list;
	}


	
	public void setSdg_code_list(List<String> sdg_code_list)
	{
		this.sdg_code_list = sdg_code_list;
	}
	

	
	public String getSdg_info()
	{
		if (getSdg_code_list() != null) {
			List<String> sdg_info_list = new ArrayList<String>();
			
			for(String sdg_code : sdg_code_list)
				sdg_info_list.add(sdg_code+" - "+LookupValueDAO.getInstance().getLookupValue("SDG",sdg_code,"US").getDescription()) ;
			
			sdg_info = String.join(", ", sdg_info_list);
		}
		return sdg_info;
	}



	
	public void setSdg_info(String sdg_info)
	{
		this.sdg_info = sdg_info;
	}



	public ProjectHeader_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(ProjectHeader_P_PK pk)
	{
		this.pk = pk;
	}


	
	public String getTitle_1()
	{
		return title_1;
	}


	
	public void setTitle_1(String title_1)
	{
		this.title_1 = title_1;
	}


	
	public String getTitle_2()
	{
		return title_2;
	}


	
	public void setTitle_2(String title_2)
	{
		this.title_2 = title_2;
	}


	
	public String getTitle_3()
	{
		return title_3;
	}


	
	public void setTitle_3(String title_3)
	{
		this.title_3 = title_3;
	}


	
	public String getTitle_4()
	{
		return title_4;
	}


	
	public void setTitle_4(String title_4)
	{
		this.title_4 = title_4;
	}


	
	public String getProject_summary()
	{
		return project_summary;
	}


	
	public void setProject_summary(String project_summary)
	{
		this.project_summary = project_summary;
	}


	
	public String getProject_summary_2()
	{
		return project_summary_2;
	}


	
	public void setProject_summary_2(String project_summary_2)
	{
		this.project_summary_2 = project_summary_2;
	}


	
	public Integer getFrom_day()
	{
		return from_day;
	}


	
	public void setFrom_day(Integer from_day)
	{
		this.from_day = from_day;
	}


	
	public Integer getFrom_month()
	{
		return from_month;
	}


	
	public void setFrom_month(Integer from_month)
	{
		this.from_month = from_month;
	}


	
	public Integer getFrom_year()
	{
		return from_year;
	}


	
	public void setFrom_year(Integer from_year)
	{
		this.from_year = from_year;
	}


	
	public Integer getTo_day()
	{
		return to_day;
	}


	
	public void setTo_day(Integer to_day)
	{
		this.to_day = to_day;
	}


	
	public Integer getTo_month()
	{
		return to_month;
	}


	
	public void setTo_month(Integer to_month)
	{
		this.to_month = to_month;
	}


	
	public Integer getTo_year()
	{
		return to_year;
	}


	
	public void setTo_year(Integer to_year)
	{
		this.to_year = to_year;
	}


	
	public String getSap_funding_source()
	{
		return sap_funding_source;
	}


	
	public void setSap_funding_source(String sap_funding_source)
	{
		this.sap_funding_source = sap_funding_source;
	}


	
	public String getUser_select()
	{
		return user_select;
	}


	
	public void setUser_select(String user_select)
	{
		this.user_select = user_select;
	}


	
	public Date getCensus_date()
	{
		return census_date;
	}


	
	public void setCensus_date(Date census_date)
	{
		this.census_date = census_date;
	}


	
	public String getRole_inst()
	{
		return role_inst;
	}


	
	public void setRole_inst(String role_inst)
	{
		this.role_inst = role_inst;
	}


	
	public Double getReleased_val()
	{
		return released_val;
	}


	
	public void setReleased_val(Double released_val)
	{
		this.released_val = released_val;
	}


	
	public Double getSap_grant_amt()
	{
		return sap_grant_amt;
	}


	
	public void setSap_grant_amt(Double sap_grant_amt)
	{
		this.sap_grant_amt = sap_grant_amt;
	}


	
	public String getSch_code()
	{
		return sch_code;
	}


	
	public void setSch_code(String sch_code)
	{
		this.sch_code = sch_code;
	}


	
	public String getSch_dtl_code()
	{
		return sch_dtl_code;
	}


	
	public void setSch_dtl_code(String sch_dtl_code)
	{
		this.sch_dtl_code = sch_dtl_code;
	}


	
	public String getRs_code()
	{
		return rs_code;
	}


	
	public void setRs_code(String rs_code)
	{
		this.rs_code = rs_code;
	}


	
	public String getRs_dtl_code()
	{
		return rs_dtl_code;
	}


	
	public void setRs_dtl_code(String rs_dtl_code)
	{
		this.rs_dtl_code = rs_dtl_code;
	}


	
	public Integer getTotal_no_of_investigator()
	{
		return total_no_of_investigator;
	}


	
	public void setTotal_no_of_investigator(Integer total_no_of_investigator)
	{
		this.total_no_of_investigator = total_no_of_investigator;
	}


	
	public String getConcatenated_inv_name()
	{
		return concatenated_inv_name;
	}


	
	public void setConcatenated_inv_name(String concatenated_inv_name)
	{
		this.concatenated_inv_name = concatenated_inv_name;
	}


	
	public String getActivity_code()
	{
		return activity_code;
	}


	
	public void setActivity_code(String activity_code)
	{
		this.activity_code = activity_code;
	}


	
	public String getKey_research_areas()
	{
		return key_research_areas;
	}


	
	public void setKey_research_areas(String key_research_areas)
	{
		this.key_research_areas = key_research_areas;
	}


	
	public String getOther_key_research_areas()
	{
		return other_key_research_areas;
	}


	
	public void setOther_key_research_areas(String other_key_research_areas)
	{
		this.other_key_research_areas = other_key_research_areas;
	}


	
	public String getName_of_collaborative_partner()
	{
		return name_of_collaborative_partner;
	}


	
	public void setName_of_collaborative_partner(String name_of_collaborative_partner)
	{
		this.name_of_collaborative_partner = name_of_collaborative_partner;
	}


	
	public String getCollaborative_partner_country()
	{
		return collaborative_partner_country;
	}


	
	public void setCollaborative_partner_country(String collaborative_partner_country)
	{
		this.collaborative_partner_country = collaborative_partner_country;
	}


	
	public String getCollaborative_partner_city()
	{
		return collaborative_partner_city;
	}


	
	public void setCollaborative_partner_city(String collaborative_partner_city)
	{
		this.collaborative_partner_city = collaborative_partner_city;
	}


	
	public String getDa_code()
	{
		return da_code;
	}


	
	public void setDa_code(String da_code)
	{
		this.da_code = da_code;
	}


	
	public String getDa_dtl_code()
	{
		return da_dtl_code;
	}


	
	public void setDa_dtl_code(String da_dtl_code)
	{
		this.da_dtl_code = da_dtl_code;
	}


	
	public String getOther_da_dtl()
	{
		return other_da_dtl;
	}


	
	public void setOther_da_dtl(String other_da_dtl)
	{
		this.other_da_dtl = other_da_dtl;
	}


	
	public String getIed_work_ind()
	{
		return ied_work_ind;
	}


	
	public void setIed_work_ind(String ied_work_ind)
	{
		this.ied_work_ind = ied_work_ind;
	}


	
	public String getKeywords()
	{
		return keywords;
	}


	
	public void setKeywords(String keywords)
	{
		this.keywords = keywords;
	}


	
	public String getProj_type()
	{
		return proj_type;
	}


	
	public void setProj_type(String proj_type)
	{
		this.proj_type = proj_type;
	}


	
	public String getCollab_output_title()
	{
		return collab_output_title;
	}


	
	public void setCollab_output_title(String collab_output_title)
	{
		this.collab_output_title = collab_output_title;
	}


	
	public String getCollab_output_type()
	{
		return collab_output_type;
	}


	
	public void setCollab_output_type(String collab_output_type)
	{
		this.collab_output_type = collab_output_type;
	}


	
	public String getCollab_output_inst()
	{
		return collab_output_inst;
	}


	
	public void setCollab_output_inst(String collab_output_inst)
	{
		this.collab_output_inst = collab_output_inst;
	}


	
	public String getCollab_output_date()
	{
		return collab_output_date;
	}


	
	public void setCollab_output_date(String collab_output_date)
	{
		this.collab_output_date = collab_output_date;
	}


	
	public String getCollab_partner_ugc()
	{
		return collab_partner_ugc;
	}


	
	public void setCollab_partner_ugc(String collab_partner_ugc)
	{
		this.collab_partner_ugc = collab_partner_ugc;
	}


	
	public String getCollab_partner_non_ins()
	{
		return collab_partner_non_ins;
	}


	
	public void setCollab_partner_non_ins(String collab_partner_non_ins)
	{
		this.collab_partner_non_ins = collab_partner_non_ins;
	}


	
	public String getRole_inst_for_t690()
	{
		return role_inst_for_t690;
	}


	
	public void setRole_inst_for_t690(String role_inst_for_t690)
	{
		this.role_inst_for_t690 = role_inst_for_t690;
	}


	
	public String getFunding_body()
	{
		return funding_body;
	}


	
	public void setFunding_body(String funding_body)
	{
		this.funding_body = funding_body;
	}


	
	public String getFunding_others()
	{
		return funding_others;
	}


	
	public void setFunding_others(String funding_others)
	{
		this.funding_others = funding_others;
	}


	
	public String getFunding_org()
	{
		return funding_org;
	}


	
	public void setFunding_org(String funding_org)
	{
		this.funding_org = funding_org;
	}


	
	public String getFunded_proj()
	{
		return funded_proj;
	}


	
	public void setFunded_proj(String funded_proj)
	{
		this.funded_proj = funded_proj;
	}


	
	public String getTech_proj()
	{
		return tech_proj;
	}


	
	public void setTech_proj(String tech_proj)
	{
		this.tech_proj = tech_proj;
	}


	
	public String getRgc_proj_num()
	{
		return rgc_proj_num;
	}


	
	public void setRgc_proj_num(String rgc_proj_num)
	{
		this.rgc_proj_num = rgc_proj_num;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProjectHeader_P other = (ProjectHeader_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "ProjectHeader_P [pk=" + pk + ", title_1=" + title_1 + ", title_2=" + title_2 + ", title_3=" + title_3
				+ ", title_4=" + title_4 + ", project_summary=" + project_summary + ", project_summary_2="
				+ project_summary_2 + ", from_day=" + from_day + ", from_month=" + from_month + ", from_year="
				+ from_year + ", to_day=" + to_day + ", to_month=" + to_month + ", to_year=" + to_year
				+ ", sap_funding_source=" + sap_funding_source + ", user_select=" + user_select + ", census_date="
				+ census_date + ", role_inst=" + role_inst + ", released_val=" + released_val + ", sap_grant_amt="
				+ sap_grant_amt + ", sch_code=" + sch_code + ", sch_dtl_code=" + sch_dtl_code + ", rs_code=" + rs_code
				+ ", rs_dtl_code=" + rs_dtl_code + ", total_no_of_investigator=" + total_no_of_investigator
				+ ", concatenated_inv_name=" + concatenated_inv_name + ", activity_code=" + activity_code
				+ ", key_research_areas=" + key_research_areas + ", other_key_research_areas="
				+ other_key_research_areas + ", name_of_collaborative_partner=" + name_of_collaborative_partner
				+ ", collaborative_partner_country=" + collaborative_partner_country + ", collaborative_partner_city="
				+ collaborative_partner_city + ", da_code=" + da_code + ", da_dtl_code=" + da_dtl_code
				+ ", other_da_dtl=" + other_da_dtl + ", ied_work_ind=" + ied_work_ind + ", keywords=" + keywords
				+ ", proj_type=" + proj_type + ", collab_output_title=" + collab_output_title + ", collab_output_type="
				+ collab_output_type + ", collab_output_inst=" + collab_output_inst + ", collab_output_date="
				+ collab_output_date + ", collab_partner_ugc=" + collab_partner_ugc + ", collab_partner_non_ins="
				+ collab_partner_non_ins + ", role_inst_for_t690=" + role_inst_for_t690 + ", funding_body="
				+ funding_body + ", funding_others=" + funding_others + ", funding_org=" + funding_org
				+ ", funded_proj=" + funded_proj + ", tech_proj=" + tech_proj + ", rgc_proj_num=" + rgc_proj_num
				+ ", sdg_code=" + sdg_code + ", sdg_info=" + sdg_info + ", sdg_code_list=" + sdg_code_list + "]";
	}



	
}
