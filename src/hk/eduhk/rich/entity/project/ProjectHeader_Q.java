package hk.eduhk.rich.entity.project;

import java.util.Date;
import java.sql.Timestamp;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@Entity
@Table(name = "RH_Q_RESEARCH_PROJECT_HDR")
public class ProjectHeader_Q extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(ProjectHeader_Q.class.toString());
	
	@Id
	@GeneratedValue(generator = "riProHeadSeq")
	@SequenceGenerator(name = "riProHeadSeq", sequenceName = "RH_RI_NO_SEQ", allocationSize = 1)
	@Column(name = "project_no")
	private int project_no;
	
	@Column(name = "publish_status")      
	private String publish_status;
	                                      
	@Column(name = "last_modified_date")  
	private Date last_modified_date;
	                                      
	@Column(name = "last_modified_by")    
	private String last_modified_by;
	                                      
	@Column(name = "last_published_date") 
	private Date last_published_date;
	                                      
	@Column(name = "last_published_by")   
	private String last_published_by;
	                                      
	@Column(name = "inst_display_ind")    
	private String inst_display_ind;
	                                      
	@Column(name = "inst_verified_ind")   
	private String inst_verified_ind;
	                                      
	@Column(name = "inst_verified_date")  
	private Date inst_verified_date;
	                                      
	@Column(name = "cdcf_status")         
	private String cdcf_status;
	                                      
	@Column(name = "cdcf_gen_ind")        
	private String cdcf_gen_ind;
	                                      
	@Column(name = "cdcf_gen_date")       
	private Date cdcf_gen_date;
	                                      
	@Column(name = "cdcf_processed_ind")  
	private String cdcf_processed_ind;
	                                      
	@Column(name = "cdcf_processed_date") 
	private Date cdcf_processed_date;
	                                      
	@Column(name = "cdcf_selected_ind")   
	private String cdcf_selected_ind;
	                                      
	@Column(name = "cdcf_changed_ind")    
	private String cdcf_changed_ind;
	                                      
	@Column(name = "remarks")             
	private String remarks;
	                                      
	@Column(name = "major_proj_ind")      
	private String major_proj_ind;
	                                      
	@Column(name = "bulletin_ind")        
	private String bulletin_ind;
	                                      
	@Column(name = "publish_freq")        
	private Integer publish_freq;
	                                      
	@Column(name = "major_proj_url")      
	private String major_proj_url;
	                                      
	@Column(name = "major_proj_photo")    
	private byte[] major_proj_photo;
	                                      
	@Column(name = "collab_t630")         
	private String collab_t630;
	                                      
	@Column(name = "collab_t690")         
	private String collab_t690;



	
	public int getProject_no()
	{
		return project_no;
	}


	
	public void setProject_no(int project_no)
	{
		this.project_no = project_no;
	}


	
	public String getPublish_status()
	{
		return publish_status;
	}


	
	public void setPublish_status(String publish_status)
	{
		this.publish_status = publish_status;
	}


	
	public Date getLast_modified_date()
	{
		return last_modified_date;
	}


	
	public void setLast_modified_date(Date last_modified_date)
	{
		this.last_modified_date = last_modified_date;
	}


	
	public String getLast_modified_by()
	{
		return last_modified_by;
	}


	
	public void setLast_modified_by(String last_modified_by)
	{
		this.last_modified_by = last_modified_by;
	}


	
	public Date getLast_published_date()
	{
		return last_published_date;
	}


	
	public void setLast_published_date(Date last_published_date)
	{
		this.last_published_date = last_published_date;
	}


	
	public String getLast_published_by()
	{
		return last_published_by;
	}


	
	public void setLast_published_by(String last_published_by)
	{
		this.last_published_by = last_published_by;
	}


	
	public String getInst_display_ind()
	{
		return inst_display_ind;
	}


	
	public void setInst_display_ind(String inst_display_ind)
	{
		this.inst_display_ind = inst_display_ind;
	}


	
	public String getInst_verified_ind()
	{
		return inst_verified_ind;
	}


	
	public void setInst_verified_ind(String inst_verified_ind)
	{
		this.inst_verified_ind = inst_verified_ind;
	}


	
	public Date getInst_verified_date()
	{
		return inst_verified_date;
	}


	
	public void setInst_verified_date(Date inst_verified_date)
	{
		this.inst_verified_date = inst_verified_date;
	}


	
	public String getCdcf_status()
	{
		return cdcf_status;
	}


	
	public void setCdcf_status(String cdcf_status)
	{
		this.cdcf_status = cdcf_status;
	}


	
	public String getCdcf_gen_ind()
	{
		return cdcf_gen_ind;
	}


	
	public void setCdcf_gen_ind(String cdcf_gen_ind)
	{
		this.cdcf_gen_ind = cdcf_gen_ind;
	}


	
	public Date getCdcf_gen_date()
	{
		return cdcf_gen_date;
	}


	
	public void setCdcf_gen_date(Date cdcf_gen_date)
	{
		this.cdcf_gen_date = cdcf_gen_date;
	}


	
	public String getCdcf_processed_ind()
	{
		return cdcf_processed_ind;
	}


	
	public void setCdcf_processed_ind(String cdcf_processed_ind)
	{
		this.cdcf_processed_ind = cdcf_processed_ind;
	}


	
	public Date getCdcf_processed_date()
	{
		return cdcf_processed_date;
	}


	
	public void setCdcf_processed_date(Date cdcf_processed_date)
	{
		this.cdcf_processed_date = cdcf_processed_date;
	}


	
	public String getCdcf_selected_ind()
	{
		return cdcf_selected_ind;
	}


	
	public void setCdcf_selected_ind(String cdcf_selected_ind)
	{
		this.cdcf_selected_ind = cdcf_selected_ind;
	}


	
	public String getCdcf_changed_ind()
	{
		return cdcf_changed_ind;
	}


	
	public void setCdcf_changed_ind(String cdcf_changed_ind)
	{
		this.cdcf_changed_ind = cdcf_changed_ind;
	}


	
	public String getRemarks()
	{
		return remarks;
	}


	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	
	public String getMajor_proj_ind()
	{
		return major_proj_ind;
	}


	
	public void setMajor_proj_ind(String major_proj_ind)
	{
		this.major_proj_ind = major_proj_ind;
	}


	
	public String getBulletin_ind()
	{
		return bulletin_ind;
	}


	
	public void setBulletin_ind(String bulletin_ind)
	{
		this.bulletin_ind = bulletin_ind;
	}


	
	public Integer getPublish_freq()
	{
		return publish_freq;
	}


	
	public void setPublish_freq(Integer publish_freq)
	{
		this.publish_freq = publish_freq;
	}


	
	public String getMajor_proj_url()
	{
		return major_proj_url;
	}


	
	public void setMajor_proj_url(String major_proj_url)
	{
		this.major_proj_url = major_proj_url;
	}

	public byte[] getMajor_proj_photo()
	{
		return major_proj_photo;
	}

	public void setMajor_proj_photo(byte[] major_proj_photo)
	{
		this.major_proj_photo = major_proj_photo;
	}



	public String getCollab_t630()
	{
		return collab_t630;
	}


	
	public void setCollab_t630(String collab_t630)
	{
		this.collab_t630 = collab_t630;
	}


	
	public String getCollab_t690()
	{
		return collab_t690;
	}


	
	public void setCollab_t690(String collab_t690)
	{
		this.collab_t690 = collab_t690;
	}



	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + project_no;
		return result;
	}



	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProjectHeader_Q other = (ProjectHeader_Q) obj;
		if (project_no != other.project_no)
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "ProjectHeader_Q [project_no=" + project_no + ", publish_status=" + publish_status
				+ ", last_modified_date=" + last_modified_date + ", last_modified_by=" + last_modified_by
				+ ", last_published_date=" + last_published_date + ", last_published_by=" + last_published_by
				+ ", inst_display_ind=" + inst_display_ind + ", inst_verified_ind=" + inst_verified_ind
				+ ", inst_verified_date=" + inst_verified_date + ", cdcf_status=" + cdcf_status + ", cdcf_gen_ind="
				+ cdcf_gen_ind + ", cdcf_gen_date=" + cdcf_gen_date + ", cdcf_processed_ind=" + cdcf_processed_ind
				+ ", cdcf_processed_date=" + cdcf_processed_date + ", cdcf_selected_ind=" + cdcf_selected_ind
				+ ", cdcf_changed_ind=" + cdcf_changed_ind + ", remarks=" + remarks + ", major_proj_ind="
				+ major_proj_ind + ", bulletin_ind=" + bulletin_ind + ", publish_freq=" + publish_freq
				+ ", major_proj_url=" + major_proj_url + ", major_proj_photo=" + major_proj_photo + ", collab_t630="
				+ collab_t630 + ", collab_t690=" + collab_t690 + "]";
	}



}
