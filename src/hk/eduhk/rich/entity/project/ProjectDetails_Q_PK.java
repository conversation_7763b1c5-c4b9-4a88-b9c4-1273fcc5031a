package hk.eduhk.rich.entity.project;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class ProjectDetails_Q_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="project_no")
	private Integer project_no;
	
	@Column(name="staff_no")
	private String staff_no;

	
	public Integer getProject_no()
	{
		return project_no;
	}

	
	public void setProject_no(Integer project_no)
	{
		this.project_no = project_no;
	}

	
	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((project_no == null) ? 0 : project_no.hashCode());
		result = prime * result + ((staff_no == null) ? 0 : staff_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProjectDetails_Q_PK other = (ProjectDetails_Q_PK) obj;
		if (project_no == null)
		{
			if (other.project_no != null)
				return false;
		}
		else if (!project_no.equals(other.project_no))
			return false;
		if (staff_no == null)
		{
			if (other.staff_no != null)
				return false;
		}
		else if (!staff_no.equals(other.staff_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ProjectDetails_Q_PK [project_no=" + project_no + ", staff_no=" + staff_no + "]";
	}

	

}
