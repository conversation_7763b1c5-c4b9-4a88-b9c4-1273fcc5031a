package hk.eduhk.rich.entity.project;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;


import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.io.IOUtils;
import org.jdom2.*;
import org.jdom2.output.*;
import org.jdom2.output.support.*;
import org.jdom2.transform.JDOMSource;

import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.util.MimeMap;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.BaseView;


@SuppressWarnings("serial")
@ManagedBean(name = "genProjectXmlView")
@ViewScoped
public class GenProjectXmlView extends BaseView
{
	private Logger logger = Logger.getLogger(getClass().getName());
	public String createProjectXmlFile() throws IOException
	{		
		String xmlString = "";
		try
		{
			Document doc=new Document();
			//<v1:upmprojects xmlns:v1="v1.upmproject.pure.atira.dk" xmlns:v3="v3.commons.pure.atira.dk">
			//Root Element
			Namespace ns1 = Namespace.getNamespace("v1", "v1.upmproject.pure.atira.dk");	
			Namespace ns2 = Namespace.getNamespace("v3", "v3.commons.pure.atira.dk");
			Element root=new Element("upmprojects", ns1);
			root.addNamespaceDeclaration(ns2);
			doc.setRootElement(root);
			
			ProjectDAO dao = ProjectDAO.getInstance();
			List<ProjectHeader> projectList = dao.getProjectList();
			for(ProjectHeader p:projectList) {
				//Project Element
				Element project=new Element("upmproject", ns1);
				project.setAttribute("id", "pj"+p.getProject_no());
				project.setAttribute("type", p.getProject_type());
				
				//Activity Types
				Element activityTypes=new Element("activityTypes", ns1);
				activityTypes.addContent(new Element("activityType", ns1).addContent(p.getProject_type()));
				project.addContent(activityTypes);
				
				//Title
				Element title = new Element("title", ns1);
				Element title_text = new Element("text", ns2);
				title_text.setAttribute("lang", "en");
				title_text.setAttribute("country","GB");
				title_text.addContent(p.getTitle());
				title.addContent(title_text);
				project.addContent(title);
				
				//Description
				Element descriptions = new Element("descriptions", ns1);
				Element description = new Element("description", ns2);
				description.setAttribute("type", "projectdescription");
				Element description_text = new Element("text", ns2);
				description_text.setAttribute("lang", "en");
				description_text.setAttribute("country","GB");
				description_text.addContent(skipInValidXMLChars(p.getProject_summary())+" "+skipInValidXMLChars(p.getproject_summary_2()));
				description_text.addContent("<br/><br/>Funding Source: " +  p.getFunding_source());
				description.addContent(description_text);
				descriptions.addContent(description);								
				project.addContent(descriptions);
				
				//ids
				Element ids = new Element("ids", ns1);
				ids.addContent(new Element("id", ns2).setAttribute("type", "projectid").addContent("pj"+p.getProject_no()));
				project.addContent(ids);
				
				//Get Project Details
				List<ProjectDetails> projectDetails = dao.getPureProjectDetails(p.getProject_no());
				
				//Internal Participants
				Element internalParticipants = new Element("internalParticipants", ns1);
				boolean hasInternalParticipant = false;
				for(ProjectDetails d:projectDetails) {
					
					if (d.getExternal_ind().equals("N")) {
						Element internalParticipant = new Element("internalParticipant", ns1);
						internalParticipant.addContent(new Element("personId", ns1).addContent(d.getPerson_source_id()));
						internalParticipant.addContent(new Element("role", ns1).addContent(d.getInvestigator_type()));
						internalParticipants.addContent(internalParticipant);
						hasInternalParticipant = true;
					}
				}
				if (hasInternalParticipant) {
					project.addContent(internalParticipants);
				}
				
				//External Participants
				Element externalParticipants = new Element("externalParticipants", ns1);
				boolean hasEnternalParticipant = false;
				for(ProjectDetails d:projectDetails) {
					if (d.getExternal_ind().equals("Y")) {
						Element externalParticipant = new Element("externalParticipant", ns1);
						externalParticipant.addContent(new Element("firstName", ns1).addContent(d.getFirst_name()));
						externalParticipant.addContent(new Element("lastName", ns1).addContent(d.getLast_name()));
						externalParticipant.addContent(new Element("role", ns1).addContent(d.getInvestigator_type()));
						externalParticipants.addContent(externalParticipant);
						hasEnternalParticipant = true;
					}
				}	
				if (hasEnternalParticipant) {
					project.addContent(externalParticipants);
				}
				
				//managedByOrganisation
				Element managedByOrganisation = new Element("managedByOrganisation", ns1);
				managedByOrganisation.setAttribute("id", "u00001");
				project.addContent(managedByOrganisation);
				
				//startDate
				Element startDate = new Element("startDate", ns1);
				startDate.addContent(p.getStart_date());
				project.addContent(startDate);
				
				//endDate
				Element endDate = new Element("endDate", ns1);
				endDate.addContent(p.getEnd_date());
				project.addContent(endDate);
				
				//keywords
				String keywordString = p.getKeywords();
				List<String> keywordList = (keywordString!=null)?Arrays.asList(keywordString.split(";")):null;
				if (keywordList != null) {
					Element keywords = new Element("keywords", ns1);
					Element logicalGroup = new Element("logicalGroup", ns2);
					logicalGroup.setAttribute("logicalName", "keywordContainers");
					Element structuredKeywords = new Element("structuredKeywords", ns2);
					Element structuredKeyword = new Element("structuredKeyword", ns2);
					Element freeKeywords = new Element("freeKeywords", ns2);
					
					for (String k:keywordList) {
						Element freeKeyword = new Element("freeKeyword", ns2);
						Element freeKeyword_text = new Element("text", ns2);
						freeKeyword_text.setAttribute("lang", "en");
						freeKeyword_text.setAttribute("country","GB");
						freeKeyword_text.addContent(k);
						freeKeyword.addContent(freeKeyword_text);
						freeKeywords.addContent(freeKeyword);
					}
					
					structuredKeyword.addContent(freeKeywords);
					structuredKeywords.addContent(structuredKeyword);
					logicalGroup.addContent(structuredKeywords);
					keywords.addContent(logicalGroup);
					
				
					
					if (p.getSdg_code() != null && p.getSdg_code().length() != 0) {
						logicalGroup = new Element("logicalGroup", ns2);
						logicalGroup.setAttribute("logicalName", "sustainabledevelopmentgoals");
						structuredKeywords = new Element("structuredKeywords", ns2);
						for (String sdg:p.getSdg_list()) {
							structuredKeyword = new Element("structuredKeyword", ns2);
							String sdg_str = sdg.toLowerCase().replace(" ", "_");
							sdg_str = sdg_str.replace("-", "_");
							structuredKeyword.setAttribute("classification", "/dk/atira/pure/sustainabledevelopmentgoals/"+sdg_str);
							structuredKeywords.addContent(structuredKeyword);
						}
						logicalGroup.addContent(structuredKeywords);
						keywords.addContent(logicalGroup);
					}

					project.addContent(keywords);
				}
				
				//visibility
				Element childVisibility = new Element("visibility", root.getNamespace());
				childVisibility.addContent("Public");
				project.addContent(childVisibility);
				
				//Add in the root Element
				root.addContent(project);		
			}
			xmlString = transform(doc);
		}
		catch (TransformerFactoryConfigurationError | TransformerException e)
		{
			logger.log(Level.WARNING, "Cannot generate xml", e);
		}
		return xmlString;
	}
	
	private String transform(Document sourceDoc) throws TransformerException {
        JDOMSource source = new JDOMSource(sourceDoc);
        StreamResult result = new StreamResult(new StringWriter());

        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
        transformer.transform(source, result);

        return result.getWriter().toString();
    }
	
	private String getXmlFilePath()
	{
		String code = Constant.isLocalEnv() ? SysParam.PARAM_FILE_PATH_LOCAL : SysParam.PARAM_FILE_PATH;
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		return paramDAO.getSysParamValueByCode(code);
	}
	
	public String getSysParamValue(String code) {
		String value = "";
		SysParamDAO sysParamDao = SysParamDAO.getInstance();
		SysParam tmp = sysParamDao.getSysParamByCode(code);
		if (tmp != null) {
			value = tmp.getValue();
		}
		return value;
	}
	
	public String skipInValidXMLChars(String in) {
        StringBuffer out = new StringBuffer();
        char current;

        if (in == null || ("".equals(in))) return "";
        for (int i = 0; i < in.length(); i++) {
            current = in.charAt(i);
            if ((current == 0x9) ||
                (current == 0xA) ||
                (current == 0xD) ||
                ((current >= 0x20) && (current <= 0xD7FF)) ||
                ((current >= 0xE000) && (current <= 0xFFFD)) ||
                ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }  
	
	public static final XMLOutputProcessor XMLOUTPUT = new AbstractXMLOutputProcessor() {
	    @Override
	    protected void printDeclaration(final Writer out, final FormatStack fstack) throws IOException {
	        write(out, "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?> ");
	        write(out, fstack.getLineSeparator());
	    }
	};
	
}
