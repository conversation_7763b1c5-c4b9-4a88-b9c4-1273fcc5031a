package hk.eduhk.rich.entity.project;

import java.io.Serializable;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.entity.BaseRI;

@SuppressWarnings("serial")
public class ProjectOverseas
{
	private String institution;
	private String city;
	private String country;


	public ProjectOverseas()
	{
	}


	
	public String getInstitution()
	{
		return institution;
	}


	
	public void setInstitution(String institution)
	{
		this.institution = institution;
	}


	
	public String getCity()
	{
		return city;
	}


	
	public void setCity(String city)
	{
		this.city = city;
	}


	
	public String getCountry()
	{
		return country;
	}


	
	public void setCountry(String country)
	{
		this.country = country;
	}



	@Override
	public String toString()
	{
		return "ProjectOverseas [institution=" + institution + ", city=" + city + ", country=" + country + "]";
	}


}