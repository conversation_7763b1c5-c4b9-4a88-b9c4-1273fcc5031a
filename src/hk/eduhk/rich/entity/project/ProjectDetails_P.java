package hk.eduhk.rich.entity.project;

import java.sql.SQLException;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.publication.PublicationDAO;

@Entity
@Table(name = "RH_P_RESEARCH_PROJECT_DTL")
public class ProjectDetails_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(ProjectDetails_P.class.toString());
	
	@EmbeddedId
	private ProjectDetails_P_PK pk = new ProjectDetails_P_PK();
	
	@Column(name = "investigator_name")
	private String investigator_name;	
	
	@Column(name = "investigator_type")
	private String investigator_type;	
	
	@Column(name = "non_ied_staff_flag")
	private String non_ied_staff_flag;	
	
	@Column(name = "investigator_person_id")
	private Integer investigator_person_id;
	
	@Column(name = "investigator_assignment_id")
	private Integer investigator_assignment_id;

	@Column(name = "investigator_staff_no")
	private String investigator_staff_no;	
	
	@Column(name = "collab_percent")
	private Integer collab_percent;

	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "project_no", referencedColumnName = "project_no", nullable = false, insertable = false, updatable = false)
	private ProjectHeader_Q projectHeader_q;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "project_no", referencedColumnName = "project_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", nullable = false, insertable = false, updatable = false)
	})
	private ProjectHeader_P projectHeader_p;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "project_no", referencedColumnName = "project_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "investigator_staff_no", referencedColumnName = "staff_no", nullable = false, insertable = false, updatable = false)
	})
	private ProjectDetails_Q projectDetails_q;
	
	@Transient
	private String fullName;

	
	public ProjectDetails_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(ProjectDetails_P_PK pk)
	{
		this.pk = pk;
	}

	
	public String getInvestigator_name()
	{
		return investigator_name;
	}

	
	public void setInvestigator_name(String investigator_name)
	{
		this.investigator_name = investigator_name;
	}

	
	public String getInvestigator_type()
	{
		return investigator_type;
	}

	
	public void setInvestigator_type(String investigator_type)
	{
		this.investigator_type = investigator_type;
	}

	
	public String getNon_ied_staff_flag()
	{
		return non_ied_staff_flag;
	}

	
	public void setNon_ied_staff_flag(String non_ied_staff_flag)
	{
		this.non_ied_staff_flag = non_ied_staff_flag;
	}

	
	public Integer getInvestigator_person_id()
	{
		return investigator_person_id;
	}

	
	public void setInvestigator_person_id(Integer investigator_person_id)
	{
		this.investigator_person_id = investigator_person_id;
	}

	
	public Integer getInvestigator_assignment_id()
	{
		return investigator_assignment_id;
	}

	
	public void setInvestigator_assignment_id(Integer investigator_assignment_id)
	{
		this.investigator_assignment_id = investigator_assignment_id;
	}

	
	public String getInvestigator_staff_no()
	{
		return investigator_staff_no;
	}

	
	public void setInvestigator_staff_no(String investigator_staff_no)
	{
		this.investigator_staff_no = investigator_staff_no;
	}

	
	public Integer getCollab_percent()
	{
		return collab_percent;
	}

	
	public void setCollab_percent(Integer collab_percent)
	{
		this.collab_percent = collab_percent;
	}

	
	public ProjectHeader_Q getProjectHeader_q()
	{
		if (projectHeader_q != null) {
			try {
				projectHeader_q.getProject_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					ProjectDAO dao = ProjectDAO.getInstance();
					projectHeader_q = dao.getProjectHeader_Q(getPk().getProject_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return projectHeader_q;
	}

	
	public ProjectHeader_P getProjectHeader_p()
	{
		if (projectHeader_p != null) {
			try {
				projectHeader_p.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					ProjectDAO dao = ProjectDAO.getInstance();
					projectHeader_p = dao.getProjectHeader_P(getPk().getProject_no(), getPk().getData_level());
				}
				else
				{
					throw re;
				}
			}
		}
		return projectHeader_p;
	}

	
	public ProjectDetails_Q getProjectDetails_q()
	{
		if (projectDetails_q != null) {
			try {
				projectDetails_q.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException") ||
					re.getClass().getName().equals("javax.persistence.EntityNotFoundException"))
				{
					// Handle case where ProjectDetails_Q entity doesn't exist or can't be loaded
					ProjectDAO dao = ProjectDAO.getInstance();
					projectDetails_q = dao.getProjectDetails_Q1(getPk().getProject_no(), getInvestigator_staff_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return projectDetails_q;
	}

	/**
	 * Safe method to get creator indicator that handles missing entities
	 */
	public String getProjectDetails_q_CreatorInd()
	{
		try {
			ProjectDetails_Q details = getProjectDetails_q();
			return (details != null) ? details.getCreator_ind() : null;
		} catch (RuntimeException re) {
			if (re.getClass().getName().equals("org.hibernate.LazyInitializationException") ||
				re.getClass().getName().equals("javax.persistence.EntityNotFoundException")) {
				// Return null for missing entities
				return null;
			}
			throw re;
		}
	}

	/**
	 * Safe method to get consent indicator that handles missing entities
	 */
	public String getProjectDetails_q_ConsentInd()
	{
		try {
			ProjectDetails_Q details = getProjectDetails_q();
			return (details != null) ? details.getConsent_ind() : null;
		} catch (RuntimeException re) {
			if (re.getClass().getName().equals("org.hibernate.LazyInitializationException") ||
				re.getClass().getName().equals("javax.persistence.EntityNotFoundException")) {
				// Return null for missing entities
				return null;
			}
			throw re;
		}
	}

	
	public String getFullName()
	{
		if(fullName == null) {
			ProjectDAO dao = ProjectDAO.getInstance();
			fullName = dao.getEligibleFullNme(pk.getProject_no(), pk.getLine_no(), pk.getData_level());
		}
		return fullName;
	}


	
	public void setFullName(String fullName)
	{
		this.fullName = fullName;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProjectDetails_P other = (ProjectDetails_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ProjectDetails_P [pk=" + pk + ", investigator_name=" + investigator_name + ", investigator_type="
				+ investigator_type + ", non_ied_staff_flag=" + non_ied_staff_flag + ", investigator_person_id="
				+ investigator_person_id + ", investigator_assignment_id=" + investigator_assignment_id
				+ ", investigator_staff_no=" + investigator_staff_no + ", collab_percent=" + collab_percent + "]";
	}
	

}
