package hk.eduhk.rich.entity.project;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import hk.eduhk.rich.entity.BaseRIFull;

@SuppressWarnings("serial")
public class ProjectReport
{

	private String dept;
	private Integer countStaff;
	private Integer num;
	private Double value;
	
	public String getDept()
	{
		return dept;
	}
	
	public void setDept(String dept)
	{
		this.dept = dept;
	}
	
	
	public Integer getCountStaff()
	{
		return countStaff;
	}

	
	public void setCountStaff(Integer countStaff)
	{
		this.countStaff = countStaff;
	}

	public Integer getNum()
	{
		return num;
	}
	
	public void setNum(Integer num)
	{
		this.num = num;
	}
	
	public Double getValue()
	{
		return value;
	}
	
	public void setValue(Double value)
	{
		this.value = value;
	}

	@Override
	public String toString()
	{
		return "ProjectReport [dept=" + dept + ", countStaff=" + countStaff + ", num=" + num + ", value=" + value + "]";
	}


}