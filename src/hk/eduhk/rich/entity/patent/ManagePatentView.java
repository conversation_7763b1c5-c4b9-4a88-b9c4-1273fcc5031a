package hk.eduhk.rich.entity.patent;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.award.AwardDetails_P;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIPatentV;
import hk.eduhk.rich.entity.importRI.ImportRIPatentV_PK;
import hk.eduhk.rich.entity.importRI.ImportRIStatus;
import hk.eduhk.rich.entity.project.ProjectDetails_Q;
import hk.eduhk.rich.entity.publication.OutputDetails_Q;
import hk.eduhk.rich.entity.staff.Assistant;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.view.ImportRIPatent;


@ManagedBean(name = "managePatentView")
@ViewScoped
@SuppressWarnings("serial")
public class ManagePatentView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(Patent.class.getName());
	
	private List<PatentDetails_P> patentList;
	private List<PatentDetails_P> patentDetails_p_list;
	
	private String riCreatorStaffNo;
	private Boolean isCreator;
	private Boolean isContributor;
	private Boolean canDelete;
	private Boolean hasSAP;
	private Boolean hasError;
	private Boolean saved;
	
	private PatentDetails_Q selectedPatentDetails_q;
	private PatentHeader_P selectedPatentHeader_p;
	private PatentHeader_Q selectedPatentHeader_q;
	private List<PatentCountry> country_list;
	private List<PatentType> type_list;
	private List<String> dayList;
	private List<SelectItem> cdcfStatusList;
	
	private PatentDAO pDao = PatentDAO.getInstance();
	
	private ImportRIPatent patentPanel;
	private ImportRIPatentV selectedImportPatent;

	public void checkValid(ComponentSystemEvent event) throws IOException
	{
		paramDataLevel = getParamDataLevel();
		String message = "";
		if (!getIsRdoAdmin()) {
			if ((getIsCreator() == false && getIsContributor() == false) || !"M".equals(paramDataLevel)) {
				message = "You don't have access right.";	
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, getLoginUserId()+" doesn't have access right");
			}
		}
	}
	
	public Boolean hasAccessRight() 
	{
		Boolean result = false;
		if ("M".equals(paramDataLevel)){
			if (getIsCreator() || getIsContributor()) {
				result = true;
			}
		}
		if ("P".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		if ("C".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		return result;
	}
	
	public Boolean canViewRIList() 
	{
		Boolean result = false;
		if (staffDetail == null) {
			if (getIsAsst() || getIsRdoAdmin()) {
				staffDetail = getStaffDetail(getParamPid(), null, true);
				//check is Former Staff
				staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
				if (staffPastDetail != null) {
					staffDetail = new StaffIdentity();
					staffDetail.setPid(staffPastDetail.getPid());
					staffDetail.setStaff_number(staffPastDetail.getStaff_number());
					staffDetail.setCn(staffPastDetail.getCn());
					staffDetail.setStaffType("F");
				}
			}else {
				staffDetail = getStaffDetail(null, null, false);
			}
		}
		if (staffDetail != null) {
			if (Strings.isNullOrEmpty(paramPid))
				paramPid = String.valueOf(staffDetail.getPid());
			if (getIsAsst() || getIsRdoAdmin() || getCurrentUserId().equals(staffDetail.getCn())){
					result = true;
			}
		}
		return result;
	}
	
	public Boolean getCanDelete()
	{
		if (canDelete == null) {
			canDelete = false;
			if (getIsCreator() && !Strings.isNullOrEmpty(paramNo)) {
				/*if (getSelectedPatentHeader_q() != null) {
					if ("CDCF_PENDING".equals(selectedPatentHeader_q.getCdcf_status())) {
						PatentHeader_P selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedPatentHeader_p != null) {
							canDelete = true;
						}
					}
				}*/
				PatentHeader_P selectedPatentHeader_p_c = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "C");
				if (selectedPatentHeader_p_c == null) {
					PatentHeader_P selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "M");
					if (selectedPatentHeader_p != null) {
						canDelete = true;
					}
				}
			}
		}
		return canDelete;
	}

	
	public void setCanDelete(Boolean canDelete)
	{
		this.canDelete = canDelete;
	}
	
	
	public Boolean getHasSAP()
	{
		if (hasSAP == null) {
			hasSAP = false;
		}
		return hasSAP;
	}

	
	public void setHasSAP(Boolean hasSAP)
	{
		this.hasSAP = hasSAP;
	}

	public Boolean getIsCreator()
	{
		if (isCreator == null) {
			isCreator = false;
			selectedPatentDetails_q = getSelectedPatentDetails_q();
			if (selectedPatentDetails_q != null) {
				isCreator = ("Y".equals(selectedPatentDetails_q.getCreator_ind()))?true:false;
				if (!Strings.isNullOrEmpty(getParamNo())) {
					PatentDetails_Q currentCreator = pDao.getPatentDetails_Q_creator(Integer.valueOf(getParamNo()));
					if (isCreator && currentCreator != null) {
						if (currentCreator.getPk().getStaff_no().equals(selectedPatentDetails_q.getPk().getStaff_no())) {
							isCreator = true;
						}else {
							isCreator = false;
						}
					}
				}
			}
			if(paramNo == null) {
				isCreator = true;
			}
			if (getIsRdoAdmin()) {
				isCreator = true;
			}
		}
		return isCreator;
	}
	
	public void setIsCreator(Boolean isCreator)
	{
		this.isCreator = isCreator;
	}

	public Boolean getIsContributor()
	{
		if (isContributor == null) {
			isContributor = false;
			selectedPatentDetails_q = getSelectedPatentDetails_q();
			if (selectedPatentDetails_q != null) {
				isContributor = ("N".equals(selectedPatentDetails_q.getCreator_ind()))?true:false;
			}
		}
		return isContributor;
	}

	public void setIsContributor(Boolean isContributor)
	{
		this.isContributor = isContributor;
	}	
	
	public Boolean getHasError()
	{
		if (hasError == null) {
			hasError = false;
		}
		return hasError;
	}

	public void setHasError(Boolean hasError)
	{
		this.hasError = hasError;
	}
	public Boolean getSaved()
	{
		if (saved == null) {
			saved = false;
		}
		return saved;
	}

	
	public void setSaved(Boolean saved)
	{
		this.saved = saved;
	}
	
	public List<SelectItem> getCdcfStatusList()
	{
		cdcfStatusList = new ArrayList<SelectItem>();
		
		cdcfStatusList.add(optionPending);
		if ("CDCF_PENDING".equals(selectedPatentHeader_q.getCdcf_status()))
				cdcfStatusList.add(optionProcessed);
		if (!"CDCF_PENDING".equals(selectedPatentHeader_q.getCdcf_status()))
			cdcfStatusList.add(optionGenerated);
		cdcfStatusList.add(optionNotSelected);
		return cdcfStatusList;
	}
	
	public void setCdcfStatusList(List<SelectItem> cdcfStatusList)
	{
		this.cdcfStatusList = cdcfStatusList;
	}
	
	public List<PatentDetails_P> getPatentList()
	{
		if (patentList == null && canViewRIList()) {
			patentList = pDao.getPatentDetails_P_byStaffNo(staffDetail.getStaff_number(), "M");
			if (!Strings.isNullOrEmpty(getParamConsent())) {
				if (!"all".equals(paramConsent)) {
					patentList = patentList.stream()
							.filter(y -> paramConsent.equals(y.getPatentDetails_q().getConsent_ind()) && y.getPatentHeader_q().getPublish_freq() > 0)
							.collect(Collectors.toList());
				}
			}else {
				patentList.removeIf(y -> "N".equals(y.getPatentDetails_q().getCreator_ind()) && y.getPatentHeader_q().getPublish_freq() < 1);
			}
		}
		return patentList;
	}
	
	public long getTotalCount() {
		if (getPatentList() != null) {
			return patentList.stream().count();
		}else {
			return 0;
		}	
    }
	
	public List<PatentDetails_P> getPatentDetails_p_list()
	{
		if (patentDetails_p_list == null) {
			patentDetails_p_list = new ArrayList<PatentDetails_P>();
			if (!Strings.isNullOrEmpty(paramNo)) {
				patentDetails_p_list = pDao.getPatentDetails_P(Integer.valueOf(paramNo), getParamDataLevel());
				for (PatentDetails_P d:patentDetails_p_list) {
					if ("F".equals(d.getNon_ied_staff_flag())){
						StaffPast tmp = staffDao.getPastStaffDetailsByStaffNo(d.getInventor_staff_no());
						if (tmp != null) {
							d.setInventor_name(tmp.getFullname_display());
						}else {
							d.setInventor_name("");
						}
					}
					if ("N".equals(d.getNon_ied_staff_flag())){
						StaffIdentity tmp = staffDao.getStaffDetailsByStaffNo(d.getInventor_staff_no());
						if (tmp != null) {
							d.setInventor_name(tmp.getFullname_display());
						}else {
							d.setInventor_name("");
						}
					}
				}
			}
			else if (getSelectedImportPatent() != null) {
				List<String> cnList = Arrays.asList(selectedImportPatent.getInventor_id_list().split(";"));
		    	for(String cn : cnList) {
		    		StaffIdentity staff = null;
					PatentDetails_P tmp = new PatentDetails_P();
			    	tmp.getPk().setData_level("M");
			    	tmp.setNon_ied_staff_flag("F");
			    	for(StaffIdentity id : getStaffNameList()) {
			    		if(id.getCn().equals(cn)) {
			    			tmp.setNon_ied_staff_flag("N");
			    			staff = id;
			    			break;
			    		}
			    	}
			    	if(staff != null) {
				    	tmp.setInventor_staff_no(staff.getStaff_number());
				    	patentDetails_p_list.add(tmp);
			    	}
		    	}
			}
			else {
				if (getIsAsst() || getIsRdoAdmin()) {
					staffDetail = getStaffDetail(getParamPid(), null, true);
					//check is Former Staff
					staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
					if (staffPastDetail != null) {
						staffDetail = new StaffIdentity();
						staffDetail.setPid(staffPastDetail.getPid());
						staffDetail.setStaff_number(staffPastDetail.getStaff_number());
						staffDetail.setCn(staffPastDetail.getCn());
						staffDetail.setStaffType("F");
					}
				}else {
					staffDetail = getStaffDetail(null, null, false);
				}
				PatentDetails_P tmp = new PatentDetails_P();
				if (staffDetail != null) {
			    	tmp.getPk().setData_level("M");
			    	if ("F".equals(staffDetail.getStaffType())){
			    		tmp.setNon_ied_staff_flag("F");
			    	}else {
			    		tmp.setNon_ied_staff_flag("N");
			    	}
			    	tmp.setInventor_staff_no(String.valueOf(staffDetail.getStaff_number()));
				}
		    	patentDetails_p_list.add(tmp);
			}
		}
		return patentDetails_p_list;
	}

	public void setPatentDetails_p_list(List<PatentDetails_P> patentDetails_p_list)
	{
		this.patentDetails_p_list = patentDetails_p_list;
	}
	
	public PatentHeader_P getSelectedPatentHeader_p() throws ParseException
	{
		if (selectedPatentHeader_p == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), getParamDataLevel());
				selectedPatentHeader_p = (selectedPatentHeader_p != null)?selectedPatentHeader_p:new PatentHeader_P();
			}else {
				selectedPatentHeader_p = new PatentHeader_P();
				if(getSelectedImportPatent() != null) {
					selectedPatentHeader_p.setPatent_granted(selectedImportPatent.getPatent_granted());
					selectedPatentHeader_p.setPatent_name(selectedImportPatent.getPatent_name());
					selectedPatentHeader_p.setSerial_no(selectedImportPatent.getSerial_no());
					selectedPatentHeader_p.setCountry(selectedImportPatent.getCountry());
					selectedPatentHeader_p.setFull_desc(selectedImportPatent.getFull_desc());
					if(StringUtils.isNotBlank(selectedImportPatent.getPatent_day()))
						selectedPatentHeader_p.setPatent_day(Integer.parseInt(selectedImportPatent.getPatent_day()));
					selectedPatentHeader_p.setPatent_month(Integer.parseInt(selectedImportPatent.getPatent_month()));
					selectedPatentHeader_p.setPatent_year(Integer.parseInt(selectedImportPatent.getPatent_year()));
				}
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_p.getPatent_granted())) {
				selectedPatentHeader_p.setPatent_granted("A");
			}
			if (selectedPatentHeader_p.getPatent_year() == null) {
				selectedPatentHeader_p.setPatent_year(getCurrentYear());
			}
		}
		return selectedPatentHeader_p;
	}

	
	public void setSelectedPatentHeader_p(PatentHeader_P selectedPatentHeader_p)
	{
		this.selectedPatentHeader_p = selectedPatentHeader_p;
	}
	
	public PatentHeader_Q getSelectedPatentHeader_q()
	{
		if (selectedPatentHeader_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedPatentHeader_q= pDao.getPatentHeader_Q(Integer.valueOf(paramNo));
				selectedPatentHeader_q = (selectedPatentHeader_q != null)?selectedPatentHeader_q:new PatentHeader_Q();
			}else {
				selectedPatentHeader_q = new PatentHeader_Q();
			}
			if ("Y".equals(selectedPatentHeader_q.getInst_verified_ind()) && selectedPatentHeader_q.getInst_verified_date() == null) {
				selectedPatentHeader_q.setInst_verified_date(getCurrentDate());
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getInst_display_ind())) {
				selectedPatentHeader_q.setInst_display_ind("Y");
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getInst_verified_ind())) {
				selectedPatentHeader_q.setInst_verified_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getCdcf_status())) {
				selectedPatentHeader_q.setCdcf_status("CDCF_PENDING");
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getCdcf_gen_ind())) {
				selectedPatentHeader_q.setCdcf_gen_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getCdcf_processed_ind())) {
				selectedPatentHeader_q.setCdcf_processed_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getCdcf_selected_ind())) {
				selectedPatentHeader_q.setCdcf_selected_ind("U");
			}
			if (Strings.isNullOrEmpty(selectedPatentHeader_q.getCdcf_changed_ind())) {
				selectedPatentHeader_q.setCdcf_changed_ind("N");
			}
			if (selectedPatentHeader_q.getPublish_freq() == null) {
				selectedPatentHeader_q.setPublish_freq(0);
			}
		}
		return selectedPatentHeader_q;
	}


	
	public void setSelectedPatentHeader_q(PatentHeader_Q selectedPatentHeader_q)
	{
		this.selectedPatentHeader_q = selectedPatentHeader_q;
	}

	public void setDisplyRI()
	{
		if (selectedPatentDetails_q != null) {
			if (!"Y".equals(selectedPatentDetails_q.getConsent_ind()))
				selectedPatentDetails_q.setDisplay_ind("N");
		}
	}
	
	public PatentDetails_Q getSelectedPatentDetails_q()
	{
		if (getIsAsst() || getIsRdoAdmin()) {
			staffDetail = getStaffDetail(getParamPid(), null, true);
			//check is Former Staff
			staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
			if (staffPastDetail != null) {
				staffDetail = new StaffIdentity();
				staffDetail.setPid(staffPastDetail.getPid());
				staffDetail.setStaff_number(staffPastDetail.getStaff_number());
				staffDetail.setCn(staffPastDetail.getCn());
				staffDetail.setStaffType("F");
			}
		}else {
			staffDetail = getStaffDetail(null, null, false);
		}
		if (staffDetail != null && selectedPatentDetails_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				List<PatentDetails_Q> tmp = pDao.getPatentDetails_Q(Integer.valueOf(paramNo), staffDetail.getStaff_number());
				selectedPatentDetails_q = (!tmp.isEmpty())?tmp.get(0):null;
			}
			else if(getSelectedImportPatent() != null) {
				riCreatorStaffNo = getSelectedImportPatent().getPk().getStaff_number();
				selectedPatentDetails_q = new PatentDetails_Q();
				selectedPatentDetails_q.getPk().setStaff_no(getSelectedImportPatent().getPk().getStaff_number());
				selectedPatentDetails_q.setCreator_ind("Y");
				selectedPatentDetails_q.setDisplay_ind("Y");
				selectedPatentDetails_q.setConsent_ind("Y");
			}
			if (selectedPatentDetails_q == null) {
				riCreatorStaffNo = staffDetail.getStaff_number();
				selectedPatentDetails_q = new PatentDetails_Q();
				selectedPatentDetails_q.getPk().setStaff_no(staffDetail.getStaff_number());
				selectedPatentDetails_q.setCreator_ind("Y");
				selectedPatentDetails_q.setDisplay_ind("Y");
				selectedPatentDetails_q.setConsent_ind("Y");
			}
		}
		return selectedPatentDetails_q;
	}


	
	public void setSelectedPatentDetails_q(PatentDetails_Q selectedPatentDetails_q)
	{
		this.selectedPatentDetails_q = selectedPatentDetails_q;
	}


	
	public List<String> getDayList()
	{
		int month = (selectedPatentHeader_p.getPatent_month() != null)?selectedPatentHeader_p.getPatent_month():1;
		int year = (selectedPatentHeader_p.getPatent_year() != null)?selectedPatentHeader_p.getPatent_year():Calendar.getInstance().get(Calendar.YEAR);
		YearMonth yearMonthObject = YearMonth.of(year, month);
		int daysInMonth = yearMonthObject.lengthOfMonth();
		dayList = new ArrayList<>();
		for (int d = 1; d <= daysInMonth; d++) {
			dayList.add(String.valueOf(d));
		}
		return dayList;
	}



	
	public List<PatentCountry> getCountry_list()
	{
		if (country_list == null) {
			country_list = new ArrayList<PatentCountry>();
			List<PatentCountry> lvOneList = pDao.getPatentCountryList(1);
			List<PatentCountry> lvTwoList = pDao.getPatentCountryList(2);
			for (PatentCountry o:lvOneList) {
				country_list.add(o);
				List<PatentCountry> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				country_list.addAll(tmpLvTwoList);
			}
		}
		return country_list;
	}

	
	public void setCountry_list(List<PatentCountry> country_list)
	{
		this.country_list = country_list;
	}


	public List<PatentType> getType_list()
	{
		if (type_list == null) {
			type_list = new ArrayList<PatentType>();
			List<PatentType> lvOneList = pDao.getPatentTypeList(1);
			List<PatentType> lvTwoList = pDao.getPatentTypeList(2);
			for (PatentType o:lvOneList) {
				type_list.add(o);
				List<PatentType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				for (PatentType t:tmpLvTwoList) {
					t.setDescription(t.getDescription().replaceAll("motors;", "motors;<br/>"));
					t.setDescription(t.getDescription().replaceAll("tables or jigs;", "tables or jigs;<br/>"));
					t.setDescription(t.getDescription().replaceAll("installations;", "installations;<br/>"));
					t.setDescription(t.getDescription().replaceAll("metallic material;", "metallic material;<br/>"));
					t.setDescription(t.getDescription().replaceAll("pump systems;", "pump systems;<br/>"));
					t.setDescription(t.getDescription().replaceAll("general;", "general;<br/>"));
					t.setDescription(t.getDescription().replaceAll("paper;", "paper;<br/>"));
					t.setDescription(t.getDescription().replaceAll("adhesives;", "adhesives;<br/>"));
					t.setDescription(t.getDescription().replaceAll("units;", "units;<br/>"));
					t.setDescription(t.getDescription().replaceAll("fluids;", "fluids;<br/>"));
					t.setDescription(t.getDescription().replaceAll("waves;", "waves;<br/>"));
				}
				type_list.addAll(tmpLvTwoList);
			}
		}
		return type_list;
	}

	
	public void setType_list(List<PatentType> type_list)
	{
		this.type_list = type_list;
	}

	public void moveColumnUp(int idx) throws SQLException
	{
		if (!getPatentDetails_p_list().isEmpty())
		{
			if (idx > 0)
			{
				PatentDetails_P tmp = patentDetails_p_list.get(idx-1);
				patentDetails_p_list.set(idx-1, patentDetails_p_list.get(idx));
				patentDetails_p_list.set(idx, tmp);
			}
		}
	}

	
	public void moveColumnDown(int idx) throws SQLException
	{
		if (getPatentDetails_p_list() != null)
		{
			if (idx+1 < patentDetails_p_list.size())
			{
				PatentDetails_P tmp = patentDetails_p_list.get(idx+1);
				patentDetails_p_list.set(idx+1, patentDetails_p_list.get(idx));
				patentDetails_p_list.set(idx, tmp);
			}
		}
	}	
	
	public void updateRowStaffNum(int idx) throws SQLException
	{
		if (patentDetails_p_list != null)
		{
			if (idx > -1)
			{
				PatentDetails_P tmp = patentDetails_p_list.get(idx);
				String staffNum = getPastStaffNumByStaffName(tmp.getInventor_name());
				tmp.setInventor_staff_no(staffNum);
				patentDetails_p_list.set(idx, tmp);
			}
		}
	}
	
	public void deleteRow(int idx) throws SQLException
	{
		if (!getPatentDetails_p_list().isEmpty())
		{
			if (idx < patentDetails_p_list.size()) patentDetails_p_list.remove(idx);
		}
	}
	public void addRow() throws SQLException
	{
		PatentDetails_P p = new PatentDetails_P();
		p.getPk().setPatent_no(selectedPatentHeader_q.getPatent_no());
		p.getPk().setData_level(getParamDataLevel());
		p.setNon_ied_staff_flag("N");
		if (!getPatentDetails_p_list().isEmpty())
		{
			patentDetails_p_list.add(p);
		}else {
			patentDetails_p_list = new ArrayList<PatentDetails_P>();
			patentDetails_p_list.add(p);
		}
	}
	
	public void save() throws Exception
	{
		if (selectedPatentHeader_p != null && selectedPatentHeader_q != null && selectedPatentDetails_q != null) {
			hasError = false;
			if ("M".equals(getParamDataLevel())) {
				save_m(false);			
			}
			if ("P".equals(getParamDataLevel())) {
				save_p();
			}
			if ("C".equals(getParamDataLevel())) {
				save_c();
			}
		}
	}
	
	//save and publish
	public void saveAndPublishForm() throws IOException
	{
		if (selectedPatentHeader_p != null && selectedPatentHeader_q != null && selectedPatentDetails_q != null) {
			hasError = false;
			save_m(true);
			publish();
		}
	}
		
	//M level
	public void save_m(boolean doPublish)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		saved = false;
		try {
			selectedPatentHeader_q.setPublish_status("MODIFIED");
			selectedPatentHeader_q.setLast_modified_by(getLoginUserId());
			selectedPatentHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
			
			selectedPatentHeader_p.getPk().setData_level("M");
			
			selectedPatentHeader_p.setUserstamp(getLoginUserId());
			selectedPatentHeader_q.setUserstamp(getLoginUserId());
			selectedPatentDetails_q.setUserstamp(getLoginUserId());
			
			//Record still can be saved even there is error
			validateRequiredField();
			validateCountry();
			
			if (validatePatentDetails_P(staffDetail.getStaff_number())) {
				//Update P, Q
				selectedPatentHeader_q = pDao.updateEntity(selectedPatentHeader_q);

				int currentPatentNo = selectedPatentHeader_q.getPatent_no();
				
				selectedPatentDetails_q.getPk().setPatent_no(currentPatentNo);
				selectedPatentDetails_q = pDao.updateEntity(selectedPatentDetails_q);

				selectedPatentHeader_p.getPk().setPatent_no(currentPatentNo);
				selectedPatentHeader_p = pDao.updateEntity(selectedPatentHeader_p);
				
				//delete all inventor in M levels
				pDao.deleteAllInventor(currentPatentNo, "M");

				//Update patent name list
				int line_no = 1;
				for(PatentDetails_P p:patentDetails_p_list) {
					p.getPk().setPatent_no(currentPatentNo);
					p.getPk().setData_level("M");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getNon_ied_staff_flag().equals("N") && p.getInventor_staff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getInventor_staff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setInventor_name(nList.get(0));
						}
					}
					//set past staff details
					if (p.getNon_ied_staff_flag().equals("F") && p.getInventor_staff_no() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getStaff_number().equals(p.getInventor_staff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setInventor_name(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					pDao.updateEntity(p);
					
					//Create record in patentDetails_q if has staff number
					if (p.getInventor_staff_no() != null) {
						PatentDetails_Q tmpDetailsQ = pDao.getPatentDetails_Q1(currentPatentNo, p.getInventor_staff_no());
						if (tmpDetailsQ.getPk().getPatent_no() == null) {
							PatentDetails_Q newDetailsQ = new PatentDetails_Q();
							newDetailsQ.getPk().setPatent_no(currentPatentNo);
							newDetailsQ.getPk().setStaff_no(p.getInventor_staff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							pDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;		
				}
				//patentDetails_p_list = null;
				// update importStatus if it is imported
				updateSelectedImportStatus(currentPatentNo);

				// Success message
				String message = "msg.success.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				saved = true;
				
				if (!doPublish) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "managePatent_edit.xhtml?pid="+paramPid+"&no="+selectedPatentHeader_p.getPk().getPatent_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ", staff_no="+ selectedPatentDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ", staff_no="+ selectedPatentDetails_q.getPk().getStaff_no() + ")", e);
		}
			
	}
	
	//P level
	public void save_p() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
				if ("Y".equals(selectedPatentHeader_q.getInst_verified_ind()) && selectedPatentHeader_q.getInst_verified_date() == null) {
					selectedPatentHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedPatentHeader_q.getCdcf_status())) {
					selectedPatentHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedPatentHeader_q.getCdcf_status())) {
					selectedPatentHeader_q.setCdcf_processed_ind("Y");
					selectedPatentHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedPatentHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedPatentHeader_q.getCdcf_status())) {
					selectedPatentHeader_q.setCdcf_processed_ind("Y");
					selectedPatentHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedPatentHeader_q.setCdcf_selected_ind("N");
				}
				selectedPatentHeader_q.setUserstamp(getLoginUserId());
				
				//Update Header Q
				selectedPatentHeader_q = pDao.updateEntity(selectedPatentHeader_q);
				
				int currentPatentNo = selectedPatentHeader_q.getPatent_no();
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentPatentNo);
				

				// Success message
				String message;
				if ("CDCF_PROCESSED".equals(selectedPatentHeader_q.getCdcf_status())) {
					takeSnapshot();
					pDao.publishToSAP(selectedPatentHeader_q.getPatent_no(), "RH_UPLOAD_PATENT_P");
					message = "msg.success.save.generate.x";
				}else {
					message = "msg.success.save.x";
				}
				message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ")", e);
		}
	}	
	
	//C level
	public void save_c() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
			validateRequiredField();
			validateCountry();
			
			if (validatePatentDetails_P(getRealRiCreatorStaffNo()) && !getHasError()) {
				if ("Y".equals(selectedPatentHeader_q.getInst_verified_ind()) && selectedPatentHeader_q.getInst_verified_date() == null) {
					selectedPatentHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedPatentHeader_q.getCdcf_status())) {
					selectedPatentHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedPatentHeader_q.getCdcf_status())) {
					selectedPatentHeader_q.setCdcf_processed_ind("Y");
					selectedPatentHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedPatentHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedPatentHeader_q.getCdcf_status())) {
					selectedPatentHeader_q.setCdcf_processed_ind("Y");
					selectedPatentHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedPatentHeader_q.setCdcf_selected_ind("N");
				}
				
				selectedPatentHeader_q.setUserstamp(getLoginUserId());
				
				//Update P, Q
				selectedPatentHeader_q = pDao.updateEntity(selectedPatentHeader_q);
				
				selectedPatentHeader_p.setUserstamp(getLoginUserId());
				selectedPatentHeader_p = pDao.updateEntity(selectedPatentHeader_p);
				
				pDao.deleteAllInventor(selectedPatentHeader_q.getPatent_no(), "C");
				int line_no = 1;
				for (PatentDetails_P p:patentDetails_p_list) {
					p.getPk().setData_level("C");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getNon_ied_staff_flag().equals("N") && p.getInventor_staff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getInventor_staff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setInventor_name(nList.get(0));
						}
					}
					//set past staff details
					if (p.getNon_ied_staff_flag().equals("F") && p.getInventor_staff_no() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getStaff_number().equals(p.getInventor_staff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setInventor_name(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					pDao.updateEntity(p);
					
					//Create record in patentDetails_q if has staff number
					if (p.getInventor_staff_no() != null) {
						PatentDetails_Q tmpDetailsQ = pDao.getPatentDetails_Q1(selectedPatentHeader_p.getPk().getPatent_no(), p.getInventor_staff_no());
						if (tmpDetailsQ.getPk().getPatent_no() == null) {
							PatentDetails_Q newDetailsQ = new PatentDetails_Q();
							newDetailsQ.getPk().setPatent_no(selectedPatentHeader_p.getPk().getPatent_no());
							newDetailsQ.getPk().setStaff_no(p.getInventor_staff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							pDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;
				}
				
				int currentPatentNo = selectedPatentHeader_q.getPatent_no();
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentPatentNo);
				
				// Success message
				String message;
				if ("CDCF_GENERATED".equals(selectedPatentHeader_q.getCdcf_status()) || "CDCF_PROCESSED".equals(selectedPatentHeader_q.getCdcf_status())) {
					pDao.publishToSAP(selectedPatentHeader_q.getPatent_no(), "RH_UPLOAD_PATENT_P");
					message = "msg.success.save.generate.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "managePatent_edit.xhtml?no="+currentPatentNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "managePatent_edit.xhtml?no="+currentPatentNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}
				
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ")", e);
		}
	}	
	
	public void publish()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (selectedPatentHeader_p != null && selectedPatentHeader_q != null && selectedPatentDetails_q != null) {
			try {
				//Get C level header
				if (getParamNo() != null) {
					PatentHeader_P selectedPatentHeader_p_c = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "C");
					if (selectedPatentHeader_p_c != null) {
						selectedPatentHeader_q.setCdcf_changed_ind("Y");
					}
				}
				selectedPatentHeader_q.setPublish_status("PUBLISHED");
				int publishFreq = (selectedPatentHeader_q.getPublish_freq() != null)?selectedPatentHeader_q.getPublish_freq()+1:1;
				selectedPatentHeader_q.setPublish_freq(publishFreq);
				selectedPatentHeader_q.setLast_modified_by(getLoginUserId());
				selectedPatentHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
				selectedPatentHeader_q.setLast_published_by(getLoginUserId());
				selectedPatentHeader_q.setLast_published_date(Timestamp.from(Instant.now()));
				
				selectedPatentDetails_q.setUserstamp(getLoginUserId());
				
				//if (validatePatentDetails_P(patentDetails_p_list, staffDetail.getStaff_number())) {
				if (!getHasError()) {	
					//Update P, Q
					selectedPatentHeader_q = pDao.updateEntity(selectedPatentHeader_q);

					int currentPatentNo = selectedPatentHeader_q.getPatent_no();
					
					selectedPatentDetails_q.getPk().setPatent_no(currentPatentNo);
					selectedPatentDetails_q = pDao.updateEntity(selectedPatentDetails_q);

					selectedPatentHeader_p.getPk().setPatent_no(currentPatentNo);
					selectedPatentHeader_p = pDao.updateEntity(selectedPatentHeader_p);
					
					PatentHeader_P selectedPatentHeader_p2 = selectedPatentHeader_p;
					selectedPatentHeader_p2.getPk().setData_level("P");
					selectedPatentHeader_p2.setUserstamp(getLoginUserId());
					PatentHeader_P patentHeader_p_publish = pDao.getPatentHeader_P(selectedPatentHeader_p.getPk().getPatent_no(), "P");
					if (patentHeader_p_publish != null) {
						pDao.deleteEntity(PatentHeader_P.class, patentHeader_p_publish.getPk());	
						//java.sql.Timestamp sqlDate = new java.sql.Timestamp(patentHeader_p_publish.getCreationDate().getTime());
						//selectedPatentHeader_p2.setTimestamp(sqlDate);
						//pDao.updateCreateDetails(patentHeader_p_publish.getPk().getPatent_no(), patentHeader_p_publish.getCreator(), sqlDate);
					}
					selectedPatentHeader_p2 = pDao.updateEntity(selectedPatentHeader_p2);

					//delete all inventor in M and P levels
					//pDao.deleteAllInventor(currentPatentNo, "M");
					pDao.deleteAllInventor(currentPatentNo, "P");
					//Update patent name list
					int line_no = 1;
					for(PatentDetails_P p:patentDetails_p_list) {
						/*p.getPk().setPatent_no(currentPatentNo);
						p.getPk().setData_level("M");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getNon_ied_staff_flag().equals("N") && p.getInventor_staff_no() != null) {
							staffNameList = getStaffNameList();
							List<String> nList = staffNameList.stream()
									.filter(x -> x.getStaff_number().equals(p.getInventor_staff_no()))
									.map(x -> x.getFullname() + " " + x.getChinesename())
									.collect(Collectors.toList());
							if (!nList.isEmpty()) {
								p.setInventor_name(nList.get(0));
							}
						}
						//set past staff details
						if (p.getNon_ied_staff_flag().equals("F") && p.getInventor_staff_no() != null) {
							staffPastList = getStaffPastList();
							List<String> fList = staffPastList.stream()
									.filter(x -> x.getStaff_number().equals(p.getInventor_staff_no()))
									.map(x -> x.getFullname())
									.collect(Collectors.toList());
							if (!fList.isEmpty()) {
								p.setInventor_name(fList.get(0));
							}
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						pDao.updateEntity(p);*/
						PatentDetails_P p2 = p;
						p2.getPk().setData_level("P");
						pDao.updateEntity(p2);
						//Create record in patentDetails_q if has staff number
						if (p.getInventor_staff_no() != null) {
							PatentDetails_Q tmpDetailsQ = pDao.getPatentDetails_Q1(currentPatentNo, p.getInventor_staff_no());
							if (tmpDetailsQ.getPk().getPatent_no() == null) {
								PatentDetails_Q newDetailsQ = new PatentDetails_Q();
								newDetailsQ.getPk().setPatent_no(currentPatentNo);
								newDetailsQ.getPk().setStaff_no(p.getInventor_staff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								pDao.updateEntity(newDetailsQ);
							}
						}
						line_no++;		
					}
					//patentDetails_p_list = null;

					// Success message
					String message = "msg.success.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}else {
					// Failed message
					String message = "msg.err.data.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				}
				if (saved) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "managePatent_edit.xhtml?pid="+paramPid+"&no="+selectedPatentHeader_p.getPk().getPatent_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ", staff_no="+ selectedPatentDetails_q.getPk().getStaff_no() + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit patent (patent_no=" + selectedPatentHeader_q.getPatent_no() + ", staff_no="+ selectedPatentDetails_q.getPk().getStaff_no() + ")", e);
			}
		}
	}
	
	public void submitConsent()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		if ( selectedPatentDetails_q != null) {
			try {
				selectedPatentDetails_q.setUserstamp(getLoginUserId());
				selectedPatentDetails_q = pDao.updateEntity(selectedPatentDetails_q);
					
					// Success message
					String message = "msg.success.submit.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Consent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedPatentDetails_q + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedPatentDetails_q + ")", e);
			}
		}
	}	
	
	public String takeSnapshot()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String destUrl = "";
		if (selectedPatentHeader_p != null && patentDetails_p_list != null && selectedPatentHeader_q != null) {
			try {
				int currentRiNo = selectedPatentHeader_p.getPk().getPatent_no();
				PatentHeader_P selectedPatentHeader_p_c = pDao.getPatentHeader_P(currentRiNo, "C");
				if (selectedPatentHeader_p_c != null) {
					pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p_c.getPk());
				}
				selectedPatentHeader_p_c = selectedPatentHeader_p;
				selectedPatentHeader_p_c.getPk().setData_level("C");
				selectedPatentHeader_p_c.setUserstamp(getLoginUserId());
				selectedPatentHeader_p_c.setCreator(getLoginUserId());
				selectedPatentHeader_p = pDao.updateEntity(selectedPatentHeader_p_c);
				
				pDao.deleteAllInventor(currentRiNo, "C");
				for (PatentDetails_P p:patentDetails_p_list) {
					PatentDetails_P p2 = p;
					p2.getPk().setData_level("C");
					p2.setCreator(getLoginUserId());
					p2.setUserstamp(getLoginUserId());
					pDao.updateEntity(p2);
				}
				
				if ("CDCF_GENERATED".equals(selectedPatentHeader_q.getCdcf_status())) {
					pDao.publishToSAP(selectedPatentHeader_q.getPatent_no(), "RH_UPLOAD_PATENT_P");
				}
				
				// Success message
				String message = "msg.success.create.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Snapshot");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				destUrl = redirect("managePatent_edit") + "&no=" + currentRiNo + "&dataLevel=C";	
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Patent No.:" + selectedPatentHeader_p.getPk().getPatent_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Patent No.:" + selectedPatentHeader_p.getPk().getPatent_no() + ")", e);
			}
		}
		return redirect(destUrl);
	}	
	
	//Delete all levels
	public String deleteForm() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (!Strings.isNullOrEmpty(paramNo)) {
			try {
				//Get C level header
				PatentHeader_P selectedPatentHeader_p_c = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "C");
				
				//In M level page
				/*if ("M".equals(getParamDataLevel())) {
					if (selectedPatentHeader_p_c == null) {
						//P
						selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "P");
						if (selectedPatentHeader_p != null) {
							pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p.getPk());
						}
						//M
						PatentHeader_P selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedPatentHeader_p != null) {
							pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p.getPk());
						}
						
						//M and P levels
						pDao.deleteAllInventor(Integer.valueOf(paramNo), "M");
						pDao.deleteAllInventor(Integer.valueOf(paramNo), "P");
						
						//Header Q and Details Q
						pDao.deleteEntity(PatentHeader_Q.class, Integer.valueOf(paramNo));
						pDao.deletePatentDetails_Q(Integer.valueOf(paramNo));
							
						// Success message
						String message = "msg.success.delete.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}else {
						String message = "Patent is already generated to SAP, so it can not be deleted. ";
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					}
				}*/
				//In P or C level page
				/*if ("P".equals(getParamDataLevel()) || "C".equals(getParamDataLevel())){
					//C
					if (selectedPatentHeader_p_c != null) {
						pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p_c.getPk());
					}
					//P
					selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "P");
					if (selectedPatentHeader_p != null) {
						pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p.getPk());
					}
					//M
					PatentHeader_P selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "M");
					if (selectedPatentHeader_p != null) {
						pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p.getPk());
					}
					
					//M and P and C levels
					pDao.deleteAllInventor(Integer.valueOf(paramNo), "M");
					pDao.deleteAllInventor(Integer.valueOf(paramNo), "P");
					pDao.deleteAllInventor(Integer.valueOf(paramNo), "C");
					
					//Header Q and Details Q
					pDao.deleteEntity(PatentHeader_Q.class, Integer.valueOf(paramNo));
					pDao.deletePatentDetails_Q(Integer.valueOf(paramNo));
					
					// Success message
					String message = "msg.success.delete.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}*/
				//C
				if (selectedPatentHeader_p_c != null) {
					pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p_c.getPk());
				}
				//P
				selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "P");
				if (selectedPatentHeader_p != null) {
					pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p.getPk());
				}
				//M
				PatentHeader_P selectedPatentHeader_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "M");
				if (selectedPatentHeader_p != null) {
					pDao.deleteEntity(PatentHeader_P.class, selectedPatentHeader_p.getPk());
				}
				
				//M and P and C levels
				pDao.deleteAllInventor(Integer.valueOf(paramNo), "M");
				pDao.deleteAllInventor(Integer.valueOf(paramNo), "P");
				pDao.deleteAllInventor(Integer.valueOf(paramNo), "C");
				
				//Header Q and Details Q
				pDao.deleteEntity(PatentHeader_Q.class, Integer.valueOf(paramNo));
				pDao.deletePatentDetails_Q(Integer.valueOf(paramNo));
				
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Patent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete patent (No.: " + selectedPatentHeader_q.getPatent_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete patent (No.: " + selectedPatentHeader_q.getPatent_no() + ")", e);
			}
		}
		String destUrl = redirect("managePatent");
		if(paramPid != null)
			destUrl += "&pid=" + paramPid;
		return redirect(destUrl);
	}
	
	public String getRealRiCreatorStaffNo()
	{
		PatentDetails_Q tmp = pDao.getPatentDetails_Q_creator(Integer.valueOf(paramNo));
		if (tmp != null) {
			return tmp.getPk().getStaff_no();
		}
		return null;
	}
	
	public String getRiCreatorStaffNo()
	{
		if (Strings.isNullOrEmpty(riCreatorStaffNo)) {
			PatentDetails_Q tmp = pDao.getPatentDetails_Q_creator(Integer.valueOf(paramNo));
			riCreatorStaffNo = (tmp!= null)?tmp.getPk().getStaff_no():getCurrentUserId();
		}
		return riCreatorStaffNo;
	}

	
	public void setRiCreatorStaffNo(String riCreatorStaffNo)
	{
		this.riCreatorStaffNo = riCreatorStaffNo;
	}

	public boolean isRiCreator(String staff_no) 
	{
		boolean result = false;
		String realRiCreatorStaffNo = ("M".equals(getParamDataLevel()))? getRiCreatorStaffNo():getRealRiCreatorStaffNo();
		if (!Strings.isNullOrEmpty(staff_no) && !Strings.isNullOrEmpty(realRiCreatorStaffNo)) {
			result = (realRiCreatorStaffNo.equals(staff_no))?true:false;
		}
		return result;
	}	
	
	public boolean checkSnapshotExists() 
	{
		boolean result = false;
		PatentHeader_P tmp = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "C");
		if (tmp != null) {
			result = true;
		}
		return result;	
	}

	//validate name list
	public boolean validatePatentDetails_P(String staff_no){
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String errMessage = "msg.err.mandatory.x";
		boolean result = true;
		boolean yourself = false;
		int countAuthor = 0;
		String message;
		String allMessage = "";
		HashSet unique=new HashSet();
		if (!Strings.isNullOrEmpty(staff_no)) {
			for (PatentDetails_P p:patentDetails_p_list) {
				int lineNo = countAuthor + 1;
				//get staff details
				if (p.getNon_ied_staff_flag().equals("N")) {
					if (p.getInventor_staff_no() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Inventor (no. "+lineNo+")");
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getInventor_staff_no());
						if (s != null) {
							p.setInventor_name(s.getFullname_save());
						}		
					}
				}
				//get past staff details				
				if (p.getNon_ied_staff_flag().equals("F")) {
					if (p.getInventor_name() == null) {
						result = false;
						message = "Inventor (no. "+lineNo+") is not correct.";
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getInventor_staff_no());
						if (sp != null) {
							//p.setInventor_staff_no(sp.getStaff_number());
							//p.setInventor_name(sp.getFullname_save());
						}else {
							result = false;
							//message = MessageFormat.format(getResourceBundle().getString(errMessage), "Inventor (no. "+lineNo+")");
							message = "Inventor (no. "+lineNo+") is not correct.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
				}
				
				//check name is not null
				if (p.getNon_ied_staff_flag().equals("Y") || p.getNon_ied_staff_flag().equals("S")) {
					if (Strings.isNullOrEmpty(p.getInventor_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Inventor (no. "+lineNo+")");
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						byte[] nameBytes = p.getInventor_name().getBytes(StandardCharsets.UTF_8);
						if (nameBytes.length > 80) {
							result = false;
							message = "Inventor (no. "+lineNo+") is too long.";
							allMessage += "- Inventor (no. "+lineNo+") is too long.<br/>";
							fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
						}else {
							p.setInventor_staff_no(null);
						}	
					}
				}
				
				//check duplicate
				if (!unique.add(p.getInventor_staff_no()) && p.getInventor_staff_no() != null){
					result = false;
					if (!Strings.isNullOrEmpty(p.getInventor_name())) {
						message = "Inventor - Staff ("+p.getInventor_name()+") cannot be duplicated.";
						allMessage += "- Inventor - Staff ("+p.getInventor_name()+") cannot be duplicated.<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						message = "Inventor cannot be duplicated.";
						allMessage += "- Inventor cannot be duplicated.<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
				}
				
				if (!Strings.isNullOrEmpty(p.getInventor_staff_no())) {
					if (staff_no.equals(p.getInventor_staff_no())) {
						yourself = true;
					}
				}
				countAuthor++;
			}
		}
		if (yourself == false && "M".equals(getParamDataLevel())) {		
			result = false;
			message = "You must be one of the inventor.";
			allMessage += "- You must be one of the inventor.<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (yourself == false && "M".equals(getParamDataLevel()) == false) {		
			result = false;
			message = "Creator must be one of the inventor.";
			allMessage += "- You must be one of the inventor.<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, allMessage));
		}
		return result;
	}
	
	public Boolean validateCountry() {
		boolean result = true;
		String message = "";
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (!Strings.isNullOrEmpty(selectedPatentHeader_p.getCountry())) {
			if (selectedPatentHeader_p.getCountry().equals("0") && "G".equals(selectedPatentHeader_p.getPatent_granted())) {
				result = false;
				message = "Country should be decided.";
				allMessage += "- Country should be decided.<br/>";
				fCtx.addMessage("editForm:country", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	//validate mandatory field	
	public Boolean validateRequiredField() {
		boolean result = true;
		String errMessage = "msg.err.mandatory.x";
		String message = "";
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (Strings.isNullOrEmpty(selectedPatentHeader_p.getPatent_name())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Patent");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Patent")+"<br/>";
			fCtx.addMessage("editForm:patent_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedPatentHeader_p.setPatent_name(selectedPatentHeader_p.getPatent_name().trim());
		}
		if (Strings.isNullOrEmpty(selectedPatentHeader_p.getSerial_no())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Serial Number of Patent");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Serial Number of Patent")+"<br/>";
			fCtx.addMessage("editForm:patent_sNo", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedPatentHeader_p.setSerial_no(selectedPatentHeader_p.getSerial_no().trim());
		}
		if (Strings.isNullOrEmpty(selectedPatentHeader_p.getCountry())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Country Granting the National Patent");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Country Granting the National Patent")+"<br/>";
			fCtx.addMessage("editForm:country", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedPatentHeader_p.getPatent_month()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Application/Grant - Month");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Application/Grant - Month")+"<br/>";
			fCtx.addMessage("editForm:patentMonth", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedPatentHeader_p.getPatent_year()==null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Application/Grant - Year");
			allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Application/Grant - Year")+"<br/>";
			fCtx.addMessage("editForm:patentYear", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}

		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	public ImportRIPatent getPatentPanel()
	{
		if(patentPanel == null) {
			patentPanel = new ImportRIPatent();
			patentPanel.setParamPid(getParamPid());
		}
		return patentPanel;
	}


	public void ignorePatent() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedPatent = dao.getStatusByPK(getPatentPanel().getSelectedIgnorePatent());
		if(selectedPatent != null) {
			selectedPatent.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedPatent);
			getPatentPanel().setPatentList(null);
		}
	}

	
	public ImportRIPatentV getSelectedImportPatent()
	{
		if(selectedImportPatent == null && getParamArea_code() != null
				&& getParamSource_id() != null && getParamStaff_number() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			ImportRIPatentV_PK pk = new ImportRIPatentV_PK();
			pk.setArea_code(getParamArea_code());
			pk.setSource_id(getParamSource_id());
			pk.setStaff_number(getParamStaff_number());
			selectedImportPatent = dao.getImportRIPatentByPK(pk);
		}
		
		return selectedImportPatent;
	}
}


