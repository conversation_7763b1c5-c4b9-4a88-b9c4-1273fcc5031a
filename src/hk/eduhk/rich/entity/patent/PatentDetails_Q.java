package hk.eduhk.rich.entity.patent;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_Q_PATENT_DTL")
public class PatentDetails_Q extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PatentDetails_Q.class.toString());
	
	@EmbeddedId
	private PatentDetails_Q_PK pk = new PatentDetails_Q_PK();
	
	@Column(name = "creator_ind")
	private String creator_ind;

	@Column(name = "display_ind")
	private String display_ind;	
	
	@Column(name = "consent_ind")
	private String consent_ind;

	
	public PatentDetails_Q_PK getPk()
	{
		return pk;
	}

	
	public void setPk(PatentDetails_Q_PK pk)
	{
		this.pk = pk;
	}

	
	public String getCreator_ind()
	{
		return creator_ind;
	}

	
	public void setCreator_ind(String creator_ind)
	{
		this.creator_ind = creator_ind;
	}

	
	public String getDisplay_ind()
	{
		return display_ind;
	}

	
	public void setDisplay_ind(String display_ind)
	{
		this.display_ind = display_ind;
	}

	
	public String getConsent_ind()
	{
		return consent_ind;
	}

	
	public void setConsent_ind(String consent_ind)
	{
		this.consent_ind = consent_ind;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatentDetails_Q other = (PatentDetails_Q) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PatentDetails_Q [pk=" + pk + ", creator_ind=" + creator_ind + ", display_ind=" + display_ind
				+ ", consent_ind=" + consent_ind + "]";
	}

	

}
