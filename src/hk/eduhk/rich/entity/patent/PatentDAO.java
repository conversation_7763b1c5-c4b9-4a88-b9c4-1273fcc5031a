package hk.eduhk.rich.entity.patent;

import java.sql.*;
import java.util.*;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.view.RISearchPanel;

@SuppressWarnings("serial")
public class PatentDAO extends BaseDAO
{

	private static PatentDAO instance = null;


	public static synchronized PatentDAO getInstance()
	{
		if (instance == null) instance = new PatentDAO();
		return instance;
	}
	
	public static PatentDAO getCacheInstance()
	{
		return PatentDAO.getInstance();
	}
	
	public List<PatentCountry> getPatentCountryList(int lookup_level) 
	{
		List<PatentCountry> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentCountry obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<PatentCountry> q = em.createQuery(query, PatentCountry.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<PatentType> getPatentTypeList(int lookup_level) 
	{
		List<PatentType> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentType obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<PatentType> q = em.createQuery(query, PatentType.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<PatentDetails_P> getPatentDetails_P_byStaffNo(String staff_no, String data_level)
	{
		List<PatentDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentDetails_P obj WHERE obj.inventor_staff_no = :staff_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.patent_no desc";			
			TypedQuery<PatentDetails_P> q = em.createQuery(query, PatentDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PatentDetails_P> getPatentDetails_P_byStaffNo_consent(String staff_no, String data_level)
	{
		List<PatentDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentDetails_P obj WHERE obj.inventor_staff_no = :staff_no AND obj.pk.data_level = :data_level " +
										" AND obj.patentHeader_q.publish_freq > :publish_freq  AND obj.patentDetails_q.consent_ind = :consent_ind ORDER BY obj.pk.patent_no desc";			
			TypedQuery<PatentDetails_P> q = em.createQuery(query, PatentDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			q.setParameter("publish_freq", 0);
			q.setParameter("consent_ind", "U");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public PatentHeader_P getPatentHeader_P (Integer patent_no, String data_level) 
	{
		List<PatentHeader_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentHeader_P obj WHERE obj.pk.patent_no = :patent_no AND obj.pk.data_level = :data_level";			
			TypedQuery<PatentHeader_P> q = em.createQuery(query, PatentHeader_P.class);
			q.setParameter("patent_no", patent_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	
	
	public PatentHeader_Q getPatentHeader_Q (Integer patent_no) 
	{
		PatentHeader_Q obj = null;
		if (patent_no > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = pm.getEntityManager();
				obj = em.find(PatentHeader_Q.class, patent_no);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public List<PatentDetails_P> getPatentDetails_P(Integer patent_no, String data_level)
	{
		List<PatentDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentDetails_P obj WHERE obj.pk.patent_no = :patent_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.line_no";			
			TypedQuery<PatentDetails_P> q = em.createQuery(query, PatentDetails_P.class);
			q.setParameter("patent_no", patent_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PatentDetails_Q> getPatentDetails_Q(Integer patent_no, String staff_no)
	{
		List<PatentDetails_Q> objList = null;
		EntityManager em = null;		
		String pNo = (patent_no > 0)? " AND obj.pk.patent_no = :patent_no ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PatentDetails_Q obj WHERE obj.pk.staff_no = :staff_no " + pNo;			
			TypedQuery<PatentDetails_Q> q = em.createQuery(query, PatentDetails_Q.class);
			if (patent_no > 0) {
				q.setParameter("patent_no", patent_no);
			}
			q.setParameter("staff_no", staff_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public PatentDetails_Q getPatentDetails_Q1 (Integer patent_no, String staffNo) 
	{
		List<PatentDetails_Q> objList = null;
		EntityManager em = null;
		if (patent_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM PatentDetails_Q obj WHERE obj.pk.patent_no = :patent_no AND obj.pk.staff_no = :staffNo";			
				TypedQuery<PatentDetails_Q> q = em.createQuery(query, PatentDetails_Q.class);
				q.setParameter("patent_no", patent_no);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):new PatentDetails_Q();
	}
	
	public PatentDetails_Q getPatentDetails_Q_creator (Integer patent_no) 
	{
		List<PatentDetails_Q> objList = null;
		EntityManager em = null;
		if (patent_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM PatentDetails_Q obj WHERE obj.pk.patent_no = :patent_no AND obj.creator_ind = :creator_ind";			
				TypedQuery<PatentDetails_Q> q = em.createQuery(query, PatentDetails_Q.class);
				q.setParameter("patent_no", patent_no);
				q.setParameter("creator_ind", "Y");
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public void deleteAllInventor(int patent_no, String data_level) throws Exception
	{
		if (patent_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();

				Query q = em.createQuery("DELETE FROM PatentDetails_P obj WHERE obj.pk.patent_no = :patent_no AND obj.pk.data_level = :data_level ");
				q.setParameter("patent_no", patent_no);
				q.setParameter("data_level", data_level);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete inventors (patent_no=" + patent_no + ", data_level="+ data_level + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deletePatentDetails_Q(int patent_no) throws Exception
	{
		if (patent_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM PatentDetails_Q obj WHERE obj.pk.patent_no = :patent_no ");
				q.setParameter("patent_no", patent_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete PatentDetails_Q (patent_no=" + patent_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public PatentHeader_P updatePatentHeader_P(PatentHeader_P obj) 
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	/*public void updateCreateDetails(Integer patent_no, String creator, Timestamp creationTime) throws Exception
	{
		if (patent_no != null)
		{
			Connection conn = null;
			PreparedStatement pStmt = null;
			UserTransaction utx = null;
			try
			{
				utx = pm.getUserTransaction();
				utx.begin();			
				conn = pm.getConnection();
				
				String sql = "UPDATE RH_P_PATENT_HDR SET creator = ?, creation_time = ? WHERE patent_no = ? AND data_level = ?";
				pStmt = conn.prepareStatement(sql);
				pStmt.setString(1, creator);
				pStmt.setTimestamp(2, creationTime);	
				pStmt.setInt(3, patent_no);
				pStmt.setString(4, "P");
				pStmt.executeQuery();
				utx.commit();
			}
			catch (Exception e)
			{
				throw e;
			}			
			finally
			{
				pm.close(em);
			}	
		}
	}*/
	
	public List<Patent> getRIList(int pid, String staffNo, List<String> riNoList) throws SQLException
	{
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
    
		try
		{
			//20210203
			//if (searchObj == null) searchObj = new Patent();
			List<Patent> voList = new ArrayList();
			conn = pm.getConnection();

			String query = this.getRetrieveRIQuery(pid, staffNo, riNoList);
			logger.log(Level.FINEST, query);
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();

			while (rs.next())
			{
				Patent vObj = new Patent();
				vObj.setPid(rs.getInt("pid"));
				vObj.setStaffNumber(rs.getString("staff_number"));
				vObj.setStaffFullname(rs.getString("fullname"));
				vObj.setStaffDept(rs.getString("dept_code"));                
				vObj.setRINo(rs.getInt("patent_no"));
				vObj.setPatentName(rs.getString("patent_name"));
				vObj.setPatentType(rs.getString("patent_type"));
				vObj.setPatentGranted(rs.getString("patent_granted"));
				vObj.setPatentDay(rs.getString("patent_day"));
				vObj.setPatentMonth(rs.getString("patent_month"));
				vObj.setPatentYear(rs.getString("patent_year"));
				vObj.setSerialNo(rs.getString("serial_no"));
				vObj.setCountry(rs.getString("country"));
				vObj.setShortDesc(rs.getString("short_desc"));
				vObj.setFullDesc(rs.getString("full_desc"));
				vObj.setIEdWorkInd(rs.getString("ied_work_ind"));
				vObj.setCreator(rs.getString("creator"));
				vObj.setCreationDate(rs.getTimestamp("creation_time"));

				// HDR Status 
				vObj.setPublishStatus(rs.getString("publish_status"));
				vObj.setLastModifiedDate(rs.getTimestamp("last_modified_date"));
				vObj.setLastModifiedBy(rs.getString("last_modified_by"));
				vObj.setLastPublishedDate(rs.getTimestamp("last_published_date"));
				vObj.setLastPublishedBy(rs.getString("last_published_by"));
				vObj.setInstDisplayInd(rs.getString("inst_display_ind"));
				vObj.setInstVerifiedInd(rs.getString("inst_verified_ind"));
				vObj.setInstVerifiedDate(rs.getTimestamp("inst_verified_date"));
				vObj.setCDCFStatus(rs.getString("cdcf_status"));
				vObj.setCDCFGenInd(rs.getString("cdcf_gen_ind"));
				vObj.setCDCFGenDate(rs.getTimestamp("cdcf_gen_date"));
				vObj.setCDCFProcessInd(rs.getString("cdcf_processed_ind"));
				vObj.setCDCFProcessDate(rs.getTimestamp("cdcf_processed_date"));
				vObj.setCDCFSelectedInd(rs.getString("cdcf_selected_ind"));
				vObj.setCDCFChangedInd(rs.getString("cdcf_changed_ind"));
				vObj.setBulletinInd(rs.getString("bulletin_ind"));
				vObj.setRemarks(rs.getString("remarks"));
				vObj.setPublishFreq(rs.getInt("publish_freq"));

				// DTL Status
				vObj.setCreatorInd(rs.getString("creator_ind"));
				vObj.setDisplayInd(rs.getString("display_ind"));
				vObj.setConsentInd(rs.getString("consent_ind"));

				// Virtual field
				vObj.setCreatorPid(rs.getInt("creator_pid"));
				vObj.setCreatorName(rs.getString("creator_name"));
				vObj.setConcatNames(rs.getString("concat_names"));

				voList.add(vObj);
			}

	    return voList;
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
	}

	private String getRetrieveRIQuery(int pid, String staffNo, List<String> riNoList)
	{
		boolean retrieveByRINoList = (riNoList != null && riNoList.size() > 0);

    // Check if the list by staff or by RI, when by staff, a special string
    // "BY_STAFF" is add at the end of the array list
    boolean byStaff = false;
    
    if(retrieveByRINoList && "BY_STAFF".equals(riNoList.get(riNoList.size()-1))) 
    {
        byStaff = true;
        // remove special code after checked   
        riNoList.remove(riNoList.size()-1);        
    }

		StringBuffer sqlBuf = new StringBuffer();

		sqlBuf.append("SELECT RI.* " +
									"FROM " +
									"( " +
									"  SELECT I.pid,  I.fullname, i.dept_code, I.staff_number, C.creator_pid, c.creator_name, N.concat_names, H.*, " +
									"	 QH.publish_status, QH.last_modified_date, QH.last_modified_by, " +
									"	 QH.last_published_date, QH.last_published_by, " +
									"	 QH.inst_display_ind, QH.inst_verified_ind, QH.inst_verified_date, " +
									"	 QH.cdcf_status, QH.cdcf_gen_ind, QH.cdcf_gen_date, " +
									"	 QH.cdcf_processed_ind, QH.cdcf_processed_date, QH.cdcf_selected_ind, " +
									"	 QH.cdcf_changed_ind, QH.bulletin_ind, QH.remarks, QH.publish_freq, " +
									"	 QD.display_ind, QD.creator_ind, QD.consent_ind, D.line_no " +
									"  FROM " + this.getDataTableHDR() + " H, " + this.getDataTableDTL() + " D, " +
									"	 " + getStatusTableHDR() + " QH, " + getStatusTableDTL() + " QD, " +
									"	 (SELECT pid, fullname, dept_code, staff_number from RH_P_STAFF_IDENTITY UNION SELECT PID, FULLNAME, NULL, STAFF_NUMBER FROM RH_P_STAFF_EMPLOYMENT_PAST) I, " +
									"	 ( " +
									"		 SELECT patent_no, pid AS creator_pid, fullname as creator_name FROM " + 
									"		 (" + 
									"			 SELECT patent_no, pid, fullname, " + 
									"			 ROW_NUMBER() OVER (PARTITION BY patent_no ORDER BY pid) AS idx, " + 
									"			 COUNT(*) OVER (PARTITION BY patent_no) AS cnt " +                   
									"			 FROM " + getStatusTableDTL() + " iqd, (SELECT STAFF_NUMBER, PID, fullname  FROM RH_P_STAFF_IDENTITY  UNION  SELECT STAFF_NUMBER, PID, fullname FROM RH_P_STAFF_EMPLOYMENT_PAST) ii " + 
									"			 WHERE iqd.staff_no = ii.staff_number AND creator_ind = 'Y' " + 
									"			) " +
									"			WHERE idx = cnt " +
									"  ) C, " + // C captures the creator pid
                  "	 ( " +
									"		  SELECT patent_no, SUBSTR(SYS_CONNECT_BY_PATH(name, '; '), 2) AS concat_names " +
									"		  FROM " +
									"		  ( " +
									"				SELECT patent_no, line_no, " +
									"				CASE " +
                  "				  WHEN NVL(non_ied_staff_flag,'X') = 'S' THEN d.inventor_name || ' [Student]' " +
									"					WHEN dept_code IS NULL THEN d.inventor_name " +
									"					ELSE i.fullname || ' [' || i.dept_code || ']' " +
									"				END AS name, " +
									"		    COUNT (*) OVER (PARTITION BY patent_no) AS cnt " +
									"				FROM " + this.getDataTableDTL() + " D, RH_P_STAFF_IDENTITY I " +
									"				WHERE D.inventor_staff_no = I.staff_number (+) " +
									"		  ) " +
									"		  WHERE line_no = cnt " +
									"		  START WITH line_no = 1 " +
									"		  CONNECT BY PRIOR line_no = line_no - 1 AND PRIOR patent_no = patent_no " +
									"	 ) N	" + // N captures the concat names of the RI
									"  WHERE H.patent_no = D.patent_no (+) " +
									"	 AND H.patent_no = QH.patent_no (+) " +
									"	 AND D.patent_no = QD.patent_no (+) " +
									"	 AND D.patent_no = C.patent_no (+) " +
									"	 AND D.patent_no = N.patent_no (+) " +
									" 	 AND H.data_level = D.data_level " +
									" 	 AND H.data_level = 'P' " +
									"	 AND D.inventor_staff_no = QD.staff_no (+) " +
									"  AND d.inventor_staff_no = I.staff_number (+) " +
									" AND QD.CONSENT_IND <> 'N' AND QD.DISPLAY_IND <> 'N' " +
									") RI " +
									"WHERE 1=1 ");

		if (pid > 0) sqlBuf.append("AND pid=" + pid + " ");
		if (!GenericValidator.isBlankOrNull(staffNo)) sqlBuf.append("AND staff_number='" + escapeSql(staffNo) + "' ");
		/*if (searchObj.getRINo() > 0) sqlBuf.append("AND patent_no=" + searchObj.getRINo() + " ");
		if (!GenericValidator.isBlankOrNull(searchObj.getPublishStatus())) sqlBuf.append("AND publish_status='" + escapeSql(searchObj.getPublishStatus()) + "' ");

		if (searchObj.getCreatorPid() > 0)
		{
			sqlBuf.append("AND pid=" + searchObj.getCreatorPid() + " " +
										"AND (creator_ind = 'Y' OR publish_freq >= 1) ");
		}*/
		
		if (retrieveByRINoList)
		{
			sqlBuf.append("AND patent_no IN (");
			sqlBuf.append(getSQLIdSet(getDataTableHDR(), "patent_no", riNoList, false));
			sqlBuf.append(")");
      //if(!byStaff) sqlBuf.append("AND (creator_ind = 'Y' OR publish_freq >= 1) ");
      if(!byStaff) {
        // retrieve first ied staff
        sqlBuf.append("AND line_no = (SELECT MIN(LINE_NO) FROM " + this.getDataTableDTL() +" SH WHERE SH.PATENT_NO = RI.PATENT_NO AND NON_IED_STAFF_FLAG IN ('N','F') )");
      }
      if(byStaff) sqlBuf.append("AND pid is not null ");  
		}
		//20210203
		/*if (searchObj instanceof BaseRIFull)
		{
			BaseRIFull fullSearch = (BaseRIFull) searchObj;
      if (!GenericValidator.isBlankOrNull(fullSearch.getDisplayInd())) sqlBuf.append("AND display_ind='" + escapeSql(fullSearch.getDisplayInd()) + "' ");
			if (!GenericValidator.isBlankOrNull(fullSearch.getConsentInd())) sqlBuf.append("AND consent_ind='" + escapeSql(fullSearch.getConsentInd()) + "' ");
			if (!GenericValidator.isBlankOrNull(fullSearch.getCreatorInd())) sqlBuf.append("AND creator_ind='" + escapeSql(fullSearch.getCreatorInd()) + "' ");
		}*/

		// Order by the actual order of pid in the List.
		if (retrieveByRINoList  && !byStaff)
		{
			sqlBuf.append("ORDER BY DECODE(patent_no");
			for (int n=0;n<riNoList.size();n++) sqlBuf.append("," + riNoList.get(n) + "," + n);
			sqlBuf.append(",9999999)");
		}

		// Default order.
		else
		{
			sqlBuf.append("ORDER BY fullname, patent_year desc, patent_month desc, patent_day desc, short_desc ");
		}
		//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
		return sqlBuf.toString();
	}	
	
	protected String getDataTableHDR()
	{
		return "RH_P_PATENT_HDR";
	}


	protected String getDataTableDTL()
	{
		return "RH_P_PATENT_DTL";
	}


	protected String getStatusTableHDR()
	{
		return "RH_Q_PATENT_HDR";
	}


	protected String getStatusTableDTL()
	{
		return "RH_Q_PATENT_DTL";
	}
	
	public static String escapeSql(String str)
	{
         if (str == null) {
                return null;
          }
         return StringUtils.replace(str, "'", "''");
	}	

	public static String getSQLIdSet(String tableName, String id, Collection idCol, boolean withQuoteChar)
	{
		StringBuffer queryBuf = new StringBuffer();
		StringBuffer idBuf = new StringBuffer();

		if (idCol != null)
		{
			int numOfIds = idCol.size();

			int n = 0;
			Iterator idIter = idCol.iterator();
			while (idIter.hasNext())
			{
				n++;
				String Id = (String) idIter.next();
				if (idBuf.length() > 0) idBuf.append(",");
				if (withQuoteChar) idBuf.append("'");
				idBuf.append(escapeSql(Id));
				if (withQuoteChar) idBuf.append("'");

				// Construct a sub-query for every 500 Ids.
				if (n % 500 == 0 || n == numOfIds)
				{
					if (queryBuf.length() > 0) queryBuf.append(" UNION ");
					queryBuf.append("SELECT DISTINCT " + id + " FROM " + tableName + " WHERE " + id + " IN (" + idBuf + ")");
					idBuf.delete(0, idBuf.length());
				}
			}
		}

		return queryBuf.toString();
	}		
	
	public List<PurePersonPatent> getPersonPatentList(String pure_source_id)
	{
		List<PurePersonPatent> objList = new ArrayList<PurePersonPatent>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		try
		{
			String query = "SELECT * FROM RH_PURE_PERSON_PATENT_V WHERE PURE_SOURCE_ID = ? ORDER BY patent_no ";
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			pStmt.setString(1, pure_source_id);
			ResultSet rs = pStmt.executeQuery();
			while (rs.next()) {
				PurePersonPatent d = new PurePersonPatent();
				d.setSource_id(rs.getString("PURE_SOURCE_ID"));
				d.setPid(rs.getInt("pid"));
				d.setPatent_no(rs.getInt("patent_no"));
				d.setPatent_name(rs.getString("patent_name"));
				d.setPatent_serial_no(rs.getString("SERIAL_NO"));
				d.setPatent_granted(rs.getString("patent_granted"));
				d.setPatent_contry(rs.getString("COUNTRY"));
				d.setPatent_type(rs.getString("patent_type"));
				d.setShort_desc(rs.getString("short_desc"));
				d.setFull_desc(rs.getString("full_desc"));
				d.setPatent_year(rs.getInt("patent_year"));
				d.setPatent_month(rs.getInt("patent_month"));
				d.setPatent_day(rs.getInt("patent_day"));
				objList.add(d);
			}
			
		}
		catch (SQLException se)
		{
			se.printStackTrace();
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}

	
		return objList;		
	}
	
	public List<Patent> getPatentListByIds(List<Integer> idList, RISearchPanel searchPanel) throws SQLException
	{
		List<Patent> voList = new ArrayList<Patent>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && searchPanel != null) {
			String listingType = searchPanel.getListingType();
			String dataLevel = searchPanel.getViewType();
			String sortCol = searchPanel.getSortCol();
			String sortOrder = searchPanel.getSortOrder();
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
				
				    sqlBuf.append(
				    		" SELECT PH.PATENT_NO, " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" (CASE WHEN STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME ELSE EXSTAFF.FULLNAME END) AS STAFF_NAME, " + 
				    		" PD.INVENTOR_STAFF_NO, " + 
				    		" STAFF.DEPT_CODE AS DEPARTMENT, " );
				    }
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.INVENTOR_LIST"+i+" AS INVENTOR_LIST"+i+", ");
				    }
				    sqlBuf.append(
				    		// 2.x
				    		" (CASE WHEN IED_WORK_IND = 'Y' THEN 'Yes' WHEN IED_WORK_IND = 'N' THEN 'No' ELSE '' END) AS IED_WORK_IND, " + 
				    		// 4.x
				    		" CASE WHEN PATENT_GRANTED = 'A' THEN 'Patent Application' WHEN PATENT_GRANTED = 'G' THEN 'Patent Granted' ELSE '' END AS PATENT_GRANTED, " + 
				    		" PATENT_NAME, " + 
				    		" SERIAL_NO, " +
				    		" (SELECT DESCRIPTION FROM RH_L_PATENT_COUNTRY_V WHERE LOOKUP_CODE = COUNTRY AND LOOKUP_LEVEL = 2) AS COUNTRY, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_PATENT_TYPE_V WHERE LOOKUP_CODE = PATENT_TYPE AND LOOKUP_LEVEL = 2) AS PATENT_TYPE, " + 
				    		" SHORT_DESC, " + 
				    		" FULL_DESC, " + 
				    		" (CASE WHEN PATENT_DAY IS NOT NULL THEN (PATENT_DAY || '/') ELSE '' END) || PATENT_MONTH || '/' || PATENT_YEAR AS PATENTDATE, " + 
				    		// manageInstituteRI
				    		" CDCF_STATUS, " +
				    		// add for sorting
				    		" PATENT_YEAR, " +
				    		" PATENT_MONTH, " +
				    		" PATENT_DAY, " +
				    		" (CASE WHEN CDCF_SELECTED_IND = 'Y' THEN 'Yes' WHEN CDCF_SELECTED_IND = 'N' THEN 'No' ELSE '' END) AS CDCF_SELECTED_IND " + 
				    		" FROM RH_P_PATENT_HDR PH  " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_P_PATENT_DTL PD ON (PD.PATENT_NO = PH.PATENT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " );
				    }
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_PATENT_HDR QH ON QH.PATENT_NO = PH.PATENT_NO  " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_Q_PATENT_DTL QD ON (QD.PATENT_NO = PH.PATENT_NO AND QD.STAFF_NO = PD.INVENTOR_STAFF_NO)  " + 
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVENTOR_STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVENTOR_STAFF_NO "); 
				    }
				    sqlBuf.append(
				    		" LEFT JOIN ( " + 
				    		" SELECT PH.PATENT_NO, " );
		    		for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" LISTAGG( " + 
					    		" CASE WHEN (PD.INVENTOR_STAFF_NO IS NOT NULL OR PD.INVENTOR_NAME IS NOT NULL) " + 
					    		" AND (LINE_NO <= " + i*30 + 
					    		" AND LINE_NO > " + (i*30 - 30) + " ) " +
					    		" THEN ( " + 
					    		" (CASE WHEN PD.INVENTOR_STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
					    		" WHEN PD.INVENTOR_STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
					    		" ELSE PD.INVENTOR_NAME END) " + 
					    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " + 
					    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END) " + 
					    		" ) ");
					    if(i == listNum) {
						    sqlBuf.append(
						    		" WHEN LINE_NO = (" + i*30 +
						    		")+1 THEN '...' ");}
					    sqlBuf.append(
					    		" ELSE '' END , '<br/>') " + 
					    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS INVENTOR_LIST" + i + " ");
					    if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append( 
				    		" FROM RH_P_PATENT_HDR PH  " + 
				    		" LEFT JOIN RH_P_PATENT_DTL PD ON (PD.PATENT_NO = PH.PATENT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " +  
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.INVENTOR_STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.INVENTOR_STAFF_NO  " + 
				    		" WHERE PH.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PH.PATENT_NO " + 
				    		" ) TMP ON PH.PATENT_NO = TMP.PATENT_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
		    		if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
		    			sqlBuf.append(
		    				" AND PD.INVENTOR_STAFF_NO IS NOT NULL" );
		    		}
				    sqlBuf.append(
				    		" AND PH.PATENT_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
				
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
					logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
		
					while (rs.next())
					{
						Patent vObj = new Patent();
						vObj.setRINo(rs.getInt("PATENT_NO"));
						if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
							vObj.setStaffFullname(rs.getString("STAFF_NAME"));
							vObj.setStaffNumber(rs.getString("INVENTOR_STAFF_NO"));
							vObj.setStaffDept(rs.getString("DEPARTMENT"));
						}
						String inventor_list = "";
						for(int i=1 ; i <= listNum ; ++i) {
							String authListSeg = rs.getString("INVENTOR_LIST"+i);
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									inventor_list += "<br/>" ;
								inventor_list += authListSeg;
							}
						}
						vObj.setInventor_list(inventor_list);
						// 2.x
						vObj.setIEdWorkInd(rs.getString("IED_WORK_IND"));
						// 4.x
						vObj.setPatentGranted(rs.getString("PATENT_GRANTED"));
						vObj.setPatentName(rs.getString("PATENT_NAME"));
						vObj.setSerialNo(rs.getString("SERIAL_NO"));
						vObj.setCountry(rs.getString("COUNTRY"));
						vObj.setPatentType(rs.getString("PATENT_TYPE"));
						vObj.setShortDesc(rs.getString("SHORT_DESC"));
						vObj.setFullDesc(rs.getString("FULL_DESC"));
						vObj.setPatentDate(rs.getString("PATENTDATE"));
						// manageInstituteRI
						vObj.setCDCFStatus(rs.getString("CDCF_STATUS"));
						// for sorting
						vObj.setPatentYear(rs.getString("PATENT_YEAR"));
						vObj.setPatentMonth(rs.getString("PATENT_MONTH"));
						vObj.setPatentDay(rs.getString("PATENT_DAY"));
						vObj.setCdcf_selected_ind(rs.getString("CDCF_SELECTED_IND"));
						
						voList.add(vObj);
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			if(voList != null && !voList.isEmpty()) {
				if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_RI_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Patent::getRINo));
					else
						voList.sort(Comparator.comparing(Patent::getRINo).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
			    	Comparator<Patent> nameComp = new Comparator<Patent>() {
			    		@Override public int compare(final Patent record1, final Patent record2) {
			    		    int c = 0;
			    		    if (c == 0 && record1.getStaffFullname() != null && record2.getStaffFullname() != null)
			    		    	c = record1.getStaffFullname().compareTo(record2.getStaffFullname());
			    		    else if (record1.getStaffFullname() == null) return 1;
			    		    else if (record2.getStaffFullname() == null) return -1;
			    		    if (c == 0 && record1.getPatentYear() != null && record2.getPatentYear() != null)
			    		       c = Integer.valueOf(record1.getPatentYear()).compareTo(Integer.valueOf(record2.getPatentYear()));
			    		    if (c == 0 && record1.getPatentMonth() != null && record2.getPatentMonth() != null)
			    		       c = Integer.valueOf(record1.getPatentMonth()).compareTo(Integer.valueOf(record2.getPatentMonth()));
			    		    if (c == 0 && record1.getPatentDay() != null && record2.getPatentDay() != null)
			    		       c = Integer.valueOf(record1.getPatentDay()).compareTo(Integer.valueOf(record2.getPatentDay()));
			    		    return c;
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, nameComp);
					else
						Collections.sort(voList, nameComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NO_VALUE)) {
			    	if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Patent::getRINo, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Patent::getRINo, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NAME_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Patent::getPatentName, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Patent::getPatentName, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CONTRIBUTOR_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Patent::getInventor_list, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Patent::getInventor_list, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_FROM_DATE_VALUE)) {
					Comparator<Patent> patentDayComp = new Comparator<Patent>() {
			    		@Override public int compare(final Patent record1, final Patent record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getPatentYear() == null ? "1000" : record1.getPatentYear();
				    		    String record1Month = record1.getPatentMonth() == null ? "1" : record1.getPatentMonth();
				    		    String record1Day = record1.getPatentDay() == null ? "1" : record1.getPatentDay();
				    		    String record1NullLast = record1.getPatentDay() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse(record1Day + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getPatentYear() == null ? "1000" : record2.getPatentYear();
				    		    String record2Month = record2.getPatentMonth() == null ? "1" : record2.getPatentMonth();
				    		    String record2Day = record2.getPatentDay() == null ? "1" : record2.getPatentDay();
				    		    String record2NullLast = record2.getPatentDay() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse(record2Day + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getPatentListByIds having non number patent date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, patentDayComp);
					else
						Collections.sort(voList, patentDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_TO_DATE_VALUE)) {
					Comparator<Patent> patentDayComp = new Comparator<Patent>() {
			    		@Override public int compare(final Patent record1, final Patent record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getPatentYear() == null ? "1000" : record1.getPatentYear();
				    		    String record1Month = record1.getPatentMonth() == null ? "1" : record1.getPatentMonth();
				    		    String record1Day = record1.getPatentDay() == null ? "1" : record1.getPatentDay();
				    		    String record1NullLast = record1.getPatentDay() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse(record1Day + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getPatentYear() == null ? "1000" : record2.getPatentYear();
				    		    String record2Month = record2.getPatentMonth() == null ? "1" : record2.getPatentMonth();
				    		    String record2Day = record2.getPatentDay() == null ? "1" : record2.getPatentDay();
				    		    String record2NullLast = record2.getPatentDay() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse(record2Day + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getPatentListByIds having non number patent date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, patentDayComp);
					else
						Collections.sort(voList, patentDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CDCF_STAT_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Patent::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Patent::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
			}
		}
		    return voList;
	}
	
	
	public List<Integer> getPatentNoListByDept(List<String> deptList, String data_level) throws SQLException
	{
		List<Integer> voList = new ArrayList<Integer>();
		
		if(deptList != null && data_level != null) {
			if (deptList.size() > 0) {
				PersistenceManager pm = PersistenceManager.getInstance();
				Connection conn = null;
				PreparedStatement pStmt = null;
			try
			{
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
			
			    sqlBuf.append(
			    		" SELECT DISTINCT PD.PATENT_NO " );
			    sqlBuf.append(
			    		" FROM RH_P_PATENT_DTL PD  " +
			    		" LEFT JOIN RH_P_STAFF_IDENTITY_DEPT STAFF_DEPT ON (PD.INVENTOR_STAFF_NO = STAFF_DEPT.STAFF_NUMBER) " );
			    sqlBuf.append(
			    		" WHERE  " + 
			    		" PD.DATA_LEVEL = '" + data_level + "' ");
			    sqlBuf.append(
			    		" AND STAFF_DEPT.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
	
				while (rs.next())
				{
					voList.add(rs.getInt("PATENT_NO"));
				}
			}
			
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			}
		}
		return voList;
	}
	
	public List<PatentDetails_P> getPatentDetails_P_byRiNo(List<List<Integer>> riNosParts, String data_level)
	{
		List<PatentDetails_P> objList = null;
		EntityManager em = null;	
		String where = "";
		try
		{
			em = getEntityManager();	
			if (!riNosParts.isEmpty()) {
				for (int i = 0; i < riNosParts.size(); i++) {
					if (i == 0) {
						where += " AND ( ";
					}
					where += " obj.pk.patent_no IN :riNos"+i;
					if (i == riNosParts.size() - 1) {
						where += " ) ";
					}else {
						where += " OR ";
					}
				}
				
				String query = "SELECT obj FROM PatentDetails_P obj WHERE obj.pk.data_level = :data_level AND obj.pk.line_no = :line_no " + where +
									" ORDER BY obj.patentHeader_p.patent_year DESC, obj.patentHeader_p.patent_month DESC ";			
				TypedQuery<PatentDetails_P> q = em.createQuery(query, PatentDetails_P.class);
				if (!riNosParts.isEmpty()) {
					for (int i = 0; i < riNosParts.size(); i++) {
						q.setParameter("riNos"+i, riNosParts.get(i));
					}
				}
				q.setParameter("data_level", data_level);
				q.setParameter("line_no", 1);
				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<Summary> getPatentSummaryCountList(String staffNo, String dataLevel, String startDate, String endDate, List<String> deptList){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (deptList != null) {
				selectDept = "SD.DEPT_CODE_EACH, ";
			}
			sqlBuf.append("SELECT RP.PERIOD_ID, RP.PERIOD_DESC, " + selectDept + "COUNT(DISTINCT(H.PATENT_NO)) AS C FROM RICH.RH_P_PATENT_HDR H"
					+ "    LEFT JOIN RICH.RH_P_PATENT_DTL D ON H.PATENT_NO = D.PATENT_NO AND H.DATA_LEVEL = D.DATA_LEVEL"
					+ "    LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT SD ON D.INVENTOR_STAFF_NO = SD.STAFF_NUMBER "
					+ "	   LEFT JOIN RICH.RH_Q_PATENT_DTL QD ON H.PATENT_NO = QD.PATENT_NO "
					+ "    LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON "
					+ "        TO_DATE(NVL(H.PATENT_MONTH, '01') || '/' || H.PATENT_YEAR, 'MM/YYYY') BETWEEN  "
					+ "        TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY')");
			sqlBuf.append(" WHERE H.DATA_LEVEL = '"+dataLevel+"' AND QD.CONSENT_IND = 'Y' ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND D.INVENTOR_STAFF_NO = '"+staffNo+"' ");
			}

			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(H.PATENT_MONTH, '"+startMonthStr+"') || '/' || H.PATENT_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE_EACH "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, SD.DEPT_CODE_EACH ");
			}else {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC ");
			}
			
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Summary obj = new Summary();
				obj.setPeriod_id(rs.getString("PERIOD_ID"));
				obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
				obj.setCount(rs.getString("C"));
				if (deptList != null) {
					obj.setFacDept(rs.getString("DEPT_CODE_EACH"));
				}
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
}
