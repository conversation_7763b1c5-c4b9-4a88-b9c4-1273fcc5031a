package hk.eduhk.rich.entity.patent;

import java.util.Date;
import java.sql.Timestamp;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@Entity
@Table(name = "RH_Q_PATENT_HDR")
public class PatentHeader_Q extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PatentHeader_Q.class.toString());
	
	@Id
	@GeneratedValue(generator = "riPatHeadSeq")
	@SequenceGenerator(name = "riPatHeadSeq", sequenceName = "RH_RI_NO_SEQ", allocationSize = 1)
	@Column(name = "PATENT_NO")
	private int patent_no;
	
	@Column(name = "PUBLISH_STATUS")     
	private String publish_status;
	                                     
	@Column(name = "LAST_MODIFIED_DATE") 
	private Date last_modified_date;
	                                     
	@Column(name = "LAST_MODIFIED_BY")   
	private String last_modified_by;
	                                     
	@Column(name = "LAST_PUBLISHED_DATE")
	private Date last_published_date;
	                                     
	@Column(name = "LAST_PUBLISHED_BY")  
	private String last_published_by;
	                                     
	@Column(name = "INST_DISPLAY_IND")   
	private String inst_display_ind;
	                                     
	@Column(name = "INST_VERIFIED_IND")  
	private String inst_verified_ind;
	                                     
	@Column(name = "INST_VERIFIED_DATE") 
	private Date inst_verified_date;
	                                     
	@Column(name = "CDCF_STATUS")        
	private String cdcf_status;
	                                     
	@Column(name = "CDCF_GEN_IND")       
	private String cdcf_gen_ind;
	                                     
	@Column(name = "CDCF_GEN_DATE")      
	private Date cdcf_gen_date;
	                                     
	@Column(name = "CDCF_PROCESSED_IND") 
	private String cdcf_processed_ind;
	                                     
	@Column(name = "CDCF_PROCESSED_DATE")
	private Date cdcf_processed_date;
	                                     
	@Column(name = "CDCF_SELECTED_IND")  
	private String cdcf_selected_ind;
	                                     
	@Column(name = "CDCF_CHANGED_IND")   
	private String cdcf_changed_ind;
	                                     
	@Column(name = "REMARKS")            
	private String remarks;
	                                     
	@Column(name = "BULLETIN_IND")       
	private String bulletin_ind;
	                                     
	@Column(name = "PUBLISH_FREQ")       
	private Integer publish_freq;


	
	public int getPatent_no()
	{
		return patent_no;
	}


	
	public void setPatent_no(int patent_no)
	{
		this.patent_no = patent_no;
	}


	
	public String getPublish_status()
	{
		return publish_status;
	}


	
	public void setPublish_status(String publish_status)
	{
		this.publish_status = publish_status;
	}


	
	public Date getLast_modified_date()
	{
		return last_modified_date;
	}


	
	public void setLast_modified_date(Date last_modified_date)
	{
		this.last_modified_date = last_modified_date;
	}


	
	public String getLast_modified_by()
	{
		return last_modified_by;
	}


	
	public void setLast_modified_by(String last_modified_by)
	{
		this.last_modified_by = last_modified_by;
	}


	
	public Date getLast_published_date()
	{
		return last_published_date;
	}


	
	public void setLast_published_date(Date last_published_date)
	{
		this.last_published_date = last_published_date;
	}


	
	public String getLast_published_by()
	{
		return last_published_by;
	}


	
	public void setLast_published_by(String last_published_by)
	{
		this.last_published_by = last_published_by;
	}


	
	public String getInst_display_ind()
	{
		return inst_display_ind;
	}


	
	public void setInst_display_ind(String inst_display_ind)
	{
		this.inst_display_ind = inst_display_ind;
	}


	
	public String getInst_verified_ind()
	{
		return inst_verified_ind;
	}


	
	public void setInst_verified_ind(String inst_verified_ind)
	{
		this.inst_verified_ind = inst_verified_ind;
	}


	
	public Date getInst_verified_date()
	{
		return inst_verified_date;
	}


	
	public void setInst_verified_date(Date inst_verified_date)
	{
		this.inst_verified_date = inst_verified_date;
	}


	
	public String getCdcf_status()
	{
		return cdcf_status;
	}


	
	public void setCdcf_status(String cdcf_status)
	{
		this.cdcf_status = cdcf_status;
	}


	
	public String getCdcf_gen_ind()
	{
		return cdcf_gen_ind;
	}


	
	public void setCdcf_gen_ind(String cdcf_gen_ind)
	{
		this.cdcf_gen_ind = cdcf_gen_ind;
	}


	
	public Date getCdcf_gen_date()
	{
		return cdcf_gen_date;
	}


	
	public void setCdcf_gen_date(Date cdcf_gen_date)
	{
		this.cdcf_gen_date = cdcf_gen_date;
	}


	
	public String getCdcf_processed_ind()
	{
		return cdcf_processed_ind;
	}


	
	public void setCdcf_processed_ind(String cdcf_processed_ind)
	{
		this.cdcf_processed_ind = cdcf_processed_ind;
	}


	
	public Date getCdcf_processed_date()
	{
		return cdcf_processed_date;
	}


	
	public void setCdcf_processed_date(Date cdcf_processed_date)
	{
		this.cdcf_processed_date = cdcf_processed_date;
	}


	
	public String getCdcf_selected_ind()
	{
		return cdcf_selected_ind;
	}


	
	public void setCdcf_selected_ind(String cdcf_selected_ind)
	{
		this.cdcf_selected_ind = cdcf_selected_ind;
	}


	
	public String getCdcf_changed_ind()
	{
		return cdcf_changed_ind;
	}


	
	public void setCdcf_changed_ind(String cdcf_changed_ind)
	{
		this.cdcf_changed_ind = cdcf_changed_ind;
	}


	
	public String getRemarks()
	{
		return remarks;
	}


	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	
	public String getBulletin_ind()
	{
		return bulletin_ind;
	}


	
	public void setBulletin_ind(String bulletin_ind)
	{
		this.bulletin_ind = bulletin_ind;
	}


	
	public Integer getPublish_freq()
	{
		return publish_freq;
	}


	
	public void setPublish_freq(Integer publish_freq)
	{
		this.publish_freq = publish_freq;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + patent_no;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatentHeader_Q other = (PatentHeader_Q) obj;
		if (patent_no != other.patent_no)
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "PatentHeader_Q [patent_no=" + patent_no + ", publish_status=" + publish_status + ", last_modified_date="
				+ last_modified_date + ", last_modified_by=" + last_modified_by + ", last_published_date="
				+ last_published_date + ", last_published_by=" + last_published_by + ", inst_display_ind="
				+ inst_display_ind + ", inst_verified_ind=" + inst_verified_ind + ", inst_verified_date="
				+ inst_verified_date + ", cdcf_status=" + cdcf_status + ", cdcf_gen_ind=" + cdcf_gen_ind
				+ ", cdcf_gen_date=" + cdcf_gen_date + ", cdcf_processed_ind=" + cdcf_processed_ind
				+ ", cdcf_processed_date=" + cdcf_processed_date + ", cdcf_selected_ind=" + cdcf_selected_ind
				+ ", cdcf_changed_ind=" + cdcf_changed_ind + ", remarks=" + remarks + ", bulletin_ind=" + bulletin_ind
				+ ", publish_freq=" + publish_freq + "]";
	}

	

}
