package hk.eduhk.rich.entity.patent;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class PatentDetails_Q_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="patent_no")
	private Integer patent_no;
	
	@Column(name="staff_no")
	private String staff_no;

	
	
	public Integer getPatent_no()
	{
		return patent_no;
	}


	
	public void setPatent_no(Integer patent_no)
	{
		this.patent_no = patent_no;
	}


	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((patent_no == null) ? 0 : patent_no.hashCode());
		result = prime * result + ((staff_no == null) ? 0 : staff_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatentDetails_Q_PK other = (PatentDetails_Q_PK) obj;
		if (patent_no == null)
		{
			if (other.patent_no != null)
				return false;
		}
		else if (!patent_no.equals(other.patent_no))
			return false;
		if (staff_no == null)
		{
			if (other.staff_no != null)
				return false;
		}
		else if (!staff_no.equals(other.staff_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PatentDetails_Q_PK [patent_no=" + patent_no + ", staff_no=" + staff_no + "]";
	}

	

}
