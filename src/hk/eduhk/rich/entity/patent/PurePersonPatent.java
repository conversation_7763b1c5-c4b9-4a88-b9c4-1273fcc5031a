package hk.eduhk.rich.entity.patent;


import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import hk.eduhk.rich.entity.project.ProjectDetails;
import hk.eduhk.rich.util.PersistenceManager;


//RH_PURE_PERSON_PATENT_V
public class PurePersonPatent{
	
	public static Logger logger = Logger.getLogger(PurePersonPatent.class.toString());
	
	private String source_id;
	
	private Integer pid;	
	
	private int patent_no;
	
	private String patent_name;	
	
	private String patent_serial_no;
	
	private String patent_granted;
	
	private String patent_contry;
	
	private String patent_type;
	
	private String short_desc;
	
	private String full_desc;
	
	private Integer patent_year;
	
	private Integer patent_month;
	
	private Integer patent_day;

	
	public String getSource_id()
	{
		return source_id;
	}

	
	public void setSource_id(String source_id)
	{
		this.source_id = source_id;
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public int getPatent_no()
	{
		return patent_no;
	}

	
	public void setPatent_no(int patent_no)
	{
		this.patent_no = patent_no;
	}

	
	public String getPatent_name()
	{
		return patent_name;
	}

	
	public void setPatent_name(String patent_name)
	{
		this.patent_name = patent_name;
	}

	
	public String getPatent_serial_no()
	{
		return patent_serial_no;
	}

	
	public void setPatent_serial_no(String patent_serial_no)
	{
		this.patent_serial_no = patent_serial_no;
	}

	
	public String getPatent_granted()
	{
		return patent_granted;
	}

	
	public void setPatent_granted(String patent_granted)
	{
		this.patent_granted = patent_granted;
	}

	
	public String getPatent_contry()
	{
		return patent_contry;
	}

	
	public void setPatent_contry(String patent_contry)
	{
		this.patent_contry = patent_contry;
	}

	
	public String getPatent_type()
	{
		return patent_type;
	}

	
	public void setPatent_type(String patent_type)
	{
		this.patent_type = patent_type;
	}

	
	public String getShort_desc()
	{
		return short_desc;
	}

	
	public void setShort_desc(String short_desc)
	{
		this.short_desc = short_desc;
	}

	
	public String getFull_desc()
	{
		return full_desc;
	}

	
	public void setFull_desc(String full_desc)
	{
		this.full_desc = full_desc;
	}

	
	public Integer getPatent_year()
	{
		return patent_year;
	}

	
	public void setPatent_year(Integer patent_year)
	{
		this.patent_year = patent_year;
	}

	
	public Integer getPatent_month()
	{
		return patent_month;
	}

	
	public void setPatent_month(Integer patent_month)
	{
		this.patent_month = patent_month;
	}

	
	public Integer getPatent_day()
	{
		return patent_day;
	}

	
	public void setPatent_day(Integer patent_day)
	{
		this.patent_day = patent_day;
	}


	@Override
	public String toString()
	{
		return "PurePersonPatent [source_id=" + source_id + ", pid=" + pid + ", patent_no=" + patent_no + ", patent_name="
				+ patent_name + ", patent_serial_no=" + patent_serial_no + ", patent_granted=" + patent_granted
				+ ", patent_contry=" + patent_contry + ", patent_type=" + patent_type + ", short_desc=" + short_desc
				+ ", full_desc=" + full_desc + ", patent_year=" + patent_year + ", patent_month=" + patent_month
				+ ", patent_day=" + patent_day + "]";
	}


}
