package hk.eduhk.rich.entity.patent;

import java.sql.SQLException;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentDetails_Q;
import hk.eduhk.rich.entity.patent.PatentHeader_P;
import hk.eduhk.rich.entity.patent.PatentHeader_Q;
import hk.eduhk.rich.entity.project.ProjectDAO;

@Entity
@Table(name = "RH_P_PATENT_DTL")
public class PatentDetails_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PatentDetails_P.class.toString());
	
	@EmbeddedId
	private PatentDetails_P_PK pk = new PatentDetails_P_PK();
	
	@Column(name = "INVENTOR_NAME")
	private String inventor_name;	
	
	@Column(name = "NON_IED_STAFF_FLAG")
	private String non_ied_staff_flag;	

	@Column(name = "INVENTOR_STAFF_NO")
	private String inventor_staff_no;

	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "patent_no", referencedColumnName = "patent_no", nullable = false, insertable = false, updatable = false)
	private PatentHeader_Q patentHeader_q;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "patent_no", referencedColumnName = "patent_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", nullable = false, insertable = false, updatable = false)
	})
	private PatentHeader_P patentHeader_p;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "patent_no", referencedColumnName = "patent_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "inventor_staff_no", referencedColumnName = "staff_no", nullable = false, insertable = false, updatable = false)
	})
	private PatentDetails_Q patentDetails_q;
	
	public PatentDetails_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(PatentDetails_P_PK pk)
	{
		this.pk = pk;
	}


	
	public String getInventor_name()
	{
		return inventor_name;
	}


	
	public void setInventor_name(String inventor_name)
	{
		this.inventor_name = inventor_name;
	}


	
	public String getNon_ied_staff_flag()
	{
		return non_ied_staff_flag;
	}


	
	public void setNon_ied_staff_flag(String non_ied_staff_flag)
	{
		this.non_ied_staff_flag = non_ied_staff_flag;
	}


	
	public String getInventor_staff_no()
	{
		return inventor_staff_no;
	}


	
	public void setInventor_staff_no(String inventor_staff_no)
	{
		this.inventor_staff_no = inventor_staff_no;
	}


	
	public PatentHeader_Q getPatentHeader_q()
	{
		if (patentHeader_q != null) {
			try {
				patentHeader_q.getPatent_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PatentDAO dao = PatentDAO.getInstance();
					patentHeader_q = dao.getPatentHeader_Q(getPk().getPatent_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return patentHeader_q;
	}


	
	public void setPatentHeader_q(PatentHeader_Q patentHeader_q)
	{
		this.patentHeader_q = patentHeader_q;
	}


	
	public PatentHeader_P getPatentHeader_p()
	{
		if (patentHeader_p != null) {
			try {
				patentHeader_p.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PatentDAO dao = PatentDAO.getInstance();
					patentHeader_p = dao.getPatentHeader_P(getPk().getPatent_no(), getPk().getData_level());
				}
				else
				{
					throw re;
				}
			}
		}
		return patentHeader_p;
	}


	
	public void setPatentHeader_p(PatentHeader_P patentHeader_p)
	{
		this.patentHeader_p = patentHeader_p;
	}


	
	public PatentDetails_Q getPatentDetails_q()
	{
		if (patentDetails_q != null) {
			try {
				patentDetails_q.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PatentDAO dao = PatentDAO.getInstance();
					patentDetails_q = dao.getPatentDetails_Q1(getPk().getPatent_no(), getInventor_staff_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return patentDetails_q;
	}


	
	public void setPatentDetails_q(PatentDetails_Q patentDetails_q)
	{
		this.patentDetails_q = patentDetails_q;
	}

	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatentDetails_P other = (PatentDetails_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PatentDetails_P [pk=" + pk + ", inventor_name=" + inventor_name + ", non_ied_staff_flag="
				+ non_ied_staff_flag + ", inventor_staff_no=" + inventor_staff_no + "]";
	}


}
