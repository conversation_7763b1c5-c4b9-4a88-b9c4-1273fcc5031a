package hk.eduhk.rich.entity.patent;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import hk.eduhk.rich.entity.BaseRIFull;


@SuppressWarnings("serial")
public class Patent extends BaseRIFull
{
	private String staffNumber;
	private String staffFullname;
	private String staffDept;
	private String patentName;
	private String patentDay;
	private String patentMonth;
	private String patentYear;
	private String patentGranted;
	private String patentType;
	private String serialNo;
	private String country;
	private String fullDesc;
	private String shortDesc;
	private String IEdWorkInd;
	
	// for RH-ADM-006 Research Information Listing
	private String inventor_list;
	private String patentDate;

	private String cdcf_selected_ind;

	public Patent()
	{
	}

	
	public String getPatentName()
	{
		return patentName;
	}

	public void setPatentName(String patentName)
	{
		this.patentName = patentName;
	}

	public String getPatentDay()
	{
		return patentDay;
	}

	public void setPatentDay(String patentDay)
	{
		this.patentDay = patentDay;
	}

	public String getPatentMonth()
	{
		return patentMonth;
	}

	public void setPatentMonth(String patentMonth)
	{
		this.patentMonth = patentMonth;
	}

	public String getPatentYear()
	{
		return patentYear;
	}

	public void setPatentYear(String patentYear)
	{
		this.patentYear = patentYear;
	}

	public String getPatentGranted()
	{
		return patentGranted;
	}

	public void setPatentGranted(String patentGranted)
	{
		this.patentGranted = patentGranted;
	}

	public String getPatentType()
	{
		return patentType;
	}

	public void setPatentType(String patentType)
	{
		this.patentType = patentType;
	}

	public String getSerialNo()
	{
		return serialNo;
	}

	public void setSerialNo(String serialNo)
	{
		this.serialNo = serialNo;
	}

	public String getCountry()
	{
		return country;
	}

	public void setCountry(String country)
	{
		this.country = country;
	}

	public String getFullDesc()
	{
		return fullDesc;
	}

	public void setFullDesc(String fullDesc)
	{
		this.fullDesc = fullDesc;
	}

	public String getShortDesc()
	{
		return shortDesc;
	}

	public void setShortDesc(String shortDesc)
	{
		this.shortDesc = shortDesc;
	}

  public String getIEdWorkInd()
  {
    return IEdWorkInd;
  }

  public void setIEdWorkInd(String IEdWorkInd)
  {
    this.IEdWorkInd = IEdWorkInd;
  }

	public String toHTMLString()
	{
		return "";
	}


	public String toString()
	{
		return "pid=" + getPid() + ", " +
					 "riNo=" + getRINo() + ", " + 
					 "staffNumber=" + StringUtils.defaultString(getStaffNumber()) + ", " +
					 "patentName=" + StringUtils.defaultString(getPatentName()) + ", " +
					 "fullDesc=" + StringUtils.defaultString(getFullDesc()) + ", " +
					 "shortDesc=" + StringUtils.defaultString(getShortDesc());
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("pid") + "\":" + "\"" + JSONObject.escape(String.valueOf(getPid())) + "\"," +
								 "\"" + JSONObject.escape("RINo") + "\":" + "\"" + JSONObject.escape(String.valueOf(getRINo())) + "\"," +
								 "\"" + JSONObject.escape("staffNumber") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getStaffNumber())) + "\"," +
								 "\"" + JSONObject.escape("patentName") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPatentName())) + "\"," +
								 "\"" + JSONObject.escape("patentDay") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPatentDay())) + "\"," +
								 "\"" + JSONObject.escape("patentMonth") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPatentMonth())) + "\"," +
								 "\"" + JSONObject.escape("patentYear") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPatentYear())) + "\"," +
								 "\"" + JSONObject.escape("patentGranted") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPatentGranted())) + "\"," +
								 "\"" + JSONObject.escape("patentType") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPatentType())) + "\"," +
								 "\"" + JSONObject.escape("serialNo") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getSerialNo())) + "\"," +
								 "\"" + JSONObject.escape("country") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCountry())) + "\"," +
								 "\"" + JSONObject.escape("fullDesc") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getFullDesc())) + "\"," +
								 "\"" + JSONObject.escape("shortDesc") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getShortDesc())) + "\"," +
								 "\"" + JSONObject.escape("IEdWorkInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getIEdWorkInd())) + "\"");

		strBuf.append(",\"" + JSONObject.escape("publishStatus") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPublishStatus())) + "\"" +
									",\"" + JSONObject.escape("lastModifiedBy") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getLastModifiedBy())) + "\"" +
								  ",\"" + JSONObject.escape("lastModifiedDate") + "\":" + (getLastModifiedDate() != null ? "new Date(" + getLastModifiedDate().getTime() + ")" : "\"\"") +
									",\"" + JSONObject.escape("lastPublishedBy") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getLastPublishedBy())) + "\"" +
								  ",\"" + JSONObject.escape("lastPublishedDate") + "\":" + (getLastPublishedDate() != null ? "new Date(" + getLastPublishedDate().getTime() + ")" : "\"\"") +
									",\"" + JSONObject.escape("instDisplayInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getInstDisplayInd())) + "\"" +
									",\"" + JSONObject.escape("instVerifiedInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getInstVerifiedInd())) + "\"" +
								  ",\"" + JSONObject.escape("instVerifiedDate") + "\":" + (getInstVerifiedDate() != null ? "new Date(" + getInstVerifiedDate().getTime() + ")" : "\"\"") +
									",\"" + JSONObject.escape("CDCFStatus") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCDCFStatus())) + "\"" +
									",\"" + JSONObject.escape("CDCFGenInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCDCFGenInd())) + "\"" +
								  ",\"" + JSONObject.escape("CDCFGenDate") + "\":" + (getCDCFGenDate() != null ? "new Date(" + getCDCFGenDate().getTime() + ")" : "\"\"") +
									",\"" + JSONObject.escape("CDCFProcessInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCDCFProcessInd())) + "\"" +
								  ",\"" + JSONObject.escape("CDCFProcessDate") + "\":" + (getCDCFProcessDate() != null ? "new Date(" + getCDCFProcessDate().getTime() + ")" : "\"\"") +
									",\"" + JSONObject.escape("CDCFSelectedInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCDCFSelectedInd())) + "\"" +
									",\"" + JSONObject.escape("CDCFChangedInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCDCFChangedInd())) + "\"" +
									",\"" + JSONObject.escape("bulletinInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getBulletinInd())) + "\"" +
									",\"" + JSONObject.escape("remarks") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getRemarks())) + "\"");

		strBuf.append(",\"" + JSONObject.escape("consentInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getConsentInd())) + "\"" +
									",\"" + JSONObject.escape("displayInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getDisplayInd())) + "\"" +
									",\"" + JSONObject.escape("creatorInd") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCreatorInd())) + "\"");


		if (getRIDetailList() != null)
		{
			strBuf.append(",");
			strBuf.append("\"" + JSONObject.escape("detailList") + "\":" + JSONArray.toJSONString(getRIDetailList()));
		}

		if (getRIAttachmentList() != null)
		{
			strBuf.append(",");
			strBuf.append("\"" + JSONObject.escape("attachmentList") + "\":" + JSONArray.toJSONString(getRIAttachmentList()));
		}

		strBuf.append("}");

		return strBuf.toString();
	}


  public void set_staffNumber(String staffNumber)
  {
    this.staffNumber = staffNumber;
  }


  public String get_staffNumber()
  {
    return staffNumber;
  }


  public void setStaffFullname(String staffFullname)
  {
    this.staffFullname = staffFullname;
  }


  public String getStaffFullname()
  {
    return staffFullname;
  }


  public void setStaffDept(String staffDept)
  {
    this.staffDept = staffDept;
  }


  public String getStaffDept()
  {
    return staffDept;
  }


  public void set_staffFullname(String staffFullname)
  {
    this.staffFullname = staffFullname;
  }


  public String get_staffFullname()
  {
    return staffFullname;
  }


  public void set_staffDept(String staffDept)
  {
    this.staffDept = staffDept;
  }


  public String get_staffDept()
  {
    return staffDept;
  }



	public String getInventor_list()
	{
		return inventor_list;
	}
	
	
	
	public void setInventor_list(String inventor_list)
	{
		this.inventor_list = inventor_list;
	}
	
	
	
	public String getPatentDate()
	{
		return patentDate;
	}
	
	
	
	public void setPatentDate(String patentDate)
	{
		this.patentDate = patentDate;
	}
	
	public int getriNo() {
		return getRINo();
	}
	
	public String getCdcf_selected_ind()
	{
		return cdcf_selected_ind;
	}


	
	public void setCdcf_selected_ind(String cdcf_selected_ind)
	{
		this.cdcf_selected_ind = cdcf_selected_ind;
	}

}