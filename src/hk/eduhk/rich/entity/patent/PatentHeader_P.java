package hk.eduhk.rich.entity.patent;

import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_P_PATENT_HDR")
public class PatentHeader_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PatentHeader_P.class.toString());
	
	@EmbeddedId
	private PatentHeader_P_PK pk = new PatentHeader_P_PK();
	                                
	@Column(name = "patent_day")    
	private Integer patent_day;
	                                
	@Column(name = "patent_month")  
	private Integer patent_month;
	                                
	@Column(name = "patent_year")   
	private Integer patent_year;
	                                
	@Column(name = "patent_granted")
	private String patent_granted;
	                                
	@Column(name = "patent_name")   
	private String patent_name;
	                                
	@Column(name = "serial_no")     
	private String serial_no;
	                                
	@Column(name = "country")       
	private String country;
	                                
	@Column(name = "patent_type")   
	private String patent_type;
	                                
	@Column(name = "short_desc")    
	private String short_desc;
	                                
	@Column(name = "full_desc")     
	private String full_desc;
	                                
	@Column(name = "ied_work_ind")  
	private String ied_work_ind;




	
	public PatentHeader_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(PatentHeader_P_PK pk)
	{
		this.pk = pk;
	}


	
	
	public Integer getPatent_day()
	{
		return patent_day;
	}


	
	public void setPatent_day(Integer patent_day)
	{
		this.patent_day = patent_day;
	}


	
	public Integer getPatent_month()
	{
		return patent_month;
	}


	
	public void setPatent_month(Integer patent_month)
	{
		this.patent_month = patent_month;
	}


	
	public Integer getPatent_year()
	{
		return patent_year;
	}


	
	public void setPatent_year(Integer patent_year)
	{
		this.patent_year = patent_year;
	}


	public String getPatent_granted()
	{
		return patent_granted;
	}


	
	public void setPatent_granted(String patent_granted)
	{
		this.patent_granted = patent_granted;
	}


	
	public String getPatent_name()
	{
		return patent_name;
	}


	
	public void setPatent_name(String patent_name)
	{
		this.patent_name = patent_name;
	}


	
	public String getSerial_no()
	{
		return serial_no;
	}


	
	public void setSerial_no(String serial_no)
	{
		this.serial_no = serial_no;
	}


	
	public String getCountry()
	{
		return country;
	}


	
	public void setCountry(String country)
	{
		this.country = country;
	}


	
	public String getPatent_type()
	{
		return patent_type;
	}


	
	public void setPatent_type(String patent_type)
	{
		this.patent_type = patent_type;
	}


	
	public String getShort_desc()
	{
		return short_desc;
	}


	
	public void setShort_desc(String short_desc)
	{
		this.short_desc = short_desc;
	}


	
	public String getFull_desc()
	{
		return full_desc;
	}


	
	public void setFull_desc(String full_desc)
	{
		this.full_desc = full_desc;
	}


	
	public String getIed_work_ind()
	{
		return ied_work_ind;
	}


	
	public void setIed_work_ind(String ied_work_ind)
	{
		this.ied_work_ind = ied_work_ind;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatentHeader_P other = (PatentHeader_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PatentHeader_P [pk=" + pk + ", patent_day=" + patent_day
				+ ", patent_month=" + patent_month + ", patent_year=" + patent_year + ", patent_granted="
				+ patent_granted + ", patent_name=" + patent_name + ", serial_no=" + serial_no + ", country=" + country
				+ ", patent_type=" + patent_type + ", short_desc=" + short_desc + ", full_desc=" + full_desc
				+ ", ied_work_ind=" + ied_work_ind + "]";
	}


	


	


}
