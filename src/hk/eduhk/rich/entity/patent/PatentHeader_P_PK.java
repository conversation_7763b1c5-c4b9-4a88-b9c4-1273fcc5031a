package hk.eduhk.rich.entity.patent;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class PatentHeader_P_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="PATENT_NO")
	private Integer patent_no;
	
	@Column(name="data_level")
	private String data_level;	



	
	public Integer getPatent_no()
	{
		return patent_no;
	}


	
	public void setPatent_no(Integer patent_no)
	{
		this.patent_no = patent_no;
	}


	public String getData_level()
	{
		return data_level;
	}

	
	public void setData_level(String data_level)
	{
		this.data_level = data_level;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((data_level == null) ? 0 : data_level.hashCode());
		result = prime * result + ((patent_no == null) ? 0 : patent_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatentHeader_P_PK other = (PatentHeader_P_PK) obj;
		if (data_level == null)
		{
			if (other.data_level != null)
				return false;
		}
		else if (!data_level.equals(other.data_level))
			return false;
		if (patent_no == null)
		{
			if (other.patent_no != null)
				return false;
		}
		else if (!patent_no.equals(other.patent_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PatentHeader_P_PK [patent_no=" + patent_no + ", data_level=" + data_level + "]";
	}


}
