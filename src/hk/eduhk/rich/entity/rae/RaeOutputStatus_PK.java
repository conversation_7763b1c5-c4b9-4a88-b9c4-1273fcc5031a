package hk.eduhk.rich.entity.rae;

import java.io.Serializable;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class RaeOutputStatus_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="staff_number")
	private String staff_number;
	
	@Column(name="output_no")
	private Integer output_no;

	
	public String getStaff_number()
	{
		return staff_number;
	}

	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}

	
	public Integer getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}


	

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((output_no == null) ? 0 : output_no.hashCode());
		result = prime * result + ((staff_number == null) ? 0 : staff_number.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutputStatus_PK other = (RaeOutputStatus_PK) obj;
		if (output_no == null)
		{
			if (other.output_no != null)
				return false;
		}
		else if (!output_no.equals(other.output_no))
			return false;
		if (staff_number == null)
		{
			if (other.staff_number != null)
				return false;
		}
		else if (!staff_number.equals(other.staff_number))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "RaeOutputStatus_PK [staff_number=" + staff_number + ", output_no=" + output_no + "]";
	}
	

}
