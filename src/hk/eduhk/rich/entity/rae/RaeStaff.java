package hk.eduhk.rich.entity.rae;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;

@Entity
@Table(name = "RH_RAE_STAFF")
@SuppressWarnings("serial")
public class RaeStaff extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeStaff.class.toString());
		
	@Id
    @Column(name = "staff_number")
    private String staffNumber;
	
	@Column(name = "pid")
    private Integer pid;
	
    @Column(name = "staff_title")
    private String staffTitle;

    @Column(name = "staff_name")
    private String staffName;

    @Column(name = "uoa_code")
    private String uoaCode;

    @Column(name = "faculty")
    private String faculty;

    @Column(name = "department")
    private String department;

    @Column(name = "no_reduced_ri")
    private Integer noReducedRi;

    @Column(name = "post")
    private String post;

    @Column(name = "staff_serial")
    private String staffSerial;

    @Column(name = "cca_output_req")
    private String ccaOutputReq;

    @Column(name = "staff_grade")
    private String staffGrade;

    @Column(name = "sel_remark")
    private String selRemark;

    @Column(name = "ra_code")
    private String raCode;

    @Column(name = "surname")
    private String surname;

    @Column(name = "given_name")
    private String givenName;

    @Column(name = "chi_name")
    private String chiName;

    @Column(name = "dept_desc")
    private String deptDesc;

    @Column(name = "new_researcher")
    private Boolean newResearcher;

    @Column(name = "len_exp_new_rar")
    private Integer lenExpNewRar;

    @Column(name = "rank")
    private String rank;

    @Column(name = "orcid")
    private String orcid;

    @Column(name = "appro_ref_no")
    private String approRefNo;

    @Column(name = "inp_remarks")
    private String inpRemarks;

    @Column(name = "sd_code")
    private String sdCode;

    @Column(name = "start_date")
    private Date startDate;

    @Column(name = "staff_grade_jtf")
    private String staffGradeJtf;

    @Column(name = "coord_faculty")
    private String coordFaculty;

    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "staff_number", referencedColumnName = "staff_number", nullable = false, insertable = false, updatable = false)
	private StaffIdentity staffIdentity;
    
    @Transient
	private String panelInfo;
	
	@Transient
	private String uoaInfo;
	
	@Transient
	private String raInfo;
	
	@Transient
	private String outSubNoStr ="4";
	
	
    
	
	public String getOutSubNoStr()
	{
		if(getNoReducedRi() > 0 )
			outSubNoStr = ( 4 -  getNoReducedRi()) +" to 4";
			
		return outSubNoStr;
	}


	
	public void setOutSubNoStr(String outSubNoStr)
	{
		this.outSubNoStr = outSubNoStr;
	}


	public String getStaffNumber()
	{
		return staffNumber;
	}

	
	public void setStaffNumber(String staffNumber)
	{
		this.staffNumber = staffNumber;
	}

	
	
	public Integer getPid()
	{
		return pid;
	}


	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}


	public String getStaffTitle()
	{
		return staffTitle;
	}

	
	public void setStaffTitle(String staffTitle)
	{
		this.staffTitle = staffTitle;
	}

	
	public String getStaffName()
	{
		return staffName;
	}

	
	public void setStaffName(String staffName)
	{
		this.staffName = staffName;
	}

	
	public String getUoaCode()
	{
		return uoaCode;
	}

	
	public void setUoaCode(String uoaCode)
	{
		this.uoaCode = uoaCode;
	}

	
	public String getFaculty()
	{
		return faculty;
	}

	
	public void setFaculty(String faculty)
	{
		this.faculty = faculty;
	}

	
	public String getDepartment()
	{
		return department;
	}

	
	public void setDepartment(String department)
	{
		this.department = department;
	}

	
	public Integer getNoReducedRi()
	{
		return noReducedRi;
	}

	
	public void setNoReducedRi(Integer noReducedRi)
	{
		this.noReducedRi = noReducedRi;
	}

	
	public String getPost()
	{
		return post;
	}

	
	public void setPost(String post)
	{
		this.post = post;
	}

	
	public String getStaffSerial()
	{
		return staffSerial;
	}

	
	public void setStaffSerial(String staffSerial)
	{
		this.staffSerial = staffSerial;
	}

	
	public String getCcaOutputReq()
	{
		return ccaOutputReq;
	}

	
	public void setCcaOutputReq(String ccaOutputReq)
	{
		this.ccaOutputReq = ccaOutputReq;
	}

	
	public String getStaffGrade()
	{
		return staffGrade;
	}

	
	public void setStaffGrade(String staffGrade)
	{
		this.staffGrade = staffGrade;
	}

	
	public String getSelRemark()
	{
		return selRemark;
	}

	
	public void setSelRemark(String selRemark)
	{
		this.selRemark = selRemark;
	}

	
	public String getRaCode()
	{
		return raCode;
	}

	
	public void setRaCode(String raCode)
	{
		this.raCode = raCode;
	}

	
	public String getSurname()
	{
		return surname;
	}

	
	public void setSurname(String surname)
	{
		this.surname = surname;
	}

	
	public String getGivenName()
	{
		return givenName;
	}

	
	public void setGivenName(String givenName)
	{
		this.givenName = givenName;
	}

	
	public String getChiName()
	{
		return chiName;
	}

	
	public void setChiName(String chiName)
	{
		this.chiName = chiName;
	}

	
	public String getDeptDesc()
	{
		return deptDesc;
	}

	
	public void setDeptDesc(String deptDesc)
	{
		this.deptDesc = deptDesc;
	}

	
	public Boolean getNewResearcher()
	{
		return newResearcher;
	}

	
	public void setNewResearcher(Boolean newResearcher)
	{
		this.newResearcher = newResearcher;
	}

	
	public Integer getLenExpNewRar()
	{
		return lenExpNewRar;
	}

	
	public void setLenExpNewRar(Integer lenExpNewRar)
	{
		this.lenExpNewRar = lenExpNewRar;
	}

	
	public String getRank()
	{
		return rank;
	}

	
	public void setRank(String rank)
	{
		this.rank = rank;
	}

	
	public String getOrcid()
	{
		return orcid;
	}

	
	public void setOrcid(String orcid)
	{
		this.orcid = orcid;
	}

	
	public String getApproRefNo()
	{
		return approRefNo;
	}

	
	public void setApproRefNo(String approRefNo)
	{
		this.approRefNo = approRefNo;
	}

	
	public String getInpRemarks()
	{
		return inpRemarks;
	}

	
	public void setInpRemarks(String inpRemarks)
	{
		this.inpRemarks = inpRemarks;
	}

	
	public String getSdCode()
	{
		return sdCode;
	}

	
	public void setSdCode(String sdCode)
	{
		this.sdCode = sdCode;
	}

	
	public Date getStartDate()
	{
		return startDate;
	}

	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}

	
	public String getStaffGradeJtf()
	{
		return staffGradeJtf;
	}

	
	public void setStaffGradeJtf(String staffGradeJtf)
	{
		this.staffGradeJtf = staffGradeJtf;
	}

	
	public String getCoordFaculty()
	{
		return coordFaculty;
	}

	
	public void setCoordFaculty(String coordFaculty)
	{
		this.coordFaculty = coordFaculty;
	}


	
	public StaffIdentity getStaffIdentity()
	{
		if (staffIdentity != null) {
			try {
				staffIdentity.getStaff_number();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					StaffDAO dao = StaffDAO.getInstance();
					staffIdentity = dao.getStaffDetailsByStaffNo(getStaffNumber());
				}
				else
				{
					throw re;
				}
			}
		}
		return staffIdentity;
	}


	public String getPanelInfo()
	{
		if (panelInfo == null) {
			getUoaInfo();
		}
		return panelInfo;
	}

	
	public void setPanelInfo(String panelInfo)
	{
		this.panelInfo = panelInfo;
	}

	
	public String getUoaInfo()
	{
		if (uoaInfo == null) {
			RaePanelDAO panelDao = RaePanelDAO.getInstance();
			RaePanel d = panelDao.getRaePanel("RAE_UOA", getUoaCode());
			uoaInfo = d.getDescription();
			d = panelDao.getRaePanel("RAE_PANEL", d.getParent_lookup_code());
			panelInfo = "("+d.getPk().getLookup_code()+") "+d.getDescription();
		}
		return uoaInfo;
	}

	
	public void setUoaInfo(String uoaInfo)
	{
		this.uoaInfo = uoaInfo;
	}

	
	public String getRaInfo()
	{
		if (raInfo == null) {
			raInfo = "";
			if (getRaCode() != null) {
				RaePanelDAO panelDao = RaePanelDAO.getInstance();
				RaePanel d = panelDao.getRaePanel("RAE_RA", getRaCode());
				if (d != null)
					raInfo = d.getDescription();
			}
			/*RaePanelDAO panelDao = RaePanelDAO.getInstance();
			List<RaePanel> dataList = panelDao.getRaePanelListByParentLookupCode("RAE_RA", getUoaCode());
			List<String> raInfo_list = new ArrayList<String>();
			for(RaePanel d:dataList)
				raInfo_list.add(d.getDescription()) ;
			
			raInfo = String.join("<br/>", raInfo_list);*/
		}
		return raInfo;
	}

	
	public void setRaInfo(String raInfo)
	{
		this.raInfo = raInfo;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(staffNumber);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeStaff other = (RaeStaff) obj;
		return Objects.equals(staffNumber, other.staffNumber);
	}



	@Override
	public String toString()
	{
		return "RaeStaff [staffNumber=" + staffNumber + ", pid=" + pid + ", staffTitle=" + staffTitle + ", staffName="
				+ staffName + ", uoaCode=" + uoaCode + ", faculty=" + faculty + ", department=" + department
				+ ", noReducedRi=" + noReducedRi + ", post=" + post + ", staffSerial=" + staffSerial + ", ccaOutputReq="
				+ ccaOutputReq + ", staffGrade=" + staffGrade + ", selRemark=" + selRemark + ", raCode=" + raCode
				+ ", surname=" + surname + ", givenName=" + givenName + ", chiName=" + chiName + ", deptDesc="
				+ deptDesc + ", newResearcher=" + newResearcher + ", lenExpNewRar=" + lenExpNewRar + ", rank=" + rank
				+ ", orcid=" + orcid + ", approRefNo=" + approRefNo + ", inpRemarks=" + inpRemarks + ", sdCode="
				+ sdCode + ", startDate=" + startDate + ", staffGradeJtf=" + staffGradeJtf + ", coordFaculty="
				+ coordFaculty + "]";
	}




}
