package hk.eduhk.rich.entity.rae;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.publication.DisciplinaryArea;


@Embeddable
public class RaePanel_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "lookup_type")
	private String lookup_type;	
	
	@Column(name = "lookup_code")
	private String lookup_code;

	@Column(name = "language")
	private String language;

	
	public String getLookup_type()
	{
		return lookup_type;
	}

	
	public void setLookup_type(String lookup_type)
	{
		this.lookup_type = lookup_type;
	}

	
	public String getLookup_code()
	{
		return lookup_code;
	}

	
	public void setLookup_code(String lookup_code)
	{
		this.lookup_code = lookup_code;
	}

	
	public String getLanguage()
	{
		return language;
	}

	
	public void setLanguage(String language)
	{
		this.language = language;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((language == null) ? 0 : language.hashCode());
		result = prime * result + ((lookup_code == null) ? 0 : lookup_code.hashCode());
		result = prime * result + ((lookup_type == null) ? 0 : lookup_type.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaePanel_PK other = (RaePanel_PK) obj;
		if (language == null)
		{
			if (other.language != null)
				return false;
		}
		else if (!language.equals(other.language))
			return false;
		if (lookup_code == null)
		{
			if (other.lookup_code != null)
				return false;
		}
		else if (!lookup_code.equals(other.lookup_code))
			return false;
		if (lookup_type == null)
		{
			if (other.lookup_type != null)
				return false;
		}
		else if (!lookup_type.equals(other.lookup_type))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "RaePanel_PK [lookup_type=" + lookup_type + ", lookup_code=" + lookup_code + ", language=" + language
				+ "]";
	}
	


}
