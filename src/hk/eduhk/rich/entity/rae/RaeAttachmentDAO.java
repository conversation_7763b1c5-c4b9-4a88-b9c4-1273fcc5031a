package hk.eduhk.rich.entity.rae;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.DigestInputStream;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.util.*;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.ejb.EJBContext;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;

import hk.eduhk.rich.util.SecurityUtils;
import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.rae.RaeStaff;

public class RaeAttachmentDAO extends BaseDAO
{
	@Resource
	private EJBContext ctx;
	
	private static RaeAttachmentDAO instance = null;


	public static synchronized RaeAttachmentDAO getInstance()
	{
		if (instance == null) instance = new RaeAttachmentDAO();
		return instance;
	}

	public List<RaeAttachment> getRaeAttachmentListByOutputs(List<Integer> outputNoList)
	{
		List<RaeAttachment> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaeAttachment obj WHERE obj.output_no IN :output_no ORDER BY obj.output_no, obj.file_tag, obj.seq ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaeAttachment> q = em.createQuery(query, RaeAttachment.class);
			q.setParameter("output_no", outputNoList);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<RaeAttachment> getRaeAttachmentList(Integer output_no, String file_tag)
	{
		List<RaeAttachment> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaeAttachment obj WHERE obj.output_no =:output_no AND obj.file_tag =:file_tag ORDER BY seq ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaeAttachment> q = em.createQuery(query, RaeAttachment.class);
			q.setParameter("output_no", output_no);
			q.setParameter("file_tag", file_tag);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}

	public RaeAttachment updateRaeAttachment (RaeAttachment obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public RaeAttachment updateRaeAttachment(RaeAttachment appFile, InputStream fileInputStream) 
											throws IOException, GeneralSecurityException
	{
		try
		{
			MessageDigest digest = DigestUtils.getMd5Digest();
			Cipher cipher = SecurityUtils.getCipher(Cipher.ENCRYPT_MODE);
			File targetFile = new File(appFile.getFile_path());
			try
			(
				InputStream is = new CipherInputStream(new DigestInputStream(new BufferedInputStream(fileInputStream), digest), cipher);
				OutputStream os = new BufferedOutputStream(new FileOutputStream(targetFile)); 
			)
			{
				IOUtils.copy(is, os);

				// Convert the digest bytes to Hex String
				String checksum = new String(Hex.encodeHex(digest.digest())); 
				appFile.setHash_key(checksum);
			}
		}
		catch (IOException | GeneralSecurityException e)
		{
			ctx.setRollbackOnly();
			throw e;
		}
			
		updateEntity(appFile);
		return appFile;
	}
	
	public void deleteRaeAttachment (Integer file_id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			RaeAttachment obj = em.find(RaeAttachment.class, file_id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public RaeAttachment getRaeAttachmentByFileId(Integer file_id)
	{
		RaeAttachment obj = null;
		
		if (file_id != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(RaeAttachment.class, file_id);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
}