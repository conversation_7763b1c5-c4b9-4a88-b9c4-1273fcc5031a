package hk.eduhk.rich.entity.rae;

import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.rae.RaeStaff;

public class RaePanelDAO extends BaseDAO
{

	private static RaePanelDAO instance = null;


	public static synchronized RaePanelDAO getInstance()
	{
		if (instance == null) instance = new RaePanelDAO();
		return instance;
	}

	
	public List<RaePanel> getRaePanelList()
	{
		List<RaePanel> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaePanel obj ORDER BY lookup_level, print_order ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaePanel> q = em.createQuery(query, RaePanel.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}

	
	public RaePanel getRaePanel(String lookup_type, String lookup_code)
	{
		List<RaePanel> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaePanel obj WHERE obj.pk.lookup_type = :lookup_type AND obj.pk.lookup_code = :lookup_code ORDER BY print_order ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaePanel> q = em.createQuery(query, RaePanel.class);
			q.setParameter("lookup_type", lookup_type);
			q.setParameter("lookup_code", lookup_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<RaePanel> getRaePanelListByParentLookupCode(String lookup_type, String parent_lookup_code)
	{
		List<RaePanel> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaePanel obj WHERE obj.pk.lookup_type = :lookup_type AND obj.parent_lookup_code = :parent_lookup_code ORDER BY print_order ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaePanel> q = em.createQuery(query, RaePanel.class);
			q.setParameter("lookup_type", lookup_type);
			q.setParameter("parent_lookup_code", parent_lookup_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
}