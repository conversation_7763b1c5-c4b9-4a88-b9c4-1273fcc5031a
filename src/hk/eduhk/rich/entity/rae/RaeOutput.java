package hk.eduhk.rich.entity.rae;

import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.JournalRank;
import hk.eduhk.rich.entity.JournalRankDAO;


@Entity
@Table(name = "RH_RAE_SEARCH_V")
@SuppressWarnings("serial")
public class RaeOutput extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeOutput.class.toString());
		
	@Id
    @Column(name = "OUTPUT_NO")
    private Integer output_no;

	@Column(name = "FACULTY")
	private String faculty;

	@Column(name = "DEPARTMENT")
	private String department;
	
	@Column(name = "UOA")
	private String uoa;	
	
	@Column(name = "jcr")
	private String jcr;	
	
	@Column(name = "sjr")
	private String sjr;	
	
	@Column(name = "STAFF_NAME")
	private String 	staff_name;
	
	@Column(name = "STAFF_NUMBER")
	private String 	staff_number;
	
	@Column(name = "OTH_OUTPUT_TYPE")
	private String 	oth_output_type;
	
	@Column(name = "CONCATENATED_AUTHOR_NAME")
	private String 	concatenated_author_name;
	
	@Column(name = "APA_CITATION")
	private String 	apa_citation;
	
	@Column(name = "SAP_OUTPUT_TYPE")
	private String 	sap_output_type;
	
	@Column(name = "OUTPUT_TYPE")
	private String 	output_type;
	
	@Column(name = "FROM_MONTH")
	private Integer from_month;
	
	@Column(name = "FROM_YEAR")
	private Integer	from_year;
	
	@Column(name = "SEL_TYPE")
	private String 	sel_type;
	
	@Column(name = "INFO_COMP")
	private String info_comp;
	
	@Column(name = "CITATION_CHK_CODE", length = 20)
	private String citation_chk_code;
	
	@Column(name = "CITATION_CHK_ABS_CODE", length = 20)
	private String citation_chk_abs_code;

	@Column(name = "CITATION_CHK_FULLTEXT_CODE", length = 20)
	private String citation_chk_fulltext_code;
	
	@Column(name = "COPYRIGHT_CLR_CODE", length = 20)
	private String copyright_clr_code;
	
	@Column(name = "RO_REMARKS", length = 4000)
	private String ro_remarks;

	@Column(name = "RO_REMARKS_LIB", length = 4000)
	private String ro_remarks_lib;

	@Column(name = "INFO_OF_NON_TRAD_OUTPUT", length = 4000)
	private String info_of_non_trad_output;
	
	@Column(name = "NON_TRAD_OUTPUT_IND", length = 1)
	private String non_trad_output_ind;
	
	@Column(name = "RESEARCH_AREA_OF_RO", length = 3)
	private String researchAreaOfRo;

	@Column(name = "SUB_DISC_CODE", length = 6)
	private String sub_disc_code;
	
	@Column(name = "SAP_REFERED_JOURNAL", length = 30)
	private String sap_refered_journal;
	
	@Column(name = "CAT_CODE", length = 1000)
	private String cat_code;
	
	@Column(name = "STMT_ORI_SIGN", length = 4000)
	private String stmt_ori_sign;
	
	@Column(name = "INTER_RESEARCH", length = 1)
	private String inter_research;
	
	@Column(name = "PRI_RESEARCH_AREA_INTER", length = 3)
	private String pri_research_area_inter;

	@Column(name = "SEC_RESEARCH_AREA_INTER", length = 3)
	private String sec_research_area_inter;
	
	@Column(name = "PRI_RESEARCH_AREA_CAT", length = 100)
	private String pri_research_area_cat;

	@Column(name = "SEC_RESEARCH_AREA_CAT", length = 100)
	private String sec_research_area_cat;
	
	@Column(name = "AI_TOOL_DESC", length = 4000)
	private String ai_tool_desc;
	
	@Column(name = "REL_LANG", length = 3)
	private String rel_lang;
	
	@Column(name = "RAE_STATUS_CODE")
	private String rae_status_code;
	
	@Column(name = "RAE_STATUS_CODE_PANEL")
	private String rae_status_code_panel;
	
	@Column(name = "RAE_STATUS_CODE_FULL_VER")
	private String rae_status_code_full_ver;
	
	@Column(name = "RAE_STATUS_CODE_OTH_INFO")
	private String rae_status_code_oth_info;

	@Column(name = "RAE_STATUS_INELIGIBLE")
	private String rae_status_ineligible;

	@Column(name = "OTH_LANG", length = 300)
	private String oth_lang;
	
	@Column(name = "TITLE_OF_ENG_OUTPUT", length = 1500)
	private String title_of_eng_output;

	@Column(name = "TITLE_OF_NON_ENG_OUTPUT", length = 4000)
	private String title_of_non_eng_output;
	
	@Column(name = "NO_OF_CO_AUTHOR", length = 2)
	private String no_of_co_author;

	@Column(name = "EXPLANATION_OF_AUTHOR_CTB", length = 4000)
	private String explanation_of_author_ctb;

	@Column(name = "LIST_OF_AUTHOR_ENG", length = 4000)
	private String list_of_author_eng;
	
	@Column(name = "LIST_OF_AUTHOR_NON_ENG", length = 4000)
	private String list_of_author_non_eng;
	
	@Column(name = "PSEUDONYM", length = 120)
	private String pseudonym;
	
	@Column (name = "SUB_STAFF_ROLE")
	private String sub_staff_role;
	
	@Column(name = " INT_RAE_RATE", length = 50)
	private String int_rae_rate;
	
	
	@Column(name = "IRC_HAS_RESULT", length = 1)
	private String irc_has_result;
	
	@Column(name = "PUBLISHED_CENSUS_DATE", length = 1)
	private String published_census_date;
	
	@Column(name = "BOOK_TITLE", length = 3000)
	private String book_title;
	
	@Column(name = "ISBN", length = 40)
	private String isbn;
	
	@Column(name = "ISSN", length = 30)
	private String issn;
	
	@Column(name = "EISSN", length = 24)
	private String eissn;
	
	@Column(name = "VOL_ISSUE", length = 90)
	private String vol_issue;
	
	@Column(name = "PAGE_NUM", length = 60)
	private String page_num;
	
	@Column(name = "ARTICLE_NO", length = 60)
	private String article_no;
	
	@Column(name = "PUBLISHER", length = 1500)
	private String publisher;
	
	@Column(name = "DESC_LOC_OUTPUT", length = 3000)
	private String desc_loc_output;
	
	@Column(name = "FUNDER", length = 4000)
	private String funder;
	
	@Column(name = "COL_NAME_LOC", length = 4000)
	private String colNameLoc;
	
	@Column(name = "INT_COL_IND", length = 1)
	private String intColInd;
	
	@Column(name = "PANEL_10_EXPLANATION", length = 4000)
	private String panel10Explanation;

	@Column(name = "PANEL_10_RO_IND", length = 1)
	private String panel10RoInd;

	@Column(name = "PANEL_10_PTB_IND", length = 1)
	private String panel10PtbInd;

	@Column(name = "PANEL_10_EXPLAIN_PTB", length = 4000)
	private String panel10ExplainPtb;

	@Column(name = "PANEL_10_REV_IND", length = 1)
	private String panel10RevInd;

	@Column(name = "PANEL_10_REV_EXPLAIN", length = 4000)
	private String panel10RevExplain;
	
	@Column(name = "PANEL_10_PUB_IND", length = 1)
	private String panel10PubInd;
	
	@Column(name = "PANEL_10_PUB_DESC", length = 4000)
	private String panel10PubDesc;
	
	@Column(name = "PANEL_11_SUB_DISC_INFO", length = 600)
	private String panel11SubDiscInfo;
	
	@Column(name = "PANEL_12_URL_EVIDENCE", length = 2000)
	private String panel12UrlEvidence;
	
	@Column(name = "PANEL_12_MUL_IND", length = 1)
	private String panel12MulInd;

	@Column(name = "PANEL_12_MUL_URL", length = 4000)
	private String panel12MulUrl;
	
	@Column(name = "FORMAT_FULL_VER_SUBMIT", length = 1)
	private String format_full_ver_submit;
	
	@Column(name = "PHY_AUDIO_QTY")
	private Integer phy_audio_qty;
	
	@Column(name = "PHY_CD_QTY")
	private Integer phy_cd_qty;
	
	@Column(name = "PHY_DVD_QTY")
	private Integer phy_dvd_qty;
	
	@Column(name = "PHY_PHOTO_QTY")
	private Integer phy_photo_qty;
	
	@Column(name = "PHY_BOOK_QTY")
	private Integer phy_book_qty;
	
	@Column(name = "PHY_USB_QTY")
	private Integer phy_usb_qty;
	
	@Column(name = "PHY_OTHER_QTY")
	private Integer phy_other_qty;
	
	@Column(name = "PHY_OTHER_TYPE", length = 4000)
	private String phy_other_type;
	
	@Column(name = "URL_FULL_VER", length = 2000)
	private String url_full_ver;

	@Column(name = "RO_WITH_DOI_IND", length = 1)
	private String ro_with_doi_ind;

	@Column(name = "DOI", length = 3000)
	private String doi;
	
	@Column(name = "TOC", length = 1)
	private String toc;

	@Column(name = "URL_TOC", length = 2000)
	private String url_toc;
	
	@Column(name = "TOC_OTH", length = 1)
	private String toc_oth;

	@Column(name = "URL_TOC_OTH", length = 2000)
	private String url_toc_oth;
	
	@Column(name = "ADDITIONAL_INFO", length = 4000)
	private String additional_info;
	
	@Column(name = "JUST_DW_REQUEST", length = 4000)
	private String just_dw_request;
	
	@Column(name = "KEYWORD_OUTPUT_1", length = 150)
	private String keyword_output_1;

	@Column(name = "KEYWORD_OUTPUT_2", length = 150)
	private String keyword_output_2;

	@Column(name = "KEYWORD_OUTPUT_3", length = 150)
	private String keyword_output_3;

	@Column(name = "KEYWORD_OUTPUT_4", length = 150)
	private String keyword_output_4;

	@Column(name = "KEYWORD_OUTPUT_5", length = 150)
	private String keyword_output_5;
	
	@Column(name = "NO_SGL_CO_WORK", length = 2)
	private String no_sgl_co_work;

	@Column(name = "URL_REF", length = 2000)
	private String url_ref;
	
	@Column(name = "UNI_ENDORSE_CONF_IND", length = 1)
	private String uni_endorse_conf_ind;

	@Column(name = "JUST_SGL_CO_WORK", length = 4000)
	private String just_sgl_co_work;
	
	@Column(name = "SUPP_COUNT")
	private Integer supp_count;
	
	@Column(name = "P10_COUNT")
	private Integer p10_count;
	
	@Column(name = "P12_COUNT")
	private Integer p12_count;
	
	@Column(name = "FULL_COUNT")
	private Integer full_count;
	
	@Column(name = "TOC_COUNT")
	private Integer toc_count;
	
	@Column(name = "TOC_OTH_COUNT")
	private Integer toc_oth_count;
	
	@Column(name = "scw_COUNT")
	private Integer scw_count;
	
	@Column(name = "INFO_COUNT")
	private Integer info_count;
	
	@Column(name = "TOTAL_COUNT")
	private Integer total_count;
	
	@Column(name = "MEDIUM_COUNT")
	private Integer medium_count;	
	
	@Transient
	private String fromDate;
	

	
	
	public Integer getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}

	
	public String getFaculty()
	{
		return faculty;
	}

	
	public void setFaculty(String faculty)
	{
		this.faculty = faculty;
	}

	
	public String getDepartment()
	{
		return department;
	}

	
	public void setDepartment(String department)
	{
		this.department = department;
	}

	
	
	
	public String getOth_output_type()
	{
		return oth_output_type;
	}


	
	public void setOth_output_type(String oth_output_type)
	{
		this.oth_output_type = oth_output_type;
	}


	public String getRae_status_code()
	{
		return rae_status_code;
	}


	
	public void setRae_status_code(String rae_status_code)
	{
		this.rae_status_code = rae_status_code;
	}


	
	public String getRae_status_code_panel()
	{
		return rae_status_code_panel;
	}


	
	public void setRae_status_code_panel(String rae_status_code_panel)
	{
		this.rae_status_code_panel = rae_status_code_panel;
	}
	

	
	public String getJcr()
	{
		return jcr;
	}

	
	public void setJcr(String jcr)
	{
		this.jcr = jcr;
	}

	
	public String getSjr()
	{
		return sjr;
	}


	
	public void setSjr(String sjr)
	{
		this.sjr = sjr;
	}


	public String getRae_status_code_full_ver()
	{
		return rae_status_code_full_ver;
	}


	
	public void setRae_status_code_full_ver(String rae_status_code_full_ver)
	{
		this.rae_status_code_full_ver = rae_status_code_full_ver;
	}


	
	public String getRae_status_code_oth_info()
	{
		return rae_status_code_oth_info;
	}


	
	public void setRae_status_code_oth_info(String rae_status_code_oth_info)
	{
		this.rae_status_code_oth_info = rae_status_code_oth_info;
	}


	
	public String getRae_status_ineligible()
	{
		return rae_status_ineligible;
	}


	
	public void setRae_status_ineligible(String rae_status_ineligible)
	{
		this.rae_status_ineligible = rae_status_ineligible;
	}


	public String getUoa()
	{
		return uoa;
	}

	
	public void setUoa(String uoa)
	{
		this.uoa = uoa;
	}

	
	
	public String getFromDate()
	{
		if(from_month !=null)
			fromDate = from_month + "/" + from_year;
		return fromDate;
	}

	
	public void setFromDate(String fromDate)
	{
		this.fromDate = fromDate;
	}


	public String getStaff_name()
	{
		return staff_name;
	}

	
	public void setStaff_name(String staff_name)
	{
		this.staff_name = staff_name;
	}

	
	public String getConcatenated_author_name()
	{
		return concatenated_author_name.replace(";", "; <br />");
	}

	
	public void setConcatenated_author_name(String concatenated_author_name)
	{
		this.concatenated_author_name = concatenated_author_name;
	}

	
	public String getApa_citation()
	{
		return apa_citation;
	}

	
	public void setApa_citation(String apa_citation)
	{
		this.apa_citation = apa_citation;
	}

	
	public String getSap_output_type()
	{
		return sap_output_type;
	}

	
	public void setSap_output_type(String sap_output_type)
	{
		this.sap_output_type = sap_output_type;
	}

	
	public String getOutput_type()
	{
		return output_type;
	}

	
	public void setOutput_type(String output_type)
	{
		this.output_type = output_type;
	}

	
	public Integer getFrom_month()
	{
		return from_month;
	}

	
	public void setFrom_month(Integer from_month)
	{
		this.from_month = from_month;
	}

	
	public Integer getFrom_year()
	{
		return from_year;
	}

	
	public void setFrom_year(Integer from_year)
	{
		this.from_year = from_year;
	}

	
	public String getSel_type()
	{
		return sel_type;
	}

	
	public void setSel_type(String sel_type)
	{
		this.sel_type = sel_type;
	}

	
	public String getInfo_comp()
	{
		return info_comp;
	}

	
	public void setInfo_comp(String info_comp)
	{
		this.info_comp = info_comp;
	}
	
	


	
	public String getCitation_chk_code()
	{
		return citation_chk_code;
	}


	
	public void setCitation_chk_code(String citation_chk_code)
	{
		this.citation_chk_code = citation_chk_code;
	}


	
	public String getCitation_chk_abs_code()
	{
		return citation_chk_abs_code;
	}


	
	public void setCitation_chk_abs_code(String citation_chk_abs_code)
	{
		this.citation_chk_abs_code = citation_chk_abs_code;
	}


	
	public String getCitation_chk_fulltext_code()
	{
		return citation_chk_fulltext_code;
	}


	
	public void setCitation_chk_fulltext_code(String citation_chk_fulltext_code)
	{
		this.citation_chk_fulltext_code = citation_chk_fulltext_code;
	}


	
	public String getCopyright_clr_code()
	{
		return copyright_clr_code;
	}


	
	public void setCopyright_clr_code(String copyright_clr_code)
	{
		this.copyright_clr_code = copyright_clr_code;
	}


	
	public String getRo_remarks()
	{
		return ro_remarks;
	}


	
	public void setRo_remarks(String ro_remarks)
	{
		this.ro_remarks = ro_remarks;
	}


	
	public String getRo_remarks_lib()
	{
		return ro_remarks_lib;
	}


	
	public void setRo_remarks_lib(String ro_remarks_lib)
	{
		this.ro_remarks_lib = ro_remarks_lib;
	}


	
	public String getInfo_of_non_trad_output()
	{
		return info_of_non_trad_output;
	}


	
	public void setInfo_of_non_trad_output(String info_of_non_trad_output)
	{
		this.info_of_non_trad_output = info_of_non_trad_output;
	}


	
	public String getNon_trad_output_ind()
	{
		return non_trad_output_ind;
	}


	
	public void setNon_trad_output_ind(String non_trad_output_ind)
	{
		this.non_trad_output_ind = non_trad_output_ind;
	}


	
	public String getResearchAreaOfRo()
	{
		return researchAreaOfRo;
	}


	
	public void setResearchAreaOfRo(String researchAreaOfRo)
	{
		this.researchAreaOfRo = researchAreaOfRo;
	}


	
	public String getSub_disc_code()
	{
		return sub_disc_code;
	}


	
	public void setSub_disc_code(String sub_disc_code)
	{
		this.sub_disc_code = sub_disc_code;
	}


	
	public String getSap_refered_journal()
	{
		return sap_refered_journal;
	}


	
	public void setSap_refered_journal(String sap_refered_journal)
	{
		this.sap_refered_journal = sap_refered_journal;
	}


	
	public String getInter_research()
	{
		return inter_research;
	}


	
	public void setInter_research(String inter_research)
	{
		this.inter_research = inter_research;
	}


	
	public String getPri_research_area_inter()
	{
		return pri_research_area_inter;
	}


	
	public void setPri_research_area_inter(String pri_research_area_inter)
	{
		this.pri_research_area_inter = pri_research_area_inter;
	}


	
	public String getSec_research_area_inter()
	{
		return sec_research_area_inter;
	}


	
	public void setSec_research_area_inter(String sec_research_area_inter)
	{
		this.sec_research_area_inter = sec_research_area_inter;
	}


	
	public String getRel_lang()
	{
		return rel_lang;
	}


	
	public void setRel_lang(String rel_lang)
	{
		this.rel_lang = rel_lang;
	}


	
	public String getOth_lang()
	{
		return oth_lang;
	}


	
	public void setOth_lang(String oth_lang)
	{
		this.oth_lang = oth_lang;
	}


	
	public String getTitle_of_eng_output()
	{
		return title_of_eng_output;
	}


	
	public void setTitle_of_eng_output(String title_of_eng_output)
	{
		this.title_of_eng_output = title_of_eng_output;
	}


	
	public String getTitle_of_non_eng_output()
	{
		return title_of_non_eng_output;
	}


	
	public void setTitle_of_non_eng_output(String title_of_non_eng_output)
	{
		this.title_of_non_eng_output = title_of_non_eng_output;
	}


	
	public String getNo_of_co_author()
	{
		return no_of_co_author;
	}


	
	public void setNo_of_co_author(String no_of_co_author)
	{
		this.no_of_co_author = no_of_co_author;
	}


	
	public String getExplanation_of_author_ctb()
	{
		return explanation_of_author_ctb;
	}


	
	public void setExplanation_of_author_ctb(String explanation_of_author_ctb)
	{
		this.explanation_of_author_ctb = explanation_of_author_ctb;
	}


	
	public String getList_of_author_eng()
	{
		return list_of_author_eng;
	}


	
	public void setList_of_author_eng(String list_of_author_eng)
	{
		this.list_of_author_eng = list_of_author_eng;
	}


	
	public String getList_of_author_non_eng()
	{
		return list_of_author_non_eng;
	}


	
	public void setList_of_author_non_eng(String list_of_author_non_eng)
	{
		this.list_of_author_non_eng = list_of_author_non_eng;
	}


	
	public String getPseudonym()
	{
		return pseudonym;
	}


	
	public void setPseudonym(String pseudonym)
	{
		this.pseudonym = pseudonym;
	}


	
	public String getSub_staff_role()
	{
		return sub_staff_role;
	}


	
	public void setSub_staff_role(String sub_staff_role)
	{
		this.sub_staff_role = sub_staff_role;
	}


	
	public String getIrc_has_result()
	{
		return irc_has_result;
	}


	
	public void setIrc_has_result(String irc_has_result)
	{
		this.irc_has_result = irc_has_result;
	}

	
	
	public String getStaff_number()
	{
		return staff_number;
	}


	
	public void setStaff_number(String staff_number)
	{
		this.staff_number = staff_number;
	}


	public String getPublished_census_date()
	{
		return published_census_date;
	}


	
	public void setPublished_census_date(String published_census_date)
	{
		this.published_census_date = published_census_date;
	}


	
	public String getBook_title()
	{
		return book_title;
	}


	
	public void setBook_title(String book_title)
	{
		this.book_title = book_title;
	}


	
	public String getIsbn()
	{
		return isbn;
	}


	
	public void setIsbn(String isbn)
	{
		this.isbn = isbn;
	}


	
	public String getIssn()
	{
		return issn;
	}


	
	public void setIssn(String issn)
	{
		this.issn = issn;
	}


	
	public String getEissn()
	{
		return eissn;
	}


	
	public void setEissn(String eissn)
	{
		this.eissn = eissn;
	}


	
	public String getVol_issue()
	{
		return vol_issue;
	}


	
	public void setVol_issue(String vol_issue)
	{
		this.vol_issue = vol_issue;
	}


	
	public String getPage_num()
	{
		return page_num;
	}


	
	public void setPage_num(String page_num)
	{
		this.page_num = page_num;
	}


	
	public String getArticle_no()
	{
		return article_no;
	}


	
	public void setArticle_no(String article_no)
	{
		this.article_no = article_no;
	}


	
	public String getPublisher()
	{
		return publisher;
	}


	
	public void setPublisher(String publisher)
	{
		this.publisher = publisher;
	}


	
	public String getDesc_loc_output()
	{
		return desc_loc_output;
	}


	
	public void setDesc_loc_output(String desc_loc_output)
	{
		this.desc_loc_output = desc_loc_output;
	}


	
	public String getPanel10Explanation()
	{
		return panel10Explanation;
	}


	
	public void setPanel10Explanation(String panel10Explanation)
	{
		this.panel10Explanation = panel10Explanation;
	}


	
	public String getPanel10RoInd()
	{
		return panel10RoInd;
	}


	
	public void setPanel10RoInd(String panel10RoInd)
	{
		this.panel10RoInd = panel10RoInd;
	}


	
	public String getPanel10PtbInd()
	{
		return panel10PtbInd;
	}


	
	public void setPanel10PtbInd(String panel10PtbInd)
	{
		this.panel10PtbInd = panel10PtbInd;
	}


	
	public String getPanel10ExplainPtb()
	{
		return panel10ExplainPtb;
	}


	
	public void setPanel10ExplainPtb(String panel10ExplainPtb)
	{
		this.panel10ExplainPtb = panel10ExplainPtb;
	}


	
	public String getPanel10RevInd()
	{
		return panel10RevInd;
	}


	
	public void setPanel10RevInd(String panel10RevInd)
	{
		this.panel10RevInd = panel10RevInd;
	}


	
	public String getPanel10RevExplain()
	{
		return panel10RevExplain;
	}


	
	public void setPanel10RevExplain(String panel10RevExplain)
	{
		this.panel10RevExplain = panel10RevExplain;
	}


	
	public String getPanel11SubDiscInfo()
	{
		return panel11SubDiscInfo;
	}


	
	public void setPanel11SubDiscInfo(String panel11SubDiscInfo)
	{
		this.panel11SubDiscInfo = panel11SubDiscInfo;
	}


	
	public String getPanel12UrlEvidence()
	{
		return panel12UrlEvidence;
	}


	
	public void setPanel12UrlEvidence(String panel12UrlEvidence)
	{
		this.panel12UrlEvidence = panel12UrlEvidence;
	}


	
	public String getFormat_full_ver_submit()
	{
		return format_full_ver_submit;
	}


	
	public void setFormat_full_ver_submit(String format_full_ver_submit)
	{
		this.format_full_ver_submit = format_full_ver_submit;
	}


	
	public Integer getPhy_audio_qty()
	{
		return phy_audio_qty;
	}


	
	public void setPhy_audio_qty(Integer phy_audio_qty)
	{
		this.phy_audio_qty = phy_audio_qty;
	}


	
	public Integer getPhy_cd_qty()
	{
		return phy_cd_qty;
	}


	
	public void setPhy_cd_qty(Integer phy_cd_qty)
	{
		this.phy_cd_qty = phy_cd_qty;
	}


	
	public Integer getPhy_dvd_qty()
	{
		return phy_dvd_qty;
	}


	
	public void setPhy_dvd_qty(Integer phy_dvd_qty)
	{
		this.phy_dvd_qty = phy_dvd_qty;
	}


	
	public Integer getPhy_photo_qty()
	{
		return phy_photo_qty;
	}


	
	public void setPhy_photo_qty(Integer phy_photo_qty)
	{
		this.phy_photo_qty = phy_photo_qty;
	}


	
	public Integer getPhy_book_qty()
	{
		return phy_book_qty;
	}


	
	public void setPhy_book_qty(Integer phy_book_qty)
	{
		this.phy_book_qty = phy_book_qty;
	}


	
	public Integer getPhy_usb_qty()
	{
		return phy_usb_qty;
	}


	
	public void setPhy_usb_qty(Integer phy_usb_qty)
	{
		this.phy_usb_qty = phy_usb_qty;
	}


	
	public Integer getPhy_other_qty()
	{
		return phy_other_qty;
	}


	
	public void setPhy_other_qty(Integer phy_other_qty)
	{
		this.phy_other_qty = phy_other_qty;
	}


	
	public String getPhy_other_type()
	{
		return phy_other_type;
	}


	
	public void setPhy_other_type(String phy_other_type)
	{
		this.phy_other_type = phy_other_type;
	}


	
	public String getUrl_full_ver()
	{
		return url_full_ver;
	}


	
	public void setUrl_full_ver(String url_full_ver)
	{
		this.url_full_ver = url_full_ver;
	}


	
	public String getRo_with_doi_ind()
	{
		return ro_with_doi_ind;
	}


	
	public void setRo_with_doi_ind(String ro_with_doi_ind)
	{
		this.ro_with_doi_ind = ro_with_doi_ind;
	}


	
	public String getDoi()
	{
		return doi;
	}


	
	public void setDoi(String doi)
	{
		this.doi = doi;
	}


	
	public String getToc()
	{
		return toc;
	}


	
	public void setToc(String toc)
	{
		this.toc = toc;
	}


	
	public String getUrl_toc()
	{
		return url_toc;
	}


	
	public void setUrl_toc(String url_toc)
	{
		this.url_toc = url_toc;
	}


	
	public String getAdditional_info()
	{
		return additional_info;
	}


	
	public void setAdditional_info(String additional_info)
	{
		this.additional_info = additional_info;
	}


	
	public String getJust_dw_request()
	{
		return just_dw_request;
	}


	
	public void setJust_dw_request(String just_dw_request)
	{
		this.just_dw_request = just_dw_request;
	}


	
	public String getKeyword_output_1()
	{
		return keyword_output_1;
	}


	
	public void setKeyword_output_1(String keyword_output_1)
	{
		this.keyword_output_1 = keyword_output_1;
	}


	
	public String getKeyword_output_2()
	{
		return keyword_output_2;
	}


	
	public void setKeyword_output_2(String keyword_output_2)
	{
		this.keyword_output_2 = keyword_output_2;
	}


	
	public String getKeyword_output_3()
	{
		return keyword_output_3;
	}


	
	public void setKeyword_output_3(String keyword_output_3)
	{
		this.keyword_output_3 = keyword_output_3;
	}


	
	public String getKeyword_output_4()
	{
		return keyword_output_4;
	}


	
	public void setKeyword_output_4(String keyword_output_4)
	{
		this.keyword_output_4 = keyword_output_4;
	}
	
	public String getInt_rae_rate()
	{
		return int_rae_rate;
	}

	public void setInt_rae_rate(String int_rae_rate)
	{
		this.int_rae_rate = int_rae_rate;
	}


	
	public String getKeyword_output_5()
	{
		return keyword_output_5;
	}


	
	public void setKeyword_output_5(String keyword_output_5)
	{
		this.keyword_output_5 = keyword_output_5;
	}


	
	public String getNo_sgl_co_work()
	{
		return no_sgl_co_work;
	}


	
	public void setNo_sgl_co_work(String no_sgl_co_work)
	{
		this.no_sgl_co_work = no_sgl_co_work;
	}


	
	public String getUrl_ref()
	{
		return url_ref;
	}


	
	public void setUrl_ref(String url_ref)
	{
		this.url_ref = url_ref;
	}


	
	public String getUni_endorse_conf_ind()
	{
		return uni_endorse_conf_ind;
	}


	
	public void setUni_endorse_conf_ind(String uni_endorse_conf_ind)
	{
		this.uni_endorse_conf_ind = uni_endorse_conf_ind;
	}


	
	public String getJust_sgl_co_work()
	{
		return just_sgl_co_work;
	}


	
	public void setJust_sgl_co_work(String just_sgl_co_work)
	{
		this.just_sgl_co_work = just_sgl_co_work;
	}


	
	public Integer getSupp_count()
	{
		return supp_count;
	}


	
	public void setSupp_count(Integer supp_count)
	{
		this.supp_count = supp_count;
	}


	
	public Integer getP10_count()
	{
		return p10_count;
	}


	
	public void setP10_count(Integer p10_count)
	{
		this.p10_count = p10_count;
	}


	
	public Integer getP12_count()
	{
		return p12_count;
	}


	
	public void setP12_count(Integer p12_count)
	{
		this.p12_count = p12_count;
	}


	
	public Integer getFull_count()
	{
		return full_count;
	}


	
	public void setFull_count(Integer full_count)
	{
		this.full_count = full_count;
	}


	
	public Integer getToc_count()
	{
		return toc_count;
	}


	
	public void setToc_count(Integer toc_count)
	{
		this.toc_count = toc_count;
	}


	
	public Integer getScw_count()
	{
		return scw_count;
	}


	
	public void setScw_count(Integer scw_count)
	{
		this.scw_count = scw_count;
	}


	
	public Integer getInfo_count()
	{
		return info_count;
	}


	
	public void setInfo_count(Integer info_count)
	{
		this.info_count = info_count;
	}


	
	public Integer getMedium_count()
	{
		return medium_count;
	}


	
	public void setMedium_count(Integer medium_count)
	{
		this.medium_count = medium_count;
	}


	
	public String getCat_code()
	{
		return cat_code;
	}


	
	public void setCat_code(String cat_code)
	{
		this.cat_code = cat_code;
	}


	
	public String getStmt_ori_sign()
	{
		return stmt_ori_sign;
	}


	
	public void setStmt_ori_sign(String stmt_ori_sign)
	{
		this.stmt_ori_sign = stmt_ori_sign;
	}


	
	public String getPri_research_area_cat()
	{
		return pri_research_area_cat;
	}


	
	public void setPri_research_area_cat(String pri_research_area_cat)
	{
		this.pri_research_area_cat = pri_research_area_cat;
	}


	
	public String getSec_research_area_cat()
	{
		return sec_research_area_cat;
	}


	
	public void setSec_research_area_cat(String sec_research_area_cat)
	{
		this.sec_research_area_cat = sec_research_area_cat;
	}




	
	public String getAi_tool_desc()
	{
		return ai_tool_desc;
	}


	
	public void setAi_tool_desc(String ai_tool_desc)
	{
		this.ai_tool_desc = ai_tool_desc;
	}


	
	public String getFunder()
	{
		return funder;
	}


	
	public void setFunder(String funder)
	{
		this.funder = funder;
	}


	
	public String getColNameLoc()
	{
		return colNameLoc;
	}


	
	public void setColNameLoc(String colNameLoc)
	{
		this.colNameLoc = colNameLoc;
	}


	
	public String getIntColInd()
	{
		return intColInd;
	}


	
	public void setIntColInd(String intColInd)
	{
		this.intColInd = intColInd;
	}


	
	public String getPanel10PubInd()
	{
		return panel10PubInd;
	}


	
	public void setPanel10PubInd(String panel10PubInd)
	{
		this.panel10PubInd = panel10PubInd;
	}


	
	public String getPanel10PubDesc()
	{
		return panel10PubDesc;
	}


	
	public void setPanel10PubDesc(String panel10PubDesc)
	{
		this.panel10PubDesc = panel10PubDesc;
	}


	
	public String getPanel12MulInd()
	{
		return panel12MulInd;
	}


	
	public void setPanel12MulInd(String panel12MulInd)
	{
		this.panel12MulInd = panel12MulInd;
	}


	
	public String getPanel12MulUrl()
	{
		return panel12MulUrl;
	}


	
	public void setPanel12MulUrl(String panel12MulUrl)
	{
		this.panel12MulUrl = panel12MulUrl;
	}


	
	public String getToc_oth()
	{
		return toc_oth;
	}


	
	public void setToc_oth(String toc_oth)
	{
		this.toc_oth = toc_oth;
	}


	
	public String getUrl_toc_oth()
	{
		return url_toc_oth;
	}


	
	public void setUrl_toc_oth(String url_toc_oth)
	{
		this.url_toc_oth = url_toc_oth;
	}


	
	public Integer getToc_oth_count()
	{
		return toc_oth_count;
	}


	
	public void setToc_oth_count(Integer toc_oth_count)
	{
		this.toc_oth_count = toc_oth_count;
	}


	
	public Integer getTotal_count()
	{
		return total_count;
	}


	
	public void setTotal_count(Integer total_count)
	{
		this.total_count = total_count;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((output_no == null) ? 0 : output_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutput other = (RaeOutput) obj;
		if (output_no == null)
		{
			if (other.output_no != null)
				return false;
		}
		else if (!output_no.equals(other.output_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "RaeOutput [output_no=" + output_no + ", faculty=" + faculty + ", department=" + department + ", uoa="
				+ uoa + ", jcr=" + jcr + ", sjr=" + sjr + ", staff_name=" + staff_name + ", staff_number="
				+ staff_number + ", oth_output_type=" + oth_output_type + ", concatenated_author_name="
				+ concatenated_author_name + ", apa_citation=" + apa_citation + ", sap_output_type=" + sap_output_type
				+ ", output_type=" + output_type + ", from_month=" + from_month + ", from_year=" + from_year
				+ ", sel_type=" + sel_type + ", info_comp=" + info_comp + ", citation_chk_code=" + citation_chk_code
				+ ", citation_chk_abs_code=" + citation_chk_abs_code + ", citation_chk_fulltext_code="
				+ citation_chk_fulltext_code + ", copyright_clr_code=" + copyright_clr_code + ", ro_remarks="
				+ ro_remarks + ", ro_remarks_lib=" + ro_remarks_lib + ", info_of_non_trad_output="
				+ info_of_non_trad_output + ", non_trad_output_ind=" + non_trad_output_ind + ", researchAreaOfRo="
				+ researchAreaOfRo + ", sub_disc_code=" + sub_disc_code + ", sap_refered_journal=" + sap_refered_journal
				+ ", cat_code=" + cat_code + ", stmt_ori_sign=" + stmt_ori_sign + ", inter_research=" + inter_research
				+ ", pri_research_area_inter=" + pri_research_area_inter + ", sec_research_area_inter="
				+ sec_research_area_inter + ", pri_research_area_cat=" + pri_research_area_cat
				+ ", sec_research_area_cat=" + sec_research_area_cat + ", ai_tool_desc=" + ai_tool_desc + ", rel_lang="
				+ rel_lang + ", rae_status_code=" + rae_status_code + ", rae_status_code_panel=" + rae_status_code_panel
				+ ", rae_status_code_full_ver=" + rae_status_code_full_ver + ", rae_status_code_oth_info="
				+ rae_status_code_oth_info + ", rae_status_ineligible=" + rae_status_ineligible + ", oth_lang="
				+ oth_lang + ", title_of_eng_output=" + title_of_eng_output + ", title_of_non_eng_output="
				+ title_of_non_eng_output + ", no_of_co_author=" + no_of_co_author + ", explanation_of_author_ctb="
				+ explanation_of_author_ctb + ", list_of_author_eng=" + list_of_author_eng + ", list_of_author_non_eng="
				+ list_of_author_non_eng + ", pseudonym=" + pseudonym + ", sub_staff_role=" + sub_staff_role
				+ ", int_rae_rate=" + int_rae_rate + ", irc_has_result=" + irc_has_result + ", published_census_date="
				+ published_census_date + ", book_title=" + book_title + ", isbn=" + isbn + ", issn=" + issn
				+ ", eissn=" + eissn + ", vol_issue=" + vol_issue + ", page_num=" + page_num + ", article_no="
				+ article_no + ", publisher=" + publisher + ", desc_loc_output=" + desc_loc_output + ", funder="
				+ funder + ", colNameLoc=" + colNameLoc + ", intColInd=" + intColInd + ", panel10Explanation="
				+ panel10Explanation + ", panel10RoInd=" + panel10RoInd + ", panel10PtbInd=" + panel10PtbInd
				+ ", panel10ExplainPtb=" + panel10ExplainPtb + ", panel10RevInd=" + panel10RevInd
				+ ", panel10RevExplain=" + panel10RevExplain + ", panel10PubInd=" + panel10PubInd + ", panel10PubDesc="
				+ panel10PubDesc + ", panel11SubDiscInfo=" + panel11SubDiscInfo + ", panel12UrlEvidence="
				+ panel12UrlEvidence + ", panel12MulInd=" + panel12MulInd + ", panel12MulUrl=" + panel12MulUrl
				+ ", format_full_ver_submit=" + format_full_ver_submit + ", phy_audio_qty=" + phy_audio_qty
				+ ", phy_cd_qty=" + phy_cd_qty + ", phy_dvd_qty=" + phy_dvd_qty + ", phy_photo_qty=" + phy_photo_qty
				+ ", phy_book_qty=" + phy_book_qty + ", phy_usb_qty=" + phy_usb_qty + ", phy_other_qty=" + phy_other_qty
				+ ", phy_other_type=" + phy_other_type + ", url_full_ver=" + url_full_ver + ", ro_with_doi_ind="
				+ ro_with_doi_ind + ", doi=" + doi + ", toc=" + toc + ", url_toc=" + url_toc + ", toc_oth=" + toc_oth
				+ ", url_toc_oth=" + url_toc_oth + ", additional_info=" + additional_info + ", just_dw_request="
				+ just_dw_request + ", keyword_output_1=" + keyword_output_1 + ", keyword_output_2=" + keyword_output_2
				+ ", keyword_output_3=" + keyword_output_3 + ", keyword_output_4=" + keyword_output_4
				+ ", keyword_output_5=" + keyword_output_5 + ", no_sgl_co_work=" + no_sgl_co_work + ", url_ref="
				+ url_ref + ", uni_endorse_conf_ind=" + uni_endorse_conf_ind + ", just_sgl_co_work=" + just_sgl_co_work
				+ ", supp_count=" + supp_count + ", p10_count=" + p10_count + ", p12_count=" + p12_count
				+ ", full_count=" + full_count + ", toc_count=" + toc_count + ", toc_oth_count=" + toc_oth_count
				+ ", scw_count=" + scw_count + ", info_count=" + info_count + ", total_count=" + total_count
				+ ", medium_count=" + medium_count + ", fromDate=" + fromDate + "]";
	}



}
