package hk.eduhk.rich.entity.rae;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.logging.Level;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.rae.RaeStaff;
import hk.eduhk.rich.entity.staff.StaffIdentity;

public class RaeStaffDAO extends BaseDAO
{

	private static RaeStaffDAO instance = null;


	public static synchronized RaeStaffDAO getInstance()
	{
		if (instance == null) instance = new RaeStaffDAO();
		return instance;
	}

	
	public List<RaeStaff> getRaeStaffList()
	{
		List<RaeStaff> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaeStaff obj ORDER BY obj.staffName, obj.staffNumber";
		try
		{
			em = getEntityManager();
			TypedQuery<RaeStaff> q = em.createQuery(query, RaeStaff.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<String> getRaeStaffUoaList()
	{
		List<String> objList = null;

		EntityManager em = null;
		String query = "SELECT distinct obj.uoaCode FROM RaeStaff obj ORDER BY obj.uoaCode";
		try
		{
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<String> getRaeStaffFacList()
	{
		List<String> objList = null;

		EntityManager em = null;
		String query = "SELECT distinct obj.coordFaculty FROM RaeStaff obj ORDER BY obj.coordFaculty";
		try
		{
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<RaeStaff> getRaeStaffListByUOA(String uoaCode)
	{
		List<RaeStaff> objList = null;
		EntityManager em = null;
		if (uoaCode != null) {
			String query = "SELECT obj FROM RaeStaff obj WHERE obj.uoaCode = :uoaCode ORDER BY obj.staffName, obj.staffNumber";
			try
			{
				em = getEntityManager();
				TypedQuery<RaeStaff> q = em.createQuery(query, RaeStaff.class);
				q.setParameter("uoaCode", uoaCode);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	
	
	public void updateRaeStaffRemark (RaeStaff staff) throws Exception
	{

			EntityManager em = null;
			UserTransaction utx = null;
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("UPDATE RaeStaff obj SET obj.selRemark = :remarks WHERE obj.staffNumber = :staffNumber ");
				q.setParameter("remarks", staff.getSelRemark());
				q.setParameter("staffNumber", staff.getStaffNumber());
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
	}
	

	
	

	public RaeStaff updateRaeStaff(RaeStaff obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteRaeStaff(String code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			RaeStaff obj = em.find(RaeStaff.class, code);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public RaeStaff getRaeStaffByStaffNo(String code)
	{
		RaeStaff obj = null;
		
		if (code != "")
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(RaeStaff.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public RaeStaff getRaeStaffDetailsByPid(int pid)
	{
		List<RaeStaff> objList = null;
		
		
		if (pid > 0)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM RaeStaff obj WHERE obj.pid = :pid";		
				TypedQuery<RaeStaff> q = em.createQuery(query, RaeStaff.class);
				q.setParameter("pid", pid);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public RaeStaff getRaeStaffByUserId(String userId)
	{
		List<RaeStaff> objList = null;
		
		if (userId != "")
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				String query = "SELECT obj FROM RaeStaff obj WHERE obj.staffIdentity.cn = :userId";		
				TypedQuery<RaeStaff> q = em.createQuery(query, RaeStaff.class);
				q.setParameter("userId", userId);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null); 
	}
	
	public void updateRaeStaffSerial() throws Exception {
	    Connection conn = null;
	    PreparedStatement pStmt = null;
	    UserTransaction utx = null;
	    try {
	        utx = pm.getUserTransaction();
	        utx.begin();
	        conn = pm.getConnection();

	        String sql = "MERGE INTO RICH.RH_RAE_STAFF t " +
	                     "USING ( " +
	                     "    SELECT staff_name, uoa_code, " +
	                     "           LPAD(ROW_NUMBER() OVER (PARTITION BY uoa_code ORDER BY staff_name), 4, '0') AS new_staff_serial " +
	                     "    FROM RICH.RH_RAE_STAFF " +
	                     ") s " +
	                     "ON (t.staff_name = s.staff_name AND t.uoa_code = s.uoa_code) " +
	                     "WHEN MATCHED THEN " +
	                     "UPDATE SET t.staff_serial = s.new_staff_serial";
	        pStmt = conn.prepareStatement(sql);
	        pStmt.executeUpdate();
	        utx.commit();
	    } catch (Exception e) {
	        if (utx != null) {
	            utx.rollback();
	        }
	        throw e;
	    } finally {
	        if (pStmt != null) {
	            pStmt.close();
	        }
	        if (conn != null) {
	            conn.close();
	        }
	    }
	}
}