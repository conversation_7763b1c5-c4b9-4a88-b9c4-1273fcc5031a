package hk.eduhk.rich.entity.rae;

import java.sql.*;
import java.sql.Date;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.faces.application.FacesMessage;
import javax.persistence.EntityManager;
import javax.persistence.OptimisticLockException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.view.RISearchPanel;
import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.*;
import hk.eduhk.rich.entity.award.Award;
import hk.eduhk.rich.entity.publication.*;
import hk.eduhk.rich.entity.report.PreRpt;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;

@Stateless
@SuppressWarnings("serial")
public class RaeOutputDAO extends BaseDAO
{

	private static RaeOutputDAO instance = null;


	public static synchronized RaeOutputDAO getInstance()
	{
		if (instance == null) instance = new RaeOutputDAO();
		return instance;
	}
	
	
	public static RaeOutputDAO getCacheInstance()
	{
		return RaeOutputDAO.getInstance();
	}
	
	
	public static String escapeSql(String str)
	{
         if (str == null) {
                return null;
          }
         return StringUtils.replace(str, "'", "''");
	}
	
	public List<OutputType> getOutputTypeList(int lookup_level) 
	{
		List<OutputType> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM OutputType obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<OutputType> q = em.createQuery(query, OutputType.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PubDiscipline> getDisciplineList(int lookup_level) 
	{
		List<PubDiscipline> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PubDiscipline obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<PubDiscipline> q = em.createQuery(query, PubDiscipline.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	
	
	
	
	public List<ResearchType> getResearchTypeList(int lookup_level) 
	{
		List<ResearchType> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ResearchType obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<ResearchType> q = em.createQuery(query, ResearchType.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<Authorship> getAuthorshipList(int lookup_level) 
	{
		List<Authorship> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Authorship obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<Authorship> q = em.createQuery(query, Authorship.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}		
		
	public List<RaeOutputSelect> getSubOutCountListbySID (List<String> staffNumberList,boolean doubleWeight) 
	{
		List<RaeOutputSelect> objList = null;
		EntityManager em = null;		
		try
		{
			if (CollectionUtils.isNotEmpty(staffNumberList))
			{
				em = getEntityManager();		
				String query = " SELECT obj FROM RaeOutputSelect obj WHERE obj.pk.staff_number IN :staffNumberList " ;
				if(doubleWeight)
					query += " AND obj.selType IN ('DW1','DW2') " ;
				
				TypedQuery<RaeOutputSelect> q = em.createQuery(query, RaeOutputSelect.class);
				q.setParameter("staffNumberList", staffNumberList);
				objList = q.getResultList();

			}
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	
	public RaeOutputSelect getRaeOutputSelect (String staffNumber, Integer outputNo ) 
	{
		RaeOutputSelect obj = null;
		EntityManager em = null;		
		try
		{
			if (staffNumber != null && outputNo != null)
			{
				em = getEntityManager();		
				String query = " SELECT obj FROM RaeOutputSelect obj WHERE obj.pk.staff_number = :staffNumber and obj.pk.output_no = :outputNo " ;
				TypedQuery<RaeOutputSelect> q = em.createQuery(query, RaeOutputSelect.class);
				q.setParameter("staffNumber", staffNumber);
				q.setParameter("outputNo", outputNo);
				obj = q.getSingleResult();
				
			}
		}
     	catch (Exception e)
 		{
     		// Catch Null single result
 		}   
		finally
		{
			pm.close(em);
		}
		
		return  obj != null ? obj : new RaeOutputSelect();
	}
	
	
	
	
	
	public List<SubAuthorship> getSubAuthorshipList(int lookup_level) 
	{
		List<SubAuthorship> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM SubAuthorship obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<SubAuthorship> q = em.createQuery(query, SubAuthorship.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<EduSector> getEduSectorList(int lookup_level) 
	{
		List<EduSector> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM EduSector obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<EduSector> q = em.createQuery(query, EduSector.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<DisciplinaryArea> getDisAreaList(int lookup_level) 
	{
		List<DisciplinaryArea> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM DisciplinaryArea obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<DisciplinaryArea> q = em.createQuery(query, DisciplinaryArea.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}		
	
	
	public List<RaeOutputDetails> getRaeOutputDetails_byRiNo(List<List<Integer>> riNosParts)
	{
		List<RaeOutputDetails> objList = null;
		EntityManager em = null;	
		String where = "";
		try
		{
			em = getEntityManager();	
			if (!riNosParts.isEmpty()) {
				for (int i = 0; i < riNosParts.size(); i++) {
					if (i == 0) {
						where += " AND ( ";
					}
					where += " obj.pk.output_no IN :riNos"+i;
					if (i == riNosParts.size() - 1) {
						where += " ) ";
					}else {
						where += " OR ";
					}
				}

				String query = "SELECT obj FROM RaeOutputDetails obj WHERE obj.pk.line_no = :line_no " + where +
									" ORDER BY obj.raeOutputHeader.from_year DESC, obj.raeOutputHeader.from_month DESC, obj.raeOutputHeader.name_other_pos, obj.raeOutputHeader.title_jour_book";			
				TypedQuery<RaeOutputDetails> q = em.createQuery(query, RaeOutputDetails.class);
				if (!riNosParts.isEmpty()) {
					for (int i = 0; i < riNosParts.size(); i++) {
						q.setParameter("riNos"+i, riNosParts.get(i));
					}
				}
				q.setParameter("line_no", 1);

				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<RaeOutputDetails> getRaeOutputDetails_byStaffNo(String staff_no)
	{
		List<RaeOutputDetails> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM RaeOutputDetails obj WHERE obj.authorship_staff_no = :staff_no " +
								" ORDER BY obj.raeOutputHeader.from_year DESC, obj.raeOutputHeader.from_month DESC, obj.raeOutputHeader.name_other_pos, obj.raeOutputHeader.title_jour_book";			
			TypedQuery<RaeOutputDetails> q = em.createQuery(query, RaeOutputDetails.class);
			q.setParameter("staff_no", staff_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	
	public List<RaeOutputDetails> getRaeOutputDetails(int output_no)
	{
		List<RaeOutputDetails> objList = null;
		EntityManager em = null;		
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutputDetails obj WHERE obj.pk.output_no = :output_no ORDER BY obj.pk.line_no";			
				TypedQuery<RaeOutputDetails> q = em.createQuery(query, RaeOutputDetails.class);
				q.setParameter("output_no", output_no);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}

	
	public RaeOutputHeader getRaeOutputHeader(int output_no)
	{
		List<RaeOutputHeader> objList = null;
		EntityManager em = null;		
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutputHeader obj WHERE obj.output_no = :output_no ";			
				TypedQuery<RaeOutputHeader> q = em.createQuery(query, RaeOutputHeader.class);
				q.setParameter("output_no", output_no);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	

	
	public RaeOutputHeader getRaeOutputHeaderByRiNo(int ri_no)
	{
		List<RaeOutputHeader> objList = null;
		EntityManager em = null;		
		if (ri_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutputHeader obj WHERE obj.riNo = :ri_no ";			
				TypedQuery<RaeOutputHeader> q = em.createQuery(query, RaeOutputHeader.class);
				q.setParameter("ri_no", ri_no);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	
	
	public List<RaeOutputStatus> getRaeOutputStatus_byStaffNo(String staff_no)
	{
		List<RaeOutputStatus> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM RaeOutputStatus obj WHERE obj.pk.staff_number = :staff_no " +
								" ORDER BY obj.raeOutputHeader.from_year DESC, obj.raeOutputHeader.from_month DESC, obj.raeOutputHeader.name_other_pos, obj.raeOutputHeader.title_jour_book";			
			TypedQuery<RaeOutputStatus> q = em.createQuery(query, RaeOutputStatus.class);
			q.setParameter("staff_no", staff_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public RaeOutput getRAEsearchByOutputNo(Integer outputNo) throws SQLException
	{
		List<RaeOutput> objList = null;
		EntityManager em = null;		
		try
		{	if(outputNo != null) {
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutput obj WHERE obj.output_no = :outputNo";			
				TypedQuery<RaeOutput> q = em.createQuery(query, RaeOutput.class);
				q.setParameter("outputNo", outputNo);
				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<RaeOutput> getRAEsearchListByIds(List<Integer> idList, RISearchPanel searchPanel) throws SQLException {
	    List<RaeOutput> objList = new ArrayList<>();
	    EntityManager em = null;

	    if (CollectionUtils.isEmpty(idList)) {
	        return objList;
	    }

	    try {
	        em = getEntityManager();
	        int chunkSize = 1000;
	        int listSize = idList.size();

	        for (int i = 0; i < listSize; i += chunkSize) {
	            List<Integer> subList = idList.subList(i, Math.min(listSize, i + chunkSize));
	            String query = "SELECT obj FROM RaeOutput obj WHERE obj.output_no IN :idList";
	            TypedQuery<RaeOutput> q = em.createQuery(query, RaeOutput.class);
	            q.setParameter("idList", subList);
	            objList.addAll(q.getResultList());
	        }
	    } finally {
	        if (em != null) {
	            pm.close(em);
	        }
	    }

	    return objList;
	}
	
	public List<RaeOutput> getRAEsearchListByStaffNo(String staff_number) throws SQLException
	{
		List<RaeOutput> objList = null;
		EntityManager em = null;		
		try
		{	if(staff_number != null ) {
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutput obj WHERE obj.staff_number = :staff_number";			
				TypedQuery<RaeOutput> q = em.createQuery(query, RaeOutput.class);
				q.setParameter("staff_number", staff_number);
				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		
		return (CollectionUtils.isNotEmpty(objList))?objList:null;
	}
	
	public RaeOutputStatus getRaeOutputStatusByOutputNo(int output_no)
	{
		List<RaeOutputStatus> objList = null;
		EntityManager em = null;		
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutputStatus obj WHERE obj.pk.output_no = :output_no ";			
				TypedQuery<RaeOutputStatus> q = em.createQuery(query, RaeOutputStatus.class);
				q.setParameter("output_no", output_no);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public RaeOutputStatus getRaeOutputStatus(int output_no, String staff_number)
	{
		List<RaeOutputStatus> objList = null;
		EntityManager em = null;		
		if (output_no > 0 && staff_number != null) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM RaeOutputStatus obj WHERE obj.pk.output_no = :output_no AND obj.pk.staff_number = :staff_number";			
				TypedQuery<RaeOutputStatus> q = em.createQuery(query, RaeOutputStatus.class);
				q.setParameter("output_no", output_no);
				q.setParameter("staff_number", staff_number);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public RaeOutputHeader updateRaeOutputHeader(RaeOutputHeader obj)
	{
		return updateEntity(obj);
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public RaeOutputDetails updateRaeOutputDetails(RaeOutputDetails obj)
	{
		return updateEntity(obj);
	}

	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public RaeOutputSelect updateRaeOutputSelect(RaeOutputSelect obj)
	{
		return updateEntity(obj);
	}
	
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public RaeOutputStatus updateRaeOutputStatus (RaeOutputStatus obj)
	{
		return updateEntity(obj);
	}

	
	public void deleteAllContributor(int output_no) throws Exception
	{
		if (output_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM RaeOutputDetails obj WHERE obj.pk.output_no = :output_no ");
				q.setParameter("output_no", output_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete contributors (output_no=" + output_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	
	public void deleteAllSelectOutput (String staff_number , int output_no) throws Exception
	{
		if (output_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM RaeOutputSelect obj WHERE obj.pk.staff_number = :staff_number AND obj.pk.output_no = :output_no ");
				q.setParameter("staff_number", staff_number);
				q.setParameter("output_no", output_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete RaeOutputSelect ( staff_number = "+ staff_number +" and output_no =" + output_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public void deleteAllSelectOutputStatus (String staff_number , int output_no) throws Exception
	{
		if (output_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM RaeOutputStatus obj WHERE obj.pk.staff_number = :staff_number AND obj.pk.output_no = :output_no ");
				q.setParameter("staff_number", staff_number);
				q.setParameter("output_no", output_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete RaeOutputStatus ( staff_number = "+ staff_number +" and output_no =" + output_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	public void deleteAllSelectOutputHeader ( int output_no) throws Exception
	{
		if (output_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM RaeOutputHeader obj WHERE obj.riNo = :output_no ");
				q.setParameter("output_no", output_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete RaeOutputHeader ( ri_no =" + output_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	
	
	
}
