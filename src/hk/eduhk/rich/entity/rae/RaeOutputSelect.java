package hk.eduhk.rich.entity.rae;

import java.util.Objects;
import java.util.logging.Logger;
import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_RAE_SELECTED_OUTPUT")
@SuppressWarnings("serial")
public class RaeOutputSelect extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeOutputSelect.class.toString());
		
	@EmbeddedId
	private RaeOutputSelect_PK pk = new RaeOutputSelect_PK();

	@Column(name = "PID")
	private Integer pid;

	@Column(name = "SEL_TYPE", length = 4)
	private String selType;

	@Column(name = "DBL_WT_JUST", length = 2000)
	private String dbl_wt_just;

	@Column(name = "NON_TRI_OUTPUT_REMARKS", length = 2000)
	private String non_tri_output_remarks;
	
	@Column(name = "INTER_DISP_IND", length = 1)
	private String inter_disp_ind;
	
	@Column(name = "INTER_DISP_1", length = 4)
	private String inter_disp_1;
	
	@Column(name = "INTER_DISP_2", length = 4)
	private String inter_disp_2;
	

	
	public RaeOutputSelect_PK getPk()
	{
		return pk;
	}

	
	public void setPk(RaeOutputSelect_PK pk)
	{
		this.pk = pk;
	}
	
	public String getStaff_number()
	{
		return getPk().getStaff_number();
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public String getSelType()
	{
		return selType;
	}

	
	public void setSelType(String selType)
	{
		this.selType = selType;
	}

	
	public String getDbl_wt_just()
	{
		return dbl_wt_just;
	}

	
	public void setDbl_wt_just(String dbl_wt_just)
	{
		this.dbl_wt_just = dbl_wt_just;
	}

	
	public String getNon_tri_output_remarks()
	{
		return non_tri_output_remarks;
	}

	
	public void setNon_tri_output_remarks(String non_tri_output_remarks)
	{
		this.non_tri_output_remarks = non_tri_output_remarks;
	}

	
	public String getInter_disp_ind()
	{
		return inter_disp_ind;
	}

	
	public void setInter_disp_ind(String inter_disp_ind)
	{
		this.inter_disp_ind = inter_disp_ind;
	}

	
	public String getInter_disp_1()
	{
		return inter_disp_1;
	}

	
	public void setInter_disp_1(String inter_disp_1)
	{
		this.inter_disp_1 = inter_disp_1;
	}

	
	public String getInter_disp_2()
	{
		return inter_disp_2;
	}

	
	public void setInter_disp_2(String inter_disp_2)
	{
		this.inter_disp_2 = inter_disp_2;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutputSelect other = (RaeOutputSelect) obj;
		return Objects.equals(pk, other.pk);
	}


	@Override
	public String toString()
	{
		return "RaeOutputSelect [pk=" + pk + ", pid=" + pid + ", selType=" + selType + ", dbl_wt_just=" + dbl_wt_just
				+ ", non_tri_output_remarks=" + non_tri_output_remarks + ", inter_disp_ind=" + inter_disp_ind
				+ ", inter_disp_1=" + inter_disp_1 + ", inter_disp_2=" + inter_disp_2 + "]";
	}

	
	
	
}
