package hk.eduhk.rich.entity.rae;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;

@Entity
@Table(name = "RH_RAE_OUTPUT_HDR")
@SuppressWarnings("serial")
public class RaeOutputHeader extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeStaff.class.toString());
		
	@Id
	@GeneratedValue(generator = "riSeq")
	@SequenceGenerator(name = "riSeq", sequenceName = "RH_RAE_NO_SEQ", allocationSize = 1)
	@Column(name = "output_no")
	private int output_no;

	@Column(name = "CENSUS_DATE")
	private Date census_date;

	@Column(name = "TITLE_JOUR_BOOK", length = 1500)
	private String title_jour_book;

	@Column(name = "OUTPUT_TITLE_CONTINUE", length = 1500)
	private String output_title_continue;

	@Column(name = "SAP_OUTPUT_TYPE", length = 30)
	private String sap_output_type;

	@Column(name = "SAP_REFERED_JOURNAL", length = 30)
	private String sap_refered_journal;

	@Column(name = "TITLE_PAPER_ART", length = 1500)
	private String title_paper_art;

	@Column(name = "NAME_OTHER_EDITORS", length = 1500)
	private String name_other_editors;

	@Column(name = "VOL_ISSUE", length = 90)
	private String vol_issue;
	
	@Column(name = "PAGE_NUM", length = 60)
	private String page_num;

	@Column(name = "CITY", length = 180)
	private String city;

	@Column(name = "FROM_MONTH")
	private Integer from_month;

	@Column(name = "FROM_YEAR")
	private Integer from_year;

	@Column(name = "TO_MONTH")
	private Integer to_month;

	@Column(name = "TO_YEAR")
	private Integer to_year;
	
	@Column(name = " INT_RAE_RATE", length = 50)
	private String int_rae_rate;
	
	
	@Column(name = "PUBLISHER", length = 1500)
	private String publisher;

	@Column(name = "OTHER_DETAILS", length = 1500)
	private String other_details;

	@Column(name = "OTHER_DETAILS_CONTINUE", length = 1500)
	private String other_details_continue;

	@Column(name = "LANGUAGE", length = 1)
	private String language;

	@Column(name = "NAME_OTHER_POS", length = 1500)
	private String name_other_pos;

	@Column(name = "SCH_CODE", length = 30)
	private String sch_code;

	@Column(name = "SCH_DTL_CODE", length = 30)
	private String sch_dtl_code;

	@Column(name = "RS_CODE", length = 30)
	private String rs_code;

	@Column(name = "RS_DTL_CODE", length = 30)
	private String rs_dtl_code;

	@Column(name = "TOTAL_NO_OF_AUTHOR")
	private Integer total_no_of_author;

	@Column(name = "CONCATENATED_AUTHOR_NAME", length = 4000)
	private String concatenated_author_name;

	@Column(name = "KEY_RESEARCH_AREAS", length = 30)
	private String key_research_areas;

	@Column(name = "OTHER_KEY_RESEARCH_AREAS", length = 200)
	private String other_key_research_areas;

	@Column(name = "RESEARCH_ACTIVITY_RANKING", length = 3)
	private String research_activity_ranking;

	@Column(name = "DA_CODE", length = 100)
	private String da_code;

	@Column(name = "DA_DTL_CODE", length = 100)
	private String da_dtl_code;

	@Column(name = "OTHER_DA_DTL", length = 150)
	private String other_da_dtl;

	@Column(name = "IED_WORK_IND", length = 1)
	private String ied_work_ind;

	@Column(name = "JOURNAL_CODE", length = 10)
	private String journal_code;

	@Column(name = "APA_CITATION", length = 4000)
	private String apa_citation;

	@Column(name = "ISSN", length = 30)
	private String issn;

	@Column(name = "IS_ENH_HIGH_EDU", length = 1)
	private String is_enh_high_edu;

	@Column(name = "IS_INTL_CONF", length = 1)
	private String is_intl_conf;

	@Column(name = "JOURNAL_PUBLICATION_DISCIPLINE", length = 50)
	private String journal_publication_discipline;

	@Column(name = "RI_NO")
	private Integer riNo;

	@Column(name = "RESEARCH_AREA_OF_RO", length = 3)
	private String researchAreaOfRo;

	@Column(name = "SUB_DISC_CODE", length = 6)
	private String sub_disc_code;

	@Column(name = "OUTPUT_TYPE", length = 1)
	private String output_type;

	@Column(name = "URL_ADDITIONAL", length = 2000)
	private String url_additional;

	@Column(name = "KEY_ADDITIONAL", length = 200)
	private String key_additional;

	@Column(name = "OTH_OUTPUT_TYPE", length = 450)
	private String oth_output_type;

	@Column(name = "TITLE_OF_ENG_OUTPUT", length = 1500)
	private String title_of_eng_output;

	@Column(name = "NON_ENG_OUTPUT_IND", length = 1)
	private String non_eng_output_ind;

	@Column(name = "REL_LANG", length = 3)
	private String rel_lang;

	@Column(name = "OTH_LANG", length = 300)
	private String oth_lang;

	@Column(name = "TITLE_OF_NON_ENG_OUTPUT", length = 4000)
	private String title_of_non_eng_output;

	@Column(name = "SURNAME_OF_ENG_AUTHOR", length = 150)
	private String surname_of_eng_author;

	@Column(name = "GIVEN_NAME_OF_ENG_AUTHOR", length = 240)
	private String given_name_of_eng_author;

	@Column(name = "NAME_OF_NON_ENG_AUTHOR", length = 450)
	private String name_of_non_eng_author;

	@Column(name = "CO_AUTHORED_IND", length = 1)
	private String co_authored_ind;

	@Column(name = "NO_OF_CO_AUTHOR", length = 2)
	private String no_of_co_author;

	@Column(name = "EXPLANATION_OF_AUTHOR_CTB", length = 4000)
	private String explanation_of_author_ctb;

	@Column(name = "LIST_OF_AUTHOR_ENG", length = 4000)
	private String list_of_author_eng;

	@Column(name = "IND_OTH_STAFF", length = 1)
	private String ind_oth_staff;

	@Column(name = "ASS_STAFF_RO", length = 300)
	private String ass_staff_ro;

	@Column(name = "IND_CO_AUTHORED_STAFF", length = 1)
	private String ind_co_authored_staff;

	@Column(name = "LIST_OF_AUTHOR_NON_ENG", length = 4000)
	private String list_of_author_non_eng;

	@Column(name = "PUBLISHED_CENSUS_DATE", length = 1)
	private String published_census_date;

	@Column(name = "ISBN", length = 40)
	private String isbn;

	@Column(name = "ISSN_R", length = 24)
	private String issnR;

	@Column(name = "FIRST_PUBLISH_DATE", length = 6)
	private String first_publish_date;
	
	@Column(name = "PUBLISHER_R", length = 600)
	private String publisher_r;

	@Column(name = "URL_SUPPORT", length = 2000)
	private String url_support;

	@Column(name = "KEY_SUPPORT", length = 200)
	private String key_support;

	@Column(name = "BOOK_TITLE", length = 3000)
	private String book_title;

	@Column(name = "ISSUE_NO", length = 3000)
	private String issue_no;

	@Column(name = "PAGE_NO", length = 3000)
	private String page_no;
	
	@Column(name = "DESC_LOC_OUTPUT", length = 3000)
	private String desc_loc_output;

	@Column(name = "FORMAT_FULL_VER_SUBMIT", length = 1)
	private String format_full_ver_submit;

	@Column(name = "NO_OF_TYPE_SUBMIT", length = 1)
	private String no_of_type_submit;

	@Column(name = "RO_WITH_DOI_IND", length = 1)
	private String ro_with_doi_ind;

	@Column(name = "DOI", length = 3000)
	private String doi;

	@Column(name = "URL_FULL_VER", length = 2000)
	private String url_full_ver;

	@Column(name = "KEY_FULL_VER", length = 200)
	private String key_full_ver;

	@Column(name = "URL_OA_FULL_VER", length = 2000)
	private String url_oa_full_ver;

	@Column(name = "FILE_TYPE_OF_FULL_VER", length = 5)
	private String file_type_of_full_ver;

	@Column(name = "FILESIZE", length = 10)
	private String filesize;

	@Column(name = "NON_TRAD_OUTPUT_IND", length = 1)
	private String non_trad_output_ind;

	@Column(name = "INFO_OF_NON_TRAD_OUTPUT", length = 4000)
	private String info_of_non_trad_output;

	@Column(name = "DW_REQUEST_IND", length = 1)
	private String dw_request_ind;

	@Column(name = "JUST_DW_REQUEST", length = 4000)
	private String just_dw_request;

	@Column(name = "RESERVE_ITEM_DW_IND", length = 1)
	private String reserve_item_dw_ind;

	@Column(name = "INTER_RESEARCH", length = 1)
	private String inter_research;

	@Column(name = "PRI_RESEARCH_AREA_INTER", length = 3)
	private String pri_research_area_inter;

	@Column(name = "SEC_RESEARCH_AREA_INTER", length = 3)
	private String sec_research_area_inter;

	@Column(name = "PRI_RESEARCH_AREA_CAT", length = 100)
	private String pri_research_area_cat;

	@Column(name = "SEC_RESEARCH_AREA_CAT", length = 100)
	private String sec_research_area_cat;
	
	@Column(name = "TOC", length = 1)
	private String toc;

	@Column(name = "IND_TOC", length = 1)
	private String ind_toc;

	@Column(name = "URL_TOC", length = 2000)
	private String url_toc;

	@Column(name = "KEY_TOC", length = 200)
	private String key_toc;
	
	@Column(name = "TOC_OTH", length = 1)
	private String toc_oth;

	@Column(name = "IND_TOC_OTH", length = 1)
	private String ind_toc_oth;

	@Column(name = "URL_TOC_OTH", length = 2000)
	private String url_toc_oth;

	@Column(name = "KEY_TOC_OTH", length = 200)
	private String key_toc_oth;
	
	@Column(name = "ADDITIONAL_INFO", length = 4000)
	private String additional_info;
	
	@Column(name = "KEYWORD_OUTPUT_1", length = 150)
	private String keyword_output_1;

	@Column(name = "KEYWORD_OUTPUT_2", length = 150)
	private String keyword_output_2;

	@Column(name = "KEYWORD_OUTPUT_3", length = 150)
	private String keyword_output_3;

	@Column(name = "KEYWORD_OUTPUT_4", length = 150)
	private String keyword_output_4;

	@Column(name = "KEYWORD_OUTPUT_5", length = 150)
	private String keyword_output_5;

	@Column(name = "IRC_HAS_RESULT", length = 1)
	private String irc_has_result;

	@Column(name = "PANEL_2_SUB_SPEC_DCT", length = 300)
	private String panel2SubSpecDct;

	@Column(name = "PANEL_8_SUBSEQUENT_EDT", length = 1)
	private String panel8SubsequentEdt;

	@Column(name = "PANEL_8_IND_STMT", length = 4000)
	private String panel8IndStmt;

	@Column(name = "PANEL_10_EXPLANATION", length = 4000)
	private String panel10Explanation;

	@Column(name = "PANEL_10_RO_IND", length = 1)
	private String panel10RoInd;

	@Column(name = "PANEL_10_URL_EVIDENCE", length = 2000)
	private String panel10UrlEvidence;

	@Column(name = "PANEL_10_KEY_EVIDENCE", length = 200)
	private String panel10KeyEvidence;

	@Column(name = "PANEL_10_PTB_IND", length = 1)
	private String panel10PtbInd;

	@Column(name = "PANEL_10_EXPLAIN_PTB", length = 4000)
	private String panel10ExplainPtb;

	@Column(name = "PANEL_10_REV_IND", length = 1)
	private String panel10RevInd;

	@Column(name = "PANEL_10_REV_EXPLAIN", length = 4000)
	private String panel10RevExplain;

	@Column(name = "PANEL_10_OPEN_URL", length = 2000)
	private String panel10OpenUrl;
	
	@Column(name = "PANEL_10_EDUHK_IND", length = 1)
	private String panel10EduhkInd;
	
	@Column(name = "PANEL_10_PUB_IND", length = 1)
	private String panel10PubInd;
	
	@Column(name = "PANEL_10_PUB_DESC", length = 4000)
	private String panel10PubDesc;
	
	@Column(name = "PANEL_11_SUB_DISC_INFO", length = 600)
	private String panel11SubDiscInfo;

	@Column(name = "PANEL_12_URL_PUB_LOC", length = 2000)
	private String panel12UrlPubLoc;

	@Column(name = "PANEL_12_URL_EVIDENCE", length = 2000)
	private String panel12UrlEvidence;

	@Column(name = "PANEL_12_KEY_EVIDENCE", length = 200)
	private String panel12KeyEvidence;
	
	@Column(name = "PANEL_12_MUL_IND", length = 1)
	private String panel12MulInd;

	@Column(name = "PANEL_12_MUL_URL", length = 4000)
	private String panel12MulUrl;
	
	@Column(name = "PANEL_12_EDUHK_IND", length = 1)
	private String panel12EduhkInd;
	
	@Column(name = "FUNDER", length = 4000)
	private String funder;
	
	@Column(name = "COL_NAME_LOC", length = 4000)
	private String colNameLoc;
	
	@Column(name = "INT_COL_IND", length = 1)
	private String intColInd;
	
	@Column(name = "RAE_STATUS_CODE", length = 20)
	private String rae_status_code;

	@Column(name = "RAE_STATUS_CODE_PANEL", length = 20)
	private String rae_status_code_panel;

	@Column(name = "RAE_STATUS_CODE_FULL_VER", length = 20)
	private String rae_status_code_full_ver;

	@Column(name = "RAE_STATUS_CODE_OTH_INFO", length = 20)
	private String rae_status_code_oth_info;

	@Column(name = "RAE_STATUS_INELIGIBLE", length = 20)
	private String rae_status_ineligible;

	@Column(name = "CITATION_CHK_CODE", length = 20)
	private String citation_chk_code;

	@Column(name = "COPYRIGHT_CLR_CODE", length = 20)
	private String copyright_clr_code;

	@Column(name = "RO_REMARKS", length = 4000)
	private String ro_remarks;

	@Column(name = "RO_REMARKS_LIB", length = 4000)
	private String ro_remarks_lib;

	@Column(name = "URL_REF", length = 2000)
	private String url_ref;

	@Column(name = "PHY_AUDIO_QTY")
	private Integer phy_audio_qty;

	@Column(name = "PHY_CD_QTY")
	private Integer phy_cd_qty;

	@Column(name = "PHY_DVD_QTY")
	private Integer phy_dvd_qty;

	@Column(name = "PHY_PHOTO_QTY")
	private Integer phy_photo_qty;

	@Column(name = "PHY_BOOK_QTY")
	private Integer phy_book_qty;

	@Column(name = "PHY_USB_QTY")
	private Integer phy_usb_qty;

	@Column(name = "PHY_OTHER_TYPE", length = 4000)
	private String phy_other_type;

	@Column(name = "PHY_OTHER_QTY")
	private Integer phy_other_qty;

	@Column(name = "NO_SGL_CO_WORK", length = 2)
	private String no_sgl_co_work;

	@Column(name = "UNI_ENDORSE_CONF_IND", length = 1)
	private String uni_endorse_conf_ind;

	@Column(name = "JUST_SGL_CO_WORK", length = 4000)
	private String just_sgl_co_work;

	@Column(name = "PSEUDONYM", length = 120)
	private String pseudonym;

	@Column(name = "ROLE_SUBMIT_STAFF", length = 120)
	private String role_submit_staff;
	
	@Column(name = "ROLE_AUTHOR_IND", length = 1)
	private String role_author_ind;
	
	@Column(name = "EISSN", length = 24)
	private String eissn;

	@Column(name = "ARTICLE_NO", length = 60)
	private String article_no;

	@Column(name = "CITATION_CHK_ABS_CODE", length = 20)
	private String citation_chk_abs_code;

	@Column(name = "CITATION_CHK_FULLTEXT_CODE", length = 20)
	private String citation_chk_fulltext_code;

	@Column(name = "INT_ID")
	private Integer int_id;
	
	@Column(name = "CAT_CODE", length = 1000)
	private String cat_code;
	
	@Column(name = "STMT_ORI_SIGN", length = 4000)
	private String stmt_ori_sign;
	
	@Column(name = "AI_TOOL_IND", length = 1)
	private String ai_tool_ind;
	
	@Column(name = "AI_TOOL_DESC", length = 4000)
	private String ai_tool_desc;
	
	@Transient
	private String output_lookup_code = null;
	
	@Transient
	private String apa_html;

	@Transient
	private Date startDate;
	
	@Transient
	private Date endDate;

	@Transient
	private String page_num_from;
	
	@Transient
	private String page_num_to;
	
	@Transient
	private List<String> oth_lang_list;

	@Transient
	private List<String> cat_code_list;
	
	public int getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(int output_no)
	{
		this.output_no = output_no;
	}

	
	public Date getCensus_date()
	{
		return census_date;
	}

	
	public void setCensus_date(Date census_date)
	{
		this.census_date = census_date;
	}

	
	public String getTitle_jour_book()
	{
		return title_jour_book;
	}

	
	public void setTitle_jour_book(String title_jour_book)
	{
		this.title_jour_book = title_jour_book;
	}

	
	public String getOutput_title_continue()
	{
		return output_title_continue;
	}

	
	public void setOutput_title_continue(String output_title_continue)
	{
		this.output_title_continue = output_title_continue;
	}

	
	public String getSap_output_type()
	{
		return sap_output_type;
	}

	
	public void setSap_output_type(String sap_output_type)
	{
		this.sap_output_type = sap_output_type;
	}

	
	public String getSap_refered_journal()
	{
		return sap_refered_journal;
	}

	
	public void setSap_refered_journal(String sap_refered_journal)
	{
		this.sap_refered_journal = sap_refered_journal;
	}

	
	public String getTitle_paper_art()
	{
		return title_paper_art;
	}

	
	public void setTitle_paper_art(String title_paper_art)
	{
		this.title_paper_art = title_paper_art;
	}

	
	public String getName_other_editors()
	{
		return name_other_editors;
	}

	
	public void setName_other_editors(String name_other_editors)
	{
		this.name_other_editors = name_other_editors;
	}

	
	public String getVol_issue()
	{
		return vol_issue;
	}

	
	public void setVol_issue(String vol_issue)
	{
		this.vol_issue = vol_issue;
	}

	
	public String getPage_num()
	{
		return page_num;
	}


	
	public void setPage_num(String page_num)
	{
		this.page_num = page_num;
	}


	public String getCity()
	{
		return city;
	}

	
	public void setCity(String city)
	{
		this.city = city;
	}

	
	public Integer getFrom_month()
	{
		return from_month;
	}

	
	public void setFrom_month(Integer from_month)
	{
		
			this.from_month = from_month;
	}
	
	
	



	public Integer getFrom_year()
	{
		return from_year;
	}

	
	public void setFrom_year(Integer from_year)
	{
		
			this.from_year = from_year;
	}

	
	public Integer getTo_month()
	{
		return to_month;
	}

	
	public void setTo_month(Integer to_month)
	{
		
			this.to_month = to_month;
	}

	
	public Integer getTo_year()
	{
		return to_year;
	}

	
	public void setTo_year(Integer to_year)
	{
		
			this.to_year = to_year;
	}

	
	public String getPublisher()
	{
		return publisher;
	}

	
	public void setPublisher(String publisher)
	{
		this.publisher = publisher;
	}

	
	public String getOther_details()
	{
		return other_details;
	}

	
	public void setOther_details(String other_details)
	{
		this.other_details = other_details;
	}

	
	public String getOther_details_continue()
	{
		return other_details_continue;
	}

	
	public void setOther_details_continue(String other_details_continue)
	{
		this.other_details_continue = other_details_continue;
	}

	
	public String getLanguage()
	{
		return language;
	}

	
	public void setLanguage(String language)
	{
		this.language = language;
	}

	
	public String getName_other_pos()
	{
		return name_other_pos;
	}

	
	public void setName_other_pos(String name_other_pos)
	{
		this.name_other_pos = name_other_pos;
	}

	
	public String getSch_code()
	{
		return sch_code;
	}

	
	public void setSch_code(String sch_code)
	{
		this.sch_code = sch_code;
	}

	
	public String getSch_dtl_code()
	{
		return sch_dtl_code;
	}

	
	public void setSch_dtl_code(String sch_dtl_code)
	{
		this.sch_dtl_code = sch_dtl_code;
	}

	
	public String getRs_code()
	{
		return rs_code;
	}

	
	public void setRs_code(String rs_code)
	{
		this.rs_code = rs_code;
	}

	
	public String getRs_dtl_code()
	{
		return rs_dtl_code;
	}

	
	public void setRs_dtl_code(String rs_dtl_code)
	{
		this.rs_dtl_code = rs_dtl_code;
	}

	
	public Integer getTotal_no_of_author()
	{
		return total_no_of_author;
	}

	
	public void setTotal_no_of_author(Integer total_no_of_author)
	{
		this.total_no_of_author = total_no_of_author;
	}

	
	public String getConcatenated_author_name()
	{
		return concatenated_author_name;
	}

	
	public void setConcatenated_author_name(String concatenated_author_name)
	{
		this.concatenated_author_name = concatenated_author_name;
	}

	
	public String getKey_research_areas()
	{
		return key_research_areas;
	}

	
	public void setKey_research_areas(String key_research_areas)
	{
		this.key_research_areas = key_research_areas;
	}

	
	public String getOther_key_research_areas()
	{
		return other_key_research_areas;
	}

	
	public void setOther_key_research_areas(String other_key_research_areas)
	{
		this.other_key_research_areas = other_key_research_areas;
	}

	
	public String getResearch_activity_ranking()
	{
		return research_activity_ranking;
	}

	
	public void setResearch_activity_ranking(String research_activity_ranking)
	{
		this.research_activity_ranking = research_activity_ranking;
	}

	
	public String getDa_code()
	{
		return da_code;
	}

	
	public void setDa_code(String da_code)
	{
		this.da_code = da_code;
	}

	
	public String getDa_dtl_code()
	{
		return da_dtl_code;
	}

	
	public void setDa_dtl_code(String da_dtl_code)
	{
		this.da_dtl_code = da_dtl_code;
	}

	
	public String getOther_da_dtl()
	{
		return other_da_dtl;
	}

	
	public void setOther_da_dtl(String other_da_dtl)
	{
		this.other_da_dtl = other_da_dtl;
	}

	
	public String getIed_work_ind()
	{
		return ied_work_ind;
	}

	
	public void setIed_work_ind(String ied_work_ind)
	{
		this.ied_work_ind = ied_work_ind;
	}

	
	public String getJournal_code()
	{
		return journal_code;
	}

	
	public void setJournal_code(String journal_code)
	{
		this.journal_code = journal_code;
	}

	
	public String getApa_citation()
	{
		return apa_citation;
	}

	
	public void setApa_citation(String apa_citation)
	{
		this.apa_citation = apa_citation;
	}

	
	public String getIssn()
	{
		return issn;
	}

	
	public void setIssn(String issn)
	{
		this.issn = issn;
	}

	
	public String getIs_enh_high_edu()
	{
		return is_enh_high_edu;
	}

	
	public void setIs_enh_high_edu(String is_enh_high_edu)
	{
		this.is_enh_high_edu = is_enh_high_edu;
	}

	
	public String getIs_intl_conf()
	{
		return is_intl_conf;
	}

	
	public void setIs_intl_conf(String is_intl_conf)
	{
		this.is_intl_conf = is_intl_conf;
	}

	
	public String getJournal_publication_discipline()
	{
		return journal_publication_discipline;
	}

	
	public void setJournal_publication_discipline(String journal_publication_discipline)
	{
		this.journal_publication_discipline = journal_publication_discipline;
	}

	
	public Integer getRiNo()
	{
		return riNo;
	}

	
	public void setRiNo(Integer riNo)
	{
		this.riNo = riNo;
	}

	
	public String getResearchAreaOfRo()
	{
		return researchAreaOfRo;
	}

	
	public void setResearchAreaOfRo(String researchAreaOfRo)
	{
		this.researchAreaOfRo = researchAreaOfRo;
	}

	
	public String getSub_disc_code()
	{
		return sub_disc_code;
	}

	
	public void setSub_disc_code(String sub_disc_code)
	{
		this.sub_disc_code = sub_disc_code;
	}

	
	public String getOutput_type()
	{
		return output_type;
	}

	
	public void setOutput_type(String output_type)
	{
		this.output_type = output_type;
	}

	
	public String getUrl_additional()
	{
		return url_additional;
	}

	
	public void setUrl_additional(String url_additional)
	{
		this.url_additional = url_additional;
	}

	
	public String getKey_additional()
	{
		return key_additional;
	}

	
	public void setKey_additional(String key_additional)
	{
		this.key_additional = key_additional;
	}

	
	public String getOth_output_type()
	{
		return oth_output_type;
	}

	
	public void setOth_output_type(String oth_output_type)
	{
		this.oth_output_type = oth_output_type;
	}

	
	public String getTitle_of_eng_output()
	{
		return title_of_eng_output;
	}

	
	public void setTitle_of_eng_output(String title_of_eng_output)
	{
		this.title_of_eng_output = title_of_eng_output;
	}

	
	public String getNon_eng_output_ind()
	{
		return non_eng_output_ind;
	}

	
	public void setNon_eng_output_ind(String non_eng_output_ind)
	{
		this.non_eng_output_ind = non_eng_output_ind;
	}

	
	public String getRel_lang()
	{
		return rel_lang;
	}

	
	public void setRel_lang(String rel_lang)
	{
		this.rel_lang = rel_lang;
	}

	
	public String getOth_lang()
	{
		return oth_lang;
	}

	
	public void setOth_lang(String oth_lang)
	{
		this.oth_lang = oth_lang;
	}

	
	public String getTitle_of_non_eng_output()
	{
		return title_of_non_eng_output;
	}

	
	public void setTitle_of_non_eng_output(String title_of_non_eng_output)
	{
		this.title_of_non_eng_output = title_of_non_eng_output;
	}

	
	public String getSurname_of_eng_author()
	{
		return surname_of_eng_author;
	}

	
	public void setSurname_of_eng_author(String surname_of_eng_author)
	{
		this.surname_of_eng_author = surname_of_eng_author;
	}

	
	public String getGiven_name_of_eng_author()
	{
		return given_name_of_eng_author;
	}

	
	public void setGiven_name_of_eng_author(String given_name_of_eng_author)
	{
		this.given_name_of_eng_author = given_name_of_eng_author;
	}

	
	public String getName_of_non_eng_author()
	{
		return name_of_non_eng_author;
	}

	
	public void setName_of_non_eng_author(String name_of_non_eng_author)
	{
		this.name_of_non_eng_author = name_of_non_eng_author;
	}

	
	public String getCo_authored_ind()
	{
		return co_authored_ind;
	}

	
	public void setCo_authored_ind(String co_authored_ind)
	{
		this.co_authored_ind = co_authored_ind;
	}

	
	public String getNo_of_co_author()
	{
		return no_of_co_author;
	}

	
	public void setNo_of_co_author(String no_of_co_author)
	{
		this.no_of_co_author = no_of_co_author;
	}

	
	public String getExplanation_of_author_ctb()
	{
		return explanation_of_author_ctb;
	}

	
	public void setExplanation_of_author_ctb(String explanation_of_author_ctb)
	{
		this.explanation_of_author_ctb = explanation_of_author_ctb;
	}

	
	public String getList_of_author_eng()
	{
		return list_of_author_eng;
	}

	
	public void setList_of_author_eng(String list_of_author_eng)
	{
		this.list_of_author_eng = list_of_author_eng;
	}

	
	public String getInd_oth_staff()
	{
		return ind_oth_staff;
	}

	
	public void setInd_oth_staff(String ind_oth_staff)
	{
		this.ind_oth_staff = ind_oth_staff;
	}

	
	public String getAss_staff_ro()
	{
		return ass_staff_ro;
	}

	
	public void setAss_staff_ro(String ass_staff_ro)
	{
		this.ass_staff_ro = ass_staff_ro;
	}

	
	public String getInd_co_authored_staff()
	{
		return ind_co_authored_staff;
	}

	
	public void setInd_co_authored_staff(String ind_co_authored_staff)
	{
		this.ind_co_authored_staff = ind_co_authored_staff;
	}

	
	public String getList_of_author_non_eng()
	{
		return list_of_author_non_eng;
	}

	
	public void setList_of_author_non_eng(String list_of_author_non_eng)
	{
		this.list_of_author_non_eng = list_of_author_non_eng;
	}

	
	public String getPublished_census_date()
	{
		return published_census_date;
	}

	
	public void setPublished_census_date(String published_census_date)
	{
		this.published_census_date = published_census_date;
	}

	
	public String getIsbn()
	{
		return isbn;
	}

	
	public void setIsbn(String isbn)
	{
		this.isbn = isbn;
	}

	
	public String getIssnR()
	{
		return issnR;
	}

	
	public void setIssnR(String issnR)
	{
		this.issnR = issnR;
	}

	
	public String getFirst_publish_date()
	{
		return first_publish_date;
	}

	
	public void setFirst_publish_date(String first_publish_date)
	{
		this.first_publish_date = first_publish_date;
	}

	
	public String getPublisher_r()
	{
		return publisher_r;
	}

	
	public void setPublisher_r(String publisher_r)
	{
		this.publisher_r = publisher_r;
	}

	
	public String getUrl_support()
	{
		return url_support;
	}

	
	public void setUrl_support(String url_support)
	{
		this.url_support = url_support;
	}

	
	public String getKey_support()
	{
		return key_support;
	}

	
	public void setKey_support(String key_support)
	{
		this.key_support = key_support;
	}

	
	public String getBook_title()
	{
		return book_title;
	}

	
	public void setBook_title(String book_title)
	{
		this.book_title = book_title;
	}

	
	public String getIssue_no()
	{
		return issue_no;
	}

	
	public void setIssue_no(String issue_no)
	{
		this.issue_no = issue_no;
	}

	
	public String getPage_no()
	{
		return page_no;
	}

	
	public void setPage_no(String page_no)
	{
		this.page_no = page_no;
	}

	
	public String getDesc_loc_output()
	{
		return desc_loc_output;
	}

	
	public void setDesc_loc_output(String desc_loc_output)
	{
		this.desc_loc_output = desc_loc_output;
	}

	
	public String getFormat_full_ver_submit()
	{
		return format_full_ver_submit;
	}

	
	public void setFormat_full_ver_submit(String format_full_ver_submit)
	{
		this.format_full_ver_submit = format_full_ver_submit;
	}

	
	public String getNo_of_type_submit()
	{
		return no_of_type_submit;
	}

	
	public void setNo_of_type_submit(String no_of_type_submit)
	{
		this.no_of_type_submit = no_of_type_submit;
	}

	
	public String getRo_with_doi_ind()
	{
		return ro_with_doi_ind;
	}

	
	public void setRo_with_doi_ind(String ro_with_doi_ind)
	{
		this.ro_with_doi_ind = ro_with_doi_ind;
	}

	
	public String getDoi()
	{
		return doi;
	}

	
	public void setDoi(String doi)
	{
		this.doi = doi;
	}

	
	public String getUrl_full_ver()
	{
		return url_full_ver;
	}

	
	public void setUrl_full_ver(String url_full_ver)
	{
		this.url_full_ver = url_full_ver;
	}

	
	public String getKey_full_ver()
	{
		return key_full_ver;
	}

	
	public void setKey_full_ver(String key_full_ver)
	{
		this.key_full_ver = key_full_ver;
	}

	
	public String getUrl_oa_full_ver()
	{
		return url_oa_full_ver;
	}

	
	public void setUrl_oa_full_ver(String url_oa_full_ver)
	{
		this.url_oa_full_ver = url_oa_full_ver;
	}

	
	public String getFile_type_of_full_ver()
	{
		return file_type_of_full_ver;
	}

	
	public void setFile_type_of_full_ver(String file_type_of_full_ver)
	{
		this.file_type_of_full_ver = file_type_of_full_ver;
	}

	
	public String getFilesize()
	{
		return filesize;
	}

	
	public void setFilesize(String filesize)
	{
		this.filesize = filesize;
	}

	
	public String getNon_trad_output_ind()
	{
		return non_trad_output_ind;
	}

	
	public void setNon_trad_output_ind(String non_trad_output_ind)
	{
		this.non_trad_output_ind = non_trad_output_ind;
	}

	
	public String getInfo_of_non_trad_output()
	{
		return info_of_non_trad_output;
	}

	
	public void setInfo_of_non_trad_output(String info_of_non_trad_output)
	{
		this.info_of_non_trad_output = info_of_non_trad_output;
	}

	
	public String getDw_request_ind()
	{
		return dw_request_ind;
	}

	
	public void setDw_request_ind(String dw_request_ind)
	{
		this.dw_request_ind = dw_request_ind;
	}

	
	public String getJust_dw_request()
	{
		return just_dw_request;
	}

	
	public void setJust_dw_request(String just_dw_request)
	{
		this.just_dw_request = just_dw_request;
	}

	
	public String getReserve_item_dw_ind()
	{
		return reserve_item_dw_ind;
	}

	
	public void setReserve_item_dw_ind(String reserve_item_dw_ind)
	{
		this.reserve_item_dw_ind = reserve_item_dw_ind;
	}

	
	public String getInter_research()
	{
		return inter_research;
	}

	
	public void setInter_research(String inter_research)
	{
		this.inter_research = inter_research;
	}

	
	public String getPri_research_area_inter()
	{
		return pri_research_area_inter;
	}

	
	public void setPri_research_area_inter(String pri_research_area_inter)
	{
		this.pri_research_area_inter = pri_research_area_inter;
	}

	
	public String getSec_research_area_inter()
	{
		return sec_research_area_inter;
	}

	
	public void setSec_research_area_inter(String sec_research_area_inter)
	{
		this.sec_research_area_inter = sec_research_area_inter;
	}

	
	
	public String getPri_research_area_cat()
	{
		return pri_research_area_cat;
	}


	
	public void setPri_research_area_cat(String pri_research_area_cat)
	{
		this.pri_research_area_cat = pri_research_area_cat;
	}


	
	public String getSec_research_area_cat()
	{
		return sec_research_area_cat;
	}


	
	public void setSec_research_area_cat(String sec_research_area_cat)
	{
		this.sec_research_area_cat = sec_research_area_cat;
	}


	public String getToc()
	{
		return toc;
	}

	
	public void setToc(String toc)
	{
		this.toc = toc;
	}

	
	public String getInd_toc()
	{
		return ind_toc;
	}

	
	public void setInd_toc(String ind_toc)
	{
		this.ind_toc = ind_toc;
	}

	
	public String getUrl_toc()
	{
		return url_toc;
	}

	
	public void setUrl_toc(String url_toc)
	{
		this.url_toc = url_toc;
	}

	
	public String getKey_toc()
	{
		return key_toc;
	}

	
	public void setKey_toc(String key_toc)
	{
		this.key_toc = key_toc;
	}

	
	
	public String getToc_oth()
	{
		return toc_oth;
	}


	
	public void setToc_oth(String toc_oth)
	{
		this.toc_oth = toc_oth;
	}


	
	public String getInd_toc_oth()
	{
		return ind_toc_oth;
	}


	
	public void setInd_toc_oth(String ind_toc_oth)
	{
		this.ind_toc_oth = ind_toc_oth;
	}


	
	public String getUrl_toc_oth()
	{
		return url_toc_oth;
	}


	
	public void setUrl_toc_oth(String url_toc_oth)
	{
		this.url_toc_oth = url_toc_oth;
	}


	
	public String getKey_toc_oth()
	{
		return key_toc_oth;
	}


	
	public void setKey_toc_oth(String key_toc_oth)
	{
		this.key_toc_oth = key_toc_oth;
	}


	public String getAdditional_info()
	{
		return additional_info;
	}

	
	public void setAdditional_info(String additional_info)
	{
		this.additional_info = additional_info;
	}

	
	public String getKeyword_output_1()
	{
		return keyword_output_1;
	}

	
	public void setKeyword_output_1(String keyword_output_1)
	{
		this.keyword_output_1 = keyword_output_1;
	}

	
	public String getKeyword_output_2()
	{
		return keyword_output_2;
	}

	
	public void setKeyword_output_2(String keyword_output_2)
	{
		this.keyword_output_2 = keyword_output_2;
	}

	
	public String getKeyword_output_3()
	{
		return keyword_output_3;
	}

	
	public void setKeyword_output_3(String keyword_output_3)
	{
		this.keyword_output_3 = keyword_output_3;
	}

	
	public String getKeyword_output_4()
	{
		return keyword_output_4;
	}

	
	public void setKeyword_output_4(String keyword_output_4)
	{
		this.keyword_output_4 = keyword_output_4;
	}

	
	public String getKeyword_output_5()
	{
		return keyword_output_5;
	}

	
	public void setKeyword_output_5(String keyword_output_5)
	{
		this.keyword_output_5 = keyword_output_5;
	}

	
	public String getIrc_has_result()
	{
		return irc_has_result;
	}

	
	public void setIrc_has_result(String irc_has_result)
	{
		this.irc_has_result = irc_has_result;
	}

	
	public String getPanel2SubSpecDct()
	{
		return panel2SubSpecDct;
	}

	
	public void setPanel2SubSpecDct(String panel2SubSpecDct)
	{
		this.panel2SubSpecDct = panel2SubSpecDct;
	}

	
	public String getPanel8SubsequentEdt()
	{
		return panel8SubsequentEdt;
	}

	
	public void setPanel8SubsequentEdt(String panel8SubsequentEdt)
	{
		this.panel8SubsequentEdt = panel8SubsequentEdt;
	}

	
	public String getPanel8IndStmt()
	{
		return panel8IndStmt;
	}

	
	public void setPanel8IndStmt(String panel8IndStmt)
	{
		this.panel8IndStmt = panel8IndStmt;
	}

	
	public String getPanel10Explanation()
	{
		return panel10Explanation;
	}

	
	public void setPanel10Explanation(String panel10Explanation)
	{
		this.panel10Explanation = panel10Explanation;
	}

	
	public String getPanel10RoInd()
	{
		return panel10RoInd;
	}

	
	public void setPanel10RoInd(String panel10RoInd)
	{
		this.panel10RoInd = panel10RoInd;
	}

	
	public String getPanel10UrlEvidence()
	{
		return panel10UrlEvidence;
	}

	
	public void setPanel10UrlEvidence(String panel10UrlEvidence)
	{
		this.panel10UrlEvidence = panel10UrlEvidence;
	}

	
	public String getPanel10KeyEvidence()
	{
		return panel10KeyEvidence;
	}

	
	public void setPanel10KeyEvidence(String panel10KeyEvidence)
	{
		this.panel10KeyEvidence = panel10KeyEvidence;
	}

	
	public String getPanel10PtbInd()
	{
		return panel10PtbInd;
	}

	
	public void setPanel10PtbInd(String panel10PtbInd)
	{
		this.panel10PtbInd = panel10PtbInd;
	}

	
	public String getPanel10ExplainPtb()
	{
		return panel10ExplainPtb;
	}

	
	public void setPanel10ExplainPtb(String panel10ExplainPtb)
	{
		this.panel10ExplainPtb = panel10ExplainPtb;
	}

	
	public String getPanel10RevInd()
	{
		return panel10RevInd;
	}

	
	public void setPanel10RevInd(String panel10RevInd)
	{
		this.panel10RevInd = panel10RevInd;
	}

	
	public String getPanel10RevExplain()
	{
		return panel10RevExplain;
	}

	
	public void setPanel10RevExplain(String panel10RevExplain)
	{
		this.panel10RevExplain = panel10RevExplain;
	}

	
	public String getPanel10OpenUrl()
	{
		return panel10OpenUrl;
	}

	
	public void setPanel10OpenUrl(String panel10OpenUrl)
	{
		this.panel10OpenUrl = panel10OpenUrl;
	}

	
	public String getPanel11SubDiscInfo()
	{
		return panel11SubDiscInfo;
	}

	
	public void setPanel11SubDiscInfo(String panel11SubDiscInfo)
	{
		this.panel11SubDiscInfo = panel11SubDiscInfo;
	}

	
	public String getPanel12UrlPubLoc()
	{
		return panel12UrlPubLoc;
	}

	
	public void setPanel12UrlPubLoc(String panel12UrlPubLoc)
	{
		this.panel12UrlPubLoc = panel12UrlPubLoc;
	}

	
	public String getPanel12UrlEvidence()
	{
		return panel12UrlEvidence;
	}

	
	public void setPanel12UrlEvidence(String panel12UrlEvidence)
	{
		this.panel12UrlEvidence = panel12UrlEvidence;
	}

	
	public String getPanel12KeyEvidence()
	{
		return panel12KeyEvidence;
	}

	
	public void setPanel12KeyEvidence(String panel12KeyEvidence)
	{
		this.panel12KeyEvidence = panel12KeyEvidence;
	}

	
	
	public String getPanel10EduhkInd()
	{
		return panel10EduhkInd;
	}


	
	public void setPanel10EduhkInd(String panel10EduhkInd)
	{
		this.panel10EduhkInd = panel10EduhkInd;
	}


	
	public String getPanel10PubInd()
	{
		return panel10PubInd;
	}


	
	public void setPanel10PubInd(String panel10PubInd)
	{
		this.panel10PubInd = panel10PubInd;
	}


	
	public String getPanel10PubDesc()
	{
		return panel10PubDesc;
	}


	
	public void setPanel10PubDesc(String panel10PubDesc)
	{
		this.panel10PubDesc = panel10PubDesc;
	}


	
	public String getPanel12MulInd()
	{
		return panel12MulInd;
	}


	
	public void setPanel12MulInd(String panel12MulInd)
	{
		this.panel12MulInd = panel12MulInd;
	}


	
	public String getPanel12MulUrl()
	{
		return panel12MulUrl;
	}


	
	public void setPanel12MulUrl(String panel12MulUrl)
	{
		this.panel12MulUrl = panel12MulUrl;
	}


	
	public String getPanel12EduhkInd()
	{
		return panel12EduhkInd;
	}


	
	public void setPanel12EduhkInd(String panel12EduhkInd)
	{
		this.panel12EduhkInd = panel12EduhkInd;
	}





	public String getRae_status_code()
	{
		return rae_status_code;
	}

	
	public void setRae_status_code(String rae_status_code)
	{
		this.rae_status_code = rae_status_code;
	}

	
	public String getRae_status_code_panel()
	{
		return rae_status_code_panel;
	}

	
	public void setRae_status_code_panel(String rae_status_code_panel)
	{
		this.rae_status_code_panel = rae_status_code_panel;
	}

	
	public String getRae_status_code_full_ver()
	{
		return rae_status_code_full_ver;
	}

	
	public void setRae_status_code_full_ver(String rae_status_code_full_ver)
	{
		this.rae_status_code_full_ver = rae_status_code_full_ver;
	}

	
	public String getRae_status_code_oth_info()
	{
		return rae_status_code_oth_info;
	}

	
	public void setRae_status_code_oth_info(String rae_status_code_oth_info)
	{
		this.rae_status_code_oth_info = rae_status_code_oth_info;
	}

	
	public String getRae_status_ineligible()
	{
		return rae_status_ineligible;
	}

	
	public void setRae_status_ineligible(String rae_status_ineligible)
	{
		this.rae_status_ineligible = rae_status_ineligible;
	}

	
	public String getCitation_chk_code()
	{
		return citation_chk_code;
	}

	
	public void setCitation_chk_code(String citation_chk_code)
	{
		this.citation_chk_code = citation_chk_code;
	}

	
	public String getCopyright_clr_code()
	{
		return copyright_clr_code;
	}

	
	public void setCopyright_clr_code(String copyright_clr_code)
	{
		this.copyright_clr_code = copyright_clr_code;
	}

	
	public String getRo_remarks()
	{
		return ro_remarks;
	}

	
	public void setRo_remarks(String ro_remarks)
	{
		this.ro_remarks = ro_remarks;
	}

	
	public String getRo_remarks_lib()
	{
		return ro_remarks_lib;
	}

	
	public void setRo_remarks_lib(String ro_remarks_lib)
	{
		this.ro_remarks_lib = ro_remarks_lib;
	}

	
	public String getUrl_ref()
	{
		return url_ref;
	}

	
	public void setUrl_ref(String url_ref)
	{
		this.url_ref = url_ref;
	}

	
	public Integer getPhy_audio_qty()
	{
		if (phy_audio_qty == null) {
			phy_audio_qty = 0;
		}
		return phy_audio_qty;
	}

	
	public void setPhy_audio_qty(Integer phy_audio_qty)
	{
		this.phy_audio_qty = phy_audio_qty;
	}

	
	public Integer getPhy_cd_qty()
	{
		if (phy_cd_qty == null) {
			phy_cd_qty = 0;
		}
		return phy_cd_qty;
	}

	
	public void setPhy_cd_qty(Integer phy_cd_qty)
	{
		this.phy_cd_qty = phy_cd_qty;
	}

	
	public Integer getPhy_dvd_qty()
	{
		if (phy_dvd_qty == null) {
			phy_dvd_qty = 0;
		}
		return phy_dvd_qty;
	}

	
	public void setPhy_dvd_qty(Integer phy_dvd_qty)
	{
		this.phy_dvd_qty = phy_dvd_qty;
	}

	
	public Integer getPhy_photo_qty()
	{
		if (phy_photo_qty == null) {
			phy_photo_qty = 0;
		}
		return phy_photo_qty;
	}

	
	public void setPhy_photo_qty(Integer phy_photo_qty)
	{
		this.phy_photo_qty = phy_photo_qty;
	}

	
	public Integer getPhy_book_qty()
	{
		if (phy_book_qty == null) {
			phy_book_qty = 0;
		}
		return phy_book_qty;
	}

	
	public void setPhy_book_qty(Integer phy_book_qty)
	{
		this.phy_book_qty = phy_book_qty;
	}

	
	public Integer getPhy_usb_qty()
	{
		if (phy_usb_qty == null) {
			phy_usb_qty = 0;
		}
		return phy_usb_qty;
	}

	
	public void setPhy_usb_qty(Integer phy_usb_qty)
	{
		this.phy_usb_qty = phy_usb_qty;
	}

	
	public String getPhy_other_type()
	{
		return phy_other_type;
	}

	
	public void setPhy_other_type(String phy_other_type)
	{
		this.phy_other_type = phy_other_type;
	}

	
	public Integer getPhy_other_qty()
	{
		if (phy_other_qty == null) {
			phy_other_qty = 0;
		}
		return phy_other_qty;
	}

	
	public void setPhy_other_qty(Integer phy_other_qty)
	{
		this.phy_other_qty = phy_other_qty;
	}

	
	public String getNo_sgl_co_work()
	{
		return no_sgl_co_work;
	}

	
	public void setNo_sgl_co_work(String no_sgl_co_work)
	{
		this.no_sgl_co_work = no_sgl_co_work;
	}

	
	public String getUni_endorse_conf_ind()
	{
		return uni_endorse_conf_ind;
	}

	
	public void setUni_endorse_conf_ind(String uni_endorse_conf_ind)
	{
		this.uni_endorse_conf_ind = uni_endorse_conf_ind;
	}

	
	public String getJust_sgl_co_work()
	{
		return just_sgl_co_work;
	}

	
	public void setJust_sgl_co_work(String just_sgl_co_work)
	{
		this.just_sgl_co_work = just_sgl_co_work;
	}

	
	public String getPseudonym()
	{
		return pseudonym;
	}

	
	public void setPseudonym(String pseudonym)
	{
		this.pseudonym = pseudonym;
	}

	
	public String getRole_submit_staff()
	{
		return role_submit_staff;
	}

	
	public void setRole_submit_staff(String role_submit_staff)
	{
		this.role_submit_staff = role_submit_staff;
	}

	
	public String getEissn()
	{
		return eissn;
	}

	
	public void setEissn(String eissn)
	{
		this.eissn = eissn;
	}

	
	public String getArticle_no()
	{
		return article_no;
	}

	
	public void setArticle_no(String article_no)
	{
		this.article_no = article_no;
	}

	
	public String getCitation_chk_abs_code()
	{
		return citation_chk_abs_code;
	}

	
	public void setCitation_chk_abs_code(String citation_chk_abs_code)
	{
		this.citation_chk_abs_code = citation_chk_abs_code;
	}

	
	public String getCitation_chk_fulltext_code()
	{
		return citation_chk_fulltext_code;
	}

	
	public void setCitation_chk_fulltext_code(String citation_chk_fulltext_code)
	{
		this.citation_chk_fulltext_code = citation_chk_fulltext_code;
	}

	
	public Integer getInt_id()
	{
		return int_id;
	}

	
	public void setInt_id(Integer int_id)
	{
		this.int_id = int_id;
	}

	
	public String getOutput_lookup_code()
	{
		return output_lookup_code;
	}

	
	public void setOutput_lookup_code(String output_lookup_code)
	{
		this.output_lookup_code = output_lookup_code;
	}

	
	public String getApa_html()
	{
		return apa_html;
	}

	
	public void setApa_html(String apa_html)
	{
		this.apa_html = apa_html;
	}

	
	public Date getStartDate()
	{
		return startDate;
	}

	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}

	
	public Date getEndDate()
	{
		return endDate;
	}

	
	public void setEndDate(Date endDate)
	{
		this.endDate = endDate;
	}


	
	public String getPage_num_from() {
	    if (Strings.isNullOrEmpty(page_num_from)) {
	        if (!Strings.isNullOrEmpty(getPage_num())) {
	            if (page_num.contains("-")) {
	                String[] parts = page_num.split("-");
	                // Ensure the array is not empty before accessing parts[0]
	                if (parts.length > 0) {
	                    page_num_from = parts[0];
	                } else {
	                    // Handle the case where the array is empty
	                    page_num_from = page_num; // or set to a default value
	                }
	            } else {
	                page_num_from = page_num;
	            }
	        }
	    }
	    return page_num_from;
	}


	
	public void setPage_num_from(String page_num_from)
	{
		this.page_num_from = page_num_from;
	}


	
	public String getPage_num_to()
	{
		if (Strings.isNullOrEmpty(page_num_to)) {
			if (Strings.isNullOrEmpty(getPage_num()) == false) {
				if (page_num.contains("-")) {
					String[] parts = page_num.split("-");
					if (parts.length>1)
						page_num_to = parts[1];
				}
			}
		}
		return page_num_to;
	}


	
	public void setPage_num_to(String page_num_to)
	{
		this.page_num_to = page_num_to;
	}
	

	public String getInt_rae_rate()
	{
		return int_rae_rate;
	}
	
	public void setInt_rae_rate(String int_rae_rate)
	{
		this.int_rae_rate = int_rae_rate;
	}


	public List<String> getOth_lang_list()
	{
		if (oth_lang != null && oth_lang_list == null )
			oth_lang_list =  Stream.of(getOth_lang().split(",")).collect(Collectors.toList());
		return oth_lang_list;
	}


	
	public void setOth_lang_list(List<String> oth_lang_list)
	{
		this.oth_lang_list = oth_lang_list;
	}


	
	
	public List<String> getCat_code_list()
	{
		if (cat_code != null && cat_code_list == null )
			cat_code_list =  Stream.of(getCat_code().split(";")).collect(Collectors.toList());
		return cat_code_list;
	}


	
	public void setCat_code_list(List<String> cat_code_list)
	{
		this.cat_code_list = cat_code_list;
	}


	public String getCat_code()
	{
		return cat_code;
	}


	
	public void setCat_code(String cat_code)
	{
		this.cat_code = cat_code;
	}


	
	public String getStmt_ori_sign()
	{
		return stmt_ori_sign;
	}


	
	public void setStmt_ori_sign(String stmt_ori_sign)
	{
		this.stmt_ori_sign = stmt_ori_sign;
	}


	
	public String getAi_tool_ind()
	{
		return ai_tool_ind;
	}


	
	public void setAi_tool_ind(String ai_tool_ind)
	{
		this.ai_tool_ind = ai_tool_ind;
	}


	
	public String getAi_tool_desc()
	{
		return ai_tool_desc;
	}


	
	public void setAi_tool_desc(String ai_tool_desc)
	{
		this.ai_tool_desc = ai_tool_desc;
	}


	
	public String getFunder()
	{
		return funder;
	}


	
	public void setFunder(String funder)
	{
		this.funder = funder;
	}


	
	public String getColNameLoc()
	{
		return colNameLoc;
	}


	
	public void setColNameLoc(String colNameLoc)
	{
		this.colNameLoc = colNameLoc;
	}


	
	public String getIntColInd()
	{
		return intColInd;
	}


	
	public void setIntColInd(String intColInd)
	{
		this.intColInd = intColInd;
	}


	
	public String getRole_author_ind()
	{
		return role_author_ind;
	}


	
	public void setRole_author_ind(String role_author_ind)
	{
		this.role_author_ind = role_author_ind;
	}



	@Override
	public int hashCode()
	{
		return Objects.hash(output_no);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutputHeader other = (RaeOutputHeader) obj;
		return output_no == other.output_no;
	}


	@Override
	public String toString()
	{
		return "RaeOutputHeader [output_no=" + output_no + ", census_date=" + census_date + ", title_jour_book="
				+ title_jour_book + ", output_title_continue=" + output_title_continue + ", sap_output_type="
				+ sap_output_type + ", sap_refered_journal=" + sap_refered_journal + ", title_paper_art="
				+ title_paper_art + ", name_other_editors=" + name_other_editors + ", vol_issue=" + vol_issue
				+ ", page_num=" + page_num + ", city=" + city + ", from_month=" + from_month + ", from_year="
				+ from_year + ", to_month=" + to_month + ", to_year=" + to_year + ", int_rae_rate=" + int_rae_rate
				+ ", publisher=" + publisher + ", other_details=" + other_details + ", other_details_continue="
				+ other_details_continue + ", language=" + language + ", name_other_pos=" + name_other_pos
				+ ", sch_code=" + sch_code + ", sch_dtl_code=" + sch_dtl_code + ", rs_code=" + rs_code
				+ ", rs_dtl_code=" + rs_dtl_code + ", total_no_of_author=" + total_no_of_author
				+ ", concatenated_author_name=" + concatenated_author_name + ", key_research_areas="
				+ key_research_areas + ", other_key_research_areas=" + other_key_research_areas
				+ ", research_activity_ranking=" + research_activity_ranking + ", da_code=" + da_code + ", da_dtl_code="
				+ da_dtl_code + ", other_da_dtl=" + other_da_dtl + ", ied_work_ind=" + ied_work_ind + ", journal_code="
				+ journal_code + ", apa_citation=" + apa_citation + ", issn=" + issn + ", is_enh_high_edu="
				+ is_enh_high_edu + ", is_intl_conf=" + is_intl_conf + ", journal_publication_discipline="
				+ journal_publication_discipline + ", riNo=" + riNo + ", researchAreaOfRo=" + researchAreaOfRo
				+ ", sub_disc_code=" + sub_disc_code + ", output_type=" + output_type + ", url_additional="
				+ url_additional + ", key_additional=" + key_additional + ", oth_output_type=" + oth_output_type
				+ ", title_of_eng_output=" + title_of_eng_output + ", non_eng_output_ind=" + non_eng_output_ind
				+ ", rel_lang=" + rel_lang + ", oth_lang=" + oth_lang + ", title_of_non_eng_output="
				+ title_of_non_eng_output + ", surname_of_eng_author=" + surname_of_eng_author
				+ ", given_name_of_eng_author=" + given_name_of_eng_author + ", name_of_non_eng_author="
				+ name_of_non_eng_author + ", co_authored_ind=" + co_authored_ind + ", no_of_co_author="
				+ no_of_co_author + ", explanation_of_author_ctb=" + explanation_of_author_ctb + ", list_of_author_eng="
				+ list_of_author_eng + ", ind_oth_staff=" + ind_oth_staff + ", ass_staff_ro=" + ass_staff_ro
				+ ", ind_co_authored_staff=" + ind_co_authored_staff + ", list_of_author_non_eng="
				+ list_of_author_non_eng + ", published_census_date=" + published_census_date + ", isbn=" + isbn
				+ ", issnR=" + issnR + ", first_publish_date=" + first_publish_date + ", publisher_r=" + publisher_r
				+ ", url_support=" + url_support + ", key_support=" + key_support + ", book_title=" + book_title
				+ ", issue_no=" + issue_no + ", page_no=" + page_no + ", desc_loc_output=" + desc_loc_output
				+ ", format_full_ver_submit=" + format_full_ver_submit + ", no_of_type_submit=" + no_of_type_submit
				+ ", ro_with_doi_ind=" + ro_with_doi_ind + ", doi=" + doi + ", url_full_ver=" + url_full_ver
				+ ", key_full_ver=" + key_full_ver + ", url_oa_full_ver=" + url_oa_full_ver + ", file_type_of_full_ver="
				+ file_type_of_full_ver + ", filesize=" + filesize + ", non_trad_output_ind=" + non_trad_output_ind
				+ ", info_of_non_trad_output=" + info_of_non_trad_output + ", dw_request_ind=" + dw_request_ind
				+ ", just_dw_request=" + just_dw_request + ", reserve_item_dw_ind=" + reserve_item_dw_ind
				+ ", inter_research=" + inter_research + ", pri_research_area_inter=" + pri_research_area_inter
				+ ", sec_research_area_inter=" + sec_research_area_inter + ", pri_research_area_cat="
				+ pri_research_area_cat + ", sec_research_area_cat=" + sec_research_area_cat + ", toc=" + toc
				+ ", ind_toc=" + ind_toc + ", url_toc=" + url_toc + ", key_toc=" + key_toc + ", toc_oth=" + toc_oth
				+ ", ind_toc_oth=" + ind_toc_oth + ", url_toc_oth=" + url_toc_oth + ", key_toc_oth=" + key_toc_oth
				+ ", additional_info=" + additional_info + ", keyword_output_1=" + keyword_output_1
				+ ", keyword_output_2=" + keyword_output_2 + ", keyword_output_3=" + keyword_output_3
				+ ", keyword_output_4=" + keyword_output_4 + ", keyword_output_5=" + keyword_output_5
				+ ", irc_has_result=" + irc_has_result + ", panel2SubSpecDct=" + panel2SubSpecDct
				+ ", panel8SubsequentEdt=" + panel8SubsequentEdt + ", panel8IndStmt=" + panel8IndStmt
				+ ", panel10Explanation=" + panel10Explanation + ", panel10RoInd=" + panel10RoInd
				+ ", panel10UrlEvidence=" + panel10UrlEvidence + ", panel10KeyEvidence=" + panel10KeyEvidence
				+ ", panel10PtbInd=" + panel10PtbInd + ", panel10ExplainPtb=" + panel10ExplainPtb + ", panel10RevInd="
				+ panel10RevInd + ", panel10RevExplain=" + panel10RevExplain + ", panel10OpenUrl=" + panel10OpenUrl
				+ ", panel10EduhkInd=" + panel10EduhkInd + ", panel10PubInd=" + panel10PubInd + ", panel10PubDesc="
				+ panel10PubDesc + ", panel11SubDiscInfo=" + panel11SubDiscInfo + ", panel12UrlPubLoc="
				+ panel12UrlPubLoc + ", panel12UrlEvidence=" + panel12UrlEvidence + ", panel12KeyEvidence="
				+ panel12KeyEvidence + ", panel12MulInd=" + panel12MulInd + ", panel12MulUrl=" + panel12MulUrl
				+ ", panel12EduhkInd=" + panel12EduhkInd + ", funder=" + funder + ", colNameLoc=" + colNameLoc
				+ ", intColInd=" + intColInd + ", rae_status_code=" + rae_status_code + ", rae_status_code_panel="
				+ rae_status_code_panel + ", rae_status_code_full_ver=" + rae_status_code_full_ver
				+ ", rae_status_code_oth_info=" + rae_status_code_oth_info + ", rae_status_ineligible="
				+ rae_status_ineligible + ", citation_chk_code=" + citation_chk_code + ", copyright_clr_code="
				+ copyright_clr_code + ", ro_remarks=" + ro_remarks + ", ro_remarks_lib=" + ro_remarks_lib
				+ ", url_ref=" + url_ref + ", phy_audio_qty=" + phy_audio_qty + ", phy_cd_qty=" + phy_cd_qty
				+ ", phy_dvd_qty=" + phy_dvd_qty + ", phy_photo_qty=" + phy_photo_qty + ", phy_book_qty=" + phy_book_qty
				+ ", phy_usb_qty=" + phy_usb_qty + ", phy_other_type=" + phy_other_type + ", phy_other_qty="
				+ phy_other_qty + ", no_sgl_co_work=" + no_sgl_co_work + ", uni_endorse_conf_ind="
				+ uni_endorse_conf_ind + ", just_sgl_co_work=" + just_sgl_co_work + ", pseudonym=" + pseudonym
				+ ", role_submit_staff=" + role_submit_staff + ", role_author_ind=" + role_author_ind
				+ ", eissn=" + eissn + ", article_no=" + article_no
				+ ", citation_chk_abs_code=" + citation_chk_abs_code + ", citation_chk_fulltext_code="
				+ citation_chk_fulltext_code + ", int_id=" + int_id + ", cat_code=" + cat_code + ", stmt_ori_sign="
				+ stmt_ori_sign + ", ai_tool_ind=" + ai_tool_ind + ", ai_tool_desc=" + ai_tool_desc
				+ ", output_lookup_code=" + output_lookup_code + ", apa_html=" + apa_html + ", startDate=" + startDate
				+ ", endDate=" + endDate + ", page_num_from=" + page_num_from + ", page_num_to=" + page_num_to
				+ ", oth_lang_list=" + oth_lang_list + ", cat_code_list=" + cat_code_list + "]";
	}

}
