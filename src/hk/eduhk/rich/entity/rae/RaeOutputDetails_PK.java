package hk.eduhk.rich.entity.rae;

import java.io.Serializable;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class RaeOutputDetails_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="output_no")
	private Integer output_no;
	
	@Column(name="line_no")
	private Integer line_no;

	
	public Integer getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}


	
	public Integer getLine_no()
	{
		return line_no;
	}

	
	public void setLine_no(Integer line_no)
	{
		this.line_no = line_no;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(line_no, output_no);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutputDetails_PK other = (RaeOutputDetails_PK) obj;
		return Objects.equals(line_no, other.line_no) && Objects.equals(output_no, other.output_no);
	}


	@Override
	public String toString()
	{
		return "RaeOutputDetails_PK [output_no=" + output_no + ", line_no=" + line_no + "]";
	}




}
