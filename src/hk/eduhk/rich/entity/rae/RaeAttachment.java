package hk.eduhk.rich.entity.rae;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.persistence.*;

import hk.eduhk.rich.util.SecurityUtils;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;

@Entity
@Table(name = "RH_RAE_ATTACHMENT")
@SuppressWarnings("serial")
public class RaeAttachment extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeAttachment.class.toString());
		
	@Id
	@GeneratedValue(generator = "fileSeq")
	@SequenceGenerator(name = "fileSeq", sequenceName = "RH_RAE_FILE_SEQ", allocationSize = 1)
    @Column(name = "file_id")
    private int file_id;
	
	@Column(name = "file_tag")
    private String file_tag;

    @Column(name = "OUTPUT_NO")
    private Integer output_no;

    @Column(name = "SEQ")
    private Integer seq;

    @Column(name = "FILE_PATH")
    private String file_path;
    
    @Column(name = "FILE_NAME")
    private String file_name;
    
    @Column(name = "FILE_DISPLAY_NAME")
    private String file_display_name;

    @Column(name = "FILE_SIZE")
    private long file_size;

    @Column(name = "HASH_KEY")
    private String hash_key;
    
    @Transient
	private File file;
	
	@Transient
	private long fileKbSize;
	
	@Transient
	private RaeStaff raeStaff;
	
	public int getFile_id()
	{
		return file_id;
	}

	
	public void setFile_id(int file_id)
	{
		this.file_id = file_id;
	}


	public String getFile_tag()
	{
		return file_tag;
	}


	
	public void setFile_tag(String file_tag)
	{
		this.file_tag = file_tag;
	}


	public Integer getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}

	
	public Integer getSeq()
	{
		return seq;
	}

	
	public void setSeq(Integer seq)
	{
		this.seq = seq;
	}


	
	
	public String getFile_path()
	{
		return file_path;
	}


	
	public void setFile_path(String file_path)
	{
		this.file_path = file_path;
	}


	public String getFile_display_name()
	{
		return file_display_name;
	}

	
	public void setFile_display_name(String file_display_name)
	{
		this.file_display_name = file_display_name;
	}

	
	
	public String getFile_name()
	{
		return file_name;
	}


	
	public void setFile_name(String file_name)
	{
		this.file_name = file_name;
	}


	public long getFile_size()
	{
		return file_size;
	}

	
	public void setFile_size(long file_size)
	{
		this.file_size = file_size;
	}

	
	public String getHash_key()
	{
		return hash_key;
	}

	
	public void setHash_key(String hash_key)
	{
		this.hash_key = hash_key;
	}


	public long getFileKbSize()
	{
		return (int) Math.ceil(getFile_size() / (double) 1024);
	}
		
	public File getFile()
	{
		if (file == null)
		{
			file = new File(getFile_path());
		}
		
		return file;
	}
	
	
	
	
	public RaeStaff getRaeStaff()
	{
		if (getOutput_no() != null) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			RaeOutputStatus riStatus = dao.getRaeOutputStatusByOutputNo(output_no);
			if (riStatus != null) {
				RaeStaffDAO sDao = RaeStaffDAO.getInstance();
				raeStaff = sDao.getRaeStaffByStaffNo(riStatus.getPk().getStaff_number());
			}
		}
		return raeStaff;
	}


	public InputStream getFileInputStream() throws IOException, GeneralSecurityException
	{
		InputStream is = null;

		if (getFile() != null && getFile().exists())
		{
			Cipher cipher = SecurityUtils.getCipher(Cipher.DECRYPT_MODE);
			is = new CipherInputStream(new BufferedInputStream(new FileInputStream(getFile())), cipher);
		}

		return is;
	}
	
	@Override
	public int hashCode()
	{
		return Objects.hash(file_id);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeAttachment other = (RaeAttachment) obj;
		return file_id == other.file_id;
	}


	@Override
	public String toString()
	{
		return "RaeAttachment [file_id=" + file_id + ", file_tag=" + file_tag + ", output_no=" + output_no + ", seq="
				+ seq + ", file_path=" + file_path + ", file_display_name=" + file_display_name + ", file_size="
				+ file_size + ", hash_key=" + hash_key + "]";
	}


}
