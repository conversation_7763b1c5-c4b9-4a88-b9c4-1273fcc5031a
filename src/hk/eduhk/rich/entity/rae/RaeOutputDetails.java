package hk.eduhk.rich.entity.rae;

import java.util.Date;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.publication.OutputDetails_Q;
import hk.eduhk.rich.entity.publication.OutputHeader_P;
import hk.eduhk.rich.entity.publication.OutputHeader_P_PK;
import hk.eduhk.rich.entity.publication.PublicationDAO;

@Entity
@Table(name = "RH_RAE_OUTPUT_DTL")
@SuppressWarnings("serial")
public class RaeOutputDetails extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeOutputDetails.class.toString());
		
	@EmbeddedId
	private RaeOutputDetails_PK pk = new RaeOutputDetails_PK();

	@Column(name = "AUTHORSHIP_NAME", length = 500)
	private String authorship_name;

	@Column(name = "AUTHORSHIP_TYPE", length = 30)
	private String authorship_type;

	@Column(name = "NON_IED_STAFF_FLAG", length = 1)
	private String non_ied_staff_flag;

	@Column(name = "AUTHORSHIP_PERSON_ID")
	private Integer authorship_person_id;

	@Column(name = "AUTHORSHIP_ASSIGNMENT_ID")
	private Integer authorship_assignment_id;

	@Column(name = "AUTHORSHIP_STAFF_NO", length = 10)
	private String authorship_staff_no;

	@Column(name = "COLLAB_PERCENT")
	private Integer collab_percent;

	@Column(name = "AUTHORSHIP_DTL_TYPE", length = 30)
	private String authorship_dtl_type;

	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "output_no", referencedColumnName = "output_no", nullable = false, insertable = false, updatable = false)
	private RaeOutputHeader raeOutputHeader;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "output_no", referencedColumnName = "output_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "authorship_staff_no", referencedColumnName = "staff_number", nullable = false, insertable = false, updatable = false)
	})
	private RaeOutputStatus raeOutputStatus;
	
	
	public RaeOutputDetails_PK getPk()
	{
		return pk;
	}



	
	public void setPk(RaeOutputDetails_PK pk)
	{
		this.pk = pk;
	}



	
	public String getAuthorship_name()
	{
		return authorship_name;
	}



	
	public void setAuthorship_name(String authorship_name)
	{
		this.authorship_name = authorship_name;
	}



	
	public String getAuthorship_type()
	{
		return authorship_type;
	}



	
	public void setAuthorship_type(String authorship_type)
	{
		this.authorship_type = authorship_type;
	}


	
	public String getNon_ied_staff_flag()
	{
		return non_ied_staff_flag;
	}




	
	public void setNon_ied_staff_flag(String non_ied_staff_flag)
	{
		this.non_ied_staff_flag = non_ied_staff_flag;
	}




	public Integer getAuthorship_person_id()
	{
		return authorship_person_id;
	}



	
	public void setAuthorship_person_id(Integer authorship_person_id)
	{
		this.authorship_person_id = authorship_person_id;
	}



	
	public Integer getAuthorship_assignment_id()
	{
		return authorship_assignment_id;
	}



	
	public void setAuthorship_assignment_id(Integer authorship_assignment_id)
	{
		this.authorship_assignment_id = authorship_assignment_id;
	}



	
	public String getAuthorship_staff_no()
	{
		return authorship_staff_no;
	}



	
	public void setAuthorship_staff_no(String authorship_staff_no)
	{
		this.authorship_staff_no = authorship_staff_no;
	}



	
	public Integer getCollab_percent()
	{
		return collab_percent;
	}



	
	public void setCollab_percent(Integer collab_percent)
	{
		this.collab_percent = collab_percent;
	}



	
	public String getAuthorship_dtl_type()
	{
		return authorship_dtl_type;
	}



	
	public void setAuthorship_dtl_type(String authorship_dtl_type)
	{
		this.authorship_dtl_type = authorship_dtl_type;
	}



	public RaeOutputHeader getRaeOutputHeader()
	{
		if (raeOutputHeader != null) {
			try {
				raeOutputHeader.getOutput_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					RaeOutputDAO dao = RaeOutputDAO.getInstance();
					raeOutputHeader = dao.getRaeOutputHeader(getPk().getOutput_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return raeOutputHeader;
	}
	
	
	
	public RaeOutputStatus getRaeOutputStatus()
	{
		if (raeOutputStatus != null) {
			try {
				raeOutputStatus.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					RaeOutputDAO dao = RaeOutputDAO.getInstance();
					raeOutputStatus = dao.getRaeOutputStatus(getPk().getOutput_no(), getAuthorship_staff_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return raeOutputStatus;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutputDetails other = (RaeOutputDetails) obj;
		return Objects.equals(pk, other.pk);
	}




	@Override
	public String toString()
	{
		return "RaeOutputDetails [pk=" + pk + ", authorship_name=" + authorship_name + ", authorship_type="
				+ authorship_type + ", non_ied_staff_flag=" + non_ied_staff_flag + ", authorship_person_id="
				+ authorship_person_id + ", authorship_assignment_id=" + authorship_assignment_id
				+ ", authorship_staff_no=" + authorship_staff_no + ", collab_percent=" + collab_percent
				+ ", authorship_dtl_type=" + authorship_dtl_type + ", raeOutputHeader=" + raeOutputHeader
				+ ", raeOutputStatus=" + raeOutputStatus + "]";
	}
	
}
