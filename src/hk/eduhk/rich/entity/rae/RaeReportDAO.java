package hk.eduhk.rich.entity.rae;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.rae.RaeStaff;

public class RaeReportDAO extends BaseDAO
{

	private static RaeReportDAO instance = null;


	public static synchronized RaeReportDAO getInstance()
	{
		if (instance == null) instance = new RaeReportDAO();
		return instance;
	}

	
	public List<RaeReport> getRaeReportList()
	{
		List<RaeReport> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaeReport obj ORDER BY file_name ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaeReport> q = em.createQuery(query, RaeReport.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}

	public RaeReport updateRaeReport (RaeReport obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteRaeReport (Integer id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			RaeReport obj = em.find(RaeReport.class, id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public RaeReport getRaeReportById(Integer id)
	{
		RaeReport obj = null;
		
		if (id != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(RaeReport.class, id);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteData(String tableName) throws Exception {
	    UserTransaction utx = null;
	    try {
	        // Begin transaction
	        utx = pm.getUserTransaction();
	        if (utx == null) {
	            throw new Exception("UserTransaction is not available.");
	        }
	        utx.begin();

	        // Get database connection and execute SQL
	        try (Connection conn = pm.getConnection();
	             PreparedStatement pStmt = conn.prepareStatement("DELETE FROM RICH." + tableName)) {

	            if (conn == null) {
	                throw new Exception("Database connection is not available.");
	            }

	            pStmt.executeUpdate();
	        }

	        // Commit the transaction
	        utx.commit();
	    } catch (Exception e) {
	        // Rollback the transaction in case of an error
	        if (utx != null) {
	            try {
	                utx.rollback();
	            } catch (Exception rollbackEx) {
	                System.err.println("Rollback failed: " + rollbackEx.getMessage());
	            }
	        }
	        System.err.println("Error during data deletion: " + e.getMessage());
	        throw e;
	    }
	}
	
	public void updateHashKey(String tableName, String column, String column_hash) throws Exception {
	    Connection conn = null;
	    try {
	        conn = pm.getConnection();
	        if (conn == null) {
	            throw new Exception("Database connection is not available.");
	        }

	        conn.setAutoCommit(false); // Disable auto-commit for batch processing

	        String selectQuery = "SELECT " + column + " FROM RICH." + tableName + " WHERE " + column + " IS NOT NULL";
	        String updateQuery = "UPDATE RICH." + tableName + " SET " + column_hash + " = ? WHERE " + column + " = ?";

	        try (PreparedStatement selectStatement = conn.prepareStatement(selectQuery);
	             ResultSet resultSet = selectStatement.executeQuery();
	             PreparedStatement updateStatement = conn.prepareStatement(updateQuery)) {

	            int batchCount = 0;
	            while (resultSet.next()) {
	                String url = resultSet.getString(column);
	                String hashedUrl = BaseView.computeHash(url);

	                updateStatement.setString(1, hashedUrl);
	                updateStatement.setString(2, url);
	                updateStatement.addBatch();

	                if (++batchCount % 100 == 0) {
	                    updateStatement.executeBatch();
	                }
	            }
	            updateStatement.executeBatch(); // Execute remaining batches
	            conn.commit();
	        } catch (SQLException e) {
	            if (conn != null) conn.rollback();
	            throw e;
	        }
	    } catch (Exception e) {
	        logger.log(Level.WARNING, "Error during hash update", e);
	        throw e;
	    } finally {
	        if (conn != null) {
	            try {
	                conn.close();
	            } catch (SQLException e) {
	                logger.log(Level.WARNING, "Failed to close connection", e);
	            }
	        }
	    }
	}
	
	public void updateHyphen(String tableName, String column) throws Exception {
        Connection conn = null;
        PreparedStatement pStmt = null;

        try {
            // Get database connection
            conn = pm.getConnection(); // Ensure 'pm' is properly initialized
            if (conn == null) {
                throw new Exception("Database connection is not available.");
            }

            // SQL query to update the column by removing hyphens
            String updateQuery = "UPDATE RICH." + tableName + " SET " + column + " = REPLACE(" + column + ", '-', '') WHERE " + column + " IS NOT NULL";

            // Prepare and execute the update statement
            pStmt = conn.prepareStatement(updateQuery);
            int rowsUpdated = pStmt.executeUpdate();

            //System.out.println(rowsUpdated + " rows updated successfully in table " + tableName + " for column " + column);

        } catch (Exception e) {
            // Log and rethrow the exception
            logger.log(Level.WARNING, "Error during data update", e);
            throw e;
        } finally {
            // Close resources
            if (pStmt != null) {
                try {
                    pStmt.close();
                } catch (Exception e) {
                    logger.log(Level.WARNING, "Failed to close PreparedStatement", e);
                }
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch (Exception e) {
                    logger.log(Level.WARNING, "Failed to close Connection", e);
                }
            }
        }
    }
	
	public void insertData(String tableName, String userId) throws Exception {
	    Connection conn = null;
	    PreparedStatement pStmt = null;

	    try {
	        // Get database connection
	        conn = pm.getConnection();
	        if (conn == null) {
	            throw new Exception("Database connection is not available.");
	        }

	        // Construct the SQL query with placeholders
	        String sql = "INSERT INTO RICH." + tableName +
	                     " SELECT t.*, ?, SYSDATE, ?, SYSDATE " +
	                     " FROM (SELECT * FROM RICH." + tableName + "_V) t";
	        
	        //System.out.println("sql:"+sql);
	        
	        // Prepare the statement
	        pStmt = conn.prepareStatement(sql);

	        // Bind the userId parameter
	        pStmt.setString(1, userId); // First placeholder
	        pStmt.setString(2, userId); // Second placeholder
	        //System.out.println("pStmt:"+pStmt);
	        // Execute the query
	        pStmt.executeUpdate();

	    } catch (Exception e) {
	        // Log and rethrow the exception
	        logger.log(Level.WARNING, "Error during data insertion", e);
	        throw e;
	    } finally {
	        // Close resources
	        if (pStmt != null) {
	            try {
	                pStmt.close();
	            } catch (Exception e) {
	                logger.log(Level.WARNING, "Failed to close PreparedStatement", e);
	            }
	        }
	        if (conn != null) {
	            try {
	                conn.close();
	            } catch (Exception e) {
	                logger.log(Level.WARNING, "Failed to close Connection", e);
	            }
	        }
	    }
	}

	public List<Map<String, Object>> getReportData(String tableName) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();

        try (Connection conn = pm.getConnection();
             PreparedStatement pStmt = conn.prepareStatement("SELECT * FROM RICH." + tableName + " ORDER BY RECORD_NO");
             ResultSet rs = pStmt.executeQuery()) {

            if (conn == null) {
                throw new SQLException("Database connection is not available.");
            }

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    row.put(metaData.getColumnName(i), rs.getObject(i));
                }
                result.add(row);
            }
        } catch (SQLException e) {
            throw new Exception("Failed to fetch data from the database: " + e.getMessage(), e);
        }

        return result;
    }
	
	public List<Map<String, Object>> getColumnMappingsForTable(Integer reportId) throws Exception {
	    List<Map<String, Object>> result = new ArrayList<>();
	    String query = "SELECT COLUMN_NAME, FIELD_NAME, COLUMN_SEQ " +
	                   "FROM RICH.RH_RAE_REPORT_MAP " +
	                   "WHERE REPORT_ID = ? " +
	                   "ORDER BY COLUMN_SEQ";

	    try (Connection conn = pm.getConnection();
	         PreparedStatement pStmt = conn.prepareStatement(query)) {

	        // Check if the connection is valid
	        if (conn == null) {
	            throw new SQLException("Database connection is not available.");
	        }

	        // Set the tableName parameter in the PreparedStatement
	        pStmt.setInt(1, reportId);

	        // Execute the query and process the ResultSet
	        try (ResultSet rs = pStmt.executeQuery()) {
	            while (rs.next()) {
	                Map<String, Object> row = new HashMap<>();
	                row.put("COLUMN_NAME", rs.getString("COLUMN_NAME"));
	                row.put("FIELD_NAME", rs.getString("FIELD_NAME"));
	                row.put("COLUMN_SEQ", rs.getInt("COLUMN_SEQ"));
	                result.add(row);
	            }
	        }
	    } catch (SQLException e) {
	        throw new Exception("Failed to fetch data from the database: " + e.getMessage(), e);
	    }

	    return result;
	}
	
	
	
}