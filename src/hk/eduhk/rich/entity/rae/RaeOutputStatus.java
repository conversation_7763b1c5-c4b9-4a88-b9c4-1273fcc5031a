package hk.eduhk.rich.entity.rae;


import java.util.Objects;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.persistence.*;

import java.sql.SQLException;
import java.util.List;
import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.JournalRank;
import hk.eduhk.rich.entity.JournalRankDAO;

@Entity
@Table(name = "RH_RAE_OUTPUT_STATUS")
@SuppressWarnings("serial")
public class RaeOutputStatus extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeOutputStatus.class.toString());
		
	@EmbeddedId
	private RaeOutputStatus_PK pk = new RaeOutputStatus_PK();

	@Column(name = "PID")
	private Integer pid;

	@Column(name = "SEL_TYPE", length = 4)
	private String selType;

	@Column(name = "INFO_COMP", length = 1)
	private String infoComp;

	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "output_no", referencedColumnName = "output_no", nullable = false, insertable = false, updatable = false)
	private RaeOutputHeader raeOutputHeader;
	
	@Transient
	private String selectType;
	
	@Transient
	private Boolean interDisp;
	
	@Transient
	private String primaryField;
	
	@Transient
	private String secondaryField;
	
	@Transient
	private String justifications;	
	
	@Transient
	private String jcr;
	
	@Transient
	private String sjr;
	
	public String getSelectType()
	{
		if(selectType == null) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			selectType = dao.getRaeOutputSelect(pk.getStaff_number(), pk.getOutput_no()).getSelType();
			
			if(selectType == null)
				selectType = "NS";
		}
		

		
		return selectType;
	}


	
	public void setSelectType(String selectType)
	{
		this.selectType = selectType;
	}

	
	

	
	public String getJcr()
	{

		/*if(jcr == null && ( raeOutputHeader.getIssn() != null || raeOutputHeader.getEissn() != null)) {
			JournalRankDAO dao = JournalRankDAO.getInstance();
			List<JournalRank> list = dao.getJournalRankListByIssn(raeOutputHeader.getIssn(), raeOutputHeader.getEissn());
			
			//Handle the case that issn is not on the in list
			if(list != null) {
				list = list.stream().filter( a ->a.getJcr() != null).collect(Collectors.toList());
				for(JournalRank rank :list)
					jcr = rank.getJcr();
			}
		}*/
		if(jcr == null) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			RaeOutput tmp;
			try
			{
				tmp = dao.getRAEsearchByOutputNo(pk.getOutput_no());
				if (tmp != null) {
					jcr = tmp.getJcr();
				}
			}
			catch (SQLException e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
		}
		return jcr;
	}



	
	public String getSjr()
	{
		/*if(sjr == null && (raeOutputHeader.getIssn() != null || raeOutputHeader.getEissn() != null )) {
			JournalRankDAO dao = JournalRankDAO.getInstance();
			List<JournalRank> list = dao.getJournalRankListByIssn(raeOutputHeader.getIssn(), raeOutputHeader.getEissn());
			
			if(list != null) {
				list = list.stream().filter( a ->a.getSjr() != null).collect(Collectors.toList());
				for(JournalRank rank :list)
					sjr = rank.getSjr();
			}
			
		}*/
		if(sjr == null) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			RaeOutput tmp;
			try
			{
				tmp = dao.getRAEsearchByOutputNo(pk.getOutput_no());
				if (tmp != null) {
					sjr = tmp.getSjr();
				}
			}
			catch (SQLException e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
		}
		return sjr;
	}





	public Boolean getInterDisp()
	{
		if (interDisp == null) {
			interDisp = false;
			
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			
			if ( dao.getRaeOutputSelect(pk.getStaff_number(), pk.getOutput_no()).getInter_disp_ind() != null)
				interDisp = dao.getRaeOutputSelect(pk.getStaff_number(), pk.getOutput_no()).getInter_disp_ind().equals("Y");
		}
		return interDisp;
	}

	
	public void setInterDisp(Boolean interDisp)
	{
		this.interDisp = interDisp;
	}
	public String getPrimaryField()
	{
		if (primaryField == null) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			primaryField = dao.getRaeOutputSelect(pk.getStaff_number(), pk.getOutput_no()).getInter_disp_1();
		}
		
		return primaryField;
	}


	
	public void setPrimaryField(String primaryField)
	{
		this.primaryField = primaryField;
	}


	
	public String getSecondaryField()
	{
		if (secondaryField == null) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			secondaryField = dao.getRaeOutputSelect(pk.getStaff_number(), pk.getOutput_no()).getInter_disp_2();
		}
		return secondaryField;
	}


	
	public void setSecondaryField(String secondaryField)
	{
		this.secondaryField = secondaryField;
	}


	
	public String getJustifications()
	{
		if ( justifications == null ) {
			RaeOutputDAO dao = RaeOutputDAO.getInstance();
			justifications = dao.getRaeOutputSelect(pk.getStaff_number(), pk.getOutput_no()).getDbl_wt_just();
		}
		return justifications;
	}


	
	public void setJustifications(String justifications)
	{
		this.justifications = justifications;
	}

	
	
	public RaeOutputStatus_PK getPk()
	{
		return pk;
	}

	
	public void setPk(RaeOutputStatus_PK pk)
	{
		this.pk = pk;
	}

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public String getSelType()
	{
		return selType;
	}

	
	public void setSelType(String selType)
	{
		this.selType = selType;
	}

	
	public String getInfoComp()
	{
		return infoComp;
	}

	
	public void setInfoComp(String infoComp)
	{
		this.infoComp = infoComp;
	}


	
	public RaeOutputHeader getRaeOutputHeader()
	{
		if (raeOutputHeader != null) {
			try {
				raeOutputHeader.getOutput_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					RaeOutputDAO dao = RaeOutputDAO.getInstance();
					raeOutputHeader = dao.getRaeOutputHeader(getPk().getOutput_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return raeOutputHeader;
	}
	
	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeOutputStatus other = (RaeOutputStatus) obj;
		return Objects.equals(pk, other.pk);
	}



	@Override
	public String toString()
	{
		return "RaeOutputStatus [pk=" + pk + ", pid=" + pid + ", selType=" + selType + ", infoComp=" + infoComp
				+ ", selectType=" + selectType + ", interDisp=" + interDisp + ", primaryField=" + primaryField
				+ ", secondaryField=" + secondaryField + ", justifications=" + justifications + ", jcr=" + jcr
				+ ", sjr=" + sjr + "]";
	}

}
