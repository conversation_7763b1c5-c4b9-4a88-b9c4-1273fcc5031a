package hk.eduhk.rich.entity.rae;

import java.util.logging.Logger;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;
import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_Q_PK;


@Entity
@Table(name = "RH_RAE_UOA")
public class RaeUOA extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeUOA.class.toString());
	
	@Id
	@Column(name = "uoa_code")
	private String uoaCode ; 

	@Column(name = "uoa_desc")
	private String uoaDesc;
	
	@Column(name = "coor_faculty")
	private String coorFaculty;
	
	@Column(name = "panel_code")
	private String panelCode;

	
	public String getUoaCode()
	{
		return uoaCode;
	}

	
	public void setUoaCode(String uoaCode)
	{
		this.uoaCode = uoaCode;
	}

	
	public String getUoaDesc()
	{
		return uoaDesc;
	}

	
	public void setUoaDesc(String uoaDesc)
	{
		this.uoaDesc = uoaDesc;
	}

	
	public String getCoorFaculty()
	{
		return coorFaculty;
	}

	
	public void setCoorFaculty(String coorFaculty)
	{
		this.coorFaculty = coorFaculty;
	}

	
	public String getPanelCode()
	{
		return panelCode;
	}

	
	public void setPanelCode(String panelCode)
	{
		this.panelCode = panelCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((uoaCode == null) ? 0 : uoaCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeUOA other = (RaeUOA) obj;
		if (uoaCode == null)
		{
			if (other.uoaCode != null)
				return false;
		}
		else if (!uoaCode.equals(other.uoaCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "RaeUOA [uoaCode=" + uoaCode + ", uoaDesc=" + uoaDesc + ", coorFaculty=" + coorFaculty + ", panelCode="
				+ panelCode + "]";
	}	
	
	
	
	

}
