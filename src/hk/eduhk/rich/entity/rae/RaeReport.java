package hk.eduhk.rich.entity.rae;

import java.util.Date;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;
import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_Q_PK;


@Entity
@Table(name = "RH_RAE_REPORT")
public class RaeReport extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(RaeReport.class.toString());
	
	@Id
	@Column(name = "report_id")
	private Integer report_id ; 

	@Column(name = "report_name")
	private String report_name;
	
	@Column(name = "table_name")
	private String table_name;
	
	@Column(name = "file_name")
	private String file_name;

	@Column(name = "report_userstamp")
	private String report_userstamp;
	
	@Column(name = "report_timestamp")
	private Date report_timestamp;
	
	public Integer getReport_id()
	{
		return report_id;
	}

	
	public void setReport_id(Integer report_id)
	{
		this.report_id = report_id;
	}

	
	public String getReport_name()
	{
		return report_name;
	}

	
	public void setReport_name(String report_name)
	{
		this.report_name = report_name;
	}

	
	public String getTable_name()
	{
		return table_name;
	}

	
	public void setTable_name(String table_name)
	{
		this.table_name = table_name;
	}

	
	public String getFile_name()
	{
		return file_name;
	}

	
	public void setFile_name(String file_name)
	{
		this.file_name = file_name;
	}


	
	public String getReport_userstamp()
	{
		return report_userstamp;
	}


	
	public void setReport_userstamp(String report_userstamp)
	{
		this.report_userstamp = report_userstamp;
	}


	
	public Date getReport_timestamp()
	{
		return report_timestamp;
	}


	
	public void setReport_timestamp(Date report_timestamp)
	{
		this.report_timestamp = report_timestamp;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(report_id);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaeReport other = (RaeReport) obj;
		return Objects.equals(report_id, other.report_id);
	}


	@Override
	public String toString()
	{
		return "RaeReport [report_id=" + report_id + ", report_name=" + report_name + ", table_name=" + table_name
				+ ", file_name=" + file_name + ", report_userstamp=" + report_userstamp + ", report_timestamp="
				+ report_timestamp + "]";
	}



}
