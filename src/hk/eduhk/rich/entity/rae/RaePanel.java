package hk.eduhk.rich.entity.rae;

import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;


@Entity
@Table(name = "RH_L_RAE_PANEL_V")
@SuppressWarnings("serial")
public class RaePanel
{
	public static Logger logger = Logger.getLogger(RaePanel.class.toString());
		
	@EmbeddedId
	private RaePanel_PK pk = new RaePanel_PK();

	@Column(name = "lookup_level")
	private String lookup_level;

	@Column(name = "description")
	private String description;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;	
	
	@Column(name = "print_order")
	private Integer print_order;
	
	@Column(name = "parent_lookup_code")
	private String parent_lookup_code;
	

	public RaePanel_PK getPk()
	{
		return pk;
	}

	
	public void setPk(RaePanel_PK pk)
	{
		this.pk = pk;
	}

	
	
	public String getLookup_level()
	{
		return lookup_level;
	}


	
	public void setLookup_level(String lookup_level)
	{
		this.lookup_level = lookup_level;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}

	
	public String getParent_lookup_code()
	{
		return parent_lookup_code;
	}

	
	public void setParent_lookup_code(String parent_lookup_code)
	{
		this.parent_lookup_code = parent_lookup_code;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RaePanel other = (RaePanel) obj;
		return Objects.equals(pk, other.pk);
	}


	@Override
	public String toString()
	{
		return "RaePanel [pk=" + pk + ", description=" + description + ", enabled_flag="
				+ enabled_flag + ", print_order=" + print_order + ", parent_lookup_code=" + parent_lookup_code
				+ "]";
	}
	
}
