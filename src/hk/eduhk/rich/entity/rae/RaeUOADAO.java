package hk.eduhk.rich.entity.rae;

import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.rae.RaeStaff;

public class Rae<PERSON><PERSON><PERSON><PERSON> extends BaseDAO
{

	private static RaeUOADAO instance = null;


	public static synchronized RaeUOADAO getInstance()
	{
		if (instance == null) instance = new RaeUOADAO();
		return instance;
	}

	
	public List<RaeUOA> getRaeUOAList()
	{
		List<RaeUOA> objList = null;

		EntityManager em = null;
		String query = "SELECT obj FROM RaeUOA obj ORDER BY CAST(obj.uoaCode AS integer) ";
		try
		{
			em = getEntityManager();
			TypedQuery<RaeUOA> q = em.createQuery(query, RaeUOA.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}

	public RaeUOA updateRaeUOA (RaeUOA obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteRaeUOA (String code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			RaeUOA obj = em.find(RaeUOA.class, code);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public RaeUOA getRaeUOAByCode(String code)
	{
		RaeUOA obj = null;
		
		if (code != "")
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(RaeUOA.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
}