package hk.eduhk.rich.entity;

import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.UIComponent;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.commons.validator.routines.UrlValidator;
import org.primefaces.PrimeFaces;

import com.google.common.base.Strings;

import de.danielbechler.diff.ObjectDifferBuilder;
import de.danielbechler.diff.node.DiffNode;
import de.danielbechler.diff.node.Visit;
import de.danielbechler.diff.node.DiffNode.Visitor;
import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.*;
import hk.eduhk.rich.entity.award.*;
import hk.eduhk.rich.entity.importRI.*;
import hk.eduhk.rich.entity.patent.*;
import hk.eduhk.rich.entity.project.*;
import hk.eduhk.rich.entity.publication.*;
import hk.eduhk.rich.entity.rae.RaeStaff;
import hk.eduhk.rich.entity.rae.RaeStaffDAO;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.param.SysParamDAO;

@ManagedBean(name = "manageRIView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageRIView extends BaseView
{
	private static final Logger logger = Logger.getLogger(ManageRIView.class.getName());

	//Compare data level P and C
	private List<String> comparisonReport;
		
	protected List<EduSector> eduSectorList;
	protected List<DisciplinaryArea> disAreaList;
	
	protected List<StaffIdentity> staffNameList;
	protected List<StaffPast> staffPastList;
	private List<UserRole> userRoleList;
	private List<String> monthList;
	private List<String> yearList;

	private List <String> ktAdminDeptList;
	private List <String> rptAdminDeptList;
	private List <String> riAdminDeptList;
	protected Boolean canModify;
	protected Boolean canModifyRae;
	
	protected Boolean isRaeAdmin;
	protected Boolean isRdoAdmin;
	protected Boolean isKtAdmin;
	protected Boolean isDeptAdmin;
	protected Boolean isInputKtAdmin;
	protected Boolean isUoaAdmin;
	protected Boolean isLibAdmin;
	protected Boolean isAsst;
	protected Boolean isRaeOrLibAdmin;
	
	protected RaeStaff raeStaffDetail;
	protected StaffIdentity staffDetail;
	protected StaffPast staffPastDetail;
	
	protected String paramPid;
	protected String paramNo;
	protected String paramDataLevel;
	protected String paramConsent;
	protected Integer paramTabpage;
	
	protected String paramArea_code;
	protected String paramSource_id;
	protected String paramStaff_number;
	protected String paramRiType;
	protected String lockGrp;
	
	protected ImportRIStatus selectedImportStatus;
	
	protected Date currentDate = new Date();
	protected int currentYear = Year.now().getValue();
	
	private List<SelectItem> staffList;
	private List<SelectItem> formerStaffList;
	protected SelectItem optionPending = new SelectItem("CDCF_PENDING", "CDCF Pending");
	protected SelectItem optionProcessed = new SelectItem("CDCF_PROCESSED", "CDCF Processed");
	protected SelectItem optionGenerated = new SelectItem("CDCF_GENERATED", "CDCF Generated");
	protected SelectItem optionNotSelected = new SelectItem("CDCF_NOT_SEL", "CDCF Not Selected");
	protected SelectItem optionSpecial = new SelectItem("CDCF_SPEC", "CDCF Special Case");
	
	protected AssistantDAO asstDao = AssistantDAO.getInstance();
	protected StaffDAO staffDao = StaffDAO.getInstance();
	private RaeStaffDAO raeStaffDao = RaeStaffDAO.getInstance();
	private AccessDAO aDao = AccessDAO.getInstance();
	private PublicationDAO oDao = PublicationDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private AwardDAO awardDao = AwardDAO.getInstance();
	private PatentDAO pDao = PatentDAO.getInstance();
	private SysParamDAO sDao = SysParamDAO.getInstance();
	
	public Date getCurrentDate()
	{
		return currentDate;
	}
	
	public int getCurrentYear()
	{
		return currentYear;
	}

	
	public void setCurrentYear(int currentYear)
	{
		this.currentYear = currentYear;
	}
	
	public String getParamRiType()
	{
		return paramRiType;
	}

	
	public void setParamRiType(String paramRiType)
	{
		this.paramRiType = paramRiType;
	}

	public String getParamPid()
	{
		return (!Strings.isNullOrEmpty(paramPid))?paramPid:"";
	}

	
	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}

	
	public String getParamNo()
	{
		return paramNo;
	}


	
	public void setParamNo(String paramNo)
	{
		this.paramNo = paramNo;
	}


	
	public String getParamDataLevel()
	{
		return (!Strings.isNullOrEmpty(paramDataLevel))?paramDataLevel:"M";
	}

	
	public void setParamDataLevel(String paramDataLevel)
	{
		this.paramDataLevel = paramDataLevel;
	}
	

	public String getParamConsent()
	{
		return (!Strings.isNullOrEmpty(paramConsent))?paramConsent:"";
	}


	
	public void setParamConsent(String paramConsent)
	{
		this.paramConsent = paramConsent;
	}


	public Integer getParamTabpage()
	{
		return paramTabpage;
	}


	
	public void setParamTabpage(Integer paramTabpage)
	{
		this.paramTabpage = paramTabpage;
	}


	public String getParamArea_code()
	{
		return paramArea_code;
	}

	
	public void setParamArea_code(String paramArea_code)
	{
		this.paramArea_code = paramArea_code;
	}

	
	public String getParamSource_id()
	{
		return paramSource_id;
	}

	
	public void setParamSource_id(String paramSource_id)
	{
		this.paramSource_id = paramSource_id;
	}

	
	public String getParamStaff_number()
	{
		return paramStaff_number;
	}

	
	public void setParamStaff_number(String paramStaff_number)
	{
		this.paramStaff_number = paramStaff_number;
	}

	
	
	public Boolean getIsRaeAdmin()
	{
		if (isRaeAdmin == null) {
			isRaeAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("raeAdmin"))
					.collect(Collectors.toList());
			isRaeAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isRaeAdmin;
	}

	
	public void setIsRaeAdmin(Boolean isRaeAdmin)
	{
		this.isRaeAdmin = isRaeAdmin;
	}

	public Boolean getIsRdoAdmin() 
	{
		if (isRdoAdmin == null) {
			isRdoAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("rdoAdmin"))
					.collect(Collectors.toList());
			isRdoAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isRdoAdmin;
	}
	
	
	public void setIsRdoAdmin(Boolean isRdoAdmin)
	{
		this.isRdoAdmin = isRdoAdmin;
	}

	
	
	
	public Boolean getIsLibAdmin()
	{
		if (isLibAdmin == null) {
			isLibAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("libAdmin"))
					.collect(Collectors.toList());
			isLibAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isLibAdmin;
	}

	
	public void setIsLibAdmin(Boolean isLibAdmin)
	{
		this.isLibAdmin = isLibAdmin;
	}

	
	public Boolean getIsRaeOrLibAdmin()
	{
		if (isRaeOrLibAdmin == null) {
			isRaeOrLibAdmin = false;
			if (getIsLibAdmin() || getIsRaeAdmin()) {
				isRaeOrLibAdmin = true;
			}
		}
		return isRaeOrLibAdmin;
	}

	
	public void setIsRaeOrLibAdmin(Boolean isRaeOrLibAdmin)
	{
		this.isRaeOrLibAdmin = isRaeOrLibAdmin;
	}

	public Boolean getIsUoaAdmin()
	{
		if (isUoaAdmin == null) {
			isUoaAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("uoaAdmin"))
					.collect(Collectors.toList());
			isUoaAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isUoaAdmin;
	}

	
	public void setIsUoaAdmin(Boolean isUoaAdmin)
	{
		this.isUoaAdmin = isUoaAdmin;
	}

	public Boolean getIsKtAdmin()
	{
		if (isKtAdmin == null) {
			isKtAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("ktAdmin"))
					.collect(Collectors.toList());
			isKtAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isKtAdmin;
	}


	
	public void setIsKtAdmin(Boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}


	
	
	public Boolean getIsDeptAdmin()
	{
		if (isDeptAdmin == null) {
			isDeptAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("deptAdmin"))
					.collect(Collectors.toList());
			isDeptAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isDeptAdmin;
	}


	
	public void setIsDeptAdmin(Boolean isDeptAdmin)
	{
		this.isDeptAdmin = isDeptAdmin;
	}


	public Boolean getIsInputKtAdmin()
	{
		if (isInputKtAdmin == null) {
			isInputKtAdmin = false;
			userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("inputktAdmin"))
					.collect(Collectors.toList());
			isInputKtAdmin = (!tmpList.isEmpty())?true:false;
		}
		return isInputKtAdmin;
	}


	
	public void setIsInputKtAdmin(Boolean isInputKtAdmin)
	{
		this.isInputKtAdmin = isInputKtAdmin;
	}


	public Boolean getIsAsst()
	{
		if (isAsst == null) {
			isAsst = false;
			if (!Strings.isNullOrEmpty(getParamPid())) {
				//get acadStaff list by asst id
				List<Assistant> acadStaffList = asstDao.getAcadStaffListByAsstId(getCurrentUserId(), "APPROVED");
				for (Assistant a:acadStaffList) {	
					//check output name list has pid
					if (paramPid.equals(String.valueOf(a.getPk().getPid()))){
						isAsst = true;
					}				
				}
			}
		}
		return isAsst;
	}
	
	public void setIsAsst(Boolean isAsst)
	{
		this.isAsst = isAsst;
	}


	public Boolean getCanModify()
	{
		if (canModify == null) {
			canModify = true;
			AccessDAO dao = AccessDAO.getInstance();
			SecFuncLock selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_RI", "MANAGE_RI", "ALL");
			if (selectedSecFuncLock != null) {
				//Y = Is Exclusive, N = Not Exclusive
				if ("Y".equals(selectedSecFuncLock.getLock_status())) {
					//Check user is extended access user
					SecFuncLockUser user = dao.getSecFuncLockUser("EXCLUSIVE_RI", "MANAGE_RI", getLoginUserId());
					if (user != null) {
						//N = Not Extended Access User
						if ("N".equals(user.getLock_status())) {
							canModify = false;
						}else {
							canModify = true;
						}
					}else {
						canModify = false;
					}
					//check user is rdo admin
					if (getIsRdoAdmin()) {
						canModify = true;
					}
				}
			}
		}
		if (!canModify) {
			PrimeFaces.current().executeScript("disable('editForm');");
		}
		return canModify;
	}
	
	public void setCanModify(Boolean canModify)
	{
		this.canModify = canModify;
	}


	
	public Boolean getCanModifyRae()
	{
		if (canModifyRae == null) {
			canModifyRae = false;
			AccessDAO dao = AccessDAO.getInstance();
			SecFuncLock selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", "RESEARCHER");
			if ("N".equals(selectedSecFuncLock.getLock_status())) {
				canModifyRae = true;
			}
			if (getIsUoaAdmin()) {
				selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_RAE", "MANAGE_RAE", "UOAADMIN");
				if ("N".equals(selectedSecFuncLock.getLock_status())) {
					canModifyRae = true;
				}
			}
			if (getIsRaeOrLibAdmin()) {
				canModifyRae = true;
			}
		}
		if (!canModifyRae) {
			PrimeFaces.current().executeScript("disable('editForm');");
		}
		return canModifyRae;
	}

	
	public void setCanModifyRae(Boolean canModifyRae)
	{
		this.canModifyRae = canModifyRae;
	}

	
	public String getLockGrp()
	{
		if (lockGrp == null) {
			lockGrp = "RESEARCHER";
			if (getIsUoaAdmin()) {
				lockGrp = "UOAADMIN";
			}
		}
		return lockGrp;
	}

	
	public void setLockGrp(String lockGrp)
	{
		this.lockGrp = lockGrp;
	}

	public List<UserRole> getUserRoleList(){
		if (userRoleList == null) {
			userRoleList = aDao.getUserRoleListByUserId(getLoginUserId());
		}
		return userRoleList;
	}

	
	
	public void setUserRoleList(List<UserRole> userRoleList)
	{
		this.userRoleList = userRoleList;
	}
	
	public List<StaffPast> getStaffPastList()
	{
		if (staffPastList == null) {
			staffPastList = staffDao.getPastStaffList();
		}
		return staffPastList;
	}

	public List<StaffIdentity> getStaffNameList()
	{
		if (staffNameList == null) {
			staffNameList = staffDao.getAcadStaffList(null);
		}
		return staffNameList;
	}

	public void setStaffNameList(List<StaffIdentity> staffNameList)
	{
		this.staffNameList = staffNameList;
	}
	
	public void setStaffPastList(List<StaffPast> staffPastList)
	{
		this.staffPastList = staffPastList;
	}
	
	public static String getMonth(int month)
	{
		return new DateFormatSymbols(Locale.US).getMonths()[month - 1];
	}
	
	public List<String> getMonthList()
	{
		if (monthList == null) {
			monthList = new ArrayList<>();
			for (int m = 1; m < 13; m++) {
				monthList.add(String.valueOf(m));
			}
		}
		return monthList;
	}

	
	public List<String> getYearList()
	{
		if (yearList == null) {
			int year = Calendar.getInstance().get(Calendar.YEAR) +5;
			yearList = new ArrayList<>();
			for (int y = year; y > 1972; y--) {
				yearList.add(String.valueOf(y));
			}
		}
		return yearList;
	}
	//check has profile
	public String getStaffProfilePid(String staff_no) {
		String pid = null;
		if (!Strings.isNullOrEmpty(staff_no)) {
			StaffInfo s = staffDao.getStaffProfileByStaffNo(staff_no);
			if (s != null) {
				pid = String.valueOf(s.getPid());
			}
		}
		return pid;
	}
	
	public List<EduSector> getEduSectorList()
	{
		if (eduSectorList == null) {
			eduSectorList = new ArrayList<EduSector>();
			List<EduSector> lvOneList = oDao.getEduSectorList(1);
			List<EduSector> lvTwoList = oDao.getEduSectorList(2);
			for (EduSector o:lvOneList) {
				List<EduSector> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				for (EduSector two:tmpLvTwoList) {
					two.setParent_lookup_code(two.getParent_lookup_code()+1000);
				}
				o.getPk().setLookup_code(o.getPk().getLookup_code()+1000);
				eduSectorList.add(o);
				eduSectorList.addAll(tmpLvTwoList);
			}
		}
		return eduSectorList;
	}
	
	
	public void setEduSectorList(List<EduSector> eduSectorList)
	{
		this.eduSectorList = eduSectorList;
	}

	public String getEduSectorParentLookupCode(String lookupCode) {
		String result = null;
		List<EduSector> lvTwoList = oDao.getEduSectorList(2);
		if (lvTwoList != null) {
			List<EduSector> tmpList = lvTwoList.stream()
					.filter(y -> y.getPk().getLookup_code().equals(lookupCode))
					.collect(Collectors.toList());
			if (!tmpList.isEmpty()) {
				result = tmpList.get(0).getParent_lookup_code();
			}
			
		}
		return result;
	}
	
	public List<DisciplinaryArea> getDisAreaList()
	{
		if (disAreaList == null) {
			disAreaList = new ArrayList<DisciplinaryArea>();
			List<DisciplinaryArea> lvOneList = oDao.getDisAreaList(1);
			List<DisciplinaryArea> lvTwoList = oDao.getDisAreaList(2);
			for (DisciplinaryArea o:lvOneList) {
				List<DisciplinaryArea> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				for (DisciplinaryArea two:tmpLvTwoList) {
					two.setParent_lookup_code(two.getParent_lookup_code()+1000);
				}
				o.getPk().setLookup_code(o.getPk().getLookup_code()+1000);
				disAreaList.add(o);
				disAreaList.addAll(tmpLvTwoList);
			}
		}
		return disAreaList;
	}

	
	public String getDisAreaParentLookupCode(String lookupCode) {
		String result = null;
		List<DisciplinaryArea> lvTwoList = oDao.getDisAreaList(2);
		if (lvTwoList != null) {
			List<DisciplinaryArea> tmpList = lvTwoList.stream()
					.filter(y -> y.getPk().getLookup_code().equals(lookupCode))
					.collect(Collectors.toList());
			if (!tmpList.isEmpty()) {
				result = tmpList.get(0).getParent_lookup_code();
			}
			
		}
		return result;
	}
	

	public RaeStaff getRaeStaffDetail(String pid, String staffNo, Boolean hasRight)
	{
		if (raeStaffDetail == null) {
			if (hasRight) {
				if (!Strings.isNullOrEmpty(pid)) {
					raeStaffDetail = raeStaffDao.getRaeStaffDetailsByPid(Integer.parseInt(pid));
				}else if (!Strings.isNullOrEmpty(staffNo)) {
					raeStaffDetail = raeStaffDao.getRaeStaffByStaffNo(staffNo);
				}else {
					raeStaffDetail = raeStaffDao.getRaeStaffByUserId(getCurrentUserId());
				}
			}else {
				raeStaffDetail = raeStaffDao.getRaeStaffByUserId(getCurrentUserId());
				//System.out.println("raeStaffDetail:"+raeStaffDetail);
				//System.out.println("getCurrentUserId():"+getCurrentUserId());
			}
		}
		return raeStaffDetail;
	}

	
	public void setRaeStaffDetail(RaeStaff raeStaffDetail)
	{
		this.raeStaffDetail = raeStaffDetail;
	}

	public StaffIdentity getStaffDetail(String pid, String staffNo, Boolean hasRight)
	{
		if (staffDetail == null) {
			if (hasRight) {
				if (!Strings.isNullOrEmpty(pid)) {
					staffDetail = staffDao.getStaffDetailsByPid(Integer.parseInt(pid));
				}else if (!Strings.isNullOrEmpty(staffNo)) {
					staffDetail = staffDao.getStaffDetailsByStaffNo(staffNo);
				}else {
					staffDetail = staffDao.getStaffDetailsByUserId(getCurrentUserId());
				}
			}else {
				staffDetail = staffDao.getStaffDetailsByUserId(getCurrentUserId());
			}
		}
		return staffDetail;
	}
	
	public void setStaffDetail(StaffIdentity staffDetail)
	{
		this.staffDetail = staffDetail;
	}
	
	
	public StaffPast getStaffPastDetail(String pid, String staffNo, Boolean hasRight)
	{
		if (staffPastDetail == null) {
			if (hasRight) {
				if (!Strings.isNullOrEmpty(pid)) {
					staffPastDetail = staffDao.getPastStaffDetailsByPid(Integer.parseInt(pid));
				}
			}
		}
		return staffPastDetail;
	}


	
	public void setStaffPastDetail(StaffPast staffPastDetail)
	{
		this.staffPastDetail = staffPastDetail;
	}


	public ImportRIStatus getSelectedImportStatus()
	{
		if(selectedImportStatus == null && getParamArea_code() != null
				&& getParamSource_id() != null && getParamStaff_number() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			ImportRIStatus_PK pk = new ImportRIStatus_PK();
			pk.setArea_code(getParamArea_code());
			pk.setSource_id(getParamSource_id());
			pk.setStaff_number(getParamStaff_number());
			selectedImportStatus = dao.getStatusByPK(pk);
		}
		
		return selectedImportStatus;
	}
	
	public void updateSelectedImportStatus(Integer RI_NO) {
		if (getSelectedImportStatus() != null && RI_NO != null ) {
			selectedImportStatus.setImport_status("IMPORTED");
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			selectedImportStatus.setRi_no(RI_NO);
			dao.updateStatus(selectedImportStatus);
		}
	}

	
	public List<String> getComparisonReport()
	{
		if (comparisonReport == null && !Strings.isNullOrEmpty(getParamNo()) && !Strings.isNullOrEmpty(getParamRiType())){
			comparisonReport = new ArrayList<>();
			if (paramRiType.equals("output")) {
				comparisonReport = outputCompare();
			}		
			if (paramRiType.equals("project")) {
				comparisonReport = projectCompare();
			}	
			if (paramRiType.equals("award")) {
				comparisonReport = awardCompare();
			}	
			if (paramRiType.equals("patent")) {
				comparisonReport = patentCompare();
			}	
		}
		return comparisonReport;
	}

	
	public void setComparisonReport(List<String> comparisonReport)
	{
		this.comparisonReport = comparisonReport;
	}
	
	public List<String> outputCompare(){
		List<String> result = new ArrayList<>();
		OutputHeader_P header_p = oDao.getOutputHeader_P(Integer.valueOf(paramNo), "P");
		OutputHeader_P header_c = oDao.getOutputHeader_P(Integer.valueOf(paramNo), "C");
		// Build the DiffNode
        DiffNode diff = ObjectDifferBuilder.buildDefault().compare(header_p, header_c);
        if (diff.hasChanges()) {
            diff.visit(new Visitor() {
                public void node(DiffNode node, Visit visit)
                {
                    if (!node.hasChildren()) { // Only print if the property has no child
                        Object pValue = node.canonicalGet(header_p);
                        Object cValue = node.canonicalGet(header_c);
                        if (!node.getPropertyName().equals("timestamp") && !node.getPropertyName().equals("creationDate") 
                        		&& !node.getPropertyName().equals("creator") && !node.getPropertyName().equals("data_level")) {
                        String message = "<span style='font-weight:700; text-align: letf; border-left: 10px solid #f06524; background: #efeae75c; color: #186ba0; border-radius: 4px; padding: 5px 6px; display:block; width:250px;'>" + 
                        						node.getPropertyName() + 
                        						"</span><br/><span style='font-weight:700; color:#186ba0;'>Published: </span><span style='color:#186ba0;'>" +
                        						(pValue == null ? "" : pValue) + 
				                        		"</span><br/><span style='font-weight:700; color:#026539;'>Snapshot: </span><span style='color:#026539;'>" + 
				                        		(cValue == null ? "" : cValue) + 
				                        		"</span>";
                        result.add(message);}
                    }
                }
            });
        }
		return result;
	}
	
	public List<String> projectCompare(){
		List<String> result = new ArrayList<>();
		ProjectHeader_P header_p = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "P");
		ProjectHeader_P header_c = projDao.getProjectHeader_P(Integer.valueOf(paramNo), "C");
		// Build the DiffNode
        DiffNode diff = ObjectDifferBuilder.buildDefault().compare(header_p, header_c);
        if (diff.hasChanges()) {
            diff.visit(new Visitor() {
                public void node(DiffNode node, Visit visit)
                {
                    if (!node.hasChildren()) { // Only print if the property has no child
                        Object pValue = node.canonicalGet(header_p);
                        Object cValue = node.canonicalGet(header_c);
                        if (!node.getPropertyName().equals("timestamp") && !node.getPropertyName().equals("creationDate") && !node.getPropertyName().equals("data_level")) {
                        String message = "<span style='font-weight:700; text-align: letf; border-left: 10px solid #f06524; background: #efeae75c; color: #186ba0; border-radius: 4px; padding: 5px 6px; display:block; width:250px;'>" + 
                        						node.getPropertyName() + 
                        						"</span><br/><span style='font-weight:700; color:#186ba0;'>Published: </span><span style='color:#186ba0;'>" +
                        						(pValue == null ? "" : pValue) + 
				                        		"</span><br/><span style='font-weight:700; color:#026539;'>Snapshot: </span><span style='color:#026539;'>" + 
				                        		(cValue == null ? "" : cValue) + 
				                        		"</span>";
                        result.add(message);}
                    }
                }
            });
        }
		return result;
	}
	
	public List<String> awardCompare(){
		List<String> result = new ArrayList<>();
		AwardHeader_P header_p = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "P");
		AwardHeader_P header_c = awardDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
		// Build the DiffNode
        DiffNode diff = ObjectDifferBuilder.buildDefault().compare(header_p, header_c);
        if (diff.hasChanges()) {
            diff.visit(new Visitor() {
                public void node(DiffNode node, Visit visit)
                {
                    if (!node.hasChildren()) { // Only print if the property has no child
                        Object pValue = node.canonicalGet(header_p);
                        Object cValue = node.canonicalGet(header_c);
                        if (!node.getPropertyName().equals("timestamp") && !node.getPropertyName().equals("creationDate") && !node.getPropertyName().equals("data_level")) {
                        String message = "<span style='font-weight:700; text-align: letf; border-left: 10px solid #f06524; background: #efeae75c; color: #186ba0; border-radius: 4px; padding: 5px 6px; display:block; width:250px;'>" + 
                        						node.getPropertyName() + 
                        						"</span><br/><span style='font-weight:700; color:#186ba0;'>Published: </span><span style='color:#186ba0;'>" +
                        						(pValue == null ? "" : pValue) + 
				                        		"</span><br/><span style='font-weight:700; color:#026539;'>Snapshot: </span><span style='color:#026539;'>" + 
				                        		(cValue == null ? "" : cValue) + 
				                        		"</span>";
                        result.add(message);}
                    }
                }
            });
        }
		return result;
	}
	
	public List<String> patentCompare(){
		List<String> result = new ArrayList<>();
		PatentHeader_P header_p = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "P");
		PatentHeader_P header_c = pDao.getPatentHeader_P(Integer.valueOf(paramNo), "C");
		// Build the DiffNode
        DiffNode diff = ObjectDifferBuilder.buildDefault().compare(header_p, header_c);
        if (diff.hasChanges()) {
            diff.visit(new Visitor() {
                public void node(DiffNode node, Visit visit)
                {
                    if (!node.hasChildren()) { // Only print if the property has no child
                        Object pValue = node.canonicalGet(header_p);
                        Object cValue = node.canonicalGet(header_c);
                        if (!node.getPropertyName().equals("timestamp") && !node.getPropertyName().equals("creationDate") && !node.getPropertyName().equals("data_level")) {
                        String message = "<span style='font-weight:700; text-align: letf; border-left: 10px solid #f06524; background: #efeae75c; color: #186ba0; border-radius: 4px; padding: 5px 6px; display:block; width:250px;'>" + 
                        						node.getPropertyName() + 
                        						"</span><br/><span style='font-weight:700; color:#186ba0;'>Published: </span><span style='color:#186ba0;'>" +
                        						(pValue == null ? "" : pValue) + 
				                        		"</span><br/><span style='font-weight:700; color:#026539;'>Snapshot: </span><span style='color:#026539;'>" + 
				                        		(cValue == null ? "" : cValue) + 
				                        		"</span>";
                        result.add(message);}
                    }
                }
            });
        }
		return result;
	}
	
	
	public List<SelectItem> getStaffList()
	{
		if (staffList == null) {
			staffList = new ArrayList<SelectItem>();
			if (getStaffNameList() != null) {
				for (StaffIdentity s:staffNameList) {
					String chineseName = (s.getChinesename() != null)?" " + s.getChinesename():"";
					String deptCode = (s.getDept_code() != null)?" [" + s.getDept_code() + "]":"";
					SelectItem option = new SelectItem(s.getStaff_number(), s.getFullname() + chineseName + deptCode);
					staffList.add(option);
				}
			}
		}
		return staffList;
	}


	
	public void setStaffList(List<SelectItem> staffList)
	{
		this.staffList = staffList;
	}


	public List<SelectItem> getFormerStaffList()
	{
		if (formerStaffList == null) {
			formerStaffList = new ArrayList<SelectItem>();
			if (getStaffPastList() != null) {
				for (StaffPast s:staffPastList) {
					SelectItem option = new SelectItem(s.getStaff_number(), s.getFullname());
					formerStaffList.add(option);
				}
			}
		}
		return formerStaffList;
	}

	public void setFormerStaffList(List<SelectItem> formerStaffList)
	{
		this.formerStaffList = formerStaffList;
	}
	
	public String getAPA(OutputHeader_P selectedHeader_p)
	{
		String result;
		//retrieve apa version
	   	String apaVer = sDao.getSysParamValueByCode("APA_EDITED_BOOK");
	   	if ("V7".equals(apaVer)) {
	   		result = getAPAv7(selectedHeader_p);
	   	}else if("V2".equals(apaVer)){
	   		result = getAPA_old(selectedHeader_p);
	   	}else {
	   		result = getAPAv7(selectedHeader_p);
	   	}
	   	
		return result;
	}
	//ver 8
	public String getMLA(OutputHeader_P selectedHeader_p)
	{
		String result = "";
	    boolean withNonAscii = StringUtils.equals(selectedHeader_p.getLanguage(), "C");
	    Locale lang = (withNonAscii)?new Locale("zh", "HK"):new Locale("en", "US");
	    String comma = (withNonAscii)?"，":", ";
	    String fullStop = (withNonAscii)?"。":". ";
	    String openQuote= (withNonAscii)?"《":" \"";
	    String closeQuote= (withNonAscii)?"》":"\" ";
	    String editedBy = (withNonAscii)?"":"Edited by ";

	    	//set page num
		   	if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())) {
		   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from()+"-"+selectedHeader_p.getPage_num_to());
		   	}else if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())){
		   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from());
		   	}else {
		   		selectedHeader_p.setPage_num("");
		   	}
		   	
		   	String publicationYear = (selectedHeader_p.getFrom_year() != null)?String.valueOf(selectedHeader_p.getFrom_year()):"";
		   	
			String articleTitle = selectedHeader_p.getTitle_jour_book();
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getOutput_title_continue())) 
				articleTitle = articleTitle + " " + selectedHeader_p.getOutput_title_continue();

			// A book, and other such as Film, video and Computer software or system
			if (selectedHeader_p.getSap_output_type().equals("120") || selectedHeader_p.getSap_output_type().equals("140") || selectedHeader_p.getSap_output_type().equals("150") 
				||  selectedHeader_p.getSap_output_type().equals("910") ||  selectedHeader_p.getSap_output_type().equals("920") || selectedHeader_p.getSap_output_type().equals("230") 
				|| selectedHeader_p.getSap_output_type().equals("270"))
			{
		          if(!selectedHeader_p.getSap_output_type().equals("150")) 
		          {
		            if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          }
		          else 
		          {       
		              if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) {
		                result += selectedHeader_p.getName_other_editors();
		              }
		              else 
		              {
		                if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		              }
		          }
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity() + " ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
		          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
		      }
			
		      //A chapter in a book
		      else if (selectedHeader_p.getSap_output_type().equals("130") || selectedHeader_p.getSap_output_type().equals("210") 
		          || selectedHeader_p.getSap_output_type().equals("220") || selectedHeader_p.getSap_output_type().equals("240")
		          || selectedHeader_p.getSap_output_type().equals("930") || selectedHeader_p.getSap_output_type().equals("350"))
		      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors() + comma;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art() + "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += " (" + selectedHeader_p.getPage_num() + ")";   
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art()) || !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity() + " ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
		          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
		      }
			
		      //An article in a journal
		      else if (selectedHeader_p.getSap_output_type().equals("160") || selectedHeader_p.getSap_output_type().equals("170") || selectedHeader_p.getSap_output_type().equals("420")
		          || selectedHeader_p.getSap_output_type().equals("940"))
		      {       
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += openQuote + articleTitle + closeQuote + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art() + "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getVol_issue())) result += comma + "vol. " + selectedHeader_p.getVol_issue();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getArticle_num())) result += comma + "no. " + selectedHeader_p.getArticle_num();  
		          if (selectedHeader_p.getFrom_month() != null) result += comma + getFullMonth(selectedHeader_p.getFrom_month(), lang);
		          if (selectedHeader_p.getFrom_year() != null) result += " " + publicationYear;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += comma + "pp. " + selectedHeader_p.getPage_num();  
		      }
			
		      //Conference paper
		      else if (selectedHeader_p.getSap_output_type().equals("180") || selectedHeader_p.getSap_output_type().equals("190") || selectedHeader_p.getSap_output_type().equals("200")
		          || selectedHeader_p.getSap_output_type().equals("950"))
		      {
		    	  if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result +=   selectedHeader_p.getTitle_paper_art() ;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += comma + selectedHeader_p.getCity();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += fullStop + editedBy + selectedHeader_p.getName_other_editors();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += comma + selectedHeader_p.getPublisher();
		          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += comma + "pp. " + selectedHeader_p.getPage_num();  
		      }
			
		      //A report
		      else if (selectedHeader_p.getSap_output_type().equals("250") || selectedHeader_p.getSap_output_type().equals("260") 
		          || selectedHeader_p.getSap_output_type().equals("280") || selectedHeader_p.getSap_output_type().equals("290"))      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += fullStop + selectedHeader_p.getPublisher();
		          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
		      }
				
			// Other type
			else if(selectedHeader_p.getSap_output_type().equals("340")) 
			{
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors();
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += fullStop + selectedHeader_p.getPublisher();
	          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
			}

			// Other type
			else
			{
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += fullStop + selectedHeader_p.getPublisher();
	          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
			}
			
			//set doi
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getDoi())) result += fullStop + "doi:"+selectedHeader_p.getDoi() + fullStop;
			//set url
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getFulltext_url()) && GenericValidator.isBlankOrNull(selectedHeader_p.getDoi())) {
				result += comma + selectedHeader_p.getFulltext_url() + fullStop;
			}
			return result;
		
	}
	//ver 17
	public String getChicago(OutputHeader_P selectedHeader_p)
	{
		String result = "";
	    boolean withNonAscii = StringUtils.equals(selectedHeader_p.getLanguage(), "C");
	    Locale lang = (withNonAscii)?new Locale("zh", "HK"):new Locale("en", "US");
	    String comma = (withNonAscii)?"，":", ";
	    String fullStop = (withNonAscii)?"。":". ";
	    String openQuote= (withNonAscii)?"《":" \"";
	    String closeQuote= (withNonAscii)?"》":"\" ";

	    	//set page num
		   	if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())) {
		   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from()+"-"+selectedHeader_p.getPage_num_to());
		   	}else if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())){
		   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from());
		   	}else {
		   		selectedHeader_p.setPage_num("");
		   	}
		   	
		   	String publicationYear = (selectedHeader_p.getFrom_year() != null)?String.valueOf(selectedHeader_p.getFrom_year()):"";
		   	
			String articleTitle = selectedHeader_p.getTitle_jour_book();
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getOutput_title_continue())) 
				articleTitle = articleTitle + " " + selectedHeader_p.getOutput_title_continue();

			// A book, and other such as Film, video and Computer software or system
			if (selectedHeader_p.getSap_output_type().equals("120") || selectedHeader_p.getSap_output_type().equals("140") || selectedHeader_p.getSap_output_type().equals("150") 
				||  selectedHeader_p.getSap_output_type().equals("910") ||  selectedHeader_p.getSap_output_type().equals("920") || selectedHeader_p.getSap_output_type().equals("230") || selectedHeader_p.getSap_output_type().equals("270"))
			{
		          if(!selectedHeader_p.getSap_output_type().equals("150")) 
		          {
		            if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          }
		          else 
		          {       
		              if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) {
		                result += selectedHeader_p.getName_other_editors();
		              }
		              else 
		              {
		                if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		              }
		          }
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getVol_issue())) result += "Vol. " + selectedHeader_p.getVol_issue() + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity() + " ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
		          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
		      }
			
		      //A chapter in a book
		      else if (selectedHeader_p.getSap_output_type().equals("130") || selectedHeader_p.getSap_output_type().equals("210") 
		          || selectedHeader_p.getSap_output_type().equals("220") || selectedHeader_p.getSap_output_type().equals("240")
		          || selectedHeader_p.getSap_output_type().equals("930") || selectedHeader_p.getSap_output_type().equals("350"))
		      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors() + comma;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art() + "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += " (" + selectedHeader_p.getPage_num() + ")";   
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art()) || !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
		          if (!GenericValidator.isBlankOrNull(publicationYear)) result +=  comma + publicationYear;
		      }
			
		      //An article in a journal
		      else if (selectedHeader_p.getSap_output_type().equals("160") || selectedHeader_p.getSap_output_type().equals("170") || selectedHeader_p.getSap_output_type().equals("420")
		          || selectedHeader_p.getSap_output_type().equals("940"))
		      {       
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += openQuote + articleTitle + closeQuote + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art() + "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getVol_issue())) result += " " + selectedHeader_p.getVol_issue();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getArticle_num())) result += comma + "no. " + selectedHeader_p.getArticle_num();  
		          if (selectedHeader_p.getFrom_month() != null || selectedHeader_p.getFrom_year() != null) result += " (";
		          if (selectedHeader_p.getFrom_month() != null) result += getShortMonth(selectedHeader_p.getFrom_month(), lang);
		          if (selectedHeader_p.getFrom_year() != null) result += " " + publicationYear;
		          if (selectedHeader_p.getFrom_month() != null || selectedHeader_p.getFrom_year() != null) result += ")";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += ": " + selectedHeader_p.getPage_num();  
		      }
			
		      //Conference paper
		      else if (selectedHeader_p.getSap_output_type().equals("180") || selectedHeader_p.getSap_output_type().equals("190") || selectedHeader_p.getSap_output_type().equals("200")
		          || selectedHeader_p.getSap_output_type().equals("950"))
		      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos() + fullStop;
		          if (selectedHeader_p.getFrom_year() != null) result += publicationYear + fullStop;
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result +=   selectedHeader_p.getTitle_paper_art() ;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += comma + selectedHeader_p.getCity();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher() + fullStop;
		      }
			
		      //A report
		      else if (selectedHeader_p.getSap_output_type().equals("250") || selectedHeader_p.getSap_output_type().equals("260") 
		          || selectedHeader_p.getSap_output_type().equals("280") || selectedHeader_p.getSap_output_type().equals("290"))      {
		    	  if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos() + fullStop;
		          if (selectedHeader_p.getFrom_year() != null) result += publicationYear + fullStop;
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity() + " ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher() + fullStop;
		      }
				
			// Other type
			else if(selectedHeader_p.getSap_output_type().equals("340")) 
			{
				if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos() + fullStop;
		       if (selectedHeader_p.getFrom_year() != null) result += publicationYear + fullStop;
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity() + " ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher() + fullStop;
			}

			// Other type
			else
			{
				if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos() + fullStop;
		       if (selectedHeader_p.getFrom_year() != null) result += publicationYear + fullStop;
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>" + fullStop;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity() + " ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher() + fullStop;
			}
			
			//set doi
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getDoi())) result += " Doi:"+selectedHeader_p.getDoi() + fullStop;
			//set url
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getFulltext_url()) && GenericValidator.isBlankOrNull(selectedHeader_p.getDoi())) {
				result += (selectedHeader_p.getFulltext_url().startsWith("http"))?" "+selectedHeader_p.getFulltext_url():"https://"+selectedHeader_p.getFulltext_url() + fullStop;
			}
			return result;
	}
	
	public String getAPAv7(OutputHeader_P selectedHeader_p) 
	{
		String result = "";
	    boolean withNonAscii = StringUtils.equals(selectedHeader_p.getLanguage(), "C");
	    String comma = (withNonAscii)?"，":", ";
	    String fullStop = (withNonAscii)?"。":". ";

	    	//set page num
		   	if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())) {
		   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from()+"-"+selectedHeader_p.getPage_num_to());
		   	}else if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())){
		   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from());
		   	}else {
		   		selectedHeader_p.setPage_num("");
		   	}
		   	
		   	//String publicationYear = (selectedHeader_p.getFrom_year() != null)?String.valueOf(selectedHeader_p.getFrom_year()):"n.d.";
		   	String publicationYear = (selectedHeader_p.getFrom_year() != null)?" ("+String.valueOf(selectedHeader_p.getFrom_year())+")"+fullStop+" ":"";
			
		   	String articleTitle = selectedHeader_p.getTitle_jour_book();
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getOutput_title_continue())) 
				articleTitle = articleTitle + " " + selectedHeader_p.getOutput_title_continue().trim();

			// A book, and other such as Film, video and Computer software or system
			if (selectedHeader_p.getSap_output_type().equals("120") || selectedHeader_p.getSap_output_type().equals("140") || selectedHeader_p.getSap_output_type().equals("150") 
				||  selectedHeader_p.getSap_output_type().equals("910") ||  selectedHeader_p.getSap_output_type().equals("920") || selectedHeader_p.getSap_output_type().equals("230") || selectedHeader_p.getSap_output_type().equals("270"))
			{
		          if(!selectedHeader_p.getSap_output_type().equals("150")) 
		          {
		            if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) 
		            	result += selectedHeader_p.getName_other_pos().trim();
		          }
		          else 
		          {       
		              if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) {
		                result += selectedHeader_p.getName_other_editors().trim();
		              }
		              else 
		              {
		                if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos()))
		                	result += selectedHeader_p.getName_other_pos().trim();
		              }
		          }
		          result += publicationYear;
		          if (!GenericValidator.isBlankOrNull(articleTitle)) 
		        	  result += " <i>"+articleTitle.trim() + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) 
		        	  result += selectedHeader_p.getCity().trim();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) 
		        	  result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) 
		        	  result += selectedHeader_p.getPublisher().trim() + fullStop;
		      }
			
		      //A chapter in a book
		      else if (selectedHeader_p.getSap_output_type().equals("130") || selectedHeader_p.getSap_output_type().equals("210") 
		          || selectedHeader_p.getSap_output_type().equals("220") || selectedHeader_p.getSap_output_type().equals("240")
		          || selectedHeader_p.getSap_output_type().equals("930") || selectedHeader_p.getSap_output_type().equals("350"))
		      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) 
		        	  result += selectedHeader_p.getName_other_pos().trim();
		          result += publicationYear;
		          if (!GenericValidator.isBlankOrNull(articleTitle)) 
		        	  result += " <i>"+articleTitle.trim() + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) 
		        	  result += selectedHeader_p.getName_other_editors().trim() + comma;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) 
		        	  result += " <i>" + selectedHeader_p.getTitle_paper_art().trim() + "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) 
		        	  result += " (" + selectedHeader_p.getPage_num() + ")";   
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art()) || !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity().trim();
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher().trim() + fullStop;
		      }
			
		      //An article in a journal
		      else if (selectedHeader_p.getSap_output_type().equals("160") || selectedHeader_p.getSap_output_type().equals("170") || selectedHeader_p.getSap_output_type().equals("420")
		          || selectedHeader_p.getSap_output_type().equals("940"))
		      {       
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos().trim();
		          result += publicationYear;
		          //Unitalicized the title of research output (for journal publications)
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " "+articleTitle.trim() + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art().trim() + "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getVol_issue())) result += comma + "<i>" + selectedHeader_p.getVol_issue().trim()+ "</i>";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += comma + selectedHeader_p.getPage_num().trim(); 
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getArticle_num())) result += comma + " Article " + selectedHeader_p.getArticle_num().trim(); 
		          result += fullStop;
		      }
				
		      //Conference paper
		      else if (selectedHeader_p.getSap_output_type().equals("180") || selectedHeader_p.getSap_output_type().equals("190") || selectedHeader_p.getSap_output_type().equals("200")
		          || selectedHeader_p.getSap_output_type().equals("950"))
		      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos().trim();
		          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year();
		          if (selectedHeader_p.getFrom_month() != null) {
		        	  if (withNonAscii) {
		        		  result += comma + selectedHeader_p.getFrom_month();
		        	  }else {
		        		  result += comma + getMonth(selectedHeader_p.getFrom_month());
		        	  } 
		          }
		          result += "). ";
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle.trim() + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result +=   selectedHeader_p.getTitle_paper_art().trim() ;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += comma + selectedHeader_p.getCity().trim() + fullStop;
		      }
			
		      //A report
		      else if (selectedHeader_p.getSap_output_type().equals("250") || selectedHeader_p.getSap_output_type().equals("260") 
		          || selectedHeader_p.getSap_output_type().equals("280") || selectedHeader_p.getSap_output_type().equals("290"))      {
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos().trim();
		          result += publicationYear;
		          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle.trim() + "</i>" + fullStop;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity().trim() ;
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
		          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher().trim() + fullStop;
		      }
				
			// Other type
			else if(selectedHeader_p.getSap_output_type().equals("340")) 
			{
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors().trim();
	          result += publicationYear;
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle.trim() + "</i>" + fullStop;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity().trim() ;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher().trim()+ fullStop;
			}

			// Other type
			else
			{
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          result += publicationYear;
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle.trim() + "</i>" + fullStop;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity().trim() ;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher().trim() + fullStop;
			}
			
			//set doi
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getDoi())) {
				String doi = selectedHeader_p.getDoi().trim();
				if (doi.startsWith("http")) {
					result += " "+doi;

				}else {
					if (doi.contains("doi.org")) {
						result += " https://"+doi;
					}else if (doi.contains("doi:") || doi.contains("Doi:")) {
						doi = doi.replaceFirst("doi:", "");
						doi = doi.replaceFirst("Doi:", "");
						result += " https://doi.org/"+doi;
					}else {
						result += " https://doi.org/"+doi;
					}
				}
			}
			//set url
			if (!GenericValidator.isBlankOrNull(selectedHeader_p.getFulltext_url()) && GenericValidator.isBlankOrNull(selectedHeader_p.getDoi())) {
				result += (selectedHeader_p.getFulltext_url().startsWith("http"))?" "+selectedHeader_p.getFulltext_url():" https://"+selectedHeader_p.getFulltext_url();
			}
			
			//Remove italic in Chinese publications
			if (withNonAscii) {
				result = result.replaceAll("<i>", "");
				result = result.replaceAll("</i>", "");
			}
		return result;
	}

	public String getAPA_old(OutputHeader_P selectedHeader_p) 
	{
		String result = "";
	    boolean withNonAscii = StringUtils.equals(selectedHeader_p.getLanguage(), "C");
	    

			    // retrieve apa version for edited book
			   	String apaEditedBook = null;
			   	
			   	//set page num
			   	if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())) {
			   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from()+"-"+selectedHeader_p.getPage_num_to());
			   	}else if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_from()) && GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num_to())){
			   		selectedHeader_p.setPage_num(selectedHeader_p.getPage_num_from());
			   	}else {
			   		selectedHeader_p.setPage_num("");
			   	}
			    if(selectedHeader_p.getSap_output_type() != null && 
			      (selectedHeader_p.getSap_output_type().equals("150") || 
			       selectedHeader_p.getSap_output_type().equals("340")) 
			    ) {
			        apaEditedBook = sDao.getSysParamValueByCode("APA_EDITED_BOOK");
			    }        
			    if(apaEditedBook == null) apaEditedBook = "V1";
	    	
				String articleTitle = selectedHeader_p.getTitle_jour_book();
				if (!GenericValidator.isBlankOrNull(selectedHeader_p.getOutput_title_continue())) 
					articleTitle = articleTitle + " " + selectedHeader_p.getOutput_title_continue();

				// A book, and other such as Film, video and Computer software or system
				if (selectedHeader_p.getSap_output_type().equals("120") || selectedHeader_p.getSap_output_type().equals("140") || selectedHeader_p.getSap_output_type().equals("150") 
	        ||  selectedHeader_p.getSap_output_type().equals("910") ||  selectedHeader_p.getSap_output_type().equals("920") || selectedHeader_p.getSap_output_type().equals("230") || selectedHeader_p.getSap_output_type().equals("270"))
				{
	        if(withNonAscii) 
	        {
	          if(!selectedHeader_p.getSap_output_type().equals("150")) 
	          {
	            if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          }
	          else 
	          {       
	            if("V1".equals(apaEditedBook)) {
	               if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	            }
	            else if ("V2".equals(apaEditedBook)){
	              if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) {
	                result += selectedHeader_p.getName_other_editors();
	              }
	              else 
	              {
	                if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	              }
	            }
	          }
	          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year() + "）";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "：" + articleTitle;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) || !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
	          result += "。";       
	        }
	        else 
	        {
	          if(!selectedHeader_p.getSap_output_type().equals("150")) 
	          {
	            if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          }
	          else 
	          {       
	            if("V1".equals(apaEditedBook)) {
	               if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	            }
	            else if ("V2".equals(apaEditedBook)){
	              if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) {
	                result += selectedHeader_p.getName_other_editors();
	              }
	              else 
	              {
	                if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	              }
	            }
	          }
	          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year() + "). ";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "<i>" + articleTitle + "</i>. ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ": ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
	          //if(sb.toString().trim().length() >= 1 && sb.charAt(sb.toString().trim().length()-1)!='.') result += ".");
	        } // end  if(getLanguage().equals("C"))
	      }
	      //A chapter in a book
	      else if (selectedHeader_p.getSap_output_type().equals("130") || selectedHeader_p.getSap_output_type().equals("210") 
	          || selectedHeader_p.getSap_output_type().equals("220") || selectedHeader_p.getSap_output_type().equals("240")
	          || selectedHeader_p.getSap_output_type().equals("930"))
	      {
	        if(withNonAscii) 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year() + "）";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "：" + articleTitle + "，";          
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors() + "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += selectedHeader_p.getTitle_paper_art() + "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += "（" + selectedHeader_p.getPage_num() + "），";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
	          result += "。"; 
	        }
	        else 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year() + "). ";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += articleTitle + ". ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors() + ", ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art() + "</i>";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += " (" + selectedHeader_p.getPage_num() + ")";   
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art()) || !GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += "。";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "). ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
	          //if(sb.toString().trim().length() >= 1 && sb.charAt(sb.toString().trim().length()-1)!='.') result += ".");
	        } // end  if(getLanguage().equals("C"))
	      }
			
	      //An article in a journal
	      else if (selectedHeader_p.getSap_output_type().equals("160") || selectedHeader_p.getSap_output_type().equals("170") || selectedHeader_p.getSap_output_type().equals("420")
	          || selectedHeader_p.getSap_output_type().equals("940"))
	      {       
	        if(withNonAscii) 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year() + "）";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "：" + articleTitle;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += "，《" + selectedHeader_p.getTitle_paper_art() + "》";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getVol_issue())) result += "，" + selectedHeader_p.getVol_issue();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += "，" + selectedHeader_p.getPage_num();  
	          result += "。";        
	        }
	        else 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year() + "). ";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += articleTitle + ". ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += " <i>" + selectedHeader_p.getTitle_paper_art() + "</i>";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getVol_issue())) result += "<i>, " + selectedHeader_p.getVol_issue()+ "</i>";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPage_num())) result += ", " + selectedHeader_p.getPage_num();  
	          //if(sb.toString().trim().length() >= 1 && sb.charAt(sb.toString().trim().length()-1)!='.') result += ".";
	        } // end  if(getLanguage().equals("C"))
	      }
	      //Conference paper
	      else if (selectedHeader_p.getSap_output_type().equals("180") || selectedHeader_p.getSap_output_type().equals("190") || selectedHeader_p.getSap_output_type().equals("200")
	          || selectedHeader_p.getSap_output_type().equals("950"))
	      {
	        if(withNonAscii) 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year();
	          if (selectedHeader_p.getFrom_month() != null) result += "，" + selectedHeader_p.getFrom_month();
	          result += "）：";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += articleTitle + "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result += selectedHeader_p.getTitle_paper_art() + "，";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();

	          result += "。";         
	        }
	        else 
	        { 
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year();
	          if (selectedHeader_p.getFrom_month() != null) result += ", " + selectedHeader_p.getFrom_month();
	          result += "). ";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>" + articleTitle + "</i>. ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getTitle_paper_art())) result +=   selectedHeader_p.getTitle_paper_art() ;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += ", " + selectedHeader_p.getCity();
	        }
	      }
	      //A report
	      else if (selectedHeader_p.getSap_output_type().equals("250") || selectedHeader_p.getSap_output_type().equals("260") 
	          || selectedHeader_p.getSap_output_type().equals("280") || selectedHeader_p.getSap_output_type().equals("290"))      {
	        if(withNonAscii) 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year() + "）";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "：" + articleTitle;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += "，" + selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "，" + selectedHeader_p.getPublisher();  
	          result += "。";        
	        }
	        else 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
	          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year() + "). ";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>. ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "). ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
	          //if(sb.toString().trim().length() >= 1 && sb.charAt(sb.toString().trim().length()-1)!='.') result += ".");
	        }
	      }
				// Other type
				else if(selectedHeader_p.getSap_output_type().equals("340")) 
				{
	        if(withNonAscii) 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors();
	          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year() + "）";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "：" + articleTitle;
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "，" + selectedHeader_p.getPublisher();  
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += "，" + selectedHeader_p.getCity();
	          result += "。";        
	        }
	        else 
	        {
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_editors())) result += selectedHeader_p.getName_other_editors();
	          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year() + "). ";
	          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i> ";
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += ". " + selectedHeader_p.getPublisher();
	          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += ". " + selectedHeader_p.getCity();
	          //if(sb.toString().trim().length() >= 1 && sb.charAt(sb.toString().trim().length()-1)!='.') result += ".");
	        }
				}

				// Other type
				else
				{
			        if(withNonAscii) 
			        {
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
			          if (selectedHeader_p.getFrom_year() != null) result += "（" + selectedHeader_p.getFrom_year() + "）";
			          if (!GenericValidator.isBlankOrNull(articleTitle)) result += "：" + articleTitle;
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += "，" + selectedHeader_p.getCity();
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "，" + selectedHeader_p.getPublisher();  
			          result += "。";        
			        }
			        else 
			        {
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getName_other_pos())) result += selectedHeader_p.getName_other_pos();
			          if (selectedHeader_p.getFrom_year() != null) result += " (" + selectedHeader_p.getFrom_year() + "). ";
			          if (!GenericValidator.isBlankOrNull(articleTitle)) result += " <i>"+articleTitle + "</i>. ";
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity())) result += selectedHeader_p.getCity();
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getCity()) && !GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += "). ";
			          if (!GenericValidator.isBlankOrNull(selectedHeader_p.getPublisher())) result += selectedHeader_p.getPublisher();
			          //if(sb.toString().trim().length() >= 1 && sb.charAt(sb.toString().trim().length()-1)!='.') result += ".");
			        }
				}

	    return result;
	}
	

	
	public List<String> getRiAdminDeptList()
	{
		if (riAdminDeptList == null) {
			riAdminDeptList = staffDao.getDataCodeByUserId(getCurrentUserId(), "D");			
			if (riAdminDeptList == null) {
				riAdminDeptList = new ArrayList<String>();
			}else {
				if(riAdminDeptList.contains("ALL_DATA")) {
					riAdminDeptList = new ArrayList<String>();
					LookupValueDAO dao = LookupValueDAO.getCacheInstance();
					List<LookupValue> list = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
					list.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y"));
					list.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L3", "US", "Y"));
					for(int i = 0; i < list.size(); i++) {
						riAdminDeptList.add(list.get(i).getPk().getLookup_code());
					}
				}
			}
		}
		return riAdminDeptList;
	}

	
	public void setRiAdminDeptList(List<String> riAdminDeptList)
	{
		this.riAdminDeptList = riAdminDeptList;
	}

	public List<String> getKtAdminDeptList()
	{
		if (ktAdminDeptList == null) {
			ktAdminDeptList = staffDao.getDataCodeByUserId(getCurrentUserId(), "KTD");			
			if (ktAdminDeptList == null) {
				ktAdminDeptList = new ArrayList<String>();
			}else {
				if(ktAdminDeptList.contains("ALL_DATA")) {
					ktAdminDeptList = new ArrayList<String>();
					LookupValueDAO dao = LookupValueDAO.getCacheInstance();
					List<LookupValue> ktdList = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
					ktdList.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y"));
					for(int i = 0; i < ktdList.size(); i++) {
						ktAdminDeptList.add(ktdList.get(i).getPk().getLookup_code());
					}
				}
			}
		}
		return ktAdminDeptList;
	}


	
	public void setKtAdminDeptList(List<String> ktAdminDeptList)
	{
		this.ktAdminDeptList = ktAdminDeptList;
	}

	
	
	public List<String> getRptAdminDeptList()
	{
		if (rptAdminDeptList == null) {
			rptAdminDeptList = staffDao.getDataCodeByUserId(getCurrentUserId(), "RPT");			
			if (rptAdminDeptList == null) {
				rptAdminDeptList = new ArrayList<String>();
			}else {
				if(rptAdminDeptList.contains("ALL_DATA")) {
					rptAdminDeptList = new ArrayList<String>();
					LookupValueDAO dao = LookupValueDAO.getCacheInstance();
					List<LookupValue> ktdList = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
					ktdList.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y"));
					for(int i = 0; i < ktdList.size(); i++) {
						rptAdminDeptList.add(ktdList.get(i).getPk().getLookup_code());
					}
				}
			}
		}
		return rptAdminDeptList;
	}


	
	public void setRptAdminDeptList(List<String> rptAdminDeptList)
	{
		this.rptAdminDeptList = rptAdminDeptList;
	}


	public String getFullMonth(int month, Locale locale) {
	    return new DateFormatSymbols(locale).getShortMonths()[month-1];
	}
	
	public String getShortMonth(int month, Locale locale) {
	    return new DateFormatSymbols(locale).getMonths()[month-1];
	}
	
	public boolean isStringInt(String s)
	{
	    try
	    {
	        Integer.parseInt(s);
	        return true;
	    } catch (NumberFormatException ex)
	    {
	        return false;
	    }
	}
	
	public String resetLookUpCode(String value)
	{
		value = value.replace("1000", "");
		return value;
	}
	
	public String getPastStaffNumByStaffName(String authorshipName) 
	{
		String staffNum = "";
		authorshipName = authorshipName.toUpperCase();
		authorshipName = authorshipName.replace(",", "");
		boolean hasChinese = containsHanScript(authorshipName);
		StaffPast sp = staffDao.getPastStaffDetailsByStaffName(authorshipName, hasChinese);
		if (sp != null) {
			staffNum = sp.getStaff_number();
		}
		return staffNum;
	}
	
	public List<String> fsCompleteText(String query) 
	{
        String queryLowerCase = query.toLowerCase();
        List<String> fsList = new ArrayList<>();
        if (getStaffPastList() != null) {
        	for (StaffPast fs : staffPastList) {
        		fsList.add(fs.getFullname_display());
            }
        }
        return fsList.stream().filter(t -> t.toLowerCase().startsWith(queryLowerCase)).collect(Collectors.toList());
    } 
	
	private String capitalizeFully(String str) 
	{
	    StringBuilder sb = new StringBuilder();
	    boolean cnl = true; // <-- capitalize next letter.
	    for (char c : str.toCharArray()) {
	        if (cnl && Character.isLetter(c)) {
	            sb.append(Character.toUpperCase(c));
	            cnl = false;
	        } else {
	            sb.append(Character.toLowerCase(c));
	        }
	        if (Character.isWhitespace(c)) {
	            cnl = true;
	        }
	    }
	    return sb.toString();
	}
	
	public Date riStringToDate(Integer day, Integer month, Integer year, boolean start, Date startDate) throws ParseException
	{
		String dateStr = (day != null)?day+"/"+month+"/"+year:"01/"+month+"/"+year;
		Date date = new SimpleDateFormat("dd/MM/yyyy").parse("01/01/1900"); 
		if (month != null && year != null) {
			date = new SimpleDateFormat("dd/MM/yyyy").parse(dateStr); 
		}
		if (start == false) {
			if (month != null && year != null) {
				if (day == null) {
					date = (getLastDayOfMonth(date));
				}
			}else {
				date = startDate;
			}
			
		}
		return date;
	}
	
	public Date getLastDayOfMonth(Date date)
	{
		Calendar calendar = Calendar.getInstance();  
        calendar.setTime(date);  

        calendar.add(Calendar.MONTH, 1);  
        calendar.set(Calendar.DAY_OF_MONTH, 1);  
        calendar.add(Calendar.DATE, -1); 
        Date lastDayOfMonth = calendar.getTime();  
        return lastDayOfMonth;
	}
	
	public String dateToStringWithFormat(Date date, String format)
	{
		String dateString = "";
		String pattern = format;
		DateFormat df = new SimpleDateFormat(pattern, Locale.ENGLISH);
		if (date != null) {
			dateString = df.format(date);
		}
		return dateString;
	}
	
	public String dateToString(Date date)
	{
		String dateString = "";
		String pattern = "dd/MM/yyyy";
		DateFormat df = new SimpleDateFormat(pattern);
		if (date != null) {
			dateString = df.format(date);
		}
		return dateString;
	}
	
	public Date stringToDate(String dateStr) throws ParseException
	{
		Date date = null;
		if (!Strings.isNullOrEmpty(dateStr)) {
			date = new SimpleDateFormat("dd/MM/yyyy").parse(dateStr); 
		}
		return date;
	}
	
	public String DateToMonthYearFormat(Date date) throws ParseException
	{
		String dateString = "";
		String pattern = "MM/yyyy";
		DateFormat df = new SimpleDateFormat(pattern);
		if (date != null) {
			dateString = df.format(date);
		}
		return dateString;
	}
	
	protected boolean containsHanScript(String s) 
	{
	    for (int i = 0; i < s.length(); ) {
	        int codepoint = s.codePointAt(i);
	        i += Character.charCount(codepoint);
	        if (Character.UnicodeScript.of(codepoint) == Character.UnicodeScript.HAN) {
	            return true;
	        }
	    }
	    return false;
	}
	
	public String str(int i) {
	    return i < 0 ? "" : str((i / 26) - 1) + (char)(65 + i % 26);
	}
	
	public static final String escapeInvalidXmlCharacters(String string) {
	    if (string != null) {
		    StringBuilder stringBuilder = new StringBuilder();
	
		    for (int i = 0, codePoint = 0; i < string.length(); i += Character.charCount(codePoint)) {
		        codePoint = string.codePointAt(i);
	
		        if (codePoint == '#') {
		            stringBuilder.append("##");
		        } else if (codePoint == 0x9 || codePoint == 0xA || codePoint == 0xD || codePoint >= 0x20 && codePoint <= 0xD7FF || codePoint >= 0xE000 && codePoint <= 0xFFFD || codePoint >= 0x10000 && codePoint <= 0x10FFFF) {
		            stringBuilder.appendCodePoint(codePoint);
		        } else {
		            stringBuilder.append("#" + codePoint + ";");
		        }
		    }
		    
		    return stringBuilder.toString();
	    }
		return string;
	}
	
	// check if a string is a valid url or not, url must contains http:// or https://
   public static boolean isValidUrl(String inStr) {
	   String[] customSchemes = { "http", "https" }; 
	   UrlValidator urlValidator = new UrlValidator(customSchemes, UrlValidator.ALLOW_2_SLASHES);
	   return urlValidator.isValid(inStr);
   }
}