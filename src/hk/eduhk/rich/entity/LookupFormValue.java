package hk.eduhk.rich.entity;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_Z_LOOKUP_FORM_VALUES")
public class LookupFormValue implements Serializable
{
	public static Logger logger = Logger.getLogger(LookupFormValue.class.toString());
	
	@EmbeddedId
	private LookupFormValuePK pk = new LookupFormValuePK();
	
	@Column(name = "column_name")
	private String columnName;

	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "form_code", referencedColumnName = "form_code", nullable = false, insertable = false, updatable = false)
	private KtForm form;
	
	public LookupFormValuePK getPk()
	{
		return pk;
	}

	
	public void setPk(LookupFormValuePK pk)
	{
		this.pk = pk;
	}

	public String getFormCode()
	{
		return getPk().getFormCode();
	}

	
	
	
	public String getLookupType()
	{
		return getPk().getLookupType();
	}

	
	public String getColumnName()
	{
		return columnName;
	}

	
	public void setColumnName(String columnName)
	{
		this.columnName = columnName;
	}

	public KtForm getForm()
	{
		if(form != null) 
		{
			try 
			{
				form.getForm_code();
			}
			catch(RuntimeException re) 
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					form = FormDAO.getCacheInstance().getKtForm(getPk().getFormCode());
				}
				else
				{
					throw re;
				}
			}
		}
		
		if(form==null)
		{
			form = FormDAO.getCacheInstance().getKtForm(getPk().getFormCode());
		}
		return form;
	}


	
	public void setForm(KtForm form)
	{
		this.form = form;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupFormValue other = (LookupFormValue) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupFormValue [pk=" + pk + ", columnName=" + columnName + "]";
	}
	
	
}
