package hk.eduhk.rich.entity;

import java.util.logging.Logger;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;
import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_Q_PK;


@Entity
@Table(name = "RH_S_FND_LOOKUP_VALUES")
public class LookupFNDValue extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(LookupFNDValue.class.toString());
	
	@EmbeddedId
	private LookupValue_PK pk = new LookupValue_PK();

	@Column(name = "description")
	private String description;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;	
	
	@Column(name = "attribute1")
	private String attribute1;
	
	@Column(name = "attribute2")
	private String attribute2;
	
	@Column(name = "attribute3")
	private String attribute3;
	
	@Column(name = "attribute4")
	private String attribute4;
	
	@Column(name = "attribute5")
	private String attribute5;
	
	@Transient
	public SecFuncLock exKt;
	
	public LookupValue_PK getPk()
	{
		return pk;
	}

	
	public void setPk(LookupValue_PK pk)
	{
		this.pk = pk;
	}

	


	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}
	
	
	public String getAttribute1()
	{
		return attribute1;
	}


	
	public void setAttribute1(String attribute1)
	{
		this.attribute1 = attribute1;
	}


	
	public String getAttribute2()
	{
		return attribute2;
	}


	
	public void setAttribute2(String attribute2)
	{
		this.attribute2 = attribute2;
	}
	
	
	
	
	public String getAttribute3()
	{
		return attribute3;
	}


	
	public void setAttribute3(String attribute3)
	{
		this.attribute3 = attribute3;
	}


	
	public String getAttribute4()
	{
		return attribute4;
	}


	
	public void setAttribute4(String attribute4)
	{
		this.attribute4 = attribute4;
	}


	
	public String getAttribute5()
	{
		return attribute5;
	}


	
	public void setAttribute5(String attribute5)
	{
		this.attribute5 = attribute5;
	}


	public SecFuncLock getExKt()
	{
		if (!Strings.isNullOrEmpty(getPk().getLookup_code())) {
			AccessDAO dao = AccessDAO.getInstance();
			exKt = dao.getSecFuncLock("EXCLUSIVE_KT", "MANAGE_KT_ACT", getPk().getLookup_code());
		}
		return exKt;
	}


	
	public void setExKt(SecFuncLock exKt)
	{
		this.exKt = exKt;
	}


	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupFNDValue other = (LookupFNDValue) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupFNDValue [pk=" + pk + ", description=" + description + ", enabled_flag=" + enabled_flag
				+ ", attribute1=" + attribute1 + ", attribute2=" + attribute2 + ", attribute3=" + attribute3
				+ ", attribute4=" + attribute4 + ", attribute5=" + attribute5 + ", exKt=" + exKt + "]";
	}
}
