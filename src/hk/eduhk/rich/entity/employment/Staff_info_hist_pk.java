package hk.eduhk.rich.entity.employment;


import javax.persistence.*;

import java.io.Serializable;


@Embeddable
public class Staff_info_hist_pk implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "pid")
	private Integer pid;
	
	@Column(name = "job_seq")
	private Integer job_seq;

	
	public Integer getPid()
	{
		return pid;
	}

	
	public void setPid(Integer pid)
	{
		this.pid = pid;
	}

	
	public Integer getJob_seq()
	{
		return job_seq;
	}

	
	public void setJob_seq(Integer job_seq)
	{
		this.job_seq = job_seq;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((job_seq == null) ? 0 : job_seq.hashCode());
		result = prime * result + ((pid == null) ? 0 : pid.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Staff_info_hist_pk other = (Staff_info_hist_pk) obj;
		if (job_seq == null)
		{
			if (other.job_seq != null)
				return false;
		}
		else if (!job_seq.equals(other.job_seq))
			return false;
		if (pid == null)
		{
			if (other.pid != null)
				return false;
		}
		else if (!pid.equals(other.pid))
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "Staff_info_hist_pk [pid=" + pid + ", job_seq=" + job_seq + "]";
	}
	

}
