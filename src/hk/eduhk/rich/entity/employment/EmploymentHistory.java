package hk.eduhk.rich.entity.employment;

import java.util.logging.Logger;

import javax.persistence.*;

import java.util.Calendar;
import java.util.Date;
import javax.persistence.Table;

import org.apache.commons.lang3.time.DateUtils;


@Entity
@Table(name = "RH_P_STAFF_EMPLOYMENT_HIST")
@SuppressWarnings("serial")
public class EmploymentHistory
{
	public static Logger logger = Logger.getLogger(EmploymentHistory.class.toString());
	
	@Id
	@Column(name = "hist_id")
	private int hist_id;
	
	@Column(name = "pid")
	private int pid;
	
	@Column(name = "staff_no")
	private String staff_no;
	
	@Column(name = "rank_code")
	private String rank_code;

	@Column(name = "rank_full")
	private String rank_full;

	@Column(name = "active_emp")
	private char active_emp;

	@Column(name = "eff_from")
	private Date eff_from;
	
	@Column(name = "eff_to")
	private Date eff_to;

	

	
	public int getHist_id()
	{
		return hist_id;
	}

	
	public void setHist_id(int hist_id)
	{
		this.hist_id = hist_id;
	}

	public int getPid()
	{
		return pid;
	}

	public void setPid(int pid)
	{
		this.pid = pid;
	}
	
	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}
	
	public String getRank_code()
	{
		return rank_code;
	}

	
	public void setRank_code(String rank_code)
	{
		this.rank_code = rank_code;
	}
	
	
	public String getRank_full()
	{
		return rank_full;
	}
	
	public void setRank_full(String rank_full)
	{
		this.rank_full = rank_full;
	}
	
	
	public char getActive_emp()
	{
		return active_emp;
	}

	
	public void setActive_emp(char active_emp)
	{
		this.active_emp = active_emp;
	}
	
	public Date getEff_from()
	{
		return eff_from;
	}

	public void setEff_from(Date eff_from)
	{
		this.eff_from = (eff_from != null) ? DateUtils.truncate(eff_from, Calendar.SECOND) : null;
	}
	
	public Date getEff_to()
	{
		return eff_to;
	}

	public void setEff_to(Date eff_to)
	{
		this.eff_to = (eff_to != null) ? DateUtils.truncate(eff_to, Calendar.SECOND) : null;;
	}
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + hist_id;
		return result;
	}

	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmploymentHistory other = (EmploymentHistory) obj;
		if (hist_id != other.hist_id)
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "EmploymentHistory [hist_id=" + hist_id + ", pid=" + pid + ", staff_no=" + staff_no + ", rank_code="
				+ rank_code + ", rank_full=" + rank_full + ", active_emp=" + active_emp + ", eff_from=" + eff_from
				+ ", eff_to=" + eff_to + "]";
	}
}
