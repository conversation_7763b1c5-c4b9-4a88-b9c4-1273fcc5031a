package hk.eduhk.rich.entity.employment;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.employment.EmploymentHistory;
import hk.eduhk.rich.entity.staff.PureDept;

public class employmentHistoryDao extends BaseDAO
{
	private static employmentHistoryDao instance = null;

	public static synchronized employmentHistoryDao getInstance()
	{
		if (instance == null) instance = new employmentHistoryDao();
		return instance;
	}
	
	public static employmentHistoryDao getCacheInstance()
	{
		return employmentHistoryDao.getInstance();
	}
	
	
	public List<EmploymentHistory> getEmploymentHistory (int pid)
	{
		List<EmploymentHistory> objList = null;
		EntityManager em = null;
		
		try
		{
			em = getEntityManager();
			String query = "SELECT a FROM EmploymentHistory a where a.pid = :pid order by a.eff_from desc";
			TypedQuery<EmploymentHistory> q = em.createQuery(query, EmploymentHistory.class);
			q.setParameter("pid", pid);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}	
		return objList;
	}
	
	
	public boolean UpdateEmploymentHistory (EmploymentHistory_edit selectedEmptHist)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (selectedEmptHist != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				selectedEmptHist = em.merge(selectedEmptHist);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return true;
	}
	
	public void deleteEmploymentHistory(Staff_info_hist_pk pk_code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (pk_code != null) {
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				EmploymentHistory_edit obj = em.find(EmploymentHistory_edit.class, pk_code);
				em.remove(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}	
	
	
	public List<EmploymentHistory_edit> getEmploymentHistory_edit(int pid)
	{
		List<EmploymentHistory_edit> objList = null;
		EntityManager em = null;
		
		try
		{
			em = getEntityManager();
			String query = "SELECT a FROM EmploymentHistory_edit a where a.pk.pid = :pid order by a.from_date desc";
			TypedQuery<EmploymentHistory_edit> q = em.createQuery(query, EmploymentHistory_edit.class);
			q.setParameter("pid", pid);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}	
		return objList;
	}
	
	
	public EmploymentHistory_edit getSelectedEmploymentHistory (Staff_info_hist_pk pk)
	{
		EmploymentHistory_edit obj = null;
		EntityManager em = null;
		try
		{
			em = getEntityManager();
			obj = em.find(EmploymentHistory_edit.class, pk);
		}
		finally
		{
			pm.close(em);
		}

		return obj;	
	}
	

	

}
