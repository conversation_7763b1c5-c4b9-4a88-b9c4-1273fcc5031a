package hk.eduhk.rich.entity.employment;

import java.util.logging.Logger;

import javax.persistence.*;

import java.util.Date;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.UserPersistenceObject;


@Entity
@Table(name = "RH_P_STAFF_INFO_HIST")
@SuppressWarnings("serial")
public class EmploymentHistory_edit extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(EmploymentHistory_edit.class.toString());
	
	@EmbeddedId
	private Staff_info_hist_pk pk = new Staff_info_hist_pk();
	
	@Column(name = "company")
	private String company;

	@Column(name = "job_title")
	private String job_title;

	@Column(name = "job_details")
	private String job_details;

	
	@Column(name = "from_date")
	private Date from_date;
	

	@Column(name = "to_date")
	private Date to_date;
	
	@Column(name = "is_current")
	private char is_current;
	
	
	
	public String getCompany()
	{
		return company;
	}


	
	public void setCompany(String company)
	{
		this.company = company;
	}

	
	public String getJob_details()
	{
		return job_details;
	}


	
	public void setJob_details(String job_details)
	{
		this.job_details = job_details;
	}


	
	public Date getFrom_date()
	{
		return from_date;
	}


	
	public void setFrom_date(Date from_date)
	{
		this.from_date = from_date;
	}


	
	public Date getTo_date()
	{
		return to_date;
	}

	public void setTo_date(Date to_date)
	{
		this.to_date = to_date;
	}
	
	public char getIs_current()
	{
		return is_current;
	}
	
	public void setIs_current(char is_current)
	{
		this.is_current = is_current;
	}
		
	public String getJob_title()
	{
		return job_title;
	}
	
	
	public Staff_info_hist_pk getPk()
	{
		return pk;
	}
	
	public void setPk(Staff_info_hist_pk pk)
	{
		this.pk = pk;
	}


	public void setJob_title(String job_title)
	{
		this.job_title = job_title;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmploymentHistory_edit other = (EmploymentHistory_edit) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}
	
	@Override
	public String toString()
	{
		return "EmploymentHistory_edit [pk=" + pk + ", company=" + company + ", job_title=" + job_title
				+ ", job_details=" + job_details + ", from_date=" + from_date + ", to_date=" + to_date + ", is_current="
				+ is_current + "]";
	}



}
