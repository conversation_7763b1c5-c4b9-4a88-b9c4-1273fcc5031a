package hk.eduhk.rich.entity;

import java.util.List;

import hk.eduhk.rich.UserPersistenceObject;

import java.sql.Timestamp;

/**
 * BaseRI is an abstract class which contains all status fields in RI status
 * table (e.g. RI_Q_ACAD_PROF_OUTPUT)
 */
@SuppressWarnings("serial")
public class BaseRI extends UserPersistenceObject
{
	private int pid;
	private String staffNumber;
	private int creatorPid;
	private String creatorName;

	protected int riNo;
	private String publishStatus;

	private String lastModifiedBy;
	private Timestamp lastModifiedDate;
	private String lastPublishedBy;
	private Timestamp lastPublishedDate;

	private String instDisplayInd;
	private String instVerifiedInd;
	private Timestamp instVerifiedDate;

	private String CDCFStatus;
	private String CDCFGenInd;
	private Timestamp CDCFGenDate;
	private String CDCFProcessInd;
	private Timestamp CDCFProcessDate;
	private String CDCFSelectedInd;
	private String CDCFChangedInd;
	private String bulletinInd;
	private String remarks;
	private int publishFreq;

	private String collabT630;
	private String collabT690;

	private List<String> riDetailList;
	private List<String> riAttachmentList;

	private String concatNames;

	private String copyright;
	private String raeRIStatus;
	private String raeRIStatusDesc;
	private int staffSelected;
	private String staffSubmitted;
	private Timestamp staffSubmitDate;
	private String headApproval;
	private Timestamp headApprovalDate;
	private String deanApproval;
	private Timestamp deanApprovalDate;
	private int relatedRI;
	private String raeInternalRating;
	private String markDelete;
	private String isIntlConf;


	public void setPid(int pid)
	{
		this.pid = pid;
	}


	public int getPid()
	{
		return this.pid;
	}


	public void setCreatorPid(int creatorPid)
	{
		this.creatorPid = creatorPid;
	}


	public int getCreatorPid()
	{
		return this.creatorPid;
	}


	public void setStaffNumber(String staffNumber)
	{
		this.staffNumber = staffNumber;
	}


	public String getStaffNumber()
	{
		return this.staffNumber;
	}


	public void setRINo(int riNo)
	{
		this.riNo = riNo;
	}


	public int getRINo()
	{
		return this.riNo;
	}


	public void setPublishStatus(String publishStatus)
	{
		this.publishStatus = publishStatus;
	}


	public String getPublishStatus()
	{
		return publishStatus;
	}


	public void setLastModifiedBy(String lastModifiedBy)
	{
		this.lastModifiedBy = lastModifiedBy;
	}


	public String getLastModifiedBy()
	{
		return lastModifiedBy;
	}


	public void setLastModifiedDate(Timestamp lastModifiedDate)
	{
		this.lastModifiedDate = lastModifiedDate;
	}


	public Timestamp getLastModifiedDate()
	{
		return lastModifiedDate;
	}


	public void setLastPublishedBy(String lastPublishedBy)
	{
		this.lastPublishedBy = lastPublishedBy;
	}


	public String getLastPublishedBy()
	{
		return lastPublishedBy;
	}


	public void setLastPublishedDate(Timestamp lastPublishedDate)
	{
		this.lastPublishedDate = lastPublishedDate;
	}


	public Timestamp getLastPublishedDate()
	{
		return lastPublishedDate;
	}


	public void setInstDisplayInd(String instDisplayInd)
	{
		this.instDisplayInd = instDisplayInd;
	}


	public String getInstDisplayInd()
	{
		return instDisplayInd;
	}


	public void setInstVerifiedInd(String instVerifiedInd)
	{
		this.instVerifiedInd = instVerifiedInd;
	}


	public String getInstVerifiedInd()
	{
		return instVerifiedInd;
	}


	public void setInstVerifiedDate(Timestamp instVerifiedDate)
	{
		this.instVerifiedDate = instVerifiedDate;
	}


	public Timestamp getInstVerifiedDate()
	{
		return instVerifiedDate;
	}


	public void setCDCFStatus(String CDCFStatus)
	{
		this.CDCFStatus = CDCFStatus;
	}


	public String getCDCFStatus()
	{
		return CDCFStatus;
	}


	public void setCDCFGenInd(String CDCFGenInd)
	{
		this.CDCFGenInd = CDCFGenInd;
	}


	public String getCDCFGenInd()
	{
		return CDCFGenInd;
	}


	public void setCDCFGenDate(Timestamp CDCFGenDate)
	{
		this.CDCFGenDate = CDCFGenDate;
	}


	public Timestamp getCDCFGenDate()
	{
		return CDCFGenDate;
	}


	public void setCDCFProcessInd(String CDCFProcessInd)
	{
		this.CDCFProcessInd = CDCFProcessInd;
	}


	public String getCDCFProcessInd()
	{
		return CDCFProcessInd;
	}


	public void setCDCFProcessDate(Timestamp CDCFProcessDate)
	{
		this.CDCFProcessDate = CDCFProcessDate;
	}


	public Timestamp getCDCFProcessDate()
	{
		return CDCFProcessDate;
	}


	public void setCDCFSelectedInd(String CDCFSelectedInd)
	{
		this.CDCFSelectedInd = CDCFSelectedInd;
	}


	public String getCDCFSelectedInd()
	{
		return CDCFSelectedInd;
	}


	public void setCDCFChangedInd(String CDCFChangedInd)
	{
		this.CDCFChangedInd = CDCFChangedInd;
	}


	public String getCDCFChangedInd()
	{
		return CDCFChangedInd;
	}


	public void setBulletinInd(String bulletinInd)
	{
		this.bulletinInd = bulletinInd;
	}


	public String getBulletinInd()
	{
		return bulletinInd;
	}


	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	public String getRemarks()
	{
		return remarks;
	}


	public void setPublishFreq(int publishFreq)
	{
		this.publishFreq = publishFreq;
	}


	public int getPublishFreq()
	{
		return publishFreq;
	}


	public void setConcatNames(String concatNames)
	{
		this.concatNames = concatNames;
	}


	public String getConcatNames()
	{
		return this.concatNames;
	}


	public void setRIDetailList(List<String> riDetailList)
	{
		this.riDetailList = riDetailList;
	}


	public List<String> getRIDetailList()
	{
		return riDetailList;
	}


	public void setRIAttachmentList(List<String> riAttachmentList)
	{
		this.riAttachmentList = riAttachmentList;
	}


	public List<String> getRIAttachmentList()
	{
		return riAttachmentList;
	}


	public void setCreatorName(String creatorName)
	{
		this.creatorName = creatorName;
	}


	public String getCreatorName()
	{
		return creatorName;
	}


	public void setCollabT630(String collabT630)
	{
		this.collabT630 = collabT630;
	}


	public String getCollabT630()
	{
		return collabT630;
	}


	public void setCollabT690(String collabT690)
	{
		this.collabT690 = collabT690;
	}


	public String getCollabT690()
	{
		return collabT690;
	}


	public void setCopyright(String copyright)
	{
		this.copyright = copyright;
	}


	public String getCopyright()
	{
		return copyright;
	}


	public void setRaeRIStatus(String raeRIStatus)
	{
		this.raeRIStatus = raeRIStatus;
	}


	public String getRaeRIStatus()
	{
		return raeRIStatus;
	}


	public void setStaffSelected(int staffSelected)
	{
		this.staffSelected = staffSelected;
	}


	public int getStaffSelected()
	{
		return staffSelected;
	}


	public void setStaffSubmitted(String staffSubmitted)
	{
		this.staffSubmitted = staffSubmitted;
	}


	public String getStaffSubmitted()
	{
		return staffSubmitted;
	}


	public void setStaffSubmitDate(Timestamp staffSubmitDate)
	{
		this.staffSubmitDate = staffSubmitDate;
	}


	public Timestamp getStaffSubmitDate()
	{
		return staffSubmitDate;
	}


	public void setHeadApproval(String headApproval)
	{
		this.headApproval = headApproval;
	}


	public String getHeadApproval()
	{
		return headApproval;
	}


	public void setHeadApprovalDate(Timestamp headApprovalDate)
	{
		this.headApprovalDate = headApprovalDate;
	}


	public Timestamp getHeadApprovalDate()
	{
		return headApprovalDate;
	}


	public void setDeanApproval(String deanApproval)
	{
		this.deanApproval = deanApproval;
	}


	public String getDeanApproval()
	{
		return deanApproval;
	}


	public void setDeanApprovalDate(Timestamp deanApprovalDate)
	{
		this.deanApprovalDate = deanApprovalDate;
	}


	public Timestamp getDeanApprovalDate()
	{
		return deanApprovalDate;
	}


	public void setRelatedRI(int relatedRI)
	{
		this.relatedRI = relatedRI;
	}


	public int getRelatedRI()
	{
		return relatedRI;
	}


	public void setRaeInternalRating(String raeInternalRating)
	{
		this.raeInternalRating = raeInternalRating;
	}


	public String getRaeInternalRating()
	{
		return raeInternalRating;
	}


	public void setRaeRIStatusDesc(String raeRIStatusDesc)
	{
		this.raeRIStatusDesc = raeRIStatusDesc;
	}


	public String getRaeRIStatusDesc()
	{
		return raeRIStatusDesc;
	}


	public void setMarkDelete(String markDelete)
	{
		this.markDelete = markDelete;
	}


	public String getMarkDelete()
	{
		return markDelete;
	}


	public void setIsIntlConf(String isIntlConf)
	{
		this.isIntlConf = isIntlConf;
	}


	public String getIsIntlConf()
	{
		return isIntlConf;
	}

}