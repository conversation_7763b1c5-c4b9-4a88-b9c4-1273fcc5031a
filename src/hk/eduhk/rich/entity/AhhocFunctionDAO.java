package hk.eduhk.rich.entity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.award.Award;
import hk.eduhk.rich.util.PersistenceManager;

@SuppressWarnings("serial")
public class AhhocFunctionDAO extends BaseDAO{
	private static AhhocFunctionDAO instance = null;
	
	public static AhhocFunctionDAO getInstance() {
		if(instance == null) {
			instance = new AhhocFunctionDAO();
		}
		return instance;
	}
	
	@SuppressWarnings({ "static-access", "resource" })
	public int updateCenseDateFromSAP() throws Exception
	{
		int row = 0;

		Connection conn = null;
		PreparedStatement pStmt = null;
		String query = "UPDATE RICH.RH_P_ACAD_PROF_OUTPUT_HDR T1"
							+ " SET CENSUS_DATE = (SELECT CENSUS_DATE FROM RICH.RH_SAP_ACAD_PROF_OUTPUT_HDR T2 WHERE T1.OUTPUT_NO = T2.OUTPUT_NO)";
		String query2 = "UPDATE RICH.RH_P_RESEARCH_PROJECT_HDR T1"
							+ " SET CENSUS_DATE = (SELECT CENSUS_DATE FROM RICH.RH_SAP_RESEARCH_PROJECT_HDR T2 WHERE T1.PROJECT_NO = T2.PROJECT_NO)";
		//System.out.println("query:"+query);
		try
		{
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			row = pStmt.executeUpdate();
			pStmt = conn.prepareStatement(query2);
			row = row + pStmt.executeUpdate();
			//System.out.println("insertData row:"+row);
		}
		catch (SQLException se)
		{
			se.printStackTrace();
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}

		return row;
	}

}
