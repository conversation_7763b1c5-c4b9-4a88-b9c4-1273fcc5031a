package hk.eduhk.rich.entity;

import java.util.logging.Logger;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import hk.eduhk.rich.UserPersistenceObject;

@Entity
@Table(name = "RH_Z_FILE")
public class ReportFile extends UserPersistenceObject{

	public static Logger logger = Logger.getLogger(FacDept.class.toString());
	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "FILE_ID_GENERATOR")
	@SequenceGenerator(name="FILE_ID_GENERATOR", sequenceName = "RH_Z_FILE_SEQ", allocationSize=1)
	@Column(name = "FILE_ID")
	private int fileId;
	
	@Column(name = "FILE_NAME")
	private String fileName;
	
	@Column(name = "ORIGINAL_NAME")
	private String originalName;
	
	@Column(name = "FILE_CAT")
	private String fileCat;
	
	@Column(name = "FILE_TYPE")
	private String fileType;
	
	@Column(name = "FILE_DESC")
	private String fileDesc;
	
	@Column(name = "FILE_SIZE")
	private int fileSize;

	
	public int getFileId()
	{
		return fileId;
	}

	
	public void setFileId(int fileId)
	{
		this.fileId = fileId;
	}

	
	public String getFileName()
	{
		return fileName;
	}

	
	public void setFileName(String fileName)
	{
		this.fileName = fileName;
	}

	
	public String getOriginalName()
	{
		return originalName;
	}

	
	public void setOriginalName(String originalName)
	{
		this.originalName = originalName;
	}

	
	public String getFileCat()
	{
		return fileCat;
	}

	
	public void setFileCat(String fileCat)
	{
		this.fileCat = fileCat;
	}

	
	public String getFileType()
	{
		return fileType;
	}

	
	public void setFileType(String fileType)
	{
		this.fileType = fileType;
	}

	
	public String getFileDesc()
	{
		return fileDesc;
	}

	
	public void setFileDesc(String fileDesc)
	{
		this.fileDesc = fileDesc;
	}

	
	public int getFileSize()
	{
		return fileSize;
	}

	
	public void setFileSize(int fileSize)
	{
		this.fileSize = fileSize;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((fileCat == null) ? 0 : fileCat.hashCode());
		result = prime * result + ((fileDesc == null) ? 0 : fileDesc.hashCode());
		result = prime * result + fileId;
		result = prime * result + ((fileName == null) ? 0 : fileName.hashCode());
		result = prime * result + fileSize;
		result = prime * result + ((fileType == null) ? 0 : fileType.hashCode());
		result = prime * result + ((originalName == null) ? 0 : originalName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ReportFile other = (ReportFile) obj;
		if (fileCat == null)
		{
			if (other.fileCat != null)
				return false;
		}
		else if (!fileCat.equals(other.fileCat))
			return false;
		if (fileDesc == null)
		{
			if (other.fileDesc != null)
				return false;
		}
		else if (!fileDesc.equals(other.fileDesc))
			return false;
		if (fileId != other.fileId)
			return false;
		if (fileName == null)
		{
			if (other.fileName != null)
				return false;
		}
		else if (!fileName.equals(other.fileName))
			return false;
		if (fileSize != other.fileSize)
			return false;
		if (fileType == null)
		{
			if (other.fileType != null)
				return false;
		}
		else if (!fileType.equals(other.fileType))
			return false;
		if (originalName == null)
		{
			if (other.originalName != null)
				return false;
		}
		else if (!originalName.equals(other.originalName))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ReportFile [fileId=" + fileId + ", fileName=" + fileName + ", originalName=" + originalName
				+ ", fileCat=" + fileCat + ", fileType=" + fileType + ", fileDesc=" + fileDesc + ", fileSize="
				+ fileSize + "]";
	}
	

}
