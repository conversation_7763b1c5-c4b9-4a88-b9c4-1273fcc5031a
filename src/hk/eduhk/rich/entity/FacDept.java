package hk.eduhk.rich.entity;

import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_Q_PK;


@Entity
@Table(name = "RH_Z_FAC_DEPT_V")
public class FacDept
{
	public static Logger logger = Logger.getLogger(FacDept.class.toString());
	
	@Id
	@Column(name = "FAC_DEPT")
	private String fac_dept;
	
	@Column(name = "LOOKUP_TYPE")
	private String lookup_type;	
	
	@Column(name = "FAC")
	private String fac;
	
	@Column(name = "DESCRIPTION")
	private String desc;
	
	@Column(name = "PRINT_ORDER")
	private String print_order;

	
	public String getFac_dept()
	{
		return fac_dept;
	}

	
	public void setFac_dept(String fac_dept)
	{
		this.fac_dept = fac_dept;
	}

	
	public String getLookup_type()
	{
		return lookup_type;
	}

	
	public void setLookup_type(String lookup_type)
	{
		this.lookup_type = lookup_type;
	}

	
	public String getFac()
	{
		return fac;
	}

	
	public void setFac(String fac)
	{
		this.fac = fac;
	}

	
	public String getDesc()
	{
		return desc;
	}

	
	public void setDesc(String desc)
	{
		this.desc = desc;
	}

	
	public String getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(String print_order)
	{
		this.print_order = print_order;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(fac_dept);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FacDept other = (FacDept) obj;
		return Objects.equals(fac_dept, other.fac_dept);
	}


	@Override
	public String toString()
	{
		return "FacDept [fac_dept=" + fac_dept + ", lookup_type=" + lookup_type + ", fac=" + fac + ", desc=" + desc
				+ ", print_order=" + print_order + "]";
	}

}
