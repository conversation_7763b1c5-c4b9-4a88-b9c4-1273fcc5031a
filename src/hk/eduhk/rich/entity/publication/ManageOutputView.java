package hk.eduhk.rich.entity.publication;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormatSymbols;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.Year;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.commons.validator.routines.ISBNValidator;
import org.apache.commons.validator.routines.ISSNValidator;
import org.primefaces.PrimeFaces;

import com.google.common.base.Strings;

import de.danielbechler.diff.ObjectDifferBuilder;
import de.danielbechler.diff.node.DiffNode;
import de.danielbechler.diff.node.DiffNode.Visitor;
import de.danielbechler.diff.node.Visit;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.*;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIOutputV;
import hk.eduhk.rich.entity.importRI.ImportRIOutputV_PK;
import hk.eduhk.rich.entity.importRI.ImportRIStatus;
import hk.eduhk.rich.entity.patent.PatentDetails_Q;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.view.ImportRIOutput;

@ManagedBean(name = "manageOutputView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageOutputView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(Publication.class.getName());
	
	private String riCreatorStaffNo;
	private Boolean isCreator;
	private Boolean isContributor;
	private Boolean canDelete;
	private Boolean hasSAP;
	private Boolean hasError;
	private Boolean saved;
	
	private List<String> selectedOutputTypeList = null;
	private List<OutputDetails_P> outputList;
	private List<OutputType> outputTypeList;
	private List<Authorship> authorshipList;
	private List<SubAuthorship> subAuthorshipList;
	private List<PubDiscipline> disciplineList;
	private List<ResearchType> researchTypeList;
	private List<LookupValue> sdgList;
	
	private List<SelectItem> cdcfStatusList;
	
	private List<OutputDetails_P> outputDetails_p_list;
	private OutputHeader_P selectedOutputHeader_p;
	private OutputAddl_P selectedOutputAddl_p;
	private OutputDetails_Q selectedOutputDetails_q;
	private OutputHeader_Q selectedOutputHeader_q;

	private ImportRIOutputV selectedImportOutput;
	
	private String[] outputTypeArray = {"120","130","140","150","160","170"};
	private List<String> articleAcceptanceDayList;
	
	private String authorshipTips;
	private String citation;
	private String defaultAPA_html;
	private String hasCustomCitation;
	
	private SysParamDAO sDao = SysParamDAO.getInstance();
	private PublicationDAO pDao = PublicationDAO.getInstance();
	private LookupValueDAO lookupDao = LookupValueDAO.getInstance();
	
	private ImportRIOutput outputPanel;
	
	
	
	
	public List<String> getArticleAcceptanceDayList()
	{
		int month = (selectedOutputHeader_p.getOpen_access_art_acc_month() != null)?selectedOutputHeader_p.getOpen_access_art_acc_month():1;
		int year = (selectedOutputHeader_p.getOpen_access_art_acc_year() != null)?selectedOutputHeader_p.getOpen_access_art_acc_year():Calendar.getInstance().get(Calendar.YEAR);
		YearMonth yearMonthObject = YearMonth.of(year, month);
		int daysInMonth = yearMonthObject.lengthOfMonth();
		articleAcceptanceDayList = new ArrayList<>();
		for (int d = 1; d <= daysInMonth; d++) {
			articleAcceptanceDayList.add(String.valueOf(d));
		}
		return articleAcceptanceDayList;
	}
	
	public void checkValid(ComponentSystemEvent event) throws IOException
	{
		paramDataLevel = getParamDataLevel();
		String message = "";
		if (!getIsRdoAdmin()) {
			if (getIsCreator() == false && getIsContributor() == false && !"M".equals(paramDataLevel)) {
				message = "You don't have access right.";	
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, getLoginUserId()+" doesn't have access right");
			}
		}
	}
	
	
	
	public Boolean hasAccessRight() 
	{
		Boolean result = false;
		if ("M".equals(paramDataLevel)){
			if (getIsCreator() || getIsContributor()) {
				result = true;
			}
		}
		if ("P".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		if ("C".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		return result;
	}
	
	
	
	/*public Boolean hasAccessRight() 
	{
		Boolean result = false;
		if ("M".equals(paramDataLevel)){
			outputDetails_p_list = getOutputDetails_p_list();
			if(getIsRdoAdmin()) result = true;
			else if (getIsAsst()) {
				staffDetail = getStaffDetail(getParamPid(), null, true);
			}else {
				staffDetail = getStaffDetail(null, null, false);
			}
			if (staffDetail != null) {
				for (OutputDetails_P p:outputDetails_p_list) {	
					if (staffDetail.getStaff_number().equals(p.getAuthorship_staff_no())) {
						result = true;
					}
				}
			}
			
		}
		if ("P".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		if ("C".equals(paramDataLevel) && getIsRdoAdmin()){
			result = true;
		}
		return result;
	}*/
	
	
	public List<LookupValue> getSdgList()
	{
		if(sdgList == null) {
			sdgList = lookupDao.getLookupValueList ("SDG","US","Y");
		}
		return sdgList;
	}

	
	public void setSdgList(List<LookupValue> sdgList)
	{
		this.sdgList = sdgList;
	}

	public Boolean canViewRIList() 
	{
		Boolean result = false;
		if (staffDetail == null) {
			if (getIsAsst() || getIsRdoAdmin()) {
				staffDetail = getStaffDetail(getParamPid(), null, true);
				if (staffDetail == null) {
					//check is Former Staff
					staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
					if (staffPastDetail != null) {
						staffDetail = new StaffIdentity();
						staffDetail.setPid(staffPastDetail.getPid());
						staffDetail.setStaff_number(staffPastDetail.getStaff_number());
						staffDetail.setCn(staffPastDetail.getCn());
						staffDetail.setStaffType("F");
					}
				}
			}else {
				staffDetail = getStaffDetail(null, null, false);
			}
		}
		if (staffDetail != null) {
			if (Strings.isNullOrEmpty(paramPid))
				paramPid = String.valueOf(staffDetail.getPid());
			if (getIsAsst() || getIsRdoAdmin() || getCurrentUserId().equals(staffDetail.getCn())){
					result = true;
			}
		}
		return result;
	}
	
	public Boolean getCanDelete()
	{
		if (canDelete == null) {
			canDelete = false;
			if (getIsCreator() && !Strings.isNullOrEmpty(paramNo)) {
				/*if (getSelectedOutputHeader_q() != null) {
					if ("CDCF_PENDING".equals(selectedOutputHeader_q.getCdcf_status())) {
						//if m level is existed
						OutputHeader_P selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedOutputHeader_p != null) {
							canDelete = true;
						}
					}
				}*/
				OutputHeader_P selectedOutputHeader_p_c = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "C");
				if ("M".equals(getParamDataLevel())) {
					//if c level is null
					if (selectedOutputHeader_p_c == null) {
						//if m level is existed
						OutputHeader_P selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedOutputHeader_p != null) {
							canDelete = true;
						}
					}
				}else {
					if (getHasSAP() == false) {
						canDelete = true;
					}
				}
			}
		}
		return canDelete;
	}

	
	public void setCanDelete(Boolean canDelete)
	{
		this.canDelete = canDelete;
	}
	
	public String checkSAPError()
	{
		String errMessage = pDao.getSAPLog("RH_UPLOAD_OUTPUT_P");
		if (errMessage.contains(paramNo) == false) {
			return errMessage;
		}
		return null;
	}
	
	public Boolean getHasSAP()
	{
		if (hasSAP == null) {
			hasSAP = false;
			hasSAP = pDao.hasSAP("CHRM_SAP_ACAD_PROF_OUTPUT_HDR", "OUTPUT_NO", Integer.valueOf(paramNo));
		}
		return hasSAP;
	}


	
	public void setHasSAP(Boolean hasSAP)
	{
		this.hasSAP = hasSAP;
	}


	public Boolean getIsCreator()
	{
		if (isCreator == null) {
			isCreator = false;
			selectedOutputDetails_q = getSelectedOutputDetails_q();
			if (selectedOutputDetails_q != null) {
				isCreator = ("Y".equals(selectedOutputDetails_q.getCreator_ind()))?true:false;
				if (!Strings.isNullOrEmpty(getParamNo())) {
					OutputDetails_Q currentCreator = pDao.getOutputDetails_Q_creator(Integer.valueOf(getParamNo()));
					if (isCreator && currentCreator != null) {
						if (currentCreator.getPk().getStaff_no().equals(selectedOutputDetails_q.getPk().getStaff_no())) {
							isCreator = true;
						}else {
							isCreator = false;
						}
					}
				}
			}
			if(paramNo == null) {
				isCreator = true;
			}
			if (getIsRdoAdmin()) {
				isCreator = true;
			}
		}
		return isCreator;
	}
	
	public void setIsCreator(Boolean isCreator)
	{
		this.isCreator = isCreator;
	}

	public Boolean getIsContributor()
	{
		if (isContributor == null) {
			isContributor = false;
			selectedOutputDetails_q = getSelectedOutputDetails_q();
			if (selectedOutputDetails_q != null) {
				isContributor = ("N".equals(selectedOutputDetails_q.getCreator_ind()))?true:false;
			}
		}
		return isContributor;
	}

	public void setIsContributor(Boolean isContributor)
	{
		this.isContributor = isContributor;
	}

	public Boolean getHasError()
	{
		if (hasError == null) {
			hasError = false;
		}
		return hasError;
	}

	public void setHasError(Boolean hasError)
	{
		this.hasError = hasError;
	}

	public Boolean getSaved()
	{
		if (saved == null) {
			saved = false;
		}
		return saved;
	}

	
	public void setSaved(Boolean saved)
	{
		this.saved = saved;
	}
	
	public List<OutputDetails_P> getOutputList()
	{
		if (canViewRIList() && outputList == null) {
			outputList = pDao.getOutputDetails_P_byStaffNo(staffDetail.getStaff_number(), "M");
			if (!Strings.isNullOrEmpty(getParamConsent())) {
				if (!"all".equals(paramConsent)) {
					outputList = outputList.stream()
							.filter(y -> paramConsent.equals(y.getOutputDetails_q_ConsentInd()) && y.getOutputHeader_q().getPublish_freq() > 0)
							.collect(Collectors.toList());
				}
			}else {
				outputList.removeIf(y -> "N".equals(y.getOutputDetails_q_CreatorInd()) && y.getOutputHeader_q().getPublish_freq() < 1);
			}
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			for (OutputDetails_P p:outputList) {
				if (Strings.isNullOrEmpty(p.getOutputHeader_p().getSap_output_type())) {
					p.getOutputHeader_p().setSap_output_type("0");
				}
				List<OutputType> tmpList = lvTwoList.stream()
														.filter(y -> p.getOutputHeader_p().getSap_output_type().equals(y.getPk().getLookup_code()))
														.collect(Collectors.toList());
				if (!tmpList.isEmpty()) {
					p.getOutputHeader_p().setOutput_lookup_code(tmpList.get(0).getParent_lookup_code());
				}else {
					p.getOutputHeader_p().setOutput_lookup_code("0");
				}
				selectedOutputHeader_p = p.getOutputHeader_p();
				genCitation();
				p.getOutputHeader_p().setApa_html(selectedOutputHeader_p.getApa_html());
			}
		}
		return outputList;
	}

	public List<String> getSelectedOutputTypeList() throws SQLException, IOException
	{
		if (selectedOutputTypeList == null) {
			selectedOutputTypeList = new ArrayList<>();
			outputList = getOutputList();
			if (outputList != null) {
				for(OutputDetails_P p:outputList) {
					if (!selectedOutputTypeList.contains(p.getOutputHeader_p().getOutput_lookup_code()))
						selectedOutputTypeList.add(p.getOutputHeader_p().getOutput_lookup_code());
				}			
			}
		}
		//System.out.println(selectedOutputTypeList);
		return selectedOutputTypeList;
	}
	
	public String getSelectedOutputCatDesc(String code) throws SQLException, IOException
	{
		String result = "No Output Type";
		if (!Strings.isNullOrEmpty(code)) {
			List<OutputType> lvOneList = pDao.getOutputTypeList(1);
			List<OutputType> tmpList = lvOneList.stream()
					.filter(y -> code.equals(y.getPk().getLookup_code()))
					.collect(Collectors.toList());
			result = (!tmpList.isEmpty())?tmpList.get(0).getDescription():"No Output Type";
			
		}
		return result;
	}	
	
	public String getSelectedOutputDesc(String code) throws SQLException, IOException
	{
		String result = "";
		if (!Strings.isNullOrEmpty(code)) {
			outputList = getOutputList();
			if (outputList != null) {
				for(OutputDetails_P p:outputList) {
					if (p.getOutputHeader_p().getOutput_lookup_code() != null) {	
						if (p.getOutputHeader_p().getOutput_lookup_code().equals(code)) {
							result += "<span class='cv_dot'></span> " + p + "</br>";
						}
					}			
				}
			}
		}
		return result;
	}		
	

	
	public long getTotalCount(String code) {
		if (getOutputList() != null) {
			if (!Strings.isNullOrEmpty(code)) {
				return outputList.stream().filter(outputList -> code.equals(outputList.getOutputHeader_p().getOutput_lookup_code())).count();
			}else {
				return outputList.stream().count();
			}
		}else {
			return 0;
		}	
    }

	public List<OutputDetails_P> getOutputDetails_p_list()
	{
		if (outputDetails_p_list == null) {
			outputDetails_p_list = new ArrayList<OutputDetails_P>();
			if (!Strings.isNullOrEmpty(paramNo)) {
				outputDetails_p_list = pDao.getOutputDetails_P(Integer.valueOf(paramNo), getParamDataLevel());
				for (OutputDetails_P d:outputDetails_p_list) {
					if ("F".equals(d.getNon_ied_staff_flag())){
						StaffPast tmp = staffDao.getPastStaffDetailsByStaffNo(d.getAuthorship_staff_no());
						if (tmp != null) {
							d.setAuthorship_name(tmp.getFullname_display());
						}else {
							d.setAuthorship_name("");
						}
					}
					if ("N".equals(d.getNon_ied_staff_flag())){
						StaffIdentity tmp = staffDao.getStaffDetailsByStaffNo(d.getAuthorship_staff_no());
						if (tmp != null) {
							d.setAuthorship_name(tmp.getFullname_display());
						}else {
							d.setAuthorship_name("");
						}
					}
				}
			}else if(getSelectedImportOutput() != null) {
				int count = 1;
				if (selectedImportOutput.getContributor_id_list() != null) {
					List<String> cnList = Arrays.asList(selectedImportOutput.getContributor_id_list().split(";"));
			    	for(String cn : cnList) {
			    		StaffIdentity staff = null;
						OutputDetails_P tmp = new OutputDetails_P();
				    	tmp.getPk().setData_level("M");
				    	tmp.setNon_ied_staff_flag("F");
				    	for(StaffIdentity id : getStaffNameList()) {
				    		if(id.getCn().equals(cn)) {
				    			tmp.setNon_ied_staff_flag("N");
				    			staff = id;
				    			break;
				    		}
				    	}
				    	if(staff != null) {
					    	tmp.setAuthorship_person_id(String.valueOf(staff.getPid()));
					    	tmp.setAuthorship_staff_no(String.valueOf(staff.getStaff_number()));
					    	tmp.setAuthorship_type("AUTHOR");
					    	if(count == 1)
					    		tmp.setAuthorship_dtl_type("FIRST");
					    	else
					    		tmp.setAuthorship_dtl_type("CO");
					    	outputDetails_p_list.add(tmp);
					    	count++;
				    	}
			    	}
		    	}
			}else {
				if (getIsAsst() || getIsRdoAdmin()) {
					staffDetail = getStaffDetail(getParamPid(), null, true);
					if (staffDetail == null) {
						//check is Former Staff
						staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
						if (staffPastDetail != null) {
							staffDetail = new StaffIdentity();
							staffDetail.setPid(staffPastDetail.getPid());
							staffDetail.setStaff_number(staffPastDetail.getStaff_number());
							staffDetail.setCn(staffPastDetail.getCn());
							staffDetail.setStaffType("F");
						}
					}
				}else {
					staffDetail = getStaffDetail(null, null, false);
				}
		    	OutputDetails_P tmp = new OutputDetails_P();
		    	if (staffDetail != null) {
			    	tmp.getPk().setData_level("M");
			    	if ("F".equals(staffDetail.getStaffType())){
			    		tmp.setNon_ied_staff_flag("F");
			    	}else {
			    		tmp.setNon_ied_staff_flag("N");
			    	}
			    	tmp.setAuthorship_person_id(String.valueOf(staffDetail.getPid()));
			    	tmp.setAuthorship_staff_no(String.valueOf(staffDetail.getStaff_number()));
			    	tmp.setAuthorship_type("AUTHOR");
			    	tmp.setAuthorship_dtl_type("FIRST");
		    	}
		    	outputDetails_p_list.add(tmp);
			}
		}
		return outputDetails_p_list;
	}

	
	public void setOutputDetails_p_list(List<OutputDetails_P> outputDetails_p_list)
	{
		this.outputDetails_p_list = outputDetails_p_list;
	}

	public OutputHeader_P getSelectedOutputHeader_p()
	{
		if (selectedOutputHeader_p == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), getParamDataLevel());
				selectedOutputHeader_p = (selectedOutputHeader_p != null)?selectedOutputHeader_p:new OutputHeader_P();
			}else {
				selectedOutputHeader_p = new OutputHeader_P();
				if(getSelectedImportOutput() != null) {
					selectedOutputHeader_p.setTitle_jour_book(selectedImportOutput.getTitle_jour_book());
					selectedOutputHeader_p.setSap_output_type(selectedImportOutput.getSap_output_type());
					selectedOutputHeader_p.setIssn(selectedImportOutput.getIssn());
					selectedOutputHeader_p.setFrom_year(Integer.parseInt(selectedImportOutput.getFrom_year()));
					if(StringUtils.isNotBlank(selectedImportOutput.getFrom_month()))
						selectedOutputHeader_p.setFrom_month(Integer.parseInt(selectedImportOutput.getFrom_month()));
					selectedOutputHeader_p.setApa_citation(selectedImportOutput.getApa_citation());
					selectedOutputHeader_p.setLanguage(selectedImportOutput.getLanguage());
					selectedOutputHeader_p.setPublisher(selectedImportOutput.getPublisher());
					selectedOutputHeader_p.setCity(selectedImportOutput.getCity());
					selectedOutputHeader_p.setPage_num(selectedImportOutput.getPage_num());
					selectedOutputHeader_p.setPage_num_from(selectedImportOutput.getPage_num_from());
					selectedOutputHeader_p.setPage_num_to(selectedImportOutput.getPage_num_to());
					selectedOutputHeader_p.setVol_issue(selectedImportOutput.getVol_issue());
					selectedOutputHeader_p.setTitle_paper_art(selectedImportOutput.getTitle_paper_art());
					selectedOutputHeader_p.setIsbn(selectedImportOutput.getFirstISBN());
					selectedOutputHeader_p.setFulltext_url(selectedImportOutput.getFulltext_url());
					selectedOutputHeader_p.setDoi(selectedImportOutput.getDoi());
					selectedOutputHeader_p.setName_other_editors(selectedImportOutput.getName_other_editors());
					selectedOutputHeader_p.setArticle_num(selectedImportOutput.getArticle_num());
				}
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_p.getSap_output_type())) {
				selectedOutputHeader_p.setSap_output_type("0");
			}
			if (selectedOutputHeader_p.getFrom_year() == null) {
				selectedOutputHeader_p.setFrom_year(getCurrentYear());
			}
			genCitation();
		}
		return selectedOutputHeader_p;
	}


	
	public void setSelectedOutputHeader_p(OutputHeader_P selectedOutputHeader_p)
	{
		this.selectedOutputHeader_p = selectedOutputHeader_p;
	}


	
	public OutputAddl_P getSelectedOutputAddl_p()
	{
		if (getIsAsst() || getIsRdoAdmin()) {
			staffDetail = getStaffDetail(getParamPid(), null, true);
			//check is Former Staff
			staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
			if (staffPastDetail != null) {
				staffDetail = new StaffIdentity();
				staffDetail.setPid(staffPastDetail.getPid());
				staffDetail.setStaff_number(staffPastDetail.getStaff_number());
				staffDetail.setCn(staffPastDetail.getCn());
				staffDetail.setStaffType("F");
			}
		}else {
			staffDetail = getStaffDetail(null, null, false);
		}
		if (staffDetail != null && selectedOutputAddl_p == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				paramDataLevel = getParamDataLevel();
				selectedOutputAddl_p = pDao.getOutputAddl_P(Integer.valueOf(paramNo), paramDataLevel, staffDetail.getStaff_number());
			}
			if (selectedOutputAddl_p == null){
				selectedOutputAddl_p = new OutputAddl_P();
				selectedOutputAddl_p.getPk().setStaff_no(staffDetail.getStaff_number());
			}
		}
		return selectedOutputAddl_p;
	}


	
	public void setSelectedOutputAddl_p(OutputAddl_P selectedOutputAddl_p)
	{
		this.selectedOutputAddl_p = selectedOutputAddl_p;
	}


	
	public String getHasCustomCitation()
	{
		if (Strings.isNullOrEmpty(hasCustomCitation)) {
			hasCustomCitation = "N";
			selectedOutputAddl_p = getSelectedOutputAddl_p();
			if (selectedOutputAddl_p != null) {
				if (!Strings.isNullOrEmpty(selectedOutputAddl_p.getCustom_citation())){
					hasCustomCitation = "Y";
				}
			}
		}
		return hasCustomCitation;
	}


	
	public void setHasCustomCitation(String hasCustomCitation)
	{
		this.hasCustomCitation = hasCustomCitation;
		if (selectedOutputAddl_p != null && selectedOutputHeader_p != null) {
			if ("Y".equals(hasCustomCitation) && Strings.isNullOrEmpty(selectedOutputAddl_p.getCustom_citation())) {
				selectedOutputAddl_p.setCustom_citation(selectedOutputHeader_p.getApa_html());
			}
		}
		
	}
	
	public OutputDetails_Q getSelectedOutputDetails_q()
	{
		if (getIsAsst() || getIsRdoAdmin()) {
			staffDetail = getStaffDetail(getParamPid(), null, true);
			//check is Former Staff
			staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
			if (staffPastDetail != null) {
				staffDetail = new StaffIdentity();
				staffDetail.setPid(staffPastDetail.getPid());
				staffDetail.setStaff_number(staffPastDetail.getStaff_number());
				staffDetail.setCn(staffPastDetail.getCn());
				staffDetail.setStaffType("F");
			}
		}else {
			staffDetail = getStaffDetail(null, null, false);
		}
		if (staffDetail != null && selectedOutputDetails_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				List<OutputDetails_Q> tmp = pDao.getOutputDetails_Q(Integer.valueOf(paramNo), staffDetail.getStaff_number());
				selectedOutputDetails_q = (!tmp.isEmpty())?tmp.get(0):null;
			}else if(getSelectedImportOutput() != null) {
				riCreatorStaffNo = getSelectedImportOutput().getPk().getStaff_number();
				selectedOutputDetails_q = new OutputDetails_Q();
				selectedOutputDetails_q.getPk().setStaff_no(getSelectedImportOutput().getPk().getStaff_number());
				selectedOutputDetails_q.setCreator_ind("Y");
				selectedOutputDetails_q.setDisplay_ind("Y");
				selectedOutputDetails_q.setConsent_ind("Y");
			}
			if (selectedOutputDetails_q == null){
				riCreatorStaffNo = staffDetail.getStaff_number();
				selectedOutputDetails_q = new OutputDetails_Q();
				selectedOutputDetails_q.getPk().setStaff_no(staffDetail.getStaff_number());
				selectedOutputDetails_q.setCreator_ind("Y");
				selectedOutputDetails_q.setDisplay_ind("Y");
				selectedOutputDetails_q.setConsent_ind("Y");
			}
		}
		return selectedOutputDetails_q;
	}


	
	public void setSelectedOutputDetails_q(OutputDetails_Q selectedOutputDetails_q)
	{
		this.selectedOutputDetails_q = selectedOutputDetails_q;
	}


	
	public OutputHeader_Q getSelectedOutputHeader_q()
	{
		if (selectedOutputHeader_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedOutputHeader_q = pDao.getOutputHeader_Q(Integer.valueOf(paramNo));
				selectedOutputHeader_q = (selectedOutputHeader_q != null)?selectedOutputHeader_q:new OutputHeader_Q();
			}else {
				selectedOutputHeader_q = new OutputHeader_Q();
			}
			if ("Y".equals(selectedOutputHeader_q.getInst_verified_ind()) && selectedOutputHeader_q.getInst_verified_date() == null) {
				selectedOutputHeader_q.setInst_verified_date(getCurrentDate());
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getInst_display_ind())) {
				selectedOutputHeader_q.setInst_display_ind("Y");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getInst_verified_ind())) {
				selectedOutputHeader_q.setInst_verified_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getCdcf_status())) {
				selectedOutputHeader_q.setCdcf_status("CDCF_PENDING");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getCdcf_gen_ind())) {
				selectedOutputHeader_q.setCdcf_gen_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getCdcf_processed_ind())) {
				selectedOutputHeader_q.setCdcf_processed_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getCdcf_selected_ind())) {
				selectedOutputHeader_q.setCdcf_selected_ind("U");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getCdcf_changed_ind())) {
				selectedOutputHeader_q.setCdcf_changed_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedOutputHeader_q.getAmis_selected_ind())) {
				selectedOutputHeader_q.setAmis_selected_ind("N");
			}
			if (selectedOutputHeader_q.getPublish_freq() == null) {
				selectedOutputHeader_q.setPublish_freq(0);
			}
		}
		return selectedOutputHeader_q;
	}


	
	public void setSelectedOutputHeader_q(OutputHeader_Q selectedOutputHeader_q)
	{
		this.selectedOutputHeader_q = selectedOutputHeader_q;
	}

	
	public List<OutputType> getOutputTypeList()
	{
		if (outputTypeList == null) {
			outputTypeList = new ArrayList<OutputType>();
			List<OutputType> lvOneList = pDao.getOutputTypeList(1);
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			for (OutputType o:lvOneList) {
				outputTypeList.add(o);
				List<OutputType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				outputTypeList.addAll(tmpLvTwoList);
			}
		}
		return outputTypeList;
	}

	
	
	public void setOutputTypeList(List<OutputType> outputTypeList)
	{
		this.outputTypeList = outputTypeList;
	}
	

	public List<SelectItem> getCdcfStatusList()
	{
		cdcfStatusList = new ArrayList<SelectItem>();
		
		cdcfStatusList.add(optionPending);
		if ("CDCF_PENDING".equals(selectedOutputHeader_q.getCdcf_status()))
				cdcfStatusList.add(optionProcessed);
		if (!"CDCF_PENDING".equals(selectedOutputHeader_q.getCdcf_status()))
			cdcfStatusList.add(optionGenerated);
		cdcfStatusList.add(optionNotSelected);
		cdcfStatusList.add(optionSpecial);
		return cdcfStatusList;
	}
	
	public void setCdcfStatusList(List<SelectItem> cdcfStatusList)
	{
		this.cdcfStatusList = cdcfStatusList;
	}


	public List<PubDiscipline> getDisciplineList()
	{
		if (disciplineList == null) {
			disciplineList = new ArrayList<PubDiscipline>();
			List<PubDiscipline> lvOneList = pDao.getDisciplineList(1);
			List<PubDiscipline> lvTwoList = pDao.getDisciplineList(2);
			for (PubDiscipline o:lvOneList) {
				disciplineList.add(o);
				List<PubDiscipline> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				disciplineList.addAll(tmpLvTwoList);
			}
		}
		return disciplineList;
	}
	
	
	public void setDisciplineList(List<PubDiscipline> disciplineList)
	{
		this.disciplineList = disciplineList;
	}

	public void setDisplyRI()
	{
		if (selectedOutputDetails_q != null) {
			if (!"Y".equals(selectedOutputDetails_q.getConsent_ind()))
				selectedOutputDetails_q.setDisplay_ind("N");
		}
	}
	
	public Boolean requireNameOfPublication()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"130","160","170","180","190","200","350","420"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}
	
	public Boolean requireVolumn()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"160","170","420"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}	
	
	public Boolean requirePageNo()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"130","160","170","420"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}	
	
	public Boolean requireArticleNo()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"130","160","170","420"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}	
	
	public Boolean requirePlace()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			//String[] values = {"120","130","140","150","180","190","200","210","220","230","240","250","260","270","280","290","300","310","330","360"};
			String[] values = {"180","190","200","210","220","230","240","250","260","270","280","290","300","310","330","360"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}
	
	public Boolean requirePublisher()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"120","130","140","150","310","340","360"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}
	public Boolean requireEditor()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"130","150","340"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}
	public Boolean requireAuthor()
	{
		Boolean result = true;
		Boolean inArray = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"150","340"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				inArray = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		if (inArray) {
			result = false;
		}
		return result;
	}
	public Boolean requireIntCon()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"180","190","200"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
		}
		return result;
	}
	public Boolean requireIsRgcProj()
	{
		Boolean result = true;
		/*
		 Boolean result = false;
		 if (selectedOutputHeader_p != null) {
			String[] values = {"Y"};
			if (selectedOutputHeader_p.getSap_refered_journal() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getSap_refered_journal()::equals);
		}*/
		return result;
	}
	public Boolean requireRgcProjNum()
	{
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String[] values = {"Y"};
			if (selectedOutputHeader_p.getIs_rgc_proj() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getIs_rgc_proj()::equals);
		}
		return result;
	}
	public Boolean requireOpenAccess()
	{
		//Boolean result = true;
		Boolean result = false;
		Boolean ans1 = false;
		Boolean ans2 = false;
		Boolean ans3 = false;
		if (selectedOutputHeader_p != null) {
			String[] values1 = {"Y"};
			if (selectedOutputHeader_p.getIs_rgc_proj() != null)
				ans1 = Arrays.stream(values1).anyMatch(selectedOutputHeader_p.getIs_rgc_proj()::equals);
			if (selectedOutputHeader_p.getSap_refered_journal() != null)
				ans2 = Arrays.stream(values1).anyMatch(selectedOutputHeader_p.getSap_refered_journal()::equals);
			String[] values3 = {"160","170"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				ans3 = Arrays.stream(values3).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);
			if (ans1 && ans2 && ans3) {
				result = true;
			}
		}
		if (!result) {
			selectedOutputHeader_p.setOpen_access_stat(null);
		}
		return result;
	}
	
	public Boolean requireOpenAccessPayment()
	{
		Boolean result = false;
		if (requireOpenAccess()) {
			String[] values = {"I","E"};
			if (selectedOutputHeader_p.getOpen_access_stat() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getOpen_access_stat()::equals);
		}
		if (!result) {
			selectedOutputHeader_p.setOpen_access_payment(null);
		}else {
			if ("N".equals(selectedOutputHeader_p.getOpen_access_apc()) && "N".equals(selectedOutputHeader_p.getOpen_access_apc_payment())){
				selectedOutputHeader_p.setOpen_access_payment("N");
			}
		}
		return result;
	}
	
	public Boolean disableOpenAccessPayment()
	{
		Boolean result = false;
		if ("N".equals(selectedOutputHeader_p.getOpen_access_apc()) && "N".equals(selectedOutputHeader_p.getOpen_access_apc_payment())){
			result = true;
		}
		return result;
	}
	
	public Boolean requireOpenAccessApc()
	{
		Boolean result = false;
		//Boolean ans1 = false;
		Boolean ans1 = true;
		//Boolean ans2 = false;
		Boolean ans2 = true;
		//Boolean ans3 = false;
		Boolean ans3 = true;
		Boolean ans4 = false;
		if (selectedOutputHeader_p != null) {
			/*String[] values1 = {"Y"};
			if (selectedOutputHeader_p.getIs_rgc_proj() != null)
				ans1 = Arrays.stream(values1).anyMatch(selectedOutputHeader_p.getIs_rgc_proj()::equals);
			if (selectedOutputHeader_p.getSap_refered_journal() != null)
				ans2 = Arrays.stream(values1).anyMatch(selectedOutputHeader_p.getSap_refered_journal()::equals);
			String[] values3 = {"160","170"};
			if (selectedOutputHeader_p.getSap_output_type() != null)
				ans3 = Arrays.stream(values3).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals);*/
			String[] values4 = {"I","E"};
			if (selectedOutputHeader_p.getOpen_access_stat() != null)
				ans4 = Arrays.stream(values4).anyMatch(selectedOutputHeader_p.getOpen_access_stat()::equals);
			if (ans1 && ans2 && ans3 && ans4) {
				result = true;
			}
		}
		if (!result) {
			selectedOutputHeader_p.setOpen_access_apc(null);
		}
		return result;
	}
	public Boolean requireOpenAccessApcPayment()
	{
		Boolean result = false;
		if (requireOpenAccess() && requireOpenAccessApc()) {
			result = true;
			/*String[] values = {"Y"};
			if (selectedOutputHeader_p.getOpen_access_apc() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getOpen_access_apc()::equals);*/
		}
		if (!result) {
			selectedOutputHeader_p.setOpen_access_apc_payment(null);
			selectedOutputHeader_p.setApc_val(null);
		}
		return result;
	}
	public Boolean requireOpenAccessEmb()
	{
		Boolean result = false;
		if (requireOpenAccess()) {
			String[] values = {"E"};
			if (selectedOutputHeader_p.getOpen_access_stat() != null)
				result = Arrays.stream(values).anyMatch(selectedOutputHeader_p.getOpen_access_stat()::equals);
		}
		if (!result) {
			selectedOutputHeader_p.setOpen_access_art_acc_day(null);
			selectedOutputHeader_p.setOpen_access_art_acc_month(null);
			selectedOutputHeader_p.setOpen_access_art_acc_year(null);
			selectedOutputHeader_p.setOpen_access_emb_end_month(null);
			selectedOutputHeader_p.setOpen_access_emb_end_year(null);
			selectedOutputHeader_p.setOpen_access_emb_period_month(null);
			selectedOutputHeader_p.setOpen_access_emb_period_year(null);
		}
		return result;
	}
	public void resetAuthorshipList()
	{	
		authorshipList = null;
	}
	
	public void resetResearchActivityList()
	{	
		ExternalContext ec = FacesContext.getCurrentInstance().getExternalContext();
		String vol_issue = ec.getRequestParameterMap().get("editForm:vol_issue");
		selectedOutputHeader_p.setVol_issue(vol_issue);
		String title_paper_art = ec.getRequestParameterMap().get("editForm:title_paper_art");
		selectedOutputHeader_p.setTitle_paper_art(title_paper_art);
		researchTypeList = null;
		genCitation();
	}
	
	public List<ResearchType> getResearchTypeList()
	{
		if (researchTypeList == null) {
			List<ResearchType> lvOneList = pDao.getResearchTypeList(1);
			if (selectedOutputHeader_p != null) {
				if (selectedOutputHeader_p.getSap_output_type() != null) {
					if (selectedOutputHeader_p.getSap_output_type().equals("160") || selectedOutputHeader_p.getSap_output_type().equals("190")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("Y"))
								.collect(Collectors.toList());
					}
					else if (selectedOutputHeader_p.getSap_output_type().equals("360") || selectedOutputHeader_p.getSap_output_type().equals("420")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("N"))
								.collect(Collectors.toList());
					}			
					else if (selectedOutputHeader_p.getSap_output_type().equals("280")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("C"))
								.collect(Collectors.toList());
					}	
					else if (selectedOutputHeader_p.getSap_output_type().equals("370") || selectedOutputHeader_p.getSap_output_type().equals("300")
								|| selectedOutputHeader_p.getSap_output_type().equals("310") || selectedOutputHeader_p.getSap_output_type().equals("320")
								|| selectedOutputHeader_p.getSap_output_type().equals("330")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("99"))
								.collect(Collectors.toList());
					}	
					else if (selectedOutputHeader_p.getSap_output_type().equals("200")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> !y.getPk().getLookup_code().equals("99"))
								.collect(Collectors.toList());
					}		
					else {
						researchTypeList = lvOneList;
					}	
					//If only 1 choose, set default value
					if (researchTypeList.size() < 2) {
						selectedOutputHeader_p.setSap_refered_journal(researchTypeList.get(0).getPk().getLookup_code());
					}
				}
			}
		}
		return researchTypeList;
	}

	public boolean meet2014AuthorshipRule()
	{
		boolean meet2014AuthorshipRule = false;
		int startDateYear = 2014;
		int startDateMonth = 7;
		String startDate =  sDao.getSysParamValueByCode("AUTHORSHIP_DETAIL_START_DATE");
		if (startDate != null) {
			String[] parts = startDate.split("-");
			if (parts.length > 1) {
				startDateYear = Integer.parseInt(parts[0]);
				startDateMonth = Integer.parseInt(parts[1]);
			}
		}
		if (selectedOutputHeader_p.getFrom_year() != null) {
			//if year > 2014
			if (selectedOutputHeader_p.getFrom_year() > startDateYear) {
				meet2014AuthorshipRule = true;
			}
			//if year == 2014 and month > 7
			if (selectedOutputHeader_p.getFrom_year() == startDateYear && selectedOutputHeader_p.getFrom_month() != null) {
				if (selectedOutputHeader_p.getFrom_month() > startDateMonth) {
					meet2014AuthorshipRule = true;
				}
			}
		}
		return meet2014AuthorshipRule;
	}
	
	public boolean mustHaveSubAuthorship()
	{
		boolean mustHaveSubAuthorship = false;

		if (!Strings.isNullOrEmpty(selectedOutputHeader_p.getSap_output_type()) && meet2014AuthorshipRule()) {
			if (Arrays.stream(outputTypeArray).anyMatch(selectedOutputHeader_p.getSap_output_type()::equals)) {
					mustHaveSubAuthorship = true;
			}
		}
		return mustHaveSubAuthorship;
	}
	
	public List<Authorship> getAuthorshipList()
	{
		if (authorshipList == null) {
			authorshipList = pDao.getAuthorshipList(1);
			if (meet2014AuthorshipRule()) {
				authorshipList.removeIf(y->"CO-EDITOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"COR-AUTHOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"COR-EDITOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"FIRST-AUTHOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"FIRST-EDITOR".equals(y.getPk().getLookup_code()));	
			}
			if (mustHaveSubAuthorship()) {
				authorshipList.removeIf(y->"CO-AUTHOR".equals(y.getPk().getLookup_code()));
			}
		}
		return authorshipList;
	}
	
	public List<SubAuthorship> getSubAuthorshipList()
	{
		if (subAuthorshipList == null) {
			subAuthorshipList = pDao.getSubAuthorshipList(1);
		}
		return subAuthorshipList;
	}

	public void resetDAList()
	{	
		String sch_dtl_code = selectedOutputHeader_p.getSch_dtl_code();
		if (!Strings.isNullOrEmpty(sch_dtl_code)) {
			String eduParentLookupCode = getEduSectorParentLookupCode(sch_dtl_code);
			if (!Strings.isNullOrEmpty(eduParentLookupCode)) {
				disAreaList = null;
				disAreaList = getDisAreaList();

				if ("3".equals(eduParentLookupCode)){
					disAreaList.removeIf(y -> !"EDU1000".equals(y.getParent_lookup_code()) && !"EDU1000".equals(y.getPk().getLookup_code()));
				}else {
					disAreaList.removeIf(y -> "EDU1000".equals(y.getParent_lookup_code()) || "EDU1000".equals(y.getPk().getLookup_code()));
				}
			}
		}
	}
	
	public void genCitation() 
	{
		if (!GenericValidator.isBlankOrNull(selectedOutputHeader_p.getSap_output_type())){
			String result = "";
			if (!"0".equals(selectedOutputHeader_p.getSap_output_type())) {
				if ("APA".equals(getCitation())) {
					result = getAPA(selectedOutputHeader_p);
				}
				if ("MLA".equals(getCitation())) {
					result = getMLA(selectedOutputHeader_p);
				}
				if ("Chicago".equals(getCitation())) {
					result = getChicago(selectedOutputHeader_p);
				}
				defaultAPA_html = result;
				if (staffDetail != null && selectedOutputHeader_p.getPk().getOutput_no() != null) {
					OutputAddl_P addl = pDao.getOutputAddl_P(selectedOutputHeader_p.getPk().getOutput_no(), "M", staffDetail.getStaff_number());
					if (addl != null) {
						if(addl.getCustom_citation() != null) {
							result = addl.getCustom_citation();
							if (!Strings.isNullOrEmpty(result)) {
								result = result.replaceAll("</p><p>","</br>");
								result = result.replaceAll("<p>","");
								result = result.replaceAll("</p>","");
							}
						}
					}
				}
				selectedOutputHeader_p.setApa_html(result);
				if (!Strings.isNullOrEmpty(result)) {
					result = result.replaceAll("\\<[^>]*>","");
					result = StringEscapeUtils.unescapeHtml4(result);
				}
				selectedOutputHeader_p.setApa_citation(result);			
			}
			resetAuthorshipList();
		}
	}
	
	    
	public Boolean isOtherDisArea() {
		Boolean result = false;
		if (selectedOutputHeader_p != null) {
			String lookup_code = selectedOutputHeader_p.getDa_dtl_code();
			if (lookup_code != null) {
				result = (lookup_code.contains("OTHER"))?true:false;
				if (!result) {
					selectedOutputHeader_p.setOther_da_dtl("");
				}
			}
		}
		return result;
	}
	
	public void moveColumnUp(int idx) throws SQLException
	{
		if (!outputDetails_p_list.isEmpty())
		{
			if (idx > 0)
			{
				OutputDetails_P tmp = outputDetails_p_list.get(idx-1);
				outputDetails_p_list.set(idx-1, outputDetails_p_list.get(idx));
				outputDetails_p_list.set(idx, tmp);
			}
		}
	}

	public void moveColumnDown(int idx) throws SQLException
	{
		if (outputDetails_p_list != null)
		{
			if (idx+1 < outputDetails_p_list.size())
			{
				OutputDetails_P tmp = outputDetails_p_list.get(idx+1);
				outputDetails_p_list.set(idx+1, outputDetails_p_list.get(idx));
				outputDetails_p_list.set(idx, tmp);
			}
		}
	}	
	
	public void deleteRow(int idx) throws SQLException
	{
		if (!outputDetails_p_list.isEmpty())
		{
			if (idx < outputDetails_p_list.size()) outputDetails_p_list.remove(idx);
		}
	}
	
	public void updateRowStaffNum(int idx) throws SQLException
	{
		if (outputDetails_p_list != null)
		{
			if (idx > -1)
			{
				OutputDetails_P tmp = outputDetails_p_list.get(idx);
				String staffNum = getPastStaffNumByStaffName(tmp.getAuthorship_name());
				tmp.setAuthorship_staff_no(staffNum);
				outputDetails_p_list.set(idx, tmp);
			}
		}
	}
	
	public void addRow() throws SQLException
	{
		OutputDetails_P p = new OutputDetails_P();
		p.getPk().setOutput_no(selectedOutputHeader_p.getPk().getOutput_no());
		p.getPk().setData_level(getParamDataLevel());
		p.setNon_ied_staff_flag("N");
		p.setAuthorship_type("AUTHOR");
		if (!getOutputDetails_p_list().isEmpty())
		{
			outputDetails_p_list.add(p);
		}else {
			outputDetails_p_list = new ArrayList<OutputDetails_P>();
			outputDetails_p_list.add(p);
		}
	}

	public String getAuthorshipTips()
	{
		if (authorshipTips == null) {
			authorshipTips = sDao.getSysParamValueByCode("AUTHORSHIP_HELP_INFO");
		}
		return authorshipTips;
	}


	public void setAuthorshipTips(String authorshipTips)
	{
		this.authorshipTips = authorshipTips;
	}



	public Boolean requireSubAuthorship(String value) 
	{
		Boolean result = false;
		if (mustHaveSubAuthorship()) {
			if (value.equals("AUTHOR") || value.equals("EDITOR")) {
				result = true;
			}	
		}
		return result;
	}
	
	//save
	public void save() throws Exception
	{
		if (selectedOutputHeader_p != null && selectedOutputHeader_q != null && selectedOutputDetails_q != null) {
			hasError = false;
			//requireOpenAccessPayment();
			if ("M".equals(getParamDataLevel())) {
				save_m(false);
			}
			if ("P".equals(getParamDataLevel())) {
				save_p();
			}
			if ("C".equals(getParamDataLevel())) {
				save_c();
			}
		}
	}
	
	//save and publish
	public void saveAndPublishForm() throws IOException
	{
		if (selectedOutputHeader_p != null && selectedOutputHeader_q != null && selectedOutputDetails_q != null) {
			hasError = false;
			save_m(true);
			publish();
		}
	}
	
	//M level
	public void save_m(boolean doPublish)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		saved = false;
		try {
			selectedOutputHeader_q.setPublish_status("MODIFIED");
			selectedOutputHeader_q.setLast_modified_by(getLoginUserId());
			selectedOutputHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
			
			
			
			selectedOutputHeader_p.getPk().setData_level("M");
			if (StringUtils.isNotBlank(selectedOutputHeader_p.getPage_num_to())){
				selectedOutputHeader_p.setPage_num(selectedOutputHeader_p.getPage_num_from()+"-"+selectedOutputHeader_p.getPage_num_to());
			}else {
				selectedOutputHeader_p.setPage_num(selectedOutputHeader_p.getPage_num_from());
			}
			if (!Strings.isNullOrEmpty(defaultAPA_html)) {
				defaultAPA_html = defaultAPA_html.replaceAll("\\<[^>]*>","");
				defaultAPA_html = StringEscapeUtils.unescapeHtml4(defaultAPA_html);
			}
			
			
			
			selectedOutputHeader_p.setApa_citation(defaultAPA_html);			
			selectedOutputHeader_p.setUserstamp(getLoginUserId());
			selectedOutputHeader_q.setUserstamp(getLoginUserId());
			
			
			
			if(selectedOutputHeader_p.getSdg_code_list() != null) 
				selectedOutputHeader_p.setSdg_code(String.join(",", selectedOutputHeader_p.getSdg_code_list()));
			
			selectedOutputDetails_q.setUserstamp(getLoginUserId());
			
			
			
			
			//Record still can be saved even there is error
			validateRequiredField();
			validateLength();
			validateISBN();
			validateISSN();
			validatePageNo();
			validateOutputDate();
			validateFieldLength();
			validateDisArea();
			
			if (validateOutputDetails_P(staffDetail.getStaff_number())) {
				//Update P, Q
				selectedOutputHeader_q = pDao.updateOutputHeader_Q(selectedOutputHeader_q);
				int currentOutputNo = selectedOutputHeader_q.getOutput_no();
				selectedOutputDetails_q.getPk().setOutput_no(currentOutputNo);
				selectedOutputDetails_q = pDao.updateOutputDetails_Q(selectedOutputDetails_q);		
				selectedOutputHeader_p.getPk().setOutput_no(currentOutputNo);
				selectedOutputHeader_p = pDao.updateOutputHeader_P(selectedOutputHeader_p);
				if (selectedOutputHeader_p != null) {
					//delete all contributors in M level
					pDao.deleteAllContributor(selectedOutputHeader_p.getPk().getOutput_no(), "M");
					int line_no = 1;
					for (OutputDetails_P p:outputDetails_p_list) {
						p.getPk().setOutput_no(currentOutputNo);
						p.getPk().setData_level("M");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getNon_ied_staff_flag().equals("N") && p.getAuthorship_staff_no() != null) {
							StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getAuthorship_staff_no());
							if (s != null) {
								p.setAuthorship_person_id(String.valueOf(s.getPid()));
								p.setAuthorship_name(s.getFullname_save());
							}							
						}
						//set past staff details
						if (p.getNon_ied_staff_flag().equals("F") && p.getAuthorship_staff_no() != null) {
							StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getAuthorship_staff_no());
							p.setAuthorship_name(sp.getFullname_save());
							p.setAuthorship_person_id(String.valueOf(sp.getPid()));
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						pDao.updateOutputDetails_P(p);
						
						//Also create record in outputDetails_q if has staff number
						if (p.getAuthorship_staff_no() != null) {
							OutputDetails_Q tmpDetailsQ = pDao.getOutputDetails_Q1(currentOutputNo, p.getAuthorship_staff_no());
							if (tmpDetailsQ.getPk().getOutput_no() == null) {
								OutputDetails_Q newDetailsQ = new OutputDetails_Q();
								newDetailsQ.getPk().setOutput_no(currentOutputNo);
								newDetailsQ.getPk().setStaff_no(p.getAuthorship_staff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								pDao.updateOutputDetails_Q(newDetailsQ);
							}
						}
						line_no++;
					}
					
					//Update custom citation
					if (selectedOutputAddl_p != null) {
						OutputAddl_P tmpAddlP = pDao.getOutputAddl_P(currentOutputNo, "M", selectedOutputAddl_p.getPk().getStaff_no());
						if (tmpAddlP == null) {
							tmpAddlP = new OutputAddl_P();
							tmpAddlP.getPk().setOutput_no(currentOutputNo);
							tmpAddlP.getPk().setData_level("M");
							tmpAddlP.getPk().setStaff_no(selectedOutputAddl_p.getPk().getStaff_no());
						}
						if ("Y".equals(getHasCustomCitation())) {
							String c = selectedOutputAddl_p.getCustom_citation();
							c = StringEscapeUtils.unescapeHtml4(c);
							tmpAddlP.setCustom_citation(c);
						}else {
							tmpAddlP.setCustom_citation("");
						}
						tmpAddlP.setUserstamp(getLoginUserId());
						pDao.updateOutputAddl_P(tmpAddlP);
					}
					//outputDetails_p_list = null;
					// update importStatus if it is imported
					updateSelectedImportStatus(currentOutputNo);
					
					// Success message
					String message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					
					saved = true;
					
					if (!doPublish) {
						//append no. and data level in url if first time saved the ri
						ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
						String redirectLink = "manageOutput_edit.xhtml?pid="+paramPid+"&no="+selectedOutputHeader_p.getPk().getOutput_no()+"&dataLevel=M";
				    	eCtx.redirect(redirectLink);
					}
				}
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  research output (output_no=" + selectedOutputHeader_q.getOutput_no() + ", staff_no="+ selectedOutputDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save research output (output_no=" + selectedOutputHeader_q.getOutput_no() + ", staff_no="+ selectedOutputDetails_q.getPk().getStaff_no() + ")", e);
		}
		//return destUrl;
	}	
	
	public void publish()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		if ( selectedOutputHeader_q != null && selectedOutputHeader_p != null && selectedOutputDetails_q != null && !outputDetails_p_list.isEmpty()) {
			try {
				//Get C level header
				if (getParamNo() != null) {
					OutputHeader_P selectedOutputHeader_p_c = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "C");
					if (selectedOutputHeader_p_c != null) {
						selectedOutputHeader_q.setCdcf_changed_ind("Y");
					}
				}
				selectedOutputHeader_q.setPublish_status("PUBLISHED");
				int publishFreq = (selectedOutputHeader_q.getPublish_freq() != null)?selectedOutputHeader_q.getPublish_freq()+1:1;
				selectedOutputHeader_q.setPublish_freq(publishFreq);
				selectedOutputHeader_q.setLast_modified_by(getLoginUserId());
				selectedOutputHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
				selectedOutputHeader_q.setLast_published_by(getLoginUserId());
				selectedOutputHeader_q.setLast_published_date(Timestamp.from(Instant.now()));
				
				selectedOutputDetails_q.setUserstamp(getLoginUserId());
				
				if (!getHasError()) {
					//Update P, Q
					selectedOutputHeader_q = pDao.updateOutputHeader_Q(selectedOutputHeader_q);
					int currentOutputNo = selectedOutputHeader_q.getOutput_no();
					
					selectedOutputDetails_q.getPk().setOutput_no(currentOutputNo);
					selectedOutputDetails_q = pDao.updateOutputDetails_Q(selectedOutputDetails_q);
					
					OutputHeader_P selectedOutputHeader_p2 = selectedOutputHeader_p;
					selectedOutputHeader_p2.getPk().setData_level("P");
					selectedOutputHeader_p2.setUserstamp(getLoginUserId());
					OutputHeader_P outputHeader_p_publish = pDao.getOutputHeader_P(currentOutputNo, "P");
					if (outputHeader_p_publish != null) {
						pDao.deleteEntity(OutputHeader_P.class, outputHeader_p_publish.getPk());
						//selectedOutputHeader_p2 = pDao.updateOutputHeader_P(selectedOutputHeader_p2);
						//java.sql.Timestamp sqlDate = new java.sql.Timestamp(outputHeader_p_publish.getCreationDate().getTime());
						//pDao.updateCreateDetails(outputHeader_p_publish.getPk().getOutput_no(), outputHeader_p_publish.getCreator(), sqlDate);
					}
					selectedOutputHeader_p2 = pDao.updateOutputHeader_P(selectedOutputHeader_p2);
					

					//delete all contributors in M and P levels
					//pDao.deleteAllContributor(currentOutputNo, "M");
					pDao.deleteAllContributor(currentOutputNo, "P");
					int line_no = 1;
					for (OutputDetails_P p:outputDetails_p_list) {
						/*p.getPk().setOutput_no(currentOutputNo);
						p.getPk().setData_level("M");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getNon_ied_staff_flag().equals("N") && p.getAuthorship_staff_no() != null) {
							StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getAuthorship_staff_no());
							if (s != null) {
								p.setAuthorship_person_id(String.valueOf(s.getPid()));
								p.setAuthorship_name(s.getFullname());
							}								
						}
						//set past staff details
						if (p.getNon_ied_staff_flag().equals("F") && p.getAuthorship_staff_no() != null) {
							StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getAuthorship_staff_no());
							p.setAuthorship_name(sp.getFullname());
							p.setAuthorship_person_id(String.valueOf(sp.getPid()));
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						pDao.updateOutputDetails_P(p);*/
						OutputDetails_P p2 = p;
						p2.getPk().setData_level("P");
						pDao.updateOutputDetails_P(p2);
						//Create record in outputDetails_q if has staff number
						if (p.getAuthorship_staff_no() != null) {
							OutputDetails_Q tmpDetailsQ = pDao.getOutputDetails_Q1(currentOutputNo, p.getAuthorship_staff_no());
							if (tmpDetailsQ.getPk().getOutput_no() == null) {
								OutputDetails_Q newDetailsQ = new OutputDetails_Q();
								newDetailsQ.getPk().setOutput_no(currentOutputNo);
								newDetailsQ.getPk().setStaff_no(p.getAuthorship_staff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								pDao.updateOutputDetails_Q(newDetailsQ);
							}
						}
						line_no++;
					}
					
					//Update custom citation
					if (selectedOutputAddl_p != null) {
						OutputAddl_P tmpAddlP = pDao.getOutputAddl_P(currentOutputNo, "P", selectedOutputAddl_p.getPk().getStaff_no());
						if (tmpAddlP == null) {
							tmpAddlP = new OutputAddl_P();
							tmpAddlP.getPk().setOutput_no(currentOutputNo);
							tmpAddlP.getPk().setData_level("P");
							tmpAddlP.getPk().setStaff_no(selectedOutputAddl_p.getPk().getStaff_no());
						}
						if ("Y".equals(getHasCustomCitation())) {
							String c = selectedOutputAddl_p.getCustom_citation();
							c = StringEscapeUtils.unescapeHtml4(c);
							tmpAddlP.setCustom_citation(c);
						}else {
							tmpAddlP.setCustom_citation("");
						}
						tmpAddlP.setUserstamp(getLoginUserId());
						pDao.updateOutputAddl_P(tmpAddlP);
					}
					//outputDetails_p_list = null;
					
					
					// update importStatus if it is imported
					//updateSelectedImportStatus(currentOutputNo);
					
					// Success message
					String message = "msg.success.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}else {
					// Failed message
					String message = "msg.err.data.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				}
				if (saved) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageOutput_edit.xhtml?pid="+paramPid+"&no="+selectedOutputHeader_p.getPk().getOutput_no()+"&dataLevel=M";
			    	eCtx.redirect(redirectLink);
				}
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit research output (output_no=" + selectedOutputHeader_q.getOutput_no() + ", staff_no="+ selectedOutputDetails_q.getPk().getStaff_no() + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit research output (output_no=" + selectedOutputHeader_q.getOutput_no() + ", staff_no="+ selectedOutputDetails_q.getPk().getStaff_no() + ")", e);
			}
		}
		//return destUrl;
	}
	//P level
	public void save_p()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {
				if ("Y".equals(selectedOutputHeader_q.getInst_verified_ind()) && selectedOutputHeader_q.getInst_verified_date() == null) {
					selectedOutputHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedOutputHeader_q.getCdcf_status())) {
					selectedOutputHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedOutputHeader_q.getCdcf_status())) {
					selectedOutputHeader_q.setCdcf_processed_ind("Y");
					selectedOutputHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedOutputHeader_q.setCdcf_selected_ind("Y");
					
					//store proc will update the CDCF status
					//selectedOutputHeader_q.setCdcf_status("CDCF_GENERATED");
					//selectedOutputHeader_q.setCdcf_gen_ind("Y");
					//selectedOutputHeader_q.setCdcf_gen_date(getCurrentDate());
				}
				if ("CDCF_NOT_SEL".equals(selectedOutputHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedOutputHeader_q.getCdcf_status())) {
					selectedOutputHeader_q.setCdcf_processed_ind("Y");
					selectedOutputHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedOutputHeader_q.setCdcf_selected_ind("N");
				}
				selectedOutputHeader_q.setUserstamp(getLoginUserId());
				
				
				
				
				//Update Header Q
				selectedOutputHeader_q = pDao.updateOutputHeader_Q(selectedOutputHeader_q);

				int currentOutputNo = selectedOutputHeader_q.getOutput_no();
				
				//Update custom citation
				if (selectedOutputAddl_p != null) {
					OutputAddl_P tmpAddlP = pDao.getOutputAddl_P(currentOutputNo, "P", selectedOutputAddl_p.getPk().getStaff_no());
					if (tmpAddlP == null) {
						tmpAddlP = new OutputAddl_P();
						tmpAddlP.getPk().setOutput_no(currentOutputNo);
						tmpAddlP.getPk().setData_level("P");
						tmpAddlP.getPk().setStaff_no(selectedOutputAddl_p.getPk().getStaff_no());
					}
					if ("Y".equals(getHasCustomCitation())) {
						String c = selectedOutputAddl_p.getCustom_citation();
						c = StringEscapeUtils.unescapeHtml4(c);
						tmpAddlP.setCustom_citation(c);
					}else {
						tmpAddlP.setCustom_citation("");
					}
					tmpAddlP.setUserstamp(getLoginUserId());
					pDao.updateOutputAddl_P(tmpAddlP);
				}
				
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentOutputNo);
				
				// Success message
				String message;
				if ("CDCF_PROCESSED".equals(selectedOutputHeader_q.getCdcf_status())) {
					takeSnapshot();
					boolean publishToSAP = true;
					String failSAPMsg = null;
					if (publishToSAP) {
						pDao.publishToSAP(selectedOutputHeader_q.getOutput_no(), "RH_UPLOAD_OUTPUT_P");
						failSAPMsg = checkSAPError();
					}
					
					//Fail SAP message
					if (failSAPMsg != null)
					{
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, failSAPMsg, ""));
					}
					
					message = "msg.success.save.generate.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageOutput_edit.xhtml?no="+currentOutputNo+"&dataLevel=P";
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}
			
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  output (output_no=" + selectedOutputHeader_q.getOutput_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  output (output_no=" + selectedOutputHeader_q.getOutput_no() + ")", e);
		}	
	}
	
	//C level
	public void save_c()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		boolean publishToSAP = false;
		try {
			validateRequiredField();
			validateLength();
			validateISBN();
			validateISSN();
			validatePageNo();
			validateOutputDate();
			validateDisArea();

			if (validateOutputDetails_P(getRealRiCreatorStaffNo()) && !getHasError()) {
				if ("Y".equals(selectedOutputHeader_q.getInst_verified_ind()) && selectedOutputHeader_q.getInst_verified_date() == null) {
					selectedOutputHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedOutputHeader_q.getCdcf_status())) {
					selectedOutputHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedOutputHeader_q.getCdcf_status())) {
					selectedOutputHeader_q.setCdcf_processed_ind("Y");
					selectedOutputHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedOutputHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedOutputHeader_q.getCdcf_status()) || "CDCF_SPEC".equals(selectedOutputHeader_q.getCdcf_status())) {
					selectedOutputHeader_q.setCdcf_processed_ind("Y");
					selectedOutputHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedOutputHeader_q.setCdcf_selected_ind("N");
				}
				
				if ("CDCF_GENERATED".equals(selectedOutputHeader_q.getCdcf_status()) || "CDCF_PROCESSED".equals(selectedOutputHeader_q.getCdcf_status())) {
					publishToSAP = true;
				}
				selectedOutputHeader_q.setUserstamp(getLoginUserId());
				
				//Update P, Q
				selectedOutputHeader_q = pDao.updateOutputHeader_Q(selectedOutputHeader_q);
				if (!Strings.isNullOrEmpty(defaultAPA_html)) {
					defaultAPA_html = defaultAPA_html.replaceAll("\\<[^>]*>","");
					defaultAPA_html = StringEscapeUtils.unescapeHtml4(defaultAPA_html);
				}
				selectedOutputHeader_p.setApa_citation(defaultAPA_html);	
				selectedOutputHeader_p.setUserstamp(getLoginUserId());
				if (publishToSAP) {
					String census_date = sDao.getSysParamValueByCode("CENSUS_DATE");
					selectedOutputHeader_p.setCensus_date(stringToDate(census_date));
				}
				selectedOutputHeader_p = pDao.updateOutputHeader_P(selectedOutputHeader_p);
				
				pDao.deleteAllContributor(selectedOutputHeader_q.getOutput_no(), "C");
				int line_no = 1;
				for (OutputDetails_P p:outputDetails_p_list) {
					p.getPk().setData_level("C");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getNon_ied_staff_flag().equals("N") && p.getAuthorship_staff_no() != null) {
						StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getAuthorship_staff_no());
						if (s != null) {
							p.setAuthorship_person_id(String.valueOf(s.getPid()));
							p.setAuthorship_name(s.getFullname_save());
						}								
					}
					//set past staff details
					if (p.getNon_ied_staff_flag().equals("F") && p.getAuthorship_staff_no() != null) {
						StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getAuthorship_staff_no());
						if (sp != null) {
							p.setAuthorship_name(sp.getFullname_save());
							p.setAuthorship_person_id(String.valueOf(sp.getPid()));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					pDao.updateOutputDetails_P(p);
					//Create record in Details_q if has staff number
					if (p.getAuthorship_staff_no() != null) {
						OutputDetails_Q tmpDetailsQ = pDao.getOutputDetails_Q1(selectedOutputHeader_p.getPk().getOutput_no(), p.getAuthorship_staff_no());
						if (tmpDetailsQ.getPk().getOutput_no() == null) {
							OutputDetails_Q newDetailsQ = new OutputDetails_Q();
							newDetailsQ.getPk().setOutput_no(selectedOutputHeader_p.getPk().getOutput_no());
							newDetailsQ.getPk().setStaff_no(p.getAuthorship_staff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							pDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;
				}
				
				int currentRiNo = selectedOutputHeader_q.getOutput_no();
				
				//Update custom citation
				if (selectedOutputAddl_p != null) {
					OutputAddl_P tmpAddlP = pDao.getOutputAddl_P(currentRiNo, "C", selectedOutputAddl_p.getPk().getStaff_no());
					if (tmpAddlP == null) {
						tmpAddlP = new OutputAddl_P();
						tmpAddlP.getPk().setOutput_no(currentRiNo);
						tmpAddlP.getPk().setData_level("C");
						tmpAddlP.getPk().setStaff_no(selectedOutputAddl_p.getPk().getStaff_no());
					}
					if ("Y".equals(getHasCustomCitation())) {
						String c = selectedOutputAddl_p.getCustom_citation();
						c = StringEscapeUtils.unescapeHtml4(c);
						tmpAddlP.setCustom_citation(c);
					}else {
						tmpAddlP.setCustom_citation("");
					}
					tmpAddlP.setUserstamp(getLoginUserId());
					pDao.updateOutputAddl_P(tmpAddlP);
				}
				
				// update importStatus if it is imported
				//updateSelectedImportStatus(currentRiNo);
				
				// Success message
				String message;
				if (publishToSAP) {
					pDao.publishToSAP(selectedOutputHeader_q.getOutput_no(), "RH_UPLOAD_OUTPUT_P");
					String failSAPMsg = checkSAPError();

					//Fail SAP message
					if (failSAPMsg != null)
					{
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, failSAPMsg, ""));
					}else {
						message = "msg.success.save.generate.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageOutput_edit.xhtml?no="+currentRiNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "manageOutput_edit.xhtml?no="+currentRiNo+"&dataLevel=C";
			    	eCtx.redirect(redirectLink);
				}
				
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Research Ouput");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save output (output_no=" + selectedOutputHeader_q.getOutput_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  output (output_no=" + selectedOutputHeader_q.getOutput_no() + ")", e);
		}
	}
	
	public void submitConsent()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		if ( selectedOutputDetails_q != null) {
			try {
				selectedOutputDetails_q.setUserstamp(getLoginUserId());
				selectedOutputDetails_q = pDao.updateOutputDetails_Q(selectedOutputDetails_q);
				
				//Update custom citation
				if (selectedOutputAddl_p != null) {
					//M level
					OutputAddl_P tmpAddlP = pDao.getOutputAddl_P(selectedOutputDetails_q.getPk().getOutput_no(), "M", selectedOutputAddl_p.getPk().getStaff_no());
					if (tmpAddlP == null) {
						tmpAddlP = new OutputAddl_P();
						tmpAddlP.getPk().setOutput_no(selectedOutputDetails_q.getPk().getOutput_no());
						tmpAddlP.getPk().setData_level("M");
						tmpAddlP.getPk().setStaff_no(selectedOutputAddl_p.getPk().getStaff_no());
					}
					if ("Y".equals(getHasCustomCitation())) {
						String c = selectedOutputAddl_p.getCustom_citation();
						c = StringEscapeUtils.unescapeHtml4(c);
						tmpAddlP.setCustom_citation(c);
					}else {
						tmpAddlP.setCustom_citation("");
					}
					tmpAddlP.setUserstamp(getLoginUserId());
					pDao.updateOutputAddl_P(tmpAddlP);
					//P level
					tmpAddlP = pDao.getOutputAddl_P(selectedOutputDetails_q.getPk().getOutput_no(), "P", selectedOutputAddl_p.getPk().getStaff_no());
					if (tmpAddlP == null) {
						tmpAddlP = new OutputAddl_P();
						tmpAddlP.getPk().setOutput_no(selectedOutputDetails_q.getPk().getOutput_no());
						tmpAddlP.getPk().setData_level("P");
						tmpAddlP.getPk().setStaff_no(selectedOutputAddl_p.getPk().getStaff_no());
					}
					if ("Y".equals(getHasCustomCitation())) {
						String c = selectedOutputAddl_p.getCustom_citation();
						c = StringEscapeUtils.unescapeHtml4(c);
						tmpAddlP.setCustom_citation(c);
					}else {
						tmpAddlP.setCustom_citation("");
					}
					tmpAddlP.setUserstamp(getLoginUserId());
					pDao.updateOutputAddl_P(tmpAddlP);
				}
				
				// Success message
				String message = "msg.success.submit.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Consent");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedOutputDetails_q + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedOutputDetails_q + ")", e);
			}
		}
	}	
	
	public String takeSnapshot()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String destUrl = "";
		boolean publishToSAP = false;
		String failSAPMsg = null;
		if (selectedOutputHeader_p != null && outputDetails_p_list != null && selectedOutputHeader_q != null) {
			try {
				if ("CDCF_GENERATED".equals(selectedOutputHeader_q.getCdcf_status())) {
					publishToSAP = true;
				}
				int currentRiNo = selectedOutputHeader_p.getPk().getOutput_no();
				OutputHeader_P selectedOutputHeader_p_c = pDao.getOutputHeader_P(currentRiNo, "C");
				if (selectedOutputHeader_p_c != null) {
					pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p_c.getPk());
				}
				selectedOutputHeader_p_c = selectedOutputHeader_p;
				selectedOutputHeader_p_c.getPk().setData_level("C");
				selectedOutputHeader_p_c.setUserstamp(getLoginUserId());
				selectedOutputHeader_p_c.setCreator(getLoginUserId());
				if (publishToSAP) {
					String census_date = sDao.getSysParamValueByCode("CENSUS_DATE");
					selectedOutputHeader_p_c.setCensus_date(stringToDate(census_date));
				}
				selectedOutputHeader_p = pDao.updateOutputHeader_P(selectedOutputHeader_p_c);
				
				pDao.deleteAllContributor(currentRiNo, "C");
				for (OutputDetails_P p:outputDetails_p_list) {
					OutputDetails_P p2 = p;
					p2.getPk().setData_level("C");
					p2.setCreator(getLoginUserId());
					p2.setUserstamp(getLoginUserId());
					pDao.updateOutputDetails_P(p2);
				}
				
				if (publishToSAP) {
					pDao.publishToSAP(selectedOutputHeader_q.getOutput_no(), "RH_UPLOAD_OUTPUT_P");
					failSAPMsg = checkSAPError();
				}
				
				//Fail SAP message
				if (failSAPMsg != null)
				{
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, failSAPMsg, ""));
				}
				
				// Success message
				String message = "msg.success.create.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Snapshot");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				destUrl = redirect("manageOutput_edit") + "&no=" + currentRiNo + "&dataLevel=C";	
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Output No.:" + selectedOutputHeader_p.getPk().getOutput_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Output No.:" + selectedOutputHeader_p.getPk().getOutput_no() + ")", e);
			}
		}
		return redirect(destUrl);
	}	
	
	//Delete all levels
	public String deleteForm() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (!Strings.isNullOrEmpty(paramNo)) {
			try {
				//Get C level header
				OutputHeader_P selectedOutputHeader_p_c = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "C");
				
				//In M level page
				/*if ("M".equals(getParamDataLevel())) {
					if (selectedOutputHeader_p_c == null) {
						//P
						selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "P");
						if (selectedOutputHeader_p != null) {
							pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p.getPk());
						}
						//M
						OutputHeader_P selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "M");
						if (selectedOutputHeader_p != null) {
							pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p.getPk());
						}
						
						//M and P levels
						pDao.deleteAllContributor(Integer.valueOf(paramNo), "M");
						pDao.deleteAllContributor(Integer.valueOf(paramNo), "P");
						
						//Header Q and Details Q
						pDao.deleteEntity(OutputHeader_Q.class, Integer.valueOf(paramNo));
						pDao.deleteOutputDetails_Q(Integer.valueOf(paramNo));
							
						// Success message
						String message = "msg.success.delete.x";
						message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					}else {
						String message = "Research Output is already generated to SAP, so it can not be deleted. ";
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					}
				}*/
				//In P or C level page
				/*if ("P".equals(getParamDataLevel()) || "C".equals(getParamDataLevel())){
					//C
					if (selectedOutputHeader_p_c != null) {
						pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p_c.getPk());
					}
					//P
					selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "P");
					if (selectedOutputHeader_p != null) {
						pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p.getPk());
					}
					//M
					OutputHeader_P selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "M");
					if (selectedOutputHeader_p != null) {
						pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p.getPk());
					}
					
					//M and P and C levels
					pDao.deleteAllContributor(Integer.valueOf(paramNo), "M");
					pDao.deleteAllContributor(Integer.valueOf(paramNo), "P");
					pDao.deleteAllContributor(Integer.valueOf(paramNo), "C");
					
					//Header Q and Details Q
					pDao.deleteEntity(OutputHeader_Q.class, Integer.valueOf(paramNo));
					pDao.deleteOutputDetails_Q(Integer.valueOf(paramNo));
					
					// Success message
					String message = "msg.success.delete.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}*/
				
				//C
				if (selectedOutputHeader_p_c != null) {
					pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p_c.getPk());
				}
				//P
				selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "P");
				if (selectedOutputHeader_p != null) {
					pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p.getPk());
				}
				//M
				OutputHeader_P selectedOutputHeader_p = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "M");
				if (selectedOutputHeader_p != null) {
					pDao.deleteEntity(OutputHeader_P.class, selectedOutputHeader_p.getPk());
				}
				
				//M and P and C levels
				pDao.deleteAllContributor(Integer.valueOf(paramNo), "M");
				pDao.deleteAllContributor(Integer.valueOf(paramNo), "P");
				pDao.deleteAllContributor(Integer.valueOf(paramNo), "C");
				
				//Header Q and Details Q
				pDao.deleteEntity(OutputHeader_Q.class, Integer.valueOf(paramNo));
				pDao.deleteOutputDetails_Q(Integer.valueOf(paramNo));
				
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Research Output");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete output (No.: " + selectedOutputHeader_q.getOutput_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot delete output (No.: " + selectedOutputHeader_q.getOutput_no() + ")", e);
			}
		}
		String destUrl = redirect("manageOutput");
		if(paramPid != null)
			destUrl += "&pid=" + paramPid;
		return redirect(destUrl);
	}
	
	public String getRealRiCreatorStaffNo()
	{
		OutputDetails_Q tmp = pDao.getOutputDetails_Q_creator(Integer.valueOf(paramNo));
		if (tmp != null) {
			return tmp.getPk().getStaff_no();
		}
		return null;
	}
	
	public String getRiCreatorStaffNo()
	{
		if (riCreatorStaffNo == null) {
			OutputDetails_Q tmp = pDao.getOutputDetails_Q_creator(Integer.valueOf(paramNo));
			riCreatorStaffNo = (tmp!= null)?tmp.getPk().getStaff_no():getCurrentUserId();
		}
		return riCreatorStaffNo;
	}

	
	public void setRiCreatorStaffNo(String riCreatorStaffNo)
	{
		this.riCreatorStaffNo = riCreatorStaffNo;
	}

	public boolean isRiCreator(String staff_no) 
	{
		boolean result = false;
		String realRiCreatorStaffNo = ("M".equals(getParamDataLevel()))? getRiCreatorStaffNo():getRealRiCreatorStaffNo();
		if (!Strings.isNullOrEmpty(staff_no) && !Strings.isNullOrEmpty(realRiCreatorStaffNo)) {
			result = (realRiCreatorStaffNo.equals(staff_no))?true:false;
		}
		return result;
	}	
	
	public boolean checkSnapshotExists() 
	{
		boolean result = false;
		OutputHeader_P tmp = pDao.getOutputHeader_P(Integer.valueOf(paramNo), "C");
		if (tmp != null) {
			result = true;
		}
		return result;	
	}
	
	public ImportRIOutput getOutputPanel()
	{
	
		if(outputPanel == null) {
			outputPanel = new ImportRIOutput();
			outputPanel.setParamPid(getParamPid());
			
	
		}
		return outputPanel;
	}


	public void ignoreOutput() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedOutput = dao.getStatusByPK(getOutputPanel().getSelectedIgnoreOutput());
		if(selectedOutput != null) {
			selectedOutput.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedOutput);
			getOutputPanel().setOutputList(null);
		}
	}

	
	public ImportRIOutputV getSelectedImportOutput()
	{
		if(selectedImportOutput == null && getParamArea_code() != null
				&& getParamSource_id() != null && getParamStaff_number() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			ImportRIOutputV_PK pk = new ImportRIOutputV_PK();
			pk.setArea_code(getParamArea_code());
			pk.setSource_id(getParamSource_id());
			pk.setStaff_number(getParamStaff_number());
			selectedImportOutput = dao.getImportRIOutputByPK(pk);
		}
		
		return selectedImportOutput;
	}
	
	public void postValidate() throws IOException
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		List<FacesMessage> errorMsgList = fCtx.getMessageList();

		/*if (fCtx.isValidationFailed())
		{
			String message = "msg.err.number.of.error.x";
			message = MessageFormat.format(getResourceBundle().getString(message), errorMsgList.size());

			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}*/
	}
	
	public boolean validateLength()
	{	
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message;
		if (selectedOutputHeader_p != null) {
			//Other Disciplinary Area
			if (Strings.isNullOrEmpty(selectedOutputHeader_p.getOther_da_dtl()) == false) {
				byte[] bytes = selectedOutputHeader_p.getOther_da_dtl().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 150) {
					result = false;
					message = "Other Disciplinary Area is too long.";
					fCtx.addMessage("editForm:other_da_dtl", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedOutputHeader_p.setOther_da_dtl(selectedOutputHeader_p.getOther_da_dtl().trim());
				}
			}
			//Details of the Output 1st line
			if (Strings.isNullOrEmpty(selectedOutputHeader_p.getOther_details()) == false) {
				byte[] bytes = selectedOutputHeader_p.getOther_details().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 500) {
					result = false;
					message = "Details of the Output 1st line is too long.";
					fCtx.addMessage("editForm:other_details", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedOutputHeader_p.setOther_details(selectedOutputHeader_p.getOther_details().trim());
				}
			}
			//Details of the Output 2nd line
			if (Strings.isNullOrEmpty(selectedOutputHeader_p.getOther_details_continue()) == false) {
				byte[] bytes = selectedOutputHeader_p.getOther_details_continue().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 500) {
					result = false;
					message = "Details of the Output 2nd line is too long.";
					fCtx.addMessage("editForm:other_details_continue", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedOutputHeader_p.setOther_details_continue(selectedOutputHeader_p.getOther_details_continue().trim());
				}
			}
			//List of editor(s) in sequential order as appeared in the research output
			if (Strings.isNullOrEmpty(selectedOutputHeader_p.getName_other_editors()) == false) {
				byte[] bytes = selectedOutputHeader_p.getName_other_editors().getBytes(StandardCharsets.UTF_8);
				if (bytes.length > 500) {
					result = false;
					message = "List of editor(s) in sequential order as appeared in the research output is too long.";
					fCtx.addMessage("editForm:name_other_editors", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedOutputHeader_p.setName_other_editors(selectedOutputHeader_p.getName_other_editors().trim());
				}
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	//validate name list
	public boolean validateOutputDetails_P(String staff_no){
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		boolean yourself = false;
		boolean mustHaveFirstAuthor = false;
		boolean mustHaveFirstEditor = false;
		boolean mustHaveSub = mustHaveSubAuthorship();
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		String concatAuthorName = "";
		int countAuthor = 0;
		int countFirstAuthor = 0;
		int countFirstEditor = 0;
		int countCorrAuthor = 0;
		String strMaxAuthor =  sDao.getSysParamValueByCode("MAX_AUTHOR_LIST_LENGTH");
		int maxAuthor = 31;
		if (strMaxAuthor != null) {
			maxAuthor = Integer.valueOf(strMaxAuthor) + 1;
		}
		
		if (selectedOutputHeader_p != null) {

			if (mustHaveSub && selectedOutputHeader_p.getSap_output_type().equals("150") == false) {
				mustHaveFirstAuthor = true;
			}
			
			//Edited book (editor)
			if (mustHaveSub && selectedOutputHeader_p.getSap_output_type().equals("150")) {
				mustHaveFirstEditor = true;
			}
			
			if (outputDetails_p_list != null) {
				HashSet unique=new HashSet();
				if (!Strings.isNullOrEmpty(staff_no)) {
					//for loop contributor list
					for (OutputDetails_P p:outputDetails_p_list) {
						int lineNo = countAuthor + 1;

						//get staff details
						if ("N".equals(p.getNon_ied_staff_flag())) {
							if (p.getAuthorship_staff_no() == null) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
								allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")")+"<br/>";
								fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getAuthorship_staff_no());
								if (s != null) {
									p.setAuthorship_name(s.getFullname_save());
								}	
							}					
						}
						
						//get past staff details						
						if ("F".equals(p.getNon_ied_staff_flag())) {
							if (p.getAuthorship_name() == null) {
								result = false;
								message = "Contributor (no. "+lineNo+") is not correct.";
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getAuthorship_staff_no());
								if (sp != null) {
									//p.setAuthorship_staff_no(sp.getStaff_number());
									//p.setAuthorship_name(sp.getFullname_save());
								}else {
									result = false;
									//message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
									message = "Contributor (no. "+lineNo+") is not correct.";
									allMessage += "- "+message+"<br/>";
									fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
								}
							}
						}
						
						//check name is not null
						if ("Y".equals(p.getNon_ied_staff_flag()) || "S".equals(p.getNon_ied_staff_flag())) {
							if (Strings.isNullOrEmpty(p.getAuthorship_name())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
								allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")")+"<br/>";
								fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								byte[] nameBytes = p.getAuthorship_name().getBytes(StandardCharsets.UTF_8);
								if (nameBytes.length > 80) {
									result = false;
									message = "Contributor (no. "+lineNo+") is too long.";
									allMessage += "- Contributor (no. "+lineNo+") is too long.<br/>";
									fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
								}else {
									p.setAuthorship_person_id(null);
									p.setAuthorship_staff_no(null);
									p.setAuthorship_assignment_id(null);
								}								
							}
						}
						//check duplicate
						if (!unique.add(p.getAuthorship_staff_no()) && p.getAuthorship_staff_no() != null){
							result = false;
							if (!Strings.isNullOrEmpty(p.getAuthorship_name())) {
								message = "Collaborative Contributor - Staff ("+p.getAuthorship_name()+") cannot be duplicated.";
								allMessage += "- Collaborative Contributor - Staff ("+p.getAuthorship_name()+") cannot be duplicated.<br/>";
								fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								message = "Collaborative Contributor cannot be duplicated.";
								allMessage += "- Collaborative Contributor cannot be duplicated.<br/>";
								fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
						}
					
						boolean removeSub = true;
						if (mustHaveSub) {
							if ("AUTHOR".equals(p.getAuthorship_type())) {
								if ("FIRST".equals(p.getAuthorship_dtl_type()) || "FIRST CO".equals(p.getAuthorship_dtl_type())){
									countFirstAuthor++;
									removeSub = false;
								}
								if ("CORRESPONDING".equals(p.getAuthorship_dtl_type())){
									countCorrAuthor++;
									removeSub = false;
								}
								if ("CO".equals(p.getAuthorship_dtl_type())){
									removeSub = false;
								}
							}
							if ("EDITOR".equals(p.getAuthorship_type())) {
								if ("FIRST".equals(p.getAuthorship_dtl_type()) || "FIRST CO".equals(p.getAuthorship_dtl_type())){
									countFirstEditor++;
									removeSub = false;
								}
								if ("CORRESPONDING".equals(p.getAuthorship_dtl_type()) || "CO".equals(p.getAuthorship_dtl_type())){
									removeSub = false;
								}
							}
						}
						
						if (removeSub) {
							p.setAuthorship_dtl_type(null);
						}
						
						countAuthor++;
						
						if (countAuthor < maxAuthor) {
							String tmpStaffNo = (p.getAuthorship_staff_no() == null)?", ":" (" + p.getAuthorship_staff_no() + "), ";
							concatAuthorName += p.getAuthorship_name() + tmpStaffNo + p.getAuthorship_type() + "; ";
						}
						if (countAuthor == maxAuthor) {
							concatAuthorName += "*";
						}
						
						if (p.getAuthorship_staff_no() != null) {
							if (staff_no.equals(p.getAuthorship_staff_no())) {
								yourself = true;
							}
						}	
					}
				}
			}else {
				result = false;
				message = "There must be one Contributor.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		if (yourself == false && "M".equals(getParamDataLevel())) {
			result = false;
			message = "You must be one of the Collaborative Contributors.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (yourself == false && "M".equals(getParamDataLevel()) == false) {
			result = false;
			message = "Creator must be one of the Collaborative Contributors.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		/*if (countFirstAuthor > 1) {
			result = false;
			message = "Only one First Author is allowed.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (countCorrAuthor > 1) {
			result = false;
			message = "Only one Corresponding Author is allowed.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}*/
		if (mustHaveFirstAuthor && countFirstAuthor < 1) {
			result = false;
			message = "There must be a First Author.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (mustHaveFirstEditor && countFirstEditor < 1) {
			result = false;
			message = "There must be a First Editor.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		if (selectedOutputHeader_p != null) {
			if (!concatAuthorName.isEmpty()) {
				if (concatAuthorName.length() > 900) {
					concatAuthorName = StringUtils.substring(concatAuthorName, 0, 900);
					concatAuthorName += "*";
				}
			}
			selectedOutputHeader_p.setTotal_no_of_author(countAuthor);
			selectedOutputHeader_p.setConcatenated_author_name(concatAuthorName);
		}
		
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	public boolean validateDisArea()
	{	
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message;
		if (selectedOutputHeader_p != null) {
			if (selectedOutputHeader_p.getSch_dtl_code() != null && selectedOutputHeader_p.getDa_dtl_code() != null) {
				String eduParentLookupCode = getEduSectorParentLookupCode(selectedOutputHeader_p.getSch_dtl_code());
				String disAreaParentLookupCode = getDisAreaParentLookupCode(selectedOutputHeader_p.getDa_dtl_code());
				if ("3".equals(eduParentLookupCode) && !"EDU".equals(disAreaParentLookupCode)) {
					result = false;
				}
				if (!"3".equals(eduParentLookupCode) && "EDU".equals(disAreaParentLookupCode)) {
					result = false;
				}
			}
		}
		if (!result) {
			hasError = true;
			message = "Sector and Disciplinary Area of the Output are not matched.";
			fCtx.addMessage("editForm:da_dtl_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}
		return result;
	}
	
	//validation
	public boolean validateOutputDate() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message;
		if (selectedOutputHeader_p != null) {
			if (selectedOutputHeader_p.getFrom_year() != null && selectedOutputHeader_p.getTo_year() != null) {
				if (selectedOutputHeader_p.getFrom_year() > selectedOutputHeader_p.getTo_year()) {
					message = "From Date cannot be greater than To Date.";
					fCtx.addMessage("editForm:from_year", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					result = false;
				}/*else if (selectedOutputHeader_p.getFrom_year().equals(selectedOutputHeader_p.getTo_year())){
					if (selectedOutputHeader_p.getFrom_month() != null && selectedOutputHeader_p.getTo_month() != null) {
						if (selectedOutputHeader_p.getFrom_month() >= selectedOutputHeader_p.getTo_month()) {
							message = "From Date cannot be greater than or equal to To Date.";
							fCtx.addMessage("editForm:from_year", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
							result = false;
						}
					}
				}*/
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;	
	}
	
	//validation
	public Boolean validateISBN() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ISBNValidator validator = ISBNValidator.getInstance();
		if (StringUtils.isNotBlank(selectedOutputHeader_p.getIsbn())) {
			if (!validator.isValid(selectedOutputHeader_p.getIsbn())) {
				result = false;
				message = "Invalid ISBN.";
				fCtx.addMessage("editForm:isbn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	//validation
	public Boolean validateISSN() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ISSNValidator validator = ISSNValidator.getInstance();
		String issn = selectedOutputHeader_p.getIssn().trim();
		if (StringUtils.isNotBlank(issn)) {
			if (!issn.equals("0000-0000")) {
				if (!validator.isValid(issn)) {
					result = false;
					message = "Invalid ISSN.";
					fCtx.addMessage("editForm:issn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedOutputHeader_p.setIssn(issn);
				}
			}
		}
		String eissn = selectedOutputHeader_p.getEissn().trim();
		if (StringUtils.isNotBlank(eissn)) {
			if (!eissn.equals("0000-0000")) {
				if (!validator.isValid(eissn)) {
					result = false;
					message = "Invalid EISSN.";
					fCtx.addMessage("editForm:eissn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
				}else {
					selectedOutputHeader_p.setEissn(eissn);
				}
			}
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	//validation
	public Boolean validatePageNo() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (!Strings.isNullOrEmpty(selectedOutputHeader_p.getPage_num_from()) && !Strings.isNullOrEmpty(selectedOutputHeader_p.getPage_num_to())){
			selectedOutputHeader_p.setPage_num(selectedOutputHeader_p.getPage_num_from()+"-"+selectedOutputHeader_p.getPage_num_to());
			if (isStringInt(selectedOutputHeader_p.getPage_num_from()) && isStringInt(selectedOutputHeader_p.getPage_num_to())) {
				Integer pageNumFrom = Integer.valueOf(selectedOutputHeader_p.getPage_num_from());
				Integer pageNumTo = Integer.valueOf(selectedOutputHeader_p.getPage_num_to());
				if (pageNumFrom != null && pageNumTo != null) {
					if (pageNumFrom > pageNumTo) {
						result = false;
						message = "Invalid page no.";
						fCtx.addMessage("editForm:page_num_from", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
					}
				}
			}
		}else {
			selectedOutputHeader_p.setPage_num(selectedOutputHeader_p.getPage_num_from()+selectedOutputHeader_p.getPage_num_to());
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	//validate field length	
	public Boolean validateFieldLength() throws IOException {
		boolean result = true;
		String message;
		String allMessage = "";

		return result;
	}
	
	//validate mandatory field	
	public Boolean validateRequiredField() {
		boolean result = true;
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		//Generation Information
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getSap_output_type())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Research Output Type");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:sap_output_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getSap_refered_journal())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Type of Research Activity");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:sap_refered_journal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getIs_intl_conf()) && requireIntCon()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Is international conference?");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:is_intl_conf", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getIs_rgc_proj()) && requireIsRgcProj()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Is the project(s) for creating the research output fully/partially funded by RGC?");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:is_rgc_proj", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getRgc_proj_num()) && requireRgcProjNum()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Project number(s) of the RGC funded project(s).");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:rgc_proj_num", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getIs_enh_high_edu())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Is this research output related to the enhancement of teaching and learning in higher education/teacher education?");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:is_enh_high_edu", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Output Information
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getTitle_jour_book())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "1st line of Title of Research Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:title_jour_book", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedOutputHeader_p.setTitle_jour_book(selectedOutputHeader_p.getTitle_jour_book().trim());
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getTitle_paper_art())) {
			if (requireNameOfPublication()) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of Publication /Conference/Journal in which the output appears");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:title_paper_art", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}else {
			selectedOutputHeader_p.setTitle_paper_art(selectedOutputHeader_p.getTitle_paper_art().trim());
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getIssn().trim()) && Strings.isNullOrEmpty(selectedOutputHeader_p.getEissn().trim()) && "160".equals(selectedOutputHeader_p.getSap_output_type())) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "ISSN");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:issn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getVol_issue()) && requireVolumn()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Volume (Issue)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:vol_issue", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getPage_num_from()) && Strings.isNullOrEmpty(selectedOutputHeader_p.getArticle_num())  && requirePageNo()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Page No.");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:page_num_from", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedOutputHeader_p.getFrom_month() == null && selectedOutputHeader_p.getFrom_year() != null) {
			if (selectedOutputHeader_p.getFrom_year() >= getCurrentYear()-1) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "From month");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:from_month", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		if (selectedOutputHeader_p.getFrom_year() == null) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "From year");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:from_year", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getCity())) {
			if (requirePlace()) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Place");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:place", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}else {
			selectedOutputHeader_p.setCity(selectedOutputHeader_p.getCity().trim());
		}
		if (requireOpenAccess()){
			if (Strings.isNullOrEmpty(selectedOutputHeader_p.getOpen_access_stat()) && getIsRdoAdmin() == false) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Open Access Status");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:open_access_stat", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}else {
			selectedOutputHeader_p.setOpen_access_stat(null);
		}
		
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getPublisher())) {
			if (requirePublisher()) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Publisher/Conference Organiser(s)/Others");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:publisher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}else {
			selectedOutputHeader_p.setPublisher(selectedOutputHeader_p.getPublisher().trim());
		}
		
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getName_other_editors())) {
			if (requireEditor()) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "List of editor(s)");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:name_other_editors", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}else {
			selectedOutputHeader_p.setName_other_editors(selectedOutputHeader_p.getName_other_editors().trim());
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getName_other_pos())) {
			if (requireAuthor()) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "List of author(s) / contributor(s)");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:name_other_pos", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}	
		}else {
			selectedOutputHeader_p.setName_other_pos(selectedOutputHeader_p.getName_other_pos().trim());
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getSch_dtl_code()) && getIsRdoAdmin() == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Sector of the Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:sch_dtl_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedOutputHeader_p.setSch_code(getEduSectorParentLookupCode(selectedOutputHeader_p.getSch_dtl_code()));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getDa_dtl_code()) && getIsRdoAdmin() == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Disciplinary Area of the Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:da_dtl_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedOutputHeader_p.setDa_code(getDisAreaParentLookupCode(selectedOutputHeader_p.getDa_dtl_code()));
		}
		if (Strings.isNullOrEmpty(selectedOutputHeader_p.getOther_da_dtl()) && isOtherDisArea()) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Other Disciplinary Area");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:other_da_dtl", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	public String getCitation()
	{
		if (citation == null) {			
			citation = "APA";
			if (!Strings.isNullOrEmpty(getParamPid())) {
				int pid = Integer.valueOf(paramPid);
				CvDAO cvDao = CvDAO.getInstance();
				StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_CITATION");
				if (obj != null) {
					citation= obj.getDisplayType();
				}
			}
		}
		return citation;
	}

	
	public void setCitation(String citation)
	{
		this.citation = citation;
	}


	
	public String getDefaultAPA_html()
	{
		genCitation();
		return defaultAPA_html;
	}


	
	public void setDefaultAPA_html(String defaultAPA_html)
	{
		this.defaultAPA_html = defaultAPA_html;
	}
	
}


