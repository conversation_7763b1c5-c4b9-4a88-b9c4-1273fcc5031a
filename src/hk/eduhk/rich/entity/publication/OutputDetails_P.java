package hk.eduhk.rich.entity.publication;

import java.sql.SQLException;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;



@Entity
@Table(name = "RH_P_ACAD_PROF_OUTPUT_DTL")
public class OutputDetails_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(OutputDetails_P.class.toString());
	
	@EmbeddedId
	private OutputDetails_P_PK pk = new OutputDetails_P_PK();
	
	@Column(name = "authorship_assignment_id")
	private Integer authorship_assignment_id;

	@Column(name = "authorship_dtl_type")
	private String authorship_dtl_type;	
	
	@Column(name = "authorship_name")
	private String authorship_name;

	@Column(name = "authorship_person_id")
	private String authorship_person_id;
	
	@Column(name = "authorship_staff_no")
	private String authorship_staff_no;
	
	@Column(name = "authorship_type")
	private String authorship_type;
	
	@Column(name = "collab_percent")
	private Integer collab_percent;
	
	@Column(name = "non_ied_staff_flag")
	private String non_ied_staff_flag;
	
	@Transient
	private Double cdcf_weighting;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "output_no", referencedColumnName = "output_no", nullable = false, insertable = false, updatable = false)
	private OutputHeader_Q outputHeader_q;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "output_no", referencedColumnName = "output_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "data_level", referencedColumnName = "data_level", nullable = false, insertable = false, updatable = false)
	})
	private OutputHeader_P outputHeader_p;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "output_no", referencedColumnName = "output_no", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "authorship_staff_no", referencedColumnName = "staff_no", nullable = false, insertable = false, updatable = false)
	})
	private OutputDetails_Q outputDetails_q;
	
	
	public OutputDetails_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(OutputDetails_P_PK pk)
	{
		this.pk = pk;
	}


	
	public Integer getAuthorship_assignment_id()
	{
		return authorship_assignment_id;
	}


	
	public void setAuthorship_assignment_id(Integer authorship_assignment_id)
	{
		this.authorship_assignment_id = authorship_assignment_id;
	}



	public String getAuthorship_dtl_type()
	{
		return authorship_dtl_type;
	}


	
	public void setAuthorship_dtl_type(String authorship_dtl_type)
	{
		this.authorship_dtl_type = authorship_dtl_type;
	}


	
	public String getAuthorship_name()
	{
		return authorship_name;
	}


	
	public void setAuthorship_name(String authorship_name)
	{
		this.authorship_name = authorship_name;
	}


	
	public String getAuthorship_person_id()
	{
		return authorship_person_id;
	}


	
	public void setAuthorship_person_id(String authorship_person_id)
	{
		this.authorship_person_id = authorship_person_id;
	}


	
	public String getAuthorship_staff_no()
	{
		return authorship_staff_no;
	}


	
	public void setAuthorship_staff_no(String authorship_staff_no)
	{
		this.authorship_staff_no = authorship_staff_no;
	}


	
	public String getAuthorship_type()
	{
		return authorship_type;
	}


	
	public void setAuthorship_type(String authorship_type)
	{
		this.authorship_type = authorship_type;
	}


	
	public Integer getCollab_percent()
	{
		return collab_percent;
	}


	
	public void setCollab_percent(Integer collab_percent)
	{
		this.collab_percent = collab_percent;
	}


	
	public String getNon_ied_staff_flag()
	{
		return non_ied_staff_flag;
	}


	
	public void setNon_ied_staff_flag(String non_ied_staff_flag)
	{
		this.non_ied_staff_flag = non_ied_staff_flag;
	}

	public OutputHeader_Q getOutputHeader_q()
	{
		if (outputHeader_q != null) {
			try {
				outputHeader_q.getOutput_no();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PublicationDAO dao = PublicationDAO.getInstance();
					outputHeader_q = dao.getOutputHeader_Q(getPk().getOutput_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return outputHeader_q;
	}

	
	public OutputHeader_P getOutputHeader_p()
	{
		if (outputHeader_p != null) {
			try {
				outputHeader_p.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PublicationDAO dao = PublicationDAO.getInstance();
					outputHeader_p = dao.getOutputHeader_P(getPk().getOutput_no(), getPk().getData_level());
				}
				else
				{
					throw re;
				}
			}
		}
		return outputHeader_p;
	}

	
	public OutputDetails_Q getOutputDetails_q()
	{
		if (outputDetails_q != null) {
			try {
				outputDetails_q.getPk();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PublicationDAO dao = PublicationDAO.getInstance();
					outputDetails_q = dao.getOutputDetails_Q1(getPk().getOutput_no(), getAuthorship_staff_no());
				}
				else
				{
					throw re;
				}
			}
		}
		return outputDetails_q;
	}
	

	
	public Double getCdcf_weighting()
	{
		if (cdcf_weighting == null) {
			try
			{
				PublicationDAO dao = PublicationDAO.getInstance();
				cdcf_weighting = dao.getOutputCdcfWeighting(getPk().getOutput_no(), getPk().getData_level(), getPk().getLine_no());
			}
			catch (SQLException e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		return cdcf_weighting;
	}




	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		OutputDetails_P other = (OutputDetails_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "OutputDetails_P [pk=" + pk + ", authorship_assignment_id=" + authorship_assignment_id
				+ ", authorship_dtl_type=" + authorship_dtl_type + ", authorship_name=" + authorship_name
				+ ", authorship_person_id=" + authorship_person_id + ", authorship_staff_no=" + authorship_staff_no
				+ ", authorship_type=" + authorship_type + ", collab_percent=" + collab_percent
				+ ", non_ied_staff_flag=" + non_ied_staff_flag + "]";
	}




}
