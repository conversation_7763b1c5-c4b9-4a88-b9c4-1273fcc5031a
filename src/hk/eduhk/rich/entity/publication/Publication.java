package hk.eduhk.rich.entity.publication;

import java.sql.Timestamp;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;

import javax.persistence.Column;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.bundle.MessageBundle;
import hk.eduhk.rich.entity.BaseRIFull;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;

@SuppressWarnings("serial")
public class Publication extends BaseRIFull
{

	private int pid; // DO NOT REMOVE THIS, even the parent class has this field.
	private int outputNo; // DO NOT REMOVE THIS, even the parent class has this field.

	private String staffNumber;
	private String staffFullname;
	private String staffDept;
	private String name;
	private String articleTitle;
	private String articleTitleContinue;
	private String title;
	private String volIssue;
	private String pageNum;
	private String city;
	private String fromMonth;
	private String fromYear;
	private String toMonth;
	private String toYear;
	private String otherDetails;
	private String otherDetailsContinue;
	private String language;
	private String sapOutputCat;
	private String sapOutputType;
	private String educationSector;
	private String educationSectorDetail;
	private String researchArea;
	private String researchAreaDetail;
	private String keyResearchAreas;
	private String keyResearchAreasOther;
	private String researchType;
	private String disciplinaryArea;
	private String disciplinaryAreaDetail;
	private String disciplinaryAreaOther;
	private String priority;
	private String nameOtherEditors;
	private String IEdWorkInd;
	private String researchActivityRanking;

	private String sapOutputCatDesc;
	private String sapOutputTypeDesc;
	private String eduSecDesc;
	private String researchAreaDesc;
	private String keyResearchAreaDesc;
	private String disAreaDesc;
	private String sapReferedJournalDesc;

	private String reposURL;
	private String reposConfirm;
	private String reposFetch;
	private Timestamp reposLastFetch;

	private String scholarCat;
	private String assessType;
	private String submitRef;
	private String reqSubjPanel;
	private String abstractData;

	private String riPublishStatus;
	private String doubleWeighted;
	private int backupRI;
	private String riDesc;
	private String riDesc2;
	private String otherOutputDesc;
	private String dblWgtJustify;
	private String raeInternalRating;

	private String toBeUpdated;

	// private String reqSecondCs;
	private String intDiscOutput;
	private String priCostCentre;
	private String secondCostCentre;

	private String submissionFormat;
	private String engTitle;

	private int countFulltext;
	private int countAbstract;
	private int countAbsEng;
	private int countAccept;

	private String numOfPage;

	private String isEnhHighEdu;

	private String engAuthorList;
	private String keyword1;
	private String keyword2;
	private String keyword3;
	private String keyword4;
	private String keyword5;

	private List detailList;
	private String isIntlConf;
	private String journalPublicationDiscipline;
	private String journalPublicationDisciplineDesc;
	// enh035
	private String authorshipType;
	private String authorshipDtlType;
	// enh035

	private String apaCitation;

	private String parentLookupCode;

	// for RAE 2020
	private String isR20;

	private String infoComp;
	private String selType;
	private String staffFaculty;
	private String uoaCode;
	private String uoaDesc;

	private Integer totalNoOfAuthor;
	private String journalCode;

	private String researchAreaOfRo;
	private String subDiscCode;
	private String outputType;
	private String urlAdditional;
	private String keyAdditional;
	private String othOutputType;
	private String titleOfEngOutput;
	private String nonEngOutputInd;
	private String relLang;
	private String othLang;
	private String titleOfNonEngOutput;
	private String surnameOfEngAuthor;
	private String givenNameOfEngAuthor;
	private String nameOfNonEngAuthor;
	private String coAuthoredInd;
	private String noOfCoAuthor;
	private String explanationOfAuthorCtb;
	private String listOfAuthorEng;
	private String indOthStaff;
	private String assStaffRO;
	private String indCoAuthoredStaff;
	private String listOfAuthorNonEng;
	private String publishedCensusDate;
	private String isbn;
	private String issn;
	private String firstPublishedDate;
	private String publisher;
	private String urlSupport;
	private String keySupport;
	private String bookTitle;
	private String issueNo;
	private String pageNo;
	private String descLocOutput;
	private String formatFullVerSubmit;
	private String noOfTypeSubmit;
	private String roWithDoiInd;
	private String doi;
	private String urlFullVer;
	private String keyFullVer;
	private String urlOaFullVer;
	private String fileTypeOfFullVer;
	private String fileSize;
	private String nonTradOutputInd;
	private String infoOfNonTradOutput;
	private String dwRequestInd;
	private String justDwRequest;
	private String reserveItemDwInd;
	private String interResearch;
	private String priResearchAreaInter;
	private String secResearchAreaInter;
	private String toc;
	private String indToc;
	private String urlToc;
	private String keyToc;
	private String additionalInfo;
	private String keywordOutput1;
	private String keywordOutput2;
	private String keywordOutput3;
	private String keywordOutput4;
	private String keywordOutput5;
	private String ircHasResult;
	private String panel2SubSpecDct;
	private String panel8SubSequentEdt;
	private String panel8IndStmt;
	private String panel10Explanation;
	private String panel10RoInd;
	private String panel10UrlEvidence;
	private String panel10KeyEvidence;
	private String panel10PtbInd;
	private String panel10ExplainPtb;
	private String panel11SubDiscInfo;
	private String panel12UrlPubLoc;
	private String panel12UrlEvidence;
	private String panel12KeyEvidence;

	private String inpRemarks;

	private String citationChkCode;
	private String citationChkDesc;
	private String citationChkAbsCode;
	private String citationChkAbsDesc;
	private String citationChkFulltextCode;
	private String citationChkFulltextDesc;

	private String copyrightClrCode;
	private String copyrightClrDesc;

	private String roRemarks;
	private String roRemarksLib;
	private String urlRef;

	private int phyAudioQty;
	private int phyCdQty;
	private int phyDvdQty;
	private int phyPhotoQty;
	private int phyBookQty;
	private int phyUsbQty;
	private int phyOtherQty;
	private String phyOtherType;

	// for RAE 2020 (Final Spec)
	private String raeStatusCode;
	private String raeStatusCodePanel;
	private String raeStatusCodeFullVer;
	private String raeStatusCodeOthInfo;
	private String raeStatusIneligible;

	private String raeStatusDesc;
	private String raeStatusPanelDesc;
	private String raeStatusFullVerDesc;
	private String raeStatusOthInfoDesc;
	private String raeStatusIneligibleDesc;

	private String panel10RevInd;
	private String panel10RevExplain;
	private String panel10OpenUrl;
	private String noSglCoWork;
	private String uniEndorseConfInd;
	private String justSglCoWork;

	private String pseudonym;
	private String roleSubmitStaff;

	// for RAE 2020 (Final Spec 2.21)
	private String eissn;
	private String articleNo;

	// upload count
	private int countFileSupDoc;
	private int countFileFullVer;
	private int countFileAbstract;
	private int countFileP10Ev;
	private int countFileP12Ev;
	private int countFileAddInfo;
	private int countFileCoWork;
	
	
	// for RH-ADM-006 Research Information Listing
	private String authorListWithDeptAndAuthType;
	private String authorship;
	private String department;
	private String sapReferedJournal;
	private String url;
	private String pageNumFrom;
	private String pageNumTo;
	private String nameOtherPos;
	private String schCode;
	private String schDtlCode;
	private String daCode;
	private String daDtlCode;
	private String otherDaDtl;
	
	private String cdcf_selected_ind;
	
	//ENH-002
	private String is_rgc_proj;
	private String rgc_proj_num;
	private String open_access_stat;
	private String open_access_statStr;
	private String open_access_apc;
	private String open_access_apc_payment;
	private String apc_val;
	private String open_access_art_acc_date;
	private String open_access_art_acc_day;
	private String open_access_art_acc_month;
	private String open_access_art_acc_year;
	private String open_access_emb_end_date;
	private String open_access_emb_end_month;
	private String open_access_emb_end_year;
	private String open_access_emb_period_date;
	private String open_access_emb_period_month;
	private String open_access_emb_period_year;
	private String open_access_payment;
	private Date cal_emb_end_date;
	private String emb_date_after_census;
	private String delay_open_access;
	private String is_tran_agrt;
	private String tran_agrt_val;
	
	// RH-RPT
	private String exceed_char_ind;
	private String accessibility;
	private List<OutputDetails_P> pDtlList;
	private int noPDtl;
	private Double weighting;
	private int dcc_percentage;
	
	//ENH 033
	private String sdg_str;
	
	
	
	public Publication()
	{
	}


	public int getOutputNo()
	{
		return getRINo();
	}


	public void setOutputNo(int outputNo)
	{
		this.outputNo = outputNo;
		setRINo(outputNo);
	}


	public String getStaffNumber()
	{
		return staffNumber;
	}


	public void setStaffNumber(String staffNumber)
	{
		this.staffNumber = staffNumber;
	}


	public String getName()
	{
		return name;
	}


	public void setName(String name)
	{
		this.name = name;
	}


	public String getTitle()
	{
		return title;
	}


	public void setTitle(String title)
	{
		this.title = title;
	}


	public String getArticleTitle()
	{
		return articleTitle;
	}


	public void setArticleTitle(String articleTitle)
	{
		this.articleTitle = articleTitle;
	}


	public String getArticleTitleContinue()
	{
		return articleTitleContinue;
	}


	public void setArticleTitleContinue(String articleTitleContinue)
	{
		this.articleTitleContinue = articleTitleContinue;
	}


	public String getVolIssue()
	{
		return volIssue;
	}


	public void setVolIssue(String volIssue)
	{
		this.volIssue = volIssue;
	}


	public String getPageNum()
	{
		return pageNum;
	}


	public void setPageNum(String pageNum)
	{
		this.pageNum = pageNum;
	}


	public String getCity()
	{
		return city;
	}


	public void setCity(String city)
	{
		this.city = city;
	}


	public String getFromYear()
	{
		return fromYear;
	}


	public void setFromYear(String fromYear)
	{
		this.fromYear = fromYear;
	}


	public String getFromMonth()
	{
		return fromMonth;
	}


	public void setFromMonth(String fromMonth)
	{
		this.fromMonth = fromMonth;
	}


	public String getToYear()
	{
		return toYear;
	}


	public void setToYear(String toYear)
	{
		this.toYear = toYear;
	}


	public String getToMonth()
	{
		return toMonth;
	}


	public void setToMonth(String toMonth)
	{
		this.toMonth = toMonth;
	}


	public String getPublisher()
	{
		return publisher;
	}


	public void setPublisher(String publisher)
	{
		this.publisher = publisher;
	}


	public String getOtherDetails()
	{
		return otherDetails;
	}


	public void setOtherDetails(String otherDetails)
	{
		this.otherDetails = otherDetails;
	}


	public String getOtherDetailsContinue()
	{
		return otherDetailsContinue;
	}


	public void setOtherDetailsContinue(String otherDetailsContinue)
	{
		this.otherDetailsContinue = otherDetailsContinue;
	}


	public String getLanguage()
	{
		return language;
	}


	public void setLanguage(String language)
	{
		this.language = language;
	}


	public String getSapOutputCat()
	{
		return sapOutputCat;
	}


	public void setSapOutputCat(String sapOutputCat)
	{
		this.sapOutputCat = sapOutputCat;
	}


	public String getSapOutputType()
	{
		return sapOutputType;
	}


	public void setSapOutputType(String sapOutputType)
	{
		this.sapOutputType = sapOutputType;
	}


	public String getEducationSector()
	{
		return educationSector;
	}


	public void setEducationSector(String educationSector)
	{
		this.educationSector = educationSector;
	}


	public String getEducationSectorDetail()
	{
		return educationSectorDetail;
	}


	public void setEducationSectorDetail(String educationSectorDetail)
	{
		this.educationSectorDetail = educationSectorDetail;
	}


	public String getDisciplinaryArea()
	{
		return disciplinaryArea;
	}


	public void setDisciplinaryArea(String disciplinaryArea)
	{
		this.disciplinaryArea = disciplinaryArea;
	}


	public String getDisciplinaryAreaDetail()
	{
		return disciplinaryAreaDetail;
	}


	public void setDisciplinaryAreaDetail(String disciplinaryAreaDetail)
	{
		this.disciplinaryAreaDetail = disciplinaryAreaDetail;
	}


	public String getDisciplinaryAreaOther()
	{
		return disciplinaryAreaOther;
	}


	public void setDisciplinaryAreaOther(String disciplinaryAreaOther)
	{
		this.disciplinaryAreaOther = disciplinaryAreaOther;
	}


	public String getResearchArea()
	{
		return researchArea;
	}


	public void setResearchArea(String researchArea)
	{
		this.researchArea = researchArea;
	}


	public String getResearchAreaDetail()
	{
		return researchAreaDetail;
	}


	public void setResearchAreaDetail(String researchAreaDetail)
	{
		this.researchAreaDetail = researchAreaDetail;
	}


	public String getKeyResearchAreas()
	{
		return keyResearchAreas;
	}


	public void setKeyResearchAreas(String keyResearchAreas)
	{
		this.keyResearchAreas = keyResearchAreas;
	}


	public String getKeyResearchAreasOther()
	{
		return keyResearchAreasOther;
	}


	public void setKeyResearchAreasOther(String keyResearchAreasOther)
	{
		this.keyResearchAreasOther = keyResearchAreasOther;
	}


	public String getResearchType()
	{
		return researchType;
	}


	public String getResearchTypeDesc()
	{
		String researchTypeDesc = "";

		if ("Y".equals(researchType))
		{
			researchTypeDesc = "Academic Research - Refereed";
		}
		else if ("N".equals(researchType))
		{
			researchTypeDesc = "Academic Research - Not Refereed";
		}
		else if ("C".equals(researchType))
		{
			researchTypeDesc = "Contract Research";
		}
		else if ("99".equals(researchType))
		{
			researchTypeDesc = "Others";
		}
		else
		{
			researchTypeDesc = researchType;
		}
		;

		return researchTypeDesc;
	}


	public void setResearchType(String researchType)
	{
		this.researchType = researchType;
	}



	private void copyR20ToRich()
	{
		if ("CHI".equals(this.relLang))
		{
			this.language = "C";
			this.articleTitle = this.titleOfNonEngOutput;
			this.name = this.listOfAuthorNonEng.replace(';', ',').replaceAll("\\*", "");
		}
		else
		{
			this.language = "E";
			this.articleTitle = this.titleOfEngOutput;
			this.name = this.listOfAuthorEng.replace(';', ',').replaceAll("\\*", "");
		}

		this.title = this.bookTitle;

	}





	public void setPriority(String priority)
	{
		this.priority = priority;
	}


	public String getPriority()
	{
		return priority;
	}


	public void setNameOtherEditors(String nameOtherEditors)
	{
		this.nameOtherEditors = nameOtherEditors;
	}


	public String getNameOtherEditors()
	{
		return nameOtherEditors;
	}


	public void setIEdWorkInd(String IEdWorkInd)
	{
		this.IEdWorkInd = IEdWorkInd;
	}


	public String getIEdWorkInd()
	{
		return IEdWorkInd;
	}


	public void setResearchActivityRanking(String researchActivityRanking)
	{
		this.researchActivityRanking = researchActivityRanking;
	}


	public String getResearchActivityRanking()
	{
		return researchActivityRanking;
	}


	public void setSapOutputTypeDesc(String sapOutputTypeDesc)
	{
		this.sapOutputTypeDesc = sapOutputTypeDesc;
	}


	public String getSapOutputTypeDesc()
	{
		return sapOutputTypeDesc;
	}


	public void setReposURL(String reposURL)
	{
		this.reposURL = reposURL;
	}


	public String getReposURL()
	{
		return reposURL;
	}


	public void setReposConfirm(String reposConfirm)
	{
		this.reposConfirm = reposConfirm;
	}


	public String getReposConfirm()
	{
		return reposConfirm;
	}


	public void setReposFetch(String reposFetch)
	{
		this.reposFetch = reposFetch;
	}


	public String getReposFetch()
	{
		return reposFetch;
	}


	public void setReposLastFetch(Timestamp reposLastFetch)
	{
		this.reposLastFetch = reposLastFetch;
	}


	public Timestamp getReposLastFetch()
	{
		return reposLastFetch;
	}


	public void setPid(int pid)
	{
		this.pid = pid;
	}


	public int getPid()
	{
		return pid;
	}


	@Override
	public String toString()
	{
		return "Publication [pid=" + pid + ", outputNo=" + outputNo + ", staffNumber=" + staffNumber + ", name=" + name
				+ ", pageNum=" + pageNum + ", sapOutputTypeDesc=" + sapOutputTypeDesc + ", detailList=" + detailList
				+ ", isIntlConf=" + isIntlConf + ", journalPublicationDiscipline=" + journalPublicationDiscipline + "]";
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + "\"" + escape("pid") + "\":" + "\"" + escape(String.valueOf(getPid())) + "\"," + "\""
				+ escape("RINo") + "\":" + "\"" + escape(String.valueOf(getRINo())) + "\"," + "\"" + escape("outputNo")
				+ "\":" + "\"" + escape(String.valueOf(getRINo())) + "\"," + "\"" + escape("staffNumber") + "\":" + "\""
				+ escape(StringUtils.defaultString(getStaffNumber())) + "\"," + "\"" + escape("name") + "\":" + "\""
				+ escape(StringUtils.defaultString(getName())) + "\"," + "\"" + escape("articleTitle") + "\":" + "\""
				+ escape(StringUtils.defaultString(getArticleTitle())) + "\"," + "\"" + escape("articleTitleContinue")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getArticleTitleContinue())) + "\"," + "\""
				+ escape("title") + "\":" + "\"" + escape(StringUtils.defaultString(getTitle())) + "\"," + "\""
				+ escape("volIssue") + "\":" + "\"" + escape(StringUtils.defaultString(getVolIssue())) + "\"," + "\""
				+ escape("pageNum") + "\":" + "\"" + escape(StringUtils.defaultString(getPageNum())) + "\"," + "\""
				+ escape("city") + "\":" + "\"" + escape(StringUtils.defaultString(getCity())) + "\"," + "\""
				+ escape("fromMonth") + "\":" + "\"" + escape(StringUtils.defaultString(getFromMonth())) + "\"," + "\""
				+ escape("toYear") + "\":" + "\"" + escape(StringUtils.defaultString(getToYear())) + "\"," + "\""
				+ escape("toMonth") + "\":" + "\"" + escape(StringUtils.defaultString(getToMonth())) + "\"," + "\""
				+ escape("fromYear") + "\":" + "\"" + escape(StringUtils.defaultString(getFromYear())) + "\"," + "\""
				+ escape("otherDetails") + "\":" + "\"" + escape(StringUtils.defaultString(getOtherDetails())) + "\","
				+ "\"" + escape("otherDetailsContinue") + "\":" + "\""
				+ escape(StringUtils.defaultString(getOtherDetailsContinue())) + "\"," + "\"" + escape("publisher")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getPublisher())) + "\"," + "\"" + escape("language")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getLanguage())) + "\"," + "\""
				+ escape("sapOutputCat") + "\":" + "\"" + escape(StringUtils.defaultString(getSapOutputCat())) + "\","
				+ "\"" + escape("sapOutputType") + "\":" + "\"" + escape(StringUtils.defaultString(getSapOutputType()))
				+ "\"," + "\"" + escape("educationSector") + "\":" + "\""
				+ escape(StringUtils.defaultString(getEducationSector())) + "\"," + "\""
				+ escape("educationSectorDetail") + "\":" + "\""
				+ escape(StringUtils.defaultString(getEducationSectorDetail())) + "\"," + "\"" + escape("researchArea")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getResearchArea())) + "\"," + "\""
				+ escape("researchAreaDetail") + "\":" + "\""
				+ escape(StringUtils.defaultString(getResearchAreaDetail())) + "\"," + "\"" + escape("keyResearchAreas")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getKeyResearchAreas())) + "\"," + "\""
				+ escape("keyResearchAreasOther") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeyResearchAreasOther())) + "\"," + "\""
				+ escape("researchActivityRanking") + "\":" + "\""
				+ escape(StringUtils.defaultString(getResearchActivityRanking())) + "\"," + "\"" + escape("issn")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getIssn())) + "\"," + "\"" + escape("researchType")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getResearchType())) + "\"," + "\""
				+ escape("disciplinaryArea") + "\":" + "\"" + escape(StringUtils.defaultString(getDisciplinaryArea()))
				+ "\"," + "\"" + escape("disciplinaryAreaDetail") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDisciplinaryAreaDetail())) + "\"," + "\""
				+ escape("disciplinaryAreaOther") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDisciplinaryAreaOther())) + "\"," + "\"" + escape("priority")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getPriority())) + "\"," + "\""
				+ escape("nameOtherEditors") + "\":" + "\"" + escape(StringUtils.defaultString(getNameOtherEditors()))
				+ "\"," + "\"" + escape("IEdWorkInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getIEdWorkInd())) + "\"," + "\"" + escape("isIntlConf") + "\":"
				+ "\"" + escape(StringUtils.defaultString(getIsIntlConf())) + "\"," + "\""
				+ escape("journalPublicationDiscipline") + "\":" + "\""
				+ escape(StringUtils.defaultString(getJournalPublicationDiscipline())) + "\"," + "\""
				+ escape("sapOutputTypeDesc") + "\":" + "\"" + escape(StringUtils.defaultString(getSapOutputTypeDesc()))
				+ "\"");

		// Split the pageNum into fromPage & toPage.
		String[] fromToPage = (!GenericValidator.isBlankOrNull(pageNum)) ? pageNum.split("-") : null;
		if (fromToPage != null)
		{
			if (fromToPage.length > 0)
				strBuf.append(",\"" + escape("fromPage") + "\":" + "\""
						+ escape(StringUtils.defaultString(fromToPage[0])) + "\"");
			if (fromToPage.length > 1)
				strBuf.append(",\"" + escape("toPage") + "\":" + "\"" + escape(StringUtils.defaultString(fromToPage[1]))
						+ "\"");
		}
		else
		{
			strBuf.append(",\"" + escape("fromPage") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("toPage") + "\":" + "\"\"");
		}

		// Split the issn
		String[] issnArr = (!GenericValidator.isBlankOrNull(issn)) ? issn.split("-") : null;
		if (issnArr != null)
		{
			if (issnArr.length > 0)
				strBuf.append(
						",\"" + escape("issn1") + "\":" + "\"" + escape(StringUtils.defaultString(issnArr[0])) + "\"");
			if (issnArr.length > 1)
				strBuf.append(
						",\"" + escape("issn2") + "\":" + "\"" + escape(StringUtils.defaultString(issnArr[1])) + "\"");
		}
		else
		{
			strBuf.append(",\"" + escape("issn1") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("issn2") + "\":" + "\"\"");
		}

		// Split the eissn
		String[] eissnArr = (!GenericValidator.isBlankOrNull(eissn)) ? eissn.split("-") : null;
		if (eissnArr != null)
		{
			if (eissnArr.length > 0)
				strBuf.append(",\"" + escape("eissn1") + "\":" + "\"" + escape(StringUtils.defaultString(eissnArr[0]))
						+ "\"");
			if (eissnArr.length > 1)
				strBuf.append(",\"" + escape("eissn2") + "\":" + "\"" + escape(StringUtils.defaultString(eissnArr[1]))
						+ "\"");
		}
		else
		{
			strBuf.append(",\"" + escape("eissn1") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("eissn2") + "\":" + "\"\"");
		}

		// Split isbn
		if (isbn != null && isbn.length() == 13)
		{
			strBuf.append(",\"" + escape("isbn1") + "\":" + "\""
					+ escape(StringUtils.defaultString(isbn.substring(0, 3))) + "\"");
			strBuf.append(",\"" + escape("isbn2") + "\":" + "\""
					+ escape(StringUtils.defaultString(isbn.substring(3, 4))) + "\"");
			strBuf.append(",\"" + escape("isbn3") + "\":" + "\""
					+ escape(StringUtils.defaultString(isbn.substring(4, 6))) + "\"");
			strBuf.append(",\"" + escape("isbn4") + "\":" + "\""
					+ escape(StringUtils.defaultString(isbn.substring(6, 12))) + "\"");
			strBuf.append(",\"" + escape("isbn5") + "\":" + "\""
					+ escape(StringUtils.defaultString(isbn.substring(12, 13))) + "\"");
		}

		if ("N/A".equals(isbn))
		{
			strBuf.append(",\"" + escape("isbn1") + "\":" + "\"" + "N/A" + "\"");
		}

		if (isbn == null)
		{
			strBuf.append(",\"" + escape("isbn1") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("isbn2") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("isbn3") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("isbn4") + "\":" + "\"\"");
			strBuf.append(",\"" + escape("isbn5") + "\":" + "\"\"");
		}

		strBuf.append(",\"" + escape("publishStatus") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPublishStatus())) + "\"" + ",\"" + escape("lastModifiedBy")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getLastModifiedBy())) + "\"" + ",\""
				+ escape("lastModifiedDate") + "\":"
				+ (getLastModifiedDate() != null ? "new Date(" + getLastModifiedDate().getTime() + ")" : "\"\"") + ",\""
				+ escape("lastPublishedBy") + "\":" + "\"" + escape(StringUtils.defaultString(getLastPublishedBy()))
				+ "\"" + ",\"" + escape("lastPublishedDate") + "\":"
				+ (getLastPublishedDate() != null ? "new Date(" + getLastPublishedDate().getTime() + ")" : "\"\"")
				+ ",\"" + escape("instDisplayInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getInstDisplayInd())) + "\"" + ",\"" + escape("instVerifiedInd")
				+ "\":" + "\"" + escape(StringUtils.defaultString(getInstVerifiedInd())) + "\"" + ",\""
				+ escape("instVerifiedDate") + "\":"
				+ (getInstVerifiedDate() != null ? "new Date(" + getInstVerifiedDate().getTime() + ")" : "\"\"") + ",\""
				+ escape("CDCFStatus") + "\":" + "\"" + escape(StringUtils.defaultString(getCDCFStatus())) + "\""
				+ ",\"" + escape("CDCFGenInd") + "\":" + "\"" + escape(StringUtils.defaultString(getCDCFGenInd()))
				+ "\"" + ",\"" + escape("CDCFGenDate") + "\":"
				+ (getCDCFGenDate() != null ? "new Date(" + getCDCFGenDate().getTime() + ")" : "\"\"") + ",\""
				+ escape("CDCFProcessInd") + "\":" + "\"" + escape(StringUtils.defaultString(getCDCFProcessInd()))
				+ "\"" + ",\"" + escape("CDCFProcessDate") + "\":"
				+ (getCDCFProcessDate() != null ? "new Date(" + getCDCFProcessDate().getTime() + ")" : "\"\"") + ",\""
				+ escape("CDCFSelectedInd") + "\":" + "\"" + escape(StringUtils.defaultString(getCDCFSelectedInd()))
				+ "\"" + ",\"" + escape("CDCFChangedInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getCDCFChangedInd())) + "\"" + ",\"" + escape("bulletinInd") + "\":"
				+ "\"" + escape(StringUtils.defaultString(getBulletinInd())) + "\"" + ",\"" + escape("remarks") + "\":"
				+ "\"" + escape(StringUtils.defaultString(getRemarks())) + "\"");

		strBuf.append(",\"" + escape("reposURL") + "\":" + "\"" + escape(StringUtils.defaultString(getReposURL()))
				+ "\"" + ",\"" + escape("reposConfirm") + "\":" + "\""
				+ escape(StringUtils.defaultString(getReposConfirm())) + "\"" + ",\"" + escape("reposFetch") + "\":"
				+ "\"" + escape(StringUtils.defaultString(getReposFetch())) + "\"" + ",\"" + escape("reposLastFetch")
				+ "\":" + (getReposLastFetch() != null ? "new Date(" + getReposLastFetch().getTime() + ")" : "\"\""));

		strBuf.append(",\"" + escape("consentInd") + "\":" + "\"" + escape(StringUtils.defaultString(getConsentInd()))
				+ "\"" + ",\"" + escape("displayInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDisplayInd())) + "\"" + ",\"" + escape("creatorInd") + "\":"
				+ "\"" + escape(StringUtils.defaultString(getCreatorInd())) + "\"");

		if (getRIDetailList() != null)
		{
			strBuf.append(",");
			strBuf.append("\"" + escape("detailList") + "\":" + getRIDetailList().toString());
		}

		if (getRIAttachmentList() != null)
		{
			strBuf.append(",");
			strBuf.append("\"" + escape("attachmentList") + "\":" + getRIAttachmentList().toString());
		}

		strBuf.append(",\"" + escape("scholarCat") + "\":" + "\"" + escape(StringUtils.defaultString(getScholarCat()))
				+ "\"");
		strBuf.append(",\"" + escape("assessType") + "\":" + "\"" + escape(StringUtils.defaultString(getAssessType()))
				+ "\"");
		strBuf.append(
				",\"" + escape("submitRef") + "\":" + "\"" + escape(StringUtils.defaultString(getSubmitRef())) + "\"");
		strBuf.append(",\"" + escape("reqSubjPanel") + "\":" + "\""
				+ escape(StringUtils.defaultString(getReqSubjPanel())) + "\"");
		strBuf.append(",\"" + escape("abstractData") + "\":" + "\""
				+ escape(StringUtils.defaultString(getAbstractData())) + "\"");
		strBuf.append(",\"" + escape("doi") + "\":" + "\"" + escape(StringUtils.defaultString(getDoi())) + "\"");
		strBuf.append(",\"" + escape("isbn") + "\":" + "\"" + escape(StringUtils.defaultString(getIsbn())) + "\"");

		strBuf.append(
				",\"" + escape("copyright") + "\":" + "\"" + escape(StringUtils.defaultString(getCopyright())) + "\"");
		strBuf.append(",\"" + escape("raeRIStatus") + "\":" + "\"" + escape(StringUtils.defaultString(getRaeRIStatus()))
				+ "\"");
		strBuf.append(",\"" + escape("raeRIStatusDesc") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRaeRIStatusDesc())) + "\"");
		strBuf.append(
				",\"" + escape("staffSelected") + "\":" + "\"" + escape(String.valueOf(getStaffSelected())) + "\"");
		strBuf.append(",\"" + escape("staffSubmitted") + "\":" + "\""
				+ escape(StringUtils.defaultString(getStaffSubmitted())) + "\"");
		strBuf.append(",\"" + escape("staffSubmitDate") + "\":"
				+ (getStaffSubmitDate() != null ? "new Date(" + getStaffSubmitDate().getTime() + ")" : "\"\""));
		strBuf.append(",\"" + escape("headApproval") + "\":" + "\""
				+ escape(StringUtils.defaultString(getHeadApproval())) + "\"");
		strBuf.append(",\"" + escape("headApprovalDate") + "\":"
				+ (getHeadApprovalDate() != null ? "new Date(" + getHeadApprovalDate().getTime() + ")" : "\"\""));
		strBuf.append(",\"" + escape("deanApproval") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDeanApproval())) + "\"");
		strBuf.append(",\"" + escape("deanApprovalDate") + "\":"
				+ (getDeanApprovalDate() != null ? "new Date(" + getDeanApprovalDate().getTime() + ")" : "\"\""));
		strBuf.append(",\"" + escape("relatedRI") + "\":" + "\"" + escape(String.valueOf(getRelatedRI())) + "\"");
		strBuf.append(",\"" + escape("raeInternalRating") + "\":" + "\""
				+ escape(String.valueOf(getRaeInternalRating())) + "\"");
		strBuf.append(",\"" + escape("markDelete") + "\":" + "\"" + escape(StringUtils.defaultString(getMarkDelete()))
				+ "\"");

		strBuf.append(",\"" + escape("riPublishStatus") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRiPublishStatus())) + "\"");
		strBuf.append(",\"" + escape("doubleWeighted") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDoubleWeighted())) + "\"");
		strBuf.append(",\"" + escape("backupRI") + "\":" + "\"" + escape(String.valueOf(getBackupRI())) + "\"");
		strBuf.append(",\"" + escape("riDesc") + "\":" + "\"" + escape(StringUtils.defaultString(getRiDesc())) + "\"");
		strBuf.append(
				",\"" + escape("riDesc2") + "\":" + "\"" + escape(StringUtils.defaultString(getRiDesc2())) + "\"");
		strBuf.append(",\"" + escape("otherOutputDesc") + "\":" + "\""
				+ escape(StringUtils.defaultString(getOtherOutputDesc())) + "\"");
		strBuf.append(",\"" + escape("dblWgtJustify") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDblWgtJustify())) + "\"");

		strBuf.append(",\"" + escape("toBeUpdated") + "\":" + "\"" + escape(StringUtils.defaultString(getToBeUpdated()))
				+ "\"");
		// strBuf.append(",\"" + escape("reqSecondCs") + "\":" + "\"" +
		// escape(StringUtils.defaultString(getReqSecondCs())) + "\"");
		strBuf.append(",\"" + escape("intDiscOutput") + "\":" + "\""
				+ escape(StringUtils.defaultString(getIntDiscOutput())) + "\"");
		strBuf.append(",\"" + escape("priCostCentre") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPriCostCentre())) + "\"");
		strBuf.append(",\"" + escape("secondCostCentre") + "\":" + "\""
				+ escape(StringUtils.defaultString(getSecondCostCentre())) + "\"");
		strBuf.append(",\"" + escape("submissionFormat") + "\":" + "\""
				+ escape(StringUtils.defaultString(getSubmissionFormat())) + "\"");
		strBuf.append(
				",\"" + escape("engTitle") + "\":" + "\"" + escape(StringUtils.defaultString(getEngTitle())) + "\"");
		strBuf.append(",\"" + escape("isEnhHighEdu") + "\":" + "\""
				+ escape(StringUtils.defaultString(getIsEnhHighEdu())) + "\"");
		strBuf.append(
				",\"" + escape("numOfPage") + "\":" + "\"" + escape(StringUtils.defaultString(getNumOfPage())) + "\"");

		strBuf.append(",\"" + escape("engAuthorList") + "\":" + "\""
				+ escape(StringUtils.defaultString(getEngAuthorList())) + "\"");
		strBuf.append(
				",\"" + escape("keyword1") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyword1())) + "\"");
		strBuf.append(
				",\"" + escape("keyword2") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyword2())) + "\"");
		strBuf.append(
				",\"" + escape("keyword3") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyword3())) + "\"");
		strBuf.append(
				",\"" + escape("keyword4") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyword4())) + "\"");
		strBuf.append(
				",\"" + escape("keyword5") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyword5())) + "\"");

		strBuf.append(
				",\"" + escape("roRemarks") + "\":" + "\"" + escape(StringUtils.defaultString(getRoRemarks())) + "\"");
		strBuf.append(",\"" + escape("roRemarksLib") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRoRemarksLib())) + "\"");

		strBuf.append(
				",\"" + escape("selType") + "\":" + "\"" + escape(StringUtils.defaultString(getSelType())) + "\"");
		strBuf.append(",\"" + escape("staffFaculty") + "\":" + "\""
				+ escape(StringUtils.defaultString(getStaffFaculty())) + "\"");
		strBuf.append(
				",\"" + escape("uoaCode") + "\":" + "\"" + escape(StringUtils.defaultString(getUoaCode())) + "\"");
		strBuf.append(
				",\"" + escape("uoaDesc") + "\":" + "\"" + escape(StringUtils.defaultString(getUoaDesc())) + "\"");
		strBuf.append(",\"" + escape("totalNoOfAuthor") + "\":" + "\""
				+ escape(getTotalNoOfAuthor() != null ? getTotalNoOfAuthor().toString() : "") + "\"");
		strBuf.append(",\"" + escape("journalCode") + "\":" + "\"" + escape(StringUtils.defaultString(getJournalCode()))
				+ "\"");
		strBuf.append(",\"" + escape("researchAreaOfRo") + "\":" + "\""
				+ escape(StringUtils.defaultString(getResearchAreaOfRo())) + "\"");
		strBuf.append(",\"" + escape("subDiscCode") + "\":" + "\"" + escape(StringUtils.defaultString(getSubDiscCode()))
				+ "\"");
		strBuf.append(",\"" + escape("outputType") + "\":" + "\"" + escape(StringUtils.defaultString(getOutputType()))
				+ "\"");
		strBuf.append(",\"" + escape("urlAdditional") + "\":" + "\""
				+ escape(StringUtils.defaultString(getUrlAdditional())) + "\"");
		strBuf.append(",\"" + escape("keyAdditional") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeyAdditional())) + "\"");
		strBuf.append(",\"" + escape("othOutputType") + "\":" + "\""
				+ escape(StringUtils.defaultString(getOthOutputType())) + "\"");
		strBuf.append(",\"" + escape("titleOfEngOutput") + "\":" + "\""
				+ escape(StringUtils.defaultString(getTitleOfEngOutput())) + "\"");
		strBuf.append(",\"" + escape("nonEngOutputInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getNonEngOutputInd())) + "\"");
		strBuf.append(
				",\"" + escape("relLang") + "\":" + "\"" + escape(StringUtils.defaultString(getRelLang())) + "\"");
		strBuf.append(
				",\"" + escape("othLang") + "\":" + "\"" + escape(StringUtils.defaultString(getOthLang())) + "\"");
		strBuf.append(",\"" + escape("titleOfNonEngOutput") + "\":" + "\""
				+ escape(StringUtils.defaultString(getTitleOfNonEngOutput())) + "\"");
		strBuf.append(",\"" + escape("surnameOfEngAuthor") + "\":" + "\""
				+ escape(StringUtils.defaultString(getSurnameOfEngAuthor())) + "\"");
		strBuf.append(",\"" + escape("givenNameOfEngAuthor") + "\":" + "\""
				+ escape(StringUtils.defaultString(getGivenNameOfEngAuthor())) + "\"");
		strBuf.append(",\"" + escape("nameOfNonEngAuthor") + "\":" + "\""
				+ escape(StringUtils.defaultString(getNameOfNonEngAuthor())) + "\"");
		strBuf.append(",\"" + escape("coAuthoredInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getCoAuthoredInd())) + "\"");
		strBuf.append(",\"" + escape("noOfCoAuthor") + "\":" + "\""
				+ escape(StringUtils.defaultString(getNoOfCoAuthor())) + "\"");
		strBuf.append(",\"" + escape("explanationOfAuthorCtb") + "\":" + "\""
				+ escape(StringUtils.defaultString(getExplanationOfAuthorCtb())) + "\"");
		strBuf.append(",\"" + escape("listOfAuthorEng") + "\":" + "\""
				+ escape(StringUtils.defaultString(getListOfAuthorEng())) + "\"");
		strBuf.append(",\"" + escape("indOthStaff") + "\":" + "\"" + escape(StringUtils.defaultString(getIndOthStaff()))
				+ "\"");
		strBuf.append(",\"" + escape("assStaffRO") + "\":" + "\"" + escape(StringUtils.defaultString(getAssStaffRO()))
				+ "\"");
		strBuf.append(",\"" + escape("indCoAuthoredStaff") + "\":" + "\""
				+ escape(StringUtils.defaultString(getIndCoAuthoredStaff())) + "\"");
		strBuf.append(",\"" + escape("listOfAuthorNonEng") + "\":" + "\""
				+ escape(StringUtils.defaultString(getListOfAuthorNonEng())) + "\"");
		strBuf.append(",\"" + escape("publishedCensusDate") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPublishedCensusDate())) + "\"");
		strBuf.append(",\"" + escape("firstPublishedDate") + "\":" + "\""
				+ escape(StringUtils.defaultString(getFirstPublishedDate())) + "\"");
		strBuf.append(
				",\"" + escape("publisher") + "\":" + "\"" + escape(StringUtils.defaultString(getPublisher())) + "\"");
		strBuf.append(",\"" + escape("urlSupport") + "\":" + "\"" + escape(StringUtils.defaultString(getUrlSupport()))
				+ "\"");
		strBuf.append(",\"" + escape("keySupport") + "\":" + "\"" + escape(StringUtils.defaultString(getKeySupport()))
				+ "\"");
		strBuf.append(
				",\"" + escape("bookTitle") + "\":" + "\"" + escape(StringUtils.defaultString(getBookTitle())) + "\"");
		strBuf.append(
				",\"" + escape("issueNo") + "\":" + "\"" + escape(StringUtils.defaultString(getIssueNo())) + "\"");
		strBuf.append(",\"" + escape("pageNo") + "\":" + "\"" + escape(StringUtils.defaultString(getPageNo())) + "\"");
		strBuf.append(",\"" + escape("descLocOutput") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDescLocOutput())) + "\"");
		strBuf.append(",\"" + escape("formatFullVerSubmit") + "\":" + "\""
				+ escape(StringUtils.defaultString(getFormatFullVerSubmit())) + "\"");
		strBuf.append(",\"" + escape("noOfTypeSubmit") + "\":" + "\""
				+ escape(StringUtils.defaultString(getNoOfTypeSubmit())) + "\"");
		strBuf.append(",\"" + escape("roWithDoiInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRoWithDoiInd())) + "\"");
		strBuf.append(",\"" + escape("doi") + "\":" + "\"" + escape(StringUtils.defaultString(getDoi())) + "\"");
		strBuf.append(",\"" + escape("urlFullVer") + "\":" + "\"" + escape(StringUtils.defaultString(getUrlFullVer()))
				+ "\"");
		strBuf.append(",\"" + escape("keyFullVer") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyFullVer()))
				+ "\"");
		strBuf.append(",\"" + escape("urlOaFullVer") + "\":" + "\""
				+ escape(StringUtils.defaultString(getUrlOaFullVer())) + "\"");
		strBuf.append(",\"" + escape("fileTypeOfFullVer") + "\":" + "\""
				+ escape(StringUtils.defaultString(getFileTypeOfFullVer())) + "\"");
		strBuf.append(
				",\"" + escape("fileSize") + "\":" + "\"" + escape(StringUtils.defaultString(getFileSize())) + "\"");
		strBuf.append(",\"" + escape("nonTradOutputInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getNonTradOutputInd())) + "\"");
		strBuf.append(",\"" + escape("infoOfNonTradOutput") + "\":" + "\""
				+ escape(StringUtils.defaultString(getInfoOfNonTradOutput())) + "\"");
		strBuf.append(",\"" + escape("dwRequestInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getDwRequestInd())) + "\"");
		strBuf.append(",\"" + escape("justDwRequest") + "\":" + "\""
				+ escape(StringUtils.defaultString(getJustDwRequest())) + "\"");
		strBuf.append(",\"" + escape("reserveItemDwInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getReserveItemDwInd())) + "\"");
		strBuf.append(",\"" + escape("interResearch") + "\":" + "\""
				+ escape(StringUtils.defaultString(getInterResearch())) + "\"");
		strBuf.append(",\"" + escape("priResearchAreaInter") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPriResearchAreaInter())) + "\"");
		strBuf.append(",\"" + escape("secResearchAreaInter") + "\":" + "\""
				+ escape(StringUtils.defaultString(getSecResearchAreaInter())) + "\"");
		strBuf.append(",\"" + escape("toc") + "\":" + "\"" + escape(StringUtils.defaultString(getToc())) + "\"");
		strBuf.append(",\"" + escape("indToc") + "\":" + "\"" + escape(StringUtils.defaultString(getIndToc())) + "\"");
		strBuf.append(",\"" + escape("urlToc") + "\":" + "\"" + escape(StringUtils.defaultString(getUrlToc())) + "\"");
		strBuf.append(",\"" + escape("keyToc") + "\":" + "\"" + escape(StringUtils.defaultString(getKeyToc())) + "\"");
		strBuf.append(",\"" + escape("additionalInfo") + "\":" + "\""
				+ escape(StringUtils.defaultString(getAdditionalInfo())) + "\"");
		strBuf.append(",\"" + escape("keywordOutput1") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeywordOutput1())) + "\"");
		strBuf.append(",\"" + escape("keywordOutput2") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeywordOutput2())) + "\"");
		strBuf.append(",\"" + escape("keywordOutput3") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeywordOutput3())) + "\"");
		strBuf.append(",\"" + escape("keywordOutput4") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeywordOutput4())) + "\"");
		strBuf.append(",\"" + escape("keywordOutput5") + "\":" + "\""
				+ escape(StringUtils.defaultString(getKeywordOutput5())) + "\"");
		strBuf.append(",\"" + escape("ircHasResult") + "\":" + "\""
				+ escape(StringUtils.defaultString(getIrcHasResult())) + "\"");
		strBuf.append(",\"" + escape("panel2SubSpecDct") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel2SubSpecDct())) + "\"");
		strBuf.append(",\"" + escape("panel8SubSequentEdt") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel8SubSequentEdt())) + "\"");
		strBuf.append(",\"" + escape("panel8IndStmt") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel8IndStmt())) + "\"");
		strBuf.append(",\"" + escape("panel10Explanation") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10Explanation())) + "\"");
		strBuf.append(",\"" + escape("panel10RoInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10RoInd())) + "\"");
		strBuf.append(",\"" + escape("panel10UrlEvidence") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10UrlEvidence())) + "\"");
		strBuf.append(",\"" + escape("panel10KeyEvidence") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10KeyEvidence())) + "\"");
		strBuf.append(",\"" + escape("panel10PtbInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10PtbInd())) + "\"");
		strBuf.append(",\"" + escape("panel10ExplainPtb") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10ExplainPtb())) + "\"");
		strBuf.append(",\"" + escape("panel11SubDiscInfo") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel11SubDiscInfo())) + "\"");
		strBuf.append(",\"" + escape("panel12UrlPubLoc") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel12UrlPubLoc())) + "\"");
		strBuf.append(",\"" + escape("panel12UrlEvidence") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel12UrlEvidence())) + "\"");
		strBuf.append(",\"" + escape("panel12KeyEvidence") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel12KeyEvidence())) + "\"");
		strBuf.append(",\"" + escape("raeStatusCode") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRaeStatusCode())) + "\"");
		strBuf.append(",\"" + escape("citationChkCode") + "\":" + "\""
				+ escape(StringUtils.defaultString(getCitationChkCode())) + "\"");
		strBuf.append(",\"" + escape("citationChkAbsCode") + "\":" + "\""
				+ escape(StringUtils.defaultString(getCitationChkAbsCode())) + "\"");
		strBuf.append(",\"" + escape("citationChkFulltextCode") + "\":" + "\""
				+ escape(StringUtils.defaultString(getCitationChkFulltextCode())) + "\"");
		strBuf.append(",\"" + escape("copyrightClrCode") + "\":" + "\""
				+ escape(StringUtils.defaultString(getCopyrightClrCode())) + "\"");
		strBuf.append(
				",\"" + escape("roRemarks") + "\":" + "\"" + escape(StringUtils.defaultString(getRoRemarks())) + "\"");
		strBuf.append(",\"" + escape("roRemarksLib") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRoRemarksLib())) + "\"");
		strBuf.append(",\"" + escape("urlRef") + "\":" + "\"" + escape(StringUtils.defaultString(getUrlRef())) + "\"");

		strBuf.append(",\"" + escape("phyAudioQty") + "\":" + "\"" + escape(String.valueOf(getPhyAudioQty())) + "\"");
		strBuf.append(",\"" + escape("phyCdQty") + "\":" + "\"" + escape(String.valueOf(getPhyCdQty())) + "\"");
		strBuf.append(",\"" + escape("phyDvdQty") + "\":" + "\"" + escape(String.valueOf(getPhyDvdQty())) + "\"");
		strBuf.append(",\"" + escape("phyPhotoQty") + "\":" + "\"" + escape(String.valueOf(getPhyPhotoQty())) + "\"");
		strBuf.append(",\"" + escape("phyBookQty") + "\":" + "\"" + escape(String.valueOf(getPhyBookQty())) + "\"");
		strBuf.append(",\"" + escape("phyUsbQty") + "\":" + "\"" + escape(String.valueOf(getPhyUsbQty())) + "\"");
		strBuf.append(",\"" + escape("phyOtherQty") + "\":" + "\"" + escape(String.valueOf(getPhyOtherQty())) + "\"");
		strBuf.append(",\"" + escape("phyOtherType") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPhyOtherType())) + "\"");

		strBuf.append(",\"" + escape("raeStatusCodePanel") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRaeStatusCodePanel())) + "\"");
		strBuf.append(",\"" + escape("raeStatusCodeFullVer") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRaeStatusCodeFullVer())) + "\"");
		strBuf.append(",\"" + escape("raeStatusCodeOthInfo") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRaeStatusCodeOthInfo())) + "\"");
		strBuf.append(",\"" + escape("raeStatusIneligible") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRaeStatusIneligible())) + "\"");

		strBuf.append(",\"" + escape("panel10RevInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10RevInd())) + "\"");
		strBuf.append(",\"" + escape("panel10RevExplain") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10RevExplain())) + "\"");
		strBuf.append(",\"" + escape("panel10OpenUrl") + "\":" + "\""
				+ escape(StringUtils.defaultString(getPanel10OpenUrl())) + "\"");
		strBuf.append(",\"" + escape("noSglCoWork") + "\":" + "\"" + escape(StringUtils.defaultString(getNoSglCoWork()))
				+ "\"");
		strBuf.append(",\"" + escape("uniEndorseConfInd") + "\":" + "\""
				+ escape(StringUtils.defaultString(getUniEndorseConfInd())) + "\"");
		strBuf.append(",\"" + escape("justSglCoWork") + "\":" + "\""
				+ escape(StringUtils.defaultString(getJustSglCoWork())) + "\"");

		strBuf.append(
				",\"" + escape("pseudonym") + "\":" + "\"" + escape(StringUtils.defaultString(getPseudonym())) + "\"");
		strBuf.append(",\"" + escape("roleSubmitStaff") + "\":" + "\""
				+ escape(StringUtils.defaultString(getRoleSubmitStaff())) + "\"");

		strBuf.append(",\"" + escape("eissn") + "\":" + "\"" + escape(StringUtils.defaultString(getEissn())) + "\"");
		strBuf.append(
				",\"" + escape("articleNo") + "\":" + "\"" + escape(StringUtils.defaultString(getArticleNo())) + "\"");

		strBuf.append(
				",\"" + escape("userstamp") + "\":" + "\"" + escape(StringUtils.defaultString(getUserstamp())) + "\"");
		strBuf.append(",\"" + escape("timestamp") + "\":" + "\"" + getTimestamp() + "\"");

		strBuf.append("}");

		return strBuf.toString();
	}


	public void set_pid(int pid)
	{
		this.pid = pid;
	}


	public int get_pid()
	{
		return pid;
	}


	public void setStaffFullname(String staffFullname)
	{
		this.staffFullname = staffFullname;
	}


	public String getStaffFullname()
	{
		return staffFullname;
	}


	public void setStaffDept(String staffDept)
	{
		this.staffDept = staffDept;
	}


	public String getStaffDept()
	{
		return staffDept;
	}


	public void set_priority(String priority)
	{
		this.priority = priority;
	}


	public String get_priority()
	{
		return priority;
	}


	public void set_nameOtherEditors(String nameOtherEditors)
	{
		this.nameOtherEditors = nameOtherEditors;
	}


	public String get_nameOtherEditors()
	{
		return nameOtherEditors;
	}


	public void set_IEdWorkInd(String IEdWorkInd)
	{
		this.IEdWorkInd = IEdWorkInd;
	}


	public String get_IEdWorkInd()
	{
		return IEdWorkInd;
	}


	public void set_researchActivityRanking(String researchActivityRanking)
	{
		this.researchActivityRanking = researchActivityRanking;
	}


	public String get_researchActivityRanking()
	{
		return researchActivityRanking;
	}


	public void set_sapOutputTypeDesc(String sapOutputTypeDesc)
	{
		this.sapOutputTypeDesc = sapOutputTypeDesc;
	}


	public String get_sapOutputTypeDesc()
	{
		return sapOutputTypeDesc;
	}


	public void set_reposURL(String reposURL)
	{
		this.reposURL = reposURL;
	}


	public String get_reposURL()
	{
		return reposURL;
	}


	public void set_reposConfirm(String reposConfirm)
	{
		this.reposConfirm = reposConfirm;
	}


	public String get_reposConfirm()
	{
		return reposConfirm;
	}


	public void set_reposFetch(String reposFetch)
	{
		this.reposFetch = reposFetch;
	}


	public String get_reposFetch()
	{
		return reposFetch;
	}


	public void set_reposLastFetch(Timestamp reposLastFetch)
	{
		this.reposLastFetch = reposLastFetch;
	}


	public Timestamp get_reposLastFetch()
	{
		return reposLastFetch;
	}


	public void setDetailList(List detailList)
	{
		this.detailList = detailList;
	}


	public List getDetailList()
	{
		return detailList;
	}


	public void set_staffFullname(String staffFullname)
	{
		this.staffFullname = staffFullname;
	}


	public String get_staffFullname()
	{
		return staffFullname;
	}


	public void set_staffDept(String staffDept)
	{
		this.staffDept = staffDept;
	}


	public String get_staffDept()
	{
		return staffDept;
	}


	public void setEduSecDesc(String eduSecDesc)
	{
		this.eduSecDesc = eduSecDesc;
	}


	public String getEduSecDesc()
	{
		return eduSecDesc;
	}


	public void setResearchAreaDesc(String researchAreaDesc)
	{
		this.researchAreaDesc = researchAreaDesc;
	}


	public String getResearchAreaDesc()
	{
		return researchAreaDesc;
	}


	public void setKeyResearchAreaDesc(String keyResearchAreaDesc)
	{
		this.keyResearchAreaDesc = keyResearchAreaDesc;
	}


	public String getKeyResearchAreaDesc()
	{
		return keyResearchAreaDesc;
	}


	public void setDisAreaDesc(String disAreaDesc)
	{
		this.disAreaDesc = disAreaDesc;
	}


	public String getDisAreaDesc()
	{
		return disAreaDesc;
	}


	public void set_detailList(List<String> detailList)
	{
		this.detailList = detailList;
	}


	public List<String> get_detailList()
	{
		return detailList;
	}


	public void setSapOutputCatDesc(String sapOutputCatDesc)
	{
		this.sapOutputCatDesc = sapOutputCatDesc;
	}


	public String getSapOutputCatDesc()
	{
		return sapOutputCatDesc;
	}


	public void set_eduSecDesc(String eduSecDesc)
	{
		this.eduSecDesc = eduSecDesc;
	}


	public String get_eduSecDesc()
	{
		return eduSecDesc;
	}


	public void set_researchAreaDesc(String researchAreaDesc)
	{
		this.researchAreaDesc = researchAreaDesc;
	}


	public String get_researchAreaDesc()
	{
		return researchAreaDesc;
	}


	public void set_keyResearchAreaDesc(String keyResearchAreaDesc)
	{
		this.keyResearchAreaDesc = keyResearchAreaDesc;
	}


	public String get_keyResearchAreaDesc()
	{
		return keyResearchAreaDesc;
	}


	public void set_disAreaDesc(String disAreaDesc)
	{
		this.disAreaDesc = disAreaDesc;
	}


	public String get_disAreaDesc()
	{
		return disAreaDesc;
	}


	public void set_sapOutputCatDesc(String sapOutputCatDesc)
	{
		this.sapOutputCatDesc = sapOutputCatDesc;
	}


	public String get_sapOutputCatDesc()
	{
		return sapOutputCatDesc;
	}


	public void setSapReferedJournalDesc(String sapReferedJournalDesc)
	{
		this.sapReferedJournalDesc = sapReferedJournalDesc;
	}


	public String getSapReferedJournalDesc()
	{
		return sapReferedJournalDesc;
	}


	public void setIssn(String issn)
	{
		this.issn = issn;
	}


	public String getIssn()
	{
		return issn;
	}


	public void set_sapReferedJournalDesc(String sapReferedJournalDesc)
	{
		this.sapReferedJournalDesc = sapReferedJournalDesc;
	}


	public String get_sapReferedJournalDesc()
	{
		return sapReferedJournalDesc;
	}


	public void set_issn(String issn)
	{
		this.issn = issn;
	}


	public String get_issn()
	{
		return issn;
	}


	public void setScholarCat(String scholarCat)
	{
		this.scholarCat = scholarCat;
	}


	public String getScholarCat()
	{
		return scholarCat;
	}


	public void setAssessType(String assessType)
	{
		this.assessType = assessType;
	}


	public String getAssessType()
	{
		return assessType;
	}


	public void setSubmitRef(String submitRef)
	{
		this.submitRef = submitRef;
	}


	public String getSubmitRef()
	{
		return submitRef;
	}


	public void setReqSubjPanel(String reqSubjPanel)
	{
		this.reqSubjPanel = reqSubjPanel;
	}


	public String getReqSubjPanel()
	{
		return reqSubjPanel;
	}


	public void setAbstractData(String abstractData)
	{
		this.abstractData = abstractData;
	}


	public String getAbstractData()
	{
		return abstractData;
	}


	public void setDoi(String doi)
	{
		this.doi = doi;
	}


	public String getDoi()
	{
		return doi;
	}


	public void setIsbn(String isbn)
	{
		this.isbn = isbn;
	}


	public String getIsbn()
	{
		return isbn;
	}


	public void setDoubleWeighted(String doubleWeighted)
	{
		this.doubleWeighted = doubleWeighted;
	}


	public String getDoubleWeighted()
	{
		return doubleWeighted;
	}


	public void setRiDesc(String riDesc)
	{
		this.riDesc = riDesc;
	}


	public String getRiDesc()
	{
		return riDesc;
	}


	public void setOtherOutputDesc(String otherOutputDesc)
	{
		this.otherOutputDesc = otherOutputDesc;
	}


	public String getOtherOutputDesc()
	{
		return otherOutputDesc;
	}


	public void setDblWgtJustify(String dblWgtJustify)
	{
		this.dblWgtJustify = dblWgtJustify;
	}


	public String getDblWgtJustify()
	{
		return dblWgtJustify;
	}


	public void setBackupRI(int backupRI)
	{
		this.backupRI = backupRI;
	}


	public int getBackupRI()
	{
		return backupRI;
	}


	public void setRiPublishStatus(String riPublishStatus)
	{
		this.riPublishStatus = riPublishStatus;
	}


	public String getRiPublishStatus()
	{
		return riPublishStatus;
	}


	public void setRaeInternalRating(String raeInternalRating)
	{
		this.raeInternalRating = raeInternalRating;
	}


	public String getRaeInternalRating()
	{
		return raeInternalRating;
	}


	public void setRiDesc2(String riDesc2)
	{
		this.riDesc2 = riDesc2;
	}


	public String getRiDesc2()
	{
		return riDesc2;
	}


	public void setToBeUpdated(String toBeUpdated)
	{
		this.toBeUpdated = toBeUpdated;
	}


	public String getToBeUpdated()
	{
		return toBeUpdated;
	}


	public void setSecondCostCentre(String secondCostCentre)
	{
		this.secondCostCentre = secondCostCentre;
	}


	public String getSecondCostCentre()
	{
		return secondCostCentre;
	}


	public void setIntDiscOutput(String intDiscOutput)
	{
		this.intDiscOutput = intDiscOutput;
	}


	public String getIntDiscOutput()
	{
		return intDiscOutput;
	}


	public void setPriCostCentre(String priCostCentre)
	{
		this.priCostCentre = priCostCentre;
	}


	public String getPriCostCentre()
	{
		return priCostCentre;
	}


	public void set_secondCostCentre(String secondCostCentre)
	{
		this.secondCostCentre = secondCostCentre;
	}


	public String get_secondCostCentre()
	{
		return secondCostCentre;
	}


	public void setSubmissionFormat(String submissionFormat)
	{
		this.submissionFormat = submissionFormat;
	}


	public String getSubmissionFormat()
	{
		return submissionFormat;
	}


	public void setEngTitle(String engTitle)
	{
		this.engTitle = engTitle;
	}


	public String getEngTitle()
	{
		return engTitle;
	}


	public void setCountFulltext(int countFulltext)
	{
		this.countFulltext = countFulltext;
	}


	public int getCountFulltext()
	{
		return countFulltext;
	}


	public void setCountAbstract(int countAbstract)
	{
		this.countAbstract = countAbstract;
	}


	public int getCountAbstract()
	{
		return countAbstract;
	}


	public void setCountAccept(int countAccept)
	{
		this.countAccept = countAccept;
	}


	public int getCountAccept()
	{
		return countAccept;
	}


	public void setNumOfPage(String numOfPage)
	{
		this.numOfPage = numOfPage;
	}


	public String getNumOfPage()
	{
		return numOfPage;
	}


	public void setIsEnhHighEdu(String isEnhHighEdu)
	{
		this.isEnhHighEdu = isEnhHighEdu;
	}


	public String getIsEnhHighEdu()
	{
		return isEnhHighEdu;
	}


	public void setEngAuthorList(String engAuthorList)
	{
		this.engAuthorList = engAuthorList;
	}


	public String getEngAuthorList()
	{
		return engAuthorList;
	}


	public void setKeyword1(String keyword1)
	{
		this.keyword1 = keyword1;
	}


	public String getKeyword1()
	{
		return keyword1;
	}


	public void setKeyword2(String keyword2)
	{
		this.keyword2 = keyword2;
	}


	public String getKeyword2()
	{
		return keyword2;
	}


	public void setKeyword3(String keyword3)
	{
		this.keyword3 = keyword3;
	}


	public String getKeyword3()
	{
		return keyword3;
	}


	public void setKeyword4(String keyword4)
	{
		this.keyword4 = keyword4;
	}


	public String getKeyword4()
	{
		return keyword4;
	}


	public void setKeyword5(String keyword5)
	{
		this.keyword5 = keyword5;
	}


	public String getKeyword5()
	{
		return keyword5;
	}


	public void setCountAbsEng(int countAbsEng)
	{
		this.countAbsEng = countAbsEng;
	}


	public int getCountAbsEng()
	{
		return countAbsEng;
	}


	public void setIsIntlConf(String isIntlConf)
	{
		this.isIntlConf = isIntlConf;
	}


	public String getIsIntlConf()
	{
		return isIntlConf;
	}


	public void setJournalPublicationDiscipline(String journalPublicationDiscipline)
	{
		this.journalPublicationDiscipline = journalPublicationDiscipline;
	}


	public String getJournalPublicationDiscipline()
	{
		return journalPublicationDiscipline;
	}


	public void setJournalPublicationDisciplineDesc(String journalPublicationDisciplineDesc)
	{
		this.journalPublicationDisciplineDesc = journalPublicationDisciplineDesc;
	}


	public String getJournalPublicationDisciplineDesc()
	{
		return journalPublicationDisciplineDesc;
	}


	public void setAuthorshipType(String authorshipType)
	{
		this.authorshipType = authorshipType;
	}


	public String getAuthorshipType()
	{
		return authorshipType;
	}


	public void setAuthorshipDtlType(String authorshipDtlType)
	{
		this.authorshipDtlType = authorshipDtlType;
	}


	public String getAuthorshipDtlType()
	{
		return authorshipDtlType;
	}


	public void setApaCitation(String apaCitation)
	{
		this.apaCitation = apaCitation;
	}


	public String getApaCitation()
	{
		return apaCitation;
	}

	/*
	 * public void setReqSecondCs(String reqSecondCs) { this.reqSecondCs =
	 * reqSecondCs; }
	 * 
	 * 
	 * public String getReqSecondCs() { return this.reqSecondCs; }
	 */


	public String getParentLookupCode()
	{
		return parentLookupCode;
	}


	public void setParentLookupCode(String parentLookupCode)
	{
		this.parentLookupCode = parentLookupCode;
	}


	public void set_scholarCat(String scholarCat)
	{
		this.scholarCat = scholarCat;
	}


	public String get_scholarCat()
	{
		return scholarCat;
	}


	public void set_assessType(String assessType)
	{
		this.assessType = assessType;
	}


	public String get_assessType()
	{
		return assessType;
	}


	public void set_submitRef(String submitRef)
	{
		this.submitRef = submitRef;
	}


	public String get_submitRef()
	{
		return submitRef;
	}


	public void set_reqSubjPanel(String reqSubjPanel)
	{
		this.reqSubjPanel = reqSubjPanel;
	}


	public String get_reqSubjPanel()
	{
		return reqSubjPanel;
	}


	public void set_abstractData(String abstractData)
	{
		this.abstractData = abstractData;
	}


	public String get_abstractData()
	{
		return abstractData;
	}


	public void set_doi(String doi)
	{
		this.doi = doi;
	}


	public String get_doi()
	{
		return doi;
	}


	public void set_riPublishStatus(String riPublishStatus)
	{
		this.riPublishStatus = riPublishStatus;
	}


	public String get_riPublishStatus()
	{
		return riPublishStatus;
	}


	public void set_doubleWeighted(String doubleWeighted)
	{
		this.doubleWeighted = doubleWeighted;
	}


	public String get_doubleWeighted()
	{
		return doubleWeighted;
	}


	public void set_backupRI(int backupRI)
	{
		this.backupRI = backupRI;
	}


	public int get_backupRI()
	{
		return backupRI;
	}


	public void set_riDesc(String riDesc)
	{
		this.riDesc = riDesc;
	}


	public String get_riDesc()
	{
		return riDesc;
	}


	public void set_riDesc2(String riDesc2)
	{
		this.riDesc2 = riDesc2;
	}


	public String get_riDesc2()
	{
		return riDesc2;
	}


	public void set_otherOutputDesc(String otherOutputDesc)
	{
		this.otherOutputDesc = otherOutputDesc;
	}


	public String get_otherOutputDesc()
	{
		return otherOutputDesc;
	}


	public void set_dblWgtJustify(String dblWgtJustify)
	{
		this.dblWgtJustify = dblWgtJustify;
	}


	public String get_dblWgtJustify()
	{
		return dblWgtJustify;
	}


	public void set_raeInternalRating(String raeInternalRating)
	{
		this.raeInternalRating = raeInternalRating;
	}


	public String get_raeInternalRating()
	{
		return raeInternalRating;
	}


	public void set_toBeUpdated(String toBeUpdated)
	{
		this.toBeUpdated = toBeUpdated;
	}


	public String get_toBeUpdated()
	{
		return toBeUpdated;
	}


	public void set_intDiscOutput(String intDiscOutput)
	{
		this.intDiscOutput = intDiscOutput;
	}


	public String get_intDiscOutput()
	{
		return intDiscOutput;
	}


	public void set_priCostCentre(String priCostCentre)
	{
		this.priCostCentre = priCostCentre;
	}


	public String get_priCostCentre()
	{
		return priCostCentre;
	}


	public void set_submissionFormat(String submissionFormat)
	{
		this.submissionFormat = submissionFormat;
	}


	public String get_submissionFormat()
	{
		return submissionFormat;
	}


	public void set_engTitle(String engTitle)
	{
		this.engTitle = engTitle;
	}


	public String get_engTitle()
	{
		return engTitle;
	}


	public void set_countFulltext(int countFulltext)
	{
		this.countFulltext = countFulltext;
	}


	public int get_countFulltext()
	{
		return countFulltext;
	}


	public void set_countAbstract(int countAbstract)
	{
		this.countAbstract = countAbstract;
	}


	public int get_countAbstract()
	{
		return countAbstract;
	}


	public void set_countAbsEng(int countAbsEng)
	{
		this.countAbsEng = countAbsEng;
	}


	public int get_countAbsEng()
	{
		return countAbsEng;
	}


	public void set_countAccept(int countAccept)
	{
		this.countAccept = countAccept;
	}


	public int get_countAccept()
	{
		return countAccept;
	}


	public void set_numOfPage(String numOfPage)
	{
		this.numOfPage = numOfPage;
	}


	public String get_numOfPage()
	{
		return numOfPage;
	}


	public void set_isEnhHighEdu(String isEnhHighEdu)
	{
		this.isEnhHighEdu = isEnhHighEdu;
	}


	public String get_isEnhHighEdu()
	{
		return isEnhHighEdu;
	}


	public void set_engAuthorList(String engAuthorList)
	{
		this.engAuthorList = engAuthorList;
	}


	public String get_engAuthorList()
	{
		return engAuthorList;
	}


	public void set_keyword1(String keyword1)
	{
		this.keyword1 = keyword1;
	}


	public String get_keyword1()
	{
		return keyword1;
	}


	public void set_keyword2(String keyword2)
	{
		this.keyword2 = keyword2;
	}


	public String get_keyword2()
	{
		return keyword2;
	}


	public void set_keyword3(String keyword3)
	{
		this.keyword3 = keyword3;
	}


	public String get_keyword3()
	{
		return keyword3;
	}


	public void set_keyword4(String keyword4)
	{
		this.keyword4 = keyword4;
	}


	public String get_keyword4()
	{
		return keyword4;
	}


	public void set_keyword5(String keyword5)
	{
		this.keyword5 = keyword5;
	}


	public String get_keyword5()
	{
		return keyword5;
	}


	public void set_isIntlConf(String isIntlConf)
	{
		this.isIntlConf = isIntlConf;
	}


	public String get_isIntlConf()
	{
		return isIntlConf;
	}


	public void set_journalPublicationDiscipline(String journalPublicationDiscipline)
	{
		this.journalPublicationDiscipline = journalPublicationDiscipline;
	}


	public String get_journalPublicationDiscipline()
	{
		return journalPublicationDiscipline;
	}


	public void set_journalPublicationDisciplineDesc(String journalPublicationDisciplineDesc)
	{
		this.journalPublicationDisciplineDesc = journalPublicationDisciplineDesc;
	}


	public String get_journalPublicationDisciplineDesc()
	{
		return journalPublicationDisciplineDesc;
	}


	public void set_authorshipType(String authorshipType)
	{
		this.authorshipType = authorshipType;
	}


	public String get_authorshipType()
	{
		return authorshipType;
	}


	public void set_authorshipDtlType(String authorshipDtlType)
	{
		this.authorshipDtlType = authorshipDtlType;
	}


	public String get_authorshipDtlType()
	{
		return authorshipDtlType;
	}


	public void set_apaCitation(String apaCitation)
	{
		this.apaCitation = apaCitation;
	}


	public String get_apaCitation()
	{
		return apaCitation;
	}


	public void set_parentLookupCode(String parentLookupCode)
	{
		this.parentLookupCode = parentLookupCode;
	}


	public String get_parentLookupCode()
	{
		return parentLookupCode;
	}


	public void setStaffFaculty(String staffFaculty)
	{
		this.staffFaculty = staffFaculty;
	}


	public String getStaffFaculty()
	{
		return staffFaculty;
	}


	public void setJournalCode(String journalCode)
	{
		this.journalCode = journalCode;
	}


	public String getJournalCode()
	{
		return journalCode;
	}


	public void setTotalNoOfAuthor(Integer totalNoOfAuthor)
	{
		this.totalNoOfAuthor = totalNoOfAuthor;
	}


	public Integer getTotalNoOfAuthor()
	{
		return totalNoOfAuthor;
	}


	public void setSelType(String selType)
	{
		this.selType = selType;
	}


	public String getSelType()
	{
		return selType;
	}


	public String getSelTypeDesc()
	{	
		ResourceBundle bundle = getResourceBundle();
		
		String dw1 = bundle.getString("rae.selected.type.dw1");
		String dw2 = bundle.getString("rae.selected.type.dw2");
		String selTypeDesc = "";

		if ("NS".equals(selType))
		{
			selTypeDesc = "Not Selected";
		}
		else if ("S".equals(selType))
		{
			selTypeDesc = "Selected";
		}
		else if ("DW1".equals(selType))
		{
			selTypeDesc = dw1;
		}
		else if ("DWB1".equals(selType))
		{
			selTypeDesc = "Reserve for "+dw1;
		}
		else if ("DW2".equals(selType))
		{
			selTypeDesc = dw2;
		}
		else if ("DWB2".equals(selType))
		{
			selTypeDesc = "Reserve for "+dw2;
		}

		return selTypeDesc;
	}


	public void setUoaCode(String uoaCode)
	{
		this.uoaCode = uoaCode;
	}


	public String getUoaCode()
	{
		return uoaCode;
	}


	public void setUoaDesc(String uoaDesc)
	{
		this.uoaDesc = uoaDesc;
	}


	public String getUoaDesc()
	{
		return uoaDesc;
	}


	public void setResearchAreaOfRo(String researchAreaOfRo)
	{
		this.researchAreaOfRo = researchAreaOfRo;
	}


	public String getResearchAreaOfRo()
	{
		return researchAreaOfRo;
	}


	public int getRoPanel()
	{
		int roPanel = -1;

		if (!"".equals(StringUtils.defaultString(researchAreaOfRo)) && researchAreaOfRo.length() > 2)
		{
			int raCodeNum = Integer.parseInt(researchAreaOfRo.substring(0, 2));

			if (raCodeNum >= 1 && raCodeNum <= 2)
			{
				roPanel = 1;
			}
			else if (raCodeNum >= 3 && raCodeNum <= 6)
			{
				roPanel = 2;
			}
			else if (raCodeNum >= 7 && raCodeNum <= 11)
			{
				roPanel = 3;
			}
			else if (raCodeNum >= 12 && raCodeNum <= 12)
			{
				roPanel = 4;
			}
			else if (raCodeNum >= 13 && raCodeNum <= 13)
			{
				roPanel = 5;
			}
			else if (raCodeNum >= 14 && raCodeNum <= 15)
			{
				roPanel = 6;
			}
			else if (raCodeNum >= 16 && raCodeNum <= 18)
			{
				roPanel = 7;
			}
			else if (raCodeNum >= 19 && raCodeNum <= 19)
			{
				roPanel = 8;
			}
			else if (raCodeNum >= 20 && raCodeNum <= 23)
			{
				roPanel = 9;
			}
			else if (raCodeNum >= 24 && raCodeNum <= 29)
			{
				roPanel = 10;
			}
			else if (raCodeNum >= 30 && raCodeNum <= 37)
			{
				roPanel = 11;
			}
			else if (raCodeNum >= 38 && raCodeNum <= 39)
			{
				roPanel = 12;
			}
			else if (raCodeNum >= 40 && raCodeNum <= 41)
			{
				roPanel = 13;
			}
		}

		return roPanel;

	}


	public void setSubDiscCode(String subDiscCode)
	{
		this.subDiscCode = subDiscCode;
	}


	public String getSubDiscCode()
	{
		return subDiscCode;
	}


	public void setOutputType(String outputType)
	{
		this.outputType = outputType;
	}


	public String getOutputType()
	{
		return outputType;
	}


	public void setUrlAdditional(String urlAdditional)
	{
		this.urlAdditional = urlAdditional;
	}


	public String getUrlAdditional()
	{
		return urlAdditional;
	}


	public void setKeyAdditional(String keyAdditional)
	{
		this.keyAdditional = keyAdditional;
	}


	public String getKeyAdditional()
	{
		return keyAdditional;
	}


	public void setOthOutputType(String othOutputType)
	{
		this.othOutputType = othOutputType;
	}


	public String getOthOutputType()
	{
		return othOutputType;
	}


	public void setTitleOfEngOutput(String titleOfEngOutput)
	{
		this.titleOfEngOutput = titleOfEngOutput;
	}


	public String getTitleOfEngOutput()
	{
		return titleOfEngOutput;
	}


	public void setNonEngOutputInd(String nonEngOutputInd)
	{
		this.nonEngOutputInd = nonEngOutputInd;
	}


	public String getNonEngOutputInd()
	{
		return nonEngOutputInd;
	}


	public void setRelLang(String relLang)
	{
		this.relLang = relLang;
	}


	public String getRelLang()
	{
		return relLang;
	}


	public void setOthLang(String othLang)
	{
		this.othLang = othLang;
	}


	public String getOthLang()
	{
		return othLang;
	}


	public void setTitleOfNonEngOutput(String titleOfNonEngOutput)
	{
		this.titleOfNonEngOutput = titleOfNonEngOutput;
	}


	public String getTitleOfNonEngOutput()
	{
		return titleOfNonEngOutput;
	}


	public void setSurnameOfEngAuthor(String surnameOfEngAuthor)
	{
		this.surnameOfEngAuthor = surnameOfEngAuthor;
	}


	public String getSurnameOfEngAuthor()
	{
		return surnameOfEngAuthor;
	}


	public void setGivenNameOfEngAuthor(String givenNameOfEngAuthor)
	{
		this.givenNameOfEngAuthor = givenNameOfEngAuthor;
	}


	public String getGivenNameOfEngAuthor()
	{
		return givenNameOfEngAuthor;
	}


	public void setNameOfNonEngAuthor(String nameOfNonEngAuthor)
	{
		this.nameOfNonEngAuthor = nameOfNonEngAuthor;
	}


	public String getNameOfNonEngAuthor()
	{
		return nameOfNonEngAuthor;
	}


	public void setCoAuthoredInd(String coAuthoredInd)
	{
		this.coAuthoredInd = coAuthoredInd;
	}


	public String getCoAuthoredInd()
	{
		return coAuthoredInd;
	}


	public void setNoOfCoAuthor(String noOfCoAuthor)
	{
		this.noOfCoAuthor = noOfCoAuthor;
	}


	public String getNoOfCoAuthor()
	{
		return noOfCoAuthor;
	}


	public void setExplanationOfAuthorCtb(String explanationOfAuthorCtb)
	{
		this.explanationOfAuthorCtb = explanationOfAuthorCtb;
	}


	public String getExplanationOfAuthorCtb()
	{
		return explanationOfAuthorCtb;
	}


	public void setListOfAuthorEng(String listOfAuthorEng)
	{
		this.listOfAuthorEng = listOfAuthorEng;
	}


	public String getListOfAuthorEng()
	{
		return listOfAuthorEng;
	}


	public void setIndOthStaff(String indOthStaff)
	{
		this.indOthStaff = indOthStaff;
	}


	public String getIndOthStaff()
	{
		return indOthStaff;
	}


	public void setAssStaffRO(String assStaffRO)
	{
		this.assStaffRO = assStaffRO;
	}


	public String getAssStaffRO()
	{
		return assStaffRO;
	}


	public void setIndCoAuthoredStaff(String indCoAuthoredStaff)
	{
		this.indCoAuthoredStaff = indCoAuthoredStaff;
	}


	public String getIndCoAuthoredStaff()
	{
		return indCoAuthoredStaff;
	}


	public void setListOfAuthorNonEng(String listOfAuthorNonEng)
	{
		this.listOfAuthorNonEng = listOfAuthorNonEng;
	}


	public String getListOfAuthorNonEng()
	{
		return listOfAuthorNonEng;
	}


	public void setPublishedCensusDate(String publishedCensusDate)
	{
		this.publishedCensusDate = publishedCensusDate;
	}


	public String getPublishedCensusDate()
	{
		return publishedCensusDate;
	}


	public void set_isbn(String isbn)
	{
		this.isbn = isbn;
	}


	public String get_isbn()
	{
		return isbn;
	}


	public void setFirstPublishedDate(String firstPublishedDate)
	{
		this.firstPublishedDate = firstPublishedDate;
	}


	public String getFirstPublishedDate()
	{
		return firstPublishedDate;
	}


	public void setUrlSupport(String urlSupport)
	{
		this.urlSupport = urlSupport;
	}


	public String getUrlSupport()
	{
		return urlSupport;
	}


	public void setKeySupport(String keySupport)
	{
		this.keySupport = keySupport;
	}


	public String getKeySupport()
	{
		return keySupport;
	}


	public void setBookTitle(String bookTitle)
	{
		this.bookTitle = bookTitle;
	}


	public String getBookTitle()
	{
		return bookTitle;
	}


	public void setIssueNo(String issueNo)
	{
		this.issueNo = issueNo;
	}


	public String getIssueNo()
	{
		return issueNo;
	}


	public void setPageNo(String pageNo)
	{
		this.pageNo = pageNo;
	}


	public String getPageNo()
	{
		return pageNo;
	}


	public void setDescLocOutput(String descLocOutput)
	{
		this.descLocOutput = descLocOutput;
	}


	public String getDescLocOutput()
	{
		return descLocOutput;
	}


	public void setFormatFullVerSubmit(String formatFullVerSubmit)
	{
		this.formatFullVerSubmit = formatFullVerSubmit;
	}


	public String getFormatFullVerSubmit()
	{
		return formatFullVerSubmit;
	}


	public void setNoOfTypeSubmit(String noOfTypeSubmit)
	{
		this.noOfTypeSubmit = noOfTypeSubmit;
	}


	public String getNoOfTypeSubmit()
	{
		return noOfTypeSubmit;
	}


	public void setRoWithDoiInd(String roWithDoiInd)
	{
		this.roWithDoiInd = roWithDoiInd;
	}


	public String getRoWithDoiInd()
	{
		return roWithDoiInd;
	}


	public void setKeyFullVer(String keyFullVer)
	{
		this.keyFullVer = keyFullVer;
	}


	public String getKeyFullVer()
	{
		return keyFullVer;
	}


	public void setUrlOaFullVer(String urlOaFullVer)
	{
		this.urlOaFullVer = urlOaFullVer;
	}


	public String getUrlOaFullVer()
	{
		return urlOaFullVer;
	}


	public void setFileTypeOfFullVer(String fileTypeOfFullVer)
	{
		this.fileTypeOfFullVer = fileTypeOfFullVer;
	}


	public String getFileTypeOfFullVer()
	{
		return fileTypeOfFullVer;
	}


	public void setFileSize(String fileSize)
	{
		this.fileSize = fileSize;
	}


	public String getFileSize()
	{
		return fileSize;
	}


	public void setNonTradOutputInd(String nonTradOutputInd)
	{
		this.nonTradOutputInd = nonTradOutputInd;
	}


	public String getNonTradOutputInd()
	{
		return nonTradOutputInd;
	}


	public void setInfoOfNonTradOutput(String infoOfNonTradOutput)
	{
		this.infoOfNonTradOutput = infoOfNonTradOutput;
	}


	public String getInfoOfNonTradOutput()
	{
		return infoOfNonTradOutput;
	}


	public void setDwRequestInd(String dwRequestInd)
	{
		this.dwRequestInd = dwRequestInd;
	}


	public String getDwRequestInd()
	{
		return dwRequestInd;
	}


	public void setJustDwRequest(String justDwRequest)
	{
		this.justDwRequest = justDwRequest;
	}


	public String getJustDwRequest()
	{
		return justDwRequest;
	}


	public void setReserveItemDwInd(String reserveItemDwInd)
	{
		this.reserveItemDwInd = reserveItemDwInd;
	}


	public String getReserveItemDwInd()
	{
		return reserveItemDwInd;
	}


	public void setInterResearch(String interResearch)
	{
		this.interResearch = interResearch;
	}


	public String getInterResearch()
	{
		return interResearch;
	}


	public void setPriResearchAreaInter(String priResearchAreaInter)
	{
		this.priResearchAreaInter = priResearchAreaInter;
	}


	public String getPriResearchAreaInter()
	{
		return priResearchAreaInter;
	}


	public void setSecResearchAreaInter(String secResearchAreaInter)
	{
		this.secResearchAreaInter = secResearchAreaInter;
	}


	public String getSecResearchAreaInter()
	{
		return secResearchAreaInter;
	}


	public void setToc(String toc)
	{
		this.toc = toc;
	}


	public String getToc()
	{
		return toc;
	}


	public void setIndToc(String indToc)
	{
		this.indToc = indToc;
	}


	public String getIndToc()
	{
		return indToc;
	}


	public void setUrlToc(String urlToc)
	{
		this.urlToc = urlToc;
	}


	public String getUrlToc()
	{
		return urlToc;
	}


	public void setKeyToc(String keyToc)
	{
		this.keyToc = keyToc;
	}


	public String getKeyToc()
	{
		return keyToc;
	}


	public void setAdditionalInfo(String additionalInfo)
	{
		this.additionalInfo = additionalInfo;
	}


	public String getAdditionalInfo()
	{
		return additionalInfo;
	}


	public void setKeywordOutput1(String keywordOutput1)
	{
		this.keywordOutput1 = keywordOutput1;
	}


	public String getKeywordOutput1()
	{
		return keywordOutput1;
	}


	public void setKeywordOutput2(String keywordOutput2)
	{
		this.keywordOutput2 = keywordOutput2;
	}


	public String getKeywordOutput2()
	{
		return keywordOutput2;
	}


	public void setKeywordOutput3(String keywordOutput3)
	{
		this.keywordOutput3 = keywordOutput3;
	}


	public String getKeywordOutput3()
	{
		return keywordOutput3;
	}


	public void setKeywordOutput4(String keywordOutput4)
	{
		this.keywordOutput4 = keywordOutput4;
	}


	public String getKeywordOutput4()
	{
		return keywordOutput4;
	}


	public void setKeywordOutput5(String keywordOutput5)
	{
		this.keywordOutput5 = keywordOutput5;
	}


	public String getKeywordOutput5()
	{
		return keywordOutput5;
	}


	public void setIrcHasResult(String ircHasResult)
	{
		this.ircHasResult = ircHasResult;
	}


	public String getIrcHasResult()
	{
		return ircHasResult;
	}


	public void setPanel2SubSpecDct(String panel2SubSpecDct)
	{
		this.panel2SubSpecDct = panel2SubSpecDct;
	}


	public String getPanel2SubSpecDct()
	{
		return panel2SubSpecDct;
	}


	public void setPanel8SubSequentEdt(String panel8SubSequentEdt)
	{
		this.panel8SubSequentEdt = panel8SubSequentEdt;
	}


	public String getPanel8SubSequentEdt()
	{
		return panel8SubSequentEdt;
	}


	public void setPanel8IndStmt(String panel8IndStmt)
	{
		this.panel8IndStmt = panel8IndStmt;
	}


	public String getPanel8IndStmt()
	{
		return panel8IndStmt;
	}


	public void setPanel10Explanation(String panel10Explanation)
	{
		this.panel10Explanation = panel10Explanation;
	}


	public String getPanel10Explanation()
	{
		return panel10Explanation;
	}


	public void setPanel10RoInd(String panel10RoInd)
	{
		this.panel10RoInd = panel10RoInd;
	}


	public String getPanel10RoInd()
	{
		return panel10RoInd;
	}


	public void setPanel10UrlEvidence(String panel10UrlEvidence)
	{
		this.panel10UrlEvidence = panel10UrlEvidence;
	}


	public String getPanel10UrlEvidence()
	{
		return panel10UrlEvidence;
	}


	public void setPanel10KeyEvidence(String panel10KeyEvidence)
	{
		this.panel10KeyEvidence = panel10KeyEvidence;
	}


	public String getPanel10KeyEvidence()
	{
		return panel10KeyEvidence;
	}


	public void setPanel10PtbInd(String panel10PtbInd)
	{
		this.panel10PtbInd = panel10PtbInd;
	}


	public String getPanel10PtbInd()
	{
		return panel10PtbInd;
	}


	public void setPanel10ExplainPtb(String panel10ExplainPtb)
	{
		this.panel10ExplainPtb = panel10ExplainPtb;
	}


	public String getPanel10ExplainPtb()
	{
		return panel10ExplainPtb;
	}


	public void setPanel11SubDiscInfo(String panel11SubDiscInfo)
	{
		this.panel11SubDiscInfo = panel11SubDiscInfo;
	}


	public String getPanel11SubDiscInfo()
	{
		return panel11SubDiscInfo;
	}


	public void setPanel12UrlPubLoc(String panel12UrlPubLoc)
	{
		this.panel12UrlPubLoc = panel12UrlPubLoc;
	}


	public String getPanel12UrlPubLoc()
	{
		return panel12UrlPubLoc;
	}


	public void setPanel12UrlEvidence(String panel12UrlEvidence)
	{
		this.panel12UrlEvidence = panel12UrlEvidence;
	}


	public String getPanel12UrlEvidence()
	{
		return panel12UrlEvidence;
	}


	public void setPanel12KeyEvidence(String panel12KeyEvidence)
	{
		this.panel12KeyEvidence = panel12KeyEvidence;
	}


	public String getPanel12KeyEvidence()
	{
		return panel12KeyEvidence;
	}


	public void setInpRemarks(String inpRemarks)
	{
		this.inpRemarks = inpRemarks;
	}


	public String getInpRemarks()
	{
		return inpRemarks;
	}


	public void setRaeStatusCode(String raeStatusCode)
	{
		this.raeStatusCode = raeStatusCode;
	}


	public String getRaeStatusCode()
	{
		return raeStatusCode;
	}


	public void setCitationChkCode(String citationChkCode)
	{
		this.citationChkCode = citationChkCode;
	}


	public String getCitationChkCode()
	{
		return citationChkCode;
	}


	public void setCopyrightClrCode(String copyrightClrCode)
	{
		this.copyrightClrCode = copyrightClrCode;
	}


	public String getCopyrightClrCode()
	{
		return copyrightClrCode;
	}


	public void setRoRemarks(String roRemarks)
	{
		this.roRemarks = roRemarks;
	}


	public String getRoRemarks()
	{
		return roRemarks;
	}


	public void setUrlFullVer(String urlFullVer)
	{
		this.urlFullVer = urlFullVer;
	}


	public String getUrlFullVer()
	{
		return urlFullVer;
	}


	public void setUrlRef(String urlRef)
	{
		this.urlRef = urlRef;
	}


	public String getUrlRef()
	{
		return urlRef;
	}


	public void setPhyAudioQty(int phyAudioQty)
	{
		this.phyAudioQty = phyAudioQty;
	}


	public int getPhyAudioQty()
	{
		return phyAudioQty;
	}


	public void setPhyCdQty(int phyCdQty)
	{
		this.phyCdQty = phyCdQty;
	}


	public int getPhyCdQty()
	{
		return phyCdQty;
	}


	public void setPhyDvdQty(int phyDvdQty)
	{
		this.phyDvdQty = phyDvdQty;
	}


	public int getPhyDvdQty()
	{
		return phyDvdQty;
	}


	public void setPhyPhotoQty(int phyPhotoQty)
	{
		this.phyPhotoQty = phyPhotoQty;
	}


	public int getPhyPhotoQty()
	{
		return phyPhotoQty;
	}


	public void setPhyBookQty(int phyBookQty)
	{
		this.phyBookQty = phyBookQty;
	}


	public int getPhyBookQty()
	{
		return phyBookQty;
	}


	public void setPhyUsbQty(int phyUsbQty)
	{
		this.phyUsbQty = phyUsbQty;
	}


	public int getPhyUsbQty()
	{
		return phyUsbQty;
	}


	public void setPhyOtherQty(int phyOtherQty)
	{
		this.phyOtherQty = phyOtherQty;
	}


	public int getPhyOtherQty()
	{
		return phyOtherQty;
	}


	public void setPhyOtherType(String phyOtherType)
	{
		this.phyOtherType = phyOtherType;
	}


	public String getPhyOtherType()
	{
		return phyOtherType;
	}


	public void setIsR20(String isR20)
	{
		this.isR20 = isR20;
	}


	public String getIsR20()
	{
		return isR20;
	}


	public void setInfoComp(String infoComp)
	{
		this.infoComp = infoComp;
	}


	public String getInfoComp()
	{
		return infoComp;
	}


	public void setRaeStatusCodePanel(String raeStatusCodePanel)
	{
		this.raeStatusCodePanel = raeStatusCodePanel;
	}


	public String getRaeStatusCodePanel()
	{
		return raeStatusCodePanel;
	}


	public void setRaeStatusCodeFullVer(String raeStatusCodeFullVer)
	{
		this.raeStatusCodeFullVer = raeStatusCodeFullVer;
	}


	public String getRaeStatusCodeFullVer()
	{
		return raeStatusCodeFullVer;
	}


	public void setRaeStatusCodeOthInfo(String raeStatusCodeOthInfo)
	{
		this.raeStatusCodeOthInfo = raeStatusCodeOthInfo;
	}


	public String getRaeStatusCodeOthInfo()
	{
		return raeStatusCodeOthInfo;
	}


	public void setPanel10RevInd(String panel10RevInd)
	{
		this.panel10RevInd = panel10RevInd;
	}


	public String getPanel10RevInd()
	{
		return panel10RevInd;
	}


	public void setPanel10RevExplain(String panel10RevExplain)
	{
		this.panel10RevExplain = panel10RevExplain;
	}


	public String getPanel10RevExplain()
	{
		return panel10RevExplain;
	}


	public void setPanel10OpenUrl(String panel10OpenUrl)
	{
		this.panel10OpenUrl = panel10OpenUrl;
	}


	public String getPanel10OpenUrl()
	{
		return panel10OpenUrl;
	}


	public void setNoSglCoWork(String noSglCoWork)
	{
		this.noSglCoWork = noSglCoWork;
	}


	public String getNoSglCoWork()
	{
		return noSglCoWork;
	}


	public void setJustSglCoWork(String justSglCoWork)
	{
		this.justSglCoWork = justSglCoWork;
	}


	public String getJustSglCoWork()
	{
		return justSglCoWork;
	}


	public void setRaeStatusIneligible(String raeStatusIneligible)
	{
		this.raeStatusIneligible = raeStatusIneligible;
	}


	public String getRaeStatusIneligible()
	{
		return raeStatusIneligible;
	}


	public void setUniEndorseConfInd(String uniEndorseConfInd)
	{
		this.uniEndorseConfInd = uniEndorseConfInd;
	}


	public String getUniEndorseConfInd()
	{
		return uniEndorseConfInd;
	}


	public void setPseudonym(String pseudonym)
	{
		this.pseudonym = pseudonym;
	}


	public String getPseudonym()
	{
		return pseudonym;
	}


	public void setRoleSubmitStaff(String roleSubmitStaff)
	{
		this.roleSubmitStaff = roleSubmitStaff;
	}


	public String getRoleSubmitStaff()
	{
		return roleSubmitStaff;
	}


	public void setRaeStatusDesc(String raeStatusDesc)
	{
		this.raeStatusDesc = raeStatusDesc;
	}


	public String getRaeStatusDesc()
	{
		return raeStatusDesc;
	}


	public void setRaeStatusPanelDesc(String raeStatusPanelDesc)
	{
		this.raeStatusPanelDesc = raeStatusPanelDesc;
	}


	public String getRaeStatusPanelDesc()
	{
		return raeStatusPanelDesc;
	}


	public void setRaeStatusFullVerDesc(String raeStatusFullVerDesc)
	{
		this.raeStatusFullVerDesc = raeStatusFullVerDesc;
	}


	public String getRaeStatusFullVerDesc()
	{
		return raeStatusFullVerDesc;
	}


	public void setRaeStatusOthInfoDesc(String raeStatusOthInfoDesc)
	{
		this.raeStatusOthInfoDesc = raeStatusOthInfoDesc;
	}


	public String getRaeStatusOthInfoDesc()
	{
		return raeStatusOthInfoDesc;
	}


	public void setRaeStatusIneligibleDesc(String raeStatusIneligibleDesc)
	{
		this.raeStatusIneligibleDesc = raeStatusIneligibleDesc;
	}


	public String getRaeStatusIneligibleDesc()
	{
		String raeStatusDesc = "";

		if (raeStatusIneligible != null)
		{
			if (raeStatusIneligible.equals("NIL"))
			{
				raeStatusDesc = "(NIL)";
			}
			else if (raeStatusIneligible.equals("IES"))
			{
				raeStatusDesc = "Ineligible staff";
			}
			else if (raeStatusIneligible.equals("IEO"))
			{
				raeStatusDesc = "Ineligible output";
			}
			else
			{
				raeStatusDesc = raeStatusIneligible;
			}
		}

		return raeStatusDesc;
	}


	public void setCitationChkDesc(String citationChkDesc)
	{
		this.citationChkDesc = citationChkDesc;
	}


	public String getCitationChkDesc()
	{
		return citationChkDesc;
	}


	public void setCopyrightClrDesc(String copyrightClrDesc)
	{
		this.copyrightClrDesc = copyrightClrDesc;
	}


	public String getCopyrightClrDesc()
	{
		return copyrightClrDesc;
	}


	public void setCountFileSupDoc(int countFileSupDoc)
	{
		this.countFileSupDoc = countFileSupDoc;
	}


	public int getCountFileSupDoc()
	{
		return countFileSupDoc;
	}


	public void setCountFileFullVer(int countFileFullVer)
	{
		this.countFileFullVer = countFileFullVer;
	}


	public int getCountFileFullVer()
	{
		return countFileFullVer;
	}


	public void setCountFileAbstract(int countFileAbstract)
	{
		this.countFileAbstract = countFileAbstract;
	}


	public int getCountFileAbstract()
	{
		return countFileAbstract;
	}


	public void setCountFileP10Ev(int countFileP10Ev)
	{
		this.countFileP10Ev = countFileP10Ev;
	}


	public int getCountFileP10Ev()
	{
		return countFileP10Ev;
	}


	public void setCountFileP12Ev(int countFileP12Ev)
	{
		this.countFileP12Ev = countFileP12Ev;
	}


	public int getCountFileP12Ev()
	{
		return countFileP12Ev;
	}


	public void setCountFileAddInfo(int countFileAddInfo)
	{
		this.countFileAddInfo = countFileAddInfo;
	}


	public int getCountFileAddInfo()
	{
		return countFileAddInfo;
	}


	public void setRoRemarksLib(String roRemarksLib)
	{
		this.roRemarksLib = roRemarksLib;
	}


	public String getRoRemarksLib()
	{
		return roRemarksLib;
	}


	public void setCountFileCoWork(int countFileCoWork)
	{
		this.countFileCoWork = countFileCoWork;
	}


	public int getCountFileCoWork()
	{
		return countFileCoWork;
	}


	public void setEissn(String eissn)
	{
		this.eissn = eissn;
	}


	public String getEissn()
	{
		return eissn;
	}


	public void setArticleNo(String articleNo)
	{
		this.articleNo = articleNo;
	}


	public String getArticleNo()
	{
		return articleNo;
	}


	public void setCitationChkAbsCode(String citationChkAbsCode)
	{
		this.citationChkAbsCode = citationChkAbsCode;
	}


	public String getCitationChkAbsCode()
	{
		return citationChkAbsCode;
	}


	public void setCitationChkAbsDesc(String citationChkAbsDesc)
	{
		this.citationChkAbsDesc = citationChkAbsDesc;
	}


	public String getCitationChkAbsDesc()
	{
		return citationChkAbsDesc;
	}


	public void setCitationChkFulltextCode(String citationChkFulltextCode)
	{
		this.citationChkFulltextCode = citationChkFulltextCode;
	}


	public String getCitationChkFulltextCode()
	{
		return citationChkFulltextCode;
	}


	public void setCitationChkFulltextDesc(String citationChkFulltextDesc)
	{
		this.citationChkFulltextDesc = citationChkFulltextDesc;
	}


	public String getCitationChkFulltextDesc()
	{
		return citationChkFulltextDesc;
	}


	
	public String getAuthorListWithDeptAndAuthType()
	{
		return authorListWithDeptAndAuthType;
	}


	
	public void setAuthorListWithDeptAndAuthType(String authorListWithDeptAndAuthType)
	{
		this.authorListWithDeptAndAuthType = authorListWithDeptAndAuthType;
	}
	

	
	public String getAuthorship()
	{
		return authorship;
	}


	
	public void setAuthorship(String authorship)
	{
		this.authorship = authorship;
	}


	
	public String getDepartment()
	{
		return department;
	}


	
	public void setDepartment(String department)
	{
		this.department = department;
	}


	
	public String getSapReferedJournal()
	{
		return sapReferedJournal;
	}


	
	public void setSapReferedJournal(String sapReferedJournal)
	{
		this.sapReferedJournal = sapReferedJournal;
	}


	
	public String getUrl()
	{
		return url;
	}


	
	public void setUrl(String url)
	{
		this.url = url;
	}


	
	public String getPageNumFrom()
	{
		return pageNumFrom;
	}


	
	public void setPageNumFrom(String pageNumFrom)
	{
		this.pageNumFrom = pageNumFrom;
	}


	
	public String getPageNumTo()
	{
		return pageNumTo;
	}


	
	public void setPageNumTo(String pageNumTo)
	{
		this.pageNumTo = pageNumTo;
	}


	
	public String getNameOtherPos()
	{
		return nameOtherPos;
	}


	
	public void setNameOtherPos(String nameOtherPos)
	{
		this.nameOtherPos = nameOtherPos;
	}


	
	public String getSchCode()
	{
		return schCode;
	}


	
	public void setSchCode(String schCode)
	{
		this.schCode = schCode;
	}


	
	public String getSchDtlCode()
	{
		return schDtlCode;
	}


	
	public void setSchDtlCode(String schDtlCode)
	{
		this.schDtlCode = schDtlCode;
	}


	
	public String getDaCode()
	{
		return daCode;
	}


	
	public void setDaCode(String daCode)
	{
		this.daCode = daCode;
	}


	
	public String getDaDtlCode()
	{
		return daDtlCode;
	}


	
	public void setDaDtlCode(String daDtlCode)
	{
		this.daDtlCode = daDtlCode;
	}


	
	public String getOtherDaDtl()
	{
		return otherDaDtl;
	}


	
	public void setOtherDaDtl(String otherDaDtl)
	{
		this.otherDaDtl = otherDaDtl;
	}


	
	public String getCdcf_selected_ind()
	{
		return cdcf_selected_ind;
	}


	
	public void setCdcf_selected_ind(String cdcf_selected_ind)
	{
		this.cdcf_selected_ind = cdcf_selected_ind;
	}


	
	public String getIs_rgc_proj()
	{
		return is_rgc_proj;
	}


	
	public void setIs_rgc_proj(String is_rgc_proj)
	{
		this.is_rgc_proj = is_rgc_proj;
	}


	
	public String getRgc_proj_num()
	{
		return rgc_proj_num;
	}


	
	public void setRgc_proj_num(String rgc_proj_num)
	{
		this.rgc_proj_num = rgc_proj_num;
	}


	
	public String getOpen_access_stat()
	{
		return open_access_stat;
	}


	
	public void setOpen_access_stat(String open_access_stat)
	{
		this.open_access_stat = open_access_stat;
	}

	
	
	public String getOpen_access_statStr()
	{
		if(open_access_statStr == null) {
			open_access_statStr = "";
			if(open_access_stat != null) {
				if(open_access_stat.equals("N")) open_access_statStr = "Non-open Access";
				else if(open_access_stat.equals("E")) open_access_statStr = "Embargoed Open Access";
				else if(open_access_stat.equals("I")) open_access_statStr = "Immediate Open Access";
			}
		}
		return open_access_statStr;
	}


	
	public void setOpen_access_statStr(String open_access_statStr)
	{
		this.open_access_statStr = open_access_statStr;
	}


	public String getOpen_access_apc()
	{
		return open_access_apc;
	}


	
	public void setOpen_access_apc(String open_access_apc)
	{
		this.open_access_apc = open_access_apc;
	}


	
	public String getOpen_access_apc_payment()
	{
		return open_access_apc_payment;
	}


	
	public void setOpen_access_apc_payment(String open_access_apc_payment)
	{
		this.open_access_apc_payment = open_access_apc_payment;
	}


	



	
	public String getApc_val()
	{
		return apc_val;
	}


	
	public void setApc_val(String apc_val)
	{
		this.apc_val = apc_val;
	}





	
	
	
	public String getOpen_access_art_acc_date()
	{
		if(open_access_art_acc_date == null) {
			if(open_access_art_acc_day != null && 
					open_access_art_acc_month != null && 
					open_access_art_acc_year != null) {
				open_access_art_acc_date = open_access_art_acc_day + "/" + 
										   open_access_art_acc_month + "/" + 
										   open_access_art_acc_year;
			}
			else if(open_access_art_acc_month != null && 
					open_access_art_acc_year != null) {
				open_access_art_acc_date = open_access_art_acc_month + "/" + 
						   				   open_access_art_acc_year;
			}
			else {
				open_access_art_acc_date = "";
			}
		}
		return open_access_art_acc_date;
	}


	
	public void setOpen_access_art_acc_date(String open_access_art_acc_date)
	{
		this.open_access_art_acc_date = open_access_art_acc_date;
	}


	public String getOpen_access_art_acc_day()
	{
		return open_access_art_acc_day;
	}


	
	public void setOpen_access_art_acc_day(String open_access_art_acc_day)
	{
		this.open_access_art_acc_day = open_access_art_acc_day;
	}


	
	public String getOpen_access_art_acc_month()
	{
		return open_access_art_acc_month;
	}


	
	public void setOpen_access_art_acc_month(String open_access_art_acc_month)
	{
		this.open_access_art_acc_month = open_access_art_acc_month;
	}


	
	public String getOpen_access_art_acc_year()
	{
		return open_access_art_acc_year;
	}


	
	public void setOpen_access_art_acc_year(String open_access_art_acc_year)
	{
		this.open_access_art_acc_year = open_access_art_acc_year;
	}


	
	public String getOpen_access_emb_end_date()
	{
		if(open_access_emb_end_date == null) {
			if(open_access_emb_end_month != null && 
					open_access_emb_end_year != null) {
				open_access_emb_end_date = open_access_emb_end_month + "/" + 
										   open_access_emb_end_year;
			}
			else {
				open_access_emb_end_date = "";
			}
		}
		return open_access_emb_end_date;
	}


	
	public void setOpen_access_emb_end_date(String open_access_emb_end_date)
	{
		this.open_access_emb_end_date = open_access_emb_end_date;
	}


	public String getOpen_access_emb_end_month()
	{
		return open_access_emb_end_month;
	}


	
	public void setOpen_access_emb_end_month(String open_access_emb_end_month)
	{
		this.open_access_emb_end_month = open_access_emb_end_month;
	}


	
	public String getOpen_access_emb_end_year()
	{
		return open_access_emb_end_year;
	}


	
	public void setOpen_access_emb_end_year(String open_access_emb_end_year)
	{
		this.open_access_emb_end_year = open_access_emb_end_year;
	}
	

	
	
	public String getOpen_access_emb_period_date()
	{
		if(open_access_emb_period_date == null) {
			if(open_access_emb_period_month != null) {
				open_access_emb_period_date = open_access_emb_period_month + "m ";
			}
			if(open_access_emb_period_year != null) {
				if(open_access_emb_period_date == null) open_access_emb_period_date = "";
				open_access_emb_period_date += open_access_emb_period_year + "y";
			}
			if(open_access_emb_period_date == null) {
				open_access_emb_period_date = "";
			}
		}
		return open_access_emb_period_date;
	}


	
	public void setOpen_access_emb_period_date(String open_access_emb_period_date)
	{
		this.open_access_emb_period_date = open_access_emb_period_date;
	}


	public String getOpen_access_emb_period_month()
	{
		return open_access_emb_period_month;
	}


	
	public void setOpen_access_emb_period_month(String open_access_emb_period_month)
	{
		this.open_access_emb_period_month = open_access_emb_period_month;
	}


	
	public String getOpen_access_emb_period_year()
	{
		return open_access_emb_period_year;
	}


	
	public void setOpen_access_emb_period_year(String open_access_emb_period_year)
	{
		this.open_access_emb_period_year = open_access_emb_period_year;
	}
	
	
	public String getOpen_access_payment()
	{
		return open_access_payment;
	}


	
	public void setOpen_access_payment(String open_access_payment)
	{
		this.open_access_payment = open_access_payment;
	}


	public Date getCal_emb_end_date() throws ParseException
	{
		
		if (!Strings.isNullOrEmpty(open_access_emb_end_month) && !Strings.isNullOrEmpty(open_access_emb_end_year)) {
			String emb_end_date="1/"+open_access_emb_end_month+"/"+open_access_emb_end_year;  
			cal_emb_end_date=new SimpleDateFormat("dd/MM/yyyy").parse(emb_end_date);  
		}else if(!Strings.isNullOrEmpty(open_access_art_acc_day) && !Strings.isNullOrEmpty(open_access_art_acc_month) && !Strings.isNullOrEmpty(open_access_art_acc_year)){
			String art_acc_date_string=open_access_art_acc_day+"/"+open_access_art_acc_month+"/"+open_access_art_acc_year;  
			Date art_acc_date=new SimpleDateFormat("dd/MM/yyyy").parse(art_acc_date_string);  
			
			int year = 0 ;	
			if (!Strings.isNullOrEmpty(open_access_emb_period_year))
				year = Integer.parseInt(open_access_emb_period_year);
			
			int month = 0;
			if (!Strings.isNullOrEmpty(open_access_emb_period_month)) 
				month = Integer.parseInt(open_access_emb_period_month);
			
			
			Calendar c = Calendar.getInstance();
			c.setTime(art_acc_date);
			c.add(Calendar.YEAR, year);
			c.add(Calendar.MONTH, month);
			cal_emb_end_date = c.getTime();	
			
		}else {
			cal_emb_end_date = null;
		}
		return cal_emb_end_date;
	}


	
	public void setCal_emb_end_date(Date cal_emb_end_date)
	{
		this.cal_emb_end_date = cal_emb_end_date;
	}


	
	public String getDelay_open_access() throws ParseException
	{
		if (getCal_emb_end_date() != null) {
			SysParamDAO sDao = SysParamDAO.getInstance();
			String cDateString = sDao.getSysParamValueByCode("CENSUS_DATE");
			SimpleDateFormat dt = new SimpleDateFormat("dd/MM/yyyy"); 
			Date cDateMin = dt.parse(cDateString);  
			Calendar c = Calendar.getInstance();
			c.setTime(cDateMin);
			c.add(Calendar.YEAR, 1);
			Date cDateMax = c.getTime();	
			if (cal_emb_end_date.compareTo(cDateMin) > 0 && cal_emb_end_date.compareTo(cDateMax) < 1) {
				delay_open_access  = "Yes";
			}else {
				delay_open_access = "No";
			}
		}
		return delay_open_access;
	}


	
	public void setDelay_open_access(String delay_open_access)
	{
		this.delay_open_access = delay_open_access;
	}


	
	public String getEmb_date_after_census() throws ParseException
	{
		if (getCal_emb_end_date() != null) {
			SysParamDAO sDao = SysParamDAO.getInstance();
			String cDateString = sDao.getSysParamValueByCode("CENSUS_DATE");
			SimpleDateFormat dt = new SimpleDateFormat("dd/MM/yyyy"); 
			Date cDateMin = dt.parse(cDateString);
			if (cal_emb_end_date.compareTo(cDateMin) > 0) {
				emb_date_after_census  = "Yes";
			}else {
				emb_date_after_census = "No";
			}
		}
		return emb_date_after_census;
	}


	
	public void setEmb_date_after_census(String emb_date_after_census)
	{
		this.emb_date_after_census = emb_date_after_census;
	}


	public String getIs_tran_agrt()
	{
		return is_tran_agrt;
	}


	
	public void setIs_tran_agrt(String is_tran_agrt)
	{
		this.is_tran_agrt = is_tran_agrt;
	}


	
	public String getTran_agrt_val()
	{
		return tran_agrt_val;
	}


	
	public void setTran_agrt_val(String tran_agrt_val)
	{
		this.tran_agrt_val = tran_agrt_val;
	}


	
	public String getExceed_char_ind()
	{
		if(exceed_char_ind == null && getAuthorListWithDeptAndAuthType() != null) {
			if(getAuthorListWithDeptAndAuthType().contains("*")) exceed_char_ind = "Yes";
			else exceed_char_ind = "No";
		}
		return exceed_char_ind;
	}


	
	public void setExceed_char_ind(String exceed_char_ind)
	{
		this.exceed_char_ind = exceed_char_ind;
	}


	
	public String getAccessibility() throws ParseException
	{
		if(accessibility == null) {
			// 1
			if("I".equals(getOpen_access_stat()) &&
				"N".equals(getOpen_access_apc()) &&
				"N".equals(getOpen_access_apc_payment()) &&
				"N".equals(getOpen_access_payment()) &&
				getEmb_date_after_census() == null &&
				getDelay_open_access() == null &&
				("Y").equals(getIs_tran_agrt())) {
				accessibility = "1";
			}
			else if("N".equals(getOpen_access_stat()) &&
					getOpen_access_apc() == null &&
					getOpen_access_apc_payment() == null &&
					getOpen_access_payment() == null &&
					getEmb_date_after_census() == null &&
					getDelay_open_access() == null &&
					("Y").equals(getIs_tran_agrt())) {
				accessibility = "1";
			}
			// 2
			else if("I".equals(getOpen_access_stat()) &&
					"Y".equals(getOpen_access_apc()) &&
					getOpen_access_apc_payment() != null &&
					"Y".equals(getOpen_access_payment()) &&
					getEmb_date_after_census() == null &&
					getDelay_open_access() == null &&
					getIs_tran_agrt() != null) {
				accessibility = "2";
			}
			else if("E".equals(getOpen_access_stat()) &&
					"Y".equals(getOpen_access_apc()) &&
					getOpen_access_apc_payment() != null &&
					"Y".equals(getOpen_access_payment()) &&
					("No").equals(getEmb_date_after_census()) &&
					("No").equals(getDelay_open_access()) &&
					getIs_tran_agrt() != null) {
				accessibility = "2";
			}
			// 3
			else if("I".equals(getOpen_access_stat()) &&
					"N".equals(getOpen_access_apc()) &&
					"Y".equals(getOpen_access_apc_payment()) &&
					"Y".equals(getOpen_access_payment()) &&
					getEmb_date_after_census() == null &&
					getDelay_open_access() == null &&
					getIs_tran_agrt() != null) {
				accessibility = "3";
			}
			else if("E".equals(getOpen_access_stat()) &&
					"N".equals(getOpen_access_apc()) &&
					"Y".equals(getOpen_access_apc_payment()) &&
					"Y".equals(getOpen_access_payment()) &&
					("No").equals(getEmb_date_after_census()) &&
					("No").equals(getDelay_open_access()) &&
					getIs_tran_agrt() != null) {
				accessibility = "3";
			}
			// 4
			else if("I".equals(getOpen_access_stat()) &&
					getOpen_access_apc() != null &&
					getOpen_access_apc_payment() != null &&
					"N".equals(getOpen_access_payment()) &&
					getEmb_date_after_census() == null  &&
					getDelay_open_access() == null &&
					getIs_tran_agrt() != null) {
				accessibility = "4";
			}
			else if("E".equals(getOpen_access_stat()) &&
					getOpen_access_apc() != null &&
					getOpen_access_apc_payment() != null &&
					"N".equals(getOpen_access_payment()) &&
					("No").equals(getEmb_date_after_census()) &&
					("No").equals(getDelay_open_access()) &&
					getIs_tran_agrt() != null) {
				accessibility = "4";
			}
			// 5
			else if("E".equals(getOpen_access_stat()) &&
					getOpen_access_apc() != null && 
					getOpen_access_apc_payment() != null &&
					getOpen_access_payment() != null && 
					("Yes").equals(getEmb_date_after_census()) &&
					("Yes").equals(getDelay_open_access()) &&
					getIs_tran_agrt() != null) {
				accessibility = "5";
			}
			// 6
			else if("E".equals(getOpen_access_stat()) &&
					getOpen_access_apc() != null && 
					getOpen_access_apc_payment() != null &&
					getOpen_access_payment() != null && 
					("Yes").equals(getEmb_date_after_census()) &&
					("No").equals(getDelay_open_access()) &&
					getIs_tran_agrt() != null) {
				accessibility = "6";
			}
			// 7
			else if("N".equals(getOpen_access_stat()) &&
					getOpen_access_apc() == null &&
					getOpen_access_apc_payment() == null &&
					getOpen_access_payment() == null &&
					getEmb_date_after_census() == null &&
					getDelay_open_access() == null &&
					("N").equals(getIs_tran_agrt())) {
				accessibility = "7";
			}
			else {
				accessibility = "99";
			}
				
		}
		return accessibility;
	}


	
	public void setAccessibility(String accessibility)
	{
		this.accessibility = accessibility;
	}


	
	public List<OutputDetails_P> getpDtlList()
	{
		return pDtlList;
	}


	
	public void setpDtlList(List<OutputDetails_P> pDtlList)
	{
		this.pDtlList = pDtlList;
	}

	
	public int getNoPDtl()
	{
		return noPDtl;
	}


	
	public void setNoPDtl(int noPDtl)
	{
		this.noPDtl = noPDtl;
	}


	
	public Double getWeighting()
	{
		return weighting;
	}


	
	public void setWeighting(Double weighting)
	{
		this.weighting = weighting;
	}
	
	
	public int getDcc_percentage()
	{
		return dcc_percentage;
	}

	
	public void setDcc_percentage(int dcc_percentage)
	{
		this.dcc_percentage = dcc_percentage;
	}


	public static String removeTailChar(String inString, char inChar)
	{
		String outString = (inString != null) ? inString.trim() : "";

		if (outString.charAt(outString.length() - 1) == inChar)
		{
			outString = outString.substring(0, outString.length() - 1);
		}

		return outString;
	}


	// remove all html tags from a string
	public static String removeHTML(String htmlString)
	{
		// Remove HTML tag from java String
		String noHTMLString = htmlString.replaceAll("\\<.*?\\>", "");

		// Remove Carriage return from java String
		noHTMLString = noHTMLString.replaceAll("<br/>", " ");

		// Remove New line from java string and replace html break
		noHTMLString = noHTMLString.replaceAll("\n", " ");
		noHTMLString = noHTMLString.replaceAll("\r", " ");

		// noHTMLString = noHTMLString.replaceAll("\'", "&#39;");
		// noHTMLString = noHTMLString.replaceAll("\"", "&quot;");
		return noHTMLString;
	}
	
	
	
	public String getSdg_str()
	{
		return sdg_str;
	}


	
	public void setSdg_str(String sdg_str)
	{
		this.sdg_str = sdg_str;
	}


	public static String getMonth(int month)
	{
		return new DateFormatSymbols(Locale.US).getMonths()[month - 1];
	}


	private String escape(String raw)
	{
		String escaped = raw;
		escaped = escaped.replace("\\", "\\\\");
		escaped = escaped.replace("\"", "\\\"");
		escaped = escaped.replace("\b", "\\b");
		escaped = escaped.replace("\f", "\\f");
		escaped = escaped.replace("\n", "\\n");
		escaped = escaped.replace("\r", "\\r");
		escaped = escaped.replace("\t", "\\t");
		// TODO: escape other non-printing characters using uXXXX notation
		return escaped;
	}
	
	public ResourceBundle getResourceBundle()
	{
		ResourceBundle bundle = MessageBundle.getResourceBundle();
		return bundle;
	}
}