package hk.eduhk.rich.entity.publication;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.param.SysParamDAO;

@Entity
@Table(name = "RH_P_ACAD_PROF_OUTPUT_HDR")
public class OutputHeader_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(OutputHeader_P.class.toString());
	
	@EmbeddedId
	private OutputHeader_P_PK pk = new OutputHeader_P_PK();
	
	@Column(name = "apa_citation")
	private String apa_citation;

	@Column(name = "article_num")
	private String article_num;	
	
	@Column(name = "census_date")
	private Date census_date;

	@Column(name = "city")
	private String city;
	
	@Column(name = "concatenated_author_name")
	private String concatenated_author_name;
	
	@Column(name = "da_code")
	private String da_code;
	
	@Column(name = "da_dtl_code")
	private String da_dtl_code;
	
	@Column(name = "doi")
	private String doi;
	
	@Column(name = "eissn")
	private String eissn;
	
	@Column(name = "from_month")
	private Integer from_month;
	
	@Column(name = "from_year")
	private Integer from_year;
	
	@Column(name = "fulltext_url")
	private String fulltext_url;
	
	@Column(name = "ied_work_ind")
	private String ied_work_ind;
	
	@Column(name = "issn")
	private String issn;
	
	@Column(name = "isbn")
	private String isbn;
	
	@Column(name = "is_enh_high_edu")
	private String is_enh_high_edu;
	
	@Column(name = "is_intl_conf")
	private String is_intl_conf;
	
	@Column(name = "journal_code")
	private String journal_code;
	
	@Column(name = "journal_publication_discipline")
	private String journal_publication_discipline;
	
	@Column(name = "key_research_areas")
	private String key_research_areas;
	
	@Column(name = "language")
	private String language;
	
	@Column(name = "name_other_editors")
	private String name_other_editors;
	
	@Column(name = "name_other_pos")
	private String name_other_pos;
	
	@Column(name = "other_da_dtl")
	private String other_da_dtl;
	
	@Column(name = "other_details")
	private String other_details;
	
	@Column(name = "other_details_continue")
	private String other_details_continue;
	
	@Column(name = "other_key_research_areas")
	private String other_key_research_areas;
	
	@Column(name = "output_title_continue")
	private String output_title_continue;
	
	@Column(name = "page_num")
	private String page_num;
	
	@Column(name = "page_num_from")
	private String page_num_from;
	
	@Column(name = "page_num_to")
	private String page_num_to;
	
	@Column(name = "publisher")
	private String publisher;
	
	@Column(name = "research_activity_ranking")
	private String research_activity_ranking;
	
	@Column(name = "rs_code")
	private String rs_code;
	
	@Column(name = "rs_dtl_code")
	private String rs_dtl_code;
	
	@Column(name = "sap_output_type")
	private String sap_output_type;
	
	@Column(name = "sap_refered_journal")
	private String sap_refered_journal;
	
	@Column(name = "sch_code")
	private String sch_code;
	
	@Column(name = "sch_dtl_code")
	private String sch_dtl_code;
	
	@Column(name = "title_jour_book")
	private String title_jour_book;
	
	@Column(name = "title_paper_art")
	private String title_paper_art;
	
	@Column(name = "total_no_of_author")
	private Integer total_no_of_author;
	
	@Column(name = "to_month")
	private Integer to_month;
	
	@Column(name = "to_year")
	private Integer to_year;
	
	@Column(name = "vol_issue")
	private String vol_issue;
	
	@Column(name = "is_rgc_proj")
	private String is_rgc_proj;
	
	@Column(name = "rgc_proj_num")
	private String rgc_proj_num;
	
	@Column(name = "open_access_stat")
	private String open_access_stat;
	
	@Column(name = "open_access_payment")
	private String open_access_payment;
	
	@Column(name = "open_access_apc")
	private String open_access_apc;
	
	@Column(name = "open_access_nonapc_payment")
	private String open_access_apc_payment;
	
	@Column(name = "apc_val")
	private Double apc_val;
	
	@Column(name = "open_access_art_acc_day")
	private Integer open_access_art_acc_day;
	
	@Column(name = "open_access_art_acc_month")
	private Integer open_access_art_acc_month;
	
	@Column(name = "open_access_art_acc_year")
	private Integer open_access_art_acc_year;
	
	@Column(name = "open_access_emb_end_month")
	private Integer open_access_emb_end_month;
	
	@Column(name = "open_access_emb_end_year")
	private Integer open_access_emb_end_year;
	
	@Column(name = "open_access_emb_period_month")
	private Integer open_access_emb_period_month;
	
	@Column(name = "open_access_emb_period_year")
	private Integer open_access_emb_period_year;
	
	@Column(name = "is_tran_agrt")
	private String is_tran_agrt;
	
	@Column(name = "tran_agrt_val")
	private Double tran_agrt_val;
	
	@Column(name = "sdg_code")
	private String sdg_code;

	@Transient
	private String output_lookup_code = null;
	
	@Transient
	private String apa_html;

	
	@Transient
	private Date startDate;
	
	@Transient
	private Date endDate;
	
	
	@Transient
	private String sdg_info;

	@Transient
	private List<String> sdg_code_list;
	
	public OutputHeader_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(OutputHeader_P_PK pk)
	{
		this.pk = pk;
	}
	
	
	public String getSdg_info()
	{
		if (getSdg_code_list() != null) {
			List<String> sdg_info_list = new ArrayList<String>();
			
			for(String sdg_code : sdg_code_list)
				sdg_info_list.add(sdg_code+" - "+LookupValueDAO.getInstance().getLookupValue("SDG",sdg_code,"US").getDescription()) ;
			
			sdg_info = String.join(", ", sdg_info_list);
		}
		return sdg_info;
	}



	
	public void setSdg_info(String sdg_info)
	{
		this.sdg_info = sdg_info;
	}

	
	
	public List<String> getSdg_code_list()
	{
		if (sdg_code != null && sdg_code_list == null )
			sdg_code_list =  Stream.of(getSdg_code().split(",")).collect(Collectors.toList());
		
		return sdg_code_list;
	}


	
	public void setSdg_code_list(List<String> sdg_code_list)
	{
		this.sdg_code_list = sdg_code_list;
	}


	public String getOutput_lookup_code()
	{
		return output_lookup_code;
	}


	
	public void setOutput_lookup_code(String output_lookup_code)
	{
		this.output_lookup_code = output_lookup_code;
	}


	
	public String getApa_html()
	{
		return apa_html;
	}


	
	public void setApa_html(String apa_html)
	{
		this.apa_html = apa_html;
	}


	public String getApa_citation()
	{
		return apa_citation;
	}


	
	public void setApa_citation(String apa_citation)
	{
		this.apa_citation = apa_citation;
	}


	
	public String getArticle_num()
	{
		return article_num;
	}


	
	public void setArticle_num(String article_num)
	{
		this.article_num = article_num;
	}

	
	
	
	public String getSdg_code()
	{
		return sdg_code;
	}


	
	public void setSdg_code(String sdg_code)
	{
		this.sdg_code = sdg_code;
	}


	public Date getCensus_date()
	{
		return census_date;
	}


	
	public void setCensus_date(Date census_date)
	{
		this.census_date = census_date;
	}


	
	public String getCity()
	{
		return city;
	}


	
	public void setCity(String city)
	{
		this.city = city;
	}


	
	public String getConcatenated_author_name()
	{
		return concatenated_author_name;
	}


	
	public void setConcatenated_author_name(String concatenated_author_name)
	{
		this.concatenated_author_name = concatenated_author_name;
	}


	
	public String getDa_code()
	{
		return da_code;
	}


	
	public void setDa_code(String da_code)
	{
		this.da_code = da_code;
	}


	
	public String getDa_dtl_code()
	{
		return da_dtl_code;
	}


	
	public void setDa_dtl_code(String da_dtl_code)
	{
		this.da_dtl_code = da_dtl_code;
	}


	
	public String getDoi()
	{
		return doi;
	}


	
	public void setDoi(String doi)
	{
		this.doi = doi;
	}


	
	public String getEissn()
	{
		return eissn;
	}


	
	public void setEissn(String eissn)
	{
		this.eissn = eissn;
	}


	
	public Integer getFrom_month()
	{
		return from_month;
	}


	
	public void setFrom_month(Integer from_month)
	{
		this.from_month = from_month;
	}


	
	public Integer getFrom_year()
	{
		return from_year;
	}


	
	public void setFrom_year(Integer from_year)
	{
		this.from_year = from_year;
	}


	
	public String getFulltext_url()
	{
		return fulltext_url;
	}


	
	public void setFulltext_url(String fulltext_url)
	{
		this.fulltext_url = fulltext_url;
	}


	
	public String getIed_work_ind()
	{
		return ied_work_ind;
	}


	
	public void setIed_work_ind(String ied_work_ind)
	{
		this.ied_work_ind = ied_work_ind;
	}


	
	public String getIssn()
	{
		return issn;
	}


	
	public void setIssn(String issn)
	{
		this.issn = issn;
	}


	
	public String getIs_enh_high_edu()
	{
		return is_enh_high_edu;
	}


	
	public void setIs_enh_high_edu(String is_enh_high_edu)
	{
		this.is_enh_high_edu = is_enh_high_edu;
	}


	
	public String getIs_intl_conf()
	{
		return is_intl_conf;
	}


	
	public void setIs_intl_conf(String is_intl_conf)
	{
		this.is_intl_conf = is_intl_conf;
	}


	
	public String getJournal_code()
	{
		return journal_code;
	}


	
	public void setJournal_code(String journal_code)
	{
		this.journal_code = journal_code;
	}


	
	public String getJournal_publication_discipline()
	{
		return journal_publication_discipline;
	}


	
	public void setJournal_publication_discipline(String journal_publication_discipline)
	{
		this.journal_publication_discipline = journal_publication_discipline;
	}


	
	public String getKey_research_areas()
	{
		return key_research_areas;
	}


	
	public void setKey_research_areas(String key_research_areas)
	{
		this.key_research_areas = key_research_areas;
	}


	
	public String getLanguage()
	{
		return language;
	}


	
	public void setLanguage(String language)
	{
		this.language = language;
	}


	
	public String getName_other_editors()
	{
		return name_other_editors;
	}


	
	public void setName_other_editors(String name_other_editors)
	{
		this.name_other_editors = name_other_editors;
	}


	
	public String getName_other_pos()
	{
		return name_other_pos;
	}


	
	public void setName_other_pos(String name_other_pos)
	{
		this.name_other_pos = name_other_pos;
	}


	
	public String getOther_da_dtl()
	{
		return other_da_dtl;
	}


	
	public void setOther_da_dtl(String other_da_dtl)
	{
		this.other_da_dtl = other_da_dtl;
	}


	
	public String getOther_details()
	{
		return other_details;
	}


	
	public void setOther_details(String other_details)
	{
		this.other_details = other_details;
	}


	
	public String getOther_details_continue()
	{
		return other_details_continue;
	}


	
	public void setOther_details_continue(String other_details_continue)
	{
		this.other_details_continue = other_details_continue;
	}


	
	public String getOther_key_research_areas()
	{
		return other_key_research_areas;
	}


	
	public void setOther_key_research_areas(String other_key_research_areas)
	{
		this.other_key_research_areas = other_key_research_areas;
	}


	
	public String getOutput_title_continue()
	{
		return output_title_continue;
	}


	
	public void setOutput_title_continue(String output_title_continue)
	{
		this.output_title_continue = output_title_continue;
	}


	
	public String getPage_num()
	{
		return page_num;
	}


	
	public void setPage_num(String page_num)
	{
		this.page_num = page_num;
	}


	
	public String getPage_num_from()
	{
		return page_num_from;
	}


	
	public void setPage_num_from(String page_num_from)
	{
		this.page_num_from = page_num_from;
	}


	
	public String getPage_num_to()
	{
		return page_num_to;
	}


	
	public void setPage_num_to(String page_num_to)
	{
		this.page_num_to = page_num_to;
	}


	
	public String getPublisher()
	{
		return publisher;
	}


	
	public void setPublisher(String publisher)
	{
		this.publisher = publisher;
	}


	
	public String getResearch_activity_ranking()
	{
		return research_activity_ranking;
	}


	
	public void setResearch_activity_ranking(String research_activity_ranking)
	{
		this.research_activity_ranking = research_activity_ranking;
	}


	
	public String getRs_code()
	{
		return rs_code;
	}


	
	public void setRs_code(String rs_code)
	{
		this.rs_code = rs_code;
	}


	
	public String getRs_dtl_code()
	{
		return rs_dtl_code;
	}


	
	public void setRs_dtl_code(String rs_dtl_code)
	{
		this.rs_dtl_code = rs_dtl_code;
	}


	
	public String getSap_output_type()
	{
		return sap_output_type;
	}


	
	public void setSap_output_type(String sap_output_type)
	{
		this.sap_output_type = sap_output_type;
	}


	
	public String getSap_refered_journal()
	{
		return sap_refered_journal;
	}


	
	public void setSap_refered_journal(String sap_refered_journal)
	{
		this.sap_refered_journal = sap_refered_journal;
	}


	
	public String getSch_code()
	{
		return sch_code;
	}


	
	public void setSch_code(String sch_code)
	{
		this.sch_code = sch_code;
	}


	
	public String getSch_dtl_code()
	{
		return sch_dtl_code;
	}


	
	public void setSch_dtl_code(String sch_dtl_code)
	{
		this.sch_dtl_code = sch_dtl_code;
	}


	
	public String getTitle_jour_book()
	{
		return title_jour_book;
	}


	
	public void setTitle_jour_book(String title_jour_book)
	{
		this.title_jour_book = title_jour_book;
	}


	
	public String getTitle_paper_art()
	{
		return title_paper_art;
	}


	
	public void setTitle_paper_art(String title_paper_art)
	{
		this.title_paper_art = title_paper_art;
	}


	
	public Integer getTotal_no_of_author()
	{
		return total_no_of_author;
	}


	
	public void setTotal_no_of_author(Integer total_no_of_author)
	{
		this.total_no_of_author = total_no_of_author;
	}


	
	public Integer getTo_month()
	{
		return to_month;
	}


	
	public void setTo_month(Integer to_month)
	{
		this.to_month = to_month;
	}


	
	public Integer getTo_year()
	{
		return to_year;
	}


	
	public void setTo_year(Integer to_year)
	{
		this.to_year = to_year;
	}


	
	public String getVol_issue()
	{
		return vol_issue;
	}


	
	public void setVol_issue(String vol_issue)
	{
		this.vol_issue = vol_issue;
	}


	
	
	public String getIs_rgc_proj()
	{
		return is_rgc_proj;
	}


	
	public void setIs_rgc_proj(String is_rgc_proj)
	{
		this.is_rgc_proj = is_rgc_proj;
	}


	
	public String getRgc_proj_num()
	{
		return rgc_proj_num;
	}


	
	public void setRgc_proj_num(String rgc_proj_num)
	{
		this.rgc_proj_num = rgc_proj_num;
	}


	
	public String getOpen_access_stat()
	{
		return open_access_stat;
	}


	
	public void setOpen_access_stat(String open_access_stat)
	{
		this.open_access_stat = open_access_stat;
	}


	
	
	public String getOpen_access_payment()
	{
		return open_access_payment;
	}


	
	public void setOpen_access_payment(String open_access_payment)
	{
		this.open_access_payment = open_access_payment;
	}


	public String getOpen_access_apc()
	{
		return open_access_apc;
	}


	
	public void setOpen_access_apc(String open_access_apc)
	{
		this.open_access_apc = open_access_apc;
	}


	
	public String getOpen_access_apc_payment()
	{
		return open_access_apc_payment;
	}


	
	public void setOpen_access_apc_payment(String open_access_apc_payment)
	{
		this.open_access_apc_payment = open_access_apc_payment;
	}


	
	public Double getApc_val()
	{
		return apc_val;
	}


	
	public void setApc_val(Double apc_val)
	{
		this.apc_val = apc_val;
	}


	public Integer getOpen_access_art_acc_day()
	{
		return open_access_art_acc_day;
	}


	
	public void setOpen_access_art_acc_day(Integer open_access_art_acc_day)
	{
		this.open_access_art_acc_day = open_access_art_acc_day;
	}


	
	public Integer getOpen_access_art_acc_month()
	{
		return open_access_art_acc_month;
	}


	
	public void setOpen_access_art_acc_month(Integer open_access_art_acc_month)
	{
		this.open_access_art_acc_month = open_access_art_acc_month;
	}


	
	public Integer getOpen_access_art_acc_year()
	{
		return open_access_art_acc_year;
	}


	
	public void setOpen_access_art_acc_year(Integer open_access_art_acc_year)
	{
		this.open_access_art_acc_year = open_access_art_acc_year;
	}


	public Integer getOpen_access_emb_end_month()
	{
		return open_access_emb_end_month;
	}


	
	public void setOpen_access_emb_end_month(Integer open_access_emb_end_month)
	{
		this.open_access_emb_end_month = open_access_emb_end_month;
	}


	
	public Integer getOpen_access_emb_end_year()
	{
		return open_access_emb_end_year;
	}


	
	public void setOpen_access_emb_end_year(Integer open_access_emb_end_year)
	{
		this.open_access_emb_end_year = open_access_emb_end_year;
	}


	
	public Integer getOpen_access_emb_period_month()
	{
		return open_access_emb_period_month;
	}


	
	public void setOpen_access_emb_period_month(Integer open_access_emb_period_month)
	{
		this.open_access_emb_period_month = open_access_emb_period_month;
	}


	
	public Integer getOpen_access_emb_period_year()
	{
		return open_access_emb_period_year;
	}


	
	public void setOpen_access_emb_period_year(Integer open_access_emb_period_year)
	{
		this.open_access_emb_period_year = open_access_emb_period_year;
	}


	
	public String getIs_tran_agrt()
	{
		return is_tran_agrt;
	}


	
	public void setIs_tran_agrt(String is_tran_agrt)
	{
		this.is_tran_agrt = is_tran_agrt;
	}


	
	public Double getTran_agrt_val()
	{
		return tran_agrt_val;
	}


	
	public void setTran_agrt_val(Double tran_agrt_val)
	{
		this.tran_agrt_val = tran_agrt_val;
	}


	public String getIsbn()
	{
		return isbn;
	}


	
	public void setIsbn(String isbn)
	{
		this.isbn = isbn;
	}

	

	
	public Date getStartDate() throws ParseException
	{
		if (startDate == null) {
			startDate = riStringToDate(1, getFrom_month(), getFrom_year(), true, null);
		}
		return startDate;
	}


	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}


	
	public Date getEndDate() throws ParseException
	{
		if (endDate == null) {
			endDate = riStringToDate(1, getTo_month(), getTo_year(), false, getStartDate());
		}
		return endDate;
	}


	
	public void setEndDate(Date endDate)
	{
		this.endDate = endDate;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		OutputHeader_P other = (OutputHeader_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "OutputHeader_P [pk=" + pk + ", apa_citation=" + apa_citation + ", article_num=" + article_num
				+ ", census_date=" + census_date + ", city=" + city + ", concatenated_author_name="
				+ concatenated_author_name + ", da_code=" + da_code + ", da_dtl_code=" + da_dtl_code + ", doi=" + doi
				+ ", eissn=" + eissn + ", from_month=" + from_month + ", from_year=" + from_year + ", fulltext_url="
				+ fulltext_url + ", ied_work_ind=" + ied_work_ind + ", issn=" + issn + ", isbn=" + isbn
				+ ", is_enh_high_edu=" + is_enh_high_edu + ", is_intl_conf=" + is_intl_conf + ", journal_code="
				+ journal_code + ", journal_publication_discipline=" + journal_publication_discipline
				+ ", key_research_areas=" + key_research_areas + ", language=" + language + ", name_other_editors="
				+ name_other_editors + ", name_other_pos=" + name_other_pos + ", other_da_dtl=" + other_da_dtl
				+ ", other_details=" + other_details + ", other_details_continue=" + other_details_continue
				+ ", other_key_research_areas=" + other_key_research_areas + ", output_title_continue="
				+ output_title_continue + ", page_num=" + page_num + ", page_num_from=" + page_num_from
				+ ", page_num_to=" + page_num_to + ", publisher=" + publisher + ", research_activity_ranking="
				+ research_activity_ranking + ", rs_code=" + rs_code + ", rs_dtl_code=" + rs_dtl_code
				+ ", sap_output_type=" + sap_output_type + ", sap_refered_journal=" + sap_refered_journal
				+ ", sch_code=" + sch_code + ", sch_dtl_code=" + sch_dtl_code + ", title_jour_book=" + title_jour_book
				+ ", title_paper_art=" + title_paper_art + ", total_no_of_author=" + total_no_of_author + ", to_month="
				+ to_month + ", to_year=" + to_year + ", vol_issue=" + vol_issue + ", is_rgc_proj=" + is_rgc_proj
				+ ", rgc_proj_num=" + rgc_proj_num + ", open_access_stat=" + open_access_stat + ", open_access_payment="
				+ open_access_payment + ", open_access_apc=" + open_access_apc + ", open_access_apc_payment="
				+ open_access_apc_payment + ", apc_val=" + apc_val + ", open_access_art_acc_day="
				+ open_access_art_acc_day + ", open_access_art_acc_month=" + open_access_art_acc_month
				+ ", open_access_art_acc_year=" + open_access_art_acc_year + ", open_access_emb_end_month="
				+ open_access_emb_end_month + ", open_access_emb_end_year=" + open_access_emb_end_year
				+ ", open_access_emb_period_month=" + open_access_emb_period_month + ", open_access_emb_period_year="
				+ open_access_emb_period_year + ", is_tran_agrt=" + is_tran_agrt + ", tran_agrt_val=" + tran_agrt_val
				+ ", sdg_code=" + sdg_code + ", output_lookup_code=" + output_lookup_code + ", apa_html=" + apa_html
				+ ", startDate=" + startDate + ", endDate=" + endDate + ", sdg_info=" + sdg_info + ", sdg_code_list="
				+ sdg_code_list + "]";
	}



}
