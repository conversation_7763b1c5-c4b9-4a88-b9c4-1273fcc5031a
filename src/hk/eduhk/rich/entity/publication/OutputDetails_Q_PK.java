package hk.eduhk.rich.entity.publication;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class OutputDetails_Q_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="output_no")
	private Integer output_no;
	
	@Column(name="staff_no")
	private String staff_no;

	
	public Integer getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}

	
	public String getStaff_no()
	{
		return staff_no;
	}

	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((output_no == null) ? 0 : output_no.hashCode());
		result = prime * result + ((staff_no == null) ? 0 : staff_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		OutputDetails_Q_PK other = (OutputDetails_Q_PK) obj;
		if (output_no == null)
		{
			if (other.output_no != null)
				return false;
		}
		else if (!output_no.equals(other.output_no))
			return false;
		if (staff_no == null)
		{
			if (other.staff_no != null)
				return false;
		}
		else if (!staff_no.equals(other.staff_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "OutputDetails_Q_PK [output_no=" + output_no + ", staff_no=" + staff_no + "]";
	}

	

}
