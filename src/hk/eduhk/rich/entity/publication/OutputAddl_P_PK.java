package hk.eduhk.rich.entity.publication;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class OutputAddl_P_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name="output_no")
	private Integer output_no;
	
	@Column(name="data_level")
	private String data_level;	
	
	@Column(name = "staff_no")
	private String staff_no;

	
	public Integer getOutput_no()
	{
		return output_no;
	}

	
	public void setOutput_no(Integer output_no)
	{
		this.output_no = output_no;
	}

	
	public String getData_level()
	{
		return data_level;
	}

	
	public void setData_level(String data_level)
	{
		this.data_level = data_level;
	}


	
	public String getStaff_no()
	{
		return staff_no;
	}


	
	public void setStaff_no(String staff_no)
	{
		this.staff_no = staff_no;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((data_level == null) ? 0 : data_level.hashCode());
		result = prime * result + ((output_no == null) ? 0 : output_no.hashCode());
		result = prime * result + ((staff_no == null) ? 0 : staff_no.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		OutputAddl_P_PK other = (OutputAddl_P_PK) obj;
		if (data_level == null)
		{
			if (other.data_level != null)
				return false;
		}
		else if (!data_level.equals(other.data_level))
			return false;
		if (output_no == null)
		{
			if (other.output_no != null)
				return false;
		}
		else if (!output_no.equals(other.output_no))
			return false;
		if (staff_no == null)
		{
			if (other.staff_no != null)
				return false;
		}
		else if (!staff_no.equals(other.staff_no))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "OutputAddl_P_PK [output_no=" + output_no + ", data_level=" + data_level + ", staff_no=" + staff_no
				+ "]";
	}


}
