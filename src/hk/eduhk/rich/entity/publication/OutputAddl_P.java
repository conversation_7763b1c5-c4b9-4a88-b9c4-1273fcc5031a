package hk.eduhk.rich.entity.publication;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;



@Entity
@Table(name = "RH_P_ACAD_PROF_OUTPUT_ADDL")
public class OutputAddl_P extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(OutputAddl_P.class.toString());
	
	@EmbeddedId
	private OutputAddl_P_PK pk = new OutputAddl_P_PK();
	
	@Column(name = "custom_citation")
	private String custom_citation;
	
	
	
	public OutputAddl_P_PK getPk()
	{
		return pk;
	}

	
	public void setPk(OutputAddl_P_PK pk)
	{
		this.pk = pk;
	}


	
	public String getCustom_citation()
	{
		return custom_citation;
	}


	
	public void setCustom_citation(String custom_citation)
	{
		this.custom_citation = custom_citation;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		OutputAddl_P other = (OutputAddl_P) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "OutputAddl_P [pk=" + pk + ", custom_citation=" + custom_citation + "]";
	}



}
