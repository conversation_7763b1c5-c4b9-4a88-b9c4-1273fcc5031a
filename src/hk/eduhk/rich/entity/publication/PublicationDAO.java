package hk.eduhk.rich.entity.publication;

import java.sql.*;
import java.sql.Date;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;

import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.view.RISearchPanel;
import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.*;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;

@Stateless
@SuppressWarnings("serial")
public class PublicationDAO extends BaseDAO
{

	private static PublicationDAO instance = null;


	public static synchronized PublicationDAO getInstance()
	{
		if (instance == null) instance = new PublicationDAO();
		return instance;
	}
	
	
	public static PublicationDAO getCacheInstance()
	{
		return PublicationDAO.getInstance();
	}
	
	public List<Publication> getRecentPublicationList(int pid, String staffNo) throws SQLException
	{
		List<Publication> voList = new ArrayList<Publication>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		
		
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		String sapOutputType = paramDAO.getSysParamValueByCode(SysParam.PARAM_SAP_OUTPUT_TYPES);
		String outputPriority = paramDAO.getSysParamValueByCode(SysParam.PARAM_OUTPUT_PRIORITY);


		StaffProfileDisplay displaySearchObj = new StaffProfileDisplay();
		displaySearchObj.getPk().setPid(pid);
		displaySearchObj.getPk().setItemCode("CAT_OUTPUT");
		List<StaffProfileDisplay> displayObjList = getStaffProfileDisplayList(displaySearchObj);
		StaffProfileDisplay displayObj = (displayObjList.size() > 0)? displayObjList.get(0):null;
		String displayOutputSubCat = (displayObj != null) ? StringUtils.defaultString(displayObj.getShowInd(), "N") : "N";

		String outputOrder = paramDAO.getSysParamValueByCode(SysParam.PARAM_OUTPUT_ORDER);
    if("Y".equals(displayOutputSubCat)) 
    {
    	outputOrder = paramDAO.getSysParamValueByCode(SysParam.PARAM_OUTPUT_ORDER_SUB);
    }

		try
		{
			conn = pm.getConnection();

			Publication searchObj = new Publication();
			String strWithinMonth = paramDAO.getSysParamValueByCode(SysParam.PARAM_PERSONAL_WITHIN_YEAR_PUBLICATION);
			int withinMonth = Integer.parseInt(strWithinMonth) * 12;
			String strNumRetrieved = paramDAO.getSysParamValueByCode(SysParam.PARAM_PERSONAL_NUM_RETRIEVE_PUBLICATION);
			int numRetrieved = Integer.parseInt(strNumRetrieved);

			StringBuffer sqlBuf = new StringBuffer();
		
      sqlBuf.append("SELECT * FROM " +
										"( " +
                    "  SELECT * FROM ( SELECT staff_number, output_no, census_date, title_jour_book, " +
										"  output_title_continue,title_paper_art, sap_output_type, sap_refered_journal, " +
										"  name_other_editors, vol_issue, " +
									  "  page_num, city, from_month, " +
									  "  from_year, to_month, to_year, " +
									  "  publisher, publish_status, other_details, other_details_continue, " +
									  "  language, name_other_pos, sch_code, " +
									  "  sch_dtl_code, rs_code, rs_dtl_code, " +
									  "  total_no_of_author, concatenated_author_name, key_research_areas, " +
									  "  other_key_research_areas, " +
										"  (SELECT DISTINCT print_order FROM RH_L_OUTPUT_TYPE_V WHERE lookup_code=sap_output_cat) AS priority, " +
										"	 sap_output_cat, lookup_value_desc sap_output_type_desc, " +
										"  repos_url, repos_confirm " +
										"  FROM " +
										"  ( " +
										"    SELECT D.authorship_staff_no AS staff_number, H.*, QH.publish_status, " +
										"	   TO_DATE('01'||NVL(from_year,TO_CHAR(SYSDATE,'YYYY')),'MMYYYY') AS from_pub_date, " +
										"	   TO_DATE('01'||NVL(to_year,TO_CHAR(SYSDATE,'YYYY')),'MMYYYY') AS to_pub_date, " +
										"	   TO_DATE('01'||TO_CHAR(SYSDATE,'YYYY'),'MMYYYY') AS today, "  +
										"		 T.parent_lookup_code AS sap_output_cat, T.description AS LOOKUP_VALUE_DESC, repos_url, repos_confirm " +
										"    FROM RH_P_ACAD_PROF_OUTPUT_HDR H LEFT JOIN RH_L_OUTPUT_TYPE_V T ON (H.SAP_OUTPUT_TYPE = T.LOOKUP_CODE AND T.lookup_level = 2) " +
										"    LEFT JOIN RH_P_ACAD_PROF_OUTPUT_DTL D ON (H.output_no = D.output_no AND H.data_level = D.data_level) " +
										"    LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_DTL QD ON (QD.output_no = D.output_no AND QD.staff_no = D.authorship_staff_no) " +
										"		 LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_HDR QH ON (QH.output_no = QD.output_no) " +
										"	   WHERE NVL(QD.display_ind,'N')='Y' " +
										"		AND H.data_level = 'P' " +
										"		AND sap_output_type IN ( " + sapOutputType  + " ) " +
										"  ) " +
										"  WHERE from_pub_date >= ADD_MONTHS(today," + String.valueOf(withinMonth*-1) + ") AND (to_pub_date is null or to_pub_date <= today )) WHERE 1=1 ");

			if (!GenericValidator.isBlankOrNull(staffNo)) sqlBuf.append("AND staff_number='" + escapeSql(staffNo) + "' ");
			sqlBuf.append(" ORDER BY " + outputOrder);

			sqlBuf.append(") WHERE rownum <= " + numRetrieved);
			//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
			logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();

			while (rs.next())
			{
				Publication vObj = new Publication();
				vObj.setStaffNumber(rs.getString("staff_number"));
				vObj.setOutputNo(rs.getInt("output_no"));
				vObj.setName(rs.getString("name_other_pos"));
				vObj.setArticleTitle(rs.getString("title_jour_book"));
				vObj.setTitle(rs.getString("title_paper_art"));
				vObj.setVolIssue(rs.getString("vol_issue"));
				vObj.setPageNum(rs.getString("page_num"));
				vObj.setCity(rs.getString("city"));
				vObj.setFromYear(rs.getString("from_year"));
				vObj.setFromMonth(rs.getString("from_month"));
				vObj.setPublisher(rs.getString("publisher"));
				vObj.setPublishStatus(rs.getString("publish_status"));
				vObj.setLanguage(rs.getString("language"));
				vObj.setNameOtherEditors(rs.getString("name_other_editors"));
				vObj.setPriority(rs.getString("priority"));
				vObj.setSapOutputCat(rs.getString("sap_output_cat"));
				vObj.setSapOutputType(rs.getString("sap_output_type"));
				vObj.setSapOutputTypeDesc(rs.getString("sap_output_type_desc"));
				vObj.setReposURL(rs.getString("repos_url"));
				vObj.setReposConfirm(rs.getString("repos_confirm"));
				voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}

    return voList;
	}
	
	public List<StaffProfileDisplay> getStaffProfileDisplayList(StaffProfileDisplay searchObj) throws SQLException
	{
		List<StaffProfileDisplay> voList = new ArrayList<StaffProfileDisplay>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if (searchObj == null) searchObj = new StaffProfileDisplay();

    try 
    {

			StringBuffer sqlBuf = new StringBuffer();
			sqlBuf.append("SELECT pid, item_code, item_type, show_ind, " +
										"creator, creation_time, userstamp, timestamp " +
										"FROM RH_U_STAFF_PROFILE_DISPLAY " + 
										"WHERE pid=? ");

			if (!GenericValidator.isBlankOrNull(searchObj.getPk().getItemCode())) sqlBuf.append("AND item_code = '" + StringUtils.defaultString(escapeSql(searchObj.getPk().getItemCode())) + "' ");
			if (!GenericValidator.isBlankOrNull(searchObj.getItemType())) sqlBuf.append("AND item_type = '" + StringUtils.defaultString(escapeSql(searchObj.getItemType())) + "' ");
			if (!GenericValidator.isBlankOrNull(searchObj.getShowInd())) sqlBuf.append("AND show_ind = '" + StringUtils.defaultString(escapeSql(searchObj.getShowInd())) + "' ");
			sqlBuf.append("ORDER BY pid, item_code, item_type ");

			if (logger.isLoggable(Level.FINEST)) logger.log(Level.FINEST, sqlBuf.toString());

			int i = 0;
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(sqlBuf.toString());
			pStmt.setInt(++i, searchObj.getPk().getPid());
			ResultSet rs = pStmt.executeQuery();

			while (rs.next())
			{
				StaffProfileDisplay vObj = new StaffProfileDisplay();
				vObj.getPk().setPid(rs.getInt("pid"));
				vObj.getPk().setItemCode(rs.getString("item_code"));
				vObj.setItemType(rs.getString("item_type"));
				vObj.setShowInd(rs.getString("show_ind"));
				voList.add(vObj);
			}
    }
		finally 
    {
			pm.close(pStmt);
			pm.close(conn);
    }

		return voList;
	}	
	
	public static String escapeSql(String str)
	{
         if (str == null) {
                return null;
          }
         return StringUtils.replace(str, "'", "''");
	}
	
	public List<OutputType> getOutputTypeList(int lookup_level) 
	{
		List<OutputType> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM OutputType obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<OutputType> q = em.createQuery(query, OutputType.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PubDiscipline> getDisciplineList(int lookup_level) 
	{
		List<PubDiscipline> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PubDiscipline obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<PubDiscipline> q = em.createQuery(query, PubDiscipline.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<ResearchType> getResearchTypeList(int lookup_level) 
	{
		List<ResearchType> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ResearchType obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<ResearchType> q = em.createQuery(query, ResearchType.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}		
	
	public List<Authorship> getAuthorshipList(int lookup_level) 
	{
		List<Authorship> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM Authorship obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<Authorship> q = em.createQuery(query, Authorship.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}		
	
	public List<SubAuthorship> getSubAuthorshipList(int lookup_level) 
	{
		List<SubAuthorship> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM SubAuthorship obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<SubAuthorship> q = em.createQuery(query, SubAuthorship.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<EduSector> getEduSectorList(int lookup_level) 
	{
		List<EduSector> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM EduSector obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<EduSector> q = em.createQuery(query, EduSector.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}	
	
	public List<DisciplinaryArea> getDisAreaList(int lookup_level) 
	{
		List<DisciplinaryArea> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM DisciplinaryArea obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<DisciplinaryArea> q = em.createQuery(query, DisciplinaryArea.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}		
	
	
	public List<Publication> getOutputList(int pid, String staffNo) throws SQLException
	{
		List<Publication> voList = new ArrayList<Publication>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
				
		SysParamCacheDAO paramDAO = SysParamCacheDAO.getInstance();
		String outputPriority = paramDAO.getSysParamValueByCode(SysParam.PARAM_OUTPUT_PRIORITY);

		StaffProfileDisplay displaySearchObj = new StaffProfileDisplay();
		displaySearchObj.getPk().setPid(pid);
		displaySearchObj.getPk().setItemCode("CAT_OUTPUT");
		List<StaffProfileDisplay> displayObjList = getStaffProfileDisplayList(displaySearchObj);
		StaffProfileDisplay displayObj = (displayObjList.size() > 0)? displayObjList.get(0):null;
		String displayOutputSubCat = (displayObj != null) ? StringUtils.defaultString(displayObj.getShowInd(), "N") : "N";

		String outputOrder = paramDAO.getSysParamValueByCode(SysParam.PARAM_OUTPUT_ORDER);
	    if("Y".equals(displayOutputSubCat)) 
	    {
	    	outputOrder = paramDAO.getSysParamValueByCode(SysParam.PARAM_OUTPUT_ORDER_SUB);
	    }

		try
		{
			conn = pm.getConnection();

			String strNumRetrieved = paramDAO.getSysParamValueByCode(SysParam.PARAM_PERSONAL_NUM_RETRIEVE_PUBLICATION);
			int numRetrieved = Integer.parseInt(strNumRetrieved);

			StringBuffer sqlBuf = new StringBuffer();
		
      sqlBuf.append("SELECT * FROM " +
										"( " +
                    "  SELECT * FROM ( SELECT staff_number, output_no, census_date, title_jour_book, " +
										"  output_title_continue,title_paper_art, sap_output_type, sap_refered_journal, " +
										"  name_other_editors, vol_issue, " +
									  "  page_num, city, from_month, " +
									  "  from_year, to_month, to_year, " +
									  "  publisher, publish_status, creator_ind, other_details, other_details_continue, " +
									  "  language, name_other_pos, sch_code, " +
									  "  sch_dtl_code, rs_code, rs_dtl_code, " +
									  "  total_no_of_author, concatenated_author_name, key_research_areas, " +
									  "  other_key_research_areas, " +
										"  (SELECT DISTINCT print_order FROM RH_L_OUTPUT_TYPE_V WHERE lookup_code=sap_output_cat) AS priority, " +
										"	 sap_output_cat, lookup_value_desc sap_output_type_desc, " +
										"  repos_url, repos_confirm " +
										"  FROM " +
										"  ( " +
										"    SELECT D.authorship_staff_no AS staff_number, H.*, QH.publish_status, QD.creator_ind, " +
										"	   TO_DATE('01'||NVL(from_year,TO_CHAR(SYSDATE,'YYYY')),'MMYYYY') AS from_pub_date, " +
										"	   TO_DATE('01'||NVL(to_year,TO_CHAR(SYSDATE,'YYYY')),'MMYYYY') AS to_pub_date, " +
										"	   TO_DATE('01'||TO_CHAR(SYSDATE,'YYYY'),'MMYYYY') AS today, "  +
										"		 T.parent_lookup_code AS sap_output_cat, T.description AS LOOKUP_VALUE_DESC, repos_url, repos_confirm " +
										"    FROM RH_P_ACAD_PROF_OUTPUT_HDR H LEFT JOIN RH_L_OUTPUT_TYPE_V T ON (H.SAP_OUTPUT_TYPE = T.LOOKUP_CODE AND T.lookup_level = 2) " +
										"    LEFT JOIN RH_P_ACAD_PROF_OUTPUT_DTL D ON (H.output_no = D.output_no AND H.DATA_LEVEL = D.DATA_LEVEL) " +
										"    LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_DTL QD ON (QD.output_no = D.output_no AND QD.staff_no = D.authorship_staff_no) " +
										"		 LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_HDR QH ON (QH.output_no = QD.output_no) " +
										"	   WHERE D.DATA_LEVEL = 'M' AND H.DATA_LEVEL = 'M' " +
										"  ) " +
										"  ) WHERE 1=1 ");

			if (!GenericValidator.isBlankOrNull(staffNo)) sqlBuf.append("AND staff_number='" + escapeSql(staffNo) + "' ");
			sqlBuf.append(" ORDER BY " + outputOrder);

			sqlBuf.append(") WHERE rownum <= " + numRetrieved);
			//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
			logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();

			while (rs.next())
			{
				Publication vObj = new Publication();
				vObj.setStaffNumber(rs.getString("staff_number"));
				vObj.setOutputNo(rs.getInt("output_no"));
				vObj.setName(rs.getString("name_other_pos"));
				vObj.setArticleTitle(rs.getString("title_jour_book"));
				vObj.setTitle(rs.getString("title_paper_art"));
				vObj.setVolIssue(rs.getString("vol_issue"));
				vObj.setPageNum(rs.getString("page_num"));
				vObj.setCity(rs.getString("city"));
				vObj.setFromYear(rs.getString("from_year"));
				vObj.setFromMonth(rs.getString("from_month"));
				vObj.setPublisher(rs.getString("publisher"));
				vObj.setPublishStatus(rs.getString("publish_status"));
				vObj.setLanguage(rs.getString("language"));
				vObj.setNameOtherEditors(rs.getString("name_other_editors"));
				vObj.setPriority(rs.getString("priority"));
				vObj.setSapOutputCat(rs.getString("sap_output_cat"));
				vObj.setSapOutputType(rs.getString("sap_output_type"));
				vObj.setSapOutputTypeDesc(rs.getString("sap_output_type_desc"));
				vObj.setReposURL(rs.getString("repos_url"));
				vObj.setReposConfirm(rs.getString("repos_confirm"));
				vObj.setCreatorInd(rs.getString("creator_ind"));
				voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}

    return voList;
	}
	
	public List<OutputDetails_P> getOutputDetails_P_byRiNo(List<List<Integer>> riNosParts, String data_level)
	{
		List<OutputDetails_P> objList = null;
		EntityManager em = null;	
		String where = "";
		try
		{
			em = getEntityManager();	
			if (!riNosParts.isEmpty()) {
				for (int i = 0; i < riNosParts.size(); i++) {
					if (i == 0) {
						where += " AND ( ";
					}
					where += " obj.pk.output_no IN :riNos"+i;
					if (i == riNosParts.size() - 1) {
						where += " ) ";
					}else {
						where += " OR ";
					}
				}

				String query = "SELECT obj FROM OutputDetails_P obj WHERE obj.pk.data_level = :data_level AND obj.pk.line_no = :line_no " + where +
									" ORDER BY obj.outputHeader_p.from_year DESC, obj.outputHeader_p.from_month DESC, obj.outputHeader_p.name_other_pos, obj.outputHeader_p.title_jour_book";			
				TypedQuery<OutputDetails_P> q = em.createQuery(query, OutputDetails_P.class);
				if (!riNosParts.isEmpty()) {
					for (int i = 0; i < riNosParts.size(); i++) {
						q.setParameter("riNos"+i, riNosParts.get(i));
					}
				}
				q.setParameter("data_level", data_level);
				q.setParameter("line_no", 1);

				objList = q.getResultList();
			}
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList) ? objList : null); 
	}
	
	public List<OutputDetails_P> getOutputDetails_P_byStaffNo(String staff_no, String data_level)
	{
		List<OutputDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM OutputDetails_P obj WHERE obj.authorship_staff_no = :staff_no AND obj.pk.data_level = :data_level " +
								" ORDER BY obj.outputHeader_p.from_year DESC, obj.outputHeader_p.from_month DESC, obj.outputHeader_p.name_other_pos, obj.outputHeader_p.title_jour_book";			
			TypedQuery<OutputDetails_P> q = em.createQuery(query, OutputDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<OutputDetails_P> getOutputDetails_P_byStaffNo_outputType(String staff_no, String data_level, List<String> sap_output_type)
	{
		List<OutputDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM OutputDetails_P obj WHERE obj.authorship_staff_no = :staff_no AND obj.pk.data_level = :data_level AND obj.outputHeader_p.sap_output_type IN :sap_output_type " +
								" ORDER BY obj.outputHeader_p.from_year DESC, obj.outputHeader_p.from_month DESC, obj.outputHeader_p.name_other_pos, obj.outputHeader_p.title_jour_book";			
			TypedQuery<OutputDetails_P> q = em.createQuery(query, OutputDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			q.setParameter("sap_output_type", sap_output_type);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<OutputDetails_P> getOutputDetails_P_byStaffNo_consent(String staff_no, String data_level)
	{
		List<OutputDetails_P> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM OutputDetails_P obj WHERE obj.authorship_staff_no = :staff_no AND obj.pk.data_level = :data_level " +
								" AND obj.outputHeader_q.publish_freq > :publish_freq  ORDER BY obj.outputHeader_p.from_year DESC, obj.outputHeader_p.from_month DESC, obj.outputHeader_p.name_other_pos, obj.outputHeader_p.title_jour_book";			
			TypedQuery<OutputDetails_P> q = em.createQuery(query, OutputDetails_P.class);
			q.setParameter("staff_no", staff_no);
			q.setParameter("data_level", data_level);
			q.setParameter("publish_freq", 0);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<OutputDetails_P> getOutputDetails_P(int output_no, String data_level)
	{
		List<OutputDetails_P> objList = null;
		EntityManager em = null;		
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM OutputDetails_P obj WHERE obj.pk.output_no = :output_no AND obj.pk.data_level = :data_level ORDER BY obj.pk.line_no";			
				TypedQuery<OutputDetails_P> q = em.createQuery(query, OutputDetails_P.class);
				q.setParameter("output_no", output_no);
				q.setParameter("data_level", data_level);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return objList;
	}
	
	public Double getOutputCdcfWeighting(int output_no, String data_level, Integer line_no) throws SQLException
	{
		List<Double> objList = new ArrayList<Double>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;	
		if(output_no > 0 && data_level != null && line_no != null) {
			try
			{
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
			
			    sqlBuf.append(
			    		" SELECT * FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V " +
			    		" WHERE OUTPUT_NO = '" + output_no + "' " +
			    		" AND DATA_LEVEL = '" + data_level + "' " +
			    		" AND line_no = '" + line_no + "' ");
			
				//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
				logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
	
				while (rs.next())
				{
					objList.add(rs.getDouble("WEIGHTING"));
				}
			}
			
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):0.0;
	}
	
	public OutputAddl_P getOutputAddl_P(int output_no, String data_level, String staff_no)
	{
		List<OutputAddl_P> objList = null;
		EntityManager em = null;		
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM OutputAddl_P obj WHERE obj.pk.output_no = :output_no AND obj.pk.staff_no = :staff_no AND obj.pk.data_level = :data_level ";			
				TypedQuery<OutputAddl_P> q = em.createQuery(query, OutputAddl_P.class);
				q.setParameter("output_no", output_no);
				q.setParameter("data_level", data_level);
				q.setParameter("staff_no", staff_no);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public List<OutputDetails_Q> getOutputDetails_Q(int output_no, String staff_no)
	{
		List<OutputDetails_Q> objList = null;
		EntityManager em = null;		
		String outputNo = (output_no > 0)? " AND obj.pk.output_no = :output_no ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM OutputDetails_Q obj WHERE obj.pk.staff_no = :staff_no " + outputNo;			
			TypedQuery<OutputDetails_Q> q = em.createQuery(query, OutputDetails_Q.class);
			if (output_no > 0) {
				q.setParameter("output_no", output_no);
			}
			q.setParameter("staff_no", staff_no);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public OutputHeader_P getOutputHeader_P(int output_no, String data_level)
	{
		List<OutputHeader_P> objList = null;
		EntityManager em = null;		
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM OutputHeader_P obj WHERE obj.pk.output_no = :output_no AND obj.pk.data_level = :data_level";			
				TypedQuery<OutputHeader_P> q = em.createQuery(query, OutputHeader_P.class);
				q.setParameter("output_no", output_no);
				q.setParameter("data_level", data_level);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}	
	
	public OutputHeader_Q getOutputHeader_Q(int output_no)
	{
		OutputHeader_Q obj = null;
		EntityManager em = null;
		if (output_no > 0)
		{
			try
			{
				em = pm.getEntityManager();
				obj = em.find(OutputHeader_Q.class, output_no);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public OutputDetails_Q getOutputDetails_Q1 (Integer output_no, String staffNo) 
	{
		List<OutputDetails_Q> objList = null;
		EntityManager em = null;
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM OutputDetails_Q obj WHERE obj.pk.output_no = :output_no AND obj.pk.staff_no = :staffNo";			
				TypedQuery<OutputDetails_Q> q = em.createQuery(query, OutputDetails_Q.class);
				q.setParameter("output_no", output_no);
				q.setParameter("staffNo", staffNo);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):new OutputDetails_Q();
	}		
	
	public OutputDetails_Q getOutputDetails_Q_creator (Integer output_no) 
	{
		List<OutputDetails_Q> objList = null;
		EntityManager em = null;
		if (output_no > 0) {
			try
			{
				em = getEntityManager();		
				String query = "SELECT obj FROM OutputDetails_Q obj WHERE obj.pk.output_no = :output_no AND obj.creator_ind = :creator_ind";			
				TypedQuery<OutputDetails_Q> q = em.createQuery(query, OutputDetails_Q.class);
				q.setParameter("output_no", output_no);
				q.setParameter("creator_ind", "Y");
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public OutputHeader_P updateOutputHeader_P(OutputHeader_P obj)
	{
		return updateEntity(obj);
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public OutputAddl_P updateOutputAddl_P(OutputAddl_P obj)
	{
		return updateEntity(obj);
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public OutputHeader_Q updateOutputHeader_Q(OutputHeader_Q obj)
	{
		return updateEntity(obj);
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public OutputDetails_P updateOutputDetails_P(OutputDetails_P obj)
	{
		return updateEntity(obj);
	}
	
	@TransactionAttribute(TransactionAttributeType.REQUIRED)
	public OutputDetails_Q updateOutputDetails_Q(OutputDetails_Q obj)
	{
		return updateEntity(obj);
	}
	
	
	
	public void deleteAllContributor(int output_no, String data_level) throws Exception
	{
		if (output_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM OutputDetails_P obj WHERE obj.pk.output_no = :output_no AND obj.pk.data_level = :data_level ");
				q.setParameter("output_no", output_no);
				q.setParameter("data_level", data_level);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete contributors (output_no=" + output_no + ", data_level="+ data_level + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}	
	
	public void deleteOutputDetails_Q(int output_no) throws Exception
	{
		if (output_no > 0)
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				Query q = em.createQuery("DELETE FROM OutputDetails_Q obj WHERE obj.pk.output_no = :output_no ");
				q.setParameter("output_no", output_no);
				q.executeUpdate();
				
				utx.commit();
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot delete OutputDetails_Q (output_no=" + output_no + ")", e);
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
	}
	
	public List<FundingSource> getFundingSrcList(int lookup_level) 
	{
		List<FundingSource> objList = null;
		EntityManager em = null;		
		String parent_lookup_code = (lookup_level == 2)?" AND obj.parent_lookup_code IS NOT NULL ":"";
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM FundingSource obj WHERE obj.pk.lookup_level = :lookup_level AND obj.enabled_flag = :enabled_flag " + parent_lookup_code + " ORDER BY obj.print_order ";			
			TypedQuery<FundingSource> q = em.createQuery(query, FundingSource.class);
			q.setParameter("lookup_level", lookup_level);
			q.setParameter("enabled_flag", "Y");
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<Publication> getPublicationListByIds(List<Integer> idList, RISearchPanel searchPanel) throws SQLException
	{
		List<Publication> voList = new ArrayList<Publication>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		if(idList != null && !idList.isEmpty() && searchPanel != null) {
			String listingType = searchPanel.getListingType();
			String dataLevel = searchPanel.getViewType();
			String sortCol = searchPanel.getSortCol();
			String sortOrder = searchPanel.getSortOrder();
			List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
			if(idList.size() > 1000) {
				for(int i = 0 ; true ; ++i) {
					if(1000*i+1000 > idList.size()) {
						idListPatch.add(idList.subList(1000*i, idList.size()));
						break;
					}
					else
						idListPatch.add(idList.subList(1000*i, 1000*i+1000));
				}
			}
			else idListPatch.add(idList);
			
			try
			{
				SysParamDAO sysDao = SysParamDAO.getCacheInstance();
				int listNum = sysDao.getSysParamIntByCode(SysParam.PARAM_MAX_AUTHOR_LIST_LENGTH);
				List<String> selectedSDG  = searchPanel.getSelectedSDGList();
				
				conn = pm.getConnection();
				for(List<Integer> list : idListPatch) {
					
					StringBuffer sqlBuf = new StringBuffer();
					
				    sqlBuf.append(
				    		" SELECT DISTINCT PH.OUTPUT_NO, SDG_CODE, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" TMP.AUTHOR_LIST"+i+" AS AUTHOR_LIST"+i+", ");
				    }
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" (CASE WHEN STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME "
				    		+ " WHEN STAFFDEPT.FULLNAME IS NOT NULL THEN STAFFDEPT.FULLNAME ELSE EXSTAFF.FULLNAME END) AS STAFF_NAME, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_AUTHORSHIP_V WHERE LOOKUP_CODE = PD.AUTHORSHIP_TYPE) AS AUTHORSHIP_TYPE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_SUBAUTHORSHIP_V WHERE LOOKUP_CODE = PD.AUTHORSHIP_DTL_TYPE) AS AUTHORSHIP, " + 
				    		" PD.AUTHORSHIP_STAFF_NO, " + 
				    		" CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN STAFF.DEPT_CODE WHEN STAFFDEPT.DEPT_CODE IS NOT NULL THEN STAFFDEPT.DEPT_CODE WHEN ALLSTAFF.DEPARTMENT_CODE IS NOT NULL THEN ALLSTAFF.DEPARTMENT_CODE ELSE EXSTAFF.DEPARTMENT_CODE  END AS DEPARTMENT, " );
				    }
				    sqlBuf.append(
				    		// 2.x
				    		" PH.LANGUAGE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_OUTPUT_TYPE_V WHERE LOOKUP_CODE = (SELECT PARENT_LOOKUP_CODE FROM RH_L_OUTPUT_TYPE_V WHERE LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND LOOKUP_LEVEL = 2)) AS OUTPUT_CAT, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_OUTPUT_TYPE_V WHERE LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND LOOKUP_LEVEL = 2) AS OUTPUT_TYPE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_PUBLICATION_DISCIPLINE_V WHERE LOOKUP_CODE = PH.JOURNAL_PUBLICATION_DISCIPLINE AND LOOKUP_LEVEL = 2) AS JOURNAL_PUBLICATION_DISCIPLINE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_RESEARCH_TYPE_V WHERE LOOKUP_CODE = SAP_REFERED_JOURNAL) AS SAP_REFERED_JOURNAL, " + 
				    		" (CASE WHEN IED_WORK_IND = 'Y' THEN 'Yes' WHEN IED_WORK_IND = 'N' THEN 'No' ELSE '' END) AS IED_WORK_IND, " + 
				    		" (CASE WHEN IS_ENH_HIGH_EDU = 'Y' THEN 'Yes' WHEN IS_ENH_HIGH_EDU = 'N' THEN 'No' ELSE '' END) AS IS_ENH_HIGH_EDU, " + 
				    		" (CASE WHEN IS_INTL_CONF = 'Y' THEN 'Yes' WHEN IS_INTL_CONF = 'N' THEN 'No' ELSE '' END) AS IS_INTL_CONF, " + 
				    		" CASE WHEN IS_RGC_PROJ = 'Y' THEN 'Yes' WHEN IS_RGC_PROJ = 'N' THEN 'No' ELSE '' END AS IS_RGC_PROJ, " + 
				    		" RGC_PROJ_NUM, " + 
				    		// 3.x
				    		" APA_CITATION, " + 
				    		// 4.x
				    		" TITLE_JOUR_BOOK||' '||OUTPUT_TITLE_CONTINUE AS TITLE_OF_RESEARCH_OUTPUT, " + 
				    		" TITLE_PAPER_ART, " + 
				    		" RESEARCH_ACTIVITY_RANKING, " + 
				    		" (SELECT count(1) from RH_P_ACAD_PROF_OUTPUT_DTL dtl where dtl.DATA_LEVEL = PH.DATA_LEVEL AND dtl.OUTPUT_NO =PH.OUTPUT_NO group by dtl.OUTPUT_NO) AS TOTAL_NO_OF_AUTHOR, " + 
				    		" ISSN, " + 
				    		" EISSN, " + 
				    		" ISBN, " + 
				    		" DOI, " + 
				    		" FULLTEXT_URL, " + 
				    		" VOL_ISSUE, " + 
				    		" PAGE_NUM, " + 
				    		" PAGE_NUM_FROM, " + 
				    		" PAGE_NUM_TO, " + 
				    		" FROM_MONTH, " + 
				    		" FROM_YEAR, " + 
				    		" TO_MONTH, " + 
				    		" TO_YEAR, " + 
				    		" ARTICLE_NUM, " + 
				    		" CITY, " + 
				    		" OPEN_ACCESS_STAT, " + 
				    		" (CASE WHEN OPEN_ACCESS_APC = 'Y' THEN 'Yes' WHEN OPEN_ACCESS_APC = 'N' THEN 'No' ELSE '' END) AS OPEN_ACCESS_APC, " + 
				    		" (CASE WHEN OPEN_ACCESS_NONAPC_PAYMENT = 'Y' THEN 'Yes' WHEN OPEN_ACCESS_NONAPC_PAYMENT = 'N' THEN 'No' ELSE '' END) AS OPEN_ACCESS_NONAPC_PAYMENT, " + 
				    		" APC_VAL, " + 
				    		"  NVL(open_access_art_acc_day,1) as open_access_art_acc_day, " +
				    		"  open_access_art_acc_month, " +
				    		"  open_access_art_acc_year, " +
				    		" open_access_emb_end_month, " + 
				    		" open_access_emb_end_year, " + 
				    		" open_access_emb_period_month, " + 
				    		" open_access_emb_period_year, " + 
				    		" (CASE WHEN open_access_payment = 'Y' THEN 'Yes' WHEN open_access_payment = 'N' THEN 'No' ELSE '' END) AS open_access_payment, " + 
				    		" (CASE WHEN IS_TRAN_AGRT = 'Y' THEN 'Yes' WHEN IS_TRAN_AGRT = 'N' THEN 'No' ELSE '' END) AS IS_TRAN_AGRT, " + 
				    		" tran_agrt_val, " + 
				    		" PUBLISHER, " + 
				    		" OTHER_DETAILS||' '||OTHER_DETAILS_CONTINUE AS OTHER_DETAILS, " + 
				    		" NAME_OTHER_EDITORS, " + 
				    		" NAME_OTHER_POS, " + 
				    		// 6.x
				    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_CODE AND LOOKUP_LEVEL = 1) AS SCH_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_DTL_CODE AND LOOKUP_LEVEL = 2) AS SCH_DTL_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_CODE AND LOOKUP_LEVEL = 1) AS DA_CODE, " + 
				    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_DTL_CODE AND LOOKUP_LEVEL = 2) AS DA_DTL_CODE, " + 
				    		" OTHER_DA_DTL, " + 
				    		// manageInstituteRI
				    		" CDCF_STATUS, " +
				    		" (CASE WHEN CDCF_SELECTED_IND = 'Y' THEN 'Yes' WHEN CDCF_SELECTED_IND = 'N' THEN 'No' ELSE '' END) AS CDCF_SELECTED_IND " + 
				    		" FROM RH_P_ACAD_PROF_OUTPUT_HDR PH " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_P_ACAD_PROF_OUTPUT_DTL PD ON (PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " );
				    }
				    sqlBuf.append(
				    		" LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_HDR QH ON QH.OUTPUT_NO = PH.OUTPUT_NO  " );
				    if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
				    	sqlBuf.append(
				    		" LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_DTL QD ON (QD.OUTPUT_NO = PH.OUTPUT_NO AND QD.STAFF_NO = PD.AUTHORSHIP_STAFF_NO)  " + 
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO  " + 
				    		" LEFT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V ALLSTAFF ON ALLSTAFF.EMPLOYEE_NUMBER = PD.PD.AUTHORSHIP_STAFF_NO "+
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO " +
				    		" LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT STAFFDEPT ON STAFFDEPT.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO  " ); 
				    }
				    sqlBuf.append(
				    		" LEFT JOIN ( " + 
				    		" SELECT DISTINCT PH.OUTPUT_NO, ");
				    for(int i=1 ; i <= listNum ; ++i) {
					    sqlBuf.append(
					    		" LISTAGG( " + 
					    		" CASE WHEN (PD.AUTHORSHIP_STAFF_NO IS NOT NULL OR PD.AUTHORSHIP_NAME IS NOT NULL) " + 
					    		" AND (LINE_NO <= " + i*30 + 
					    		" AND LINE_NO > " + (i*30 - 30) + " ) " +
					    		" THEN ( " + 
					    		" (CASE WHEN PD.AUTHORSHIP_STAFF_NO IS NOT NULL AND STAFF.FULLNAME IS NOT NULL THEN STAFF.FULLNAME  " + 
					    		" WHEN PD.AUTHORSHIP_STAFF_NO IS NOT NULL AND EXSTAFF.FULLNAME IS NOT NULL THEN EXSTAFF.FULLNAME " + 
					    		" ELSE PD.AUTHORSHIP_NAME END) " + 
					    		" ||' '||(CASE WHEN PD.NON_IED_STAFF_FLAG = 'S' THEN '[Student]' ELSE '' END) " + 
					    		" ||(CASE WHEN STAFF.DEPT_CODE IS NOT NULL THEN '['||STAFF.DEPT_CODE||']' ELSE '' END)|| " + 
					    		" '['||(SELECT DESCRIPTION FROM RH_L_AUTHORSHIP_V WHERE LOOKUP_CODE = PD.AUTHORSHIP_TYPE)|| " +
					    		" (CASE WHEN PD.AUTHORSHIP_DTL_TYPE IS NOT NULL THEN ' '|| " + 
					    		" (SELECT DESCRIPTION FROM RH_L_SUBAUTHORSHIP_V WHERE LOOKUP_CODE = PD.AUTHORSHIP_DTL_TYPE) ELSE '' END)" +
					    		" ||'] ' " + 
					    		" ) ");
					    if(i == listNum) {
						    sqlBuf.append(
						    		" WHEN LINE_NO = (" + i*30 +
						    		")+1 THEN '...' ");}
					    sqlBuf.append(
					    		" ELSE '' END , '<br/>') " + 
					    		" WITHIN GROUP ( ORDER BY PD.LINE_NO) AS AUTHOR_LIST" + i + " ");
					    if(i != listNum) {
						    sqlBuf.append(", " );}
				    }
				    sqlBuf.append(
				    		" FROM RH_P_ACAD_PROF_OUTPUT_HDR PH  " + 
				    		" LEFT JOIN RH_P_ACAD_PROF_OUTPUT_DTL PD ON (PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL)  " +  
				    		" LEFT JOIN RH_P_STAFF_IDENTITY STAFF ON STAFF.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO  " + 
				    		" LEFT JOIN RH_P_STAFF_EMPLOYMENT_PAST EXSTAFF ON EXSTAFF.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO  " + 
				    		" LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT STAFFDEPT ON STAFFDEPT.STAFF_NUMBER = PD.AUTHORSHIP_STAFF_NO  " + 
				    		" WHERE PH.DATA_LEVEL = '" + dataLevel + "' " +
				    		" GROUP BY PH.OUTPUT_NO " + 
				    		" ) TMP ON PH.OUTPUT_NO = TMP.OUTPUT_NO " + 
				    		" WHERE 1=1 AND PH.DATA_LEVEL = '" + dataLevel + "' " );
		    		if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
		    			sqlBuf.append(
		    				" AND PD.AUTHORSHIP_STAFF_NO IS NOT NULL" );
		    		}
				    sqlBuf.append(
				    		" AND PH.OUTPUT_NO IN ( " +
				    		list.stream().map(String::valueOf).collect(Collectors.joining(",")) + " ) ");
				    
	
				    
					//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
					logger.log(Level.FINEST, sqlBuf.toString());
					pStmt = conn.prepareStatement(sqlBuf.toString());
					ResultSet rs = pStmt.executeQuery();
					
					
					
		
					while (rs.next())
					{
						Publication vObj = new Publication();
						vObj.setOutputNo(rs.getInt("OUTPUT_NO"));
						if(listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
							vObj.setName(rs.getString("STAFF_NAME"));
							vObj.setAuthorshipType(rs.getString("AUTHORSHIP_TYPE"));
							vObj.setAuthorship(rs.getString("AUTHORSHIP"));
							vObj.setStaffNumber(rs.getString("AUTHORSHIP_STAFF_NO"));
							vObj.setDepartment(rs.getString("DEPARTMENT"));
						}
						String authorList = "";

						for(int i=1 ; i <= listNum ; ++i) {
							
							String authListSeg = rs.getString("AUTHOR_LIST"+i);
							
							
							if (authListSeg != null) {
								String [] authArr = authListSeg.split("<br/>");
								
								//For ordering, please use linkedHashSet
								
								Set<String> authSet = new LinkedHashSet<>(Arrays.asList(authArr));
								authListSeg = String.join("<br/>", authSet);
							}
							//System.out.println("Author AFTER:" + authListSeg);
							
							if(StringUtils.isNotBlank(authListSeg)) {
								if(i != 1)
									authorList += "<br/>" ;
								authorList += authListSeg;
							}
						}
						vObj.setAuthorListWithDeptAndAuthType(authorList);
						// 2.x
						vObj.setLanguage(rs.getString("LANGUAGE"));
						vObj.setSapOutputCat(rs.getString("OUTPUT_CAT"));
						vObj.setSapOutputType(rs.getString("OUTPUT_TYPE"));
						vObj.setJournalPublicationDiscipline(rs.getString("JOURNAL_PUBLICATION_DISCIPLINE"));
						vObj.setSapReferedJournal(rs.getString("SAP_REFERED_JOURNAL"));
						vObj.setIEdWorkInd(rs.getString("IED_WORK_IND"));
						vObj.setIsEnhHighEdu(rs.getString("IS_ENH_HIGH_EDU"));
						vObj.setIsIntlConf(rs.getString("IS_INTL_CONF"));
						vObj.setIs_rgc_proj(rs.getString("IS_RGC_PROJ"));
						vObj.setRgc_proj_num(rs.getString("RGC_PROJ_NUM"));
						// 3.x
						vObj.setApaCitation(rs.getString("APA_CITATION"));
						// 4.x
						vObj.setArticleTitle(rs.getString("TITLE_OF_RESEARCH_OUTPUT"));
						vObj.setTitle(rs.getString("TITLE_PAPER_ART"));
						vObj.setResearchActivityRanking(rs.getString("RESEARCH_ACTIVITY_RANKING"));
						vObj.setTotalNoOfAuthor(rs.getInt("TOTAL_NO_OF_AUTHOR"));
						vObj.setIssn(rs.getString("ISSN"));
						vObj.setEissn(rs.getString("EISSN"));
						vObj.setIsbn(rs.getString("ISBN"));
						vObj.setDoi(rs.getString("DOI"));
						vObj.setUrl(rs.getString("FULLTEXT_URL"));
						vObj.setVolIssue(rs.getString("VOL_ISSUE"));
						vObj.setPageNum(rs.getString("PAGE_NUM"));
						vObj.setPageNumFrom(rs.getString("PAGE_NUM_FROM"));
						vObj.setPageNumTo(rs.getString("PAGE_NUM_TO"));
						vObj.setFromMonth(rs.getString("FROM_MONTH"));
						vObj.setFromYear(rs.getString("FROM_YEAR"));
						vObj.setToMonth(rs.getString("TO_MONTH"));
						vObj.setToYear(rs.getString("TO_YEAR"));
						vObj.setArticleNo(rs.getString("ARTICLE_NUM"));
						vObj.setCity(rs.getString("CITY"));
						vObj.setOpen_access_stat(rs.getString("open_access_stat"));
						vObj.getOpen_access_statStr();
						vObj.setOpen_access_apc(rs.getString("open_access_apc"));
						vObj.setOpen_access_apc_payment(rs.getString("open_access_nonapc_payment"));
						vObj.setApc_val(rs.getString("apc_val"));
						vObj.setOpen_access_art_acc_day(rs.getString("open_access_art_acc_day"));
						vObj.setOpen_access_art_acc_month(rs.getString("open_access_art_acc_month"));
						vObj.setOpen_access_art_acc_year(rs.getString("open_access_art_acc_year"));
						vObj.getOpen_access_art_acc_date();
						vObj.setOpen_access_emb_end_month(rs.getString("open_access_emb_end_month"));
						vObj.setOpen_access_emb_end_year(rs.getString("open_access_emb_end_year"));
						vObj.getOpen_access_emb_end_date();
						vObj.setOpen_access_emb_period_month(rs.getString("open_access_emb_period_month"));
						vObj.setOpen_access_emb_period_year(rs.getString("open_access_emb_period_year"));
						vObj.getOpen_access_emb_period_date();
						vObj.setOpen_access_payment(rs.getString("open_access_payment"));
						vObj.setIs_tran_agrt(rs.getString("is_tran_agrt"));
						vObj.setTran_agrt_val(rs.getString("tran_agrt_val"));
						vObj.setPublisher(rs.getString("PUBLISHER"));
						vObj.setOtherDetails(rs.getString("OTHER_DETAILS"));
						vObj.setNameOtherEditors(rs.getString("NAME_OTHER_EDITORS"));
						vObj.setNameOtherPos(rs.getString("NAME_OTHER_POS"));
						// 6.x
						vObj.setSchCode(rs.getString("SCH_CODE"));
						vObj.setSchDtlCode(rs.getString("SCH_DTL_CODE"));
						vObj.setDaCode(rs.getString("DA_CODE"));
						vObj.setDaDtlCode(rs.getString("DA_DTL_CODE"));
						vObj.setOtherDaDtl(rs.getString("OTHER_DA_DTL"));
						// manageInstituteRI
						vObj.setSdg_str(rs.getString("SDG_CODE"));
						vObj.setCDCFStatus(rs.getString("CDCF_STATUS"));
						vObj.setCdcf_selected_ind(rs.getString("CDCF_SELECTED_IND"));
						
						
						// change to NA by requirements
						if(!(vObj.getIs_rgc_proj() != null && vObj.getIs_rgc_proj().equals("Yes") && 
								vObj.getSapOutputType() != null && 
								(vObj.getSapOutputType().equals("Publication in refereed journal") || 
								vObj.getSapOutputType().equals("Publication in policy or professional journal")) &&
								vObj.getSapReferedJournal() != null &&
								vObj.getSapReferedJournal().equals("Academic Research - Refereed"))) {
							if(!StringUtils.isNotBlank(vObj.getOpen_access_art_acc_date())) vObj.setOpen_access_art_acc_date("NA");
							if(!StringUtils.isNotBlank(vObj.getOpen_access_emb_end_date())) vObj.setOpen_access_emb_end_date("NA");
							if(!StringUtils.isNotBlank(vObj.getOpen_access_emb_period_date())) vObj.setOpen_access_emb_period_date("NA");
							if(!StringUtils.isNotBlank(vObj.getOpen_access_statStr())) vObj.setOpen_access_statStr("NA");
							if(!vObj.getOpen_access_statStr().equals("Immediate Open Access")) {
								if(!StringUtils.isNotBlank(vObj.getOpen_access_apc())) vObj.setOpen_access_apc("NA");
								if(!StringUtils.isNotBlank(vObj.getOpen_access_apc_payment())) vObj.setOpen_access_apc_payment("NA");
								if(!StringUtils.isNotBlank(vObj.getOpen_access_payment())) vObj.setOpen_access_payment("NA");
								if(!StringUtils.isNotBlank(vObj.getApc_val())) vObj.setApc_val("NA");
							}
						}
						
						
						
						if (!CollectionUtils.isEmpty(selectedSDG))
						{
							Boolean sdgPass = false;
							
							for(String sdg : selectedSDG) {
								if(Arrays.asList(vObj.getSdg_str().split(",")).contains(sdg))
									sdgPass = true;
							}
							
							if(sdgPass)
								voList.add(vObj);
						}
						else
							voList.add(vObj);
						
						
						

						
						
					}
				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
			if(voList != null && !voList.isEmpty()) {
				if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_RI_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Publication::getOutputNo));
					else
						voList.sort(Comparator.comparing(Publication::getOutputNo).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_DEFAULT_VALUE) && listingType.equals(RISearchPanel.LIST_TYPE_STAFF_VALUE)) {
			    	Comparator<Publication> nameComp = new Comparator<Publication>() {
			    		@Override public int compare(final Publication record1, final Publication record2) {
			    		    int c = 0;
			    		    if (record1.getName() != null && record2.getName() != null)
			    		    	c = record1.getName().compareTo(record2.getName());
			    		    else if (record1.getName() == null) return 1;
			    		    else if (record2.getName() == null) return -1;
			    		    if (c == 0 && record1.getSapOutputType() != null && record2.getSapOutputType() != null)
			    		       c = record1.getSapOutputType().compareTo(record2.getSapOutputType());
			    		    if (c == 0 && record1.getFromYear() != null && record2.getFromYear() != null)
			    		       c = Integer.valueOf(record1.getFromYear()).compareTo(Integer.valueOf(record2.getFromYear()));
			    		    if (c == 0 && record1.getFromMonth() != null && record2.getFromMonth() != null)
			    		       c = Integer.valueOf(record1.getFromMonth()).compareTo(Integer.valueOf(record2.getFromMonth()));
			    		    if (c == 0 && record1.getNameOtherPos() != null && record2.getNameOtherPos() != null)
			    		       c = record1.getNameOtherPos().compareTo(record2.getNameOtherPos());
			    		    if (c == 0 && record1.getArticleTitle() != null && record2.getArticleTitle() != null)
			    		       c = record1.getArticleTitle().compareTo(record2.getArticleTitle());
			    		    return c;
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, nameComp);
					else
						Collections.sort(voList, nameComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NO_VALUE)) {
			    	if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Publication::getOutputNo, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Publication::getOutputNo, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_RI_NAME_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Publication::getArticleTitle, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Publication::getArticleTitle, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CONTRIBUTOR_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Publication::getAuthorListWithDeptAndAuthType, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Publication::getAuthorListWithDeptAndAuthType, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_FROM_DATE_VALUE)) {
					Comparator<Publication> fromDayComp = new Comparator<Publication>() {
			    		@Override public int compare(final Publication record1, final Publication record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getFromYear() == null ? "1000" : record1.getFromYear();
				    		    String record1Month = record1.getFromMonth() == null ? "1" : record1.getFromMonth();
				    		    String record1NullLast = record1.getFromMonth() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse("01" + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getFromYear() == null ? "1000" : record2.getFromYear();
				    		    String record2Month = record2.getFromMonth() == null ? "1" : record2.getFromMonth();
				    		    String record2NullLast = record2.getFromMonth() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse("01" + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getPublicationListByIds having non number from date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, fromDayComp);
					else
						Collections.sort(voList, fromDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_TO_DATE_VALUE)) {
					Comparator<Publication> toDayComp = new Comparator<Publication>() {
			    		@Override public int compare(final Publication record1, final Publication record2) {
			    			try {
				    		    SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH");
				    		    String record1Year = record1.getToYear() == null ? "1000" : record1.getToYear();
				    		    String record1Month = record1.getToMonth() == null ? "1" : record1.getToMonth();
				    		    String record1NullLast = record1.getToMonth() == null ? "00" : "01";
				    		    java.util.Date record1Date = format.parse("01" + "/" + record1Month + "/" + record1Year + " " + record1NullLast);
				    		    String record2Year = record2.getToYear() == null ? "1000" : record2.getToYear();
				    		    String record2Month = record2.getToMonth() == null ? "1" : record2.getToMonth();
				    		    String record2NullLast = record2.getToMonth() == null ? "00" : "01";
				    		    java.util.Date record2Date = format.parse("01" + "/" + record2Month + "/" + record2Year + " " + record2NullLast);
				    			return record1Date.compareTo(record2Date);
			    			}
			    			catch (Exception e)
			    			{
			    				logger.log(Level.WARNING, "getPublicationListByIds having non number to date", e);
			    				throw new RuntimeException(e);
			    			}
			    		}
			    	};
			    	if(sortOrder.equals("ASC"))
						Collections.sort(voList, toDayComp);
					else
						Collections.sort(voList, toDayComp.reversed());
			    }
				else if(sortCol.equals(RISearchPanel.SORT_COL_CDCF_STAT_VALUE)) {
					if(sortOrder.equals("ASC"))
						voList.sort(Comparator.comparing(Publication::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())));
					else
						voList.sort(Comparator.comparing(Publication::getCDCFStatus, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
			    }
			}
		}
		
		return voList;
	}
	
	public List<Publication> getResearchOutputBySelectedOutputTypes(String[] selectedOutputTypes, String staffNo) {
		List<Publication> selectedOutputList = new ArrayList<Publication>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
	
			sqlBuf.append("SELECT DISTINCT PD.OUTPUT_NO, PH.APA_CITATION, PH.FROM_YEAR, PH.FROM_MONTH, PH.TO_YEAR, PH.TO_MONTH, " + 
						  "OTV.DESCRIPTION, OTV.LOOKUP_CODE, OTV.PARENT_LOOKUP_CODE, " + 
						  "QH.CDCF_GEN_DATE " + 
						  "FROM RH_P_ACAD_PROF_OUTPUT_DTL PD  " + 
						  "LEFT JOIN RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO " + 
						  "LEFT JOIN RH_L_OUTPUT_TYPE_V OTV ON OTV.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE " + 
						  "LEFT JOIN RH_Q_ACAD_PROF_OUTPUT_HDR QH ON QH.OUTPUT_NO = PD.OUTPUT_NO " + 
						  "WHERE PD.DATA_LEVEL = 'P' " + 
						  "AND OTV.ENABLED_FLAG = 'Y' " + 
						  "AND PD.AUTHORSHIP_STAFF_NO = '" + staffNo + "' ");

				if(selectedOutputTypes.length > 0) {
					sqlBuf.append(" AND ( ");
				}
				
				for(String selectedType: selectedOutputTypes) {
					sqlBuf.append("OTV.LOOKUP_CODE = '" + selectedType + "' OR  ");
				}
				   
				if(selectedOutputTypes.length > 0) {
					sqlBuf.delete(sqlBuf.length() - 4, sqlBuf.length() - 1);
					sqlBuf.append(" ) ");
				}
				
				sqlBuf.append(" ORDER BY OTV.PARENT_LOOKUP_CODE, OTV.LOOKUP_CODE");
				
				//System.out.println("sqlBuf.toString():"+sqlBuf.toString());
				logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next())
				{
					Publication publication = new Publication();
					publication.setOutputNo(rs.getInt("OUTPUT_NO"));
					publication.setApaCitation(rs.getString("APA_CITATION"));
					publication.setFromYear(rs.getString("FROM_YEAR"));
					publication.setFromMonth(rs.getString("FROM_MONTH"));
					publication.setToYear(rs.getString("TO_YEAR"));
					publication.setToMonth(rs.getString("TO_MONTH"));
					publication.setSapOutputType(rs.getString("LOOKUP_CODE"));
					publication.setSapOutputTypeDesc(rs.getString("DESCRIPTION"));
					publication.setCDCFGenDate(rs.getTimestamp("CDCF_GEN_DATE"));
					publication.setParentLookupCode(rs.getString("PARENT_LOOKUP_CODE"));
					
					selectedOutputList.add(publication);						
				}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(selectedOutputList) ? selectedOutputList : null); 
	}
	
	public List<Publication> getParentOutputTypesList(){
		List<Publication> parentOutputTypesList = new ArrayList<Publication>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
	
			sqlBuf.append("SELECT DESCRIPTION, LOOKUP_CODE " + 
						  "FROM RH_L_OUTPUT_TYPE_V  " + 
						  "WHERE PARENT_LOOKUP_CODE IS NULL " + 
						  "AND ENABLED_FLAG = 'Y' " + 
						  "ORDER BY LOOKUP_CODE ");

				logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next())
				{
					Publication publication = new Publication();
					publication.setSapOutputType(rs.getString("LOOKUP_CODE"));
					publication.setSapOutputTypeDesc(rs.getString("DESCRIPTION"));
					
					parentOutputTypesList.add(publication);						
				}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return parentOutputTypesList;
	}
	
	public List<Integer> getOutputNoListByDept(List<String> deptList, String data_level, List<String> outputTypeList) throws SQLException
	{
		List<Integer> voList = new ArrayList<Integer>();
		
		if(deptList != null && data_level != null) {
			if (deptList.size() > 0) {
				PersistenceManager pm = PersistenceManager.getInstance();
				Connection conn = null;
				PreparedStatement pStmt = null;
			try
			{
				conn = pm.getConnection();
				
				StringBuffer sqlBuf = new StringBuffer();
			
			    sqlBuf.append(
			    		" SELECT DISTINCT PD.OUTPUT_NO " );
			    sqlBuf.append(
			    		" FROM RH_P_ACAD_PROF_OUTPUT_DTL PD  " +
			    		" LEFT JOIN RH_P_ACAD_PROF_OUTPUT_HDR PH ON (PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL) " +		
			    		" LEFT JOIN RH_P_STAFF_IDENTITY_DEPT STAFF_DEPT ON (PD.AUTHORSHIP_STAFF_NO = STAFF_DEPT.STAFF_NUMBER) " );
			    sqlBuf.append(
			    		" WHERE  " + 
			    		" PD.DATA_LEVEL = '" + data_level + "' ");
			    sqlBuf.append(
			    		" AND STAFF_DEPT.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			    if (outputTypeList != null) {
				    sqlBuf.append(
				    		" AND PH.SAP_OUTPUT_TYPE IN ( '" +
				    		outputTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			    }
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
	
				while (rs.next())
				{
					voList.add(rs.getInt("OUTPUT_NO"));
				}
			}
			
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			}
		}
		return voList;
	}
	
	public List<Summary> getOutputSummaryCountList(String staffNo, String dataLevel, String startDate, String endDate, List<String> deptList, boolean isTotal, List<String> outputTypeList, String is_intl_conf, String is_rgc_proj, String is_enh_high_edu){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (isTotal == false && deptList != null) {
				selectDept = "SD.DEPT_CODE_EACH, ";
			}
			sqlBuf.append("SELECT RP.PERIOD_ID, RP.PERIOD_DESC, " + selectDept + "COUNT((H.OUTPUT_NO)) AS C FROM RICH.RH_P_ACAD_PROF_OUTPUT_HDR H"
					+ "    LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL D ON H.OUTPUT_NO = D.OUTPUT_NO AND H.DATA_LEVEL = D.DATA_LEVEL"
					+ "    LEFT JOIN RICH.RH_P_STAFF_IDENTITY_DEPT SD ON D.AUTHORSHIP_STAFF_NO = SD.STAFF_NUMBER "
					+ "    LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON "
					+ "        TO_DATE(NVL(FROM_MONTH, '01') || '/' || FROM_YEAR, 'MM/YYYY') BETWEEN  "
					+ "        TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY')");
			sqlBuf.append(" WHERE H.DATA_LEVEL = '"+dataLevel+"' AND RP.PERIOD_ID > 1 ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND D.AUTHORSHIP_STAFF_NO = '"+staffNo+"' ");
			}
			if (outputTypeList != null) {
				sqlBuf.append(" AND H.sap_output_type IN ( '" +
						outputTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (!Strings.isNullOrEmpty(is_intl_conf)) {
				sqlBuf.append(" AND H.is_intl_conf = '"+is_intl_conf+"' AND H.sap_output_type IN (180,190,200) ");
			}
			if (!Strings.isNullOrEmpty(is_rgc_proj)) {
				sqlBuf.append(" AND H.is_rgc_proj = '"+is_rgc_proj+"' ");
			}
			if (!Strings.isNullOrEmpty(is_enh_high_edu)) {
				sqlBuf.append(" AND H.is_enh_high_edu = '"+is_enh_high_edu+"' ");
			}

			
			if (!Strings.isNullOrEmpty(startDate) && Strings.isNullOrEmpty(endDate)) {
				
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(FROM_MONTH, '"+startMonthStr+"') || '/' || FROM_YEAR, 'MM/YYYY') >= "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') ");
			}
			
			if (Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				
				sqlBuf.append(" AND TO_DATE(NVL(FROM_MONTH, '01') || '/' || FROM_YEAR, 'MM/YYYY') <= "
						+ "        TO_DATE('"+endDate+"', 'MM/YYYY') ");
			}
			
			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(FROM_MONTH, '"+startMonthStr+"') || '/' || FROM_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			
			
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (isTotal == false && deptList != null) {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC, SD.DEPT_CODE_EACH "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC, SD.DEPT_CODE_EACH ");
			}else {
				sqlBuf.append("	GROUP BY RP.PERIOD_ID, RP.PERIOD_DESC "
						+ "	ORDER BY RP.PERIOD_ID DESC, RP.PERIOD_DESC ");
			}
			//System.out.println("getOutputSummaryCountList:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Summary obj = new Summary();
				obj.setPeriod_id(rs.getString("PERIOD_ID"));
				obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
				obj.setCount(rs.getString("C"));
				if (isTotal == false && deptList != null) {
					obj.setFacDept(rs.getString("DEPT_CODE_EACH"));
				}
				objList.add(obj);	

			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		return objList;
	}
	
	public List<Summary> getOutputSummaryWeightingList(String staffNo, String dataLevel, String startDate, String endDate, List<String> deptList, boolean isTotal, List<String> outputTypeList, String is_intl_conf, String is_rgc_proj, String is_enh_high_edu){
		List<Summary> objList = new ArrayList<Summary>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();
			String selectDept = "";
			if (isTotal == false && deptList != null) {
				selectDept = "DEPT_CODE_EACH, ";
			}
			sqlBuf.append(" SELECT DISTINCT H.OUTPUT_NO, RP.PERIOD_ID, RP.PERIOD_DESC, " + selectDept + "TO_NUMBER(DV.WEIGHTING) AS W FROM RH_P_ACAD_PROF_OUTPUT_HDR H"
					+ "    LEFT JOIN RH_P_ACAD_PROF_OUTPUT_DTL_V DV ON H.OUTPUT_NO = DV.OUTPUT_NO AND H.DATA_LEVEL = DV.DATA_LEVEL  "
					+ "    LEFT JOIN RH_P_STAFF_IDENTITY_DEPT SD ON DV.AUTHORSHIP_STAFF_NO = SD.STAFF_NUMBER "
					+ "    LEFT JOIN RH_Z_CDCF_RPT_PERIOD RP ON "
					+ "        TO_DATE(NVL(H.FROM_MONTH, '01') || '/' || H.FROM_YEAR, 'MM/YYYY') BETWEEN  "
					+ "        TO_DATE(TO_CHAR(RP.DATE_FROM, 'MM/YYYY'), 'MM/YYYY') AND TO_DATE(TO_CHAR(RP.DATE_TO, 'MM/YYYY'), 'MM/YYYY')");
			sqlBuf.append(" WHERE H.DATA_LEVEL = '"+dataLevel+"' AND RP.PERIOD_ID > 1 ");
			if (!Strings.isNullOrEmpty(staffNo)) {
				sqlBuf.append(" AND DV.AUTHORSHIP_STAFF_NO = '"+staffNo+"' ");
			}
			if (outputTypeList != null) {
				sqlBuf.append(" AND H.sap_output_type IN ( '" +
						outputTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (!Strings.isNullOrEmpty(is_intl_conf)) {
				sqlBuf.append(" AND H.is_intl_conf = '"+is_intl_conf+"' AND H.sap_output_type IN (180,190,200) ");
			}
			if (!Strings.isNullOrEmpty(is_rgc_proj)) {
				sqlBuf.append(" AND H.is_rgc_proj = '"+is_rgc_proj+"' ");
			}
			if (!Strings.isNullOrEmpty(is_enh_high_edu)) {
				sqlBuf.append(" AND H.is_enh_high_edu = '"+is_enh_high_edu+"' ");
			}
			
			if (!Strings.isNullOrEmpty(startDate) && Strings.isNullOrEmpty(endDate)) {
				
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(FROM_MONTH, '"+startMonthStr+"') || '/' || FROM_YEAR, 'MM/YYYY') >= "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') ");
			}
			
			if (Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				
				sqlBuf.append(" AND TO_DATE(NVL(FROM_MONTH, '01') || '/' || FROM_YEAR, 'MM/YYYY') <= "
						+ "        TO_DATE('"+endDate+"', 'MM/YYYY') ");
			}
			
			if (!Strings.isNullOrEmpty(startDate) && !Strings.isNullOrEmpty(endDate)) {
				String startMonthStr = startDate.substring(0,2);
				sqlBuf.append(" AND TO_DATE(NVL(H.FROM_MONTH, '"+startMonthStr+"') || '/' || H.FROM_YEAR, 'MM/YYYY') BETWEEN "
						+ "        TO_DATE('"+startDate+"', 'MM/YYYY') AND TO_DATE('"+endDate+"', 'MM/YYYY')");
			}
			
			if (deptList != null) {
				sqlBuf.append(" AND SD.DEPT_CODE_EACH IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			
			if (isTotal == false && deptList != null) {
				sqlBuf.append("	ORDER BY PERIOD_ID DESC, PERIOD_DESC, DEPT_CODE_EACH, H.OUTPUT_NO ");
			}else {
				sqlBuf.append("	ORDER BY PERIOD_ID DESC, PERIOD_DESC, H.OUTPUT_NO ");
			}
			
			//System.out.println("getOutputSummaryWeightingList:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Summary obj = new Summary();
				obj.setPeriod_id(rs.getString("PERIOD_ID"));
				obj.setPeriod_desc(rs.getString("PERIOD_DESC"));
				obj.setWeighting(rs.getString("W"));
				if (isTotal == false && deptList != null) {
					obj.setFacDept(rs.getString("DEPT_CODE_EACH"));
				}
				objList.add(obj);						
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		return objList;
	}
	
	public List<Publication> getCHRMAcadProfReportList(String dataLevel, String startDate, String endDate){
		List<Publication> objList = new ArrayList<Publication>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(
		    		" SELECT PH.OUTPUT_NO, " +
		    		" TO_CHAR(PH.CENSUS_DATE, 'DD-FMMon-YYYY') AS CENSUS_DATE, " +
		    		" TITLE_JOUR_BOOK, " + 
		    		" OUTPUT_TITLE_CONTINUE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_OUTPUT_TYPE_V WHERE LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND LOOKUP_LEVEL = 2) AS OUTPUT_TYPE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_RESEARCH_TYPE_V WHERE LOOKUP_CODE = SAP_REFERED_JOURNAL) AS SAP_REFERED_JOURNAL, " + 
		    		" TITLE_PAPER_ART, " + 
		    		" NAME_OTHER_EDITORS, " + 
		    		" VOL_ISSUE, " + 
		    		" PAGE_NUM, " + 
		    		" CITY, " + 
		    		" FROM_MONTH, " + 
		    		" FROM_YEAR, " + 
		    		" TO_MONTH, " + 
		    		" TO_YEAR, " + 
		    		" PUBLISHER, " + 
		    		" OTHER_DETAILS, " + 
		    		" OTHER_DETAILS_CONTINUE, " + 
		    		" PH.LANGUAGE, " + 
		    		" CONCATENATED_AUTHOR_NAME, " + 
		    		//Exceed Char Indicator
		    		" CASE WHEN IS_RGC_PROJ = 'Y' THEN 'Yes' WHEN IS_RGC_PROJ = 'N' THEN 'No' ELSE '' END AS IS_RGC_PROJ, " + 
		    		" RGC_PROJ_NUM, " + 
		    		" OPEN_ACCESS_STAT, " + 
		    		" (CASE WHEN OPEN_ACCESS_APC = 'Y' THEN 'Yes' WHEN OPEN_ACCESS_APC = 'N' THEN 'No' ELSE '' END) AS OPEN_ACCESS_APC, " + 
		    		" (CASE WHEN OPEN_ACCESS_NONAPC_PAYMENT = 'Y' THEN 'Yes' WHEN OPEN_ACCESS_NONAPC_PAYMENT = 'N' THEN 'No' ELSE '' END) AS OPEN_ACCESS_NONAPC_PAYMENT, " + 
		    		" (CASE WHEN open_access_payment = 'Y' THEN 'Yes' WHEN open_access_payment = 'N' THEN 'No' ELSE '' END) AS open_access_payment, " + 
		    		" APC_VAL, " + 
		    		" open_access_art_acc_day, " +
		    		" open_access_art_acc_month, " +
		    		" open_access_art_acc_year, " +
		    		" open_access_emb_end_month, " + 
		    		" open_access_emb_end_year, " + 
		    		" open_access_emb_period_month, " + 
		    		" open_access_emb_period_year, " + 
		    		" (CASE WHEN IS_TRAN_AGRT = 'Y' THEN 'Yes' WHEN IS_TRAN_AGRT = 'N' THEN 'No' ELSE '' END) AS IS_TRAN_AGRT, " + 
		    		" tran_agrt_val, " + 
		    		//Accessibility
		    		" key_research_areas, " + 
		    		" other_key_research_areas, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_CODE AND LOOKUP_LEVEL = 1) AS SCH_CODE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_EDU_SECTOR_V WHERE LOOKUP_CODE = SCH_DTL_CODE AND LOOKUP_LEVEL = 2) AS SCH_DTL_CODE, " + 
		    		//research areas
		    		//research areas details
		    		//No. of Author(s) and the details
		    		" RESEARCH_ACTIVITY_RANKING, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_CODE AND LOOKUP_LEVEL = 1) AS DA_CODE, " + 
		    		" (SELECT DESCRIPTION FROM RH_L_DIS_AREA_V WHERE LOOKUP_CODE = DA_DTL_CODE AND LOOKUP_LEVEL = 2) AS DA_DTL_CODE, " + 
		    		" OTHER_DA_DTL ");
		    		sqlBuf.append(" FROM RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH "
		    		+ " LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON (QH.output_no = PH.output_no) ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			sqlBuf.append("	ORDER BY PH.OUTPUT_NO ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Publication vObj = new Publication();
				vObj.setOutputNo(rs.getInt("OUTPUT_NO"));
				vObj.setPublishedCensusDate(rs.getString("CENSUS_DATE"));
				vObj.setArticleTitle(rs.getString("TITLE_JOUR_BOOK"));
				vObj.setArticleTitleContinue(rs.getString("OUTPUT_TITLE_CONTINUE"));
				vObj.setSapOutputType(rs.getString("OUTPUT_TYPE"));
				vObj.setSapReferedJournal(rs.getString("SAP_REFERED_JOURNAL"));
				vObj.setTitle(rs.getString("TITLE_PAPER_ART"));
				vObj.setNameOtherEditors(rs.getString("NAME_OTHER_EDITORS"));
				vObj.setVolIssue(rs.getString("VOL_ISSUE"));
				vObj.setPageNum(rs.getString("PAGE_NUM"));
				vObj.setCity(rs.getString("CITY"));
				vObj.setFromMonth(rs.getString("FROM_MONTH"));
				vObj.setFromYear(rs.getString("FROM_YEAR"));
				vObj.setToMonth(rs.getString("TO_MONTH"));
				vObj.setToYear(rs.getString("TO_YEAR"));
				vObj.setPublisher(rs.getString("PUBLISHER"));
				vObj.setOtherDetails(rs.getString("OTHER_DETAILS"));
				vObj.setOtherDetailsContinue(rs.getString("OTHER_DETAILS_CONTINUE"));
				vObj.setLanguage(rs.getString("LANGUAGE"));
				vObj.setAuthorListWithDeptAndAuthType(rs.getString("CONCATENATED_AUTHOR_NAME"));
				vObj.getExceed_char_ind();
				vObj.setIs_rgc_proj(rs.getString("IS_RGC_PROJ"));
				vObj.setRgc_proj_num(rs.getString("RGC_PROJ_NUM"));
				vObj.setOpen_access_stat(rs.getString("open_access_stat"));
				vObj.getOpen_access_statStr();
				vObj.setOpen_access_apc(rs.getString("open_access_apc"));
				vObj.setOpen_access_apc_payment(rs.getString("open_access_nonapc_payment"));
				vObj.setOpen_access_payment(rs.getString("open_access_payment"));
				vObj.setApc_val(rs.getString("apc_val"));
				vObj.setOpen_access_art_acc_day(rs.getString("open_access_art_acc_day"));
				vObj.setOpen_access_art_acc_month(rs.getString("open_access_art_acc_month"));
				vObj.setOpen_access_art_acc_year(rs.getString("open_access_art_acc_year"));
				vObj.getOpen_access_art_acc_date();
				vObj.setOpen_access_emb_end_month(rs.getString("open_access_emb_end_month"));
				vObj.setOpen_access_emb_end_year(rs.getString("open_access_emb_end_year"));
				vObj.getOpen_access_emb_end_date();
				vObj.setOpen_access_emb_period_month(rs.getString("open_access_emb_period_month"));
				vObj.setOpen_access_emb_period_year(rs.getString("open_access_emb_period_year"));
				vObj.getOpen_access_emb_period_date();
				vObj.getCal_emb_end_date();
				vObj.getEmb_date_after_census();
				vObj.getDelay_open_access();
				vObj.setIs_tran_agrt(rs.getString("is_tran_agrt"));
				vObj.setTran_agrt_val(rs.getString("tran_agrt_val"));
				vObj.getAccessibility();
				vObj.setKeyResearchAreas(rs.getString("key_research_areas"));
				vObj.setKeyResearchAreasOther(rs.getString("other_key_research_areas"));
				vObj.setSchCode(rs.getString("SCH_CODE"));
				vObj.setSchDtlCode(rs.getString("SCH_DTL_CODE"));
				vObj.setResearchArea("");
				vObj.setResearchAreaDetail("");
				List<OutputDetails_P> pDtlList = getOutputDetails_P(rs.getInt("OUTPUT_NO"), dataLevel);
				vObj.setpDtlList(pDtlList);
				vObj.setNoPDtl(pDtlList.size());
				vObj.setResearchActivityRanking(rs.getString("RESEARCH_ACTIVITY_RANKING"));
				vObj.setDaCode(rs.getString("DA_CODE"));
				vObj.setDaDtlCode(rs.getString("DA_DTL_CODE"));
				vObj.setOtherDaDtl(rs.getString("OTHER_DA_DTL"));	
				
				objList.add(vObj);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	
	public Map<String, Map<String, List<Publication>>> getCHRM001ReportList(String dataLevel, String startDate, String endDate, List<String> deptList,String selectedDept){
		Map<String, Map<String, List<Publication>>> objMap = new LinkedHashMap<String, Map<String, List<Publication>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT PH.OUTPUT_NO, "
					+ " PH.APA_CITATION, "
					+ " (SELECT DESCRIPTION FROM RH_L_OUTPUT_TYPE_V WHERE LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND LOOKUP_LEVEL = 2) AS OUTPUT_TYPE, "
					+ " (SELECT DESCRIPTION FROM RH_L_RESEARCH_TYPE_V WHERE LOOKUP_CODE = PH.SAP_REFERED_JOURNAL) AS SAP_REFERED_JOURNAL, " 
					+ " PDV.WEIGHTING, "
					+ "  ( SELECT rtrim( xmlagg( xmlelement(e, PDS.AUTHORSHIP_NAME || nvl2( PDS.AUTHORSHIP_STAFF_NO,' ('|| PDS.AUTHORSHIP_STAFF_NO ||') ','' )||','||  AUTH.DESCRIPTION || nvl2(AUTHTYPE.DESCRIPTION, ','||AUTHTYPE.DESCRIPTION,'')   ,  " + 
					"            ';').extract('//text()') order by PDS.line_no ).getclobval(),';')  " + 
					"        FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PDS "
					+ "	LEFT JOIN RICH.RH_Z_LOOKUP_VALUES AUTH ON   AUTH.LOOKUP_CODE = PDS.AUTHORSHIP_TYPE  AND AUTH.LOOKUP_TYPE = 'AUTHORSHIP' " 
					+ "	LEFT JOIN RICH.RH_Z_LOOKUP_VALUES AUTHTYPE ON   AUTHTYPE.LOOKUP_CODE = PDS.AUTHORSHIP_DTL_TYPE AND AUTHTYPE.LOOKUP_TYPE = 'CHRM_CIRD_SUBAUTHORSHIP'" + 
					"        WHERE  PDS.OUTPUT_NO = PD.OUTPUT_NO "
					+ "		AND PDS.DATA_LEVEL = PD.DATA_LEVEL GROUP BY PDS.OUTPUT_NO ) AS  AUTHORSHIP_NAME,  "
					
					+ " ((SELECT DESCRIPTION FROM RH_L_ORG_UNIT_V WHERE LOOKUP_CODE = S.DEPARTMENT_CODE) || ' (' || S.DEPARTMENT_CODE || ')') AS DEPARTMENT, "
					+ " ( NVL2(S.HUSBAND_LAST_NAME,S.HUSBAND_LAST_NAME || ' ','') || S.LAST_NAME || ' ' || S.FIRST_NAME || ' ' || S.CHINESE_NAME || ' (' || S.EMPLOYEE_NUMBER || ') ' ) AS STAFF_NAME "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " RIGHT JOIN (select DISTINCT SV.EMPLOYEE_NUMBER, SV.DEPARTMENT_CODE, SV.HUSBAND_LAST_NAME,SV.LAST_NAME,SV.FIRST_NAME,SV.CHINESE_NAME " + 
					"                FROM RICH.RH_S_ELIGIBLE_STAFF_LIST_V SV  ) S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED'" 
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (!selectedDept.equals("All")) 
				sqlBuf.append( " AND S.DEPARTMENT_CODE = '"+selectedDept+"'" );
			else {
				if(deptList != null) {
					sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
				}
			}
			
			sqlBuf.append("	ORDER BY S.DEPARTMENT_CODE, S.LAST_NAME, S.FIRST_NAME ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
		
			
			while (rs.next())
			{
				Publication vObj = new Publication();
				vObj.setOutputNo(rs.getInt("OUTPUT_NO"));
				vObj.setApaCitation(rs.getString("APA_CITATION"));
				vObj.setSapOutputType(rs.getString("OUTPUT_TYPE"));
				vObj.setSapReferedJournal(rs.getString("SAP_REFERED_JOURNAL"));
				vObj.setWeighting(rs.getDouble("WEIGHTING"));
				
				
				//Special Symbol: & '  
				vObj.setAuthorListWithDeptAndAuthType(rs.getString("AUTHORSHIP_NAME").replace("&amp;","&").replace("&apos;", "'"));
				vObj.getExceed_char_ind();
				vObj.setDepartment(rs.getString("DEPARTMENT"));
				vObj.setStaffFullname(rs.getString("STAFF_NAME"));
				
				if(objMap.get(vObj.getDepartment()) == null) {
					objMap.put(vObj.getDepartment(), new LinkedHashMap<String, List<Publication>>());
				}
				if(objMap.get(vObj.getDepartment()).get(vObj.getStaffFullname()) == null) {
					objMap.get(vObj.getDepartment()).put(vObj.getStaffFullname(), new ArrayList<Publication>());
				}
				objMap.get(vObj.getDepartment()).get(vObj.getStaffFullname()).add(vObj);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, Double>>> getCHRM004ReportList(String dataLevel, String startDate, String endDate, List<String> deptList, String selectedDept){
		Map<String, Map<String, Map<String, Double>>> objMap = new LinkedHashMap<String, Map<String, Map<String, Double>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append(""
					+ "SELECT DEPARTMENT_CODE, " + 
					"SAP_OUTPUT_TYPE,  " + 
					"SAP_REFERED_JOURNAL,  " + 
					"OUTPUT_TYPE_STR,   " + 
					"SAP_REFERED_JOURNAL_STR,  " + 
					"PRINT_ORDER,  " + 
					"SUM(WEIGHTING) AS WEIGHTING " + 
					"FROM (  "
					+ "SELECT S.DEPARTMENT_CODE, "
					+ " PH.SAP_OUTPUT_TYPE, "
					+ " PH.SAP_REFERED_JOURNAL, "
					+ " OPTYPE.DESCRIPTION AS OUTPUT_TYPE_STR, OPTYPE.PRINT_ORDER as type_order,"
					+ " SAP.DESCRIPTION AS SAP_REFERED_JOURNAL_STR, " 
					+ " SAP.PRINT_ORDER ,"
					+ " PD.AUTHORSHIP_PERSON_ID, " 
					+ " (PDV.WEIGHTING * S.DCC_PERCENTAGE /100) AS WEIGHTING "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_RESEARCH_TYPE_V SAP ON SAP.LOOKUP_CODE = PH.SAP_REFERED_JOURNAL "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			
			
			if (selectedDept.equals("All") ) {
				if (deptList != null) {
	
					sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
				}
			}
			else {
				sqlBuf.append("AND S.DEPARTMENT_CODE = '"+selectedDept+"' ") ;
			}
				
			sqlBuf.append(" ) GROUP BY DEPARTMENT_CODE, SAP_OUTPUT_TYPE, OUTPUT_TYPE_STR, SAP_REFERED_JOURNAL, type_order, SAP_REFERED_JOURNAL_STR, PRINT_ORDER "
					+ " ORDER BY type_order, PRINT_ORDER, DEPARTMENT_CODE ");
			
			List<OutputType> outputTypeList =  getOutputTypeListByLevel(2,"Y");
			
			for(OutputType  outputTypeEle : outputTypeList) {
				
				if(objMap.get(outputTypeEle.getDescription()) == null) 
					objMap.put(outputTypeEle.getDescription(), new LinkedHashMap<String, Map<String, Double>>());
				
			}
			
			//System.out.println("SQL: " + sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			
			while (rs.next())
			{
				String dept = rs.getString("DEPARTMENT_CODE");
				String outputType = rs.getString("OUTPUT_TYPE_STR");
				String sapReferedJournal = rs.getString("SAP_REFERED_JOURNAL_STR");
				Double weighting = rs.getDouble("WEIGHTING");
				
				if(objMap.get(outputType) == null) {
					objMap.put(outputType, new LinkedHashMap<String, Map<String, Double>>());
				}
				if(objMap.get(outputType).get(sapReferedJournal) == null) {
					objMap.get(outputType).put(sapReferedJournal, new LinkedHashMap<String, Double>());
				}
				objMap.get(outputType).get(sapReferedJournal).put(dept, weighting);
				
				//System.out.println("objMap: " + objMap);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, Double>>> getCHRM009ReportList(String dataLevel, String startDate, String endDate, List<String> deptList, String selectedDept){
		Map<String, Map<String, Map<String, Double>>> objMap = new LinkedHashMap<String, Map<String, Map<String, Double>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT  DEPARTMENT_NAME,  " + 
					"        OUTPUT_TYPE,  " + 
					"        STAFF_NAME,  " + 
					"        (LAST_NAME || ' ' || FIRST_NAME),  " + 
					"        nvl(SUM(WEIGHTING),0) AS WEIGHTING   " + 
					"FROM "
					+ " ( SELECT DISTINCT NVL(DEPT.DEPARTMENT_NAME, S.DEPARTMENT_CODE) || '_' || S.DEPARTMENT_CODE  AS DEPARTMENT_NAME, " + 
					"    S.EMPLOYEE_NUMBER," + 
					"    (NVL2(S.HUSBAND_LAST_NAME,S.HUSBAND_LAST_NAME || ' ','') || S.LAST_NAME || ' ' || S.FIRST_NAME || '_' || S.EMPLOYEE_NUMBER ) AS STAFF_NAME," + 
					"    S.LAST_NAME, S.FIRST_NAME" + 
					"    FROM RICH.RH_S_ELIGIBLE_STAFF_LIST_V S" + 
					"    LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE  WHERE 1=1 " );
			
			if (selectedDept.equals("All") ) {
				if (deptList != null) {
	
					sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
				}
			}
			else {
				sqlBuf.append("AND S.DEPARTMENT_CODE = '"+selectedDept+"' ") ;
			}
			
			sqlBuf.append( " ) A  LEFT JOIN ( "
					+ " SELECT DISTINCT  PD.AUTHORSHIP_STAFF_NO,  "
					+ " OPTYPE.PARENT_LOOKUP_CODE AS OUTPUT_TYPE,  "
					+ " PDV.WEIGHTING , PD.OUTPUT_NO "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS IN ( 'CDCF_GENERATED', 'CDCF_SPEC') "
					+ " AND TO_DATE('01/'||NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'DD/MM/YYYY') BETWEEN "
					+ " TO_DATE('01/'||'" + startDate + "', 'DD/MM/YYYY') AND " + " TO_DATE('30/'||'" + endDate + "', 'DD/MM/YYYY') ");
			

			sqlBuf.append(" )B ON  B.AUTHORSHIP_STAFF_NO = A.EMPLOYEE_NUMBER GROUP BY DEPARTMENT_NAME, "
					+ " OUTPUT_TYPE, "
					+ " STAFF_NAME, "
					+ " (LAST_NAME || ' ' || FIRST_NAME) "
					+ " ORDER BY DEPARTMENT_NAME, (LAST_NAME || ' ' || FIRST_NAME) ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String dept = rs.getString("DEPARTMENT_NAME");
				String outputType = rs.getString("OUTPUT_TYPE");
				String staffName = rs.getString("STAFF_NAME");
				Double weighting = rs.getDouble("WEIGHTING");
				
				if(objMap.get(dept) == null) {
					objMap.put(dept, new LinkedHashMap<String, Map<String, Double>>());
				}
				if(objMap.get(dept).get(staffName) == null) {
					objMap.get(dept).put(staffName, new LinkedHashMap<String, Double>());
				}
				objMap.get(dept).get(staffName).put(outputType, weighting);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objMap;
	}
	
	public Map<String, Map<String, Map<String,  Map<String, Double>>>> getCHRM011ReportList(String dataLevel, String startDate, String endDate, List<String> deptList, String selectedDept){
		Map<String, Map<String, Map<String,  Map<String, Double>>>> objMap = new LinkedHashMap<String, Map<String, Map<String,  Map<String, Double>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("select  DEPARTMENT_NAME,  " + 
					"        DEPARTMENT_CODE,  " + 
					"        OUTPUT_TYPE,  " + 
					"        STAFF_NAME,  " + 
					"        EMPLOYEE_NUMBER,   " + 
					"        ORDER_NAME,  " + 
					"        SAP_REFERED_JOURNAL,  " + 
					"        SUM(WEIGHTING) AS WEIGHTING   " + 
					" FROM  ( SELECT distinct NVL(DEPT.DEPARTMENT_NAME, S.DEPARTMENT_CODE) AS DEPARTMENT_NAME, " + 
					"                    S.DEPARTMENT_CODE,  " + 
					"                    CASE WHEN S.HUSBAND_LAST_NAME is not null THEN (S.HUSBAND_LAST_NAME || '-' || S.LAST_NAME || ' ' || S.FIRST_NAME)  " + 
					"                        ELSE (S.LAST_NAME || ' ' || S.FIRST_NAME) END AS STAFF_NAME,  " + 
					"                    (S.LAST_NAME || ' ' || S.FIRST_NAME) AS ORDER_NAME , " + 
					"                    S.EMPLOYEE_NUMBER  " + 
					"                    FROM RICH.RH_S_ELIGIBLE_STAFF_LIST_V S  " + 
					"                        LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE  WHERE 1=1 " );
				    if (selectedDept.equals("All") ) {
						if (deptList != null) {
			
							sqlBuf.append("AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
							sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
						}
					}
					else  {
						sqlBuf.append("AND S.DEPARTMENT_CODE = '"+selectedDept+"' ") ;
					}
				sqlBuf.append( ") A " +
					"  LEFT JOIN ( SELECT  DISTINCT PD.AUTHORSHIP_STAFF_NO,  " + 
					"                        OPTYPE.PARENT_LOOKUP_CODE AS OUTPUT_TYPE,   " + 
					"                        PH.SAP_REFERED_JOURNAL,   " + 
					"                        PD.WEIGHTING, PD.OUTPUT_NO, QH.CDCF_STATUS  " + 
					" 	FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PD  "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO 	= PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER 	= PD.AUTHORSHIP_STAFF_NO"
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO 	= QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE 	= PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " WHERE TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || NVL(PH.FROM_YEAR,'9999'), 'MM/YYYY') BETWEEN " +" TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') "
					+ " AND  QH.CDCF_STATUS = 'CDCF_GENERATED' AND PD.DATA_LEVEL = 'C'  " 
					+ "  ) B  ON A.EMPLOYEE_NUMBER =  B.AUTHORSHIP_STAFF_NO    " );

			sqlBuf.append("	GROUP BY DEPARTMENT_NAME, DEPARTMENT_CODE, "
					+ " OUTPUT_TYPE, "
					+ " STAFF_NAME, EMPLOYEE_NUMBER,  "
					+ " SAP_REFERED_JOURNAL, "
					+ " ORDER_NAME "
					+ " ORDER BY DEPARTMENT_NAME, ORDER_NAME ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String dept = rs.getString("DEPARTMENT_NAME") +"---"+rs.getString("DEPARTMENT_CODE");
				String outputType = rs.getString("OUTPUT_TYPE");
				String sapReferedJournal = rs.getString("SAP_REFERED_JOURNAL");
				String staffName = rs.getString("STAFF_NAME")+"---"+rs.getString("EMPLOYEE_NUMBER");
				Double weighting = rs.getDouble("WEIGHTING");
				
				
				if(objMap.get(dept) == null) {
					objMap.put(dept, new LinkedHashMap<String, Map<String,  Map<String, Double>>>());
				}
				if(objMap.get(dept).get(staffName) == null) {
					objMap.get(dept).put(staffName, new LinkedHashMap<String,  Map<String, Double>>());
				}
				if(objMap.get(dept).get(staffName).get(outputType) == null) {
					objMap.get(dept).get(staffName).put(outputType, new HashMap<String, Double>());
				}
				objMap.get(dept).get(staffName).get(outputType).put(sapReferedJournal, weighting);
				
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		//System.out.println("objList: "+objMap);
		return objMap;
	}
	
	public Map<String, List<Publication>> getCHRM012ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		Map<String, List<Publication>> objMap = new LinkedHashMap<String, List<Publication>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT (DCC.LOOKUP_TYPE || ' - ' || DCC.DESCRIPTION) AS DEPARTMENT_NAME, "
					+ " PH.APA_CITATION, "
					+ " (PH.FROM_MONTH || '-' || PH.FROM_YEAR) AS PERIOD, "
					+ " PH.NAME_OTHER_POS, "
					+ " OPTYPE.DESCRIPTION AS OUTPUT_TYPE, "
					+ " PH.SAP_REFERED_JOURNAL, "
					+ " SUM(PDV.WEIGHTING*(S.DCC_PERCENTAGE/100)) AS WEIGHTING, "
					+ "  LISTAGG (S.LAST_NAME || ' ' || S.FIRST_NAME, ';' ) WITHIN GROUP(ORDER BY S.LAST_NAME ) AS STAFF_NAME  "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_L_DCC_V DCC ON S.DCC = DCC.LOOKUP_CODE "
					+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			/*if (deptList != null) {
				sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
			}*/
			sqlBuf.append("	GROUP BY (DCC.LOOKUP_TYPE || ' - ' || DCC.DESCRIPTION), "
					+ " PH.APA_CITATION, "
					+ " (PH.FROM_MONTH || '-' || PH.FROM_YEAR), "
					+ " PH.NAME_OTHER_POS, "
					+ " OPTYPE.DESCRIPTION, "
					+ " PH.SAP_REFERED_JOURNAL "
					+ " ORDER BY (DCC.LOOKUP_TYPE || ' - ' || DCC.DESCRIPTION), PH.APA_CITATION ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				Publication vObj = new Publication();
				vObj.setDepartment(rs.getString("DEPARTMENT_NAME"));
				vObj.setApaCitation(rs.getString("APA_CITATION"));
				vObj.setFromYear(rs.getString("PERIOD"));
				vObj.setNameOtherPos(rs.getString("NAME_OTHER_POS"));
				vObj.setSapOutputType(rs.getString("OUTPUT_TYPE"));
				vObj.setSapReferedJournal(rs.getString("SAP_REFERED_JOURNAL"));
				vObj.setWeighting(rs.getDouble("WEIGHTING"));
				vObj.setStaffFullname(rs.getString("STAFF_NAME"));
				
				if(objMap.get(vObj.getDepartment()) == null) {
					objMap.put(vObj.getDepartment(), new ArrayList<Publication>());
				}
				objMap.get(vObj.getDepartment()).add(vObj);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, Map<String, Map<String,Map<String,List<Double> >>>>>>  getCHRM015ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		
		
		Map<String, Map<String, Map<String, Map<String, Map<String,Map<String,List<Double> >>>>>> objMap 
			= new LinkedHashMap<String, Map<String, Map<String, Map<String, Map<String,Map<String,List<Double>>>>>>>();
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			/*sqlBuf.append("SELECT  SUM(WEIGHTING) AS WEIGHTING, STAFF_GRADE, DEPARTMENT_CODE,  OUTPUT_TYPE,  " + 
					"        SAP_REFERED_JOURNAL,  " + 
					"        IS_RGC_PROJ,  " + 
					"        SUM(APC_VAL)AS APC_VAL, ACCESSIBILITY " + 
					"	FROM " + 
					"	(";*/
			sqlBuf.append( "SELECT DISTINCT S.EMPLOYEE_NUMBER,  " + 
					"   (PDV.WEIGHTING * (S.DCC_PERCENTAGE/100)) AS WEIGHTING,  " + 
					"        S.STAFF_GRADE,  S.DCC AS DEPARTMENT_CODE,  " + 
					"        OPTYPE.PRINT_ORDER AS OUTPUT_TYPE, " + 
					"        PH.SAP_REFERED_JOURNAL,  " + 
					"        PH.IS_RGC_PROJ,  " + 
					"        (NVL(PH.APC_VAL,0)/PDE.EDU_NO_STAFF)*(S.DCC_PERCENTAGE/100) AS APC_VAL, " + 
					"        PH.OUTPUT_NO, "+
					" 		 PH.CENSUS_DATE, " + 
					"        PH.OPEN_ACCESS_APC," + 
					"        PH.OPEN_ACCESS_ART_ACC_DAY," + 
					"        PH.OPEN_ACCESS_ART_ACC_MONTH," + 
					"        PH.OPEN_ACCESS_ART_ACC_YEAR," + 
					"        PH.OPEN_ACCESS_EMB_END_MONTH," + 
					"        PH.OPEN_ACCESS_EMB_END_YEAR," + 
					"        PH.OPEN_ACCESS_EMB_PERIOD_MONTH, " + 
					"        PH.OPEN_ACCESS_EMB_PERIOD_YEAR," + 
					"        PH.OPEN_ACCESS_NONAPC_PAYMENT," + 
					"        PH.OPEN_ACCESS_PAYMENT," + 
					"        PH.OPEN_ACCESS_STAT," + 
					"        PH.IS_TRAN_AGRT  "+
					" FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN (SELECT PDL.OUTPUT_NO,PDL.DATA_LEVEL,PDL.LINE_NO,COUNT(DISTINCT PDL.AUTHORSHIP_STAFF_NO) OVER (PARTITION BY PDL.OUTPUT_NO , PDL.DATA_LEVEL ) AS EDU_NO_STAFF " + 
					"            FROM RH_P_ACAD_PROF_OUTPUT_DTL PDL INNER JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V ALLS ON ALLS.EMPLOYEE_NUMBER = PDL.AUTHORSHIP_STAFF_NO ) PDE  	" + 
					"            ON PD.OUTPUT_NO = PDE.OUTPUT_NO AND PD.DATA_LEVEL = PDE.DATA_LEVEL " 
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_RESEARCH_TYPE_V SAP ON SAP.LOOKUP_CODE = PH.SAP_REFERED_JOURNAL "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_L_DCC_V DCC ON S.DCC = DCC.LOOKUP_CODE "
					+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			
			/*
			if (deptList != null) {
				sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
				sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
			}
			*/
			
			
			/*sqlBuf.append("		)	GROUP BY STAFF_GRADE,DEPARTMENT_CODE,  OUTPUT_TYPE,  " + 
							"        SAP_REFERED_JOURNAL,  IS_RGC_PROJ, ACCESSIBILITY ");
			
			
			sqlBuf.append("	GROUP BY S.EMPLOYEE_NUMBER, "
					+ " S.STAFF_GRADE, "
					+ " S.DCC, "
					+ " OPTYPE.PRINT_ORDER, "
					+ " PH.SAP_REFERED_JOURNAL, "
					+ " SAP.PRINT_ORDER, "
					+ " PH.IS_RGC_PROJ, " + 
					" PH.APC_VAL, " + 
					" PDE.EDU_NO_STAFF, " + 
					" S.DCC_PERCENTAGE, " + 
					" PH.CENSUS_DATE, " + 
					" PH.OPEN_ACCESS_EMB_END_MONTH, " + 
					" PH.OPEN_ACCESS_EMB_END_YEAR, " + 
					" PH.OPEN_ACCESS_EMB_PERIOD_MONTH, " + 
					" PH.OPEN_ACCESS_EMB_PERIOD_YEAR, " + 
					" PH.OPEN_ACCESS_ART_ACC_DAY, " + 
					" PH.OPEN_ACCESS_ART_ACC_MONTH, " + 
					" PH.OPEN_ACCESS_ART_ACC_YEAR, " + 
					" PH.IS_TRAN_AGRT,PH.OPEN_ACCESS_APC, " + 
					" PH.OPEN_ACCESS_NONAPC_PAYMENT, " + 
					" PH.OPEN_ACCESS_PAYMENT, " + 
					" PH.OPEN_ACCESS_STAT "
					+ " ORDER BY S.STAFF_GRADE, S.DCC, OPTYPE.PRINT_ORDER, SAP.PRINT_ORDER ");*/
			
			//System.out.println("sqlBuf:"+sqlBuf.toString());
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
						
			while (rs.next())
			{
				Publication vObj = new Publication();
				List<Double> weightApc = null;
				vObj.setDepartment(rs.getString("DEPARTMENT_CODE"));
				
				vObj.setStaffNumber(rs.getString("STAFF_GRADE"));
				vObj.setSapOutputType(rs.getString("OUTPUT_TYPE"));
				vObj.setSapReferedJournal(rs.getString("SAP_REFERED_JOURNAL"));
				vObj.setWeighting(rs.getDouble("WEIGHTING"));
				vObj.setIs_rgc_proj(rs.getString("IS_RGC_PROJ"));
				vObj.setApc_val(rs.getString("apc_val"));

				vObj.setOpen_access_stat(rs.getString("open_access_stat"));
				vObj.setOpen_access_apc(rs.getString("OPEN_ACCESS_APC"));
				vObj.setOpen_access_apc_payment(rs.getString("open_access_nonapc_payment"));
				vObj.setApc_val(rs.getString("apc_val"));
				vObj.setOpen_access_art_acc_day(rs.getString("open_access_art_acc_day"));
				vObj.setOpen_access_art_acc_month(rs.getString("open_access_art_acc_month"));
				vObj.setOpen_access_art_acc_year(rs.getString("open_access_art_acc_year"));
				vObj.getOpen_access_art_acc_date();
				vObj.setOpen_access_emb_end_month(rs.getString("open_access_emb_end_month"));
				vObj.setOpen_access_emb_end_year(rs.getString("open_access_emb_end_year"));
				vObj.getOpen_access_emb_end_date();
				vObj.setOpen_access_emb_period_month(rs.getString("open_access_emb_period_month"));
				vObj.setOpen_access_emb_period_year(rs.getString("open_access_emb_period_year"));
				vObj.getOpen_access_emb_period_date();
				vObj.setOpen_access_payment(rs.getString("open_access_payment"));
				vObj.setIs_tran_agrt(rs.getString("is_tran_agrt"));

				vObj.getAccessibility();
				
				
				Double apcValue = Double.parseDouble(vObj.getApc_val());
				
				//System.out.println("OBJ: " +vObj.getSapOutputType() +vObj.getAccessibility() );
				
				if(objMap.get(vObj.getStaffNumber()) == null) {
					objMap.put(vObj.getStaffNumber(), new HashMap<String, Map<String,  Map<String, Map<String,Map<String,List<Double>>>>>>());
				}
				if(objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()) == null) {
					objMap.get(vObj.getStaffNumber()).put(vObj.getDepartment(), new HashMap<String,  Map<String, Map<String,Map<String,List<Double>>>>>());
				}
				if(objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()) == null) {
					objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).put(vObj.getSapOutputType(), new HashMap<String, Map<String,Map<String,List<Double>>>>());
				}
				
				if(objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal())== null) {
					objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).put(vObj.getSapReferedJournal() ,new HashMap< String,Map<String,List<Double>>>());
				}
				
				if(objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal()).get(vObj.getIs_rgc_proj()) == null) {
					objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal()).put(vObj.getIs_rgc_proj(),new HashMap<String,List<Double>>());
				}
				
				if(objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal()).get(vObj.getIs_rgc_proj()).get(vObj.getAccessibility()) == null) {
					
					weightApc = new ArrayList<>(Arrays.asList(vObj.getWeighting(),apcValue));
					objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal()).get(vObj.getIs_rgc_proj()).put(vObj.getAccessibility(),weightApc);
				}
				else {
					weightApc = objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal()).get(vObj.getIs_rgc_proj()).get(vObj.getAccessibility()) ;
					
					weightApc.set(0, weightApc.get(0)+vObj.getWeighting());
					weightApc.set(1, weightApc.get(1)+apcValue);
					
					objMap.get(vObj.getStaffNumber()).get(vObj.getDepartment()).get(vObj.getSapOutputType()).get(vObj.getSapReferedJournal()).get(vObj.getIs_rgc_proj()).put(vObj.getAccessibility(),weightApc);
				
				}

				
			}
			
			
		}catch (Exception e){
			//System.out.println("RUNTIME");
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objMap:"+objMap);
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, Map<String, Integer>>>> getCRD001RankReportList(String dataLevel, 
			String startDate, String endDate, List<String> facList, 
			List<String> deptList, List<String> outTypeList){
		Map<String, Map<String, Map<String, Map<String, Integer>>>> objMap = new LinkedHashMap<String, Map<String, Map<String, Map<String, Integer>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT (CASE RANK.RANK WHEN ' A*' THEN 'A*' WHEN 'U' THEN 'Unclassified' ELSE RANK.RANK END) AS RANK, "
					+ " DEPT1.LOOKUP_CODE AS FAC_CODE, "
					+ " DEPT2.LOOKUP_CODE AS DEPARTMENT_CODE, "
					+ " OPTYPE.PARENT_LOOKUP_CODE AS OUTPUT_TYPE, "
					+ " COUNT(PH.OUTPUT_NO) AS WEIGHTING "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_S_JNL_RANK RANK ON (RANK.ISSN_1 = PH.ISSN OR RANK.ISSN_2 = PH.ISSN OR RANK.ISSN_3 = PH.ISSN) "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT2 ON DEPT2.LOOKUP_CODE = S.DEPARTMENT_CODE "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT1 ON DEPT1.LOOKUP_CODE = DEPT2.PARENT_LOOKUP_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND PD.LINE_NO = 1 "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (outTypeList != null) {
				sqlBuf.append(" AND OPTYPE.PARENT_LOOKUP_CODE IN ( '" +
						outTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (facList != null) {
				sqlBuf.append(" AND DEPT1.LOOKUP_CODE IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND DEPT2.LOOKUP_CODE IN ( '" +
						deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY RANK.RANK, "
					+ " DEPT1.LOOKUP_CODE, "
					+ " DEPT2.LOOKUP_CODE, "
					+ " OPTYPE.PARENT_LOOKUP_CODE "
					+ " ORDER BY DEPT1.LOOKUP_CODE, DEPT2.LOOKUP_CODE, RANK.RANK ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fac = rs.getString("FAC_CODE");
				String rank = rs.getString("RANK");
				String dept = rs.getString("DEPARTMENT_CODE");
				String outputType = rs.getString("OUTPUT_TYPE");
				Integer weighting = rs.getInt("WEIGHTING");
				
				
				
				
				if(objMap.get(fac) == null) {
					objMap.put(fac, new HashMap<String, Map<String,  Map<String, Integer>>>());
				}
				if(objMap.get(fac).get(rank) == null) {
					objMap.get(fac).put(rank, new HashMap<String,  Map<String, Integer>>());
				}
				if(objMap.get(fac).get(rank).get(dept) == null) {
					objMap.get(fac).get(rank).put(dept, new HashMap<String, Integer>());
				}
				objMap.get(fac).get(rank).get(dept).put(outputType, weighting);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objMap);
		return objMap;
	}
	
	public Map<String, Map<String, Map<String, Map<String, Integer>>>> getCRD001SJRReportList(String dataLevel, 
			String startDate, String endDate, List<String> facList, 
			List<String> deptList, List<String> outTypeList){
		Map<String, Map<String, Map<String, Map<String, Integer>>>> objMap = new LinkedHashMap<String, Map<String, Map<String, Map<String, Integer>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT RANK.SJR, "
					+ " DEPT1.LOOKUP_CODE AS FAC_CODE, "
					+ " DEPT2.LOOKUP_CODE AS DEPARTMENT_CODE, "
					+ " OPTYPE.PARENT_LOOKUP_CODE AS OUTPUT_TYPE, "
					+ " COUNT(PH.OUTPUT_NO) AS WEIGHTING "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_S_JNL_RANK RANK ON (RANK.ISSN_1 = PH.ISSN OR RANK.ISSN_2 = PH.ISSN OR RANK.ISSN_3 = PH.ISSN) "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT2 ON DEPT2.LOOKUP_CODE = S.DEPARTMENT_CODE "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT1 ON DEPT1.LOOKUP_CODE = DEPT2.PARENT_LOOKUP_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND PD.LINE_NO = 1 "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (outTypeList != null) {
				sqlBuf.append(" AND OPTYPE.PARENT_LOOKUP_CODE IN ( '" +
						outTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (facList != null) {
				sqlBuf.append(" AND DEPT1.LOOKUP_CODE IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND DEPT2.LOOKUP_CODE IN ( '" +
						deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY RANK.SJR, "
					+ " DEPT1.LOOKUP_CODE, "
					+ " DEPT2.LOOKUP_CODE, "
					+ " OPTYPE.PARENT_LOOKUP_CODE "
					+ " ORDER BY DEPT1.LOOKUP_CODE, DEPT2.LOOKUP_CODE, RANK.SJR ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fac = rs.getString("FAC_CODE");
				String sjr = rs.getString("SJR");
				String dept = rs.getString("DEPARTMENT_CODE");
				String outputType = rs.getString("OUTPUT_TYPE");
				Integer weighting = rs.getInt("WEIGHTING");
				
				if(objMap.get(fac) == null) {
					objMap.put(fac, new HashMap<String, Map<String,  Map<String, Integer>>>());
				}
				if(objMap.get(fac).get(sjr) == null) {
					objMap.get(fac).put(sjr, new HashMap<String,  Map<String, Integer>>());
				}
				if(objMap.get(fac).get(sjr).get(dept) == null) {
					objMap.get(fac).get(sjr).put(dept, new HashMap<String, Integer>());
				}
				objMap.get(fac).get(sjr).get(dept).put(outputType, weighting);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public List<Map<String, Map<String, Map<String, Map<String, Integer>>>>> getCRD002ReportList(String dataLevel, 
			String startDate, String endDate, List<String> facList, 
			List<String> deptList, List<String> outTypeList){
		List<Map<String, Map<String, Map<String, Map<String, Integer>>>>> objList= new ArrayList<Map<String, Map<String, Map<String, Map<String, Integer>>>>>();
		Map<String, Map<String, Map<String, Map<String, Integer>>>> rankMap = new LinkedHashMap<String, Map<String, Map<String, Map<String, Integer>>>>();
		Map<String, Map<String, Map<String, Map<String, Integer>>>> sjrMap = new LinkedHashMap<String, Map<String, Map<String, Map<String, Integer>>>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT (CASE RANK.RANK WHEN ' A*' THEN 'A*' WHEN 'U' THEN 'Unclassified' ELSE RANK.RANK END) AS RANK, "
					+ " RANK.SJR, "
					+ " DEPT2.LOOKUP_CODE, S.LAST_NAME, S.FIRST_NAME,"
					+ " (S.LAST_NAME || '/' || S.FIRST_NAME || '/' || S.DEPARTMENT_CODE || '/' || S.POST_RANK_CODE) AS STAFF_NAME, "
					+ " OPTYPE.PARENT_LOOKUP_CODE AS OUTPUT_TYPE, "
					+ " DEPT1.LOOKUP_CODE AS FAC_CODE, "
					+ " COUNT(PH.OUTPUT_NO) AS WEIGHTING "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_S_JNL_RANK RANK ON (RANK.ISSN_1 = PH.ISSN OR RANK.ISSN_2 = PH.ISSN OR RANK.ISSN_3 = PH.ISSN) "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT2 ON DEPT2.LOOKUP_CODE = S.DEPARTMENT_CODE "
					+ " LEFT JOIN RICH.RH_L_ORG_UNIT_V DEPT1 ON DEPT1.LOOKUP_CODE = DEPT2.PARENT_LOOKUP_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (outTypeList != null) {
				sqlBuf.append(" AND OPTYPE.PARENT_LOOKUP_CODE IN ( '" +
						outTypeList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (facList != null) {
				sqlBuf.append(" AND DEPT1.LOOKUP_CODE IN ( '" +
						facList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +
						deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY RANK.RANK, "
					+ " RANK.SJR, "
					+ " DEPT1.LOOKUP_CODE, "
					+ " DEPT2.LOOKUP_CODE, S.LAST_NAME, S.FIRST_NAME, "
					+ " (S.LAST_NAME || '/' || S.FIRST_NAME || '/' || S.DEPARTMENT_CODE || '/' || S.POST_RANK_CODE), "
					+ " OPTYPE.PARENT_LOOKUP_CODE "
					+ " ORDER BY DEPT1.LOOKUP_CODE, DEPT2.LOOKUP_CODE, S.LAST_NAME, S.FIRST_NAME ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String fac = rs.getString("FAC_CODE");
				String rank = rs.getString("RANK");
				String sjr = rs.getString("SJR");
				String name = rs.getString("STAFF_NAME");
				String outputType = rs.getString("OUTPUT_TYPE");
				Integer weighting = rs.getInt("WEIGHTING");
				Integer oriVal = 0;
				
				if(rankMap.get(fac) == null) {
					rankMap.put(fac, new LinkedHashMap<String, Map<String,  Map<String, Integer>>>());
				}
				if(rankMap.get(fac).get(name) == null) {
					rankMap.get(fac).put(name, new HashMap<String,  Map<String, Integer>>());
				}
				if(rankMap.get(fac).get(name).get(outputType) == null) {
					rankMap.get(fac).get(name).put(outputType, new HashMap<String, Integer>());
				}
				oriVal = rankMap.get(fac).get(name).get(outputType).get(rank);
				if(oriVal == null) oriVal = 0;
				rankMap.get(fac).get(name).get(outputType).put(rank, oriVal + weighting);
				
				
				if(sjrMap.get(fac) == null) {
					sjrMap.put(fac, new HashMap<String, Map<String,  Map<String, Integer>>>());
				}
				if(sjrMap.get(fac).get(name) == null) {
					sjrMap.get(fac).put(name, new HashMap<String,  Map<String, Integer>>());
				}
				if(sjrMap.get(fac).get(name).get(outputType) == null) {
					sjrMap.get(fac).get(name).put(outputType, new HashMap<String, Integer>());
				}
				oriVal = sjrMap.get(fac).get(name).get(outputType).get(sjr);
				if(oriVal == null) oriVal = 0;
				sjrMap.get(fac).get(name).get(outputType).put(sjr, oriVal + weighting);
			}
			objList.add(rankMap);
			objList.add(sjrMap);
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public Map<String, Map<String, Double>> getAdHoc001ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		Map<String, Map<String, Double>> objMap = new LinkedHashMap<String, Map<String, Double>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT "
					+ " PH.SAP_OUTPUT_TYPE AS OUTPUT_TYPE, "
					+ " S.DEPARTMENT_CODE, (S.LAST_NAME || ' ' || S.FIRST_NAME), "
					+ " (S.EMPLOYEE_NUMBER || '/' || S.LAST_NAME || ', ' || S.FIRST_NAME || '/' || S.DEPARTMENT_CODE || '/' || S.POST_RANK_CODE) AS STAFF_NAME, "
					+ " SUM(PDV.WEIGHTING) AS WEIGHTING "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS IN ('CDCF_GENERATED' , 'CDCF_SPEC' ) AND SAP_REFERED_JOURNAL = 'Y' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (deptList != null) {
				sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append("	GROUP BY "
					+ " PH.SAP_OUTPUT_TYPE,"
					+ " S.DEPARTMENT_CODE, (S.LAST_NAME || ' ' || S.FIRST_NAME), "
					+ " (S.EMPLOYEE_NUMBER || '/' || S.LAST_NAME || ', ' || S.FIRST_NAME || '/' || S.DEPARTMENT_CODE || '/' || S.POST_RANK_CODE) "
					+ " ORDER BY S.DEPARTMENT_CODE, (S.LAST_NAME || ' ' || S.FIRST_NAME) ");
			
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				
				String outputType = rs.getString("OUTPUT_TYPE");
				
				if(outputType.equals("130") || outputType.equals("120") || outputType.equals("150"))
					outputType = "120";
				if(outputType.equals("160"))
					outputType = "160";
				String staffName = rs.getString("STAFF_NAME");
				Double weighting = rs.getDouble("WEIGHTING");
				
				
				if(objMap.get(staffName) == null) {
					objMap.put(staffName, new HashMap<String, Double>());
				}
				else {
					if (objMap.get(staffName).get(outputType) != null)
						weighting += objMap.get(staffName).get(outputType);
				}	
				objMap.get(staffName).put(outputType, weighting);
				
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objMap;
	}
	
	public List<List<String>> getAMIS001ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		List<List<String>> objList = new ArrayList<List<String>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT DISTINCT "
					+ " (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END) AS YEARSTR, "
					+ " PH.OUTPUT_NO, "
					+ " (CASE PH.SCH_CODE WHEN '3' THEN 'EDU' WHEN '4' THEN 'OTH-DIS' ELSE '' END) AS OUTPUT_AREA "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
//					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
//					+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE "
					);
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND QH.AMIS_SELECTED_IND = 'Y' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
//			if (deptList != null) {
//				sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +
//			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
//			}
			sqlBuf.append(" ORDER BY (CASE PH.SCH_CODE WHEN '3' THEN 'EDU' WHEN '4' THEN 'OTH-DIS' ELSE '' END), PH.OUTPUT_NO ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String no = rs.getString("OUTPUT_NO");
				String outputArea = rs.getString("OUTPUT_AREA");
				String year = rs.getString("YEARSTR");
				
				List<String> tmpList = new ArrayList<String>();
				tmpList.add(year);
				tmpList.add(no);
				tmpList.add(outputArea);
				
				objList.add(tmpList);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<List<Object>> getAMIS002ReportList(String dataLevel, String startDate, String endDate, List<String> deptList){
		List<List<Object>> objList = new ArrayList<List<Object>>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT "
					+ " (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END) AS YEARSTR, "
					+ " S.DEPARTMENT_CODE, "
					+ " SUM(CASE WHEN PH.SCH_CODE = '3' THEN PDV.WEIGHTING ELSE 0 END) AS EDU_WEIGHTING, "
					+ " SUM(CASE WHEN PH.SCH_CODE = '4' THEN PDV.WEIGHTING ELSE 0 END) AS OTH_WEIGHTING, "
					+ " '' AS AWARD_PROJ, "
					+ " '' AS RGC_FUND, "
					+ " '' AS TOTAL_AWARD_PROJ "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_DTL PD "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_DTL_V PDV ON PD.OUTPUT_NO = PDV.OUTPUT_NO AND PD.DATA_LEVEL = PDV.DATA_LEVEL AND PD.LINE_NO = PDV.LINE_NO "
					+ " LEFT JOIN RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ON PD.OUTPUT_NO = PH.OUTPUT_NO AND PD.DATA_LEVEL = PH.DATA_LEVEL "
					+ "	LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON PD.OUTPUT_NO = QH.OUTPUT_NO "
					+ " LEFT JOIN RICH.RH_L_OUTPUT_TYPE_V OPTYPE ON OPTYPE.LOOKUP_CODE = PH.SAP_OUTPUT_TYPE AND OPTYPE.LOOKUP_LEVEL = 2 "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.EMPLOYEE_NUMBER = PD.AUTHORSHIP_STAFF_NO "
					+ " LEFT JOIN RICH.RH_P_DEPARTMENT DEPT ON DEPT.DEPARTMENT_CODE = S.DEPARTMENT_CODE ");
			sqlBuf.append(" WHERE PD.DATA_LEVEL = '"+dataLevel+"' AND QH.CDCF_STATUS = 'CDCF_GENERATED' "
					+ " AND QH.AMIS_SELECTED_IND = 'Y' "
					+ " AND PD.LINE_NO = 1 "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			if (deptList != null) {
				sqlBuf.append(" AND S.DEPARTMENT_CODE IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append(" GROUP BY "
					+ " (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END), "
					+ " S.DEPARTMENT_CODE "
					+ " ORDER BY (CASE WHEN PH.FROM_MONTH <= 6 THEN (PH.FROM_YEAR - 1) ELSE PH.FROM_YEAR END), S.DEPARTMENT_CODE ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String year = rs.getString("YEARSTR");
				String dept = rs.getString("DEPARTMENT_CODE");
				Integer eduWeighting = rs.getInt("EDU_WEIGHTING");
				Integer othWeighting = rs.getInt("OTH_WEIGHTING");
				String awardProj = rs.getString("AWARD_PROJ");
				String rgcFund = rs.getString("RGC_FUND");
				String totalAwardProj = rs.getString("TOTAL_AWARD_PROJ");
				
				List<Object> tmpList = new ArrayList<Object>();
				tmpList.add(year);
				tmpList.add(dept);
				tmpList.add(eduWeighting);
				tmpList.add(othWeighting);
				tmpList.add(awardProj);
				tmpList.add(rgcFund);
				tmpList.add(totalAwardProj);
				
				objList.add(tmpList);
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<String> getAmisIndList(String dataLevel, String startDate, String endDate){
		List<String> objList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT DISTINCT QH.OUTPUT_NO "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH "
					+ " LEFT JOIN RICH.RH_Q_ACAD_PROF_OUTPUT_HDR QH ON QH.OUTPUT_NO = PH.OUTPUT_NO ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' AND QH.AMIS_SELECTED_IND = 'Y' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			sqlBuf.append("	ORDER BY QH.OUTPUT_NO ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objList.add(rs.getString("OUTPUT_NO"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<Integer> getOutputNoListByPeriod(String dataLevel, String startDate, String endDate){
		List<Integer> objList = new ArrayList<Integer>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT OUTPUT_NO "
					+ " FROM RICH.RH_P_ACAD_PROF_OUTPUT_HDR PH ");
			sqlBuf.append(" WHERE PH.DATA_LEVEL = '"+dataLevel+"' "
					+ " AND TO_DATE(NVL(PH.FROM_MONTH, '01') || '/' || PH.FROM_YEAR, 'MM/YYYY') BETWEEN "
					+ " TO_DATE('" + startDate + "', 'MM/YYYY') AND " + " TO_DATE('" + endDate + "', 'MM/YYYY') ");
			sqlBuf.append("	ORDER BY OUTPUT_NO ");
			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objList.add(rs.getInt("OUTPUT_NO"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<String> getOrgUnitList(List<String> deptList,String selectedDept){
		List<String> objList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT LOOKUP_CODE FROM RH_L_ORG_UNIT_V WHERE 1=1 ");
			
			if( selectedDept.equals("All")) {
				if (deptList != null) {
					sqlBuf.append("   AND (LOOKUP_CODE IN ( '" +
				    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					sqlBuf.append(SysParam.PARAM_SPECIAL_LOOKUP_STR );
				}
			}else
			{
				
				sqlBuf.append("AND LOOKUP_CODE = '" +selectedDept+"' " );
			}

			sqlBuf.append(" ORDER BY LOOKUP_CODE ");
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objList.add(rs.getString("LOOKUP_CODE"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public Map<String, String> getDeptNameMap(){
		Map<String, String> objMap = new LinkedHashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT V.LOOKUP_CODE AS DEPT_CODE, "
					+ " (CASE V.LOOKUP_CODE WHEN 'OTHERS' THEN V.DESCRIPTION ELSE "
					+ " V.DESCRIPTION||' ('||V.LOOKUP_CODE||')' END) AS DEPT_NAME "
					+ " FROM RH_L_ORG_UNIT_V V "
					+ " WHERE 1=1 AND LOOKUP_LEVEL = 2 AND ENABLED_FLAG = 'Y' ");
			sqlBuf.append(" ORDER BY V.LOOKUP_CODE ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("DEPT_CODE"), rs.getString("DEPT_NAME"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public List<String> getFacListWithoutOther(){
		List<String> objList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT LOOKUP_CODE FROM RH_L_ORG_UNIT_V WHERE 1=1 "
					+ " AND LOOKUP_LEVEL = 1 AND LOOKUP_CODE != 'OTHERS' ");
			sqlBuf.append(" ORDER BY PRINT_ORDER ");
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objList.add(rs.getString("LOOKUP_CODE"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public List<String> getResearchTypeList(){
		List<String> objList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT DESCRIPTION FROM RH_L_RESEARCH_TYPE_V ORDER BY PRINT_ORDER ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objList.add(rs.getString("DESCRIPTION"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
	
	public Map<String, String> getOutputTypeLvl1List(){
		Map<String, String> objMap = new LinkedHashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT LOOKUP_CODE, DESCRIPTION FROM RH_L_OUTPUT_TYPE_V "
					+ " WHERE LOOKUP_LEVEL = 1 "
					+ " AND ENABLED_FLAG = 'Y' "
					+ " ORDER BY PRINT_ORDER ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("DESCRIPTION"), rs.getString("LOOKUP_CODE"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, Integer> getOrgUnitNoStaffMap(Integer periodId, List<String> deptList, String selectedDept){
		Map<String, Integer> objMap = new HashMap<String, Integer>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT V.LOOKUP_CODE, COUNT(DISTINCT S.EMPLOYEE_NUMBER) AS NOSTAFF"
					+ " FROM RH_L_ORG_UNIT_V V "
					+ " RIGHT JOIN RICH.RH_S_ELIGIBLE_STAFF_LIST_V S ON S.DEPARTMENT_CODE = V.LOOKUP_CODE "
					+ " LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RP ON S.START_PERIOD = RP.CHART_DESC_2 "
					+ " WHERE 1=1 ");
			if (periodId != null && !(periodId == 1 || periodId == 10000)) {
				sqlBuf.append(" AND (RP.PERIOD_ID <=" + periodId + " OR RP.PERIOD_ID IS NULL) ");
			}
			if( selectedDept.equals("All") || selectedDept == null ) {
				if (deptList != null) {
					sqlBuf.append(" AND ( S.DEPARTMENT_CODE IN ( '" +
				    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
					sqlBuf.append(SysParam.PARAM_SPECIAL_DEPT_STR) ;
				}
			}
			else
			{
				sqlBuf.append("AND S.DEPARTMENT_CODE = '"+ selectedDept +"' ");
			}

			sqlBuf.append(" GROUP BY V.LOOKUP_CODE "
					+ " ORDER BY V.LOOKUP_CODE ");
			
			//System.out.println("sqlBuf:"+sqlBuf);
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("LOOKUP_CODE"), rs.getInt("NOSTAFF"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, String> getFacNameMap(){
		Map<String, String> objMap = new HashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT V.LOOKUP_CODE AS FAC_CODE, "
					+ " (CASE V.LOOKUP_CODE WHEN 'OTHERS' THEN V.DESCRIPTION ELSE "
					+ " V.DESCRIPTION||' ('||V.LOOKUP_CODE||')' END) AS FAC_NAME "
					+ " FROM RH_L_ORG_UNIT_V V "
					+ " WHERE 1=1 AND LOOKUP_LEVEL = 1 AND ENABLED_FLAG = 'Y' ");
			sqlBuf.append(" ORDER BY V.PRINT_ORDER ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("FAC_CODE"), rs.getString("FAC_NAME"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, String> getDeptMapByFac(String fac){
		return getDeptMapByFac(fac, null);
	}
	
	public Map<String, String> getDeptMapByFac(String fac, List<String> deptList){
		Map<String, String> objMap = new LinkedHashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT V.LOOKUP_CODE AS DEPT_CODE, "
					+ " (V.DESCRIPTION||' ('||V.LOOKUP_CODE||')') AS DEPT_NAME "
					+ " FROM RH_L_ORG_UNIT_V V "
					+ " WHERE 1=1 AND LOOKUP_LEVEL = 2 AND ENABLED_FLAG = 'Y' ");
			if (fac != null) {
				sqlBuf.append(" AND V.PARENT_LOOKUP_CODE = '" + fac + "' ");
			}
			if (deptList != null) {
				sqlBuf.append(" AND V.LOOKUP_CODE IN ( '" +
			    		deptList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ");
			}
			sqlBuf.append(" ORDER BY V.LOOKUP_CODE ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("DEPT_CODE"), rs.getString("DEPT_NAME"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public Map<String, String> getOutTypeMap(){
		Map<String, String> objMap = new LinkedHashMap<String, String>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT V.LOOKUP_CODE AS CODE, "
					+ " V.DESCRIPTION AS NAME "
					+ " FROM RH_L_OUTPUT_TYPE_V V "
					+ " WHERE 1=1 AND ENABLED_FLAG = 'Y' ");
			sqlBuf.append(" ORDER BY V.PRINT_ORDER ");
			
			pStmt = conn.prepareStatement(sqlBuf.toString());
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				objMap.put(rs.getString("CODE"), rs.getString("NAME"));
			}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objMap;
	}
	
	public void outputSnapshot(CdcfRptPeriod period, String userId) throws Exception
	{
		if (period != null)
		{
			Connection conn = null;
			CallableStatement cStmt = null;
			try{
				DateFormat df = new SimpleDateFormat("MM/yyyy", Locale.ENGLISH);
				conn = pm.getConnection();
				cStmt = conn.prepareCall("{CALL RH_OUTPUT_SNAPSHOT_P (?,?,?,?)}");
				cStmt.setInt(1, period.getPeriod_id());
				cStmt.setString(2, df.format(period.getDate_from()));
				cStmt.setString(3, df.format(period.getDate_to()));
				cStmt.setString(4, userId);
				cStmt.execute();
				
			}
			finally{
				PersistenceManager.close(cStmt);
				PersistenceManager.close(conn);
			}
		}
	}
	
	public void updateAmisInd(List<Integer> importList, List<Integer> outputInPeriod) throws Exception
	{
		if (outputInPeriod != null && !outputInPeriod.isEmpty())
		{
			Connection conn = null;
			CallableStatement cStmt = null;
			try{
				int separate = 500;
				conn = pm.getConnection();
				for(int l = 0 ; l < 2 ; l ++) {
					List<Integer> list = null;
					String AmisInd = null;
					if(l == 0) {
						list = outputInPeriod;
						AmisInd = "N";
					}
					else if(l == 1) {
						list = importList;
						AmisInd = "Y";
					}
					List<List<Integer>> idListPatch = new ArrayList<List<Integer>>();
					if(list.size() > separate) {
						for(int i = 0 ; true ; ++i) {
							if(separate*i+separate > list.size()) {
								idListPatch.add(list.subList(separate*i, list.size()));
								break;
							}
							else
								idListPatch.add(list.subList(separate*i, separate*i+separate));
						}
					}
					else idListPatch.add(list);
					
					for(List<Integer> idList : idListPatch) {
						String importStr = "";
						for(Integer val : idList) {
							importStr += val + ",";
						}
						if(!importStr.isEmpty())
							importStr.substring(0, importStr.length()-1);
						
						cStmt = conn.prepareCall("{CALL RH_AMIS_IND_UPDATE_P (?,?)}");
						cStmt.setString(1, importStr);
						cStmt.setString(2, AmisInd);
						cStmt.execute();
					}
				}
				
			}
			finally{
				PersistenceManager.close(cStmt);
				PersistenceManager.close(conn);
			}
		}
	}
	
	
	public List<OutputType> getOutputTypeListByLevel(Integer level,String flag)
	{
		List<OutputType> objList = null;
		EntityManager em = null;
		
		try 
		{
			String query = "SELECT obj FROM OutputType obj " +
						" WHERE obj.pk.lookup_level = :level AND obj.enabled_flag = :flag "+
					   " ORDER BY obj.print_order ";
			
			em = getEntityManager();
			
			TypedQuery<OutputType> q = em.createQuery(query, OutputType.class);
			q.setParameter("flag", flag);
			q.setParameter("level", level);
			//q.setHint("org.hibernate.cacheable", true);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
}
