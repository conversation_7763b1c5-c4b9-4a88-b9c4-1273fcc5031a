package hk.eduhk.rich.entity.publication;

import java.io.Serializable;
import java.util.logging.Logger;

import javax.persistence.*;


@Embeddable
public class FundingSource_PK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "lookup_level")
	private Integer lookup_level;	
	
	@Column(name = "lookup_code")
	private String lookup_code;

	
	public Integer getLookup_level()
	{
		return lookup_level;
	}

	
	public void setLookup_level(Integer lookup_level)
	{
		this.lookup_level = lookup_level;
	}

	
	public String getLookup_code()
	{
		return lookup_code;
	}

	
	public void setLookup_code(String lookup_code)
	{
		this.lookup_code = lookup_code;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((lookup_code == null) ? 0 : lookup_code.hashCode());
		result = prime * result + ((lookup_level == null) ? 0 : lookup_level.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FundingSource_PK other = (FundingSource_PK) obj;
		if (lookup_code == null)
		{
			if (other.lookup_code != null)
				return false;
		}
		else if (!lookup_code.equals(other.lookup_code))
			return false;
		if (lookup_level == null)
		{
			if (other.lookup_level != null)
				return false;
		}
		else if (!lookup_level.equals(other.lookup_level))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "FundingSource_PK [lookup_level=" + lookup_level + ", lookup_code=" + lookup_code + "]";
	}


}
