package hk.eduhk.rich.entity.publication;

import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.entity.EduSector_PK;


@Entity
@Table(name = "RH_L_PUBLICATION_DISCIPLINE_V")
public class PubDiscipline
{
	public static Logger logger = Logger.getLogger(PubDiscipline.class.toString());
	
	@EmbeddedId
	private PubDiscipline_PK pk = new PubDiscipline_PK();

	@Column(name = "description")
	private String description;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;	
	
	@Column(name = "print_order")
	private Integer print_order;
	
	@Column(name = "parent_lookup_code")
	private String parent_lookup_code;

	
	public PubDiscipline_PK getPk()
	{
		return pk;
	}

	
	public void setPk(PubDiscipline_PK pk)
	{
		this.pk = pk;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}

	
	public String getParent_lookup_code()
	{
		return parent_lookup_code;
	}

	
	public void setParent_lookup_code(String parent_lookup_code)
	{
		this.parent_lookup_code = parent_lookup_code;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PubDiscipline other = (PubDiscipline) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "PubDiscipline [pk=" + pk + ", description=" + description + ", enabled_flag=" + enabled_flag
				+ ", print_order=" + print_order + ", parent_lookup_code=" + parent_lookup_code + "]";
	}

	


}
