package hk.eduhk.rich.entity.email;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;

import hk.eduhk.rich.email.tmpl.TemplateConverter;

/**
 * EmailTemplate does not extends UserPeristenceObject
 * as the control fields in MESSAGING schema have different name
 *
 */
@Entity
@Table(name = "MESSAGING.MG_EMAIL_TML")
@Cacheable
@SuppressWarnings("serial")
public class EmailTemplate implements Serializable
{
	@EmbeddedId
	private EmailTemplatePK pk;
	
	@Column(name = "grp_code", length = 30)
	private String groupCode;
	
	@Column(name = "name", length = 100)
	private String name;
	
	@Column(name = "description", length = 500)
	private String description;
	
	@Column(name = "from_addr")
	private String fromAddr;
	
	@Column(name = "cc_addr")
	private String ccAddr;

	@Column(name = "subject", length = 78)
	private String subject;
	
	@Column(name = "content")
	private String content;
	
	@Column(name = "created_by", length = 32)
	private String creator;

	@Column(name = "userstamp", length = 32)
	private String userstamp;
	
	@Column(name = "creation_date")
	private Date creationDate;

	@Column(name = "timestamp")
	@Version
	private Date timestamp;	
	
	@JsonIgnore
	public EmailTemplatePK getPk()
	{
		if (pk == null) pk = new EmailTemplatePK();
		return pk;
	}


	public void setPk(EmailTemplatePK pk)
	{
		this.pk = pk;
	}


	public String getAppCode()
	{
		return getPk().getAppCode();
	}

	
	public void setAppCode(String appCode)
	{
		getPk().setAppCode(appCode);
	}

	
	public String getTemplateCode()
	{
		return StringUtils.trim(getPk().getTemplateCode());
	}

	
	public void setTemplateCode(String templateCode)
	{
		getPk().setTemplateCode(StringUtils.trim(templateCode));
	}

	
	public String getGroupCode()
	{
		return groupCode;
	}


	public void setGroupCode(String groupCode)
	{
		this.groupCode = groupCode;
	}


	public String getName()
	{
		return name;
	}


	public void setName(String name)
	{
		this.name = name;
	}


	public String getDescription()
	{
		return description;
	}


	public void setDescription(String description)
	{
		this.description = description;
	}


	public String getFromAddr()
	{
		return fromAddr;
	}


	public void setFromAddr(String fromAddr)
	{
		this.fromAddr = fromAddr;
	}


	public String getCcAddr()
	{
		return ccAddr;
	}


	public void setCcAddr(String ccAddr)
	{
		this.ccAddr = ccAddr;
	}


	public String getSubject()
	{
		return subject;
	}


	public void setSubject(String subject)
	{
		this.subject = subject;
	}
	
	public String getContent()
	{
		return content;
	}


	public void setContent(String content)
	{
		//this.content = StringEscapeUtils.unescapeHtml4(Constant.HTML_POLICY_EMAIL_TML.sanitize(content));
		this.content = content;
	}

		
	public String getCreator()
	{
		return creator;
	}

	
	public void setCreator(String creator)
	{
		this.creator = creator;
	}

	
	public String getUserstamp()
	{
		return userstamp;
	}

	
	public void setUserstamp(String userstamp)
	{
		this.userstamp = userstamp;
	}

	
	public Date getCreationDate()
	{
		return creationDate;
	}

	
	public void setCreationDate(Date creationDate)
	{
		this.creationDate = creationDate;
	}

	
	public Date getTimestamp()
	{
		return timestamp;
	}

	
	public void setTimestamp(Date timestamp)
	{
		this.timestamp = timestamp;
	}
	
	public List<Map<String, Object>> convertParamList(Object... objs)
	{	
		TemplateConverter converter = TemplateConverter.getTemplateConverter();
		
		return (converter != null) ? converter.convertParamList(objs) : Collections.emptyList();
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmailTemplate other = (EmailTemplate) obj;
		if (pk == null) {
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "EmailTemplate [pk=" + pk + ", groupCode=" + groupCode
				+ ", name=" + name + ", description=" + description
				+ ", fromAddr=" + fromAddr + ", ccAddr=" + ccAddr + ", subject="
				+ subject + ", content=" + content + "]";
	}
	
}
