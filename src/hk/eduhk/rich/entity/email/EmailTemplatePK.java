package hk.eduhk.rich.entity.email;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import org.apache.commons.beanutils.BeanMap;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


@Embeddable
public class EmailTemplatePK implements Serializable
{
	
	private static final long serialVersionUID = 1L;
	
	private static Logger logger = Logger.getLogger(EmailTemplatePK.class.getName());
	

	@Column(name = "app_code", length = 20)
	private String appCode;

	@Column(name = "tml_code", length = 50)
	private String templateCode;


	public EmailTemplatePK()
	{
	}
	
	
	public EmailTemplatePK(String json)
	{
		try 
		{
			TypeReference<HashMap<String,Object>> typeRef = new TypeReference<HashMap<String,Object>>() {};
			ObjectMapper objMapper = new ObjectMapper();
			Map<String,Object> objMap = objMapper.readValue(json, typeRef);
			
			setAppCode((String) objMap.get("appCode"));
			setTemplateCode((String) objMap.get("templateCode"));
		}
		catch (Exception e) 
		{
			logger.log(Level.WARNING, "Cannot parse JSON", e);
		}

	}


	public String getAppCode()
	{
		return appCode;
	}


	public void setAppCode(String appCode)
	{
		this.appCode = appCode;
	}


	public String getTemplateCode()
	{
		return templateCode;
	}


	public void setTemplateCode(String templateCode)
	{
		this.templateCode = templateCode;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmailTemplatePK other = (EmailTemplatePK) obj;
		if (appCode == null) {
			if (other.appCode != null)
				return false;
		}
		else if (!appCode.equals(other.appCode))
			return false;
		if (templateCode == null) {
			if (other.templateCode != null)
				return false;
		}
		else if (!templateCode.equals(other.templateCode))
			return false;
		return true;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((appCode == null) ? 0 : appCode.hashCode());
		result = prime * result
				+ ((templateCode == null) ? 0 : templateCode.hashCode());
		return result;
	}


	@Override
	public String toString()
	{
		String json = null;
		
		try 
		{
			ObjectMapper objMapper = new ObjectMapper();
			BeanMap beanMap = new BeanMap(this);
			json = objMapper.writeValueAsString(beanMap);
		}
		catch (JsonProcessingException e) 
		{
			logger.log(Level.WARNING, "Cannot convert to JSON", e);
		}
				
		return json;
	}
	
	
}
