package hk.eduhk.rich.entity.email;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;

import javax.enterprise.inject.spi.CDI;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.BaseDAO;


public class EmailDAO extends BaseDAO
{
		
	
	private static final Level LVL_QUERY = Level.FINEST;
	
	public static synchronized EmailDAO getInstance()
	{
		return CDI.current().select(EmailDAO.class).get();
	}
	
	public List<EmailTemplate> getEmailTemplateListByAppCode(String appCode)
	{
		List<EmailTemplate> objList = null;

		if (StringUtils.isNotBlank(appCode))
		{
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT DISTINCT obj FROM EmailTemplate obj " +
					   "WHERE obj.pk.appCode = :appCode " +
					   "ORDER BY obj.pk.templateCode ");
			
			logger.log(LVL_QUERY, "query=" + buf);
			
			// Query execution
			TypedQuery<EmailTemplate> q = em.createQuery(buf.toString(), EmailTemplate.class);
			q.setParameter("appCode", appCode);
			objList = q.getResultList();
		}
		
		return getSafeList(objList);
	}

	
	public List<EmailTemplate> getEmailTemplateListByTemplateCodeCol(String appCode, Collection<String> templateCodeCol)
	{
		
		List<EmailTemplate> objList = null;
		
		if (StringUtils.isNotBlank(appCode) && CollectionUtils.isNotEmpty(templateCodeCol))
		{
			// Query construction
			
			StringBuilder buf = new StringBuilder();
			
			buf.append("SELECT DISTINCT obj FROM EmailTemplate obj " +
					   "WHERE obj.pk.appCode = :appCode " +
					   "AND obj.pk.templateCode IN :templateCodeCol " +
					   "ORDER BY obj.pk.templateCode");
			
			logger.log(LVL_QUERY, "query=" + buf);
			
			// Query execution
			TypedQuery<EmailTemplate> q = em.createQuery(buf.toString(), EmailTemplate.class);
			q.setParameter("appCode", appCode);
			q.setParameter("templateCodeCol", templateCodeCol);
			objList = q.getResultList();
		}
		
		return getSafeList(objList);
	}
	
	
	public EmailTemplate getEmailTemplateByTemplateCode(String appCode, String templateCode)
	{
		EmailTemplate obj = null;	
		
		if (StringUtils.isNotBlank(appCode) && StringUtils.isNotBlank(templateCode))
		{
			List<String> templateCodeList = new ArrayList<String>(1);
			templateCodeList.add(templateCode);
			
			// Get the 1st element if the list is not empty
			List<EmailTemplate> templateList = getEmailTemplateListByTemplateCodeCol(appCode, templateCodeList);
			if (CollectionUtils.isNotEmpty(templateList)) obj = templateList.get(0);
		}
		
		return obj;
	}
	
	
	public List<EmailTemplate> getEmailTemplateListByGroupCode(String appCode, String groupCode)
	{
		List<EmailTemplate> objList = null;
		
		if (StringUtils.isNotBlank(appCode) && StringUtils.isNotBlank(groupCode))
		{
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT obj FROM EmailTemplate obj " +
					   "WHERE obj.pk.appCode = :appCode " +
					   "AND obj.groupCode = :groupCode ");
			
			// Query execution
			TypedQuery<EmailTemplate> q = em.createQuery(buf.toString(), EmailTemplate.class);
			q.setParameter("appCode", appCode);
			q.setParameter("groupCode", groupCode);
			objList = q.getResultList();
		}
		
		return getSafeList(objList);
	}
	

//	@TransactionAttribute(TransactionAttributeType.REQUIRED)
//	public EmailLog createEmailLog(EmailLog obj)
//	{
//		if (obj != null) em.persist(obj);
//		return obj;
//	}
}
