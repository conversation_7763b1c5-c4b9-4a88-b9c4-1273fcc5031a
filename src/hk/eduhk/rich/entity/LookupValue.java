package hk.eduhk.rich.entity;

import java.util.logging.Logger;

import javax.persistence.*;

import com.google.common.base.Strings;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;
import hk.eduhk.rich.entity.publication.DisciplinaryArea;
import hk.eduhk.rich.entity.publication.OutputDetails_Q_PK;


@Entity
@Table(name = "RH_Z_LOOKUP_VALUES")
public class LookupValue extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(LookupValue.class.toString());
	
	@EmbeddedId
	private LookupValue_PK pk = new LookupValue_PK();

	@Column(name = "short_desc")
	private String short_desc;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;	
	
	@Column(name = "print_order")
	private Integer print_order;
	
	@Column(name = "parent_lookup_code")
	private String parent_lookup_code;
	
	@Transient
	public SecFuncLock exKt;
	
	public LookupValue_PK getPk()
	{
		return pk;
	}

	
	public void setPk(LookupValue_PK pk)
	{
		this.pk = pk;
	}

	
	public String getShort_desc()
	{
		return short_desc;
	}


	
	public void setShort_desc(String short_desc)
	{
		this.short_desc = short_desc;
	}


	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}

	
	public String getParent_lookup_code()
	{
		return parent_lookup_code;
	}

	
	public void setParent_lookup_code(String parent_lookup_code)
	{
		this.parent_lookup_code = parent_lookup_code;
	}

	public SecFuncLock getExKt()
	{
		if (!Strings.isNullOrEmpty(getPk().getLookup_code())) {
			AccessDAO dao = AccessDAO.getInstance();
			exKt = dao.getSecFuncLock("EXCLUSIVE_KT", "MANAGE_KT_ACT", getPk().getLookup_code());
		}
		return exKt;
	}


	
	public void setExKt(SecFuncLock exKt)
	{
		this.exKt = exKt;
	}
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupValue other = (LookupValue) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupValue [pk=" + pk + ", short_desc=" + short_desc + ", description=" + description
				+ ", enabled_flag=" + enabled_flag + ", print_order=" + print_order + ", parent_lookup_code="
				+ parent_lookup_code + "]";
	}

}
