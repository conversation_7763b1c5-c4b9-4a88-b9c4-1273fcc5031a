package hk.eduhk.rich.entity;

import java.sql.Timestamp;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.persistence.Column;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.google.common.base.Strings;


@SuppressWarnings("serial")
public class SnapShotLog 
{
	private Integer log_id;
	private Integer period_id; 
	private String period_desc; 
	private String s_type;
	private String userstamp;
	private Date timestamp;
	
	public Integer getLog_id()
	{
		return log_id;
	}
	
	public void setLog_id(Integer log_id)
	{
		this.log_id = log_id;
	}
	
	public Integer getPeriod_id()
	{
		return period_id;
	}
	
	public void setPeriod_id(Integer period_id)
	{
		this.period_id = period_id;
	}
	
	public String getPeriod_desc()
	{
		return period_desc;
	}
	
	public void setPeriod_desc(String period_desc)
	{
		this.period_desc = period_desc;
	}
	
	public String getS_type()
	{
		return s_type;
	}
	
	public void setS_type(String s_type)
	{
		this.s_type = s_type;
	}
	
	public String getUserstamp()
	{
		return userstamp;
	}
	
	public void setUserstamp(String userstamp)
	{
		this.userstamp = userstamp;
	}
	
	public Date getTimestamp()
	{
		return timestamp;
	}
	
	public void setTimestamp(Date timestamp)
	{
		this.timestamp = timestamp;
	}

	@Override
	public String toString()
	{
		return "SnapShotLog [log_id=" + log_id + ", period_id=" + period_id + ", period_desc=" + period_desc
				+ ", s_type=" + s_type + ", userstamp=" + userstamp + ", timestamp=" + timestamp + "]";
	}

}