package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class deptFundingIntExt
{

	private String 	dept_code;
	private String 	fund_type;
	private String 	count;
	private String 	amount;


	public String getDept_code()
	{
		return dept_code;
	}


	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}


	
	public String getFund_type()
	{	
		//System.out.println("GeT FUND "+fund_type);
		return fund_type;
	}


	
	public void setFund_type(String fund_type)
	{
		this.fund_type = fund_type;
	}


	
	public String getCount()
	{
		return count;
	}


	
	public void setCount(String count)
	{
		this.count = count;
	}


	
	public String getAmount()
	{
		return amount;
	}


	
	public void setAmount(String amount)
	{
		this.amount = amount;
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("fund_type") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFund_type())) + "\"," +
								 "\"" + JSONObject.escape("count") + "\":" + "\"" + JSONObject.escape(String.valueOf(getCount())) + "\"," +
								 "\"" + JSONObject.escape("amount") + "\":" + "\"" + JSONObject.escape(String.valueOf(getAmount())) + "\"");
		strBuf.append("}");

		return strBuf.toString();
	}

	
	@Override
	public String toString()
	{
		return "deptFundingIntExt [dept_code=" + dept_code + ", fund_type=" + fund_type + ", count=" + count
				+ ", amount=" + amount + "]";
	}


}
