package hk.eduhk.rich.entity.report;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

public class a4ReportField
{
	public static Logger logger = Logger.getLogger(a4ReportField.class.toString());
	
	
	//Personal details
	private String 	dept_code;
	private String 	last_name;
	private String 	first_name;
	private String 	post_rank_code;
	private String 	new_project;
	private String 	employee_number;
	
	//Funding sources
	private List <a4ReportCountAmt> fund_source;

	
	public String getDept_code()
	{
		return dept_code;
	}
	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}
	
	public String getLast_name()
	{
		return last_name;
	}
	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}
	
	public String getFirst_name()
	{
		return first_name;
	}
	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}
	
	public String getPost_rank_code()
	{
		return post_rank_code;
	}
	
	public void setPost_rank_code(String post_rank_code)
	{
		this.post_rank_code = post_rank_code;
	}
	
	public String getNew_project()
	{
		return new_project;
	}
	
	public void setNew_project(String new_project)
	{
		this.new_project = new_project;
	}
	
	public String getEmployee_number()
	{
		return employee_number;
	}
	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}
	
	
	
	public List<a4ReportCountAmt> getFund_source()
	{
		return fund_source;
	}

	
	public void setFund_source(List<a4ReportCountAmt> fund_source)
	{
		this.fund_source = fund_source;
	}

	@Override
	public String toString()
	{
		return "a4ReportField [dept_code=" + dept_code + ", last_name=" + last_name + ", first_name=" + first_name
				+ ", post_rank_code=" + post_rank_code + ", new_project=" + new_project + ", employee_number="
				+ employee_number + ", fund_source=" + fund_source + "]";
	}

}
