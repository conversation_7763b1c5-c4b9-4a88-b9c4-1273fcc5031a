package hk.eduhk.rich.entity.report;

import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_Z_KT_RPT_SUM")
public class KtRptSum extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(KtRptSum.class.toString());
	
	@Id
	@SequenceGenerator(name = "ktSumSeq", sequenceName = "RH_Z_KT_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ktSumSeq")
	@Column(name = "sum_id")
	private Integer sum_id;
	
	@Column(name = "form_code")
	private String form_code;
	
	@Column(name = "sum_key")
	private String sum_key;
	
	@Column(name = "sum_desc")
	private String sum_desc;
	
	@Column(name = "print_order")
	private Integer print_order;

	@Transient
	private Double sum_value = 0.0;
	
	@Transient
	private String sum_type;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "form_code", referencedColumnName = "form_code", nullable = false, insertable = false, updatable = false)
	private KtForm form;
	
	public Integer getSum_id()
	{
		return sum_id;
	}

	
	public void setSum_id(Integer sum_id)
	{
		this.sum_id = sum_id;
	}

	
	public String getForm_code()
	{
		return form_code;
	}

	
	public void setForm_code(String form_code)
	{
		this.form_code = form_code;
	}

	
	
	public String getSum_key()
	{
		return sum_key;
	}


	
	public void setSum_key(String sum_key)
	{
		this.sum_key = sum_key;
	}


	public String getSum_desc()
	{
		return sum_desc;
	}

	
	public void setSum_desc(String sum_desc)
	{
		this.sum_desc = sum_desc;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}


	
	public Double getSum_value()
	{
		return sum_value;
	}


	
	public void setSum_value(Double sum_value)
	{
		this.sum_value = sum_value;
	}


	public String getSum_type()
	{
		if (getSum_key() != null && sum_type == null) {
			if (sum_key.startsWith("num")) {
				sum_type = "num";
			}else if (sum_key.startsWith("income")) {
				sum_type = "dollar";
			}else {
				sum_type = "num";
			}
		}
		return sum_type;
	}


	
	public void setSum_type(String sum_type)
	{
		this.sum_type = sum_type;
	}


	public KtForm getForm()
	{
		if (form != null)
		{
			try
			{
				form.getForm_code();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					FormDAO dao = FormDAO.getInstance();
					form = dao.getKtForm(getForm_code());
				}
				else
				{
					throw re;
				}
			}
		}
		return form;
	}


	
	public void setForm(KtForm form)
	{
		this.form = form;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((sum_id == null) ? 0 : sum_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KtRptSum other = (KtRptSum) obj;
		if (sum_id == null)
		{
			if (other.sum_id != null)
				return false;
		}
		else if (!sum_id.equals(other.sum_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "KtRptSum [sum_id=" + sum_id + ", form_code=" + form_code + ", sum_key=" + sum_key + ", sum_desc="
				+ sum_desc + ", print_order=" + print_order + ", sum_value=" + sum_value + "]";
	}



	
}
