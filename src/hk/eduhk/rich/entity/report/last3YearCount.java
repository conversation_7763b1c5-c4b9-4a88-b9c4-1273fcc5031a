package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class last3YearCount
{
	private String 	dept_code;
	private String 	count_staff;
	
	public String getDept_code()
	{
		return dept_code;
	}

	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}
	

	
	public String getCount_staff()
	{
		return count_staff;
	}
	
	public void setCount_staff(String count_staff)
	{
		this.count_staff = count_staff;
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("count_staff") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_staff())) + "\"");

		strBuf.append("}");

		return strBuf.toString();
	}

	@Override
	public String toString()
	{
		return "last3YearCount [dept_code=" + dept_code + ", count_staff=" + count_staff + "]";
	}



	
	
}
