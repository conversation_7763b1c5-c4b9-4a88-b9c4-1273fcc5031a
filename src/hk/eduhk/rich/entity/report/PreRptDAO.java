package hk.eduhk.rich.entity.report;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.staff.StaffRank;


@SuppressWarnings("serial")
public class PreRptDAO extends BaseDAO
{

	private static PreRptDAO instance = null;


	public static synchronized PreRptDAO getInstance()
	{
		if (instance == null) instance = new PreRptDAO();
		return instance;
	}
	
	
	public static PreRptDAO getCacheInstance()
	{
		return PreRptDAO.getInstance();
	}	
	
	public List<PreRpt> getPreRptList(String enabled_flag)
	{
		List<PreRpt> objList = null;
		EntityManager em = null;		
		String where = "";
		try
		{
			if (enabled_flag != null) {
				where = " WHERE obj.enabled_flag = :enabled_flag ";
			}
			em = getEntityManager();		
			String query = "SELECT obj FROM PreRpt obj "+where+" ORDER BY obj.print_order, obj.rpt_code";		
			TypedQuery<PreRpt> q = em.createQuery(query, PreRpt.class);
			if (enabled_flag != null) {
				q.setParameter("enabled_flag", enabled_flag);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<PreRptCat> getPreRptCatList(String enabled_flag, String is_rdo)
	{
		List<PreRptCat> objList = null;
		EntityManager em = null;		
		String where = " WHERE obj.is_rdo <= :is_rdo ";
		try
		{
			if (enabled_flag != null) {
				where += " AND obj.enabled_flag = :enabled_flag ";
			}
			em = getEntityManager();		
			String query = "SELECT obj FROM PreRptCat obj "+where+"  ORDER BY obj.print_order";			
			TypedQuery<PreRptCat> q = em.createQuery(query, PreRptCat.class);
			q.setParameter("is_rdo", is_rdo);
			if (enabled_flag != null) {
				q.setParameter("enabled_flag", enabled_flag);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public PreRptCat getPreRptCat(String cat)
	{
		List<PreRptCat> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PreRptCat obj WHERE obj.rpt_cat = :rpt_cat ORDER BY obj.print_order";			
			TypedQuery<PreRptCat> q = em.createQuery(query, PreRptCat.class);
			q.setParameter("rpt_cat", cat);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}
	
	public PreRpt getPreRpt(String rpt_code)
	{
		List<PreRpt> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM PreRpt obj WHERE obj.rpt_code = :rpt_code ORDER BY obj.print_order";			
			TypedQuery<PreRpt> q = em.createQuery(query, PreRpt.class);
			q.setParameter("rpt_code", rpt_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}

	public PreRpt getPreRptById(Integer id)
	{
		PreRpt obj = null;
		
		if (id != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(PreRpt.class, id);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public PreRpt updatePreRpt(PreRpt obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public PreRptCat updatePreRptCat(PreRptCat obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deletePreRpt(Integer id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			PreRpt obj = em.find(PreRpt.class, id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public void deletePreRptCat(String id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			PreRptCat obj = em.find(PreRptCat.class, id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
}