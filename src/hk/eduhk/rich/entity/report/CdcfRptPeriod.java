package hk.eduhk.rich.entity.report;

import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_Z_CDCF_RPT_PERIOD")
public class CdcfRptPeriod extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(CdcfRptPeriod.class.toString());
	
	@Id
	@SequenceGenerator(name = "cdcfSeq", sequenceName = "RH_Z_CDCF_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cdcfSeq")
	@Column(name = "period_id")
	private Integer period_id;
	
	@Column(name = "date_from")
	private Date date_from;
	
	@Column(name = "date_to")
	private Date date_to;
	
	@Column(name = "period_desc")
	private String period_desc;
	
	@Column(name = "chart_desc")
	private String chart_desc;
	
	@Column(name = "chart_desc_2")
	private String chart_desc_2;
	
	@Column(name = "remarks")
	private String remarks;
	
	@Column(name = "is_current")
	private Boolean is_current = false;

	
	public Integer getPeriod_id()
	{
		return period_id;
	}

	
	public void setPeriod_id(Integer period_id)
	{
		this.period_id = period_id;
	}

	public Date getDate_from()
	{
		return date_from;
	}


	
	public void setDate_from(Date date_from)
	{
		this.date_from = date_from;
	}


	
	public Date getDate_to()
	{
		return date_to;
	}


	
	public void setDate_to(Date date_to)
	{
		this.date_to = date_to;
	}


	
	public String getPeriod_desc()
	{
		return period_desc;
	}


	
	public void setPeriod_desc(String period_desc)
	{
		this.period_desc = period_desc;
	}


	
	public String getRemarks()
	{
		return remarks;
	}


	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	
	public Boolean getIs_current()
	{
		return is_current;
	}


	
	public void setIs_current(Boolean is_current)
	{
		this.is_current = is_current;
	}


	
	public String getChart_desc()
	{
		return chart_desc;
	}


	
	public void setChart_desc(String chart_desc)
	{
		this.chart_desc = chart_desc;
	}

	public String getChart_desc_2()
	{
		return chart_desc_2;
	}


	
	public void setChart_desc_2(String chart_desc_2)
	{
		this.chart_desc_2 = chart_desc_2;
	}

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((period_id == null) ? 0 : period_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CdcfRptPeriod other = (CdcfRptPeriod) obj;
		if (period_id == null)
		{
			if (other.period_id != null)
				return false;
		}
		else if (!period_id.equals(other.period_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "CdcfRptPeriod [period_id=" + period_id + ", date_from=" + date_from + ", date_to=" + date_to
				+ ", period_desc=" + period_desc + ", chart_desc=" + chart_desc + ", chart_desc_2=" + chart_desc_2
				+ ", remarks=" + remarks + ", is_current=" + is_current + "]";
	}


}
