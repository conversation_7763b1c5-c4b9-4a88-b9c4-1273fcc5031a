package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class StaffProjCount
{
	private String 	dept_code;
	private String 	last_name;
	private String 	first_name;
	private String 	post_rank_code;
	private String 	employee_number;
	private String 	ac;
	private String 	am;
	private String 	bc;
	private String 	bm;
	private String 	cc;
	private String 	cm;
	private String 	dc;
	private String 	dm;
	private String 	ec;
	private String 	em;
	private String 	fc;
	private String 	fm;
	private String 	gc;
	private String 	gm;
	private String 	hc;
	private String 	hm;
	private String 	ic;
	private String 	im;
	private String 	jc;
	private String 	jm;
	private String 	kc;
	private String 	km;
	private String 	lc;
	private String 	lm;
	private String 	mc;
	private String 	mm;
	private String 	nc;
	private String 	nm;	
	private String 	oc;
	private String 	om;
	private String 	pc;
	private String 	pm;	
	private String 	qc;
	private String 	qm;
	private String 	rc;
	private String 	rm;
	private String 	new_proj;

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}


	
	
	
	public String getEmployee_number()
	{
		return employee_number;
	}


	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}


	public String getLast_name()
	{
		return last_name;
	}



	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}



	
	public String getFirst_name()
	{
		return first_name;
	}



	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}



	
	public String getPost_rank_code()
	{
		return post_rank_code;
	}



	
	public void setPost_rank_code(String post_rank_code)
	{
		this.post_rank_code = post_rank_code;
	}

	
	public String getAc()
	{
		return ac;
	}


	
	public void setAc(String ac)
	{
		this.ac = ac;
	}


	
	public String getAm()
	{
		return am;
	}


	
	public void setAm(String am)
	{
		this.am = am;
	}


	
	public String getBc()
	{
		return bc;
	}


	
	public void setBc(String bc)
	{
		this.bc = bc;
	}


	
	public String getBm()
	{
		return bm;
	}


	
	public void setBm(String bm)
	{
		this.bm = bm;
	}


	
	public String getCc()
	{
		return cc;
	}


	
	public void setCc(String cc)
	{
		this.cc = cc;
	}


	
	public String getCm()
	{
		return cm;
	}


	
	public void setCm(String cm)
	{
		this.cm = cm;
	}


	
	public String getDc()
	{
		return dc;
	}


	
	public void setDc(String dc)
	{
		this.dc = dc;
	}


	
	public String getDm()
	{
		return dm;
	}


	
	public void setDm(String dm)
	{
		this.dm = dm;
	}


	
	public String getEc()
	{
		return ec;
	}


	
	public void setEc(String ec)
	{
		this.ec = ec;
	}


	
	public String getEm()
	{
		return em;
	}


	
	public void setEm(String em)
	{
		this.em = em;
	}


	
	public String getFc()
	{
		return fc;
	}


	
	public void setFc(String fc)
	{
		this.fc = fc;
	}


	
	public String getFm()
	{
		return fm;
	}


	
	public void setFm(String fm)
	{
		this.fm = fm;
	}


	
	public String getGc()
	{
		return gc;
	}


	
	public void setGc(String gc)
	{
		this.gc = gc;
	}


	
	public String getGm()
	{
		return gm;
	}


	
	public void setGm(String gm)
	{
		this.gm = gm;
	}


	
	public String getHc()
	{
		return hc;
	}


	
	public void setHc(String hc)
	{
		this.hc = hc;
	}


	
	public String getHm()
	{
		return hm;
	}


	
	public void setHm(String hm)
	{
		this.hm = hm;
	}


	
	public String getIc()
	{
		return ic;
	}


	
	public void setIc(String ic)
	{
		this.ic = ic;
	}


	
	public String getIm()
	{
		return im;
	}


	
	public void setIm(String im)
	{
		this.im = im;
	}


	
	public String getJc()
	{
		return jc;
	}


	
	public void setJc(String jc)
	{
		this.jc = jc;
	}


	
	public String getJm()
	{
		return jm;
	}


	
	public void setJm(String jm)
	{
		this.jm = jm;
	}


	
	public String getKc()
	{
		return kc;
	}


	
	public void setKc(String kc)
	{
		this.kc = kc;
	}


	
	public String getKm()
	{
		return km;
	}


	
	public void setKm(String km)
	{
		this.km = km;
	}


	
	public String getLc()
	{
		return lc;
	}


	
	public void setLc(String lc)
	{
		this.lc = lc;
	}


	
	public String getLm()
	{
		return lm;
	}


	
	public void setLm(String lm)
	{
		this.lm = lm;
	}


	
	public String getMc()
	{
		return mc;
	}


	
	public void setMc(String mc)
	{
		this.mc = mc;
	}


	
	public String getMm()
	{
		return mm;
	}


	
	public void setMm(String mm)
	{
		this.mm = mm;
	}


	
	public String getNc()
	{
		return nc;
	}


	
	public void setNc(String nc)
	{
		this.nc = nc;
	}


	
	public String getNm()
	{
		return nm;
	}


	
	public void setNm(String nm)
	{
		this.nm = nm;
	}


	
	public String getOc()
	{
		return oc;
	}


	
	public void setOc(String oc)
	{
		this.oc = oc;
	}


	
	public String getOm()
	{
		return om;
	}


	
	public void setOm(String om)
	{
		this.om = om;
	}


	
	public String getPc()
	{
		return pc;
	}


	
	public void setPc(String pc)
	{
		this.pc = pc;
	}


	
	public String getPm()
	{
		return pm;
	}


	
	public void setPm(String pm)
	{
		this.pm = pm;
	}


	
	public String getQc()
	{
		return qc;
	}


	
	public void setQc(String qc)
	{
		this.qc = qc;
	}


	
	public String getQm()
	{
		return qm;
	}


	
	public void setQm(String qm)
	{
		this.qm = qm;
	}


	
	public String getRc()
	{
		return rc;
	}


	
	public void setRc(String rc)
	{
		this.rc = rc;
	}


	
	public String getRm()
	{
		return rm;
	}


	
	public void setRm(String rm)
	{
		this.rm = rm;
	}
	
	public String getNew_proj()
	{
		return new_proj;
	}
	
	public void setNew_proj(String new_proj)
	{
		this.new_proj = new_proj;
	}


	@Override
	public String toString()
	{
		return "StaffProjCount [dept_code=" + dept_code + ", last_name=" + last_name + ", first_name=" + first_name
				+ ", post_rank_code=" + post_rank_code + ", employee_number=" + employee_number + ", ac=" + ac + ", am="
				+ am + ", bc=" + bc + ", bm=" + bm + ", cc=" + cc + ", cm=" + cm + ", dc=" + dc + ", dm=" + dm + ", ec="
				+ ec + ", em=" + em + ", fc=" + fc + ", fm=" + fm + ", gc=" + gc + ", gm=" + gm + ", hc=" + hc + ", hm="
				+ hm + ", ic=" + ic + ", im=" + im + ", jc=" + jc + ", jm=" + jm + ", kc=" + kc + ", km=" + km + ", lc="
				+ lc + ", lm=" + lm + ", mc=" + mc + ", mm=" + mm + ", nc=" + nc + ", nm=" + nm + ", oc=" + oc + ", om="
				+ om + ", pc=" + pc + ", pm=" + pm + ", qc=" + qc + ", qm=" + qm + ", rc=" + rc + ", rm=" + rm
				+ ", new_proj=" + new_proj + "]";
	}
}
