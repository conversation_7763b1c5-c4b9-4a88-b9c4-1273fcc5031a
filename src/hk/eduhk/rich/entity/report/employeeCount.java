package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class employeeCount
{
	
	private String 	dept_code;
	private String 	employee_number;
	private String 	last_name;
	private String 	first_name;
	private String 	full_name;
	private String 	post_rank_code;
	private String 	period_id;
	private String 	count_output;
	private String 	count_output_2;
	private String 	count_output_3;
	private String 	count_output_4;
	private String 	count_output_5;
	
	

	public String getCount_output_2()
	{
		return count_output_2;
	}

	
	public void setCount_output_2(String count_output_2)
	{
		this.count_output_2 = count_output_2;
	}
	
	public String getPeriod_id()
	{
		return period_id;
	}
	
	public void setPeriod_id(String period_id)
	{
		this.period_id = period_id;
	}


	public String getCount_output_3()
	{
		return count_output_3;
	}



	
	public void setCount_output_3(String count_output_3)
	{
		this.count_output_3 = count_output_3;
	}



	
	public String getCount_output_4()
	{
		return count_output_4;
	}



	
	public void setCount_output_4(String count_output_4)
	{
		this.count_output_4 = count_output_4;
	}



	
	public String getCount_output_5()
	{
		return count_output_5;
	}



	
	public void setCount_output_5(String count_output_5)
	{
		this.count_output_5 = count_output_5;
	}



	public String getDept_code()
	{
		return dept_code;
	}


	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}


	
	public String getEmployee_number()
	{
		return employee_number;
	}


	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}


	
	public String getLast_name()
	{
		return last_name;
	}


	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}


	
	public String getFirst_name()
	{
		return first_name;
	}


	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}


	
	public String getFull_name()
	{
		return full_name;
	}


	
	public void setFull_name(String full_name)
	{
		this.full_name = full_name;
	}


	
	public String getPost_rank_code()
	{
		return post_rank_code;
	}


	
	public void setPost_rank_code(String post_rank_code)
	{
		this.post_rank_code = post_rank_code;
	}


	
	public String getCount_output()
	{
		return count_output;
	}


	
	public void setCount_output(String count_output)
	{
		this.count_output = count_output;
	}
	
	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("employee_number") + "\":" + "\"" + JSONObject.escape(String.valueOf(getEmployee_number())) + "\"," +
								 "\"" + JSONObject.escape("last_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getLast_name())) + "\"," +
								 "\"" + JSONObject.escape("first_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFirst_name())) + "\"," +
								 "\"" + JSONObject.escape("full_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFull_name())) + "\"," +
								 "\"" + JSONObject.escape("post_rank_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getPost_rank_code())) + "\"," +
								 "\"" + JSONObject.escape("period_id") + "\":" + "\"" + JSONObject.escape(String.valueOf(getPeriod_id())) + "\"," +
								 "\"" + JSONObject.escape("count_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_output()))  + "\","+
								 "\"" + JSONObject.escape("count_output_2") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_output_2()))  + "\","+
								 "\"" + JSONObject.escape("count_output_3") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_output_3()))  + "\","+
								 "\"" + JSONObject.escape("count_output_4") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_output_4()))  + "\","+
								 "\"" + JSONObject.escape("count_output_5") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_output_5()))  + "\"" 
		);

		strBuf.append("}");

		return strBuf.toString();
	}



	@Override
	public String toString()
	{
		return "employeeCount [dept_code=" + dept_code + ", employee_number=" + employee_number + ", last_name="
				+ last_name + ", first_name=" + first_name + ", full_name=" + full_name + ", post_rank_code="
				+ post_rank_code + ", period_id=" + period_id + ", count_output=" + count_output + ", count_output_2="
				+ count_output_2 + ", count_output_3=" + count_output_3 + ", count_output_4=" + count_output_4
				+ ", count_output_5=" + count_output_5 + "]";
	}

	
	
}
