package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class researchType
{
	private String 	r;
	private String 	nr;
	private String 	c;
	private String 	o;
	
	public String getR()
	{
		return r;
	}
	
	public void setR(String r)
	{
		this.r = r;
	}
	
	public String getNr()
	{
		return nr;
	}
	
	public void setNr(String nr)
	{
		this.nr = nr;
	}
	
	public String getC()
	{
		return c;
	}
	
	public void setC(String c)
	{
		this.c = c;
	}
	
	public String getO()
	{
		return o;
	}
	
	public void setO(String o)
	{
		this.o = o;
	}

	@Override
	public String toString()
	{
		return "researchType [r=" + r + ", nr=" + nr + ", c=" + c + ", o=" + o + "]";
	}
	
	
}
