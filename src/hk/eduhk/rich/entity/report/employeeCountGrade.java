package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class employeeCountGrade
{
	
	private String 	dept_code;
	private String 	last_name;
	private String 	other_name;
	private String 	post_rank_code;
	private String 	total_number;
	private String 	grade;
	
	public String getDept_code()
	{
		return dept_code;
	}

	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	public String getLast_name()
	{
		return last_name;
	}

	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}
	
	public String getOther_name()
	{
		return other_name;
	}
	
	public void setOther_name(String other_name)
	{
		this.other_name = other_name;
	}
	
	public String getPost_rank_code()
	{
		return post_rank_code;
	}
	
	public void setPost_rank_code(String post_rank_code)
	{
		this.post_rank_code = post_rank_code;
	}
	
	public String getTotal_number()
	{
		return total_number;
	}
	
	public void setTotal_number(String total_number)
	{
		this.total_number = total_number;
	}
	
	public String getGrade()
	{
		return grade;
	}
	
	public void setGrade(String grade)
	{
		this.grade = grade;
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("last_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getLast_name())) + "\"," +
								 "\"" + JSONObject.escape("other_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getOther_name())) + "\"," +
								 "\"" + JSONObject.escape("post_rank_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getPost_rank_code())) + "\"," +
								 "\"" + JSONObject.escape("total_number") + "\":" + "\""   + JSONObject.escape(StringUtils.defaultString(getTotal_number()))  + "\","+
								 "\"" + JSONObject.escape("grade") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getGrade()))  + "\""
		);

		strBuf.append("}");
		return strBuf.toString();
	}

	@Override
	public String toString()
	{
		return "employeeCountGrade [dept_code=" + dept_code + ", last_name=" + last_name + ", other_name=" + other_name
				+ ", post_rank_code=" + post_rank_code + ", total_number=" + total_number + ", grade=" + grade + "]";
	}
	
}
