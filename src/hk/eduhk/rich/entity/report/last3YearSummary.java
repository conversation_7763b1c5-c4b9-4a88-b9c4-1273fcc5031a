package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class last3YearSummary
{

	
	private String 	dept_code;
	private String 	employee_number;
	private String 	cdcf_weighting;
	private String 	apa_citation;
	private String 	name_other_pos;
	private String 	description;
	


	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getEmployee_number()
	{
		return employee_number;
	}

	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}

	
	public String getCdcf_weighting()
	{
		return cdcf_weighting;
	}

	
	public void setCdcf_weighting(String cdcf_weighting)
	{
		this.cdcf_weighting = cdcf_weighting;
	}

	
	public String getApa_citation()
	{
		return apa_citation;
	}

	
	public void setApa_citation(String apa_citation)
	{
		this.apa_citation = apa_citation;
	}

	
	public String getName_other_pos()
	{
		return name_other_pos;
	}

	
	public void setName_other_pos(String name_other_pos)
	{
		this.name_other_pos = name_other_pos;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("employee_number") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("cdcf_weighting") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("apa_citation") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("name_other_pos") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getName_other_pos())) + "\"," +
	 							 "\"" + JSONObject.escape("description") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getDescription())) + "\"");
		

		strBuf.append("}");

		return strBuf.toString();
	}

	@Override
	public String toString()
	{
		return "last3YearSummary [dept_code=" + dept_code + ", employee_number=" + employee_number + ", cdcf_weighting="
				+ cdcf_weighting + ", apa_citation=" + apa_citation + ", name_other_pos=" + name_other_pos
				+ ", description=" + description + "]";
	}



	
	
}
