package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class DeptLabelCount
{
	private String 	count_id;
	private String 	dept_code;
	private String 	label;
	private String 	count_staff;
	
	public String getCount_id()
	{
		return count_id;
	}

	
	public void setCount_id(String count_id)
	{
		this.count_id = count_id;
	}
	public String getDept_code()
	{
		return dept_code;
	}


	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}
	
	public String getLabel()
	{
		return label;
	}
	
	public void setLabel(String label)
	{
		this.label = label;
	}
	
	public String getCount_staff()
	{
		return count_staff;
	}
	
	public void setCount_staff(String count_staff)
	{
		this.count_staff = count_staff;
	}


	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("count_id") + "\":" + "\"" + JSONObject.escape(String.valueOf(getCount_id())) + "\"," +
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("label") + "\":" + "\"" + JSONObject.escape(String.valueOf(getLabel())) + "\"," +
								 "\"" + JSONObject.escape("count_staff") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_staff())) + "\"");

		strBuf.append("}");

		return strBuf.toString();
	}
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((count_id == null) ? 0 : count_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DeptLabelCount other = (DeptLabelCount) obj;
		if (count_id == null)
		{
			if (other.count_id != null)
				return false;
		}
		else if (!count_id.equals(other.count_id))
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "DeptLabelCount [count_id="+ count_id +", dept_code=" + dept_code + ", label=" + label + ", count_staff=" + count_staff + "]";
	}


	
	
}
