package hk.eduhk.rich.entity.report;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.SnapShotLog;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.util.PersistenceManager;


@SuppressWarnings("serial")
public class CdcfRptDAO extends BaseDAO
{

	private static CdcfRptDAO instance = null;


	public static synchronized CdcfRptDAO getInstance()
	{
		if (instance == null) instance = new CdcfRptDAO();
		return instance;
	}
	
	
	public static CdcfRptDAO getCacheInstance()
	{
		return CdcfRptDAO.getInstance();
	}	
	
	public List<CdcfRptPeriod> getCdcfRptPeriodListByPeriodIds(List<Integer> cdcfRptPeriodIdList)
	{
		List<CdcfRptPeriod> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM CdcfRptPeriod obj WHERE obj.period_id IN :period_id ORDER BY obj.date_to desc, obj.date_from desc, obj,period_id desc";			
			TypedQuery<CdcfRptPeriod> q = em.createQuery(query, CdcfRptPeriod.class);
			q.setParameter("period_id", cdcfRptPeriodIdList);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<CdcfRptPeriod> getCdcfRptPeriodList()
	{
		List<CdcfRptPeriod> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM CdcfRptPeriod obj ORDER BY obj.date_to desc, obj.date_from desc, obj,period_id desc";			
			TypedQuery<CdcfRptPeriod> q = em.createQuery(query, CdcfRptPeriod.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public CdcfRptPeriod getLastCdcfRptPeriod()
	{
		CdcfRptPeriod obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM CdcfRptPeriod obj ORDER BY obj.date_to desc, period_id desc";			
			TypedQuery<CdcfRptPeriod> q = em.createQuery(query, CdcfRptPeriod.class);
			List<CdcfRptPeriod> objList = q.setMaxResults(1).getResultList();
			obj = (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
		}
		finally
		{
			pm.close(em);
		}
		return obj;
	}
	
	public CdcfRptPeriod getCurrentCdcfRptPeriod()
	{
		CdcfRptPeriod obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM CdcfRptPeriod obj WHERE obj.is_current = :is_current ORDER BY obj.date_to desc, period_id desc";			
			TypedQuery<CdcfRptPeriod> q = em.createQuery(query, CdcfRptPeriod.class);
			q.setParameter("is_current", true);
			List<CdcfRptPeriod> objList = q.setMaxResults(1).getResultList();
			obj = (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
		}
		finally
		{
			pm.close(em);
		}
		return obj;
	}
	
	public CdcfRptPeriod getCdcfRptPeriod(Integer period_id)
	{
		CdcfRptPeriod obj = null;		
		if (period_id != null)
		{
			EntityManager em = null;
			try
			{
				em = getEntityManager();
				obj = em.find(CdcfRptPeriod.class, period_id);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public CdcfRptPeriod updateCdcfRptPeriod(CdcfRptPeriod obj) 
	{
		return updateEntity(obj);
	}
	
	public void deleteCdcfRptPeriod(Integer period_id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			CdcfRptPeriod obj = em.find(CdcfRptPeriod.class, period_id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public List<KtRptSum> getKtRptSumList(String form_code)
	{
		List<KtRptSum> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtRptSum obj WHERE obj.form_code = :form_code ORDER BY obj.form_code, obj.print_order ";			
			TypedQuery<KtRptSum> q = em.createQuery(query, KtRptSum.class);
			q.setParameter("form_code", form_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public List<SnapShotLog> getSnapShotLogList(){
		List<SnapShotLog> objList = new ArrayList<SnapShotLog>();
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		try {
			conn = pm.getConnection();
			
			StringBuffer sqlBuf = new StringBuffer();

			sqlBuf.append("SELECT * FROM RICH.RH_Z_SNAPSHOT_LOG ORDER BY LOG_ID DESC");

			//System.out.println("sqlBuf:"+sqlBuf);
			//logger.log(Level.FINEST, sqlBuf.toString());
				pStmt = conn.prepareStatement(sqlBuf.toString());
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next())
				{
					SnapShotLog obj = new SnapShotLog();
					obj.setLog_id(rs.getInt("LOG_ID"));
					obj.setPeriod_id(rs.getInt("PERIOD_ID"));
					obj.setS_type(rs.getString("S_TYPE"));
					obj.setUserstamp(rs.getString("CREATOR"));
					obj.setTimestamp(rs.getDate("CREATION_TIME"));
					objList.add(obj);						
				}
		}catch (Exception e){
			throw new RuntimeException(e);
		}finally{
			pm.close(pStmt);
			pm.close(conn);
		}
		//System.out.println("objList:"+objList);
		return objList;
	}
}