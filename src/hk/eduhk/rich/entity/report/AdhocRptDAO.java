package hk.eduhk.rich.entity.report;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;

import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.staff.StaffRank;


@SuppressWarnings("serial")
public class AdhocRptDAO extends BaseDAO
{

	private static AdhocRptDAO instance = null;


	public static synchronized AdhocRptDAO getInstance()
	{
		if (instance == null) instance = new AdhocRptDAO();
		return instance;
	}
	
	
	public static AdhocRptDAO getCacheInstance()
	{
		return AdhocRptDAO.getInstance();
	}	
	
	public List<AdhocRpt> getAdhocRptList(String enabled_flag)
	{
		List<AdhocRpt> objList = null;
		EntityManager em = null;		
		String where = "";
		try
		{
			if (enabled_flag != null) {
				where = " WHERE obj.enabled_flag = :enabled_flag ";
			}
			em = getEntityManager();		
			String query = "SELECT obj FROM AdhocRpt obj "+where+" ORDER BY obj.print_order, obj.rpt_code";		
			TypedQuery<AdhocRpt> q = em.createQuery(query, AdhocRpt.class);
			if (enabled_flag != null) {
				q.setParameter("enabled_flag", enabled_flag);
			}
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	

	public AdhocRpt getAdhocRpt(String rpt_code)
	{
		List<AdhocRpt> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM AdhocRpt obj WHERE obj.rpt_code = :rpt_code ORDER BY obj.print_order";			
			TypedQuery<AdhocRpt> q = em.createQuery(query, AdhocRpt.class);
			q.setParameter("rpt_code", rpt_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
	}

	public AdhocRpt getAdhocRptById(Integer id)
	{
		AdhocRpt obj = null;
		
		if (id != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(AdhocRpt.class, id);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public AdhocRpt updateAdhocRpt(AdhocRpt obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteAdhocRpt(Integer id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			AdhocRpt obj = em.find(AdhocRpt.class, id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
}