package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class staffNumberName
{

	private String 	employee_number;
	private String 	full_name;
	
	public String getEmployee_number()
	{
		return employee_number;
	}

	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}

	
	public String getFull_name()
	{
		return full_name;
	}

	
	public void setFull_name(String full_name)
	{
		this.full_name = full_name;
	}

	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("employee_number") + "\":" + "\"" + JSONObject.escape(String.valueOf(getEmployee_number())) + "\"," +
								 "\"" + JSONObject.escape("full_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFull_name())) + "\"" );

		strBuf.append("}");

		return strBuf.toString();
	}

	@Override
	public String toString()
	{
		return "staffNumberName [employee_number=" + employee_number + ", full_name=" + full_name + "]";
	}



	
	
}
