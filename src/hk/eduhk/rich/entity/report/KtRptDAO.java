package hk.eduhk.rich.entity.report;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;

import hk.eduhk.rich.BaseDAO;


@SuppressWarnings("serial")
public class KtRptDAO extends BaseDAO
{

	private static KtRptDAO instance = null;


	public static synchronized KtRptDAO getInstance()
	{
		if (instance == null) instance = new KtRptDAO();
		return instance;
	}
	
	
	public static KtRptDAO getCacheInstance()
	{
		return KtRptDAO.getInstance();
	}	
	
	public List<KtRptPeriod> getKtRptPeriodList()
	{
		List<KtRptPeriod> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtRptPeriod obj ORDER BY obj.date_to desc, obj.date_from desc, obj,period_id desc";			
			TypedQuery<KtRptPeriod> q = em.createQuery(query, KtRptPeriod.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
	public KtRptPeriod getLastKtRptPeriod()
	{
		KtRptPeriod obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtRptPeriod obj ORDER BY obj.date_to desc, period_id desc";			
			TypedQuery<KtRptPeriod> q = em.createQuery(query, KtRptPeriod.class);
			List<KtRptPeriod> objList = q.setMaxResults(1).getResultList();
			obj = (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
		}
		finally
		{
			pm.close(em);
		}
		return obj;
	}
	
	public KtRptPeriod getCurrentKtRptPeriod()
	{
		KtRptPeriod obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtRptPeriod obj WHERE obj.is_current = :is_current ORDER BY obj.date_to desc, period_id desc";			
			TypedQuery<KtRptPeriod> q = em.createQuery(query, KtRptPeriod.class);
			q.setParameter("is_current", true);
			List<KtRptPeriod> objList = q.setMaxResults(1).getResultList();
			obj = (CollectionUtils.isNotEmpty(objList))?objList.get(0):null;
		}
		finally
		{
			pm.close(em);
		}
		return obj;
	}
	
	public KtRptPeriod getKtRptPeriod(Integer period_id)
	{
		KtRptPeriod obj = null;		
		if (period_id != null)
		{
			EntityManager em = null;
			try
			{
				em = getEntityManager();
				obj = em.find(KtRptPeriod.class, period_id);
			}
			finally
			{
				pm.close(em);
			}
		}
		return obj;
	}
	
	public KtRptPeriod updateKtRptPeriod(KtRptPeriod obj) 
	{
		return updateEntity(obj);
	}
	
	public void deleteKtRptPeriod(Integer period_id)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			KtRptPeriod obj = em.find(KtRptPeriod.class, period_id);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public List<KtRptSum> getKtRptSumList(String form_code)
	{
		List<KtRptSum> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM KtRptSum obj WHERE obj.form_code = :form_code ORDER BY obj.form_code, obj.print_order ";			
			TypedQuery<KtRptSum> q = em.createQuery(query, KtRptSum.class);
			q.setParameter("form_code", form_code);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return objList;
	}
	
}