package hk.eduhk.rich.entity.report;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

public class a4ReportCountAmt
{
	public static Logger logger = Logger.getLogger(a4ReportCountAmt.class.toString());
	
	
	//found sources field
	private String count;
	private String amount;
	
	public String getCount()
	{
		return count;
	}
	
	public void setCount(String count)
	{
		this.count = count;
	}
	
	public String getAmount()
	{
		return amount;
	}
	
	public void setAmount(String amount)
	{
		this.amount = amount;
	}

	@Override
	public String toString()
	{
		return "a4ReportCountAmt [count=" + count + ", amount=" + amount + "]";
	}
	

	
}
