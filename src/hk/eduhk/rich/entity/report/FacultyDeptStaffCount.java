package hk.eduhk.rich.entity.report;

import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class FacultyDeptStaffCount
{
	
	private String 	report_id;
	private String 	faculty;
	private String 	dept_code;
	private String 	no_of_item_band;
	private String 	lv;
	private String 	count_staff;
	
	public String getReport_id()
	{
		return report_id;
	}
	
	public void setReport_id(String report_id)
	{
		this.report_id = report_id;
	}
	
	public String getFaculty()
	{
		return faculty;
	}

	
	public void setFaculty(String faculty)
	{
		this.faculty = faculty;
	} 

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getNo_of_item_band()
	{
		return no_of_item_band;
	}

	
	public void setNo_of_item_band(String no_of_item_band)
	{
		this.no_of_item_band = no_of_item_band;
	}

	
	public String getLv()
	{
		return lv;
	}

	
	public void setLv(String lv)
	{
		this.lv = lv;
	}

	
	public String getCount_staff()
	{
		return count_staff;
	}

	
	public void setCount_staff(String count_staff)
	{
		this.count_staff = count_staff;
	}
	
	
	
	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("report_id") + "\":" + "\"" + JSONObject.escape(String.valueOf(getReport_id())) + "\"," +
								 "\"" + JSONObject.escape("faculty") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFaculty())) + "\"," +
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("no_of_item_band") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getNo_of_item_band())) + "\"," +
								 "\"" + JSONObject.escape("lv") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getLv())) + "\"," +
								 "\"" + JSONObject.escape("count_staff") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCount_staff())) + "\"");

		strBuf.append("}");

		return strBuf.toString();
	}
	
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((report_id == null) ? 0 : report_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FacultyDeptStaffCount other = (FacultyDeptStaffCount) obj;
		if (report_id == null)
		{
			if (other.report_id != null)
				return false;
		}
		else if (!report_id.equals(other.report_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "FacultyDeptStaffCount [report_id=" + report_id + ", faculty=" + faculty + ", dept_code=" + dept_code
				+ ", no_of_item_band=" + no_of_item_band + ", lv=" + lv  + ", count_staff="	+ count_staff + "]";
	}

	
	
}
