package hk.eduhk.rich.entity.report;

import java.util.logging.Logger;

import javax.persistence.*;


@Entity
@Table(name = "RH_ELIG_ACAD_STAFF_COUNT_V")
public class AppStaffCount
{
	public static Logger logger = Logger.getLogger(AppStaffCount.class.toString());
	
	@Id
	@Column(name = "report_id")
	private Integer report_id;

	@Column(name = "college_code")
	private String college_code;
	
	@Column(name = "dept_code")
	private String dept_code;

	@Column(name = "staff_count")
	private Integer staff_count;


	public Integer getReport_id()
	{
		return report_id;
	}

	
	public void setReport_id(Integer report_id)
	{
		this.report_id = report_id;
	}
	

	
	public String getCollege_code()
	{
		return college_code;
	}

	public void setCollege_code(String college_code)
	{
		this.college_code = college_code;
	}

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public Integer getStaff_count()
	{
		return staff_count;
	}

	
	public void setStaff_count(Integer staff_count)
	{
		this.staff_count = staff_count;
	}

	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((report_id == null) ? 0 : report_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AppStaffCount other = (AppStaffCount) obj;
		if (report_id == null)
		{
			if (other.report_id != null)
				return false;
		}
		else if (!report_id.equals(other.report_id))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "AppStaffCount [college_code=" + college_code + ", dept_code=" + dept_code + ", staff_count="
				+ staff_count + "]";
	}
}
