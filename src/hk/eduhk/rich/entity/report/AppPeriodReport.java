package hk.eduhk.rich.entity.report;

import java.util.logging.Logger;

import javax.persistence.*;


@Entity
@Table(name = "RH_A1_REPORT_V")
@SuppressWarnings("serial")
public class AppPeriodReport
{
	public static Logger logger = Logger.getLogger(AppPeriodReport.class.toString());
	
	@Id
	@Column(name = "report_id")
	private Integer report_id;
	
	@Column(name = "faculty")
	private String faculty;
	
	@Column(name = "dept_code")
	private String dept_code;
	
	@Column(name = "no_of_item_band")
	private String no_of_item_band;
	
	@Column(name = "lv")
	private Integer lv;
	
	
	@Column(name = "count_staff")
	private Integer count_staff;

	
	public Integer getReport_id()
	{
		return report_id;
	}
	
	public void setReport_id(Integer report_id)
	{
		this.report_id = report_id;
	}
	
	public String getFaculty()
	{
		return faculty;
	}

	
	public void setFaculty(String faculty)
	{
		this.faculty = faculty;
	} 

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getNo_of_item_band()
	{
		return no_of_item_band;
	}

	
	public void setNo_of_item_band(String no_of_item_band)
	{
		this.no_of_item_band = no_of_item_band;
	}

	
	public Integer getLv()
	{
		return lv;
	}

	
	public void setLv(Integer lv)
	{
		this.lv = lv;
	}

	
	public Integer getCount_staff()
	{
		return count_staff;
	}

	
	public void setCount_staff(Integer count_staff)
	{
		this.count_staff = count_staff;
	}
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((report_id == null) ? 0 : report_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AppPeriodReport other = (AppPeriodReport) obj;
		if (report_id == null)
		{
			if (other.report_id != null)
				return false;
		}
		else if (!report_id.equals(other.report_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "AppPeriodReport [report_id=" + report_id + ", faculty=" + faculty + ", dept_code=" + dept_code
				+ ", no_of_item_band=" + no_of_item_band + ", lv=" + lv  + ", count_staff="	+ count_staff + "]";
	}

	
	
}
