package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class FacDeptDesc
{
	private String 	faculty;
	private String 	faculty_name;
	private String 	dept_code;
	private String 	department_name;
	
	public String getFaculty()
	{
		return faculty;
	}



	
	public void setFaculty(String faculty)
	{
		this.faculty = faculty;
	}



	
	public String getFaculty_name()
	{
		return faculty_name;
	}



	
	public void setFaculty_name(String faculty_name)
	{
		this.faculty_name = faculty_name;
	}



	
	public String getDept_code()
	{
		return dept_code;
	}



	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}



	
	public String getDepartment_name()
	{
		return department_name;
	}



	
	public void setDepartment_name(String department_name)
	{
		this.department_name = department_name;
	}



	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("faculty") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFaculty())) + "\"," +
								 "\"" + JSONObject.escape("faculty_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFaculty_name())) + "\"," +
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("department_name") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getDepartment_name())) + "\"");

		strBuf.append("}");

		return strBuf.toString();
	}




	@Override
	public String toString()
	{
		return "FacDeptDesc [faculty=" + faculty + ", faculty_name=" + faculty_name + ", dept_code=" + dept_code
				+ ", department_name=" + department_name + "]";
	}
	
	
	
}
