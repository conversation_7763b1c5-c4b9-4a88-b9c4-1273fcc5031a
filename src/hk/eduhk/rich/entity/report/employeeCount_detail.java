package hk.eduhk.rich.entity.report;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;


@SuppressWarnings("serial")
public class employeeCount_detail
{
	
	private String 	dept_code;
	private String 	employee_number;
	private String 	last_name;
	private String 	first_name;
	private String 	post_rank_code;
	private String 	sr_output;
	private String 	snr_output;
	private String 	sc_output;
	private String 	so_output;
	private String 	jr_output;
	private String 	jnr_output;
	private String 	jc_output;
	private String 	jo_output;
	private String 	cr_output;
	private String 	cnr_output;
	private String 	cc_output;
	private String 	co_output;
	private String 	crr_output;
	private String 	crnr_output;
	private String 	crc_output;
	private String 	cro_output;
	private String 	pr_output;
	private String 	pnr_output;
	private String 	pc_output;
	private String 	po_output;
	private String 	or_output;
	private String 	onr_output;
	private String 	oc_output;
	private String 	oo_output;
	
	
	public String getDept_code()
	{
		return dept_code;
	}

	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}



	
	public String getEmployee_number()
	{
		return employee_number;
	}



	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}



	
	public String getLast_name()
	{
		return last_name;
	}



	
	public void setLast_name(String last_name)
	{
		this.last_name = last_name;
	}



	
	public String getFirst_name()
	{
		return first_name;
	}



	
	public void setFirst_name(String first_name)
	{
		this.first_name = first_name;
	}



	
	public String getPost_rank_code()
	{
		return post_rank_code;
	}



	
	public void setPost_rank_code(String post_rank_code)
	{
		this.post_rank_code = post_rank_code;
	}
	
	public String getSr_output()
	{
		return sr_output;
	}

	
	public void setSr_output(String sr_output)
	{
		this.sr_output = sr_output;
	}

	
	public String getSnr_output()
	{
		return snr_output;
	}

	
	public void setSnr_output(String snr_output)
	{
		this.snr_output = snr_output;
	}

	
	public String getSc_output()
	{
		return sc_output;
	}

	
	public void setSc_output(String sc_output)
	{
		this.sc_output = sc_output;
	}

	
	public String getSo_output()
	{
		return so_output;
	}

	
	public void setSo_output(String so_output)
	{
		this.so_output = so_output;
	}

	
	public String getJr_output()
	{
		return jr_output;
	}

	
	public void setJr_output(String jr_output)
	{
		this.jr_output = jr_output;
	}

	
	public String getJnr_output()
	{
		return jnr_output;
	}

	
	public void setJnr_output(String jnr_output)
	{
		this.jnr_output = jnr_output;
	}

	
	public String getJc_output()
	{
		return jc_output;
	}

	
	public void setJc_output(String jc_output)
	{
		this.jc_output = jc_output;
	}

	
	public String getJo_output()
	{
		return jo_output;
	}

	
	public void setJo_output(String jo_output)
	{
		this.jo_output = jo_output;
	}

	
	public String getCr_output()
	{
		return cr_output;
	}

	
	public void setCr_output(String cr_output)
	{
		this.cr_output = cr_output;
	}

	
	public String getCnr_output()
	{
		return cnr_output;
	}

	
	public void setCnr_output(String cnr_output)
	{
		this.cnr_output = cnr_output;
	}

	
	public String getCc_output()
	{
		return cc_output;
	}

	
	public void setCc_output(String cc_output)
	{
		this.cc_output = cc_output;
	}

	
	public String getCo_output()
	{
		return co_output;
	}

	
	public void setCo_output(String co_output)
	{
		this.co_output = co_output;
	}

	
	public String getCrr_output()
	{
		return crr_output;
	}

	
	public void setCrr_output(String crr_output)
	{
		this.crr_output = crr_output;
	}

	
	public String getCrnr_output()
	{
		return crnr_output;
	}

	
	public void setCrnr_output(String crnr_output)
	{
		this.crnr_output = crnr_output;
	}

	
	public String getCrc_output()
	{
		return crc_output;
	}

	
	public void setCrc_output(String crc_output)
	{
		this.crc_output = crc_output;
	}

	
	public String getCro_output()
	{
		return cro_output;
	}

	
	public void setCro_output(String cro_output)
	{
		this.cro_output = cro_output;
	}

	
	public String getPr_output()
	{
		return pr_output;
	}

	
	public void setPr_output(String pr_output)
	{
		this.pr_output = pr_output;
	}

	
	public String getPnr_output()
	{
		return pnr_output;
	}

	
	public void setPnr_output(String pnr_output)
	{
		this.pnr_output = pnr_output;
	}

	
	public String getPc_output()
	{
		return pc_output;
	}

	
	public void setPc_output(String pc_output)
	{
		this.pc_output = pc_output;
	}

	
	public String getPo_output()
	{
		return po_output;
	}

	
	public void setPo_output(String po_output)
	{
		this.po_output = po_output;
	}

	
	public String getOr_output()
	{
		return or_output;
	}

	
	public void setOr_output(String or_output)
	{
		this.or_output = or_output;
	}

	
	public String getOnr_output()
	{
		return onr_output;
	}

	
	public void setOnr_output(String onr_output)
	{
		this.onr_output = onr_output;
	}

	
	public String getOc_output()
	{
		return oc_output;
	}

	
	public void setOc_output(String oc_output)
	{
		this.oc_output = oc_output;
	}

	
	public String getOo_output()
	{
		return oo_output;
	}

	
	public void setOo_output(String oo_output)
	{
		this.oo_output = oo_output;
	}

	public String toJSONString()
	{
		StringBuffer strBuf = new StringBuffer();
		strBuf.append("{" + 
								 "\"" + JSONObject.escape("dept_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getDept_code())) + "\"," +
								 "\"" + JSONObject.escape("employee_number") + "\":" + "\"" + JSONObject.escape(String.valueOf(getEmployee_number())) + "\"," +
								 "\"" + JSONObject.escape("last_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getLast_name())) + "\"," +
								 "\"" + JSONObject.escape("first_name") + "\":" + "\"" + JSONObject.escape(String.valueOf(getFirst_name())) + "\"," +
								 "\"" + JSONObject.escape("post_rank_code") + "\":" + "\"" + JSONObject.escape(String.valueOf(getPost_rank_code())) + "\"," +
								 "\"" + JSONObject.escape("sr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getSr_output()))  + "\"," +
								 "\"" + JSONObject.escape("snr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getSnr_output()))  + "\"," +
								 "\"" + JSONObject.escape("sc_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getSc_output()))  + "\"," +
								 "\"" + JSONObject.escape("so_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getSo_output()))  + "\"," +
								 "\"" + JSONObject.escape("jr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getJr_output()))  + "\"," +
								 "\"" + JSONObject.escape("jnr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getJnr_output()))  + "\"," +
								 "\"" + JSONObject.escape("jc_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getJc_output()))  + "\"," +
								 "\"" + JSONObject.escape("jo_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getJo_output()))  + "\"," +
								 "\"" + JSONObject.escape("cr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCr_output()))  + "\"," +
								 "\"" + JSONObject.escape("cnr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCnr_output()))  + "\"," +
								 "\"" + JSONObject.escape("cc_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCc_output()))  + "\"," +
								 "\"" + JSONObject.escape("co_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCo_output()))  + "\"," +
								 "\"" + JSONObject.escape("crr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCrr_output()))  + "\"," +
								 "\"" + JSONObject.escape("crnr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCrnr_output()))  + "\"," +
								 "\"" + JSONObject.escape("crc_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCrc_output()))  + "\"," +
								 "\"" + JSONObject.escape("cro_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getCro_output()))  + "\"," +
								 "\"" + JSONObject.escape("pr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPr_output()))  + "\"," +
								 "\"" + JSONObject.escape("pnr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPnr_output()))  + "\"," +
								 "\"" + JSONObject.escape("pc_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPc_output()))  + "\"," +
								 "\"" + JSONObject.escape("po_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getPo_output()))  + "\"," +
								 "\"" + JSONObject.escape("or_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getOr_output()))  + "\"," +
								 "\"" + JSONObject.escape("onr_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getOnr_output()))  + "\"," +
								 "\"" + JSONObject.escape("oc_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getOc_output()))  + "\"," +
								 "\"" + JSONObject.escape("oo_output") + "\":" + "\"" + JSONObject.escape(StringUtils.defaultString(getOo_output()))  + "\""
		);

		strBuf.append("}");

		return strBuf.toString();
	}

	@Override
	public String toString()
	{
		return "employeeCount_detail [dept_code=" + dept_code + ", employee_number=" + employee_number + ", last_name="
				+ last_name + ", first_name=" + first_name + ", post_rank_code=" + post_rank_code + ", sr_output="
				+ sr_output + ", snr_output=" + snr_output + ", sc_output=" + sc_output + ", so_output=" + so_output
				+ ", jr_output=" + jr_output + ", jnr_output=" + jnr_output + ", jc_output=" + jc_output
				+ ", jo_output=" + jo_output + ", cr_output=" + cr_output + ", cnr_output=" + cnr_output
				+ ", cc_output=" + cc_output + ", co_output=" + co_output + ", crr_output=" + crr_output
				+ ", crnr_output=" + crnr_output + ", crc_output=" + crc_output + ", cro_output=" + cro_output
				+ ", pr_output=" + pr_output + ", pnr_output=" + pnr_output + ", pc_output=" + pc_output
				+ ", po_output=" + po_output + ", or_output=" + or_output + ", onr_output=" + onr_output
				+ ", oc_output=" + oc_output + ", oo_output=" + oo_output + "]";
	}
}
