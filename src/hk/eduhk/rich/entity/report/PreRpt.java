package hk.eduhk.rich.entity.report;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.publication.PublicationDAO;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_Z_PRE_RPT")
public class PreRpt extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PreRpt.class.toString());
	
	@Id
	@Column(name = "rpt_id")
	private Integer rpt_id;
	
	@Column(name = "rpt_code")
	private String rpt_code;
	
	@Column(name = "rpt_desc")
	private String rpt_desc;
	
	@Column(name = "print_order")
	private Integer print_order;
	
	@Column(name = "rpt_cat")
	private String rpt_cat;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;
	
	@Column(name = "rpt_note")
	private String rpt_note;
	
	@Column(name = "rpt_type")
	private String rpt_type;
	
	@Column(name = "rpt_file_name")
	private String rpt_file_name;
	
	@Column(name = "is_rdo")
	private String is_rdo;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "rpt_cat", referencedColumnName = "rpt_cat", nullable = false, insertable = false, updatable = false)
	private PreRptCat preRptCat;
	
	public Integer getRpt_id()
	{
		return rpt_id;
	}

	
	public void setRpt_id(Integer rpt_id)
	{
		this.rpt_id = rpt_id;
	}

	
	public String getRpt_code()
	{
		return rpt_code;
	}

	
	public void setRpt_code(String rpt_code)
	{
		this.rpt_code = rpt_code;
	}

	
	public String getRpt_desc()
	{
		return rpt_desc;
	}

	
	public void setRpt_desc(String rpt_desc)
	{
		this.rpt_desc = rpt_desc;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}


	
	public String getRpt_cat()
	{
		return rpt_cat;
	}


	
	public void setRpt_cat(String rpt_cat)
	{
		this.rpt_cat = rpt_cat;
	}


	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}


	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}
	
	
	
	public String getRpt_note()
	{
		return rpt_note;
	}


	
	public void setRpt_note(String rpt_note)
	{
		this.rpt_note = rpt_note;
	}

	
	
	public String getRpt_type()
	{
		return rpt_type;
	}


	
	public void setRpt_type(String rpt_type)
	{
		this.rpt_type = rpt_type;
	}


	
	public String getRpt_file_name()
	{
		return rpt_file_name;
	}


	
	public void setRpt_file_name(String rpt_file_name)
	{
		this.rpt_file_name = rpt_file_name;
	}


	
	public String getIs_rdo()
	{
		return is_rdo;
	}


	
	public void setIs_rdo(String is_rdo)
	{
		this.is_rdo = is_rdo;
	}


	public PreRptCat getPreRptCat()
	{
		if (preRptCat != null) {
			try {
				preRptCat.getRpt_cat();
			}catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					PreRptDAO preRptDao = PreRptDAO.getInstance();
					preRptCat = preRptDao.getPreRptCat(getRpt_cat());
				}
				else
				{
					throw re;
				}
			}
		}
		return preRptCat;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(rpt_id);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PreRpt other = (PreRpt) obj;
		return Objects.equals(rpt_id, other.rpt_id);
	}


	@Override
	public String toString()
	{
		return "PreRpt [rpt_id=" + rpt_id + ", rpt_code=" + rpt_code + ", rpt_desc=" + rpt_desc + ", print_order="
				+ print_order + ", rpt_cat=" + rpt_cat + ", enabled_flag=" + enabled_flag + ", rpt_note=" + rpt_note
				+ ", rpt_type=" + rpt_type + ", rpt_file_name=" + rpt_file_name + ", is_rdo=" + is_rdo + ", preRptCat="
				+ preRptCat + "]";
	}


}
