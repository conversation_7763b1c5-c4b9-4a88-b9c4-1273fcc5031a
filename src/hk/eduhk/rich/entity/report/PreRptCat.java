package hk.eduhk.rich.entity.report;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;

import javax.persistence.*;

import hk.eduhk.rich.UserPersistenceObject;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;


@SuppressWarnings("serial")
@Entity
@Table(name = "RH_Z_PRE_RPT_CAT")
public class PreRptCat extends UserPersistenceObject
{
	public static Logger logger = Logger.getLogger(PreRptCat.class.toString());
	
	@Id
	@Column(name = "rpt_cat")
	private String rpt_cat;
	
	@Column(name = "rpt_cat_desc")
	private String rpt_cat_desc;
	
	@Column(name = "print_order")
	private Integer print_order;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;

	@Column(name = "is_rdo")
	private String is_rdo;
	
	public String getRpt_cat()
	{
		return rpt_cat;
	}

	
	public void setRpt_cat(String rpt_cat)
	{
		this.rpt_cat = rpt_cat;
	}

	
	public String getRpt_cat_desc()
	{
		return rpt_cat_desc;
	}

	
	public void setRpt_cat_desc(String rpt_cat_desc)
	{
		this.rpt_cat_desc = rpt_cat_desc;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}

	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}

	
	public String getIs_rdo()
	{
		return is_rdo;
	}


	
	public void setIs_rdo(String is_rdo)
	{
		this.is_rdo = is_rdo;
	}


	@Override
	public int hashCode()
	{
		return Objects.hash(rpt_cat);
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PreRptCat other = (PreRptCat) obj;
		return Objects.equals(rpt_cat, other.rpt_cat);
	}


	@Override
	public String toString()
	{
		return "PreRptCat [rpt_cat=" + rpt_cat + ", rpt_cat_desc=" + rpt_cat_desc + ", print_order=" + print_order
				+ ", enabled_flag=" + enabled_flag + ", is_rdo=" + is_rdo + "]";
	}
}
