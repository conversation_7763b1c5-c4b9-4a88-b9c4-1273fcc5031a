package hk.eduhk.rich.entity.report;

import java.util.logging.Logger;

import javax.persistence.*;


@Entity
@Table(name = "RH_REPORT_STAFF_WEIGHT_V")
@SuppressWarnings("serial")
public class AppStaffWeight
{
	public static Logger logger = Logger.getLogger(AppStaffWeight.class.toString());
	
	@Id
	@Column(name = "weight_id")
	private Integer weight_id;
	
	@Column(name = "from_year")
	private Integer from_year;
	
	@Column(name = "from_month")
	private Integer from_month;
	
	@Column(name = "parent_lookup_code")
	private String parent_lookup_code;
	
	@Column(name = "dept_code")
	private String dept_code;
	
	@Column(name = "employee_number")
	private String employee_number;
	
	@Column(name = "cdcf_weighting")
	private Integer cdcf_weighting;

	
	public Integer getWeight_id()
	{
		return weight_id;
	}

	
	public void setWeight_id(Integer weight_id)
	{
		this.weight_id = weight_id;
	}

	
	public Integer getFrom_year()
	{
		return from_year;
	}

	
	public void setFrom_year(Integer from_year)
	{
		this.from_year = from_year;
	}

	
	public Integer getFrom_month()
	{
		return from_month;
	}

	
	public void setFrom_month(Integer from_month)
	{
		this.from_month = from_month;
	}

	
	public String getParent_lookup_code()
	{
		return parent_lookup_code;
	}

	
	public void setParent_lookup_code(String parent_lookup_code)
	{
		this.parent_lookup_code = parent_lookup_code;
	}

	
	public String getDept_code()
	{
		return dept_code;
	}

	
	public void setDept_code(String dept_code)
	{
		this.dept_code = dept_code;
	}

	
	public String getEmployee_number()
	{
		return employee_number;
	}

	
	public void setEmployee_number(String employee_number)
	{
		this.employee_number = employee_number;
	}

	
	public Integer getCdcf_weighting()
	{
		return cdcf_weighting;
	}

	
	public void setCdcf_weighting(Integer cdcf_weighting)
	{
		this.cdcf_weighting = cdcf_weighting;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((weight_id == null) ? 0 : weight_id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AppStaffWeight other = (AppStaffWeight) obj;
		if (weight_id == null)
		{
			if (other.weight_id != null)
				return false;
		}
		else if (!weight_id.equals(other.weight_id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "AppStaffWeight [weight_id=" + weight_id + ", from_year=" + from_year + ", from_month=" + from_month
				+ ", parent_lookup_code=" + parent_lookup_code + ", dept_code=" + dept_code + ", employee_number="
				+ employee_number + ", cdcf_weighting=" + cdcf_weighting + "]";
	}

}
