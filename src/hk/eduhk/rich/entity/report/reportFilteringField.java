package hk.eduhk.rich.entity.report;

import java.util.List;
import java.util.logging.Logger;

import javax.persistence.*;

public class reportFilteringField
{
	public static Logger logger = Logger.getLogger(reportFilteringField.class.toString());
	
	
	//Reporting Period
	private boolean p_period;
	private int p_from_year;
	private int p_from_month;
	private int p_to_year;
	private int P_to_month;
	private int p_id_no;
	
	//RI Date Period
	private boolean d_period;
	private int d_from_year;
	private int d_from_month;
	private int d_to_year;
	private int d_to_month;


	public boolean isP_period()
	{
		return p_period;
	}

	
	public void setP_period(boolean p_period)
	{
		this.p_period = p_period;
	}
	
	
	
	
	public int getP_id_no()
	{
		return p_id_no;
	}


	
	public void setP_id_no(int p_id_no)
	{
		this.p_id_no = p_id_no;
	}


	public boolean isD_period()
	{
		return d_period;
	}

	
	public void setD_period(boolean d_period)
	{
		this.d_period = d_period;
	}

	public int getP_from_year()
	{
		return p_from_year;
	}
	
	public void setP_from_year(int p_from_year)
	{
		this.p_from_year = p_from_year;
	}
	
	public int getP_from_month()
	{
		return p_from_month;
	}
	
	public void setP_from_month(int p_from_month)
	{
		this.p_from_month = p_from_month;
	}
	
	public int getP_to_year()
	{
		return p_to_year;
	}
	
	public void setP_to_year(int p_to_year)
	{
		this.p_to_year = p_to_year;
	}
	

	public int getP_to_month()
	{
		return P_to_month;
	}
	
	public void setP_to_month(int p_to_month)
	{
		P_to_month = p_to_month;
	}
	
	public int getD_from_year()
	{
		return d_from_year;
	}
	
	public void setD_from_year(int d_from_year)
	{
		this.d_from_year = d_from_year;
	}
	
	public int getD_from_month()
	{
		return d_from_month;
	}
	
	public void setD_from_month(int d_from_month)
	{
		this.d_from_month = d_from_month;
	}
	
	public int getD_to_year()
	{
		return d_to_year;
	}
	
	public void setD_to_year(int d_to_year)
	{
		this.d_to_year = d_to_year;
	}
	
	public int getD_to_month()
	{
		return d_to_month;
	}
	
	public void setD_to_month(int d_to_month)
	{
		this.d_to_month = d_to_month;
	}

	


	@Override
	public String toString()
	{
		return "reportFilteringField [p_period=" + p_period + ", p_from_year=" + p_from_year + ", p_from_month="
				+ p_from_month + ", p_to_year=" + p_to_year + ", P_to_month=" + P_to_month + ", p_id_no=" + p_id_no
				+ ", d_period=" + d_period + ", d_from_year=" + d_from_year + ", d_from_month=" + d_from_month
				+ ", d_to_year=" + d_to_year + ", d_to_month=" + d_to_month + "]";
	}
}

