package hk.eduhk.rich.entity.report;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;

import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.util.PersistenceManager;

import org.apache.commons.collections.CollectionUtils;

import hk.eduhk.rich.BaseDAO;

@SuppressWarnings("serial")
public class AppPeriodReportDAO extends BaseDAO
{

	private static AppPeriodReportDAO instance = null;



	public static synchronized AppPeriodReportDAO getInstance()
	{
		if (instance == null) instance = new AppPeriodReportDAO();
		return instance;
	}
	
	
	public static AppPeriodReportDAO getCacheInstance()
	{
		return AppPeriodReportDAO.getInstance();
	}	
	
		
	public static List<FacultyDeptStaffCount> getAppStaff_weight (reportFilteringField filter) throws SQLException
	//ToDO: PARA filter object type
	{
		
		List<FacultyDeptStaffCount> voList = new ArrayList<FacultyDeptStaffCount>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
			try 
			{
				String count_query = "SELECT D.PARENT_LOOKUP_CODE FACULTY, " + 
						"    BAND.dept_code, " + 
						"    BAND.NO_OF_ITEM_BAND, " + 
						"    BAND.LV, " + 
						"    COUNT( DISTINCT EMPLOYEE_NUMBER ) COUNT_STAFF " + 
						"	 FROM  ( " + 
						"    SELECT *  FROM ( " + 
						"        SELECT  CASE LEVEL " + 
						"                  WHEN 1 THEN '0' " + 
						"                  WHEN 2 THEN '>0 to <1' " + 
						"                  WHEN 3 THEN '>=1 to 2' " + 
						"                  WHEN 4 THEN '>2 to 3' " + 
						"                  WHEN 5 THEN '>3 to 4' " + 
						"                  WHEN 6 THEN'>4 to 5' " + 
						"                  WHEN 7 THEN '>5 to 6' " + 
						"                  WHEN 8 THEN 'over 6' " + 
						"              END NO_OF_ITEM_BAND, LEVEL LV" + 
						"        FROM DUAL" + 
						"        CONNECT BY LEVEL <= 8 ) " + 
						"    ,( SELECT DISTINCT dept_code FROM RICH.RH_ELIG_ACAD_STAFF_V ) " + 
						"    ) BAND 	" + 
						" LEFT JOIN ( " + 
						"    SELECT  " + 
						"            CASE" + 
						"              WHEN COUNT_OUTPUT IS NULL OR COUNT_OUTPUT = 0 THEN '0' " + 
						"              WHEN COUNT_OUTPUT > 0 AND COUNT_OUTPUT < 1 THEN '>0 to <1' " + 
						"              WHEN COUNT_OUTPUT >= 1 AND COUNT_OUTPUT <= 2 THEN '>=1 to 2' " + 
						"              WHEN COUNT_OUTPUT > 2 AND COUNT_OUTPUT <= 3 THEN '>2 to 3' " + 
						"              WHEN COUNT_OUTPUT > 3 AND COUNT_OUTPUT <= 4 THEN '>3 to 4' " + 
						"              WHEN COUNT_OUTPUT > 4 AND COUNT_OUTPUT <= 5 THEN '>4 to 5' " + 
						"              WHEN COUNT_OUTPUT > 5 AND COUNT_OUTPUT <= 6 THEN '>5 to 6' " + 
						"              WHEN COUNT_OUTPUT > 6 THEN 'over 6' " + 
						"          END NO_OF_ITEM_BAND,  " + 
						"        A.dept_code, " + 
						"        A.EMPLOYEE_NUMBER, " + 
						"        B.COUNT_OUTPUT " + 
						"    FROM  ( "     + 
						"    SELECT dept_code, EMPLOYEE_NUMBER FROM RICH.RH_ELIG_ACAD_STAFF_V  ) A " + 
						"    LEFT JOIN	( " + 
						"        SELECT  dept_code, " + 
						"                EMPLOYEE_NUMBER, " + 
						"                ROUND (SUM (CDCF_WEIGHTING), 2) COUNT_OUTPUT " + 
						"       		 FROM RICH.RH_REPORT_STAFF_WEIGHT_V " + 
						"        		 WHERE  ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA') " + 
						"           		 OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA'))  ";
						if (filter.isD_period()) {
							 
							count_query += "        		 AND (  (FROM_YEAR = "+ filter.getD_from_year() +" AND FROM_MONTH >= "+ filter.getD_from_month() 
										 +" ) OR (FROM_YEAR = "+ filter.getD_to_year()+" AND FROM_MONTH <= "+ filter.getD_to_month() +" )) " ;
						}
						if (filter.isP_period()) {
						
							count_query += "   AND  	PERIOD_ID <=  "+ filter.getP_id_no()
									+ "	  AND (  (FROM_YEAR = "+ filter.getP_from_year() +" AND FROM_MONTH >= "+ filter.getP_from_month() +" ) OR (FROM_YEAR = "
									+ filter.getP_to_year()+" AND FROM_MONTH <= "+ filter.getP_to_month() +" )) " ;  
						}
			count_query += "        		 GROUP BY dept_code, EMPLOYEE_NUMBER" + 
						"    ) B" + 
						"    ON A.dept_code = B.dept_code AND A.EMPLOYEE_NUMBER = B.EMPLOYEE_NUMBER	" + 
						") R ON BAND.NO_OF_ITEM_BAND =  R.NO_OF_ITEM_BAND  AND BAND.dept_code = R.dept_code " + 
						"LEFT JOIN " + 
						"RICH.RH_L_ORG_UNIT_V D ON BAND.dept_code = D.LOOKUP_CODE " + 
						"WHERE D.PARENT_LOOKUP_CODE IN ('FLASS', 'FEHD', 'FHM') " + 
						"GROUP BY BAND.NO_OF_ITEM_BAND, BAND.LV, D.PARENT_LOOKUP_CODE, BAND.dept_code " + 
						"ORDER BY DECODE(FACULTY, 'FLASS', 10, 'FEHD', 20, 'FHM', 30), BAND.dept_code, BAND.LV ";	
				
			//System.out.println(count_query);
			conn = pm.getConnection();
				pStmt = conn.prepareStatement(count_query);
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next()) {
					
					FacultyDeptStaffCount vObj = new FacultyDeptStaffCount();
					vObj.setFaculty(rs.getString(1));
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setNo_of_item_band(rs.getString("no_of_item_band"));
					vObj.setLv(Integer.toString(rs.getInt("lv")));
					vObj.setCount_staff(Integer.toString(rs.getInt("count_staff")));			
					voList.add(vObj);

				}
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	public static List<CdcfRptPeriod> getCdcfRptPeriod(Integer period_id)
	{
		List<CdcfRptPeriod> obj = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM CdcfRptPeriod obj where obj.period_id = :period_id";			
			TypedQuery<CdcfRptPeriod> q = em.createQuery(query, CdcfRptPeriod.class);
			q.setParameter("period_id", period_id);
			obj = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return obj;
	}
	
	
	public static List<FacDeptDesc> getDeptDesc (String college,String dept, boolean all, boolean callDept) throws SQLException
	{
		List<FacDeptDesc> voList = new ArrayList<FacDeptDesc>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;		
		
		try
		{	
			String query = "select distinct A.FACULTY, " + 
					"       C.DEPARTMENT_NAME AS FACULTY_NAME, " + 
					"       A.DEPT_CODE, " + 
					"       B.DEPARTMENT_NAME " + 
					"  FROM  " + 
					"    RH_A1_REPORT_V A " + 
					"        INNER JOIN RH_P_DEPARTMENT_V B   ON A.DEPT_CODE = B.DEPARTMENT_CODE " + 
					"        INNER JOIN RH_P_DEPARTMENT_V C   ON A.FACULTY = C.DEPARTMENT_CODE " ;
					if(all == false)
						query += "  WHERE A.FACULTY =  '" + college+ "' ";
					if(callDept == true)
						query += "  WHERE A.DEPT_CODE =  '" + dept+ "' ";
					query += "  ORDER BY DECODE(A.FACULTY, 'FLASS', 10, 'FEHD', 20, 'FHM', 30),A.DEPT_CODE ";			
			
			
			//System.out.println(query)		;
					
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {			
				FacDeptDesc vObj = new FacDeptDesc();
				vObj.setFaculty(rs.getString(1));
				vObj.setFaculty_name(rs.getString(2));
				vObj.setDept_code(rs.getString(3));
				vObj.setDepartment_name(rs.getString(4));
				voList.add(vObj);
			
			}
			
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	public static List<FacDeptDesc> getDeptDescByEligStaffList  () throws SQLException
	{
		List<FacDeptDesc> voList = new ArrayList<FacDeptDesc>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;		
		
		try
		{	
			String query = "select distinct A.DEPARTMENT_CODE,  B.DEPARTMENT_NAME   " + 
					"FROM  RH_S_ELIGIBLE_STAFF_LIST A         " + 
					"INNER JOIN RH_P_DEPARTMENT_V B   ON A.DEPARTMENT_CODE = B.DEPARTMENT_CODE         " + 
					"ORDER BY A.DEPARTMENT_CODE   " ;
					
			
					
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {			
				FacDeptDesc vObj = new FacDeptDesc();
				vObj.setDept_code(rs.getString(1));
				vObj.setDepartment_name(rs.getString(2));
				voList.add(vObj);
			}
			
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	

	
	
	public static List<DeptLabelCount>  getDeptLabelCount (reportFilteringField filter) throws SQLException
	//ToDO: PARA filter object type
	{
		
		List<DeptLabelCount> voList = new ArrayList<DeptLabelCount>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
			try 
			{
				String count_query = "Select dept_code, " + 
						"       label, " + 
						"       sum(count_staff) " + 
						"from " + 
						"( " + 
						"select t.DEPT_CODE, " + 
						"       CASE  " + 
						"         WHEN t.lv <=  2 then 'below 1 item' " + 
						"         ELSE '1 item or above'  " + 
						"            END AS label, " + 
						"       t.no_of_item_band, " + 
						"       t.count_STAFF         " + 
						"         from ( " + 
						"         SELECT " + 
						"    row_number() over ( order by D.PARENT_LOOKUP_CODE ) as report_id, " + 
						"    D.PARENT_LOOKUP_CODE FACULTY,  " + 
						"    BAND.dept_code, " + 
						"    BAND.NO_OF_ITEM_BAND,  " + 
						"    BAND.LV, " + 
						"    COUNT(DISTINCT EMPLOYEE_NUMBER) COUNT_STAFF  " + 
						"FROM  " + 
						"    ( " + 
						"        SELECT *  FROM ( " + 
						"            SELECT  CASE LEVEL  " + 
						"                      WHEN 1 THEN '0' " + 
						"                      WHEN 2 THEN '>0 to <1' " + 
						"                      WHEN 3 THEN '>=1 to 2' " + 
						"                      WHEN 4 THEN '>2 to 3' " + 
						"                      WHEN 5 THEN '>3 to 4' " + 
						"                      WHEN 6 THEN'>4 to 5' " + 
						"                      WHEN 7 THEN '>5 to 6' " + 
						"                      WHEN 8 THEN 'over 6' " + 
						"                  END NO_OF_ITEM_BAND, LEVEL LV " + 
						"            FROM DUAL " + 
						"            CONNECT BY LEVEL <= 8 " + 
						"        )  " + 
						"        ,(SELECT DISTINCT dept_code FROM RH_ELIG_ACAD_STAFF_V)  " + 
						"        ) BAND 	 " + 
						"    LEFT JOIN	 " + 
						"    ( " + 
						"        SELECT  " + 
						"                CASE " + 
						"                  WHEN COUNT_OUTPUT IS NULL OR COUNT_OUTPUT = 0 THEN '0' " + 
						"                  WHEN COUNT_OUTPUT > 0 AND COUNT_OUTPUT < 1 THEN '>0 to <1' " + 
						"                  WHEN COUNT_OUTPUT >= 1 AND COUNT_OUTPUT <= 2 THEN '>=1 to 2' " + 
						"                  WHEN COUNT_OUTPUT > 2 AND COUNT_OUTPUT <= 3 THEN '>2 to 3' " + 
						"                  WHEN COUNT_OUTPUT > 3 AND COUNT_OUTPUT <= 4 THEN '>3 to 4' " + 
						"                  WHEN COUNT_OUTPUT > 4 AND COUNT_OUTPUT <= 5 THEN '>4 to 5' " + 
						"                  WHEN COUNT_OUTPUT > 5 AND COUNT_OUTPUT <= 6 THEN '>5 to 6' " + 
						"                  WHEN COUNT_OUTPUT > 6 THEN 'over 6' " + 
						"              END NO_OF_ITEM_BAND	, " + 
						"            A.dept_code, " + 
						"            A.EMPLOYEE_NUMBER, " + 
						"            B.COUNT_OUTPUT " + 
						"        FROM  " + 
						"        (   SELECT dept_code, EMPLOYEE_NUMBER " + 
						"            FROM RH_ELIG_ACAD_STAFF_V " + 
						"        ) A	 " + 
						"        LEFT JOIN	 " + 
						"        ( " + 
						"            SELECT  dept_code, " + 
						"                    EMPLOYEE_NUMBER, " + 
						"                    ROUND (SUM (CDCF_WEIGHTING), 2) COUNT_OUTPUT " + 
						"            FROM RH_REPORT_STAFF_WEIGHT_V " + 
						"            WHERE   ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA') " + 
						"            	OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA'))  " ;
						if (filter.isD_period()) {
							 
							count_query += "        		 AND (  (FROM_YEAR = "+ filter.getD_from_year() +" AND FROM_MONTH >= "+ filter.getD_from_month() 
										 +" ) OR (FROM_YEAR = "+ filter.getD_to_year()+" AND FROM_MONTH <= "+ filter.getD_to_month() +" )) " ;
						}
						if (filter.isP_period()) {
						
							count_query += "   AND  PERIOD_ID <= "+ filter.getP_id_no()
									+ "    AND (  (FROM_YEAR = "+ filter.getP_from_year() +" AND FROM_MONTH >= "+ filter.getP_from_month() +" ) OR (FROM_YEAR = "
									+ filter.getP_to_year() + " AND FROM_MONTH <= "+ filter.getP_to_month() +" )) " ;  
						}
						count_query += "            GROUP BY dept_code, EMPLOYEE_NUMBER " + 
						"        ) B " + 
						"        ON A.dept_code = B.dept_code AND A.EMPLOYEE_NUMBER = B.EMPLOYEE_NUMBER	 " + 
						"    ) R ON BAND.NO_OF_ITEM_BAND = R.NO_OF_ITEM_BAND AND BAND.dept_code = R.dept_code " + 
						"    LEFT JOIN  " + 
						"    RICH.RH_L_ORG_UNIT_V D ON BAND.dept_code = D.LOOKUP_CODE  " + 
						"    WHERE D.PARENT_LOOKUP_CODE IN ('FLASS', 'FEHD', 'FHM') " + 
						"    GROUP BY BAND.NO_OF_ITEM_BAND, BAND.LV, D.PARENT_LOOKUP_CODE, BAND.dept_code " + 
						"    ORDER BY DECODE(FACULTY, 'FLASS', 10, 'FEHD', 20, 'FHM', 30), BAND.dept_code, BAND.LV  )t " + 
						"         order by t.faculty) " + 
						"  	group by dept_code, label " + 
						" 	order by dept_code ";	
				
				//System.out.println(count_query);
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(count_query);
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next()) {			
					DeptLabelCount vObj = new DeptLabelCount();
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setLabel(rs.getString("label"));
					vObj.setCount_staff(rs.getString(3));
					voList.add(vObj);
				}
				
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	public static String lastYearCountDeptQuery (String itemsValue,  reportFilteringField filter, int last_year) {
		String count_query = " Select * from ( " + 
				"	SELECT  " + 
				"	A.dept_code, " + 
				"	A.EMPLOYEE_NUMBER, " 	+ 
				"	B.COUNT_OUTPUT, " 		+
				"	RPT.PERIOD_ID	" 		+
				"	FROM  " + 
				"	(   SELECT dept_code, EMPLOYEE_NUMBER , START_PERIOD " + 
				"	FROM RICH.RH_ELIG_ACAD_STAFF_V " + 
				"	) A	 " +
				"	LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RPT on RPT.CHART_DESC_2 = A.START_PERIOD  " +
				"	LEFT JOIN	 " + 
				"	( " + 
				"	Select * from ( " + 
				"    SELECT  dept_code, " + 
				"            EMPLOYEE_NUMBER, " + 
				"            ROUND (SUM (CDCF_WEIGHTING), 2) COUNT_OUTPUT " + 
				"    FROM RICH.RH_REPORT_STAFF_WEIGHT_V " + 
				"    WHERE  " + 
				"         ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA') " + 
				"            OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA'))  " ;
				
				if (filter.isD_period()) {
					 
					count_query += "        		 AND (  (FROM_YEAR = "+ filter.getD_from_year() +" AND FROM_MONTH >= "+ filter.getD_from_month() 
								 +" ) OR (FROM_YEAR = "+ filter.getD_to_year()+" AND FROM_MONTH <= "+ filter.getD_to_month() +" )) " ;
				}
				if (filter.isP_period()) {
				
					count_query += "   AND PERIOD_ID <= " + (filter.getP_id_no() - last_year)
								+ "		 AND (  (FROM_YEAR = "+ (filter.getP_from_year() - last_year) 
								+"  AND FROM_MONTH >= "+ filter.getP_from_month() +" ) OR (FROM_YEAR = "
							+ (filter.getP_to_year() - last_year)+" AND FROM_MONTH <= "+ filter.getP_to_month() +" )) " ;  
				}
				
				count_query +=  
				"   GROUP BY dept_code, EMPLOYEE_NUMBER ))  B  " + 
				"	ON A.dept_code = B.dept_code AND A.EMPLOYEE_NUMBER = B.EMPLOYEE_NUMBER )   " + 
				"	where (COUNT_OUTPUT is null OR COUNT_OUTPUT  "+ itemsValue  + ") AND PERIOD_ID <= " +(filter.getP_id_no() - last_year);
		return count_query;
	}
	
	public static List<last3YearCount> lastYearCount_Dept (String itemsValue, reportFilteringField filter ) throws SQLException
	//ToDO: PARA filter object type
	{
		
		List<last3YearCount> voList = new ArrayList<last3YearCount>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
			try 
			{
				String count_query = "Select dept_code,count(employee_number) from ( " + 
						" select distinct dept_code, employee_number from ( " + 
						" Select   row_number() over ( PARTITION BY row_t.DEPT_CODE, row_t.EMPLOYEE_NUMBER order by row_t.DEPT_CODE, row_t.EMPLOYEE_NUMBER ) as repeat_id, row_t.*  " + 
						" from (  " + 
						lastYearCountDeptQuery(itemsValue,filter,0) 
						+ "	 UNION ALL " + 
						lastYearCountDeptQuery(itemsValue,filter,1) 
						+   "	UNION ALL " + 
						lastYearCountDeptQuery(itemsValue,filter,2) 
						+ "  ) row_t)  " + 
						"	 where repeat_id = 3  " + 
						"	 ) group by dept_code ";
				
				//System.out.println(count_query)	;		
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(count_query);
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next()) {			
					last3YearCount vObj = new last3YearCount();
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setCount_staff(rs.getString(2));
					voList.add(vObj);
				}
				
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	
	public static List<deptFundingIntExt> deptFunding_Int_Ext (reportFilteringField filter,boolean b2, int i_s_period ) throws SQLException
	//ToDO: PARA filter object type
	{
		
		List<deptFundingIntExt> voList = new ArrayList<deptFundingIntExt>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
			try 
			{
				String count_query = "	Select DEPT_CODE,  		" + 
						"       Fund_type,  	" + 
						"       COUNT (PROJECT_NO) as count,  	" + 
						"       SUM (SAP_GRANT_AMT) as amount  	" + 
						"	from (  		" + 
						"	SELECT * from  	" ;
				if (b2)
					count_query +=	"        RH_B2_REPORT_BASE_V  " ;
				else
					count_query +=	"        RH_B3_REPORT_BASE_V  " ;
	
			 count_query +=	"        WHERE FUND_TYPE is not null    " + 
			 			"        				AND PERIOD_ID = "+i_s_period+"   " + 
						"	)  " + 
						"	GROUP by DEPT_CODE,	Fund_type  " + 
						" 	ORDER BY DEPT_CODE 	";
			 	
			 	//System.out.println(count_query);
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(count_query);
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next()) {			
					deptFundingIntExt vObj = new deptFundingIntExt();
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setFund_type(rs.getString(2));
					vObj.setCount(rs.getString(3));
					vObj.setAmount(rs.getString(4));
					voList.add(vObj);
				}
				
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	
	
	public static List<String >  getRptNote (String rpt_code) throws SQLException
	//ToDO: PARA filter object type
	{
		List<String> voList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String query = "select t.rpt_note from rh_z_pre_rpt t where t.rpt_code = '"+rpt_code+"' ";
			
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {
						
				voList.add(rs.getString(1));
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	@SuppressWarnings("static-access")
	public static List<String>  getAccessFacultyDeptList(List<String> accessList) throws SQLException
	{
		List<String> voList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String query = " select DEPT_CODE, FACULTY from RICH.RH_A1_REPORT_V t "
					+ " where t.dept_code IN ( '" + accessList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) "
					+ "  group by FACULTY, DEPT_CODE ORDER BY DECODE(FACULTY, 'FLASS', 10, 'FEHD', 20, 'FHM', 30), dept_code ";
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
		
			while (rs.next()) {
						
				voList.add(rs.getString(1));
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	
	
	public static List<String>  getALLFacultyDeptList() throws SQLException
	{
		List<String> voList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String query = " select DEPT_CODE, FACULTY from RICH.RH_A1_REPORT_V t "
					+ " group by FACULTY, DEPT_CODE "
					+ " ORDER BY DECODE(FACULTY, 'FLASS', 10, 'FEHD', 20, 'FHM', 30), dept_code ";
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
		
			while (rs.next()) {
						
				voList.add(rs.getString(1));
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	
	
	
	
	
	public static List<String>  getAccessIconnectUnitList(List<String> accessList) throws SQLException
	{
		List<String> voList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String query = " select FAC_DEPT from RICH.RH_Z_FAC_DEPT_V t "
					+ " where t.FAC_DEPT !='OTHERS' AND t.FAC_DEPT  "
					+ "IN ( '" + accessList.stream().map(String::valueOf).collect(Collectors.joining("','")) + "' ) ";

			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {
						
				voList.add(rs.getString(1));
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	
	
	public static List<String >  getFacultyDept (String college) throws SQLException
	//ToDO: PARA filter object type
	{
		List<String> voList = new ArrayList<String>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String query = " select DEPT_CODE from RICH.RH_A1_REPORT_V t "
					+ "  where FACULTY = '"+college+"' "
					+ "  group by FACULTY, DEPT_CODE Order by FACULTY,DEPT_CODE ";
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {
						
				voList.add(rs.getString(1));
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	public static String getStaffProjCount_query ( String fund_source, String label, String new_project,reportFilteringField filter, boolean all) {
		String count_query  = "   ( " + 
				"    SELECT  DEPT_CODE, ";
				if (! all) 
					count_query += 	"    EMPLOYEE_NUMBER, 	" + 	"  	 NEW_PROJECT , 		" ;
				
				count_query +=
				"            COUNT(project_no) AS proj_count, 	" + 
				"            SUM(sap_grant_amt) AS proj_amt 	" + 
				"    FROM RH_B4_REPORT_BASE_V  " + 
				"    WHERE PERIOD_ID = "+filter.getP_id_no()+"   " +
				"	 AND sap_funding_source IN ("+fund_source+")   " ;
				if (all) { 
					count_query += 	"  GROUP BY DEPT_CODE ) "+label+" " + 
							"    on staff.DEPT_CODE = "+label+".DEPT_CODE   ";	
				}
				else {
					count_query += 	"    AND  NEW_PROJECT = '"+new_project+"' " +
					"    GROUP BY DEPT_CODE, EMPLOYEE_NUMBER,NEW_PROJECT  ) "+label+" " + 
					"    on staff.EMPLOYEE_NUMBER = "+label+".EMPLOYEE_NUMBER   ";
				}
				
		return count_query;
	}
	
	public static String getStaffProjCount_query_b ( String label,String label_2, String label_3, String label_4) {
		String count_query  = "	nvl("+label+".proj_count,0)  	as 	"+label+"c,  " 			+ 
				"				nvl("+label+".proj_amt,0)   	as  "+label+"m,  " +
				
				"				nvl("+label_2+".proj_count,0) 	as 	"+label_2+"c,  " 	+ 
				"				nvl("+label_2+".proj_amt,0)    	as  "+label_2+"m,  " + 
				"				nvl("+label_3+".proj_count,0)	as 	"+label_3+"c,  " 	+ 
				"				nvl("+label_3+".proj_amt,0)   	as  "+label_3+"m,  "	+
				
				"				nvl("+label_4+".proj_count,0) 	as "+label_4+"c,  " 	+ 
				"				nvl("+label_4+".proj_amt,0)   	as "+label_4+"m,  "	;
		return count_query;
	}
	
	public static String subStringB4 (reportFilteringField filter,String i_new_project, boolean all) {
		String sub_query = "   SELECT  staff.dept_code,  		" ;
		if (!all) {
			sub_query +=	"        staff.LAST_NAME,  		" + 
				"        staff.FIRST_NAME,  	" + 
				"        Staff.POST_RANK_CODE,  " ;
		}
		else
			sub_query = "   SELECT distinct staff.dept_code,  	" ;
			sub_query += 	getStaffProjCount_query_b("a","b","c","d")+
							getStaffProjCount_query_b("e","f","g","h")+
							getStaffProjCount_query_b("i","j","k","l")+ 
							getStaffProjCount_query_b("m","n","o","p")+  
							"        nvl(q.proj_count,0) 	as qc,  " + 
							"        nvl(q.proj_amt,0)	as qm,  	" + 
							"        nvl(r.proj_count,0)	as rc,  " + 
							"        nvl(r.proj_amt,0)	as rm  		" ;
		 if (!all) {
			if (i_new_project == "Y") {
				sub_query		+=
						"      ,   CASE " + 
						"            WHEN  'Y' IN  (a.NEW_PROJECT) THEN 'Y'	" 	+ 
						"            ELSE 'Y' " + 
						"        END AS NEW_PROJECT, " +
						"		 staff.EMPLOYEE_NUMBER  	" ;
			}
			else {
				sub_query		+=
						"      ,   CASE " + 
						"            WHEN  'N' IN  (a.NEW_PROJECT) THEN 'N'	" 	+ 
						"            ELSE 'N' " + 
						"        END AS NEW_PROJECT, " +
						"		 staff.EMPLOYEE_NUMBER  	" ;
			}
		 }
			sub_query +=
					"  from  RH_ELIG_ACAD_STAFF_V staff  	" + 
					"  LEFT JOIN "+getStaffProjCount_query("'9997'","a",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'10005'","b",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'10007','10006','10012','250','10021','150','10010','10014','10008','10011','10019','10009','10013','10004','10032'","c",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'310'","d",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'925','510'","e",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'10001'","f",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'1621','10023','10024','10025','1635','710','10001','10022','10026','10027'","g",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'10028','10029','10030','10031','1680','1585'","h",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'900'","i",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'830'","j",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'880'","k",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'1400'","l",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'1581','1015','10003'","m",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'890','920','10016','10020','10017','9998'","n",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'1600','950','980'","o",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'940','935'","p",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'10002'","q",i_new_project,filter, all ) +"  "	+
					"  LEFT JOIN "+getStaffProjCount_query("'1100'","r",i_new_project,filter, all );
			return sub_query;
	}
	
	
	public static List<StaffProjCount >  getStaffProjCount ( reportFilteringField filter, String i_dept_code,boolean all) throws SQLException
	//ToDO: PARA filter object type
	{
		List<StaffProjCount> voList =  new ArrayList();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String  query = "Select * from ( " ;
			String 	sub_query = subStringB4(filter,"Y",false) + "  UNION  "+ subStringB4(filter,"N",false) ;

			if (all)
				sub_query = subStringB4 (filter,"",true);
			if ( !all) {
				query +=  sub_query +"  )  WHERE DEPT_CODE = '"+i_dept_code+"'  " + 
						"   ORDER BY DEPT_CODE, LAST_NAME,FIRST_NAME, NEW_PROJECT  ";
			}
			else {
				query +=  sub_query +"  )  "
						+ "WHERE DEPT_CODE NOT IN ('AHKS','FLASS') ORDER BY DEPT_CODE ";
			}
					
			//System.out.println(query);
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			if ( !all) {
				while (rs.next()) {
							
					StaffProjCount vObj = new StaffProjCount();
					
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setLast_name(rs.getString(2));
					vObj.setFirst_name(rs.getString(3));
					vObj.setPost_rank_code(rs.getString(4));
					vObj.setAc(rs.getString(5));
					vObj.setAm(rs.getString(6));
					vObj.setBc(rs.getString(7));
					vObj.setBm(rs.getString(8));
					vObj.setCc(rs.getString(9));
					vObj.setCm(rs.getString(10));
					vObj.setDc(rs.getString(11));
					vObj.setDm(rs.getString(12));
					vObj.setEc(rs.getString(13));
					vObj.setEm(rs.getString(14));
					vObj.setFc(rs.getString(15));
					vObj.setFm(rs.getString(16));
					vObj.setGc(rs.getString(17));
					vObj.setGm(rs.getString(18));
					vObj.setHc(rs.getString(19));
					vObj.setHm(rs.getString(20));
					vObj.setIc(rs.getString(21));
					vObj.setIm(rs.getString(22));
					vObj.setJc(rs.getString(23));
					vObj.setJm(rs.getString(24));
					vObj.setKc(rs.getString(25));
					vObj.setKm(rs.getString(26));
					vObj.setLc(rs.getString(27));
					vObj.setLm(rs.getString(28));
					vObj.setMc(rs.getString(29));
					vObj.setMm(rs.getString(30));
					vObj.setNc(rs.getString(31));
					vObj.setNm(rs.getString(32));
					vObj.setOc(rs.getString(33));
					vObj.setOm(rs.getString(34));
					vObj.setPc(rs.getString(35));
					vObj.setPm(rs.getString(36));
					vObj.setQc(rs.getString(37));
					vObj.setQm(rs.getString(38));
					vObj.setRc(rs.getString(39));
					vObj.setRm(rs.getString(40));
					vObj.setNew_proj(rs.getString(41));
					vObj.setEmployee_number(rs.getString(42));
					
					voList.add(vObj);
				}
			}
			else {
					while (rs.next()) {
						
						StaffProjCount vObj = new StaffProjCount();
						
						vObj.setDept_code(rs.getString("dept_code"));
						vObj.setAc(rs.getString(2));
						vObj.setAm(rs.getString(3));
						vObj.setBc(rs.getString(4));
						vObj.setBm(rs.getString(5));
						vObj.setCc(rs.getString(6));
						vObj.setCm(rs.getString(7));
						vObj.setDc(rs.getString(8));
						vObj.setDm(rs.getString(9));
						vObj.setEc(rs.getString(10));
						vObj.setEm(rs.getString(11));
						vObj.setFc(rs.getString(12));
						vObj.setFm(rs.getString(13));
						vObj.setGc(rs.getString(14));
						vObj.setGm(rs.getString(15));
						vObj.setHc(rs.getString(16));
						vObj.setHm(rs.getString(17));
						vObj.setIc(rs.getString(18));
						vObj.setIm(rs.getString(19));
						vObj.setJc(rs.getString(20));
						vObj.setJm(rs.getString(21));
						vObj.setKc(rs.getString(22));
						vObj.setKm(rs.getString(23));
						vObj.setLc(rs.getString(24));
						vObj.setLm(rs.getString(25));
						vObj.setMc(rs.getString(26));
						vObj.setMm(rs.getString(27));
						vObj.setNc(rs.getString(28));
						vObj.setNm(rs.getString(29));
						vObj.setOc(rs.getString(30));
						vObj.setOm(rs.getString(31));
						vObj.setPc(rs.getString(32));
						vObj.setPm(rs.getString(33));
						vObj.setQc(rs.getString(34));
						vObj.setQm(rs.getString(35));
						vObj.setRc(rs.getString(36));
						vObj.setRm(rs.getString(37));
						voList.add(vObj);
					}
			}
			
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	
	public static List<a4ReportField >  getStaffProjCount_enh ( reportFilteringField filter, String i_dept_code,String fund_source, boolean all) throws SQLException
	//ToDO: PARA filter object type
	{
		List<a4ReportField> voList =  new ArrayList();
		
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String  query = "Select * from (  SELECT  staff.dept_code, staff.LAST_NAME, staff.FIRST_NAME, staff.POST_RANK_CODE,  " + 
					"    nvl(a.proj_count,0) as  ac, nvl(a.proj_amt,0)   as  am, 'Y' AS NEW_PROJECT,	" + 
					"    staff.EMPLOYEE_NUMBER  	  		" + 
					"from  RH_ELIG_ACAD_STAFF_V staff  	  	" + 
					"LEFT JOIN    (  SELECT  DEPT_CODE,    EMPLOYEE_NUMBER, 	   NEW_PROJECT , 	COUNT(project_no) AS proj_count, 	 SUM(sap_grant_amt) AS proj_amt 	" + 
					"                FROM RH_B4_REPORT_BASE_V      "+
					"    			 WHERE PERIOD_ID = 		"+	filter.getP_id_no()+"   " +
					"	             AND sap_funding_source IN ("+fund_source+")   " + 
					"				 AND  NEW_PROJECT = 'Y'     				" + 
					"                GROUP BY DEPT_CODE, EMPLOYEE_NUMBER,NEW_PROJECT  ) a     on staff.EMPLOYEE_NUMBER = a.EMPLOYEE_NUMBER       							" + 
					"UNION           " + 
					"SELECT  staff.dept_code,  staff.LAST_NAME,   staff.FIRST_NAME,   staff.POST_RANK_CODE,		" + 
					"nvl(a.proj_count,0) as 	ac, nvl(a.proj_amt,0)  as  am, 'N'  AS NEW_PROJECT, 		 	" + 
					"staff.EMPLOYEE_NUMBER  	  			" + 
					"from  RH_ELIG_ACAD_STAFF_V staff  	  	" + 
					"LEFT JOIN    (  SELECT  DEPT_CODE,   EMPLOYEE_NUMBER,  NEW_PROJECT , COUNT(project_no) AS proj_count, SUM(sap_grant_amt) AS proj_amt 	    	" + 
					"                FROM RH_B4_REPORT_BASE_V      " +
					"    			 WHERE PERIOD_ID = 		"+	filter.getP_id_no()+"   		" +
					"	             AND sap_funding_source IN ("+fund_source+")   				" + 
					"				 AND  NEW_PROJECT = 'N'     								" + 
					"                GROUP BY DEPT_CODE, EMPLOYEE_NUMBER,NEW_PROJECT  ) a     	" + 
					"                on staff.EMPLOYEE_NUMBER = a.EMPLOYEE_NUMBER       )  		" + 
					"WHERE DEPT_CODE =  '"+i_dept_code+"'   ORDER BY DEPT_CODE, LAST_NAME,FIRST_NAME, NEW_PROJECT 	" ;
			
			if (all) {
				query = "  		Select  * from (   " + 
						"            SELECT  distinct staff.dept_code, " + 
						"                    nvl(a.proj_count,0) as  ac, " + 
						"                    nvl(a.proj_amt,0)   as  am	" + 
						"            from  RH_ELIG_ACAD_STAFF_V staff  	  	" + 
						"            LEFT JOIN    (  " + 
						"            SELECT  DEPT_CODE,  " + 
						"            COUNT(project_no) AS proj_count, 	 " + 
						"            SUM(sap_grant_amt) AS proj_amt 	               "+ 
						"            FROM RH_B4_REPORT_BASE_V  					"    	+
						"    	     WHERE PERIOD_ID = 		"+	filter.getP_id_no()+"   		" +
						"			 AND sap_funding_source IN ("+fund_source+")   				" + 
						"            GROUP BY DEPT_CODE  ) a    on staff.DEPT_CODE = a.DEPT_CODE		 " + 
						"        )						" + 
						"  		WHERE DEPT_CODE NOT IN ('AHKS','FLASS')    " + 
						"  		ORDER BY DEPT_CODE  ";
			}
			
			
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			if(!all) {
				while (rs.next()) {
					List <a4ReportCountAmt> vObj_in_list = new ArrayList();	
					a4ReportField vObj = new a4ReportField();
					a4ReportCountAmt vObj_in = new a4ReportCountAmt();
					
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setLast_name(rs.getString(2));
					vObj.setFirst_name(rs.getString(3));
					vObj.setPost_rank_code(rs.getString(4));
					
					//Create a new a4ReportCountAmt obj, then add it into a list, then add it into a field list
					vObj_in.setCount(rs.getString(5));
					vObj_in.setAmount(rs.getString(6));
					vObj_in_list.add(vObj_in);
					vObj.setFund_source(vObj_in_list);
					vObj.setNew_project(rs.getString(7));
					vObj.setEmployee_number(rs.getString(8));
					voList.add(vObj);
				}
			}
			else {
				while (rs.next()) {
					
					List <a4ReportCountAmt> vObj_in_list = new ArrayList();	
					a4ReportField vObj = new a4ReportField();
					a4ReportCountAmt vObj_in = new a4ReportCountAmt();
					
					vObj.setDept_code(rs.getString("dept_code"));
					//Create a new a4ReportCountAmt obj, then add it into a list, then add it into a field list
					vObj_in.setCount(rs.getString(2));
					vObj_in.setAmount(rs.getString(3));
					vObj_in_list.add(vObj_in);
					vObj.setFund_source(vObj_in_list);
					voList.add(vObj);
				}
				
			}
			
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}
	
	
	
	public static List<employeeCountGrade> getEmployeeCountGrade ( reportFilteringField filter ) throws SQLException
	{
		
		List<employeeCountGrade> voList = new ArrayList();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
			try 
			{
				String count_query = "  SELECT  " + 
						"        A.dept_code,  " + 
						"        A.LAST_NAME,  " + 
						"        A.FIRST_NAME,  " + 
						"        A.POST_RANK_CODE,  " + 
						"        NVL(B.COUNT_OUTPUT,0),  " + 
						"         CASE  " + 
						"              WHEN COUNT_OUTPUT IS NULL OR COUNT_OUTPUT = 0 THEN 'A \"0\"'  " + 
						"              WHEN COUNT_OUTPUT > 0 AND COUNT_OUTPUT < 1 THEN 'B \">0 to <1 \"'  " + 
						"              WHEN COUNT_OUTPUT >= 1 AND COUNT_OUTPUT <= 2 THEN 'C \" >= 1 to 2 \"'  " + 
						"              WHEN COUNT_OUTPUT > 2 AND COUNT_OUTPUT <= 3 THEN 'D \"> 2 to 3 \"'  " + 
						"              WHEN COUNT_OUTPUT > 3 AND COUNT_OUTPUT <= 4 THEN 'E \">3 to 4\"'  " + 
						"              WHEN COUNT_OUTPUT > 4 AND COUNT_OUTPUT <= 5 THEN 'F \">4 to 5\"'  " + 
						"              WHEN COUNT_OUTPUT > 5 AND COUNT_OUTPUT <= 6 THEN 'G \">5 to 6\"'  " + 
						"              WHEN COUNT_OUTPUT > 6 THEN 'H \"over 6\"' " + 
						"          END NO_OF_ITEM_BAND " + 
						"    FROM  " + 
						"    (   SELECT dept_code,  " + 
						"           EMPLOYEE_NUMBER, " + 
						"           LAST_NAME, " + 
						"           FIRST_NAME, " + 
						"           POST_RANK_CODE " + 
						"     FROM RH_ELIG_ACAD_STAFF_V " + 
						"   ) A	 " + 
						"  LEFT JOIN	 " + 
						"  ( " + 
						"    SELECT  dept_code, " + 
						"            EMPLOYEE_NUMBER, " + 
						"            ROUND (SUM (CDCF_WEIGHTING), 2) COUNT_OUTPUT " + 
						"    FROM RH_REPORT_STAFF_WEIGHT_V " + 
						"    WHERE  ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')  " + 
						"             OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA'))  " ;
						if (filter.isD_period()) {
							 
							count_query += "        		 AND (  (FROM_YEAR = "+ filter.getD_from_year() +" AND FROM_MONTH >= "+ filter.getD_from_month() 
										 +" ) OR (FROM_YEAR = "+ filter.getD_to_year()+" AND FROM_MONTH <= "+ filter.getD_to_month() +" )) " ;
						}
						if (filter.isP_period()) {
						
							count_query += "   		 AND (  (FROM_YEAR = "+ filter.getP_from_year() + "  AND FROM_MONTH >= "+ filter.getP_from_month() 
									+" ) OR (FROM_YEAR = " +  filter.getP_to_year() +" AND FROM_MONTH <= "
									+ filter.getP_to_month() +" )) " ;  
						}
						count_query += 
						"    GROUP BY dept_code, EMPLOYEE_NUMBER  " + 
						"  ) B " + 
						"  ON A.dept_code = B.dept_code AND A.EMPLOYEE_NUMBER = B.EMPLOYEE_NUMBER	 " + 
						"  ORDER by A.dept_code, A.LAST_NAME, A.FIRST_NAME  " ;
				
								
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(count_query);
				ResultSet rs = pStmt.executeQuery();
				
				while (rs.next()) {			
					employeeCountGrade vObj = new employeeCountGrade();
					vObj.setDept_code(rs.getString("dept_code"));
					vObj.setLast_name(rs.getString(2));
					vObj.setOther_name(rs.getString(3));
					vObj.setPost_rank_code(rs.getString(4));
					vObj.setTotal_number(rs.getString(5));
					vObj.setGrade(rs.getString(6));
					voList.add(vObj);
				}
				
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
			
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	public static String employeeCount_details_query (String outputType, String researchType, String label, reportFilteringField filter) {
		String count_query  = "  (        " + 
				"   Select * from (        " + 
				"	  SELECT  dept_code,        " + 
				"			  EMPLOYEE_NUMBER,  " + 
				"              PARENT_LOOKUP_CODE as output_type,  " + 
				"              SAP_REFERED_JOURNAL AS research_type,  " + 
				"			  SUM (WEIGHTING) COUNT_OUTPUT        " + 
				"	  FROM RH_REPORT_STAFF_WEIGHT_NREF_V        " + 
				"	  WHERE  PARENT_LOOKUP_CODE = '"+outputType +"'  " + 
				"           AND SAP_REFERED_JOURNAL = '"+researchType+"'  " ;
				if (filter.isD_period()) {
					 
					count_query += "        		 AND (  (FROM_YEAR = "+ filter.getD_from_year() +" AND FROM_MONTH >= "+ filter.getD_from_month() 
								 +" ) OR (FROM_YEAR = "+ filter.getD_to_year()+" AND FROM_MONTH <= "+ filter.getD_to_month() +" )) " ;
				}
				if (filter.isP_period()) {
				
					count_query += "   AND PERIOD_ID <= " + filter.getP_id_no()   		 
							+ "    AND (  (FROM_YEAR = "+ filter.getP_from_year() + "  AND FROM_MONTH >= "+ filter.getP_from_month() 
							+"     ) OR (FROM_YEAR = " +  filter.getP_to_year() +" AND FROM_MONTH <= "
							+ filter.getP_to_month() +" )) " ;  
				}
			
				count_query +=
				"	  GROUP BY dept_code, EMPLOYEE_NUMBER ,PARENT_LOOKUP_CODE,  SAP_REFERED_JOURNAL   " + 
				"      )        " + 
				"  ) "+label+" on A.dept_code = "+label+".dept_code AND A.EMPLOYEE_NUMBER = "+label+".EMPLOYEE_NUMBER  ";
		return count_query;
	}
	
	public static List<employeeCount_detail>  getEmployeeCount_details (reportFilteringField filter) throws SQLException
	{
		List<employeeCount_detail> voList = new ArrayList<employeeCount_detail>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		
		try 
		{
			String count_query = " Select * from (        " + 
					"  SELECT         " + 
					"  A.dept_code,        " + 
					"  A.EMPLOYEE_NUMBER,        " + 
					"  UPPER (A.LAST_NAME) as Last_name,        " + 
					"  A.FIRST_NAME,  " + 
					"  A.POST_RANK_CODE ,    " + 
					"  NVL (sr.COUNT_OUTPUT,0.00) as sr_output,  " + 
					"  NVL (snr.COUNT_OUTPUT,0.00) as snr_output,  " + 
					"  NVL (sc.COUNT_OUTPUT,0.00) as sc_output,  " + 
					"  NVL (so.COUNT_OUTPUT,0.00) as so_output,  " + 
					"    " + 
					"  NVL (cr.COUNT_OUTPUT,0.00)  as cr_output,  " + 
					"  NVL (cnr.COUNT_OUTPUT,0.00) as cnr_output,  " + 
					"  NVL (cc.COUNT_OUTPUT,0.00)  as cc_output,  " + 
					"  NVL (co.COUNT_OUTPUT,0.00)  as co_output,  " + 
					"    " + 
					"  NVL (dr.COUNT_OUTPUT,0.00)  as dr_output,  " + 
					"  NVL (dnr.COUNT_OUTPUT,0.00) as dnr_output,  " + 
					"  NVL (dc.COUNT_OUTPUT,0.00)  as dc_output,  " + 
					"  NVL (do.COUNT_OUTPUT,0.00)  as do_output,  " + 
					"    " + 
					"  NVL (er.COUNT_OUTPUT,0.00)  as er_output,  " + 
					"  NVL (enr.COUNT_OUTPUT,0.00) as enr_output,  " + 
					"  NVL (ec.COUNT_OUTPUT,0.00)  as ec_output,  " + 
					"  NVL (eo.COUNT_OUTPUT,0.00)  as eo_output,  " + 
					"    " + 
					"  NVL (fr.COUNT_OUTPUT,0.00)  as fr_output,  " + 
					"  NVL (fnr.COUNT_OUTPUT,0.00) as fnr_output,  " + 
					"  NVL (fc.COUNT_OUTPUT,0.00)  as fc_output,  " + 
					"  NVL (fo.COUNT_OUTPUT,0.00)  as fo_output,  " + 
					"    " + 
					"  NVL (gr.COUNT_OUTPUT,0.00)  as gr_output,  " + 
					"  NVL (gnr.COUNT_OUTPUT,0.00) as gnr_output,  " + 
					"  NVL (gc.COUNT_OUTPUT,0.00)  as gc_output,  " + 
					"  NVL (go.COUNT_OUTPUT,0.00)  as go_output  " + 
					"  FROM         " + 
					"  (   SELECT dept_code,         " + 
					"	  LAST_NAME,        " + 
					"	  FIRST_NAME,        " + 
					"	  POST_RANK_CODE,  " + 
					"	  EMPLOYEE_NUMBER     " + 
					"  FROM RH_ELIG_ACAD_STAFF_V        " + 
					"  ) A	        " + 
					"  LEFT JOIN	        " +
					employeeCount_details_query ("1100","Y","SR",filter) +
					"    LEFT JOIN	        " + 
					employeeCount_details_query ("1100","N","SNR",filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1100","C","SC",filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1100","99","SO",filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1200","Y","CR",filter) +
					"  LEFT JOIN	        " +
					employeeCount_details_query ("1200","N","CNR",filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1200","C","CC",filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1200","99","CO",filter) 	 +  
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1300","Y", "DR",filter) 	 +
					"    LEFT JOIN	        " +
					employeeCount_details_query ("1300","N", "DNR",filter) 	 +
					"  LEFT JOIN	        " +
					employeeCount_details_query ("1300","C", "DC",filter) 	 +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1300","99", "DO",filter) 	 +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1400","Y",  "ER", filter)  +
					"      LEFT JOIN	        " + 
					employeeCount_details_query ("1400","N",  "ENR", filter) +
					"  LEFT JOIN	        " +
					employeeCount_details_query ("1400","C",  "EC", filter)  + 
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1400","99",  "EO", filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1500","Y",   "FR", filter) +
					"   LEFT JOIN	        " + 
					employeeCount_details_query ("1500","N",   "FNR", filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1500","C",   "FC", filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1500","99",  "FO", filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1600","Y",  "GR", filter) +
					"   LEFT JOIN	        " + 
					employeeCount_details_query ("1600","N",  "GNR", filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1600","C",  "GC", filter) +
					"  LEFT JOIN	        " + 
					employeeCount_details_query ("1600","99",  "GO", filter) +
					"  WHERE A.DEPT_CODE NOT IN ('AHKS','FLASS')    "
					+ "order by A.DEPT_CODE, A.LAST_NAME,A.FIRST_NAME  " + 
					"  ) " ;
			
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(count_query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {			
				employeeCount_detail vObj = new employeeCount_detail();
				vObj.setDept_code(rs.getString(1));
				vObj.setEmployee_number(rs.getString(2));
				vObj.setLast_name(rs.getString(3));
				vObj.setFirst_name(rs.getString(4));
				vObj.setPost_rank_code(rs.getString(5));
				
				vObj.setSr_output(rs.getString(6));
				vObj.setSnr_output(rs.getString(7));
				vObj.setSc_output(rs.getString(8));
				vObj.setSo_output(rs.getString(9));
				
				vObj.setJr_output(rs.getString(10));
				vObj.setJnr_output(rs.getString(11));
				vObj.setJc_output(rs.getString(12));
				vObj.setJo_output(rs.getString(13));
				
				vObj.setCr_output(rs.getString(14));
				vObj.setCnr_output(rs.getString(15));
				vObj.setCc_output(rs.getString(16));
				vObj.setCo_output(rs.getString(17));
				
				vObj.setCrr_output(rs.getString(18));
				vObj.setCrnr_output(rs.getString(19));
				vObj.setCrc_output(rs.getString(20));
				vObj.setCro_output(rs.getString(21));
				
				vObj.setPr_output(rs.getString(22));
				vObj.setPnr_output(rs.getString(23));
				vObj.setPc_output(rs.getString(24));
				vObj.setPo_output(rs.getString(25));
				
				vObj.setOr_output(rs.getString(26));
				vObj.setOnr_output(rs.getString(27));
				vObj.setOc_output(rs.getString(28));
				vObj.setOo_output(rs.getString(29));

				voList.add(vObj);
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
	return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
		
		
		
	}
	

	public static List<employeeCount>  getEmployeeCount (int curY, int clause,int i_period_id) throws SQLException
	{
		List<employeeCount> voList = new ArrayList<employeeCount>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;	
		String whereCause = " ";
		
		try 
		{
			if(clause == 5) 
				whereCause = " where Count_output < 0.999 AND Count_output_2 < 0.999 AND Count_output_3 < 0.999 AND Count_output_4 < 0.999 AND  Count_output_5 < 0.999  ";
			else if (clause == 3)
				whereCause = " where Count_output < 0.999 AND Count_output_2 < 0.999 AND Count_output_3 <  0.999 " ;
			else if (clause == 2)
				whereCause = " where Count_output < 0.999 AND Count_output_2 < 0.999  ";
			

			String query = " Select * from ( "  + 
					" SELECT  "  + 
					" A.dept_code, "  + 
					" A.EMPLOYEE_NUMBER, "  + 
					" UPPER (A.LAST_NAME) as Last_name, "  + 
					" A.FIRST_NAME, "  + 
					" UPPER (A.LAST_NAME) || ' ' || A.FIRST_NAME as full_name, "  + 
					" 	A.POST_RANK_CODE, 	"  	+	 
					"	RPT.PERIOD_ID,		"	+
					" NVL (B.COUNT_OUTPUT, 0.00 ) as Count_output, "  	+ 
					" NVL (C.COUNT_OUTPUT, 0.00 ) as Count_output_2, "  + 
					" NVL (D.COUNT_OUTPUT, 0.00 ) as Count_output_3, "  + 
					" NVL (E.COUNT_OUTPUT, 0.00 ) as Count_output_4, "  + 
					" NVL (F.COUNT_OUTPUT, 0.00 ) as Count_output_5 "  	+ 
					"  "  + 
					" FROM  "  + 
					" (   SELECT dept_code,  "  + 
					"     LAST_NAME, "  		+ 
					"     FIRST_NAME, "  		+ 
					"     POST_RANK_CODE, "  	+ 
					"     EMPLOYEE_NUMBER, "  	+ 
					"     START_PERIOD 		"  	+ 
					" FROM RICH.RH_ELIG_ACAD_STAFF_V "  + 
					" ) A	 "  + 
					"  	LEFT JOIN RICH.RH_Z_CDCF_RPT_PERIOD RPT on RPT.CHART_DESC_2 = A.START_PERIOD   "+
					"  	LEFT JOIN	     "  + 
					" ( "  + 
					"  Select * from ( "  + 
					"     SELECT  dept_code, "  + 
					"             EMPLOYEE_NUMBER, "  + 
					"             SUM (CDCF_WEIGHTING) COUNT_OUTPUT "  + 
					"     FROM RICH.RH_REPORT_STAFF_WEIGHT_V "  + 
					"     WHERE  "  + 
					"          ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')  "  + 
					"             OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA') ) "  + 
					"          AND PERIOD_ID <= "+ i_period_id +
					"          AND (  (FROM_YEAR = "+ (curY - 1 )  +" AND FROM_MONTH >= 7) OR (FROM_YEAR = "+ curY +" AND FROM_MONTH <= 6)) "  + 
					"     GROUP BY dept_code, EMPLOYEE_NUMBER "    + 
					" ) "  + 
					" ) B "  + 
					" ON A.dept_code = B.dept_code AND A.EMPLOYEE_NUMBER = B.EMPLOYEE_NUMBER  "  + 
					"  "  + 
					" LEFT JOIN	 "  + 
					" ( "  + 
					" Select * from ( "  + 
					"     SELECT  dept_code, "  + 
					"             EMPLOYEE_NUMBER, "  + 
					"             SUM (CDCF_WEIGHTING) COUNT_OUTPUT "  + 
					"     FROM RICH.RH_REPORT_STAFF_WEIGHT_V "  + 
					"     WHERE  "  + 
					"          ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')  "  + 
					"             OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA') ) "  + 
					"          AND PERIOD_ID <= "+ (i_period_id-1) +
					"          AND (  (FROM_YEAR = " + (curY - 2)+ " AND FROM_MONTH >= 7) OR (FROM_YEAR = "+(curY-1)+" AND FROM_MONTH <= 6)) "  + 
					"     GROUP BY dept_code, EMPLOYEE_NUMBER "  + 
					" ) "  + 
					" ) C "  + 
					" ON A.dept_code = C.dept_code AND A.EMPLOYEE_NUMBER = C.EMPLOYEE_NUMBER  "  + 
					"  "  + 
					" LEFT JOIN	 "  + 
					" ( "  + 
					" Select * from ( "  + 
					"     SELECT  dept_code, "  + 
					"             EMPLOYEE_NUMBER, "  + 
					"             SUM (CDCF_WEIGHTING) COUNT_OUTPUT "  + 
					"     FROM RICH.RH_REPORT_STAFF_WEIGHT_V "  + 
					"     WHERE  "  + 
					"          ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')  "  + 
					"             OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA') ) "  + 
					"          AND PERIOD_ID <= "+ (i_period_id-2) +
					"          AND (  (FROM_YEAR = "+ (curY-3)+" AND FROM_MONTH >= 7) OR (FROM_YEAR = " + (curY-2) + " AND FROM_MONTH <= 6)) "  + 
					"     GROUP BY dept_code, EMPLOYEE_NUMBER "  + 
					" ) "  + 
					" ) D "  + 
					" ON A.dept_code = D.dept_code AND A.EMPLOYEE_NUMBER = D.EMPLOYEE_NUMBER  "  + 
					"  "  + 
					"  "  + 
					" LEFT JOIN	 "  + 
					" ( "  + 
					" Select * from ( "  + 
					"     SELECT  dept_code, "  + 
					"             EMPLOYEE_NUMBER, "  + 
					"             SUM (CDCF_WEIGHTING) COUNT_OUTPUT "  + 
					"     FROM RICH.RH_REPORT_STAFF_WEIGHT_V "  + 
					"     WHERE  "  + 
					"          ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')  "  + 
					"             OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA') ) "  +
					"          AND PERIOD_ID <= "+ (i_period_id - 3) +
					"          AND (  (FROM_YEAR = " + ( curY - 4 )+ " AND FROM_MONTH >= 7) OR (FROM_YEAR = "+ (curY - 3)+" AND FROM_MONTH <= 6)) "  + 
					"     GROUP BY dept_code, EMPLOYEE_NUMBER "  + 
					" ) "  + 
					" ) E "  + 
					" ON A.dept_code = E.dept_code AND A.EMPLOYEE_NUMBER = E.EMPLOYEE_NUMBER  "  + 
					"  "  + 
					" LEFT JOIN	 "  + 
					" ( "  + 
					" Select * from ( "  + 
					"     SELECT  dept_code, "  + 
					"             EMPLOYEE_NUMBER, "  + 
					"             SUM (CDCF_WEIGHTING) COUNT_OUTPUT "  + 
					"     FROM RICH.RH_REPORT_STAFF_WEIGHT_V "  + 
					"     WHERE  "  + 
					"          ((PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')  "  + 
					"             OR (PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA') ) "  + 
					"          AND PERIOD_ID <= "+ (i_period_id - 4) +
					"          AND (  (FROM_YEAR = "+ ( curY - 5 ) +" AND FROM_MONTH >= 7) OR (FROM_YEAR = "+ ( curY - 4 )+" AND FROM_MONTH <= 6)) "  + 
					"     GROUP BY dept_code, EMPLOYEE_NUMBER "  + 
					" ) "  + 
					" ) F "  + 
					" ON A.dept_code = F.dept_code AND A.EMPLOYEE_NUMBER = F.EMPLOYEE_NUMBER  "  + 
					"  "  + 
					" )   "  + 
					 whereCause +
					"  order by DEPT_CODE, full_name " ;			
			
			//System.out.println(query);
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {
				employeeCount vObj = new employeeCount();
				vObj.setDept_code(rs.getString(1));
				vObj.setEmployee_number(rs.getString(2));
				vObj.setLast_name(rs.getString(3));
				vObj.setFirst_name(rs.getString(4));
				vObj.setFull_name(rs.getString(5));
				vObj.setPost_rank_code(rs.getString(6));
				vObj.setPeriod_id(rs.getString(7));
				vObj.setCount_output(rs.getString(8));
				vObj.setCount_output_2(rs.getString(9));
				vObj.setCount_output_3(rs.getString(10));
				vObj.setCount_output_4(rs.getString(11));
				vObj.setCount_output_5(rs.getString(12));
				
				voList.add(vObj);
				
			}
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();	
	}

	public static List<AppStaffCount> getAppStaffCount_AllList()
	{
		List<AppStaffCount> objList = null;
		EntityManager em = null;
		
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM AppStaffCount obj  ";
				TypedQuery<AppStaffCount> q = em.createQuery(query, AppStaffCount.class);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
			
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public static List<AppStaffCount> getAppStaffCount_List(String college,String department,boolean notFull)
	{
		List<AppStaffCount> objList = null;
		EntityManager em = null;
		
			try 
			{
				em = getEntityManager();
				String query = 	"SELECT obj FROM AppStaffCount obj ";
				if (notFull)
					query += " WHERE obj.college_code = :college_code and obj.dept_code = :dept_code ";
				
				TypedQuery<AppStaffCount> q = em.createQuery(query, AppStaffCount.class);
				if (notFull) {
					q.setParameter("college_code", college);
					q.setParameter("dept_code", department);
				}
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
			
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	
	public static List<last3YearSummary> getDeptStaffDetails (int curY,String department) throws SQLException
	{
		List<last3YearSummary> voList = new ArrayList<last3YearSummary>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;		
		
		try
		{	
			String query = "  select staff.*   " + 
					"   from RH_REPORT_STAFF_WEIGHT_V staff   " + 
					"   WHERE(  (FROM_YEAR =  "+(curY-1) + "   AND FROM_MONTH >= 7) OR (FROM_YEAR =  "+curY+"  AND FROM_MONTH <= 6))   " + 
					"   AND 	((staff.PARENT_LOOKUP_CODE IN (1100, 1200,1400) AND dept_code = 'CCA')     " + 
					"					  OR (staff.PARENT_LOOKUP_CODE IN (1100, 1200) AND dept_code != 'CCA') )   " ;
			if(! department.equals("All"))
				query += "   AND  staff.DEPT_CODE = '"+department+"'   " ;
			
			query += "   order by  DESCRIPTION, APA_CITATION  ";			
			
			//System.out.println(query);
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			
			while (rs.next()) {			
				last3YearSummary vObj = new last3YearSummary();
				vObj.setDept_code(rs.getString("dept_code"));
				vObj.setEmployee_number(rs.getString("employee_number"));
				vObj.setCdcf_weighting(rs.getString("cdcf_weighting"));
				vObj.setApa_citation(rs.getString("apa_citation"));
				vObj.setName_other_pos(rs.getString("author"));
				vObj.setDescription(rs.getString("description"));
				voList.add(vObj);
			}
			
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	
	public static List<staffNumberName> getStaffName (String department) throws SQLException
	{
		List<staffNumberName> voList = new ArrayList<staffNumberName>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;		
		
		try
		{	
			String query = "select EMPLOYEE_NUMBER,  " + 
					"      	last_name ||' '||first_name as full_name " + 
					"from RH_ELIG_ACAD_STAFF_V  " ;
			if(! department.equals("All"))
				query += "where dept_code = '"+department+"'  " ;
			query += 	" order by last_name, first_name" ; 			
			
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next()) {			
				staffNumberName vObj = new staffNumberName();
				vObj.setEmployee_number(rs.getString("employee_number"));
				vObj.setFull_name(rs.getString("full_name"));
				voList.add(vObj);
			}
			
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return (CollectionUtils.isNotEmpty(voList)) ? voList : Collections.emptyList();
	}
	
	
	
}
