package hk.eduhk.rich.entity;

import java.util.List;
import java.util.logging.Level;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;

import org.apache.commons.collections.CollectionUtils;

import hk.eduhk.rich.BaseDAO;

@SuppressWarnings("serial")
public class ReportFileDAO extends BaseDAO{
	
	private static ReportFileDAO instance = null;
	
	public static ReportFileDAO getInstance() {
		if(instance == null) {
			instance = new ReportFileDAO();
		}
		
		return instance;
	}
	
	public List<ReportFile> getReportFileList(){
		List<ReportFile> objList = null;
		EntityManager em = null;		
		try
		{
			em = getEntityManager();		
			String query = "SELECT obj FROM ReportFile obj ";
			TypedQuery<ReportFile> q = em.createQuery(query, ReportFile.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList))?objList:null;
	}
	
	public ReportFile updateReportFile(ReportFile file) {
		return updateEntity(file);
	}
	
	public void deleteReportFile(ReportFile file) {
		deleteEntity(ReportFile.class, file.getFileId());
	}
}
