package hk.eduhk.rich.data;

import java.io.*;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.primefaces.model.file.UploadedFile;

import hk.eduhk.rich.util.POIUtils;


public class DataMatrixExtractor
{

	private UploadedFile uploadFile = null;
	private File file = null;

	private DataMatrix dataMatrix = null;

	
	public DataMatrixExtractor(UploadedFile uploadFile)
	{
		if (uploadFile == null) throw new NullPointerException("uploadFile cannot be null");
		this.uploadFile = uploadFile;
	}
	
	
	public DataMatrixExtractor(File file)
	{
		if (file == null) throw new NullPointerException("file cannot be null");
		this.file = file;
	}
	
	
	public DataMatrix getDataMatrix() throws Exception
	{
		if (dataMatrix == null)
		{
			// Get the extension name from the upload file
    		InputStream is = new BufferedInputStream((uploadFile != null ? uploadFile.getInputStream() : new FileInputStream(file)));
    		String extName = FilenameUtils.getExtension((uploadFile != null ? uploadFile.getFileName() : file.getName()));
    		
    		// Placeholder for xls format only
    		Workbook workbook = null;
    		
    		try
    		{
	    		// Determine the creation method of Workbook by the extension name
	    		if (StringUtils.equalsIgnoreCase("xlsx", extName))
	    		{
	            	WorkbookHandler handler = new WorkbookHandler(is);
	            	
	        		// Get the target Sheet
	        		dataMatrix = handler.getDataMatrix(); 
	    		}
	    		else
	    		{
					workbook = WorkbookFactory.create(is);
					dataMatrix = new DataMatrix();
					
					// Get the target Sheet
					Sheet sheet = workbook.getSheet(WorkbookHandler.DEFAULT_SHEET_NAME);
					if (sheet == null) sheet = workbook.getSheetAt(0);
					
					// Populate the dataMatrix
					int rowNum = sheet.getLastRowNum();
					for (int rowIdx=0;rowIdx<=rowNum;rowIdx++)
					{
						Row row = sheet.getRow(rowIdx);
						if (row != null)
						{
							int colNum = row.getLastCellNum();
							for (int colIdx=0;colIdx<=colNum;colIdx++)
							{
								String value = POIUtils.getStringCellValue(row, colIdx);
								if (value != null) dataMatrix.setValue(rowIdx, colIdx, value);
							}
						}
					}
	    		}
			}
			finally
			{
				if (workbook != null) workbook.close();
				IOUtils.closeQuietly(is);
			}
		}
		
		return dataMatrix;
	}
	

	/**
	 * Test method
	 * 
	 * @param args
	 * @throws Exception
	 */
	public static void main(String[] args) throws Exception
	{
		// File
		String fileName = "C:\\temp\\GES_2014_3.xls";
		File file = new File(fileName);
		
		// Get the Sheet
		DataMatrixExtractor extractor = new DataMatrixExtractor(file);
		DataMatrix dataMatrix = extractor.getDataMatrix();
		
		System.out.println("lastRow="+dataMatrix.getLastRowNum());
		System.out.println("lastColumn="+dataMatrix.getLastColumnNum());
		System.out.println("dataMatrix="+dataMatrix);
		
		System.out.println(dataMatrix.getRowValueList(0));
	}
	
}
