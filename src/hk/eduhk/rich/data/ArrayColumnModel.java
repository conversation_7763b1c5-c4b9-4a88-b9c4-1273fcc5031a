package hk.eduhk.rich.data;

import java.io.Serializable;


@SuppressWarnings("serial")
public class ArrayColumnModel implements Serializable
{
	
	private int index;
	private String fieldId;
	private String header;
	private String cssStyle;
	
	
	public ArrayColumnModel()
	{
	}

	
	public int getIndex()
	{
		return index;
	}

	
	public void setIndex(int idx)
	{
		this.index = idx;
	}
	
	
	public String getFieldId()
	{
		return fieldId;
	}

	
	public void setFieldId(String fieldId)
	{
		this.fieldId = fieldId;
	}


	public String getHeader()
	{
		return header;
	}


	public void setHeader(String header)
	{
		this.header = header;
	}

	
	public String getCssStyle()
	{
		return cssStyle;
	}

	
	public void setCssStyle(String cssStyle)
	{
		this.cssStyle = cssStyle;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((fieldId == null) ? 0 : fieldId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ArrayColumnModel other = (ArrayColumnModel) obj;
		if (fieldId == null)
		{
			if (other.fieldId != null)
				return false;
		}
		else if (!fieldId.equals(other.fieldId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ArrayColumnModel [index=" + index + ", fieldId=" + fieldId + ", header=" + header + ", cssStyle="
				+ cssStyle + "]";
	}

}
