package hk.eduhk.rich.data;

import java.io.Serializable;


@SuppressWarnings("serial")
public class SelectOptionLookup implements Serializable
{

	private String name;
	private String value;
	
	
	public SelectOptionLookup()
	{
	}

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}

	
	public String getValue()
	{
		return value;
	}

	
	public void setValue(String value)
	{
		this.value = value;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((value == null) ? 0 : value.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SelectOptionLookup other = (SelectOptionLookup) obj;
		if (value == null)
		{
			if (other.value != null)
				return false;
		}
		else if (!value.equals(other.value))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupSelectOption [name=" + name + ", value=" + value + "]";
	}
	
}
