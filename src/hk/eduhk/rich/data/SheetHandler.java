package hk.eduhk.rich.data;

import org.apache.commons.validator.GenericValidator;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.model.SharedStringsTable;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.xml.sax.*;
import org.xml.sax.helpers.DefaultHandler;


/**
 * 
 * ECMA Open XML Ref: http://www.ecma-international.org/news/TC45_current_work/OpenXML%20White%20Paper.pdf
 * 
 * <AUTHOR>
 *
 */
public class SheetHandler extends DefaultHandler 
{
	
	private SharedStringsTable sst;
	private StringBuilder contentBuf = new StringBuilder();
	private boolean isStringCellType;
	
	private DataMatrix dataMatrix = new DataMatrix();
	
	private int rowIdx;
	private int colIdx;
		
	
	public SheetHandler(SharedStringsTable sst) 
	{
		this.sst = sst;
	}
	
	
	public void startElement(String uri, String localName, String name, Attributes attributes) throws SAXException 
	{
		// Cell
		if (name.equals("c")) 
		{
			/*
			int attrLen = attributes.getLength();
			for (int n=0;n<attrLen;n++)
			{
				System.out.println("col attributes " + n + ", QName="+attributes.getQName(n) + ", value=" + attributes.getValue(n));
			}
			*/
			
			colIdx = getColumnIndex(attributes.getValue("r"));
			
			// Figure out if the value is an index in the SST
			String cellType = attributes.getValue("t");
			isStringCellType = (cellType != null && cellType.equals("s"));
			
			// Clear contents cache
			contentBuf.delete(0, contentBuf.length());
		}

		// Row
		else if (name.equals("row"))
		{
			String rowNumValue = attributes.getValue("r");
			rowIdx = Integer.parseInt(rowNumValue) - 1;
		}
	}
	
	
	public void endElement(String uri, String localName, String name) throws SAXException 
	{
		// Cell 
		if (name.equals("c"))
		{
			// Reset the flag
			isStringCellType = false;
		}
		
		// Value
		else if (name.equals("v")) 
		{
			// Process the last contents as required.
			// Do now, as characters() may be called more than once
			if (isStringCellType)
			{
				int idx = Integer.parseInt(contentBuf.toString());
				contentBuf.delete(0, contentBuf.length());
				contentBuf.append(new XSSFRichTextString(sst.getEntryAt(idx)).toString());
			}
			
			// v => contents of a cell
			// Output after we've seen the string contents
			String cellValue = contentBuf.toString();
			
			// Apply conversion to non-string cell type
			// Excel converts numbers to text with different rules to those of java 
			// so Double.toString(value) won't do.
			// e.g. 0.9997 is saved as 0.99970000000000003 in the xlsx file.
			// Ref: https://poi.apache.org/apidocs/org/apache/poi/ss/util/NumberToTextConverter.html
			if (GenericValidator.isDouble(cellValue) && !isStringCellType)
			{
				cellValue = NumberToTextConverter.toText(Double.parseDouble(cellValue));
			}
			
			dataMatrix.setValue(rowIdx, colIdx, cellValue);
		}
	}

	
	public void characters(char[] ch, int start, int length) throws SAXException 
	{
		contentBuf.append(ch, start, length);
		//lastContents += new String(ch, start, length);
	}
	
	
	/**
	 * Convert the column name to the actual index in decimal format
	 * e.g. A -> 0, B -> 1, AB -> 27
	 * 
	 * @param name
	 * @return
	 */
	public int getColumnIndex(String ref)
	{
		int index = -1;
		
		if (ref != null)
		{
			int idx = 0;
			
			// Extract the alphabetic part from the name
			for (int n=0;n<ref.length();n++)
			{
				char c = ref.charAt(n);
				if (c < 'A' || c > 'Z') break;
				idx = n;
			}
			
			index = -1;
			
			String colName = ref.substring(0, idx+1);
			for (int n=0;n<colName.length();n++)
			{
				int pow = (colName.length() - 1 - n);
				int multiply = 1;
				
				// Use for-loop instead of Math.pow, 
				// as for-loop is much faster than Math.pow
				for (int p=0;pow>0 && p<pow;p++) multiply *= 26;
				index += (int) ((colName.charAt(n) - 'A' + 1) * multiply);
			}
		}
		
		return index;
	}
	
	
	public DataMatrix getContentDataMatrix() throws Exception
	{
		return dataMatrix;
	}
	
	
}