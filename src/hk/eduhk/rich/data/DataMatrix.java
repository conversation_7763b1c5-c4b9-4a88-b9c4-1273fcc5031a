package hk.eduhk.rich.data;

import java.io.Serializable;
import java.util.*;

import org.apache.commons.validator.GenericValidator;


public class DataMatrix implements Serializable
{

	private static final long serialVersionUID = 1L;
	
	private int lastRowNum = -1;
	private int lastColumnNum = -1;
	
	private Map<DataMatrixIndex, String> dataMap = new HashMap<DataMatrixIndex, String>(); 
	private DataMatrixIndex fetchKey = new DataMatrixIndex();
	
	
	public void setValue(int rowIdx, int colIdx, String value)
	{
		if (rowIdx > lastRowNum) lastRowNum = rowIdx;
		if (colIdx > lastColumnNum) lastColumnNum = colIdx;
		
		DataMatrixIndex key = new DataMatrixIndex(rowIdx, colIdx);
		dataMap.put(key, value);
	}
	
	
	/**
	 * This method must be synchronized, as the fetchKey is an instance variable
	 * 
	 * @param rowIdx
	 * @param colIdx
	 * @return
	 */
	public synchronized String getValue(int rowIdx, int colIdx)
	{
		fetchKey.setRow(rowIdx);
		fetchKey.setColumn(colIdx);
		return dataMap.get(fetchKey);
	}
	
	
	public List<String> getRowValueList(int rowIdx)
	{
		List<String> valueList = new ArrayList<String>();
		
		for (int colIdx=0;colIdx<=lastColumnNum;colIdx++)
		{
			String value = getValue(rowIdx, colIdx);
			valueList.add(value);
		}
			
		return valueList;
	}
	
	
	public int getLastRowNum()
	{
		return lastRowNum;
	}
	
	
	public int getLastColumnNum()
	{
		return lastColumnNum;
	}
	
	
	public boolean isEmptyRow(int rowIdx)
	{
		boolean empty = true;
		
		if (rowIdx < 0 && rowIdx > lastRowNum) throw new IllegalArgumentException("rowIdx is invalid");
		
		for (int colIdx=0;colIdx<=lastColumnNum;colIdx++)
		{
			Object value = getValue(rowIdx, colIdx);
			
			if ((value != null && !(value instanceof String)) || 
				(value instanceof String && !GenericValidator.isBlankOrNull((String) value)))
			{
				empty = false;
				break;
			}
		}
		
		return empty;
	}
	
	
	public String toString()
	{
		StringBuilder buf = new StringBuilder();
		
		if (lastRowNum >= 0 && lastColumnNum >= 0)
		{
			for (int row=0;row<=lastRowNum;row++)
			{
				if (row > 0) buf.append("\n");
				
				for (int column=0;column<=lastColumnNum;column++)
				{
					if (column > 0) buf.append(",");
					Object value = getValue(row, column);
					buf.append(value != null ? value.toString() : "");
				}
			}
		}
		
		return buf.toString();
	}


}
