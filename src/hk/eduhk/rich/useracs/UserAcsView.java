package hk.eduhk.rich.useracs;

import java.io.*;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.List;

import javax.faces.application.FacesMessage;
import javax.faces.bean.*;
import javax.faces.context.FacesContext;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.BaseView;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class UserAcsView extends BaseView
{
	
	private List<String> userIdList;
	private List<UserAcs> userAcsList;
	
	private String selectedUserId;
	private UserAcs selectedUserAcs;
	
	public UserAcsView()
	{
		super();
	}


	public String getSelectedUserId()
	{
		if(selectedUserId == null) {
			selectedUserId = "ALL";
		}
		return StringUtils.defaultString(selectedUserId);
	}


	public void setSelectedUserId(String selectedUserId)
	{
		this.selectedUserId = selectedUserId;
		this.userAcsList = null;
	}


	public UserAcs getSelectedUserAcs()
	{
		// To avoid NullPointerException
		if (selectedUserAcs == null) selectedUserAcs = new UserAcs();
		
		return selectedUserAcs;
	}


	public void setSelectedUserAcs(UserAcs selectedUserAcs)
	{
		this.selectedUserAcs = selectedUserAcs;
	}


	public List<String> getUserIdList()
	{
		if (userIdList == null)
		{
			userIdList = UserAcsDAO.getInstance().getUserIdList();
		}
		
		return userIdList;
	}


	public void setUserIdList(List<String> userIdList)
	{
		this.userIdList = userIdList;
	}


	public List<UserAcs> getUserAcsList()
	{
		if (userAcsList == null)
		{
			UserAcsDAO dao = UserAcsDAO.getInstance();
			userAcsList = dao.getUserAcsListByUserId(getSelectedUserId());
		}
		
		return userAcsList;
	}
	
	
	public void setUserAcsList(List<UserAcs> userAcsList)
	{
		this.userAcsList = userAcsList;
	}
	
	
	public void clearUserAcsList()
	{
		setUserAcsList(null);
	}
	
	
	public String gotoNewUserAcsPage() throws UnsupportedEncodingException
	{
		// Clear the saved object in this View
		selectedUserAcs = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("userAcsEdit")+
				   "&userId=" + URLEncoder.encode(getSelectedUserId(), "UTF-8") +
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	
	public int getFieldMaxLength(String field)
	{
		int maxLength = Integer.MAX_VALUE;
		
		if (StringUtils.equals(field, "userId"))
		{
			maxLength = UserAcsValidator.VALID_LENGTH_USER_ID[1];
		}
		else if (StringUtils.equals(field, "crseSubjCode"))
		{
			maxLength = UserAcsValidator.VALID_LENGTH_CRSE_SUBJ_CODE[1];
		}
		else if (StringUtils.equals(field, "crseNum"))
		{
			maxLength = UserAcsValidator.VALID_LENGTH_CRSE_NUM[1];
		}
		
		return maxLength;
	}
	
	
	public String insertUserAcs() throws UnsupportedEncodingException
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		//UserSessionView userSessionView = UserSessionView.getCurrentInstance();
		
		// Set the userstamp of the object
		// Using getUserId will get loginId of impersonate person
		//if (getSelectedUserAcs() != null) selectedUserAcs.setUserstamp(userSessionView.getUserId());
		if (getSelectedUserAcs() != null) selectedUserAcs.setUserStamp(getLoginUserId());
		UserAcsDAO dao = UserAcsDAO.getInstance();
		if(dao.checkUserAcsSameRightExist(selectedUserAcs)) {
			String message =  "This User Access Right: " + selectedUserAcs.getAllthreeCol() + " already exists.";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return "";
		}
		if(selectedUserAcs.getCrseNum() != null && 
				!selectedUserAcs.getCrseNum().isEmpty() && 
				dao.checkUserAcsAllRightExist(selectedUserAcs)) {
			String message =  "This User:" + selectedUserAcs.getUserId() + 
					" already got full access right of: " + selectedUserAcs.getCrseSubjCode();
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return "";
		}
		if((selectedUserAcs.getCrseNum() == null || 
				selectedUserAcs.getCrseNum().isEmpty()) && 
				dao.checkUserAcsSubsetRightExist(selectedUserAcs)) {
			String message =  "Please delete all access right of: " + selectedUserAcs.getCrseSubjCode() + 
					" for user: " + selectedUserAcs.getUserId() + " before adding a full access right.";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return "";
		}
		boolean successInsert;
		// Persist the change to db
		try
		{
			successInsert = dao.insertUserAcs(selectedUserAcs);
			
			if(successInsert) {
				// Success message
				String message =  "msg.success.create.x";
				message = MessageFormat.format(getResourceBundle().getString(message), selectedUserAcs.getAllthreeCol());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			else {
				String message =  "Insert fail";
				System.out.println("Insert fail at UserAcsView, insertUserAcs().");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
		}
		catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return "";
		}
		
		// Redirect to System Parameter List page
		return redirect("userAcsList") +
				"&userId=" + URLEncoder.encode(selectedUserAcs.getUserId(), "UTF-8");

	}
	
	
	public String deleteUserAcs() throws UnsupportedEncodingException
	{
		if (selectedUserAcs != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			
			// Delete the UserAcs from db
			try
			{
				UserAcsDAO dao = UserAcsDAO.getInstance();
				dao.deleteUserAcs(selectedUserAcs);
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.delete.x"), selectedUserAcs.getAllthreeCol());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (IllegalArgumentException e)
			{
				String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedUserAcs.getAllthreeCol());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}

			// Clear the userAcsList such that the userAcsList can be reloaded
			userAcsList = null;

			// Redirect to System Parameter List page
			return redirect("userAcsList")+
					"&userId=" + URLEncoder.encode(getSelectedUserId(), "UTF-8");
		}
		
		return "";
	}
	
}
