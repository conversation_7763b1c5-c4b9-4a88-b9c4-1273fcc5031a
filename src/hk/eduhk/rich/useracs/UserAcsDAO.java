package hk.eduhk.rich.useracs;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.rich.BaseDAO;


@SuppressWarnings("serial")
public class UserAcsDAO extends BaseDAO
{
	
	private static UserAcsDAO instance;
	
	private Logger logger = Logger.getLogger(UserAcsDAO.class.getName());


	public static synchronized UserAcsDAO getInstance()
	{
		if (instance == null) instance = new UserAcsDAO();
		return instance;
	}

	
	protected UserAcsDAO()
	{
	}
	
	public List<UserAcs> getUserAcsListByUserId(String userId)
	{
		List<UserAcs> objList = new ArrayList<UserAcs>();
		if (userId != null && !userId.equals("ALL"))
		{
			Connection conn = null;
			PreparedStatement stmt = null;
			try
			{
				String query = "SELECT DISTINCT t.USER_ID, t.CRSE_SUBJ_CODE, t.CRSE_NUM FROM FA_USER_ACS t " +
							   " WHERE t.USER_ID = ? " +
							   " ORDER BY USER_ID ";
	
				conn = pm.getConnection();
				stmt = conn.prepareStatement(query);
				stmt.setString(1, userId);
				ResultSet rs = stmt.executeQuery();
				while (rs.next()) {
					UserAcs stuUserAcs = new UserAcs();
					stuUserAcs.setUserId(rs.getString(1));
					stuUserAcs.setCrseSubjCode(rs.getString(2));
					stuUserAcs.setCrseNum(rs.getString(3));
					objList.add(stuUserAcs);
				}
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
			}
			finally
			{
				pm.close(stmt);
				pm.close(conn);
			}
		}
		else if (userId.equals("ALL")) {
			Connection conn = null;
			PreparedStatement stmt = null;
			try
			{
				String query = "SELECT DISTINCT t.USER_ID, t.CRSE_SUBJ_CODE, t.CRSE_NUM FROM FA_USER_ACS t " +
							   " ORDER BY USER_ID ";

				conn = pm.getConnection();
				stmt = conn.prepareStatement(query);
				ResultSet rs = stmt.executeQuery();
				while (rs.next()) {
					UserAcs stuUserAcs = new UserAcs();
					stuUserAcs.setUserId(rs.getString(1));
					stuUserAcs.setCrseSubjCode(rs.getString(2));
					stuUserAcs.setCrseNum(rs.getString(3));
					objList.add(stuUserAcs);
				}
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
			}
			finally
			{
				pm.close(stmt);
				pm.close(conn);
			}
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public List<String> getUserIdList()
	{
		List<String> objList = new ArrayList<String>();
		Connection conn = null;
		PreparedStatement stmt = null;
		try
		{
			String query = "SELECT DISTINCT t.USER_ID FROM FA_USER_ACS t " +
						   " ORDER BY USER_ID ";

			conn = pm.getConnection();
			stmt = conn.prepareStatement(query);
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) objList.add(rs.getString(1));
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	public boolean checkUserAcsSameRightExist(UserAcs stuUserAcs)
	{
		List<String> objList = new ArrayList<String>();
		Connection conn = null;
		PreparedStatement stmt = null;
		String query;
		try
		{
			if(stuUserAcs.getCrseNum() == null || stuUserAcs.getCrseNum().isEmpty()) {
				query = "SELECT DISTINCT t.USER_ID, t.CRSE_SUBJ_CODE, t.CRSE_NUM FROM FA_USER_ACS t " +
						   " WHERE USER_ID = ? " +
						   " AND CRSE_SUBJ_CODE = ? " +
						   " AND CRSE_NUM IS NULL ";
				conn = pm.getConnection();
				stmt = conn.prepareStatement(query);
				stmt.setString(1, stuUserAcs.getUserId());
				stmt.setString(2, stuUserAcs.getCrseSubjCode());
			}
			else {
				query = "SELECT DISTINCT t.USER_ID, t.CRSE_SUBJ_CODE, t.CRSE_NUM FROM FA_USER_ACS t " +
							   " WHERE USER_ID = ? " +
							   " AND CRSE_SUBJ_CODE = ? " +
							   " AND CRSE_NUM = ? ";
	
				conn = pm.getConnection();
				stmt = conn.prepareStatement(query);
				stmt.setString(1, stuUserAcs.getUserId());
				stmt.setString(2, stuUserAcs.getCrseSubjCode());
				stmt.setString(3, stuUserAcs.getCrseNum());
			}
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) objList.add(rs.getString(1));
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(objList.isEmpty()) 
			return false;
		else
			return true;
	}
	
	public boolean checkUserAcsAllRightExist(UserAcs stuUserAcs)
	{
		List<String> objList = new ArrayList<String>();
		Connection conn = null;
		PreparedStatement stmt = null;
		String query;
		try
		{
			query = "SELECT DISTINCT t.USER_ID, t.CRSE_SUBJ_CODE, t.CRSE_NUM FROM FA_USER_ACS t " +
					   " WHERE USER_ID = ? " +
					   " AND CRSE_SUBJ_CODE = ? " +
					   " AND CRSE_NUM IS NULL ";
			conn = pm.getConnection();
			stmt = conn.prepareStatement(query);
			stmt.setString(1, stuUserAcs.getUserId());
			stmt.setString(2, stuUserAcs.getCrseSubjCode());
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) objList.add(rs.getString(1));
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(objList.isEmpty()) 
			return false;
		else
			return true;
	}
	
	public boolean checkUserAcsSubsetRightExist(UserAcs stuUserAcs)
	{
		List<String> objList = new ArrayList<String>();
		Connection conn = null;
		PreparedStatement stmt = null;
		String query;
		try
		{
			query = "SELECT DISTINCT t.USER_ID, t.CRSE_SUBJ_CODE, t.CRSE_NUM FROM FA_USER_ACS t " +
					   " WHERE USER_ID = ? " +
					   " AND CRSE_SUBJ_CODE = ? ";
			conn = pm.getConnection();
			stmt = conn.prepareStatement(query);
			stmt.setString(1, stuUserAcs.getUserId());
			stmt.setString(2, stuUserAcs.getCrseSubjCode());
			ResultSet rs = stmt.executeQuery();
			while (rs.next()) objList.add(rs.getString(1));
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(objList.isEmpty()) 
			return false;
		else
			return true;
	}
	
	public boolean insertUserAcs(UserAcs stuUserAcs) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "INSERT INTO FA_USER_ACS (USER_ID, CRSE_SUBJ_CODE, CRSE_NUM , CREATOR, USERSTAMP) " +
						   " VALUES (?, ?, ?, ?, ?) ";

			conn = pm.getConnection();
			stmt = conn.prepareStatement(query);
			stmt.setString(1, stuUserAcs.getUserId());
			stmt.setString(2, stuUserAcs.getCrseSubjCode());
			if(stuUserAcs.getCrseNum() == null || stuUserAcs.getCrseNum().isEmpty())
				stmt.setNull(3, 12);
			else
				stmt.setString(3, stuUserAcs.getCrseNum());
			stmt.setString(4, stuUserAcs.getUserStamp());
			stmt.setString(5, stuUserAcs.getUserStamp());
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}
	
	public boolean deleteUserAcs(UserAcs stuUserAcs) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		String query;
		try
		{
			if(stuUserAcs.getCrseNum() == null || stuUserAcs.getCrseNum().isEmpty()) {
				query = "DELETE FROM FA_USER_ACS " +
						   " WHERE USER_ID = ? " +
						   " AND CRSE_SUBJ_CODE = ? " +
						   " AND CRSE_NUM IS NULL ";
				conn = pm.getConnection();
				stmt = conn.prepareStatement(query);
				stmt.setString(1, stuUserAcs.getUserId());
				stmt.setString(2, stuUserAcs.getCrseSubjCode());
			}
			else {
				query = "DELETE FROM FA_USER_ACS " +
							   " WHERE USER_ID = ? " +
							   " AND CRSE_SUBJ_CODE = ? " +
							   " AND CRSE_NUM = ? ";
	
				conn = pm.getConnection();
				stmt = conn.prepareStatement(query);
				stmt.setString(1, stuUserAcs.getUserId());
				stmt.setString(2, stuUserAcs.getCrseSubjCode());
				stmt.setString(3, stuUserAcs.getCrseNum());
			}
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row >= 1)
			return true;
		else
			return false;
	}

	
}
