package hk.eduhk.rich.useracs;


public class UserAcs
{
	private String userId = null;
	private String crseSubjCode = null;
	private String crseNum = null;
	private String userStamp = null;

	public UserAcs() {
		
	}
	
	public UserAcs(String userId, String crseSubjCode, String crseNum) {
		setUserId(userId);
		setCrseSubjCode(crseSubjCode);
		if(crseNum != null && !crseNum.isEmpty())
			setCrseNum(crseNum);
	}
	
	public String getUserId()
	{
		if(userId != null)
			return userId.trim().toLowerCase();
		else
			return null;
	}

	
	public void setUserId(String userId)
	{
		if(userId != null)
			this.userId = userId.trim().toLowerCase();
	}

	
	public String getCrseSubjCode()
	{
		if(crseSubjCode != null)
			return crseSubjCode.trim().toUpperCase();
		else
			return null;
	}

	
	public void setCrseSubjCode(String crseSubjCode)
	{
		if(crseSubjCode != null)
			this.crseSubjCode = crseSubjCode.trim().toUpperCase();
	}

	
	public String getCrseNum()
	{
		if(crseNum != null)
			return crseNum.trim();
		else
			return null;
	}

	
	public void setCrseNum(String crseNum)
	{
		if(crseNum != null)
			this.crseNum = crseNum.trim();
		else
			this.crseNum = null;
	}
	
	public String getUserStamp()
	{
		return userStamp;
	}

	
	public void setUserStamp(String userStamp)
	{
		this.userStamp = userStamp.trim();
	}
	
	public String getAllthreeCol() {
		if(getUserId() != null &&  getCrseSubjCode() != null) {
			if(getCrseNum() != null)
				return "User ID: " + getUserId() + "; Access Right: " + getCrseSubjCode() + getCrseNum() + ";";
			else
				return "User ID: " + getUserId() + "; Access Right: " + getCrseSubjCode() + ";";
		}
		return "";
	}

	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserAcs other = (UserAcs) obj;
		if (userId == null) {
			if (other.userId != null)
				return false;
		}
		else if (!userId.equals(other.userId))
			return false;
		if (crseSubjCode == null) {
			if (other.crseSubjCode != null)
				return false;
		}
		else if (!crseSubjCode.equals(other.crseSubjCode))
			return false;
		if (crseNum == null) {
			if (other.crseNum != null)
				return false;
		}
		else if (!crseNum.equals(other.crseNum))
			return false;
		return true;
	}

	@Override
	public String toString()
	{
		return "UserAcs [userId=" + userId + ", crseSubjCode=" + crseSubjCode + ", crseNum=" + crseNum + ", userStamp="
				+ userStamp + "]";
	}
	
	
}
