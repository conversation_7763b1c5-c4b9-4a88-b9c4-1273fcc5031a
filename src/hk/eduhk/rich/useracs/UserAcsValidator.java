package hk.eduhk.rich.useracs;

import javax.faces.application.FacesMessage;
import javax.faces.component.*;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;

import hk.eduhk.rich.BaseFacesValidator;


@FacesValidator("hk.eduhk.rich.param.UserAcsValidator")
public class UserAcsValidator extends BaseFacesValidator
{
	
	public static final Integer[] VALID_LENGTH_USER_ID 			= {1, 30};
	public static final Integer[] VALID_LENGTH_CRSE_SUBJ_CODE	= {1, 6};
	public static final Integer[] VALID_LENGTH_CRSE_NUM 		= {0, 10};
	
	
	public UserAcsValidator()
	{
		super();
	}

	
	public void validateUserId(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		if (obj instanceof String)
		{
			validateEmpty(((String) obj).trim());
		}
		validateEmpty(obj);
		validateLength(obj, VALID_LENGTH_USER_ID);
	}

	
	public void validateCrseSubjCode(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		if (obj instanceof String)
		{
			validateEmpty(((String) obj).trim());
		}
		validateEmpty(obj);
		validateLength(obj, VALID_LENGTH_CRSE_SUBJ_CODE);
	}
	
	
	public void validateCrseNum(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		if (obj instanceof String) {
			if(!((String) obj).trim().isEmpty()){
			    try
			    {
			        Integer.parseInt(obj.toString());
			    } catch (NumberFormatException ex)
			    {
					String msg = "Please insert number here.";
					throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			    }
			}
	    }
		validateLength(obj, VALID_LENGTH_CRSE_NUM);
	}
	
	
}
