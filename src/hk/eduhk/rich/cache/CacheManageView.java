package hk.eduhk.rich.cache;

import java.text.MessageFormat;
import java.util.*;

import javax.faces.application.FacesMessage;
import javax.faces.bean.*;
import javax.faces.context.FacesContext;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.rich.BaseView;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class CacheManageView extends BaseView
{

	private List<String> cacheNameList;
	private List<String> selectedCacheNameList;
	
	
	public List<String> getCacheNameList()
	{
		if (cacheNameList == null)
		{
			AppCache appCache = AppCache.getInstance();
			cacheNameList = appCache.getCacheNameList();
		}
		
		return cacheNameList;
	}
	
		
	public List<String> getSelectedCacheNameList()
	{
		if (selectedCacheNameList == null)
		{
			selectedCacheNameList = new ArrayList<String>();
			selectedCacheNameList.addAll(cacheNameList);
		}
		
		return selectedCacheNameList;
	}
	
	
	public void setSelectedCacheNameList(List<String> selectedCacheNameList)
	{
		this.selectedCacheNameList = selectedCacheNameList;
	}
	
	
	public void clearSelected()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (CollectionUtils.isNotEmpty(getSelectedCacheNameList()))
		{
			AppCache appCache = AppCache.getInstance();
			for (String cacheName : getSelectedCacheNameList())
			{
				appCache.clear(cacheName);
			}
			
			// Success message
			String message = MessageFormat.format(getResourceBundle().getString("msg.success.clear.x"), getSelectedCacheNameList().toString());
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
		}
		else
		{
			// At least select one
			String message = getResourceBundle().getString("msg.err.select.atLeastOne");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
		
		// Preload the lookup cache in a separate thread
		AppCachePreloader preloader = new AppCachePreloader();
		Thread preloadThread = new Thread(preloader);
		preloadThread.start();		
	}
	
	
	public void selectAll()
	{
		if (getSelectedCacheNameList() != null)
		{
			getSelectedCacheNameList().clear();
			selectedCacheNameList.addAll(cacheNameList);
		}
	}
	
	
	public void unselectAll()
	{
		if (getSelectedCacheNameList() != null)
		{
			getSelectedCacheNameList().clear();
		}
	}
	
}
