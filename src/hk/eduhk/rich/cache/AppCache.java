package hk.eduhk.rich.cache;

import java.net.MalformedURLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletContext;

import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.Configuration;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.xml.XmlConfiguration;
import org.ehcache.xml.exceptions.XmlConfigurationException;


public class AppCache
{

	public static final String CACHE_ACS				= "app.acs";
	public static final String CACHE_ACS_TYPE			= "app.acsType";
	public static final String CACHE_SYS_PARAM			= "app.sysParam";
	public static final String CACHE_USER_ROLE 			= "app.userRole";
	
	public static final String CACHE_BAN_CRSE			= "ban.crse";
	public static final String CACHE_BAN_ORG_UNIT_MAP	= "ban.orgUnitMap";
	public static final String CACHE_BAN_PERSON			= "ban.person";
	public static final String CACHE_BAN_PERSON_USER_ID	= "ban.person.userId";
	public static final String CACHE_BAN_PROG			= "ban.prog";
	public static final String CACHE_BAN_PROG_ATTR		= "ban.progAttr";
	public static final String CACHE_BAN_TERM			= "ban.term";
	
	
	private static AppCache instance = null;
	
	private CacheManager cacheManager = null;
	private Map<String, CacheConfiguration<?, ?>> configMap = null;
	
	private List<String> cacheNameList = Collections.EMPTY_LIST;
	
	private static Logger logger = Logger.getLogger(AppCache.class.getName());

		
	public static synchronized AppCache getInstance()
	{
		if (instance == null) instance = new AppCache();
		return instance;
	}
	
	
	/**
	 * Protected constructor
	 */
	protected AppCache()
	{
	}
	
	
	/**
	 * Initialize CacheManager.
	 *  
	 * @param sCtx
	 * @throws XmlConfigurationException
	 * @throws MalformedURLException
	 */
	public void init(ServletContext sCtx) throws XmlConfigurationException, MalformedURLException
	{
		// Initialize CacheManager using cache-config.xml
		Configuration cacheConfig = new XmlConfiguration(sCtx.getResource("/WEB-INF/cache-config.xml"));
		cacheManager = CacheManagerBuilder.newCacheManager(cacheConfig);
		cacheManager.init();
		
		// List of cache names
		// Get all available Cache alias
		configMap = cacheConfig.getCacheConfigurations();
		if (configMap != null)
		{
			cacheNameList = new ArrayList<String>(configMap.keySet());
			Collections.sort(cacheNameList);
			cacheNameList = Collections.unmodifiableList(cacheNameList);
		}
		
		logger.log(Level.INFO, "Ehcache configuration file loaded=" + cacheConfig);
	}

	
	public List<String> getCacheNameList()
	{
		return cacheNameList;
	}
	

	/**
	 * Get the Cache from the CacheManager.
	 * 
	 * @param name
	 * @param keyType
	 * @param valueType
	 * @return
	 */
	public <K, V> Cache<K, V> getCache(String name, Class<K> keyType, Class<V> valueType)
	{
		return cacheManager.getCache(name, keyType, valueType);
	}
	
	
	/**
	 * Remove all mappings in a cache.
	 * 
	 * @param cacheName Cache name
	 */
	public void clear(String cacheName)
	{
		CacheConfiguration<?, ?> cacheConfig = configMap.get(cacheName);
		Cache<?, ?> cache = cacheManager.getCache(cacheName, cacheConfig.getKeyType(), cacheConfig.getValueType());
		cache.clear();
	}
	
	
	/**
	 * Removes all mappings currently present in Cache.
	 */
	public void clearAll()
	{
		// Iterate to clear each Cache
		for (String cacheName : getCacheNameList())
		{
			clear(cacheName);
		}
	}
	

	/**
	 * Release all transient resources that CacheManager manages.
	 */
	public void close()
	{
		if (cacheManager != null)
		{
			cacheManager.close();
		}
	}
	
}
