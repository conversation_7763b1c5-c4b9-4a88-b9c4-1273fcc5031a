package hk.eduhk.rich.cache;

import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.ehcache.event.CacheEvent;
import org.ehcache.event.CacheEventListener;
import org.ehcache.event.EventType;

import hk.eduhk.rich.banner.BanCourse;
import hk.eduhk.rich.banner.BannerLookupCacheDAO;


public class BanProgramCacheExpiredEventListener implements CacheEventListener<String, BanCourse>
{
	
	private Object refreshLock = new Object();
	private Date lastRefreshDate = null;

	private final String id = BanProgramAttributeCacheExpiredEventListener.class.getName();
	private static Logger logger = Logger.getLogger(BanProgramCacheExpiredEventListener.class.getName());

	
	@Override
	public void onEvent(CacheEvent<? extends String, ? extends BanCourse> event)
	{
		// If there is any expired cache instance, refresh the whole cache
		if (event.getType() == EventType.EXPIRED)
		{
			synchronized (refreshLock)
			{
				Date currentDate = new Date();
				
				// Comparison here to ensure not to reload entities again 
				// if there are multiple threads calling this
				if (lastRefreshDate == null || currentDate.getTime() - lastRefreshDate.getTime() > 5000)
				{
					// Load the cache with entities
					BannerLookupCacheDAO dao = BannerLookupCacheDAO.getInstance();
					dao.reloadProgramCache();
					
					// Update the last refresh date
					lastRefreshDate = currentDate;
				}
			}
		}
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanProgramCacheExpiredEventListener other = (BanProgramCacheExpiredEventListener) obj;
		if (id == null)
		{
			if (other.id != null)
				return false;
		}
		else if (!id.equals(other.id))
			return false;
		return true;
	}

}
