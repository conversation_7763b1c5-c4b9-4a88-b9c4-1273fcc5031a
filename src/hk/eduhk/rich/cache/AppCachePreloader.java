package hk.eduhk.rich.cache;

import java.lang.reflect.Method;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import hk.eduhk.rich.banner.BannerLookupCacheDAO;


public class AppCachePreloader implements Runnable
{

	private static final Logger logger = Logger.getLogger(AppCachePreloader.class.getName());
	
	
	public void preload()
	{
		BannerLookupCacheDAO dao = BannerLookupCacheDAO.getInstance();
		
		Method[] methods = BannerLookupCacheDAO.class.getDeclaredMethods();
		
		List<Method> invokeMethodList = new ArrayList<Method>();
		
		if (ArrayUtils.isNotEmpty(methods))
		{
			// Fetch all reload cache methods that with this format (reloadXXXCache)
			for (Method method : methods)
			{
				String methodName = method.getName();
				if (methodName.startsWith("reload") && methodName.endsWith("Cache") && 
					method.getParameterCount() == 0)
				{
					invokeMethodList.add(method);
				}
			}
			
			// Invoke all reload cache methods
			if (CollectionUtils.isNotEmpty(invokeMethodList))
			{
				for (Method method : invokeMethodList)
				{
					try
					{
						// setAccessible(true) allows invoking private method
						method.setAccessible(true);
						method.invoke(dao, null);
					}
					catch (Exception e)
					{
						logger.log(Level.WARNING, "Cannot invoke method " + method.getName(), e);
					}
				}
			}
		}
	}

	
	@Override
	public void run()
	{
		preload();
	}
	
	
}
