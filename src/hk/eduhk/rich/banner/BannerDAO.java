package hk.eduhk.rich.banner;

import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.persistence.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.BaseDAO;


@SuppressWarnings("serial")
public class BannerDAO extends BaseDAO
{
	
	// logging
	private static final Level LVL_EXEC_TIME 	= Level.FINE;
	private static final Level LVL_QUERY_DB 	= Level.FINER;
	
	private static BannerDAO instance;
	
	private Logger logger = Logger.getLogger(BannerDAO.class.getName());


	public static synchronized BannerDAO getInstance()
	{
		if (instance == null) instance = new BannerDAO();
		return instance;
	}
	

	public static synchronized BannerDAO getCacheInstance()
	{
		return BannerCacheDAO.getInstance();
	}
	
	
	/**
	 * Get the number of records in the corresponding database table.
	 * 
	 * @param cls Entity class name that extends BanEntity.
	 * @return The number of records.
	 */
	public int getEntityTotalCount(Class<? extends BanEntity> cls)
	{
		long t = System.currentTimeMillis();
		int count = 0;
		
		EntityManager em = null;
		
		String query = "SELECT COUNT(obj) FROM " + cls.getSimpleName() + " obj ";
		logger.log(LVL_QUERY_DB, query);

		try
		{
			em = getDWEntityManager();
			
			TypedQuery<Long> q = em.createQuery(query, Long.class);
			count = q.getSingleResult().intValue();
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return count;
	}
	
	
	/**
	 * Get the course hosting department list of the specified term
	 * 
	 * @param termCode Term code of the specified term 
	 * @param withClassEnrol Whether the course has student registrations
	 * @return
	 */
	public List<String> getCourseSectionDeptList(String termCode, boolean withClassEnrol)
	{
		long t = System.currentTimeMillis();
		List<String> objList = null;
		
		if (!GenericValidator.isBlankOrNull(termCode))
		{
			EntityManager em = null;
			
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT obj.sectionDepartment " +
							"FROM BanClassGroup obj " +
							"WHERE obj.pk.termCode = :termCode ");
			
			if (withClassEnrol) queryBuf.append("AND obj.groupEnroll > 0 ");
			queryBuf.append("GROUP BY obj.sectionDepartment ");
						
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);

			try
			{
				em = getDWEntityManager();
				
				TypedQuery<String> q = em.createQuery(query, String.class);
				q.setParameter("termCode", termCode);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	

	public List<BanCourse> getDeptCourseList(String termCode, String department, boolean withClassEnrol)
	{
		long t = System.currentTimeMillis();
		List<BanCourse> objList = null;
		
		if (!GenericValidator.isBlankOrNull(termCode) && !GenericValidator.isBlankOrNull(department))
		{
			EntityManager em = null;
			
			StringBuilder queryBuf = new StringBuilder();
			
			queryBuf.append("SELECT obj.pk.courseCode, COUNT(DISTINCT obj.pk.groupCode) " +
							"FROM BanClassGroup obj " +
							"WHERE obj.pk.termCode = :termCode " +
							"AND obj.sectionDepartment = :department ");
			
			if (withClassEnrol) queryBuf.append("AND obj.groupEnroll > 0 ");
			queryBuf.append("GROUP BY obj.pk.courseCode " +
							"ORDER BY obj.pk.courseCode ");
						
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);

			Map<String, Integer> courseCountMap = null;

			try
			{
				em = getDWEntityManager();
				
				TypedQuery<Object[]> q = em.createQuery(query, Object[].class);
				q.setParameter("termCode", termCode);
				q.setParameter("department", department);
				
				List<Object[]> resultList = q.getResultList();
				if (CollectionUtils.isNotEmpty(resultList))
				{
					courseCountMap = new LinkedHashMap<String, Integer>();
					
					for (Object[] result : resultList)
					{
						String courseCode = (String) result[0];
						int count = ((Long) result[1]).intValue();
						courseCountMap.put(courseCode, count);
					}
				}
			}
			finally
			{
				pm.close(em);
			}
			
			if (courseCountMap != null)
			{
				// Retrieve BanCourse
				BannerLookupDAO lookupDAO = BannerLookupDAO.getCacheInstance();
				Set<String> courseCodeSet = courseCountMap.keySet();
				objList = lookupDAO.getCourseList(courseCodeSet);
				
				// Associate groupCount to the corresponding BanCourse
				for (BanCourse obj : objList)
				{
					Integer count = courseCountMap.get(obj.getCourseCode());
					if (count != null) obj.getAttrMap().put("groupCount", count);
				}
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	

	public BanClassGroup getClassGroup(String termCode, String groupCode, String courseCode)
	{
		List<BanClassGroup> objList = getClassGroupList(termCode, groupCode, courseCode, false, false);
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	

	public BanClassGroup getClassGroupWithSchd(String termCode, String groupCode, String courseCode)
	{
		List<BanClassGroup> objList = getClassGroupList(termCode, groupCode, courseCode, true, false);
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	
	public List<BanClassGroup> getClassGroupListByCourseCode(String termCode, String courseCode, boolean withClassEnrol)
	{
		return getClassGroupList(termCode, null, courseCode, false, withClassEnrol);
	}
	
	
	private List<BanClassGroup> getClassGroupList(String termCode,
												 String groupCode,
												 String courseCode, 
												 boolean withSchd,
												 boolean withGroupEnroll) 
	{
		long t = System.currentTimeMillis();
		List<BanClassGroup> objList = null;
		
		if (!GenericValidator.isBlankOrNull(termCode) || 
			!GenericValidator.isBlankOrNull(groupCode) ||
			!GenericValidator.isBlankOrNull(courseCode))
		{
			EntityManager em = null;
			
			// Query construction
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT obj FROM BanClassGroup obj ");
			
			if (withSchd) queryBuf.append("LEFT JOIN FETCH obj.scheduleList schd ");
			
			queryBuf.append("WHERE 1=1 ");
							
			if (!GenericValidator.isBlankOrNull(termCode)) queryBuf.append("AND obj.pk.termCode = :termCode ");
			if (!GenericValidator.isBlankOrNull(groupCode)) queryBuf.append("AND obj.pk.groupCode = :groupCode ");
			if (!GenericValidator.isBlankOrNull(courseCode)) queryBuf.append("AND obj.pk.courseCode = :courseCode ");
			if (withGroupEnroll) queryBuf.append("AND obj.groupEnroll > 0 ");

			queryBuf.append("ORDER BY obj.pk.termCode, obj.pk.groupCode, obj.pk.courseCode ");
						
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			try 
			{
				em = getDWEntityManager();
				TypedQuery<BanClassGroup> q = em.createQuery(query, BanClassGroup.class);
				if (!GenericValidator.isBlankOrNull(termCode)) q.setParameter("termCode", termCode);
				if (!GenericValidator.isBlankOrNull(groupCode)) q.setParameter("groupCode", groupCode);
				if (!GenericValidator.isBlankOrNull(courseCode)) q.setParameter("courseCode", courseCode);
				objList = q.getResultList();
			}
			catch (PersistenceException pe)
			{
				String message = "Cannot query class group list (termCode=" + termCode + ", " +  
															    "groupCode=" + groupCode + ", " + 
															    "courseCode=" + courseCode + ")";
				
				logger.log(Level.WARNING, message, pe);
				throw pe;
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	
	public List<BanClassGroupSchedule> getClassGroupScheduleList(String termCode,
																 String groupCode,
																 String courseCode)
	{
		return getClassGroupScheduleList(termCode, groupCode, courseCode, (String[]) null);
	}
	
	
	public BanClassGroupSchedule getClassGroupSchedule(String termCode, String groupCode, String courseCode, String scheduleTypeCode)
	{
		List<BanClassGroupSchedule> objList = getClassGroupScheduleList(termCode, groupCode, courseCode, scheduleTypeCode);
		return CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null;
	}
	

	public List<BanClassGroupSchedule> getClassGroupScheduleList(String termCode,
																 String groupCode,
																 String courseCode,
																 String... scheduleTypeCodes)
	{
		long t = System.currentTimeMillis();
		List<BanClassGroupSchedule> objList = null;
		
		if (!GenericValidator.isBlankOrNull(termCode) || 
			!GenericValidator.isBlankOrNull(groupCode) ||
			!GenericValidator.isBlankOrNull(courseCode))
		{
			EntityManager em = null;
			
			// Convert scheduleTypeCode array into List<String>
			List<String> scheduleTypeCodeList = (scheduleTypeCodes != null) ? Arrays.asList(scheduleTypeCodes) : null;
			
			// Query construction
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT obj FROM BanClassGroupSchedule obj " +
							"JOIN FETCH obj.classGroup "  +
							"LEFT JOIN FETCH obj.instructorSet " +
							"LEFT JOIN FETCH obj.meetingSet ");
						
			queryBuf.append("WHERE 1=1 ");
							
			if (!GenericValidator.isBlankOrNull(termCode)) queryBuf.append("AND obj.pk.termCode = :termCode ");
			if (!GenericValidator.isBlankOrNull(groupCode)) queryBuf.append("AND obj.pk.groupCode = :groupCode ");
			if (!GenericValidator.isBlankOrNull(courseCode)) queryBuf.append("AND obj.pk.courseCode = :courseCode ");
			if (scheduleTypeCodeList != null) queryBuf.append("AND obj.pk.scheduleTypeCode IN :scheduleTypeCodeList ");
			
			queryBuf.append("ORDER BY obj.pk.termCode, obj.pk.groupCode, obj.pk.courseCode ");
						
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			try 
			{
				em = getDWEntityManager();
				TypedQuery<BanClassGroupSchedule> q = em.createQuery(query, BanClassGroupSchedule.class);
				if (!GenericValidator.isBlankOrNull(termCode)) q.setParameter("termCode", termCode);
				if (!GenericValidator.isBlankOrNull(groupCode)) q.setParameter("groupCode", groupCode);
				if (!GenericValidator.isBlankOrNull(courseCode)) q.setParameter("courseCode", courseCode);
				if (scheduleTypeCodeList != null) q.setParameter("scheduleTypeCodeList", scheduleTypeCodeList);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public List<BanClassGroupInstructor> getClassGroupInstructorList(BanClassGroupSchedule schedule)
	{
		long t = System.currentTimeMillis();
		List<BanClassGroupInstructor> objList = null;
		
		if (schedule != null)
		{
			EntityManager em = null;
			
			// Query construction
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT obj " +
							"FROM BanClassGroupInstructor obj " +
							"LEFT JOIN FETCH obj.person person " +
							"WHERE obj.classGroupSchedule = :schedule " +
							"ORDER BY person.lastName, person.firstName ");
						
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			try 
			{
				em = getDWEntityManager();
				TypedQuery<BanClassGroupInstructor> q = em.createQuery(query, BanClassGroupInstructor.class);
				q.setParameter("schedule", schedule);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	/**
	 * Get all students' information who have registered to the specific list of CRNs.
	 * 
	 * @param termCode
	 * @param crnList
	 * @param enrolled
	 * @return
	 */
	public List<BanStudentProgram> getStudentProgramListByCRNList(String termCode, List<String> crnList, Boolean enrolled)
	{
		long t = System.currentTimeMillis();
		List<BanStudentProgram> objList = null;
		
		if (!GenericValidator.isBlankOrNull(termCode) && CollectionUtils.isNotEmpty(crnList))
		{
			EntityManager em = null;
			
			// Query construction
			// This query possibly returns duplicate students with different programmes
			// given that the student has enrolled to more than 1 programme (e.g. 1 Master, 1 PDP)
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT obj " +
							"FROM BanStudentProgram obj " +
							"WHERE obj.pk.termCode = :termCode " +
							"AND obj.pk.personId IN " + 
							"( " +
							"	SELECT reg.pk.personId " +
							" 	FROM BanStudentClassRegistration reg " +
							"	WHERE reg.registered = true " + 
							"	AND reg.pk.termCode = :termCode " +
							"	AND reg.pk.courseCRN IN :crnList " +
							") ");
			
			if (enrolled != null) queryBuf.append("AND obj.enrolled = " + enrolled + " ");
			queryBuf.append("ORDER BY obj.lastName, obj.firstName, obj.pk.programCode ");
			
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			// Query for class registration
			queryBuf.delete(0,  queryBuf.length());
			queryBuf.append("SELECT reg " +
							"FROM BanStudentClassRegistration reg " +
							"WHERE reg.registered = true " +
							"AND reg.pk.termCode = :termCode " +
							"AND reg.pk.courseCRN IN :crnList ");
			
			String regQuery = queryBuf.toString();
			logger.log(LVL_QUERY_DB, regQuery);
			
			try 
			{
				// Query BanStudentProgram
				em = getDWEntityManager();
				TypedQuery<BanStudentProgram> q = em.createQuery(query, BanStudentProgram.class);
				q.setParameter("termCode", termCode);
				q.setParameter("crnList", crnList);
				objList = q.getResultList();
				
				if (CollectionUtils.isNotEmpty(objList))
				{
					// Query BanStudentClassRegistration
					TypedQuery<BanStudentClassRegistration> rq = em.createQuery(regQuery, BanStudentClassRegistration.class);
					rq.setParameter("termCode", termCode);
					rq.setParameter("crnList", crnList);
					List<BanStudentClassRegistration> regList = rq.getResultList();
					
					// Attach corresponding CRN and SectionNumber to BanStudentProgram instances
					Map<String, BanStudentClassRegistration> regMap = regList.stream().collect(Collectors.toMap(r -> (r.getPersonId() + "_" + r.getProgramCode()), r -> r, (r1, r2) -> r1, LinkedHashMap::new));
					
					Iterator<BanStudentProgram> objIter = objList.iterator();
					while (objIter.hasNext())
					{
						BanStudentProgram obj = objIter.next();
						BanStudentClassRegistration reg = regMap.get(obj.getPersonId() + "_" + obj.getProgramCode());
						
						if (reg != null)
						{
							obj.getAttrMap().put("courseCRN", reg.getCourseCRN());
							obj.getAttrMap().put("sectionNumber", reg.getSectionNumber());
						}
						
						// Cannot lookup the record, this is a duplicate record.
						//else objIter.remove();
					}
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public List<BanClassGroupMeeting> getClassGroupMeetingList(BanClassGroupSchedule schedule)
	{
		List<BanClassGroupMeeting> objList = null;
		
		if (schedule != null)
		{
			EntityManager em = null;
			
			// Query construction
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT obj " +
							"FROM BanClassGroupMeeting obj " +
							"WHERE obj.classGroupSchedule = :schedule " +
							"ORDER BY obj.pk.startDate, obj.pk.beginTime, obj.pk.buildingCode ");
			
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			// Query BanClassGroupMeeting
			try 
			{
				em = getDWEntityManager();
				TypedQuery<BanClassGroupMeeting> q = em.createQuery(query, BanClassGroupMeeting.class);
				q.setParameter("schedule", schedule);
				objList = q.getResultList();
				
				// Remove null object from BanClassGroupMeeting
				for (int n=objList.size()-1;n>=0;n--)
				{
					BanClassGroupMeeting obj = objList.get(n);
					if (obj == null) objList.remove(n);
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	

	/**
	 * Get all programmes where the students have registered to the courses in the specified terms. 
	 * 
	 * @param termCodeList
	 * @return
	 */
	public List<String> getRegProgramCodeListByTerm(List<String> termCodeList)
	{
		long t = System.currentTimeMillis();
		List<String> objList = null;
		
		if (CollectionUtils.isNotEmpty(termCodeList))
		{
			EntityManager em = null;
			
			// Query construction
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT classReg.pk.programCode " +
							"FROM BanStudentClassRegistration classReg " +
							"WHERE classReg.pk.termCode IN :termCodeList " +
							"ORDER BY classReg.pk.programCode ");
			
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			// Query BanCourse
			try 
			{
				em = getDWEntityManager();
				TypedQuery<String> q = em.createQuery(query, String.class);
				q.setParameter("termCodeList", termCodeList);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(Level.INFO, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return objList;
	}
	
	
	/**
	 * Get all courses where the students have registered to the courses in the specified terms and programmes. 
	 * 
	 * @param termCodeList
	 * @return
	 */
	public List<String> getRegCourseCodeListByTermProgram(List<String> termCodeList, List<String> programCodeList)
	{
		long t = System.currentTimeMillis();
		List<String> objList = null;
		
		if (CollectionUtils.isNotEmpty(termCodeList) && CollectionUtils.isNotEmpty(programCodeList))
		{
			EntityManager em = null;
			
			// Query construction
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT DISTINCT classReg.cls.course.courseCode " +
							"FROM BanStudentClassRegistration classReg " +
							"WHERE classReg.pk.termCode IN :termCodeList " +
							"AND classReg.pk.programCode IN :programCodeList " +
							"ORDER BY classReg.cls.course.courseCode ");
			
			// Query logging
			String query = queryBuf.toString();
			logger.log(LVL_QUERY_DB, query);
			
			// Query BanCourse
			try 
			{
				em = getDWEntityManager();
				TypedQuery<String> q = em.createQuery(query, String.class);
				q.setParameter("termCodeList", termCodeList);
				q.setParameter("programCodeList", programCodeList);
				objList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(Level.INFO, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return objList;
	}
	
}
