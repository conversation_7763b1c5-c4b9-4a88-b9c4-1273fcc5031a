package hk.eduhk.rich.banner;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class BanClassMeetingInstructorPK implements Serializable
{

	@Column(name = "TermCode", length = 6)
	private String termCode;
	
	@Column(name = "CourseCRN", length = 6)
	private String courseCRN;

	@Column(name = "ScheduleTypeCode", length = 3)
	private String scheduleTypeCode;
	
	@Column(name = "StartDate", length = 8)
	private String startDate;
	
	@Column(name = "EndDate", length = 8)
	private String endDate;
	
	@Column(name = "DayOfWeek", length = 5)
	@Convert(converter = NullToStringConverter.class)
	private String dayOfWeek;
	
	@Column(name = "BeginTime", length = 5)
	@Convert(converter = NullToStringConverter.class)
	private String beginTime;
	
	@Column(name = "EndTime", length = 5)
	@Convert(converter = NullToStringConverter.class)
	private String endTime;
	
	@Column(name = "MeetingHoursWeek", length = 5)
	@Convert(converter = NullToStringConverter.class)
	private String meetingHoursWeek;
	
	@Column(name = "Category", length = 2)
	private String category;
	
	@Column(name = "BuildingCode", length = 6)
	@Convert(converter = NullToStringConverter.class)
	private String buildingCode;
	
	@Column(name = "RoomCode", length = 10)
	@Convert(converter = NullToStringConverter.class)
	private String roomCode;
	
	@Column(name = "PersonId", length = 9)
	@Convert(converter = NullToStringConverter.class)
	private String personId;
	


	
	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String termCode)
	{
		this.termCode = termCode;
	}

	
	public String getCourseCRN()
	{
		return courseCRN;
	}

	
	public void setCourseCRN(String courseCRN)
	{
		this.courseCRN = courseCRN;
	}



	public String getScheduleTypeCode()
	{
		return scheduleTypeCode;
	}


	
	public void setScheduleTypeCode(String scheduleTypeCode)
	{
		this.scheduleTypeCode = scheduleTypeCode;
	}


	
	public String getStartDate()
	{
		return startDate;
	}


	
	public void setStartDate(String startDate)
	{
		this.startDate = startDate;
	}


	
	public String getEndDate()
	{
		return endDate;
	}


	
	public void setEndDate(String endDate)
	{
		this.endDate = endDate;
	}


	
	public String getDayOfWeek()
	{
		return dayOfWeek;
	}


	
	public void setDayOfWeek(String dayOfWeek)
	{
		this.dayOfWeek = dayOfWeek;
	}


	
	public String getBeginTime()
	{
		return beginTime;
	}


	
	public void setBeginTime(String beginTime)
	{
		this.beginTime = beginTime;
	}


	
	public String getEndTime()
	{
		return endTime;
	}


	
	public void setEndTime(String endTime)
	{
		this.endTime = endTime;
	}


	
	public String getMeetingHoursWeek()
	{
		return meetingHoursWeek;
	}


	
	public void setMeetingHoursWeek(String meetingHoursWeek)
	{
		this.meetingHoursWeek = meetingHoursWeek;
	}


	
	public String getCategory()
	{
		return category;
	}


	
	public void setCategory(String category)
	{
		this.category = category;
	}


	
	public String getBuildingCode()
	{
		return buildingCode;
	}


	
	public void setBuildingCode(String buildingCode)
	{
		this.buildingCode = buildingCode;
	}


	
	public String getRoomCode()
	{
		return roomCode;
	}


	
	public void setRoomCode(String roomCode)
	{
		this.roomCode = roomCode;
	}


	
	public String getPersonId()
	{
		return personId;
	}


	
	public void setPersonId(String personId)
	{
		this.personId = personId;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		result = prime * result + ((courseCRN == null) ? 0 : courseCRN.hashCode());
		result = prime * result + ((scheduleTypeCode == null) ? 0 : scheduleTypeCode.hashCode());
		result = prime * result + ((startDate == null) ? 0 : startDate.hashCode());
		result = prime * result + ((endDate == null) ? 0 : endDate.hashCode());
		result = prime * result + ((dayOfWeek == null) ? 0 : dayOfWeek.hashCode());
		result = prime * result + ((beginTime == null) ? 0 : beginTime.hashCode());
		result = prime * result + ((endTime == null) ? 0 : endTime.hashCode());
		result = prime * result + ((meetingHoursWeek == null) ? 0 : meetingHoursWeek.hashCode());
		result = prime * result + ((category == null) ? 0 : category.hashCode());
		result = prime * result + ((buildingCode == null) ? 0 : buildingCode.hashCode());
		result = prime * result + ((roomCode == null) ? 0 : roomCode.hashCode());
		result = prime * result + ((personId == null) ? 0 : personId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassMeetingInstructorPK other = (BanClassMeetingInstructorPK) obj;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		if (courseCRN == null)
		{
			if (other.courseCRN != null)
				return false;
		}
		else if (!courseCRN.equals(other.courseCRN))
			return false;
		if (scheduleTypeCode == null)
		{
			if (other.scheduleTypeCode != null)
				return false;
		}
		else if (!scheduleTypeCode.equals(other.scheduleTypeCode))
			return false;
		if (startDate == null)
		{
			if (other.startDate != null)
				return false;
		}
		else if (!startDate.equals(other.startDate))
			return false;
		if (endDate == null)
		{
			if (other.endDate != null)
				return false;
		}
		else if (!endDate.equals(other.endDate))
			return false;
		if (dayOfWeek == null)
		{
			if (other.dayOfWeek != null)
				return false;
		}
		else if (!dayOfWeek.equals(other.dayOfWeek))
			return false;
		if (beginTime == null)
		{
			if (other.beginTime != null)
				return false;
		}
		else if (!beginTime.equals(other.beginTime))
			return false;
		if (endTime == null)
		{
			if (other.endTime != null)
				return false;
		}
		else if (!endTime.equals(other.endTime))
			return false;
		if (meetingHoursWeek == null)
		{
			if (other.meetingHoursWeek != null)
				return false;
		}
		else if (!meetingHoursWeek.equals(other.meetingHoursWeek))
			return false;
		if (category == null)
		{
			if (other.category != null)
				return false;
		}
		else if (!category.equals(other.category))
			return false;
		if (buildingCode == null)
		{
			if (other.buildingCode != null)
				return false;
		}
		else if (!buildingCode.equals(other.buildingCode))
			return false;
		if (roomCode == null)
		{
			if (other.roomCode != null)
				return false;
		}
		else if (!roomCode.equals(other.roomCode))
			return false;
		if (personId == null)
		{
			if (other.personId != null)
				return false;
		}
		else if (!personId.equals(other.personId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassMeetingInstructorPK [termCode=" + termCode + ", courseCRN=" + courseCRN + ", scheduleTypeCode="
				+ scheduleTypeCode + ", startDate=" + startDate + ", endDate=" + endDate + ", dayOfWeek=" + dayOfWeek
				+ ", beginTime=" + beginTime + ", endTime=" + endTime + ", meetingHoursWeek=" + meetingHoursWeek
				+ ", category=" + category + ", buildingCode=" + buildingCode + ", roomCode=" + roomCode + ", personId="
				+ personId + "]";
	}




	
}
