package hk.eduhk.rich.banner;

import javax.persistence.*;


@Entity
@Table(name = "AMIS.StudentClassRegistration")
@SuppressWarnings("serial")
public class BanStudentClassRegistration extends BanEntity
{

	@EmbeddedId
	private BanStudentClassRegistrationPK pk = null;
	
	@Column(name = "SectionNumber", length = 3)
	private String sectionNumber;

	@Column(name = "RegistrationStatus", length = 3)
	private String registrationStatus;

	@Column(name = "IsRegistered")
	private boolean registered;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "TermCode", referencedColumnName = "TermCode", insertable = false, updatable = false),
		@JoinColumn(name = "CourseCRN", referencedColumnName = "CourseCRN", insertable = false, updatable = false)
	})	
	private BanClass cls;

	
	public BanStudentClassRegistrationPK getPk()
	{
		if (pk == null) pk = new BanStudentClassRegistrationPK();
		return pk;
	}

	
	public void setPk(BanStudentClassRegistrationPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public Integer getPersonId()
	{
		return pk.getPersonId();
	}

	
	public void setPersonId(Integer personId)
	{
		pk.setPersonId(personId);
	}

	
	public String getProgramCode()
	{
		return pk.getProgramCode();
	}
	
	
	public void setProgramCode(String programCode)
	{
		pk.setProgramCode(programCode);
	}
	
	
	public String getCourseCRN()
	{
		return pk.getCourseCRN();
	}
	
	
	public void setCourseCRN(String courseCRN)
	{
		pk.setCourseCRN(courseCRN);
	}
		
	
	public String getSectionNumber()
	{
		return sectionNumber;
	}

	
	public void setSectionNumber(String sectionNumber)
	{
		this.sectionNumber = sectionNumber;
	}


	public String getRegistrationStatus()
	{
		return registrationStatus;
	}

	
	public void setRegistrationStatus(String registrationStatus)
	{
		this.registrationStatus = registrationStatus;
	}

	
	public boolean isRegistered()
	{
		return registered;
	}

	
	public void setRegistered(boolean registered)
	{
		this.registered = registered;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanStudentClassRegistration other = (BanStudentClassRegistration) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanStudentClassRegistration [pk=" + pk + ", registrationStatus=" + registrationStatus + ", registered="
				+ registered + "]";
	}
	
}
