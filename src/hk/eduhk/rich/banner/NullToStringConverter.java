package hk.eduhk.rich.banner;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class NullToStringConverter implements AttributeConverter<String, String> 
{
	@Override
    public String convertToDatabaseColumn(String Value) {
        if (Value == null) {
            return null;
        }
        return Value;
    }
 
    @Override
    public String convertToEntityAttribute(String dbValue) {
        if(dbValue == null) {
        	return "";
        }
        return dbValue;
    }
}
