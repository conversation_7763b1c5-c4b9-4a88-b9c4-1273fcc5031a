package hk.eduhk.rich.banner;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class BanClassGroupPK implements Serializable
{

	@Column(name = "TermCode", length = 6)
	private String termCode;

	@Column(name = "GroupCode", length = 5)
	private String groupCode; 

	@Column(name = "CourseCode", length = 9)
	private String courseCode;

	
	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String termCode)
	{
		this.termCode = termCode;
	}

	
	public String getGroupCode()
	{
		return groupCode;
	}

	
	public void setGroupCode(String groupCode)
	{
		this.groupCode = groupCode;
	}

	
	public String getCourseCode()
	{
		return courseCode;
	}

	
	public void setCourseCode(String courseCode)
	{
		this.courseCode = courseCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((courseCode == null) ? 0 : courseCode.hashCode());
		result = prime * result + ((groupCode == null) ? 0 : groupCode.hashCode());
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassGroupPK other = (BanClassGroupPK) obj;
		if (courseCode == null)
		{
			if (other.courseCode != null)
				return false;
		}
		else if (!courseCode.equals(other.courseCode))
			return false;
		if (groupCode == null)
		{
			if (other.groupCode != null)
				return false;
		}
		else if (!groupCode.equals(other.groupCode))
			return false;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassGroupPK [termCode=" + termCode + ", groupCode=" + groupCode + ", courseCode=" + courseCode
				+ "]";
	}
	
}
