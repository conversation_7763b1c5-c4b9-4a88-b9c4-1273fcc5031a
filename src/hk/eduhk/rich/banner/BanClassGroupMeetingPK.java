package hk.eduhk.rich.banner;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class BanClassGroupMeetingPK implements Serializable
{

	@Column(name = "TermCode", length = 6)
	private String termCode;

	@Column(name = "GroupCode", length = 5)
	private String groupCode; 

	@Column(name = "CourseCode", length = 9)
	private String courseCode;

	@Column(name = "ScheduleTypeCode", length = 3)
	private String scheduleTypeCode;
	
	@Column(name = "StartDate")
	private Date startDate;
	
	@Column(name = "EndDate")
	private Date endDate;
	
	@Column(name = "DayOfWeek")
	private Integer dayOfWeek;
	
	@Column(name = "BeginTime", length = 5)
	private String beginTime;
	
	@Column(name = "EndTime", length = 5)
	private String endTime;
	
	@Column(name = "BuildingCode", length = 6)
	private String buildingCode;
	
	@Column(name = "RoomCode", length = 10)
	private String roomCode;

	
	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String termCode)
	{
		this.termCode = termCode;
	}

	
	public String getGroupCode()
	{
		return groupCode;
	}

	
	public void setGroupCode(String groupCode)
	{
		this.groupCode = groupCode;
	}

	
	public String getCourseCode()
	{
		return courseCode;
	}

	
	public void setCourseCode(String courseCode)
	{
		this.courseCode = courseCode;
	}

	
	public String getScheduleTypeCode()
	{
		return scheduleTypeCode;
	}

	
	public void setScheduleTypeCode(String scheduleTypeCode)
	{
		this.scheduleTypeCode = scheduleTypeCode;
	}

	
	public Date getStartDate()
	{
		return startDate;
	}

	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}

	
	public Date getEndDate()
	{
		return endDate;
	}

	
	public void setEndDate(Date endDate)
	{
		this.endDate = endDate;
	}
	
	
	public int getDayOfWeek()
	{
		return dayOfWeek;
	}

	
	public void setDayOfWeek(int dayOfWeek)
	{
		this.dayOfWeek = dayOfWeek;
	}


	public String getBeginTime()
	{
		return beginTime;
	}

	
	public void setBeginTime(String beginTime)
	{
		this.beginTime = beginTime;
	}

	
	public String getEndTime()
	{
		return endTime;
	}

	
	public void setEndTime(String endTime)
	{
		this.endTime = endTime;
	}

	
	public String getBuildingCode()
	{
		return buildingCode;
	}

	
	public void setBuildingCode(String buildingCode)
	{
		this.buildingCode = buildingCode;
	}

	
	public String getRoomCode()
	{
		return roomCode;
	}

	
	public void setRoomCode(String roomCode)
	{
		this.roomCode = roomCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((buildingCode == null) ? 0 : buildingCode.hashCode());
		result = prime * result + ((courseCode == null) ? 0 : courseCode.hashCode());
		result = prime * result + ((dayOfWeek == null) ? 0 : dayOfWeek.hashCode());
		result = prime * result + ((endDate == null) ? 0 : endDate.hashCode());
		result = prime * result + ((endTime == null) ? 0 : endTime.hashCode());
		result = prime * result + ((groupCode == null) ? 0 : groupCode.hashCode());
		result = prime * result + ((roomCode == null) ? 0 : roomCode.hashCode());
		result = prime * result + ((scheduleTypeCode == null) ? 0 : scheduleTypeCode.hashCode());
		result = prime * result + ((startDate == null) ? 0 : startDate.hashCode());
		result = prime * result + ((beginTime == null) ? 0 : beginTime.hashCode());
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassGroupMeetingPK other = (BanClassGroupMeetingPK) obj;
		if (buildingCode == null)
		{
			if (other.buildingCode != null)
				return false;
		}
		else if (!buildingCode.equals(other.buildingCode))
			return false;
		if (courseCode == null)
		{
			if (other.courseCode != null)
				return false;
		}
		else if (!courseCode.equals(other.courseCode))
			return false;
		if (dayOfWeek == null)
		{
			if (other.dayOfWeek != null)
				return false;
		}
		else if (!dayOfWeek.equals(other.dayOfWeek))
			return false;
		if (endDate == null)
		{
			if (other.endDate != null)
				return false;
		}
		else if (!endDate.equals(other.endDate))
			return false;
		if (endTime == null)
		{
			if (other.endTime != null)
				return false;
		}
		else if (!endTime.equals(other.endTime))
			return false;
		if (groupCode == null)
		{
			if (other.groupCode != null)
				return false;
		}
		else if (!groupCode.equals(other.groupCode))
			return false;
		if (roomCode == null)
		{
			if (other.roomCode != null)
				return false;
		}
		else if (!roomCode.equals(other.roomCode))
			return false;
		if (scheduleTypeCode == null)
		{
			if (other.scheduleTypeCode != null)
				return false;
		}
		else if (!scheduleTypeCode.equals(other.scheduleTypeCode))
			return false;
		if (startDate == null)
		{
			if (other.startDate != null)
				return false;
		}
		else if (!startDate.equals(other.startDate))
			return false;
		if (beginTime == null)
		{
			if (other.beginTime != null)
				return false;
		}
		else if (!beginTime.equals(other.beginTime))
			return false;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassGroupMeetingPK [termCode=" + termCode + ", groupCode=" + groupCode + ", courseCode="
				+ courseCode + ", scheduleTypeCode=" + scheduleTypeCode + ", startDate=" + startDate + ", endDate="
				+ endDate + ", dayOfWeek=" + dayOfWeek + ", beginTime=" + beginTime + ", endTime=" + endTime
				+ ", buildingCode=" + buildingCode + ", roomCode=" + roomCode + "]";
	}
	
}
