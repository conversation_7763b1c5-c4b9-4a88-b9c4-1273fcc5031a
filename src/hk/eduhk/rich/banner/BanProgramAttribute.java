package hk.eduhk.rich.banner;

import java.io.Serializable;

import javax.persistence.*;


@Entity
@Table(name = "CustomFinal.DimProgramAttribute")
@SuppressWarnings("serial")
public class BanProgramAttribute implements Serializable
{
	
	@Id
	@Column(name = "ProgramAttributeKey")
	private int programAttributeKey;
	
	@Column(name = "StartTerm", length = 6)
	private String startTerm;
	
	@Column(name = "EndTerm", length = 6)
	private String endTerm;

	@Column(name = "ProgramCode", length = 12)
	private String programCode;

	@Column(name = "ProgramYear")
	private int programYear;

	@Column(name = "FacultyCode", length = 30)
	private String facultyCode;

	@Column(name = "DepartmentCode", length = 30)
	private String departmentCode;

	@Column(name = "StudyModeCode", length = 10)
	private String studyModeCode;

	@Column(name = "ProgramTypeCode", length = 10)
	private String programTypeCode;
	
	
	public BanProgramAttribute()
	{
	}


	public int getProgramAttributeKey()
	{
		return programAttributeKey;
	}


	public void setProgramAttributeKey(int programAttributeKey)
	{
		this.programAttributeKey = programAttributeKey;
	}


	public String getStartTerm()
	{
		return startTerm;
	}


	public void setStartTerm(String startTerm)
	{
		this.startTerm = startTerm;
	}


	public String getEndTerm()
	{
		return endTerm;
	}


	public void setEndTerm(String endTerm)
	{
		this.endTerm = endTerm;
	}


	public String getProgramCode()
	{
		return programCode;
	}


	public void setProgramCode(String programCode)
	{
		this.programCode = programCode;
	}

	
	public int getProgramYear()
	{
		return programYear;
	}

	
	public void setProgramYear(int programYear)
	{
		this.programYear = programYear;
	}

	
	public String getFacultyCode()
	{
		return facultyCode;
	}

	
	public void setFacultyCode(String facultyCode)
	{
		this.facultyCode = facultyCode;
	}

	
	public String getDepartmentCode()
	{
		return departmentCode;
	}

	
	public void setDepartmentCode(String departmentCode)
	{
		this.departmentCode = departmentCode;
	}

	
	public String getStudyModeCode()
	{
		return studyModeCode;
	}

	
	public void setStudyModeCode(String studyModeCode)
	{
		this.studyModeCode = studyModeCode;
	}

	
	public String getProgramTypeCode()
	{
		return programTypeCode;
	}

	
	public void setProgramTypeCode(String programTypeCode)
	{
		this.programTypeCode = programTypeCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + programAttributeKey;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanProgramAttribute other = (BanProgramAttribute) obj;
		if (programAttributeKey != other.programAttributeKey)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanProgramAttribute [programAttributeKey=" + programAttributeKey + ", startTerm=" + startTerm
				+ ", endTerm=" + endTerm + ", programCode=" + programCode + ", programYear=" + programYear
				+ ", facultyCode=" + facultyCode + ", departmentCode=" + departmentCode + ", studyModeCode="
				+ studyModeCode + ", programTypeCode=" + programTypeCode + "]";
	}
	
	
}
