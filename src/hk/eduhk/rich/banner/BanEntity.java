package hk.eduhk.rich.banner;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.beanutils.BeanUtils;


@SuppressWarnings("serial")
public class BanEntity implements Cloneable, Serializable
{

	private Map<String, Object> attrMap;

	private static final Logger logger = Logger.getLogger(BanEntity.class.getName());

	
	public Map<String, Object> getAttrMap()
	{
		if (attrMap == null) attrMap = new HashMap<String, Object>();
		return attrMap;
	}

	
	public void setAttrMap(Map<String, Object> attrMap)
	{
		this.attrMap = attrMap;
	}
	
	
	@Override
	public BanEntity clone()
	{
		BanEntity obj = null;
		
		try
		{
			obj = (BanEntity) BeanUtils.cloneBean(this);
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot clone " + this.getClass().getSimpleName() + " - " + e.getMessage());
		}
		
		return obj;
	}
	
}
