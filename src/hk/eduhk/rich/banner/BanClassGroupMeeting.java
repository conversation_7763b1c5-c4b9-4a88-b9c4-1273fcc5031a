package hk.eduhk.rich.banner;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.primefaces.model.DefaultScheduleEvent;
import org.primefaces.model.ScheduleEvent;


@Entity
@Table(name = "AMIS.ClassGroupMeeting")
@SuppressWarnings("serial")
public class BanClassGroupMeeting extends BanEntity
{

	public static final String ROOM_CODE_NO_ROOM = "-";
	
	@EmbeddedId
	private BanClassGroupMeetingPK pk = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "TermCode", referencedColumnName = "TermCode", insertable = false, updatable = false),
		@JoinColumn(name = "GroupCode", referencedColumnName = "GroupCode", insertable = false, updatable = false),
		@JoinColumn(name = "CourseCode", referencedColumnName = "CourseCode", insertable = false, updatable = false),
		@JoinColumn(name = "ScheduleTypeCode", referencedColumnName = "ScheduleTypeCode", insertable = false, updatable = false)
	})
	private BanClassGroupSchedule classGroupSchedule = null;
	
	@Transient
	private List<ScheduleEvent> scheduleEventList = null;
	
	private static final Logger logger = Logger.getLogger(BanClassGroupSchedule.class.getName());

	
	public BanClassGroupMeetingPK getPk()
	{
		if (pk == null) return new BanClassGroupMeetingPK();
		return pk;
	}

	
	public void setPk(BanClassGroupMeetingPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public String getGroupCode()
	{
		return pk.getGroupCode();
	}

	
	public void setGroupCode(String groupCode)
	{
		pk.setGroupCode(groupCode);
	}

	
	public String getCourseCode()
	{
		return pk.getCourseCode();
	}

	
	public void setCourseCode(String courseCode)
	{
		pk.setCourseCode(courseCode);
	}

	
	public String getScheduleTypeCode()
	{
		return pk.getScheduleTypeCode();
	}

	
	public void setScheduleTypeCode(String scheduleTypeCode)
	{
		pk.setScheduleTypeCode(scheduleTypeCode);
	}

	
	public Date getStartDate()
	{
		return pk.getStartDate();
	}

	
	public void setStartDate(Date startDate)
	{
		pk.setStartDate(startDate);
	}

	
	public Date getEndDate()
	{
		return pk.getEndDate();
	}

	
	public void setEndDate(Date endDate)
	{
		pk.setEndDate(endDate);
	}

	
	public Integer getDayOfWeek()
	{
		return pk.getDayOfWeek();
	}

	
	public void setDayOfWeek(Integer dayOfWeek)
	{
		pk.setDayOfWeek(dayOfWeek);
	}
	
	
	public String getBeginTime()
	{
		return pk.getBeginTime();
	}

	
	public void setBeginTime(String beginTime)
	{
		pk.setBeginTime(beginTime);
	}

	
	public String getEndTime()
	{
		return pk.getEndTime();
	}

	
	public void setEndTime(String endTime)
	{
		pk.setEndTime(endTime);
	}

	
	public String getBuildingCode()
	{
		return StringUtils.defaultString(pk.getBuildingCode());
	}

	
	public void setBuildingCode(String buildingCode)
	{
		pk.setEndTime(buildingCode);
	}

	
	public String getRoomCode()
	{
		return StringUtils.defaultString(pk.getRoomCode());
	}

	
	public void setRoomCode(String roomCode)
	{
		pk.setEndTime(roomCode);
	}

	
	public List<ScheduleEvent> getScheduleEventList()
	{
		if (scheduleEventList == null)
		{
			scheduleEventList = new ArrayList<ScheduleEvent>();
			
			if (getStartDate() != null && getEndDate() != null &&
				getBeginTime() != null && getEndTime() != null)
			{
				LocalDateTime localTime = LocalDateTime.ofInstant(getStartDate().toInstant(), ZoneId.systemDefault());
				
				// If the start date is the not target day of week
				// Adjust the date to the next day of week 
				if (localTime.getDayOfWeek().getValue() != getDayOfWeek())
				{
					localTime = localTime.with(TemporalAdjusters.next(DayOfWeek.of(getDayOfWeek())));
				}

				// Date for iteration
				Date date = Date.from(localTime.atZone(ZoneId.systemDefault()).toInstant());
				
				// Iteration of every week defined in the period
				while (date.equals(getEndDate()) || date.before(getEndDate()))
				{
					// Get the start time of the session
					Calendar startCal = Calendar.getInstance();
					startCal.setTime(date);
					startCal.set(Calendar.HOUR, Integer.parseInt(getBeginTime().substring(0,2)));
					startCal.set(Calendar.MINUTE, Integer.parseInt(getBeginTime().substring(2,4)));
					Date meetingStartTime = startCal.getTime();
					
					// Get the end time of the session
					Calendar endCal = Calendar.getInstance();
					endCal.setTime(date);
					endCal.set(Calendar.HOUR, Integer.parseInt(getEndTime().substring(0,2)));
					endCal.set(Calendar.MINUTE, Integer.parseInt(getEndTime().substring(2,4)));
					Date meetingEndTime = endCal.getTime();

					logger.log(Level.FINEST, "Period=" + meetingStartTime + " - "  + meetingEndTime);

					// Room of the meeting
					String room = null;
					
					if (!StringUtils.equals(getRoomCode(), ROOM_CODE_NO_ROOM))
					{
						room = StringUtils.startsWith(getRoomCode(), getBuildingCode()) ? getRoomCode() : getBuildingCode() + " "  + getRoomCode();
					}
					else
					{
						room = "No Room";
					}
					
					// Add a new instance of ScheduleEvent to the List
					DefaultScheduleEvent scheduleEvent = new DefaultScheduleEvent();
					scheduleEventList.add(scheduleEvent);
					
					// Add 7 days to get the next week session
					Calendar nextWeekCal = Calendar.getInstance();
					nextWeekCal.setTime(date);
					nextWeekCal.add(Calendar.DATE, 7);
					date = nextWeekCal.getTime();
				}
			}
		}
		
		return scheduleEventList;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassGroupMeeting other = (BanClassGroupMeeting) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassGroupMeeting [pk=" + pk + "]";
	}
	
	
}
