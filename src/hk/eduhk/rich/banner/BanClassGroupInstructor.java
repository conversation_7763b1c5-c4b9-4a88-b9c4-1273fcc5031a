package hk.eduhk.rich.banner;

import java.util.Comparator;

import javax.persistence.*;


@Entity
@Table(name = "AMIS.ClassGroupInstructor")
@SuppressWarnings("serial")
public class BanClassGroupInstructor extends BanEntity
{
	
	public static final Comparator<BanClassGroupInstructor> COMPARATOR_NAME = Comparator.comparing(BanClassGroupInstructor::getName);
	
	
	@EmbeddedId
	private BanClassGroupInstructorPK pk = null;
	
	@Column(name = "UserID", length = 32)
	private String userId;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "TermCode", referencedColumnName = "TermCode", insertable = false, updatable = false),
		@JoinColumn(name = "GroupCode", referencedColumnName = "GroupCode", insertable = false, updatable = false),
		@JoinColumn(name = "CourseCode", referencedColumnName = "CourseCode", insertable = false, updatable = false),
		@JoinColumn(name = "ScheduleTypeCode", referencedColumnName = "ScheduleTypeCode", insertable = false, updatable = false)
	})
	private BanClassGroupSchedule classGroupSchedule = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "PersonID", referencedColumnName = "PersonID", insertable = false, updatable = false)
	private BanPerson person;
	
	@Transient
	private String name;
	
	
	public BanClassGroupInstructorPK getPk()
	{
		if (pk == null) return new BanClassGroupInstructorPK();
		return pk;
	}

	
	public void setPk(BanClassGroupInstructorPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public String getGroupCode()
	{
		return pk.getGroupCode();
	}

	
	public void setGroupCode(String groupCode)
	{
		pk.setGroupCode(groupCode);
	}

	
	public String getCourseCode()
	{
		return pk.getCourseCode();
	}

	
	public void setCourseCode(String courseCode)
	{
		pk.setCourseCode(courseCode);
	}

	
	public String getScheduleTypeCode()
	{
		return pk.getScheduleTypeCode();
	}

	
	public void setScheduleTypeCode(String scheduleTypeCode)
	{
		pk.setScheduleTypeCode(scheduleTypeCode);
	}

	
	public Integer getPersonId()
	{
		return pk.getPersonId();
	}

	
	public void setPersonId(Integer personId)
	{
		pk.setPersonId(personId);
	}
		
	
	public String getUserId()
	{
		return userId;
	}

	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}

	
	public BanPerson getPerson()
	{
		if (person != null)
		{
			try
			{
				person.getPersonId();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					BannerLookupDAO dao = BannerLookupDAO.getCacheInstance();
					person = dao.getPerson(getPersonId());
				}
				else
				{
					throw re;
				}
			}
		}
		
		return person;
	}
	
	
	/**
	 * Get the instructor name.
	 * 
	 * The name is actually retrieved from BanPerson which redundant to put it in BanClassGroupInstructor class
	 * However, this method is to make the static name Comparator to work 
	 * 
	 * @return
	 */
	public String getName()
	{
		if (name == null && getPerson() != null)
		{
			name = getPerson().getName();
		}
		
		return name;
	}


	public BanClassGroupSchedule getClassGroupSchedule()
	{
		return classGroupSchedule;
	}

	
	public void setClassGroupSchedule(BanClassGroupSchedule classGroupSchedule)
	{
		this.classGroupSchedule = classGroupSchedule;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassGroupInstructor other = (BanClassGroupInstructor) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassGroupInstructor [pk=" + pk + ", userId=" + userId + "]";
	}
	
}
