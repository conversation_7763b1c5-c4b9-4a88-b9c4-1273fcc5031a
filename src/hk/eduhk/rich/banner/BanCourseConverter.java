package hk.eduhk.rich.banner;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.FacesConverter;

import hk.eduhk.rich.BaseConverter;


@FacesConverter("hk.eduhk.rich.banner.BanCourseConverter")
public class BanCourseConverter extends BaseConverter
{
	
	@Override
	public Object getAsObject(FacesContext fCtx, UIComponent component, String value) 
	{
		BannerLookupDAO lookupDAO = BannerLookupDAO.getCacheInstance();
		return lookupDAO.getCourse(value);
	}
	
	
	@Override
	public String getAsString(FacesContext fCtx, UIComponent component, Object value) 
	{
		return (value != null && value instanceof BanCourse) ? ((BanCourse) value).getCourseCode() : null;
	}	
	
	
}
