package hk.eduhk.rich.banner;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class BanStudentClassRegistrationPK implements Serializable
{

	@Column(name = "TermCode", length = 6)
	private String termCode;

	@Column(name = "PersonID")
	private Integer personId;

	@Column(name = "ProgramCode", length = 12)
	private String programCode;

	@Column(name = "CourseCRN", length = 5)
	private String courseCRN;


	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String termCode)
	{
		this.termCode = termCode;
	}


	public Integer getPersonId()
	{
		return personId;
	}

	
	public void setPersonId(Integer personId)
	{
		this.personId = personId;
	}

	
	public String getProgramCode()
	{
		return programCode;
	}

	
	public void setProgramCode(String programCode)
	{
		this.programCode = programCode;
	}


	public String getCourseCRN()
	{
		return courseCRN;
	}

	
	public void setCourseCRN(String courseCRN)
	{
		this.courseCRN = courseCRN;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((courseCRN == null) ? 0 : courseCRN.hashCode());
		result = prime * result + ((personId == null) ? 0 : personId.hashCode());
		result = prime * result + ((programCode == null) ? 0 : programCode.hashCode());
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanStudentClassRegistrationPK other = (BanStudentClassRegistrationPK) obj;
		if (courseCRN == null)
		{
			if (other.courseCRN != null)
				return false;
		}
		else if (!courseCRN.equals(other.courseCRN))
			return false;
		if (personId == null)
		{
			if (other.personId != null)
				return false;
		}
		else if (!personId.equals(other.personId))
			return false;
		if (programCode == null)
		{
			if (other.programCode != null)
				return false;
		}
		else if (!programCode.equals(other.programCode))
			return false;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanStudentClassRegistrationPK [termCode=" + termCode + ", personId=" + personId + ", programCode="
				+ programCode + ", courseCRN=" + courseCRN + "]";
	}

}
