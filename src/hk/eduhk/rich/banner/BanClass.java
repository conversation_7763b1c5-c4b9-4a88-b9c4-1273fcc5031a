package hk.eduhk.rich.banner;

import java.util.*;
import javax.persistence.*;


@Entity
@Table(name = "AMIS.Class")
@SuppressWarnings("serial")
public class BanClass extends BanEntity
{
	
	@EmbeddedId
	private BanClassPK pk = null;
	
	@Column(name = "GroupCode", length = 5)
	private String groupCode;

	@Column(name = "CourseCode", length = 9)
	private String courseCode;

	@Column(name = "SectionNumber", length = 3)
	private String sectionNumber;

	@Column(name = "SectionDepartment", length = 30)
	private String sectionDepartment;
	
	@Column(name = "EnrollTotal")
	private int enrollTotal = 0;
	
	@Column(name = "SectionCapacity")
	private int sectionCapacity = 0;
	
	@Column(name = "StartDate")
	private Date startDate;

	@Column(name = "EndDate")
	private Date endDate;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "TermCode", referencedColumnName = "TermCode", insertable = false, updatable = false),
		@JoinColumn(name = "GroupCode", referencedColumnName = "GroupCode", insertable = false, updatable = false),
		@JoinColumn(name = "CourseCode", referencedColumnName = "CourseCode", insertable = false, updatable = false)
	})
	private BanClassGroup classGroup = null;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "CourseCode", insertable = false, updatable = false)
	private BanCourse course = null;
	
	
	public BanClassPK getPk()
	{
		if (pk == null) return new BanClassPK();
		return pk;
	}

	
	public void setPk(BanClassPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}


	public String getCourseCRN()
	{
		return pk.getCourseCRN();
	}

	
	public void setCourseCRN(String courseCRN)
	{
		pk.setCourseCRN(courseCRN);
	}

	
	public String getGroupCode()
	{
		return groupCode;
	}

	
	public void setGroupCode(String groupCode)
	{
		this.groupCode = groupCode;
	}

	
	public String getCourseCode()
	{
		return courseCode;
	}

	
	public void setCourseCode(String courseCode)
	{
		this.courseCode = courseCode;
	}

	
	public String getSectionNumber()
	{
		return sectionNumber;
	}

	
	public void setSectionNumber(String sectionNumber)
	{
		this.sectionNumber = sectionNumber;
	}

	
	public String getSectionDepartment()
	{
		return sectionDepartment;
	}

	
	public void setSectionDepartment(String sectionDepartment)
	{
		this.sectionDepartment = sectionDepartment;
	}

	
	public int getEnrollTotal()
	{
		return enrollTotal;
	}

	
	public void setEnrollTotal(int enrollTotal)
	{
		this.enrollTotal = enrollTotal;
	}

	
	public int getSectionCapacity()
	{
		return sectionCapacity;
	}

	
	public void setSectionCapacity(int sectionCapacity)
	{
		this.sectionCapacity = sectionCapacity;
	}

	
	public Date getStartDate()
	{
		return startDate;
	}

	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}

	
	public Date getEndDate()
	{
		return endDate;
	}

	
	public void setEndDate(Date endDate)
	{
		this.endDate = endDate;
	}

	
	public BanClassGroup getClassGroup()
	{
		return classGroup;
	}

	
	public void setClassGroup(BanClassGroup classGroup)
	{
		this.classGroup = classGroup;
	}

	
	public BanCourse getCourse()
	{
		return course;
	}

	
	public void setCourse(BanCourse course)
	{
		this.course = course;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClass other = (BanClass) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClass [pk=" + pk + ", groupCode=" + groupCode + ", courseCode=" + courseCode + ", sectionNumber="
				+ sectionNumber + ", sectionDepartment=" + sectionDepartment + ", enrollTotal=" + enrollTotal
				+ ", sectionCapacity=" + sectionCapacity + ", startDate=" + startDate + ", endDate=" + endDate + "]";
	}
		
}
