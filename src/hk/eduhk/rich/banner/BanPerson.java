package hk.eduhk.rich.banner;

import java.util.Comparator;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;


@Entity
@Table(name = "AMIS.Person")
@SuppressWarnings("serial")
public class Ban<PERSON>erson extends BanEntity
{

	public static final String ACCT_STATUS_ACTIVE = "ANS";
	
	public static final Comparator<BanPerson> COMPARATOR_NAME = Comparator.comparing(BanPerson::getName);
	
	@Id
	@Column(name = "PersonID")
	private Integer personId;
	
	@Column(name = "UserID", length = 32)
	private String userId;
	
	@Column(name = "Title", length = 255)
	private String title;
	
	@Column(name = "LastName", length = 255)
	private String lastName;
	
	@Column(name = "FirstName", length = 255)
	private String firstName;
	
	@Column(name = "MiddleInitial", length = 255)
	private String middleInitial;
	
	@Column(name = "Gender", length = 255)
	private String gender;

	@Column(name = "InstituteID", length = 10)
	private String instituteId;

	@Column(name = "Email", length = 50)
	private String email;

	@Column(name = "AccountStatus", length = 5)
	private String accountStatus;

	@Column(name = "MemberType", length = 2)
	private String memberType;

	@Column(name = "UserStatus", length = 2)
	private String userStatus;

	@Column(name = "IntranetGroup", length = 20)
	private String intranetGroup;

	@Column(name = "InstructorDepartment", length = 30)
	private String instructorDepartment;

	@Column(name = "Department", length = 50)
	private String department;

	@Column(name = "OriginalDepartment", length = 50)
	private String originalDepartment;

	@Column(name = "ContainInetUserInfo")
	private boolean containInetUserInfo;

	@Transient
	private String name;
	
	
	public Integer getPersonId()
	{
		return personId;
	}

	
	public void setPersonId(Integer personId)
	{
		this.personId = personId;
	}

	
	public String getUserId()
	{
		return userId;
	}

	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getLastName()
	{
		return lastName;
	}

	
	public void setLastName(String lastName)
	{
		this.lastName = lastName;
	}

	
	public String getFirstName()
	{
		return firstName;
	}

	
	public void setFirstName(String firstName)
	{
		this.firstName = firstName;
	}

	
	public String getMiddleInitial()
	{
		return middleInitial;
	}

	
	public void setMiddleInitial(String middleInitial)
	{
		this.middleInitial = middleInitial;
	}
	
	
	public String getName()
	{
		if (name == null)
		{
			StringBuilder buf = new StringBuilder();
			
			if (!GenericValidator.isBlankOrNull(getLastName()))
			{
				buf.append(getLastName());
			}
			
			if (!GenericValidator.isBlankOrNull(getFirstName()))
			{
				if (buf.length() > 0) buf.append(" ");
				buf.append(getFirstName());
			}
			
			name = buf.toString();
		}
		
		return name;
	}
	
	
	public String getGender()
	{
		return gender;
	}

	
	public void setGender(String gender)
	{
		this.gender = gender;
	}

	
	public String getInstituteId()
	{
		return instituteId;
	}

	
	public void setInstituteId(String instituteId)
	{
		this.instituteId = instituteId;
	}

	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}

	
	public String getAccountStatus()
	{
		return accountStatus;
	}

	
	public void setAccountStatus(String accountStatus)
	{
		this.accountStatus = accountStatus;
	}
	
	
	public boolean isActive()
	{
		return StringUtils.equals(getAccountStatus(), ACCT_STATUS_ACTIVE);
	}

	
	public String getMemberType()
	{
		return memberType;
	}

	
	public void setMemberType(String memberType)
	{
		this.memberType = memberType;
	}

	
	public String getUserStatus()
	{
		return userStatus;
	}

	
	public void setUserStatus(String userStatus)
	{
		this.userStatus = userStatus;
	}

	
	public String getIntranetGroup()
	{
		return intranetGroup;
	}

	
	public void setIntranetGroup(String intranetGroup)
	{
		this.intranetGroup = intranetGroup;
	}

	
	public String getInstructorDepartment()
	{
		return instructorDepartment;
	}

	
	public void setInstructorDepartment(String instructorDepartment)
	{
		this.instructorDepartment = instructorDepartment;
	}

	
	public String getDepartment()
	{
		return department;
	}

	
	public void setDepartment(String department)
	{
		this.department = department;
	}

	
	public String getOriginalDepartment()
	{
		return originalDepartment;
	}

	
	public void setOriginalDepartment(String originalDepartment)
	{
		this.originalDepartment = originalDepartment;
	}

	
	public boolean isContainInetUserInfo()
	{
		return containInetUserInfo;
	}

	
	public void setContainInetUserInfo(boolean containInetUserInfo)
	{
		this.containInetUserInfo = containInetUserInfo;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((personId == null) ? 0 : personId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanPerson other = (BanPerson) obj;
		if (personId == null)
		{
			if (other.personId != null)
				return false;
		}
		else if (!personId.equals(other.personId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanPerson [personId=" + personId + ", userId=" + userId + ", title=" + title + ", lastName=" + lastName
				+ ", firstName=" + firstName + ", middleInitial=" + middleInitial + ", gender=" + gender
				+ ", instituteId=" + instituteId + ", email=" + email + ", accountStatus=" + accountStatus
				+ ", memberType=" + memberType + ", userStatus=" + userStatus + ", intranetGroup=" + intranetGroup
				+ ", instructorDepartment=" + instructorDepartment + ", department=" + department
				+ ", originalDepartment=" + originalDepartment + ", containInetUserInfo=" + containInetUserInfo + "]";
	}
	
}
