package hk.eduhk.rich.banner;

import javax.persistence.*;


@Entity
@Table(name = "Final.DimMajor")
@SuppressWarnings("serial")
public class BanMajor extends BanEntity
{
	
	@Id
	@Column(name = "<PERSON><PERSON><PERSON>")
	private int key;
	
	@Column(name = "SourceKey", length = 4)
	private String sourceKey;
	
	@Column(name = "Description", length = 30)
	private String description;
	
	@Column(name = "UniqueDescription", length = 8000)
	public String uniqueDescription;
	
	@Column(name = "CIPCode", length = 6)
	public String cipCode;
	
	@Column(name = "CIPDescription", length = 30)
	public String cipDescription;
	
	@Column(name = "Active")
	public boolean active;

	@Column(name = "OrderBy")
	public short orderBy;

	
	public int getKey()
	{
		return key;
	}


	
	public void setKey(int key)
	{
		this.key = key;
	}


	
	public String getSourceKey()
	{
		return sourceKey;
	}


	
	public void setSourceKey(String sourceKey)
	{
		this.sourceKey = sourceKey;
	}


	
	public String getDescription()
	{
		return description;
	}


	
	public void setDescription(String description)
	{
		this.description = description;
	}


	
	public String getUniqueDescription()
	{
		return uniqueDescription;
	}


	
	public void setUniqueDescription(String uniqueDescription)
	{
		this.uniqueDescription = uniqueDescription;
	}


	
	public String getCipCode()
	{
		return cipCode;
	}


	
	public void setCipCode(String cipCode)
	{
		this.cipCode = cipCode;
	}


	
	public String getCipDescription()
	{
		return cipDescription;
	}


	
	public void setCipDescription(String cipDescription)
	{
		this.cipDescription = cipDescription;
	}


	
	public boolean isActive()
	{
		return active;
	}


	
	public void setActive(boolean active)
	{
		this.active = active;
	}


	
	public short getOrderBy()
	{
		return orderBy;
	}


	
	public void setOrderBy(short orderBy)
	{
		this.orderBy = orderBy;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + key;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanMajor other = (BanMajor) obj;
		if (key != other.key)
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "BanMajor [key=" + key + ", sourceKey=" + sourceKey + ", description=" + description
				+ ", uniqueDescription=" + uniqueDescription + ", cipCode=" + cipCode + ", cipDescription="
				+ cipDescription + ", active=" + active + ", orderBy=" + orderBy + "]";
	}



	
}
