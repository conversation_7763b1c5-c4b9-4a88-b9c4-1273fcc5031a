package hk.eduhk.rich.banner;

import java.text.MessageFormat;

import javax.faces.application.FacesMessage;
import javax.faces.component.*;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.BaseFacesValidator;
import hk.eduhk.rich.util.JPAUtils;


@FacesValidator("hk.eduhk.rich.banner.BanCourseValidator")
public class BanCourseValidator extends BaseFacesValidator
{
	
	public static final Integer[] VALID_LENGTH_NAME 		= {1, JPAUtils.getColumnLength(BanCourse.class, "courseCode")};

	
	public BanCourseValidator()
	{
		super();
	}
	

	public void validateCourseCode(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		String code = StringUtils.trim((String) obj);
		
		validateLength(code, VALID_LENGTH_NAME);
		
		// Check whether the course code is a valid one
		BannerLookupDAO lookupDAO = BannerLookupDAO.getCacheInstance();
		BanCourse course = lookupDAO.getCourse(code);
		if (course == null || GenericValidator.isBlankOrNull(course.getCourseCode()))
		{
			String msg = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.x"), "course code");
			throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
		}
		
		System.out.println("validate complete");
	}
	
}
