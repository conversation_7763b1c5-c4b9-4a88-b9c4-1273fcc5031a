package hk.eduhk.rich.banner;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


@Entity
@Table(name = "AMIS.ClassGroupSchedule")
@SuppressWarnings("serial")
public class BanClassGroupSchedule extends BanEntity
{

	@EmbeddedId
	private BanClassGroupSchedulePK pk = null;

	@Column(name = "CRNCount")
	private int crnCount;
	
	@Column(name = "GroupEnroll")
	private int groupEnroll = 0;
	
	@Column(name = "SectionNumbers", length = 4000)
	private String sections;
	
	@Column(name = "CourseCRNs", length = 4000)
	private String crns;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "classGroupSchedule", cascade = {CascadeType.DETACH, CascadeType.REFRESH})
	@OrderBy("pk.personId")
	private Set<BanClassGroupInstructor> instructorSet = null;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "classGroupSchedule", cascade = {CascadeType.DETACH, CascadeType.REFRESH})
	@OrderBy("StartDate, BeginTime")
	private Set<BanClassGroupMeeting> meetingSet = null;
	
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns
	({
		@JoinColumn(name = "TermCode", referencedColumnName = "TermCode", insertable = false, updatable = false),
		@JoinColumn(name = "GroupCode", referencedColumnName = "GroupCode", insertable = false, updatable = false),
		@JoinColumn(name = "CourseCode", referencedColumnName = "CourseCode", insertable = false, updatable = false)
	})
	private BanClassGroup classGroup = null;
	
	@Transient
	private List<String> sectionList;
	
	@Transient
	private List<String> crnList;
	
	@Transient
	private List<BanClassGroupInstructor> instructorList = null;
	
	@Transient
	private Set<BanPerson> instructorPersonSet;
	
	
	private static final ObjectMapper objMapper = new ObjectMapper();
	private static final TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
	private static final Logger logger = Logger.getLogger(BanClassGroupSchedule.class.getName());

	
	public BanClassGroupSchedulePK getPk()
	{
		if (pk == null) return new BanClassGroupSchedulePK();
		return pk;
	}

	
	public void setPk(BanClassGroupSchedulePK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public String getGroupCode()
	{
		return pk.getGroupCode();
	}

	
	public void setGroupCode(String groupCode)
	{
		pk.setGroupCode(groupCode);
	}

	
	public String getCourseCode()
	{
		return pk.getCourseCode();
	}

	
	public void setCourseCode(String courseCode)
	{
		pk.setCourseCode(courseCode);
	}

	
	public String getScheduleTypeCode()
	{
		return pk.getScheduleTypeCode();
	}

	
	public void setScheduleTypeCode(String scheduleTypeCode)
	{
		pk.setScheduleTypeCode(scheduleTypeCode);
	}

	
	public int getCrnCount()
	{
		return crnCount;
	}

	
	public void setCrnCount(int crnCount)
	{
		this.crnCount = crnCount;
	}

	
	public int getGroupEnroll()
	{
		return groupEnroll;
	}

	
	public void setGroupEnroll(int groupEnroll)
	{
		this.groupEnroll = groupEnroll;
	}


	public String getSections()
	{
		return sections;
	}

	
	public void setSections(String sections)
	{
		this.sections = sections;
		this.sectionList = null;
	}


	public List<String> getSectionList()
	{
		if (sectionList == null)
		{
			try
			{
				sectionList = objMapper.readValue(getSections(), typeRef);
			}
			catch (IOException ioe)
			{
				logger.log(Level.WARNING, "Cannot parse JSON (sections=" + getSections() + ")");
			}
			
			if (sectionList == null) sectionList = new ArrayList<String>();
		}
		
		return sectionList;
	}

	
	public void setSectionList(List<String> sectionList)
	{
		this.sectionList = sectionList;
	}

	
	public String getSectionDesc()
	{
		String desc = null;
		
		if (!GenericValidator.isBlankOrNull(getSections()))
		{
			desc = getSectionList().toString();
			desc = desc.substring(1, desc.length()-1);
		}
		
		return (desc != null) ? desc : "";
	}
	
	
	public String getCrns()
	{
		return crns;
	}

	
	public void setCrns(String crns)
	{
		this.crns = crns;
		this.crnList = null;
	}


	public List<String> getCrnList()
	{
		if (crnList == null)
		{
			try
			{
				crnList = objMapper.readValue(getCrns(), typeRef);
			}
			catch (IOException ioe)
			{
				logger.log(Level.WARNING, "Cannot parse JSON (CRNs=" + getCrns() + ")");
			}
			
			if (crnList == null) crnList = new ArrayList<String>();
		}

		return crnList;
	}

	
	public void setCrnList(List<String> crnList)
	{
		this.crnList = crnList;
	}
	
	
	public String getCrnDesc()
	{
		String desc = null;
		
		if (!GenericValidator.isBlankOrNull(getCrns()))
		{
			desc = getCrnList().toString();
			desc = desc.substring(1, desc.length()-1);
		}
		
		return (desc != null) ? desc : "";
	}
	
	
	public Set<BanClassGroupInstructor> getInstructorSet()
	{
		if (instructorSet != null)
		{
			try
			{
				instructorSet.size();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					BannerDAO dao = BannerDAO.getCacheInstance();
					List<BanClassGroupInstructor> objList = dao.getClassGroupInstructorList(this);
					instructorSet = new LinkedHashSet<BanClassGroupInstructor>(objList);
				}
				else
				{
					throw re;
				}
			}
		}
		
		return instructorSet;
	}

	
	public void setInstructorSet(Set<BanClassGroupInstructor> instructorSet)
	{
		this.instructorSet = instructorSet;
		this.instructorList = null;
	}

	
	public List<BanClassGroupInstructor> getInstructorList()
	{
		if (instructorList == null)
		{
			instructorList = new ArrayList<BanClassGroupInstructor>(getInstructorSet());
			Collections.sort(instructorList, BanClassGroupInstructor.COMPARATOR_NAME);
		}
		
		return instructorList;
	}
	
	
	public Set<BanPerson> getInstructorPersonSet()
	{
		if (instructorPersonSet == null)
		{
			instructorPersonSet = new TreeSet<BanPerson>(BanPerson.COMPARATOR_NAME);
			
			if (getInstructorSet() != null)
			{
				for (BanClassGroupInstructor instructor : getInstructorSet())
				{
					instructorPersonSet.add(instructor.getPerson());
				}
			}
		}
		
		return instructorPersonSet;
	}
	
	
	public Set<BanClassGroupMeeting> getMeetingSet()
	{
		if (meetingSet != null)
		{
			try
			{
				meetingSet.size();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					BannerDAO dao = BannerDAO.getCacheInstance();
					List<BanClassGroupMeeting> objList = dao.getClassGroupMeetingList(this);
					meetingSet = new LinkedHashSet<BanClassGroupMeeting>(objList);
				}
				else
				{
					throw re;
				}
			}
		}
		
		return meetingSet;
	}

	
	public void setMeetingSet(Set<BanClassGroupMeeting> meetingSet)
	{
		this.meetingSet = meetingSet;
	}


	public BanClassGroup getClassGroup()
	{
		return classGroup;
	}

	
	public void setClassGroup(BanClassGroup classGroup)
	{
		this.classGroup = classGroup;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassGroupSchedule other = (BanClassGroupSchedule) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassGroupSchedule [pk=" + pk + ", crnCount=" + crnCount + ", groupEnroll=" + groupEnroll
				+ ", sections=" + sections + ", crns=" + crns + "]";
	}
	
}
