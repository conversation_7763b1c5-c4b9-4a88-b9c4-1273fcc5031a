package hk.eduhk.rich.banner;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.enterprise.inject.Default;
import javax.inject.Singleton;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;
import org.ehcache.Cache;

import hk.eduhk.rich.cache.AppCache;


@Default
@Singleton
@SuppressWarnings("serial")
public class BannerCacheDAO extends BannerDAO
{
	
	private static BannerCacheDAO instance = null;

//	private transient Cache<String, SysParam> paramCache;
	
	private static final Logger logger = Logger.getLogger(BannerCacheDAO.class.getName());

	
	public static synchronized BannerCacheDAO getInstance()
	{
		if (instance == null) instance = new BannerCacheDAO();
		return instance;
	}
	
}
	