package hk.eduhk.rich.banner;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.enterprise.inject.Default;
import javax.inject.Singleton;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.ehcache.Cache;
import org.ehcache.Cache.Entry;

import hk.eduhk.rich.cache.AppCache;


@Default
@Singleton
@SuppressWarnings("serial")
public class BannerLookupCacheDAO extends BannerLookupDAO
{
	
	// logging
	private static final Level LVL_CACHE_HIT 	= Level.FINER;
	private static final Level LVL_QUERY_DB 	= Level.FINEST;
	
	private static BannerLookupCacheDAO instance = null;

	private transient Cache<String, BanCourse> courseCache;
	private transient Cache<String, Map> orgUnitMapCache;
	private transient Cache<Integer, BanPerson> personCache;
	private transient Cache<String, BanPerson> personByUserIdCache;
	private transient Cache<String, BanProgram> programCache;
	private transient Cache<String, List> programAttrCache;
	private transient Cache<String, BanTerm> termCache;
	
	private static final Logger logger = Logger.getLogger(BannerLookupCacheDAO.class.getName());

	
	public static synchronized BannerLookupCacheDAO getInstance()
	{
		if (instance == null) instance = new BannerLookupCacheDAO();
		return instance;
	}
	
	
	private Cache<String, BanCourse> getCourseCache()
	{
		if (courseCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			courseCache = appCache.getCache(AppCache.CACHE_BAN_CRSE, String.class, BanCourse.class);
		}
		
		return courseCache;
	}

	
	private Cache<Integer, BanPerson> getPersonCache()
	{
		if (personCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			personCache = appCache.getCache(AppCache.CACHE_BAN_PERSON, Integer.class, BanPerson.class);
		}
		
		return personCache;
	}

	
	private Cache<String, BanPerson> getPersonByUserIdCache()
	{
		if (personByUserIdCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			personByUserIdCache = appCache.getCache(AppCache.CACHE_BAN_PERSON_USER_ID, String.class, BanPerson.class);
		}
		
		return personByUserIdCache;
	}

	
	private Cache<String, Map> getOrgUnitMapCache()
	{
		if (orgUnitMapCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			orgUnitMapCache = (Cache<String, Map>) appCache.getCache(AppCache.CACHE_BAN_ORG_UNIT_MAP, String.class, Map.class);
		}
		
		return orgUnitMapCache;
	}

	
	private Cache<String, BanProgram> getProgramCache()
	{
		if (programCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			programCache = appCache.getCache(AppCache.CACHE_BAN_PROG, String.class, BanProgram.class);
		}
		
		return programCache;
	}

	
	private Cache<String, List> getProgramAttrCache()
	{
		if (programAttrCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			programAttrCache = appCache.getCache(AppCache.CACHE_BAN_PROG_ATTR, String.class, List.class);
		}
		
		return programAttrCache;
	}
	
	
	private Cache<String, BanTerm> getTermCache()
	{
		if (termCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			termCache = appCache.getCache(AppCache.CACHE_BAN_TERM, String.class, BanTerm.class);
		}
		
		return termCache;
	}
	
	
	public synchronized void reloadCourseCache()
	{
		if (getCourseCache() != null)
		{
			long t = System.currentTimeMillis();
			
			// Clear all existing entries
			courseCache.clear();
			
			// Put all fresh entries into cache
			List<BanCourse> objList = super.getCourseList();
			for (BanCourse obj : objList) courseCache.put(obj.getCourseCode(), obj);
			
			logger.log(Level.INFO, "Course cache is reloaded " +
								   "(entries=" + CollectionUtils.size(objList) + ", execution time=" + (System.currentTimeMillis()-t) + "ms)");
		}
	}
	
	
	public synchronized void reloadPersonCache()
	{
		if (getPersonCache() != null && getPersonByUserIdCache() != null)
		{
			long t = System.currentTimeMillis();
			
			// Clear all existing entries
			personCache.clear();
			personByUserIdCache.clear();
			
			// Put all fresh entries into cache
			String prevUserId = null;
			
			List<BanPerson> objList = super.getActiveStaffList();
			for (BanPerson obj : objList)
			{
				personCache.put(obj.getPersonId(), obj);
				
				if (!StringUtils.equals(obj.getUserId(), prevUserId))
				{
					personByUserIdCache.put(obj.getUserId(), obj);
				}
				
				prevUserId = obj.getUserId();
			}
			
			logger.log(Level.INFO, "Person cache is reloaded " +
					   			   "(entries=" + CollectionUtils.size(objList) + ", execution time=" + (System.currentTimeMillis()-t) + "ms)");
		}
	}

	
	public synchronized void reloadProgramCache()
	{
		if (getProgramCache() != null)
		{
			long t = System.currentTimeMillis();
			
			// Clear all existing entries
			programCache.clear();
			
			// Put all fresh entries into cache
			List<BanProgram> objList = super.getProgramList();
			for (BanProgram obj : objList) programCache.put(obj.getProgramCode(), obj);
			
			logger.log(Level.INFO, "Program cache is reloaded " +
								   "(entries=" + CollectionUtils.size(objList) + ", execution time=" + (System.currentTimeMillis()-t) + "ms)");
		}
	}

	
	public synchronized void reloadProgramAttrCache()
	{
		if (getProgramAttrCache() != null)
		{
			long t = System.currentTimeMillis();
			
			// Clear all existing entries
			programAttrCache.clear();
			
			// Put all fresh entries into cache
			List<BanProgramAttribute> objList = super.getProgramAttributeList();
			for (BanProgramAttribute obj : objList)
			{
				List<BanProgramAttribute> progAttrList = programAttrCache.get(obj.getProgramCode());
				if (progAttrList == null) programAttrCache.put(obj.getProgramCode(), progAttrList = new ArrayList<BanProgramAttribute>());
				progAttrList.add(obj);
			}
			
			// Order the program attribute list
			Comparator<BanProgramAttribute> comparator = Comparator.comparing(BanProgramAttribute::getStartTerm).reversed()
											  					   .thenComparing(BanProgramAttribute::getProgramYear);
			
			Iterator<Cache.Entry<String, List>> entryIter = programAttrCache.iterator();
			while (entryIter.hasNext())
			{
				Cache.Entry<String, List> entry = entryIter.next();
				List<BanProgramAttribute> progAttrList = (List<BanProgramAttribute>) entry.getValue();
				Collections.sort(progAttrList, comparator);
			}
			
			logger.log(Level.INFO, "Program attribute cache is reloaded " +
								   "(entries=" + CollectionUtils.size(objList) + ", execution time=" + (System.currentTimeMillis()-t) + "ms)");
		}
	}
	
	
	public synchronized void reloadTermCache()
	{
		if (getTermCache() != null)
		{
			long t = System.currentTimeMillis();
			
			// Clear all existing entries
			termCache.clear();

			// Put all fresh entries into cache
			List<BanTerm> objList = super.getTermListByTermRange(null, null);
			for (BanTerm obj : objList) termCache.put(obj.getTermCode(), obj);
			
			logger.log(Level.INFO, "Term cache is reloaded " +
					   			   "(entries=" + CollectionUtils.size(objList) + ", execution time=" + (System.currentTimeMillis()-t) + "ms)");
		}
	}
	
	
	@Override
	public BanCourse getCourse(String code)
	{
		BanCourse obj = null;
		
		if (!GenericValidator.isBlankOrNull(code))
		{
			obj = (getCourseCache() != null) ? courseCache.get(code) : null;
			
			if (obj == null)
			{
				logger.log(LVL_QUERY_DB, "Course to be queried from db");
				
				obj = super.getCourse(code);
				
				if (obj != null && getCourseCache() != null)
				{
					getCourseCache().put(code, obj);
				}
			}
			else
			{
				logger.log(LVL_CACHE_HIT, "Course is retrieved from cache (code=" + code + ")"); 
			}
		}
		
		return obj;
	}

	
	@Override
	public List<BanCourse> getCourseList(Collection<String> codeCol)
	{
		List<BanCourse> objList = new ArrayList<BanCourse>();
		
		if (CollectionUtils.isNotEmpty(codeCol))
		{
			Set<String> dbIdSet = new HashSet<String>();
			
			// Fetch objects from cache first
			// Record object id if the object cannot be found in cache
			for (String code : codeCol)
			{
				if (getCourseCache() != null)
				{
					BanCourse obj = courseCache.get(code);
					
					if (obj != null)
					{
						objList.add(obj);
					}
					else
					{
						dbIdSet.add(code);
					}
				}
			}
			
			// Fetch objects that could not find in cache
			if (CollectionUtils.isNotEmpty(dbIdSet))
			{
				logger.log(LVL_QUERY_DB, "Course to be queried from db (courseCode=" + dbIdSet + ")"); 
				
				List<BanCourse> dbObjList = super.getCourseList(dbIdSet);
				if (CollectionUtils.isNotEmpty(dbObjList))
				{
					objList.addAll(dbObjList);
					
					// Add fetched objects to cache, 
					// the cached object is immutable
					for (BanCourse dbObj : dbObjList)
					{
						getCourseCache().put(dbObj.getCourseCode(), dbObj);
					}
				}
			}
		}
		
		return objList;
	}
	
	
	@Override
	public Map<String, BanOrganizationUnit> getOrganizationUnitMapByTerm(String termCode)
	{
		Map<String, BanOrganizationUnit> objMap = null;
		
		if (!GenericValidator.isBlankOrNull(termCode))
		{
			objMap = (getOrgUnitMapCache() != null) ? (Map<String, BanOrganizationUnit>) orgUnitMapCache.get(termCode) : null;
			
			if (objMap == null)
			{
				logger.log(LVL_QUERY_DB, "OrganizationUnit Map to be queried from db");
				
				objMap = super.getOrganizationUnitMapByTerm(termCode);
				
				if (objMap != null && getOrgUnitMapCache() != null)
				{
					objMap = Collections.unmodifiableMap(objMap);
					getOrgUnitMapCache().put(termCode, objMap);
				}
			}
			else
			{
				logger.log(LVL_CACHE_HIT, "OrganizationUnit Map is retrieved from cache (code=" + termCode + ")"); 
			}
		}
		
		return objMap;
	}
	
	
	@Override
	public BanPerson getPerson(int id)
	{
		BanPerson obj = null;
		
		if (id > 0)
		{
			obj = (getPersonCache() != null) ? personCache.get(id) : null;
			
			if (obj == null)
			{
				logger.log(LVL_QUERY_DB, "Person to be queried from db");
				
				obj = super.getPerson(id);
				
				if (obj != null && getPersonCache() != null)
				{
					getPersonCache().put(id, obj);
				}
			}
			else
			{
				logger.log(LVL_CACHE_HIT, "Person is retrieved from cache (code=" + id + ")"); 
			}
		}
		
		return obj;
	}
	
	
	@Override
	public BanPerson getActivePersonByUserId(String userId)
	{
		BanPerson obj = getPersonByUserId(userId);
		return (obj != null && obj.isActive()) ? obj : null;
	}

	
	@Override
	public BanPerson getPersonByUserId(String userId)
	{
		BanPerson obj = null;
		
		if (!GenericValidator.isBlankOrNull(userId))
		{
			// Fetch objects from cache first
			if (getPersonByUserIdCache() != null)
			{
				obj = personByUserIdCache.get(userId);
			}
			
			// Fetch objects that could not find in cache
			if (obj == null)
			{
				logger.log(LVL_QUERY_DB, "Person to be queried from db (userId=" + userId + ")");
				
				obj = super.getPersonByUserId(userId);
				
				if (obj != null && getPersonByUserIdCache() != null)
				{
					getPersonByUserIdCache().put(userId, obj);
				}
			}
			else
			{
				logger.log(LVL_CACHE_HIT, "Person is retrieved from cache (userId=" + userId + ")"); 
			}
		}
		
		return obj;
	}

	
	@Override
	public List<BanPerson> getPersonList(Collection<Integer> idCol)
	{
		List<BanPerson> objList = new ArrayList<BanPerson>();
		
		if (CollectionUtils.isNotEmpty(idCol))
		{
			Set<Integer> dbIdSet = new HashSet<Integer>();
			
			// Fetch objects from cache first
			// Record object id if the object cannot be found in cache
			for (Integer id : idCol)
			{
				if (getPersonCache() != null)
				{
					BanPerson obj = personCache.get(id);
					
					if (obj != null)
					{
						objList.add(obj);
					}
					else
					{
						dbIdSet.add(id);
					}
				}
			}
			
			// Fetch objects that could not find in cache
			if (CollectionUtils.isNotEmpty(dbIdSet))
			{
				logger.log(LVL_QUERY_DB, "Person to be queried from db (personId=" + dbIdSet + ")"); 
				
				List<BanPerson> dbObjList = super.getPersonList(dbIdSet);
				if (CollectionUtils.isNotEmpty(dbObjList))
				{
					objList.addAll(dbObjList);
					
					// Add fetched objects to cache, 
					// the cached object is immutable
					for (BanPerson dbObj : dbObjList)
					{
						getPersonCache().put(dbObj.getPersonId(), dbObj);
					}
				}
			}
		}
		
		return objList;
	}
	
	
	@Override
	public List<BanPerson> getPersonListByUserId(Collection<String> idCol)
	{
		List<BanPerson> objList = new ArrayList<BanPerson>();
		
		if (CollectionUtils.isNotEmpty(idCol))
		{
			Set<String> dbIdSet = new HashSet<String>();
			
			// Fetch objects from cache first
			// Record object id if the object cannot be found in cache
			for (String id : idCol)
			{
				if (getPersonByUserIdCache() != null)
				{
					BanPerson obj = personByUserIdCache.get(id);
					
					if (obj != null)
					{
						objList.add(obj);
					}
					else
					{
						dbIdSet.add(id);
					}
				}
			}
			
			// Fetch objects that could not find in cache
			if (CollectionUtils.isNotEmpty(dbIdSet))
			{
				logger.log(LVL_QUERY_DB, "Person to be queried from db (userId=" + dbIdSet + ")"); 
				
				List<BanPerson> dbObjList = super.getPersonListByUserId(dbIdSet);
				if (CollectionUtils.isNotEmpty(dbObjList))
				{
					objList.addAll(dbObjList);
					
					// Add fetched objects to cache, 
					// the cached object is immutable
					for (BanPerson dbObj : dbObjList)
					{
						if (dbObj != null && getPersonByUserIdCache() != null)
						{
							getPersonByUserIdCache().put(dbObj.getUserId(), dbObj);
						}
					}
				}
			}
		}
		
		return objList;
	}
	
	
	@Override
	public BanProgram getProgram(String code)
	{
		BanProgram obj = null;
		
		if (!GenericValidator.isBlankOrNull(code))
		{
			obj = (getProgramCache() != null) ? programCache.get(code) : null;
			
			if (obj == null)
			{
				logger.log(LVL_QUERY_DB, "Program to be queried from db");
				
				obj = super.getProgram(code);
				
				if (obj != null && getTermCache() != null)
				{
					getProgramCache().put(code, obj);
				}
			}
			else
			{
				logger.log(LVL_CACHE_HIT, "Program is retrieved from cache (code=" + code + ")"); 
			}
		}
		
		return obj;
	}


	@Override
	public List<BanProgram> getProgramList(Collection<String> codeCol)
	{
		List<BanProgram> objList = new ArrayList<BanProgram>();
		
		if (CollectionUtils.isNotEmpty(codeCol))
		{
			Set<String> dbIdSet = new HashSet<String>();
			
			// Fetch objects from cache first
			// Record object id if the object cannot be found in cache
			for (String code : codeCol)
			{
				if (getProgramCache() != null)
				{
					BanProgram obj = programCache.get(code);
					
					if (obj != null)
					{
						objList.add(obj);
					}
					else
					{
						dbIdSet.add(code);
					}
				}
			}
			
			// Fetch objects that could not find in cache
			if (CollectionUtils.isNotEmpty(dbIdSet))
			{
				logger.log(LVL_QUERY_DB, "Program to be queried from db (courseCode=" + dbIdSet + ")"); 
				
				List<BanProgram> dbObjList = super.getProgramList(dbIdSet);
				if (CollectionUtils.isNotEmpty(dbObjList))
				{
					objList.addAll(dbObjList);
					
					// Add fetched objects to cache, 
					// the cached object is immutable
					for (BanProgram dbObj : dbObjList)
					{
						getProgramCache().put(dbObj.getProgramCode(), dbObj);
					}
				}
			}
		}
		
		return objList;
	}
	

	@Override
	public Map<String, String> getProgramSubLevelMap()
	{
		Map<String, String> valueDescMap = null;
		
		if (getProgramCache() != null)
		{
			Iterator<Entry<String, BanProgram>> entryIter = programCache.iterator();
			while (entryIter.hasNext())
			{
				Entry<String, BanProgram> entry = entryIter.next(); 
				BanProgram obj = entry.getValue();
				
				if (obj.getSubLevelCode() != null)
				{
					if (valueDescMap == null) valueDescMap = new LinkedHashMap<String, String>();
					valueDescMap.put(obj.getSubLevelCode(), StringUtils.defaultString(obj.getSubLevelDesc()));
				}
			}
		}
		
		if (valueDescMap == null) valueDescMap = super.getProgramSubLevelMap();
		
		return valueDescMap;
	}
	
	
	@Override
	public BanProgramAttribute getProgramAttribute(String termCode, String programCode)
	{
		BanProgramAttribute obj = null;
		
		if (GenericValidator.isInt(termCode) && !GenericValidator.isBlankOrNull(programCode))
		{
			if (getProgramAttrCache() != null)
			{
				List<BanProgramAttribute> progAttrList = programAttrCache.get(programCode);
				if (CollectionUtils.isNotEmpty(progAttrList))
				{
					for (BanProgramAttribute progAttr : progAttrList)
					{
						try
						{
							int term = Integer.parseInt(termCode);
							int startTerm = Integer.parseInt(progAttr.getStartTerm());
							int endTerm = Integer.parseInt(progAttr.getEndTerm());
							
							if (term >= startTerm && term < endTerm)							
							{
								obj = progAttr;
								logger.log(LVL_CACHE_HIT, "Program attribute is retrieved from cache (termCode=" + termCode + ", programCode="  + programCode + ")"); 
								break;
							}
						}
						catch (NumberFormatException nfe)
						{
							// Nothing to handle
						}
					}
				}
				
				if (obj == null)
				{
					obj = super.getProgramAttribute(termCode, programCode);
				}
			}
		}
		
		return obj;
	}
	
	
	@Override
	public BanTerm getTerm(String code)
	{
		BanTerm obj = null;
		
		if (!GenericValidator.isBlankOrNull(code))
		{
			obj = (getTermCache() != null) ? termCache.get(code) : null;
			
			if (obj == null)
			{
				logger.log(LVL_QUERY_DB, "Term to be queried from db");
				
				obj = super.getTerm(code);
				
				if (obj != null && getTermCache() != null)
				{
					getTermCache().put(code, obj);
				}
			}
			else
			{
				logger.log(LVL_CACHE_HIT, "Term is retrieved from cache (code=" + code + ")"); 
			}
		}
		
		return obj;
	}
	
	
	@Override
	public List<BanTerm> getTermList(Collection<String> codeCol)
	{
		List<BanTerm> objList = new ArrayList<BanTerm>();
		
		if (CollectionUtils.isNotEmpty(codeCol))
		{
			Set<String> dbIdSet = new HashSet<String>();
			
			// Fetch objects from cache first
			// Record object id if the object cannot be found in cache
			for (String code : codeCol)
			{
				if (getTermCache() != null)
				{
					BanTerm obj = termCache.get(code);
					
					if (obj != null)
					{
						objList.add(obj);
					}
					else
					{
						dbIdSet.add(code);
					}
				}
			}
			
			// Fetch objects that could not find in cache
			if (CollectionUtils.isNotEmpty(dbIdSet))
			{
				logger.log(LVL_QUERY_DB, "Term to be queried from db (termCode=" + dbIdSet + ")"); 
				
				List<BanTerm> dbObjList = super.getTermList(dbIdSet);
				if (CollectionUtils.isNotEmpty(dbObjList))
				{
					objList.addAll(dbObjList);
					
					// Add fetched objects to cache, 
					// the cached object is immutable
					for (BanTerm dbObj : dbObjList)
					{
						getTermCache().put(dbObj.getTermCode(), dbObj);
					}
				}
				
				// Some objects are from database. The whole list must be reordered
				objList = sortValueListByKeyList(objList, codeCol, "termCode");				
			}
		}
		
		return objList;
	}
	
	
	@Override
	public List<BanTerm> getTermListByAcadYear(String acadYear)
	{
		if (GenericValidator.isBlankOrNull(acadYear)) throw new NullPointerException("acadYear cannot be null or empty");
		
		long t = System.currentTimeMillis();
		List<BanTerm> objList = new ArrayList<BanTerm>();
		
		// Iterate all cache entries 
		// to find all BanTerm instances which are between startTerm and endTerm
		Iterator<Cache.Entry<String, BanTerm>> entryIterator = getTermCache().iterator();
		while (entryIterator.hasNext())
		{
			Cache.Entry<String, BanTerm> entry = entryIterator.next();
			BanTerm obj = entry.getValue();
			if (obj != null && StringUtils.equals(acadYear, obj.getAcadYear())) objList.add(obj);
		}
		
		// Order the list by termCode
		if (CollectionUtils.isNotEmpty(objList))
		{
			objList.sort(Comparator.comparing(BanTerm::getTermCode));
		}
		
		return objList;
	}
	
	
	@Override
	public List<BanTerm> getTermListByTermRange(String startTerm, String endTerm)
	{
		long t = System.currentTimeMillis();
		List<BanTerm> objList = new ArrayList<BanTerm>();
		
		// Iterate all cache entries 
		// to find all BanTerm instances which are between startTerm and endTerm
		Iterator<Cache.Entry<String, BanTerm>> entryIterator = getTermCache().iterator();
		while (entryIterator.hasNext())
		{
			Cache.Entry<String, BanTerm> entry = entryIterator.next();
			String termCode = entry.getKey();
			
			if ((startTerm == null || StringUtils.compare(termCode, startTerm) >= 0) &&
				(endTerm == null ||  StringUtils.compare(termCode, endTerm) <= 0))
			{
				BanTerm obj = entry.getValue();
				if (obj != null) objList.add(obj);
			}
		}
		
		// When start term is not equal to end term, the list is very likely not equal to 1
		// Probably cache is not reloaded, retrieve them from database instead
		if (!StringUtils.equals(startTerm, endTerm) && objList.size() == 1)
		{
			logger.log(LVL_QUERY_DB, "Term list to be queried from db");
			
			objList = super.getTermListByTermRange(startTerm, endTerm);
			if (CollectionUtils.isNotEmpty(objList))
			{
				for (BanTerm obj : objList)
				{
					getTermCache().put(obj.getTermCode(), obj);
				}
			}
		}
		
		// Order the list by termCode in reversed order
		if (CollectionUtils.isNotEmpty(objList))
		{
			objList.sort(Comparator.comparing(BanTerm::getTermCode).reversed());
		}
		
		return objList;
	}
	
	
	@Override
	public BanTerm getCurrentRegistrationTerm()
	{
		BanTerm obj = null;
		
		// Fetch object from cache first
		if (getTermCache() != null)
		{
			Iterator<Entry<String, BanTerm>> entryIter = getTermCache().iterator();
			
			while (entryIter.hasNext())
			{
				Entry<String, BanTerm> entry = entryIter.next();
				BanTerm cachedObj = entry.getValue();
				
				// Object found
				if (Boolean.TRUE.equals(cachedObj.getCurrentRegistrationTerm()))
				{
					obj = cachedObj;
					logger.log(LVL_CACHE_HIT, "Current registration Term is retrieved from cache (code=" + obj.getTermCode() + ")");
					break;
				}
			}
		}
		
		// Object not found in cache
		if (obj == null)
		{
			logger.log(LVL_QUERY_DB, "Current registration Term to be queried from db");
			
			obj = super.getCurrentRegistrationTerm();
			
			if (obj != null && getTermCache() != null)
			{
				getTermCache().put(obj.getTermCode(), obj);
			}
		}
		
		return obj;
	}
	
}
