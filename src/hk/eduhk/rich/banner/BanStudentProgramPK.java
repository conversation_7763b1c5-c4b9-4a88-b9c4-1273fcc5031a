package hk.eduhk.rich.banner;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class BanStudentProgramPK implements Serializable
{

	@Column(name = "TermCode", length = 6)
	private String termCode;

	@Column(name = "PersonID")
	private Integer personId;

	@Column(name = "ProgramCode", length = 12)
	private String programCode;
	
	@Column(name = "AdmitTermCode", length = 6)
	private String admitTermCode;

	@Column(name = "BlockCode", length = 10)
	private String blockCode;

	
	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String studentTermCode)
	{
		this.termCode = studentTermCode;
	}

	
	public Integer getPersonId()
	{
		return personId;
	}

	
	public void setPersonId(Integer personId)
	{
		this.personId = personId;
	}

	
	public String getProgramCode()
	{
		return programCode;
	}

	
	public void setProgramCode(String programCode)
	{
		this.programCode = programCode;
	}

	
	public String getAdmitTermCode()
	{
		return admitTermCode;
	}

	
	public void setAdmitTermCode(String admitTermCode)
	{
		this.admitTermCode = admitTermCode;
	}

	
	public String getBlockCode()
	{
		return blockCode;
	}

	
	public void setBlockCode(String blockCode)
	{
		this.blockCode = blockCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((admitTermCode == null) ? 0 : admitTermCode.hashCode());
		result = prime * result + ((blockCode == null) ? 0 : blockCode.hashCode());
		result = prime * result + ((personId == null) ? 0 : personId.hashCode());
		result = prime * result + ((programCode == null) ? 0 : programCode.hashCode());
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanStudentProgramPK other = (BanStudentProgramPK) obj;
		if (admitTermCode == null)
		{
			if (other.admitTermCode != null)
				return false;
		}
		else if (!admitTermCode.equals(other.admitTermCode))
			return false;
		if (blockCode == null)
		{
			if (other.blockCode != null)
				return false;
		}
		else if (!blockCode.equals(other.blockCode))
			return false;
		if (personId == null)
		{
			if (other.personId != null)
				return false;
		}
		else if (!personId.equals(other.personId))
			return false;
		if (programCode == null)
		{
			if (other.programCode != null)
				return false;
		}
		else if (!programCode.equals(other.programCode))
			return false;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanStudentProgramPK [termCode=" + termCode + ", personId=" + personId
				+ ", programCode=" + programCode + ", admitTermCode=" + admitTermCode + ", blockCode=" + blockCode
				+ "]";
	}
	
}
