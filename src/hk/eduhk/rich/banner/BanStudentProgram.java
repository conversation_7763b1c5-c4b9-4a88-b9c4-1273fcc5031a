package hk.eduhk.rich.banner;

import javax.persistence.*;

import org.apache.commons.validator.GenericValidator;


@Entity
@Table(name = "AMIS.StudentProgram")
@SuppressWarnings("serial")
public class BanStudentProgram extends BanEntity
{

	@EmbeddedId
	private BanStudentProgramPK pk = null;

	@Column(name = "StudentID", length = 20)
	private String studentId;

	@Column(name = "UserID", length = 32)
	private String userId;

	@Column(name = "Title", length = 20)
	private String title;

	@Column(name = "LastName", length = 255)
	private String lastName;

	@Column(name = "FirstName", length = 255)
	private String firstName;

	@Column(name = "ChineseName", length = 255)
	private String chineseName;
	
	@Column(name = "Gender", length = 1)
	private String gender;
	
	@Column(name = "ProgramYear", length = 10)
	private String programYear;

	@Column(name = "LevelCode", length = 10)
	private String levelCode;

	@Column(name = "Major1Code", length = 4)
	private String major1Code;

	@Column(name = "Major2Code", length = 4)
	private String major2Code;

	@Column(name = "Minor1Code", length = 4)
	private String minor1Code;

	@Column(name = "Concentration1Code", length = 4)
	private String concentration1Code;

	@Column(name = "Concentration2Code", length = 4)
	private String concentration2Code;
	
	@Column(name = "isEnrolled")
	private boolean enrolled;
	
	@Column(name = "yearStatus")
	private String yearStatus;

	@Transient
	private String name;
	
	
	public BanStudentProgramPK getPk()
	{
		if (pk == null) pk = new BanStudentProgramPK();
		return pk;
	}

	
	public void setPk(BanStudentProgramPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public Integer getPersonId()
	{
		return pk.getPersonId();
	}

	
	public void setPersonId(Integer personId)
	{
		pk.setPersonId(personId);
	}

	
	public String getStudentId()
	{
		return studentId;
	}

	
	public void setStudentId(String studentId)
	{
		this.studentId = studentId;
	}

	
	public String getUserId()
	{
		return userId;
	}

	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getLastName()
	{
		return lastName;
	}

	
	public void setLastName(String lastName)
	{
		this.lastName = lastName;
		this.name = null;
	}

	
	public String getFirstName()
	{
		return firstName;
	}

	
	public void setFirstName(String firstName)
	{
		this.firstName = firstName;
		this.name = null;
	}
	
	
	public String getName()
	{
		if (name == null)
		{
			StringBuilder buf = new StringBuilder();
			
			if (!GenericValidator.isBlankOrNull(getLastName()))
			{
				buf.append(getLastName());
			}
			
			if (!GenericValidator.isBlankOrNull(getFirstName()))
			{
				if (buf.length() > 0) buf.append(", ");
				buf.append(getFirstName());
			}

			name = getLastName() + ", " + getFirstName();
		}
		
		return name;
	}

	
	public String getChineseName()
	{
		return chineseName;
	}

	
	public void setChineseName(String chineseName)
	{
		this.chineseName = chineseName;
	}

	
	public String getGender()
	{
		return gender;
	}

	
	public void setGender(String gender)
	{
		this.gender = gender;
	}

	
	public String getProgramCode()
	{
		return pk.getProgramCode();
	}

	
	public void setProgramCode(String programCode)
	{
		pk.setProgramCode(programCode);
	}

	
	public String getProgramYear()
	{
		return programYear;
	}

	
	public void setProgramYear(String programYear)
	{
		this.programYear = programYear;
	}

	
	public String getLevelCode()
	{
		return levelCode;
	}

	
	public void setLevelCode(String levelCode)
	{
		this.levelCode = levelCode;
	}

	
	public String getAdmitTermCode()
	{
		return pk.getAdmitTermCode();
	}

	
	public void setAdmitTermCode(String admitTermCode)
	{
		pk.setAdmitTermCode(admitTermCode);
	}

	
	public String getBlockCode()
	{
		return pk.getBlockCode();
	}

	
	public void setBlockCode(String blockCode)
	{
		pk.setBlockCode(blockCode);
	}

	
	public String getMajor1Code()
	{
		return major1Code;
	}

	
	public void setMajor1Code(String major1Code)
	{
		this.major1Code = major1Code;
	}

	
	public String getMajor2Code()
	{
		return major2Code;
	}

	
	public void setMajor2Code(String major2Code)
	{
		this.major2Code = major2Code;
	}

	
	public String getMinor1Code()
	{
		return minor1Code;
	}

	
	public void setMinor1Code(String minor1Code)
	{
		this.minor1Code = minor1Code;
	}

	
	public String getConcentration1Code()
	{
		return concentration1Code;
	}

	
	public void setConcentration1Code(String concentration1Code)
	{
		this.concentration1Code = concentration1Code;
	}

	
	public String getConcentration2Code()
	{
		return concentration2Code;
	}

	
	public void setConcentration2Code(String concentration2Code)
	{
		this.concentration2Code = concentration2Code;
	}

	
	public boolean isEnrolled()
	{
		return enrolled;
	}

	
	public void setEnrolled(boolean enrolled)
	{
		this.enrolled = enrolled;
	}

	
	public String getYearStatus()
	{
		return yearStatus;
	}

	
	public void setYearStatus(String yearStatus)
	{
		this.yearStatus = yearStatus;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanStudentProgram other = (BanStudentProgram) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanStudentProgram [pk=" + pk + ", studentId=" + studentId + ", userId=" + userId + ", title=" + title
				+ ", lastName=" + lastName + ", firstName=" + firstName + ", chineseName=" + chineseName + ", gender="
				+ gender + ", programYear=" + programYear + ", levelCode=" + levelCode + ", major1Code=" + major1Code
				+ ", major2Code=" + major2Code + ", minor1Code=" + minor1Code + ", concentration1Code="
				+ concentration1Code + ", concentration2Code=" + concentration2Code + ", enrolled=" + enrolled
				+ ", yearStatus=" + yearStatus + "]";
	}
	
}
