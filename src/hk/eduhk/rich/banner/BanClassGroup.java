package hk.eduhk.rich.banner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;


@Entity
@Table(name = "AMIS.ClassGroup")
@SuppressWarnings("serial")
public class BanClassGroup extends BanEntity
{
	
	@EmbeddedId
	private BanClassGroupPK pk = null;
	
	@Column(name = "SectionDepartment", length = 30)
	private String sectionDepartment;

	@Column(name = "GroupEnroll")
	private int groupEnroll = 0;

	@Column(name = "CRNCount")
	private int crnCount = 0;
	
	@Column(name = "MergedScheduleCount")
	private int mergedScheduleCount = 0;

	@Column(name = "ScheduleCount")
	private int scheduleCount = 0;

	@Column(name = "InstructorCount")
	private int instructorCount = 0;
	
	@Column(name = "SectionNumbers", length = 4000)
	private String sections;
	
	@Column(name = "CourseCRNs", length = 4000)
	private String crns;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "classGroup", cascade = {CascadeType.DETACH, CascadeType.REFRESH})
	@OrderBy("ScheduleTypeCode")
	private List<BanClassGroupSchedule> scheduleList = null;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "CourseCode", insertable = false, updatable = false)
	private BanCourse course = null;
	
	@Transient
	private List<String> sectionList;
	
	@Transient
	private List<String> crnList;

	
	private static final ObjectMapper objMapper = new ObjectMapper();
	private static final TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
	private static final Logger logger = Logger.getLogger(BanClassGroup.class.getName());

	
	public BanClassGroupPK getPk()
	{
		if (pk == null) return new BanClassGroupPK();
		return pk;
	}

	
	public void setPk(BanClassGroupPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public String getGroupCode()
	{
		return pk.getGroupCode();
	}

	
	public void setGroupCode(String groupCode)
	{
		pk.setGroupCode(groupCode);
	}

	
	public String getCourseCode()
	{
		return pk.getCourseCode();
	}

	
	public void setCourseCode(String courseCode)
	{
		pk.setCourseCode(courseCode);
	}
	
	
	public String getSectionDepartment()
	{
		return sectionDepartment;
	}

	
	public void setSectionDepartment(String sectionDepartment)
	{
		this.sectionDepartment = sectionDepartment;
	}

	
	public int getGroupEnroll()
	{
		return groupEnroll;
	}

	
	public void setGroupEnroll(int groupEnroll)
	{
		this.groupEnroll = groupEnroll;
	}
	

	public int getCrnCount()
	{
		return crnCount;
	}

	
	public void setCrnCount(int crnCount)
	{
		this.crnCount = crnCount;
	}

	
	public int getMergedScheduleCount()
	{
		return mergedScheduleCount;
	}

	
	public void setMergedScheduleCount(int mergedScheduleCount)
	{
		this.mergedScheduleCount = mergedScheduleCount;
	}

	
	public int getScheduleCount()
	{
		return scheduleCount;
	}

	
	public void setScheduleCount(int scheduleCount)
	{
		this.scheduleCount = scheduleCount;
	}

	
	public int getInstructorCount()
	{
		return instructorCount;
	}

	
	public void setInstructorCount(int instructorCount)
	{
		this.instructorCount = instructorCount;
	}


	public String getSections()
	{
		return sections;
	}

	
	public void setSections(String sections)
	{
		this.sections = sections;
		this.sectionList = null;
	}


	public List<String> getSectionList()
	{
		if (sectionList == null)
		{
			try
			{
				sectionList = objMapper.readValue(getSections(), typeRef);
			}
			catch (IOException ioe)
			{
				logger.log(Level.WARNING, "Cannot parse JSON (sections=" + getSections() + ")");
			}
			
			if (sectionList == null) sectionList = new ArrayList<String>();
		}
		
		return sectionList;
	}

	
	public void setSectionList(List<String> sectionList)
	{
		this.sectionList = sectionList;
	}

	
	public String getSectionDesc()
	{
		String desc = null;
		
		if (!GenericValidator.isBlankOrNull(getSections()))
		{
			desc = getSectionList().toString();
			desc = desc.substring(1, desc.length()-1);
		}
		
		return (desc != null) ? desc : "";
	}
	
	
	public String getCrns()
	{
		return crns;
	}

	
	public void setCrns(String crns)
	{
		this.crns = crns;
		this.crnList = null;
	}


	public List<String> getCrnList()
	{
		if (crnList == null)
		{
			try
			{
				crnList = objMapper.readValue(getCrns(), typeRef);
			}
			catch (IOException ioe)
			{
				logger.log(Level.WARNING, "Cannot parse JSON (CRNs=" + getCrns() + ")");
			}
			
			if (crnList == null) crnList = new ArrayList<String>();
		}

		return crnList;
	}

	
	public void setCrnList(List<String> crnList)
	{
		this.crnList = crnList;
	}
	
	
	public String getCrnDesc()
	{
		String desc = null;
		
		if (!GenericValidator.isBlankOrNull(getCrns()))
		{
			desc = getCrnList().toString();
			desc = desc.substring(1, desc.length()-1);
		}
		
		return (desc != null) ? desc : "";
	}
	
	
	public boolean isCrossListed()
	{
		return !(GenericValidator.isInt(pk.getGroupCode()) && StringUtils.length(pk.getGroupCode()) == 5);
	}
	
	
	public List<BanClassGroupSchedule> getScheduleList()
	{
		return scheduleList;
	}

	
	public void setScheduleList(List<BanClassGroupSchedule> scheduleList)
	{
		this.scheduleList = scheduleList;
	}


	private BanCourse getCourse()
	{
		return course;
	}
	

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassGroup other = (BanClassGroup) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassGroup [pk=" + pk + ", sectionDepartment=" + sectionDepartment + ", groupEnroll=" + groupEnroll
				+ ", crnCount=" + crnCount + ", mergedScheduleCount=" + mergedScheduleCount + ", scheduleCount="
				+ scheduleCount + ", instructorCount=" + instructorCount + ", sections=" + sections + ", crns=" + crns
				+ "]";
	}
	
}
