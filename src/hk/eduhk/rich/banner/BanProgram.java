package hk.eduhk.rich.banner;

import java.io.Serializable;
import javax.persistence.*;


@Entity
@Table(name = "AMIS.Program")
@SuppressWarnings("serial")
public class BanProgram implements Serializable
{
	
	@Id
	@Column(name = "ProgramCode", length = 12)
	private String programCode;
	
	@Column(name = "TitleAbbrv", length = 30)
	private String titleAbbrv;
	
	@Column(name = "Title", length = 255)
	private String title;
	
	@Column(name = "DegreeCode", length = 6)
	private String degreeCode;
	
	@Column(name = "DegreeDescription", length = 30)
	private String degreeDesc;

	@Column(name = "DegreeAwardCategoryCode", length = 2)
	private String degreeAwardCatCode;

	@Column(name = "DegreeAwardCategoryDescription", length = 30)
	private String degreeAwardCatDesc;
	
	@Column(name = "LevelCode", length = 2)
	private String levelCode;
	
	@Column(name = "LevelDescription", length = 30)
	private String levelDesc;
	
	@Column(name = "LevelOrderBy")
	private int levelOrderBy;
	
	@Column(name = "SubLevelCode", length = 10)
	private String subLevelCode;

	@Column(name = "SubLevelDescription", length = 60)
	private String subLevelDesc;
	
	@Column(name = "SubLevelOrderBy")
	private int subLevelOrderBy;
	
	@Column(name = "FundTypeCode", length = 10)
	private String fundTypeCode;

	@Column(name = "FundTypeDescription", length = 60)
	private String fundTypeDesc;

	
	public String getProgramCode()
	{
		return programCode;
	}

	
	public void setProgramCode(String programCode)
	{
		this.programCode = programCode;
	}

	
	public String getTitleAbbrv()
	{
		return titleAbbrv;
	}

	
	public void setTitleAbbrv(String titleAbbrv)
	{
		this.titleAbbrv = titleAbbrv;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getDegreeCode()
	{
		return degreeCode;
	}

	
	public void setDegreeCode(String degreeCode)
	{
		this.degreeCode = degreeCode;
	}

	
	public String getDegreeDesc()
	{
		return degreeDesc;
	}

	
	public void setDegreeDesc(String degreeDesc)
	{
		this.degreeDesc = degreeDesc;
	}

	
	public String getDegreeAwardCatCode()
	{
		return degreeAwardCatCode;
	}

	
	public void setDegreeAwardCatCode(String degreeAwardCatCode)
	{
		this.degreeAwardCatCode = degreeAwardCatCode;
	}

	
	public String getDegreeAwardCatDesc()
	{
		return degreeAwardCatDesc;
	}

	
	public void setDegreeAwardCatDesc(String degreeAwardCatDesc)
	{
		this.degreeAwardCatDesc = degreeAwardCatDesc;
	}

	
	public String getLevelCode()
	{
		return levelCode;
	}

	
	public void setLevelCode(String levelCode)
	{
		this.levelCode = levelCode;
	}

	
	public String getLevelDesc()
	{
		return levelDesc;
	}

	
	public void setLevelDesc(String levelDesc)
	{
		this.levelDesc = levelDesc;
	}
	
	
	public int getLevelOrderBy()
	{
		return levelOrderBy;
	}

	
	public void setLevelOrderBy(int levelOrderBy)
	{
		this.levelOrderBy = levelOrderBy;
	}


	public String getSubLevelCode()
	{
		return subLevelCode;
	}

	
	public void setSubLevelCode(String subLevelCode)
	{
		this.subLevelCode = subLevelCode;
	}

	
	public String getSubLevelDesc()
	{
		return subLevelDesc;
	}

	
	public void setSubLevelDesc(String subLevelDesc)
	{
		this.subLevelDesc = subLevelDesc;
	}
	
	
	public int getSubLevelOrderBy()
	{
		return subLevelOrderBy;
	}

	
	public void setSubLevelOrderBy(int subLevelOrderBy)
	{
		this.subLevelOrderBy = subLevelOrderBy;
	}


	public String getFundTypeCode()
	{
		return fundTypeCode;
	}

	
	public void setFundTypeCode(String fundTypeCode)
	{
		this.fundTypeCode = fundTypeCode;
	}

	
	public String getFundTypeDesc()
	{
		return fundTypeDesc;
	}

	
	public void setFundTypeDesc(String fundTypeDesc)
	{
		this.fundTypeDesc = fundTypeDesc;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((programCode == null) ? 0 : programCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanProgram other = (BanProgram) obj;
		if (programCode == null)
		{
			if (other.programCode != null)
				return false;
		}
		else if (!programCode.equals(other.programCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanProgram [programCode=" + programCode + ", titleAbbrv=" + titleAbbrv + ", title=" + title
				+ ", degreeCode=" + degreeCode + ", degreeDesc=" + degreeDesc + ", degreeAwardCatCode="
				+ degreeAwardCatCode + ", degreeAwardCatDesc=" + degreeAwardCatDesc + ", levelCode=" + levelCode
				+ ", levelDesc=" + levelDesc + ", subLevelCode=" + subLevelCode + ", subLevelDesc=" + subLevelDesc
				+ ", fundTypeCode=" + fundTypeCode + ", fundTypeDesc=" + fundTypeDesc + "]";
	}
	
}
