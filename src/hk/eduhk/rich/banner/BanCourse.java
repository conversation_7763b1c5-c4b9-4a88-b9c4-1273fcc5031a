package hk.eduhk.rich.banner;

import javax.persistence.*;


@Entity
@Table(name = "AMIS.Course")
@SuppressWarnings("serial")
public class BanCourse extends BanEntity
{

	@Id
	@Column(name = "CourseCode", length = 9)
	private String courseCode;
	
	@Column(name = "SubjectCode", length = 4)
	private String subjectCode;
	
	@Column(name = "CourseNumber", length = 5)
	private String courseNumber;

	@Column(name = "CurrentDepartmentCode", length = 4)
	private String currentDeptCode;

	@Column(name = "CurrentDepartment", length = 30)
	private String currentDepartment;
	
	@Column(name = "TitleAbbrv", length = 255)
	private String titleAbbrv;
	
	@Column(name = "Title", length = 255)
	private String title;
	
	@Column(name = "ChineseTitle", length = 255)
	private String chineseTitle;

		
	public String getCourseCode()
	{
		return courseCode;
	}

	
	public void setCourseCode(String courseCode)
	{
		this.courseCode = courseCode;
	}

	
	public String getSubjectCode()
	{
		return subjectCode;
	}

	
	public void setSubjectCode(String subjectCode)
	{
		this.subjectCode = subjectCode;
	}

	
	public String getCourseNumber()
	{
		return courseNumber;
	}

	
	public void setCourseNumber(String courseNumber)
	{
		this.courseNumber = courseNumber;
	}

	
	public String getCurrentDeptCode()
	{
		return currentDeptCode;
	}

	
	public void setCurrentDeptCode(String currentDeptCode)
	{
		this.currentDeptCode = currentDeptCode;
	}
	
	
	public String getCurrentDepartment()
	{
		return currentDepartment;
	}

	
	public void setCurrentDepartment(String currentDepartment)
	{
		this.currentDepartment = currentDepartment;
	}


	public String getTitleAbbrv()
	{
		return titleAbbrv;
	}

	
	public void setTitleAbbrv(String titleAbbrv)
	{
		this.titleAbbrv = titleAbbrv;
	}

	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	public String getChineseTitle()
	{
		return chineseTitle;
	}

	
	public void setChineseTitle(String chineseTitle)
	{
		this.chineseTitle = chineseTitle;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((courseCode == null) ? 0 : courseCode.hashCode());
		return result;
	}
	

	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanCourse other = (BanCourse) obj;
		if (courseCode == null)
		{
			if (other.courseCode != null)
				return false;
		}
		else if (!courseCode.equals(other.courseCode))
			return false;
		return true;
	}

	
	@Override
	public String toString()
	{
		return "BanCourse [courseCode=" + courseCode + ", subjectCode=" + subjectCode + ", courseNumber=" + courseNumber
				+ ", currentDeptCode=" + currentDeptCode + ", currentDept=" + currentDepartment + ", titleAbbrv=" + titleAbbrv
				+ ", title=" + title + ", chineseTitle=" + chineseTitle + "]";
	}
	
}
