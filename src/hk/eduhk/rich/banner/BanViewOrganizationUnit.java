package hk.eduhk.rich.banner;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;


@Entity
@Table(name = "AMIS.ViewOrganizationUnit")
@SuppressWarnings("serial")
public class BanViewOrganizationUnit extends BanEntity
{
	
	public static final String TYPE_DEPARTMENT	= "Department/Office";
	public static final String TYPE_FACULTY 	= "Faculty";
	
	
	@Id
	@Column(name = "lookup_code")
	private String lookup_code;
	
	@Column(name = "lookup_type")
	private String lookup_type;
	
	@Column(name = "lookup_level")
	private String lookup_level;
	
	@Column(name = "language")
	private String language;
	
	@Column(name = "description")
	public String description;
	
	@Column(name = "enabled_flag")
	public String enabled_flag;
	
	@Column(name = "print_order")
	public Integer print_order;
	
	@Column(name = "parent_lookup_code")
	public String parent_lookup_code;

	
	public String getLookup_code()
	{
		return lookup_code;
	}

	
	public void setLookup_code(String lookup_code)
	{
		this.lookup_code = lookup_code;
	}

	
	public String getLookup_level()
	{
		return lookup_level;
	}

	
	public void setLookup_level(String lookup_level)
	{
		this.lookup_level = lookup_level;
	}

	
	public String getLanguage()
	{
		return language;
	}

	
	public void setLanguage(String language)
	{
		this.language = language;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}

	
	
	public Integer getPrint_order()
	{
		return print_order;
	}


	
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}


	public String getParent_lookup_code()
	{
		return parent_lookup_code;
	}

	
	public void setParent_lookup_code(String parent_lookup_code)
	{
		this.parent_lookup_code = parent_lookup_code;
	}


	
	public String getLookup_type()
	{
		return lookup_type;
	}


	
	public void setLookup_type(String lookup_type)
	{
		this.lookup_type = lookup_type;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((lookup_code == null) ? 0 : lookup_code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanViewOrganizationUnit other = (BanViewOrganizationUnit) obj;
		if (lookup_code == null)
		{
			if (other.lookup_code != null)
				return false;
		}
		else if (!lookup_code.equals(other.lookup_code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanViewOrganizationUnit [lookup_code=" + lookup_code + ", lookup_type=" + lookup_type
				+ ", lookup_level=" + lookup_level + ", language=" + language + ", description=" + description
				+ ", enabled_flag=" + enabled_flag + ", print_order=" + print_order + ", parent_lookup_code="
				+ parent_lookup_code + "]";
	}







}
