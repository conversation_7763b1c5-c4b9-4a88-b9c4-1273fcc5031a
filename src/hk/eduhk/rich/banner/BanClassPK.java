package hk.eduhk.rich.banner;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class BanClassPK implements Serializable
{

	@Column(name = "TermCode", length = 6)
	private String termCode;

	@Column(name = "CourseCRN", length = 5)
	private String courseCRN; 

	
	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String termCode)
	{
		this.termCode = termCode;
	}

	
	public String getCourseCRN()
	{
		return courseCRN;
	}


	
	public void setCourseCRN(String courseCRN)
	{
		this.courseCRN = courseCRN;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((courseCRN == null) ? 0 : courseCRN.hashCode());
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassPK other = (BanClassPK) obj;
		if (courseCRN == null)
		{
			if (other.courseCRN != null)
				return false;
		}
		else if (!courseCRN.equals(other.courseCRN))
			return false;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanClassPK [termCode=" + termCode + ", courseCRN=" + courseCRN + "]";
	}
	
}
