package hk.eduhk.rich.banner;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;


@Entity
@Table(name = "CustomFinal.DimOrganizationUnit")
@SuppressWarnings("serial")
public class BanOrganizationUnit extends BanEntity
{
	
	public static final String TYPE_DEPARTMENT	= "Department/Office";
	public static final String TYPE_FACULTY 	= "Faculty";
	
	
	@Id
	@Column(name = "OrganizationUnitKey")
	private int key;
	
	@Column(name = "StartTerm", length = 6)
	private String startTerm;
	
	@Column(name = "EndTerm", length = 6)
	private String endTerm;
	
	@Column(name = "DepartmentCode", length = 4)
	public String departmentCode;
	
	@Column(name = "UnitCode", length = 20)
	public String unitCode;
	
	@Column(name = "UnitDescription", length = 100)
	public String description;
	
	@Column(name = "UnitType", length = 20)
	public String unitType;
	
	@Column(name = "L2Description", length = 20)
	public String parentUnitCode;
	
	public int getKey()
	{
		return key;
	}

	
	public void setKey(int key)
	{
		this.key = key;
	}

	
	public String getStartTerm()
	{
		return startTerm;
	}

	
	public void setStartTerm(String startTerm)
	{
		this.startTerm = startTerm;
	}

	
	public String getEndTerm()
	{
		return endTerm;
	}

	
	public void setEndTerm(String endTerm)
	{
		this.endTerm = endTerm;
	}

	
	public String getDepartmentCode()
	{
		return departmentCode;
	}

	
	public void setDepartmentCode(String departmentCode)
	{
		this.departmentCode = departmentCode;
	}

	
	public String getUnitCode()
	{
		return unitCode;
	}

	
	public void setUnitCode(String unitCode)
	{
		this.unitCode = unitCode;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public String getUnitType()
	{
		return unitType;
	}

	
	public void setUnitType(String unitType)
	{
		this.unitType = unitType;
	}
	
	
	public boolean isDepartment()
	{
		return StringUtils.equals(getUnitType(), TYPE_DEPARTMENT) && 
			   StringUtils.containsIgnoreCase(getDescription(), "Department");
	}
	
	
	public boolean isFaculty()
	{
		return StringUtils.equals(getUnitType(), TYPE_FACULTY);
	}
	
	
	public boolean isOtherUnitType()
	{
		return !isFaculty() && !isDepartment(); 
	}

	
	public String getParentUnitCode()
	{
		return parentUnitCode;
	}

	
	public void setParentUnitCode(String parentUnitCode)
	{
		this.parentUnitCode = parentUnitCode;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + key;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanOrganizationUnit other = (BanOrganizationUnit) obj;
		if (key != other.key)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanOrganizationUnit [key=" + key + ", startTerm=" + startTerm + ", endTerm=" + endTerm
				+ ", departmentCode=" + departmentCode + ", unitCode=" + unitCode + ", description=" + description
				+ ", unitType=" + unitType + ", parentUnitCode=" + parentUnitCode + "]";
	}
	
}
