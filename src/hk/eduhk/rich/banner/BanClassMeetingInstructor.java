package hk.eduhk.rich.banner;

import javax.persistence.*;


@Entity
@Table(name = "AMIS.ClassMeetingInstructor")
@SuppressWarnings("serial")
public class BanClassMeetingInstructor extends BanEntity
{
	@EmbeddedId
	private BanClassMeetingInstructorPK pk = null;
	
	@Column(name = "GroupCode", length = 5)
	private String groupCode;
	
	@Column(name = "CourseCode", length = 9)
	private String courseCode;
	
	@Column(name = "SectionNumber", length = 3)
	private String sectionNumber;
	
	@Column(name = "PrimaryInstructor", length = 5)
	private String primaryInstructor;
	
	@Column(name = "AssignmentPercentage", length = 5)
	private String assignmentPercentage;
	
	@Column(name = "SessionPercentage", length = 5)
	private String sessionPercentage;

	
	
	public BanClassMeetingInstructorPK getPk()
	{
		return pk;
	}


	
	public void setPk(BanClassMeetingInstructorPK pk)
	{
		this.pk = pk;
	}


	public String getTermCode()
	{
		return pk.getTermCode();
	}

	
	public void setTermCode(String termCode)
	{
		pk.setTermCode(termCode);
	}

	
	public String getGroupCode()
	{
		return groupCode;
	}

	
	public void setGroupCode(String groupCode)
	{
		this.groupCode = groupCode;
	}

	
	public String getCourseCode()
	{
		return courseCode;
	}

	
	public void setCourseCode(String courseCode)
	{
		this.courseCode = courseCode;
	}

	
	public String getScheduleTypeCode()
	{
		return pk.getScheduleTypeCode();
	}

	
	public void setScheduleTypeCode(String scheduleTypeCode)
	{
		pk.setScheduleTypeCode(scheduleTypeCode);
	}

	
	public String getCourseCRN()
	{
		return pk.getCourseCRN();
	}

	
	public void setCourseCRN(String courseCRN)
	{
		pk.setCourseCRN(courseCRN);;
	}

	
	public String getSectionNumber()
	{
		return sectionNumber;
	}

	
	public void setSectionNumber(String sectionNumber)
	{
		this.sectionNumber = sectionNumber;
	}

	
	public String getStartDate()
	{
		return pk.getStartDate();
	}

	
	public void setStartDate(String startDate)
	{
		pk.setStartDate(startDate);
	}

	
	public String getEndDate()
	{
		return pk.getEndDate();
	}

	
	public void setEndDate(String endDate)
	{
		pk.setEndDate(endDate);
	}

	
	public String getDayOfWeek()
	{
		return pk.getDayOfWeek();
	}

	
	public void setDayOfWeek(String dayOfWeek)
	{
		pk.setDayOfWeek(dayOfWeek);
	}

	
	public String getBeginTime()
	{
		return pk.getBeginTime();
	}

	
	public void setBeginTime(String beginTime)
	{
		pk.setBeginTime(beginTime);
	}

	
	public String getEndTime()
	{
		return pk.getEndTime();
	}

	
	public void setEndTime(String endTime)
	{
		pk.setEndTime(endTime);
	}

	
	public String getMeetingHoursWeek()
	{
		return pk.getMeetingHoursWeek();
	}

	
	public void setMeetingHoursWeek(String meetingHoursWeek)
	{
		pk.setMeetingHoursWeek(meetingHoursWeek);
	}

	
	public String getCategory()
	{
		return pk.getCategory();
	}

	
	public void setCategory(String category)
	{
		pk.setCategory(category);
	}

	
	public String getBuildingCode()
	{
		return pk.getBuildingCode();
	}

	
	public void setBuildingCode(String buildingCode)
	{
		pk.setBuildingCode(buildingCode);
	}

	
	public String getRoomCode()
	{
		return pk.getRoomCode();
	}

	
	public void setRoomCode(String roomCode)
	{
		pk.setRoomCode(roomCode);
	}

	
	public String getPersonId()
	{
		return pk.getPersonId();
	}

	
	public void setPersonId(String personId)
	{
		pk.setPersonId(personId);
	}

	
	public String getPrimaryInstructor()
	{
		return primaryInstructor;
	}

	
	public void setPrimaryInstructor(String primaryInstructor)
	{
		this.primaryInstructor = primaryInstructor;
	}

	
	public String getAssignmentPercentage()
	{
		return assignmentPercentage;
	}

	
	public void setAssignmentPercentage(String assignmentPercentage)
	{
		this.assignmentPercentage = assignmentPercentage;
	}

	
	public String getSessionPercentage()
	{
		return sessionPercentage;
	}

	
	public void setSessionPercentage(String sessionPercentage)
	{
		this.sessionPercentage = sessionPercentage;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanClassMeetingInstructor other = (BanClassMeetingInstructor) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}



	@Override
	public String toString()
	{
		return "BanClassMeetingInstructor [pk=" + pk + ", groupCode=" + groupCode + ", courseCode=" + courseCode
				+ ", sectionNumber=" + sectionNumber + ", primaryInstructor=" + primaryInstructor
				+ ", assignmentPercentage=" + assignmentPercentage + ", sessionPercentage=" + sessionPercentage + "]";
	}
	
	
	
	
}
