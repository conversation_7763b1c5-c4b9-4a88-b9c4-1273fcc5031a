package hk.eduhk.rich;

import javax.faces.application.FacesMessage;


@SuppressWarnings("serial")
public class ComponentFacesMessage extends FacesMessage
{
	
	private String compId = null;
	
	
	public ComponentFacesMessage()
	{
		super();
	}

	
	public ComponentFacesMessage(Severity severity, String summary, String detail)
	{
		super(severity, summary, detail);
	}

	
	public ComponentFacesMessage(String summary, String detail)
	{
		super(summary, detail);
	}

	
	public ComponentFacesMessage(String summary)
	{
		super(summary);
	}

	
	public String getCompId()
	{
		return compId;
	}

	
	public void setCompId(String compId)
	{
		this.compId = compId;
	}
	

}
