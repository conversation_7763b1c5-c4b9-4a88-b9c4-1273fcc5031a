package hk.eduhk.rich.service;

import java.util.HashSet;
import java.util.Set;

import javax.servlet.ServletConfig;
import javax.servlet.ServletContext;
import javax.ws.rs.ApplicationPath;
import javax.ws.rs.core.Application;
import javax.ws.rs.core.Context;


@ApplicationPath("/service")
public class WebService extends Application
{

    private Set<Object> singletons = new HashSet<Object>();
    
    
    public WebService(@Context ServletConfig config)
    {
    	// ServletContext must be acquired from ServletConfig 
    	// instead of directly inject through @Context ServletContext sCtx
    	//
    	// Otherwise, exception will be thrown when the services try to access ServletContext
    	// RESTEASY003880: Unable to find contextual data of type: javax.servlet.ServletContext
    	ServletContext sCtx = config.getServletContext();
    		
    	// Report services
    	/*
        singletons.add(new DistributionReportService(sCtx));    	
        singletons.add(new UserReportService(sCtx));
        singletons.add(new AggregationReportService(sCtx));
        */    	
    }
    
    
    @Override
    public Set<Object> getSingletons() 
    {
        return singletons;
    }	
	
}