package hk.eduhk.rich.model;

import java.io.Serializable;
import java.util.*;

import org.apache.commons.lang3.StringUtils;


@SuppressWarnings("serial")
public class ParamMap extends HashMap<String, String> implements Serializable
{	
	
	
	private List<ParamMapEventListener> eventListenerList = new ArrayList<ParamMapEventListener>();
			
	
	@Override
	public String put(String name, String value)
	{
		String previousValue = super.put(name, value);
		
		// Compare the new value and the previous value
		// Generate a param change event if the value has been changed  
		if (!StringUtils.equals(previousValue, value))
		{
			triggerParamValueChanged(name);
		}
		
		return previousValue;
	}
	
	
	@Override
	public String remove(Object key)
	{
		String name = (String) key;
		triggerParamValueChanged(name);
		return super.remove(name);
	}

	
	public void addEventListener(ParamMapEventListener eventListener)
	{
		eventListenerList.add(eventListener);
	}
	
	
	public boolean containsEventListener(ParamMapEventListener eventListener)
	{
		return eventListenerList.contains(eventListener);
	}
	
	
	private void triggerParamValueChanged(String name)
	{
		ParamMapEvent event = new ParamMapEvent(name);
		
		for (ParamMapEventListener eventListener : eventListenerList)
		{
			eventListener.paramValueChanged(event);
		}
	}
		
}
