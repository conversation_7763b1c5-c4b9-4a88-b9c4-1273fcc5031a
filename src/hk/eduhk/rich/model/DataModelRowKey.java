package hk.eduhk.rich.model;


/**
 * DataModelRowKey is to define a row key that can be used in Primefaces DataTable.
 * 
 * <AUTHOR>
 *
 * @param <K>
 */
public interface DataModelRowKey<K>
{
	
	/**
	 * Convert the key instance to String representation.
	 * 
	 * @return
	 */
	String getAsKeyString();
	
	
	/**
	 * Convert the String key to an instance.
	 * 
	 * @return
	 */
	K getAsKeyObject(String strKey);
	
}
