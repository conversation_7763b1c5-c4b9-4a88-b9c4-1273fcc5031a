package hk.eduhk.rich.model;

import java.io.Serializable;

public class ColumnModel implements Serializable {
	 
	private static final long serialVersionUID = 1L;
	private static final int DEFALT_WIDTH = 200;
	
    private String header;
    private String property;
    private int width;
    private String sortBy;
    
    
    public ColumnModel()
    {
    }
    
    public ColumnModel(String header, String property) 
    {
    	this(header, property, property, DEFALT_WIDTH);
    }
    
    public ColumnModel(String header, String property, int width) 
    {
    	this(header, property, property, width);
    }

    public ColumnModel(String header, String property, String sortBy) 
    {
    	this(header, property, sortBy, DEFALT_WIDTH);
    }
    
    public ColumnModel(String header, String property, String sortBy, int width) 
    {
        this.header = header;
        this.property = property;
        this.sortBy = sortBy;
        this.width = width;

    }

    public String getProperty() 
    {
        return property;
    }
 
    public void setProperty(String property) 
    {
        this.property = property;
    }
    
    public String getHeader() 
    {
        return header;
    }
    
    public void setHeader(String header) 
    {
        this.header = header;
    }

	
	public int getWidth()
	{
		return width;
	}

	
	public void setWidth(int width)
	{
		this.width = width;
	}

	
	public String getSortBy()
	{
		return sortBy;
	}

	
	public void setSortBy(String sortBy)
	{
		this.sortBy = sortBy;
	}

	@Override
	public String toString()
	{
		return "ColumnModel [header=" + header + ", property=" + property + ", width=" + width + ", sortBy=" + sortBy
				+ "]";
	}


    
}