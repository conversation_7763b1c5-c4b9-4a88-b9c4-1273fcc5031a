package hk.eduhk.rich.researcher;

import java.util.Arrays;
import java.util.Base64;
import java.util.logging.Logger;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table (name = "RH_RESEARCHER_LIST_V")
public class ResearcherInfo
{
	public static Logger logger = Logger.getLogger(ResearcherInfo.class.toString());
	
	@Id
	@Column(name = "pid")
	private int pid;
	
	@Column(name = "fullname")
	private String fullname;
	
	@Column(name = "email")
	private String email;

	@Column(name = "orcid")
	private String orcid;

	@Column(name = "scopus_id_1")
	private String scopus_id_1;

	@Column(name = "scopus_id_2")
	private String scopus_id_2;

	@Column(name = "researcherid")
	private String researcherid;
	
	@Column(name = "timestamp")
	private String timestamp;
	
	
	public int getPid()
	{
		return pid;
	}

	
	public void setPid(int pid)
	{
		this.pid = pid;
	}
	
	public String getOrcid()
	{
		return orcid;
	}

	
	public void setOrcid(String orcid)
	{
		this.orcid = orcid;
	}

	
	public String getScopus_id_1()
	{
		return scopus_id_1;
	}

	
	public void setScopus_id_1(String scopus_id_1)
	{
		this.scopus_id_1 = scopus_id_1;
	}

	
	public String getScopus_id_2()
	{
		return scopus_id_2;
	}

	
	public void setScopus_id_2(String scopus_id_2)
	{
		this.scopus_id_2 = scopus_id_2;
	}

	
	public String getResearcherid()
	{
		return researcherid;
	}

	
	public void setResearcherid(String researcherid)
	{
		this.researcherid = researcherid;
	}
	
	public String getFullname()
	{
		return fullname;
	}

	
	public void setFullname(String fullname)
	{
		this.fullname = fullname;
	}
	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}

	
	
	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + pid;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ResearcherInfo other = (ResearcherInfo) obj;
		if (pid != other.pid)
			return false;
		return true;
	}
	
	
	@Override
	public String toString()
	{
		return "ResearcherInfo [pid=" + pid + ", fullname=" + fullname +", email=" + email+ ", orcid=" + orcid + ", scopus_id_1=" 
				+ scopus_id_1 + ", scopus_id_2="+ scopus_id_2 + ", researcherid=" + researcherid + ", timestamp="+ timestamp + "]";
	}
	
}
