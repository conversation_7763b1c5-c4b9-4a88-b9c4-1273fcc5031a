package hk.eduhk.rich.researcher;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffInfo;
import hk.eduhk.rich.entity.staff.StaffInfoView;
import hk.eduhk.rich.entity.staff.StaffRank;


@ManagedBean(name = "researcherInfoView")
@ViewScoped
@SuppressWarnings("serial")

public class ResearcherInfoView extends BaseView
{
	private static Logger logger = Logger.getLogger(StaffRank.class.getName());
	
	private List<StaffIdentity> researcherInfoList = null;
	private StaffIdentity selectedresearcherInfo;
	private StaffInfo staffInfo = null;
	private String paramStudNumber;

	
	
	public ResearcherInfoView()
	{
		super();
	}
	
	public String getParamStudNumber()
	{
		return paramStudNumber;
	}

	public void setParamStudNumber(String paramStudNumber)
	{
		this.paramStudNumber = paramStudNumber;
		researcherInfoDao dao = researcherInfoDao.getInstance();
		selectedresearcherInfo = dao.getResearcherInfo(paramStudNumber);
	}
	

	
	public StaffIdentity getSelectedresearcherInfo()
	{
		// To avoid NullPointerException
		if (selectedresearcherInfo == null)
			selectedresearcherInfo = new StaffIdentity();
		return selectedresearcherInfo;
	}

	
	public void setSelectedresearcherInfo(StaffIdentity selectedresearcherInfo)
	{
		this.selectedresearcherInfo = selectedresearcherInfo;
	}

	public List<StaffIdentity> getResearcherInfoList() {
		if (researcherInfoList == null) {
			researcherInfoDao dao = researcherInfoDao.getInstance();
			researcherInfoList = dao.getResearcherList();
		}
		return researcherInfoList;
	}
	
	public void setResearcherInfoList(List<StaffIdentity> researcherInfoList)
	{
		this.researcherInfoList = researcherInfoList;
	}

	public StaffInfo getstaffInfo(int paramPid)
	{
		
		StaffDAO dao = StaffDAO.getInstance();
		staffInfo = dao.getStaffProfileByPid(paramPid);

		return staffInfo;
	}
	
	public Boolean updateResearcherInfo() {
    	String message;
    	FacesContext fCtx = FacesContext.getCurrentInstance();
    	ResourceBundle bundle = getResourceBundle();
    	if (selectedresearcherInfo != null) {
    		try {
				
    			if (validateOrcid()) 
    			{
	    			if (selectedresearcherInfo.getStaffInfo().getResearcherid() != null) {
						selectedresearcherInfo.getStaffInfo().setResearcherid(StringUtils.capitalize(selectedresearcherInfo.getStaffInfo().getResearcherid()));
					}
	    			selectedresearcherInfo.setUserstamp(getLoginUserId());
	    			researcherInfoDao dao = researcherInfoDao.getInstance();
					dao.updateResarcherInfo(selectedresearcherInfo.getStaffInfo());
					message = "msg.success.update.x";
					message = MessageFormat.format(getResourceBundle().getString(message), selectedresearcherInfo.getPid());
		    		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
		    		FacesContext.getCurrentInstance().addMessage(null, msg);
		    		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
		    		return true;
    			}
    		}
    		catch (IllegalStateException ise){
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("StaffInfo");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole){
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
		return false;
    }
	
	public Boolean validateOrcid() {
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (StringUtils.isNotBlank(selectedresearcherInfo.getStaffInfo().getOrcid())) {
			String orcid = selectedresearcherInfo.getStaffInfo().getOrcid().replaceAll("-", "");
			String last =  StaffInfoView.takeLast(orcid, 1);
			String checkValue = StaffInfoView.removeLastCharacter(orcid);
        	String checkDegit = generateCheckDigit(checkValue);
        	
        	if (!last.equals(checkDegit)) {
        		result = false;
				message = "ORCID is not valid.";
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, message, "");
				fCtx.addMessage(null, msg);
        	}
		}else {
			result = true;
		}
		return result;
	}
	
	public String generateCheckDigit(String baseDigits) { 
	    int total = 0; 
	    for (int i = 0; i < baseDigits.length(); i++) { 
	        int digit = Character.getNumericValue(baseDigits.charAt(i)); 
	        total = (total + digit) * 2; 
	    } 
	    int remainder = total % 11; 
	    int result = (12 - remainder) % 11; 
	    return result == 10 ? "X" : String.valueOf(result); 
	}
	
    public String updateForm() {
    	if(updateResearcherInfo()) {
    		return redirect("researcherIDList");
    	}
    	else {
    		return "";
    	}
    	
    }
	
	public String gotoEditPage() throws UnsupportedEncodingException
	{		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("researcherIDList_edit")+
				   "&staff_number=" + selectedresearcherInfo.getStaff_number() +
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	

}
