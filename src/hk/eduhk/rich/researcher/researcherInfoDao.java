package hk.eduhk.rich.researcher;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;


import hk.eduhk.rich.BaseDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffInfo;

public class researcherInfoDao extends BaseDAO
{
	private static researcherInfoDao instance = null;

	public static synchronized researcherInfoDao getInstance()
	{
		if (instance == null) instance = new researcherInfoDao();
		return instance;
	}
	
	public static researcherInfoDao getCacheInstance()
	{
		return researcherInfoDao.getInstance();
	}
	
	
	public List<StaffIdentity> getResearcherList ()
	{
		List<StaffIdentity> objList = null;
		EntityManager em = null;
		String acad_staff_ind = "Y";
		
		try
		{
		 
			em = getEntityManager();
			String query = "SELECT a FROM StaffIdentity a where a.acad_staff_ind = :acad_staff_ind order by a.pid";
			//String query = "SELECT a FROM StaffInfo a where a.acad_staff_ind = :acad_staff_ind order by a.pid";		
			TypedQuery<StaffIdentity> q = em.createQuery(query, StaffIdentity.class);
			q.setParameter("acad_staff_ind", acad_staff_ind);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}	
		return objList;
	}
	
	public StaffIdentity getResearcherInfo (String paramStudNumber)
	{
		StaffIdentity obj = null;
		EntityManager em = null;
		try
		{
			em = getEntityManager();
			obj = em.find(StaffIdentity.class, paramStudNumber);
		}
		finally
		{
			pm.close(em);
		}

		return obj;	
	}
	
	public StaffInfo updateResarcherInfo (StaffInfo obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	

}
