package hk.eduhk.rich;

import javax.faces.FacesException;
import javax.faces.component.*;


/**
 * Override the getValues method of org.primefaces.component.selectmanymenu.SelectManyMenuRenderer
 * to prevent it throwing FacesException
 * 
 * <AUTHOR>
 *
 */
public class SelectManyMenuRenderer extends org.primefaces.component.selectmanymenu.SelectManyMenuRenderer
{
		
    protected Object getValues(UIComponent component) 
    {
    	Object value = null;
    	
    	try
    	{
    		value = super.getValues(component);
    	}
    	catch (FacesException fe)
    	{
    		// Ignore this exception
    	}

        return value;
    }

}
