package hk.eduhk.rich.util.listener;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.access.AccessDAO;

import javax.servlet.http.HttpSessionAttributeListener;
import javax.servlet.http.HttpSessionBindingEvent;


public class AppSessionAttributeListener implements HttpSessionAttributeListener
{

	private static final Logger logger = Logger.getLogger(AppSessionAttributeListener.class.getName());
	
	
	@Override
	public void attributeAdded(HttpSessionBindingEvent event)
	{
		// Do not update last login date in local environment
		//logger.log(Level.INFO, "AppSessionAttributeListener!");
		if (!Constant.LOCAL_ENV)
		{
		}
	}
	

	@Override
	public void attributeRemoved(HttpSessionBindingEvent event)
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void attributeReplaced(HttpSessionBindingEvent event)
	{
		// TODO Auto-generated method stub
		
	}

}
