package hk.eduhk.rich.util.concurrent;

import java.util.concurrent.*;


public class PriorityThreadFactory implements ThreadFactory
{

	private int priority;


	/**
	 * Default Constructor
	 */
	public PriorityThreadFactory()
	{
		this(Thread.NORM_PRIORITY);
	}


	public PriorityThreadFactory(int priority)
	{
		this.priority = priority;
	}


	 /**
	  * Return a new thread which is low priority (normal priority = 3)
	  */
	public Thread newThread(Runnable r) 
	{
		Thread t = new Thread(r);
		t.setPriority(priority);
		return t;
	}

}