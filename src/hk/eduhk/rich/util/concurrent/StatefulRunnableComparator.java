package hk.eduhk.rich.util.concurrent;

import java.util.Comparator;

/**
 * StatefulRunnableComparator implements the comparison function of StatefulRunnable.
 * The defined order is the start date of StatefulRunnable.
 */
public class StatefulRunnableComparator implements Comparator<StatefulRunnable>
{

	public int compare(StatefulRunnable r1, StatefulRunnable r2)
	{
		int cp = 0;

		// Both start dates are null, compare the queue date instead.
		if (r1.getStartDate() == null && r2.getStartDate() == null)
		{
			if (r1.getQueueDate() != null)
			{
				if (r2.getQueueDate() != null) cp = r1.getQueueDate().compareTo(r2.getQueueDate());
				  else cp = -1;
			}
			else
			{
				if (r2.getQueueDate() != null) cp = 1;
				  else cp = 0;
			}
		}
		else
		{
			if (r1.getStartDate() != null)
			{
				if (r2.getStartDate() != null) cp = r1.getStartDate().compareTo(r2.getStartDate());
					else cp = -1;
			}
			else
			{
				if (r2.getStartDate() != null) cp = 1;
					else cp = 0;
			}
		}

		return cp;
	}


	public boolean equals(Object obj)
	{
		return (this == obj);
	}

}