package hk.eduhk.rich.util.filter;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;


/**
 * ExcludeUrlFilter 
 * 
 * <AUTHOR> <PERSON>
 * @version 1.0
 */
@SuppressWarnings("unused")
public abstract class ExcludeURIFilter implements Filter
{
	
	protected FilterConfig filterConfig = null;
	
	private Set<String> excludeURISet = new HashSet<String>();	
	
	private static final Logger logger = Logger.getLogger(ExcludeURIFilter.class.getName());
	
	
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
		
		String contextPath = filterConfig.getServletContext().getContextPath();
		
		// Parse the excludeUrl parameter
		String excludeURIsValue = filterConfig.getInitParameter("excludeURI");
		
		if (!GenericValidator.isBlankOrNull(excludeURIsValue))
		{
			String[] excludeURIs = excludeURIsValue.split("\\s");
			if (excludeURIs != null)
			{
				// Get the URL that are excluded from this filter checking
				for (String excludeURI : excludeURIs)
				{
					if (excludeURI.length() > 0)
					{
						excludeURISet.add(contextPath + excludeURI);
					}
				}
			}
		}
	}


	public void destroy()
	{
		filterConfig = null;
	}
	
	
	protected Set<String> getExcludeURISet()
	{
		return excludeURISet;
	}
	
	
	public boolean skipFilter(ServletRequest req) throws IOException, ServletException
	{
		boolean skipFilter = false;
		
		// Make sure we are dealing with HTTP
		if (req instanceof HttpServletRequest)
		{
			HttpServletRequest httpReq = (HttpServletRequest) req;
			String requestURI = httpReq.getRequestURI();
	    	skipFilter = excludeURISet.contains(requestURI);
		}
		
		return skipFilter;
	}


}