package hk.eduhk.rich.util.filter;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.access.AccessControl;
import hk.eduhk.rich.access.Function;


/**
 * FunctionAccessControlFilter 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SuppressWarnings("unused")
public class AdminFunctionAccessFilter implements Filter
{
	
	private FilterConfig filterConfig = null;
	
	private static final Logger logger = Logger.getLogger(AdminFunctionAccessFilter.class.getName());
	private static final Level LVL_PATH_INFO = Level.FINEST;
	
	
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
	}


	public void destroy()
	{
		filterConfig = null;
	}


	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
	    if (req instanceof HttpServletRequest && res instanceof HttpServletResponse)
	    {
	    	// DO NOT remove this line, it inserts userId in local environment
	    	//req = new RemoteUserHttpServletRequestWrapper((HttpServletRequest) req);
	    	
	    	HttpServletRequest httpReq = (HttpServletRequest) req;
	    	HttpServletResponse httpRes = (HttpServletResponse) res;

	    	boolean isAuthorized = false;
	    	
	    	// Get the user information
    		// Get userId and serverPath
	    	String userId = httpReq.getRemoteUser();
	    	String serverPath = httpReq.getServletPath();
	    	
	    	if (logger.isLoggable(LVL_PATH_INFO))
	    	{
	    		logger.log(LVL_PATH_INFO, "userId=" + userId + ", serverPath=" + serverPath);
	    	}
	    	
	    	// Get the corresponding mapped Function from the request URL
    		AccessControl acsControl = AccessControl.getInstance();
    		List<Function> funcList = acsControl.getFunctionListByMatchedUrl(serverPath);

    		for (Function func : funcList)
    		{
		    	// Identify whether current user has access right to the target Function
	    		if (func != null && func.getFunctionAuthorizer() != null && func.isAuthorized(userId))
	    		{
	    			isAuthorized = true;
	    			break;
	    		}
    		}

    		// Deny access if the access page is not authorized
	    	if (!isAuthorized)
	    	{
	    		httpRes.sendError(HttpServletResponse.SC_NOT_FOUND);
	    		return;
	    	}
	    }
	    
	    chain.doFilter(req, res);
	}

}