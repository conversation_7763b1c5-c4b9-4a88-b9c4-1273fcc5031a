package hk.eduhk.rich.util.filter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.validator.GenericValidator;



public class UserAccessFilter extends ExcludeURIFilter implements Filter 
{

	private FilterConfig filterConfig = null;
	private static final Logger logger = Logger.getLogger(UserAccessFilter.class.getName());
	
	public UserAccessFilter(){
		super();
	}
	
	@Override
	public void destroy()
	{
		// TODO Auto-generated method stub
		filterConfig = null;
	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
		// TODO Auto-generated method stub
		//logger.log(Level.INFO, "UserAccessFilter!");
		HttpServletRequest httpReq = (HttpServletRequest) req;
    	HttpServletResponse httpRes = (HttpServletResponse) res;
    	HttpSession session = httpReq.getSession();

    	Set<String> excludeUrlSet = new HashSet<String>();
    	super.init(filterConfig);
    	excludeUrlSet = super.getExcludeURISet();

    	String requestUri = httpReq.getRequestURI();

    	//logger.log(Level.INFO, "requestUri" + requestUri + " contains?"+excludeUrlSet.contains(requestUri)+") ");
    	if(!excludeUrlSet.contains(requestUri)){
    		if(session!=null){
    			String userId = httpReq.getRemoteUser();
    			if(GenericValidator.isBlankOrNull(userId)){
    				httpRes.sendError(HttpServletResponse.SC_NOT_FOUND);
					return;
    			}
    		}
    	}

    	chain.doFilter(req, res);
    	
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException
	{
		// TODO Auto-generated method stub
		this.filterConfig = filterConfig;
		
	}

}
