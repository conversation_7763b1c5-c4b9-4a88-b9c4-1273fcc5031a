package hk.eduhk.rich.util.filter;

import java.io.IOException;

import javax.servlet.*;
import javax.servlet.http.*;


public class NoCacheFilter implements Filter 
{

    /**
     * {@inheritDoc}
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException 
    {
    }

    
    /**
     * <p>
     * Set HTTP cache headers.
     * </p>
     * {@inheritDoc}
     */
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletResponse res = (HttpServletResponse) servletResponse;

        // set cache directives
        res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // HTTP
        // 1.1.
		res.setHeader("Pragma", "no-cache"); // HTTP 1.0.
		res.setDateHeader("Expires", 0); // Proxies.

        filterChain.doFilter(servletRequest, servletResponse);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void destroy() 
    {
    }
    
}