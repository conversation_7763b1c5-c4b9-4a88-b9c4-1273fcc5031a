package hk.eduhk.rich.util.filter;

import java.io.IOException;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.ws.rs.HttpMethod;

import org.apache.commons.lang3.StringUtils;


public class CORSFilter implements Filter 
{

	private FilterConfig filterConfig = null;

	
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
	}


	public void destroy()
	{
		filterConfig = null;
	}

	 
	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException 
	{
		HttpServletRequest httpReq = (HttpServletRequest) req;
		HttpServletResponse httpRes = (HttpServletResponse) res;

		httpRes.setHeader("Access-Control-Allow-Origin", "*");
		httpRes.setHeader("Access-Control-Allow-Methods", "GET,POST,DELETE,PUT");
		httpRes.setHeader("Access-Control-Max-Age", "3600");
		httpRes.setHeader("Access-Control-Allow-Headers", "authorization,content-type,x-requested-with");

		// Do NOT chain if it is an OPTIONS request 
		// which is a CORS preflight request
		if (StringUtils.equalsIgnoreCase(httpReq.getMethod(), HttpMethod.OPTIONS))
		{
			return;
		}
		
		chain.doFilter(req, res);
	}
 
}