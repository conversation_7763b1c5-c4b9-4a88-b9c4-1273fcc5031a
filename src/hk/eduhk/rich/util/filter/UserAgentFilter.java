package hk.eduhk.rich.util.filter;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

//import javax.inject.Inject;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;

import hk.eduhk.rich.param.*;
import nl.basjes.parse.useragent.*;


public class UserAgentFilter implements Filter
{

	private FilterConfig filterConfig = null;
	private UserAgentAnalyzer analyzer;
	private Logger logger = Logger.getLogger(UserAgentFilter.class.getName());
	
	// For JSON parsing
	private ObjectMapper objMapper = new ObjectMapper();
	private CollectionType typeRef = objMapper.getTypeFactory().constructCollectionType(ArrayList.class, String.class);
		
//	@Inject
//	private SysParamDAO paramDAO;

	
	@Override
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
		
		// Initialize UserAgentAnalyzer
		List<String> fieldList = Arrays.asList(new String[] {UserAgent.AGENT_NAME});
		this.analyzer = UserAgentAnalyzer.newBuilder()
							            .withCache(128)
							            .withFields(fieldList)
							            .withAllFields()
							            .build();
	}


	@Override
	public void destroy()
	{
		this.filterConfig = null;
		this.analyzer.destroy();
		this.analyzer = null;
	}


	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException
	{
		if (request instanceof HttpServletRequest)
		{
			List<String> supportList = null;

			// Retrieve the browser support list from system parameter.
			SysParamDAO paramDAO = SysParamDAO.getCacheInstance();
			String json = paramDAO.getSysParamValueByCode(SysParam.PARAM_BROWSER_SUPPORT);				
			try
			{
				if (StringUtils.isNotBlank(json))
				{
					supportList = objMapper.readValue(json, typeRef);
				}
			}
			catch (IOException ioe)
			{
				logger.log(Level.WARNING, "Cannot convert browser support JSON to List (json=" + json + ")", ioe.getMessage());
			}
			
			// Check whether the current browser is supported.
			// supportList is loaded when it is not null.
			if (supportList != null)
			{
				HttpServletRequest httpReq = (HttpServletRequest) request;
				HttpServletResponse httpRes = (HttpServletResponse) response;
				
				// User agent parsing
				UserAgent userAgent = analyzer.parse(httpReq.getHeader("User-Agent"));
				AgentField field = userAgent.get(UserAgent.AGENT_NAME);
				String browserName = field.getValue();
				
				// Current browser not supported
				if (!supportList.contains(browserName))
				{
					// Put the browser support list in session. 
					HttpSession session = httpReq.getSession();
					session.setAttribute(SysParam.PARAM_BROWSER_SUPPORT, supportList);
					
					// Redirect to the support browser list page.
					logger.log(Level.INFO, "Unsupported browser: " + browserName + " (user=" + httpReq.getRemoteUser() + ")");
					httpRes.sendRedirect(httpReq.getContextPath() + "/browser.xhtml");
					return;
				}
			}
		}
	
		chain.doFilter(request, response);
	}

}