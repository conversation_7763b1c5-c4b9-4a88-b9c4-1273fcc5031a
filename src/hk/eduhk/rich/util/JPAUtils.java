package hk.eduhk.rich.util;

import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.Column;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.rich.BaseDAO;


/**
 * <tt>JPAUtils</tt>  
 */
public class JPAUtils 
{

	private static Logger logger = Logger.getLogger(JPAUtils.class.getName());

	
	public static <T> String convertQueryParamCol(String id, T[] paramArray, Class<T> type)
	{
		return convertQueryParamList(id, Arrays.asList(paramArray), type);
	}

	
	public static <T> String convertQueryParamCol(String id, Collection<T> paramCol, Class<T> type)
	{
		return convertQueryParamList(id, new ArrayList<T>(paramCol), type);
	}

	
	public static <T> String convertQueryParamList(String id, List<T> paramList, Class<T> type)
	{
		String queryParam = null;
		
		if (!GenericValidator.isBlankOrNull(id) && CollectionUtils.isNotEmpty(paramList))
		{
			StringBuilder buf = new StringBuilder();
			
			// Determine whether the parameters need to quote or not
			Object param = paramList.get(0);
			boolean asNumber = type.isAssignableFrom(Long.class) || type.isAssignableFrom(Integer.class) || type.isAssignableFrom(Short.class);
			
			int batchSize = BaseDAO.MAX_BATCH_SIZE;
			
			// One IN condition cannot have elements more than the batchSize
			// Splitting is required if exceeds the limit
			if (paramList.size() <= batchSize)
			{
				buf.append(id + " IN (");

				for (int n=0;n<paramList.size();n++)
				{
					param = paramList.get(n);
					buf.append((n > 0 ? "," : "") + (asNumber ? "" : "'") + param.toString() + (asNumber ? "" : "'"));
				}
				
				buf.append(") ");
			}
			else
			{
				// Determine the number of fragments is required
				int batchNum = (int) Math.ceil(paramList.size() / (double) batchSize);
				
				for (int n=0;n<batchNum;n++)
				{
					int startIdx = n * batchSize;
					int endIdx = Math.min(startIdx + batchSize , paramList.size());
					
					List<T> subParamList = paramList.subList(startIdx, endIdx);
					String subQueryParam = convertQueryParamList(id, subParamList, type);
					buf.append((n == 0 ? "(" : " OR ") + subQueryParam + (n == batchNum-1 ? ")" : ""));
				}
			}
			
			queryParam = buf.toString();
		}
		else
		{
			queryParam =" AND 1=0 ";
		}
			
		return queryParam;
	}
	
	
	public static int getColumnLength(String className, String fieldName)
	{
		int length = -1;
		
		try
		{
			Class<? extends Object> cls = Class.forName(className);
			if (cls != null) length = getColumnLength(cls, fieldName);
		}
		catch (ClassNotFoundException e)
		{
			// TODO Auto-generated catch block
			logger.log(Level.WARNING, "Class not found", e);
		}
		
		return length;
	}
	
	
	/**
	 * Get the maximum column length defined in the annotation of the specified Field name in a Class
	 * @param c Class
	 * @param fieldName Field name
	 * @return the maximum column length
	 */
	public static int getColumnLength(Class c, String fieldName)
	{
		List<String> fieldNameList = Arrays.asList(fieldName.split("\\."));
		return getColumnLength(c, fieldNameList);
	}
	
	
	private static int getColumnLength(Class c, List<String> fieldNameList)
	{
		int length = -1;
		
		if (c != null)
		{
			try
			{
				if (fieldNameList != null)
				{
					// Fetch along the path
					if (fieldNameList.size() > 1)
					{
						Field field = c.getDeclaredField(fieldNameList.get(0));
						return getColumnLength(field.getType(), fieldNameList.subList(1, fieldNameList.size()));
					}
					
					// End of the path, get the length defined
					else
					{
						String fieldName = fieldNameList.get(0);
						try
						{
							Field field = c.getDeclaredField(fieldName);
							Column annot = field.getAnnotation(Column.class);
							length = (annot != null) ? annot.length() : Integer.MAX_VALUE;
						}
						catch (NoSuchFieldException nsfe)
						{
							// No such field defined in the class, try to retrieve from its superclass
							Class superClass = c.getSuperclass();
							return (superClass != null) ? getColumnLength(superClass, fieldName) : length;
						}
					}
				}
			}
			catch (Exception e)
			{
				// Something unexpected, log it.
				logger.log(Level.WARNING, "", e);
			}
		}
		
		return length;
	}
	
	
	/**
	 * Get annotated declared fields
	 *  
	 * @param cls
	 * @param annotationClass
	 * @return
	 */
	public static List<Field> getAnnotatedDeclaredFieldList(Class cls, Class<? extends Annotation> annotationClass) 
	{
		List<Field> fieldList = new ArrayList<Field>();
		
		// Fetch the annotated field in specified class
		Field[] fields = cls.getDeclaredFields();
		for (Field field : fields) 
		{
			if (field.isAnnotationPresent(annotationClass))
			{
				fieldList.add(field);
			}
		}
		
		// Fetch the annotated field in parent class
		Class superClass = cls.getSuperclass();
        if (superClass != null) 
        {
            List<Field> superFieldList = getAnnotatedDeclaredFieldList(superClass, annotationClass);
            if(superFieldList.size() > 0) fieldList.addAll(superFieldList);
        }
		
		return fieldList;
	}
	
	
	// Testing method
	public static void main(String[] args)
	{
		
		List<Integer> intList = new ArrayList<Integer>();
		List<String> strList = new ArrayList<String>();
		
		for (int n=1;n<=10;n++)
		{
			intList.add(n);
			strList.add(String.valueOf(n));
		}
		
		System.out.println(JPAUtils.convertQueryParamList("pk.tagId", intList, Integer.class));
		System.out.println(JPAUtils.convertQueryParamList("pk.tagId", strList, String.class));
	}
	
}