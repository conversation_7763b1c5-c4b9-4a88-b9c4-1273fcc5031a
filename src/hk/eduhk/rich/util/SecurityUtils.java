package hk.eduhk.rich.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.ObjectOutputStream;
import java.io.UnsupportedEncodingException;
import java.security.DigestInputStream;
import java.security.DigestOutputStream;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.io.IOUtils;

import hk.eduhk.rich.Constant;


public class SecurityUtils
{

	public static final String ALGO_MD5  	= "MD5";
	public static final String ALGO_SHA1 	= "SHA1";
	public static final String ALGO_SHA256 	= "SHA-256";	// SHA-2, 256-bits
	public static final String ALGO_SHA512 	= "SHA-512";	// SHA-2, 512-bits
	
	private static final Logger logger = Logger.getLogger(SecurityUtils.class.getName());
	

	public static String getDigest(String obj)
	{
		return getDigest(obj, ALGO_MD5);
	}
	
	
	public static String getDigest(File obj)
	{
		return getDigest(obj, ALGO_MD5);
	}
	
	
	public static String getDigest(Object obj)
	{
		return getDigest(obj, ALGO_MD5);
	}
	
	
	/**
	 * Return the message digest of the provided String.
	 * @param obj Input String
	 * @param algorithm Digest algorithm
	 * @return
	 */
	public static String getDigest(String obj, String algorithm)
	{
		String digest = null;

		if (obj != null)
		{
			try
			{
				MessageDigest md = MessageDigest.getInstance(algorithm);
				digest = byteToHexString(md.digest(obj.getBytes("UTF-8")));
			}
			catch (Exception e)
			{
				// Nothing can be done.
			}
		}
		
		return digest;
	}
	
	
	/**
	 * Return the message digest of the provided File.
	 * This method computes the message digest based on the binary representation
	 * of the File. 
	 * 
	 * @param obj Target File
	 * @param algorithm Digest algorithm
	 * @return Messsage digest of the target File, or null if the target 
	 * 		   File is null or does not exist.
	 */
	public static String getDigest(File obj, String algorithm)
	{
		String digest = null;
		InputStream is = null;

		if (obj != null && obj.exists())
		{
			try
			{
				MessageDigest md = MessageDigest.getInstance(algorithm);
				is = new BufferedInputStream(new DigestInputStream(new FileInputStream(obj), md));

				// Just read and discard the bytes
				int numOfBytes = 0;
				byte[] buf = new byte[8192];
				while ((numOfBytes = is.read(buf, 0, buf.length)) > 0) {}
				
				digest = byteToHexString(md.digest());
			}
			catch (Exception e)
			{
				// Ignore Exception
			}
			finally
			{
				IOUtils.closeQuietly(is);
			}
		}
		
		return digest;
	}
	
	
	/**
	 * Return the message digest of the provided object.
	 * This method serializes the target Object into byte stream. 
	 * The message digest is computed based on the bytes generated.
	 * 
	 * As this involves the serialization, the target Object must 
	 * implements Serializable interface. Otherwise, this method 
	 * returns null.  
	 * 
	 * @param obj Target Object
	 * @param algorithm Digest algorithm
	 * @return Messsage digest of the target Object, or null if the target 
	 * 		   Object is null or does not implement Serializable.
	 */
	public static String getDigest(Object obj, String algorithm)
	{
		String digest = null;
		ObjectOutputStream oos = null;
		DigestOutputStream dos = null;

		if (obj != null)
		{
			try
			{
				MessageDigest md = MessageDigest.getInstance(algorithm);
				dos = new DigestOutputStream(new ByteArrayOutputStream(), md);
				oos = new ObjectOutputStream(new BufferedOutputStream(dos));
				oos.writeObject(obj);
				oos.flush();
				digest = byteToHexString(md.digest());
			}
			catch (Exception e)
			{
				// Ignore Exception
			}
			finally
			{
				IOUtils.closeQuietly(dos);
				IOUtils.closeQuietly(oos);
			}
		}
		
		return digest;
	}
	
	
	/***
	 * Convert the byte array into its hexadecimal representation.
	 * @param b
	 * @return
	 */
	private static String byteToHexString(byte[] b)
	{
		String strHex = null;

		if (b != null)
		{
			StringBuilder buf = new StringBuilder();
			for (int i=0;i<b.length;i++)
			{
		        String s = Integer.toHexString(0xFF & b[i]);
		        if (s.length() < 2) buf.append("0");
		        buf.append(s);
			}
			strHex = buf.toString();	
		}

		return strHex;
	}
	

	public static String encrypt(String value)
	{
		String strEncrypted = null;
		
		try
		{
			SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, spec, new IvParameterSpec(new byte[16]));
			
			byte[] encryptData = cipher.doFinal(value.getBytes("UTF-8"));
			strEncrypted = Hex.encodeHexString(encryptData); 
		}
		catch (GeneralSecurityException | UnsupportedEncodingException e)
		{
			logger.log(Level.WARNING, "Encryption is failed", e);
		}

		return strEncrypted; 
	}
	
	
	public static String decrypt(String value)
	{
		String strDecrypted = null;

		try
		{
			SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
			cipher.init(Cipher.DECRYPT_MODE, spec, new IvParameterSpec(new byte[16]));
			
			byte[] decryptData = cipher.doFinal(Hex.decodeHex(value.toCharArray()));
			strDecrypted = new String(decryptData, "UTF-8");			
		}
		catch (GeneralSecurityException | UnsupportedEncodingException | DecoderException e)
		{
			logger.log(Level.WARNING, "Decryption is failed", e);
		}

		return strDecrypted; 
	}
	
	public static Cipher getCipher(int opMode) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchAlgorithmException, NoSuchPaddingException
	{
		SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
		cipher.init(opMode, spec, new IvParameterSpec(new byte[16]));
		return cipher;
	}
}
