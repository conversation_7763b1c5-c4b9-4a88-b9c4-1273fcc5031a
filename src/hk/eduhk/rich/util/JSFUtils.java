package hk.eduhk.rich.util;

import java.io.IOException;
import java.util.*;

import javax.faces.FacesException;
import javax.faces.application.Application;
import javax.faces.application.Resource;
import javax.faces.component.*;
import javax.faces.component.visit.*;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.faces.view.facelets.FaceletContext;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;


public class JSFUtils
{
	
	
	public static <T> List<SelectItem> getGroupedSelectItemList(List<T> objList, String name, String value, String groupBy)
	{
		List<SelectItem> itemList = new ArrayList<SelectItem>();
		
		if (CollectionUtils.isNotEmpty(objList))
		{
			Map<String, List<SelectItem>> itemGroupMap = new LinkedHashMap<String, List<SelectItem>>();
			
			for (T obj : objList)
			{
				String groupName = null;
				String itemName = null;
				String itemValue = null;
				
				// Get the properties from the object
				try
				{
					groupName = BeanUtils.getProperty(obj, groupBy);
					itemName = BeanUtils.getProperty(obj, name);
					itemValue = BeanUtils.getProperty(obj, value);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
				
				// Instantiate SelectItem and add to the corresponding item group
				List<SelectItem> subItemList = itemGroupMap.get(groupName);
				
				if (subItemList == null)
				{
					subItemList = new ArrayList<SelectItem>();
					itemGroupMap.put(groupName, subItemList);
				}
				
				SelectItem item = new SelectItem(itemValue, itemName);
				subItemList.add(item);				
			}
			
			
			// Iterate keys of nameGroupMap
			Set<String> groupNameSet = itemGroupMap.keySet();
			for (String groupName : groupNameSet)
			{
				SelectItemGroup itemGroup = new SelectItemGroup(groupName);
				List<SelectItem> subItemList = itemGroupMap.get(groupName);
				
				if (subItemList != null)
				{
					itemGroup.setSelectItems(subItemList.toArray(new SelectItem[0]));
					itemList.add(itemGroup);
				}
			}
		}
		
		return itemList;
	}
	

	public static UIComponent findComponent(final String id) 
	{
	    FacesContext context = FacesContext.getCurrentInstance(); 
	    UIViewRoot root = context.getViewRoot();
	    final UIComponent[] found = new UIComponent[1];

	    root.visitTree(VisitContext.createVisitContext(context), new VisitCallback() {     
	        @Override
	        public VisitResult visit(VisitContext context, UIComponent component) {
	            if(component.getId().equals(id)){
	                found[0] = component;
	                return VisitResult.COMPLETE;
	            }
	            return VisitResult.ACCEPT;              
	        }
	    });

	    return found[0];
	}

	
	public static UIComponent findComponentByClientId(final String clientId) 
	{
	    FacesContext context = FacesContext.getCurrentInstance(); 
	    UIViewRoot root = context.getViewRoot();
	    final UIComponent[] found = new UIComponent[1];

	    root.visitTree(VisitContext.createVisitContext(context), new VisitCallback() {     
	        @Override
	        public VisitResult visit(VisitContext context, UIComponent component) {
	            if(component.getClientId().equals(clientId)){
	                found[0] = component;
	                return VisitResult.COMPLETE;
	            }
	            return VisitResult.ACCEPT;              
	        }
	    });

	    return found[0];

	}
	
	
	
	
	public static void includeCompositeComponent(UIComponent parent, String libraryName, String resourceName, String id) {
	    // Prepare.
	    FacesContext context = FacesContext.getCurrentInstance();
	    Application application = context.getApplication();
	    FaceletContext faceletContext = (FaceletContext) context.getAttributes().get(FaceletContext.FACELET_CONTEXT_KEY);

	    // This basically creates <ui:component> based on <composite:interface>.
	    Resource resource = application.getResourceHandler().createResource(resourceName, libraryName);
	    
	    System.out.println("libraryExists="+application.getResourceHandler().libraryExists(libraryName));
	    
	    System.out.println("resource="+resource);
	    
	    UIComponent composite = application.createComponent(context, resource);
	    composite.setId(id); // Mandatory for the case composite is part of UIForm! Otherwise JSF can't find inputs.

	    // This basically creates <composite:implementation>.
	    UIComponent implementation = application.createComponent(UIPanel.COMPONENT_TYPE);
	    implementation.setRendererType("javax.faces.Group");
	    composite.getFacets().put(UIComponent.COMPOSITE_FACET_NAME, implementation);

	    // Now include the composite component file in the given parent.
	    parent.getChildren().add(composite);
	    parent.pushComponentToEL(context, composite); // This makes #{cc} available.
	    try {
	        faceletContext.includeFacelet(implementation, resource.getURL());
	    } catch (IOException e) {
	        throw new FacesException(e);
	    } finally {
	        parent.popComponentFromEL(context);
	    }
	}
	
}
