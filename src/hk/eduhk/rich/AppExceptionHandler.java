package hk.eduhk.rich;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.FacesException;
import javax.faces.application.NavigationHandler;
import javax.faces.application.ViewExpiredException;
import javax.faces.context.*;
import javax.faces.event.ExceptionQueuedEvent;
import javax.faces.event.ExceptionQueuedEventContext;
import javax.servlet.http.HttpServletResponse;


public class AppExceptionHandler extends ExceptionHandlerWrapper
{

	private static Logger logger = Logger.getLogger(AppExceptionHandler.class.getName());
	
	private ExceptionHandler exceptionHandler;


	public AppExceptionHandler(ExceptionHandler exceptionHandler)
	{
		this.exceptionHandler = exceptionHandler;
	}


	@Override
	public ExceptionHandler getWrapped()
	{
		return this.exceptionHandler;
	}


	@Override
	public void handle() throws FacesException
	{
		for (Iterator<ExceptionQueuedEvent> i = getUnhandledExceptionQueuedEvents()
				.iterator(); i.hasNext();)
		{
			ExceptionQueuedEvent exceptionQueuedEvent = i.next();
			ExceptionQueuedEventContext exceptionQueuedEventContext = (ExceptionQueuedEventContext) exceptionQueuedEvent.getSource();

			// Get the Throwable from the exception queue
			FacesContext fCtx = FacesContext.getCurrentInstance();
			ExternalContext eCtx = fCtx.getExternalContext();
			NavigationHandler navHandler = fCtx.getApplication().getNavigationHandler();
			Throwable t = exceptionQueuedEventContext.getException();
			
			// Get the innermost throwable
			Throwable innerT = t;
			
			if (innerT != null)
			{
				while (innerT.getCause() != null)
				{
					innerT = innerT.getCause();
				}
			}
			
			try
			{
				if (innerT instanceof ViewExpiredException)
				{
					// Redirect to session timeout page if the view is expired
					// which is probably caused by session timeout
					navHandler.handleNavigation(fCtx, null, "/sessionTimeout.xhtml");
				}
				else
				{
					
					if (t.getCause() instanceof FileNotFoundException)
					{
							// Redirect to login page if the view is expired
							// which is probably caused by session timeout
							navHandler.handleNavigation(fCtx, null, "/index?faces-redirect=true");
					}
					else if (t instanceof NullPointerException ||
							 t.getCause() instanceof NullPointerException)
					{
						try 
						{
							eCtx.responseSendError(HttpServletResponse.SC_NOT_FOUND, null);
						}
						catch (IOException e) 
						{
							logger.log(Level.WARNING, "Cannot send HTTP error", e);
						}
					}
					else 
					{	
						// Log the Throwable to console
						logger.log(Level.WARNING, "", t);
	
						Map<String, Object> sessionMap = eCtx.getSessionMap();
						
						// Write message & stack trace 
						// from Exception to session attributes
						StringWriter sw = new StringWriter();
						t.printStackTrace(new PrintWriter(sw));
						
						sessionMap.put("exception", t.getMessage());
						sessionMap.put("stackTrace", sw.toString());
						
						navHandler.handleNavigation(fCtx, null, "/index?faces-redirect=true");
					}
				
					// Log the caught Exception
					if (logger.isLoggable(Level.INFO))
					{
						StringBuilder buf = new StringBuilder();
						buf.append("Exception=" + t.getClass().getName());
						if (t.getCause() != null) buf.append(", Cause=" + t.getCause().getClass().getName());
						logger.log(Level.INFO, buf.toString());
					}
				}
				
				fCtx.renderResponse();
			}
			finally
			{
				i.remove();
			}
		}
		
		getWrapped().handle();
	}

}
