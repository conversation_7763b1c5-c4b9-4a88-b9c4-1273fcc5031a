package hk.eduhk.rich.view;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.imageio.ImageIO;
import javax.persistence.OptimisticLockException;
import javax.swing.event.ChangeEvent;

import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;
import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.CartesianTicks;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.*;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;

import com.google.common.base.Strings;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.cv.CvView;
import hk.eduhk.rich.entity.FacDept;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectReport;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.DeptLabelCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.AdhocRpt;
import hk.eduhk.rich.entity.report.AdhocRptDAO;
import hk.eduhk.rich.entity.report.StaffProjCount;
import hk.eduhk.rich.entity.report.deptFundingIntExt;
import hk.eduhk.rich.entity.report.last3YearCount;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.entity.report.staffNumberName;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffRank;



@SuppressWarnings("serial")
@ManagedBean(name = "adhocRptView")
@ViewScoped
public class AdHocRptView extends ManageRIView
{
	private List<String> accessFacDeptList;
	private List<String> accessDeptList;
	
	private List<AdhocRpt> adhocRptList;
	private List<AdhocRpt> allAdhocRptList;
	private List<SelectItem> deptfilterList = null;
	
	private AdhocRpt selectedAdhocRpt;
	private AdhocRpt removeAdhocRpt;
	
	private String selectedDept;
	
	private String selectedRpt;
	private String selectedRptCat;
	private String filteredPeriod;
	private String selectedRptnote;
		
	private Date selectedStartDate;
	private Date selectedEndDate;
	
	private Integer selectedCdcfPeriods;
	private boolean isChartGenerated;

	private AppPeriodReportDAO fDao = AppPeriodReportDAO.getInstance();
	private AdhocRptDAO adhocRptDao = AdhocRptDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private PublicationDAO publicationDao = PublicationDAO.getInstance();
	private CdcfRptDAO cdcfRptDao= CdcfRptDAO.getInstance();
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	
	public String getFilteredPeriod()
	{
		return filteredPeriod;
	}

	
	public void setFilteredPeriod(String filteredPeriod)
	{
		this.filteredPeriod = filteredPeriod;
	}
	
	
	
	public List<SelectItem> getDeptfilterList() throws SQLException
	{
		
		if (deptfilterList == null) {
			deptfilterList = new ArrayList<SelectItem>();
			List<FacDeptDesc> list = fDao.getDeptDesc("","",true,false);
			List<FacDeptDesc> filterList = new ArrayList<>();
			accessDeptList = getAccessDeptList();
			//logger.log(Level.INFO, "accessDeptList:"+accessDeptList);
			if (accessDeptList != null) {
				for (int x = 0; x < list.size(); x++) {
					boolean contains = accessDeptList.contains(list.get(x).getDept_code());
					if (contains) {
						filterList.add(list.get(x));
					}
				}
			}
			List<SelectItem> itemList = new ArrayList<SelectItem>();
			SelectItemGroup itemGroup = new SelectItemGroup("--Please select--");
			if (filterList.size() > 0) {
				for (int j = 0; j < filterList.size(); j++) {
					itemList.add(new SelectItem(filterList.get(j).getDepartment_name() + " ("+filterList.get(j).getDept_code()+")"));
				}
			}
			SelectItem[] itemArr = new SelectItem[itemList.size()];
			itemGroup.setSelectItems(itemList.toArray(itemArr));
			deptfilterList.add(itemGroup);			
		}
		
		return deptfilterList;
	}


	
	public void setDeptfilterList(List<SelectItem> deptfilterList)
	{
		this.deptfilterList = deptfilterList;
	}

	public String getSelectedDept()
	{
		if (!Strings.isNullOrEmpty(selectedDept)) {
			if (selectedDept.contains("(") && selectedDept.contains(")")) {
				selectedDept = selectedDept.substring(selectedDept.indexOf("(") + 1);
				selectedDept = selectedDept.substring(0, selectedDept.indexOf(")"));
			}
		}
		return selectedDept;
	}


	
	public void setSelectedDept(String selectedDept)
	{
		this.selectedDept = selectedDept;
	}


	public Date getSelectedStartDate()
	{
		return selectedStartDate;
	}

	
	public void setSelectedStartDate(Date selectedStartDate)
	{
		this.selectedStartDate = selectedStartDate;
	}

	
	public Date getSelectedEndDate()
	{
		return selectedEndDate;
	}

	
	public void setSelectedEndDate(Date selectedEndDate)
	{
		this.selectedEndDate = selectedEndDate;
	}

	
	public Integer getSelectedCdcfPeriods()
	{
		return selectedCdcfPeriods;
	}

	
	public void setSelectedCdcfPeriods(Integer selectedCdcfPeriods)
	{
		this.selectedCdcfPeriods = selectedCdcfPeriods;
	}
	
	public String getSelectedRptnote() throws SQLException
	{
		if (!Strings.isNullOrEmpty(selectedRpt))
		{
			AdhocRpt tmp = adhocRptDao.getAdhocRpt(selectedRpt);
			selectedRptnote = tmp.getRpt_note();
		}else {
			selectedRptnote = "";
		}
		return selectedRptnote;
	}

	
	public void setSelectedRptnote(String selectedRptnote)
	{
		this.selectedRptnote = selectedRptnote;
	}
	
	public String getAdhocRptType()
	{
		String result = "";
		if (getAdhocRptList() != null && !Strings.isNullOrEmpty(getSelectedRpt())) {

			List<AdhocRpt> tmp = adhocRptList.stream()
								.filter(y -> y.getRpt_code().equals(selectedRpt))
								.collect(Collectors.toList());
			if (tmp.size() > 0) {
				result = tmp.get(0).getRpt_type();
			}
		}
		return result;
	}


	public List<AdhocRpt> getAdhocRptList()
	{
		if (adhocRptList == null) {
			adhocRptList =  adhocRptDao.getAdhocRptList("1");		
		}
		return adhocRptList;
	}

	
	public void setAdhocRptList(List<AdhocRpt> adhocRptList)
	{
		this.adhocRptList = adhocRptList;
	}

	
	
	public List<AdhocRpt> getAllAdhocRptList()
	{
		if (allAdhocRptList == null) {
			allAdhocRptList =  adhocRptDao.getAdhocRptList(null);
		}
		return allAdhocRptList;
	}


	
	public void setAllAdhocRptList(List<AdhocRpt> allAdhocRptList)
	{
		this.allAdhocRptList = allAdhocRptList;
	}



	public String getSelectedRpt()
		
	{
		return selectedRpt;
	}


	
	public void setSelectedRpt(String selectedRpt)
	{
		this.selectedRpt = selectedRpt;
	}



	public static double roundAvoid(double value, int places) {
		BigDecimal bd = new BigDecimal(Double.toString(value));
		bd = bd.setScale(places, RoundingMode.HALF_UP);
	    return bd.doubleValue();
	}
	
	
	public List <String> getAvilDeptList (List <String> i_access_dept, String College) throws SQLException{
		
		List <String> default_dept_list =  AppPeriodReportDAO.getFacultyDept(College);
		
		return default_dept_list.stream().filter(x->i_access_dept.contains(x)).collect(Collectors.toList());
	}
	

	
	private Date getReportStartDate()
	{
		Date tmpStartDate = null;
		if (getSelectedCdcfPeriods() != null) {
			List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
			for (int i = 0; i < filterPeriod.size(); i++) {
				if (tmpStartDate == null) {
					tmpStartDate = filterPeriod.get(i).getDate_from();
				}
				if (tmpStartDate.after(filterPeriod.get(i).getDate_from())) {
					tmpStartDate = filterPeriod.get(i).getDate_from();
				}
			}
		}		
		if (getSelectedStartDate() != null) {
//			if (tmpStartDate == null) {
//				tmpStartDate = selectedStartDate;
//			}
//			if (selectedStartDate.before(tmpStartDate)) {
//				tmpStartDate = selectedStartDate;
//			}
			tmpStartDate = selectedStartDate;
		}
		return tmpStartDate;
		
	}
	
	private Date getReportEndDate()
	{
		Date tmpEndDate = null;
		if (getSelectedCdcfPeriods() != null) {
			List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
			for (int i = 0; i < filterPeriod.size(); i++) {
				if (tmpEndDate == null) {
					tmpEndDate = filterPeriod.get(i).getDate_to();
				}
				if (tmpEndDate.before(filterPeriod.get(i).getDate_to())) {
					tmpEndDate = filterPeriod.get(i).getDate_to();
				}
			}
		}		
		if (getSelectedEndDate() != null) {
//			if (tmpEndDate == null) {
//				tmpEndDate = selectedEndDate;
//			}
//			if (selectedEndDate.before(tmpEndDate)) {
//				tmpEndDate = selectedEndDate;
//			}
			tmpEndDate = selectedEndDate;
		}
		return tmpEndDate;
		
	}


	
	public AdhocRpt getSelectedAdhocRpt()
	{
		return selectedAdhocRpt;
	}


	
	public void setSelectedAdhocRpt(AdhocRpt selectedAdhocRpt)
	{
		this.selectedAdhocRpt = selectedAdhocRpt;
	}
	
	
	public AdhocRpt getRemoveAdhocRpt()
	{
		return removeAdhocRpt;
	}


	
	public void setRemoveAdhocRpt(AdhocRpt removeAdhocRpt)
	{
		this.removeAdhocRpt = removeAdhocRpt;
	}


	public void onRowEdit(RowEditEvent<AdhocRpt> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
     	try {
 			//Check report is unique
 			int count = 0;
 			for (AdhocRpt r: allAdhocRptList){
 				if (event.getObject().getRpt_id().equals(r.getRpt_id())) {
 					count++;
 				}
 			}
 			if (count > 1) {
 				isDuplicateKey = true;
 			}
 			
 			if (isDuplicateKey) {
 				String param = "Report";
 				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
 				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 			}
 			
 			////Check data
 			if (event.getObject().getRpt_code().isEmpty()) {
     			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Report code cannot be null", ""));
     		}
  
 
 			//Update rpt
     		if (fCtx.getMessageList().isEmpty()) {
     			event.getObject().setUserstamp(getLoginUserId());
     			adhocRptDao.updateAdhocRpt(event.getObject());
     			if (removeAdhocRpt != null) {
     				adhocRptDao.deleteAdhocRpt(removeAdhocRpt.getRpt_id());
     				removeAdhocRpt = null;
      			}
     			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
     			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getRpt_code());
        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
        		selectedAdhocRpt = null;
        		allAdhocRptList = null;
     		}
     	}
     	catch (IllegalStateException ise)
 		{
 			logger.log(Level.WARNING, "", ise);
 			String param = bundle.getString("Report");
 			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
 		catch (OptimisticLockException ole)
 		{
 			message = bundle.getString("msg.err.optimistic.lock");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
     	catch (Exception e)
 		{
 			logger.log(Level.WARNING, "", e);
 			message = bundle.getString("msg.err.unexpected");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
	            
	        
	}
	
	public void onRowCancel(RowEditEvent<AdhocRpt> event) {
	     FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getRpt_code()));
	     FacesContext.getCurrentInstance().addMessage(null, msg);
	 }
	
	 public void onCellEdit(CellEditEvent event) {
	     Object oldValue = event.getOldValue();
	     Object newValue = event.getNewValue();
	
	
	 }
	 
	 public void onAddNew() {
	 	AdhocRpt newItem = new AdhocRpt();
	 	allAdhocRptList.add(0, newItem);
	 }
	
	 public void keyChangedListener(ValueChangeEvent event) {
	 	if (event.getOldValue() != null) {
	 		removeAdhocRpt =  adhocRptDao.getAdhocRptById((Integer)event.getOldValue());
	 	}
	 } 
	 
	 public void deleteAdhocRpt() {
    	if (selectedAdhocRpt != null) {
    		try {
    			if (selectedAdhocRpt.getRpt_id() != null) {
    				adhocRptDao.deleteAdhocRpt(selectedAdhocRpt.getRpt_id());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedAdhocRpt.getRpt_code());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
    			allAdhocRptList.remove(selectedAdhocRpt);
    			selectedAdhocRpt = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedAdhocRpt.getRpt_code());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
	public void reloadAdhocRptList() 
	{
		allAdhocRptList = null;
	}


	//Filter by access data list
	public List<String> getAccessFacDeptList() throws SQLException
	{
		if(accessFacDeptList == null) {
			accessFacDeptList = AppPeriodReportDAO.getAccessFacultyDeptList(getAccessDeptList());
		}

		return accessFacDeptList;
	}


	
	public void setAccessFacDeptList(List<String> accessFacDeptList)
	{
		this.accessFacDeptList = accessFacDeptList;
	}


	
	public List<String> getAccessDeptList() throws SQLException
	{
		if (accessDeptList == null) {
			accessDeptList = getRiAdminDeptList();
			if (!accessDeptList.isEmpty()) {
				LookupValueDAO dao = LookupValueDAO.getCacheInstance();
				StaffDAO staffDao = StaffDAO.getInstance();
				List<String> facDeptList = new ArrayList<>();
				List<LookupValue> facList = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
				for (LookupValue f:facList) {
					if (accessDeptList.contains(f.getPk().getLookup_code())) {
						List<FacDept> tmpList = staffDao.getFacDeptByFac(f.getPk().getLookup_code());
						for (FacDept d:tmpList) {
							if (!facDeptList.contains(d.getFac_dept())) {
								facDeptList.add(d.getFac_dept());
							}
						}
					}
				}
				Set<String> accessDeptSet = new LinkedHashSet<>(accessDeptList);
				accessDeptSet.addAll(facDeptList);
				accessDeptList = new ArrayList<>(accessDeptSet);
			}
		}
		//System.out.println("accessDeptList:"+accessDeptList);
		return accessDeptList;
	}


	
	public void setAccessDeptList(List<String> accessDeptList)
	{
		this.accessDeptList = accessDeptList;
	}
	
	public boolean getIsChartGenerated()
	{
		return isChartGenerated;
	}

	public void setIsChartGenerated(boolean isChartGenerated)
	{
		this.isChartGenerated = isChartGenerated;
	}
	
	public void disableDownloadButton(){
		isChartGenerated = false;
	}	
	
	public void exportChart() throws Exception {
		
	}
	
	public void exportReport(String reportCode) throws SQLException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();	
		getAccessFacDeptList();
		try 
		{
			Workbook wb = null;
    		wb = new XSSFWorkbook();
    		
    		String reportPeriod = "";
    		List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
    		if (filterPeriod != null) {
    			reportPeriod = filterPeriod.get(0).getChart_desc_2()+"_";
    		}
    		//File name define
    		DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = "";
			
			reportFilteringField filter = new reportFilteringField();
			String periodDesc = "";
			if (selectedCdcfPeriods != null) {
				
				int from_y = filterPeriod.get(0).getDate_from().getYear() + 1900;
				int from_m = filterPeriod.get(0).getDate_from().getMonth()+1;
				int to_y = filterPeriod.get(0).getDate_to().getYear() + 1900;
				int to_m = filterPeriod.get(0).getDate_to().getMonth()+1;
				periodDesc = filterPeriod.get(0).getPeriod_desc();
				
				filter.setP_period(Boolean.TRUE);
				filter.setP_from_year(from_y);
				filter.setP_from_month(from_m);
				filter.setP_to_year(to_y);
				filter.setP_to_month(to_m);
				filter.setP_id_no(selectedCdcfPeriods);
			}
			else {
				filter.setP_period(Boolean.FALSE);
			}
			if( selectedStartDate != null && selectedEndDate != null ) {
				
				filter.setD_period(Boolean.TRUE);
				filter.setD_from_year(selectedStartDate.getYear()+1900);
				filter.setD_from_month(selectedStartDate.getMonth() +1);
				filter.setD_to_year(selectedEndDate.getYear()+1900);
				filter.setD_to_month(selectedEndDate.getMonth()+1);		
			}
			else {
				filter.setD_period(Boolean.FALSE);		
			}
			AdhocRpt adhocRpt = adhocRptDao.getAdhocRpt(reportCode);
			fileName = adhocRpt.getRpt_file_name() + reportPeriod + dateFormat.format(new Date()) + ".xlsx";
			switch (reportCode) {
			    case "HRO_RPT":
			    	createAdHoc001DataSheet(wb);
			        break;
			        
			}
			

    		// Get the byte array of the Workbook
	    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			eCtx.responseReset();

			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");		        
			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

			// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
			//setPrimeFacesDownloadCompleted("exportRptBtn");
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
		
			fCtx.responseComplete();

		}
		catch (IOException e) 
    	{
			
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		catch (Exception e)
		{
			
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		finally {
			String message = reportCode + " Report is Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));	
		}
	}
	
	private void createAdHoc001DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		List<String> outTypeList = Arrays.asList("120", "160");
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy h:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
    	String[] headerArray = {"Staff Number", "Full Name", "Dept", "Post", 
    			"R_Bk", "R_Jr"};
		
		
    	AdhocRpt adhocRpt = adhocRptDao.getAdhocRpt("HRO_RPT");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(adhocRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();

		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));

		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
//    		sheet.autoSizeColumn(n);
//    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, Map<String, Double>> dataList = publicationDao.getAdHoc001ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessFacDeptList());
		
		if (dataList != null) {
			// Create data rows
			for(Map.Entry<String, Map<String, Double>> name : dataList.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				int i = 0;
				String[] nameArr = name.getKey().split("/");
				for(String n : nameArr)
				{
					cell = row.createCell(i++);
		    		cell.setCellValue(n);
				}
				for(String outType : outTypeList) {
					cell = row.createCell(i++);
					if(name.getValue() != null && name.getValue().get(outType) != null)
						cell.setCellValue(PreRptView.roundAvoid(name.getValue().get(outType), 2));
					else
						cell.setCellValue(0.0);
				}
				
	    		numOfRows++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
}
