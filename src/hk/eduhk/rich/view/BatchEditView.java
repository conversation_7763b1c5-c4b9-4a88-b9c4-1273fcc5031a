package hk.eduhk.rich.view;

import java.util.List;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.importRI.ImportRIBatch;
import hk.eduhk.rich.entity.importRI.ImportRICA;
import hk.eduhk.rich.entity.importRI.ImportRIDAO;
import hk.eduhk.rich.entity.importRI.ImportRIStore;


@SuppressWarnings("serial")
@ManagedBean(name = "batchEditView")
@ViewScoped
public class BatchEditView extends BaseView
{	
	private String batchId;
	private ImportRIBatch batch;
	private ImportRICA ca;
	private List<ImportRIStore> storeList;

	
	public String getBatchId()
	{
		return batchId;
	}

	
	public void setBatchId(String batchId)
	{
		this.batchId = batchId;
	}


	
	public ImportRIBatch getBatch()
	{
		if(batch == null && getBatchId() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			batch = dao.getBatchByPK(Integer.parseInt(getBatchId()));
		}
		return batch;
	}


	
	public void setBatch(ImportRIBatch batch)
	{
		this.batch = batch;
	}
	
	
	public Integer getDataNum() {
		return getBatch().getRow_num() != null ? getBatch().getRow_num() : 0;
	}

	
	
	public ImportRICA getCa()
	{
		if(ca == null && getBatch() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			ca = dao.getCAByPK(getBatch().getArea_code());
		}
		return ca;
	}


	
	public void setCa(ImportRICA ca)
	{
		this.ca = ca;
	}


	public List<ImportRIStore> getStoreList()
	{
		if(storeList == null && getBatch() != null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			storeList = dao.getImportRIStoreList(getBatch());
		}
		return storeList;
	}


	
	public void setStoreList(List<ImportRIStore> storeList)
	{
		this.storeList = storeList;
	}
	
	
	public String update() {
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		String destUrl = redirect("uploadRI");
		if(getBatch() != null)
		{
			dao.updateBatch(getBatch());
			destUrl = redirect("uploadRI") + "&uploadSrc=" + getBatch().getArea_code();
			String message = "Update batch " + getBatch().getBatch_key() + " sucess";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}
		else {
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}
	}
	
	public String delete() {
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		String destUrl = redirect("uploadRI");
		if(getBatch() != null)
		{
			try {
				dao.deleteBatch(getBatch().getBatch_id());
				dao.runBuiltStoreStaff(getBatch().getArea_code());
			}
			catch(Exception e){
				String message = "Cannot delete batch (batch_id=" + getBatch().getBatch_id() + ")";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
			destUrl = redirect("uploadRI") + "&uploadSrc=" + getBatch().getArea_code();
			String message = "Delete batch " + getBatch().getBatch_key() + " success";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}
		else {
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}
	}
	
	
}
