package hk.eduhk.rich.view;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.entity.Department;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;
import hk.eduhk.rich.entity.publication.FundingSource;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.OutputType_PK;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.query.ExStaffQueryChain;
import hk.eduhk.rich.entity.query.KTQueryChain;
import hk.eduhk.rich.entity.query.RIQueryChain;
import hk.eduhk.rich.entity.query.StaffQueryChain;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.entity.staff.StaffRank;
import hk.eduhk.rich.entity.staff.SecDataUser;;


@SuppressWarnings("serial")
public class KTSearchPanel implements Serializable
{
	public static final String SELECT_ALL_VALUE = "all";
	public static final String SELECT_ALL_LABEL = "Select All";
	public static final String YES_VALUE = "Y";
	public static final String YES_LABEL = "Yes";
	public static final String NO_VALUE = "N";
	public static final String NO_LABEL = "No";
	public static final String EMPTY_VALUE = "empty";
	public static final String EMPTY_LABEL = " ";
	public static final String UNCONFIRM_VALUE = "U";
	public static final String UNCONFIRM_LABEL = "Unconfirm";
	
	public static final String RICH_EDITION_VALUE = "P";
	public static final String RICH_EDITION_LABEL = "RICH Edition";
	public static final String CDCF_SNAPSHOT_VALUE = "C";
	public static final String CDCF_SNAPSHOT_LABEL = "CDCF Snapshot";
	public static final String DEPT_EDITION_VALUE = "D";
	public static final String DEPT_EDITION_LABEL = "Department Edition";
	
	public static final String CDCF_PENDING_VALUE = "CDCF_PENDING";
	public static final String CDCF_PENDING_LABEL = "CDCF Pending";
	public static final String CDCF_PROCESSED_VALUE = "CDCF_PROCESSED";
	public static final String CDCF_PROCESSED_LABEL = "CDCF Processed";
	public static final String CDCF_GENERATED_VALUE = "CDCF_GENERATED";
	public static final String CDCF_GENERATED_LABEL = "CDCF Generated";
	public static final String CDCF_NOT_SELECTED_VALUE = "CDCF_NOT_SEL";
	public static final String CDCF_NOT_SELECTED_LABEL = "CDCF Not Selected";
	
	public static final String LIST_TYPE_RI_VALUE = "ri";
	public static final String LIST_TYPE_RI_LABEL = "By RI";
	public static final String LIST_TYPE_STAFF_VALUE = "staff";
	public static final String LIST_TYPE_STAFF_LABEL = "By Staff";
	
	public static final String SORT_COL_DEFAULT_VALUE = "default";
	public static final String SORT_COL_DEFAULT_LABEL = "Default";
	public static final String SORT_COL_KT_NO_VALUE = "formNo";
	public static final String SORT_COL_KT_NO_LABEL = "Form No.";
	public static final String SORT_COL_RI_NAME_VALUE = "riName";
	public static final String SORT_COL_RI_NAME_LABEL = "Title";
	public static final String SORT_COL_CONTRIBUTOR_VALUE = "contributor";
	public static final String SORT_COL_CONTRIBUTOR_LABEL = "Contributor Name";
	public static final String SORT_COL_FROM_DATE_VALUE = "fromDate";
	public static final String SORT_COL_FROM_DATE_LABEL = "Start Date";
	public static final String SORT_COL_TO_DATE_VALUE = "toDate";
	public static final String SORT_COL_TO_DATE_LABEL = "End Date";
	public static final String SORT_COL_CDCF_STAT_VALUE = "cdcfStat";
	public static final String SORT_COL_CDCF_STAT_LABEL = "CDCF Status";
	
	public static final String SORT_ORDER_ASC_VALUE = "ASC";
	public static final String SORT_ORDER_ASC_LABEL = "Ascending";
	public static final String SORT_ORDER_DESC_VALUE = "DESC";
	public static final String SORT_ORDER_DESC_LABEL = "Descending";

	// Selection lists - Person
	private List<String> staffNameList;
	private List<StaffRank> rankList;
	
	// Selection lists - ex-IEd staff	
	private List<String> exStaffNameList;
	
	// Selection lists - kt
	private List<KtForm> ktTypeList;
	private List<SelectItem> viewTypeList;
	private List<SelectItem> facDeptList = null;
	
	// Selection lists - listing type
	private List<SelectItem> listingTypeList;
	private List<SelectItem> sortColList;
	private List<SelectItem> sortOrderList;
	
	
	// Selection lists - share
	private List<SelectItem> allYesNoEmptyList;
	
	// Selected values - Person	
	private String staffName;
	private List<String> selectedRankList;
	private String acadStaff = EMPTY_VALUE;
	private List<String> selectedDepartmentList;
	
	// Selected values - ex-IEd staff	
	private String exStaffName;
	private String formStaffNum;
	
	// Selected values - KT
	private String ktType;
	private String formNo;
	private String title;
	private String viewType = RICH_EDITION_VALUE;
	private List<String> selectedFacDeptList;
	private Date ktDateFrom;
	private Date ktDateTo;
	
	// Selected values - listing type
	private String sortCol = SORT_COL_DEFAULT_VALUE;
	private String sortOrder = SORT_ORDER_DESC_VALUE;
	
	// userId for restricting user access to department
	private String userId;
	private List<String> facDeptCodeList;
	
	private String paramStartDate;
	private String paramEndDate;
	private String paramFacDept;
	
	
	public String getParamStartDate()
	{
		return paramStartDate;
	}


	
	public void setParamStartDate(String paramStartDate)
	{
		this.paramStartDate = paramStartDate;
	}


	
	public String getParamEndDate()
	{
		return paramEndDate;
	}


	
	public void setParamEndDate(String paramEndDate)
	{
		this.paramEndDate = paramEndDate;
	}


	
	public String getParamFacDept()
	{
		return paramFacDept;
	}


	
	public void setParamFacDept(String paramFacDept)
	{
		this.paramFacDept = paramFacDept;
	}


	public List<String> getStaffNameList()
	{
		if(staffNameList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			List<StaffIdentity> staffList = dao.getAcadStaffList(null);
			if(staffList != null)
				staffNameList = staffList.stream().map(staff -> staff.getFullname()).collect(Collectors.toList());
		}
		return staffNameList;
	}
	

	public List<StaffRank> getRankList()
	{
		if(rankList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			rankList = new ArrayList<StaffRank>();
			List<StaffRank> staffRankList = dao.getRankList();
			List<String> EmploymentRankList = dao.getEmploymentRankList();
			for(StaffRank rank : staffRankList) {
				if(EmploymentRankList.contains(rank.getRank_code()))
					rankList.add(rank);
			}
		}
		return rankList;
	}
	
	
	public List<SelectItem> getAllYesNoEmptyList()
	{
		if(allYesNoEmptyList == null) {
			allYesNoEmptyList = new ArrayList<SelectItem>();
			allYesNoEmptyList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			allYesNoEmptyList.add(new SelectItem(YES_VALUE, YES_LABEL));
			allYesNoEmptyList.add(new SelectItem(NO_VALUE, NO_LABEL));
			allYesNoEmptyList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return allYesNoEmptyList;
	}
	
	
	public List<String> getExStaffNameList()
	{
		
		if(exStaffNameList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			List<StaffPast> staffList = dao.getPastStaffList();
			if(staffList != null)
				exStaffNameList = staffList.stream().map(staff -> staff.getFullname()).collect(Collectors.toList());
		}
		return exStaffNameList;
	}

	
	public List<KtForm> getKtTypeList()
	{
		if(ktTypeList == null) {
			FormDAO dao = FormDAO.getInstance();
			ktTypeList = dao.getKtFormList();
		}
		return ktTypeList;
	}
	
	public List<SelectItem> getViewTypeList()
	{
		if(viewTypeList == null) {
			viewTypeList = new ArrayList<SelectItem>();
			viewTypeList.add(new SelectItem(RICH_EDITION_VALUE, RICH_EDITION_LABEL));
			viewTypeList.add(new SelectItem(CDCF_SNAPSHOT_VALUE, CDCF_SNAPSHOT_LABEL));
			viewTypeList.add(new SelectItem(DEPT_EDITION_VALUE, DEPT_EDITION_LABEL));
		}
		return viewTypeList;
	}

	
	public List<SelectItem> getFacDeptList()
	{
		if(facDeptList == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			StaffDAO staffDao = StaffDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<LookupValue> l2List = dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			l2List.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L3", "US", "Y"));
			List<String> accessList = staffDao.getDataCodeByUserId(userId, "KTD");
			if(accessList == null) accessList = new ArrayList<String>();
			facDeptList = new ArrayList<SelectItem>();
			facDeptCodeList = new ArrayList<String>();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			if (haveAll) facDeptCodeList.add("xxx"+SecDataUser.allData+"xxx");
			for(LookupValue lkVal1 : l1List) {
				SelectItemGroup itemGroup = new SelectItemGroup(
						lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")");
				List<SelectItem> itemList = new ArrayList<SelectItem>();
				if(haveAll || accessList.contains(lkVal1.getPk().getLookup_code())) {
					itemList.add(new SelectItem(lkVal1.getPk().getLookup_code(), 
							lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")"));
					facDeptCodeList.add(lkVal1.getPk().getLookup_code());
				}
				for(LookupValue lkVal2 : l2List) {
					if(haveAll || 
							(lkVal2.getParent_lookup_code().equals(lkVal1.getPk().getLookup_code()) &&
							accessList.contains(lkVal2.getPk().getLookup_code()))) {
						itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
								lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
						facDeptCodeList.add(lkVal2.getPk().getLookup_code());
					}
				}
				if(!itemList.isEmpty()) {
					SelectItem[] itemArr = new SelectItem[itemList.size()];
					itemGroup.setSelectItems(itemList.toArray(itemArr));
					facDeptList.add(itemGroup);
				}
			}
			SelectItemGroup itemGroup = new SelectItemGroup("Other");
			List<SelectItem> itemList = new ArrayList<SelectItem>();
			for(LookupValue lkVal2 : l2List) {
				if(haveAll ||
						(lkVal2.getParent_lookup_code().equals(null) &&
						accessList.contains(lkVal2.getPk().getLookup_code()))) {
					itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
							lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
					facDeptCodeList.add(lkVal2.getPk().getLookup_code());
				}
			}
			if(!itemList.isEmpty()) {
				SelectItem[] itemArr = new SelectItem[itemList.size()];
				itemGroup.setSelectItems(itemList.toArray(itemArr));
				facDeptList.add(itemGroup);
			}
		}
		return facDeptList;
	}

	private List<String> getFacDeptCodeList() {
		if(facDeptCodeList == null) getFacDeptList();
		return facDeptCodeList;
	}
	
	public List<SelectItem> getListingTypeList()
	{
		if(listingTypeList == null) {
			listingTypeList = new ArrayList<SelectItem>();
			listingTypeList.add(new SelectItem(LIST_TYPE_RI_VALUE, LIST_TYPE_RI_LABEL));
			listingTypeList.add(new SelectItem(LIST_TYPE_STAFF_VALUE, LIST_TYPE_STAFF_LABEL));
		}
		return listingTypeList;
	}


	
	public List<SelectItem> getSortColList()
	{
		if(sortColList == null) {
			sortColList = new ArrayList<SelectItem>();
			sortColList.add(new SelectItem(SORT_COL_DEFAULT_VALUE, SORT_COL_DEFAULT_LABEL));
			sortColList.add(new SelectItem(SORT_COL_KT_NO_VALUE, SORT_COL_KT_NO_LABEL));
			sortColList.add(new SelectItem(SORT_COL_FROM_DATE_VALUE, SORT_COL_FROM_DATE_LABEL));
			sortColList.add(new SelectItem(SORT_COL_TO_DATE_VALUE, SORT_COL_TO_DATE_LABEL));
			//sortColList.add(new SelectItem(SORT_COL_CDCF_STAT_VALUE, SORT_COL_CDCF_STAT_LABEL));
		}
		return sortColList;
	}


	
	public List<SelectItem> getSortOrderList()
	{
		if(sortOrderList == null) {
			sortOrderList = new ArrayList<SelectItem>();
			sortOrderList.add(new SelectItem(SORT_ORDER_DESC_VALUE, SORT_ORDER_DESC_LABEL));
			sortOrderList.add(new SelectItem(SORT_ORDER_ASC_VALUE, SORT_ORDER_ASC_LABEL));
		}
		return sortOrderList;
	}


	public String getStaffName()
	{
		return staffName != null ? staffName.trim() : null;
	}

	
	public void setStaffName(String staffName)
	{
		this.staffName = staffName;
	}
	
	
	public List<String> getSelectedRankList()
	{
		return selectedRankList;
	}
	
	
	public void setSelectedRankList(List<String> selectedRankList)
	{
		this.selectedRankList = selectedRankList;
	}
	
	
	public String getAcadStaff()
	{
		return acadStaff;
	}

	
	public void setAcadStaff(String acadStaff)
	{
		this.acadStaff = acadStaff;
	}

	
	public List<String> getSelectedDepartmentList()
	{
		return selectedDepartmentList;
	}

	
	public void setSelectedDepartmentList(List<String> selectedDepartmentList)
	{
		this.selectedDepartmentList = selectedDepartmentList;
	}

	
	public String getExStaffName()
	{
		return exStaffName != null ? exStaffName.trim() : null;
	}

	
	public void setExStaffName(String exStaffName)
	{
		this.exStaffName = exStaffName;
	}
	
	
	public String getFormStaffNum()
	{
		return formStaffNum != null ? formStaffNum.trim() : null;
	}
	

	
	public void setFormStaffNum(String formStaffNum)
	{
		this.formStaffNum = formStaffNum;
	}


	
	public String getKtType()
	{
		if(ktType == null) {
			if(getKtTypeList() != null) {
				ktType = getKtTypeList().get(0).getForm_code();
			}
		}
		return ktType;
	}


	
	public void setKtType(String ktType)
	{
		this.ktType = ktType;
	}


	
	public String getFormNo()
	{
		return formNo != null ? formNo.trim() : null;
	}


	
	public void setFormNo(String formNo)
	{
		this.formNo = formNo;
	}

	
	
	public String getTitle()
	{
		return title;
	}


	
	public void setTitle(String title)
	{
		this.title = title;
	}

	
	
	public String getViewType()
	{
		return viewType;
	}


	
	public void setViewType(String viewType)
	{
		this.viewType = viewType;
	}

	
	public List<String> getSelectedFacDeptList()
	{
		if (!Strings.isNullOrEmpty(getParamFacDept())) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramFacDept.split(",")));
			selectedFacDeptList = tmp;
		}
		return selectedFacDeptList;
	}


	
	public void setSelectedFacDeptList(List<String> selectedFacDeptList)
	{
		this.selectedFacDeptList = selectedFacDeptList;
	}


	public Date getKtDateFrom() throws ParseException
	{
		if (!Strings.isNullOrEmpty(getParamStartDate())) {
			ktDateFrom = new SimpleDateFormat("dd/MM/yyyy").parse(paramStartDate); 
		}
		return ktDateFrom;
	}


	
	public void setKtDateFrom(Date ktDateFrom)
	{
		this.ktDateFrom = ktDateFrom;
	}


	
	public Date getKtDateTo() throws ParseException
	{
		if (!Strings.isNullOrEmpty(getParamEndDate())) {
			ktDateTo = new SimpleDateFormat("dd/MM/yyyy").parse(paramEndDate); 
		}
		return ktDateTo;
	}

	
	
	public void setKtDateTo(Date ktDateTo)
	{
		this.ktDateTo = ktDateTo;
	}


	public String getSortCol()
	{
		return sortCol;
	}


	
	public void setSortCol(String sortCol)
	{
		this.sortCol = sortCol;
	}


	
	public String getSortOrder()
	{
		return sortOrder;
	}


	
	public void setSortOrder(String sortOrder)
	{
		this.sortOrder = sortOrder;
	}


	
	public static String getRichEditionValue()
	{
		return RICH_EDITION_VALUE;
	}
	


	
	public static String getListTypeRiValue()
	{
		return LIST_TYPE_RI_VALUE;
	}


	
	public static String getListTypeStaffValue()
	{
		return LIST_TYPE_STAFF_VALUE;
	}


	
	public String getUserId()
	{
		return userId;
	}


	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}


	public List<String> nameAutoComplete(String query) {
		String queryLowerCase = query.toLowerCase();
        return getStaffNameList().stream().filter(t -> t.toLowerCase().contains(queryLowerCase)).collect(Collectors.toList());
	}
	
	public List<String> exNameAutoComplete(String query) {
		String queryLowerCase = query.toLowerCase();
        return getExStaffNameList().stream().filter(t -> t.toLowerCase().contains(queryLowerCase)).collect(Collectors.toList());
	}

	
	public KTQueryChain getQueryChain() throws ParseException
	{
		KTQueryChain chain = new KTQueryChain(getKtType());
		
		// Restricted user access of department
		if(((StringUtils.isBlank(getExStaffName()) && StringUtils.isBlank(getFormStaffNum())) 
				|| getViewType().equals("D"))
				&& !getFacDeptCodeList().contains("xxx"+SecDataUser.allData+"xxx")) {
			if(!CollectionUtils.isEmpty(getFacDeptCodeList()))
				chain = chain.facDept(getFacDeptCodeList(), getViewType());
			else
				chain = chain.end();
		}
		
		// Selected values - Person	
		if (StringUtils.isNotBlank(getStaffName())) chain = chain.staffName(getStaffName());
		if (!CollectionUtils.isEmpty(getSelectedRankList())) {
			chain = chain.staffRank(getSelectedRankList());
		}
		if (StringUtils.isNotBlank(getAcadStaff()) && !getAcadStaff().equals(EMPTY_VALUE)) chain = chain.isAcadStaff(getAcadStaff());
		
		// Selected values - KT
		if (StringUtils.isNotBlank(getFormNo())) chain = chain.formNo(getFormNo());
		if (StringUtils.isNotBlank(getTitle())) chain = chain.title(getTitle());
		if (StringUtils.isNotBlank(getViewType())) chain = chain.dataLevel(getViewType());
		if (!CollectionUtils.isEmpty(getSelectedFacDeptList())) {
			chain = chain.facDept(getSelectedFacDeptList(), getViewType());
		}
		if (getKtDateFrom() != null || getKtDateTo() != null) chain = chain.ktDate(getKtDateFrom(), getKtDateTo());

		// Selected values - ex-IEd staff	
		if (StringUtils.isNotBlank(getExStaffName())) chain = chain.exStaffName(getExStaffName());
		if (StringUtils.isNotBlank(getFormStaffNum())) chain = chain.formStaffNum(getFormStaffNum());

		
		return chain;
	}
	
	public StaffQueryChain getStaffQueryChain() {
		StaffQueryChain chain = new StaffQueryChain();
		
		// Restricted user access of department
//		if(!getDeptCodeList().contains("xxx"+SecDataUser.allData+"xxx")) {
//			if(!CollectionUtils.isEmpty(getDeptCodeList())) 
//				chain = chain.department(getDeptCodeList());
//			else
//				chain = chain.end();
//		}
		
		// Selected values - Person	
		if (StringUtils.isNotBlank(getStaffName())) chain = chain.staffName(getStaffName());
		if (!CollectionUtils.isEmpty(getSelectedRankList())) {
			chain = chain.staffRank(getSelectedRankList());
		}
		if (StringUtils.isNotBlank(getAcadStaff()) && !getAcadStaff().equals(EMPTY_VALUE)) chain = chain.isAcadStaff(getAcadStaff());
		if (!CollectionUtils.isEmpty(getSelectedDepartmentList())) {
			chain = chain.department(getSelectedDepartmentList());
		}
		return chain;
	}
	
	public ExStaffQueryChain getExStaffQueryChain() {
		ExStaffQueryChain chain = new ExStaffQueryChain();
		
		// Selected values - ex-IEd staff	
		if (StringUtils.isNotBlank(getExStaffName())) chain = chain.exStaffName(getExStaffName());
		if (StringUtils.isNotBlank(getFormStaffNum())) chain = chain.formStaffNum(getFormStaffNum());
				
		return chain;
	}
	
}
