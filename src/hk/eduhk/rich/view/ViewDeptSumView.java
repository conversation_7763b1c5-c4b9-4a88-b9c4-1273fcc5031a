package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.BarChartDataSet;
import org.primefaces.model.charts.bar.BarChartModel;
import org.primefaces.model.charts.bar.BarChartOptions;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.primefaces.event.CloseEvent;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.award.AwardDetails_P;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentDetails_P;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.staff.SecDataUser;


@ManagedBean(name = "viewDeptSumView")
@ViewScoped
@SuppressWarnings("serial")
public class ViewDeptSumView extends ManageRIView
{
	private String paramDept;

	private List<SelectItem> facDeptList = null; 
	private DashboardModel model;
	
	private List<CdcfRptPeriod> cdcfPeriodList = null;
	
	private List<OutputDetails_P> outputList;
	private List<ProjectDetails_P> projectList;
	private List<AwardDetails_P> awardList;
	private List<PatentDetails_P> patentList;
	
	private List<OutputType> outputTypeList;
	
	private PieChartModel outputRBkRJrPieModel;
	
	private PieChartModel projectTypePieModel;
	
	private BarChartModel outputYrTotalBarModel;
		
	private List<String> selectedOutputTypes;
	
	private PublicationDAO outputDao = PublicationDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private AwardDAO awardDao = AwardDAO.getInstance();
	private PatentDAO patentDao = PatentDAO.getInstance();
	private CdcfRptDAO cdcfRptDao = CdcfRptDAO.getInstance();
	

	public List<SelectItem> getFacDeptList()
	{
		if(facDeptList == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<LookupValue> l2List = dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			List<String> accessList = getKtAdminDeptList();
			facDeptList = new ArrayList<SelectItem>();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			for(LookupValue lkVal1 : l1List) {
				SelectItemGroup itemGroup = new SelectItemGroup(
						lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")");
				List<SelectItem> itemList = new ArrayList<SelectItem>();
				if(haveAll || accessList.contains(lkVal1.getPk().getLookup_code())) {
					itemList.add(new SelectItem(lkVal1.getPk().getLookup_code(), 
							lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")"));
				}
				for(LookupValue lkVal2 : l2List) {
					if(haveAll || 
							(lkVal2.getParent_lookup_code().equals(lkVal1.getPk().getLookup_code()) &&
							accessList.contains(lkVal2.getPk().getLookup_code()))) {
						itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
								lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
					}
				}
				if(!itemList.isEmpty()) {
					SelectItem[] itemArr = new SelectItem[itemList.size()];
					itemGroup.setSelectItems(itemList.toArray(itemArr));
					facDeptList.add(itemGroup);
				}
			}
			SelectItemGroup itemGroup = new SelectItemGroup("Other");
			List<SelectItem> itemList = new ArrayList<SelectItem>();
			for(LookupValue lkVal2 : l2List) {
				if(haveAll ||
						(lkVal2.getParent_lookup_code().equals(null) &&
						accessList.contains(lkVal2.getPk().getLookup_code()))) {
					itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
							lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
				}
			}
			if(!itemList.isEmpty()) {
				SelectItem[] itemArr = new SelectItem[itemList.size()];
				itemGroup.setSelectItems(itemList.toArray(itemArr));
				facDeptList.add(itemGroup);
			}
		}
		return facDeptList;
	}



	public List<OutputType> getOutputTypeList()
	{
		if (outputTypeList == null) {
			outputTypeList = new ArrayList<OutputType>();
			List<OutputType> lvOneList = outputDao.getOutputTypeList(1);
			List<OutputType> lvTwoList = outputDao.getOutputTypeList(2);
			for (OutputType o:lvOneList) {
				outputTypeList.add(o);
				List<OutputType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				outputTypeList.addAll(tmpLvTwoList);
			}
		}
		return outputTypeList;
	}
	
	public void setOutputTypeList(List<OutputType> outputTypeList)
	{
		this.outputTypeList = outputTypeList;
	}
	
	public List<String> getSelectedOutputTypes()
	{
		return selectedOutputTypes;
	}
	
	public void setSelectedOutputTypes(List<String> selectedOutputTypes)
	{
		this.selectedOutputTypes = selectedOutputTypes;
	}
	



	
	public List<CdcfRptPeriod> getCdcfPeriodList()
	{
		if (cdcfPeriodList == null) {
			List<CdcfRptPeriod> tmpCdcfPeriodList = cdcfRptDao.getCdcfRptPeriodList();
			cdcfPeriodList = tmpCdcfPeriodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return cdcfPeriodList;
	}


	
	public void setCdcfPeriodList(List<CdcfRptPeriod> cdcfPeriodList)
	{
		this.cdcfPeriodList = cdcfPeriodList;
	}

	public List<CdcfRptPeriod> getSelectedCdcfPeriodList(int i)
	{
		List<CdcfRptPeriod> resultList = new ArrayList<CdcfRptPeriod>();
		if (getCdcfPeriodList() != null) {
			boolean startCount = false;
			int count = 0;
			for (CdcfRptPeriod d:cdcfPeriodList) {
				if (d.getIs_current()) {
					startCount = true;
				}
				if (startCount && count < i) {
					resultList.add(d);
					count++;
				}
			}	
		}
		return resultList;
	}
	
	public boolean checkDateInRange(Date startDate, Date endDate, Date rptStartDate, Date rptEndDate) 
	{
		boolean result = false;
		if ((!startDate.before(rptStartDate) && !startDate.after(rptEndDate)) || 
				(!endDate.before(rptStartDate) && !endDate.after(rptEndDate)) || 
				(startDate.before(rptStartDate) && endDate.after(rptEndDate))) {
			result = true;
		}
		return result;
	}
}


