package hk.eduhk.rich.view;

import java.io.Serializable;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.Department;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtFormCPD_P;
import hk.eduhk.rich.entity.form.KtFormCntProj_P;
import hk.eduhk.rich.entity.form.KtFormCons_P;
import hk.eduhk.rich.entity.form.KtFormDetails_P;
import hk.eduhk.rich.entity.form.KtFormEA_P;
import hk.eduhk.rich.entity.form.KtFormHeader_PK;
import hk.eduhk.rich.entity.form.KtFormHeader_Q;
import hk.eduhk.rich.entity.form.KtFormIP_P;
import hk.eduhk.rich.entity.form.KtFormInn_P;
import hk.eduhk.rich.entity.form.KtFormInvAward_P;
import hk.eduhk.rich.entity.form.KtFormProfConf_P;
import hk.eduhk.rich.entity.form.KtFormProfEngmt_P;
import hk.eduhk.rich.entity.form.KtFormSem_P;
import hk.eduhk.rich.entity.form.KtFormSocEngmt_P;
import hk.eduhk.rich.entity.form.KtFormStaffEngmt_P;
import hk.eduhk.rich.entity.form.KtFormStartup_P;
import hk.eduhk.rich.entity.form.KtFormState_Q;
import hk.eduhk.rich.entity.importRI.*;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.project.ProjectDetails_P_PK;
import hk.eduhk.rich.entity.project.ProjectHeader_P;
import hk.eduhk.rich.entity.project.ProjectHeader_P_PK;
import hk.eduhk.rich.entity.project.ProjectSummary;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.staff.StaffDAO;



@SuppressWarnings("serial")
public class ImportKTAct extends BaseView
{
	private String paramPeriodId;
	private KtRptPeriod ktPeriod; 
	private String paramFacCode;
	private String paramDeptCode;
	private String paramFacDept;
	private String paramFormCode;
	private String paramStaffNo;
	private KtFormHeader_PK selectedPreview;
	private Integer selectedProjPreview;
	private Map<Integer, String> authorListMap;
	
	// KT CPD
	private List<KtFormCPD_P> ktCPDList;
	private List<KtFormCPD_P> importedKtCPDList;
	private List<KtFormCPD_P> selectedKtCPDList;
	private Map<KtFormHeader_PK, KtFormCPD_P> ktCPDMap;
	
	// KT CntProj
	private List<KtFormCntProj_P> ktCntProjList;
	private List<KtFormCntProj_P> importedKtCntProjList;
	private List<KtFormCntProj_P> selectedKtCntProjList;
	private Map<KtFormHeader_PK, KtFormCntProj_P> ktCntProjMap;
	
	// KT Cons
	private List<KtFormCons_P> ktConsList;
	private List<KtFormCons_P> importedKtConsList;
	private List<KtFormCons_P> selectedKtConsList;
	private Map<KtFormHeader_PK, KtFormCons_P> ktConsMap;
	
	// KT Inn
	private List<KtFormInn_P> ktInnList;
	private List<KtFormInn_P> importedKtInnList;
	private List<KtFormInn_P> selectedKtInnList;
	private Map<KtFormHeader_PK, KtFormInn_P> ktInnMap;
	
	// KT IP
	private List<KtFormIP_P> ktIPList;
	private List<KtFormIP_P> importedKtIPList;
	private List<KtFormIP_P> selectedKtIPList;
	private Map<KtFormHeader_PK, KtFormIP_P> ktIPMap;
	
	// KT ProfConf
	private List<KtFormProfConf_P> ktProfConfList;
	private List<KtFormProfConf_P> importedKtProfConfList;
	private List<KtFormProfConf_P> selectedKtProfConfList;
	private Map<KtFormHeader_PK, KtFormProfConf_P> ktProfConfMap;
	
	// KT ProfEngmt
	private List<KtFormProfEngmt_P> ktProfEngmtList;
	private List<KtFormProfEngmt_P> importedKtProfEngmtList;
	private List<KtFormProfEngmt_P> selectedKtProfEngmtList;
	private Map<KtFormHeader_PK, KtFormProfEngmt_P> ktProfEngmtMap;
	
	// KT Sem
	private List<KtFormSem_P> ktSemList;
	private List<KtFormSem_P> importedKtSemList;
	private List<KtFormSem_P> selectedKtSemList;
	private Map<KtFormHeader_PK, KtFormSem_P> ktSemMap;
	
	// KT SocEngmt
	private List<KtFormSocEngmt_P> ktSocEngmtList;
	private List<KtFormSocEngmt_P> importedKtSocEngmtList;
	private List<KtFormSocEngmt_P> selectedKtSocEngmtList;
	private Map<KtFormHeader_PK, KtFormSocEngmt_P> ktSocEngmtMap;
	
	// KT StaffEngmt
	private List<KtFormStaffEngmt_P> ktStaffEngmtList;
	private List<KtFormStaffEngmt_P> importedKtStaffEngmtList;
	private List<KtFormStaffEngmt_P> selectedKtStaffEngmtList;
	private Map<KtFormHeader_PK, KtFormStaffEngmt_P> ktStaffEngmtMap;
	
	// KT EA
	private List<KtFormEA_P> ktEAList;
	private List<KtFormEA_P> importedKtEAList;
	private List<KtFormEA_P> selectedKtEAList;
	private Map<KtFormHeader_PK, KtFormEA_P> ktEAMap;
	
	// KT InvAward
	private List<KtFormInvAward_P> ktInvAwardList;
	private List<KtFormInvAward_P> importedKtInvAwardList;
	private List<KtFormInvAward_P> selectedKtInvAwardList;
	private Map<KtFormHeader_PK, KtFormInvAward_P> ktInvAwardMap;
	
	// KT Startup
	private List<KtFormStartup_P> ktStartupList;
	private List<KtFormStartup_P> importedKtStartupList;
	private List<KtFormStartup_P> selectedKtStartupList;
	private Map<KtFormHeader_PK, KtFormStartup_P> ktStartupMap;
	
	// RI Project
	private List<ProjectSummary> riProjList;
	private Map<Integer, ProjectSummary> riProjMap;
	
	
	private FormDAO fDao = FormDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private KtRptDAO rDao = KtRptDAO.getInstance();
	
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	public ImportKTAct(String periodId, String facCode, String deptCode, String facDept, String formCode, String staffNo) {
		rDao = KtRptDAO.getInstance();
		paramPeriodId = periodId;
		ktPeriod = rDao.getKtRptPeriod(Integer.valueOf(periodId));
		paramFacCode = facCode;
		paramDeptCode = deptCode;
		paramFormCode = formCode;
		paramStaffNo = staffNo;
		paramFacDept = facDept;
	}
	
	
	
	public KtRptPeriod getKtPeriod()
	{
		return ktPeriod;
	}


	
	public String getParamFormCode()
	{
		return paramFormCode;
	}

	public String getParamStaffNo()
	{
		return paramStaffNo;
	}

	public void setSelectedPreview(String form_no, String data_level) {
		selectedPreview = new KtFormHeader_PK();
		selectedPreview.setForm_no(Integer.valueOf(form_no));
		selectedPreview.setData_level(data_level);
	}
	
	
	public KtFormHeader_PK getSelectedPreview()
	{
		return selectedPreview;
	}
	
	
	public void setSelectedPreview(String no, String data_level, String type)
	{
		if(type.equals("kt")) {
			selectedPreview = new KtFormHeader_PK();
			selectedPreview.setForm_no(Integer.valueOf(no));
			selectedPreview.setData_level(data_level);
		}
		else if(type.equals("riProj")) {
			selectedProjPreview = Integer.valueOf(no);;
		}
	}
	
	
	public Integer getSelectedProjPreview()
	{
		return selectedProjPreview;
	}



	public String getParamFacCode()
	{
		return paramFacCode;
	}



	
	public String getParamDeptCode()
	{
		return paramDeptCode;
	}



	public int getCollectNum() {
		if(paramFormCode.equals("KT_CPD")) {
			return selectedKtCPDList.size();
		}
		if(paramFormCode.equals("KT_CNT_PROJ")) {
			return selectedKtCntProjList.size();
		}
		if(paramFormCode.equals("KT_CONSULT")) {
			return selectedKtConsList.size();
		}
		if(paramFormCode.equals("KT_INNOVATION")) {
			return selectedKtInnList.size();
		}
		if(paramFormCode.equals("KT_IP")) {
			return selectedKtIPList.size();
		}
		if(paramFormCode.equals("KT_PROF_CONF")) {
			return selectedKtProfConfList.size();
		}
		if(paramFormCode.equals("KT_PROF_ENGMT")) {
			return selectedKtProfEngmtList.size();
		}
		if(paramFormCode.equals("KT_SEMINAR")) {
			return selectedKtSemList.size();
		}
		if(paramFormCode.equals("KT_SOC_ENGMT")) {
			return selectedKtSocEngmtList.size();
		}
		if(paramFormCode.equals("KT_STAFF_ENGMT")) {
			return selectedKtStaffEngmtList.size();
		}
		if(paramFormCode.equals("KT_EA")) {
			return selectedKtEAList.size();
		}
		if(paramFormCode.equals("KT_INV_AWARD")) {
			return selectedKtInvAwardList.size();
		}
		if(paramFormCode.equals("KT_STARTUP")) {
			return selectedKtStartupList.size();
		}
		return 0;
	}
	
	public void importKt() {
		if(paramFormCode.equals("KT_CPD")) {
			importKtCPD();
		}
		else if(paramFormCode.equals("KT_CNT_PROJ")) {
			importKtCntProj();
		}
		else if(paramFormCode.equals("KT_CONSULT")) {
			importKtCons();
		}
		else if(paramFormCode.equals("KT_INNOVATION")) {
			importKtInn();
		}
		else if(paramFormCode.equals("KT_IP")) {
			importKtIP();
		}
		else if(paramFormCode.equals("KT_PROF_CONF")) {
			importKtProfConf();
		}
		else if(paramFormCode.equals("KT_PROF_ENGMT")) {
			importKtProfEngmt();
		}
		else if(paramFormCode.equals("KT_SEMINAR")) {
			importKtSem();
		}
		else if(paramFormCode.equals("KT_SOC_ENGMT")) {
			importKtSocEngmt();
		}
		else if(paramFormCode.equals("KT_STAFF_ENGMT")) {
			importKtStaffEngmt();
		}
		if(paramFormCode.equals("KT_EA")) {
			importKtEA();
		}
		if(paramFormCode.equals("KT_INV_AWARD")) {
			importKtInvAward();
		}
		if(paramFormCode.equals("KT_STARTUP")) {
			importKtStartup();
		}
	}
	
	private void importKtQ(Integer formNo) {
		List<KtFormDetails_P> dtlList = fDao.getKtFormDetails_P(formNo, "P");
		for(KtFormDetails_P dtl : dtlList) {
			KtFormDetails_P tmp_d = dtl;
			tmp_d.getPk().setData_level("N");
			tmp_d.setUserstamp(getLoginUserId());
			tmp_d.setCreator(getLoginUserId());
			tmp_d = fDao.updateEntity(tmp_d);
		}
		KtFormHeader_Q ori_q = fDao.getKtFormHeader_Q(formNo, "M");
		KtFormHeader_Q tmp_q = ori_q;
		tmp_q.getPk().setData_level("N");
		tmp_q.setPublish_status("IMPORTED");
		tmp_q.setLast_published_by(null);
		tmp_q.setLast_published_date(null);
		tmp_q.setPublish_freq(null);
		tmp_q.setInst_display_ind(null);
		tmp_q.setInst_verified_date(null);
		tmp_q.setInst_verified_ind(null);
		tmp_q.setCdcf_status(null);
		tmp_q.setCdcf_gen_ind(null);
		tmp_q.setCdcf_gen_date(null);
		tmp_q.setCdcf_processed_ind(null);
		tmp_q.setCdcf_processed_date(null);
		tmp_q.setCdcf_selected_ind(null);
		tmp_q.setCdcf_changed_ind(null);
		tmp_q.setRemarks(null);
		tmp_q.setMajor_proj_ind(null);
		tmp_q.setBulletin_ind(null);
		tmp_q.setLast_modified_by(getLoginUserId());
		tmp_q.setLast_modified_date(Timestamp.from(Instant.now()));
		tmp_q.setUserstamp(getLoginUserId());
		tmp_q.setCreator(getLoginUserId());
		tmp_q = fDao.updateEntity(tmp_q);
	}

	
	public Map<Integer, String> getAuthorListMap()
	{
		return authorListMap;
	}

	
	public void setAuthorListMap(Map<Integer, String> authorListMap)
	{
		this.authorListMap = authorListMap;
	}

	public void createAuthorListMap(List<Integer> formNoList) {
		try {
			authorListMap = fDao.getAuthorListMap(formNoList, paramFormCode);
		}
		catch(Exception e){
			logger.log(Level.WARNING, "Cannot createAuthorListMap ", e);
		}
	}
	
	public List<String> deptListGen(){
		List<String> rtnList = new ArrayList<String>();
		if(paramFacCode != null && paramDeptCode == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			rtnList = dao.getDeptListByFac(paramFacCode);
		}
		else
			rtnList.add(paramDeptCode);
		return rtnList;
	}


	// KT CPD functions
	public List<KtFormCPD_P> getImportedKtCPDList()
	{
		if(importedKtCPDList == null) {
			try {
				importedKtCPDList = fDao.getImportedKTCPDListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTCPDListByDept ", e);
			}
			if(importedKtCPDList == null) {
				importedKtCPDList = new ArrayList<KtFormCPD_P>();
			}
		}
		return importedKtCPDList;
	}
	
	public List<KtFormCPD_P> getKtCPDList()
	{
		if(ktCPDList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktCPDList = fDao.getUnimportedPLevelKTCPDListByDept(deptList, paramFormCode);
				ktCPDList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTCPDListByDept ", e);
			}
			if(ktCPDList == null) {
				ktCPDList = new ArrayList<KtFormCPD_P>();
				ktCPDMap = new HashMap<KtFormHeader_PK, KtFormCPD_P>();
			}
			else {
				ktCPDMap = new HashMap<KtFormHeader_PK, KtFormCPD_P>();
				for(KtFormCPD_P v : ktCPDList) {
					ktCPDMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktCPDList.stream().
					map(KtFormCPD_P -> KtFormCPD_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktCPDList;
	}
	
	public Map<KtFormHeader_PK, KtFormCPD_P> getKtCPDMap()
	{
		return ktCPDMap;
	}
	
	
	public List<KtFormCPD_P> getSelectedKtCPDList()
	{
		if(selectedKtCPDList == null) selectedKtCPDList = new ArrayList<KtFormCPD_P>();
		return selectedKtCPDList;
	}



	
	public void setSelectedKtCPDList(List<KtFormCPD_P> selectedKtCPDList)
	{
		this.selectedKtCPDList = selectedKtCPDList;
	}
	
	public KtFormCPD_P getKtCPD() {
		if(getKtCPDMap() != null)
			return getKtCPDMap().get(getSelectedPreview());
		else
			return new KtFormCPD_P();
	}
	
	public void importKtCPD() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtCPDList().isEmpty()) {
			for(KtFormCPD_P form : getSelectedKtCPDList()) {
				KtFormCPD_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.Consa.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT CntProj functions
	public List<KtFormCntProj_P> getImportedKtCntProjList()
	{
		if(importedKtCntProjList == null) {
			try {
				importedKtCntProjList = fDao.getImportedKTCntProjListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTCntProjListByDept ", e);
			}
			if(importedKtCntProjList == null) {
				importedKtCntProjList = new ArrayList<KtFormCntProj_P>();
			}
		}
		return importedKtCntProjList;
	}
	
	public List<KtFormCntProj_P> getKtCntProjList()
	{
		if(ktCntProjList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktCntProjList = fDao.getUnimportedPLevelKTCntProjListByDept(deptList, paramFormCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTCntProjListByDept ", e);
			}
			if(ktCntProjList == null) {
				ktCntProjList = new ArrayList<KtFormCntProj_P>();
				ktCntProjMap = new HashMap<KtFormHeader_PK, KtFormCntProj_P>();
			}
			else {
				ktCntProjMap = new HashMap<KtFormHeader_PK, KtFormCntProj_P>();
				for(KtFormCntProj_P v : ktCntProjList) {
					ktCntProjMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktCntProjList.stream().
					map(KtFormCntProj_P -> KtFormCntProj_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktCntProjList;
	}
	
	public Map<KtFormHeader_PK, KtFormCntProj_P> getKtCntProjMap()
	{
		return ktCntProjMap;
	}
	
	
	public List<KtFormCntProj_P> getSelectedKtCntProjList()
	{
		if(selectedKtCntProjList == null) selectedKtCntProjList = new ArrayList<KtFormCntProj_P>();
		return selectedKtCntProjList;
	}



	
	public void setSelectedKtCntProjList(List<KtFormCntProj_P> selectedKtCntProjList)
	{
		this.selectedKtCntProjList = selectedKtCntProjList;
	}
	
	public KtFormCntProj_P getKtCntProj() {
		if(getKtCntProjMap() != null)
			return getKtCntProjMap().get(getSelectedPreview());
		else
			return new KtFormCntProj_P();
	}
	
	public void importKtCntProj() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtCntProjList().isEmpty()) {
			for(KtFormCntProj_P form : getSelectedKtCntProjList()) {
				KtFormCntProj_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT KtCons functions
	public List<KtFormCons_P> getImportedKtConsList()
	{
		if(importedKtConsList == null) {
			try {
				importedKtConsList = fDao.getImportedKTConsListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTConsListByDept ", e);
			}
			if(importedKtConsList == null) {
				importedKtConsList = new ArrayList<KtFormCons_P>();
			}
		}
		return importedKtConsList;
	}
	
	public List<KtFormCons_P> getKtConsList()
	{
		if(ktConsList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktConsList = fDao.getUnimportedPLevelKTConsListByDept(deptList, paramFormCode);
				ktConsList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTConsListByDept ", e);
			}
			if(ktConsList == null) {
				ktConsList = new ArrayList<KtFormCons_P>();
				ktConsMap = new HashMap<KtFormHeader_PK, KtFormCons_P>();
			}
			else {
				ktConsMap = new HashMap<KtFormHeader_PK, KtFormCons_P>();
				for(KtFormCons_P v : ktConsList) {
					ktConsMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktConsList.stream().
					map(KtFormCons_P -> KtFormCons_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktConsList;
	}
	
	public Map<KtFormHeader_PK, KtFormCons_P> getKtConsMap()
	{
		return ktConsMap;
	}
	
	
	public List<KtFormCons_P> getSelectedKtConsList()
	{
		if(selectedKtConsList == null) selectedKtConsList = new ArrayList<KtFormCons_P>();
		return selectedKtConsList;
	}



	
	public void setSelectedKtConsList(List<KtFormCons_P> selectedKtConsList)
	{
		this.selectedKtConsList = selectedKtConsList;
	}
	
	public KtFormCons_P getKtCons() {
		if(getKtConsMap() != null)
			return getKtConsMap().get(getSelectedPreview());
		else
			return new KtFormCons_P();
	}
	
	public void importKtCons() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtConsList().isEmpty()) {
			for(KtFormCons_P form : getSelectedKtConsList()) {
				KtFormCons_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT Inn functions
	public List<KtFormInn_P> getImportedKtInnList()
	{
		if(importedKtInnList == null) {
			try {
				importedKtInnList = fDao.getImportedKTInnListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTInnListByDept ", e);
			}
			if(importedKtInnList == null) {
				importedKtInnList = new ArrayList<KtFormInn_P>();
			}
		}
		return importedKtInnList;
	}
	
	public List<KtFormInn_P> getKtInnList()
	{
		if(ktInnList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktInnList = fDao.getUnimportedPLevelKTInnListByDept(deptList, paramFormCode);
				ktInnList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTInnListByDept ", e);
			}
			if(ktInnList == null) {
				ktInnList = new ArrayList<KtFormInn_P>();
				ktInnMap = new HashMap<KtFormHeader_PK, KtFormInn_P>();
			}
			else {
				ktInnMap = new HashMap<KtFormHeader_PK, KtFormInn_P>();
				for(KtFormInn_P v : ktInnList) {
					ktInnMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktInnList.stream().
					map(KtFormInn_P -> KtFormInn_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktInnList;
	}
	
	public Map<KtFormHeader_PK, KtFormInn_P> getKtInnMap()
	{
		return ktInnMap;
	}
	
	
	public List<KtFormInn_P> getSelectedKtInnList()
	{
		if(selectedKtInnList == null) selectedKtInnList = new ArrayList<KtFormInn_P>();
		return selectedKtInnList;
	}



	
	public void setSelectedKtInnList(List<KtFormInn_P> selectedKtInnList)
	{
		this.selectedKtInnList = selectedKtInnList;
	}
	
	public KtFormInn_P getKtInn() {
		if(getKtInnMap() != null)
			return getKtInnMap().get(getSelectedPreview());
		else
			return new KtFormInn_P();
	}
	
	public void importKtInn() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtInnList().isEmpty()) {
			for(KtFormInn_P form : getSelectedKtInnList()) {
				KtFormInn_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT IP functions
	public List<KtFormIP_P> getImportedKtIPList()
	{
		if(importedKtIPList == null) {
			try {
				importedKtIPList = fDao.getImportedKTIPListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTIPListByDept ", e);
			}
			if(importedKtIPList == null) {
				importedKtIPList = new ArrayList<KtFormIP_P>();
			}
		}
		return importedKtIPList;
	}
	
	public List<KtFormIP_P> getKtIPList()
	{
		if(ktIPList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktIPList = fDao.getUnimportedPLevelKTIPListByDept(deptList, paramFormCode);
				ktIPList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTIPListByDept ", e);
			}
			if(ktIPList == null) {
				ktIPList = new ArrayList<KtFormIP_P>();
				ktIPMap = new HashMap<KtFormHeader_PK, KtFormIP_P>();
			}
			else {
				ktIPMap = new HashMap<KtFormHeader_PK, KtFormIP_P>();
				for(KtFormIP_P v : ktIPList) {
					ktIPMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktIPList.stream().
					map(KtFormIP_P -> KtFormIP_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktIPList;
	}
	
	public Map<KtFormHeader_PK, KtFormIP_P> getKtIPMap()
	{
		return ktIPMap;
	}
	
	
	public List<KtFormIP_P> getSelectedKtIPList()
	{
		if(selectedKtIPList == null) selectedKtIPList = new ArrayList<KtFormIP_P>();
		return selectedKtIPList;
	}



	
	public void setSelectedKtIPList(List<KtFormIP_P> selectedKtIPList)
	{
		this.selectedKtIPList = selectedKtIPList;
	}
	
	public KtFormIP_P getKtIP() {
		if(getKtIPMap() != null)
			return getKtIPMap().get(getSelectedPreview());
		else
			return new KtFormIP_P();
	}
	
	public void importKtIP() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtIPList().isEmpty()) {
			for(KtFormIP_P form : getSelectedKtIPList()) {
				KtFormIP_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT ProfConf functions
	public List<KtFormProfConf_P> getImportedKtProfConfList()
	{
		if(importedKtProfConfList == null) {
			try {
				importedKtProfConfList = fDao.getImportedKTProfConfListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTProfConfListByDept ", e);
			}
			if(importedKtProfConfList == null) {
				importedKtProfConfList = new ArrayList<KtFormProfConf_P>();
			}
		}
		return importedKtProfConfList;
	}
	
	public List<KtFormProfConf_P> getKtProfConfList()
	{
		if(ktProfConfList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktProfConfList = fDao.getUnimportedPLevelKTProfConfListByDept(deptList, paramFormCode);
				ktProfConfList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTProfConfListByDept ", e);
			}
			if(ktProfConfList == null) {
				ktProfConfList = new ArrayList<KtFormProfConf_P>();
				ktProfConfMap = new HashMap<KtFormHeader_PK, KtFormProfConf_P>();
			}
			else {
				ktProfConfMap = new HashMap<KtFormHeader_PK, KtFormProfConf_P>();
				for(KtFormProfConf_P v : ktProfConfList) {
					ktProfConfMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktProfConfList.stream().
					map(KtFormProfConf_P -> KtFormProfConf_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktProfConfList;
	}
	
	public Map<KtFormHeader_PK, KtFormProfConf_P> getKtProfConfMap()
	{
		return ktProfConfMap;
	}
	
	
	public List<KtFormProfConf_P> getSelectedKtProfConfList()
	{
		if(selectedKtProfConfList == null) selectedKtProfConfList = new ArrayList<KtFormProfConf_P>();
		return selectedKtProfConfList;
	}



	
	public void setSelectedKtProfConfList(List<KtFormProfConf_P> selectedKtProfConfList)
	{
		this.selectedKtProfConfList = selectedKtProfConfList;
	}
	
	public KtFormProfConf_P getKtProfConf() {
		if(getKtProfConfMap() != null)
			return getKtProfConfMap().get(getSelectedPreview());
		else
			return new KtFormProfConf_P();
	}
	
	public void importKtProfConf() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtProfConfList().isEmpty()) {
			for(KtFormProfConf_P form : getSelectedKtProfConfList()) {
				KtFormProfConf_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT ProfEngmt functions
	public List<KtFormProfEngmt_P> getImportedKtProfEngmtList()
	{
		if(importedKtProfEngmtList == null) {
			try {
				importedKtProfEngmtList = fDao.getImportedKTProfEngmtListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTProfEngmtListByDept ", e);
			}
			if(importedKtProfEngmtList == null) {
				importedKtProfEngmtList = new ArrayList<KtFormProfEngmt_P>();
			}
		}
		return importedKtProfEngmtList;
	}
	
	public List<KtFormProfEngmt_P> getKtProfEngmtList()
	{
		if(ktProfEngmtList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktProfEngmtList = fDao.getUnimportedPLevelKTProfEngmtListByDept(deptList, paramFormCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTProfEngmtListByDept ", e);
			}
			if(ktProfEngmtList == null) {
				ktProfEngmtList = new ArrayList<KtFormProfEngmt_P>();
				ktProfEngmtMap = new HashMap<KtFormHeader_PK, KtFormProfEngmt_P>();
			}
			else {
				ktProfEngmtMap = new HashMap<KtFormHeader_PK, KtFormProfEngmt_P>();
				for(KtFormProfEngmt_P v : ktProfEngmtList) {
					ktProfEngmtMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktProfEngmtList.stream().
					map(KtFormProfEngmt_P -> KtFormProfEngmt_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktProfEngmtList;
	}
	
	public Map<KtFormHeader_PK, KtFormProfEngmt_P> getKtProfEngmtMap()
	{
		return ktProfEngmtMap;
	}
	
	
	public List<KtFormProfEngmt_P> getSelectedKtProfEngmtList()
	{
		if(selectedKtProfEngmtList == null) selectedKtProfEngmtList = new ArrayList<KtFormProfEngmt_P>();
		return selectedKtProfEngmtList;
	}



	
	public void setSelectedKtProfEngmtList(List<KtFormProfEngmt_P> selectedKtProfEngmtList)
	{
		this.selectedKtProfEngmtList = selectedKtProfEngmtList;
	}
	
	public KtFormProfEngmt_P getKtProfEngmt() {
		if(getKtProfEngmtMap() != null)
			return getKtProfEngmtMap().get(getSelectedPreview());
		else
			return new KtFormProfEngmt_P();
	}
	
	public void importKtProfEngmt() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtProfEngmtList().isEmpty()) {
			for(KtFormProfEngmt_P form : getSelectedKtProfEngmtList()) {
				KtFormProfEngmt_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT Sem functions
	public List<KtFormSem_P> getImportedKtSemList()
	{
		if(importedKtSemList == null) {
			try {
				importedKtSemList = fDao.getImportedKTSemListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTSemListByDept ", e);
			}
			if(importedKtSemList == null) {
				importedKtSemList = new ArrayList<KtFormSem_P>();
			}
		}
		return importedKtSemList;
	}
	
	public List<KtFormSem_P> getKtSemList()
	{
		if(ktSemList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktSemList = fDao.getUnimportedPLevelKTSemListByDept(deptList, paramFormCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTSemListByDept ", e);
			}
			if(ktSemList == null) {
				ktSemList = new ArrayList<KtFormSem_P>();
				ktSemMap = new HashMap<KtFormHeader_PK, KtFormSem_P>();
			}
			else {
				ktSemMap = new HashMap<KtFormHeader_PK, KtFormSem_P>();
				for(KtFormSem_P v : ktSemList) {
					ktSemMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktSemList.stream().
					map(KtFormSem_P -> KtFormSem_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktSemList;
	}
	
	public Map<KtFormHeader_PK, KtFormSem_P> getKtSemMap()
	{
		return ktSemMap;
	}
	
	
	public List<KtFormSem_P> getSelectedKtSemList()
	{
		if(selectedKtSemList == null) selectedKtSemList = new ArrayList<KtFormSem_P>();
		return selectedKtSemList;
	}



	
	public void setSelectedKtSemList(List<KtFormSem_P> selectedKtSemList)
	{
		this.selectedKtSemList = selectedKtSemList;
	}
	
	public KtFormSem_P getKtSem() {
		if(getKtSemMap() != null)
			return getKtSemMap().get(getSelectedPreview());
		else
			return new KtFormSem_P();
	}
	
	public void importKtSem() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtSemList().isEmpty()) {
			for(KtFormSem_P form : getSelectedKtSemList()) {
				KtFormSem_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT SocEngmt functions
	public List<KtFormSocEngmt_P> getImportedKtSocEngmtList()
	{
		if(importedKtSocEngmtList == null) {
			try {
				importedKtSocEngmtList = fDao.getImportedKTSocEngmtListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTSocEngmtListByDept ", e);
			}
			if(importedKtSocEngmtList == null) {
				importedKtSocEngmtList = new ArrayList<KtFormSocEngmt_P>();
			}
		}
		return importedKtSocEngmtList;
	}
	
	public List<KtFormSocEngmt_P> getKtSocEngmtList()
	{
		if(ktSocEngmtList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktSocEngmtList = fDao.getUnimportedPLevelKTSocEngmtListByDept(deptList, paramFormCode);
				ktSocEngmtList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTSocEngmtListByDept ", e);
			}
			if(ktSocEngmtList == null) {
				ktSocEngmtList = new ArrayList<KtFormSocEngmt_P>();
				ktSocEngmtMap = new HashMap<KtFormHeader_PK, KtFormSocEngmt_P>();
			}
			else {
				ktSocEngmtMap = new HashMap<KtFormHeader_PK, KtFormSocEngmt_P>();
				for(KtFormSocEngmt_P v : ktSocEngmtList) {
					ktSocEngmtMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktSocEngmtList.stream().
					map(KtFormSocEngmt_P -> KtFormSocEngmt_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktSocEngmtList;
	}
	
	public Map<KtFormHeader_PK, KtFormSocEngmt_P> getKtSocEngmtMap()
	{
		return ktSocEngmtMap;
	}
	
	
	public List<KtFormSocEngmt_P> getSelectedKtSocEngmtList()
	{
		if(selectedKtSocEngmtList == null) selectedKtSocEngmtList = new ArrayList<KtFormSocEngmt_P>();
		return selectedKtSocEngmtList;
	}



	
	public void setSelectedKtSocEngmtList(List<KtFormSocEngmt_P> selectedKtSocEngmtList)
	{
		this.selectedKtSocEngmtList = selectedKtSocEngmtList;
	}
	
	public KtFormSocEngmt_P getKtSocEngmt() {
		if(getKtSocEngmtMap() != null)
			return getKtSocEngmtMap().get(getSelectedPreview());
		else
			return new KtFormSocEngmt_P();
	}
	
	public void importKtSocEngmt() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtSocEngmtList().isEmpty()) {
			for(KtFormSocEngmt_P form : getSelectedKtSocEngmtList()) {
				KtFormSocEngmt_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	// KT StaffEngmt functions
	public List<KtFormStaffEngmt_P> getImportedKtStaffEngmtList()
	{
		if(importedKtStaffEngmtList == null) {
			try {
				importedKtStaffEngmtList = fDao.getImportedKTStaffEngmtListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTStaffEngmtListByDept ", e);
			}
			if(importedKtStaffEngmtList == null) {
				importedKtStaffEngmtList = new ArrayList<KtFormStaffEngmt_P>();
			}
		}
		return importedKtStaffEngmtList;
	}
	
	public List<KtFormStaffEngmt_P> getKtStaffEngmtList()
	{
		if(ktStaffEngmtList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktStaffEngmtList = fDao.getUnimportedPLevelKTStaffEngmtListByDept(deptList, paramFormCode);
				ktStaffEngmtList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTStaffEngmtListByDept ", e);
			}
			if(ktStaffEngmtList == null) {
				ktStaffEngmtList = new ArrayList<KtFormStaffEngmt_P>();
				ktStaffEngmtMap = new HashMap<KtFormHeader_PK, KtFormStaffEngmt_P>();
			}
			else {
				ktStaffEngmtMap = new HashMap<KtFormHeader_PK, KtFormStaffEngmt_P>();
				for(KtFormStaffEngmt_P v : ktStaffEngmtList) {
					ktStaffEngmtMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktStaffEngmtList.stream().
					map(KtFormStaffEngmt_P -> KtFormStaffEngmt_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktStaffEngmtList;
	}
	
	public Map<KtFormHeader_PK, KtFormStaffEngmt_P> getKtStaffEngmtMap()
	{
		return ktStaffEngmtMap;
	}
	
	
	public List<KtFormStaffEngmt_P> getSelectedKtStaffEngmtList()
	{
		if(selectedKtStaffEngmtList == null) selectedKtStaffEngmtList = new ArrayList<KtFormStaffEngmt_P>();
		return selectedKtStaffEngmtList;
	}



	
	public void setSelectedKtStaffEngmtList(List<KtFormStaffEngmt_P> selectedKtStaffEngmtList)
	{
		this.selectedKtStaffEngmtList = selectedKtStaffEngmtList;
	}
	
	public KtFormStaffEngmt_P getKtStaffEngmt() {
		if(getKtStaffEngmtMap() != null)
			return getKtStaffEngmtMap().get(getSelectedPreview());
		else
			return new KtFormStaffEngmt_P();
	}
	
	public void importKtStaffEngmt() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtStaffEngmtList().isEmpty()) {
			for(KtFormStaffEngmt_P form : getSelectedKtStaffEngmtList()) {
				KtFormStaffEngmt_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	
	// KT EA functions
	public List<KtFormEA_P> getImportedKtEAList()
	{
		if(importedKtEAList == null) {
			try {
				importedKtEAList = fDao.getImportedKTEAListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTEAListByDept ", e);
			}
			if(importedKtEAList == null) {
				importedKtEAList = new ArrayList<KtFormEA_P>();
			}
		}
		return importedKtEAList;
	}
	
	public List<KtFormEA_P> getKtEAList()
	{
		if(ktEAList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktEAList = fDao.getUnimportedPLevelKTEAListByDept(deptList, paramFormCode);
				ktEAList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTEAListByDept ", e);
			}
			if(ktEAList == null) {
				ktEAList = new ArrayList<KtFormEA_P>();
				ktEAMap = new HashMap<KtFormHeader_PK, KtFormEA_P>();
			}
			else {
				ktEAMap = new HashMap<KtFormHeader_PK, KtFormEA_P>();
				for(KtFormEA_P v : ktEAList) {
					ktEAMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktEAList.stream().
					map(KtFormEA_P -> KtFormEA_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktEAList;
	}
	
	public Map<KtFormHeader_PK, KtFormEA_P> getKtEAMap()
	{
		return ktEAMap;
	}
	
	
	public List<KtFormEA_P> getSelectedKtEAList()
	{
		if(selectedKtEAList == null) selectedKtEAList = new ArrayList<KtFormEA_P>();
		return selectedKtEAList;
	}



	
	public void setSelectedKtEAList(List<KtFormEA_P> selectedKtEAList)
	{
		this.selectedKtEAList = selectedKtEAList;
	}
	
	public KtFormEA_P getKtEA() {
		if(getKtEAMap() != null)
			return getKtEAMap().get(getSelectedPreview());
		else
			return new KtFormEA_P();
	}
	
	public void importKtEA() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtEAList().isEmpty()) {
			for(KtFormEA_P form : getSelectedKtEAList()) {
				KtFormEA_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	
	// KT InvAward functions
	public List<KtFormInvAward_P> getImportedKtInvAwardList()
	{
		if(importedKtInvAwardList == null) {
			try {
				importedKtInvAwardList = fDao.getImportedKTInvAwardListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTInvAwardListByDept ", e);
			}
			if(importedKtInvAwardList == null) {
				importedKtInvAwardList = new ArrayList<KtFormInvAward_P>();
			}
		}
		return importedKtInvAwardList;
	}
	
	public List<KtFormInvAward_P> getKtInvAwardList()
	{
		if(ktInvAwardList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktInvAwardList = fDao.getUnimportedPLevelKTInvAwardListByDept(deptList, paramFormCode);
				ktInvAwardList.removeAll(Collections.singleton(null));
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTInvAwardListByDept ", e);
			}
			if(ktInvAwardList == null) {
				ktInvAwardList = new ArrayList<KtFormInvAward_P>();
				ktInvAwardMap = new HashMap<KtFormHeader_PK, KtFormInvAward_P>();
			}
			else {
				ktInvAwardMap = new HashMap<KtFormHeader_PK, KtFormInvAward_P>();
				for(KtFormInvAward_P v : ktInvAwardList) {
					ktInvAwardMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktInvAwardList.stream().
					map(KtFormInvAward_P -> KtFormInvAward_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktInvAwardList;
	}
	
	public Map<KtFormHeader_PK, KtFormInvAward_P> getKtInvAwardMap()
	{
		return ktInvAwardMap;
	}
	
	
	public List<KtFormInvAward_P> getSelectedKtInvAwardList()
	{
		if(selectedKtInvAwardList == null) selectedKtInvAwardList = new ArrayList<KtFormInvAward_P>();
		return selectedKtInvAwardList;
	}



	
	public void setSelectedKtInvAwardList(List<KtFormInvAward_P> selectedKtInvAwardList)
	{
		this.selectedKtInvAwardList = selectedKtInvAwardList;
	}
	
	public KtFormInvAward_P getKtInvAward() {
		if(getKtInvAwardMap() != null)
			return getKtInvAwardMap().get(getSelectedPreview());
		else
			return new KtFormInvAward_P();
	}
	
	public void importKtInvAward() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtInvAwardList().isEmpty()) {
			for(KtFormInvAward_P form : getSelectedKtInvAwardList()) {
				KtFormInvAward_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	
	// KT Startup functions
	public List<KtFormStartup_P> getImportedKtStartupList()
	{
		if(importedKtStartupList == null) {
			try {
				importedKtStartupList = fDao.getImportedKTStartupListByFacAndDept(paramFacCode, paramDeptCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getImportedKTStartupListByDept ", e);
			}
			if(importedKtStartupList == null) {
				importedKtStartupList = new ArrayList<KtFormStartup_P>();
			}
		}
		return importedKtStartupList;
	}
	
	public List<KtFormStartup_P> getKtStartupList()
	{
		if(ktStartupList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				ktStartupList = fDao.getUnimportedPLevelKTStartupListByDept(deptList, paramFormCode);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTStartupListByDept ", e);
			}
			if(ktStartupList == null) {
				ktStartupList = new ArrayList<KtFormStartup_P>();
				ktStartupMap = new HashMap<KtFormHeader_PK, KtFormStartup_P>();
			}
			else {
				ktStartupMap = new HashMap<KtFormHeader_PK, KtFormStartup_P>();
				for(KtFormStartup_P v : ktStartupList) {
					ktStartupMap.put(v.getPk(), v);
				}
			}
			// create authorListMap
			List<Integer> formNoList = ktStartupList.stream().
					map(KtFormStartup_P -> KtFormStartup_P.getPk().getForm_no()).collect(Collectors.toList());
			createAuthorListMap(formNoList);
		}
		return ktStartupList;
	}
	
	public Map<KtFormHeader_PK, KtFormStartup_P> getKtStartupMap()
	{
		return ktStartupMap;
	}
	
	
	public List<KtFormStartup_P> getSelectedKtStartupList()
	{
		if(selectedKtStartupList == null) selectedKtStartupList = new ArrayList<KtFormStartup_P>();
		return selectedKtStartupList;
	}



	
	public void setSelectedKtStartupList(List<KtFormStartup_P> selectedKtStartupList)
	{
		this.selectedKtStartupList = selectedKtStartupList;
	}
	
	public KtFormStartup_P getKtStartup() {
		if(getKtStartupMap() != null)
			return getKtStartupMap().get(getSelectedPreview());
		else
			return new KtFormStartup_P();
	}
	
	public void importKtStartup() {
		//String destUrl = redirect("mangeKtForm") + "&admin=" + "Y" + "&fac=" + paramFacCode + 
		//		"&dept=" + paramDeptCode + "&form=" + paramFormCode + "&period=" + paramPeriodId;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		String redirectLink = "manageKtForm.xhtml"+ "?admin=" + "Y" + "&facDept=" + paramFacDept + 
						 "&form=" + paramFormCode + "&period=" + paramPeriodId;
    	
		if(!getSelectedKtStartupList().isEmpty()) {
			for(KtFormStartup_P form : getSelectedKtStartupList()) {
				KtFormStartup_P tmp_c = form;
				tmp_c.getPk().setData_level("N");
				tmp_c.setUserstamp(getLoginUserId());
				tmp_c.setCreator(getLoginUserId());
				tmp_c.setFac(paramFacCode);
				tmp_c.setDept(paramDeptCode);
				tmp_c = fDao.updateEntity(tmp_c);
				importKtQ(form.getPk().getForm_no());
			}
		}
		try {
			eCtx.redirect(redirectLink);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.collect.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot collect information KT Activity Form", e);
		}
	}
	
	
	// RI Proj functions
	public List<ProjectSummary> getRiProjList()
	{
		if(riProjList == null) {
			List<String> deptList = new ArrayList<String>();
			deptList.addAll(deptListGen());
			try {
				List<ProjectDetails_P> riProjDtlList = projDao.getProjectDetails_P_byStaffNo(paramStaffNo, "P");
				List<Integer> projNoList = riProjDtlList.stream().
						map(ProjectDetails_P -> ProjectDetails_P.getPk().getProject_no()).collect(Collectors.toList());
				riProjList = projDao.getProjectListByIds(projNoList);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getUnimportedPLevelKTCntProjListByDept ", e);
			}
			if(riProjList == null) {
				riProjList = new ArrayList<ProjectSummary>();
				riProjMap = new HashMap<Integer, ProjectSummary>();
			}
			else {
				riProjMap = new HashMap<Integer, ProjectSummary>();
				for(ProjectSummary v : riProjList) {
					riProjMap.put(v.getProjectNo(), v);
				}
			}
		}
		return riProjList;
	}
	
	public Map<Integer, ProjectSummary> getRiProjMap()
	{
		return riProjMap;
	}
	
	public ProjectSummary getRiProj() {
		if(getRiProjMap() != null)
			return getRiProjMap().get(getSelectedProjPreview());
		else
			return new ProjectSummary();
	}
	

}
