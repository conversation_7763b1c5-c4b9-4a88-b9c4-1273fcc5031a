package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.BarChartDataSet;
import org.primefaces.model.charts.bar.BarChartModel;
import org.primefaces.model.charts.bar.BarChartOptions;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.primefaces.model.charts.pie.PieChartOptions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.event.CloseEvent;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentDetails_P;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name = "viewPatentSumView")
@ViewScoped
@SuppressWarnings("serial")
public class ViewPatentSumView extends ViewSumView
{
	private static Logger logger = Logger.getLogger(ViewPatentSumView.class.getName());
	private BarChartModel patentYrTotalBarModel;

	private PatentDAO patentDao = PatentDAO.getInstance();
	

	@PostConstruct
    public void init() throws ParseException {
		staffDetail = getStaffDetail(getParamPid(), null, true);
    }

	public void updateChart() throws ParseException, SQLException
	{
		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		patentCount = null;
		patentCountList = null;
		
    	createPatentYrTotalBarModel();
	}
	

	
	public void createPatentYrTotalBarModel() throws ParseException, SQLException 
	{
		patentYrTotalBarModel = new BarChartModel();
        ChartData data = new ChartData();
        BarChartDataSet barDataSet = new BarChartDataSet();
        barDataSet.setLabel("Patents");
        int countTotalBar = (getRiChartPeriodList() != null)?riChartPeriodList.size():0;
        
        List<String> bgColor = new ArrayList<>();
        for (int i = 0; i < countTotalBar; i++) {
        	bgColor.add("#03a9f4");
        }
        barDataSet.setBackgroundColor(bgColor);

        data.addChartDataSet(barDataSet);

        List<String> labels = new ArrayList<>();
        List<Number> values = new ArrayList<>();
        
        if (riChartPeriodList != null) {
       	 	for (int i = 0;  i< riChartPeriodList.size(); i++) {
	       		int countYearTotal = 0;
	        	labels.add(riChartPeriodList.get(i).getChart_desc());
	        	countYearTotal += getPatentTotalCount(riChartPeriodList.get(i).getPeriod_id());
	        	values.add(countYearTotal);
            }
        }
        barDataSet.setData(values);
        data.setLabels(labels);
        patentYrTotalBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setOffset(true);
        //linearAxes.setBeginAtZero(true);
        CartesianLinearTicks ticks = new CartesianLinearTicks();
        linearAxes.setTicks(ticks);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(false);
        title.setText("Patents by Year");
        options.setTitle(title);

       Legend legend = new Legend();
        legend.setDisplay(false);
        legend.setPosition("top");
        LegendLabel legendLabels = new LegendLabel();
        legendLabels.setFontStyle("italic");
        legendLabels.setFontColor("#2980B9");
        legendLabels.setFontSize(24);
        legend.setLabels(legendLabels);
        options.setLegend(legend);

        // disable animation
        Animation animation = new Animation();
        animation.setDuration(0);
        options.setAnimation(animation);

        //patentYrTotalBarModel.setOptions(options);
        patentYrTotalBarModel.setExtender("barChartExtender");
    }

	
	public BarChartModel getPatentYrTotalBarModel()
	{
		return patentYrTotalBarModel;
	}


	
	public void setPatentYrTotalBarModel(BarChartModel patentYrTotalBarModel)
	{
		this.patentYrTotalBarModel = patentYrTotalBarModel;
	}

	public void exportSummary() 
	{
		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		patentCount = null;
		patentCountList = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();

		try 
		{
			Workbook wb = null;
    		wb = new XSSFWorkbook();
    		
    		if(CollectionUtils.isNotEmpty(getPatentCountList())) 
	    	{
	    		createDataSheet(wb);
	    	}
	    	
	    	// Get the byte array of the Workbook
	    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			eCtx.responseReset();
			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");

			DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = "DataExport-" + dateFormat.format(new Date()) + ".xlsx";		        
			
			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
		
			fCtx.responseComplete();
		}
		catch (IOException e) 
    	{
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
				
	}
		
	private void createDataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
			
		Sheet sheet = workbook.createSheet("Total no. of Patents");
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        String sheetHeader = "Total no. of Patents";
    	String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null) {
    			depts = selectedDepts.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}else {
    		String[] headerLastArray = {"Total no."};
        	headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	}

    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());
	    		
	    		if ("Y".equals(getParamAdmin())) {
	    			if (depts != null) {
	    				for (int j = 0; j < depts.length; j++) {
	    					String facDept = depts[j];
	    					Double c = patentCountList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    				}
	    			}
	    		}else {
	    			Double count = getPatentTotalCount(r.getPeriod_id());
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
	    		}

	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
}


