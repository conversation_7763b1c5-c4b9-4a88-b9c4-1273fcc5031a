package hk.eduhk.rich.view;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Logger;

import hk.eduhk.rich.entity.importRI.*;


@SuppressWarnings("serial")
public class ImportRIPatent implements Serializable
{	
	private List<ImportRIPatentV> patentList;
	private Map<ImportRIPatentV_PK, ImportRIPatentV> patentMap;
	private ImportRIPatentV_PK selectedPreview;
	private String paramPid;
	
	
	private ImportRIStatus_PK selectedIgnorePatent;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public List<ImportRIPatentV> getPatentList() {
		if(patentList == null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			patentList = dao.getNewImportRIPatentV(getParamPid());
			patentMap = new HashMap<ImportRIPatentV_PK, ImportRIPatentV>();
			if(patentList == null) {
				patentList = new ArrayList<ImportRIPatentV>();
			}
			else {
				for(ImportRIPatentV v : patentList) {
					patentMap.put(v.getPk(), v);
				}
			}
				
		}
		return patentList;
	}
	
	
	public void setPatentList(List<ImportRIPatentV> patentList)
	{
		this.patentList = patentList;
	}



	public void setSelectedPreview(String area_code, String source_id, String staff_number) {
		selectedPreview = new ImportRIPatentV_PK();
		selectedPreview.setArea_code(area_code);
		selectedPreview.setSource_id(source_id);
		selectedPreview.setStaff_number(staff_number);
	}
	
	
	public ImportRIPatentV_PK getSelectedPreview()
	{
		return selectedPreview;
	}

	public Map<ImportRIPatentV_PK, ImportRIPatentV> getPatentMap()
	{
		return patentMap;
	}
	
	
	public ImportRIPatentV getPatent() {
		if(getPatentMap() != null)
			return getPatentMap().get(getSelectedPreview());
		else
			return new ImportRIPatentV();
	}
	
	public void setSelectedIgnorePatent(String area_code, String source_id, String staff_number) {
		selectedIgnorePatent = new ImportRIStatus_PK();
		selectedIgnorePatent.setArea_code(area_code);
		selectedIgnorePatent.setSource_id(source_id);
		selectedIgnorePatent.setStaff_number(staff_number);
	}
	
	public void setSelectedIgnorePatent() {
		selectedIgnorePatent = new ImportRIStatus_PK();
		selectedIgnorePatent.setArea_code(selectedPreview.getArea_code());
		selectedIgnorePatent.setSource_id(selectedPreview.getSource_id());
		selectedIgnorePatent.setStaff_number(selectedPreview.getStaff_number());
	}
	
	
	public ImportRIStatus_PK getSelectedIgnorePatent()
	{
		return selectedIgnorePatent;
	}
		
	
	public String getParamPid()
	{
		return paramPid;
	}


	
	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}
}
