package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.PrimeFaces;

import com.google.common.base.Strings;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;
import hk.eduhk.rich.access.SecFuncLockUser;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.form.*;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.report.KtRptSum;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.report.form.*;


@ManagedBean(name = "manageKtSumView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageKtSumView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(KtForm.class.getName());
	
	private List<KtForm> formList = null;
	private List<KtRptPeriod> periodList = null;
	private List<SelectItem> facDeptList = null;
	private List<KtFormSummary> ktFormSummaryList = null;
	private List<KtFormDetails_P> formSubList = null;
	
	private KtRptPeriod period;
	private KtForm selectedForm;
	private String selectedPeriod;
	private String selectedFacDept;
	private LookupValue selectedFacDeptLkup;
	private String selectedFac;
	private String selectedDept;
	
	private Boolean canModifyKt;
	
	private String paramFormCode;
	private String paramPeriod;
	private String paramAdmin;
	
	private Date oldDate;
	private Date futureDate;
	
	private FormDAO fDao = FormDAO.getInstance();
	private KtRptDAO rDao = KtRptDAO.getInstance();
	
	private Map<String, List<KtRptSum>> allUnitSumMap = null;
	
	private ImportKTAct importKTPanel; 

	public Date getOldDate()
	{
		if (oldDate == null) {
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.YEAR, 1900);
			cal.set(Calendar.MONTH, Calendar.JANUARY);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			oldDate = cal.getTime();
		}
		return oldDate;
	}

	public void setOldDate(Date oldDate)
	{
		this.oldDate = oldDate;
	}
	
	
	
	public Date getFutureDate()
	{
		if (futureDate == null) {
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.YEAR, 2999);
			cal.set(Calendar.MONTH, Calendar.JANUARY);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			futureDate = cal.getTime();
		}
		return futureDate;
	}

	
	public void setFutureDate(Date futureDate)
	{
		this.futureDate = futureDate;
	}

	public boolean showDeleteBtn(String creatorInd, String paramNo)
	{
		boolean result = false;

		if ("Y".equals(creatorInd) && !Strings.isNullOrEmpty(paramNo)) {
			if ("C".equals(getFormDataLevel())) {
				result = true;
			}else {
				if (checkFormHasLv("C", paramNo) == false) {
					result = true;
				}
			}
		}
		
		if (getIsRdoAdmin()) {
			result = true;
		}
		if (getIsKtAdmin()) {
			result = true;
		}
		if (getIsInputKtAdmin()) {
			result = true;
		}
		return result;
	}
	
	public boolean showCopyBtn(String creatorInd)
	{
		boolean result = false;
		if ("M".equals(getFormDataLevel()) && "Y".equals(creatorInd) && getCanModifyKt()) {
			result = true;
		}
		if ("N".equals(getFormDataLevel()) && "Y".equals(getParamAdmin()) && getCanModifyKt()) {
			result = true;
		}
		return result;
	}
	
	public boolean showModifyBtn(String creatorInd)
	{
		boolean result = false;
		if ("M".equals(getFormDataLevel()) && "Y".equals(creatorInd) && getCanModifyKt()) {
			result = true;
		}
		if ("N".equals(getFormDataLevel()) && "Y".equals(getParamAdmin()) && getCanModifyKt()) {
			result = true;
		}
		return result;
	}
	
	public boolean showViewBtn(String creatorInd)
	{
		boolean result = false;
		if ("M".equals(getFormDataLevel()) && ("N".equals(creatorInd) || getCanModifyKt() == false)) {
			result = true;
		}
		if ("N".equals(getFormDataLevel()) && "Y".equals(getParamAdmin()) && getCanModifyKt() == false) {
			result = true;
		}
		return result;
	}
	
	public String getParamFormCode()
	{
		return paramFormCode;
	}

	
	public void setParamFormCode(String paramFormCode)
	{
		this.paramFormCode = paramFormCode;
	}

	
	
	public String getParamPeriod()
	{
		return paramPeriod;
	}


	
	public void setParamPeriod(String paramPeriod)
	{
		this.paramPeriod = paramPeriod;
	}
	
	
	
	public String getParamAdmin()
	{
		return paramAdmin;
	}


	
	public void setParamAdmin(String paramAdmin)
	{
		this.paramAdmin = paramAdmin;
	}

	public Boolean getCanModifyKt()
	{
		if (canModifyKt == null) {
			canModifyKt = true;
			SecFuncLock selectedSecFuncLock = null;
			AccessDAO dao = AccessDAO.getInstance();
			if (!Strings.isNullOrEmpty(getSelectedFacDept())) {
				selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_KT", "MANAGE_KT_ACT", getSelectedFacDept());
			}
			if (selectedSecFuncLock != null) {
				//Y = Is Exclusive, N = Not Exclusive
				if ("Y".equals(selectedSecFuncLock.getLock_status())) {
					//Check user is extended access user
					SecFuncLockUser user = dao.getSecFuncLockUser("EXCLUSIVE_KT", "MANAGE_KT_ACT", getCurrentUserId());
					if (user != null) {
						//N = Not Extended Access User
						if ("N".equals(user.getLock_status())) {
							canModifyKt = false;
						}else {
							canModifyKt = true;
						}
					}else {
						canModifyKt = false;
					}
				}
			}
		}
		if (!"Y".equals(getParamAdmin())) {
			canModifyKt = true;
		}
		//check user is rdo admin
		if (getIsRdoAdmin()) {
			canModifyKt = true;
		}
		if (!canModifyKt) {
			PrimeFaces.current().executeScript("disable('editForm');");
		}
		return canModifyKt;
	}

	
	public void setCanModifyKt(Boolean canModifyKt)
	{
		this.canModifyKt = canModifyKt;
	}
	
	public List<KtForm> getFormList()
	{
		if (formList == null) {
			formList = fDao.getKtFormListWithCon(true);
			summary();
		}
		return formList;
	}

	public void setFormList(List<KtForm> formList)
	{
		this.formList = formList;
	}

	public List<KtRptPeriod> getPeriodList()
	{
		if (periodList == null) {
			periodList = rDao.getKtRptPeriodList();
		}
		return periodList;
	}
	
	public void setPeriodList(List<KtRptPeriod> periodList)
	{
		this.periodList = periodList;
	}
	
	public List<SelectItem> getFacDeptList()
	{
		if(facDeptList == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<LookupValue> l2List = dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			l2List.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L3", "US", "Y"));
			List<String> accessList = getKtAdminDeptList();
			facDeptList = new ArrayList<SelectItem>();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			for(LookupValue lkVal1 : l1List) {
				SelectItemGroup itemGroup = new SelectItemGroup(
						lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")");
				List<SelectItem> itemList = new ArrayList<SelectItem>();
				if(haveAll || accessList.contains(lkVal1.getPk().getLookup_code())) {
					itemList.add(new SelectItem(lkVal1.getPk().getLookup_code(), 
							lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")"));
				}
				for(LookupValue lkVal2 : l2List) {
					if(haveAll || 
							(lkVal2.getParent_lookup_code().equals(lkVal1.getPk().getLookup_code()) &&
							accessList.contains(lkVal2.getPk().getLookup_code()))) {
						itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
								lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
					}
				}
				if(!itemList.isEmpty()) {
					SelectItem[] itemArr = new SelectItem[itemList.size()];
					itemGroup.setSelectItems(itemList.toArray(itemArr));
					facDeptList.add(itemGroup);
				}
			}
			SelectItemGroup itemGroup = new SelectItemGroup("Other");
			List<SelectItem> itemList = new ArrayList<SelectItem>();
			for(LookupValue lkVal2 : l2List) {
				if(haveAll ||
						(lkVal2.getParent_lookup_code().equals(null) &&
						accessList.contains(lkVal2.getPk().getLookup_code()))) {
					itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
							lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
				}
			}
			if(!itemList.isEmpty()) {
				SelectItem[] itemArr = new SelectItem[itemList.size()];
				itemGroup.setSelectItems(itemList.toArray(itemArr));
				facDeptList.add(itemGroup);
			}
		}
		return facDeptList;
	}

	public boolean checkDateInRange(Date startDate, Date endDate, Date rptStartDate, Date rptEndDate) 
	{
		boolean result = false;
		if ((!startDate.before(rptStartDate) && !startDate.after(rptEndDate)) || 
				(!endDate.before(rptStartDate) && !endDate.after(rptEndDate)) || 
				(startDate.before(rptStartDate) && endDate.after(rptEndDate))) {
			result = true;
		}
		return result;
	}
		
	
	public List<KtFormDetails_P> getFormSubList() {
	    if (formSubList == null && !Strings.isNullOrEmpty(getParamFormCode()) && getPeriod() != null) {
	        // Get form list by form code, staff no, data level
	        formSubList = fDao.getKtFormSubList(
	            paramFormCode, 
	            getSumStaffNo(), 
	            getFormDataLevel(), 
	            period.getDate_from(), 
	            period.getDate_to(), 
	            period.getPeriod_id()
	        );
	        
	        // Ensure formSubList is not null
	        if (formSubList == null) {
	            formSubList = new ArrayList<>();
	        }

	        // If admin
	        if ("Y".equals(getParamAdmin())) {
	            // Get form list by form code, fac, dept, data level
	            getSelectedFacDeptLkup();
	            ktFormSummaryList = fDao.getKtFormSummaryListByFacDept(
	                getSelectedFac(), 
	                getSelectedDept(), 
	                getFormDataLevel(), 
	                paramFormCode, 
	                period.getDate_from(), 
	                period.getDate_to(), 
	                period.getPeriod_id()
	            );
	            
	            // Initialize ktFormSummaryList if null
	            if (ktFormSummaryList == null) {
	                ktFormSummaryList = new ArrayList<>();
	            }

	            Map<String, List<Integer>> formNumberMap = formNumberMapFunction(ktFormSummaryList);
	            if (formNumberMap == null) {
	                formNumberMap = new HashMap<>();
	            }

	            List<Integer> formCodeNumList = formNumberMap.getOrDefault(paramFormCode, new ArrayList<>());
	            List<Integer> sumlistDistinct = formCodeNumList.stream().distinct().collect(Collectors.toList());
	            if (sumlistDistinct == null) {
	                sumlistDistinct = new ArrayList<>();
	            }

	            List<Integer> formlistDistinct = new ArrayList<>();
	            for (KtFormDetails_P d : formSubList) {
	                if (d != null && d.getPk() != null) {
	                    formlistDistinct.add(d.getPk().getForm_no());
	                }
	            }
	            formlistDistinct = formlistDistinct.stream().distinct().collect(Collectors.toList());
	            sumlistDistinct.removeAll(formlistDistinct);

	            // Add KtFormDetails_P if formlistDistinct not in sumlistDistinct
	            for (int i = 0; i < sumlistDistinct.size(); i++) {
					KtFormDetails_P tmp = new KtFormDetails_P();
					tmp.getPk().setForm_no(sumlistDistinct.get(i));
					tmp.getPk().setData_level(getFormDataLevel());
					tmp.getPk().setLine_no(1);
					tmp.setKtFormState_q(fDao.getKtFormState_Q(tmp.getPk().getForm_no()));
					tmp.setKtFormHeader_q(fDao.getKtFormHeader_Q(tmp.getPk().getForm_no(), getFormDataLevel()));
					formSubList.add(tmp);
				}
	        }

	        // Setup start and end date
	        for (KtFormDetails_P d : formSubList) {
	            if (d != null) {
	                d = setupStartEndDate(paramFormCode, d);
	            }
	        }

	        // Filter based on form code
	        if (paramFormCode != null) {
	            switch (paramFormCode) {
	                case SysParam.PARAM_KT_FORM_CPD:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormCPD_p() != null && 
	                                checkFacDeptMatch(d.getKtFormCPD_p().getFac(), d.getKtFormCPD_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_PROF_CONF:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormProfConf_p() != null && 
	                                checkFacDeptMatch(d.getKtFormProfConf_p().getFac(), d.getKtFormProfConf_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_SEM:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormSem_p() != null && 
	                                checkFacDeptMatch(d.getKtFormSem_p().getFac(), d.getKtFormSem_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_CNT_PROJ:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormCntProj_p() != null && 
	                                checkFacDeptMatch(d.getKtFormCntProj_p().getFac(), d.getKtFormCntProj_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_INN:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormInn_p() != null && 
	                                checkFacDeptMatch(d.getKtFormInn_p().getFac(), d.getKtFormInn_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_CONS:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormCons_p() != null && 
	                                checkFacDeptMatch(d.getKtFormCons_p().getFac(), d.getKtFormCons_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_PROF_ENGMT:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormProfEngmt_p() != null && 
	                                checkFacDeptMatch(d.getKtFormProfEngmt_p().getFac(), d.getKtFormProfEngmt_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_IP:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormIP_p() != null && 
	                                checkFacDeptMatch(d.getKtFormIP_p().getFac(), d.getKtFormIP_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_SOC_ENGMT:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormSocEngmt_p() != null && 
	                                d.getKtFormSocEngmt_p().getFac() != null && 
	                                d.getKtFormSocEngmt_p().getDept() != null && 
	                                checkFacDeptMatch(d.getKtFormSocEngmt_p().getFac(), d.getKtFormSocEngmt_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormStaffEngmt_p() != null && 
	                                checkFacDeptMatch(d.getKtFormStaffEngmt_p().getFac(), d.getKtFormStaffEngmt_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_EA:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormEA_p() != null && 
	                                checkFacDeptMatch(d.getKtFormEA_p().getFac(), d.getKtFormEA_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_STARTUP:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormStartup_p() != null && 
	                                checkFacDeptMatch(d.getKtFormStartup_p().getFac(), d.getKtFormStartup_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	                case SysParam.PARAM_KT_FORM_INV_AWARD:
	                    if ("N".equals(getFormDataLevel())) {
	                        formSubList = formSubList.stream()
	                            .filter(d -> d != null && d.getKtFormInvAward_p() != null && 
	                                checkFacDeptMatch(d.getKtFormInvAward_p().getFac(), d.getKtFormInvAward_p().getDept()))
	                            .collect(Collectors.toList());
	                    }
	                    break;
	            }
	        }

	        // Ensure unique entries
	        if (formSubList != null) {
	            List<KtFormDetails_P> tmpList = new ArrayList<>();
	            HashSet<Integer> unique = new HashSet<>();
	            for (KtFormDetails_P f : formSubList) {
	                if (f != null && f.getPk() != null && unique.add(f.getPk().getForm_no())) {
	                    tmpList.add(f);
	                }
	            }
	            formSubList = tmpList;
	        } else {
	            formSubList = new ArrayList<>();
	        }
	    }
	    return formSubList != null ? formSubList : new ArrayList<>();
	}


	
	public void setFormSubList(List<KtFormDetails_P> formSubList)
	{
		this.formSubList = formSubList;
	}


	public KtForm getSelectedForm()
	{
		if (selectedForm == null) {
			if (!Strings.isNullOrEmpty(getParamFormCode())) {
				selectedForm = fDao.getKtForm(paramFormCode);
			}
		}
		return selectedForm;
	}

	public void setSelectedForm(KtForm selectedForm)
	{
		this.selectedForm = selectedForm;
	}

	
	public String getSelectedPeriod()
	{
		if (selectedPeriod == null) {
			selectedPeriod = getParamPeriod();
			if (selectedPeriod == null) {
				KtRptPeriod pObj = rDao.getCurrentKtRptPeriod();
				if (pObj != null) {
					selectedPeriod = String.valueOf(pObj.getPeriod_id());
				}
			}
		}
		return selectedPeriod;
	}


	
	public void setSelectedPeriod(String selectedPeriod)
	{
		this.selectedPeriod = selectedPeriod;
		importKTPanel = null;
	}
	
	
	public String getSelectedFacDept()
	{
		if(selectedFacDept != null) {
			List<String> accessList = getKtAdminDeptList();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			if(!accessList.contains(selectedFacDept) || haveAll) selectedFacDept = null;
		}
		if(selectedFacDept == null && getFacDeptList() != null && !getFacDeptList().isEmpty()) {
			SelectItemGroup firstLayer = (SelectItemGroup) getFacDeptList().get(0);
			selectedFacDept = firstLayer.getSelectItems()[0].getValue().toString();
		}
		return selectedFacDept;
	}

	
	public void setSelectedFacDept(String selectedFacDept)
	{
		this.selectedFacDept = selectedFacDept;
		importKTPanel = null;
		selectedFacDeptLkup = null;
		
	}
	
	public LookupValue getSelectedFacDeptLkup()
	{
		if(selectedFacDeptLkup == null && getSelectedFacDept() != null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			selectedFacDeptLkup = dao.getLookupValue("ORGANIZATION_UNIT_L1", getSelectedFacDept(), "US");
			if(selectedFacDeptLkup == null) 
				selectedFacDeptLkup = dao.getLookupValue("ORGANIZATION_UNIT_L2", getSelectedFacDept(), "US");
			
			if(selectedFacDeptLkup.getPk().getLookup_type().equals("ORGANIZATION_UNIT_L1")) {
				selectedFac = getSelectedFacDeptLkup().getPk().getLookup_code();
			}
			else {
				selectedFac = getSelectedFacDeptLkup().getParent_lookup_code();
				selectedDept = getSelectedFacDeptLkup().getPk().getLookup_code();
			}
		}
		return selectedFacDeptLkup;
	}

	
	public void setSelectedFacDeptLkup(LookupValue selectedFacDeptLkup)
	{
		this.selectedFacDeptLkup = selectedFacDeptLkup;
	}

	
	public String getSelectedFac()
	{
		return selectedFac;
	}

	
	public void setSelectedFac(String selectedFac)
	{
		this.selectedFac = selectedFac;
	}

	
	public String getSelectedDept()
	{
		return selectedDept;
	}

	
	public void setSelectedDept(String selectedDept)
	{
		this.selectedDept = selectedDept;
	}

	public String updateForm()  
	{
		if (selectedForm != null) {
			FacesContext fCtx = FacesContext.getCurrentInstance();
			if (validFormData()) {
				boolean isNew = (selectedForm.getCreationDate() == null);
				try
				{
					selectedForm.setUserstamp(getLoginUserId());
					selectedForm = fDao.updateKtForm(selectedForm);

					// Success message
					String message = (isNew) ? "msg.success.create.x" : "msg.success.update.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
				}
				catch (OptimisticLockException ole)
				{
					String message = getResourceBundle().getString("msg.err.optimistic.lock");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
					return "";
				}
			}else {
				return "";
			}
		}
		return redirect("ktActFormList");
	}
	
	private boolean validFormData()
	{
		boolean result = true;
		return result;
	}


	public String deleteForm(String formCode)  
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (!Strings.isNullOrEmpty(formCode)) {
			try
			{
				fDao.deleteKtForm(formCode);
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
		}else {
			// Failed message
			String message = "msg.err.not.exist";
			message = MessageFormat.format(getResourceBundle().getString(message), "Selected KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}	
		return redirect("ktActFormList");
	}
	
	public Boolean canViewKtActList() 
	{
		Boolean result = false;
		//if in admin page
		if ("Y".equals(getParamAdmin())) {
			//if is kt admin
			if (getIsKtAdmin() || getIsInputKtAdmin()) {
				List<String> accessList = getKtAdminDeptList();
				String d = getSumDept();
				if (accessList != null & !Strings.isNullOrEmpty(d)) {
					if (accessList.contains(d)) {
						result = true;
					}
				}
			}
			//if is rdo admin
			if (getIsRdoAdmin()) {
				result = true;
			}
		}else {
			if (staffDetail == null) {
				if (getIsAsst() || getIsKtAdmin() || getIsRdoAdmin()) {
					staffDetail = getStaffDetail(getParamPid(), null, true);
				}else {
					staffDetail = getStaffDetail(null, null, false);
				}
			}
			if (staffDetail != null) {
				if (Strings.isNullOrEmpty(paramPid))
					paramPid = String.valueOf(staffDetail.getPid());
				if (getIsAsst() || getIsKtAdmin() || getIsRdoAdmin() || getCurrentUserId().equals(staffDetail.getCn())){
						result = true;
				}
			}
		}
		
		return result;
	}
	
	
	public KtRptPeriod getPeriod()
	{
		if (!Strings.isNullOrEmpty(selectedPeriod)) {
			period = rDao.getKtRptPeriod(Integer.valueOf(selectedPeriod));
		}
		return period;
	}


	
	public void setPeriod(KtRptPeriod period)
	{
		this.period = period;
	}
	public String getFormDataLevel()
	{
		String dataLevel = "M";
		if ("Y".equals(getParamAdmin())) {
			dataLevel = "N";
		}
		return dataLevel;
	}
	
	public String getSumDataLevel()
	{
		String dataLevel = "P";
		if ("Y".equals(getParamAdmin())) {
			dataLevel = "D";
		}
		return dataLevel;
	}
	
	public String getSumStaffNo()
	{
		String staffNo = null;
		if (staffDetail != null) {
			if("D".equals(getSumDataLevel())) {
				staffNo = null;
			}else {
				staffNo = staffDetail.getStaff_number();
			}
		}
		return staffNo;
	}
	
	public String getSumDept()
	{
		String dept = "";
		if (getSumStaffNo() == null) {
			if (!Strings.isNullOrEmpty(getSelectedFacDept())) {
				dept = selectedFacDept;
			}
		}
		return dept;
	}
	
	public void count(String formCode, String countType)
	{
		for (KtForm f:formList) {
			if (formCode.equals(f.getForm_code())) {
				if ("total".equals(countType)) {
					f.setCountTotal(f.getCountTotal()+1);
				}
				if ("waitConsent".equals(countType)) {
					f.setCountWaitConsent(f.getCountWaitConsent()+1);
				}
			}
		}
	}
	
	public boolean checkFacDeptMatch(String fac, String dept)
	{
		boolean match = false;
		if (!Strings.isNullOrEmpty(fac)) {
			if (getSumDept().equals(fac)) {
				match = true;
			}
		}
		if (!Strings.isNullOrEmpty(dept)) {
			if (getSumDept().equals(dept)) {
				match = true;
			}
		}
		return  match;	
	}
	
	public void summary()
	{
		if (getFormList() != null && getPeriod() != null) {
			getOldDate();
			getFutureDate();
			HashSet unique=new HashSet();
			getSelectedFacDeptLkup();
			List<KtFormSummary> ktFormSummaryListByFormCode = null;
			if ("Y".equals(getParamAdmin())) {
				ktFormSummaryList = fDao.getKtFormSummaryListByFacDept(getSelectedFac(), getSelectedDept(), getSumDataLevel(), null, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
			}else {
				ktFormSummaryList = fDao.getKtFormSummaryListByStaff(getSumStaffNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id(), null);
			}
			
			for (KtForm f:formList) {
				switch(f.getForm_code()) {
					//A-8
					case SysParam.PARAM_KT_FORM_CPD:
						List<KtFormCPD_P> cpd_headerList = new ArrayList<KtFormCPD_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormCPD_P cpd = fDao.getKtFormCPD_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (cpd != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										cpd_headerList.add(cpd);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(cpd.getFac(), cpd.getDept()) && unique.add(d.getPk().getFormNo())) {
										cpd_headerList.add(cpd);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cpd_headerList.size()));
									break;
								case "income_total":
									Double income_total = cpd_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cpd_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
								case "num_hr":
									Integer num_hr = cpd_headerList.stream().filter(a -> a.getNum_stu_contact_hr() != null).mapToInt(a -> a.getNum_stu_contact_hr()).sum();
									s.setSum_value(Double.valueOf(num_hr));
									break;
							}	
						}
						break;
					//A-9
					case SysParam.PARAM_KT_FORM_PROF_CONF:
						List<KtFormProfConf_P> prof_conf_headerList = new ArrayList<KtFormProfConf_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormProfConf_P profConf = fDao.getKtFormProfConf_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (profConf != null) {

									if (!Strings.isNullOrEmpty(getSumStaffNo())) {
										//P Level by staff no
										KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
										if ("Y".equals(details_q.getConsent_ind())) {
											prof_conf_headerList.add(profConf);
										}
										count(f.getForm_code(), "total");
										if ("U".equals(details_q.getConsent_ind())){
											count(f.getForm_code(), "waitConsent");
										}
									}else {
										//D Level by dept
										if (checkFacDeptMatch(profConf.getFac(), profConf.getDept()) && unique.add(d.getPk().getFormNo())) {
											prof_conf_headerList.add(profConf);
											count(f.getForm_code(), "total");
										}
									}
								}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(prof_conf_headerList.size()));
									break;
								case "income_total":
									Double income_total = prof_conf_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = prof_conf_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
								case "num_present":
									Integer num_present = prof_conf_headerList.stream().filter(a -> a.getNum_presentation() != null).mapToInt(a -> a.getNum_presentation()).sum();
									s.setSum_value(Double.valueOf(num_present));
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_SEM:
						List<KtFormSem_P> sem_headerList = new ArrayList<KtFormSem_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormSem_P sem = fDao.getKtFormSem_P(d.getPk().getFormNo(), getSumDataLevel(), null, null, 0);
							if (sem != null) {
									if (!Strings.isNullOrEmpty(getSumStaffNo())) {
										//P Level by staff no
										KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
										if ("Y".equals(details_q.getConsent_ind())) {
											sem_headerList.add(sem);
										}
										count(f.getForm_code(), "total");
										if ("U".equals(details_q.getConsent_ind())){
											count(f.getForm_code(), "waitConsent");
										}
									}else {
										//D Level by dept
										if (checkFacDeptMatch(sem.getFac(), sem.getDept()) && unique.add(d.getPk().getFormNo())) {
											sem_headerList.add(sem);
											count(f.getForm_code(), "total");
										}
									
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(sem_headerList.size()));
									break;
								case "income_total":
									Double income_total = sem_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = sem_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_CNT_PROJ:
						List<KtFormCntProj_P> cnt_proj_headerList = new ArrayList<KtFormCntProj_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormCntProj_P cntProj = fDao.getKtFormCntProj_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (cntProj != null) {
									if (!Strings.isNullOrEmpty(getSumStaffNo())) {
										//P Level by staff no
										KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
										if ("Y".equals(details_q.getConsent_ind())) {
											cnt_proj_headerList.add(cntProj);
										}
										count(f.getForm_code(), "total");
										if ("U".equals(details_q.getConsent_ind())){
											count(f.getForm_code(), "waitConsent");
										}
									}else {
										//D Level by dept
										if (checkFacDeptMatch(cntProj.getFac(), cntProj.getDept()) && unique.add(d.getPk().getFormNo())) {
											cnt_proj_headerList.add(cntProj);
											count(f.getForm_code(), "total");
										}
									}
								
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cnt_proj_headerList.size()));
									break;
								case "income_total":
									Double income_total = cnt_proj_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cnt_proj_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_INN:
						List<KtFormInn_P> inn_headerList = new ArrayList<KtFormInn_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormInn_P inn = fDao.getKtFormInn_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (inn != null) {
									if (!Strings.isNullOrEmpty(getSumStaffNo())) {
										//P Level by staff no
										KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
										if ("Y".equals(details_q.getConsent_ind())) {
											inn_headerList.add(inn);
										}
										count(f.getForm_code(), "total");
										if ("U".equals(details_q.getConsent_ind())){
											count(f.getForm_code(), "waitConsent");
										}
									}else {
										//D Level by dept
										if (checkFacDeptMatch(inn.getFac(), inn.getDept()) && unique.add(d.getPk().getFormNo())) {
											inn_headerList.add(inn);
											count(f.getForm_code(), "total");
										}
									}
								
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(inn_headerList.size()));
									break;
								case "income_total":
									Double income_total = inn_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = inn_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_CONS:
						List<KtFormCons_P> cons_headerList = new ArrayList<KtFormCons_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormCons_P cons = fDao.getKtFormCons_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (cons != null) {						
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										cons_headerList.add(cons);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(cons.getFac(), cons.getDept()) && unique.add(d.getPk().getFormNo())) {
										cons_headerList.add(cons);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cons_headerList.size()));
									break;
								case "income_total":
									Double income_total = cons_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cons_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_PROF_ENGMT:
						List<KtFormProfEngmt_P> prof_engmt_headerList = new ArrayList<KtFormProfEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormProfEngmt_P profEngmt = fDao.getKtFormProfEngmt_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (profEngmt != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										prof_engmt_headerList.add(profEngmt);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(profEngmt.getFac(), profEngmt.getDept()) && unique.add(d.getPk().getFormNo())) {
										prof_engmt_headerList.add(profEngmt);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(prof_engmt_headerList.size()));
									break;
								case "num_teacher":
									Integer num_teacher = prof_engmt_headerList.stream().filter(a -> a.getNum_teacher() != null).mapToInt(a -> a.getNum_teacher()).sum();
									s.setSum_value(Double.valueOf(num_teacher));
									break;
								case "num_principal":
									Integer num_principal = prof_engmt_headerList.stream().filter(a -> a.getNum_principal() != null).mapToInt(a -> a.getNum_principal()).sum();
									s.setSum_value(Double.valueOf(num_principal));
									break;
								case "num_other":
									Integer num_other = prof_engmt_headerList.stream().filter(a -> a.getNum_other() != null).mapToInt(a -> a.getNum_other()).sum();
									s.setSum_value(Double.valueOf(num_other));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_IP:
						List<KtFormIP_P> ip_headerList = new ArrayList<KtFormIP_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormIP_P ip = fDao.getKtFormIP_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (ip != null) {
									if (!Strings.isNullOrEmpty(getSumStaffNo())) {
										//P Level by staff no
										KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
										if ("Y".equals(details_q.getConsent_ind())) {
											ip_headerList.add(ip);
										}
										count(f.getForm_code(), "total");
										if ("U".equals(details_q.getConsent_ind())){
											count(f.getForm_code(), "waitConsent");
										}
									}else {
										//D Level by dept
										if (checkFacDeptMatch(ip.getFac(), ip.getDept()) && unique.add(d.getPk().getFormNo())) {
											ip_headerList.add(ip);
											count(f.getForm_code(), "total");
										}
									}
								}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(ip_headerList.size()));
									break;
								case "income_total":
									Double income_total = ip_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_SOC_ENGMT:
						List<KtFormSocEngmt_P> soc_engmt_headerList = new ArrayList<KtFormSocEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormSocEngmt_P socEngmt = fDao.getKtFormSocEngmt_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (socEngmt != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										soc_engmt_headerList.add(socEngmt);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(socEngmt.getFac(), socEngmt.getDept()) && unique.add(d.getPk().getFormNo())) {
										soc_engmt_headerList.add(socEngmt);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_attendee":
									Integer num_attendee = soc_engmt_headerList.stream().filter(a -> a.getNum_attendee() != null).mapToInt(a -> a.getNum_attendee()).sum();
									s.setSum_value(Double.valueOf(num_attendee));
									break;
								case "num_perform":
									Long num_perform = soc_engmt_headerList.stream().filter(a -> "Y".equals(a.getPerform())).count();
									s.setSum_value(Double.valueOf(num_perform));
									break;
								case "num_total":
									s.setSum_value(Double.valueOf(soc_engmt_headerList.size()));	
									break;
								case "num_key":
									Integer num_key = soc_engmt_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
						List<KtFormStaffEngmt_P> staff_engmt_headerList = new ArrayList<KtFormStaffEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormStaffEngmt_P staffEngmt = fDao.getKtFormStaffEngmt_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (staffEngmt != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										staff_engmt_headerList.add(staffEngmt);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(staffEngmt.getFac(), staffEngmt.getDept()) && unique.add(d.getPk().getFormNo())) {
										staff_engmt_headerList.add(staffEngmt);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(staff_engmt_headerList.size()));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_EA:
						List<KtFormEA_P> ea_headerList = new ArrayList<KtFormEA_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormEA_P ea = fDao.getKtFormEA_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (ea != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										ea_headerList.add(ea);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(ea.getFac(), ea.getDept()) && unique.add(d.getPk().getFormNo())) {
										ea_headerList.add(ea);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(ea_headerList.size()));
									break;
								case "num_team":
									s.setSum_value(null);
									break;
								case "num_stu":
									Integer num_pg_stu = ea_headerList.stream()
								    .filter(a -> a.getNum_stu_pg() != null)
								    .mapToInt(a -> a.getNum_stu_pg())
								    .sum();
								    
									Integer num_ug_stu = ea_headerList.stream()
									    .filter(a -> a.getNum_stu_ug() != null)
									    .mapToInt(a -> a.getNum_stu_ug())
									    .sum();
	
									// Handle potential null values (if no elements matched the filter, sum will be 0)
									int pg = num_pg_stu != null ? num_pg_stu : 0;
									int ug = num_ug_stu != null ? num_ug_stu : 0;
	
									int num_stu = pg + ug;
									s.setSum_value((double) num_stu);
									break;
								case "num_alumni":
									Integer num_alumni = ea_headerList.stream().filter(a -> a.getNum_alumni() != null).mapToInt(a -> a.getNum_alumni()).sum();
									s.setSum_value(Double.valueOf(num_alumni));
									break;
								case "num_staff":
									Integer num_staff = ea_headerList.stream().filter(a -> a.getNum_staff() != null).mapToInt(a -> a.getNum_staff()).sum();
									s.setSum_value(Double.valueOf(num_staff));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_STARTUP:
						List<KtFormStartup_P> startup_headerList = new ArrayList<KtFormStartup_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormStartup_P startup = fDao.getKtFormStartup_P(d.getPk().getFormNo(), getSumDataLevel(), null, null, 0);
							if (startup != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										startup_headerList.add(startup);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(startup.getFac(), startup.getDept()) && unique.add(d.getPk().getFormNo())) {
										startup_headerList.add(startup);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_INV_AWARD:
						List<KtFormInvAward_P> inv_award_headerList = new ArrayList<KtFormInvAward_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) {
							KtFormInvAward_P invAward = fDao.getKtFormInvAward_P(d.getPk().getFormNo(), getSumDataLevel(), period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							if (invAward != null) {
								if (!Strings.isNullOrEmpty(getSumStaffNo())) {
									//P Level by staff no
									KtFormDetails_Q details_q = fDao.getKtFormDetails_Q(d.getPk().getFormNo(), getSumStaffNo());
									if ("Y".equals(details_q.getConsent_ind())) {
										inv_award_headerList.add(invAward);
									}
									count(f.getForm_code(), "total");
									if ("U".equals(details_q.getConsent_ind())){
										count(f.getForm_code(), "waitConsent");
									}
								}else {
									//D Level by dept
									if (checkFacDeptMatch(invAward.getFac(), invAward.getDept()) && unique.add(d.getPk().getFormNo())) {
										inv_award_headerList.add(invAward);
										count(f.getForm_code(), "total");
									}
								}
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(inv_award_headerList.size()));
									break;
								case "num_filed":
									Integer num_filed = inv_award_headerList.stream().filter(a -> a.getNum_pat_filed() != null).mapToInt(a -> a.getNum_pat_filed()).sum();
									s.setSum_value(Double.valueOf(num_filed));
									break;
								case "num_granted":
									Integer num_granted = inv_award_headerList.stream().filter(a -> a.getNum_pat_granted() != null).mapToInt(a -> a.getNum_pat_granted()).sum();
									s.setSum_value(Double.valueOf(num_granted));
									break;	
							}
						}
						break;
					case "":
						break;
				}
			}
		}
	}
	
	public Date setupStartDateByDate(Date value) 
	{
		if (value == null) {
			value = getOldDate();
		}
		return value;
	}
	
	public Date setupEndDateByDate(Date value) 
	{
		if (value == null) {
			value = getFutureDate();
		}
		return value;
	}
	
	public KtFormDetails_P setupStartEndDate(String form_code, KtFormDetails_P obj) {
	    // Check if obj or form_code is null
	    if (obj == null || form_code == null) {
	        return obj; // Return early to avoid null pointer exceptions
	    }

	    // Check if fDao is null
	    if (fDao == null) {
	        return obj; // Return early if data access object is null
	    }

	    // Store results of date methods and check for null
	    Date oldDate = getOldDate();
	    if (oldDate == null) {
	        return obj; // Return early if oldDate is null
	    }

	    // Check if primary key is null
	    if (obj.getPk() == null) {
	        return obj; // Return early if primary key is null
	    }

	    switch (form_code) {
	        case SysParam.PARAM_KT_FORM_CPD:
	            if (obj.getKtFormCPD_p() == null) {
	                obj.setKtFormCPD_p(fDao.getKtFormCPD_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormCPD_p() != null) {
	                if (obj.getKtFormCPD_p().getStart_date() == null) {
	                    obj.getKtFormCPD_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormCPD_p().getEnd_date() == null) {
	                    obj.getKtFormCPD_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_PROF_CONF:
	            if (obj.getKtFormProfConf_p() == null) {
	                obj.setKtFormProfConf_p(fDao.getKtFormProfConf_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormProfConf_p() != null) {
	                if (obj.getKtFormProfConf_p().getStart_date() == null) {
	                    obj.getKtFormProfConf_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormProfConf_p().getEnd_date() == null) {
	                    obj.getKtFormProfConf_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_SEM:
	            if (obj.getKtFormSem_p() == null) {
	                obj.setKtFormSem_p(fDao.getKtFormSem_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormSem_p() != null) {
	                if (obj.getKtFormSem_p().getStart_date() == null) {
	                    obj.getKtFormSem_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormSem_p().getEnd_date() == null) {
	                    obj.getKtFormSem_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_CNT_PROJ:
	            if (obj.getKtFormCntProj_p() == null) {
	                obj.setKtFormCntProj_p(fDao.getKtFormCntProj_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormCntProj_p() != null) {
	                if (obj.getKtFormCntProj_p().getStart_date() == null) {
	                    obj.getKtFormCntProj_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormCntProj_p().getEnd_date() == null) {
	                    obj.getKtFormCntProj_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_INN:
	            if (obj.getKtFormInn_p() == null) {
	                obj.setKtFormInn_p(fDao.getKtFormInn_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormInn_p() != null) {
	                if (obj.getKtFormInn_p().getStart_date() == null) {
	                    obj.getKtFormInn_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormInn_p().getEnd_date() == null) {
	                    obj.getKtFormInn_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_CONS:
	            if (obj.getKtFormCons_p() == null) {
	                obj.setKtFormCons_p(fDao.getKtFormCons_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormCons_p() != null) {
	                if (obj.getKtFormCons_p().getStart_date() == null) {
	                    obj.getKtFormCons_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormCons_p().getEnd_date() == null) {
	                    obj.getKtFormCons_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_PROF_ENGMT:
	            if (obj.getKtFormProfEngmt_p() == null) {
	                obj.setKtFormProfEngmt_p(fDao.getKtFormProfEngmt_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormProfEngmt_p() != null) {
	                if (obj.getKtFormProfEngmt_p().getStart_date() == null) {
	                    obj.getKtFormProfEngmt_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormProfEngmt_p().getEnd_date() == null) {
	                    obj.getKtFormProfEngmt_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_IP:
	            if (obj.getKtFormIP_p() == null) {
	                obj.setKtFormIP_p(fDao.getKtFormIP_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormIP_p() != null) {
	                if (obj.getKtFormIP_p().getStart_date() == null) {
	                    obj.getKtFormIP_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormIP_p().getEnd_date() == null) {
	                    obj.getKtFormIP_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_SOC_ENGMT:
	            if (obj.getKtFormSocEngmt_p() == null) {
	                obj.setKtFormSocEngmt_p(fDao.getKtFormSocEngmt_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormSocEngmt_p() != null) {
	                if (obj.getKtFormSocEngmt_p().getStart_date() == null) {
	                    obj.getKtFormSocEngmt_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormSocEngmt_p().getEnd_date() == null) {
	                    obj.getKtFormSocEngmt_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
	            if (obj.getKtFormStaffEngmt_p() == null) {
	                obj.setKtFormStaffEngmt_p(fDao.getKtFormStaffEngmt_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormStaffEngmt_p() != null) {
	                if (obj.getKtFormStaffEngmt_p().getStart_date() == null) {
	                    obj.getKtFormStaffEngmt_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormStaffEngmt_p().getEnd_date() == null) {
	                    obj.getKtFormStaffEngmt_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_EA:
	            if (obj.getKtFormEA_p() == null) {
	                obj.setKtFormEA_p(fDao.getKtFormEA_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormEA_p() != null) {
	                if (obj.getKtFormEA_p().getStart_date() == null) {
	                    obj.getKtFormEA_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormEA_p().getEnd_date() == null) {
	                    obj.getKtFormEA_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_STARTUP:
	            if (obj.getKtFormStartup_p() == null) {
	                obj.setKtFormStartup_p(fDao.getKtFormStartup_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormStartup_p() != null) {
	                if (obj.getKtFormStartup_p().getStart_date() == null) {
	                    obj.getKtFormStartup_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormStartup_p().getEnd_date() == null) {
	                    obj.getKtFormStartup_p().setEnd_date(oldDate);
	                }
	            }
	            break;
	        case SysParam.PARAM_KT_FORM_INV_AWARD:
	            if (obj.getKtFormInvAward_p() == null) {
	                obj.setKtFormInvAward_p(fDao.getKtFormInvAward_P(obj.getPk().getForm_no(), getFormDataLevel(), null, null, 0));
	            }
	            if (obj.getKtFormInvAward_p() != null) {
	                if (obj.getKtFormInvAward_p().getStart_date() == null) {
	                    obj.getKtFormInvAward_p().setStart_date(oldDate);
	                }
	                if (obj.getKtFormInvAward_p().getEnd_date() == null) {
	                    obj.getKtFormInvAward_p().setEnd_date(obj.getKtFormInvAward_p().getStart_date());
	                }
	            }
	            break;
	        default:
	            // Optionally handle unrecognized form_code
	            break;
	    }
	    return obj;
	}
	
	public ImportKTAct getImportKTPanel()
	{
		if(importKTPanel == null) {
			getSelectedFacDeptLkup();
			importKTPanel = new ImportKTAct(getSelectedPeriod(), getSelectedFac(), getSelectedDept(), getSelectedFacDept(), getParamFormCode(), getSumStaffNo());
		}
		return importKTPanel;
	}
	
	
	public String getSideBarTitle() {
		if(getImportKTPanel() != null) {
			String rtnStr = "(";
			rtnStr += "Reporting Period: ";
			if (importKTPanel.getKtPeriod() != null) {
				rtnStr += importKTPanel.getKtPeriod().getPeriod_desc() + "; ";
			}else {
				rtnStr += " ;";
			}
			rtnStr += "Faculty: " + importKTPanel.getParamFacCode();
			if (!Strings.isNullOrEmpty(importKTPanel.getParamDeptCode())) {
				rtnStr += "; ";
				rtnStr += "Department: " + importKTPanel.getParamDeptCode();
			}
			rtnStr += ")";
			return rtnStr;
		}
		return "";
	}
	
	public List<KtFormSummary> getKtFormSummaryListFunction(boolean isAllUnit)
	{
		String acadStaffDataLevel = "P";
		String deptStaffDataLevel = "D";
		getPeriod();
		if(StringUtils.equals(getParamAdmin(), "Y")) 
		{
			// Department Admin
			getSelectedFacDeptLkup();
			
			if(isAllUnit) 
			{
				ktFormSummaryList = fDao.getKtFormSummaryListByDataLevel(deptStaffDataLevel, period.getDate_from(), period.getDate_to(), 2);
			}
			else
			{
				ktFormSummaryList = fDao.getKtFormSummaryListByFacDept(getSelectedFac(), getSelectedDept(), deptStaffDataLevel, null, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
			}
		}
		else 
		{
			// Academic Staff
			if(staffDetail!=null) 
			{
				String staffNumber = staffDetail.getStaff_number();
				ktFormSummaryList = fDao.getKtFormSummaryListByStaff(staffNumber, acadStaffDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id(), null);
				
			}
		}
		return ktFormSummaryList;
	}


	
	public List<KtFormSummary> getKtFormSummaryList()
	{
		return ktFormSummaryList;
	}

	
	public void setKtFormSummaryList(List<KtFormSummary> ktFormSummaryList)
	{
		this.ktFormSummaryList = ktFormSummaryList;
	}

	public Map<String, List<Integer>> formNumberMapFunction(List<KtFormSummary> ktFormSummaryList)
	{
		Map<String, List<Integer>> formNumberMap = new HashMap<String, List<Integer>>();
		for(KtFormSummary obj : ktFormSummaryList)
		{
			if(obj!=null) 
			{
				List<Integer> objList = formNumberMap.get(obj.getFormCode());
				if(objList == null) objList = new ArrayList<Integer>();
				objList.add(obj.getPk().getFormNo());
				
				formNumberMap.put(obj.getFormCode(), objList);
			}
		}
		return formNumberMap;
	}
	
	public void exportKtReport(boolean isAllUnit) 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		String dataLevel = (StringUtils.equals(getParamAdmin(), "Y")) ? "D" : "P";
		if(((StringUtils.equals(getParamAdmin(), "Y")) || (!GenericValidator.isBlankOrNull(getParamPid())) && period!=null )) 
		{		
			//List<String> formCodeList = getFormList().stream().map(f->f.getForm_code()).collect(Collectors.toList());
			ktFormSummaryList = getKtFormSummaryListFunction(isAllUnit);
			
			Map<String, List<Integer>> formNumberMap = formNumberMapFunction(ktFormSummaryList);
			
			try 
			{
				Workbook wb = null;
	    		wb = new XSSFWorkbook();
	    		
	    		if(CollectionUtils.isNotEmpty(getFormList())) 
		    	{
		    		//KtForm
		    		createKtFormDataSheet(wb, getFormList(), formNumberMap, dataLevel, period);
		    		
		    		//Summary
		    		createKtFormSummaryDataSheet(wb, getFormList(), period, isAllUnit);
		    	}
		    	
		    	// Get the byte array of the Workbook
		    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
				wb.write(baos);
				
				// Dispose of temporary files backing this workbook on disk
				if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
				
				wb.close();
				byte[] wbBytes = baos.toByteArray();
				
				// Set the response header
				eCtx.responseReset();
				//eCtx.addResponseHeader("Cache-control", "no-cache");
				//eCtx.addResponseHeader("Pragma", "no-cache");
	
				eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
				eCtx.setResponseHeader("Expires", "-1");
				eCtx.setResponseHeader("Pragma", "private");
	
				DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
				String fileName = "DataExport-" + dateFormat.format(new Date()) + ".xlsx";		        
				
				eCtx.setResponseContentType(new Tika().detect(fileName));
				eCtx.setResponseContentLength(wbBytes.length);
				eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

				// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
				//setPrimeFacesDownloadCompleted("exportRptBtn");
				
				// Send the bytes to response OutputStream
				OutputStream os = eCtx.getResponseOutputStream();
				os.write(wbBytes);
			
				fCtx.responseComplete();

			}
			catch (IOException e) 
	    	{
				String message = "Cannot send Workbook bytes to response OutputStream ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			
		}
		
	}
	
	public void createKtFormDataSheet(Workbook wb, List<KtForm> formList, Map<String, List<Integer>> formNumberMap, String dataLevel, KtRptPeriod period)
	{
		for(KtForm form : getFormList())
    	{
    		List<Integer> formCodeNumList = formNumberMap.get(form.getForm_code());
    		
    		if(StringUtils.equals(form.getForm_code(), KtFormIP_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormIP_P> objList = fDao.getKtFormIP_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			/*if(CollectionUtils.isNotEmpty(objList)) 
    			{
    				if(!period.getPeriod_id().equals(1)) 
    				{
    					List<KtFormIP_P> tempList = new ArrayList<KtFormIP_P>();
	    				
	    				for(KtFormIP_P obj : objList)
	    				{
	    					boolean valid = false;
    						
    						if(obj.getStart_date()!=null) 
    						{
    							if((obj.getStart_date().after(period.getDate_from()) || obj.getStart_date().equals(period.getDate_from())) 
    									&& (obj.getStart_date().before(period.getDate_to()) || obj.getStart_date().equals(period.getDate_to()))) 
    							{
    								valid = true;
    							}
    							
    							if(valid) tempList.add(obj);
	    					}
	    					
	    					objList = tempList;
	    				}
    				}
    			}*/
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormIPReportGroup reportGroup = new KtFormIPReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormIP_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    			
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormCntProj_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormCntProj_P> objList = fDao.getKtFormCntProj_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id()); 			

    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormCntProjReportGroup reportGroup = new KtFormCntProjReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormCntProj_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}	
    		else if(StringUtils.equals(form.getForm_code(), KtFormInn_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormInn_P> objList = fDao.getKtFormInn_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());

    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormInnReportGroup reportGroup = new KtFormInnReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormInn_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormCons_P.REPORT_FORM_CODE))
    		{
    			List<KtFormCons_P> objList = fDao.getKtFormCons_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormConsultReportGroup reportGroup = new KtFormConsultReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormCons_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormEA_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormEA_P> objList = fDao.getKtFormEA_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormEaReportGroup reportGroup = new KtFormEaReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormEA_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormStartup_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormStartup_P> objList = fDao.getKtFormStartup_PList(dataLevel, formCodeNumList);
    			
    			if(CollectionUtils.isNotEmpty(objList)) 
    			{
    				if(!period.getPeriod_id().equals(1)) 
    				{
    					List<KtFormStartup_P> tempList = new ArrayList<KtFormStartup_P>();
    					
    					for(KtFormStartup_P obj : objList)
    					{
    						boolean valid = false;
    						
    						if(obj.getStart_date()!=null) 
    						{
    							if((obj.getStart_date().after(period.getDate_from()) || obj.getStart_date().equals(period.getDate_from())) && (obj.getStart_date().before(period.getDate_to()) || obj.getStart_date().equals(period.getDate_to()))) 
    							{
    								valid = true;
    							}
    						}
    						
    						if(valid) tempList.add(obj);
    					}
    					
    					objList = tempList;
    				}
    			}
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormStartupReportGroup reportGroup = new KtFormStartupReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormStartup_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormInvAward_P.REPORT_FORM_CODE))
    		{
    			List<KtFormInvAward_P> objList = fDao.getKtFormInvAward_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormInvAwardReportGroup reportGroup = new KtFormInvAwardReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormInvAward_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormSocEngmt_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormSocEngmt_P> objList = fDao.getKtFormSocEngmt_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
        			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormSocEngmtReportGroup reportGroup = new KtFormSocEngmtReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormSocEngmt_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormCPD_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormCPD_P> objList = fDao.getKtFormCPD_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());

    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormCPDReportGroup reportGroup = new KtFormCPDReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormCPD_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormProfConf_P.REPORT_FORM_CODE)) 
    		{
				List<KtFormProfConf_P> objList = fDao.getKtFormProfConf_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());

    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormProfConfReportGroup reportGroup = new KtFormProfConfReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormProfConf_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormSem_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormSem_P> objList = fDao.getKtFormSem_PList(dataLevel, formCodeNumList);
    			
    			if(CollectionUtils.isNotEmpty(objList)) 
    			{
    				if(!period.getPeriod_id().equals(1)) 
    				{
    					List<KtFormSem_P> tempList = new ArrayList<KtFormSem_P>();
    					
    					for(KtFormSem_P obj : objList)
    					{
    						boolean valid = false;
    						
    						if(obj.getStart_date()!=null) 
    						{
    							if((obj.getStart_date().after(period.getDate_from()) || obj.getStart_date().equals(period.getDate_from())) && (obj.getStart_date().before(period.getDate_to()) || obj.getStart_date().equals(period.getDate_to()))) 
    							{
    								valid = true;
    							}
    						}
    						
    						if(valid) tempList.add(obj);
    					}
    					
    					objList = tempList;
    				}
    			}
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormSeminarReportGroup reportGroup = new KtFormSeminarReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormSem_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormProfEngmt_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormProfEngmt_P> objList = fDao.getKtFormProfEngmt_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormProfEngmtReportGroup reportGroup = new KtFormProfEngmtReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormProfEngmt_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormStaffEngmt_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormStaffEngmt_P> objList = fDao.getKtFormStaffEngmt_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormStaffEngmtReportGroup reportGroup = new KtFormStaffEngmtReportGroup();
    			reportGroup.setKtAdmin(Boolean.TRUE.equals(getIsKtAdmin()));
    			reportGroup.setRdoAdmin(Boolean.TRUE.equals(getIsRdoAdmin()));
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormStaffEngmt_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    	}
	}
	
	public void createKtFormSummaryDataSheet(Workbook workbook, List<KtForm> formList, KtRptPeriod period, boolean isAllUnit) 
	{
		String sheetName = "Summary";
		Sheet sheet = workbook.createSheet(sheetName);
		
		//Create the header row
		Row row = sheet.createRow(1);
		Cell cell = null;
		
		//Font style of bold
		Font fontBold = workbook.createFont();
		fontBold.setBold(true);
		
		CellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setFont(fontBold);
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle defaultCellStyle = textCellStyle;
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle dollarCellStyle = workbook.createCellStyle();
		//dollarCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("$#,##0.00"));
		dollarCellStyle.setDataFormat(workbook.createDataFormat().getFormat("$#,##0.00_);($#,##0.00)"));
		
		cell = row.createCell(1);
		cell.setCellValue("Summary of Performance Indicators");
		cell.setCellStyle(titleStyle);
		
		//Header row
		row = sheet.createRow(5);
		cell = row.createCell(3);
		cell.setCellValue(period.getPeriod_desc());
		sheet.autoSizeColumn(cell.getColumnIndex());
		
		sheet.setAutoFilter(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, row.getLastCellNum()-1));
		
		// Summary Description column width
		row.getSheet().setColumnWidth(2, 10000);
		
		int count = 1;
		
		for(KtForm form : formList)
		{
			row = sheet.createRow(sheet.getLastRowNum()+1);
			int formStartRow = row.getRowNum(); 
					
			List<KtRptSum> sumList;
			
			if(isAllUnit) 
			{
				sumList = getAllUnitSumMap().get(form.getForm_code());
			}
			else
			{
				sumList = form.getSum();
			}
			
			cell = row.createCell(0);
			cell.setCellValue(count);
			
			cell = row.createCell(1);
			cell.setCellValue(form.getForm_full_desc());
			cell.setCellStyle(textCellStyle);
			sheet.autoSizeColumn(cell.getColumnIndex());
			
			if(CollectionUtils.isNotEmpty(sumList)) 
			{
				for (int i = 0; i < sumList.size(); i++) {
				    KtRptSum summary = sumList.get(i);
				    
				    if (summary == null) {
				        continue; // Skip null entries
				    }
				    
				    // Column 2: Description
				    cell = row.createCell(2);
				    cell.setCellValue(summary.getSum_desc() != null ? summary.getSum_desc() : "");
				    cell.setCellStyle(textCellStyle != null ? textCellStyle : defaultCellStyle); // Assuming defaultCellStyle exists
				    
				    // Column 3: Value
				    cell = row.createCell(3);
				    Double sumValue = summary.getSum_value(); // Assuming getSum_value returns Double
				    if (sumValue != null) {
				        cell.setCellValue(sumValue); // Set the numeric value
				    } else {
				        cell.setCellValue((String) null); // Set cell to blank (null equivalent for display)
				    }
				    
				    // Check sum_type for styling
				    String sumType = summary.getSum_type();
				    if (sumType != null) {
				        if ("dollar".equals(sumType)) {
				            cell.setCellStyle(dollarCellStyle != null ? dollarCellStyle : defaultCellStyle);
				        } else if ("num".equals(sumType)) {
				            cell.setCellStyle(numberCellStyle != null ? numberCellStyle : defaultCellStyle);
				        }
				    }
				    
				    // Create new row only if not last iteration and sheet is not null
				    if (i < sumList.size() - 1 && sheet != null) {
				        row = sheet.createRow(sheet.getLastRowNum() + 1);
				    }
				}
				
				if(sumList.size()>1) 
				{
					// seq column
					sheet.addMergedRegion(new CellRangeAddress(formStartRow,row.getRowNum(),0,0));
					
					// form desc column
					sheet.addMergedRegion(new CellRangeAddress(formStartRow,row.getRowNum(),1,1));
				}
				
			}
			count++;
		}
	}

	
	public Map<String, List<KtRptSum>> getAllUnitSumMap()
	{
		if(allUnitSumMap == null) 
		{
			allUnitSumMap = new HashMap<String, List<KtRptSum>>();
			
			
			if(getFormList() != null && getPeriod() != null) 
			{
				List<KtFormSummary> ktFormSummaryListByFormCode = null;
				
				String summaryDataLevel = getSumDataLevel();
				
				ktFormSummaryList = FormDAO.getCacheInstance().getKtFormSummaryListByDataLevel(summaryDataLevel, period.getDate_from(), period.getDate_to(), 2);
				List<KtRptSum> sumList;
				
				List<KtForm> fList = new ArrayList<KtForm>(formList);
				
				for (KtForm f:fList) {
					switch(f.getForm_code()) {
						case SysParam.PARAM_KT_FORM_CPD:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormCPD_P> cpd_headerList = new ArrayList<KtFormCPD_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormCPD_P obj = fDao.getKtFormCPD_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								cpd_headerList.add(obj);
							}
							
							for (KtRptSum rptSum:f.getSum()) {
								KtRptSum s = rptSum;
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(cpd_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = cpd_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = cpd_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
									case "num_hr":
										Integer num_hr = cpd_headerList.stream().filter(a -> a.getNum_stu_contact_hr() != null).mapToInt(a -> a.getNum_stu_contact_hr()).sum();
										s.setSum_value(Double.valueOf(num_hr));
										sumList.add(s);
										break;
								}	
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_PROF_CONF:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
														
							List<KtFormProfConf_P> prof_conf_headerList = new ArrayList<KtFormProfConf_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormProfConf_P obj = fDao.getKtFormProfConf_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								prof_conf_headerList.add(obj);
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(prof_conf_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = prof_conf_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = prof_conf_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
									case "num_present":
										Integer num_present = prof_conf_headerList.stream().filter(a -> a.getNum_presentation() != null).mapToInt(a -> a.getNum_presentation()).sum();
										s.setSum_value(Double.valueOf(num_present));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_SEM:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormSem_P> sem_headerList = new ArrayList<KtFormSem_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormSem_P obj = fDao.getKtFormSem_P(d.getPk().getFormNo(), summaryDataLevel, null, null, 0);
								
								if (obj != null) 
								{
									boolean valid = false;

									if(obj.getStart_date()!=null) 
									{
										if((obj.getStart_date().after(period.getDate_from()) || obj.getStart_date().equals(period.getDate_from())) && (obj.getStart_date().before(period.getDate_to()) || obj.getStart_date().equals(period.getDate_to()))) 
										{
											valid = true;
										}
									}

									if(obj.getEnd_date()!=null) 
									{
										if((obj.getEnd_date().after(period.getDate_from()) || obj.getEnd_date().equals(period.getDate_from())) && (obj.getEnd_date().before(period.getDate_to()) || obj.getEnd_date().equals(period.getDate_to()))) 
										{
											valid = true;
										}
									}

		    						if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
		    						{
		    							if(!(obj.getStart_date().after(period.getDate_to()) || obj.getEnd_date().before(period.getDate_from()))) 
		    							{
		    								valid = true;
		    							}
		    						}

									if(valid) sem_headerList.add(obj);
								}
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(sem_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = sem_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = sem_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_CNT_PROJ:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormCntProj_P> cnt_proj_headerList = new ArrayList<KtFormCntProj_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());

							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormCntProj_P obj = fDao.getKtFormCntProj_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								if (obj != null) 
								{
									cnt_proj_headerList.add(obj);
								}
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(cnt_proj_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = cnt_proj_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = cnt_proj_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_INN:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							List<KtFormInn_P> inn_headerList = new ArrayList<KtFormInn_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormInn_P obj = fDao.getKtFormInn_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								inn_headerList.add(obj);
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(inn_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = inn_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = inn_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_CONS:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormCons_P> cons_headerList = new ArrayList<KtFormCons_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormCons_P obj = fDao.getKtFormCons_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								cons_headerList.add(obj);
								
							}
							System.out.println("cons_headerList:"+cons_headerList);
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(cons_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = cons_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = cons_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_PROF_ENGMT:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormProfEngmt_P> prof_engmt_headerList = new ArrayList<KtFormProfEngmt_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormProfEngmt_P obj = fDao.getKtFormProfEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								if (obj != null) 
								{
									prof_engmt_headerList.add(obj);
								}
							}
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(prof_engmt_headerList.size()));
										sumList.add(s);
										break;
									case "num_teacher":
										Integer num_teacher = prof_engmt_headerList.stream().filter(a -> a.getNum_teacher() != null).mapToInt(a -> a.getNum_teacher()).sum();
										s.setSum_value(Double.valueOf(num_teacher));
										sumList.add(s);
										break;
									case "num_principal":
										Integer num_principal = prof_engmt_headerList.stream().filter(a -> a.getNum_principal() != null).mapToInt(a -> a.getNum_principal()).sum();
										s.setSum_value(Double.valueOf(num_principal));
										sumList.add(s);
										break;
									case "num_other":
										Integer num_other = prof_engmt_headerList.stream().filter(a -> a.getNum_other() != null).mapToInt(a -> a.getNum_other()).sum();
										s.setSum_value(Double.valueOf(num_other));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_IP:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormIP_P> ip_headerList = new ArrayList<KtFormIP_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormIP_P obj = fDao.getKtFormIP_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								if (obj != null) 
								{
									ip_headerList.add(obj);
								}
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(ip_headerList.size()));
										sumList.add(s);
										break;
									case "income_total":
										Double income_total = ip_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
										s.setSum_value(Double.valueOf(income_total));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_SOC_ENGMT:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormSocEngmt_P> soc_engmt_headerList = new ArrayList<KtFormSocEngmt_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormSocEngmt_P obj = fDao.getKtFormSocEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								if (obj != null) 
								{
									soc_engmt_headerList.add(obj);
								}
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_attendee":
										Integer num_attendee = soc_engmt_headerList.stream().filter(a -> a.getNum_attendee() != null).mapToInt(a -> a.getNum_attendee()).sum();
										s.setSum_value(Double.valueOf(num_attendee));
										sumList.add(s);
										break;
									case "num_perform":
										Long num_perform = soc_engmt_headerList.stream().filter(a -> "Y".equals(a.getPerform())).count();
										s.setSum_value(Double.valueOf(num_perform));
										sumList.add(s);
										break;
									case "num_total":
										s.setSum_value(Double.valueOf(soc_engmt_headerList.size()));	
										sumList.add(s);
										break;
									case "num_key":
										Integer num_key = soc_engmt_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
										s.setSum_value(Double.valueOf(num_key));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
							sumList = allUnitSumMap.get(f.getForm_code());
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormStaffEngmt_P> staff_engmt_headerList = new ArrayList<KtFormStaffEngmt_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormStaffEngmt_P obj = fDao.getKtFormStaffEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								if (obj != null) 
								{
									staff_engmt_headerList.add(obj);
								}
							}
							
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(staff_engmt_headerList.size()));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_EA:
							sumList = allUnitSumMap.get(f.getForm_code());
							
							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormEA_P> ea_headerList = new ArrayList<KtFormEA_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());

							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormEA_P obj = fDao.getKtFormEA_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								
								if (obj != null) 
								{
									ea_headerList.add(obj);
								}
							}
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(ea_headerList.size()));
										sumList.add(s);
										break;
									case "num_team":
										s.setSum_value(null);
										sumList.add(s);
										break;
									case "num_stu":
										Integer num_pg_stu = ea_headerList.stream()
									    .filter(a -> a.getNum_stu_pg() != null)
									    .mapToInt(a -> a.getNum_stu_pg())
									    .sum();
									    
										Integer num_ug_stu = ea_headerList.stream()
										    .filter(a -> a.getNum_stu_ug() != null)
										    .mapToInt(a -> a.getNum_stu_ug())
										    .sum();
		
										// Handle potential null values (if no elements matched the filter, sum will be 0)
										int pg = num_pg_stu != null ? num_pg_stu : 0;
										int ug = num_ug_stu != null ? num_ug_stu : 0;
		
										int num_stu = pg + ug;
										s.setSum_value((double) num_stu);
										sumList.add(s);
										break;
									case "num_alumni":
										Integer num_alumni = ea_headerList.stream().filter(a -> a.getNum_alumni() != null).mapToInt(a -> a.getNum_alumni()).sum();
										s.setSum_value(Double.valueOf(num_alumni));
										sumList.add(s);
										break;
									case "num_staff":
										Integer num_staff = ea_headerList.stream().filter(a -> a.getNum_staff() != null).mapToInt(a -> a.getNum_staff()).sum();
										s.setSum_value(Double.valueOf(num_staff));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case SysParam.PARAM_KT_FORM_STARTUP:
							List<KtFormStartup_P> startup_headerList = new ArrayList<KtFormStartup_P>();
//							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
//															.filter(y -> y.getFormCode().equals(f.getForm_code()))
//															.collect(Collectors.toList());
							
							break;
						case SysParam.PARAM_KT_FORM_INV_AWARD:
							sumList = allUnitSumMap.get(f.getForm_code());

							if(sumList == null) sumList = new ArrayList<KtRptSum>();
							
							List<KtFormInvAward_P> inv_award_headerList = new ArrayList<KtFormInvAward_P>();
							ktFormSummaryListByFormCode = ktFormSummaryList.stream()
															.filter(y -> y.getFormCode().equals(f.getForm_code()))
															.collect(Collectors.toList());
							for (KtFormSummary d:ktFormSummaryListByFormCode) 
							{
								KtFormInvAward_P obj = fDao.getKtFormInvAward_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
								if (obj != null) 
								{
									inv_award_headerList.add(obj);
								}
							}
							for (KtRptSum s:f.getSum()) {
								switch(s.getSum_key()) {
									case "num_total":
										s.setSum_value(Double.valueOf(inv_award_headerList.size()));
										sumList.add(s);
										break;
									case "num_filed":
										Integer num_filed = inv_award_headerList.stream().filter(a -> a.getNum_pat_filed() != null).mapToInt(a -> a.getNum_pat_filed()).sum();
										s.setSum_value(Double.valueOf(num_filed));
										sumList.add(s);
										break;
									case "num_granted":
										Integer num_granted = inv_award_headerList.stream().filter(a -> a.getNum_pat_granted() != null).mapToInt(a -> a.getNum_pat_granted()).sum();
										s.setSum_value(Double.valueOf(num_granted));
										sumList.add(s);
										break;
								}
							}
							allUnitSumMap.put(f.getForm_code(), sumList);
							break;
						case "":
							break;
					}
				}
			}
			
			
		}
		return allUnitSumMap;
	}

	
	public void setAllUnitSumMap(Map<String, List<KtRptSum>> allUnitSumMap)
	{
		this.allUnitSumMap = allUnitSumMap;
	}

	public boolean checkFormHasLv(String dataLevel, String paramNo)
	{
		boolean result = false;
		switch(paramFormCode) {
			case SysParam.PARAM_KT_FORM_CPD:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormCPD_P tmp = fDao.getKtFormCPD_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_PROF_CONF:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormProfConf_P tmp = fDao.getKtFormProfConf_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_SEM:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormSem_P tmp = fDao.getKtFormSem_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_CNT_PROJ:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormCntProj_P tmp = fDao.getKtFormCntProj_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_INN:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormInn_P tmp = fDao.getKtFormInn_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_CONS:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormCons_P tmp = fDao.getKtFormCons_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_PROF_ENGMT:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormProfEngmt_P tmp = fDao.getKtFormProfEngmt_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_IP:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormIP_P tmp = fDao.getKtFormIP_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_SOC_ENGMT:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormSocEngmt_P tmp = fDao.getKtFormSocEngmt_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormStaffEngmt_P tmp = fDao.getKtFormStaffEngmt_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_EA:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormEA_P tmp = fDao.getKtFormEA_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_STARTUP:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormStartup_P tmp = fDao.getKtFormStartup_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_INV_AWARD:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormInvAward_P tmp = fDao.getKtFormInvAward_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case "":
				break;
		}
		return result;
	}
}


