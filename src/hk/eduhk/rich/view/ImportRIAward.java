package hk.eduhk.rich.view;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Logger;

import hk.eduhk.rich.entity.importRI.*;


@SuppressWarnings("serial")
public class ImportRIAward implements Serializable
{	
	private List<ImportRIAwardV> awardList;
	private Map<ImportRIAwardV_PK, ImportRIAwardV> awardMap;
	private ImportRIAwardV_PK selectedPreview;
	private String paramPid;
	
	
	private ImportRIStatus_PK selectedIgnoreAward;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public List<ImportRIAwardV> getAwardList() {
		if(awardList == null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			awardList = dao.getNewImportRIAwardV(getParamPid());
			awardMap = new HashMap<ImportRIAwardV_PK, ImportRIAwardV>();
			if(awardList == null) {
				awardList = new ArrayList<ImportRIAwardV>();
			}
			else {
				for(ImportRIAwardV v : awardList) {
					awardMap.put(v.getPk(), v);
				}
			}
				
		}
		return awardList;
	}
	
	
	public void setAwardList(List<ImportRIAwardV> awardList)
	{
		this.awardList = awardList;
	}



	public void setSelectedPreview(String area_code, String source_id, String staff_number) {
		selectedPreview = new ImportRIAwardV_PK();
		selectedPreview.setArea_code(area_code);
		selectedPreview.setSource_id(source_id);
		selectedPreview.setStaff_number(staff_number);
	}
	
	
	public ImportRIAwardV_PK getSelectedPreview()
	{
		return selectedPreview;
	}

	public Map<ImportRIAwardV_PK, ImportRIAwardV> getAwardMap()
	{
		return awardMap;
	}
	
	
	public ImportRIAwardV getAward() {
		if(getAwardMap() != null)
			return getAwardMap().get(getSelectedPreview());
		else
			return new ImportRIAwardV();
	}
	
	public void setSelectedIgnoreAward(String area_code, String source_id, String staff_number) {
		selectedIgnoreAward = new ImportRIStatus_PK();
		selectedIgnoreAward.setArea_code(area_code);
		selectedIgnoreAward.setSource_id(source_id);
		selectedIgnoreAward.setStaff_number(staff_number);
	}
	
	public void setSelectedIgnoreAward() {
		selectedIgnoreAward = new ImportRIStatus_PK();
		selectedIgnoreAward.setArea_code(selectedPreview.getArea_code());
		selectedIgnoreAward.setSource_id(selectedPreview.getSource_id());
		selectedIgnoreAward.setStaff_number(selectedPreview.getStaff_number());
	}
	
	
	public ImportRIStatus_PK getSelectedIgnoreAward()
	{
		return selectedIgnoreAward;
	}
		
	
	public String getParamPid()
	{
		return paramPid;
	}


	
	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}
}
