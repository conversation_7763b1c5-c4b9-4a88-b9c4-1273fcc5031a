package hk.eduhk.rich.view;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;

import javax.faces.application.FacesMessage;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.model.file.UploadedFile;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.data.DataMatrix;
import hk.eduhk.rich.data.DataMatrixExtractor;
import hk.eduhk.rich.entity.JournalRank;
import hk.eduhk.rich.entity.JournalRankDAO;

@SuppressWarnings("serial")
@ManagedBean(name = "manageJournalRankingView")
@ViewScoped
public class ManageJournalRankingView extends BaseView implements Serializable{
	private transient UploadedFile uploadedFile = null;
	private List<JournalRank> journalRankList = null;
	private List<JournalRank> filteredJournalRank;
	private JournalRankDAO jdao = JournalRankDAO.getInstance();
	
	
	public UploadedFile getUploadedFile(){
		return uploadedFile;
	}

	public void setUploadedFile(UploadedFile uploadedFile){
		this.uploadedFile = uploadedFile;
	}
	
	public List<JournalRank> getJournalRankList(){
		if(journalRankList == null) {
			journalRankList = jdao.getJournalRankList();
		}
		
		return journalRankList;
	}

	public void setJournalRankList(List<JournalRank> journalRankList){
		this.journalRankList = journalRankList;
	}
	
	public List<JournalRank> getFilteredJournalRank(){
		return filteredJournalRank;
	}

	public void setFilteredJournalRank(List<JournalRank> filteredJournalRank){
		this.filteredJournalRank = filteredJournalRank;
	}

	public String upload() throws Exception {
		String redirectURL = redirect("manageJournalRanking");
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if(uploadedFile == null || uploadedFile.getContent() == null || uploadedFile.getContent().length<= 0) {
			printMessage(FacesMessage.SEVERITY_ERROR, "Please choose an Excel file.", "");
		}else {
			String fileName = uploadedFile.getFileName();
			String extension = FilenameUtils.getExtension(fileName);
			if (!"xlsx".equalsIgnoreCase(extension) && !"xls".equalsIgnoreCase(extension)){
				printMessage(FacesMessage.SEVERITY_ERROR, "Invalid file. The file must be in Excel format (xlsx or xls).", "");
				return redirectURL;
			}
			
			DataMatrixExtractor extractor = new DataMatrixExtractor(uploadedFile);
			DataMatrix dataMatrix = null;
			
			try{
				dataMatrix = extractor.getDataMatrix();
			}
			catch (Exception e){
				getLogger().log(Level.WARNING, "", e.getCause());
				printMessage(FacesMessage.SEVERITY_ERROR, getResourceBundle().getString("msg.err.unexpected"), "");
				return redirectURL;
			}
			
			List<String> templateHeaderList = getTemplateHeaderList();
			List<String> fileHeaderList = null;
			int rowNum = dataMatrix.getLastRowNum();
			
    		if (rowNum > 0){
    			fileHeaderList = new ArrayList<String>();
    			for(String header : dataMatrix.getRowValueList(0)) {
    				//System.out.println("HEADER: " + header);
	    			if(!StringUtils.isBlank(header)) fileHeaderList.add(header);
	    		}
    		}
    		
    		boolean headerError = false;
    		if(fileHeaderList != null && fileHeaderList.size() != 0){
    			if(fileHeaderList.size() != templateHeaderList.size())
    				headerError = true;

    			for(int i=0; i < fileHeaderList.size(); i++){
    				if(fileHeaderList.get(i) == null || !fileHeaderList.get(i).equals(templateHeaderList.get(i))){
    					headerError = true;
    					break;
    				}
    			}
    		}else{
    			headerError = true;
    		}
    		
    		if(headerError == true) {
    			printMessage(FacesMessage.SEVERITY_ERROR, "The headers of the excel file do not match.", "");
    			return redirectURL;
    		}
    		
    		Integer dataRowNum = 0;
    		List<JournalRank> newJournalRankList = new ArrayList<JournalRank>();
    		
    		for(int i = 1; i <= rowNum; i++) {
    			if (dataMatrix.isEmptyRow(i)) continue;
    			dataRowNum++;
    			JournalRank journalRank = new JournalRank();
    			
    			for (int j = 0; j < fileHeaderList.size(); j++){
    				String strCellValue = dataMatrix.getValue(i, j);
    				if ("NULL".equals(strCellValue)) {
    					strCellValue = null;
    				}
    				switch(j+1) {
    					case 1 :
    						journalRank.setTitle(strCellValue);
    						break;
    					case 2 :
    						journalRank.setIssn_1(strCellValue);
    						break;
    					case 3 :
    						journalRank.setIssn_2(strCellValue);
    						break;
    					case 4 :
    						journalRank.setIssn_3(strCellValue);
    						break;
    					case 5 :
    						journalRank.setRank(strCellValue);
    						break;	
    					case 6 :
    						if(strCellValue != null && !strCellValue.equals("")) journalRank.setJcr(strCellValue);
    						break;
    					case 7 :
    						if(strCellValue != null && !strCellValue.equals("")) journalRank.setSjr(strCellValue);
    						break;
    					case 8 :
    						journalRank.setYear(strCellValue);
    						break;
    				}
    			}
    			
    			journalRank.setCreator(fCtx.getExternalContext().getRemoteUser());
    			journalRank.setUserstamp(fCtx.getExternalContext().getRemoteUser());
    			newJournalRankList.add(journalRank);
    		}
    		jdao.clearOldRecord();
    		int count = 0;
			for(JournalRank journalRank: newJournalRankList) {
				count++;
				journalRank.setRank_id(count);
				jdao.updateJournalRank(journalRank);
			}
			
			printMessage(FacesMessage.SEVERITY_INFO, "Journal Ranking is successfully updated.", "");
		}
		
		return redirectURL;
	}
	
	public String currentDateString() throws ParseException
	{
		SimpleDateFormat sdfDate = new SimpleDateFormat("YYMMdd");
	    Date currentDate = new Date();
	    String strDate = sdfDate.format(currentDate);
	    return strDate;
	}
	
	public void export() throws Exception {	
		XSSFWorkbook workbook = new XSSFWorkbook();   
		
		//Set width
		XSSFSheet sheet = workbook.createSheet(currentDateString()+"_Journals");
		sheet.setColumnWidth(0, 20000);
		sheet.setColumnWidth(1, 3000);
		sheet.setColumnWidth(2, 3000);
		sheet.setColumnWidth(3, 3000);
		sheet.setColumnWidth(4, 2000);
		sheet.setColumnWidth(5, 4000);
		sheet.setColumnWidth(6, 4000);
		
		//Set header
		XSSFRow header = sheet.createRow(0);
	    List<String> templateHeader = getTemplateHeaderList();
	    for(int i = 0; i < templateHeader.size(); i++) {
	    	Cell headerCell = header.createCell(i);
	    	headerCell.setCellValue(templateHeader.get(i));
	    }
	    
	    if(journalRankList == null) {
	    	journalRankList = jdao.getJournalRankList();
	    }
	    
	    if (journalRankList != null) {
		    for(int i = 0; i < journalRankList.size(); i++) {
		    	XSSFRow row = sheet.createRow(i + 1);
		    	JournalRank journalRank = journalRankList.get(i);
		    	for(int j = 0; j < templateHeader.size(); j++) {
		    		Cell cell = row.createCell(j);
	   				switch(j+1) {
						case 1 :
							cell.setCellValue(journalRank.getTitle());
							break;
						case 2 :
							cell.setCellValue(journalRank.getIssn_1());
							break;
						case 3 :
							cell.setCellValue(journalRank.getIssn_2());
							break;
						case 4 :
							cell.setCellValue(journalRank.getIssn_3());
							break;
						case 5 :
							cell.setCellValue(journalRank.getRank());
							break;	
						case 6 :
							cell.setCellValue(journalRank.getJcr());
							break;
						case 7 :
							cell.setCellValue(journalRank.getSjr());
							break; 	
						case 8 :
							cell.setCellValue(journalRank.getYear());
							break; 
	   				}
		    	}
		    }
	    }
	    try {
	        File file = new File("Journal Ranking.xlsx");
			OutputStream fileOut = new FileOutputStream(file);   
			workbook.write(fileOut);   
			workbook.close();
	        fileOut.flush();
	        fileOut.close();
			
			ExternalContext context =  FacesContext.getCurrentInstance().getExternalContext();
			
			context.responseReset();			
			context.setResponseContentType("application/xlsx");
			context.setResponseContentLength((int) file.length());
			
			context.setResponseHeader("Content-Disposition", "inline; filename=\"" + file.getName() + "\""); 
			context.setResponseHeader("Cache-Control", "private, must-revalidate");
			context.setResponseHeader("Expires", "-1");
			context.setResponseHeader("Pragma", "private");
			
			OutputStream output = context.getResponseOutputStream();
	        Files.copy(file.toPath(), output);

	        output.flush();
	        output.close();
	        FacesContext.getCurrentInstance().responseComplete();
	    }catch (Exception e){
	    	
	    }
	}
	
	public List<String> getTemplateHeaderList(){
		List<String> templateHeaderList = new ArrayList<>();
		templateHeaderList.add("Research Journal Title");
		templateHeaderList.add("ISSN_1");
		templateHeaderList.add("ISSN_2");
		templateHeaderList.add("ISSN_3");
		templateHeaderList.add("Rank");
		templateHeaderList.add("JCR Best Quartile");
		templateHeaderList.add("SJR Best Quartile");
		templateHeaderList.add("Year");
		
		return templateHeaderList;		
	}
	
	public void printMessage(Severity severity, String summary, String content) {
		FacesMessage message = new FacesMessage(severity, summary, content);
		FacesContext.getCurrentInstance().addMessage(null, message);
	}
}
