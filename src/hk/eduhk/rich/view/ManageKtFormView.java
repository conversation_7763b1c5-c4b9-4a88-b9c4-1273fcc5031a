package hk.eduhk.rich.view;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.*;
import javax.faces.component.visit.*;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

import com.google.common.base.Strings;

import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.SecFuncLock;
import hk.eduhk.rich.access.SecFuncLockUser;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.form.*;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.project.ProjectHeader_P;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.staff.*;
import hk.eduhk.rich.param.SysParam;



@ManagedBean(name = "manageKtFormView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageKtFormView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(KtForm.class.getName());
	private List<KtForm> formList = null;
	private KtForm selectedForm;
	private KtRptPeriod selectedKtRptPeriod;
	private List<SelectItem> cdcfStatusList;
	
	//KT Form select item list
	private List<SelectItem> ip_fundSrcList;
	private List<SelectItem> cons_fundSrcList;
	private List<SelectItem> prof_conf_eventTypeList;
	private List<SelectItem> prof_conf_targetPaxList;
	private List<SelectItem> sem_targetPaxList;
	private List<SelectItem> cons_roleList;
	private List<SelectItem> ip_catList;
	private List<SelectItem> staff_engmt_natureList;
	private List<SelectItem> ip_orgTypeList;
	private List<SelectItem> cons_orgTypeList;
	private List<SelectItem> ea_actCatList;
	private List<SelectItem> ea_actTypeList;
	private List<SelectItem> soc_engmt_actTypeList;
	private List<SelectItem> startup_actTypeList;
	private List<SelectItem> cnt_proj_ownershipIpRightList;
	private List<SelectItem> inn_ownershipIpRightList;
	private List<SelectItem> cons_hkFundList;
	private List<SelectItem> cons_hkGovFundList;
	private List<SelectItem> cons_hkPriFundList;
	private List<SelectItem> cons_jointProjList;
	private List<SelectItem> ip_hkLicList;
	private List<SelectItem> ip_hkGovList;
	private List<SelectItem> ip_hkPriList;
	private List<SelectItem> ea_modeList;
	private List<SelectItem> ea_targetPaxList;
	private List<SelectItem> soc_engmt_modeList;
	private List<SelectItem> soc_engmt_targetPaxList;
	private List<SelectItem> cpd_modeList;
	private List<SelectItem> prof_conf_modeList;
	
	private List<KtFormDetails_P> selectedFormDetails_p_list;
	private KtFormDetails_Q selectedFormDetails_q;
	private KtFormHeader_Q selectedFormHeader_q;
	private KtFormState_Q selectedFormState_q;
	
	private List<AwardDetails> inv_award_awardDetails_list;
	private List<PatentDetails> inv_award_patentFiled_list;
	private List<PatentDetails> inv_award_patentGranted_list;
	
	private String paramFormCode;
	private String paramPeriod;
	private String paramFacDept;
	private String paramAdmin;
	private String paramRiNo;
	
	private String riCreatorStaffNo;
	private Boolean isCreator;
	private Boolean isContributor;
	
	private Boolean canModifyKt;
	private Boolean canDelete;
	
	private Boolean hasError;
	private Boolean saved;

	private KtFormCPD_P selectedFormCPD_p;
	private KtFormProfConf_P selectedFormProfConf_p;
	private KtFormSem_P selectedFormSem_p;
	private KtFormCntProj_P selectedFormCntProj_p;
	private KtFormInn_P selectedFormInn_p;
	private KtFormCons_P selectedFormCons_p;
	private KtFormProfEngmt_P selectedFormProfEngmt_p;
	private KtFormIP_P selectedFormIP_p;
	private KtFormSocEngmt_P selectedFormSocEngmt_p;
	private KtFormStaffEngmt_P selectedFormStaffEngmt_p;
	private KtFormEA_P selectedFormEA_p;
	private KtFormStartup_P selectedFormStartup_p;
	private KtFormInvAward_P selectedFormInvAward_p;
	
	private FormDAO fDao = FormDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private KtRptDAO rDao = KtRptDAO.getInstance();
	private LookupValueDAO vDao = LookupValueDAO.getInstance();
	
	public String getParamFormCode()
	{
		return paramFormCode;
	}

	
	public void setParamFormCode(String paramFormCode)
	{
		this.paramFormCode = paramFormCode;
	}

	
	
	public String getParamPeriod()
	{
		if (paramPeriod == null) {
			KtRptPeriod pObj = rDao.getCurrentKtRptPeriod();
			if (pObj != null) {
				paramPeriod = String.valueOf(pObj.getPeriod_id());
			}else {
				paramPeriod = "1";
			}
		}
		return paramPeriod;
	}


	
	public void setParamPeriod(String paramPeriod)
	{
		this.paramPeriod = paramPeriod;
	}


	
	
	public String getParamFacDept()
	{
		return paramFacDept;
	}


	
	public void setParamFacDept(String paramFacDept)
	{
		this.paramFacDept = paramFacDept;
	}


	
	public String getParamRiNo()
	{
		return paramRiNo;
	}


	
	public void setParamRiNo(String paramRiNo)
	{
		this.paramRiNo = paramRiNo;
	}


	public void checkValid(ComponentSystemEvent event) throws IOException
	{
		paramDataLevel = getParamDataLevel();
		String message = "";
		if (getIsKtAdmin() || getIsInputKtAdmin() || getIsRdoAdmin()) {
			message = "";
		}else {
			if ((getIsCreator() == false && getIsContributor() == false) || !"M".equals(paramDataLevel)) {
				message = "You don't have access right.";	
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, getLoginUserId()+" doesn't have access right1"+", datalevel: "+paramDataLevel);
			}
		}
	}
	
	public Boolean getCanDelete()
	{
		if (canDelete == null) {
			canDelete = false;
			if (getIsCreator() && !Strings.isNullOrEmpty(paramNo)) {
				if ("M".equals(getParamDataLevel()) || "P".equals(getParamDataLevel())) {
					if (checkFormHasLv("M") == true && checkFormHasLv("C") == false) {
						canDelete = true;
					}
				}
				if ("N".equals(getParamDataLevel()) || "D".equals(getParamDataLevel())) {
					if (checkFormHasLv("N") == true && checkFormHasLv("C") == false) {
						canDelete = true;
					}
				}
				if ("C".equals(getParamDataLevel())) {
					canDelete = true;
				}
			}
		}
		return canDelete;
	}


	
	public void setCanDelete(Boolean canDelete)
	{
		this.canDelete = canDelete;
	}
	public String getParamAdmin()
	{
		return paramAdmin;
	}


	
	public void setParamAdmin(String paramAdmin)
	{
		this.paramAdmin = paramAdmin;
	}
	public Boolean getCanModifyKt()
	{
		if (canModifyKt == null) {
			canModifyKt = true;
			SecFuncLock selectedSecFuncLock = null;
			AccessDAO dao = AccessDAO.getInstance();
			if (!Strings.isNullOrEmpty(getParamFacDept())) {
				selectedSecFuncLock = dao.getSecFuncLock("EXCLUSIVE_KT", "MANAGE_KT_ACT", getParamFacDept());
			}
			if (selectedSecFuncLock != null) {
				//Y = Is Exclusive, N = Not Exclusive
				if ("Y".equals(selectedSecFuncLock.getLock_status())) {
					//Check user is extended access user
					SecFuncLockUser user = dao.getSecFuncLockUser("EXCLUSIVE_KT", "MANAGE_KT_ACT", getCurrentUserId());
					if (user != null) {
						//N = Not Extended Access User
						if ("N".equals(user.getLock_status())) {
							canModifyKt = false;
						}else {
							canModifyKt = true;
						}
					}else {
						canModifyKt = false;
					}
					if (!"Y".equals(getParamAdmin())) {
						canModifyKt = true;
					}
					//check user is rdo admin
					if (getIsRdoAdmin()) {
						canModifyKt = true;
					}
				}
				
			}
		}
		if (!canModifyKt) {
			PrimeFaces.current().executeScript("disable('editForm');");
		}
		return canModifyKt;
	}


	
	public void setCanModifyKt(Boolean canModifyKt)
	{
		this.canModifyKt = canModifyKt;
	}

	
	public List<KtForm> getFormList()
	{
		if (formList == null) {
			formList = fDao.getKtFormList();
		}
		return formList;
	}


	
	public void setFormList(List<KtForm> formList)
	{
		this.formList = formList;
	}


	public KtForm getSelectedForm()
	{
		if (selectedForm == null) {
			if (!Strings.isNullOrEmpty(getParamFormCode())) {
				selectedForm = fDao.getKtForm(paramFormCode);
				getKtFormHeader_P();
			}
		}
		return selectedForm;
	}


	
	public void setSelectedForm(KtForm selectedForm)
	{
		this.selectedForm = selectedForm;
	}

	public String updateForm()  
	{
		if (selectedForm != null) {
			FacesContext fCtx = FacesContext.getCurrentInstance();

			boolean isNew = (selectedForm.getCreationDate() == null);
			try
			{
				selectedForm.setUserstamp(getLoginUserId());
				selectedForm = fDao.updateKtForm(selectedForm);

				// Success message
				String message = (isNew) ? "msg.success.create.x" : "msg.success.update.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return "";
			}
			
		}
		return redirect("ktActFormList");
	}
	
	public Boolean getIsCreator()
	{
		if (isCreator == null) {
			isCreator = false;
			selectedFormDetails_q = getSelectedFormDetails_q();
			if (selectedFormDetails_q != null) {
				isCreator = ("Y".equals(selectedFormDetails_q.getCreator_ind()))?true:false;
				if (!Strings.isNullOrEmpty(getParamNo())) {
					KtFormDetails_Q currentCreator = fDao.getKtFormDetails_Q_creator(Integer.valueOf(getParamNo()), selectedFormDetails_q.getPk().getStaff_no());
					if (isCreator && currentCreator != null) {
						if (currentCreator.getPk().getStaff_no().equals(selectedFormDetails_q.getPk().getStaff_no())) {
							isCreator = true;
						}else {
							isCreator = false;
						}
					}
				}
			}
			if(paramNo == null) {
				isCreator = true;
			}
			if (getIsRdoAdmin()) {
				isCreator = true;
			}
			if (getIsKtAdmin()) {
				isCreator = true;
			}
			if (getIsInputKtAdmin()) {
				isCreator = true;
			}
		}
		return isCreator;
	}
	
	public void setIsCreator(Boolean isCreator)
	{
		this.isCreator = isCreator;
	}

	public Boolean getIsContributor()
	{
		if (isContributor == null) {
			isContributor = false;
			selectedFormDetails_q = getSelectedFormDetails_q();
			if (selectedFormDetails_q != null) {
				isContributor = ("N".equals(selectedFormDetails_q.getCreator_ind()))?true:false;
			}
		}
		return isContributor;
	}

	public void setIsContributor(Boolean isContributor)
	{
		this.isContributor = isContributor;
	}	


	public void setDisplyRI()
	{
		if (selectedFormDetails_q != null) {
			if (!"Y".equals(selectedFormDetails_q.getConsent_ind()))
				selectedFormDetails_q.setDisplay_ind("N");
		}
	}
	
	public Boolean hasAccessRight() 
	{
		Boolean result = false;
		if ("M".equals(paramDataLevel)){
			if (getIsCreator() || getIsContributor()) {
				result = true;
			}
		}
		if ("P".equals(paramDataLevel) && getIsKtAdmin()){
			result = true;
		}
		if ("N".equals(paramDataLevel) && (getIsKtAdmin() || getIsInputKtAdmin())){
			result = true;
		}
		if ("D".equals(paramDataLevel) && (getIsKtAdmin() || getIsInputKtAdmin())){
			result = true;
		}
		if ("C".equals(paramDataLevel) && getIsKtAdmin()){
			result = true;
		}
		return result;
	}
	
	public Boolean getHasError()
	{
		if (hasError == null) {
			hasError = false;
		}
		return hasError;
	}

	public void setHasError(Boolean hasError)
	{
		this.hasError = hasError;
	}
	
	
	public Boolean getSaved()
	{
		if (saved == null) {
			saved = false;
		}
		return saved;
	}

	
	public void setSaved(Boolean saved)
	{
		this.saved = saved;
	}
	
	
	public String gotoFormEditPage(String form_code) 
	{
		return redirect("ktActFormEdit")+"&formCode="+form_code;
	}


	
	public KtFormState_Q getSelectedFormState_q()
	{
		if (selectedFormState_q == null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedFormState_q = fDao.getKtFormState_Q(Integer.valueOf(paramNo));
			}
			if (selectedFormState_q == null) {
				selectedFormState_q = new KtFormState_Q();
				selectedFormState_q.setCreation_data_level(paramDataLevel);
				selectedFormState_q.setForm_code(getParamFormCode());
			}
		}
		return selectedFormState_q;
	}


	
	public void setSelectedFormState_q(KtFormState_Q selectedFormState_q)
	{
		this.selectedFormState_q = selectedFormState_q;
	}


	public List<KtFormDetails_P> getSelectedFormDetails_p_list()
	{
		if (selectedFormDetails_p_list == null) {
			selectedFormDetails_p_list = new ArrayList<KtFormDetails_P>();
			if (!Strings.isNullOrEmpty(getParamNo())) {
				selectedFormDetails_p_list = fDao.getKtFormDetails_P(Integer.valueOf(paramNo), getParamDataLevel());
				if (selectedFormDetails_p_list != null) {
					for (KtFormDetails_P d:selectedFormDetails_p_list) {
						if ("F".equals(d.getFlag())){
							StaffPast tmp = staffDao.getPastStaffDetailsByStaffNo(d.getStaff_no());
							if (tmp != null) {
								d.setName(tmp.getFullname_display());
							}else {
								d.setName("");
							}
						}
						if ("N".equals(d.getFlag())){
							StaffIdentity tmp = staffDao.getStaffDetailsByStaffNo(d.getStaff_no());
							if (tmp != null) {
								d.setName(tmp.getFullname_display());
							}else {
								d.setName("");
							}
						}
					}
				}
			}
			else if(!Strings.isNullOrEmpty(paramRiNo)) {
				List<ProjectDetails_P> projDtlList= projDao.getProjectDetails_P(Integer.valueOf(paramRiNo), "P");
				for(ProjectDetails_P dtl : projDtlList) {
					KtFormDetails_P tmp = new KtFormDetails_P();
					tmp.getPk().setData_level("M");
					tmp.getPk().setLine_no(dtl.getPk().getLine_no());
					tmp.setFlag(dtl.getNon_ied_staff_flag());
					tmp.setStaff_no(dtl.getInvestigator_staff_no());
					tmp.setPid(dtl.getInvestigator_person_id());
					tmp.setName(dtl.getInvestigator_name());
					selectedFormDetails_p_list.add(tmp);
				}
			}
			else {
				if (getIsAsst() || getIsRdoAdmin() || getIsKtAdmin()) {
					staffDetail = getStaffDetail(getParamPid(), null, true);
					if (staffDetail == null) {
						//check is Former Staff
						staffPastDetail = getStaffPastDetail(getParamPid(), null, true);
						if (staffPastDetail != null) {
							staffDetail = new StaffIdentity();
							staffDetail.setPid(staffPastDetail.getPid());
							staffDetail.setStaff_number(staffPastDetail.getStaff_number());
							staffDetail.setCn(staffPastDetail.getCn());
							staffDetail.setStaffType("F");
						}
					}
				}else {
					staffDetail = getStaffDetail(null, null, false);
				}
				if (getParamDataLevel().equals("M")) {
					KtFormDetails_P tmp = new KtFormDetails_P();
					if (staffDetail != null) {
				    	tmp.getPk().setData_level("M");
				    	if ("F".equals(staffDetail.getStaffType())){
				    		tmp.setFlag("F");
				    	}else {
				    		tmp.setFlag("N");
				    	}
				    	tmp.setStaff_no(String.valueOf(staffDetail.getStaff_number()));
					}
					selectedFormDetails_p_list.add(tmp);
				}
			}
		}
		return selectedFormDetails_p_list;
	}


	
	public void setSelectedFormDetails_p_list(List<KtFormDetails_P> selectedFormDetails_p_list)
	{
		this.selectedFormDetails_p_list = selectedFormDetails_p_list;
	}


	public KtFormHeader_Q getSelectedFormHeader_q()
	{
		if (selectedFormHeader_q == null) {
			if (!Strings.isNullOrEmpty(paramNo) && !Strings.isNullOrEmpty(getParamDataLevel())) {
				selectedFormHeader_q = fDao.getKtFormHeader_Q(Integer.valueOf(paramNo), getParamDataLevel());
			}
			if (selectedFormHeader_q == null) {
				selectedFormHeader_q = new KtFormHeader_Q();
				selectedFormHeader_q.getPk().setData_level(getParamDataLevel());
			}
			if ("Y".equals(selectedFormHeader_q.getInst_verified_ind()) && selectedFormHeader_q.getInst_verified_date() == null) {
				selectedFormHeader_q.setInst_verified_date(getCurrentDate());
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getInst_display_ind())) {
				selectedFormHeader_q.setInst_display_ind("Y");
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getInst_verified_ind())) {
				selectedFormHeader_q.setInst_verified_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getCdcf_status())) {
				selectedFormHeader_q.setCdcf_status("CDCF_PENDING");
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getCdcf_gen_ind())) {
				selectedFormHeader_q.setCdcf_gen_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getCdcf_processed_ind())) {
				selectedFormHeader_q.setCdcf_processed_ind("N");
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getCdcf_selected_ind())) {
				selectedFormHeader_q.setCdcf_selected_ind("U");
			}
			if (Strings.isNullOrEmpty(selectedFormHeader_q.getCdcf_changed_ind())) {
				selectedFormHeader_q.setCdcf_changed_ind("N");
			}
			if (selectedFormHeader_q.getPublish_freq() == null) {
				selectedFormHeader_q.setPublish_freq(0);
			}
		}
		return selectedFormHeader_q;
	}


	
	public void setSelectedFormHeader_q(KtFormHeader_Q selectedFormHeader_q)
	{
		this.selectedFormHeader_q = selectedFormHeader_q;
	}
	
	
	
	public KtFormDetails_Q getSelectedFormDetails_q()
	{
		if (getIsAsst() || getIsRdoAdmin() || getIsKtAdmin()) {
			staffDetail = getStaffDetail(getParamPid(), null, true);
		}else {
			staffDetail = getStaffDetail(null, null, false);
		}
		if (selectedFormDetails_q == null && staffDetail != null) {
			if (!Strings.isNullOrEmpty(paramNo)) {
				selectedFormDetails_q = fDao.getKtFormDetails_Q(Integer.valueOf(paramNo), staffDetail.getStaff_number());
			}
			if (selectedFormDetails_q == null) {
				selectedFormDetails_q = new KtFormDetails_Q();
				riCreatorStaffNo = staffDetail.getStaff_number();
				selectedFormDetails_q.getPk().setStaff_no(staffDetail.getStaff_number());
				selectedFormDetails_q.setCreator_ind("Y");
				selectedFormDetails_q.setDisplay_ind("Y");
				selectedFormDetails_q.setConsent_ind("Y");
			}
		}
		return selectedFormDetails_q;
	}


	
	public void setSelectedFormDetails_q(KtFormDetails_Q selectedFormDetails_q)
	{
		this.selectedFormDetails_q = selectedFormDetails_q;
	}


	
	public List<AwardDetails> getInv_award_awardDetails_list()
	{
		if (inv_award_awardDetails_list == null) {
			inv_award_awardDetails_list = new ArrayList<AwardDetails>();
			String title[] = null;
			String region[] = null;
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getAward_title())) {
				title = selectedFormInvAward_p.getAward_title().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getAward_title())) {
				region = selectedFormInvAward_p.getAward_type().split("_",-1);
			}
			int countTitle = (title != null)?title.length:0;
			int countRegion = (region != null)?region.length:0;
			int[] dataLength = {countTitle, countRegion};
			int max = Arrays.stream(dataLength).max().getAsInt();
			for (int i = 0; i < max; i++) {
				AwardDetails tmp = new AwardDetails();
				if (title != null) {
					if (countTitle >= max) {
						tmp.setTitle(title[i]);
					}	
				}
				if (region != null) {
					if (countRegion >= max) {
						tmp.setRegion(region[i]);
					}	
				}
				inv_award_awardDetails_list.add(tmp);
			}
			if (inv_award_awardDetails_list.size() < 1) {
				AwardDetails tmp = new AwardDetails();
				inv_award_awardDetails_list.add(tmp);
			}
		}
		return inv_award_awardDetails_list;
	}


	
	public void setInv_award_awardDetails_list(List<AwardDetails> inv_award_awardDetails_list)
	{
		this.inv_award_awardDetails_list = inv_award_awardDetails_list;
	}

	
	
	public List<PatentDetails> getInv_award_patentFiled_list()
	{
		if (inv_award_patentFiled_list == null) {
			inv_award_patentFiled_list = new ArrayList<PatentDetails>();
			String name[] = null;
			String num[] = null;
			String date[] = null;
			String country[] = null;
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_filed_name())){
				name = selectedFormInvAward_p.getPatent_filed_name().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_filed_num())){
				num = selectedFormInvAward_p.getPatent_filed_num().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_filed_date())){
				date = selectedFormInvAward_p.getPatent_filed_date().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_filed_country())){
				country = selectedFormInvAward_p.getPatent_filed_country().split("_",-1);
			}
			int countName = (name != null)?name.length:0;
			int countNum = (num != null)?num.length:0;
			int countDate = (date != null)?date.length:0;
			int countCountry = (country != null)?country.length:0;
			int[] dataLength = {countName, countNum, countDate, countCountry};
			int max = Arrays.stream(dataLength).max().getAsInt();
				for (int i = 0; i < max; i++) {
					PatentDetails tmp = new PatentDetails();				
					if (name != null) {
						if (countName >= max) {
							tmp.setName(name[i]);
						}	
					}
					if (num != null) {
						if (countNum >= max) {
							tmp.setNum(num[i]);
						}	
					}
					if (date != null) {
						if (countDate >= max) {
							tmp.setDate(date[i]);
						}	
					}
					if (country != null) {
						if (countCountry >= max) {
							tmp.setCountry(country[i]);
						}	
					}
					inv_award_patentFiled_list.add(tmp);
				}
			}
		return inv_award_patentFiled_list;
	}


	
	public void setInv_award_patentFiled_list(List<PatentDetails> inv_award_patentFiled_list)
	{
		this.inv_award_patentFiled_list = inv_award_patentFiled_list;
	}


	
	public List<PatentDetails> getInv_award_patentGranted_list()
	{
		if (inv_award_patentGranted_list == null) {
			inv_award_patentGranted_list = new ArrayList<PatentDetails>();
			String name[] = null;
			String num[] = null;
			String date[] = null;
			String country[] = null;
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_granted_name())){
				name = selectedFormInvAward_p.getPatent_granted_name().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_granted_num())){
				num = selectedFormInvAward_p.getPatent_granted_num().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_granted_date())){
				date = selectedFormInvAward_p.getPatent_granted_date().split("_",-1);
			}
			if (!Strings.isNullOrEmpty(selectedFormInvAward_p.getPatent_granted_country())){
				country = selectedFormInvAward_p.getPatent_granted_country().split("_",-1);
			}
			int countName = (name != null)?name.length:0;
			int countNum = (num != null)?num.length:0;
			int countDate = (date != null)?date.length:0;
			int countCountry = (country != null)?country.length:0;
			int[] dataLength = {countName, countNum, countDate, countCountry};
			int max = Arrays.stream(dataLength).max().getAsInt();
				for (int i = 0; i < max; i++) {
					PatentDetails tmp = new PatentDetails();				
					if (name != null) {
						if (countName >= max) {
							tmp.setName(name[i]);
						}	
					}
					if (num != null) {
						if (countNum >= max) {
							tmp.setNum(num[i]);
						}	
					}
					if (date != null) {
						if (countDate >= max) {
							tmp.setDate(date[i]);
						}	
					}
					if (country != null) {
						if (countCountry >= max) {
							tmp.setCountry(country[i]);
						}	
					}
					inv_award_patentGranted_list.add(tmp);
				}
			}
		return inv_award_patentGranted_list;
	}


	
	public void setInv_award_patentGranted_list(List<PatentDetails> inv_award_patentGranted_list)
	{
		this.inv_award_patentGranted_list = inv_award_patentGranted_list;
	}


	public void save() throws Exception
	{
		if (getSelectedFormState_q() != null) {
			hasError = false;
			updateKtFormProjectDays();
			if ("M".equals(getParamDataLevel())) {
				save_m(false, true);
			}
			if ("P".equals(getParamDataLevel())) {
				save_p();
			}
			if ("N".equals(getParamDataLevel())) {
				save_n(false, true);
			}
			if ("D".equals(getParamDataLevel())) {
				save_d();
			}
			if ("C".equals(getParamDataLevel())) {
				save_c();
			}
		}
	}
	
	//save and publish
	public void saveAndPublishForm() throws IOException
	{
		if (getSelectedFormState_q() != null) {
			hasError = false;
			updateKtFormProjectDays();
			if ("M".equals(getParamDataLevel())) {
				save_m(true, true);
				publish();
			}
			if ("N".equals(getParamDataLevel())) {
				save_n(true, true);
				publish();
			}
		}
	}
		
	//M level
	public void save_m(boolean doPublish, Boolean doValidation)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		saved = false;
		try {
			selectedFormState_q.setUserstamp(getLoginUserId());
			
			selectedFormHeader_q.setPublish_status("MODIFIED");
			selectedFormHeader_q.setLast_modified_by(getLoginUserId());
			selectedFormHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));

			selectedFormHeader_q.setUserstamp(getLoginUserId());
			selectedFormDetails_q.setUserstamp(getLoginUserId());

			//Record still can be saved even there is error
			if (doValidation) {
				validateRequiredField();
				validateDate();
			}

			if (validateFormDetails_P(staffDetail.getStaff_number(), doValidation)) {

				//Update Q
				selectedFormState_q = fDao.updateEntity(selectedFormState_q);

				int currentFormNo = selectedFormState_q.getForm_no();
				selectedFormHeader_q.getPk().setForm_no(currentFormNo);
				selectedFormHeader_q = fDao.updateEntity(selectedFormHeader_q);
				selectedFormDetails_q.getPk().setForm_no(currentFormNo);
				selectedFormDetails_q = fDao.updateEntity(selectedFormDetails_q);
				
				//Update P
				updateKtFormHeader_P(currentFormNo, "M");
				
				//delete all contributor in M levels
				fDao.deleteKtFormDetails_P(currentFormNo, "M");
				
				//Update form name list
				int line_no = 1;
				for(KtFormDetails_P p:selectedFormDetails_p_list) {
					p.getPk().setForm_no(currentFormNo);
					p.getPk().setData_level("M");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getFlag().equals("N") && p.getStaff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setName(nList.get(0));
						}
					}
					//set past staff details
					if (p.getFlag().equals("F") && p.getName() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getFullname_save().equals(p.getName()))
								.map(x -> x.getStaff_number())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setStaff_no(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					fDao.updateEntity(p);
					
					//Create record in formDetails_q if has staff number
					if (p.getStaff_no() != null) {
						KtFormDetails_Q tmpDetailsQ = fDao.getKtFormDetails_Q(currentFormNo, p.getStaff_no());
						if (tmpDetailsQ == null) {
							KtFormDetails_Q newDetailsQ = new KtFormDetails_Q();
							newDetailsQ.getPk().setForm_no(currentFormNo);
							newDetailsQ.getPk().setStaff_no(p.getStaff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							fDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;		
				}
				
				// Success message
				String message = "msg.success.save.x";
				if (doValidation == false) {
					message = "msg.success.duplicate.x";
				}
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				saved = true;
				if (!doPublish) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "ktForm.xhtml?pid="+paramPid+"&no="+selectedFormHeader_q.getPk().getForm_no()+"&dataLevel=M&form="+paramFormCode;
					if (doValidation == false) {
						redirectLink = "manageKtForm.xhtml?pid="+paramPid+"&form=" + paramFormCode+"&period="+getParamPeriod();
					}
			    	eCtx.redirect(redirectLink);
				}
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", e);
		}
			
	}
	
	//P level
	public void save_p() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try
		{
			if ("Y".equals(selectedFormHeader_q.getInst_verified_ind()) && selectedFormHeader_q.getInst_verified_date() == null) {
				selectedFormHeader_q.setInst_verified_date(getCurrentDate());
			}

			selectedFormHeader_q.setUserstamp(getLoginUserId());
						
			// Success message
			String message = "msg.success.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));

		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", e);
		}
	}	
	
	//C level
	public void save_c() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		boolean doValidation = true;
		try {
			validateRequiredField();
			
			if (validateFormDetails_P(staffDetail.getStaff_number(), doValidation) && !getHasError()) {
								
				
				if ("Y".equals(selectedFormHeader_q.getInst_verified_ind()) && selectedFormHeader_q.getInst_verified_date() == null) {
					selectedFormHeader_q.setInst_verified_date(getCurrentDate());
				}
				
				if ("CDCF_PENDING".equals(selectedFormHeader_q.getCdcf_status())) {
					selectedFormHeader_q.setCdcf_selected_ind("U");
				}
				if ("CDCF_PROCESSED".equals(selectedFormHeader_q.getCdcf_status())) {
					selectedFormHeader_q.setCdcf_processed_ind("Y");
					selectedFormHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedFormHeader_q.setCdcf_selected_ind("Y");
				}
				if ("CDCF_NOT_SEL".equals(selectedFormHeader_q.getCdcf_status())) {
					selectedFormHeader_q.setCdcf_processed_ind("Y");
					selectedFormHeader_q.setCdcf_processed_date(getCurrentDate());
					selectedFormHeader_q.setCdcf_selected_ind("N");
				}
				
				selectedFormHeader_q.setUserstamp(getLoginUserId());
				
				//Update P, Q
				selectedFormHeader_q = fDao.updateEntity(selectedFormHeader_q);
				
				
				int currentFormNo = selectedFormHeader_q.getPk().getForm_no();
				updateKtFormHeader_P(currentFormNo, "C");
				
				//delete all contributor in N levels
				fDao.deleteKtFormDetails_P(currentFormNo, "C");
				
				//Update form name list
				if (selectedFormDetails_p_list != null) {
					int line_no = 1;
					for(KtFormDetails_P p:selectedFormDetails_p_list) {
						p.getPk().setForm_no(currentFormNo);
						p.getPk().setData_level("C");
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getFlag().equals("N") && p.getStaff_no() != null) {
							staffNameList = getStaffNameList();
							List<String> nList = staffNameList.stream()
									.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
									.map(x -> x.getFullname_save())
									.collect(Collectors.toList());
							if (!nList.isEmpty()) {
								p.setName(nList.get(0));
							}
						}
						//set past staff details
						if (p.getFlag().equals("F") && p.getName() != null) {
							staffPastList = getStaffPastList();
							List<String> fList = staffPastList.stream()
									.filter(x -> x.getFullname_save().equals(p.getName()))
									.map(x -> x.getStaff_number())
									.collect(Collectors.toList());
							if (!fList.isEmpty()) {
								p.setStaff_no(fList.get(0));
							}
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						fDao.updateEntity(p);
						
						//Create record in formDetails_q if has staff number
						if (p.getStaff_no() != null) {
							KtFormDetails_Q tmpDetailsQ = fDao.getKtFormDetails_Q(currentFormNo, p.getStaff_no());
							if (tmpDetailsQ == null) {
								KtFormDetails_Q newDetailsQ = new KtFormDetails_Q();
								newDetailsQ.getPk().setForm_no(currentFormNo);
								newDetailsQ.getPk().setStaff_no(p.getStaff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								fDao.updateEntity(newDetailsQ);
							}
						}
						line_no++;		
					}
				}
				
				// update importStatus if it is imported
				updateSelectedImportStatus(currentFormNo);
				
				// Success message
				String message;
				if ("CDCF_GENERATED".equals(selectedFormHeader_q.getCdcf_status()) || "CDCF_PROCESSED".equals(selectedFormHeader_q.getCdcf_status())) {
					message = "msg.success.save.generate.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "KT Form");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "ktform.xhtml?no="+currentFormNo+"&dataLevel=C&form="+paramFormCode;
			    	eCtx.redirect(redirectLink);
				}else {
					message = "msg.success.save.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "KT Form");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}
				
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			}
		}catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  KT Activity Form (kt_no=" + selectedFormHeader_q.getPk().getForm_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot save  KT Activity Form (kt_no=" + selectedFormHeader_q.getPk().getForm_no() + ")", e);
		}
	}	
	
	public void publish()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		if (selectedFormHeader_q != null && selectedFormDetails_q != null) {
			try {
				//Get C level header
				if (getParamNo() != null) {
					/*AwardHeader_P selectedAwardHeader_p_c = fDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
					if (selectedAwardHeader_p_c != null) {
						selectedFormHeader_q.setCdcf_changed_ind("Y");
					}*/
				}
				String publishDataLevel = "P";
				if ("N".equals(getParamDataLevel()) || "D".equals(getParamDataLevel())) {
					publishDataLevel = "D";
				}
				selectedFormHeader_q.setPublish_status("PUBLISHED");

				int publishFreq = (selectedFormHeader_q.getPublish_freq() != null)?selectedFormHeader_q.getPublish_freq()+1:1;
				selectedFormHeader_q.setPublish_freq(publishFreq);
				selectedFormHeader_q.setLast_modified_by(getLoginUserId());
				selectedFormHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));
				selectedFormHeader_q.setLast_published_by(getLoginUserId());
				selectedFormHeader_q.setLast_published_date(Timestamp.from(Instant.now()));
				
				selectedFormDetails_q.setUserstamp(getLoginUserId());
				
				if (!getHasError()) {	
					//Update Header_q
					selectedFormHeader_q = fDao.updateEntity(selectedFormHeader_q);

					int currentFormNo = selectedFormHeader_q.getPk().getForm_no();
					
					selectedFormDetails_q.getPk().setForm_no(currentFormNo);
					//Update Details_q
					selectedFormDetails_q = fDao.updateEntity(selectedFormDetails_q);

					//Update P
					updateKtFormHeader_P(currentFormNo, publishDataLevel);
					
					//delete all contributor in P levels
					fDao.deleteKtFormDetails_P(currentFormNo, publishDataLevel);

					//Update form name list
					int line_no = 1;
					for(KtFormDetails_P p:selectedFormDetails_p_list) {
						p.getPk().setForm_no(currentFormNo);
						p.getPk().setData_level(publishDataLevel);
						p.getPk().setLine_no(line_no);
						//set staff details
						if (p.getFlag().equals("N") && p.getStaff_no() != null) {
							staffNameList = getStaffNameList();
							List<String> nList = staffNameList.stream()
									.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
									.map(x -> x.getFullname_save())
									.collect(Collectors.toList());
							if (!nList.isEmpty()) {
								p.setName(nList.get(0));
							}
						}
						//set past staff details
						if (p.getFlag().equals("F") && p.getName() != null) {
							staffPastList = getStaffPastList();
							List<String> fList = staffPastList.stream()
									.filter(x -> x.getFullname_save().equals(p.getName()))
									.map(x -> x.getStaff_number())
									.collect(Collectors.toList());
							if (!fList.isEmpty()) {
								p.setStaff_no(fList.get(0));
							}
						}
						p.setCreator(getLoginUserId());
						p.setUserstamp(getLoginUserId());
						fDao.updateEntity(p);
						
						//Create record in formDetails_q if has staff number
						if (p.getStaff_no() != null) {
							KtFormDetails_Q tmpDetailsQ = fDao.getKtFormDetails_Q(currentFormNo, p.getStaff_no());
							if (tmpDetailsQ == null) {
								KtFormDetails_Q newDetailsQ = new KtFormDetails_Q();
								newDetailsQ.getPk().setForm_no(currentFormNo);
								newDetailsQ.getPk().setStaff_no(p.getStaff_no());
								newDetailsQ.setCreator_ind("N");
								newDetailsQ.setDisplay_ind("N");
								newDetailsQ.setConsent_ind("U");
								newDetailsQ.setCreator(getLoginUserId());
								newDetailsQ.setUserstamp(getLoginUserId());
								fDao.updateEntity(newDetailsQ);
							}
						}
						line_no++;		
					}


					// Success message
					String message = "msg.success.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				}else {
					// Failed message
					String message = "msg.err.data.publish.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				}
				if (saved) {
					//append no. and data level in url if first time saved the ri
					ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
					String redirectLink = "ktForm.xhtml?pid="+paramPid+"&no="+selectedFormHeader_q.getPk().getForm_no()+"&dataLevel="+getParamDataLevel()+"&form="+paramFormCode+"&period="+getParamPeriod()+"&facDept="+getParamFacDept();
			    	eCtx.redirect(redirectLink);
				}
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot save and submit KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", e);
			}
		}
	}
	
	public void submitConsent()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		if (selectedFormDetails_q != null) {
			try {
				selectedFormDetails_q.setUserstamp(getLoginUserId());
				selectedFormDetails_q = fDao.updateEntity(selectedFormDetails_q);
					
					// Success message
					String message = "msg.success.submit.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "Consent");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedFormDetails_q + ")", ole);
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot submit consent (" + selectedFormDetails_q + ")", e);
			}
		}
	}	
	
	
	//N level
	public void save_n(boolean doPublish, boolean doValidation)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		saved = false;
		try {
			selectedFormState_q.setUserstamp(getLoginUserId());
			
			selectedFormHeader_q.setPublish_status("MODIFIED");
			selectedFormHeader_q.setLast_modified_by(getLoginUserId());
			selectedFormHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));

			selectedFormHeader_q.setUserstamp(getLoginUserId());
			selectedFormDetails_q.setUserstamp(getLoginUserId());

			//Record still can be saved even there is error
			if (doValidation) {
				validateRequiredField();
				validateDate();
			}
			//Update Q
			selectedFormState_q = fDao.updateEntity(selectedFormState_q);

			int currentFormNo = selectedFormState_q.getForm_no();
			selectedFormHeader_q.getPk().setForm_no(currentFormNo);
			selectedFormHeader_q = fDao.updateEntity(selectedFormHeader_q);
			selectedFormDetails_q.getPk().setForm_no(currentFormNo);
			selectedFormDetails_q = fDao.updateEntity(selectedFormDetails_q);
				
			//Update P
			updateKtFormHeader_P(currentFormNo, "N");
				
			//delete all contributor in N levels
			fDao.deleteKtFormDetails_P(currentFormNo, "N");

			//Update form name list
			if (selectedFormDetails_p_list != null) {
				int line_no = 1;
				for(KtFormDetails_P p:selectedFormDetails_p_list) {
					p.getPk().setForm_no(currentFormNo);
					p.getPk().setData_level("N");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getFlag().equals("N") && p.getStaff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setName(nList.get(0));
						}
					}
					//set past staff details
					if (p.getFlag().equals("F") && p.getName() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getFullname_save().equals(p.getName()))
								.map(x -> x.getStaff_number())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setStaff_no(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					fDao.updateEntity(p);
					
					//Create record in formDetails_q if has staff number
					if (p.getStaff_no() != null) {
						KtFormDetails_Q tmpDetailsQ = fDao.getKtFormDetails_Q(currentFormNo, p.getStaff_no());
						if (tmpDetailsQ == null) {
							KtFormDetails_Q newDetailsQ = new KtFormDetails_Q();
							newDetailsQ.getPk().setForm_no(currentFormNo);
							newDetailsQ.getPk().setStaff_no(p.getStaff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							fDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;		
				}
			}
			
			// Success message
			String message = "msg.success.save.x";
			if (doValidation == false) {
				message = "msg.success.duplicate.x";
			}
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	    	saved = true;
			if (!doPublish) {
				ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
				String redirectLink = "ktForm.xhtml?pid="+paramPid+"&no="+selectedFormHeader_q.getPk().getForm_no()+"&dataLevel=N&form="+paramFormCode+"&period="+getParamPeriod()+"&facDept="+getParamFacDept();
				if (doValidation == false) {
					redirectLink = "manageKtForm.xhtml?form=" + paramFormCode+"&period="+getParamPeriod()+"&admin=Y&facDept="+getParamFacDept();
				}
				eCtx.redirect(redirectLink);
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", e);
		}
	}
		
	//D level
	public void save_d()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		try {			
			selectedFormHeader_q.setPublish_status("PUBLISHED");
			selectedFormHeader_q.setLast_modified_by(getLoginUserId());
			selectedFormHeader_q.setLast_modified_date(Timestamp.from(Instant.now()));

			selectedFormHeader_q.setUserstamp(getLoginUserId());
			selectedFormDetails_q.setUserstamp(getLoginUserId());

			//Record still can be saved even there is error
			validateRequiredField();
			validateDate();
			//Update Q
			int currentFormNo = selectedFormState_q.getForm_no();
			selectedFormHeader_q.getPk().setForm_no(currentFormNo);
			selectedFormHeader_q = fDao.updateEntity(selectedFormHeader_q);
			selectedFormDetails_q.getPk().setForm_no(currentFormNo);
			selectedFormDetails_q = fDao.updateEntity(selectedFormDetails_q);
				
			//Update P
			updateKtFormHeader_P(currentFormNo, "D");
				
			//delete all contributor in D levels
			fDao.deleteKtFormDetails_P(currentFormNo, "D");

			//Update form name list
			if (selectedFormDetails_p_list != null) {
				int line_no = 1;
				for(KtFormDetails_P p:selectedFormDetails_p_list) {
					p.getPk().setForm_no(currentFormNo);
					p.getPk().setData_level("D");
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getFlag().equals("N") && p.getStaff_no() != null) {
						staffNameList = getStaffNameList();
						List<String> nList = staffNameList.stream()
								.filter(x -> x.getStaff_number().equals(p.getStaff_no()))
								.map(x -> x.getFullname_save())
								.collect(Collectors.toList());
						if (!nList.isEmpty()) {
							p.setName(nList.get(0));
						}
					}
					//set past staff details
					if (p.getFlag().equals("F") && p.getName() != null) {
						staffPastList = getStaffPastList();
						List<String> fList = staffPastList.stream()
								.filter(x -> x.getFullname_save().equals(p.getName()))
								.map(x -> x.getStaff_number())
								.collect(Collectors.toList());
						if (!fList.isEmpty()) {
							p.setStaff_no(fList.get(0));
						}
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					fDao.updateEntity(p);
					
					//Create record in formDetails_q if has staff number
					if (p.getStaff_no() != null) {
						KtFormDetails_Q tmpDetailsQ = fDao.getKtFormDetails_Q(currentFormNo, p.getStaff_no());
						if (tmpDetailsQ == null) {
							KtFormDetails_Q newDetailsQ = new KtFormDetails_Q();
							newDetailsQ.getPk().setForm_no(currentFormNo);
							newDetailsQ.getPk().setStaff_no(p.getStaff_no());
							newDetailsQ.setCreator_ind("N");
							newDetailsQ.setDisplay_ind("N");
							newDetailsQ.setConsent_ind("U");
							newDetailsQ.setCreator(getLoginUserId());
							newDetailsQ.setUserstamp(getLoginUserId());
							fDao.updateEntity(newDetailsQ);
						}
					}
					line_no++;		
				}
			}
			
			// Success message
			String message = "msg.success.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
			String redirectLink = "ktForm.xhtml?pid="+paramPid+"&no="+selectedFormHeader_q.getPk().getForm_no()+"&dataLevel=N&form="+paramFormCode;
	    	eCtx.redirect(redirectLink);

		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save  KT Activity Form (form_no=" + selectedFormHeader_q.getPk().getForm_no() + ", staff_no="+ selectedFormDetails_q.getPk().getStaff_no() + ")", e);
		}
	}
		
	public String takeSnapshot()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String destUrl = "";
		if (selectedFormDetails_p_list != null && selectedFormHeader_q != null) {
			try {
				int currentFormNo = selectedFormHeader_q.getPk().getForm_no();
				takeSnapshotFormHeaderP(currentFormNo, "C");

				fDao.deleteKtFormDetails_P(currentFormNo, "C");
				for (KtFormDetails_P p:selectedFormDetails_p_list) {
					KtFormDetails_P p2 = p;
					p2.getPk().setData_level("C");
					p2.setCreator(getLoginUserId());
					p2.setUserstamp(getLoginUserId());
					fDao.updateEntity(p2);
				}
				
				// Success message
				String message = "msg.success.create.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "Snapshot");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				destUrl = redirect("ktForm") + "&no=" + selectedFormHeader_q.getPk().getForm_no() + "&dataLevel=C&form="+paramFormCode;	
			}catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Form No.:" + selectedFormHeader_q.getPk().getForm_no() + ")", ole);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.WARNING, "Cannot take Snapshot (Form No.:" + selectedFormHeader_q.getPk().getForm_no() + ")", e);
			}
		}
		return redirect(destUrl);
	}		
	
	public boolean takeSnapshotFormHeaderP(int currentFormNo, String dataLevel)
	{
		boolean result = false;
		switch(paramFormCode) {
			case SysParam.PARAM_KT_FORM_CPD:
				if (currentFormNo > 0) {
					KtFormCPD_P tmp_c = fDao.getKtFormCPD_P(currentFormNo, dataLevel, null, null, 0);
					if (tmp_c != null) {
						fDao.deleteEntity(KtFormCPD_P.class, tmp_c.getPk());
					}
					tmp_c = selectedFormCPD_p;
					tmp_c.getPk().setData_level("C");
					tmp_c.setUserstamp(getLoginUserId());
					tmp_c.setCreator(getLoginUserId());
					tmp_c = fDao.updateEntity(tmp_c);
				}
				break;
			case "":
				break;
		}
		return result;
	}
	
	public boolean checkFormHasLv(String dataLevel)
	{
		boolean result = false;
		switch(paramFormCode) {
			case SysParam.PARAM_KT_FORM_CPD:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormCPD_P tmp = fDao.getKtFormCPD_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_PROF_CONF:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormProfConf_P tmp = fDao.getKtFormProfConf_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_SEM:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormSem_P tmp = fDao.getKtFormSem_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_CNT_PROJ:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormCntProj_P tmp = fDao.getKtFormCntProj_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_INN:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormInn_P tmp = fDao.getKtFormInn_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_CONS:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormCons_P tmp = fDao.getKtFormCons_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_PROF_ENGMT:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormProfEngmt_P tmp = fDao.getKtFormProfEngmt_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_IP:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormIP_P tmp = fDao.getKtFormIP_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_SOC_ENGMT:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormSocEngmt_P tmp = fDao.getKtFormSocEngmt_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormStaffEngmt_P tmp = fDao.getKtFormStaffEngmt_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_EA:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormEA_P tmp = fDao.getKtFormEA_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_STARTUP:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormStartup_P tmp = fDao.getKtFormStartup_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case SysParam.PARAM_KT_FORM_INV_AWARD:
				if (!Strings.isNullOrEmpty(paramNo)) {
					KtFormInvAward_P tmp = fDao.getKtFormInvAward_P(Integer.valueOf(paramNo), dataLevel, null, null, 0);
					if (tmp != null) {
						result = true;
					}
				}
				break;
			case "":
				break;
		}
		return result;
	}
	
	private boolean validateFormDetails_P(String staff_no, Boolean doValidation)
	{
		if (doValidation == false) {
			return true;
		}
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String errMessage = "msg.err.mandatory.x";
		boolean result = true;
		boolean yourself = false;
		String message;
		String allMessage = "";
		int countAuthor = 0;
		HashSet unique=new HashSet();
		if (!Strings.isNullOrEmpty(staff_no)) {
			for (KtFormDetails_P p:selectedFormDetails_p_list) {
				
				if (!Strings.isNullOrEmpty(p.getStaff_no())) {
					if (staff_no.equals(p.getStaff_no())) {
						yourself = true;
					}
				}

				//get staff details
				if (p.getFlag().equals("N")) {
					if (p.getStaff_no() == null) {
						result = false;
						int lineNo = countAuthor + 1;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "contributor (no. "+lineNo+")");
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getStaff_no());
						if (s != null) {
							p.setName(s.getFullname_save());
							p.setPid(s.getPid());
						}		
					}
				}
				//get past staff details
				if (p.getFlag().equals("F")) {
					int lineNo = countAuthor + 1;
					if (p.getName() == null) {
						result = false;
						message = "Contributor (no. "+lineNo+") is not correct.";
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						String authorshipName = p.getName().toUpperCase();
						authorshipName = authorshipName.replace(",", "");
						boolean hasChinese = containsHanScript(authorshipName);
						StaffPast sp = staffDao.getPastStaffDetailsByStaffName(authorshipName, hasChinese);
						if (sp != null) {
							//p.setName(sp.getFullname());
							p.setPid(sp.getPid());
							p.setStaff_no(sp.getStaff_number());
							p.setName(sp.getFullname_save());
						}else {
							result = false;
							message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
				}
				
				//check name is not null
				if (p.getFlag().equals("Y") || p.getFlag().equals("S")) {
					if (Strings.isNullOrEmpty(p.getName())) {
						result = false;
						int lineNo = countAuthor + 1;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						p.setPid(null);
						p.setStaff_no(null);
					}
				}
				
				//check duplicate
				if (!unique.add(p.getStaff_no()) && p.getStaff_no() != null){
					result = false;
					if (!Strings.isNullOrEmpty(p.getName())) {
						message = "Contributor - Staff ("+p.getName()+") cannot be duplicated.";
						allMessage += "- Contributor - Staff ("+p.getName()+") cannot be duplicated.<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						message = "Contributor cannot be duplicated.";
						allMessage += "- Contributor cannot be duplicated.<br/>";
						fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
				}
				countAuthor++;
			}
		}
		if (yourself == false && "M".equals(getParamDataLevel())) {		
			result = false;
			message = "You must be one of the contributor.";
			allMessage += "- You must be one of the contributor.<br/>";
			fCtx.addMessage("editForm:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, allMessage));
		}
		return result;
	}
	
	public void deleteFormByLv() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		String redirectLink = "manageKtForm.xhtml?pid="+paramPid+"&form=" + paramFormCode+"&period="+getParamPeriod();
		Boolean hasCDataLv = checkFormHasLv("C");
		Boolean deleted_header_p = false;
		Boolean deleted_success = false;
		List<String> dataLvList = new ArrayList<>();
		if ("M".equals(getParamDataLevel()) || "P".equals(getParamDataLevel())) {
			dataLvList.add("P");
			dataLvList.add("M");
		}
		if ("N".equals(getParamDataLevel()) || "D".equals(getParamDataLevel())) {
			dataLvList.add("D");
			dataLvList.add("N");
			redirectLink += "&admin=Y";
			if(!Strings.isNullOrEmpty(getParamFacDept())) {
				redirectLink += "&facDept="+getParamFacDept();
			}
			
		}
		if ("C".equals(getParamDataLevel())) {
			dataLvList.add("C");
			dataLvList.add("D");
			dataLvList.add("N");
			dataLvList.add("P");
			dataLvList.add("M");
			redirectLink += "&admin=Y";
			if(!Strings.isNullOrEmpty(getParamFacDept())) {
				redirectLink += "&facDept="+getParamFacDept();
			}
		}
		try {
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormCPD_p = fDao.getKtFormCPD_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormCPD_p != null) {
								fDao.deleteEntity(KtFormCPD_P.class, selectedFormCPD_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormProfConf_p = fDao.getKtFormProfConf_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormProfConf_p != null) {
								fDao.deleteEntity(KtFormProfConf_P.class, selectedFormProfConf_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_SEM:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormSem_p = fDao.getKtFormSem_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormSem_p != null) {
								fDao.deleteEntity(KtFormSem_P.class, selectedFormSem_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormCntProj_p = fDao.getKtFormCntProj_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormCntProj_p != null) {
								fDao.deleteEntity(KtFormCntProj_P.class, selectedFormCntProj_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_INN:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormInn_p = fDao.getKtFormInn_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormInn_p != null) {
								fDao.deleteEntity(KtFormInn_P.class, selectedFormInn_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_CONS:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormCons_p = fDao.getKtFormCons_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormCons_p != null) {
								fDao.deleteEntity(KtFormCons_P.class, selectedFormCons_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormProfEngmt_p = fDao.getKtFormProfEngmt_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormProfEngmt_p != null) {
								fDao.deleteEntity(KtFormProfEngmt_P.class, selectedFormProfEngmt_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_IP:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormIP_p = fDao.getKtFormIP_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormIP_p != null) {
								fDao.deleteEntity(KtFormIP_P.class, selectedFormIP_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormSocEngmt_p = fDao.getKtFormSocEngmt_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormSocEngmt_p != null) {
								fDao.deleteEntity(KtFormSocEngmt_P.class, selectedFormSocEngmt_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormStaffEngmt_p = fDao.getKtFormStaffEngmt_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormStaffEngmt_p != null) {
								fDao.deleteEntity(KtFormStaffEngmt_P.class, selectedFormStaffEngmt_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_EA:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormEA_p = fDao.getKtFormEA_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormEA_p != null) {
								fDao.deleteEntity(KtFormEA_P.class, selectedFormEA_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_STARTUP:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormStartup_p = fDao.getKtFormStartup_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormStartup_p != null) {
								fDao.deleteEntity(KtFormStartup_P.class, selectedFormStartup_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case SysParam.PARAM_KT_FORM_INV_AWARD:
					if (canProcessDataLevelC()) {
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormInvAward_p = fDao.getKtFormInvAward_P(Integer.valueOf(paramNo), dataLvList.get(i), null, null, 0);
							if (selectedFormInvAward_p != null) {
								fDao.deleteEntity(KtFormInvAward_P.class, selectedFormInvAward_p.getPk());
							}
						}
						deleted_header_p = true;
					}
					break;
				case "":
					break;
			}
			
			if (deleted_header_p) {
				for(int i = 0; i < dataLvList.size(); i++) {
					fDao.deleteKtFormDetails_P(Integer.valueOf(paramNo), dataLvList.get(i));
					fDao.deleteKtFormHeader_Q(Integer.valueOf(paramNo), dataLvList.get(i));
				}
				deleted_success = true;
			}
			if (deleted_success) {
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}else {
				String message = "KT Activity Form can not be deleted. ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
			ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
			
	    	eCtx.redirect(redirectLink);
		}
		catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot delete KT Activity Form (No.: " + selectedFormHeader_q.getPk().getForm_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot delete KT Activity Form (No.: " + selectedFormHeader_q.getPk().getForm_no() + ")", e);
		}		
	}

	public void deleteForm() 
	{
		if (!Strings.isNullOrEmpty(paramNo) && !Strings.isNullOrEmpty(paramFormCode)) {
			deleteFormByLv();
		}
	}

	public void deleteFormInList(String pid, String formNo, String formCode, String periodId, String dataLevel, String selectedFacDept) 
	{
		paramPid = pid;
		paramNo = formNo;
		paramFormCode = formCode;
		paramPeriod = periodId;
		paramDataLevel = dataLevel;
		paramFacDept = selectedFacDept;
		if (!Strings.isNullOrEmpty(paramNo) && !Strings.isNullOrEmpty(paramFormCode)) {
			deleteFormByLv();
		}
	}
	
	public String getRiCreatorStaffNo()
	{
		if (Strings.isNullOrEmpty(riCreatorStaffNo)) {
			KtFormDetails_Q tmp = fDao.getKtFormDetails_Q_creator(Integer.valueOf(paramNo), null);
			riCreatorStaffNo = (tmp!= null)?tmp.getPk().getStaff_no():getCurrentUserId();
		}
		return riCreatorStaffNo;
	}
	
	public void setRiCreatorStaffNo(String riCreatorStaffNo)
	{
		this.riCreatorStaffNo = riCreatorStaffNo;
	}

	public boolean isRiCreator(String staff_no) 
	{
		boolean result = false;
		if (!Strings.isNullOrEmpty(staff_no) && !Strings.isNullOrEmpty(getRiCreatorStaffNo())) {
			result = (riCreatorStaffNo.equals(staff_no))?true:false;
		}
		return result;
	}	
	
	
	public boolean checkSnapshotExists() 
	{
		boolean result = false;
		/*AwardHeader_P tmp = fDao.getAwardHeader_P(Integer.valueOf(paramNo), "C");
		if (tmp != null) {
			result = true;
		}*/
		return result;	
	}
	
	public List<SelectItem> getCdcfStatusList()
	{
		cdcfStatusList = new ArrayList<SelectItem>();
		
		cdcfStatusList.add(optionPending);
		if ("CDCF_PENDING".equals(selectedFormHeader_q.getCdcf_status()))
				cdcfStatusList.add(optionProcessed);
		if (!"CDCF_PENDING".equals(selectedFormHeader_q.getCdcf_status()))
			cdcfStatusList.add(optionGenerated);
		cdcfStatusList.add(optionNotSelected);
		return cdcfStatusList;
	}
	
	public void setCdcfStatusList(List<SelectItem> cdcfStatusList)
	{
		this.cdcfStatusList = cdcfStatusList;
	}
	
	public void getKtFormHeader_P()
	{
		if (!Strings.isNullOrEmpty(paramFormCode)) {
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormCPD_p = fDao.getKtFormCPD_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormCPD_p == null){
						selectedFormCPD_p = new KtFormCPD_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormProfConf_p = fDao.getKtFormProfConf_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormProfConf_p == null){
						selectedFormProfConf_p = new KtFormProfConf_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_SEM:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormSem_p = fDao.getKtFormSem_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormSem_p == null){
						selectedFormSem_p = new KtFormSem_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormCntProj_p = fDao.getKtFormCntProj_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					else if (!Strings.isNullOrEmpty(paramRiNo)) {
						selectedFormCntProj_p = new KtFormCntProj_P();
						ProjectHeader_P projP = projDao.getProjectHeader_P(Integer.valueOf(paramRiNo), "P");
						String title = projP.getTitle_1() != null ? projP.getTitle_1() : "";
						title += projP.getTitle_2() != null ? projP.getTitle_2() : "";
						title += projP.getTitle_3() != null ? projP.getTitle_3() : "";
						title += projP.getTitle_4() != null ? projP.getTitle_4() : "";
						if(title.length() > 500) title.subSequence(0, 500);
						selectedFormCntProj_p.setTitle(title);
					}
					if (selectedFormCntProj_p == null){
						selectedFormCntProj_p = new KtFormCntProj_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_INN:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormInn_p = fDao.getKtFormInn_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					else if (!Strings.isNullOrEmpty(paramRiNo)) {
						selectedFormInn_p = new KtFormInn_P();
						ProjectHeader_P projP = projDao.getProjectHeader_P(Integer.valueOf(paramRiNo), "P");
						String title = projP.getTitle_1() != null ? projP.getTitle_1() : "";
						title += projP.getTitle_2() != null ? projP.getTitle_2() : "";
						title += projP.getTitle_3() != null ? projP.getTitle_3() : "";
						title += projP.getTitle_4() != null ? projP.getTitle_4() : "";
						if(title.length() > 500) title.subSequence(0, 500);
						selectedFormInn_p.setTitle(title);
					}
					if (selectedFormInn_p == null){
						selectedFormInn_p = new KtFormInn_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_CONS:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormCons_p = fDao.getKtFormCons_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					else if (!Strings.isNullOrEmpty(paramRiNo)) {
						selectedFormCons_p = new KtFormCons_P();
						ProjectHeader_P projP = projDao.getProjectHeader_P(Integer.valueOf(paramRiNo), "P");
						String title = projP.getTitle_1() != null ? projP.getTitle_1() : "";
						title += projP.getTitle_2() != null ? projP.getTitle_2() : "";
						title += projP.getTitle_3() != null ? projP.getTitle_3() : "";
						title += projP.getTitle_4() != null ? projP.getTitle_4() : "";
						if(title.length() > 500) title.subSequence(0, 500);
						selectedFormCons_p.setTitle(title);
					}
					if (selectedFormCons_p == null){
						selectedFormCons_p = new KtFormCons_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormProfEngmt_p = fDao.getKtFormProfEngmt_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormProfEngmt_p == null){
						selectedFormProfEngmt_p = new KtFormProfEngmt_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_IP:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormIP_p = fDao.getKtFormIP_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormIP_p == null){
						selectedFormIP_p = new KtFormIP_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormSocEngmt_p = fDao.getKtFormSocEngmt_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormSocEngmt_p == null){
						selectedFormSocEngmt_p = new KtFormSocEngmt_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormStaffEngmt_p = fDao.getKtFormStaffEngmt_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormStaffEngmt_p == null){
						selectedFormStaffEngmt_p = new KtFormStaffEngmt_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_EA:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormEA_p = fDao.getKtFormEA_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormEA_p == null){
						selectedFormEA_p = new KtFormEA_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_STARTUP:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormStartup_p = fDao.getKtFormStartup_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormStartup_p == null){
						selectedFormStartup_p = new KtFormStartup_P();
					}
					break;
				case SysParam.PARAM_KT_FORM_INV_AWARD:
					if (!Strings.isNullOrEmpty(paramNo)) {
						selectedFormInvAward_p = fDao.getKtFormInvAward_P(Integer.valueOf(paramNo), getParamDataLevel(), null, null, 0);
					}
					if (selectedFormInvAward_p == null){
						selectedFormInvAward_p = new KtFormInvAward_P();
					}
					break;
				case "":
					break;
			}
		}
	}

	public void updateKtFormHeader_P(int currentFormNo, String dataLevel)
	{
		if (!Strings.isNullOrEmpty(paramFormCode)) {
			String fac = null;
			String dept = null;
			LookupValue tmpLookupValue = null;
			if (getParamFacDept() != null && ("N".equals(dataLevel) || "D".equals(dataLevel)  || "C".equals(dataLevel))) {
				tmpLookupValue = vDao.getLookupValue("ORGANIZATION_UNIT_L1", getParamFacDept(), "US");
				if (tmpLookupValue != null) {
					fac = tmpLookupValue.getPk().getLookup_code();
				}else {
					tmpLookupValue = vDao.getLookupValue("ORGANIZATION_UNIT_L2", getParamFacDept(), "US");
					if (tmpLookupValue != null) {
						fac = tmpLookupValue.getParent_lookup_code();
						dept = tmpLookupValue.getPk().getLookup_code();
					}else {
						tmpLookupValue = vDao.getLookupValue("ORGANIZATION_UNIT_L3", getParamFacDept(), "US");
						if (tmpLookupValue != null) {
							fac = tmpLookupValue.getParent_lookup_code();
							dept = tmpLookupValue.getPk().getLookup_code();
						}
					}
				}
			}
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
					if (selectedFormCPD_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormCPD_P tmp = fDao.getKtFormCPD_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormCPD_P.class, tmp.getPk());
							}
						}
						KtFormCPD_P p2 = selectedFormCPD_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:
					if (selectedFormProfConf_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormProfConf_P tmp = fDao.getKtFormProfConf_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormProfConf_P.class, tmp.getPk());
							}
						}
						KtFormProfConf_P p2 = selectedFormProfConf_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_SEM:
					if (selectedFormSem_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormSem_P tmp = fDao.getKtFormSem_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormSem_P.class, tmp.getPk());
							}
						}
						KtFormSem_P p2 = selectedFormSem_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
					if (selectedFormCntProj_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormCntProj_P tmp = fDao.getKtFormCntProj_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormCntProj_P.class, tmp.getPk());
							}
						}
						KtFormCntProj_P p2 = selectedFormCntProj_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_INN:
					if (selectedFormInn_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormInn_P tmp = fDao.getKtFormInn_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormInn_P.class, tmp.getPk());
							}
						}
						KtFormInn_P p2 = selectedFormInn_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_CONS:
					if (selectedFormCons_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormCons_P tmp = fDao.getKtFormCons_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormCons_P.class, tmp.getPk());
							}
						}
						KtFormCons_P p2 = selectedFormCons_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
					if (selectedFormProfEngmt_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormProfEngmt_P tmp = fDao.getKtFormProfEngmt_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormProfEngmt_P.class, tmp.getPk());
							}
						}
						KtFormProfEngmt_P p2 = selectedFormProfEngmt_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_IP:
					if (selectedFormIP_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormIP_P tmp = fDao.getKtFormIP_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormIP_P.class, tmp.getPk());
							}
						}
						KtFormIP_P p2 = selectedFormIP_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
					if (selectedFormSocEngmt_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormSocEngmt_P tmp = fDao.getKtFormSocEngmt_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormSocEngmt_P.class, tmp.getPk());
							}
						}
						KtFormSocEngmt_P p2 = selectedFormSocEngmt_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
					if (selectedFormStaffEngmt_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormStaffEngmt_P tmp = fDao.getKtFormStaffEngmt_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormStaffEngmt_P.class, tmp.getPk());
							}
						}
						KtFormStaffEngmt_P p2 = selectedFormStaffEngmt_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
				case SysParam.PARAM_KT_FORM_EA:
					if (selectedFormEA_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormEA_P tmp = fDao.getKtFormEA_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormEA_P.class, tmp.getPk());
							}
						}
						KtFormEA_P p2 = selectedFormEA_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
				case SysParam.PARAM_KT_FORM_STARTUP:
					if (selectedFormStartup_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormStartup_P tmp = fDao.getKtFormStartup_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormStartup_P.class, tmp.getPk());
							}
						}
						KtFormStartup_P p2 = selectedFormStartup_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						if (selectedFormStartup_p.getAct_type_list() != null) {
							selectedFormStartup_p.setAct_type(String.join(",", selectedFormStartup_p.getAct_type_list()));
						}
						p2 = fDao.updateEntity(p2);
					}
				case SysParam.PARAM_KT_FORM_INV_AWARD:
					if (selectedFormInvAward_p != null) {
						//Delete Header_P P Level
						if ("P".equals(dataLevel) || "D".equals(dataLevel)) {
							KtFormInvAward_P tmp = fDao.getKtFormInvAward_P(currentFormNo, dataLevel, null, null, 0);
							if (tmp != null) {
								fDao.deleteEntity(KtFormInvAward_P.class, tmp.getPk());
							}
						}
						KtFormInvAward_P p2 = selectedFormInvAward_p;
						p2.getPk().setForm_no(currentFormNo);
						p2.getPk().setData_level(dataLevel);
						p2.setUserstamp(getLoginUserId());
						if ("C".equals(dataLevel) == false) {
							p2.setFac(fac);
							p2.setDept(dept);
						}
						p2 = fDao.updateEntity(p2);
					}
					break;
				case "":
					break;
			}
		}
	}

	public void updateKtFormProjectDays()
	{
		if (!Strings.isNullOrEmpty(getParamFormCode())) {
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
					if (selectedFormCPD_p != null) {
						selectedFormCPD_p.setNum_proj_day(countProjectDay(selectedFormCPD_p.getStart_date(), selectedFormCPD_p.getEnd_date()));
						selectedFormCPD_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormCPD_p.getStart_date(), selectedFormCPD_p.getEnd_date()));
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:
					if (selectedFormProfConf_p != null) {
						selectedFormProfConf_p.setNum_proj_day(countProjectDay(selectedFormProfConf_p.getStart_date(), selectedFormProfConf_p.getEnd_date()));
						selectedFormProfConf_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormProfConf_p.getStart_date(), selectedFormProfConf_p.getEnd_date()));
					}
					break;
				case SysParam.PARAM_KT_FORM_SEM:
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
					if (selectedFormCntProj_p != null) {
						selectedFormCntProj_p.setNum_proj_day(countProjectDay(selectedFormCntProj_p.getStart_date(), selectedFormCntProj_p.getEnd_date()));
						selectedFormCntProj_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormCntProj_p.getStart_date(), selectedFormCntProj_p.getEnd_date()));
					}
					break;
				case SysParam.PARAM_KT_FORM_INN:
					if (selectedFormInn_p != null) {
						selectedFormInn_p.setNum_proj_day(countProjectDay(selectedFormInn_p.getStart_date(), selectedFormInn_p.getEnd_date()));
						selectedFormInn_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormInn_p.getStart_date(), selectedFormInn_p.getEnd_date()));
					}
					break;
				case SysParam.PARAM_KT_FORM_CONS:
					if (selectedFormCons_p != null) {
						selectedFormCons_p.setNum_proj_day(countProjectDay(selectedFormCons_p.getStart_date(), selectedFormCons_p.getEnd_date()));
						selectedFormCons_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormCons_p.getStart_date(), selectedFormCons_p.getEnd_date()));
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
					break;
				case SysParam.PARAM_KT_FORM_IP:
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
					if (selectedFormSocEngmt_p != null) {
						selectedFormSocEngmt_p.setNum_proj_day(countProjectDay(selectedFormSocEngmt_p.getStart_date(), selectedFormSocEngmt_p.getEnd_date()));
						selectedFormSocEngmt_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormSocEngmt_p.getStart_date(), selectedFormSocEngmt_p.getEnd_date()));
					}
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
					break;
				case SysParam.PARAM_KT_FORM_EA:
					if (selectedFormEA_p != null) {
						selectedFormEA_p.setNum_proj_day(countProjectDay(selectedFormEA_p.getStart_date(), selectedFormEA_p.getEnd_date()));
						selectedFormEA_p.setNum_proj_day_in_yr(countProjectDayInYear(selectedFormEA_p.getStart_date(), selectedFormEA_p.getEnd_date()));
					}
				case SysParam.PARAM_KT_FORM_STARTUP:
					break;
				case SysParam.PARAM_KT_FORM_INV_AWARD:
					break;
				case "":
					break;
			}
		}
	}
	
	public void moveColumnUp(int idx) throws SQLException
	{
		if (!getSelectedFormDetails_p_list().isEmpty())
		{
			if (idx > 0)
			{
				KtFormDetails_P tmp = selectedFormDetails_p_list.get(idx-1);
				selectedFormDetails_p_list.set(idx-1, selectedFormDetails_p_list.get(idx));
				selectedFormDetails_p_list.set(idx, tmp);
			}
		}
	}

	
	public void moveColumnDown(int idx) throws SQLException
	{
		if (getSelectedFormDetails_p_list() != null)
		{
			if (idx+1 < selectedFormDetails_p_list.size())
			{
				KtFormDetails_P tmp = selectedFormDetails_p_list.get(idx+1);
				selectedFormDetails_p_list.set(idx+1, selectedFormDetails_p_list.get(idx));
				selectedFormDetails_p_list.set(idx, tmp);
			}
		}
	}	
	public void deleteRow(int idx) throws SQLException
	{
		if (!getSelectedFormDetails_p_list().isEmpty())
		{
			if (idx < selectedFormDetails_p_list.size()) selectedFormDetails_p_list.remove(idx);
		}
	}
	public void addRow() throws SQLException
	{
		KtFormDetails_P p = new KtFormDetails_P();
		p.getPk().setForm_no(selectedFormHeader_q.getPk().getForm_no());
		p.getPk().setData_level(getParamDataLevel());
		p.setFlag("N");
		if (!getSelectedFormDetails_p_list().isEmpty())
		{
			selectedFormDetails_p_list.add(p);
		}else {
			selectedFormDetails_p_list = new ArrayList<KtFormDetails_P>();
			selectedFormDetails_p_list.add(p);
		}
	}


	
	//validate start end date	function
	public Boolean validateStartEndDate(Date startDate, Date endDate) 
	{
		boolean result = true;
		int num = startDate.compareTo(endDate);
		if (num > 0) {
			result = false;
		}
		return result;
	}
	//validate mandatory field	
	public Boolean validateRequiredField() 
	{
		boolean result = true;
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		Double income = 0.0;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (!Strings.isNullOrEmpty(paramFormCode)) {
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
					//KT Form CPD
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getResearch_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_research_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormCPD_p.getResearch_element())) {
							result = false;
							message = "Please report this project in either A1 (Contract Research) or A2 (Collaborative Research Projects).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cpd_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getCrse_prog())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_crse_prog"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_crse_prog", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormCPD_p.getCrse_prog())) {
							result = false;
							message = "Please report this item in A3 (Consultancy).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cpd_crse_prog", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getEnt_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_ent_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormCPD_p.getEnt_element())) {
							result = false;
							message = "Please report this item in A6 (Entrepreneurship Activity).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cpd_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getAct_mode())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_act_mode"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_act_mode", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getPi())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_pi"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_pi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getFund_src())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_fund_src"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_fund_src", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getEduhk_org())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_eduhk_org"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormCPD_p.getEduhk_org())){
							result = false;
							message = "Please report the activities organized by EdUHK.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cpd_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getRegion())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_region"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_region", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCPD_p.getFree_charge())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_free_charge"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_free_charge", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getBudget() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_budget"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					/*if (selectedFormCPD_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormCPD_p.getExpnd_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_expnd_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_expnd_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncome(selectedFormCPD_p.getStart_date(), selectedFormCPD_p.getEnd_date(), selectedFormCPD_p.getBudget());
					selectedFormCPD_p.setIncome_rpt_unit(income);
					if (selectedFormCPD_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getNum_other() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_num_other"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_num_other", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCPD_p.getNum_stu_contact_hr() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cpd_num_stu_contact_hr"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cpd_num_stu_contact_hr", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:
					//KT Form Prof Conf
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getEnt_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_ent_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormProfConf_p.getEnt_element())){
							result = false;
							message = "Please report this activity in A6 (Entrepreneurship Activity).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:prof_conf_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getPerf_art())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_perf_art"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_perf_art", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormProfConf_p.getPerf_art())){
							result = false;
							message = "Please report this activity in A7 (Social, Community and Cultural Engagement).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:prof_conf_perf_art", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getNum_subsessions() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_subsessions"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_subsessions", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getPi())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_pi"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_pi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getEvent_type())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_event_type"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_event_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getMuseum())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_museum"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_museum", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getAct_mode())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_act_mode"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_act_mode", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getEduhk_org())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_eduhk_org"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormProfConf_p.getEduhk_org())){
							result = false;
							message = "Please report the activities organized by EdUHK.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:prof_conf_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getRegion())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_region"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_region", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getTarget_pax())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_target_pax"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_target_pax", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getBudget_holder())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_budget_holder"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_budget_holder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getFree_charge())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_free_charge"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_free_charge", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfConf_p.getBudget_approval())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_budget_approval"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_budget_approval", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getBudget() == null) {
						if ("Y".equals(selectedFormProfConf_p.getBudget_approval())) {
							result = false;
							message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_budget"));
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:prof_conf_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					/*if (selectedFormProfConf_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormProfConf_p.getExpnd_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_expnd_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_expnd_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncomeWithFilter(selectedFormProfConf_p.getStart_date(), selectedFormProfConf_p.getEnd_date(), selectedFormProfConf_p.getBudget(), selectedFormProfConf_p.getExpnd_rpt_unit(), selectedFormProfConf_p.getBudget_approval());
					selectedFormProfConf_p.setIncome_rpt_unit(income);
					if (selectedFormProfConf_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getNum_speakers() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_speakers"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_speakers", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}			
					if (selectedFormProfConf_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getNum_other() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_other"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_other", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getNum_stu() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_stu"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_stu", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfConf_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_conf_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_conf_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_SEM:
					//KT Form Sem
					if (Strings.isNullOrEmpty(selectedFormSem_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSem_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSem_p.getOrganizer())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_organizer"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_organizer", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSem_p.getCt_person())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_ct_person"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_ct_person", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSem_p.getTarget_pax())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_target_pax"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_target_pax", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSem_p.getFree_charge())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_free_charge"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_free_charge", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					/*if (selectedFormSem_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormSem_p.getExpnd_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_expnd_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_expnd_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getNum_pax() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_num_pax"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_num_pax", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getNum_other() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_num_other"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_num_other", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getStaff_man_day() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_staff_man_day"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_staff_man_day", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSem_p.getNum_ext_prof() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:sem_num_ext_prof"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:sem_num_ext_prof", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
					//KT Form Cnt Proj
					if (Strings.isNullOrEmpty(selectedFormCntProj_p.getResearch_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_research_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormCntProj_p.getResearch_element())) {
							result = false;
							message = "Please report this project in either A3 (Consultancy) or A8 (CPD Courses).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cnt_proj_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormCntProj_p.getOwnership_ip_right())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_ownership_ip_right"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_ownership_ip_right", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						boolean isEduhk = Arrays.stream(selectedFormCntProj_p.getOwnership_ip_right_array()).anyMatch("E"::equals);
						if (isEduhk) {
							result = false;
							message = "Please report this project in A2 (Collaborative Research Project).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cnt_proj_ownership_ip_right", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormCntProj_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCntProj_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCntProj_p.getPrincipal_inves())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_principal_inves"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_principal_inves", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCntProj_p.getFund_src())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_fund_src"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_fund_src", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getBudget() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_budget"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncome(selectedFormCntProj_p.getStart_date(), selectedFormCntProj_p.getEnd_date(), selectedFormCntProj_p.getBudget());
					selectedFormCntProj_p.setIncome_rpt_unit(income);
					/*if (selectedFormCntProj_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormCntProj_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getNum_stakeholder() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_stakeholder"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_stakeholder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCntProj_p.getNum_stu() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_stu"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_stu", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					/*if (selectedFormCntProj_p.getNum_ext_prof() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cnt_proj_num_ext_prof"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cnt_proj_num_ext_prof", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					break;
				case SysParam.PARAM_KT_FORM_INN:
					//KT Form Inn
					if (Strings.isNullOrEmpty(selectedFormInn_p.getResearch_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_research_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormInn_p.getResearch_element())) {
							result = false;
							message = "Please report this project in either A3 (Consultancy) or A8 (CPD Courses).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:inn_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormInn_p.getOwnership_ip_right())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_ownership_ip_right"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_ownership_ip_right", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						boolean isEduhk = Arrays.stream(selectedFormInn_p.getOwnership_ip_right_array()).anyMatch("E"::equals);
						if (isEduhk == false) {
							result = false;
							message = "Please report this project in A1 (Contract Research).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:inn_ownership_ip_right", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (selectedFormInn_p.getPercentage_ownership() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_percentage_ownership"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_percentage_ownership", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInn_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInn_p.getName_colla())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_name_colla"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_name_colla", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInn_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInn_p.getPrincipal_inves())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_principal_inves"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_principal_inves", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInn_p.getFund_src())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_fund_src"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_fund_src", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getBudget() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_budget"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncome(selectedFormInn_p.getStart_date(), selectedFormInn_p.getEnd_date(), selectedFormInn_p.getBudget());
					selectedFormInn_p.setIncome_rpt_unit(income);
					/*if (selectedFormInn_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormInn_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getNum_stakeholder() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_stakeholder"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_stakeholder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormInn_p.getNum_stu() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_stu"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_stu", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					/*if (selectedFormInn_p.getNum_ext_prof() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inn_num_ext_prof"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inn_num_ext_prof", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					break;
				case SysParam.PARAM_KT_FORM_CONS:
					//KT Form Cons
					if (Strings.isNullOrEmpty(selectedFormCons_p.getResearch_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_research_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormCons_p.getResearch_element())) {
							result = false;
							message = "Please report this project in either A1 (Contract Research) or A2 (Collaborative Research Projects).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cons_research_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getEdu_crse())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_edu_crse"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_edu_crse", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormCons_p.getEdu_crse())) {
							result = false;
							message = "Please report this item in A8 (CPD Courses).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:cons_edu_crse", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getPrincipal_inves())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_principal_inves"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_principal_inves", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getFund_src())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_fund_src"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_fund_src", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getHk_fund())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_hk_fund"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_hk_fund", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getHk_gov_fund()) && "HK".equals(selectedFormCons_p.getHk_fund())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_hk_gov_fund"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_hk_gov_fund", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getHk_pri_fund()) && "PRI".equals(selectedFormCons_p.getHk_gov_fund())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_hk_pri_fund"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_hk_pri_fund", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getFund_src_org()) && "NON-HK".equals(selectedFormCons_p.getHk_fund())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_fund_src_org"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_fund_src_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getJoint_proj())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_joint_proj"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_joint_proj", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getEduhk_role()) && "N".equals(selectedFormCons_p.getJoint_proj()) == false) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_eduhk_role"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_eduhk_role", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getName_coor()) && "P".equals(selectedFormCons_p.getEduhk_role())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_name_coor"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_name_coor", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getFund_src_coor()) && "P".equals(selectedFormCons_p.getEduhk_role())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_fund_src_coor"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_fund_src_coor", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getProj_title_coor()) && "P".equals(selectedFormCons_p.getEduhk_role())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_proj_title_coor"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_proj_title_coor", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormCons_p.getPi_coor()) && "P".equals(selectedFormCons_p.getEduhk_role())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_pi_coor"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_pi_coor", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getBudget() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_budget"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncome(selectedFormCons_p.getStart_date(), selectedFormCons_p.getEnd_date(), selectedFormCons_p.getBudget());
					selectedFormCons_p.setIncome_rpt_unit(income);
					/*if (selectedFormCons_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormCons_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_stu() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_stu"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_stu", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_stakeholder() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_stakeholder"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_stakeholder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					/*if (selectedFormCons_p.getNum_org() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_org"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_adv_body() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_adv_body"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_adv_body", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormCons_p.getNum_ext_prof() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:cons_num_ext_prof"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:cons_num_ext_prof", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
					//KT Form Prof Engmt
					if (Strings.isNullOrEmpty(selectedFormProfEngmt_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfEngmt_p.getCond_staff())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_cond_staff"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_cond_staff", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormProfEngmt_p.getOrganizer())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_organizer"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_organizer", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getNum_leader() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_num_leader"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_num_leader", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getNum_other() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormProfEngmt_p.getNum_pax_ben() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:prof_engmt_num_pax_ben"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:prof_engmt_num_pax_ben", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_IP:
					//KT Form IP
					if (Strings.isNullOrEmpty(selectedFormIP_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getCat())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_cat"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_cat", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getSoftware_lic())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_software_lic"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_software_lic", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormIP_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormIP_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getStaff_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_staff_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_staff_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getHk_lic())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_hk_lic"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_hk_lic", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getHk_gov()) && "HK".equals(selectedFormIP_p.getHk_lic())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_hk_gov"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_hk_gov", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getHk_pri()) && "PRI".equals(selectedFormIP_p.getHk_gov())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_hk_pri"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_hk_pri", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getIncome_name())) {			
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_income_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_income_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getOrg_type())) {
						if ("NON-HK".equals(selectedFormIP_p.getHk_lic()) || "I".equals(selectedFormIP_p.getHk_pri())) {
							result = false;
							message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_org_type"));
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:ip_org_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (selectedFormIP_p.getAmt_upfront() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_amt_upfront"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_amt_upfront", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormIP_p.getAmt_royalty() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_amt_royalty"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_amt_royalty", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormIP_p.getDebit_note())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_debit_note"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_debit_note", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncome2(selectedFormIP_p.getAmt_upfront(), selectedFormIP_p.getAmt_royalty());
					selectedFormIP_p.setIncome_rpt_unit(income);
					/*if (selectedFormIP_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ip_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ip_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
					//KT Form Soc Engmt
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getEnt_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_ent_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("Y".equals(selectedFormSocEngmt_p.getEnt_element())){
							result = false;
							message = "Please report this activity in A6 (Entrepreneurship Activity).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:soc_engmt_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getPerf_art())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_perf_art"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_perf_art", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormSocEngmt_p.getPerf_art())){
							result = false;
							message = "Please report this activity in A9 (Public Dissemination and Speeches).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:soc_engmt_perf_art", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getNum_subsessions() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_subsessions"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_subsessions", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getPi())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_pi"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_pi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getAct_mode())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_act_mode"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_act_mode", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getBudget_holder())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_budget_holder"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_budget_holder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getEduhk_org())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_eduhk_org"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getRegion())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_region"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_region", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getFree_charge())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_free_charge"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_free_charge", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getBudget_approval())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_budget_approval"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_budget_approval", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getBudget() == null) {
						if ("Y".equals(selectedFormSocEngmt_p.getBudget_approval())) {
							result = false;
							message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_budget"));
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:soc_engmt_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					/*if (selectedFormSocEngmt_p.getIncome_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_income_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_income_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (selectedFormSocEngmt_p.getExpnd_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_expnd_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_expnd_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncomeWithFilter(selectedFormSocEngmt_p.getStart_date(), selectedFormSocEngmt_p.getEnd_date(), selectedFormSocEngmt_p.getBudget(), selectedFormSocEngmt_p.getExpnd_rpt_unit(), selectedFormSocEngmt_p.getBudget_approval());
					selectedFormSocEngmt_p.setIncome_rpt_unit(income);
					if (selectedFormSocEngmt_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getNum_speakers() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_speakers"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_speakers", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}			
					if (Strings.isNullOrEmpty(selectedFormSocEngmt_p.getTarget_pax())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_target_pax"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_target_pax", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (selectedFormSocEngmt_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getNum_other() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_other"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_other", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getNum_stu() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_stu"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_stu", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormSocEngmt_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:soc_engmt_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:soc_engmt_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
					//KT Form Staff Engmt
					if (Strings.isNullOrEmpty(selectedFormStaffEngmt_p.getStaff_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_staff_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_staff_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormStaffEngmt_p.getAdmin_tech())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_admin_tech"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_admin_tech", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}/*else {
						if ("Y".equals(selectedFormStaffEngmt_p.getAdmin_tech())) {
							result = false;
							message = "Please report the post engaged by academic/ teaching/ project staff only.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:staff_engmt_admin_tech", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}*/
					if (selectedFormStaffEngmt_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					//A13 form end date can be null
					/*if (selectedFormStaffEngmt_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}*/
					if (Strings.isNullOrEmpty(selectedFormStaffEngmt_p.getExt_body_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_ext_body_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_ext_body_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormStaffEngmt_p.getRegion())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_region"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_region", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}

					if (Strings.isNullOrEmpty(selectedFormStaffEngmt_p.getExt_body_nature())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_ext_body_nature"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_ext_body_nature", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormStaffEngmt_p.getPost_engaged())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:staff_engmt_post_engaged"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:staff_engmt_post_engaged", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_EA:
					if (Strings.isNullOrEmpty(selectedFormEA_p.getEnt_element())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_ent_element"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormEA_p.getEnt_element())){
							result = false;
							message = "Please report this item in either A7 (Social, Community and Cultural Engagement) or A9 (Public Dissemination and Speeches).";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:ea_ent_element", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (selectedFormEA_p.getNum_subsessions() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_subsessions"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_subsessions", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getPi())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_pi"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_pi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getTeam_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_team_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_team_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getAct_type())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_act_type"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_act_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getMuseum())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_museum"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_museum", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getEduhk_org())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_eduhk_org"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormEA_p.getEduhk_org())){
							result = false;
							message = "Please report the activities organized by EdUHK.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:ea_eduhk_org", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getAct_mode())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_act_mode"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_act_mode", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getCredit_bearing())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_credit_bearing"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_credit_bearing", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}

					if (Strings.isNullOrEmpty(selectedFormEA_p.getAct_code())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_act_code"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_act_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (Strings.isNullOrEmpty(selectedFormEA_p.getBudget_holder())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_budget_holder"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_budget_holder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (Strings.isNullOrEmpty(selectedFormEA_p.getFree_charge())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_free_charge"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_free_charge", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getRegion())) {
							result = false;
							message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_region"));
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:ea_region", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getEnd_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_end_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_end_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormEA_p.getBudget_approval())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_budget_approval"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_budget_approval", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}else {
						if ("N".equals(selectedFormEA_p.getBudget_approval())) {
							selectedFormEA_p.setBudget(null);
						}
					}
					if (selectedFormEA_p.getBudget() == null && "Y".equals(selectedFormEA_p.getBudget_approval())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_budget"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_budget", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getExpnd_rpt_unit() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_expnd_rpt_unit"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_expnd_rpt_unit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					income = countIncomeWithFilter(selectedFormEA_p.getStart_date(), selectedFormEA_p.getEnd_date(), selectedFormEA_p.getBudget(), selectedFormEA_p.getExpnd_rpt_unit(), selectedFormEA_p.getBudget_approval());
					selectedFormEA_p.setIncome_rpt_unit(income);
					if (selectedFormEA_p.getNum_key_partner() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_key_partner"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_key_partner", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_speakers() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_speakers"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_speakers", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_stu_ug() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_stu_ug"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_stu_ug", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_stu_pg() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_stu_pg"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_stu_pg", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					
					if (selectedFormEA_p.getNum_staff() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_staff"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_staff", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_alumni() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_alumni"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_alumni", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_teacher() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_teacher"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_teacher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_principal() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_principal"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_principal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_other() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_other"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_other", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormEA_p.getNum_school() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:ea_num_school"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:ea_num_school", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_STARTUP:
					if (Strings.isNullOrEmpty(selectedFormStartup_p.getTitle())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_title"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormStartup_p.getAct_type_list() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_act_type"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_act_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormStartup_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormStartup_p.getOrganizer())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_organizer"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_organizer", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormStartup_p.getCt_person())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_ct_person"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_ct_person", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormStartup_p.getNum_stu_ug() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_num_stu_ug"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_num_stu_ug", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormStartup_p.getNum_stu_pg() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_num_stu_pg"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_num_stu_pg", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormStartup_p.getNum_staff() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_num_staff"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_num_staff", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (selectedFormStartup_p.getNum_alumni() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:startup_num_alumni"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:startup_num_alumni", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_INV_AWARD:
					if (Strings.isNullOrEmpty(selectedFormInvAward_p.getIp_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inv_award_ip_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inv_award_ip_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInvAward_p.getEvent_name())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inv_award_event_name"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inv_award_event_name", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}

					if (selectedFormInvAward_p.getStart_date() == null) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inv_award_start_date"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inv_award_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInvAward_p.getName_pi())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inv_award_name_pi"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inv_award_name_pi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (Strings.isNullOrEmpty(selectedFormInvAward_p.getName_other())) {
						result = false;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), getFormLabel("editForm:inv_award_name_other"));
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:inv_award_name_other", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					if (inv_award_awardDetails_list != null) {
						int lineNo = 1;
						for (AwardDetails o:inv_award_awardDetails_list) {
							if (Strings.isNullOrEmpty(o.getTitle())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Title of Award (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:awardsTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getRegion())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Award Type (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:awardsTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							lineNo++;
						}
						if (convertAwardDetails() == false) {
							result = false;
						}
					}
					if (inv_award_patentFiled_list != null) {
						int lineNo = 1;
						for (PatentDetails o:inv_award_patentFiled_list) {
							if (Strings.isNullOrEmpty(o.getName())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of the Invention (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentFiledTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getNum())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Application Number (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentFiledTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getDate())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Application (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentFiledTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getCountry())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Country/ Region Where the Patent is filed (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentFiledTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							lineNo++;
						}
						if (convertPatentFiled() == false) {
							result = false;	
						}
					}
					if (inv_award_patentGranted_list != null) {
						int lineNo = 1;
						for (PatentDetails o:inv_award_patentGranted_list) {
							if (Strings.isNullOrEmpty(o.getName())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name of the Patent (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentGrantedTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getNum())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Patent Number Granted (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentGrantedTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getDate())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date of Patent (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentGrantedTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							if (Strings.isNullOrEmpty(o.getCountry())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Country/ Region Granting the Patent (no. "+lineNo+")");
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:patentGrantedTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
							lineNo++;
						}
						if (convertPatentGranted() == false) {
							result = false;	
						}
					}
					break;
				case "":
					break;
			}
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	//validate form start end date
	public boolean validateDate()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message = "Start Date cannot be greater than End Date.";
		if (!Strings.isNullOrEmpty(paramFormCode)) {
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
					if (selectedFormCPD_p.getStart_date() != null && selectedFormCPD_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormCPD_p.getStart_date(), selectedFormCPD_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:cpd_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:
					if (selectedFormProfConf_p.getStart_date() != null && selectedFormProfConf_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormProfConf_p.getStart_date(), selectedFormProfConf_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:prof_conf_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_SEM:
					if (selectedFormSem_p.getStart_date() != null && selectedFormSem_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormSem_p.getStart_date(), selectedFormSem_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:sem_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
					if (selectedFormCntProj_p.getStart_date() != null && selectedFormCntProj_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormCntProj_p.getStart_date(), selectedFormCntProj_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:cnt_proj_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_INN:
					if (selectedFormInn_p.getStart_date() != null && selectedFormInn_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormInn_p.getStart_date(), selectedFormInn_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:inn_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_CONS:
					if (selectedFormCons_p.getStart_date() != null && selectedFormCons_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormCons_p.getStart_date(), selectedFormCons_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:cons_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
					if (selectedFormProfEngmt_p.getStart_date() != null && selectedFormProfEngmt_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormProfEngmt_p.getStart_date(), selectedFormProfEngmt_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:prof_engmt_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_IP:
					if (selectedFormIP_p.getStart_date() != null && selectedFormIP_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormIP_p.getStart_date(), selectedFormIP_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:ip_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
					if (selectedFormSocEngmt_p.getStart_date() != null && selectedFormSocEngmt_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormSocEngmt_p.getStart_date(), selectedFormSocEngmt_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:soc_engmt_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
					if (selectedFormStaffEngmt_p.getStart_date() != null && selectedFormStaffEngmt_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormStaffEngmt_p.getStart_date(), selectedFormStaffEngmt_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:staff_engmt_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case SysParam.PARAM_KT_FORM_EA:
					if (selectedFormEA_p.getStart_date() != null && selectedFormEA_p.getEnd_date() != null) {
						result = validateStartEndDate(selectedFormEA_p.getStart_date(), selectedFormEA_p.getEnd_date());
						if (result == false)
							fCtx.addMessage("editForm:ea_start_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					break;
				case "":
					break;
			}
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}
		return result;	
	}
	
	
	public KtFormCPD_P getSelectedFormCPD_p()
	{
		return selectedFormCPD_p;
	}


	public void setSelectedFormCPD_p(KtFormCPD_P selectedFormCPD_p)
	{
		this.selectedFormCPD_p = selectedFormCPD_p;
	}
	
	
	
	
	public KtFormProfConf_P getSelectedFormProfConf_p()
	{
		return selectedFormProfConf_p;
	}


	
	public void setSelectedFormProfConf_p(KtFormProfConf_P selectedFormProfConf_p)
	{
		this.selectedFormProfConf_p = selectedFormProfConf_p;
	}


	
	public KtFormSem_P getSelectedFormSem_p()
	{
		return selectedFormSem_p;
	}


	
	public void setSelectedFormSem_p(KtFormSem_P selectedFormSem_p)
	{
		this.selectedFormSem_p = selectedFormSem_p;
	}


	
	public KtFormCntProj_P getSelectedFormCntProj_p()
	{
		return selectedFormCntProj_p;
	}


	
	public void setSelectedFormCntProj_p(KtFormCntProj_P selectedFormCntProj_p)
	{
		this.selectedFormCntProj_p = selectedFormCntProj_p;
	}


	
	public KtFormInn_P getSelectedFormInn_p()
	{
		return selectedFormInn_p;
	}


	
	public void setSelectedFormInn_p(KtFormInn_P selectedFormInn_p)
	{
		this.selectedFormInn_p = selectedFormInn_p;
	}


	
	public KtFormCons_P getSelectedFormCons_p()
	{
		return selectedFormCons_p;
	}


	
	public void setSelectedFormCons_p(KtFormCons_P selectedFormCons_p)
	{
		this.selectedFormCons_p = selectedFormCons_p;
	}


	
	public KtFormProfEngmt_P getSelectedFormProfEngmt_p()
	{
		return selectedFormProfEngmt_p;
	}


	
	public void setSelectedFormProfEngmt_p(KtFormProfEngmt_P selectedFormProfEngmt_p)
	{
		this.selectedFormProfEngmt_p = selectedFormProfEngmt_p;
	}


	
	public KtFormIP_P getSelectedFormIP_p()
	{
		return selectedFormIP_p;
	}


	
	public void setSelectedFormIP_p(KtFormIP_P selectedFormIP_p)
	{
		this.selectedFormIP_p = selectedFormIP_p;
	}


	
	public KtFormSocEngmt_P getSelectedFormSocEngmt_p()
	{
		return selectedFormSocEngmt_p;
	}


	
	public void setSelectedFormSocEngmt_p(KtFormSocEngmt_P selectedFormSocEngmt_p)
	{
		this.selectedFormSocEngmt_p = selectedFormSocEngmt_p;
	}


	
	public KtFormStaffEngmt_P getSelectedFormStaffEngmt_p()
	{
		return selectedFormStaffEngmt_p;
	}


	
	public void setSelectedFormStaffEngmt_p(KtFormStaffEngmt_P selectedFormStaffEngmt_p)
	{
		this.selectedFormStaffEngmt_p = selectedFormStaffEngmt_p;
	}

	
	public KtFormEA_P getSelectedFormEA_p()
	{
		return selectedFormEA_p;
	}


	
	public void setSelectedFormEA_p(KtFormEA_P selectedFormEA_p)
	{
		this.selectedFormEA_p = selectedFormEA_p;
	}


	
	public KtFormStartup_P getSelectedFormStartup_p()
	{
		return selectedFormStartup_p;
	}


	
	public void setSelectedFormStartup_p(KtFormStartup_P selectedFormStartup_p)
	{
		this.selectedFormStartup_p = selectedFormStartup_p;
	}


	
	public KtFormInvAward_P getSelectedFormInvAward_p()
	{
		return selectedFormInvAward_p;
	}


	
	public void setSelectedFormInvAward_p(KtFormInvAward_P selectedFormInvAward_p)
	{
		this.selectedFormInvAward_p = selectedFormInvAward_p;
	}

	public List<SelectItem> getEa_actTypeList()
	{
		if (ea_actTypeList == null) {
			ea_actTypeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_EA_ACT_TYPE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ea_actTypeList.add(option);
				}
			}
		}
		return ea_actTypeList;
	}


	
	public void setEa_actTypeList(List<SelectItem> ea_actTypeList)
	{
		this.ea_actTypeList = ea_actTypeList;
	}


	
	
	public List<SelectItem> getEa_actCatList()
	{
		if (ea_actCatList == null) {
			ea_actCatList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_EA_ACT_CAT" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ea_actCatList.add(option);
				}
			}
		}
		return ea_actCatList;
	}


	
	public void setEa_actCatList(List<SelectItem> ea_actCatList)
	{
		this.ea_actCatList = ea_actCatList;
	}


	public List<SelectItem> getStartup_actTypeList()
	{
		if (startup_actTypeList == null) {
			startup_actTypeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_STARTUP_ACT_TYPE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					startup_actTypeList.add(option);
				}
			}
		}
		return startup_actTypeList;
	}


	
	public void setStartup_actTypeList(List<SelectItem> startup_actTypeList)
	{
		this.startup_actTypeList = startup_actTypeList;
	}


	
	public List<SelectItem> getIp_catList()
	{
		if (ip_catList == null) {
			ip_catList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_IP_CAT" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ip_catList.add(option);
				}
			}
		}
		return ip_catList;
	}


	
	public void setIp_catList(List<SelectItem> ip_catList)
	{
		this.ip_catList = ip_catList;
	}


	
	
	public List<SelectItem> getIp_fundSrcList()
	{
		if (ip_fundSrcList == null) {
			ip_fundSrcList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_IP_FUND_SRC" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ip_fundSrcList.add(option);
				}
			}
		}
		return ip_fundSrcList;
	}


	
	public void setIp_fundSrcList(List<SelectItem> ip_fundSrcList)
	{
		this.ip_fundSrcList = ip_fundSrcList;
	}


	
	public List<SelectItem> getCons_fundSrcList()
	{
		if (cons_fundSrcList == null) {
			cons_fundSrcList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_FUND_SRC" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_fundSrcList.add(option);
				}
			}
		}
		return cons_fundSrcList;
	}


	
	public void setCons_fundSrcList(List<SelectItem> cons_fundSrcList)
	{
		this.cons_fundSrcList = cons_fundSrcList;
	}


	public List<SelectItem> getProf_conf_eventTypeList()
	{
		if (prof_conf_eventTypeList == null) {
			prof_conf_eventTypeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_PROF_CONF_EVENT_TYPE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					prof_conf_eventTypeList.add(option);
				}
			}
		}
		return prof_conf_eventTypeList;
	}


	
	public void setProf_conf_eventTypeList(List<SelectItem> prof_conf_eventTypeList)
	{
		this.prof_conf_eventTypeList = prof_conf_eventTypeList;
	}


	public List<SelectItem> getProf_conf_targetPaxList()
	{
		if (prof_conf_targetPaxList == null) {
			prof_conf_targetPaxList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_PROF_CONF_TARGET_PAX" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					prof_conf_targetPaxList.add(option);
				}
			}
		}
		return prof_conf_targetPaxList;
	}


	
	public void setProf_conf_targetPaxList(List<SelectItem> prof_conf_targetPaxList)
	{
		this.prof_conf_targetPaxList = prof_conf_targetPaxList;
	}


	
	
	public List<SelectItem> getEa_targetPaxList()
	{
		if (ea_targetPaxList == null) {
			ea_targetPaxList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_EA_TARGET_PAX" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ea_targetPaxList.add(option);
				}
			}
		}
		return ea_targetPaxList;
	}


	
	public void setEa_targetPaxList(List<SelectItem> ea_targetPaxList)
	{
		this.ea_targetPaxList = ea_targetPaxList;
	}


	public List<SelectItem> getSem_targetPaxList()
	{
		if (sem_targetPaxList == null) {
			sem_targetPaxList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_SEM_TARGET_PAX" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					sem_targetPaxList.add(option);
				}
			}
		}
		return sem_targetPaxList;
	}


	
	public void setSem_targetPaxList(List<SelectItem> sem_targetPaxList)
	{
		this.sem_targetPaxList = sem_targetPaxList;
	}



	
	public List<SelectItem> getIp_orgTypeList()
	{
		if (ip_orgTypeList == null) {
			ip_orgTypeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_IP_ORG_TYPE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ip_orgTypeList.add(option);
				}
			}
		}
		return ip_orgTypeList;
	}


	
	public void setIp_orgTypeList(List<SelectItem> ip_orgTypeList)
	{
		this.ip_orgTypeList = ip_orgTypeList;
	}


	
	public List<SelectItem> getCons_orgTypeList()
	{
		if (cons_orgTypeList == null) {
			cons_orgTypeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_ORG_TYPE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_orgTypeList.add(option);
				}
			}
		}
		return cons_orgTypeList;
	}


	
	public void setCons_orgTypeList(List<SelectItem> cons_orgTypeList)
	{
		this.cons_orgTypeList = cons_orgTypeList;
	}


	
	public List<SelectItem> getCons_roleList()
	{
		if (cons_roleList == null) {
			cons_roleList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_ROLE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_roleList.add(option);
				}
			}
		}
		return cons_roleList;
	}


	
	public void setCons_roleList(List<SelectItem> cons_roleList)
	{
		this.cons_roleList = cons_roleList;
	}


	
	public List<SelectItem> getSoc_engmt_actTypeList()
	{
		if (soc_engmt_actTypeList == null) {
			soc_engmt_actTypeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_SOC_ENGMT_ACT_TYPE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					soc_engmt_actTypeList.add(option);
				}
			}
		}
		return soc_engmt_actTypeList;
	}


	
	public void setSoc_engmt_actTypeList(List<SelectItem> soc_engmt_actTypeList)
	{
		this.soc_engmt_actTypeList = soc_engmt_actTypeList;
	}

	
	public List<SelectItem> getStaff_engmt_natureList()
	{
		if (staff_engmt_natureList == null) {
			staff_engmt_natureList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_STAFF_ENGMT_NATURE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					staff_engmt_natureList.add(option);
				}
			}
		}
		return staff_engmt_natureList;
	}


	
	public void setStaff_engmt_natureList(List<SelectItem> staff_engmt_natureList)
	{
		this.staff_engmt_natureList = staff_engmt_natureList;
	}


	
	public List<SelectItem> getCnt_proj_ownershipIpRightList()
	{
		if (cnt_proj_ownershipIpRightList == null) {
			cnt_proj_ownershipIpRightList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CNT_PROJ_OWNERSHIP_IP_RIGHT" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cnt_proj_ownershipIpRightList.add(option);
				}
			}
		}
		return cnt_proj_ownershipIpRightList;
	}


	
	public void setCnt_proj_ownershipIpRightList(List<SelectItem> cnt_proj_ownershipIpRightList)
	{
		this.cnt_proj_ownershipIpRightList = cnt_proj_ownershipIpRightList;
	}


	
	public List<SelectItem> getInn_ownershipIpRightList()
	{
		if (inn_ownershipIpRightList == null) {
			inn_ownershipIpRightList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CNT_PROJ_OWNERSHIP_IP_RIGHT" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					inn_ownershipIpRightList.add(option);
				}
			}
		}
		return inn_ownershipIpRightList;
	}

	public void setInn_ownershipIpRightList(List<SelectItem> inn_ownershipIpRightList)
	{
		this.inn_ownershipIpRightList = inn_ownershipIpRightList;
	}


	
	public List<SelectItem> getCons_hkFundList()
	{
		if (cons_hkFundList == null) {
			cons_hkFundList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_HK_FUND" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_hkFundList.add(option);
				}
			}
		}
		return cons_hkFundList;
	}


	
	public void setCons_hkFundList(List<SelectItem> cons_hkFundList)
	{
		this.cons_hkFundList = cons_hkFundList;
	}


	
	public List<SelectItem> getCons_hkGovFundList()
	{
		if (cons_hkGovFundList == null) {
			cons_hkGovFundList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_HK_GOV_FUND" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_hkGovFundList.add(option);
				}
			}
		}
		return cons_hkGovFundList;
	}


	
	public void setCons_hkGovFundList(List<SelectItem> cons_hkGovFundList)
	{
		this.cons_hkGovFundList = cons_hkGovFundList;
	}


	
	public List<SelectItem> getCons_hkPriFundList()
	{
		if (cons_hkPriFundList == null) {
			cons_hkPriFundList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_HK_PRI_FUND" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_hkPriFundList.add(option);
				}
			}
		}
		return cons_hkPriFundList;
	}


	
	public void setCons_hkPriFundList(List<SelectItem> cons_hkPriFundList)
	{
		this.cons_hkPriFundList = cons_hkPriFundList;
	}


	
	public List<SelectItem> getCons_jointProjList()
	{
		if (cons_jointProjList == null) {
			cons_jointProjList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CONS_JOINT_PROJ" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cons_jointProjList.add(option);
				}
			}
		}
		return cons_jointProjList;
	}


	
	public void setCons_jointProjList(List<SelectItem> cons_jointProjList)
	{
		this.cons_jointProjList = cons_jointProjList;
	}


	
	public List<SelectItem> getIp_hkLicList()
	{
		if (ip_hkLicList == null) {
			ip_hkLicList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_IP_HK_LIC" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ip_hkLicList.add(option);
				}
			}
		}
		return ip_hkLicList;
	}


	
	public void setIp_hkLicList(List<SelectItem> ip_hkLicList)
	{
		this.ip_hkLicList = ip_hkLicList;
	}


	
	public List<SelectItem> getIp_hkGovList()
	{
		if (ip_hkGovList == null) {
			ip_hkGovList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_IP_HK_GOV" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ip_hkGovList.add(option);
				}
			}
		}
		return ip_hkGovList;
	}


	
	public void setIp_hkGovList(List<SelectItem> ip_hkGovList)
	{
		this.ip_hkGovList = ip_hkGovList;
	}


	
	public List<SelectItem> getIp_hkPriList()
	{
		if (ip_hkPriList == null) {
			ip_hkPriList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_IP_HK_PRI" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ip_hkPriList.add(option);
				}
			}
		}
		return ip_hkPriList;
	}


	
	public void setIp_hkPriList(List<SelectItem> ip_hkPriList)
	{
		this.ip_hkPriList = ip_hkPriList;
	}

	
	public List<SelectItem> getEa_modeList()
	{
		if (ea_modeList == null) {
			ea_modeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_EA_MODE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					ea_modeList.add(option);
				}
			}
		}
		return ea_modeList;
	}


	
	public void setEa_modeList(List<SelectItem> ea_modeList)
	{
		this.ea_modeList = ea_modeList;
	}


	
	public List<SelectItem> getSoc_engmt_modeList()
	{
		if (soc_engmt_modeList == null) {
			soc_engmt_modeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_SOC_ENGMT_MODE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					soc_engmt_modeList.add(option);
				}
			}
		}
		return soc_engmt_modeList;
	}


	
	public void setSoc_engmt_modeList(List<SelectItem> soc_engmt_modeList)
	{
		this.soc_engmt_modeList = soc_engmt_modeList;
	}


	
	public List<SelectItem> getSoc_engmt_targetPaxList()
	{
		if (soc_engmt_targetPaxList == null) {
			soc_engmt_targetPaxList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_SOC_ENGMT_TARGET_PAX" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					soc_engmt_targetPaxList.add(option);
				}
			}
		}
		return soc_engmt_targetPaxList;
	}


	
	public void setSoc_engmt_targetPaxList(List<SelectItem> soc_engmt_targetPaxList)
	{
		this.soc_engmt_targetPaxList = soc_engmt_targetPaxList;
	}


	
	public List<SelectItem> getCpd_modeList()
	{
		if (cpd_modeList == null) {
			cpd_modeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_CPD_MODE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					cpd_modeList.add(option);
				}
			}
		}
		return cpd_modeList;
	}


	
	public void setCpd_modeList(List<SelectItem> cpd_modeList)
	{
		this.cpd_modeList = cpd_modeList;
	}


	
	public List<SelectItem> getProf_conf_modeList()
	{
		if (prof_conf_modeList == null) {
			prof_conf_modeList = new ArrayList<SelectItem>();
			List<LookupValue> lookupValueList = getLookupValueList("KT_PROF_CONF_MODE" , "US", "Y");
			if (lookupValueList != null) {
				for (LookupValue v:lookupValueList) {
					SelectItem option = new SelectItem(v.getPk().getLookup_code(), v.getDescription());
					prof_conf_modeList.add(option);
				}
			}
		}
		return prof_conf_modeList;
	}


	
	public void setProf_conf_modeList(List<SelectItem> prof_conf_modeList)
	{
		this.prof_conf_modeList = prof_conf_modeList;
	}


	public List<LookupValue> getLookupValueList(String lookup_type, String language, String enabled_flag)
	{
		List<LookupValue> lookupValueList = vDao.getLookupValueList(lookup_type, language, enabled_flag);
		return lookupValueList;
	}
	
	public String getFormLabel(String component_id)
	{
		String label = "";
		if (!Strings.isNullOrEmpty(component_id)) {
			UIViewRoot view = FacesContext.getCurrentInstance().getViewRoot();
			UIComponent component = view.findComponent(component_id);
			label = (String) component.getAttributes().get("label");
		}
		return label;
	}
	
	public Integer countProjectDay(Date startDate, Date endDate)
	{
		Integer count_project_day = null;
		if (startDate != null && endDate != null) {
			long diff = endDate.getTime() - startDate.getTime();
			long count_project_day_long = diff / 1000 / 60 / 60 / 24;
			count_project_day = (int)count_project_day_long;
			if (count_project_day < 0) {
				count_project_day = 0;
			}else {
				count_project_day = count_project_day+1;
			}
		}
		return count_project_day;
	}
	
	public Integer countProjectDayInYear(Date startDate, Date endDate)
	{
		Integer count_project_day_in_year = null;
		if (startDate != null && endDate != null) {
			if (getSelectedKtRptPeriod() != null) {
				if (selectedKtRptPeriod != null) {
					Date pStartDate = selectedKtRptPeriod.getDate_from();
					Date pEndDate = selectedKtRptPeriod.getDate_to();
					if (selectedKtRptPeriod.getPeriod_id() <= 1) {	
						pStartDate = startDate;
						pEndDate = endDate;
					}
					long diff;
					if (endDate.before(pEndDate)) {
						if (startDate.after(pStartDate)) {
							diff = endDate.getTime() - startDate.getTime();
						}else {
							diff = endDate.getTime() - pStartDate.getTime();
						}
					}else{
						if (startDate.after(pStartDate)) {
							diff = pEndDate.getTime() - startDate.getTime();
						}else {
							diff = pEndDate.getTime() - pStartDate.getTime();
						}
					}
					long count_project_day_in_year_long = diff / 1000 / 60 / 60 / 24;
					count_project_day_in_year = (int)count_project_day_in_year_long;
					if (count_project_day_in_year < 0) {
						count_project_day_in_year = 0;
					}else {
						count_project_day_in_year = count_project_day_in_year+1;
					}
				}
			}
		}
		return count_project_day_in_year;
	}

	public Double countIncome(Date startDate, Date endDate, Double budget)
	{
		Double result = 0.0;
		if (startDate != null && endDate != null && budget != null) {
			Integer totalDay = countProjectDay(startDate, endDate);
			Integer dayInYear = countProjectDayInYear(startDate, endDate);
			if (totalDay != null && dayInYear != null) {
				if (totalDay > 0 && dayInYear > 0 && budget > 0) {
					Double totalDayDouble=Double.valueOf(totalDay);
					result = dayInYear / totalDayDouble * budget; 
				}
			}
		}
		return result;
	}
	
	//count 2 budgets
	public Double countIncome2(Double budget1, Double budget2)
	{
		Double result = 0.0;
		Double budget = 0.0;
		if (budget1 != null) {
			budget = budget + budget1;
		}
		if (budget2 != null) {
			budget = budget + budget2;
		}
		result = budget;
		return result;
	}
	
	//filter 2 budgets
	public Double countIncomeWithFilter(Date startDate, Date endDate, Double budget1, Double budget2, String useBudget1)
	{
		Double result = 0.0;
		Double budget = 0.0;
		if ("Y".equals(useBudget1)) {
			//if budget1 <= 0 or null, result = budget2
			if (budget1 != null) {
				if (budget1 > 0) {
					budget = budget1;
					result = countIncome(startDate, endDate, budget);
				}else {
					result = budget2;
				}
			}else {
				result = budget2;
			}
		}else {
			result = budget2;
		}
		return result;
	}
	
	public KtRptPeriod getSelectedKtRptPeriod()
	{
		if (selectedKtRptPeriod == null) {
			if (getParamPeriod() != null) {
				selectedKtRptPeriod = rDao.getKtRptPeriod(Integer.valueOf(paramPeriod));
			}
		}
		return selectedKtRptPeriod;
	}


	
	public void setSelectedKtRptPeriod(KtRptPeriod selectedKtRptPeriod)
	{
		this.selectedKtRptPeriod = selectedKtRptPeriod;
	}

	public boolean valueGreaterThanZero(Integer value)
	{
		boolean result = false;
		if (value != null) {
			if (value > 0) {
				result = true;
			}
		}
		return result;
	}
	
	public boolean showField(String value)
	{
		boolean result = true;
		if ("N".equals(value)) {
			result = false;
		}
		return result;
	}
	
	public void addAwardsTableRow() throws SQLException
	{
		AwardDetails p = new AwardDetails();
		if (!getInv_award_awardDetails_list().isEmpty())
		{	
			inv_award_awardDetails_list.add(p);
		}else {
			inv_award_awardDetails_list = new ArrayList<AwardDetails>();
			inv_award_awardDetails_list.add(p);
		}
	}
	
	public void deleteAwardsTableRow(int idx) throws SQLException
	{
		if (!getInv_award_awardDetails_list().isEmpty())
		{
			if (idx < inv_award_awardDetails_list.size()) inv_award_awardDetails_list.remove(idx);
		}
	}
	
	//validate KT_INV_AWARD Award Details
	public boolean convertAwardDetails() 
	{
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		String title = "";
		String region = "";
		if (inv_award_awardDetails_list != null) {
			int i = 0;
			for (AwardDetails o:inv_award_awardDetails_list) {		
				if (o.getTitle() != null) {
					o.setTitle(o.getTitle().replace("_", "＿"));
				}
				if (o.getRegion() != null) {
					o.setRegion(o.getRegion().replace("_", "＿"));
				}

				title += o.getTitle();
				region += o.getRegion();
				
				if(++i < inv_award_awardDetails_list.size()) {
					title += "_";
					region += "_";
				}
			}
		}
		if (title.length() > 4000 || region.length() > 4000) {
			result = false;
			message = "There are too many awards.";
			fCtx.addMessage("editForm:awardsTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}else {
			selectedFormInvAward_p.setAward_title(title);
			selectedFormInvAward_p.setAward_type(region);
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	public void addPatentFiledTableRow() throws SQLException
	{
		PatentDetails p = new PatentDetails();
		if (!getInv_award_patentFiled_list().isEmpty())
		{	
			inv_award_patentFiled_list.add(p);
		}else {
			inv_award_patentFiled_list = new ArrayList<PatentDetails>();
			inv_award_patentFiled_list.add(p);
		}
	}
	
	public void deletePatentFiledTableRow(int idx) throws SQLException
	{
		if (!getInv_award_patentFiled_list().isEmpty())
		{
			if (idx < inv_award_patentFiled_list.size()) inv_award_patentFiled_list.remove(idx);
		}
	}
	
	//validate KT_INV_AWARD PatentFiled
	public boolean convertPatentFiled() 
	{
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		String name = "";
		String num = "";
		String date = "";
		String country = "";
		if (inv_award_patentFiled_list != null) {
			int i = 0;
			for (PatentDetails o:inv_award_patentFiled_list) {		
				if (o.getName() != null) {
					o.setName(o.getName().replace("_", "＿"));
				}
				if (o.getNum() != null) {
					o.setNum(o.getNum().replace("_", "＿"));
				}
				if (o.getDate() != null) {
					o.setDate(o.getDate().replace("_", "＿"));
				}
				if (o.getCountry() != null) {
					o.setCountry(o.getCountry().replace("_", "＿"));
				}
				name += o.getName();
				num += o.getNum();
				date += o.getDate();
				country += o.getCountry();

				if(++i < inv_award_patentFiled_list.size()) {
					name += "_";
					num += "_";
					date += "_";
					country += "_";
				}
			}
		}
		
		if (name.length() > 4000 || num.length() > 4000 || date.length() > 4000 || country.length() > 4000) {
			result = false;
			message = "There are too many patent.";
			fCtx.addMessage("editForm:patentFiledTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}else {
			selectedFormInvAward_p.setPatent_filed_name(name);
			selectedFormInvAward_p.setPatent_filed_num(num);
			selectedFormInvAward_p.setPatent_filed_date(date);
			selectedFormInvAward_p.setPatent_filed_country(country);
			selectedFormInvAward_p.setNum_pat_filed(inv_award_patentFiled_list.size());
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	public void addPatentGrantedTableRow() throws SQLException
	{
		PatentDetails p = new PatentDetails();
		if (!getInv_award_patentGranted_list().isEmpty())
		{	
			inv_award_patentGranted_list.add(p);
		}else {
			inv_award_patentGranted_list = new ArrayList<PatentDetails>();
			inv_award_patentGranted_list.add(p);
		}
	}
	
	public void deletePatentGrantedTableRow(int idx) throws SQLException
	{
		if (!getInv_award_patentGranted_list().isEmpty())
		{
			if (idx < inv_award_patentGranted_list.size()) inv_award_patentGranted_list.remove(idx);
		}
	}
	
	//validate KT_INV_AWARD PatentGranted
	public boolean convertPatentGranted() 
	{
		boolean result = true;
		String message;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		String name = "";
		String num = "";
		String date = "";
		String country = "";
		if (inv_award_patentGranted_list != null) {
			int i = 0;
			for (PatentDetails o:inv_award_patentGranted_list) {		
				if (o.getName() != null) {
					o.setName(o.getName().replace("_", "＿"));
				}
				if (o.getNum() != null) {
					o.setNum(o.getNum().replace("_", "＿"));
				}
				if (o.getDate() != null) {
					o.setDate(o.getDate().replace("_", "＿"));
				}
				if (o.getCountry() != null) {
					o.setCountry(o.getCountry().replace("_", "＿"));
				}
				name += o.getName();
				num += o.getNum();
				date += o.getDate();
				country += o.getCountry();
				
				if(++i < inv_award_patentGranted_list.size()) {
					name += "_";
					num += "_";
					date += "_";
					country += "_";
				}
			}
		}
		if (name.length() > 4000 || num.length() > 4000 || date.length() > 4000 || country.length() > 4000) {
			result = false;
			message = "There are too many patent.";
			fCtx.addMessage("editForm:patentGrantedTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- "+message, ""));
		}else {
			selectedFormInvAward_p.setPatent_granted_name(name);
			selectedFormInvAward_p.setPatent_granted_num(num);
			selectedFormInvAward_p.setPatent_granted_date(date);
			selectedFormInvAward_p.setPatent_granted_country(country);
			selectedFormInvAward_p.setNum_pat_granted(inv_award_patentGranted_list.size());
		}
		if (!result) {
			hasError = true;
		}
		return result;
	}
	
	public void copyFormInList(String pid, String formNo, String formCode, String periodId, String dataLevel, String selectedFacDept) 
	{
		paramPid = pid;
		paramNo = formNo;
		paramFormCode = formCode;
		paramPeriod = periodId;
		paramDataLevel = dataLevel;
		paramFacDept = selectedFacDept;
		if (!Strings.isNullOrEmpty(paramNo) && !Strings.isNullOrEmpty(paramFormCode)) {
			copyFormByLv();
		}
	}
	
	public void copyForm() 
	{
		if (!Strings.isNullOrEmpty(paramNo) && !Strings.isNullOrEmpty(paramFormCode)) {
			copyFormByLv();
		}
	}
	
	public void copyFormByLv() 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		//String redirectLink = "manageKtForm.xhtml?pid="+paramPid+"&form=" + paramFormCode+"&period="+getParamPeriod();
		
		String oldParamNo = paramNo;
		paramNo = null;
		
		Boolean copy_header_p = false;
		Boolean copy_success = false;
		
		List<String> dataLvList = new ArrayList<>();
		
		if ("M".equals(getParamDataLevel()) || "P".equals(getParamDataLevel())) {
			dataLvList.add("M");
		}
		if ("N".equals(getParamDataLevel()) || "D".equals(getParamDataLevel())) {
			dataLvList.add("N");			
		}
		
		try {
			switch(paramFormCode) {
				case SysParam.PARAM_KT_FORM_CPD:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormCPD_p = fDao.getKtFormCPD_P(Integer.valueOf(oldParamNo), getParamDataLevel(), null, null, 0);
							if (selectedFormCPD_p != null) {
								selectedFormCPD_p.getPk().setForm_no(null);
								selectedFormCPD_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_PROF_CONF:

						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormProfConf_p = fDao.getKtFormProfConf_P(Integer.valueOf(oldParamNo), getParamDataLevel(), null, null, 0);
							if (selectedFormProfConf_p != null) {
								selectedFormProfConf_p.getPk().setForm_no(null);
								selectedFormProfConf_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_SEM:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormSem_p = fDao.getKtFormSem_P(Integer.valueOf(oldParamNo), getParamDataLevel(), null, null, 0);
							if (selectedFormSem_p != null) {
								selectedFormSem_p.getPk().setForm_no(null);
								selectedFormSem_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_CNT_PROJ:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormCntProj_p = fDao.getKtFormCntProj_P(Integer.valueOf(oldParamNo), getParamDataLevel(), null, null, 0);
							if (selectedFormCntProj_p != null) {
								selectedFormCntProj_p.getPk().setForm_no(null);
								selectedFormCntProj_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_INN:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormInn_p = fDao.getKtFormInn_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormInn_p != null) {
								selectedFormInn_p.getPk().setForm_no(null);
								selectedFormInn_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_CONS:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormCons_p = fDao.getKtFormCons_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormCons_p != null) {
								selectedFormCons_p.getPk().setForm_no(null);
								selectedFormCons_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_PROF_ENGMT:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormProfEngmt_p = fDao.getKtFormProfEngmt_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormProfEngmt_p != null) {
								selectedFormProfEngmt_p.getPk().setForm_no(null);
								selectedFormProfEngmt_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_IP:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormIP_p = fDao.getKtFormIP_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormIP_p != null) {
								selectedFormIP_p.getPk().setForm_no(null);
								selectedFormIP_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_SOC_ENGMT:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormSocEngmt_p = fDao.getKtFormSocEngmt_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormSocEngmt_p != null) {
								selectedFormSocEngmt_p.getPk().setForm_no(null);
								selectedFormSocEngmt_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormStaffEngmt_p = fDao.getKtFormStaffEngmt_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormStaffEngmt_p != null) {
								selectedFormStaffEngmt_p.getPk().setForm_no(null);
								selectedFormStaffEngmt_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_EA:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormEA_p = fDao.getKtFormEA_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormEA_p != null) {
								selectedFormEA_p.getPk().setForm_no(null);
								selectedFormEA_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_STARTUP:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormStartup_p = fDao.getKtFormStartup_P(Integer.valueOf(oldParamNo), getParamDataLevel(), null, null, 0);
							if (selectedFormStartup_p != null) {
								selectedFormStartup_p.getPk().setForm_no(null);
								selectedFormStartup_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case SysParam.PARAM_KT_FORM_INV_AWARD:
						for(int i = 0; i < dataLvList.size(); i++) {
							selectedFormInvAward_p = fDao.getKtFormInvAward_P(Integer.valueOf(oldParamNo), dataLvList.get(i), null, null, 0);
							if (selectedFormInvAward_p != null) {
								selectedFormInvAward_p.getPk().setForm_no(null);
								selectedFormInvAward_p.getPk().setData_level(dataLvList.get(i));
							}
						}
						copy_header_p = true;
					
					break;
				case "":
					break;
			}

			if (copy_header_p) {
				for(int i = 0; i < dataLvList.size(); i++) {
					getSelectedFormState_q();
					getSelectedFormHeader_q();
					getSelectedFormDetails_q();
					selectedFormDetails_p_list = fDao.getKtFormDetails_P(Integer.valueOf(oldParamNo), getParamDataLevel());
					if (selectedFormDetails_p_list != null) {
						for (int x = 0; x < selectedFormDetails_p_list.size(); x++) {
							selectedFormDetails_p_list.get(x).getPk().setForm_no(null);
						}
					}
					
					if ("M".equals(dataLvList.get(i))) {
						save_m(false, false);
					}
					if ("N".equals(dataLvList.get(i))) {
						save_n(false, false);
					}
				}
				copy_success = true;
			}
			/*if (copy_success) {
				// Success message
				String message = "msg.success.duplicate.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Activity Form");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}else {
				String message = "KT Activity Form can not be duplicated. ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
			ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
			System.out.println("redirectLink:"+redirectLink);
	    	eCtx.redirect(redirectLink);*/
		}
		catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot duplicate KT Activity Form (No.: " + oldParamNo + ")", ole);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.WARNING, "Cannot duplicate KT Activity Form (No.: " + oldParamNo + ")", e);
		}		
	}
	
	public boolean canProcessDataLevelC() {
		//if hasCDataLv == false || (hasCDataLv && "C".equals(getParamDataLevel()))
		return true;
	}
}


