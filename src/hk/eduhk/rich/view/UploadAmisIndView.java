package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.ejb.EJB;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.LookupFormValue;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;
import hk.eduhk.rich.entity.form.KtFormCPD_P;
import hk.eduhk.rich.entity.form.KtFormCntProj_P;
import hk.eduhk.rich.entity.form.KtFormCons_P;
import hk.eduhk.rich.entity.form.KtFormDetails_Q;
import hk.eduhk.rich.entity.form.KtFormEA_P;
import hk.eduhk.rich.entity.form.KtFormIP_P;
import hk.eduhk.rich.entity.form.KtFormInn_P;
import hk.eduhk.rich.entity.form.KtFormInvAward_P;
import hk.eduhk.rich.entity.form.KtFormProfConf_P;
import hk.eduhk.rich.entity.form.KtFormProfEngmt_P;
import hk.eduhk.rich.entity.form.KtFormSem_P;
import hk.eduhk.rich.entity.form.KtFormService;
import hk.eduhk.rich.entity.form.KtFormSocEngmt_P;
import hk.eduhk.rich.entity.form.KtFormStaffEngmt_P;
import hk.eduhk.rich.entity.form.KtFormStartup_P;
import hk.eduhk.rich.entity.form.KtFormSummary;
import hk.eduhk.rich.entity.operation.UploadStatus;
import hk.eduhk.rich.entity.operation.UploadStatusPK;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.report.KtRptSum;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.report.form.*;
import hk.eduhk.rich.report.upload.*;

@ManagedBean(name = "uploadAmisIndView")
@ViewScoped
@SuppressWarnings("serial")
public class UploadAmisIndView extends BaseView
{
	@EJB
	private KtFormService ktFormService;
	
	private static Logger logger = Logger.getLogger(UploadAmisIndView.class.getName());
	
	private static String fileUploadExtension = "xls, xlsx";
	
	private static String invalidStartEndDateMsg = "Start Date cannot be greater than End Date.";
	private static String invalidDataMsg = "Invalid value in {0}.";
	
	private List<CdcfRptPeriod> periodList = null;

	private List<KtForm> formList = null;
	
	private Map<String, List<String>> facDeptMap = null;
	private List<String> deptList = null;
	
	private List<LookupFormValue> lookupFormValueList = null;
	
	private List<KtFormSummary> ktFormSummaryList = null;
	
//	private UploadedFile uploadedFile = null;
//	private long uploadedFileSize;
//	private InputStream uploadedFileIS;
//	private String uploadedFileName = null;
	
	private Map<Integer, UploadedFile> uploadedFileMap = null;
	private Map<Integer, Workbook> workbookMap = null;
	
	private FormDAO fDao = FormDAO.getInstance();
	
	private KtRptDAO dao = KtRptDAO.getInstance();
	private CdcfRptDAO cdcfRptDao = CdcfRptDAO.getInstance();
	private PublicationDAO publicationDao = PublicationDAO.getInstance();
	
	public void fileUploadListener(FileUploadEvent event) throws IOException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		try 
		{
			UploadedFile uploadedFile = event.getFile();
			Integer paramPeriodId = (Integer) event.getComponent().getAttributes().get("periodId");
			
			
			
			String message = "";
			if(uploadedFile == null)
			{
				message = getResourceBundle().getString("msg.err.invalid.file.upload.incomplete");
				throw new Exception(message);
			}
			
			if(uploadedFile.getFileName().lastIndexOf(".") == -1)
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), fileUploadExtension);
				throw new Exception(message);
			}
			
			int index = uploadedFile.getFileName().lastIndexOf(".");
			String fileExtension = uploadedFile.getFileName().substring(index+1);
			List<String> extensionsList = new ArrayList<String>(Arrays.asList(fileUploadExtension.split(", ")));
			if(!extensionsList.contains(fileExtension))
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), fileUploadExtension);
				throw new Exception(message);
			}
			
			if(uploadedFile!=null && paramPeriodId!=null && getUploadedFileMap()!=null && getWorkbookMap()!=null) 
			{
				uploadedFileMap.put(paramPeriodId, uploadedFile);
				Workbook wb = new XSSFWorkbook(uploadedFile.getInputStream());
				workbookMap.put(paramPeriodId, wb);
			}
		}
		catch(Exception e)
		{
			String msg = e.getMessage();
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, ""));
			
			logger.log(Level.WARNING, msg);
		}
		
//		uploadedFileSize = uploadedFile.getSize();
//		uploadedFileIS = uploadedFile.getInputStream();
//		uploadedFileName = FilenameUtils.getName(uploadedFile.getFileName());

    }
	
	public String getUploadedFileName(UploadedFile file) 
	{
		if(file!=null) 
		{
			return FilenameUtils.getName(file.getFileName());
		}
		return null;
	}
	
		
	public Map<Integer, UploadedFile> getUploadedFileMap()
	{
		if(uploadedFileMap == null)
			uploadedFileMap = new HashMap<Integer, UploadedFile>();
			
		return uploadedFileMap;
	}
	

	public void setUploadedFileMap(Map<Integer, UploadedFile> uploadedFileMap)
	{
		this.uploadedFileMap = uploadedFileMap;
	}
	

	
	public Map<Integer, Workbook> getWorkbookMap()
	{
		if(workbookMap == null)
			workbookMap = new HashMap<Integer, Workbook>();
		
		return workbookMap;
	}

	
	public void setWorkbookMap(Map<Integer, Workbook> workbookMap)
	{
		this.workbookMap = workbookMap;
	}

	public List<KtFormSummary> getKtFormSummaryList()
	{
		return ktFormSummaryList;
	}

	
	public void setKtFormSummaryList(List<KtFormSummary> ktFormSummaryList)
	{
		this.ktFormSummaryList = ktFormSummaryList;
	}
	
	public List<CdcfRptPeriod> getPeriodList()
	{
		if (periodList == null) {
			List<CdcfRptPeriod> tmpCdcfPeriodList = CdcfRptDAO.getInstance().getCdcfRptPeriodList();
			periodList = tmpCdcfPeriodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return periodList;
	}
	
	public void setPeriodList(List<CdcfRptPeriod> periodList)
	{
		this.periodList = periodList;
	}
	
	
	public List<KtForm> getFormList()
	{
		if (formList == null) {
			formList = FormDAO.getInstance().getKtFormListWithCon(true);
			//summary();
		}
		return formList;
	}

	public void setFormList(List<KtForm> formList)
	{
		this.formList = formList;
	}
		
	
	

	public Map<String, List<String>> getFacDeptMap()
	{
		if(facDeptMap==null) 
		{
			facDeptMap = new HashMap<String, List<String>>();
			
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<LookupValue> l2List = dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			
			if(CollectionUtils.isNotEmpty(l1List))
			{
				for(LookupValue facLookupValue : l1List)
				{
					String fac = facLookupValue.getPk().getLookup_code();
					
					if(fac!=null) 
					{
						facDeptMap.put(fac, new ArrayList<String>());
					}
				}
				
				if(CollectionUtils.isNotEmpty(l2List)) 
				{
					for(LookupValue deptLookupValue : l2List)
					{
						String dept = deptLookupValue.getPk().getLookup_code();
						String parentFac = deptLookupValue.getParent_lookup_code();
						if(dept!=null && parentFac!=null) 
						{
							if(facDeptMap.get(parentFac)!=null) 
							{
								List<String> deptList = facDeptMap.get(parentFac);
								deptList.add(dept);
								facDeptMap.put(parentFac, deptList);
							}
						}
					}
				}
			}
		}
		return facDeptMap;
	}

	
	public void setFacDeptMap(Map<String, List<String>> facDeptMap)
	{
		this.facDeptMap = facDeptMap;
	}

	
	
	public List<String> getDeptList()
	{
		if(deptList == null) 
		{
			deptList = new ArrayList<String>();
			
			if(MapUtils.isNotEmpty(getFacDeptMap())) 
			{
				for(List<String> dList : facDeptMap.values())
				{ 
					deptList.addAll(dList);
				}
			}
		}
		return deptList;
	}

	
	public void setDeptList(List<String> deptList)
	{
		this.deptList = deptList;
	}
	

	public void exportAmisIndReport(CdcfRptPeriod selectedPeriod) 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		
		if(selectedPeriod!=null) 
		{
			try 
			{
				Workbook wb = null;
	    		wb = new XSSFWorkbook();
	    		
	    		Sheet sheet = wb.createSheet("output");
	        	sheet.createFreezePane(0, 1);
	        	
	        	CellStyle hStyle=null;
	        	// Creating a font
	            Font font= wb.createFont();
	            //font.setFontName("Arial");
	            font.setColor(IndexedColors.BLACK.getIndex());
	            font.setBold(true);
	        	hStyle=wb.createCellStyle();
	            hStyle.setFont(font);
	        	
	        	Row row = sheet.createRow(0);
	        	Cell cell = null;
	        	
	        	//First row header
	        	cell = row.createCell(0);
	    		cell.setCellValue("Output No");
	    		cell.setCellStyle(hStyle);  
	    		
	    		DateFormat df = new SimpleDateFormat("MM/yyyy", Locale.ENGLISH);
//	    		List<Integer> outputInPeriod = publicationDao.getOutputNoListByPeriod("C", df.format(selectedPeriod.getDate_from()), df.format(selectedPeriod.getDate_to()));
	    		List<String> dataList = PublicationDAO.getInstance().getAmisIndList("C", df.format(selectedPeriod.getDate_from()), df.format(selectedPeriod.getDate_to()));
	    		
	    		if(dataList != null) {
	    			for(String no : dataList) {
	    				row = sheet.createRow(sheet.getLastRowNum()+1);
	    				
	    				cell = row.createCell(0);
	    	    		cell.setCellValue(no);
	    			}
	    		}
	    		
		    	
	    		// Get the byte array of the Workbook
		    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
				wb.write(baos);
				
				// Dispose of temporary files backing this workbook on disk
				if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
				
				wb.close();
				byte[] wbBytes = baos.toByteArray();
				
				// Set the response header
				eCtx.responseReset();

				DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
				String fileName = "AmisInd_"+selectedPeriod.getChart_desc_2()+"_"+dateFormat.format(new Date())+".xlsx";
				eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
				eCtx.setResponseHeader("Expires", "-1");
				eCtx.setResponseHeader("Pragma", "private");		        
				eCtx.setResponseContentType(new Tika().detect(fileName));
				eCtx.setResponseContentLength(wbBytes.length);
				eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

				// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
				//setPrimeFacesDownloadCompleted("exportRptBtn");
				
				// Send the bytes to response OutputStream
				OutputStream os = eCtx.getResponseOutputStream();
				os.write(wbBytes);
			
				fCtx.responseComplete();

			}
			catch (IOException e) 
	    	{
				String message = "Cannot send Workbook bytes to response OutputStream ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			
		}
		
	}
	
	public void exportErrorAmisIndReport(List<String> importList, Map<Integer, String> errMsg) 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		
		if(importList!=null) 
		{
			try 
			{
				Workbook wb = null;
	    		wb = new XSSFWorkbook();
	    		
	    		Sheet sheet = wb.createSheet("output");
	        	sheet.createFreezePane(0, 1);
	        	
	        	CellStyle hStyle=null;
	        	// Creating a font
	            Font font= wb.createFont();
	            //font.setFontName("Arial");
	            font.setColor(IndexedColors.BLACK.getIndex());
	            font.setBold(true);
	        	hStyle=wb.createCellStyle();
	            hStyle.setFont(font);
	        	
	        	Row row = sheet.createRow(0);
	        	Cell cell = null;
	        	
	        	//First row header
	        	cell = row.createCell(0);
	    		cell.setCellValue("Output No");
	    		cell.setCellStyle(hStyle); 
	    		
	    		if(importList != null) {
	    			for(int i = 0 ; i < importList.size() ; ++i) {
	    				row = sheet.createRow(sheet.getLastRowNum()+1);
	    				
	    				cell = row.createCell(0);
	    	    		cell.setCellValue(importList.get(i));
	    	    		
	    	    		if(errMsg != null && errMsg.get(i) != null) {
	    	    			cell = row.createCell(1);
		    	    		cell.setCellValue(errMsg.get(i));
	    	    		}
	    			}
	    		}
	    		
		    	
	    		// Get the byte array of the Workbook
		    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
				wb.write(baos);
				
				// Dispose of temporary files backing this workbook on disk
				if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
				
				wb.close();
				byte[] wbBytes = baos.toByteArray();
				
				// Set the response header
				eCtx.responseReset();

				DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
				String fileName = "AmisInd_Error_"+dateFormat.format(new Date())+".xlsx";
				eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
				eCtx.setResponseHeader("Expires", "-1");
				eCtx.setResponseHeader("Pragma", "private");		        
				eCtx.setResponseContentType(new Tika().detect(fileName));
				eCtx.setResponseContentLength(wbBytes.length);
				eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

				// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
				//setPrimeFacesDownloadCompleted("exportRptBtn");
				
				// Send the bytes to response OutputStream
				OutputStream os = eCtx.getResponseOutputStream();
				os.write(wbBytes);
			
				fCtx.responseComplete();

			}
			catch (IOException e) 
	    	{
				String message = "Cannot send Workbook bytes to response OutputStream ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			
		}
		
	}
	
	
	public void uploadFile(Integer periodId) throws Exception
	{
		String message = "";
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		
		if(periodId != null) 
		{
			try
			{
				UploadedFile uploadedFile = getUploadedFileMap().get(periodId);
				
				Workbook workbook = getWorkbookMap().get(periodId);
				
				Map<Integer, String> errMsgMap = new HashMap<Integer, String>();

				List<String> importList = new ArrayList<String>();
				Iterator<Row> itr = workbook.getSheetAt(0).iterator();
				while(itr.hasNext()) 
				{
					Row row = itr.next();
					
					// skip header row
					if(row.getRowNum()<1) continue;
					
					Cell valueCell = row.getCell(0);
					
					CellType cellType = valueCell.getCellType();
					
					Object obj = null;
					if(cellType.equals(CellType.STRING)) 
					{
						obj = valueCell.getStringCellValue();
					}
					else if(cellType.equals(CellType.NUMERIC)) 
					{
						Double dObj = valueCell.getNumericCellValue();
						obj = dObj.intValue();
					}
					if(obj != null)
						importList.add(obj.toString());
				}
				
				CdcfRptPeriod period = cdcfRptDao.getCdcfRptPeriod(periodId);
				DateFormat df = new SimpleDateFormat("MM/yyyy", Locale.ENGLISH);
				List<Integer> outputInPeriod = publicationDao.getOutputNoListByPeriod("C", df.format(period.getDate_from()), df.format(period.getDate_to()));
				List<Integer> outputNoList = new ArrayList<Integer>();
				
				for(int i = 0 ; i < importList.size() ; ++i) {
					String importVal = importList.get(i);
					Integer outputNo = null;
					try {
						outputNo = Integer.parseInt(importVal);
					} catch (NumberFormatException nfe) {
						errMsgMap.put(i, "Output No must be a number");
					}
					if(outputNo != null) {
						if(!outputInPeriod.contains(outputNo)) {
							errMsgMap.put(i, "This Output No is not within current period");
						}
						else
							outputNoList.add(outputNo);
					}
				}
				
				if(MapUtils.isEmpty(errMsgMap))
				{
					//Perform upload
					for(String importVal : importList) {
						outputNoList.add(Integer.parseInt(importVal));
					}
					publicationDao.updateAmisInd(outputNoList, outputInPeriod);
					
					uploadedFileMap.remove(periodId);
					workbookMap.remove(periodId);
					
					message = "Successfully uploaded.";
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					
				}
				else
				{
					// return error message
					exportErrorAmisIndReport(importList, errMsgMap);
					
					// Get the byte array of the Workbook
			    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
					workbook.write(baos);
					
					// Dispose of temporary files backing this workbook on disk
					if (workbook instanceof SXSSFWorkbook) ((SXSSFWorkbook) workbook).dispose();
					
					workbook.close();
					byte[] wbBytes = baos.toByteArray();
					
					// Set the response header
					eCtx.responseReset();
					//eCtx.addResponseHeader("Cache-control", "no-cache");
					//eCtx.addResponseHeader("Pragma", "no-cache");
		
					eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
					eCtx.setResponseHeader("Expires", "-1");
					eCtx.setResponseHeader("Pragma", "private");
		
					DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
					String fileName = "Upload Failed-" + dateFormat.format(new Date()) + ".xlsx";		        
					
					eCtx.setResponseContentType(new Tika().detect(fileName));
					eCtx.setResponseContentLength(wbBytes.length);
					eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

					// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
					//setPrimeFacesDownloadCompleted("exportRptBtn");
					
					// Send the bytes to response OutputStream
					OutputStream os = eCtx.getResponseOutputStream();
					os.write(wbBytes);
				
					fCtx.responseComplete();
				}
			}
			catch(Exception e) 
			{
				message = "Unable to upload file. Exception = "+e.getMessage();
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
						
		}
	}
	
	public boolean isValidStartEndDate(Date startDate, Date endDate) 
	{
		if(startDate != null && endDate != null) 
		{
			return startDate.before(endDate);
		}
		
		return false;
	}

	
	public List<LookupFormValue> getLookupFormValueList()
	{
		if(lookupFormValueList == null && getFormList()!=null) 
		{
			List<String> formCodeList = formList.stream().map(f->f.getForm_code()).collect(Collectors.toList());
			lookupFormValueList = LookupValueDAO.getCacheInstance().getLookupFormValueList(formCodeList);
		}
		
		return lookupFormValueList;
	}

	
	public void setLookupFormValueList(List<LookupFormValue> lookupFormValueList)
	{
		this.lookupFormValueList = lookupFormValueList;
	}

	public Map<String, Map<String, List<LookupValue>>> getLookupValueMap()
	{
		//Map<formCode, Map<LookupType, List<LookupValue>>
		Map<String, Map<String, List<LookupValue>>> objMap = new LinkedHashMap<String, Map<String, List<LookupValue>>>();
		LookupValueDAO lookupValueDAO = LookupValueDAO.getCacheInstance();
		
		if(CollectionUtils.isNotEmpty(getLookupFormValueList())) 
		{
			for(LookupFormValue lookupFormValue : lookupFormValueList)
			{
				String formCode = lookupFormValue.getPk().getFormCode();
				String lookupType = lookupFormValue.getPk().getLookupType();
				
				Map<String, List<LookupValue>> lookupMap = objMap.get(formCode);
				
				if(lookupMap == null) lookupMap = new LinkedHashMap<String, List<LookupValue>>();
				
				List<LookupValue> lookupValueList = lookupValueDAO.getLookupValueList(lookupFormValue.getLookupType(), "US", "Y");
				lookupMap.put(lookupFormValue.getLookupType(), lookupValueList);
				
				objMap.put(formCode, lookupMap);
			}
		}
//		
//		
//		LookupValueDAO lookupValueDao = LookupValueDAO.getCacheInstance();
//		
//		List<LookupValue> lookupValueList;
//		
//		String lookupType = "";
//		String formCode = "";
//		
//		//KT_IP
//		{
//			formCode = "KT_IP";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_IP_FUND_SRC";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//		
//			lookupType = "KT_IP_CAT";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			lookupType = "KT_IP_ORG_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_CONSULT
//		{
//			formCode = "KT_CONSULT";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_CONS_ORG_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			lookupType = "KT_CONS_ROLE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			lookupType = "KT_CONS_FUND_SRC";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_EA
//		{
//			formCode = "KT_EA";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_EA_ACT_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_STARTUP
//		{
//			formCode = "KT_STARTUP";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_STARTUP_ACT_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_SOC_ENGMT
//		{
//			formCode = "KT_SOC_ENGMT";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_SOC_ENGMT_ACT_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_PROF_CONF
//		{
//			formCode = "KT_PROF_CONF";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_PROF_CONF_TARGET_PAX";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_SEMINAR
//		{
//			formCode = "KT_SEMINAR";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_SEM_TARGET_PAX";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_STAFF_ENGMT
//		{
//			formCode = "KT_STAFF_ENGMT";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_STAFF_ENGMT_NATURE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
		return objMap;
	}
	
	public Map<String, Map<String, List<String>>> getLookupCodeMap()
	{
		//Map<formCode, Map<LookupType, List<LookupCode>>>
		Map<String, Map<String, List<String>>> objMap = new HashMap<String, Map<String, List<String>>>();
		
		List<String> objList;
		
		Map<String, Map<String, List<LookupValue>>> lookupValueMap = getLookupValueMap();
		
		if(MapUtils.isNotEmpty(lookupValueMap))
		{
			Set<String> formCodeSet = lookupValueMap.keySet();
			
			for(String formCode : formCodeSet)
			{
				Map<String, List<LookupValue>> lookupMap = lookupValueMap.get(formCode);
				Map<String, List<String>> valueMap = new LinkedHashMap<String,List<String>>();
				
				if(MapUtils.isNotEmpty(lookupMap)) 
				{
					Set<String> lookupTypeSet = lookupMap.keySet();
					
					for(String lookupType : lookupTypeSet)
					{
						List<LookupValue> lookupValueList = lookupMap.get(lookupType);
						objList = lookupValueList.stream().map(v->v.getPk().getLookup_code()).collect(Collectors.toList());
						if(CollectionUtils.isNotEmpty(objList)) 
						{
							valueMap.put(lookupType, objList);
						}
					}
				}
				objMap.put(formCode, valueMap);
			}
			
			
		}
		
		return objMap;
	}
	
	public boolean isValidFac(String obj) 
	{
		if(MapUtils.isNotEmpty(getFacDeptMap()) && obj!=null) 
		{
			return facDeptMap.containsKey(obj);
		}
		return false;
	}

	public boolean isValidDept(String fac, String dept) 
	{
		if(MapUtils.isNotEmpty(getFacDeptMap()) && fac!=null && dept!=null) 
		{
			if(facDeptMap.get(fac)!=null) 
			{
				return facDeptMap.get(fac).contains(dept);
			}
		}
		return false;
	}
	
	public void summary(KtRptPeriod period)
	{
		if (getFormList() != null && period != null) {
			//getOldDate();
			HashSet unique=new HashSet();
			
			List<KtFormSummary> ktFormSummaryListByFormCode = null;
			
			String summaryDataLevel = "C";
			ktFormSummaryList = FormDAO.getCacheInstance().getKtFormSummaryListByDataLevel(summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
			
			
			for (KtForm f:formList) {
				switch(f.getForm_code()) {
					case SysParam.PARAM_KT_FORM_CPD:
						List<KtFormCPD_P> cpd_headerList = new ArrayList<KtFormCPD_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormCPD_P obj = fDao.getKtFormCPD_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) cpd_headerList.add(obj);
							}
						}

						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cpd_headerList.size()));
									break;
								case "income_total":
									Double income_total = cpd_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cpd_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
								case "num_hr":
									Integer num_hr = cpd_headerList.stream().filter(a -> a.getNum_stu_contact_hr() != null).mapToInt(a -> a.getNum_stu_contact_hr()).sum();
									s.setSum_value(Double.valueOf(num_hr));
									break;
							}	
						}
						break;
					case SysParam.PARAM_KT_FORM_PROF_CONF:
						List<KtFormProfConf_P> prof_conf_headerList = new ArrayList<KtFormProfConf_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormProfConf_P obj = fDao.getKtFormProfConf_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) prof_conf_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(prof_conf_headerList.size()));
									break;
								case "income_total":
									Double income_total = prof_conf_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = prof_conf_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
								case "num_present":
									Integer num_present = prof_conf_headerList.stream().filter(a -> a.getNum_presentation() != null).mapToInt(a -> a.getNum_presentation()).sum();
									s.setSum_value(Double.valueOf(num_present));
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_SEM:
						List<KtFormSem_P> sem_headerList = new ArrayList<KtFormSem_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormSem_P obj = fDao.getKtFormSem_P(d.getPk().getFormNo(), summaryDataLevel, null, null, 0);
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) sem_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(sem_headerList.size()));
									break;
								case "income_total":
									Double income_total = sem_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = sem_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_CNT_PROJ:
						List<KtFormCntProj_P> cnt_proj_headerList = new ArrayList<KtFormCntProj_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());

						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormCntProj_P obj = fDao.getKtFormCntProj_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) cnt_proj_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cnt_proj_headerList.size()));
									break;
								case "income_total":
									Double income_total = cnt_proj_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cnt_proj_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_INN:
						List<KtFormInn_P> inn_headerList = new ArrayList<KtFormInn_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormInn_P obj = fDao.getKtFormInn_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) inn_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(inn_headerList.size()));
									break;
								case "income_total":
									Double income_total = inn_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = inn_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_CONS:
						List<KtFormCons_P> cons_headerList = new ArrayList<KtFormCons_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormCons_P obj = fDao.getKtFormCons_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) cons_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cons_headerList.size()));
									break;
								case "income_total":
									Double income_total = cons_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_PROF_ENGMT:
						List<KtFormProfEngmt_P> prof_engmt_headerList = new ArrayList<KtFormProfEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormProfEngmt_P obj = fDao.getKtFormProfEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) prof_engmt_headerList.add(obj);
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(prof_engmt_headerList.size()));
									break;
								case "num_teacher":
									Integer num_teacher = prof_engmt_headerList.stream().filter(a -> a.getNum_teacher() != null).mapToInt(a -> a.getNum_teacher()).sum();
									s.setSum_value(Double.valueOf(num_teacher));
									break;
								case "num_principal":
									Integer num_principal = prof_engmt_headerList.stream().filter(a -> a.getNum_principal() != null).mapToInt(a -> a.getNum_principal()).sum();
									s.setSum_value(Double.valueOf(num_principal));
									break;
								case "num_other":
									Integer num_other = prof_engmt_headerList.stream().filter(a -> a.getNum_other() != null).mapToInt(a -> a.getNum_other()).sum();
									s.setSum_value(Double.valueOf(num_other));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_IP:
						List<KtFormIP_P> ip_headerList = new ArrayList<KtFormIP_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormIP_P obj = fDao.getKtFormIP_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) ip_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(ip_headerList.size()));
									break;
								case "income_total":
									Double income_total = ip_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
							}
						}
						
						break;
					case SysParam.PARAM_KT_FORM_SOC_ENGMT:
						List<KtFormSocEngmt_P> soc_engmt_headerList = new ArrayList<KtFormSocEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormSocEngmt_P obj = fDao.getKtFormSocEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) soc_engmt_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_attendee":
									Integer num_attendee = soc_engmt_headerList.stream().filter(a -> a.getNum_attendee() != null).mapToInt(a -> a.getNum_attendee()).sum();
									s.setSum_value(Double.valueOf(num_attendee));
									break;
								case "num_perform":
									Long num_perform = soc_engmt_headerList.stream().filter(a -> "Y".equals(a.getPerform())).count();
									s.setSum_value(Double.valueOf(num_perform));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
						List<KtFormStaffEngmt_P> staff_engmt_headerList = new ArrayList<KtFormStaffEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormStaffEngmt_P obj = fDao.getKtFormStaffEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) staff_engmt_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(staff_engmt_headerList.size()));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_EA:
						List<KtFormEA_P> ea_headerList = new ArrayList<KtFormEA_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormEA_P obj = fDao.getKtFormEA_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) ea_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(ea_headerList.size()));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_STARTUP:
						List<KtFormStartup_P> startup_headerList = new ArrayList<KtFormStartup_P>();
//						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
//														.filter(y -> y.getFormCode().equals(f.getForm_code()))
//														.collect(Collectors.toList());
						
						break;
					case SysParam.PARAM_KT_FORM_INV_AWARD:
						List<KtFormInvAward_P> inv_award_headerList = new ArrayList<KtFormInvAward_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormInvAward_P obj = fDao.getKtFormInvAward_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) inv_award_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(inv_award_headerList.size()));
									break;
								case "num_filed":
									Integer num_filed = inv_award_headerList.stream().filter(a -> a.getNum_pat_filed() != null).mapToInt(a -> a.getNum_pat_filed()).sum();
									s.setSum_value(Double.valueOf(num_filed));
									break;
								case "num_granted":
									Integer num_granted = inv_award_headerList.stream().filter(a -> a.getNum_pat_granted() != null).mapToInt(a -> a.getNum_pat_granted()).sum();
									s.setSum_value(Double.valueOf(num_granted));
									break;	
							}
						}
						break;
					case "":
						break;
				}
			}
		}
	}
}
