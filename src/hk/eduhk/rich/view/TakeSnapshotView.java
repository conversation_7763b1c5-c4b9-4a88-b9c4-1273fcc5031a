package hk.eduhk.rich.view;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.imageio.ImageIO;
import javax.persistence.OptimisticLockException;
import javax.swing.event.ChangeEvent;

import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.omnifaces.util.Faces;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;
import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.CartesianTicks;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.*;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.tuckey.web.filters.urlrewrite.utils.StringUtils;

import com.google.common.base.Strings;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.cv.CvView;
import hk.eduhk.rich.entity.FacDept;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.SnapShotLog;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.award.AwardReport;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.project.ProjectReport;
import hk.eduhk.rich.entity.project.ProjectSummary;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.DeptLabelCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.PreRpt;
import hk.eduhk.rich.entity.report.PreRptCat;
import hk.eduhk.rich.entity.report.PreRptDAO;
import hk.eduhk.rich.entity.report.StaffProjCount;
import hk.eduhk.rich.entity.report.deptFundingIntExt;
import hk.eduhk.rich.entity.report.last3YearCount;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.entity.report.staffNumberName;
import hk.eduhk.rich.entity.staff.InternetUserInfo;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffRank;
import hk.eduhk.rich.report.form.KtFormCDCFReportGroup;
import hk.eduhk.rich.report.form.KtFormCDCFSummaryGroup;
import hk.eduhk.rich.report.form.KtFormCDCFinalReportGroup;
import hk.eduhk.rich.report.form.KtFormDeptSummaryGroup;
import hk.eduhk.rich.report.form.KtFormProjStaffReportGroup;


@SuppressWarnings("serial")
@ManagedBean(name = "takeSnapshotView")
@ViewScoped
public class TakeSnapshotView extends ManageRIView
{
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private PublicationDAO publicationDao = PublicationDAO.getInstance();
	private CdcfRptDAO cdcfRptDao= CdcfRptDAO.getInstance();
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	private List<SnapShotLog> snapShotLogList;
	
	public void takeOutputSnapshot() throws SQLException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();	
		
		CdcfRptPeriod period = cdcfRptDao.getCurrentCdcfRptPeriod();
		if(period != null)
		{
			try 
			{
				publicationDao.outputSnapshot(period, getLoginUserId());
	
				String message = "Output Snapshot is Successfully Taken.";
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
		}
		else {
			String message = "Do not have current period.";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
		snapShotLogList = null;
	}
	
	public void takeProjectSnapshot() throws SQLException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();	
		
		CdcfRptPeriod period = cdcfRptDao.getCurrentCdcfRptPeriod();
		if(period != null)
		{
			try 
			{
				projDao.projectSnapshot(period, getLoginUserId());
				
				String message = "Project Snapshot is Successfully Taken.";
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
		}
		else {
			String message = "Do not have current period.";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
		snapShotLogList = null;
	}
	
	public List<SnapShotLog> getSnapShotLogList()
	{
		if (snapShotLogList == null) {
			snapShotLogList = cdcfRptDao.getSnapShotLogList();
		}
		return snapShotLogList;
	}

	
	public void setSnapShotLogList(List<SnapShotLog> snapShotLogList)
	{
		this.snapShotLogList = snapShotLogList;
	}
	
	public String getCdcfRptPeriodDesc(Integer period_id)
	{
		String result = "";
		CdcfRptPeriod period = cdcfRptDao.getCdcfRptPeriod(period_id);
		result = period.getPeriod_desc();
		return result;
	}
}
