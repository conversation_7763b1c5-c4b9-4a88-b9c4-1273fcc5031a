package hk.eduhk.rich.view;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.award.Award;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.patent.Patent;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectSummary;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.staff.InternetUserInfo;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffInfo;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.entity.staff.StaffRank;;


@SuppressWarnings("serial")
@ManagedBean(name = "searchProfileView")
@ViewScoped
public class SearchProfileView extends BaseView
{
	public static final String EMPTY_VALUE = "empty";
	
	private boolean firstSearch = false;
	private RISearchPanel searchPanel;
	
	private List<StaffIdentity> staffList;
	private List<StaffPast> exStaffList;
	
	public Map<String, StaffInfo> infoMap;
	public Map<String, String> iUserInfoStringMap;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public boolean isFirstSearch()
	{
		return firstSearch;
	}

	
	public void setFirstSearch(boolean firstSearch)
	{
		if(isExStaff() != -1) this.firstSearch = firstSearch;
		staffList = null;
		exStaffList = null;
	}

	public RISearchPanel getSearchPanel()
	{
		if(searchPanel == null) {
			searchPanel = new RISearchPanel();
			searchPanel.setUserId(getLoginUserId());
		}
		return searchPanel;
	}
	
	public int isExStaff() {
		if(getSearchPanel() != null) {
			String staffName = searchPanel.getStaffName();
			List<String> rankList = searchPanel.getSelectedRankList();
			String acadStaff = searchPanel.getAcadStaff();
			List<String> departmentList = searchPanel.getSelectedDepartmentList();
			String exStaffName = searchPanel.getExStaffName();
			String formStaffNum = searchPanel.getFormStaffNum();
			boolean staff = StringUtils.isNotBlank(staffName) || 
							!CollectionUtils.isEmpty(rankList) || 
							(StringUtils.isNotBlank(acadStaff) && !acadStaff.equals(EMPTY_VALUE)) ||
							!CollectionUtils.isEmpty(departmentList);
			boolean exStaff = StringUtils.isNotBlank(exStaffName) ||
								StringUtils.isNotBlank(formStaffNum);
			if(staff && !exStaff) return 0;
			else if(!staff && exStaff) return 1;
		}
		return -1;
	}
	
	public List<StaffIdentity> getStaffList() {
		if(staffList == null) {
			List<String> idList = searchPanel.getStaffQueryChain().queryIdList();
			StaffDAO dao = StaffDAO.getCacheInstance();
			CvDAO cvdao = CvDAO.getCacheInstance();
			staffList = new ArrayList<StaffIdentity>();
			if(!CollectionUtils.isEmpty(idList)) {
				try {
					staffList = dao.getAcadStaffMapByIdList(idList);
				}
				catch(Exception e){
					logger.log(Level.WARNING, "Cannot getAcadStaffMapByIdList ", e);
				}
				infoMap = dao.getStaffInfoMapByIdList(idList);
				iUserInfoStringMap = new HashMap<String, String>();
				for (String id : idList) {
					List<InternetUserInfo> intInfoList = cvdao.getInternetUserInfoByStaffNo(id);
					String value = "";
					if (intInfoList != null) {
						for (InternetUserInfo i:intInfoList) {
							value += i.getPost() + ", <span style='color:#ff5722'>" +i.getDeptdesc() + "</span><br/>";
						}
					}
					iUserInfoStringMap.put(id, value);
				}
			}
		}
		return staffList;
	}
	
	public List<StaffPast> getExStaffList() {
		if(exStaffList == null) {
			List<String> idList = searchPanel.getExStaffQueryChain().queryIdList();
			StaffDAO dao = StaffDAO.getCacheInstance();
			CvDAO cvdao = CvDAO.getCacheInstance();
			exStaffList = new ArrayList<StaffPast>();
			if(!CollectionUtils.isEmpty(idList)) {
				try {
					exStaffList = dao.getPastStaffMapByIdList(idList);
				}
				catch(Exception e){
					logger.log(Level.WARNING, "Cannot getPastStaffMapByIdList ", e);
				}
				infoMap = dao.getStaffInfoMapByIdList(idList);
				iUserInfoStringMap = new HashMap<String, String>();
				for (String id : idList) {
					List<InternetUserInfo> intInfoList = cvdao.getInternetUserInfoByStaffNo(id);
					String value = "";
					if (intInfoList != null) {
						for (InternetUserInfo i:intInfoList) {
							value += i.getPost() + ", <span style='color:#ff5722'>" +i.getDeptdesc() + "</span><br/>";
						}
					}
					iUserInfoStringMap.put(id, value);
				}
			}
		}
		return exStaffList;
	}
	
	public Map<String, StaffInfo> getInfoMap()
	{
		return infoMap;
	}


	
	public Map<String, String> getiUserInfoStringMap()
	{
		return iUserInfoStringMap;
	}
	
	

	
	
}
