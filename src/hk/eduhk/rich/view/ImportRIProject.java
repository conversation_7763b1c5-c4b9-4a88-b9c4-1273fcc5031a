package hk.eduhk.rich.view;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Logger;

import hk.eduhk.rich.entity.importRI.*;


@SuppressWarnings("serial")
public class ImportRIProject implements Serializable
{	
	private List<ImportRIProjectV> projectList;
	private Map<ImportRIProjectV_PK, ImportRIProjectV> projectMap;
	private ImportRIProjectV_PK selectedPreview;
	private String paramPid;
	
	
	private ImportRIStatus_PK selectedIgnoreProject;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public List<ImportRIProjectV> getProjectList() {
		if(projectList == null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			projectList = dao.getNewImportRIProjectV(getParamPid());
			projectMap = new HashMap<ImportRIProjectV_PK, ImportRIProjectV>();
			if(projectList == null) {
				projectList = new ArrayList<ImportRIProjectV>();
			}
			else {
				for(ImportRIProjectV v : projectList) {
					projectMap.put(v.getPk(), v);
				}
			}
				
		}
		return projectList;
	}
	
	
	public void setProjectList(List<ImportRIProjectV> projectList)
	{
		this.projectList = projectList;
	}



	public void setSelectedPreview(String area_code, String source_id, String staff_number) {
		selectedPreview = new ImportRIProjectV_PK();
		selectedPreview.setArea_code(area_code);
		selectedPreview.setSource_id(source_id);
		selectedPreview.setStaff_number(staff_number);
	}
	
	
	public ImportRIProjectV_PK getSelectedPreview()
	{
		return selectedPreview;
	}

	public Map<ImportRIProjectV_PK, ImportRIProjectV> getProjectMap()
	{
		return projectMap;
	}
	
	
	public ImportRIProjectV getProject() {
		if(getProjectMap() != null)
			return getProjectMap().get(getSelectedPreview());
		else
			return new ImportRIProjectV();
	}
	
	public void setSelectedIgnoreProject(String area_code, String source_id, String staff_number) {
		selectedIgnoreProject = new ImportRIStatus_PK();
		selectedIgnoreProject.setArea_code(area_code);
		selectedIgnoreProject.setSource_id(source_id);
		selectedIgnoreProject.setStaff_number(staff_number);
	}
	
	public void setSelectedIgnoreProject() {
		selectedIgnoreProject = new ImportRIStatus_PK();
		selectedIgnoreProject.setArea_code(selectedPreview.getArea_code());
		selectedIgnoreProject.setSource_id(selectedPreview.getSource_id());
		selectedIgnoreProject.setStaff_number(selectedPreview.getStaff_number());
	}
	
	
	public ImportRIStatus_PK getSelectedIgnoreProject()
	{
		return selectedIgnoreProject;
	}
		
	
	public String getParamPid()
	{
		return paramPid;
	}


	
	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}
}
