package hk.eduhk.rich.view.rae;

import java.awt.event.ActionEvent;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Reader;
import java.sql.Clob;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.persistence.OptimisticLockException;
import javax.resource.cci.ResultSet;
import javax.transaction.UserTransaction;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.rae.RaeReport;
import hk.eduhk.rich.entity.rae.RaeReportDAO;

@ManagedBean(name = "raeRptView")
@ViewScoped
@SuppressWarnings("serial")
public class RaeReportView extends BaseView
{
	private static Logger logger = Logger.getLogger(RaeReport.class.getName());
	private List<RaeReport> raeReportList = null;
	private RaeReport selectedRaeReport;
	private RaeReport removeRaeReport;
	private String selectedUoA;
	private String selectedReportId;
	private RaeReportDAO dao = RaeReportDAO.getInstance();
	
	public void reloadList() {
		raeReportList = null;
	}
	
	
	public String getSelectedUoA()
	{
		return selectedUoA;
	}

	
	public void setSelectedUoA(String selectedUoA)
	{
		this.selectedUoA = selectedUoA;
	}

	
	
	public String getSelectedReportId()
	{
		return selectedReportId;
	}
	
	public void setSelectedReportId(String selectedReportId) {
	    this.selectedReportId = selectedReportId;
	}
	
	public void setSelectedReportId(ActionEvent event) {
	    String reportId = FacesContext.getCurrentInstance()
	                                 .getExternalContext()
	                                 .getRequestParameterMap()
	                                 .get("reportId");
	    this.selectedReportId = reportId; // Set the selectedReportId
	}
	
	public List<RaeReport> getRaeReportList()
	{
		if (raeReportList == null)
		{
			raeReportList = dao.getRaeReportList();
		}
		return raeReportList;
	}
	
	
	public RaeReport getSelectedRaeReport()
	{
		return selectedRaeReport;
	}

	
	public void setSelectedRaeReport(RaeReport selectedRaeReport)
	{
		this.selectedRaeReport = selectedRaeReport;
	}

	public void onRowEdit(RowEditEvent<RaeReport> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
        	try {
        		RaeReportDAO dao = RaeReportDAO.getInstance();

    			//Check staff number is unique
    			int count = 0;
    			for (RaeReport r: raeReportList){
    				if (event.getObject().getReport_id().equals(r.getReport_id())) {
    					count++;
    				}
    			}
    			if (count > 1) {
    				isDuplicateKey = true;
    			}
    			
    			if (isDuplicateKey) {
    				String param = "Report ID";
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    			
    			////Check data
    			if (event.getObject().getReport_id() == null) {
        			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Report ID cannot be null", ""));
        		}

    			
    			//Update RaeReport
        		if (fCtx.getMessageList().isEmpty()) {
        			event.getObject().setUserstamp(getLoginUserId());
        			dao.updateRaeReport(event.getObject());
        			if (removeRaeReport != null) {
         				dao.deleteRaeReport(removeRaeReport.getReport_id());
         				removeRaeReport = null;
         			}
        			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
        			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getReport_id());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedRaeReport = null;
	        		raeReportList = null;
        		}
        	}
        	catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("RaeReport");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
	            
	        
    }
	
	public void onRowCancel(RowEditEvent<RaeReport> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getReport_id()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();

 
    }
    
    public void onAddNew() {
    	RaeReport newRaeReport = new RaeReport();
    	raeReportList.add(0, newRaeReport);
    }
   
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		RaeReportDAO dao = RaeReportDAO.getInstance();
    		removeRaeReport =  dao.getRaeReportById((Integer)event.getOldValue());
    	}
    }    
    
    public void deleteRaeReport() {
    	if (selectedRaeReport != null) {
    		try {
    			if (selectedRaeReport.getReport_id() != null) {
    				RaeReportDAO dao = RaeReportDAO.getInstance();
			    	dao.deleteRaeReport(Integer.valueOf(selectedRaeReport.getReport_id()));
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedRaeReport.getReport_id());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		raeReportList.remove(selectedRaeReport);
		        selectedRaeReport = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedRaeReport.getReport_id());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
    
    public void generateReport() {
        // Validate selectedReportId
        if (selectedReportId == null || selectedReportId.trim().isEmpty()) {
            String errorMessage = "Selected Report ID cannot be null or empty.";
            logger.log(Level.WARNING, errorMessage);
            FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", errorMessage));
            return; // Exit the method early
        }
        
        try {
            // Fetch the report details from the DAO
            int reportId = Integer.parseInt(selectedReportId); // This can throw NumberFormatException
            RaeReport rpt = dao.getRaeReportById(reportId);

            // Check if the report exists
            if (rpt == null) {
                String errorMessage = "Report not found for ID: " + selectedReportId;
                logger.log(Level.WARNING, errorMessage);
                FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", errorMessage));
                return; // Exit the method early
            }

            // Set userstamp and timestamp
            Date currentDateTime = new Date();
            rpt.setReport_userstamp(getLoginUserId());
            rpt.setReport_timestamp(currentDateTime);

            // Delete existing data from the table
            dao.deleteData(rpt.getTable_name());

            // Insert new data into the table
            dao.insertData(rpt.getTable_name(), getLoginUserId());
            
            //Report D2, D5
            /*if (reportId == 4 || reportId == 7) {	
            	dao.updateHashKey(rpt.getTable_name(), "URL_ADDITIONAL", "KEY_ADDITIONAL");
            	dao.updateHashKey(rpt.getTable_name(), "URL_SUPPORT", "KEY_SUPPORT");
            	dao.updateHashKey(rpt.getTable_name(), "URL_FULL_VER", "KEY_FULL_VER");
            	dao.updateHashKey(rpt.getTable_name(), "URL_TOC", "KEY_TOC");
            	dao.updateHashKey(rpt.getTable_name(), "URL_TOC_OTH", "KEY_TOC_OTH");
            	dao.updateHashKey(rpt.getTable_name(), "PANEL_10_URL_EVIDENCE", "PANEL_10_KEY_EVIDENCE");
            	dao.updateHashKey(rpt.getTable_name(), "PANEL_12_URL_EVIDENCE", "PANEL_12_KEY_EVIDENCE");
            }*/
            
            //Report D4
            if (reportId == 6) {
            	dao.updateHashKey(rpt.getTable_name(), "URL_REF", "KEY_REF");
            	//dao.updateHyphen(rpt.getTable_name(), "ISBN");
            	//dao.updateHyphen(rpt.getTable_name(), "ISSN");
            	//dao.updateHyphen(rpt.getTable_name(), "EISSN");
            }
            
            // Update the report in the database
            dao.updateRaeReport(rpt);
            raeReportList = null;
            // Add success message
            FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, "Success", "Report ID "+selectedReportId+ " generated successfully."));
        } catch (NumberFormatException e) {
            // Handle invalid selectedReportId format
            String errorMessage = "Invalid Report ID format: " + selectedReportId;
            logger.log(Level.WARNING, errorMessage, e);
            FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", errorMessage));
        } catch (Exception e) {
            // Handle any other exceptions
            String errorMessage = "Failed to generate report for ID: " + selectedReportId;
            logger.log(Level.SEVERE, errorMessage, e);
            FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", errorMessage));
        }
    }
    
    public void exportTableToExcel(String tableName, Integer reportId, String fileName) throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        ExternalContext eCtx = fCtx.getExternalContext();
        
        boolean filterByUoA = false;
        if (fileName.contains("<UoA>")) {
            if (selectedUoA != null) {
            	filterByUoA = true;
            }
        }
        
        // Sanitize file name
        fileName = sanitizeFileName(fileName);

        // Validate table name to prevent SQL injection
        if (!isValidTableName(tableName)) {
            throw new IllegalArgumentException("Invalid table name: " + tableName);
        }

        // Fetch column mapping data from RH_RAE_REPORT_MAP
        List<Map<String, Object>> columnMappings = fetchColumnMappings(reportId);

        if (columnMappings.isEmpty()) {
            throw new IllegalArgumentException("No column mappings found for reportId: " + reportId);
        }

        // Sort column mappings by COLUMN_SEQ
        columnMappings.sort(Comparator.comparingInt(m -> ((Number) m.get("COLUMN_SEQ")).intValue()));

        // Fetch data from the database
        List<Map<String, Object>> tableData = fetchTableData(tableName);
        
        // Filter the tableData by "UNIT_OF_ASS_CODE" if filterByUoA is true
        if (filterByUoA && selectedUoA != null) {
            tableData = tableData.stream()
                    .filter(row -> selectedUoA.equals(row.get("UNIT_OF_ASS_CODE")))
                    .collect(Collectors.toList());
        }

        if (tableData.isEmpty()) {
        	FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, "Error", "No data found in report "+ fileName + " of "+selectedUoA));
        	return;
            //throw new IllegalArgumentException("No data found for table: " + tableName);
        }

        // Create Excel workbook and sheet
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
                Workbook workbook = new XSSFWorkbook()) {

               Sheet sheet = workbook.createSheet(fileName);
               writeHeaders(sheet, columnMappings);
               
               // Modified writeData call to handle CLOBs
               writeDataWithClobSupport(sheet, tableData, columnMappings);

               autoSizeColumns(sheet, columnMappings.size());
               workbook.write(baos);
               byte[] wbBytes = baos.toByteArray();

               prepareResponse(eCtx, fileName, wbBytes.length);

               try (OutputStream os = eCtx.getResponseOutputStream()) {
                   os.write(wbBytes);
               }

               fCtx.responseComplete();

           } catch (IOException e) {
               throw new IOException("Failed to write Excel file: " + e.getMessage(), e);
           }
    }

    // Helper methods
    private String sanitizeFileName(String fileName) {
    	// Replace <UNI> with 05
        fileName = fileName.replace("<UNI>", "05");

        // Replace <UoA> with selectedUoA (handle null case)
        if (fileName.contains("<UoA>")) {
            if (selectedUoA != null) {
                fileName = fileName.replace("<UoA>", selectedUoA);
            } else {
                // Handle null case: replace <UoA> with a default value or skip
                fileName = fileName.replace("<UoA>", "ALL"); // or use "NULL" or another placeholder
            }
        }

        // Replace invalid characters (if needed)
        fileName = fileName.replaceAll("[^a-zA-Z0-9.-]", "_");

        return fileName;
    }

    private boolean isValidTableName(String tableName) {
        return tableName.matches("[a-zA-Z0-9_]+");
    }

    private List<Map<String, Object>> fetchColumnMappings(Integer reportId) throws Exception {
        try {
            return dao.getColumnMappingsForTable(reportId);
        } catch (Exception e) {
            throw new Exception("Failed to fetch column mappings: " + e.getMessage(), e);
        }
    }

    private List<Map<String, Object>> fetchTableData(String tableName) throws Exception {
        try {
            return dao.getReportData(tableName);
        } catch (Exception e) {
            throw new Exception("Failed to fetch table data: " + e.getMessage(), e);
        }
    }

    private void writeHeaders(Sheet sheet, List<Map<String, Object>> columnMappings) {
        Row headerRow = sheet.createRow(0);
        int columnIndex = 0;
        for (Map<String, Object> columnMapping : columnMappings) {
            String columnName = (String) columnMapping.get("FIELD_NAME");
            headerRow.createCell(columnIndex++).setCellValue(columnName);
        }
    }

    private void writeData(Sheet sheet, List<Map<String, Object>> tableData, List<Map<String, Object>> columnMappings) {
        int rowIndex = 1;
        for (Map<String, Object> rowData : tableData) {
            Row row = sheet.createRow(rowIndex++);
            int columnIndex = 0;
            for (Map<String, Object> columnMapping : columnMappings) {
                String columnName = (String) columnMapping.get("COLUMN_NAME");
                Object value = rowData.get(columnName);
                row.createCell(columnIndex++).setCellValue(value != null ? value.toString() : "");
            }
        }
    }

    private void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
        }
    }
    
 // New method to handle CLOB data
    private void writeDataWithClobSupport(Sheet sheet, List<Map<String, Object>> tableData, 
                                         List<Map<String, Object>> columnMappings) {
        int rowNum = 1; // Start after header row
        
        for (Map<String, Object> row : tableData) {
            Row excelRow = sheet.createRow(rowNum++);
            int colNum = 0;
            
            for (Map<String, Object> column : columnMappings) {
                String columnName = (String) column.get("COLUMN_NAME");
                Object value = row.get(columnName);
                
                // Handle CLOB objects
                if (value instanceof Clob) {
                    value = clobToString((Clob) value);
                }
                
                // Handle other special cases if needed
                if (value == null) {
                    value = "";
                }
                
                Cell cell = excelRow.createCell(colNum++);
                cell.setCellValue(value.toString());
            }
        }
    }

    // Helper method to convert CLOB to String
    private String clobToString(Clob clob) {
        if (clob == null) {
            return "";
        }
        
        try {
            long length = clob.length();
            if (length > Integer.MAX_VALUE) {
                // Handle very large CLOBs by reading in chunks
                return readLargeClob(clob);
            }
            return clob.getSubString(1, (int) length);
        } catch (SQLException e) {
            // Log error and return empty string
            //System.err.println("Error converting CLOB to string: " + e.getMessage());
            return "";
        }
    }

    // Helper method for very large CLOBs
    private String readLargeClob(Clob clob) throws SQLException {
        StringBuilder sb = new StringBuilder();
        Reader reader = clob.getCharacterStream();
        char[] buffer = new char[4096];
        int charsRead;
        
        try {
            while ((charsRead = reader.read(buffer)) != -1) {
                sb.append(buffer, 0, charsRead);
            }
        } catch (IOException e) {
            throw new SQLException("Error reading large CLOB", e);
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                // Ignore close error
            }
        }
        
        return sb.toString();
    }

    
    private void prepareResponse(ExternalContext eCtx, String fileName, int contentLength) {
        eCtx.responseReset();
        eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
        eCtx.setResponseHeader("Expires", "-1");
        eCtx.setResponseHeader("Pragma", "private");
        eCtx.setResponseContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        eCtx.setResponseContentLength(contentLength);
        eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");
    }


}
