package hk.eduhk.rich.view.rae;

import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.importRI.*;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.rae.RaeOutputDAO;
import hk.eduhk.rich.entity.rae.RaeOutputDetails;
import hk.eduhk.rich.entity.rae.RaeOutputDetails_PK;
import hk.eduhk.rich.entity.rae.RaeOutputHeader;
import hk.eduhk.rich.entity.rae.RaeOutputStatus;
import hk.eduhk.rich.entity.rae.RaeOutputStatus_PK;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;


@SuppressWarnings("serial")
public class ImportRAEOutput extends BaseView implements Serializable
{	
	private List<ImportRAEOutputV> outputList;
	private Map<Integer, ImportRAEOutputV> outputMap;
	private Integer selectedOutputNo;
	private String paramPid;
	
	public static final String RAE_INFO_COMP_N = "N";
	public static final String RAE_SEL_TYPE_NS = "NS";
	public static final String RAE_LANG_E 	   = "E";
	public static final String RAE_DOI_IND_Y   = "Y";
	
	
	
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public List<ImportRAEOutputV> getOutputList() {
		
		if(outputList == null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			StaffDAO sdao 	= StaffDAO.getCacheInstance();
			StaffIdentity s = sdao.getStaffDetailsByPid(Integer.parseInt(getParamPid()));
			if (s != null) {
				outputList = dao.getImportRAEOutputV(s.getStaff_number());
			}
			
			outputMap = new HashMap<Integer, ImportRAEOutputV>();
			
			
			if(outputList == null) {
				outputList = new ArrayList<ImportRAEOutputV>();
			}
			else {
				for(ImportRAEOutputV v : outputList) {
					outputMap.put(v.getOutput_no(), v);
				}
			}
				
		}

		return outputList;
	}
	
	
	public void setOutputList(List<ImportRAEOutputV> outputList)
	{
		this.outputList = outputList;
	}



	public Map<Integer, ImportRAEOutputV> getOutputMap()
	{
		return outputMap;
	}
	
	
	public ImportRAEOutputV getOutput() {
		
		if(getOutputMap() != null)
			return getOutputMap().get(getSelectedOutputNo());
		else
			return new ImportRAEOutputV();
	}
	
	
	public String getParamPid()
	{
		return paramPid;
	}


	
	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}


	
	public Integer getSelectedOutputNo()
	{
		return selectedOutputNo;
	}


	
	public void setSelectedOutputNo(Integer selectedOutputNo)
	{

		this.selectedOutputNo = selectedOutputNo;
	}


	
	public void setOutputMap(Map<Integer, ImportRAEOutputV> outputMap)
	{
		this.outputMap = outputMap;
	}
	
	public String importRI(Integer outputNo) throws IOException  {
		
		RaeOutputDAO raeDao = RaeOutputDAO.getCacheInstance();
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ResourceBundle bundle = getResourceBundle();
		String message = "";
		
		setSelectedOutputNo(outputNo);
		ImportRAEOutputV output = getOutput();
		
		try {
						
			//Saving Header
			RaeOutputHeader outputHeader = new RaeOutputHeader();
			outputHeader.setRiNo(output.getOutput_no());
			outputHeader.setRel_lang(output.getLanguage());

			if(output.getLanguage().equals(RAE_LANG_E))
				outputHeader.setTitle_of_eng_output(output.getTitle_of_output());
			else
				outputHeader.setTitle_of_non_eng_output(output.getTitle_of_output());

			outputHeader.setSap_output_type(output.getSap_output_type());
			outputHeader.setSap_refered_journal(output.getSap_refered_journal());
			outputHeader.setTitle_paper_art(output.getTitle_paper_art());
			outputHeader.setVol_issue(output.getVol_issue());
			outputHeader.setPage_num(output.getPage_num());
			outputHeader.setCity(output.getCity());

			outputHeader.setFrom_month(parseIntValue(output.getFrom_month()));
			outputHeader.setFrom_year(parseIntValue(output.getFrom_year()));
			outputHeader.setTo_month(parseIntValue(output.getTo_month()));
			outputHeader.setTo_year(parseIntValue(output.getTo_year()));

			outputHeader.setPublisher(output.getPublisher());
			outputHeader.setTotal_no_of_author(Integer.parseInt(output.getTotal_no_of_author()));
			
			if(output.getLanguage().equals(RAE_LANG_E))
				outputHeader.setList_of_author_eng(output.getName_other_pos());
			else
				outputHeader.setList_of_author_non_eng(output.getName_other_pos());
			
			outputHeader.setIsbn(output.getIsbn());

			
			if(output.getIssn()!= null && output.getIssn().length() == 9)  
					outputHeader.setIssn(output.getIssn());

			if(output.getEissn()!= null && output.getEissn().length() == 9) 
					outputHeader.setEissn(output.getEissn());
			
			
			outputHeader.setDoi(output.getDoi());
			
			if(output.getDoi() != null )
				outputHeader.setRo_with_doi_ind(RAE_DOI_IND_Y);
			
			outputHeader.setUrl_full_ver(output.getFulltext_url());
			outputHeader.setApa_citation(output.getApa_citation());
			outputHeader.setArticle_no(output.getArticle_num());
			
			//System.out.println("Header:" + outputHeader);
			
			raeDao.deleteAllSelectOutputHeader(outputNo);
			raeDao.updateRaeOutputHeader(outputHeader);
			Integer newOutputNo = raeDao.getRaeOutputHeaderByRiNo(outputNo).getOutput_no();
			
		
			//Saving Status
			RaeOutputStatus outputStatus = new RaeOutputStatus ();
			RaeOutputStatus_PK statusPK = new RaeOutputStatus_PK ();
			
			statusPK.setOutput_no(newOutputNo);
			statusPK.setStaff_number(output.getStaff_no());
			
			outputStatus.setPk(statusPK);
			outputStatus.setPid(Integer.parseInt(getParamPid()));
			outputStatus.setInfoComp(RAE_INFO_COMP_N);
			outputStatus.setSelType(RAE_SEL_TYPE_NS);
			
			raeDao.deleteAllSelectOutputStatus(output.getStaff_no(), newOutputNo);
			raeDao.updateRaeOutputStatus(outputStatus);
			
			//System.out.println("Status:" + outputStatus);
			
	
			//Saving Details
			List<OutputDetails_P> outputDetailList = PublicationDAO.getCacheInstance().getOutputDetails_P(outputNo,"P");
			
			raeDao.deleteAllContributor(newOutputNo);
			
			for(OutputDetails_P outputDetail :outputDetailList) {
				
				RaeOutputDetails outputDTL = new RaeOutputDetails();
				RaeOutputDetails_PK outputDTLpk = new RaeOutputDetails_PK();
				
				outputDTLpk.setLine_no(outputDetail.getPk().getLine_no());
				outputDTLpk.setOutput_no(newOutputNo);
				
				outputDTL.setPk(outputDTLpk);
				outputDTL.setAuthorship_name(outputDetail.getAuthorship_name());
				outputDTL.setAuthorship_type(outputDetail.getAuthorship_type());
				outputDTL.setNon_ied_staff_flag(outputDetail.getNon_ied_staff_flag());
				if( outputDetail.getAuthorship_person_id() != null )
					outputDTL.setAuthorship_person_id(Integer.parseInt(outputDetail.getAuthorship_person_id()));
				outputDTL.setAuthorship_assignment_id(outputDetail.getAuthorship_assignment_id());
				outputDTL.setAuthorship_staff_no(outputDetail.getAuthorship_staff_no());
				outputDTL.setCollab_percent(outputDetail.getCollab_percent());
				outputDTL.setAuthorship_dtl_type(outputDetail.getAuthorship_dtl_type());
				
				raeDao.updateRaeOutputDetails(outputDTL);
				//System.out.println("Detail:" + outputDTL);
				
			}
		
		
		
		
		message = "Existing RI No."+outputNo+" is imported successfully.";
		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
		
		}
     	catch (Exception e)
 		{
 			message = bundle.getString("msg.err.unexpected");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
		
		//System.out.println("Header:" + outputHeader);
		
		return redirect("manageRAEOutput") + "&pid=" + getParamPid();
	}
		
	
	
}
