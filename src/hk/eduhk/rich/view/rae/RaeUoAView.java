package hk.eduhk.rich.view.rae;

import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.rae.RaeUOA;
import hk.eduhk.rich.entity.rae.RaeUOADAO;

@ManagedBean(name = "raeUoAView")
@ViewScoped
@SuppressWarnings("serial")
public class RaeUoAView extends BaseView
{
	private static Logger logger = Logger.getLogger(RaeUOA.class.getName());
	private List<RaeUOA> raeUoAList = null;
	private RaeUOA selectedRaeUoA;
	private RaeUOA removeRaeUoA;

	
	public void reloadRaeUoAList() {
		raeUoAList = null;
	}
	
	public List<RaeUOA> getRaeUoAList()
	{
		if (raeUoAList == null)
		{
			RaeUOADAO dao = RaeUOADAO.getInstance();
			raeUoAList = dao.getRaeUOAList();
		}
		return raeUoAList;
	}
	
	
	public RaeUOA getSelectedRaeUoA()
	{
		return selectedRaeUoA;
	}

	
	public void setSelectedRaeUoA(RaeUOA selectedRaeUoA)
	{
		this.selectedRaeUoA = selectedRaeUoA;
	}

	public void onRowEdit(RowEditEvent<RaeUOA> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
        	try {
        		RaeUOADAO dao = RaeUOADAO.getInstance();

    			//Check staff number is unique
    			int count = 0;
    			for (RaeUOA r: raeUoAList){
    				if (event.getObject().getUoaCode().equals(r.getUoaCode())) {
    					count++;
    				}
    			}
    			if (count > 1) {
    				isDuplicateKey = true;
    			}
    			
    			if (isDuplicateKey) {
    				String param = "Staff Number";
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    			
    			////Check data
    			if (event.getObject().getUoaCode().isEmpty()) {
        			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Staff number cannot be null", ""));
        		}
     
    			if (event.getObject().getUoaCode().length() > 10) {
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Staff number is too long", ""));
    			}

    			
    			//Update RaeUoA
        		if (fCtx.getMessageList().isEmpty()) {
        			event.getObject().setUserstamp(getLoginUserId());
        			dao.updateRaeUOA(event.getObject());
        			if (removeRaeUoA != null) {
         				dao.deleteRaeUOA(removeRaeUoA.getUoaCode());
         				removeRaeUoA = null;
         			}
        			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
        			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getUoaCode());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedRaeUoA = null;
	        		raeUoAList = null;
        		}
        	}
        	catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("RaeUoA");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
	            
	        
    }
	
	public void onRowCancel(RowEditEvent<RaeUOA> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getUoaCode()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();

 
    }
    
    public void onAddNew() {
    	RaeUOA newRaeUoA = new RaeUOA();
    	raeUoAList.add(0, newRaeUoA);
    }
   
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		RaeUOADAO dao = RaeUOADAO.getInstance();
    		removeRaeUoA =  dao.getRaeUOAByCode((String)event.getOldValue());
    	}
    }    
    
    public void deleteRaeUoA() {
    	if (selectedRaeUoA != null) {
    		try {
    			if (selectedRaeUoA.getUoaCode() != null) {
    				RaeUOADAO dao = RaeUOADAO.getInstance();
			    	dao.deleteRaeUOA(selectedRaeUoA.getUoaCode());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedRaeUoA.getUoaCode());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		raeUoAList.remove(selectedRaeUoA);
		        selectedRaeUoA = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedRaeUoA.getUoaCode());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
}
