package hk.eduhk.rich.view.rae;

import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.rae.RaeStaff;
import hk.eduhk.rich.entity.rae.RaeStaffDAO;

@ManagedBean(name = "raeStaffView")
@ViewScoped
@SuppressWarnings("serial")
public class RaeStaffView extends BaseView
{
	private static Logger logger = Logger.getLogger(RaeStaff.class.getName());
	private List<RaeStaff> raeStaffList = null;
	private RaeStaff selectedRaeStaff;
	private RaeStaff removeRaeStaff;

	
	public void reloadRaeStaffList() {
		raeStaffList = null;
	}
	
	public List<RaeStaff> getRaeStaffList()
	{
		if (raeStaffList == null)
		{
			RaeStaffDAO dao = RaeStaffDAO.getInstance();
			raeStaffList = dao.getRaeStaffList();
		}
		return raeStaffList;
	}
	
	
	public RaeStaff getSelectedRaeStaff()
	{
		return selectedRaeStaff;
	}

	
	public void setSelectedRaeStaff(RaeStaff selectedRaeStaff)
	{
		this.selectedRaeStaff = selectedRaeStaff;
	}

	public void onRowEdit(RowEditEvent<RaeStaff> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
        	try {
        		RaeStaffDAO dao = RaeStaffDAO.getInstance();

    			//Check staff number is unique
    			int count = 0;
    			for (RaeStaff r: raeStaffList){
    				if (event.getObject().getStaffNumber().equals(r.getStaffNumber())) {
    					count++;
    				}
    			}
    			if (count > 1) {
    				isDuplicateKey = true;
    			}
    			
    			if (isDuplicateKey) {
    				String param = "Staff Number";
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    			
    			////Check data
    			if (event.getObject().getStaffNumber().isEmpty()) {
        			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Staff number cannot be null", ""));
        		}
     
    			if (event.getObject().getStaffNumber().length() > 10) {
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Staff number is too long", ""));
    			}

    			
    			//Update RaeStaff
        		if (fCtx.getMessageList().isEmpty()) {
        			event.getObject().setUserstamp(getLoginUserId());
        			dao.updateRaeStaff(event.getObject());
        			if (removeRaeStaff != null) {
         				dao.deleteRaeStaff(removeRaeStaff.getStaffNumber());
         				removeRaeStaff = null;
         			}
        			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
        			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getStaffNumber());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedRaeStaff = null;
	        		raeStaffList = null;
        		}
        	}
        	catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("RaeStaff");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
	            
	        
    }
	
	public void onRowCancel(RowEditEvent<RaeStaff> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getStaffNumber()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();

 
    }
    
    public void onAddNew() {
    	RaeStaff newRaeStaff = new RaeStaff();
    	raeStaffList.add(0, newRaeStaff);
    }
   
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		RaeStaffDAO dao = RaeStaffDAO.getInstance();
    		removeRaeStaff =  dao.getRaeStaffByStaffNo((String)event.getOldValue());
    	}
    }    
    
    public void deleteRaeStaff() {
    	if (selectedRaeStaff != null) {
    		try {
    			if (selectedRaeStaff.getStaffNumber() != null) {
			    	RaeStaffDAO dao = RaeStaffDAO.getInstance();
			    	dao.deleteRaeStaff(selectedRaeStaff.getStaffNumber());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedRaeStaff.getStaffNumber());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		raeStaffList.remove(selectedRaeStaff);
		        selectedRaeStaff = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedRaeStaff.getStaffNumber());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
    
    public void updateRaeStaffSerial() 
    {
    	if (getRaeStaffList() != null) {
    		ResourceBundle bundle = getResourceBundle();
		    FacesContext fCtx = FacesContext.getCurrentInstance();
		    String message = "";
    		try {
    			RaeStaffDAO dao = RaeStaffDAO.getInstance();
		    	dao.updateRaeStaffSerial();
		    	raeStaffList = null;
		        message = "msg.success.update.x";
    			message = MessageFormat.format(getResourceBundle().getString(message), "RAE Staff Serial");
        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
        		FacesContext.getCurrentInstance().addMessage(null, msg);
        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
}
