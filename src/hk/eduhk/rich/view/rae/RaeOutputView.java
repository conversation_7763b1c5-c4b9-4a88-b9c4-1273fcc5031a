package hk.eduhk.rich.view.rae;


import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.sql.SQLException;

import java.text.MessageFormat;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.ejb.EJBException;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.commons.validator.routines.ISBNValidator;
import org.apache.commons.validator.routines.ISSNValidator;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.PrimeFaces;
import org.primefaces.context.PrimeRequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;
import org.primefaces.shaded.commons.io.output.ByteArrayOutputStream;

import com.google.common.base.Strings;

import hk.eduhk.rich.exception.FileDuplicateException;
import hk.eduhk.rich.exception.FileNumberException;
import hk.eduhk.rich.exception.FileSizeException;
import hk.eduhk.rich.exception.FileUploadException;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.access.AccessCacheDAO;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.Authorship;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.SubAuthorship;
import hk.eduhk.rich.entity.publication.OutputDetails_Q;
import hk.eduhk.rich.entity.publication.OutputHeader_P;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PubDiscipline;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.publication.ResearchType;
import hk.eduhk.rich.entity.rae.RaeAttachment;
import hk.eduhk.rich.entity.rae.RaeAttachmentDAO;
import hk.eduhk.rich.entity.rae.RaeOutput;
import hk.eduhk.rich.entity.rae.RaeOutputDAO;
import hk.eduhk.rich.entity.rae.RaeOutputDetails;
import hk.eduhk.rich.entity.rae.RaeOutputHeader;
import hk.eduhk.rich.entity.rae.RaeOutputSelect;
import hk.eduhk.rich.entity.rae.RaeOutputSelect_PK;
import hk.eduhk.rich.entity.rae.RaeOutputStatus;
import hk.eduhk.rich.entity.rae.RaePanel;
import hk.eduhk.rich.entity.rae.RaePanelDAO;
import hk.eduhk.rich.entity.rae.RaeStaff;
import hk.eduhk.rich.entity.rae.RaeStaffDAO;
import hk.eduhk.rich.entity.rae.RaeUOA;
import hk.eduhk.rich.entity.rae.RaeUOADAO;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.entity.staff.StaffProfileDisplay;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name = "raeOutputView")
@ViewScoped
@SuppressWarnings("serial")
public class RaeOutputView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(RaeOutputView.class.getName());

	private UploadedFile uploadedFile = null;
	private long uploadedFileSize;
	private InputStream uploadedFileIS;
	private String uploadedFileName = null;
    
    private List<RaeAttachment> uploadSupDocList;
    private List<RaeAttachment> uploadP10List;
    private List<RaeAttachment> uploadP12List;
    private List<RaeAttachment> uploadFullVerList;
    private List<RaeAttachment> uploadTocAttList;
    private List<RaeAttachment> uploadTocAttOthList;
    private List<RaeAttachment> uploadScwList;
    private List<RaeAttachment> uploadAddInfoList;
    
	private List<RaeOutputStatus> outputStatusList;
	private List<RaeStaff> raeStaffList = null;
	private List<RaeUOA> uoaList = null;
	private List<String> staffUoaList;
	
	private String selectedUOA = null;
	Set<String>	selectedUoaSet = null;
	
	private RaeStaff selectedRaeStaff;
	private RaeOutputStatus selectedRaeOutputStatus;
	private RaeOutputHeader selectedRaeOutputHeader;
	private RaeOutputSelect selectedRaeOutputSelect;
	private List<RaeOutputDetails> selectedRaeOutputDetails;
	private List<RaeOutputSelect> selectedRaeOutput = new ArrayList <RaeOutputSelect>();
	
	
	private List<String> selectedOutputTypeList ;
	private List<OutputType> outputTypeList;
	private List<Authorship> authorshipList;
	private List<SubAuthorship> subAuthorshipList;
	private List<PubDiscipline> disciplineList;
	private List<ResearchType> researchTypeList;
	private List<RaePanel> raList;
	private List<RaePanel> catList;
	private List<RaePanel> uoaFullList = null;
	
	private List<LookupValue> citationCheckingList;
	private List<LookupValue> copyrightClearList;
	private List<LookupValue> outputLangList;
	private List<LookupValue> othLangList;
	private List<LookupValue> raeOutputTypeList;
	private List<LookupValue> statusList;
	private List<LookupValue> subDiscList;
	
	private List<SelectItem> noOfAuthorList;
	private List<SelectItem> noOfPhyQtyList;
	private List<SelectItem> noOfSglCoWorkList;
	private List<SelectItem> selectTypeList;
	
	private String[] outputTypeArray = {"120","130","140","150","160","170"};
	
	private String authorshipTips;
	private String citation;
	private String defaultAPA_html;
	private String hasCustomCitation;
	
	private Boolean hasAccessRight;
	private Boolean isDoubledWeighted;
	private Boolean isEndorsed;
	private Boolean hasError;
	private Boolean canSave;
	private Boolean doSave;
	
	private Integer countErrorRoInfo = 0;
	private Integer countErrorP10 = 0;
	private Integer countErrorP11 = 0;
	private Integer countErrorP12 = 0;
	private Integer countErrorFullVerSubmit = 0;
	private Integer countErrorOther = 0;
	
	private String uploadErrMsgSupDoc;
	private String uploadErrMsgP10;
	private String uploadErrMsgP12;
	private String uploadErrMsgFullVer;
	private String uploadErrMsgTocAtt;
	private String uploadErrMsgTocAttOth;
	private String uploadErrMsgScw;
	private String uploadErrMsgAddInfo;
	
	private Integer maxCoAuthor;
	
	Map<String , Long> totalCountMap;
	Map<String , Long> doubleCountMap;
	Map<String, String> p11SampleMap;
	
	private List<Entry<String, String>> p11SampleList;
	
	private RaeAttachmentDAO attDao = RaeAttachmentDAO.getInstance();
	private RaeOutputDAO oDao = RaeOutputDAO.getInstance();
	private RaeStaffDAO sDao = RaeStaffDAO.getInstance();
	private RaeUOADAO uoaDao = RaeUOADAO.getInstance();
	private PublicationDAO pDao = PublicationDAO.getInstance();
	private LookupValueDAO lookupDao = LookupValueDAO.getInstance();
	private RaePanelDAO panelDao = RaePanelDAO.getInstance();
	private SysParamDAO sysParamDao = SysParamDAO.getInstance();
	
	private ImportRAEOutput outputPanel;

	public ImportRAEOutput getOutputPanel()
	{
		
		if(outputPanel == null && ! getParamPid().isEmpty()) {
			
			outputPanel = new ImportRAEOutput();
			outputPanel.setParamPid(getParamPid());
		}
		
		//System.out.println(outputPanel.getOutputList());


		return outputPanel;
	}
	
	
	private AccessDAO getAccessDAO()
	{
		return AccessCacheDAO.getInstance();
	}
	
	public String getSelectedUOA()
	{
		return selectedUOA;
	}


	public void setSelectedUOA(String selectedUOA)
	
	{
		this.selectedUOA = selectedUOA;
		
		if (!GenericValidator.isBlankOrNull(selectedUOA)) {
			
			List<String> staffNumberList = new ArrayList<String>();
			
			
			raeStaffList = sDao.getRaeStaffListByUOA(selectedUOA);	
			
			
			if(raeStaffList.size() != 0) {

				for (RaeStaff raeStaff : raeStaffList )
					staffNumberList.add(raeStaff.getStaffNumber());
				
				List<RaeOutputSelect> outputList = RaeOutputDAO.getInstance().getSubOutCountListbySID(staffNumberList,false);
				totalCountMap = outputList.stream().collect(Collectors.groupingBy(RaeOutputSelect::getStaff_number,Collectors.counting()));

				//Double Weight
				outputList = RaeOutputDAO.getInstance().getSubOutCountListbySID(staffNumberList,true);
				doubleCountMap = outputList.stream().collect(Collectors.groupingBy(RaeOutputSelect::getStaff_number,Collectors.counting()));
			}
			
			
		}
		
	}
	
	
	public long getOutputCount (String staffNumber)
	{
		if(staffNumber == null)
			return 0 ;
		return totalCountMap.get(staffNumber)!=null? totalCountMap.get(staffNumber): 0;
	}
	
	public long getDWCount (String staffNumber)
	{
		if(staffNumber == null)
			return 0 ;
		return doubleCountMap.get(staffNumber)!=null? doubleCountMap.get(staffNumber): 0;
	}

	
	public List<RaeStaff> getRaeStaffList()
	{
		return raeStaffList;
	}

	
	public void setRaeStaffList(List<RaeStaff> raeStaffList)
	{
		this.raeStaffList = raeStaffList;
	}
	
	
	
	public RaeStaff getSelectedRaeStaff()
	{
		
		List<String> staffNumberList = new ArrayList<String>();
		if (selectedRaeStaff == null) {
			if (getIsAsst()) {
				selectedRaeStaff = getRaeStaffDetail(getParamPid(), null, true);
			}
			else if (getIsRaeOrLibAdmin() || getIsUoaAdmin()) {
				if (getParamNo() != null) {
					if (getSelectedRaeOutputStatus() != null) {
						String staffNo = selectedRaeOutputStatus.getPk().getStaff_number();
						if (staffNo != null) {
							selectedRaeStaff = getRaeStaffDetail(null, staffNo, true);
							paramPid = selectedRaeStaff.getPid().toString();
						}
					}
				}
				if (selectedRaeStaff == null) {
					selectedRaeStaff = getRaeStaffDetail(getParamPid(), null, true);
				}
			}
			else {
				selectedRaeStaff = getRaeStaffDetail(null, null, false);
			}
			if (selectedRaeStaff != null) {
				staffNumberList.add(selectedRaeStaff.getStaffNumber());
				List<RaeOutputSelect> outputList = RaeOutputDAO.getInstance().getSubOutCountListbySID(staffNumberList,false);
				totalCountMap = outputList.stream().collect(Collectors.groupingBy(RaeOutputSelect::getStaff_number,Collectors.counting()));
				//Double Weight
				outputList = RaeOutputDAO.getInstance().getSubOutCountListbySID(staffNumberList,true);
				doubleCountMap = outputList.stream().collect(Collectors.groupingBy(RaeOutputSelect::getStaff_number,Collectors.counting()));
			}
		}
		return selectedRaeStaff;
	}

	
	public void setSelectedRaeStaff(RaeStaff selectedRaeStaff)
	{
		this.selectedRaeStaff = selectedRaeStaff;
	}

	public List<RaeUOA> getUoaList()
	{
		if(uoaList == null) {
			List <SecDataUser> userUoaList = getAccessDAO().getDataCodeByUserId(getCurrentUserId(), "UOA");
			
			if (! CollectionUtils.isEmpty(userUoaList)) {
				uoaList = uoaDao.getRaeUOAList();
				selectedUoaSet = userUoaList.stream().map(SecDataUser -> SecDataUser.getPk().getData_code()).collect(Collectors.toSet());
				uoaList = uoaList.stream().filter(a -> selectedUoaSet.contains(a.getUoaCode())).collect(Collectors.toList());
				uoaList = uoaList.stream().filter(a -> getStaffUoaList().contains(a.getUoaCode())).collect(Collectors.toList());
			}
		}
		return uoaList;
	}
	
	public String submit() throws IOException {
		
		int count = 0 , dw1Count = 0, dw2Count = 0, dwb1Count = 0 , dwb2Count = 0;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ResourceBundle bundle = getResourceBundle();
		
		String dw1 = bundle.getString("rae.selected.type.dw1");
		String dw2 = bundle.getString("rae.selected.type.dw2");
		String allMessage = "";
		String message = "";
		int counter = 0;
		Boolean pass = true;
		
		try {
		//Check Validation
			
		for ( RaeOutputStatus p :outputStatusList) {
				if(p.getSelectType().equals("S"))
					count += 1;
				else if(p.getSelectType().equals("DW1") || p.getSelectType().equals("DW2"))
					count += 2;
								
				String dispInd = (p.getInterDisp() == true)? "Y":"N";

				//Primary and Secondary Area cannot be the same.
				if (p.getPrimaryField() != null || p.getSecondaryField() != null ) {
					if (p.getPrimaryField().equals(p.getSecondaryField())) {
						message = "Primary and Secondary Area cannot be the same."; 
						fCtx.addMessage("dataForm:dataTable:"+counter+":inter_disp_1", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						//fCtx.addMessage("dataForm:dataTable:"+counter+":inter_disp_2", new FacesMessage(FacesMessage.SEVERITY_ERROR, "", message));
						pass = false;
					}
				}
				

				//If interdisciplinary = true, Primary Field && Secondary Field should be selected 	
				if (dispInd.equals("Y")) {
					if (p.getPrimaryField() == null ) {
						message = "Please select a Primary Area for the interdisciplinary item."; 
						fCtx.addMessage("dataForm:dataTable:"+counter+":inter_disp_1", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						pass = false;
					}
					if ( p.getSecondaryField() == null) {
						message = "Please select a Secondary Area for the interdisciplinary item."; 

						fCtx.addMessage("dataForm:dataTable:"+counter+":inter_disp_2", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						pass = false;
					}
				}		
				
				if(p.getSelectType().equals("DW1") || p.getSelectType().equals("DW2")) {
					if(p.getSelectType().equals("DW1"))
						dw1Count += 1;
					else
						dw2Count += 1;
					
					if(p.getJustifications().isEmpty()) {
						message = "Justification cannot be empty."; 
						fCtx.addMessage("dataForm:dataTable:"+counter+":dbl_wt_just", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						pass = false;
					}
					if(validateCountWords(p.getJustifications(), 100) == false){ 
						message = "Justification cannot exceed 100 words."; 
						if(! allMessage.contains(message))
							allMessage += message + "<br/>" ;
						fCtx.addMessage("dataForm:dataTable:"+counter+":dbl_wt_just", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						pass = false;
					}
				}

				
				if(p.getSelectType().equals("DWB1"))
					dwb1Count += 1;
			
				if(p.getSelectType().equals("DWB2"))
					dwb2Count += 1;	
								
			
			
			counter ++;
		}

		
		if(dw1Count > 1) {
			allMessage += "Cannot select more than one "+dw1+".<br/>"; 
			pass = false;
		}
		else {
			if(dwb1Count != dw1Count) {
				if(dw1Count == 1 ) {
					if(dwb1Count > 1) {
						allMessage += "Cannot select more than one 'reserve' output for "+dw1+".<br/>"; 
					}else {
						allMessage += "Please select a 'reserve' output for "+dw1+".<br/>"; 
					}
				}	
				if(dwb1Count == 1 )	
					allMessage += "Please select an output for "+dw1+".<br/>"; 
				
				pass = false;
			}
		}
		
		if(dw2Count > 1) {
			allMessage += "Cannot select more than one "+dw2+".<br/>"; 
			pass = false;
		}
		else {
				if(dwb2Count != dw2Count) {
					if(dw2Count == 1) {
						if(dwb2Count > 1) {
							allMessage += "Cannot select more than one 'reserve' output for "+dw2+".<br/>"; 
						}else{
							allMessage += "Please select a 'reserve' output for "+dw2+".<br/>"; 
						}
					}
						
					if(dwb2Count == 1)
						allMessage += "Please select an output for "+dw2+".<br/>";
					
					pass = false;
				}
		}

			
		
		if (count > 4) {
			allMessage += "Cannot select more than 4 outputs.<br/>"; 
			pass = false;
		}
			
		
		if (pass) {
		
			for ( RaeOutputStatus p :outputStatusList) {
					
					if(p.getSelectType().equals("S"))
						count += 1;
					else if(p.getSelectType().equals("DW1") || p.getSelectType().equals("DW2"))
						count += 2;
					
					RaeOutputSelect output = new RaeOutputSelect();
					RaeOutputSelect_PK outputPK = new RaeOutputSelect_PK ();
					
					outputPK.setStaff_number(selectedRaeStaff.getStaffNumber());
					outputPK.setOutput_no(p.getPk().getOutput_no());
					
					output.setPk(outputPK);
					output.setPid(p.getPid());
					output.setSelType(p.getSelectType());
	
					p.setSelType(p.getSelectType());
					
					String dispInd = (p.getInterDisp() == true)? "Y":"N";
					
					output.setInter_disp_ind(dispInd);
					output.setInter_disp_1(p.getPrimaryField());
					output.setInter_disp_2(p.getSecondaryField());
					output.setDbl_wt_just(p.getJustifications());
					
					
					oDao.deleteAllSelectOutput(outputPK.getStaff_number(), outputPK.getOutput_no());
					
					if(! p.getSelectType().equals("NS")) 
						oDao.updateRaeOutputSelect(output);
					
					
					oDao.updateRaeOutputStatus(p);
					selectedRaeOutputHeader = oDao.getRaeOutputHeader(p.getPk().getOutput_no());
					selectedRaeOutputHeader.setInter_research(output.getInter_disp_ind());
					selectedRaeOutputHeader.setPri_research_area_inter(output.getInter_disp_1());
					selectedRaeOutputHeader.setSec_research_area_inter(output.getInter_disp_2());
					selectedRaeOutputHeader.setJust_dw_request(output.getDbl_wt_just());
					oDao.updateRaeOutputHeader(selectedRaeOutputHeader);
			}
			sDao.updateRaeStaffRemark(selectedRaeStaff);
			message = "Data is successfully submitted.";
			
		}
		else {
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
			return "";
		}
		}
     	catch (Exception e)
 		{
 			message = bundle.getString("msg.err.unexpected");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
		
		

		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));

		
		return redirect("manageRAEOutput") + "&pid=" + getParamPid();

		
	}

	public Boolean canViewRAEList() 
	{
		Boolean result = false;
		if (getSelectedRaeStaff() != null) {
			if (Strings.isNullOrEmpty(getParamPid())) {
				paramPid = String.valueOf(selectedRaeStaff.getPid());
			}
			if (getIsAsst() || getIsLibAdmin() || getIsRaeAdmin() || getIsUoaAdmin() || getCurrentUserId().equals(selectedRaeStaff.getStaffIdentity().getCn())){
					result = true;
			}
		}
		return result;
	}
	
	public Boolean canBackRoList() 
	{
		Boolean result = false;
		if (getSelectedRaeStaff() != null) {
			if (Strings.isNullOrEmpty(getParamPid())) {
				paramPid = String.valueOf(selectedRaeStaff.getPid());
			}
			if (getIsAsst() || getIsRaeAdmin() || getIsUoaAdmin() || getCurrentUserId().equals(selectedRaeStaff.getStaffIdentity().getCn())){
					result = true;
			}
		}
		return result;
	}
	
	

	public List<RaeOutputStatus> getOutputStatusList()
	{
		String staffNumber = selectedRaeStaff.getStaffNumber();
		
		if (canViewRAEList() && outputStatusList == null) {
			outputStatusList = oDao.getRaeOutputStatus_byStaffNo(staffNumber);
		
			List<OutputType> lvTwoList = oDao.getOutputTypeList(2);
			for (RaeOutputStatus p:outputStatusList) {
				if (Strings.isNullOrEmpty(p.getRaeOutputHeader().getSap_output_type())) {
					p.getRaeOutputHeader().setSap_output_type("0");
				}
				List<OutputType> tmpList = lvTwoList.stream()
														.filter(y -> p.getRaeOutputHeader().getSap_output_type().equals(y.getPk().getLookup_code()))
														.collect(Collectors.toList());
				if (!tmpList.isEmpty()) {
					p.getRaeOutputHeader().setOutput_lookup_code(tmpList.get(0).getParent_lookup_code());
				}else {
					p.getRaeOutputHeader().setOutput_lookup_code("0");
				}
				selectedRaeOutputHeader = p.getRaeOutputHeader();
				genCitation();
				p.getRaeOutputHeader().setApa_html(selectedRaeOutputHeader.getApa_html());
				

				
			}
			
		}
		return outputStatusList;
	}

	
	public void setOutputStatusList(List<RaeOutputStatus> outputStatusList)
	{
		this.outputStatusList = outputStatusList;
	}

	public String getCitation()
	{
		if (citation == null) {			
			citation = "APA";
			if (!Strings.isNullOrEmpty(getParamPid())) {
				int pid = Integer.valueOf(paramPid);
				CvDAO cvDao = CvDAO.getInstance();
				StaffProfileDisplay obj = cvDao.getStaffProfileDisplay(pid, "DATA_CITATION");
				if (obj != null) {
					citation= obj.getDisplayType();
				}
			}
		}
		return citation;
	}

	
	public void setCitation(String citation)
	{
		this.citation = citation;
	}

	public void genCitation() 
	{
		if (!GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getSap_output_type())){
			String result = "";
			if (!"0".equals(selectedRaeOutputHeader.getSap_output_type())) {
				OutputHeader_P selectedHeader_p = new OutputHeader_P();
				
				//selectedHeader_p.setCity(selectedRaeOutputHeader.getCity());
				selectedHeader_p.setCity(null);
				selectedHeader_p.setFrom_year(selectedRaeOutputHeader.getFrom_year());
				selectedHeader_p.setFrom_month(selectedRaeOutputHeader.getFrom_month());
				if ("ENG".equals(selectedRaeOutputHeader.getRel_lang())) {
					selectedHeader_p.setTitle_jour_book(selectedRaeOutputHeader.getTitle_of_eng_output());
				}else {
					selectedHeader_p.setTitle_jour_book(selectedRaeOutputHeader.getTitle_of_non_eng_output());
					
				}
				//selectedHeader_p.setOutput_title_continue(selectedRaeOutputHeader.getOutput_title_continue());
				selectedHeader_p.setSap_output_type(selectedRaeOutputHeader.getSap_output_type());
				String listOfAuthor = selectedRaeOutputHeader.getList_of_author_eng();
				if (listOfAuthor != null) {
					listOfAuthor = listOfAuthor.replaceAll("\\*", "");
				}
				selectedHeader_p.setName_other_pos(listOfAuthor);
				selectedHeader_p.setName_other_editors(selectedRaeOutputHeader.getName_other_editors());
				selectedHeader_p.setPublisher(selectedRaeOutputHeader.getPublisher());
				selectedHeader_p.setTitle_paper_art(selectedRaeOutputHeader.getBook_title());
				selectedHeader_p.setPage_num(selectedRaeOutputHeader.getPage_num_from()+"-"+selectedRaeOutputHeader.getPage_num_to());
				selectedHeader_p.setPage_num_from(selectedRaeOutputHeader.getPage_num_from());
				selectedHeader_p.setPage_num_to(selectedRaeOutputHeader.getPage_num_to());
				selectedHeader_p.setVol_issue("N/A".equalsIgnoreCase(selectedRaeOutputHeader.getVol_issue()) ? null : selectedRaeOutputHeader.getVol_issue());
				selectedHeader_p.setArticle_num("N/A".equalsIgnoreCase(selectedRaeOutputHeader.getArticle_no()) ? null : selectedRaeOutputHeader.getArticle_no());
				selectedHeader_p.setDoi(selectedRaeOutputHeader.getDoi());
				selectedHeader_p.setFulltext_url(selectedRaeOutputHeader.getUrl_full_ver());

				if ("APA".equals(getCitation())) {
					result = getAPA(selectedHeader_p);
				}
				if ("MLA".equals(getCitation())) {
					result = getMLA(selectedHeader_p);
				}
				if ("Chicago".equals(getCitation())) {
					result = getChicago(selectedHeader_p);
				}
				defaultAPA_html = result;
				
				selectedRaeOutputHeader.setApa_html(result);
				if (!Strings.isNullOrEmpty(result)) {
					result = result.replaceAll("\\<[^>]*>","");
					result = StringEscapeUtils.unescapeHtml4(result);
				}
				selectedRaeOutputHeader.setApa_citation(result);			
			}
		}
	}
	
	public List<String> getSelectedOutputTypeList() throws SQLException, IOException
	{
		if (selectedOutputTypeList == null) {
			selectedOutputTypeList = new ArrayList<>();
			outputStatusList = getOutputStatusList();
			if (outputStatusList != null) {
				for(RaeOutputStatus p:outputStatusList) {
					if (!selectedOutputTypeList.contains(p.getRaeOutputHeader().getOutput_lookup_code()))
						selectedOutputTypeList.add(p.getRaeOutputHeader().getOutput_lookup_code());
				}			
			}
		}
		return selectedOutputTypeList;
	}
	
	public String getSelectedOutputCatDesc(String code) throws SQLException, IOException
	{
		String result = "No Output Type";
		if (!Strings.isNullOrEmpty(code)) {
			List<OutputType> lvOneList = pDao.getOutputTypeList(1);
			List<OutputType> tmpList = lvOneList.stream()
					.filter(y -> code.equals(y.getPk().getLookup_code()))
					.collect(Collectors.toList());
			result = (!tmpList.isEmpty())?tmpList.get(0).getDescription():"No Output Type";
			
		}
		return result;
	}	
	
	public String getSelectedOutputDesc(String code) throws SQLException, IOException
	{
		String result = "";
		if (!Strings.isNullOrEmpty(code)) {
			outputStatusList = getOutputStatusList();
			if (outputStatusList != null) {
				for(RaeOutputStatus p:outputStatusList) {
					if (p.getRaeOutputHeader().getOutput_lookup_code() != null) {	
						if (p.getRaeOutputHeader().getOutput_lookup_code().equals(code)) {
							result += "<span class='cv_dot'></span> " + p + "</br>";
						}
					}			
				}
			}
		}
		return result;
	}		
	

	
	public long getTotalCount(String code) {
		if (getOutputStatusList() != null) {
			if (!Strings.isNullOrEmpty(code)) {
				return outputStatusList.stream().filter(outputStatusList -> code.equals(outputStatusList.getRaeOutputHeader().getOutput_lookup_code())).count();
			}else {
				return outputStatusList.stream().count();
			}
		}else {
			return 0;
		}	
    }

	
	public List<LookupValue> getStatusList()
	{
		if (statusList == null) {
			statusList = lookupDao.getLookupValueList("RAE_STATUS", "US", "Y");
		}
		return statusList;
	}

	
	public void setStatusList(List<LookupValue> statusList)
	{
		this.statusList = statusList;
	}

	
	
	public List<LookupValue> getCitationCheckingList()
	{
		if (citationCheckingList == null) {
			citationCheckingList = lookupDao.getLookupValueList("RAE_CITATION_CHK", "US", "Y");
		}
		return citationCheckingList;
	}

	
	public void setCitationCheckingList(List<LookupValue> citationCheckingList)
	{
		this.citationCheckingList = citationCheckingList;
	}

	
	public List<LookupValue> getCopyrightClearList()
	{
		if (copyrightClearList == null) {
			copyrightClearList = lookupDao.getLookupValueList("RAE_COPYRIGHT_CLR", "US", "Y");
		}
		return copyrightClearList;
	}

	
	public void setCopyrightClearList(List<LookupValue> copyrightClearList)
	{
		this.copyrightClearList = copyrightClearList;
	}

	
	
	public List<LookupValue> getOutputLangList()
	{
		if (outputLangList == null) {
			outputLangList = lookupDao.getLookupValueList("RAE_LANG", "US", "Y");
		}
		return outputLangList;
	}

	
	public void setOutputLangList(List<LookupValue> outputLangList)
	{
		this.outputLangList = outputLangList;
	}

	public List<LookupValue> getOthLangList()
	{
		if (othLangList == null) {
			othLangList = lookupDao.getLookupValueList("RAE_COUNTRY", "US", "Y");
		}
		return othLangList;
	}

	
	public void setOthLangList(List<LookupValue> othLangList)
	{
		this.othLangList = othLangList;
	}

	public RaeOutputStatus getSelectedRaeOutputStatus()
	{
		if (selectedRaeOutputStatus == null) {
			if (!Strings.isNullOrEmpty(getParamNo())) {
				selectedRaeOutputStatus = oDao.getRaeOutputStatusByOutputNo(Integer.valueOf(paramNo));
			}
			if (selectedRaeOutputStatus == null) {
				selectedRaeOutputStatus = new RaeOutputStatus();
				if (getSelectedRaeStaff() != null) {
					selectedRaeOutputStatus.getPk().setStaff_number(selectedRaeStaff.getStaffNumber());
					selectedRaeOutputStatus.setPid(selectedRaeStaff.getPid());
				}
				selectedRaeOutputStatus.setInfoComp("N");
				selectedRaeOutputStatus.setSelType("NS");
			}
			
		}
		return selectedRaeOutputStatus;
	}

	
	public void setSelectedRaeOutputStatus(RaeOutputStatus selectedRaeOutputStatus)
	{
		this.selectedRaeOutputStatus = selectedRaeOutputStatus;
	}

	
	
	public RaeOutputSelect getSelectedRaeOutputSelect()
	{
		if (selectedRaeOutputSelect == null) {
			if (getSelectedRaeOutputStatus() != null) {
				if ("NS".equals(selectedRaeOutputStatus.getSelType()) == false) {
					selectedRaeOutputSelect = oDao.getRaeOutputSelect(selectedRaeOutputStatus.getPk().getStaff_number(), selectedRaeOutputStatus.getPk().getOutput_no());
				}
			}
		}
		
		return selectedRaeOutputSelect;
	}

	
	public void setSelectedRaeOutputSelect(RaeOutputSelect selectedRaeOutputSelect)
	{
		this.selectedRaeOutputSelect = selectedRaeOutputSelect;
	}

	public RaeOutputHeader getSelectedRaeOutputHeader()
	{
		if (selectedRaeOutputHeader == null) {
			if (!Strings.isNullOrEmpty(getParamNo())) {
				selectedRaeOutputHeader = oDao.getRaeOutputHeader(Integer.valueOf(paramNo));
			}
			if (selectedRaeOutputHeader == null) {
				selectedRaeOutputHeader = new RaeOutputHeader();
				selectedRaeOutputHeader.setCreator(getLoginUserId());
				selectedRaeOutputHeader.setUni_endorse_conf_ind("N");
			}
			if (selectedRaeOutputHeader.getInter_research() == null) {
				selectedRaeOutputHeader.setInter_research("N");
			}
			if (selectedRaeOutputHeader.getIed_work_ind() == null) {
				selectedRaeOutputHeader.setIed_work_ind("Y");
			}
			getIsEndorsed();
		}else {
			genCitation();
			selectedRaeOutputHeader.setPage_num(null);
		}
		return selectedRaeOutputHeader;
	}

	
	public void setSelectedRaeOutputHeader(RaeOutputHeader selectedRaeOutputHeader)
	{
		this.selectedRaeOutputHeader = selectedRaeOutputHeader;
	}

	
	public List<RaeOutputDetails> getSelectedRaeOutputDetails()
	{
		if (selectedRaeOutputDetails == null) {
			if (!Strings.isNullOrEmpty(getParamNo())) {
				selectedRaeOutputDetails = oDao.getRaeOutputDetails(Integer.valueOf(paramNo));
			}
			if (selectedRaeOutputDetails == null) {
				selectedRaeOutputDetails = new ArrayList<RaeOutputDetails>();
				RaeOutputDetails d = new RaeOutputDetails();
				d.setAuthorship_person_id(selectedRaeStaff.getPid());
				d.setAuthorship_staff_no(selectedRaeStaff.getStaffNumber());
				d.setNon_ied_staff_flag("N");
				d.setAuthorship_type("AUTHOR");
		    	d.setAuthorship_dtl_type("FIRST");
				selectedRaeOutputDetails.add(d);
			}
			if (selectedRaeOutputDetails.size() == 1) {
				if (selectedRaeOutputDetails.get(0).getAuthorship_type() == null) {
					selectedRaeOutputDetails.get(0).setAuthorship_type("AUTHOR");
					if (selectedRaeOutputDetails.get(0).getAuthorship_dtl_type() == null) {
						selectedRaeOutputDetails.get(0).setAuthorship_dtl_type("FIRST");
					}
				}
			}
		}
		return selectedRaeOutputDetails;
	}

	
	public void setSelectedRaeOutputDetails(List<RaeOutputDetails> selectedRaeOutputDetails)
	{
		this.selectedRaeOutputDetails = selectedRaeOutputDetails;
	}
	
	public void addRow() throws SQLException
	{
		RaeOutputDetails p = new RaeOutputDetails();
		p.getPk().setOutput_no(selectedRaeOutputStatus.getPk().getOutput_no());
		p.setNon_ied_staff_flag("N");
		p.setAuthorship_type("AUTHOR");
		if (!getSelectedRaeOutputDetails().isEmpty())
		{
			selectedRaeOutputDetails.add(p);
		}else {
			selectedRaeOutputDetails = new ArrayList<RaeOutputDetails>();
			selectedRaeOutputDetails.add(p);
		}
	}
	
	public void moveColumnUp(int idx) throws SQLException
	{
		if (!selectedRaeOutputDetails.isEmpty())
		{
			if (idx > 0)
			{
				RaeOutputDetails tmp = selectedRaeOutputDetails.get(idx-1);
				selectedRaeOutputDetails.set(idx-1, selectedRaeOutputDetails.get(idx));
				selectedRaeOutputDetails.set(idx, tmp);
			}
		}
	}

	public void moveColumnDown(int idx) throws SQLException
	{
		if (selectedRaeOutputDetails != null)
		{
			if (idx+1 < selectedRaeOutputDetails.size())
			{
				RaeOutputDetails tmp = selectedRaeOutputDetails.get(idx+1);
				selectedRaeOutputDetails.set(idx+1, selectedRaeOutputDetails.get(idx));
				selectedRaeOutputDetails.set(idx, tmp);
			}
		}
	}	
	
	public void deleteRow(int idx) throws SQLException
	{
		if (!selectedRaeOutputDetails.isEmpty())
		{
			if (idx < selectedRaeOutputDetails.size()) selectedRaeOutputDetails.remove(idx);
		}
	}
	
	public void updateRowStaffNum(int idx) throws SQLException
	{
		if (selectedRaeOutputDetails != null)
		{
			if (idx > -1)
			{
				RaeOutputDetails tmp = selectedRaeOutputDetails.get(idx);
				String staffNum = getPastStaffNumByStaffName(tmp.getAuthorship_name());
				tmp.setAuthorship_staff_no(staffNum);
				selectedRaeOutputDetails.set(idx, tmp);
			}
		}
	}
	
	public void checkValidRae(ComponentSystemEvent event) throws IOException
	{
		String message = "";
		if (!getIsRdoAdmin() && !getIsUoaAdmin()) {
			if (getHasAccessRight() == false) {
				message = "You don't have access right.";	
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
	}
	
	public Boolean getHasAccessRight() 
	{
		if (hasAccessRight == null) {
			hasAccessRight = false;
			if (getSelectedRaeStaff() != null && getSelectedRaeOutputStatus() != null) {
				if (selectedRaeOutputStatus.getPk().getStaff_number().equals(selectedRaeStaff.getStaffNumber()) == true) {
					hasAccessRight = true;
					//paramPid = selectedRaeStaff.getPid().toString();
				}
			}
		}
		return hasAccessRight;
	}

	public void setHasAccessRight(Boolean hasAccessRight)
	{
		this.hasAccessRight = hasAccessRight;
	}

	
	public Boolean getIsEndorsed()
	{
		if (isEndorsed == null) {
			isEndorsed = false;
			if (getSelectedRaeOutputHeader() != null) {
				if ("Y".equals(selectedRaeOutputHeader.getUni_endorse_conf_ind()) == true) {
					isEndorsed = true;
				}
			}
		}
		return isEndorsed;
	}

	
	public void setIsEndorsed(Boolean isEndorsed)
	{
		this.isEndorsed = isEndorsed;
	}

	public List<OutputType> getOutputTypeList()
	{
		if (outputTypeList == null) {
			outputTypeList = new ArrayList<OutputType>();
			List<OutputType> lvOneList = pDao.getOutputTypeList(1);
			List<OutputType> lvTwoList = pDao.getOutputTypeList(2);
			for (OutputType o:lvOneList) {
				outputTypeList.add(o);
				List<OutputType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				outputTypeList.addAll(tmpLvTwoList);
			}
		}
		return outputTypeList;
	}

	
	public void setOutputTypeList(List<OutputType> outputTypeList)
	{
		this.outputTypeList = outputTypeList;
	}

	public boolean meet2014AuthorshipRule()
	{
		boolean meet2014AuthorshipRule = false;
		int startDateYear = 2014;
		int startDateMonth = 7;
		String startDate =  sysParamDao.getSysParamValueByCode("AUTHORSHIP_DETAIL_START_DATE");
		if (startDate != null) {
			String[] parts = startDate.split("-");
			if (parts.length > 1) {
				startDateYear = Integer.parseInt(parts[0]);
				startDateMonth = Integer.parseInt(parts[1]);
			}
		}
		if (selectedRaeOutputHeader.getFrom_year() != null) {
			//if year > 2014
			if (selectedRaeOutputHeader.getFrom_year() > startDateYear) {
				meet2014AuthorshipRule = true;
			}
			//if year == 2014 and month > 7
			if (selectedRaeOutputHeader.getFrom_year() == startDateYear && selectedRaeOutputHeader.getFrom_month() != null) {
				if (selectedRaeOutputHeader.getFrom_month() > startDateMonth) {
					meet2014AuthorshipRule = true;
				}
			}
		}
		return meet2014AuthorshipRule;
	}
	
	public boolean mustHaveSubAuthorship()
	{
		boolean mustHaveSubAuthorship = false;

		if (!Strings.isNullOrEmpty(selectedRaeOutputHeader.getSap_output_type()) && meet2014AuthorshipRule()) {
			if (Arrays.stream(outputTypeArray).anyMatch(selectedRaeOutputHeader.getSap_output_type()::equals)) {
					mustHaveSubAuthorship = true;
			}
		}
		return mustHaveSubAuthorship;
	}
	
	public List<Authorship> getAuthorshipList()
	{
		if (authorshipList == null) {
			authorshipList = pDao.getAuthorshipList(1);
			if (meet2014AuthorshipRule()) {
				authorshipList.removeIf(y->"CO-EDITOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"COR-AUTHOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"COR-EDITOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"FIRST-AUTHOR".equals(y.getPk().getLookup_code()));
				authorshipList.removeIf(y->"FIRST-EDITOR".equals(y.getPk().getLookup_code()));	
			}
			if (mustHaveSubAuthorship()) {
				authorshipList.removeIf(y->"CO-AUTHOR".equals(y.getPk().getLookup_code()));
			}
		}
		return authorshipList;
	}

	
	public void setAuthorshipList(List<Authorship> authorshipList)
	{
		this.authorshipList = authorshipList;
	}

	
	public List<SubAuthorship> getSubAuthorshipList()
	{
		if (subAuthorshipList == null) {
			subAuthorshipList = pDao.getSubAuthorshipList(1);
		}
		return subAuthorshipList;
	}

	
	public void setSubAuthorshipList(List<SubAuthorship> subAuthorshipList)
	{
		this.subAuthorshipList = subAuthorshipList;
	}

	public Boolean requireSubAuthorship(String value) 
	{
		Boolean result = false;
		if (mustHaveSubAuthorship()) {
			if (value.equals("AUTHOR") || value.equals("EDITOR")) {
				result = true;
			}	
		}
		return result;
	}
	
	public List<PubDiscipline> getDisciplineList()
	{
		if (disciplineList == null) {
			disciplineList = new ArrayList<PubDiscipline>();
			List<PubDiscipline> lvOneList = pDao.getDisciplineList(1);
			List<PubDiscipline> lvTwoList = pDao.getDisciplineList(2);
			for (PubDiscipline o:lvOneList) {
				disciplineList.add(o);
				List<PubDiscipline> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				disciplineList.addAll(tmpLvTwoList);
			}
		}
		return disciplineList;
	}

	
	public void setDisciplineList(List<PubDiscipline> disciplineList)
	{
		this.disciplineList = disciplineList;
	}

	
	
	public List<LookupValue> getRaeOutputTypeList()
	{
		if (raeOutputTypeList == null) {
			raeOutputTypeList = lookupDao.getLookupValueList("RAE_OUTPUT_TYPE", "US", "Y");
		}
		return raeOutputTypeList;
	}

	
	public void setRaeOutputTypeList(List<LookupValue> raeOutputTypeList)
	{
		this.raeOutputTypeList = raeOutputTypeList;
	}

	public List<ResearchType> getResearchTypeList()
	{
		if (researchTypeList == null) {
			List<ResearchType> lvOneList = pDao.getResearchTypeList(1);
			if (selectedRaeOutputHeader != null) {
				if (selectedRaeOutputHeader.getSap_output_type() != null) {
					if (selectedRaeOutputHeader.getSap_output_type().equals("160") || selectedRaeOutputHeader.getSap_output_type().equals("190")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("Y"))
								.collect(Collectors.toList());
					}
					else if (selectedRaeOutputHeader.getSap_output_type().equals("360") || selectedRaeOutputHeader.getSap_output_type().equals("420")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("N"))
								.collect(Collectors.toList());
					}			
					else if (selectedRaeOutputHeader.getSap_output_type().equals("280")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("C"))
								.collect(Collectors.toList());
					}	
					else if (selectedRaeOutputHeader.getSap_output_type().equals("370") || selectedRaeOutputHeader.getSap_output_type().equals("300")
								|| selectedRaeOutputHeader.getSap_output_type().equals("310") || selectedRaeOutputHeader.getSap_output_type().equals("320")
								|| selectedRaeOutputHeader.getSap_output_type().equals("330")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> y.getPk().getLookup_code().equals("99"))
								.collect(Collectors.toList());
					}	
					else if (selectedRaeOutputHeader.getSap_output_type().equals("200")) {
						researchTypeList = lvOneList.stream()
								.filter(y -> !y.getPk().getLookup_code().equals("99"))
								.collect(Collectors.toList());
					}		
					else {
						researchTypeList = lvOneList;
					}	
					//If only 1 choose, set default value
					if (researchTypeList.size() < 2) {
						selectedRaeOutputHeader.setSap_refered_journal(researchTypeList.get(0).getPk().getLookup_code());
					}
				}
			}
		}
		return researchTypeList;
	}

	
	public void setResearchTypeList(List<ResearchType> researchTypeList)
	{
		this.researchTypeList = researchTypeList;
	}

	
	public String getAuthorshipTips()
	{
		if (authorshipTips == null) {
			authorshipTips = sysParamDao.getSysParamValueByCode("AUTHORSHIP_HELP_INFO");
		}
		return authorshipTips;
	}

	
	public void setAuthorshipTips(String authorshipTips)
	{
		this.authorshipTips = authorshipTips;
	}

	public Boolean isOtherDisArea() {
		Boolean result = false;
		if (selectedRaeOutputHeader != null) {
			String lookup_code = selectedRaeOutputHeader.getDa_dtl_code();
			if (lookup_code != null) {
				result = (lookup_code.contains("OTHER"))?true:false;
				if (!result) {
					selectedRaeOutputHeader.setOther_da_dtl("");
				}
			}
		}
		return result;
	}
	
	public boolean isRiCreator(String staff_no) 
	{
		boolean result = false;
		String creatorStaffNo = selectedRaeStaff.getStaffNumber();
		if (!Strings.isNullOrEmpty(staff_no) && !Strings.isNullOrEmpty(creatorStaffNo)) {
			result = (creatorStaffNo.equals(staff_no))?true:false;
		}
		return result;
	}

	public List<RaePanel> getCatListByRa(String ra_code)
	{
		List<RaePanel> catListByRa = new ArrayList<RaePanel>();
		List<RaePanel> fullList = panelDao.getRaePanelList();
		List<RaePanel> lvTwoList = fullList.stream()
											.filter(y -> y.getPk().getLookup_type().equals("RAE_CAT"))
											.collect(Collectors.toList());
		for (RaePanel o:lvTwoList) {
			o.setDescription(o.getDescription().replaceAll("\\; ", "\\; <br/>"));
		}
		List<RaePanel> tmpLvTwoList = lvTwoList.stream()
				.filter(y -> y.getParent_lookup_code().equals(ra_code))
				.collect(Collectors.toList());
		catListByRa.addAll(tmpLvTwoList);

		return catListByRa;
	}
	
	public List<RaePanel> getCatList()
	{
		if (catList == null) {
			catList = new ArrayList<RaePanel>();
			List<RaePanel> fullList = panelDao.getRaePanelList();
			List<RaePanel> lvOneList = fullList.stream()
												.filter(y -> y.getPk().getLookup_type().equals("RAE_RA_RO"))
												.collect(Collectors.toList());
			List<RaePanel> lvTwoList = fullList.stream()
												.filter(y -> y.getPk().getLookup_type().equals("RAE_CAT"))
												.collect(Collectors.toList());
			for (RaePanel o:lvTwoList) {
				o.setDescription(o.getDescription().replaceAll("\\; ", "\\; <br/>"));
			}
			for (RaePanel o:lvOneList) {
				o.setDescription(o.getDescription().replaceAll("\\)\\,", "\\)\\,<br/>"));
				//catList.add(o);
				List<RaePanel> tmpLvTwoList = lvTwoList.stream()
														.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
														.collect(Collectors.toList());
				catList.addAll(tmpLvTwoList);
			}
		}
		return catList;
	}


	
	public void setCatList(List<RaePanel> catList)
	{
		this.catList = catList;
	}


	public List<RaePanel> getRaList()
	{
		if (raList == null) {
			raList = new ArrayList<RaePanel>();
			List<RaePanel> fullList = panelDao.getRaePanelList();
			List<RaePanel> lvOneList = fullList.stream()
												.filter(y -> y.getPk().getLookup_type().equals("RAE_UOA"))
												.collect(Collectors.toList());
			List<RaePanel> lvTwoList = fullList.stream()
												.filter(y -> y.getPk().getLookup_type().equals("RAE_RA_RO"))
												.collect(Collectors.toList());
			for (RaePanel o:lvOneList) {
				o.setDescription(o.getDescription().replaceAll("\\(incl\\.", "<br/>\\(incl\\."));
				o.setDescription(o.getDescription().replaceAll("\\)\\,", "\\)\\,<br/>"));
				raList.add(o);
				List<RaePanel> tmpLvTwoList = lvTwoList.stream()
														.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
														.collect(Collectors.toList());
				raList.addAll(tmpLvTwoList);
			}
		}
		return raList;
	}

	
	public void setRaList(List<RaePanel> raList)
	{
		this.raList = raList;
	}

	
	
	public List<RaePanel> getUoaFullList()
	{
		if (uoaFullList == null) {
			uoaFullList = new ArrayList<RaePanel>();
			List<RaePanel> fullList = panelDao.getRaePanelList();
			uoaFullList = fullList.stream()
												.filter(y -> y.getPk().getLookup_type().equals("RAE_UOA"))
												.collect(Collectors.toList());
		}
		return uoaFullList;
	}

	
	public void setUoaFullList(List<RaePanel> uoaFullList)
	{
		this.uoaFullList = uoaFullList;
	}

	public List<LookupValue> getSubDiscList()
	{
		subDiscList = new ArrayList<LookupValue>();
		if (selectedRaeOutputHeader.getResearchAreaOfRo() != null) {
			subDiscList = lookupDao.getLookupValueList("RAE_SD_RO", "US", "Y");
			subDiscList = subDiscList.stream()
									.filter(y -> y.getPk().getLookup_code().substring(0, Math.min(y.getPk().getLookup_code().length(), 3)).equals(selectedRaeOutputHeader.getResearchAreaOfRo()))
									.collect(Collectors.toList());
		}
		return subDiscList;
	}

	
	public void setSubDiscList(List<LookupValue> subDiscList)
	{
		this.subDiscList = subDiscList;
	}

	
	public void resetAuthorshipList()
	{	
		authorshipList = null;
	}
	
	public void resetResearchActivityList()
	{	
		ExternalContext ec = FacesContext.getCurrentInstance().getExternalContext();
		String vol_issue = ec.getRequestParameterMap().get("editForm:formTab:vol_issue");
		selectedRaeOutputHeader.setVol_issue(vol_issue);
		String title_paper_art = ec.getRequestParameterMap().get("editForm:formTab:title_paper_art");
		selectedRaeOutputHeader.setTitle_paper_art(title_paper_art);
		researchTypeList = null;
		genCitation();
	}

	
	public Boolean getIsDoubledWeighted()
	{
		if(isDoubledWeighted == null) {
			isDoubledWeighted = false;
			if (getSelectedRaeOutputStatus() != null) {
				if ("DW1".equals(selectedRaeOutputStatus.getSelType()) || "DW2".equals(selectedRaeOutputStatus.getSelType())) {
					isDoubledWeighted = true;
				}
			}
		}
		return isDoubledWeighted;
	}

	
	public void setIsDoubledWeighted(Boolean isDoubledWeighted)
	{
		this.isDoubledWeighted = isDoubledWeighted;
	}
	
	public Boolean showRoleSubmitAuthor(Integer value)
	{
		boolean result = false;
		if (selectedRaeOutputHeader != null && selectedRaeStaff != null) {
			String panel = getStaffPanel();
			if (value != null) {
				if ("10".equals(panel) && value > 6) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showExplanationOfAuthorContribution(Integer value, String roleSubmitAuthor)
	{
		boolean result = false;
		if (selectedRaeOutputHeader != null && selectedRaeStaff != null) {
			String panel = getStaffPanel();
			if (value != null) {
				if ("2".equals(panel) && value > 15) {
					result = true;
				}
				if ("3".equals(panel) && value > 14) {
					result = true;
				}
				if ("5".equals(panel) && value > 6) {
					result = true;
				}
				/*if ("8".equals(panel) && value > 0) {
					result = true;
				}*/
				if ("10".equals(panel) && value > 6 && "N".equals(roleSubmitAuthor)) {
					result = true;
				}
				if ("12".equals(panel) && value > 3) {
					result = true;
				}
				if ("13".equals(panel) && value > 5) {
					result = true;
				}
			}
		}
		return result;
	}
	
	//Statement on Originality and Significance
	public Boolean showStmt_ori_sign()
	{
		String[] options = {"4", "6", "8", "11", "12", "13"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (getStaffPanel() != null) {
				if (checkInput(getStaffPanel(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showPhySubmitPanel()
	{
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			String value = selectedRaeOutputHeader.getFormat_full_ver_submit();
			if (value != null) {
				if ("2".equals(value) || "3".equals(value) || "4".equals(value)) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showEleSubmitPanel()
	{
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			String value = selectedRaeOutputHeader.getFormat_full_ver_submit();
			if (value != null) {
				if ("1".equals(value) || "3".equals(value) || "4".equals(value)) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showIssn()
	{
		boolean result = true;
		/*String[] options = {"D", "E", "M", "N"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}*/
		return result;
	}
	
	public Boolean showIsbn()
	{
		String[] options = {"A", "B", "C", "E", "M", "N"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showArticleNo()
	{
		String[] options = {"C", "D"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showVol()
	{
		String[] options = {"C", "D"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showPageNum()
	{
		String[] options = {"C", "D"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showBookTitle()
	{
		String[] options = {"C", "D"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showDescLocOutput()
	{
		String[] options = {"A", "B", "C", "D"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == false) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showDescLocOutputRemarksE()
	{
		String[] options = {"E"};
		boolean result = false;
		if (selectedRaeOutputHeader != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showDescLocOutputRemarksF()
	{
		String[] options = {"F"};
		boolean result = false;
		if (selectedRaeOutputHeader != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showAttInfoScw()
	{
		String[] options = {"M"};
		boolean result = false;
		if (selectedRaeOutputHeader != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
		}
		return result;
	}
	
	public Boolean showUploadTocAttPanel()
	{
		String[] options = {"A", "B"};
		boolean result = false;
		if (selectedRaeOutputHeader != null) {
			if ("N".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
				if (selectedRaeOutputHeader.getToc() != null) {
					if (checkInput(selectedRaeOutputHeader.getToc(), options) == true) {
						result = true;
					}
				}
			}
		}
		return result;
	}
	
	public Boolean showUploadTocAttOthPanel()
	{
		String[] options = {"Y"};
		boolean result = false;
		if (selectedRaeOutputHeader != null) {
			if ("N".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
				if (selectedRaeOutputHeader.getToc_oth() != null) {
					if (checkInput(selectedRaeOutputHeader.getToc_oth(), options) == true) {
						result = true;
					}
				}
			}
		}
		return result;
	}
	
	public Boolean requirePublisher()
	{
		String[] options = {"A", "B", "C", "D"};
		boolean result = false;
		if (getSelectedRaeOutputHeader() != null) {
			if (selectedRaeOutputHeader.getOutput_type() != null) {
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options) == true) {
					result = true;
				}
			}
			if (selectedRaeOutputHeader.getPublished_census_date() != null) {
				String[] options1 = {"A"};
				if (checkInput(selectedRaeOutputHeader.getOutput_type(), options1) == true) {
					result = true;
				}
				else {
					result = false;
				}
			}else {
				result = false;
			}
		}
		return result;
	}
	
	public boolean checkInput(String input, String[] inputOptions)
	{
		boolean found = false;
		for(String i : inputOptions) {
		    if(i.equals(input)) {
		        found = true;
		        break;
		    }
		}
	    return found;
	}
	
	public String getStaffPanel()
	{
		String result = null;
		if (selectedRaeStaff != null) {
			Pattern pattern = Pattern.compile("\\((.*?)\\)");
	        Matcher matcher = pattern.matcher(selectedRaeStaff.getPanelInfo());

	        while (matcher.find()) {
	        	result = matcher.group(1);
	        }
		}
		return result;
	}
	
	public void saveRaeOutputForm() throws Exception
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);
		hasError = false;
		canSave = true;
		doSave = true;
		countErrorRoInfo = 0;
		countErrorP10 = 0;
		countErrorP11 = 0;
		countErrorP12 = 0;
		countErrorFullVerSubmit = 0;
		countErrorOther = 0;
		try {
			validateRaeOutputDetails(selectedRaeStaff.getStaffNumber());
			validateRequiredField();
			validateISSN();
			validateISBN();
			validatePhysicalSubmission();
			setNullValue();
			setFieldValue();
			if (canSave == true) {
				if (getSelectedRaeStaff() != null) {
					sDao.updateRaeStaffRemark(selectedRaeStaff);
				}
				selectedRaeOutputSelect = null;
				if (getSelectedRaeOutputSelect() != null) {
					/*if ("NS".equals(selectedRaeOutputSelect.getSelType()) == true) {
						selectedRaeOutputHeader.setInter_research("N");
						selectedRaeOutputHeader.setPri_research_area_inter(null);
						selectedRaeOutputHeader.setSec_research_area_inter(null);
						selectedRaeOutputHeader.setJust_dw_request(null);
					}
					if ("N".equals(selectedRaeOutputHeader.getInter_research())) {
						selectedRaeOutputHeader.setPri_research_area_inter(null);
						selectedRaeOutputHeader.setSec_research_area_inter(null);
					}*/
					selectedRaeOutputSelect.setInter_disp_ind(selectedRaeOutputHeader.getInter_research());
					selectedRaeOutputSelect.setInter_disp_1(selectedRaeOutputHeader.getPri_research_area_inter());
					selectedRaeOutputSelect.setInter_disp_2(selectedRaeOutputHeader.getSec_research_area_inter());
					selectedRaeOutputSelect.setDbl_wt_just(selectedRaeOutputHeader.getJust_dw_request());
					
					//update output select
					selectedRaeOutputSelect.setUserstamp(getLoginUserId());
					oDao.updateRaeOutputSelect(selectedRaeOutputSelect);
				}
				
				//update output header
				selectedRaeOutputHeader.setUserstamp(getLoginUserId());
				selectedRaeOutputHeader = oDao.updateRaeOutputHeader(selectedRaeOutputHeader);
				int currentOutputNo = selectedRaeOutputHeader.getOutput_no();
				
				//update output status
				if (hasError == false) {
					selectedRaeOutputStatus.setInfoComp("Y");
				}else {
					selectedRaeOutputStatus.setInfoComp("N");
				}
				if (selectedRaeOutputStatus.getPk().getOutput_no() == null) {
					selectedRaeOutputStatus.getPk().setOutput_no(currentOutputNo);
				}
				selectedRaeOutputStatus.setUserstamp(getLoginUserId());
				selectedRaeOutputStatus = oDao.updateRaeOutputStatus(selectedRaeOutputStatus);
				
				//delete all contributors
				oDao.deleteAllContributor(currentOutputNo);
				
				////update output details
				int line_no = 1;
				for (RaeOutputDetails p:selectedRaeOutputDetails) {
					p.getPk().setOutput_no(currentOutputNo);
					p.getPk().setLine_no(line_no);
					//set staff details
					if (p.getNon_ied_staff_flag().equals("N") && p.getAuthorship_staff_no() != null) {
						StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getAuthorship_staff_no());
						if (s != null) {
							p.setAuthorship_person_id(s.getPid());
							p.setAuthorship_name(s.getFullname_save());
						}							
					}
					//set past staff details
					if (p.getNon_ied_staff_flag().equals("F") && p.getAuthorship_staff_no() != null) {
						StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getAuthorship_staff_no());
						p.setAuthorship_name(sp.getFullname_save());
						p.setAuthorship_person_id(sp.getPid());
					}
					p.setCreator(getLoginUserId());
					p.setUserstamp(getLoginUserId());
					oDao.updateRaeOutputDetails(p);
					line_no++;
				}
				
				// Success message
				String message = "msg.success.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "RAE Output");
				if (validateSapOutputType() == false) {
					message += "<br/>Please note that the inputs for the fields “Research Output Type (RICH)” and “Research Output Type (RAE)” are different. You are recommended to double-check them for accuracy.";
				}
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));		
				PrimeFaces.current().executeScript("triggerCountWords();");
			}else {
				// Failed message
				String message = "msg.err.data.save.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "RAE Output");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				PrimeFaces.current().executeScript("triggerCountWords();");
			}
		}catch (OptimisticLockException ole)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "RAE Output");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save RAE Output (output_no=" + selectedRaeOutputHeader.getOutput_no() + ")", ole);
		}
		catch (Exception e)
		{
			String message = "msg.err.data.save.x";
			message = MessageFormat.format(getResourceBundle().getString(message), "RAE Output");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
			logger.log(Level.WARNING, "Cannot save RAE Output (output_no=" + selectedRaeOutputHeader.getOutput_no() + ")", e);
		}
	}

	
	public Integer getMaxCoAuthor()
	{
		if (maxCoAuthor == null) {
			maxCoAuthor =  sysParamDao.getSysParamIntByCode("RAE_MAX_CO_AUTHOR");
		}
		return maxCoAuthor;
	}

	
	public void setMaxCoAuthor(Integer maxCoAuthor)
	{
		this.maxCoAuthor = maxCoAuthor;
	}

	
	public List<SelectItem> getNoOfAuthorList()
	{
		if (noOfAuthorList == null) {
			noOfAuthorList = new ArrayList<SelectItem>();
			for (int i = 2; i <= getMaxCoAuthor(); i++) {
				SelectItem option = new SelectItem(String.valueOf(i), String.valueOf(i));
				noOfAuthorList.add(option);
			}
		}
		return noOfAuthorList;
	}

	
	public void setNoOfAuthorList(List<SelectItem> noOfAuthorList)
	{
		this.noOfAuthorList = noOfAuthorList;
	}


	
	public List<SelectItem> getNoOfPhyQtyList()
	{
		if (noOfPhyQtyList == null) {
			noOfPhyQtyList = new ArrayList<SelectItem>();
			Integer max =  sysParamDao.getSysParamIntByCode("RAE_MAX_PHY_QTY");
			for (int i = 0; i < max; i++) {
				SelectItem option = new SelectItem(String.valueOf(i), String.valueOf(i));
				noOfPhyQtyList.add(option);
			}
		}
		return noOfPhyQtyList;
	}

	
	public void setNoOfPhyQtyList(List<SelectItem> noOfPhyQtyList)
	{
		this.noOfPhyQtyList = noOfPhyQtyList;
	}

	
	public List<SelectItem> getNoOfSglCoWorkList()
	{
		if (noOfSglCoWorkList == null) {
			noOfSglCoWorkList = new ArrayList<SelectItem>();
			Integer max =  20;
			for (int i = 2; i <= max; i++) {
				SelectItem option = new SelectItem(String.valueOf(i), String.valueOf(i));
				noOfSglCoWorkList.add(option);
			}
		}
		return noOfSglCoWorkList;
	}

	
	public void setNoOfSglCoWorkList(List<SelectItem> noOfSglCoWorkList)
	{
		this.noOfSglCoWorkList = noOfSglCoWorkList;
	}

	
	public List<SelectItem> getSelectTypeList()
	{
		if (selectTypeList == null) {
			selectTypeList = new ArrayList<SelectItem>();
			SelectItem option = new SelectItem("NS", getFormResourceBundle().getString("rae.selected.type.ns"));
			selectTypeList.add(option);
			option = new SelectItem("S", getFormResourceBundle().getString("rae.selected.type.s"));
			selectTypeList.add(option);
			if ("5".equals(getStaffPanel()) == false) {
				option = new SelectItem("DW1", getFormResourceBundle().getString("rae.selected.type.dw1"));
				selectTypeList.add(option);
				option = new SelectItem("DWB1", getFormResourceBundle().getString("rae.selected.type.dwb1"));
				selectTypeList.add(option);
				option = new SelectItem("DW2", getFormResourceBundle().getString("rae.selected.type.dw2"));
				selectTypeList.add(option);
				option = new SelectItem("DWB2", getFormResourceBundle().getString("rae.selected.type.dwb2"));
				selectTypeList.add(option);
			}
		}
		return selectTypeList;
	}


	
	public void setSelectTypeList(List<SelectItem> selectTypeList)
	{
		this.selectTypeList = selectTypeList;
	}


	public StreamedContent downloadFile(RaeAttachment file) throws IOException, GeneralSecurityException
	{
		StreamedContent content = null;
		
		InputStream is = file.getFileInputStream();
		if (is != null)
		{
			content = DefaultStreamedContent.builder().name(file.getFile_name())
													  .contentType(new Tika().detect(file.getFile_name()))
													  .stream(() -> is)
													  .build();
		}
		else
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String message = file.getFile_display_name() + " does not exist on server";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
		
		return content;
	}
	
	public StreamedContent downloadFilesAsZipByUoA(String uoa) throws IOException, GeneralSecurityException {
		List<String> staffNumberList = new ArrayList<String>();
		List<RaeStaff> tmpRaeStaffList = sDao.getRaeStaffListByUOA(uoa);	
		
		// Check if the list is null or empty
	    if (tmpRaeStaffList == null || tmpRaeStaffList.isEmpty()) {
	        return null; // Return null if no files are found
	    }
	    
		for (RaeStaff raeStaff : tmpRaeStaffList)
			staffNumberList.add(raeStaff.getStaffNumber());
	    List<RaeAttachment> totalFileListByStaff = getTotalFileListByStaffIds(staffNumberList);

	    // Check if the list is null or empty
	    if (totalFileListByStaff == null || totalFileListByStaff.isEmpty()) {
	        return null; // Return null if no files are found
	    }

	    // Create a byte array output stream to hold the zip file
	    ByteArrayOutputStream baos = new ByteArrayOutputStream();
	    try (ZipOutputStream zos = new ZipOutputStream(baos)) {
	        for (RaeAttachment file : totalFileListByStaff) {
	        	String valueAfterLastDot = file.getFile_path().substring(file.getFile_path().lastIndexOf(".") + 1);
	        	file.setFile_name(uoa+"_"+file.getRaeStaff().getStaffName()+"_"+file.getFile_tag()+"_"+file.getOutput_no()+"_"+file.getSeq()+"."+valueAfterLastDot);
	            String fileName = file.getFile_name();
	            if (fileName == null) {
	                logError("File display name is null for uoa: " + uoa);
	                continue;
	            }

	            try (InputStream is = file.getFileInputStream()) {
	                if (is == null) {
	                    logError(fileName + " does not exist on the server for uoa: " + uoa);
	                    continue;
	                }

	                // Add the file to the zip archive
	                zos.putNextEntry(new ZipEntry(fileName));
	                byte[] buffer = new byte[1024];
	                int length;
	                while ((length = is.read(buffer)) > 0) {
	                    zos.write(buffer, 0, length);
	                }
	                zos.closeEntry();
	            } catch (IOException e) {
	                logError("Error processing file: " + fileName + " for uoa: " + uoa, e);
	            }
	        }
	    }

	    // Create and return the zip file as StreamedContent
	    return DefaultStreamedContent.builder()
	            .name("uoa"+uoa+"-files.zip")
	            .contentType("application/zip")
	            .stream(() -> new ByteArrayInputStream(baos.toByteArray()))
	            .build();
	}
	
	public StreamedContent downloadFilesAsZip(String staffNo) throws IOException, GeneralSecurityException {
	    List<RaeAttachment> totalFileListByStaff = getTotalFileListByStaff(staffNo);
	    RaeStaff raeStaff = sDao.getRaeStaffByStaffNo(staffNo);
	    // Check if the list is null or empty
	    if (totalFileListByStaff == null || totalFileListByStaff.isEmpty()) {
	        return null; // Return null if no files are found
	    }

	    // Create a byte array output stream to hold the zip file
	    ByteArrayOutputStream baos = new ByteArrayOutputStream();
	    try (ZipOutputStream zos = new ZipOutputStream(baos)) {
	        for (RaeAttachment file : totalFileListByStaff) {
	        	String valueAfterLastDot = file.getFile_path().substring(file.getFile_path().lastIndexOf(".") + 1);
	        	file.setFile_name(file.getRaeStaff().getUoaCode()+"_"+file.getRaeStaff().getStaffName()+"_"+file.getFile_tag()+"_"+file.getOutput_no()+"_"+file.getSeq()+"."+valueAfterLastDot);
	            String fileName = file.getFile_name();
	            if (fileName == null) {
	                logError("File display name is null for staff: " + staffNo);
	                continue;
	            }

	            try (InputStream is = file.getFileInputStream()) {
	                if (is == null) {
	                    logError(fileName + " does not exist on the server for staff: " + staffNo);
	                    continue;
	                }

	                // Add the file to the zip archive
	                zos.putNextEntry(new ZipEntry(fileName));
	                byte[] buffer = new byte[1024];
	                int length;
	                while ((length = is.read(buffer)) > 0) {
	                    zos.write(buffer, 0, length);
	                }
	                zos.closeEntry();
	            } catch (IOException e) {
	                logError("Error processing file: " + fileName + " for staff: " + staffNo, e);
	            }
	        }
	    }

	    // Create and return the zip file as StreamedContent
	    return DefaultStreamedContent.builder()
	            .name(raeStaff.getStaffName()+"-files.zip")
	            .contentType("application/zip")
	            .stream(() -> new ByteArrayInputStream(baos.toByteArray()))
	            .build();
	}

	// Helper method to log errors
	private void logError(String message) {
	    // Use a logger or FacesContext to log the error
	    System.err.println(message); // Replace with proper logging
	}

	private void logError(String message, Throwable throwable) {
	    // Use a logger or FacesContext to log the error with stack trace
	    System.err.println(message); // Replace with proper logging
	    throwable.printStackTrace();
	}
	
	public void deleteFile(int fileId, String file_tag) {
		switch (file_tag) {
		    case SysParam.PARAM_RAE_FILE_SUP_DOC:			uploadErrMsgSupDoc = null;
		    												break;
		    case SysParam.PARAM_RAE_FILE_P10: 				uploadErrMsgP10 = null;
		    												break;
		    case SysParam.PARAM_RAE_FILE_P12: 				uploadErrMsgP12 = null;
		    												break;
		    case SysParam.PARAM_RAE_FILE_FULL_VER: 			uploadErrMsgFullVer = null;
															break;
		    case SysParam.PARAM_RAE_FILE_TOC_ATT: 			uploadErrMsgTocAtt = null;
															break;
		    case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 		uploadErrMsgTocAttOth = null;
															break;
		    case SysParam.PARAM_RAE_FILE_SCW: 				uploadErrMsgScw = null;
															break;
		    case SysParam.PARAM_RAE_FILE_ADD_INFO: 			uploadErrMsgAddInfo = null;
															break;
		}
		deleteFile(fileId, file_tag, true);
	}
	
	public List<RaeAttachment> getFileList(String file_tag) {
		List<RaeAttachment> fileList = new ArrayList<RaeAttachment>();
		switch (file_tag) {
		    case SysParam.PARAM_RAE_FILE_SUP_DOC:		fileList = getUploadSupDocList();
		    											break;
		    case SysParam.PARAM_RAE_FILE_P10: 			fileList = getUploadP10List();
		    											break;
		    case SysParam.PARAM_RAE_FILE_P12: 			fileList = getUploadP12List();
		    											break;
		    case SysParam.PARAM_RAE_FILE_FULL_VER: 		fileList = getUploadFullVerList();
														break;
		    case SysParam.PARAM_RAE_FILE_TOC_ATT: 		fileList = getUploadTocAttList();
														break;
		    case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	fileList = getUploadTocAttOthList();
														break;										
		    case SysParam.PARAM_RAE_FILE_SCW: 			fileList = getUploadScwList();
														break;
		    case SysParam.PARAM_RAE_FILE_ADD_INFO: 		fileList = getUploadAddInfoList();
														break;
		}
		return fileList;
	}
	
	public void resetFileList(String file_tag) {
		switch (file_tag) {
		    case SysParam.PARAM_RAE_FILE_SUP_DOC:		uploadSupDocList = null;
		    											break;
		    case SysParam.PARAM_RAE_FILE_P10: 			uploadP10List = null;
		    											break;
		    case SysParam.PARAM_RAE_FILE_P12: 			uploadP12List = null;
		    											break;
		    case SysParam.PARAM_RAE_FILE_FULL_VER: 		uploadFullVerList = null;
														break;
		    case SysParam.PARAM_RAE_FILE_TOC_ATT: 		uploadTocAttList = null;
														break;
		    case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	uploadTocAttOthList = null;
														break;
		    case SysParam.PARAM_RAE_FILE_SCW: 			uploadScwList = null;
														break;
		    case SysParam.PARAM_RAE_FILE_ADD_INFO: 		uploadAddInfoList = null;
														break;
		}
	}
	
	public void deleteFile(int fileId, String file_tag, boolean showMessage) 
	{
		doSave = false;
		List<RaeAttachment> fileList = getFileList(file_tag);

		if(!CollectionUtils.isEmpty(fileList)) {
			RaeAttachment selectedFile = fileList.stream().filter(af->af.getFile_id()==fileId).findFirst().get();

			if(selectedFile!=null) {
				File file = new File(selectedFile.getFile_path());
				if(file.exists()) {
					if(file.delete()) {
						attDao.deleteRaeAttachment(fileId);
						//String message = MessageFormat.format(getResourceBundle().getString("msg.success.delete.x"), selectedFile.getFile_display_name());
						/*if(showMessage) {
							FacesContext.getCurrentInstance().addMessage("editForm:formTab:panel_upload_"+file_tag+"_msg", new FacesMessage(FacesMessage.SEVERITY_INFO, message,""));
						}*/
					}else {

						String msg = selectedFile.getFile_display_name() + " cannot be deleted. " + getResourceBundle().getString("msg.err.unexpected");
						if(showMessage) {
							switch (file_tag) {
							    case SysParam.PARAM_RAE_FILE_SUP_DOC:		uploadErrMsgSupDoc = msg;
							    											break;
							    case SysParam.PARAM_RAE_FILE_P10: 			uploadErrMsgP10 = msg;
							    											break;
							    case SysParam.PARAM_RAE_FILE_P12: 			uploadErrMsgP12 = msg;
							    											break;
							    case SysParam.PARAM_RAE_FILE_FULL_VER: 		uploadErrMsgFullVer = msg;
																			break;
							    case SysParam.PARAM_RAE_FILE_TOC_ATT: 		uploadErrMsgTocAtt = msg;
																			break;
							    case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	uploadErrMsgTocAttOth = msg;
																			break;										
							    case SysParam.PARAM_RAE_FILE_SCW: 			uploadErrMsgScw = msg;
																			break;
							    case SysParam.PARAM_RAE_FILE_ADD_INFO: 		uploadErrMsgAddInfo = msg;
																			break;
							}
						}
					}
				}else {
					attDao.deleteRaeAttachment(fileId);
					
					//String message = MessageFormat.format(getResourceBundle().getString("msg.success.delete.x"), selectedFile.getFile_display_name());
					//if(showMessage)	FacesContext.getCurrentInstance().addMessage("editForm:formTab:panel_upload_"+file_tag+"_msg", new FacesMessage(FacesMessage.SEVERITY_INFO, message,""));
				}
			}
		}
		resetFileList(file_tag);
	}
	
	public void setUploadedFileDetails() throws IOException
	{
		uploadedFileSize = uploadedFile.getSize();
		uploadedFileIS = uploadedFile.getInputStream();
		uploadedFileName = FilenameUtils.getName(uploadedFile.getFileName());
	}
	
	public void fileUploadListener_sup_doc(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_sup_doc");
    }
	
	public void fileUploadListener_p10(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_p10");
    }
	
	public void fileUploadListener_p12(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_p12");
    }
	
	public void fileUploadListener_full_ver(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_full_ver");
    }
	
	public void fileUploadListener_toc_att(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_toc_att");
    }
	
	public void fileUploadListener_toc_att_oth(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_toc_att_oth");
    }
	
	public void fileUploadListener_scw(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_scw");
    }
	
	public void fileUploadListener_add_info(FileUploadEvent event) throws Exception 
	{
		uploadedFile = event.getFile();
		uploadFile("rae_add_info");
    }
	
	public void uploadFile(String file_tag) throws Exception
	{
		doSave = false;
		String msg = null;
		FacesContext fCtx = FacesContext.getCurrentInstance();
		RaeAttachment raeFile = new RaeAttachment();
		String extensions = "";
		Integer maxFileSize = 0;
		int highestSeq = 0;
		setUploadedFileDetails();
		try
		{
			String userId = getLoginUserId();
			
			List<RaeAttachment> fileList = getFileList(file_tag);
			
			OptionalInt summaryStatistics = fileList.stream()
								            .mapToInt(RaeAttachment::getSeq)
								            .max();
			
			if (summaryStatistics.isPresent()) {
				highestSeq = summaryStatistics.getAsInt();
			}
			
			switch (file_tag) {
			    case SysParam.PARAM_RAE_FILE_SUP_DOC:		maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_SUP_DOC");
			    											extensions = "pdf";
			    											uploadErrMsgSupDoc = null;
			    											break;
			    case SysParam.PARAM_RAE_FILE_P10: 			maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_SUP_DOC");
			    											uploadErrMsgP10 = null;
			    											extensions = "pdf";
			    											break;
			    case SysParam.PARAM_RAE_FILE_P12: 			maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_P12_EV");
															extensions = "pdf";
															uploadErrMsgP12 = null;
															break;
			    case SysParam.PARAM_RAE_FILE_FULL_VER: 		maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_FULL_VER_ENG");
															extensions = sysParamDao.getSysParamValueByCode("RAE_FULL_VER_FILE_TYPES");
															uploadErrMsgFullVer = null;
															break;
			    case SysParam.PARAM_RAE_FILE_TOC_ATT: 		maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_ABSTRACT");
															extensions = "pdf";
															uploadErrMsgTocAtt = null;
															break;
			    case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_ABSTRACT");
															extensions = "pdf";
															uploadErrMsgTocAttOth = null;
															break;										
			    case SysParam.PARAM_RAE_FILE_SCW: 			maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_FULL_VER_ENG");
															extensions = sysParamDao.getSysParamValueByCode("RAE_FULL_VER_FILE_TYPES");
															uploadErrMsgScw = null;
															break;
			    case SysParam.PARAM_RAE_FILE_ADD_INFO: 		maxFileSize = sysParamDao.getSysParamIntByCode("RAE_UPLOAD_MAX_SIZE_ADD_INFO");
															extensions = "pdf";
															uploadErrMsgAddInfo = null;
															break;
			}
			
			extensions = extensions.toLowerCase();
			List<String> appFileChecksumList = new ArrayList<String>();
			String message;
			
			if(getUploadedFile() == null)
			{
				message = getResourceBundle().getString("msg.err.invalid.file.upload.incomplete");
				throw new FileUploadException(message);
			}
			if(maxFileSize > 0 && uploadedFileSize/1024/1024 > maxFileSize)
			{	
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.size"), new Object[] {maxFileSize + "MB"});
				throw new FileSizeException(message);
			}
			
			if(uploadedFile != null) 
			{
				List<String> currentFileNameList = fileList.stream().map(f->f.getFile_display_name()).collect(Collectors.toList());
				if(currentFileNameList.contains(uploadedFile.getFileName())) 
				{
					message = MessageFormat.format(getResourceBundle().getString("msg.err.duplicate.x"), "file");
					throw new FileDuplicateException(message);
				}				
			}
			if(uploadedFile.getFileName().lastIndexOf(".") == -1)
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), extensions);
				throw new FileUploadException(message);
			}
			
			int index = uploadedFile.getFileName().lastIndexOf(".");
			String fileExtension = uploadedFile.getFileName().substring(index+1);

			List<String> extensionsList = new ArrayList<String>(Arrays.asList(extensions.split("/")));
			if(!extensionsList.contains(fileExtension))
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), extensions);
				throw new FileUploadException(message);
			}
			
			int newSeq = highestSeq+1;
			
			raeFile.setSeq(newSeq);
			raeFile.setOutput_no(selectedRaeOutputHeader.getOutput_no());
			raeFile.setFile_tag(file_tag);
			raeFile.setFile_display_name(uploadedFile.getFileName());
			raeFile.setFile_size(uploadedFileSize);
			raeFile.setUserstamp(userId);
			
			raeFile = attDao.updateRaeAttachment(raeFile);		
			
			selectedRaeStaff = getSelectedRaeStaff();
			
			String staffName = selectedRaeStaff.getStaffIdentity().getFullname();
			staffName = staffName.replace(",", "");
			staffName = staffName.replace(" ", "_");
			
			String fileName = selectedRaeStaff.getPid()+"_"+selectedRaeStaff.getStaffNumber()+"_"+staffName+"_"+selectedRaeOutputHeader.getOutput_no()+"_"+file_tag+"_"+newSeq+uploadedFile.getFileName().substring(index);

			String appFilePath = getFilePath()+ File.separator + File.separator +fileName;
			
			raeFile.setFile_name(fileName);
			raeFile.setFile_path(appFilePath);
			try
			{
				raeFile = attDao.updateRaeAttachment(raeFile, uploadedFileIS);

				msg = MessageFormat.format(getResourceBundle().getString("msg.success.upload.x"), raeFile.getFile_display_name());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, msg, ""));
				
				resetFileList(file_tag);
			}
			catch (EJBException ee)
			{
				attDao.deleteRaeAttachment(raeFile.getFile_id());
				throw getRootCause(ee);				
			}
			catch (FileUploadException fne)
			{
				attDao.deleteRaeAttachment(raeFile.getFile_id());
				msg = fne.getMessage();
				//fCtx.addMessage("editForm:formTab:panel_upload_"+file_tag+"_msg", new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, ""));
				switch (file_tag) {
				    case SysParam.PARAM_RAE_FILE_SUP_DOC:		uploadErrMsgSupDoc = msg;
				    											break;
				    case SysParam.PARAM_RAE_FILE_P10: 			uploadErrMsgP10 = msg;
				    											break;
				    case SysParam.PARAM_RAE_FILE_P12: 			uploadErrMsgP12 = msg;
				    											break;
				    case SysParam.PARAM_RAE_FILE_FULL_VER: 		uploadErrMsgFullVer = msg;
																break;
				    case SysParam.PARAM_RAE_FILE_TOC_ATT: 		uploadErrMsgTocAtt = msg;
																break;
				    case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	uploadErrMsgTocAttOth = msg;
																break;
				    case SysParam.PARAM_RAE_FILE_SCW: 			uploadErrMsgScw = msg;
																break;
				    case SysParam.PARAM_RAE_FILE_ADD_INFO: 		uploadErrMsgAddInfo = msg;
																break;
				}
			}

		}
		catch (FileUploadException fne)
		{
			msg = fne.getMessage();
			switch (file_tag) {
				case SysParam.PARAM_RAE_FILE_SUP_DOC:		uploadErrMsgSupDoc = msg;
								break;
				case SysParam.PARAM_RAE_FILE_P10: 			uploadErrMsgP10 = msg;
								break;
				case SysParam.PARAM_RAE_FILE_P12: 			uploadErrMsgP12 = msg;
								break;
				case SysParam.PARAM_RAE_FILE_FULL_VER: 		uploadErrMsgFullVer = msg;
								break;
				case SysParam.PARAM_RAE_FILE_TOC_ATT: 		uploadErrMsgTocAtt = msg;
								break;
				case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	uploadErrMsgTocAttOth = msg;
								break;
				case SysParam.PARAM_RAE_FILE_SCW: 			uploadErrMsgScw = msg;
								break;
				case SysParam.PARAM_RAE_FILE_ADD_INFO: 		uploadErrMsgAddInfo = msg;
								break;
			}
		}
		catch (IOException ioe)
		{
			msg = "Cannot copy uploaded file '" + raeFile.getFile_display_name();
			switch (file_tag) {
				case SysParam.PARAM_RAE_FILE_SUP_DOC:		uploadErrMsgSupDoc = msg;
								break;
				case SysParam.PARAM_RAE_FILE_P10: 			uploadErrMsgP10 = msg;
								break;
				case SysParam.PARAM_RAE_FILE_P12: 			uploadErrMsgP12 = msg;
								break;
				case SysParam.PARAM_RAE_FILE_FULL_VER: 		uploadErrMsgFullVer = msg;
								break;
				case SysParam.PARAM_RAE_FILE_TOC_ATT: 		uploadErrMsgTocAtt = msg;
								break;
				case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	uploadErrMsgTocAttOth = msg;
								break;
				case SysParam.PARAM_RAE_FILE_SCW: 			uploadErrMsgScw = msg;
								break;
				case SysParam.PARAM_RAE_FILE_ADD_INFO: 		uploadErrMsgAddInfo = msg;
								break;
			}
			logger.log(Level.WARNING, "Unexpected Exception", ioe);
		}
		catch (Exception e) 
		{	
			msg = getResourceBundle().getString("msg.err.unexpected");
			switch (file_tag) {
				case SysParam.PARAM_RAE_FILE_SUP_DOC:		uploadErrMsgSupDoc = msg;
								break;
				case SysParam.PARAM_RAE_FILE_P10: 			uploadErrMsgP10 = msg;
								break;
				case SysParam.PARAM_RAE_FILE_P12: 			uploadErrMsgP12 = msg;
								break;
				case SysParam.PARAM_RAE_FILE_FULL_VER: 		uploadErrMsgFullVer = msg;
								break;
				case SysParam.PARAM_RAE_FILE_TOC_ATT: 		uploadErrMsgTocAtt = msg;
								break;
				case SysParam.PARAM_RAE_FILE_TOC_ATT_OTH: 	uploadErrMsgTocAttOth = msg;
								break;
				case SysParam.PARAM_RAE_FILE_SCW: 			uploadErrMsgScw = msg;
								break;
				case SysParam.PARAM_RAE_FILE_ADD_INFO: 		uploadErrMsgAddInfo = msg;
								break;
			}
			logger.log(Level.WARNING, "Unexpected Exception", e);
		}
		
		// Clear the corresponding variables
		uploadedFile = null;
		uploadedFileName = null;
		
	}
	
	public List<RaeAttachment> getUploadFileList(Integer output_no, String file_tag)
	{
		List<RaeAttachment> uploadFileList = new ArrayList<RaeAttachment>();
		uploadFileList = attDao.getRaeAttachmentList(output_no, file_tag);
		if (uploadFileList == null) {
			uploadFileList = new ArrayList<RaeAttachment>();
		}
		return uploadFileList;
	}

	public List<RaeAttachment> getUploadSupDocList()
	{
		if (uploadSupDocList == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadSupDocList = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_sup_doc");
			}
		}
		return uploadSupDocList;
	}

	
	public void setUploadSupDocList(List<RaeAttachment> uploadSupDocList)
	{
		this.uploadSupDocList = uploadSupDocList;
	}

	
	public List<RaeAttachment> getUploadP10List()
	{
		if (uploadP10List == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadP10List = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_p10");
			}
		}
		return uploadP10List;
	}

	
	public void setUploadP10List(List<RaeAttachment> uploadP10List)
	{
		this.uploadP10List = uploadP10List;
	}

	
	public List<RaeAttachment> getUploadP12List()
	{
		if (uploadP12List == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadP12List = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_p12");
			}
		}
		return uploadP12List;
	}

	
	public void setUploadP12List(List<RaeAttachment> uploadP12List)
	{
		this.uploadP12List = uploadP12List;
	}

	
	
	public List<RaeAttachment> getUploadFullVerList()
	{
		if (uploadFullVerList == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadFullVerList = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_full_ver");
			}
		}
		return uploadFullVerList;
	}

	
	public void setUploadFullVerList(List<RaeAttachment> uploadFullVerList)
	{
		this.uploadFullVerList = uploadFullVerList;
	}

	
	public List<RaeAttachment> getUploadTocAttList()
	{
		if (uploadTocAttList == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadTocAttList = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_toc_att");
			}
		}
		return uploadTocAttList;
	}

	
	public void setUploadTocAttList(List<RaeAttachment> uploadTocAttList)
	{
		this.uploadTocAttList = uploadTocAttList;
	}

	
	
	
	public List<RaeAttachment> getUploadTocAttOthList()
	{
		if (uploadTocAttOthList == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadTocAttOthList = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_toc_att_oth");
			}
		}
		return uploadTocAttOthList;
	}


	
	public void setUploadTocAttOthList(List<RaeAttachment> uploadTocAttOthList)
	{
		this.uploadTocAttOthList = uploadTocAttOthList;
	}


	public List<RaeAttachment> getUploadScwList()
	{
		if (uploadScwList == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadScwList = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_scw");
			}
		}
		return uploadScwList;
	}

	
	public void setUploadScwList(List<RaeAttachment> uploadScwList)
	{
		this.uploadScwList = uploadScwList;
	}

	
	public List<RaeAttachment> getUploadAddInfoList()
	{
		if (uploadAddInfoList == null) {
			if (getSelectedRaeOutputStatus() != null) {
				uploadAddInfoList = getUploadFileList(selectedRaeOutputStatus.getPk().getOutput_no(), "rae_add_info");
			}
		}
		return uploadAddInfoList;
	}

	
	public void setUploadAddInfoList(List<RaeAttachment> uploadAddInfoList)
	{
		this.uploadAddInfoList = uploadAddInfoList;
	}
	



	private String getFilePath(){
		String code = Constant.isLocalEnv() ? SysParam.PARAM_FILE_PATH_LOCAL : SysParam.PARAM_FILE_PATH;
		SysParamDAO paramDAO = SysParamDAO.getCacheInstance();
		return paramDAO.getSysParamValueByCode(code);
	}
	
	public UploadedFile getUploadedFile()
	{
		/*if (uploadedFile == null) {
			System.out.println("1");
		}else {
			System.out.println("2");
		}*/
		return uploadedFile;
	}


	public void setUploadedFile(UploadedFile uploadedFile)
	{
		this.uploadedFile = uploadedFile;
	}

	
	public long getUploadedFileSize()
	{
		return uploadedFileSize;
	}

	
	public void setUploadedFileSize(long uploadedFileSize)
	{
		this.uploadedFileSize = uploadedFileSize;
	}

	
	public InputStream getUploadedFileIS()
	{
		return uploadedFileIS;
	}

	
	public void setUploadedFileIS(InputStream uploadedFileIS)
	{
		this.uploadedFileIS = uploadedFileIS;
	}

	
	public String getUploadedFileName()
	{
		return uploadedFileName;
	}

	
	public void setUploadedFileName(String uploadedFileName)
	{
		this.uploadedFileName = uploadedFileName;
	}

	
	public Map<String, String> getP11SampleMap()
	{
		if (p11SampleMap == null) {
			p11SampleMap = new HashMap<String, String>();
			String type = "RAE_RA_RO";
			RaePanel sample = panelDao.getRaePanel(type, "30a");
			p11SampleMap.put(sample.getDescription(), "Modernism in Modern Chinese Literature");
			sample = panelDao.getRaePanel(type, "31a");
			p11SampleMap.put(sample.getDescription(), "English for Academic Purposes");
			sample = panelDao.getRaePanel(type, "33a");
			p11SampleMap.put(sample.getDescription(), "Interlanguage Phonology");
			sample = panelDao.getRaePanel(type, "35a");
			p11SampleMap.put(sample.getDescription(), "Hong Kong Model");
			sample = panelDao.getRaePanel(type, "35b");
			p11SampleMap.put(sample.getDescription(), "Europeans in East Asia");
			sample = panelDao.getRaePanel(type, "35c");
			p11SampleMap.put(sample.getDescription(), "Quality of life");
		}
		return p11SampleMap;
	}

	
	public void setP11SampleMap(Map<String, String> p11SampleMap)
	{
		this.p11SampleMap = p11SampleMap;
	}

	
	public List<Entry<String, String>> getP11SampleList()
	{
		if (p11SampleList == null)
			p11SampleList = new ArrayList<>(getP11SampleMap().entrySet());
		return p11SampleList;
	}

	
	public void setP11SampleList(List<Entry<String, String>> p11SampleList)
	{
		this.p11SampleList = p11SampleList;
	}
	
	public String getSelectedTypeDesc(String value)
	{
		String result = "";
		if (value != null) {
			value = value.toLowerCase();
			result = getFormResourceBundle().getString("rae.selected.type."+value);			
		}
		return result;
		
	}
	
	public void setNullValue() 
	{
		Integer panelCode = Integer.valueOf(getStaffPanel());
		// Justification for DW
		if (!("DW1".equals(selectedRaeOutputStatus.getSelType()) || "DW2".equals(selectedRaeOutputStatus.getSelType())))
			selectedRaeOutputHeader.setJust_dw_request(null);
      
		// Supp. Info on Non-trad. RO
		if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNon_trad_output_ind()) || "N".equals(selectedRaeOutputHeader.getNon_trad_output_ind()))
		     selectedRaeOutputHeader.setInfo_of_non_trad_output(null);
		  
		  // Sub-discipline
		  if(getSubDiscList().size() < 1)
		    selectedRaeOutputHeader.setSub_disc_code(null);
		    
		  // Other Output Type
		  if (!"N".equals(selectedRaeOutputHeader.getOutput_type()))
		    selectedRaeOutputHeader.setOth_output_type(null);
		    
		  // Primary Area
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getInter_research()) || "N".equals(selectedRaeOutputHeader.getInter_research())) {
		    selectedRaeOutputHeader.setPri_research_area_inter(null);
		    selectedRaeOutputHeader.setPri_research_area_cat(null);
		  }
		    
		  // Secondary Area
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getInter_research()) || "N".equals(selectedRaeOutputHeader.getInter_research())) {
		    selectedRaeOutputHeader.setSec_research_area_inter(null);
		    selectedRaeOutputHeader.setSec_research_area_cat(null);
		  }
		  
		  // AI TOOL
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getAi_tool_ind()) || "N".equals(selectedRaeOutputHeader.getAi_tool_ind())) {
		    selectedRaeOutputHeader.setAi_tool_desc(null); 
		  }
		  
		  // Other Language
		  if (!"MUL".equals(selectedRaeOutputHeader.getRel_lang()))
		    selectedRaeOutputHeader.setOth_lang(null); 
		    
		  // Title (Non ENG)
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getRel_lang()) || "ENG".equals(selectedRaeOutputHeader.getRel_lang()))
		    selectedRaeOutputHeader.setTitle_of_non_eng_output(null);
		    
		  // Explan. of Author's Contribution
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNo_of_co_author()) || !(panelCode == 3 || panelCode == 5 || panelCode == 10 || panelCode == 13)) {
		    selectedRaeOutputHeader.setExplanation_of_author_ctb(null);
		  }
		  else {
			  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNo_of_co_author()) == false){
				  if((panelCode == 3 && Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author()) < 10) ||
					 (panelCode == 5 && Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author()) <= 6) ||  
					 (panelCode == 10 && Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author()) <= 6) ||  
					 (panelCode == 13 && Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author()) <= 5)) 
					   selectedRaeOutputHeader.setExplanation_of_author_ctb(null);  
			  }
		          
		  }
		  
		  // List of Author (Non ENG)
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getRel_lang()) || "ENG".equals(selectedRaeOutputHeader.getRel_lang()))
		    selectedRaeOutputHeader.setList_of_author_non_eng(null);
		  
		  // Role of Sub. Staff
		  if (!"B".equals(selectedRaeOutputHeader.getOutput_type()))
		    selectedRaeOutputHeader.setRole_submit_staff(null);  
		    
		  // Intl. Research Collab
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNo_of_co_author()) || Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author()) < 2) {
		    selectedRaeOutputHeader.setIrc_has_result(null);
		  }
		  
		  // Book / Journal Title
		  if (!("C".equals(selectedRaeOutputHeader.getOutput_type()) || "D".equals(selectedRaeOutputHeader.getOutput_type())))
		    selectedRaeOutputHeader.setBook_title(null); 
		    
		  // ISBN
		  /*if (!("A".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "B".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "C".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "E".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "M".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "N".equals(selectedRaeOutputHeader.getOutput_type())))
		    selectedRaeOutputHeader.setIsbn(null);*/
		    
		  // ISSN / eISSN
		  /*if (!("D".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "E".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "M".equals(selectedRaeOutputHeader.getOutput_type()) ||
		  "N".equals(selectedRaeOutputHeader.getOutput_type()))) {
		    selectedRaeOutputHeader.setIssn(null);
		    selectedRaeOutputHeader.setEissn(null);
		  }*/
		    
		  // Volume (Issue)
		  if (!("C".equals(selectedRaeOutputHeader.getOutput_type()) || "D".equals(selectedRaeOutputHeader.getOutput_type())))
		    selectedRaeOutputHeader.setVol_issue(null);
		
		  // Page No.
		  if (!("C".equals(selectedRaeOutputHeader.getOutput_type()) || "D".equals(selectedRaeOutputHeader.getOutput_type())))
		    selectedRaeOutputHeader.setPage_num(null);
		  
		  if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPage_num_from()) && Strings.isNullOrEmpty(selectedRaeOutputHeader.getPage_num_to())) {
			  selectedRaeOutputHeader.setPage_num(null);
		  }
		
		  // Description on Location
		  if(GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getOutput_type()) ||
		    "A".equals(selectedRaeOutputHeader.getOutput_type()) ||
			"B".equals(selectedRaeOutputHeader.getOutput_type()) ||
			"C".equals(selectedRaeOutputHeader.getOutput_type()) ||
			"D".equals(selectedRaeOutputHeader.getOutput_type()))
		        selectedRaeOutputHeader.setDesc_loc_output(null);
		  
		      // Panel 10
		  if(panelCode != 10) {
		    selectedRaeOutputHeader.setPanel10RevInd(null);
		    selectedRaeOutputHeader.setPanel10RevExplain(null);
		    selectedRaeOutputHeader.setPanel10RoInd(null);
		    selectedRaeOutputHeader.setPanel10OpenUrl(null);
		    selectedRaeOutputHeader.setPanel10PtbInd(null);
		    selectedRaeOutputHeader.setPanel10ExplainPtb(null);
		    selectedRaeOutputHeader.setPanel10Explanation(null);
		  }
		  
		  // Panel 10 - Explan. of Quality Review
		  if (!"Y".equals(selectedRaeOutputHeader.getPanel10RevInd())) {
		    selectedRaeOutputHeader.setPanel10RevExplain(null);
		  }
		    
		  // Panel 10 - URL: Post-pub. Review
		  if (!"Y".equals(selectedRaeOutputHeader.getPanel10RoInd())) {
		    selectedRaeOutputHeader.setPanel10OpenUrl(null);
		  }
		  
		  // Panel 10 - Explan. of Practice-based or Commissioned RO
		  if (!"Y".equals(selectedRaeOutputHeader.getPanel10PtbInd())) {
		    selectedRaeOutputHeader.setPanel10ExplainPtb(null);
		  }
		  
		  // Panel 10 - Explan. of Conference Paper and Report
		  if (!"E".equals(selectedRaeOutputHeader.getOutput_type()))
		    selectedRaeOutputHeader.setPanel10Explanation(null);       
		  
		  // Panel 11 - Sub-disciplines info
		  if(panelCode != 11) {
		    selectedRaeOutputHeader.setPanel11SubDiscInfo(null);
		  }
		  
		  // Panel 12 - URL: Body of Evidence
		  if(panelCode != 12) {
		    selectedRaeOutputHeader.setPanel12UrlEvidence(null);
		  }
		  
		  // Physical Submission
		  if (!("2".equals(selectedRaeOutputHeader.getFormat_full_ver_submit()) ||
		  "3".equals(selectedRaeOutputHeader.getFormat_full_ver_submit()) ||
		  "4".equals(selectedRaeOutputHeader.getFormat_full_ver_submit()))) 
		  {
		    selectedRaeOutputHeader.setPhy_audio_qty(0);
		    selectedRaeOutputHeader.setPhy_cd_qty(0);
		    selectedRaeOutputHeader.setPhy_dvd_qty(0);
		    selectedRaeOutputHeader.setPhy_photo_qty(0);
		    selectedRaeOutputHeader.setPhy_book_qty(0);
		    selectedRaeOutputHeader.setPhy_usb_qty(0);
		    selectedRaeOutputHeader.setPhy_other_qty(0);
		    selectedRaeOutputHeader.setPhy_other_type(null);
		  }
		  
		  // Electionic Submission
		  if (!("1".equals(selectedRaeOutputHeader.getFormat_full_ver_submit()) ||
		  "3".equals(selectedRaeOutputHeader.getFormat_full_ver_submit()) ||
		  "4".equals(selectedRaeOutputHeader.getFormat_full_ver_submit()))) 
		  {
		    selectedRaeOutputHeader.setUrl_oa_full_ver(null);
		    // remove force blank of DOI in v2.3, 25 Oct 2019        
		// selectedRaeOutputHeader.setRoWithDoiInd(null);
		// selectedRaeOutputHeader.setDoi(null);
		  }
		  
		  // doi
		  if (!"Y".equals(selectedRaeOutputHeader.getRo_with_doi_ind())) {
		    selectedRaeOutputHeader.setDoi(null);
		  }
		  
		  // Presence of Abstract or TOC
		  // URL: Abstract or TOC
		  // Add. Info. on New Insights
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNon_trad_output_ind()) || "Y".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
		        selectedRaeOutputHeader.setToc(null);
		        selectedRaeOutputHeader.setUrl_toc(null);
		        selectedRaeOutputHeader.setAdditional_info(null);
		  }
		  
		  // Presence of Abstract or TOC Other
		  // URL: Abstract or TOC Other
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNon_trad_output_ind()) || "Y".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
		        selectedRaeOutputHeader.setToc_oth(null);
		        selectedRaeOutputHeader.setUrl_toc_oth(null);
		  }
		  
		  // URL: Abstract or TOC
		  if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getToc()) || "C".equals(selectedRaeOutputHeader.getToc())) 
			  selectedRaeOutputHeader.setUrl_toc(null);
		
		  // No. of part(s) of Single Coherent Work
		  // URL: Other Part(s) of Single coherent work  
		  // University Endorse. Form
		  // Justification and Supporting Argument
		  if (!("M".equals(selectedRaeOutputHeader.getOutput_type()))) {
		    selectedRaeOutputHeader.setNo_sgl_co_work(null);
		    selectedRaeOutputHeader.setUrl_ref(null);
		    selectedRaeOutputHeader.setUni_endorse_conf_ind(null);
		    selectedRaeOutputHeader.setJust_sgl_co_work(null);
		  }
	}
	
	//validate Research Output Type (RICH)
	public boolean validateSapOutputType() {
		boolean result = true;
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getSap_output_type()) == false && 
				Strings.isNullOrEmpty(selectedRaeOutputHeader.getOutput_type()) == false) {
			String sapOutputType = selectedRaeOutputHeader.getSap_output_type();
			String outputType = selectedRaeOutputHeader.getOutput_type();
			switch (outputType) {
			    case "A":	
			    	if ("120".equals(sapOutputType) == false) {
			    		result = false;
			    	}
			    	break;
			    case "B":
			    	if ("150".equals(sapOutputType) == false) {
			    		result = false;
			    	}
			    	break;
			    case "C":
			    	if ("130".equals(sapOutputType) == false) {
			    		result = false;
			    	}
			    	break;
			    case "D":
			    	if ("160".equals(sapOutputType) == false &&
			    		"170".equals(sapOutputType) == false &&
			    		"420".equals(sapOutputType) == false) {
			    		result = false;
			    	}
			    	break;
			    case "E":
			    	if ("180".equals(sapOutputType) == false &&
			    		"190".equals(sapOutputType) == false &&
			    		"200".equals(sapOutputType) == false) {
			    		result = false;
			    	}
			    	break;
			}
		}
		return result;
	}
	
	//validate name list
	public boolean validateRaeOutputDetails(String staff_no){
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		boolean yourself = false;
		boolean mustHaveFirstAuthor = false;
		boolean mustHaveFirstEditor = false;
		boolean mustHaveSub = mustHaveSubAuthorship();
		String errMessage = "msg.err.mandatory.x";
		String message;
		String allMessage = "";
		String concatAuthorName = "";
		int countAuthor = 0;
		int countFirstAuthor = 0;
		int countFirstEditor = 0;
		int countCorrAuthor = 0;
		String strMaxAuthor =  sysParamDao.getSysParamValueByCode("MAX_AUTHOR_LIST_LENGTH");
		int maxAuthor = 31;
		if (strMaxAuthor != null) {
			maxAuthor = Integer.valueOf(strMaxAuthor) + 1;
		}
		
		if (selectedRaeOutputHeader != null) {

			if (mustHaveSub && selectedRaeOutputHeader.getSap_output_type().equals("150") == false) {
				mustHaveFirstAuthor = true;
			}
			
			//Edited book (editor)
			if (mustHaveSub && selectedRaeOutputHeader.getSap_output_type().equals("150")) {
				mustHaveFirstEditor = true;
			}
			
			if (selectedRaeOutputDetails != null) {
				HashSet unique=new HashSet();
				if (!Strings.isNullOrEmpty(staff_no)) {
					//for loop contributor list
					for (RaeOutputDetails p:selectedRaeOutputDetails) {
						int lineNo = countAuthor + 1;

						//get staff details
						if ("N".equals(p.getNon_ied_staff_flag())) {
							if (p.getAuthorship_staff_no() == null) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
								allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")")+"<br/>";
								fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								StaffIdentity s = staffDao.getStaffDetailsByStaffNo(p.getAuthorship_staff_no());
								if (s != null) {
									p.setAuthorship_name(s.getFullname_save());
								}	
							}					
						}
						
						//get past staff details						
						if ("F".equals(p.getNon_ied_staff_flag())) {
							if (p.getAuthorship_name() == null) {
								result = false;
								message = "Contributor (no. "+lineNo+") is not correct.";
								allMessage += "- "+message+"<br/>";
								fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								StaffPast sp = staffDao.getPastStaffDetailsByStaffNo(p.getAuthorship_staff_no());
								if (sp != null) {
									//p.setAuthorship_staff_no(sp.getStaff_number());
									//p.setAuthorship_name(sp.getFullname_save());
								}else {
									result = false;
									message = "Contributor (no. "+lineNo+") is not correct.";
									allMessage += "- "+message+"<br/>";
									fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
								}
							}
						}
						
						//check name is not null
						if ("Y".equals(p.getNon_ied_staff_flag()) || "S".equals(p.getNon_ied_staff_flag())) {
							if (Strings.isNullOrEmpty(p.getAuthorship_name())) {
								result = false;
								message = MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")");
								allMessage += "- "+MessageFormat.format(getResourceBundle().getString(errMessage), "Contributor (no. "+lineNo+")")+"<br/>";
								fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								byte[] nameBytes = p.getAuthorship_name().getBytes(StandardCharsets.UTF_8);
								if (nameBytes.length > 80) {
									result = false;
									message = "Contributor (no. "+lineNo+") is too long.";
									allMessage += "- Contributor (no. "+lineNo+") is too long.<br/>";
									fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
								}else {
									p.setAuthorship_person_id(null);
									p.setAuthorship_staff_no(null);
									p.setAuthorship_assignment_id(null);
								}								
							}
						}
						//check duplicate
						if (!unique.add(p.getAuthorship_staff_no()) && p.getAuthorship_staff_no() != null){
							result = false;
							if (!Strings.isNullOrEmpty(p.getAuthorship_name())) {
								message = "Collaborative Contributor - Staff ("+p.getAuthorship_name()+") cannot be duplicated.";
								allMessage += "- Collaborative Contributor - Staff ("+p.getAuthorship_name()+") cannot be duplicated.<br/>";
								fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}else {
								message = "Collaborative Contributor cannot be duplicated.";
								allMessage += "- Collaborative Contributor cannot be duplicated.<br/>";
								fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
							}
						}
					
						boolean removeSub = true;
						if (mustHaveSub) {
							if ("AUTHOR".equals(p.getAuthorship_type())) {
								if ("FIRST".equals(p.getAuthorship_dtl_type()) || "FIRST CO".equals(p.getAuthorship_dtl_type())){
									countFirstAuthor++;
									removeSub = false;
								}
								if ("CORRESPONDING".equals(p.getAuthorship_dtl_type())){
									countCorrAuthor++;
									removeSub = false;
								}
								if ("CO".equals(p.getAuthorship_dtl_type())){
									removeSub = false;
								}
							}
							if ("EDITOR".equals(p.getAuthorship_type())) {
								if ("FIRST".equals(p.getAuthorship_dtl_type()) || "FIRST CO".equals(p.getAuthorship_dtl_type())){
									countFirstEditor++;
									removeSub = false;
								}
								if ("CORRESPONDING".equals(p.getAuthorship_dtl_type()) || "CO".equals(p.getAuthorship_dtl_type())){
									removeSub = false;
								}
							}
						}
						
						if (removeSub) {
							p.setAuthorship_dtl_type(null);
						}
						
						countAuthor++;
						
						if (countAuthor < maxAuthor) {
							String tmpStaffNo = (p.getAuthorship_staff_no() == null)?", ":" (" + p.getAuthorship_staff_no() + "), ";
							concatAuthorName += p.getAuthorship_name() + tmpStaffNo + p.getAuthorship_type() + "; ";
						}
						if (countAuthor == maxAuthor) {
							concatAuthorName += "*";
						}
						
						if (p.getAuthorship_staff_no() != null) {
							if (staff_no.equals(p.getAuthorship_staff_no())) {
								yourself = true;
							}
						}	
					}
				}
			}else {
				result = false;
				message = "There must be one Contributor.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		if (yourself == false) {
			result = false;
			message = "Creator must be one of the Collaborative Contributors.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (mustHaveFirstAuthor && countFirstAuthor < 1) {
			result = false;
			message = "There must be a First Author.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (mustHaveFirstEditor && countFirstEditor < 1) {
			result = false;
			message = "There must be a First Editor.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:columnTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		if (selectedRaeOutputHeader != null) {
			if (!concatAuthorName.isEmpty()) {
				if (concatAuthorName.length() > 900) {
					concatAuthorName = StringUtils.substring(concatAuthorName, 0, 900);
					concatAuthorName += "*";
				}
			}
			selectedRaeOutputHeader.setTotal_no_of_author(countAuthor);
			selectedRaeOutputHeader.setConcatenated_author_name(concatAuthorName);
		}
		
		if (!result) {
			hasError = true;
			canSave = false;
			countErrorRoInfo++;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	public void setFieldValue(){
		//set other language
		if ("MUL".equals(selectedRaeOutputHeader.getRel_lang())) {
			if(selectedRaeOutputHeader.getOth_lang_list() != null) 
				selectedRaeOutputHeader.setOth_lang(String.join(",", selectedRaeOutputHeader.getOth_lang_list()));
		}else {
			selectedRaeOutputHeader.setOth_lang(null);
		}
		//set cat code
		if(selectedRaeOutputHeader.getCat_code_list() != null) 
			selectedRaeOutputHeader.setCat_code(String.join(";", selectedRaeOutputHeader.getCat_code_list()));
		
		//set page number
		String page_num = null;
		if(Strings.isNullOrEmpty(selectedRaeOutputHeader.getPage_num_to()) == false) {
			page_num = selectedRaeOutputHeader.getPage_num_from() + "-" + selectedRaeOutputHeader.getPage_num_to();
		}else {
			page_num = selectedRaeOutputHeader.getPage_num_from();
		}
		selectedRaeOutputHeader.setPage_num(page_num);
	}
	
	//validate Physical Submission
	public Boolean validatePhysicalSubmission() {
		boolean result = true;
		String message;
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (showPhySubmitPanel() == true) {
			int totalQty = selectedRaeOutputHeader.getPhy_audio_qty()
						+ selectedRaeOutputHeader.getPhy_audio_qty()
						+ selectedRaeOutputHeader.getPhy_cd_qty()
						+ selectedRaeOutputHeader.getPhy_dvd_qty()
						+ selectedRaeOutputHeader.getPhy_photo_qty()
						+ selectedRaeOutputHeader.getPhy_book_qty()
						+ selectedRaeOutputHeader.getPhy_usb_qty()
						+ selectedRaeOutputHeader.getPhy_other_qty();
			if (totalQty < 1) {
				result = false;
				countErrorFullVerSubmit++;
				message = "Please select appropriate Medium Type(s) for Physical Submission.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:phy_audio_qty", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (selectedRaeOutputHeader.getPhy_other_qty() > 0) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPhy_other_type())) {
					result = false;
					countErrorFullVerSubmit++;
					message = "Please specify Other medium of output for Physical Submission.";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:phy_other_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}else {
					if(StringUtils.countMatches(selectedRaeOutputHeader.getPhy_other_type(), ",") > 0) {
						result = false;
						countErrorFullVerSubmit++;
						message = "Please use semi-colon (;) to separate between each medium.";
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:formTab:phy_other_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
				}
			}
			if (selectedRaeOutputHeader.getPhy_other_qty() < 1 && Strings.isNullOrEmpty(selectedRaeOutputHeader.getPhy_other_type()) == false) {
				result = false;
				countErrorFullVerSubmit++;
				message = "Please select Quantity of Other medium.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:phy_other_qty", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
	
	public Boolean validateISSN() {
	    if (selectedRaeOutputHeader == null) {
	        return false; // or handle null case appropriately
	    }

	    boolean result = true;
	    FacesContext fCtx = FacesContext.getCurrentInstance();
	    ISSNValidator validator = ISSNValidator.getInstance();

	    result &= validateIssnField(selectedRaeOutputHeader.getIssn(), "issn", "ISSN", fCtx, validator);
	    result &= validateIssnField(selectedRaeOutputHeader.getEissn(), "eissn", "EISSN", fCtx, validator);

	    if (!result) {
	        hasError = true;
	    }

	    return result;
	}

	private boolean validateIssnField(String issn, String fieldId, String fieldName, FacesContext fCtx, ISSNValidator validator) {
	    if ("N/A".equalsIgnoreCase(issn)) {
	        return true;
	    }

	    if (StringUtils.isNotBlank(issn)) {
	        issn = issn.trim();
	        if (!issn.equals("0000-0000") && !validator.isValid(issn)) {
	            countErrorRoInfo++;
	            String message = "Invalid " + fieldName + ".";
	            fCtx.addMessage("editForm:formTab:" + fieldId, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
	            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- " + message, ""));
	            return false;
	        } else {
	            if (fieldId.equals("issn")) {
	                selectedRaeOutputHeader.setIssn(issn);
	            } else if (fieldId.equals("eissn")) {
	                selectedRaeOutputHeader.setEissn(issn);
	            }
	        }
	    }

	    return true;
	}
	
	public Boolean validateISBN() {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        ISBNValidator validator = ISBNValidator.getInstance();

        if (selectedRaeOutputHeader == null || StringUtils.isBlank(selectedRaeOutputHeader.getIsbn())) {
            return true;
        }

        String isbn = selectedRaeOutputHeader.getIsbn();

        if ("N/A".equalsIgnoreCase(isbn)) {
            return true;
        }

        if (!validator.isValid(isbn)) {
            String message = "Invalid ISBN.";
            fCtx.addMessage("editForm:formTab:isbn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "- " + message, ""));
            countErrorRoInfo++;
            hasError = true;
            return false;
        }

        return true;
    }
	
	//validate mandatory field	
	public Boolean validateRequiredField() {
		boolean result = true;
		String errMessage = "msg.err.mandatory.x";
		String errWordMessage = "msg.err.word.exceed.x";
		String message;
		String allMessage = "";
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		//Generation Information	
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getResearchAreaOfRo())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Research Area of Research Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:researchAreaOfRo", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			if(checkRaOfRo(selectedRaeOutputHeader.getResearchAreaOfRo()) == false) {
				result = false;
				countErrorRoInfo++;
				message = "Research Area of Research Output is not under the same UoA.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:researchAreaOfRo", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//Sub-disciplines of Research Output
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getSub_disc_code()) && getSubDiscList().size() > 0) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Sub-disciplines of Research Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:sub_disc_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		//Cat Code List
		if (selectedRaeOutputHeader.getCat_code_list() == null) {
			selectedRaeOutputHeader.setCat_code_list(new ArrayList<String>());
		}
		if (selectedRaeOutputHeader.getCat_code_list().size() < 1) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Category Code(s)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:cat_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (selectedRaeOutputHeader.getCat_code_list().size() > 2) {
			result = false;
			countErrorRoInfo++;
			message = "Max. two Category Codes for each research output.";
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:cat_code", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}

				
		//Research Output Type (RICH)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getSap_output_type())) {
			result = false;
			canSave = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Research Output Type (RICH)");
			allMessage += "- "+message+" [Record not saved]<br/>";
			fCtx.addMessage("editForm:formTab:sap_output_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Research Output Type (RAE)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getOutput_type())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Research Output Type (RAE)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:output_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Other Output Type (in English)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getOth_output_type()) && "N".equals(selectedRaeOutputHeader.getOutput_type())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Other Output Type (in English)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:oth_output_type", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Type of Research Activity
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getSap_refered_journal())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Type of Research Activity");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:sap_refered_journal", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Non-traditional Research Output
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNon_trad_output_ind())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Non-traditional Research Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:non_trad_output_ind", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			//Supplementary Information on Non-traditional Research Output
			if ("Y".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getInfo_of_non_trad_output())) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Supplementary Information on Non-traditional Research Output");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:info_of_non_trad_output", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (validateCountWords(selectedRaeOutputHeader.getInfo_of_non_trad_output(), 300) == false) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Supplementary Information on Non-traditional Research Output", 300);
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:info_of_non_trad_output", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
		}
		//Justification for Double-weighting Request
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getJust_dw_request()) && getIsDoubledWeighted() == true) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Justification for Double-weighting Request");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:just_dw_request", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (validateCountWords(selectedRaeOutputHeader.getJust_dw_request(), 100) == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Justification for Double-weighting Request", 100);
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:just_dw_request", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Interdisciplinary
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getInter_research())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Interdisciplinary");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:inter_research", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Primary Area
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPri_research_area_inter()) && "Y".equals(selectedRaeOutputHeader.getInter_research()) == true) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Primary Area of Interdisciplinary Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:pri_research_area_inter", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Primary Area Cat
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPri_research_area_cat()) && "Y".equals(selectedRaeOutputHeader.getInter_research()) == true) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Category Code under Primary Research Area");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:pri_research_area_cat", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Secondary Area
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getSec_research_area_inter()) && "Y".equals(selectedRaeOutputHeader.getInter_research()) == true) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Secondary Area of Interdisciplinary Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:sec_research_area_inter", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Secondary Area Cat
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getSec_research_area_cat()) && "Y".equals(selectedRaeOutputHeader.getInter_research()) == true) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Category Code under Secondary Research Area");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:sec_research_area_cat", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPri_research_area_inter()) == false && 
			Strings.isNullOrEmpty(selectedRaeOutputHeader.getSec_research_area_inter()) == false && 
			"Y".equals(selectedRaeOutputHeader.getInter_research()) == true) {
			//System.out.println("selectedRaeOutputHeader.getPri_research_area_inter():"+selectedRaeOutputHeader.getPri_research_area_inter());
			//System.out.println("selectedRaeOutputHeader.getSec_research_area_inter():"+selectedRaeOutputHeader.getSec_research_area_inter());
			//System.out.println("selectedRaeOutputHeader.getResearchAreaOfRo():"+selectedRaeOutputHeader.getResearchAreaOfRo());
			//Primary Area / Secondary Area cannot be the same
			if (selectedRaeOutputHeader.getPri_research_area_inter().equals(selectedRaeOutputHeader.getSec_research_area_inter())) {
				result = false;
				countErrorRoInfo++;
				message = "Primary Area / Secondary Area cannot be the same.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:pri_research_area_inter", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			//Either Primary Area or Secondary Area of Interdisciplinary should be the same as the Research Area of Research Output.
			if (selectedRaeOutputHeader.getPri_research_area_inter().equals(selectedRaeOutputHeader.getResearchAreaOfRo()) == false &&
				selectedRaeOutputHeader.getSec_research_area_inter().equals(selectedRaeOutputHeader.getResearchAreaOfRo()) == false && 
				"Y".equals(selectedRaeOutputHeader.getInter_research()) == true) {
				result = false;
				countErrorRoInfo++;
				message = "Either Primary Area or Secondary Area of Interdisciplinary should be the same as the Research Area of Research Output.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:pri_research_area_inter", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//AI Tool
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getAi_tool_ind())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Use of AI Tool");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:ai_tool_ind", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//AI Tool Description
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getAi_tool_desc()) && "Y".equals(selectedRaeOutputHeader.getAi_tool_ind()) == true) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Description of Use of AI Tool");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:ai_tool_desc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (validateCountWords(selectedRaeOutputHeader.getAi_tool_desc(), 200) == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Description of Use of AI Tool", 200);
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:ai_tool_desc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Output Language
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getRel_lang())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Output Language");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:rel_lang", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Other Output Langauge
		if ("MUL".equals(selectedRaeOutputHeader.getRel_lang())) {
			if (selectedRaeOutputHeader.getOth_lang_list() == null) {
				selectedRaeOutputHeader.setOth_lang_list(new ArrayList<String>());
			}
			if (selectedRaeOutputHeader.getOth_lang_list().size() < 1) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Other Language / Multiple Languages");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:oth_lang", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		
		//Title of Research Output (in English)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getTitle_of_eng_output())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Title of Research Output (in English)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:title_of_eng_output", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			//Title of Research Output (in English) should not contain DOI number.
			if (StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getTitle_of_eng_output(), "doi:")) {
				result = false;
				countErrorRoInfo++;
				message = "Title of Research Output (in English) should not contain DOI number.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:title_of_eng_output", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//Title of Research Output in Published Language
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getRel_lang()) == false) {
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getTitle_of_non_eng_output()) && "ENG".equals(selectedRaeOutputHeader.getRel_lang()) == false) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Title of Research Output in Published Language");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:title_of_non_eng_output", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//Statement on Originality and Significance
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getStmt_ori_sign()) && showStmt_ori_sign()) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Statement on Originality and Significance");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:stmt_ori_sign", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (validateCountWords(selectedRaeOutputHeader.getStmt_ori_sign(), 100) == false) {
			result = false;
			message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Statement on Originality and Significance", 100);
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:stmt_ori_sign", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//No. of Author(s)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNo_of_co_author())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "No. of Author(s)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:no_of_co_author", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNo_of_co_author()) == false) {
			//International Research Collaboration
			/*if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getIrc_has_result()) && Integer.valueOf(selectedRaeOutputHeader.getNo_of_co_author()) > 1) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "International Research Collaboration");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:irc_has_result", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}*/
			//Role of Submitting Author
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getRole_author_ind()) && 
					showRoleSubmitAuthor(Integer.valueOf(selectedRaeOutputHeader.getNo_of_co_author())) == true) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Role of Submitting Author");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:role_author_ind", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			//Explanation of Submitting Author's Contribution
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getExplanation_of_author_ctb()) && 
					showExplanationOfAuthorContribution(Integer.valueOf(selectedRaeOutputHeader.getNo_of_co_author()), selectedRaeOutputHeader.getRole_author_ind()) == true) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Explanation of Submitting Author's Contribution");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:explanation_of_author_ctb", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (validateCountWords(selectedRaeOutputHeader.getExplanation_of_author_ctb(), 100) == false) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Explanation of Submitting Author's Contribution", 100);
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:explanation_of_author_ctb", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//Role of Submitting Staff
		/*if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getRole_submit_staff()) && "B".equals(selectedRaeOutputHeader.getOutput_type())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Role of Submitting Staff");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:role_submit_staff", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}*/
		//List of Author(s) (in English)
		String err_msg_list_of_author_eng = "";
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getList_of_author_eng())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "List of Author(s) (in English)");
			allMessage += "- "+message+"<br/>";
			err_msg_list_of_author_eng += message;
		}else {
			selectedRaeOutputHeader.setList_of_author_eng(selectedRaeOutputHeader.getList_of_author_eng().trim());
			
			//List of Author(s) (in English) - format check '*'
			if(StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_eng(), "*") != 1) {
				result = false;
				countErrorRoInfo++;
				message = "Please indicate the submitting staff with a * in the List of Author(s) (in English).";
				allMessage += "- "+message+"<br/>";
				err_msg_list_of_author_eng += message;
			}
			//List of Author(s) (in English) - format check ';'
			if(StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_eng(), ",") > 0 || StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_eng(), "&") > 0) {
				result = false;
				countErrorRoInfo++;
				message = "Please use a semi-colon (;) to separate each author, and avoid comma (,) in name.";
				allMessage += "- "+message+"<br/>";
				if (err_msg_list_of_author_eng != "") {
					err_msg_list_of_author_eng += "　";
				}
				err_msg_list_of_author_eng += message;
			}
			if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNo_of_co_author()) == false){
				Integer noOfAuthor = Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author());
				Integer countColon = StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_eng(), ";");
				if (noOfAuthor > 1) {
					if (noOfAuthor - countColon > 1 || noOfAuthor - countColon < 1) {
						result = false;
						countErrorRoInfo++;
						message = "Please input " + noOfAuthor + " name(s) in the List of Author(s) (in English).";
						allMessage += "- "+message+"<br/>";
						if (err_msg_list_of_author_eng != "") {
							err_msg_list_of_author_eng += "　";
						}
						err_msg_list_of_author_eng += message;
					}
				}
				//System.out.println("noOfAuthor1:"+noOfAuthor);
				//System.out.println("countColon1:"+countColon);
			}
			
		}
		if (err_msg_list_of_author_eng != "") {
			fCtx.addMessage("editForm:formTab:list_of_author_eng", new FacesMessage(FacesMessage.SEVERITY_ERROR, err_msg_list_of_author_eng, err_msg_list_of_author_eng));
		}
		//List of Author(s) (other than English) 
		if (selectedRaeOutputHeader.getRel_lang() != null) {
			if ("ENG".equals(selectedRaeOutputHeader.getRel_lang()) == false) {
				String err_msg_list_of_author_non_eng = "";
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getList_of_author_non_eng())) {
					result = false;
					countErrorRoInfo++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "List of Author(s) (other than English)");
					allMessage += "- "+message+"<br/>";
					err_msg_list_of_author_non_eng += message;
					fCtx.addMessage("editForm:formTab:list_of_author_non_eng", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}else {
					selectedRaeOutputHeader.setList_of_author_non_eng(selectedRaeOutputHeader.getList_of_author_non_eng().trim());
					//List of Author(s) (other than English) (in English) - format check '*'
					if(StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_non_eng(), "*") != 1) {
						result = false;
						countErrorRoInfo++;
						message = "Please indicate the submitting staff with a * in the List of Author(s) (other than English).";
						allMessage += "- "+message+"<br/>";
						err_msg_list_of_author_non_eng += message;
					}
					//List of Author(s) (other than English) (in English) - format check ';'
					if(StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_non_eng(), ",") > 0 || 
					   StringUtils.countMatches(StringEscapeUtils.escapeJava(selectedRaeOutputHeader.getList_of_author_non_eng()), "\\u3001") > 0) {
						result = false;
						countErrorRoInfo++;
						message = "Please use a semi-colon (;) to separate each author, and avoid comma (,) in name.";
						allMessage += "- "+message+"<br/>";
						if (err_msg_list_of_author_non_eng != "") {
							err_msg_list_of_author_non_eng += "　";
						}
						err_msg_list_of_author_non_eng += message;
					}
					if (GenericValidator.isBlankOrNull(selectedRaeOutputHeader.getNo_of_co_author()) == false){
						Integer noOfAuthor = Integer.parseInt(selectedRaeOutputHeader.getNo_of_co_author());
						Integer countColon = StringUtils.countMatches(selectedRaeOutputHeader.getList_of_author_non_eng(), ";");
						if (noOfAuthor > 1) {
							if (noOfAuthor - countColon > 1 || noOfAuthor - countColon < 1) {
								result = false;
								countErrorRoInfo++;
								message = "Please input " + noOfAuthor + " name(s) in the List of Author(s) (other than English).";
								allMessage += "- "+message+"<br/>";
								if (err_msg_list_of_author_non_eng != "") {
									err_msg_list_of_author_non_eng += "　";
								}
								err_msg_list_of_author_non_eng += message;
							}
						}
						//System.out.println("noOfAuthor:"+noOfAuthor);
						//System.out.println("countColon:"+countColon);
					}
				}
				if (err_msg_list_of_author_non_eng != "") {
					fCtx.addMessage("editForm:formTab:list_of_author_non_eng", new FacesMessage(FacesMessage.SEVERITY_ERROR, err_msg_list_of_author_non_eng, err_msg_list_of_author_non_eng));
				}	
			}else {
				selectedRaeOutputHeader.setList_of_author_non_eng(null);
			}
		}
		
		//Published as of Census Date
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPublished_census_date())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Published as of Census Date");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:published_census_date", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Supporting Document / Letter of Acceptance for Publishing the Research Output
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPublished_census_date()) == false) {
			if (getUploadSupDocList().size() < 1 && "D".equals(selectedRaeOutputHeader.getPublished_census_date())) {
				result = false;
				countErrorRoInfo++;
				message = "Please upload Supporting Document / Letter of Acceptance for Publishing the Research Output.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:uploadedSupDocTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//Book / Journal Title
		if (showBookTitle()) {
			String err_msg_book_title = "";
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getBook_title())) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Book / Journal Title");
				allMessage += "- "+message+"<br/>";
				err_msg_book_title += message;
			}else {
				selectedRaeOutputHeader.setBook_title(selectedRaeOutputHeader.getBook_title().trim());
			}
			
			//Book / Journal Title - format
			if (StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getBook_title(), "http://") || StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getBook_title(), "https://")){
				result = false;
				countErrorRoInfo++;
				message = "Book / Journal Title should not contain hyperlink.";
				allMessage += "- "+message+"<br/>";
				if (err_msg_book_title != "") {
					err_msg_book_title += "　";
				}
				err_msg_book_title += message;			}
			if (err_msg_book_title != "") {
				fCtx.addMessage("editForm:formTab:book_title", new FacesMessage(FacesMessage.SEVERITY_ERROR, err_msg_book_title, err_msg_book_title));
			}
		}else {
			selectedRaeOutputHeader.setBook_title(null);
		}
		
		//ISSN
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getIssn()) && 
				("A".equals(selectedRaeOutputHeader.getOutput_type()) || "B".equals(selectedRaeOutputHeader.getOutput_type()) || "C".equals(selectedRaeOutputHeader.getOutput_type()) || "D".equals(selectedRaeOutputHeader.getOutput_type())) &&
				"A".equals(selectedRaeOutputHeader.getPublished_census_date())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "ISSN");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:issn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//EISSN
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getEissn()) && 
				("A".equals(selectedRaeOutputHeader.getOutput_type()) || "B".equals(selectedRaeOutputHeader.getOutput_type()) || "C".equals(selectedRaeOutputHeader.getOutput_type()) || "D".equals(selectedRaeOutputHeader.getOutput_type())) &&
				"A".equals(selectedRaeOutputHeader.getPublished_census_date())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "eISSN");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:eissn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//ISBN
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getIsbn()) && 
			("A".equals(selectedRaeOutputHeader.getOutput_type()) || "B".equals(selectedRaeOutputHeader.getOutput_type()) || "C".equals(selectedRaeOutputHeader.getOutput_type()) || "D".equals(selectedRaeOutputHeader.getOutput_type())) && 
			"A".equals(selectedRaeOutputHeader.getPublished_census_date())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "ISBN");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:isbn", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Article No.
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getArticle_no()) && showArticleNo()) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Article No.");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:article_no", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Volume (Issue)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getVol_issue()) && showVol()) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Volume (Issue)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:vol_issue", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Page No.
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPage_num_from()) && showPageNum()) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Page No.");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:page_num_from", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Page No. Range
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPage_num_from()) == false && Strings.isNullOrEmpty(selectedRaeOutputHeader.getPage_num_to()) == false && showPageNum()) {
			if (GenericValidator.isInt(selectedRaeOutputHeader.getPage_num_from()) && GenericValidator.isInt(selectedRaeOutputHeader.getPage_num_to())) {
				int fromPage = Integer.parseInt(selectedRaeOutputHeader.getPage_num_from());
				int toPage = Integer.parseInt(selectedRaeOutputHeader.getPage_num_to());
				if (fromPage > toPage) {
					result = false;
					countErrorRoInfo++;
					message = "Page No. (From) cannot greater than Page No. (To).";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:page_num_from", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
		}
		//Date
		if (selectedRaeOutputHeader.getFrom_year() == null) {
			if (selectedRaeOutputHeader.getFrom_month() != null) {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date (year)");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:from_year", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}else {
				result = false;
				countErrorRoInfo++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Date");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:from_year", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		//Month and Year Range
		if (selectedRaeOutputHeader.getFrom_month() != null && selectedRaeOutputHeader.getFrom_year() != null) { 
			String strMonth = selectedRaeOutputHeader.getFrom_month().toString();
		    String twoDigitMonth = ((strMonth.length() == 1)?"0":"") + selectedRaeOutputHeader.getFrom_month();          
		    int outputDate = Integer.parseInt(selectedRaeOutputHeader.getFrom_year() + twoDigitMonth);
		    
		    if(outputDate < Integer.parseInt("201910") || outputDate > Integer.parseInt("202509")) {
		    	result = false;
		    	countErrorRoInfo++;
				message = "Date falls outside the assessment period (1 October 2019 to 30 September 2025).";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:from_month", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		    }
		}
		//Description on Location of Output
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getDesc_loc_output()) && showDescLocOutput()) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Description on Location of Output");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:desc_loc_output", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Publisher / Manufacturer
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPublisher()) && requirePublisher()) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Publisher / Manufacturer");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:publisher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Publisher / Manufacturer - format
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPublisher()) == false && requirePublisher()) {
			if (selectedRaeOutputHeader.getPublisher().matches(".*[a-zA-Z]+.*") == false) {
				result = false;
				countErrorRoInfo++;
				message = "Please input translated Publisher / Manufacturer name in English with name in original language in ().";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:publisher", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		
		//Funder(s) and Funding Programme(s)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getFunder())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Funder(s) and Funding Programme(s)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:funder", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		
		//Name and Location of Collaborator(s)
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getColNameLoc())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Name and Location of Collaborator(s)");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:colNameLoc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
				
		//International Research Collaboration
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getIntColInd())) {
			result = false;
			countErrorRoInfo++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "International Research Collaboration");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:intColInd", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
				
		//Panel 10
		if ("10".equals(getStaffPanel())) {
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10RevInd())) {
				result = false;
				countErrorP10++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Outputs that have not been subject to formal peer-review or refereeing processes");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel10RevInd", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10RevInd()) == false) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10RevExplain()) && "Y".equals(selectedRaeOutputHeader.getPanel10RevInd())) {
					result = false;
					countErrorP10++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "A Note Explaining What Quality Review Has Been Carried Out on the Output");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel10RevExplain", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10RoInd())) {
				result = false;
				countErrorP10++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Availability of evidence of post-publication review for research outputs in open repositories");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel10RoInd", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10RoInd()) == false) {
				if (getUploadP10List().size() < 1 && "Y".equals(selectedRaeOutputHeader.getPanel10RoInd())) {
					result = false;
					countErrorP10++;
					message = "Please upload Evidence of Post-publication Review for Research Output in Open Repository.";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:uploadedP10Table", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10PtbInd())) {
				result = false;
				countErrorP10++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Practice-based Output or Commissioned Research Output");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel10PtbInd", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10PtbInd()) == false) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10ExplainPtb()) && "Y".equals(selectedRaeOutputHeader.getPanel10PtbInd())) {
					result = false;
					countErrorP10++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Short Text Note Explaining Practice-based Output or Commissioned Research Output");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel10ExplainPtb", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (validateCountWords(selectedRaeOutputHeader.getPanel10ExplainPtb(), 100) == false) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Short Text Note Explaining Practice-based Output or Commissioned Research Output", 100);
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel10ExplainPtb", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			//Explanation for Conference Paper and Report
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10Explanation()) && "E".equals(selectedRaeOutputHeader.getOutput_type())) {
				result = false;
				countErrorP10++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Explanation for Conference Paper and Report");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel10Explanation", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (validateCountWords(selectedRaeOutputHeader.getPanel10Explanation(), 100) == false) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Explanation for Conference Paper and Report", 100);
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel10Explanation", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10PubInd())) {
				result = false;
				countErrorP10++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Published Report");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel10PubInd", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			//Explanation for published Report
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10PubInd()) == false) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel10PubDesc()) && "Y".equals(selectedRaeOutputHeader.getPanel10PubInd())) {
					result = false;
					countErrorP10++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Explanation for published Report");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel10PubDesc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (validateCountWords(selectedRaeOutputHeader.getPanel10PubDesc(), 100) == false) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Explanation for published Report", 100);
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel10PubDesc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
		}
		//Panel 12
		if ("12".equals(getStaffPanel())) {
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel12UrlEvidence()) == false) {
				selectedRaeOutputHeader.setPanel12UrlEvidence(selectedRaeOutputHeader.getPanel12UrlEvidence().trim());
				if (isValidUrl(selectedRaeOutputHeader.getPanel12UrlEvidence()) == false) {
					result = false;
					countErrorP12++;
					message = "Invalid format of URL on Publicly Accessible Internet Location for Body of Evidence. (must start with http:// or https://)";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel12UrlEvidence", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel12MulInd())) {
				result = false;
				countErrorP12++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Multi-component Output");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:panel12MulInd", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel12MulInd()) == false) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel12MulUrl()) && "Y".equals(selectedRaeOutputHeader.getPanel12MulInd())) {
					result = false;
					countErrorP12++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "URL for the location of multi-component output");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:panel12MulUrl", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getPanel12MulUrl()) == false && "Y".equals(selectedRaeOutputHeader.getPanel12MulInd())) {
					selectedRaeOutputHeader.setPanel12MulUrl(selectedRaeOutputHeader.getPanel12MulUrl().trim());
					if (isValidUrl(selectedRaeOutputHeader.getPanel12MulUrl()) == false) {
						result = false;
						countErrorP12++;
						message = "Invalid format of URL for the location of multi-component output. (must start with http:// or https://)";
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:formTab:panel12MulUrl", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
				}
			}
			
		}
		//Full Version Submission of Research Output
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getFormat_full_ver_submit())) {
			result = false;
			countErrorFullVerSubmit++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Format of Full Version Submission");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:format_full_ver_submit", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//Electronic Submission
		if (showEleSubmitPanel()) {
			if (getUploadFullVerList().size() < 1) {
				result = false;
				countErrorFullVerSubmit++;
				message = "Please upload Full Version of Research Output.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:uploadedFullVerTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getUrl_full_ver()) == false) {
				selectedRaeOutputHeader.setUrl_full_ver(selectedRaeOutputHeader.getUrl_full_ver());
				if (isValidUrl(selectedRaeOutputHeader.getUrl_full_ver()) == false) {
					result = false;
					countErrorFullVerSubmit++;
					message = "Invalid format of URL to Open Access for Full Version of Research Output (must start with http:// or https://)";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:url_full_ver", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNon_trad_output_ind()) == false){
				if ("N".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
					//TOC
					if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getToc())) {
						result = false;
						countErrorFullVerSubmit++;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Presence of Abstract or Table of Content (TOC)");
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:formTab:toc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					//Upload Abstract or TOC
					if (showUploadTocAttPanel()) {
						if (getUploadTocAttList().size() < 1) {
							result = false;
							countErrorFullVerSubmit++;
							message = "Please upload Abstract or TOC.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:formTab:uploadedTocAttTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					//URL to Open Access for Abstract or TOC
					if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getUrl_toc()) == false) {
						selectedRaeOutputHeader.setUrl_toc(selectedRaeOutputHeader.getUrl_toc().trim());
						if (isValidUrl(selectedRaeOutputHeader.getUrl_toc()) == false) {
							result = false;
							countErrorFullVerSubmit++;
							message = "Invalid format of URL to Open Access for Abstract or TOC. (must start with http:// or https://)";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:formTab:url_toc", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
						
					}
				}
			}
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNon_trad_output_ind()) == false){
				if ("N".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
					//TOC Other
					if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getToc_oth())) {
						result = false;
						countErrorFullVerSubmit++;
						message = MessageFormat.format(getResourceBundle().getString(errMessage), "Presence of Abstract or Table of Content (TOC) in other widely-used language");
						allMessage += "- "+message+"<br/>";
						fCtx.addMessage("editForm:formTab:toc_oth", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
					}
					//Upload Abstract or TOC Other
					if (showUploadTocAttOthPanel()) {
						if (getUploadTocAttOthList().size() < 1) {
							result = false;
							countErrorFullVerSubmit++;
							message = "Please upload Abstract or TOC in other widely-used language.";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:formTab:uploadedTocAttOthTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
					}
					//URL to Open Access for Abstract or TOC Other
					if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getUrl_toc_oth()) == false) {
						selectedRaeOutputHeader.setUrl_toc(selectedRaeOutputHeader.getUrl_toc_oth().trim());
						if (isValidUrl(selectedRaeOutputHeader.getUrl_toc_oth()) == false) {
							result = false;
							countErrorFullVerSubmit++;
							message = "Invalid format of URL to Open Access for Abstract or TOC in other widely-used language. (must start with http:// or https://)";
							allMessage += "- "+message+"<br/>";
							fCtx.addMessage("editForm:formTab:url_toc_oth", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
						}
						
					}
				}
			}
		}
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNon_trad_output_ind()) == false){
			if ("N".equals(selectedRaeOutputHeader.getNon_trad_output_ind())) {
				//Additional Information
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getAdditional_info().trim())) {
					result = false;
					countErrorFullVerSubmit++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Additional Information to Abstract on New Insights or Innovation Presented in the Output");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:additional_info", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
				if (validateCountWords(selectedRaeOutputHeader.getAdditional_info(), 100) == false) {
					result = false;
					message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Additional Information to Abstract on New Insights or Innovation Presented in the Output", 100);
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:additional_info", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
		}
		//DOI indicator
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getRo_with_doi_ind())) {
			result = false;
			countErrorFullVerSubmit++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Digital Object Identifier (DOI) indicator");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:ro_with_doi_ind", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}
		//DOI
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getRo_with_doi_ind()) == false) {
			if ("Y".equals(selectedRaeOutputHeader.getRo_with_doi_ind())) {
				if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getDoi())) {
					result = false;
					countErrorFullVerSubmit++;
					message = MessageFormat.format(getResourceBundle().getString(errMessage), "Digital Object Identifier (DOI)");
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:doi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}else {
				selectedRaeOutputHeader.setDoi(null);
			}
				
			//DOI format
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getDoi()) == false) {
				selectedRaeOutputHeader.setDoi(selectedRaeOutputHeader.getDoi().trim());
				if ((StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getDoi(), "http://") 
					|| StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getDoi(), "https://") 
					|| StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getDoi(), "doi.org/") 
					|| StringUtils.containsIgnoreCase(selectedRaeOutputHeader.getDoi(), "www.")
					|| selectedRaeOutputHeader.getDoi().startsWith("10.")) == false) {
					result = false;
					countErrorFullVerSubmit++;
					message = "Please input DOI number only (start with 10.)";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:doi", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
		}
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getKeyword_output_1())) {
			result = false;
			countErrorOther++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Keyword 1");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:keyword_output_1", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedRaeOutputHeader.setKeyword_output_1(selectedRaeOutputHeader.getKeyword_output_1().trim());
		}
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getKeyword_output_2())) {
			result = false;
			countErrorOther++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Keyword 2");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:keyword_output_2", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedRaeOutputHeader.setKeyword_output_2(selectedRaeOutputHeader.getKeyword_output_2().trim());
		}
		if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getKeyword_output_3())) {
			result = false;
			countErrorOther++;
			message = MessageFormat.format(getResourceBundle().getString(errMessage), "Keyword 3");
			allMessage += "- "+message+"<br/>";
			fCtx.addMessage("editForm:formTab:keyword_output_3", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
		}else {
			selectedRaeOutputHeader.setKeyword_output_3(selectedRaeOutputHeader.getKeyword_output_3().trim());
		}
		if (showAttInfoScw()) {
			//Total No. of Part(s) of Single Coherent Work
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getNo_sgl_co_work())) {
				result = false;
				countErrorOther++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Total No. of Part(s) of Single Coherent Work");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:no_sgl_co_work", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			//Other Part(s) of the Single Coherent Work
			if (getUploadScwList().size() < 1) {
				result = false;
				countErrorOther++;
				message = "Please upload Other Part(s) of the Single Coherent Work.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:uploadedScwTable", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			//URL to Open Access for Other Part(s) of the Single Coherent Work
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getUrl_ref()) == false) {
				if (isValidUrl(selectedRaeOutputHeader.getUrl_ref()) == false) {
					result = false;
					countErrorOther++;
					message = "Invalid format of URL to Open Access for Other Part(s) of the Single Coherent Work. (must start with http:// or https://)";
					allMessage += "- "+message+"<br/>";
					fCtx.addMessage("editForm:formTab:url_ref", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				}
			}
			//University Endorsement Form
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getUni_endorse_conf_ind())) {
				result = false;
				countErrorOther++;
				message = "Please confirm if you agree with the statement outlined in the section of University Endorsement Form.";
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:uni_endorse_conf_ind", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			//Justification and Supporting Argument
			if (Strings.isNullOrEmpty(selectedRaeOutputHeader.getJust_sgl_co_work())) {
				result = false;
				countErrorOther++;
				message = MessageFormat.format(getResourceBundle().getString(errMessage), "Justification and Supporting Argument");
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:just_sgl_co_work", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
			if (validateCountWords(selectedRaeOutputHeader.getJust_sgl_co_work(), 600) == false) {
				result = false;
				message = MessageFormat.format(getResourceBundle().getString(errWordMessage), "Justification and Supporting Argument", 600);
				allMessage += "- "+message+"<br/>";
				fCtx.addMessage("editForm:formTab:just_sgl_co_work", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
			}
		}
		if (!result) {
			hasError = true;
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, allMessage, ""));
		}
		return result;
	}
		
	public Boolean getHasError()
	{
		if (hasError == null) {
			hasError = false;
		}
		return hasError;
	}

	public void setHasError(Boolean hasError)
	{
		this.hasError = hasError;
	}

	
	public Boolean getCanSave()
	{
		if (canSave == null) {
			canSave = true;
		}
		return canSave;
	}

	
	public void setCanSave(Boolean canSave)
	{
		this.canSave = canSave;
	}

	
	public Boolean getDoSave()
	{
		if (doSave == null) {
			doSave = false;
		}
		return doSave;
	}

	
	public void setDoSave(Boolean doSave)
	{
		this.doSave = doSave;
	}
	
	public Integer getRaeStep()
	{
		Integer result = 0;
		return 0;
	}
	
	public void scrollTo(String id) {
	      PrimeFaces.current().scrollTo(id);
	}
	
	public Integer countError(String id) {
		Integer count = 0;
		switch (id) {
		    case "ro_info":			count = countErrorRoInfo;
		    						break;
		    case "P10":				count = countErrorP10;
		    						break;
		    case "P11":				count = countErrorP11;
									break;
		    case "P12":				count = countErrorP12;
									break;
		    case "full_ver_submit":	count = countErrorFullVerSubmit;
									break;
		    case "other":			count = countErrorOther;
		    						break;
		    
		}
		return count;
	}


	
	public Integer getCountErrorRoInfo()
	{
		return countErrorRoInfo;
	}


	
	public void setCountErrorRoInfo(Integer countErrorRoInfo)
	{
		this.countErrorRoInfo = countErrorRoInfo;
	}


	
	public Integer getCountErrorP10()
	{
		return countErrorP10;
	}


	
	public void setCountErrorP10(Integer countErrorP10)
	{
		this.countErrorP10 = countErrorP10;
	}


	
	public Integer getCountErrorP11()
	{
		return countErrorP11;
	}


	
	public void setCountErrorP11(Integer countErrorP11)
	{
		this.countErrorP11 = countErrorP11;
	}


	
	public Integer getCountErrorP12()
	{
		return countErrorP12;
	}


	
	public void setCountErrorP12(Integer countErrorP12)
	{
		this.countErrorP12 = countErrorP12;
	}


	
	public Integer getCountErrorFullVerSubmit()
	{
		return countErrorFullVerSubmit;
	}


	
	public void setCountErrorFullVerSubmit(Integer countErrorFullVerSubmit)
	{
		this.countErrorFullVerSubmit = countErrorFullVerSubmit;
	}


	
	public Integer getCountErrorOther()
	{
		return countErrorOther;
	}


	
	public void setCountErrorOther(Integer countErrorOther)
	{
		this.countErrorOther = countErrorOther;
	}


	
	public List<String> getStaffUoaList()
	{
		if (staffUoaList == null) {
			staffUoaList = sDao.getRaeStaffUoaList();
		}
		return staffUoaList;
	}


	
	public void setStaffUoaList(List<String> staffUoaList)
	{
		this.staffUoaList = staffUoaList;
	}


	
	public String getUploadErrMsgSupDoc()
	{
		return uploadErrMsgSupDoc;
	}


	
	public void setUploadErrMsgSupDoc(String uploadErrMsgSupDoc)
	{
		this.uploadErrMsgSupDoc = uploadErrMsgSupDoc;
	}


	
	public String getUploadErrMsgP10()
	{
		return uploadErrMsgP10;
	}


	
	public void setUploadErrMsgP10(String uploadErrMsgP10)
	{
		this.uploadErrMsgP10 = uploadErrMsgP10;
	}


	
	public String getUploadErrMsgP12()
	{
		return uploadErrMsgP12;
	}


	
	public void setUploadErrMsgP12(String uploadErrMsgP12)
	{
		this.uploadErrMsgP12 = uploadErrMsgP12;
	}


	
	public String getUploadErrMsgFullVer()
	{
		return uploadErrMsgFullVer;
	}


	
	public void setUploadErrMsgFullVer(String uploadErrMsgFullVer)
	{
		this.uploadErrMsgFullVer = uploadErrMsgFullVer;
	}


	
	public String getUploadErrMsgTocAtt()
	{
		return uploadErrMsgTocAtt;
	}


	
	public void setUploadErrMsgTocAtt(String uploadErrMsgTocAtt)
	{
		this.uploadErrMsgTocAtt = uploadErrMsgTocAtt;
	}


	public String getUploadErrMsgTocAttOth()
	{
		return uploadErrMsgTocAttOth;
	}


	
	public void setUploadErrMsgTocAttOth(String uploadErrMsgTocAttOth)
	{
		this.uploadErrMsgTocAttOth = uploadErrMsgTocAttOth;
	}


	public String getUploadErrMsgScw()
	{
		return uploadErrMsgScw;
	}


	
	public void setUploadErrMsgScw(String uploadErrMsgScw)
	{
		this.uploadErrMsgScw = uploadErrMsgScw;
	}


	
	public String getUploadErrMsgAddInfo()
	{
		return uploadErrMsgAddInfo;
	}


	
	public void setUploadErrMsgAddInfo(String uploadErrMsgAddInfo)
	{
		this.uploadErrMsgAddInfo = uploadErrMsgAddInfo;
	}


	public boolean checkRaOfRo(String raOfRo) {
	    // Early return if raOfRo is null or selectedRaeStaff is null
	    if (raOfRo == null || getSelectedRaeStaff() == null) {
	        return false;
	    }

	    // Remove all non-numeric characters from raOfRo
	    String cleanedRaOfRo = raOfRo.replaceAll("[^0-9]", "");

	    // Compare the cleaned string with the UOA code
	    boolean result = cleanedRaOfRo.equals(selectedRaeStaff.getUoaCode());

	    // Debugging logs (replace with a proper logging framework in production)
	    //System.out.println("Cleaned raOfRo: " + cleanedRaOfRo);
	    //System.out.println("selectedRaeStaff.getUoaCode(): " + selectedRaeStaff.getUoaCode());

	    return result;
	}
	
	public int getCountTotalFilesByStaff(String staffNo) throws SQLException
	{
		if(staffNo != null) {
			List <RaeAttachment> totalFileListByStaff = getTotalFileListByStaff(staffNo);
			return totalFileListByStaff.size();
		}else {
			return 0;
		}
	}

	public int getCountTotalFilesByUoA(String uoa) throws SQLException {
	    if (uoa == null) {
	        return 0; // Return 0 if UoA is null
	    }

	    // Fetch the list of RaeStaff objects by UoA
	    List<RaeStaff> tmpRaeStaffList = sDao.getRaeStaffListByUOA(uoa);

	    // Check if the list is null or empty
	    if (tmpRaeStaffList == null || tmpRaeStaffList.isEmpty()) {
	        return 0; // Return 0 if no staff members are found for the given UoA
	    }

	    // Extract staff numbers from the list of RaeStaff objects
	    List<String> staffNumberList = new ArrayList<>();
	    for (RaeStaff raeStaff : tmpRaeStaffList) {
	        staffNumberList.add(raeStaff.getStaffNumber());
	    }

	    // Fetch the total file list for the staff numbers
	    List<RaeAttachment> totalFileListByStaff = getTotalFileListByStaffIds(staffNumberList);

	    // Return the size of the total file list
	    return totalFileListByStaff.size();
	}
	
	public List<RaeAttachment> getTotalFileListByStaff(String staffNo)
	{
		List <RaeAttachment> totalFileListByStaff = new ArrayList<RaeAttachment>();
		if (staffNo != null) {
			List<String> staffNumberList = new ArrayList<>();
			staffNumberList.add(staffNo);
			List<RaeOutputSelect> outputList = RaeOutputDAO.getInstance().getSubOutCountListbySID(staffNumberList,false);
			List<Integer> outputNoList = outputList.stream()
				                        .map(raeOutputSelect -> raeOutputSelect.getPk().getOutput_no())
				                        .collect(Collectors.toList());
			if (outputList.size() > 0) {
				totalFileListByStaff = attDao.getRaeAttachmentListByOutputs(outputNoList);
			}
		}
		return totalFileListByStaff;
	}
	
	public List<RaeAttachment> getTotalFileListByStaffIds(List<String> staffNumberList)
	{
		List <RaeAttachment> totalFileListByStaff = new ArrayList<RaeAttachment>();
		if (staffNumberList != null) {
			List<RaeOutputSelect> outputList = RaeOutputDAO.getInstance().getSubOutCountListbySID(staffNumberList,false);
			List<Integer> outputNoList = outputList.stream()
				                        .map(raeOutputSelect -> raeOutputSelect.getPk().getOutput_no())
				                        .collect(Collectors.toList());
			if (outputList.size() > 0) {
				totalFileListByStaff = attDao.getRaeAttachmentListByOutputs(outputNoList);
			}
		}
		return totalFileListByStaff;
	}
	
	public boolean validateCountWords(String text, int maxWords) {
		if (text == null) {
            return true;
        }
        // Trim leading and trailing spaces
        text = text.trim();

        // Regex to match words using Microsoft Word's method
        // Treats hyphenated words and words with apostrophes as single words
        // Ignores standalone punctuation marks
        String regex = "[\\w’'-]+(?:-[\\w’'-]+)*";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);

        // Count the total number of words
        int totalWords = 0;
        while (matcher.find()) {
            totalWords++;
        }

        // Display the word count and warnings
        if (totalWords > maxWords) {
            //System.out.println("Word limit exceeded! You have " + (totalWords - maxWords) + " word(s) over the limit.");
            return false;
        }
        return true;
    }
}
