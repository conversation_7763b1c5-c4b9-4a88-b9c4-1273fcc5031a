package hk.eduhk.rich.view;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.component.export.ExcelOptions;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.Department;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtActivity;
import hk.eduhk.rich.entity.staff.StaffRank;;


@SuppressWarnings("serial")
@ManagedBean(name = "manageKtActivitesView")
@ViewScoped
public class ManageKtActivitiesView extends BaseView
{

	private boolean firstSearch = false;
	private KTSearchPanel searchPanel;
	
	private List<KtActivity> ktActivitiesList;
	
	private ExcelOptions excelOpt;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public boolean isFirstSearch()
	{
		return firstSearch;
	}

	
	public void setFirstSearch(boolean firstSearch)
	{
		this.firstSearch = firstSearch;
		ktActivitiesList = null;
	}

	public KTSearchPanel getSearchPanel()
	{
		if(searchPanel == null) {
			searchPanel = new KTSearchPanel();
			searchPanel.setUserId(getLoginUserId());
		}
		return searchPanel;
	}
	
	public List<Integer> getQueryIdList() throws ParseException{
		return searchPanel.getQueryChain().queryIdList();
	}
	
	public List<KtActivity> getKtActivitiesList() throws ParseException {
		if(ktActivitiesList == null) {
			List<Integer> idList = getQueryIdList();
			FormDAO dao = FormDAO.getCacheInstance();
			try {
				ktActivitiesList = dao.getKtActivityListByIds(idList, searchPanel);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getKtActivityListByIds ", e);
			}
		}
		return ktActivitiesList;
	}
	
	public ExcelOptions getExcelOpt() {
		if(excelOpt == null) {
			excelOpt = new ExcelOptions();
		}
		if(ktActivitiesList != null)
			for(KtActivity act : ktActivitiesList) {
				if(act.getAuthorList() != null)
					act.setAuthorList(act.getAuthorList().replace("<br/>", "\n"));
			}
        return excelOpt;
    }
	
	public void postPrsc(Object document) throws ParseException {
		Integer numOfObj = 0;
		// handle line break
		if(ktActivitiesList != null) {
			for(KtActivity pub : ktActivitiesList) {
				if(pub.getAuthorList() != null)
					pub.setAuthorList(pub.getAuthorList().replace("\n", "<br/>"));
			}
			numOfObj = ktActivitiesList.size();
		}
		// search criteria tab page
		int rowCount = 0;
		Workbook wb = (XSSFWorkbook) document;
		Sheet sheet = wb.createSheet("Search criteria");
		Row row;
		Cell cell;
		
		row = sheet.createRow(rowCount);
		cell = row.createCell(0);
		cell.setCellValue("Total no. of Research Information items: ");
		cell = row.createCell(1);
		cell.setCellValue(numOfObj.toString());
		rowCount++;
		
		row = sheet.createRow(rowCount);
		cell = row.createCell(0);
		cell.setCellValue("Search Criterion");
		rowCount++;
		
		if(searchPanel != null) {
			if(searchPanel.getStaffName() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Staff Name: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getStaffName());
				rowCount++;
			}
			if(!CollectionUtils.isEmpty(searchPanel.getSelectedRankList())) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Academic Staff Rank: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(StaffRank rank : searchPanel.getRankList()) {
					if(searchPanel.getSelectedRankList().contains(rank.getRank_code()))
						rtnStr += rank.getRank_full() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(!searchPanel.getAcadStaff().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Academic Staff: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getAllYesNoEmptyList()) {
					if(searchPanel.getAcadStaff().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getExStaffName() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Former Staff Name: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getExStaffName());
				rowCount++;
			}
			if(searchPanel.getFormStaffNum() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Former Staff Number: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getFormStaffNum());
				rowCount++;
			}
			if(searchPanel.getKtType() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Type of KT: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getKtType());
				rowCount++;
			}
			if(searchPanel.getFormNo() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("KT Activity No.: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getFormNo());
				rowCount++;
			}
			if(!CollectionUtils.isEmpty(searchPanel.getSelectedFacDeptList())) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("KT Faculty(s) and Department(s): ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(Object obj : searchPanel.getFacDeptList()) {
					SelectItemGroup grp = (SelectItemGroup) obj;
					for(SelectItem type : grp.getSelectItems()) {
						if(searchPanel.getSelectedFacDeptList().contains(type.getValue()))
							rtnStr += type.getLabel() + "; ";
					}
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getViewType() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Type of View: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getViewTypeList()) {
					if(searchPanel.getViewType().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			if(searchPanel.getKtDateFrom() != null || searchPanel.getKtDateTo() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Kt Date Period: ");
				cell = row.createCell(1);
				String from = "";
				if(searchPanel.getKtDateFrom() != null) 
					from = dateFormat.format(searchPanel.getKtDateFrom());
				cell.setCellValue("From " + from);
				cell = row.createCell(2);
				String to = "";
				if(searchPanel.getKtDateTo() != null) 
					to = dateFormat.format(searchPanel.getKtDateTo());
				cell.setCellValue("To " + to);
				rowCount++;
			}
			if(searchPanel.getSortCol() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Sort by Column: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getSortColList()) {
					if(searchPanel.getSortCol().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getSortOrder() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Sort Order: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getSortOrderList()) {
					if(searchPanel.getSortOrder().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
		}
		
	}

    public void setExcelOpt(ExcelOptions excelOpt) {
        this.excelOpt = excelOpt;
    }

	
	
}
