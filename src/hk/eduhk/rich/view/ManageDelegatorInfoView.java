package hk.eduhk.rich.view;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.rich.access.UserSessionView;
import hk.eduhk.rich.cv.CvDAO;
import hk.eduhk.rich.entity.rae.RaeStaffDAO;
import hk.eduhk.rich.entity.staff.Assistant;
import hk.eduhk.rich.entity.staff.AssistantDAO;
import hk.eduhk.rich.entity.staff.InternetUserInfo;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffInfo;


@SuppressWarnings("serial")
@ManagedBean(name = "manageDelegatorView")
@ViewScoped
public class ManageDelegatorInfoView implements Serializable
{
	public static final String EMPTY_VALUE = "empty";
	
	public Map<String, StaffInfo> infoMap;
	public Map<String, String> iUserInfoStringMap;
	public Map<String, Boolean> raeStaffMap;
	
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	
	
	public List<StaffIdentity> getStaffList() {
		AssistantDAO asstDao = AssistantDAO.getInstance();
		RaeStaffDAO raeDao = RaeStaffDAO.getInstance();
		List<Assistant> acadStaffList = asstDao.getAcadStaffListByAsstId(UserSessionView.getCurrentInstance().getUserId(), "APPROVED");
		List<Integer> idList = null;
		if(!CollectionUtils.isEmpty(acadStaffList)) {
			idList = acadStaffList.stream().map(a -> a.getPk().getPid()).collect(Collectors.toList());
		}
		List<StaffIdentity> staffList = new ArrayList<StaffIdentity>();
		if(idList != null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			CvDAO cvdao = CvDAO.getCacheInstance();
			try {
				staffList = dao.getAcadStaffMapByPidList(idList);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getAcadStaffMapByPidList ", e);
			}
			List<String> staffNumList = new ArrayList<String>();
			for(StaffIdentity staff : staffList) {
				staffNumList.add(staff.getStaff_number());
			}
			infoMap = dao.getStaffInfoMapByIdList(staffNumList);
			iUserInfoStringMap = new HashMap<String, String>();
			raeStaffMap = new HashMap<String, Boolean>();
			for (String id : staffNumList) {
				List<InternetUserInfo> intInfoList = cvdao.getInternetUserInfoByStaffNo(id);
				String value = "";
				if (intInfoList != null) {
					for (InternetUserInfo i:intInfoList) {
						value += i.getPost() + ", <span style='color:#ff5722'>" +i.getDeptdesc() + "</span><br/>";
					}
				}
				iUserInfoStringMap.put(id, value);
				boolean isRae = false;
				if (raeDao.getRaeStaffByStaffNo(id) != null) {
					isRae = true;
				}
				raeStaffMap.put(id, isRae);
			}
		}
		return staffList;
	}
	
	public Map<String, StaffInfo> getInfoMap()
	{
		return infoMap;
	}


	
	public Map<String, String> getiUserInfoStringMap()
	{
		return iUserInfoStringMap;
	}

	
	public Map<String, Boolean> getRaeStaffMap()
	{
		return raeStaffMap;
	}
	
	
	
	
	
}
