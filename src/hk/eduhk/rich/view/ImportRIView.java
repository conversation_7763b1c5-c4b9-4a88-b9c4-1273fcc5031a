package hk.eduhk.rich.view;

import java.util.logging.Logger;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.importRI.*;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;


@SuppressWarnings("serial")
@ManagedBean(name = "importRIView")
@ViewScoped
public class ImportRIView extends BaseView
{	
	private ImportRIOutput outputPanel;
	private ImportRIProject projectPanel;
	private ImportRIAward awardPanel;
	private ImportRIPatent patentPanel;
	private String paramPid;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public ImportRIOutput getOutputPanel()
	{
		if(outputPanel == null) {
			outputPanel = new ImportRIOutput();
			outputPanel.setParamPid(getParamPid());
		}
		return outputPanel;
	}

	public void ignoreOutput() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedOutput = dao.getStatusByPK(getOutputPanel().getSelectedIgnoreOutput());
		if(selectedOutput != null) {
			selectedOutput.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedOutput);
			getOutputPanel().setOutputList(null);
		}
	}
	
	public ImportRIProject getProjectPanel()
	{
		if(projectPanel == null) {
			projectPanel = new ImportRIProject();
			projectPanel.setParamPid(getParamPid());
		}
		return projectPanel;
	}
	
	public void ignoreProject() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedProject = dao.getStatusByPK(getProjectPanel().getSelectedIgnoreProject());
		if(selectedProject != null) {
			selectedProject.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedProject);
			getProjectPanel().setProjectList(null);
		}
	}
	
	public ImportRIAward getAwardPanel()
	{
		if(awardPanel == null) {
			awardPanel = new ImportRIAward();
			awardPanel.setParamPid(getParamPid());
		}
		return awardPanel;
	}
	
	public void ignoreAward() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedAward = dao.getStatusByPK(getAwardPanel().getSelectedIgnoreAward());
		if(selectedAward != null) {
			selectedAward.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedAward);
			getAwardPanel().setAwardList(null);
		}
	}
	
	public ImportRIPatent getPatentPanel()
	{
		if(patentPanel == null) {
			patentPanel = new ImportRIPatent();
			patentPanel.setParamPid(getParamPid());
		}
		return patentPanel;
	}
	
	public void ignorePatent() {
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		ImportRIStatus selectedPatent = dao.getStatusByPK(getPatentPanel().getSelectedIgnorePatent());
		if(selectedPatent != null) {
			selectedPatent.setImport_status(ImportRIStatus.statusIgnore);
			dao.updateStatus(selectedPatent);
			getPatentPanel().setPatentList(null);
		}
	}
	
	public String getParamPid()
	{
		if (paramPid == null) {
			StaffDAO dao = StaffDAO.getInstance();
			StaffIdentity sIdentity = dao.getStaffDetailsByUserId(getCurrentUserId());
			this.paramPid = (sIdentity != null)?String.valueOf(sIdentity.getPid()):"";
		}
		return paramPid;
	}


	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}
		
	
}
