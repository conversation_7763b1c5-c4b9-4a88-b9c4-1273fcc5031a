package hk.eduhk.rich.view;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.omnifaces.util.Faces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;
import org.primefaces.shaded.commons.io.FilenameUtils;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.ReportFile;
import hk.eduhk.rich.entity.ReportFileDAO;
import hk.eduhk.rich.param.SysParamDAO;
import hk.eduhk.rich.util.MimeMap;

@SuppressWarnings("serial")
@ManagedBean(name = "manageReportFilesView")
@ViewScoped
public class ManageReportFilesView extends BaseView{
	private long maxFileSize = 0;
	private List<ReportFile> reportFileList = null;
	private List<ReportFile> filteredList;
	
	private String uploadedFileCat;
	private String uploadedFileDesc;
	private UploadedFile uploadedFile;
	
	private ReportFileDAO fDao = ReportFileDAO.getInstance();
	private SysParamDAO sDao = SysParamDAO.getInstance();
	
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	public ManageReportFilesView() {}
	
	public long getMaxFileSize() {
		if(maxFileSize <= 0) {
			maxFileSize = sDao.getSysParamIntByCode("UPLOAD_MAX_SIZE");
			maxFileSize = maxFileSize * 1024 * 1024;
		}
		
		return maxFileSize;
	}
	
	public void setMaxFileSize(long maxFileSize) {
		this.maxFileSize = maxFileSize;
	}
	
	public List<ReportFile> getReportFileList() {
		if(reportFileList == null) {
			reportFileList = fDao.getReportFileList();
		}
		
		return reportFileList;
	}
	
	public void setReportFileList(List<ReportFile> fileList) {
		this.reportFileList = fileList;
	}
		
	public List<ReportFile> getFilteredList(){
		return filteredList;
	}

	public void setFilteredList(List<ReportFile> filteredList){
		this.filteredList = filteredList;
	}

	public String getUploadedFileCat(){
		return uploadedFileCat;
	}

	public void setUploadedFileCat(String uploadedFileCat){
		this.uploadedFileCat = uploadedFileCat;
	}
	
	public String getUploadedFileDesc(){
		return uploadedFileDesc;
	}

	public void setUploadedFileDesc(String uploadedFileDesc){
		this.uploadedFileDesc = uploadedFileDesc;
	}

	public UploadedFile getUploadedFile(){
		return uploadedFile;
	}
	
	public void setUploadedFile(UploadedFile uploadedFile){	
		this.uploadedFile = uploadedFile;
	}
	
	public void validateFile() throws Exception {
		String fileDescTrim = uploadedFileDesc.trim();
		String fileCatTrim = uploadedFileCat.trim();
		
		if(fileDescTrim.split(" ").length > 200) {
			showFacesMessage(FacesMessage.SEVERITY_ERROR, "The number of words of description cannot more than 200.", "");
		}
		
		if(fileCatTrim.split(" ").length > 30) {
			showFacesMessage(FacesMessage.SEVERITY_ERROR, "The number of words of file category cannot more than 30.", "");
		}
			
		if(uploadedFile != null) {
			if (uploadedFile.getSize() > 0) {
				if(maxFileSize <= 0) {
					maxFileSize = getMaxFileSize();
				}
				
				if(uploadedFile.getSize() > maxFileSize) {
					uploadedFile = null;
					showFacesMessage(FacesMessage.SEVERITY_ERROR, "File size exceeds limit.", "");
				}
			}
			else {
				showFacesMessage(FacesMessage.SEVERITY_ERROR, "No file found.", "");
			}
		}else {
			showFacesMessage(FacesMessage.SEVERITY_ERROR, "No file found.", "");
		}
		
		if(FacesContext.getCurrentInstance().getMessageList().size() > 0) {
			return;
		}
		
		uploadFile();
			
		uploadedFileDesc = "";
		uploadedFileCat = "";
	}
	
	public void uploadFile() throws Exception {
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String filePath = sDao.getSysParamValueByCode("FILE_PATH");
		String fileName = uploadedFile.getFileName();
		String originalName = FilenameUtils.getBaseName(fileName);
		String fileType = FilenameUtils.getExtension(fileName);

		int fileSizeInKB = (int) uploadedFile.getSize() / 1024;
		
		byte[] bytes = uploadedFile.getContent();
		BufferedOutputStream bos = null;
		ReportFile reportFile = null;
		
		try {				
			reportFile = new ReportFile();
			reportFile.setOriginalName(originalName);
			reportFile.setFileType(fileType);
			reportFile.setFileCat(uploadedFileCat);
			reportFile.setFileDesc(uploadedFileDesc);
			reportFile.setFileSize(fileSizeInKB);
			reportFile.setCreator(fCtx.getExternalContext().getRemoteUser());
			reportFile.setUserstamp(fCtx.getExternalContext().getRemoteUser());		
			
			reportFile = fDao.updateReportFile(reportFile);
			
			String newFileName = Integer.toString(reportFile.getFileId()) + "." + fileType;
			
			reportFile.setFileName(newFileName);
			File file = new File(filePath + File.separator + newFileName);
			bos = new BufferedOutputStream(new FileOutputStream(file));
			bos.write(bytes);
			fDao.updateReportFile(reportFile);
			
			reportFileList = fDao.getReportFileList();
			showFacesMessage(FacesMessage.SEVERITY_INFO, "Uploaded the file.", "");
		}catch (Exception e) {

		}finally {
			bos.close();
		}
			
	}
	
	public void downloadSelectedFile(ReportFile selectedFile) throws Exception{
		if(selectedFile == null) {
			showFacesMessage(FacesMessage.SEVERITY_ERROR, "Cannot find the file.", "");
			return;
		}
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
	    ExternalContext eCtx = fCtx.getExternalContext();
	    String originalName = selectedFile.getOriginalName() + "." + selectedFile.getFileType();
	        
	    eCtx.responseReset(); 
	    eCtx.setResponseContentType(MimeMap.getInstance().get(selectedFile.getFileType()));
	    eCtx.setResponseHeader("Content-Disposition", "inline; filename=\"" + originalName + "\"");
	    
	    eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
	    eCtx.setResponseHeader("Expires", "-1");
	    eCtx.setResponseHeader("Pragma", "private");
	    
	    InputStream is = null;
	    
	    try{
	    	File file = new File(sDao.getSysParamValueByCode("FILE_PATH") + File.separator + selectedFile.getFileName());
	    	is = new BufferedInputStream(new FileInputStream(file));
		    Faces.sendFile(is, originalName, true);
	    }catch (Exception e){
	    	
	    }finally {
	    	is.close();
	    }
	    
	    fCtx.responseComplete();
	}

	public void deleteSelectedFile(ReportFile selectedFile) throws Exception {
		if(selectedFile == null) {
			showFacesMessage(FacesMessage.SEVERITY_ERROR, "Cannot find the file.", "");
			return;
		}
		
		String filePath = sDao.getSysParamDecryptedValueByCode("FILE_PATH");
		
		try {
			File file = new File(filePath + File.separator + selectedFile.getFileName());
			if(file.delete()) {
				fDao.deleteReportFile(selectedFile);
				
				FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);;
				showFacesMessage(FacesMessage.SEVERITY_INFO, "Deleted the file.", "");
				FacesContext.getCurrentInstance().getExternalContext().redirect("manageReportFiles.xhtml");
			}else {
				showFacesMessage(FacesMessage.SEVERITY_INFO, "Failed to delete the file.", "");
			}
		}catch(Exception e) {
			
		}

	}
	
	public void showFacesMessage(Severity severity, String summary, String content) {
		FacesMessage message = new FacesMessage(severity, summary, content);
		FacesContext.getCurrentInstance().addMessage(null, message);
	}
		
}
