package hk.eduhk.rich.view;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.component.export.ExcelOptions;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.entity.Department;
import hk.eduhk.rich.entity.award.Award;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.patent.Patent;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectSummary;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.rae.RaeOutput;
import hk.eduhk.rich.entity.rae.RaeOutputDAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffRank;


@SuppressWarnings("serial")
@ManagedBean(name = "riListingView")
@ViewScoped
public class ResearchInformationListingView extends BaseView
{

	private boolean firstSearch = false;
	private RISearchPanel searchPanel;
	
	private List<Publication> outputList;
	private List<ProjectSummary> projectList;
	private List<Award> awardList;
	private List<Patent> patentList;
	private List<RaeOutput> raeOutputList;
	

	private ExcelOptions excelOpt;
	
	private boolean isRdoLib;
    private boolean isDeptAdmin;
    private boolean isUoaAdmin;
    private boolean isAcadStaff;
	
	private String paramRiType;
	private Integer userPID ;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public Integer getUserPID()
	{
		if (userPID == null)
			userPID = StaffDAO.getInstance().getStaffDetailsByUserId(getCurrentUserId()).getPid() ;
		return userPID;
	}
	
	
	public boolean isFirstSearch()
	{
		return firstSearch;
	}

	
	public void setFirstSearch(boolean firstSearch)
	{
		this.firstSearch = firstSearch;
		outputList = null;
		projectList = null;
		awardList = null;
		patentList = null;
		raeOutputList = null;
	}
	
	public RISearchPanel getSearchPanel()
	{
		if(searchPanel == null) {
			searchPanel = new RISearchPanel();
			searchPanel.setUserId(getLoginUserId());
		}
		return searchPanel;
	}
	
	public List<Integer> getQueryIdList() throws ParseException{
		return searchPanel.getQueryChain().queryIdList();
	}
	
	public List<Publication> getOutputList() throws ParseException {
		if(outputList == null) {
			List<Integer> idList = getQueryIdList();
			PublicationDAO dao = PublicationDAO.getCacheInstance();
			try {
				outputList = dao.getPublicationListByIds(idList, searchPanel);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getPublicationListByIds ", e);
			}
		}
		return outputList;
	}
	
	public List<ProjectSummary> getProjectList() throws ParseException {
		if(projectList == null) {
			List<Integer> idList = getQueryIdList();
			ProjectDAO dao = ProjectDAO.getCacheInstance();
			try {

				projectList = dao.getProjectListByIds(idList, searchPanel);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getProjectListByIds ", e);
			}
		}
		return projectList;
	}
	
	public List<Award> getAwardList() throws ParseException {
		if(awardList == null) {
			List<Integer> idList = getQueryIdList();
			AwardDAO dao = AwardDAO.getCacheInstance();
			try {
				awardList = dao.getAwardListByIds(idList, searchPanel);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getAwardListByIds ", e);
			}
		}
		return awardList;
	}
	
	public List<Patent> getPatentList() throws ParseException {
		if(patentList == null) {
			List<Integer> idList = getQueryIdList();
			PatentDAO dao = PatentDAO.getCacheInstance();
			try {
				patentList = dao.getPatentListByIds(idList, searchPanel);
			}
			catch(Exception e){
				logger.log(Level.WARNING, "Cannot getPatentListByIds ", e);
			}
		}
		return patentList;
	}
	
	
	public List<RaeOutput> getRaeOutputList() throws ParseException {
	    if (raeOutputList == null) {
	        List<Integer> idList = getQueryIdList();
	        RaeOutputDAO dao = RaeOutputDAO.getCacheInstance();
	        raeOutputList = new ArrayList<>();

	        // Split the idList into chunks of 1000 elements each
	        int chunkSize = 1000;
	        for (int i = 0; i < idList.size(); i += chunkSize) {
	            List<Integer> chunk = idList.subList(i, Math.min(idList.size(), i + chunkSize));
	            try {
	                List<RaeOutput> chunkResults = dao.getRAEsearchListByIds(chunk, searchPanel);
	                raeOutputList.addAll(chunkResults);
	            } catch (Exception e) {
	                logger.log(Level.WARNING, "Cannot getRaeOutputList for chunk starting at index " + i, e);
	            }
	        }
	    }
	    return raeOutputList;
	}
	
	
	public ExcelOptions getExcelOpt() {
		if(excelOpt == null) {
			excelOpt = new ExcelOptions();
		}
		if(outputList != null)
			for(Publication pub : outputList) {
				if(pub.getAuthorListWithDeptAndAuthType() != null)
					pub.setAuthorListWithDeptAndAuthType(pub.getAuthorListWithDeptAndAuthType().replace("<br/>", "\n"));
				if(pub.getIsbn() != null)
					pub.setIsbn(pub.getIsbn()+"\0");
			}
		else if(projectList != null)
			for(ProjectSummary proj : projectList) {
				if(proj.getPrin_inves_author_list() != null)
					proj.setPrin_inves_author_list(proj.getPrin_inves_author_list().replace("<br/>", "\n"));
				if(proj.getCo_prin_inves_author_list() != null)
					proj.setCo_prin_inves_author_list(proj.getCo_prin_inves_author_list().replace("<br/>", "\n"));
				if(proj.getCo_inves_author_list() != null)
					proj.setCo_inves_author_list(proj.getCo_inves_author_list().replace("<br/>", "\n"));
				if(proj.getOther_author_list() != null)
					proj.setOther_author_list(proj.getOther_author_list().replace("<br/>", "\n"));
				if(proj.getCollaborativePartnerStr() != null)
					proj.setCollaborativePartnerStr(proj.getCollaborativePartnerStr().replace("<br/>", "\n"));
				if(proj.getCollaborativeOutputStr() != null)
					proj.setCollaborativeOutputStr(proj.getCollaborativeOutputStr().replace("<br/>", "\n"));
			}
		else if(awardList != null)
			for(Award award : awardList) {
				if(award.getRecipient_list() != null)
					award.setRecipient_list(award.getRecipient_list().replace("<br/>", "\n"));
			}
		else if(patentList != null)
			for(Patent pant : patentList) {
				if(pant.getInventor_list() != null)
					pant.setInventor_list(pant.getInventor_list().replace("<br/>", "\n"));
			}
		
		else if(raeOutputList != null)
			for(RaeOutput output : raeOutputList) {
				if(output.getConcatenated_author_name() != null)
					output.setConcatenated_author_name(output.getConcatenated_author_name().replace("<br/>", "\n"));
			}
        return excelOpt;
    }
	
	public void postPrsc(Object document) throws ParseException {
		Integer numOfObj = 0;
		// handle line break
		if(outputList != null) {
			for(Publication pub : outputList) {
				if(pub.getAuthorListWithDeptAndAuthType() != null)
					pub.setAuthorListWithDeptAndAuthType(pub.getAuthorListWithDeptAndAuthType().replace("\n", "<br/>"));
			}
			numOfObj = outputList.size();
		}
		else if(projectList != null) {
			for(ProjectSummary proj : projectList) {
				if(proj.getPrin_inves_author_list() != null)
					proj.setPrin_inves_author_list(proj.getPrin_inves_author_list().replace("\n", "<br/>"));
				if(proj.getCo_prin_inves_author_list() != null)
					proj.setCo_prin_inves_author_list(proj.getCo_prin_inves_author_list().replace("\n", "<br/>"));
				if(proj.getCo_inves_author_list() != null)
					proj.setCo_inves_author_list(proj.getCo_inves_author_list().replace("\n", "<br/>"));
				if(proj.getOther_author_list() != null)
					proj.setOther_author_list(proj.getOther_author_list().replace("\n", "<br/>"));
				if(proj.getCollaborativePartnerStr() != null)
					proj.setCollaborativePartnerStr(proj.getCollaborativePartnerStr().replace("\n", "<br/>"));
				if(proj.getCollaborativeOutputStr() != null)
					proj.setCollaborativeOutputStr(proj.getCollaborativeOutputStr().replace("\n", "<br/>"));
			}
			numOfObj = projectList.size();
		}
		else if(awardList != null) {
			for(Award award : awardList) {
				if(award.getRecipient_list() != null)
					award.setRecipient_list(award.getRecipient_list().replace("\n", "<br/>"));
			}
			numOfObj = awardList.size();
		}
		else if(patentList != null) {
			for(Patent pant : patentList) {
				if(pant.getInventor_list() != null)
					pant.setInventor_list(pant.getInventor_list().replace("\n", "<br/>"));
			}
			numOfObj = patentList.size();
		}
		
		else if(raeOutputList != null) {
			for(RaeOutput output : raeOutputList) {
				if(output.getConcatenated_author_name() != null)
					output.setConcatenated_author_name(output.getConcatenated_author_name().replace("<br/>", "\n"));
			}
			numOfObj = raeOutputList.size();
		}
		
		// search criteria tab page
		int rowCount = 0;
		Workbook wb = (XSSFWorkbook) document;
		Sheet sheet = wb.createSheet("Search criteria");
		Row row;
		Cell cell;
		
		row = sheet.createRow(rowCount);
		cell = row.createCell(0);
		cell.setCellValue("Total no. of Research Information items: ");
		cell = row.createCell(1);
		cell.setCellValue(numOfObj.toString());
		rowCount++;
		
		row = sheet.createRow(rowCount);
		cell = row.createCell(0);
		cell.setCellValue("Search Criterion");
		rowCount++;

		if(searchPanel != null) {
			if(searchPanel.getStaffName() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Staff Name: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getStaffName());
				rowCount++;
			}
			if(!CollectionUtils.isEmpty(searchPanel.getSelectedRankList())) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Academic Staff Rank: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(StaffRank rank : searchPanel.getRankList()) {
					if(searchPanel.getSelectedRankList().contains(rank.getRank_code()))
						rtnStr += rank.getRank_full() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(!searchPanel.getAcadStaff().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Academic Staff: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getAllYesNoEmptyList()) {
					if(searchPanel.getAcadStaff().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(!CollectionUtils.isEmpty(searchPanel.getSelectedDepartmentList())) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Department(s): ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(Department dept : searchPanel.getDepartmentList()) {
					if(searchPanel.getSelectedDepartmentList().contains(dept.getDepartmentCode()))
						rtnStr += dept.getDepartmentName() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getExStaffName() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Former Staff Name: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getExStaffName());
				rowCount++;
			}
			if(searchPanel.getFormStaffNum() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Former Staff Number: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getFormStaffNum());
				rowCount++;
			}
			
			if(searchPanel.getRiType() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Type of RI: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getRiType());
				rowCount++;
			}
			if(searchPanel.getRiNo() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("RI No.: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getRiNo());
				rowCount++;
			}
			if(!CollectionUtils.isEmpty(searchPanel.getSelectedOutputTypeList()) && searchPanel.getRiType().equals(searchPanel.RI_TYPE_OUTPUT)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Output Type: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(Object obj : searchPanel.getOutputTypeList()) {
					SelectItemGroup grp = (SelectItemGroup) obj;
					for(SelectItem type : grp.getSelectItems()) {
						if(searchPanel.getSelectedOutputTypeList().contains(type.getValue()))
							rtnStr += type.getLabel() + "; ";
					}
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(searchPanel.getViewType() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Type of View: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getViewTypeList()) {
					if(searchPanel.getViewType().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getJournalName() != null && searchPanel.getRiType().equals(searchPanel.RI_TYPE_OUTPUT)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Journal Name: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getJournalName());
				rowCount++;
			}
			if(searchPanel.getKeyword() != null && searchPanel.getRiType().equals(searchPanel.RI_TYPE_PROJECT)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Keyword: ");
				cell = row.createCell(1);
				cell.setCellValue(searchPanel.getJournalName());
				rowCount++;
			}
			if(!searchPanel.getCdcfStatus().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("CDCF Status: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getCdcfStatusList()) {
					if(searchPanel.getCdcfStatus().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(!searchPanel.getInsDecIndicator().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Institute Declared Indicator: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getAllYesNoEmptyList()) {
					if(searchPanel.getInsDecIndicator().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			
			
			
			
			if(!searchPanel.getConsentIndicator().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Consent Indicator: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getConsentIndicatorList()) {
					if(searchPanel.getConsentIndicator().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(!searchPanel.getChgAfterProcess().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Change after CDCF Processed: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getAllYesNoEmptyList()) {
					if(searchPanel.getChgAfterProcess().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			if(searchPanel.getRiDateFrom() != null || searchPanel.getRiDateTo() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("RI Date Period: ");
				cell = row.createCell(1);
				String from = "";
				if(searchPanel.getRiDateFrom() != null) 
					from = dateFormat.format(searchPanel.getRiDateFrom());
				cell.setCellValue("From " + from);
				cell = row.createCell(2);
				String to = "";
				if(searchPanel.getRiDateTo() != null) 
					to = dateFormat.format(searchPanel.getRiDateTo());
				cell.setCellValue("To " + to);
				rowCount++;
			}
			if(searchPanel.getRiFirstPublishFrom() != null || searchPanel.getRiFirstPublishTo() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("RI First Submit Date: ");
				cell = row.createCell(1);
				String from = "";
				if(searchPanel.getRiFirstPublishFrom() != null) 
					from = dateFormat.format(searchPanel.getRiFirstPublishFrom());
				cell.setCellValue("From " + from);
				cell = row.createCell(2);
				String to = "";
				if(searchPanel.getRiFirstPublishTo() != null) 
					to = dateFormat.format(searchPanel.getRiFirstPublishTo());
				cell.setCellValue("To " + to);
				rowCount++;
			}
			if((searchPanel.getRiLastPublishFrom() != null || searchPanel.getRiLastPublishTo() != null)
					&& searchPanel.getViewType().equals(searchPanel.RICH_EDITION_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("RI Last Submit Date: ");
				cell = row.createCell(1);
				String from = "";
				if(searchPanel.getRiLastPublishFrom() != null) 
					from = dateFormat.format(searchPanel.getRiLastPublishFrom());
				cell.setCellValue("From " + from);
				cell = row.createCell(2);
				String to = "";
				if(searchPanel.getRiLastPublishTo() != null) 
					to = dateFormat.format(searchPanel.getRiLastPublishTo());
				cell.setCellValue("To " + to);
				rowCount++;
			}
			if(!searchPanel.getEnhTLInHighEdu().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Enh. T&L in High Edu.: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getAllYesNoEmptyList()) {
					if(searchPanel.getEnhTLInHighEdu().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getListingType() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Listing Type: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getListingTypeList()) {
					if(searchPanel.getListingType().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			//RAE Output Search Critera
			if(!CollectionUtils.isEmpty(searchPanel.getSelectedUOAList()) && searchPanel.getRiType().equals(searchPanel.RI_TYPE_RAE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("RAE Unit of Assessment (UoA): ");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getUoaList()) {
					if(searchPanel.getSelectedUOAList().contains(obj.getValue()))
						rtnStr += obj.getLabel() + "; ";
				}

				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeFac().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Coordination Faculty:");
				cell = row.createCell(1);
				String rtnStr = searchPanel.getRaeFac();
				cell.setCellValue(rtnStr);
				rowCount++;
			}

			if(!searchPanel.getRaeOutputType().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Research Output Type (RAE):");
				cell = row.createCell(1);
				String rtnStr = "";

				for(SelectItem obj : searchPanel.getRaeOutputTypeList()) {
					if(searchPanel.getRaeOutputType().equals(obj.getValue()))
						rtnStr = obj.getLabel() ;
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeSelType().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("RAE Selection Type:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeSelTypeList()) {
					if(searchPanel.getRaeSelType().equals(obj.getValue()))
						rtnStr = obj.getLabel();
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeInfoCom().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Information Completed:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeInfoComList()) {
					if(searchPanel.getRaeInfoCom().equals(obj.getValue()))
						rtnStr = obj.getLabel() ;
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeStatus().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Status - Research Output Info:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeStatusList()) {
					if(searchPanel.getRaeStatus().equals(obj.getValue()))
						rtnStr = obj.getLabel() ;
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeStatusSpec().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Status - Info. for Specific Panels:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeStatusSpecList()) {
					if(searchPanel.getRaeStatusSpec().equals(obj.getValue()))
						rtnStr = obj.getLabel() ;
				}
				
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeStatusFull().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Status - Full Version:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeStatusFullList()) {
					if(searchPanel.getRaeStatusFull().equals(obj.getValue()))
						rtnStr = obj.getLabel() ;
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeStatusOther().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Status - Other Information:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeStatusOtherList()) {
					if(searchPanel.getRaeStatusOther().equals(obj.getValue()))
						rtnStr = obj.getLabel();
				}
				
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeStatusInel().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Status - Ineligible:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeStatusInetList()) {
					if(searchPanel.getRaeStatusInel().equals(obj.getValue()))
						rtnStr = obj.getLabel();
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeCitation().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Citation Checking:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeCitationList()) {
					if(searchPanel.getRaeCitation().equals(obj.getValue()))
						rtnStr = obj.getLabel();
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			
			if(!searchPanel.getRaeCopyright().equals(searchPanel.EMPTY_VALUE)) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Copyright Clearance:");
				cell = row.createCell(1);
				String rtnStr = "";
				
				for(SelectItem obj : searchPanel.getRaeCopyrightList()) {
					if(searchPanel.getRaeCopyright().equals(obj.getValue()))
						rtnStr = obj.getLabel();
				}
				
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			

			
			if(searchPanel.getSortCol() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Sort by Column: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getSortColList()) {
					if(searchPanel.getSortCol().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
			if(searchPanel.getSortOrder() != null) {
				row = sheet.createRow(rowCount);
				cell = row.createCell(0);
				cell.setCellValue("Sort Order: ");
				cell = row.createCell(1);
				String rtnStr = "";
				for(SelectItem type : searchPanel.getSortOrderList()) {
					if(searchPanel.getSortOrder().equals(type.getValue()))
						rtnStr += type.getLabel() + "; ";
				}
				rtnStr = rtnStr.substring(0, rtnStr.length()-2);
				cell.setCellValue(rtnStr);
				rowCount++;
			}
		}
		
	}

    public void setExcelOpt(ExcelOptions excelOpt) {
        this.excelOpt = excelOpt;
    }


	
	public boolean getIsRdoLib()
	{
		Set<String> roleSet = AccessDAO.getCacheInstance().getRoleIdSetByUserId(getLoginUserId());
		if(roleSet.contains("rdoAdmin") || roleSet.contains("libAdmin") || roleSet.contains("raeAdmin")) {
			return true;
		}			
		else {
			return false;
		}
	}
	
	public boolean getIsDeptAdmin()
	{
		Set<String> roleSet = AccessDAO.getCacheInstance().getRoleIdSetByUserId(getLoginUserId());
		if(roleSet.contains("deptAdmin"))
			return true;
		else 
			return false;
	}


	public boolean getIsAcadStaff()
	{
		if (getIsRdoLib() == false && getIsDeptAdmin() == false && getIsUoaAdmin() == false) {
			return true;
		}else {
			return false;
		}
	}

	
	public boolean getIsUoaAdmin()
	{
		Set<String> roleSet = AccessDAO.getCacheInstance().getRoleIdSetByUserId(getLoginUserId());
		if(roleSet.contains("uoaAdmin"))
			return true;
		else
			return false;
	}


	public String getParamRiType()
	{
		return paramRiType;
	}


	
	public void setParamRiType(String paramRiType)
	{
		this.paramRiType = paramRiType;
	}
    
	
}
