package hk.eduhk.rich.view;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.DoubleStream;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.BarChartDataSet;
import org.primefaces.model.charts.bar.BarChartModel;
import org.primefaces.model.charts.bar.BarChartOptions;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.primefaces.model.charts.pie.PieChartOptions;
import org.primefaces.model.charts.radar.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.event.CloseEvent;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.FacDept;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.award.AwardDetails_P;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;
import hk.eduhk.rich.entity.form.KtFormDetails_P;
import hk.eduhk.rich.entity.form.KtFormSummary;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentDetails_P;
import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.staff.SecDataUser;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name = "viewSumView")
@ViewScoped
@SuppressWarnings("serial")
public class ViewSumView extends ManageRIView
{
	private static Logger logger = Logger.getLogger(ViewSumView.class.getName());
	
	protected String paramSumType;
	protected String paramFormCode;
	protected String paramStartDate;
	protected String paramEndDate;
	protected String paramRiPeriod;
	protected String paramKtPeriod;
	protected String paramAdmin;
	protected String paramFacDept;
	protected String paramStaffName;
	
	private Boolean isTotal;
	private String selectedPeriod;
	protected String selectedFacDept;
	protected LookupValue selectedFacDeptLkup;

	protected Date selectedStartDate;
	protected Date selectedEndDate;
	protected String[] selectedFacDepts;
	
	protected String[] selectedCdcfPeriods;
	protected String[] selectedKtPeriods;
	
	protected List<CdcfRptPeriod> selectedCdcfPeriodList;
	protected List<KtRptPeriod> selectedKtPeriodList;
	
	protected List<CdcfRptPeriod> riChartPeriodList;
	protected List<KtRptPeriod> ktChartPeriodList;
	
	protected List<String> selectedFacs;
	protected List<String> selectedDepts;
	protected List<String> selectedOutputTypeList;
	protected List<String> selectedFundingSourceList;
	protected String[] selectedOutputTypes;	
	protected String[] selectedFundingSources;	
	
	private KtRptPeriod ktPeriod;
	
	protected List<CdcfRptPeriod> cdcfPeriodList = null;
	protected List<KtRptPeriod> ktPeriodList = null;
	
	protected List<KtForm> ktFormList = null;
	private List<KtFormSummary> ktFormSummaryList = null;
	
	private List<OutputType> outputTypeList;
	private List<FundSource> fundSourceList;
	
	protected List<Summary> outputCountList;
	protected List<Summary> outputWeightingList;
	protected List<Summary> projectCountList;
	protected List<Summary> projectCountNewList;
	protected List<Summary> awardCountList;
	protected List<Summary> patentCountList;
	protected List<Summary> ktCountList;
	
	protected Integer outputCount;
	protected Double outputWeighting;
	protected Integer projectCount;
	protected Integer awardCount;
	protected Integer patentCount;
	protected Integer ktCount;
	
	private PieChartModel summaryPieModel;
	private RadarChartModel summaryRadarModel;
	private PieChartModel summaryKtPieModel;
	
	protected List<SelectItem> facDeptList = null;
	
	private String[] riTypeArray = {"Research Outputs", "Research Outputs (By Weighting)", "Projects", "Prizes and Awards", "Patents"};
	private String[] colors = {"#619ED6", "#6BA547", "#F7D027", "#E48F18", "#B77EA3", "#E64345", "#60CEED", "#9CF168", "#F7EA4A", "#FBC543", "#FFC9ED", "#E6696E"};
	
	private PublicationDAO outputDao = PublicationDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private AwardDAO awardDao = AwardDAO.getInstance();
	private PatentDAO patentDao = PatentDAO.getInstance();
	private CdcfRptDAO cdcfRptDao = CdcfRptDAO.getInstance();
	private FormDAO fDao = FormDAO.getInstance();
	private KtRptDAO rDao = KtRptDAO.getInstance();
	private StaffDAO staffDao = StaffDAO.getInstance();
	private SysParamDAO sDao = SysParamDAO.getInstance();
	
	
	@PostConstruct
    public void init() throws ParseException, SQLException 
	{
		staffDetail = getStaffDetail(getParamPid(), null, true);
		this.paramStaffName = staffDetail.getFullname();
		//updateChart();
		//awardDao.getAwardReportList("C", paramStartDate, paramEndDate, selectedDepts);
    }
	
	public void updateChart(Boolean first) throws ParseException, SQLException
	{
		if (first == false) {
			paramRiPeriod = null;
		}

		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		
		outputCount = null;
		outputWeighting = null;
		projectCount = null;
		awardCount = null;
		patentCount = null;
		ktCount = null;
		
		outputCountList = null;
		outputWeightingList = null;
		projectCountList = null;
		projectCountNewList = null;
		awardCountList = null;
		patentCountList = null;
		ktCountList = null;
		
		createSummaryPieModel();
		createSummaryRadarModel();
		createSummaryKtPieModel();
	}
	
	private void createSummaryPieModel() throws ParseException, SQLException
	{
		summaryPieModel = new PieChartModel();
        ChartData data = new ChartData();

        PieChartDataSet dataSet = new PieChartDataSet();
        List<Number> values = new ArrayList<>();
        Integer output = getOutputCount();
        Integer project = getProjectCount();
        Integer award = getAwardCount();
        Integer patent = getPatentCount();
        
        values.add(output);
        values.add(project);
        values.add(award);
        values.add(patent);
        dataSet.setData(values);

        List<String> bgColors = new ArrayList<>();
        bgColors.add("#EA4335");
        bgColors.add("#FBBC05");
        bgColors.add("#34A853");
        bgColors.add("#4285F4");
        dataSet.setBackgroundColor(bgColors);

        data.addChartDataSet(dataSet);
        List<String> labels = new ArrayList<>();
        labels.add("Research Outputs");
        labels.add("Projects");
        labels.add("Prizes and Awards");
        labels.add("Patents");
        data.setLabels(labels);
        
        PieChartOptions options = new PieChartOptions();
        Title title = new Title();
        title.setDisplay(true);
        title.setText("Summary");
        options.setTitle(title);
        
        //projectTypePieModel.setOptions(options);
        summaryPieModel.setExtender("pieChartExtender");
        summaryPieModel.setData(data);
		
	}
	
	private void createSummaryRadarModel() throws ParseException, SQLException
	{
		summaryRadarModel = new RadarChartModel();
        ChartData data = new ChartData();

        RadarChartDataSet dataSet = new RadarChartDataSet();
        dataSet.setLabel("Number");
        dataSet.setFill(true);
        dataSet.setBackgroundColor("rgba(255, 99, 132, 0.2)");
        dataSet.setBorderColor("rgb(255, 99, 132)");
        dataSet.setPointBackgroundColor("rgb(255, 99, 132)");
        dataSet.setPointBorderColor("#fff");
        dataSet.setPointHoverBackgroundColor("#fff");
        dataSet.setPointHoverBorderColor("rgb(255, 99, 132)");
        
        List<Number> values = new ArrayList<>();
        Integer output = getOutputCount();
        Integer project = getProjectCount();
        Integer award = getAwardCount();
        Integer patent = getPatentCount();
        
        values.add(output);
        values.add(project);
        values.add(award);
        values.add(patent);
        dataSet.setData(values);

        data.addChartDataSet(dataSet);
        List<String> labels = new ArrayList<>();
        labels.add("Research Outputs");
        labels.add("Projects");
        labels.add("Prizes and Awards");
        labels.add("Patents");
        data.setLabels(labels);
        
        RadarChartOptions options = new RadarChartOptions();
        Title title = new Title();
        title.setDisplay(true);
        title.setText("Summary");
        options.setTitle(title);
        
        //summaryRadarModel.setOptions(options);
        summaryRadarModel.setExtender("pieChartExtender");
        summaryRadarModel.setData(data);
		
	}
	
	private void createSummaryKtPieModel() throws ParseException, NumberFormatException, SQLException
	{
		summaryKtPieModel = new PieChartModel();
        ChartData data = new ChartData();

        PieChartDataSet dataSet = new PieChartDataSet();
        List<Number> values = new ArrayList<>();
        List<String> labels = new ArrayList<>();
        List<String> bgColors = new ArrayList<>();

        if (getKtFormList() != null) {
        	int i = 0;
        	for (KtForm f:ktFormList) {
        		labels.add(f.getForm_full_desc());
        		Integer count = getKtCount(f.getForm_code());
        		values.add(count);
        		if (i < colors.length) {
        			bgColors.add(colors[i]);
        		}else{
        			bgColors.add("#666666");
        		}
        		i++;
        	}
        }

        dataSet.setData(values);
        dataSet.setBackgroundColor(bgColors);
        
        data.addChartDataSet(dataSet);
        data.setLabels(labels);
        
        PieChartOptions options = new PieChartOptions();
        Title title = new Title();
        title.setDisplay(true);
        title.setText("Summary of KT Activities");
        options.setTitle(title);
        
        //projectTypePieModel.setOptions(options);
        summaryKtPieModel.setExtender("pieChartExtender");
        summaryKtPieModel.setData(data);
		
	}

	
	public Double getOutputTotalCount(boolean byWeighting, Integer periodId) throws ParseException, SQLException 
	{
		Double count = 0.0;
		if (byWeighting) {
			if (getOutputWeightingList() != null) {
				if (periodId != null) {
					count = outputWeightingList.stream()
							.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getWeighting()))
							.sum();
				}else {
					count = outputWeightingList.stream()
							.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null)
							.mapToDouble(d -> Double.parseDouble(d.getWeighting()))
							.sum();
				}
				
			}
		}else {
			if (getOutputCountList() != null) {
				if (periodId != null) {
					count = outputCountList.stream()
							.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}else {
					count = outputCountList.stream()
							.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null)
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}
			}
		}
		
		Double truncatedDouble = new BigDecimal(count).setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
		count = truncatedDouble;
		return count;
    }
	
	public Double getProjectTotalCount(Integer periodId, Boolean newProject) throws ParseException, SQLException 
	{
		Double count = 0.0;
		if (newProject) {
			if (getProjectCountNewList() != null) {
				if (periodId != null) {
					count = projectCountNewList.stream()
							.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}else {
					count = projectCountNewList.stream()
							.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null)
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}
				
			}
		}else {
			if (getProjectCountList() != null) {
				if (periodId != null) {
					count = projectCountList.stream()
							.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}else {
					count = projectCountList.stream()
							.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null)
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}
			}
		}
		Double truncatedDouble = new BigDecimal(count).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		count = truncatedDouble;
		return count;
    }
	
	public Double getAwardTotalCount(Integer periodId) throws ParseException, SQLException 
	{	
		Double count = 0.0;

		if (getAwardCountList() != null) {
			if (periodId != null) {
				count = awardCountList.stream()
						.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
			}else {
				count = awardCountList.stream()
						.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null)
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
			}
		}
		Double truncatedDouble = new BigDecimal(count).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		count = truncatedDouble;
		return count;
    }
	
	public Double getPatentTotalCount(Integer periodId) throws ParseException, SQLException 
	{
		Double count = 0.0;

		if (getPatentCountList() != null) {
			if (periodId != null) {
				count = patentCountList.stream()
						.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
			}else {
				count = patentCountList.stream()
						.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null)
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
			}
			
		}
		Double truncatedDouble = new BigDecimal(count).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		count = truncatedDouble;
		return count;
    }
	
	public Double getKtTotalCount(Integer periodId, String formCode) throws ParseException, SQLException 
	{
		Double count = 0.0;

		if (getKtCountList() != null) {
			if (periodId != null) {
				if (!Strings.isNullOrEmpty(formCode)) {
					count = ktCountList.stream()
							.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id())
									&& formCode.equals(d.getForm_code()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}else {
					if (getKtFormList() != null) {
						for (KtForm f:ktFormList) {
							count += ktCountList.stream()
									.filter(d -> String.valueOf(periodId).equals(d.getPeriod_id())
											&& f.getForm_code().equals(d.getForm_code()))
									.mapToDouble(d -> Double.parseDouble(d.getCount()))
									.sum();
						}
					}
				}
			}else {
				if (!Strings.isNullOrEmpty(formCode)) {
					count = ktCountList.stream()
							.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null
									&& formCode.equals(d.getForm_code()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
				}else {
					if (getKtFormList() != null) {
						for (KtForm f:ktFormList) {
							count += ktCountList.stream()
									.filter(d -> !String.valueOf(1).equals(d.getPeriod_id()) && d.getPeriod_id() != null 
											&& f.getForm_code().equals(d.getForm_code()))
									.mapToDouble(d -> Double.parseDouble(d.getCount()))
									.sum();
						}
					}
				}
			}
		}
		Double truncatedDouble = new BigDecimal(count).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		count = truncatedDouble;
		return count;
    }
	
	public List<String> getSelectedFacs()
	{
		if (selectedFacs == null && "Y".equals(getParamAdmin())) {
			selectedFacs = new ArrayList<>();
			if (getSelectedFacDepts() != null) {
				if (selectedFacDepts.length > 0) {
					for (int i = 0; i < selectedFacDepts.length; i++) {
						FacDept tmp = staffDao.getFacDeptByCode(selectedFacDepts[i]);
						if (tmp != null) {
							if ("FAC".equals(tmp.getLookup_type())) {
								selectedFacs.add(selectedFacDepts[i]);
							}
						}
					}
				}
			}
			Set<String> selectedFacsSet = new HashSet<>(selectedFacs);
			selectedFacs.clear();
			selectedFacs.addAll(selectedFacsSet);
		}
		return selectedFacs;
	}

	
	public void setSelectedFacs(List<String> selectedFacs)
	{
		this.selectedFacs = selectedFacs;
	}
	
	public List<String> getSelectedDepts()
	{
		if (selectedDepts == null && "Y".equals(getParamAdmin())) {
			selectedDepts = new ArrayList<>();
			if (getSelectedFacDepts() != null) {
				if (selectedFacDepts.length > 0) {
					for (int i = 0; i < selectedFacDepts.length; i++) {
						FacDept tmp = staffDao.getFacDeptByCode(selectedFacDepts[i]);
						if (tmp != null) {
							if ("FAC".equals(tmp.getLookup_type())) {
								List<FacDept> tmpList = staffDao.getFacDeptByFac(selectedFacDepts[i]);
								/*if (tmpList != null) {
									for (FacDept d:tmpList) {
										selectedDepts.add(d.getFac_dept());
									}
								}*/
							}else {
								selectedDepts.add(selectedFacDepts[i]);
							}
						}
					}
				}
			}
			Set<String> selectedDeptsSet = new HashSet<>(selectedDepts);
			selectedDepts.clear();
			selectedDepts.addAll(selectedDeptsSet);
		}
		return selectedDepts;
		
	}
	
	
	public void setSelectedDepts(List<String> selectedDepts)
	{
		this.selectedDepts = selectedDepts;
	}


	public List<KtFormSummary> getKtFormSummaryList() throws ParseException
	{
		if (ktFormSummaryList == null) {
			List<KtRptPeriod> periodList = getKtChartPeriodList();
			
			Date tmpStartDate = new Date();
			Date tmpEndDate = new Date();
			
			if (periodList != null) {
				for (int i = 0; i < periodList.size(); i++) {
					if (tmpStartDate.after(periodList.get(i).getDate_from())) {
						tmpStartDate = periodList.get(i).getDate_from();
					}
					if (tmpEndDate.before(periodList.get(i).getDate_to())) {
						tmpEndDate = periodList.get(i).getDate_to();
					}
				}
			}
			
			if (getSelectedStartDate() != null) {
				if (selectedStartDate.before(tmpStartDate)) {
					tmpStartDate = selectedStartDate;
				}
			}
			
			if (getSelectedEndDate() != null) {
				if (selectedEndDate.after(tmpEndDate)) {
					tmpEndDate = selectedEndDate;
				}
			}
			ktFormSummaryList = new ArrayList<KtFormSummary>();
			if ("Y".equals(getParamAdmin())) {
				if (!Strings.isNullOrEmpty(getSelectedFacDept())) {
					ktFormSummaryList = fDao.getKtFormSummaryListByFacsAndDepts(getSelectedFacs(), getSelectedDepts(), getKtSumDataLevel(), null, tmpStartDate, tmpEndDate, 2);
				}
			}else {
				ktFormSummaryList = fDao.getKtFormSummaryListByStaff(staffDetail.getStaff_number(), getKtSumDataLevel(), tmpStartDate, tmpEndDate, 2, null);
			}
		}
		return ktFormSummaryList;
	}


	
	public void setKtFormSummaryList(List<KtFormSummary> ktFormSummaryList)
	{
		this.ktFormSummaryList = ktFormSummaryList;
	}	
	

	

	
	public String getParamSumType()
	{
		return paramSumType;
	}

	public void setParamSumType(String paramSumType)
	{
		this.paramSumType = paramSumType;
	}



	/*private void createOutputRBkRJrPieModel() 
	{
		outputRBkRJrPieModel = new PieChartModel();
        ChartData data = new ChartData();

        PieChartDataSet dataSet = new PieChartDataSet();
        List<Number> values = new ArrayList<>();
        values.add(getOutputRBkCount());
        values.add(getOutputRJrCount());
        dataSet.setData(values);
        
        List<String> bgColors = new ArrayList<>();
        bgColors.add("#fe8a7d");
        bgColors.add("#0ac282");
        //bgColors.add("#fe5d70");
        //bgColors.add("#01a9ac");
        dataSet.setBackgroundColor(bgColors);

        data.addChartDataSet(dataSet);
        List<String> labels = new ArrayList<>();
        labels.add("Refereed Book");
        labels.add("Refereed Journal");
        data.setLabels(labels);
        
        outputRBkRJrPieModel.setData(data);
    }*/




	public List<OutputType> getOutputTypeList()
	{
		if (outputTypeList == null) {
			outputTypeList = new ArrayList<OutputType>();
			List<OutputType> lvOneList = outputDao.getOutputTypeList(1);
			List<OutputType> lvTwoList = outputDao.getOutputTypeList(2);
			for (OutputType o:lvOneList) {
				outputTypeList.add(o);
				List<OutputType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				outputTypeList.addAll(tmpLvTwoList);
			}
		}
		return outputTypeList;
	}
	
	public void setOutputTypeList(List<OutputType> outputTypeList)
	{
		this.outputTypeList = outputTypeList;
	}
	
	
	
	public List<FundSource> getFundSourceList()
	{
		if (fundSourceList == null) {
			fundSourceList = projDao.getFundSourceList(1);
		}
		return fundSourceList;
	}

	
	public void setFundSourceList(List<FundSource> fundSourceList)
	{
		this.fundSourceList = fundSourceList;
	}
	
	public PieChartModel getSummaryPieModel()
	{
		return summaryPieModel;
	}

	
	public void setSummaryPieModel(PieChartModel summaryPieModel)
	{
		this.summaryPieModel = summaryPieModel;
	}



	
	public RadarChartModel getSummaryRadarModel()
	{
		return summaryRadarModel;
	}

	
	public void setSummaryRadarModel(RadarChartModel summaryRadarModel)
	{
		this.summaryRadarModel = summaryRadarModel;
	}

	public PieChartModel getSummaryKtPieModel()
	{
		return summaryKtPieModel;
	}



	
	public void setSummaryKtPieModel(PieChartModel summaryKtPieModel)
	{
		this.summaryKtPieModel = summaryKtPieModel;
	}





	public String getSumPath()
	{
		String result = "";
		if (getParamSumType() != null) {
			result = "../resources/component/summary/"+paramSumType+".xhtml";
		}
		return result;
	}


	
	public boolean checkDateInRange(Date startDate, Date endDate, Date rptStartDate, Date rptEndDate, Date selectedStartDate, Date selectedEndDate) 
	{
		boolean result = false;
		if (startDate != null) {
			if (endDate == null) {
				endDate = startDate;
			}
			if ((!startDate.before(rptStartDate) && !startDate.after(rptEndDate)) || 
					(!endDate.before(rptStartDate) && !endDate.after(rptEndDate)) || 
					(startDate.before(rptStartDate) && endDate.after(rptEndDate))) {
				if (selectedStartDate == null && selectedEndDate == null) {
					result = true;
				}
				if (selectedStartDate != null && selectedEndDate != null) {
					if ((!startDate.before(selectedStartDate) && !startDate.after(selectedEndDate)) || 
							(!endDate.before(selectedStartDate) && !endDate.after(selectedEndDate)) || 
							(startDate.before(selectedStartDate) && endDate.after(selectedEndDate))) {
						result = true;
					}
				}
				if (selectedStartDate != null && selectedEndDate == null) {
					if (!startDate.before(selectedStartDate) && !endDate.before(selectedStartDate)) {
						result = true;
					}
				}
				if (selectedStartDate == null && selectedEndDate != null) {
					if (!startDate.after(selectedEndDate) && !endDate.after(selectedEndDate)) {
						result = true;
					}
				}
			}
		}
		return result;
	}
	
	
	
	
	public String[] getContainsValue(String[] originalArray, String[] selectedArray)
	{
		List<String> containsList = new ArrayList<>();
		boolean contains = false;
		 for(int j=0;j<originalArray.length;j++) {
			 contains = Arrays.asList(selectedArray).contains(originalArray[j]);
			 if (contains) {
				 containsList.add(originalArray[j]);
			 }
		 }
	    String[] resultArray = new String[containsList.size()];
	    containsList.toArray(resultArray);
		return resultArray;
	}
	
	public List<KtForm> getKtFormList()
	{
		if (ktFormList == null) {
			ktFormList = fDao.getKtFormListWithCon(true);
		}
		return ktFormList;
	}

	public void setKtFormList(List<KtForm> ktFormList)
	{
		this.ktFormList = ktFormList;
	}
	
	
	
	public List<KtRptPeriod> getKtPeriodList()
	{
		if (ktPeriodList == null) {
			List<KtRptPeriod> tmpKtPeriodList = rDao.getKtRptPeriodList();
			ktPeriodList = tmpKtPeriodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return ktPeriodList;
	}


	
	public void setKtPeriodList(List<KtRptPeriod> ktPeriodList)
	{
		this.ktPeriodList = ktPeriodList;
	}

	
	public String getRiSumDataLevel()
	{
		String dataLevel = "P";
		if ("Y".equals(getParamAdmin())) {
			dataLevel = "P";
		}
		return dataLevel;
	}
	
	public String getKtSumDataLevel()
	{
		String dataLevel = "P";
		if ("Y".equals(getParamAdmin())) {
			dataLevel = "D";
		}
		return dataLevel;
	}
	
	public String getSumStaffNo()
	{
		String staffNo = null;	
		if ("Y".equals(getParamAdmin())) {
			staffNo = null;
		}else {if (staffDetail != null) {
			staffNo = staffDetail.getStaff_number();

			}
		}
		return staffNo;
	}
	
	public String getSumDept()
	{
		String dept = "";
		if (getSumStaffNo() == null) {
			if (!Strings.isNullOrEmpty(getSelectedFacDept())) {
				dept = selectedFacDept;
			}
		}
		return dept;
	}
	
	
	public Boolean isTotal()
	{
		if (isTotal == null) {
			isTotal = false;
		}
		return isTotal;
	}

	
	public void setTotal(Boolean isTotal)
	{
		this.isTotal = isTotal;
	}

	public String getSelectedFacDept()
	{
		if(selectedFacDept != null) {
			List<String> accessList = getRiAdminDeptList();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			if(!accessList.contains(selectedFacDept) || haveAll) selectedFacDept = null;
		}
		if(selectedFacDept == null && getFacDeptList() != null && !getFacDeptList().isEmpty()) {
			SelectItemGroup firstLayer = (SelectItemGroup) getFacDeptList().get(0);
			selectedFacDept = firstLayer.getSelectItems()[0].getValue().toString();
		}
		return selectedFacDept;
	}

	
	public void setSelectedFacDept(String selectedFacDept)
	{
		this.selectedFacDept = selectedFacDept;
		
	}


	
	public KtRptPeriod getKtPeriod()
	{
		if (!Strings.isNullOrEmpty(selectedPeriod)) {
			ktPeriod = rDao.getKtRptPeriod(Integer.valueOf(selectedPeriod));
		}
		return ktPeriod;
	}


	
	public void setKtPeriod(KtRptPeriod ktPeriod)
	{
		this.ktPeriod = ktPeriod;
	}


	
	public String getParamFormCode()
	{
		return paramFormCode;
	}


	
	public void setParamFormCode(String paramFormCode)
	{
		this.paramFormCode = paramFormCode;
	}



	public String getParamAdmin()
	{
		return paramAdmin;
	}


	
	public void setParamAdmin(String paramAdmin)
	{
		this.paramAdmin = paramAdmin;
	}
	

	
	public String getParamFacDept()
	{
		return paramFacDept;
	}

	
	public void setParamFacDept(String paramFacDept)
	{
		this.paramFacDept = paramFacDept;
	}

	
	public String getParamStartDate()
	{
		return paramStartDate;
	}

	
	public void setParamStartDate(String paramStartDate)
	{
		this.paramStartDate = paramStartDate;
	}

	
	public String getParamEndDate()
	{
		return paramEndDate;
	}

	
	public void setParamEndDate(String paramEndDate)
	{
		this.paramEndDate = paramEndDate;
	}

	
	public String getParamRiPeriod()
	{
		return paramRiPeriod;
	}

	
	public void setParamRiPeriod(String paramRiPeriod)
	{
		this.paramRiPeriod = paramRiPeriod;
	}

	
	public String getParamKtPeriod()
	{
		return paramKtPeriod;
	}

	
	public void setParamKtPeriod(String paramKtPeriod)
	{
		this.paramKtPeriod = paramKtPeriod;
	}

	
	public String getParamStaffName()
	{
		return paramStaffName;
	}

	
	public void setParamStaffName(String paramStaffName)
	{
		this.paramStaffName = paramStaffName;
	}

	public List<SelectItem> getFacDeptList()
	{
		if(facDeptList == null) {
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<LookupValue> l2List = dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			List<String> accessList = getRiAdminDeptList();
			facDeptList = new ArrayList<SelectItem>();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			for(LookupValue lkVal1 : l1List) {
				SelectItemGroup itemGroup = new SelectItemGroup(
						lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")");
				List<SelectItem> itemList = new ArrayList<SelectItem>();
				if(haveAll || accessList.contains(lkVal1.getPk().getLookup_code())) {
					itemList.add(new SelectItem(lkVal1.getPk().getLookup_code(), 
							lkVal1.getDescription() + " (" + lkVal1.getPk().getLookup_code()+")"));
				}
				for(LookupValue lkVal2 : l2List) {
					if(haveAll || 
							(lkVal2.getParent_lookup_code().equals(lkVal1.getPk().getLookup_code()) &&
							accessList.contains(lkVal2.getPk().getLookup_code()))) {
						itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
								lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
					}
				}
				if(!itemList.isEmpty()) {
					SelectItem[] itemArr = new SelectItem[itemList.size()];
					itemGroup.setSelectItems(itemList.toArray(itemArr));
					facDeptList.add(itemGroup);
				}
			}
			SelectItemGroup itemGroup = new SelectItemGroup("Other");
			List<SelectItem> itemList = new ArrayList<SelectItem>();
			for(LookupValue lkVal2 : l2List) {
				if(haveAll ||
						(lkVal2.getParent_lookup_code().equals(null) &&
						accessList.contains(lkVal2.getPk().getLookup_code()))) {
					itemList.add(new SelectItem(lkVal2.getPk().getLookup_code(), 
							lkVal2.getDescription() + " (" + lkVal2.getPk().getLookup_code()+")"));
				}
			}
			if(!itemList.isEmpty()) {
				SelectItem[] itemArr = new SelectItem[itemList.size()];
				itemGroup.setSelectItems(itemList.toArray(itemArr));
				facDeptList.add(itemGroup);
			}
		}
		return facDeptList;
	}


	
	public Date getSelectedStartDate() throws ParseException
	{
		if (!Strings.isNullOrEmpty(paramStartDate)) {
			selectedStartDate = stringToDate(paramStartDate);
			paramStartDate = null;
		}
		return selectedStartDate;
	}


	
	public void setSelectedStartDate(Date selectedStartDate)
	{
		this.selectedStartDate = selectedStartDate;
	}


	
	public Date getSelectedEndDate() throws ParseException
	{
		if (!Strings.isNullOrEmpty(paramEndDate)) {
			selectedEndDate = stringToDate(paramEndDate);
			paramEndDate = null;
		}
		return selectedEndDate;
	}


	
	public void setSelectedEndDate(Date selectedEndDate)
	{
		this.selectedEndDate = selectedEndDate;
	}
	
	
	
	public String[] getSelectedFacDepts()
	{
		if (!Strings.isNullOrEmpty(paramFacDept)) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramFacDept.split(",")));
			selectedFacDepts = tmp.toArray(new String[0]);
			paramFacDept = null;
		}
		if(selectedFacDepts != null) {
			List<String> accessList = getRiAdminDeptList();
			Boolean haveAll = accessList.contains(SecDataUser.allData);
			//if(!accessList.contains(selectedFacDepts) || haveAll) selectedFacDepts = null;
			List<String> tmpList = new ArrayList<>();
			for (int i = 0; i < selectedFacDepts.length; i++) {
				if (accessList.contains(selectedFacDepts[i])) {
					tmpList.add(selectedFacDepts[i]);
				}
			}
			selectedFacDepts = tmpList.toArray(new String[0]);
		}
		if(selectedFacDepts == null && getFacDeptList() != null && !getFacDeptList().isEmpty()) {
			SelectItemGroup firstLayer = (SelectItemGroup) getFacDeptList().get(0);
			String item = firstLayer.getSelectItems()[0].getValue().toString();
			selectedFacDepts = new String [] {item};
			List<FacDept> tmpList = staffDao.getFacDeptByFac(selectedFacDepts[0]);
			List<String> tmpDeptList = new ArrayList<>();
			if (tmpList != null) {
				tmpDeptList.add(selectedFacDepts[0]);
				for (FacDept d:tmpList) {
					if (d.getDesc().contains("Center") == false && d.getDesc().contains("Centre") == false) {
						tmpDeptList.add(d.getFac_dept());
					}
				}
				selectedFacDepts = tmpDeptList.toArray(new String[0]);
			}
		}
		return selectedFacDepts;
	}

	
	public void setSelectedFacDepts(String[] selectedFacDepts)
	{
		this.selectedFacDepts = selectedFacDepts;
	}

	public String[] getSelectedCdcfPeriods() throws ParseException
	{
		/*if (ArrayUtils.isEmpty(selectedCdcfPeriods)) {
			if (getSelectedStartDate() == null && getSelectedEndDate() == null) {
				List<CdcfRptPeriod> periodList = getSelectedCdcfPeriodListByTop(getTopYear());
				selectedCdcfPeriods = new String[periodList.size()];
				int i = 0;
				for (CdcfRptPeriod d:periodList) {
					selectedCdcfPeriods[i] = d.getPeriod_id().toString();
					i++;
				}
			}
		}*/
		if (!Strings.isNullOrEmpty(paramRiPeriod)) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramRiPeriod.split(",")));
			selectedCdcfPeriods = tmp.toArray(new String[0]);
			paramRiPeriod = null;
		}
		return selectedCdcfPeriods;
	}


	
	public void setSelectedCdcfPeriods(String[] selectedCdcfPeriods)
	{
		this.selectedCdcfPeriods = selectedCdcfPeriods;
	}
	
	//get default top year
	public int getTopYear()
	{
		String topYearStr = sDao.getSysParamValueByCode("RI_SUM_TOP_YEAR");
        int topYear = Integer.valueOf(topYearStr);
        return topYear;
	}
	
	public List<CdcfRptPeriod> getSelectedCdcfPeriodListByTop()
	{
		List<CdcfRptPeriod> resultList = new ArrayList<CdcfRptPeriod>();
		if (getCdcfPeriodList() != null) {
			for (CdcfRptPeriod d:cdcfPeriodList) {
				resultList.add(d);
				//period_id:10000 = Before 2016
				/*if (d.getPeriod_id() != 10000) {
					resultList.add(d);
				}*/
			}	
		}
		return resultList;
	}
	
	public List<KtRptPeriod> getSelectedKtPeriodListByTop()
	{
		List<KtRptPeriod> resultList = new ArrayList<KtRptPeriod>();
		if (getKtPeriodList() != null) {
			for (KtRptPeriod d:ktPeriodList) {
				resultList.add(d);
			}	
		}
		return resultList;
	}
	
	public List<CdcfRptPeriod> getSelectedCdcfPeriodListBySelectedDate() throws ParseException
	{
		selectedCdcfPeriodList = new ArrayList<CdcfRptPeriod>();
		if (getSelectedStartDate() != null && getSelectedEndDate() != null) {
			Date tmpStartDate = new Date();
			Date tmpEndDate = new Date();
			if (getSelectedStartDate() != null) {
				tmpStartDate = getSelectedStartDate();
				if (getSelectedEndDate() == null) {
					tmpEndDate = getSelectedStartDate();
				}
			}
			if (getSelectedEndDate() != null) {
				tmpEndDate = getSelectedEndDate();
				if (getSelectedStartDate() == null) {
					tmpStartDate = getSelectedEndDate();
				}
			}
			for (CdcfRptPeriod d:getCdcfPeriodList()) {
				if (d.getPeriod_id() > 1) {
					if ((tmpStartDate.compareTo(d.getDate_to()) < 1 && tmpEndDate.compareTo(d.getDate_from()) > -1)) {
						selectedCdcfPeriodList.add(d);
					}
				}
			}
		}
		return selectedCdcfPeriodList;
	}
	
	public List<KtRptPeriod> getSelectedKtPeriodListBySelectedDate() throws ParseException
	{
		selectedKtPeriodList = new ArrayList<KtRptPeriod>();
		if (getSelectedStartDate() != null && getSelectedEndDate() != null) {
			Date tmpStartDate = new Date();
			Date tmpEndDate = new Date();
			if (getSelectedStartDate() != null) {
				tmpStartDate = getSelectedStartDate();
				if (getSelectedEndDate() == null) {
					tmpEndDate = getSelectedStartDate();
				}
			}
			if (getSelectedEndDate() != null) {
				tmpEndDate = getSelectedEndDate();
				if (getSelectedStartDate() == null) {
					tmpStartDate = getSelectedEndDate();
				}
			}
			for (KtRptPeriod d:getKtPeriodList()) {
				if (d.getPeriod_id() > 1) {
					if ((tmpStartDate.compareTo(d.getDate_to()) < 1 && tmpEndDate.compareTo(d.getDate_from()) > -1)) {
						selectedKtPeriodList.add(d);
					}
				}
			}
		}
		return selectedKtPeriodList;
	}
	
	public List<CdcfRptPeriod> getSelectedCdcfPeriodList() throws NumberFormatException, ParseException
	{
		selectedCdcfPeriodList = new ArrayList<CdcfRptPeriod>();
		if (getSelectedCdcfPeriods() != null) {
			if (selectedCdcfPeriods.length > 0) {
				for (int i = 0; i < selectedCdcfPeriods.length; i++) {
					Integer tmpPeriodId = Integer.valueOf(selectedCdcfPeriods[i]);
					CdcfRptPeriod tmpCdcfPeriod = cdcfRptDao.getCdcfRptPeriod(tmpPeriodId);
					selectedCdcfPeriodList.add(tmpCdcfPeriod);
				}
			}else {
				getSelectedCdcfPeriodListBySelectedDate();
			}
		}else {
			getSelectedCdcfPeriodListBySelectedDate();
		}
		if (selectedCdcfPeriodList.isEmpty()) {
			selectedCdcfPeriodList = getSelectedCdcfPeriodListByTop();
		}
		return selectedCdcfPeriodList;
	}

	public List<KtRptPeriod> getSelectedKtPeriodList() throws ParseException
	{
		selectedKtPeriodList = new ArrayList<KtRptPeriod>();
		if (getSelectedKtPeriods() != null) {
			if (selectedKtPeriods.length > 0) {
				for (int i = 0; i < selectedKtPeriods.length; i++) {
					Integer tmpPeriodId = Integer.valueOf(selectedKtPeriods[i]);
					KtRptPeriod tmpKtPeriod = rDao.getKtRptPeriod(tmpPeriodId);
					selectedKtPeriodList.add(tmpKtPeriod);
				}
			}else {
				getSelectedKtPeriodListBySelectedDate();
			}
		}else {
			getSelectedKtPeriodListBySelectedDate();
		}
		if (selectedKtPeriodList.isEmpty()) {
			selectedKtPeriodList = getSelectedKtPeriodListByTop();
		}
		return selectedKtPeriodList;
	}

	
	public List<CdcfRptPeriod> getCdcfPeriodList()
	{
		if (cdcfPeriodList == null) {
			List<CdcfRptPeriod> tmpCdcfPeriodList = cdcfRptDao.getCdcfRptPeriodList();
			cdcfPeriodList = tmpCdcfPeriodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return cdcfPeriodList;
	}


	
	public void setCdcfPeriodList(List<CdcfRptPeriod> cdcfPeriodList)
	{
		this.cdcfPeriodList = cdcfPeriodList;
	}
	
	//get chart reporting period list
	public List<CdcfRptPeriod> getChartPeriodListByTop() throws NumberFormatException, ParseException
	{
		List<CdcfRptPeriod> periodList = getSelectedCdcfPeriodListByTop();
		if (getSelectedCdcfPeriods() != null) {
        	periodList = getSelectedCdcfPeriodList();
        }
		return periodList;
	}
	
	//get kt chart reporting period list
	public List<KtRptPeriod> getKtChartPeriodListByTop() throws ParseException
	{
		List<KtRptPeriod> periodList = getSelectedKtPeriodListByTop();
		if (getSelectedKtPeriods() != null) {
        	periodList = getSelectedKtPeriodList();
        }
		return periodList;
	}
		
	public List<CdcfRptPeriod> getRiChartPeriodList() throws NumberFormatException, ParseException
	{
		if (riChartPeriodList == null) {
			riChartPeriodList = getChartPeriodListByTop();
		}
		return riChartPeriodList;
	}

	
	public void setRiChartPeriodList(List<CdcfRptPeriod> riChartPeriodList)
	{
		this.riChartPeriodList = riChartPeriodList;
	}

	public List<KtRptPeriod> getKtChartPeriodList() throws ParseException
	{
		if (ktChartPeriodList == null) {
			ktChartPeriodList = getKtChartPeriodListByTop();
		}
		return ktChartPeriodList;
	}

	public void setKtChartPeriodList(List<KtRptPeriod> ktChartPeriodList)
	{
		this.ktChartPeriodList = ktChartPeriodList;
	}
	
	public String[] getSelectedKtPeriods() throws ParseException
	{
		if (!Strings.isNullOrEmpty(paramKtPeriod)) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramKtPeriod.split(",")));
			selectedKtPeriods = tmp.toArray(new String[0]);
			paramKtPeriod = null;
		}
		return selectedKtPeriods;
	}


	
	public void setSelectedKtPeriods(String[] selectedKtPeriods)
	{
		this.selectedKtPeriods = selectedKtPeriods;
	}

	public String getStartDateString() throws ParseException
	{
		String date = "";
		if (getSelectedStartDate() != null) {
			date = dateToString(getSelectedStartDate());
		}
		return date;
	}
	
	public String getEndDateString() throws ParseException
	{
		String date = "";
		if (getSelectedEndDate() != null) {
			date = dateToString(getSelectedEndDate());
		}
		return date;
	}
	
	public String getListingStartDateString() throws ParseException
	{
		String date = "";
		Date tmpPeriodDate = null;
		if (getSelectedStartDate() != null) {
			date = dateToString(getSelectedStartDate());
			tmpPeriodDate = getSelectedStartDate();
		}
		
		if (getParamSumType() == "kt") {
			if (getSelectedKtPeriodList() != null) {
				for (KtRptPeriod d:selectedKtPeriodList) {
					if (tmpPeriodDate == null) {
						tmpPeriodDate = d.getDate_from();
						date = dateToString(tmpPeriodDate);
					}
					if (tmpPeriodDate.after(d.getDate_from())) {
						tmpPeriodDate = d.getDate_from();
						date = dateToString(d.getDate_from());
					}		
				}
			}
		}else {
			if (getSelectedCdcfPeriods() != null) {
				for (CdcfRptPeriod d:getSelectedCdcfPeriodList()) {
					if (tmpPeriodDate == null) {
						tmpPeriodDate = d.getDate_from();
						date = dateToString(tmpPeriodDate);
					}
					if (tmpPeriodDate.after(d.getDate_from())) {
						tmpPeriodDate = d.getDate_from();
						date = dateToString(d.getDate_from());
					}
				}
			}
		}
		return date;
	}
	
	public String getListingEndDateString() throws ParseException
	{
		String date = "";
		Date tmpPeriodDate = null;
		if (getSelectedEndDate() != null) {
			date = dateToString(getSelectedEndDate());
			tmpPeriodDate = getSelectedEndDate();
		}
		
		if (getParamSumType() == "kt") {
			if (getSelectedKtPeriodList() != null) {
				for (KtRptPeriod d:selectedKtPeriodList) {
					if (tmpPeriodDate == null) {
						tmpPeriodDate = d.getDate_to();
					}
					if (d.getDate_to().after(tmpPeriodDate)) {
						tmpPeriodDate = d.getDate_to();
						date = dateToString(tmpPeriodDate);
					}
				}
			}
		}else {
			if (getSelectedCdcfPeriods() != null) {	
				for (CdcfRptPeriod d:getSelectedCdcfPeriodList()) {
					if (tmpPeriodDate == null) {
						tmpPeriodDate = d.getDate_to();
						date = dateToString(tmpPeriodDate);
					}
					if (d.getDate_to().after(tmpPeriodDate)) {
						tmpPeriodDate = d.getDate_to();
						date = dateToString(tmpPeriodDate);
					}
				}
			}
		}
		return date;
	}

	
	public String getSelectedFacDeptString()
	{
		String listString = "";
		if (ArrayUtils.isEmpty(getSelectedFacDepts()) == false) {
			listString = String.join(",", getSelectedFacDepts());
		}
		return listString;
	}

	public String getSelectedRiPeriodString() throws ParseException
	{
		String listString = "";
		if (ArrayUtils.isEmpty(getSelectedCdcfPeriods()) == false) {
			listString = String.join(",", getSelectedCdcfPeriods());
		}
		return listString;
	}
	
	public String getSelectedKtPeriodString() throws ParseException
	{
		String listString = "";
		if (ArrayUtils.isEmpty(getSelectedKtPeriods()) == false) {
			listString = String.join(",", getSelectedKtPeriods());
		}
		return listString;
	}
	
	public List<Summary> getOutputCountList() throws ParseException
	{
		if (outputCountList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());

			outputCountList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), isTotal(), getSelectedOutputTypeList(), null, null, null);
		}
		return outputCountList;
	}
	
	public void setOutputCountList(List<Summary> outputCountList)
	{
		this.outputCountList = outputCountList;
	}

	
	public List<Summary> getOutputWeightingList() throws ParseException
	{
		if (outputWeightingList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());
			outputWeightingList = outputDao.getOutputSummaryWeightingList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), isTotal(), getSelectedOutputTypeList(), null, null, null);
		}
		return outputWeightingList;
	}

	
	public void setOutputWeightingList(List<Summary> outputWeightingList)
	{
		this.outputWeightingList = outputWeightingList;
	}

	public boolean checkRiHasSelectedPeriodDate() throws ParseException
	{
		boolean result = false;
		if (Strings.isNullOrEmpty(getSelectedRiPeriodString()) && Strings.isNullOrEmpty(getStartDateString()) && Strings.isNullOrEmpty(getEndDateString())) {
			result = false;
		}else {
			result = true;
		}
		return result;
	}
	
	public boolean checkKtHasSelectedPeriodDate() throws ParseException
	{
		boolean result = false;
		if (Strings.isNullOrEmpty(getSelectedKtPeriodString()) && Strings.isNullOrEmpty(getStartDateString()) && Strings.isNullOrEmpty(getEndDateString())) {
			result = false;
		}else {
			result = true;
		}
		return result;
	}
	
	public Integer getOutputCount() throws ParseException, SQLException
	{
        if (outputCount == null) {
        	Double count = 0.0;
        	isTotal = true;
        	if (checkRiHasSelectedPeriodDate()) {
        		
        		
	        	if (getRiChartPeriodList() != null) {
		        	for (CdcfRptPeriod p:riChartPeriodList) {
		        		count += getOutputTotalCount(false, p.getPeriod_id());
		        	}
	        	}else {
	        		count += getOutputTotalCount(false, null);
	        	}
        	}else {
        		count += getOutputTotalCount(false, null);
        	}
        	outputCount = count.intValue();
        }
		return outputCount;
	}

	
	public void setOutputCount(Integer outputCount)
	{
		this.outputCount = outputCount;
	}

	
	public Double getOutputWeighting() throws ParseException, SQLException
	{
		if (outputWeighting == null) {
			outputWeighting = 0.00;
			isTotal = true;
			if (checkRiHasSelectedPeriodDate()) {
				if (getRiChartPeriodList() != null) {
		        	for (CdcfRptPeriod p:riChartPeriodList) {
		        		// added period
		        		outputWeighting += getOutputTotalCount(true, p.getPeriod_id());
		        	}
				}else {
					outputWeighting += getOutputTotalCount(true, null);
				}
			}else {
				outputWeighting += getOutputTotalCount(true, null);
			}
			Double truncatedDouble = new BigDecimal(outputWeighting).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			outputWeighting = truncatedDouble;
        }
		return outputWeighting;
	}

	
	public void setOutputWeighting(Double outputWeighting)
	{
		this.outputWeighting = outputWeighting;
	}

	
	
	
	public Integer getProjectCount() throws NumberFormatException, ParseException, SQLException
	{
		if (projectCount == null) {
			Double count = 0.0;
        	if (checkRiHasSelectedPeriodDate()) {
	        	if (getRiChartPeriodList() != null) {
		        	for (CdcfRptPeriod p:riChartPeriodList) {
		        		count += getProjectTotalCount(p.getPeriod_id(), false);
		        	}
	        	}else {
	        		count += getProjectTotalCount(null, false);
	        	}
        	}else {
        		count += getProjectTotalCount(null, false);
        	}
        	projectCount = count.intValue();
        }
		return projectCount;
	}

	
	public void setProjectCount(Integer projectCount)
	{
		this.projectCount = projectCount;
	}

	
	public Integer getAwardCount() throws NumberFormatException, ParseException, SQLException
	{
		if (awardCount == null) {
			Double count = 0.0;
        	if (checkRiHasSelectedPeriodDate()) {
	        	if (getRiChartPeriodList() != null) {
		        	for (CdcfRptPeriod p:riChartPeriodList) {
		        		count += getAwardTotalCount(p.getPeriod_id());
		        	}
	        	}else {
	        		count += getAwardTotalCount(null);
	        	}
        	}else {
        		count += getAwardTotalCount(null);
        	}
        	awardCount = count.intValue();
        }
		return awardCount;
	}

	
	public void setAwardCount(Integer awardCount)
	{
		this.awardCount = awardCount;
	}

	
	public Integer getPatentCount() throws NumberFormatException, ParseException, SQLException
	{
		if (patentCount == null) {
			Double count = 0.0;
        	if (checkRiHasSelectedPeriodDate()) {
	        	if (getRiChartPeriodList() != null) {
		        	for (CdcfRptPeriod p:riChartPeriodList) {
		        		count += getPatentTotalCount(p.getPeriod_id());
		        	}
	        	}else {
	        		count += getPatentTotalCount(null);
	        	}
        	}else {
        		count += getPatentTotalCount(null);
        	}
        	patentCount = count.intValue();
        }
		return patentCount;
	}

	
	public void setPatentCount(Integer patentCount)
	{
		this.patentCount = patentCount;
	}


	
	public Integer getKtCount(String formCode) throws NumberFormatException, ParseException, SQLException
	{
		Double count = 0.0;
    	if (checkRiHasSelectedPeriodDate()) {
        	if (getRiChartPeriodList() != null) {
	        	for (CdcfRptPeriod p:riChartPeriodList) {
	        		count += getKtTotalCount(p.getPeriod_id(), formCode);
	        	}
        	}else {
        		count += getKtTotalCount(null, formCode);
        	}
    	}else {
    		count += getKtTotalCount(null, formCode);
    	}
    	ktCount = count.intValue();
		return ktCount;
	}


	public List<Summary> getProjectCountList() throws ParseException
	{
		if (projectCountList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());
			projectCountList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), isTotal(), getSelectedFundingSourceList(), null, null, null, null);
		}
		return projectCountList;
	}

	
	public void setProjectCountList(List<Summary> projectCountList)
	{
		this.projectCountList = projectCountList;
	}

	
	protected List<String> getSelectedCdcfPeriodListString() throws ParseException
	{
		List<String> resultList = new ArrayList<>();
		if (getSelectedCdcfPeriods() != null) {
			for (int i = 0; i < selectedCdcfPeriods.length; i++) {
				resultList.add(selectedCdcfPeriods[i]);
			}
		}
		return resultList.isEmpty()?null:resultList;
	}
	
	public List<Summary> getProjectCountNewList() throws ParseException
	{
		if (projectCountNewList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());
			projectCountNewList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), isTotal(), getSelectedFundingSourceList(), null, null, null, null);
		}
		return projectCountNewList;
	}

	
	public void setProjectCountNewList(List<Summary> projectCountNewList)
	{
		this.projectCountNewList = projectCountNewList;
	}

	public List<Summary> getAwardCountList() throws ParseException
	{
		if (awardCountList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());
			awardCountList = awardDao.getAwardSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts());
		}
		return awardCountList;
	}

	
	public void setAwardCountList(List<Summary> awardCountList)
	{
		this.awardCountList = awardCountList;
	}

	
	public List<Summary> getPatentCountList() throws ParseException
	{
		if (patentCountList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());
			patentCountList = patentDao.getPatentSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts());
		}
		return patentCountList;
	}

	
	public void setPatentCountList(List<Summary> patentCountList)
	{
		this.patentCountList = patentCountList;
	}

	
	public List<Summary> getKtCountList() throws ParseException
	{
		if (ktCountList == null) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
			String endDate = DateToMonthYearFormat(getSelectedEndDate());
			ktCountList = fDao.getKtSummaryCountList(getSumStaffNo(), getKtSumDataLevel(), startDate, endDate, getSelectedFacs(), getSelectedDepts(), null);
		}
		return ktCountList;
	}

	
	public void setKtCountList(List<Summary> ktCountList)
	{
		this.ktCountList = ktCountList;
	}
	
	public String[] getSelectedOutputTypes()
	{
		return selectedOutputTypes;
	}

	
	public void setSelectedOutputTypes(String[] selectedOutputTypes)
	{
		this.selectedOutputTypes = selectedOutputTypes;
	}

	
	
	public String[] getSelectedFundingSources()
	{
		return selectedFundingSources;
	}

	
	public void setSelectedFundingSources(String[] selectedFundingSources)
	{
		this.selectedFundingSources = selectedFundingSources;
	}

	public List<String> getSelectedOutputTypeList()
	{
		if (ArrayUtils.isEmpty(getSelectedOutputTypes()) == false) {
			selectedOutputTypeList = Arrays.asList(selectedOutputTypes);
		}
		return selectedOutputTypeList;
	}
	
	public List<String> getSelectedFundingSourceList()
	{
		if (ArrayUtils.isEmpty(getSelectedFundingSources()) == false) {
			selectedFundingSourceList = Arrays.asList(selectedFundingSources);
		}else {
			selectedFundingSourceList = null;
		}
		return selectedFundingSourceList;
	}
	
	public void exportSummary() 
	{
		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		
		outputCount = null;
		outputWeighting = null;
		projectCount = null;
		awardCount = null;
		patentCount = null;
		ktCount = null;
		
		outputCountList = null;
		outputWeightingList = null;
		projectCountList = null;
		projectCountNewList = null;
		awardCountList = null;
		patentCountList = null;
		ktCountList = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();

		try 
		{
			Workbook wb = null;
    		wb = new XSSFWorkbook();
    		
    		createDataSheet(wb);
    		if (getKtCountList() != null) {
    			createDataSheetForKt(wb);
    		}

	    	// Get the byte array of the Workbook
	    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			eCtx.responseReset();
			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");

			DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = "DataExport-" + dateFormat.format(new Date()) + ".xlsx";		        
			
			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
		
			fCtx.responseComplete();
		}
		catch (IOException e) 
    	{
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}		
	}
	
	private void createDataSheetForKt(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet("KT Activities Summary");
		String sheetHeader = "KT Activities Summary";
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
    	String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	List<String> deptsColsHeader = new ArrayList<>();
    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null && getKtFormList() != null) {
    			for (int i = 0; i < selectedFacs.size(); i++) {
    				for(KtForm f:ktFormList) {
    					deptsColsHeader.add(f.getForm_short_desc() +" ("+ selectedFacs.get(i)+")");
    				}
    			}
    			for (int i = 0; i < selectedDepts.size(); i++) {
    				for(KtForm f:ktFormList) {
    					deptsColsHeader.add(f.getForm_short_desc() +" ("+ selectedDepts.get(i)+")");
    				}
    			}
    			depts = deptsColsHeader.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}else {
    		String[] headerLastArray = new String[ktFormList.size()];
        	for(int i = 0; i < ktFormList.size(); i++){
        		headerLastArray[i] = "Total no. of "+ktFormList.get(i).getForm_short_desc();
        	}
        	headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	}
    	
    	
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());
	    		
	    		if ("Y".equals(getParamAdmin())) {
	    			if (selectedFacs != null) {
	    				for (int j = 0; j < selectedFacs.size(); j++) {
	    					String facDept = selectedFacs.get(j);
	    					Double c = 0.0;
	    					for(KtForm f:ktFormList) {
	    						c = ktCountList.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFac()) 
		    									&& f.getForm_code().equals(d.getForm_code()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
	    						cell = row.createCell(i++);
		    		    		cell.setCellValue(c);
	        				}
	    				}
	    			}
	    			if (selectedDepts != null) {
	    				for (int j = 0; j < selectedDepts.size(); j++) {
	    					String facDept = selectedDepts.get(j);
	    					Double c = 0.0;
	    					for(KtForm f:ktFormList) {
	    						c = ktCountList.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()) 
		    									&& f.getForm_code().equals(d.getForm_code()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
	    						cell = row.createCell(i++);
		    		    		cell.setCellValue(c);
	        				}
	    				}
	    			}
	    		}else {
	    			Double count = 0.0;
		    		for(KtForm f:ktFormList) {
			    		count = ktCountList.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id())
										&& f.getForm_code().equals(d.getForm_code()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
			    		cell = row.createCell(i++);
			    		cell.setCellValue(count);
		    		}
	    		}
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createDataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		isTotal = false;
		Sheet sheet = workbook.createSheet("Count RI");
		String sheetHeader = "Count RI";
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
    	String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	List<String> deptsColsHeader = new ArrayList<>();
    	String[] headerLastArray = {"Total no. of "+riTypeArray[0], riTypeArray[1], "Total no. of "+riTypeArray[2], "Total no. of "+riTypeArray[3], "Total no. of "+riTypeArray[4]};

    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null) {
    			for (int i = 0; i < selectedDepts.size(); i++) {
    				for (int j = 0; j < riTypeArray.length; j++) {
    					deptsColsHeader.add(riTypeArray[j] +" ("+ selectedDepts.get(i)+")");
    				}
    			}
    			depts = deptsColsHeader.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}else{
    		headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	}
    	
    	
    	
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());
	    		
	    		if ("Y".equals(getParamAdmin())) {
	    			if (selectedDepts != null) {
	    				for (int j = 0; j < selectedDepts.size(); j++) {
	    					String facDept = selectedDepts.get(j);
	    					Double c = 0.0;
	    					
	    					if (getOutputCountList() != null) {
    						c = outputCountList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					}
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    		    		
	    		    		c = 0.0;
	    		    		if (getOutputWeightingList() != null) {
    						c = outputWeightingList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getWeighting()))
	    							.sum();
	    		    		}
	    		    		c = new BigDecimal(c).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    		    		
	    		    		c = 0.0;
	    		    		if (getProjectCountNewList() != null) {
    						c = projectCountNewList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    		    		}
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    		    		
	    		    		c = 0.0;
	    		    		if (getAwardCountList() != null) {
    						c = awardCountList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    		    		}
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    		    		
	    		    		c = 0.0;
	    		    		if (getPatentCountList() != null) {
    						c = patentCountList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    		    		}
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    				}
	    			}
	    		}else {
	    			Double count = 0.0;
	    			count = getOutputTotalCount(false, r.getPeriod_id());
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
		    		
		    		count = 0.0;
		    		count = getOutputTotalCount(true, r.getPeriod_id());
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
		    		
		    		count = 0.0;
	    			count = getProjectTotalCount(r.getPeriod_id(), false);
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
		    		
		    		count = 0.0;
		    		count = getAwardTotalCount(r.getPeriod_id());
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
		    		
		    		count = 0.0;
		    		count = getPatentTotalCount(r.getPeriod_id());
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
	    		}
	    		
	    		
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
}


