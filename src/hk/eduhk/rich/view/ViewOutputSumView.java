package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.primefaces.model.charts.ChartData;

import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.*;

import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.primefaces.model.charts.pie.PieChartOptions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.event.CloseEvent;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;
import org.primefaces.model.chart.Axis;
import org.primefaces.model.chart.AxisType;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.award.AwardDetails_P;
import hk.eduhk.rich.entity.form.KtFormCPD_P;
import hk.eduhk.rich.entity.patent.PatentDAO;
import hk.eduhk.rich.entity.patent.PatentDetails_P;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name = "viewOutputSumView")
@ViewScoped
@SuppressWarnings("serial")
public class ViewOutputSumView extends ViewSumView
{
	private static Logger logger = Logger.getLogger(ViewOutputSumView.class.getName());
	private List<OutputType> outputTypeList;
	
	private BarChartModel outputYrTotalBarModel;	
	private BarChartModel outputWeightingYrTotalBarModel;	
	private BarChartModel outputRBkRJrBarModel;	
	private BarChartModel outputIntConfBarModel;	
	private BarChartModel outputRGCBarModel;
	private BarChartModel outputHighEduBarModel;
		
	private String[] rbkCodeArray = {"120", "130", "140", "150"};
	private String[] rjrcodeArray = {"160", "170"};
	private PublicationDAO outputDao = PublicationDAO.getInstance();

	
	@PostConstruct
    public void init() throws ParseException, SQLException {
		staffDetail = getStaffDetail(getParamPid(), null, true);
        //updateChart();
    }

	public void updateChart() throws SQLException
	{
        try
		{
        	selectedDepts = null;
    		selectedFacs = null;
    		riChartPeriodList = null;
    		ktChartPeriodList = null;
    		outputCount = null;
    		outputCountList = null;
    		outputWeighting = null;
    		outputWeightingList = null;
    		
        	createOutputYrTotalBarModel();
        	createOutputWeightingYrTotalBarModel();
        	//createOutputRBkRJrPieModel();
        	createOutputRBkRJrBarModel();
        	createOutputIntConfBarModel();
        	createOutputRGCBarModel();
        	createOutputHighEduBarModel();
		}
		catch (ParseException e)
		{
			e.printStackTrace();
		}
	}
	

	
	public List<String> getSelectedOutputTypeList()
	{
		List<String> outputTypeList = new ArrayList<>();
		if (ArrayUtils.isEmpty(getSelectedOutputTypes()) == false) {
			outputTypeList = Arrays.asList(getSelectedOutputTypes());
		}else {
			outputTypeList = null;
		}
		return outputTypeList;
	}
	
	public String getSelectedOutputTypeString()
	{
		String listString = "";
		if (ArrayUtils.isEmpty(getSelectedOutputTypes()) == false) {
			listString = String.join(",", getSelectedOutputTypes());
		}
		return listString;
	}
	

	
	
	public BarChartModel getOutputWeightingYrTotalBarModel()
	{
		return outputWeightingYrTotalBarModel;
	}


	
	public void setOutputWeightingYrTotalBarModel(BarChartModel outputWeightingYrTotalBarModel)
	{
		this.outputWeightingYrTotalBarModel = outputWeightingYrTotalBarModel;
	}


	public List<OutputType> getOutputTypeList()
	{
		if (outputTypeList == null) {
			outputTypeList = new ArrayList<OutputType>();
			List<OutputType> lvOneList = outputDao.getOutputTypeList(1);
			List<OutputType> lvTwoList = outputDao.getOutputTypeList(2);
			for (OutputType o:lvOneList) {
				outputTypeList.add(o);
				List<OutputType> tmpLvTwoList = lvTwoList.stream()
						.filter(y -> y.getParent_lookup_code().equals(o.getPk().getLookup_code()))
						.collect(Collectors.toList());
				outputTypeList.addAll(tmpLvTwoList);
			}
		}
		return outputTypeList;
	}
	
	public void setOutputTypeList(List<OutputType> outputTypeList)
	{
		this.outputTypeList = outputTypeList;
	}


	
	public BarChartModel getOutputYrTotalBarModel()
	{
		return outputYrTotalBarModel;
	}


	
	public void setOutputYrTotalBarModel(BarChartModel outputYrTotalBarModel)
	{
		this.outputYrTotalBarModel = outputYrTotalBarModel;
	}
	

	
	
	
	//create Total no. of Research Outputs by Reporting Year
	//bar chart
	public void createOutputYrTotalBarModel() throws ParseException, SQLException 
	{
		outputYrTotalBarModel = new BarChartModel();
        ChartData data = new ChartData();
        BarChartDataSet barDataSet = new BarChartDataSet();
        barDataSet.setLabel("Research Outputs");
        int countTotalBar = (getRiChartPeriodList() != null)?riChartPeriodList.size():0;
        
        List<String> bgColor = new ArrayList<>();
        for (int i = 0; i < countTotalBar; i++) {
        	bgColor.add("#03a9f4");
        }
        barDataSet.setBackgroundColor(bgColor);

        data.addChartDataSet(barDataSet);

        List<String> labels = new ArrayList<>();
        List<Number> values = new ArrayList<>();
        
        if (riChartPeriodList != null) {
    	 for (int i = 0;  i< riChartPeriodList.size(); i++) {
    		 int countYearTotal = 0;
         	labels.add(riChartPeriodList.get(i).getChart_desc());
         	countYearTotal += getOutputTotalCount(false, riChartPeriodList.get(i).getPeriod_id());
         	values.add(countYearTotal);
         }
        }
        barDataSet.setData(values);
        data.setLabels(labels);
        outputYrTotalBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setOffset(true);
        //linearAxes.setBeginAtZero(true);
        CartesianLinearTicks ticks = new CartesianLinearTicks();
        linearAxes.setTicks(ticks);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(false);
        title.setText("Research Outputs by Reporting Year");
        options.setTitle(title);

       Legend legend = new Legend();
        legend.setDisplay(true);
        legend.setPosition("top");
        LegendLabel legendLabels = new LegendLabel();
        legendLabels.setFontStyle("italic");
        legendLabels.setFontColor("#2980B9");
        legendLabels.setFontSize(24);
        legend.setLabels(legendLabels);
        options.setLegend(legend);

        // disable animation
        Animation animation = new Animation();
        animation.setDuration(0);
        options.setAnimation(animation);

        //outputYrTotalBarModel.setOptions(options);
        outputYrTotalBarModel.setExtender("barChartExtender");
    }

	//create Total no. of Research Outputs of weighting by Reporting Year
	//bar chart
	public void createOutputWeightingYrTotalBarModel() throws ParseException, SQLException 
	{
		outputWeightingYrTotalBarModel = new BarChartModel();
        ChartData data = new ChartData();
        BarChartDataSet barDataSet = new BarChartDataSet();
        barDataSet.setLabel("Research Outputs by Weighting");
        int countTotalBar = (getRiChartPeriodList() != null)?riChartPeriodList.size():0;
        
        List<String> bgColor = new ArrayList<>();
        for (int i = 0; i < countTotalBar; i++) {
        	bgColor.add("#db5687");
        }
        barDataSet.setBackgroundColor(bgColor);

        data.addChartDataSet(barDataSet);

        List<String> labels = new ArrayList<>();
        
        List<Number> values = new ArrayList<>();
        
        if (getRiChartPeriodList() != null) {
    	 for (int i = 0;  i< riChartPeriodList.size(); i++) {
    		 Double countYearTotal = 0.00;
        	 labels.add(riChartPeriodList.get(i).getChart_desc());
        	 countYearTotal = getOutputTotalCount(true, riChartPeriodList.get(i).getPeriod_id());
         	 Double truncatedDouble = new BigDecimal(countYearTotal).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
         	 countYearTotal = truncatedDouble;
         	 values.add(countYearTotal);
         }
        }
        barDataSet.setData(values);
        data.setLabels(labels);
        outputWeightingYrTotalBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setOffset(true);
        //linearAxes.setBeginAtZero(true);
        CartesianLinearTicks ticks = new CartesianLinearTicks();
        linearAxes.setTicks(ticks);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(false);
        title.setText("Research Outputs of weighting by Reporting Year");
        options.setTitle(title);

       Legend legend = new Legend();
        legend.setDisplay(false);
        legend.setPosition("top");
        LegendLabel legendLabels = new LegendLabel();
        legendLabels.setFontStyle("italic");
        legendLabels.setFontColor("#2980B9");
        legendLabels.setFontSize(24);
        legend.setLabels(legendLabels);
        options.setLegend(legend);

        // disable animation
        Animation animation = new Animation();
        animation.setDuration(0);
        options.setAnimation(animation);

        //outputWeightingYrTotalBarModel.setOptions(options);
        outputWeightingYrTotalBarModel.setExtender("weightingBarChartExtender");
    }
	
	
	//create Is international conference? by Reporting Year
	//bar chart
	public void createOutputIntConfBarModel() throws ParseException, SQLException
	{
		outputIntConfBarModel = new BarChartModel();

        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();
                
        barDataSet.setLabel("Yes");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("No");
        barDataSet2.setBackgroundColor("#0F9D58");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> outputCountIntConfList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedOutputTypeList(), "Y", null, null);
  		List<Summary> outputCountNotIntConfList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedOutputTypeList(), "N", null, null);
        int countYTotal = 0;
  		int countNTotal = 0;
  		 
        if (getRiChartPeriodList() != null && outputCountIntConfList != null && outputCountNotIntConfList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            List<Summary> match1 = outputCountIntConfList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	  	         Double c1 = outputCountIntConfList.stream()
							.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
	            int countY = (int) Math.round(c1);
  	            dataVal.add(countY);
  	            countYTotal += countY;
  	            
	            List<Summary> match2 = outputCountNotIntConfList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c2 = outputCountNotIntConfList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countN = (int) Math.round(c2);
	            dataVal2.add(countN);
	            countNTotal += countN;
        	}
         }

        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);

        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);


        data.setLabels(labels);
        outputIntConfBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(true);
        title.setText("Total of Yes: " + countYTotal + ", Total of No: " + countNTotal);
        options.setTitle(title);


        //outputIntConfBarModel.setOptions(options);
        outputIntConfBarModel.setExtender("countBarChartExtender");
	}

	//create No. of Refereed Books and Journals by Reporting Year
	//bar chart
	public void createOutputRBkRJrBarModel() throws ParseException, SQLException
	{
		outputRBkRJrBarModel = new BarChartModel();
        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();
        
        List<CdcfRptPeriod> riChartPeriodList = getRiChartPeriodList();
        
        barDataSet.setLabel("Refereed Book");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("Refereed Journal");
        barDataSet2.setBackgroundColor("#0F9D58");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        
  		List<String > rbkCodeList = Arrays.asList(rbkCodeArray);
  		List<String > rjrCodeList = Arrays.asList(rjrcodeArray);
  		String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
  		List<Summary> outputCountRbkList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, rbkCodeList, null, null, null);
  		List<Summary> outputCountRjrList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, rjrCodeList, null, null, null);

  		if (riChartPeriodList != null && outputCountRbkList != null && outputCountRjrList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            List<Summary> matchRbk = outputCountRbkList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
  	            int countRbk = (matchRbk.size() > 0)?Integer.parseInt(matchRbk.get(0).getCount()):0;
  	            dataVal.add(countRbk);
  	            
	            List<Summary> matchRjr = outputCountRjrList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            int countRjr = (matchRjr.size() > 0)?Integer.parseInt(matchRjr.get(0).getCount()):0;
	            dataVal2.add(countRjr);
        	}
        }
        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);

        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);


        data.setLabels(labels);
        outputRBkRJrBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        //outputIntConfBarModel.setOptions(options);
        outputRBkRJrBarModel.setExtender("countBarChartExtender");
	}
	
	//create Is the project(s) for creating the research output fully/partially funded by RGC?
	//bar chart
	public void createOutputRGCBarModel() throws ParseException, SQLException
	{
		outputRGCBarModel = new BarChartModel();

        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();
                
        barDataSet.setLabel("Yes");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("No");
        barDataSet2.setBackgroundColor("#0F9D58");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> outputCountRGCList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedOutputTypeList(), null, "Y", null);
  		List<Summary> outputCountNotRGCList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedOutputTypeList(), null, "N", null);
        int countYTotal = 0;
  		int countNTotal = 0;
  		 
        if (getRiChartPeriodList() != null && outputCountRGCList != null && outputCountNotRGCList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            List<Summary> match1 = outputCountRGCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
  	            int countY = (match1.size() > 0)?Integer.parseInt(match1.get(0).getCount()):0;
  	            dataVal.add(countY);
  	            countYTotal += countY;
  	            
	            List<Summary> match2 = outputCountNotRGCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            int countN = (match2.size() > 0)?Integer.parseInt(match2.get(0).getCount()):0;
	            dataVal2.add(countN);
	            countNTotal += countN;
        	}
        }
        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);

        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);


        data.setLabels(labels);
        outputRGCBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        outputRGCBarModel.setExtender("countBarChartExtender");
	}
		
	//create Is this research output related to the enhancement of teaching and learning in higher education/teacher education?
	//bar chart
	public void createOutputHighEduBarModel() throws ParseException, SQLException
	{
		outputHighEduBarModel = new BarChartModel();

        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();
        
        barDataSet.setLabel("Yes");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("No");
        barDataSet2.setBackgroundColor("#0F9D58");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> outputCountHighEduList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedOutputTypeList(), null, null, "Y");
  		List<Summary> outputCountNotHighEduList = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedOutputTypeList(), null, null, "N");
        int countYTotal = 0;
  		int countNTotal = 0;
  		 
        if (getRiChartPeriodList() != null && outputCountHighEduList != null && outputCountNotHighEduList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            List<Summary> match1 = outputCountHighEduList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
  	          int countY = (match1.size() > 0)?Integer.parseInt(match1.get(0).getCount()):0;
  	            dataVal.add(countY);
  	            countYTotal += countY;
  	            
	            List<Summary> match2 = outputCountNotHighEduList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            int countN = (match2.size() > 0)?Integer.parseInt(match2.get(0).getCount()):0;
	            dataVal2.add(countN);
	            countNTotal += countN;
        	}
        }
        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);

        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);


        data.setLabels(labels);
        outputHighEduBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        outputHighEduBarModel.setExtender("countBarChartExtender");
	}
		
	public BarChartModel getOutputRBkRJrBarModel()
	{
		return outputRBkRJrBarModel;
	}


	
	public void setOutputRBkRJrBarModel(BarChartModel outputRBkRJrBarModel)
	{
		this.outputRBkRJrBarModel = outputRBkRJrBarModel;
	}


	public BarChartModel getOutputIntConfBarModel()
	{
		return outputIntConfBarModel;
	}


	
	public void setOutputIntConfBarModel(BarChartModel outputIntConfBarModel)
	{
		this.outputIntConfBarModel = outputIntConfBarModel;
	}


	
	public BarChartModel getOutputRGCBarModel()
	{
		return outputRGCBarModel;
	}

	
	public void setOutputRGCBarModel(BarChartModel outputRGCBarModel)
	{
		this.outputRGCBarModel = outputRGCBarModel;
	}

	
	public BarChartModel getOutputHighEduBarModel()
	{
		return outputHighEduBarModel;
	}

	
	public void setOutputHighEduBarModel(BarChartModel outputHighEduBarModel)
	{
		this.outputHighEduBarModel = outputHighEduBarModel;
	}

	public String[] getSelectedOutputTypes()
	{
		return selectedOutputTypes;
	}


	
	public void setSelectedOutputTypes(String[] selectedOutputTypes)
	{
		this.selectedOutputTypes = selectedOutputTypes;
	}
	
	public void exportSummary() 
	{
		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		outputCount = null;
		outputCountList = null;
		outputWeighting = null;
		outputWeightingList = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();

		try 
		{
			Workbook wb = null;
    		wb = new XSSFWorkbook();
    		
    		if(CollectionUtils.isNotEmpty(getOutputCountList())) 
	    	{
	    		createDataSheet(wb, false);
	    		createDataSheet(wb, true);
	    	}
    		
    		//Refereed Book and Journal
    		createDataSheetByFilter(wb, "refereedBook");
    		//Is international conference?
    		createDataSheetByFilter(wb, "intConf");
    		//Is the project(s) for creating the research output fully/partially funded by RGC?
    		createDataSheetByFilter(wb, "rgcProj");
    		//Is this related to the enhancement of teaching and learning in higher edu./teacher edu.?
    		createDataSheetByFilter(wb, "highEdu");
    		
	    	// Get the byte array of the Workbook
	    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			eCtx.responseReset();
			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");

			DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = "DataExport-" + dateFormat.format(new Date()) + ".xlsx";		        
			
			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
		
			fCtx.responseComplete();
		}
		catch (IOException e) 
    	{
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
				
	}
		
	private void createDataSheet(Workbook workbook, boolean weighting) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = (weighting)?workbook.createSheet("Research Outputs by Weighting"):workbook.createSheet("Total no. of Research Outputs");
		String sheetHeader = (weighting)?"Research Outputs by Weighting":"Total no. of Research Outputs";
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
    	String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null) {
    			depts = selectedDepts.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}else {
    		String[] headerLastArray = {"Total no."};
        	headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	}
    	
    	
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());
	    		
	    		if ("Y".equals(getParamAdmin())) {
	    			if (selectedDepts != null) {
	    				for (int j = 0; j < selectedDepts.size(); j++) {
	    					String facDept = selectedDepts.get(j);
	    					Double c = 0.00;
	    					if (weighting) {
	    						if (getOutputWeightingList() != null) {
		    						c = outputWeightingList.stream()
			    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
			    							.mapToDouble(d -> Double.parseDouble(d.getWeighting()))
			    							.sum();
		    						c = new BigDecimal(c).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
	    						}
	    					}else {
	    						c = outputCountList.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
	    					}
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    				}
	    			}
	    		}else {
	    			Double count = getOutputTotalCount(weighting, r.getPeriod_id());
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count);
	    		}
    			

	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createDataSheetByFilter(Workbook workbook, String type) throws NumberFormatException, ParseException, SQLException
	{
		
		List<String> columnName = new ArrayList<>();
		String sheetName = "";
		String sheetHeader = "";
		List<Summary> outputCountList1 = new ArrayList<>();
		List<Summary> outputCountList2 = new ArrayList<>();
  		String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
		switch(type) {
			case "refereedBook":
				sheetName = "Refereed Books and Journals";
				sheetHeader = "No. of Refereed Books and Journals";
				columnName.add("Refereed Book");
				columnName.add("Refereed Journal");
				List<String > rbkCodeList = Arrays.asList(rbkCodeArray);
		  		List<String > rjrCodeList = Arrays.asList(rjrcodeArray);
		    	outputCountList1 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, rbkCodeList, null, null, null);
		  		outputCountList2 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, rjrCodeList, null, null, null);
				break;
			case "intConf":
				sheetName = "International conference";
				sheetHeader = "Is international conference?";
				columnName.add("Yes");
				columnName.add("No");
		    	outputCountList1 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedOutputTypeList(), "Y", null, null);
		  		outputCountList2 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedOutputTypeList(), "N", null, null);
		  		break;
			case "rgcProj":
				sheetName = "Arising from RGC funded projects";
				sheetHeader = "Is the project(s) for creating the research output fully/partially funded by RGC?";
				columnName.add("Yes");
				columnName.add("No");
				outputCountList1 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedOutputTypeList(), null, "Y", null);
		  		outputCountList2 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedOutputTypeList(), null, "N", null);
				break;
			case "highEdu":
				sheetName = "Higher edu";
				sheetHeader = "Is this related to the enhancement of teaching and learning in higher edu./teacher edu.?";
				columnName.add("Yes");
				columnName.add("No");
				outputCountList1 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedOutputTypeList(), null, null, "Y");
		  		outputCountList2 = outputDao.getOutputSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedOutputTypeList(), null, null, "N");
				break;
			case "":
				break;
		}
		
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet(sheetName);

    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
  		String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	List<String> deptsColsHeader = new ArrayList<>();
    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null) {
    			for (int i = 0; i < selectedDepts.size(); i++) {
    				deptsColsHeader.add(columnName.get(0) +" ("+ selectedDepts.get(i)+")");
    				deptsColsHeader.add(columnName.get(1) +" ("+ selectedDepts.get(i)+")");
    			}
    			depts = deptsColsHeader.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}else {
    		String[] headerLastArray = {"Count "+columnName.get(0), "Count "+columnName.get(1)};
        	headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	}
    	
    	
    	
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());
	    		
	    		if ("Y".equals(getParamAdmin())) {
	    			if (selectedDepts != null) {
	    				for (int j = 0; j < selectedDepts.size(); j++) {
	    					String facDept = selectedDepts.get(j);
	    					Double c1 = 0.0;
    						c1 = outputCountList1.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c1);
	    		    		
	    					Double c2 = 0.0;
    						c2 = outputCountList2.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c2);
	    				}
	    			}
	    		}else {
	    			Double count1 = outputCountList1.stream()
							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count1);
		    		
		    		Double count2 = outputCountList2.stream()
							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count2);
	    		}
    			

	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
}


