package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.omnifaces.util.Faces;
import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;
import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.CartesianTicks;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.*;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;


import com.google.common.base.Strings;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.FacDept;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.award.AwardDAO;
import hk.eduhk.rich.entity.award.AwardReport;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.project.ProjectReport;
import hk.eduhk.rich.entity.project.ProjectSummary;
import hk.eduhk.rich.entity.publication.OutputDetails_P;
import hk.eduhk.rich.entity.publication.Publication;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.DeptLabelCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.PreRpt;
import hk.eduhk.rich.entity.report.PreRptCat;
import hk.eduhk.rich.entity.report.PreRptDAO;
import hk.eduhk.rich.entity.report.StaffProjCount;
import hk.eduhk.rich.entity.report.deptFundingIntExt;
import hk.eduhk.rich.entity.report.last3YearCount;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.entity.report.staffNumberName;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.report.form.KtFormCDCFReportGroup;
import hk.eduhk.rich.report.form.KtFormCDCFSummaryGroup;
import hk.eduhk.rich.report.form.KtFormCDCFinalReportGroup;
import hk.eduhk.rich.report.form.KtFormDeptSummaryGroup;
import hk.eduhk.rich.report.form.KtFormProjStaffReportGroup;


@SuppressWarnings("serial")
@ManagedBean(name = "preRptView")
@ViewScoped
public class PreRptView extends ManageRIView
{
	public static final String CDCF_GENERATED = "CDCF_GENERATED";
	public static final String CDCF_NOT_SEL = 	"CDCF_NOT_SEL";
	public static final String CDCF_SPEC 	= 	"CDCF_SPEC";
	
	private static final String SELECT_PROMPT = "--Please select--";
	
	private static final int YEAR_ADJUSTMENT = 1900;
	private static final int MONTH_ADJUSTMENT = 1;

	
	private List<String> accessFacDeptList;
	private List<String> accessDeptList;
	
	private List<PreRpt> preRptList;
	private List<PreRpt> allPreRptList;
	private List<PreRpt> preRptListByCat;
	private List<SelectItem> deptfilterList = null;
	private List<PreRptCat> preRptCatList;
	private List<PreRptCat> allPreRptCatList;
	
	private PreRpt selectedPreRpt;
	private PreRpt removePreRpt;
	
	private PreRptCat selectedPreRptCat;
	private PreRptCat removePreRptCat;
	
	private String selectedDept;
	
	private String selectedRpt;
	private String selectedRptCat;
	private String filteredPeriod;
	private String selectedRptnote;
	
	private String chartImageBase64;
	private boolean isChartGenerated;
		
	private Date selectedStartDate;
	private Date selectedEndDate;
	
	private Integer selectedCdcfPeriods;
	
	private BarChartModel outputCountBarModel;
	private BarChartModel outputLastThreeBarModel = null;
	private BarChartModel outputLastThreeZeroBarModel = null;
	private BarChartModel fundingIntExtBarModel = null;
	private BarChartModel fundingFourTypeBarModel = null;

	private List<DeptLabelCount> deptLbaelCountList;
	private List<last3YearCount> last3YearCountList;
	
	private List<deptFundingIntExt> deptFundingIntExtList;
	
	private List<CdcfRptPeriod> cdcfPeriodList = null;
	
	private AppPeriodReportDAO fDao = AppPeriodReportDAO.getInstance();
	private PreRptDAO preRptDao = PreRptDAO.getInstance();
	private ProjectDAO projDao = ProjectDAO.getInstance();
	private AwardDAO awardDao = AwardDAO.getInstance();
	private CdcfRptDAO cdcfRptDao= CdcfRptDAO.getInstance();
	private PublicationDAO publicationDao = PublicationDAO.getInstance();
	Logger logger = Logger.getLogger(this.getClass().getName());
	
	public List<CdcfRptPeriod> getCdcfPeriodList()
	{
		//if (cdcfPeriodList == null) {
			List<CdcfRptPeriod> tmpCdcfPeriodList = cdcfRptDao.getCdcfRptPeriodList();
			cdcfPeriodList = tmpCdcfPeriodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
			if (!Strings.isNullOrEmpty(selectedRptCat))
			{
				if (selectedRptCat.equals("S")) {
					cdcfPeriodList = tmpCdcfPeriodList.stream().filter(a -> a.getIs_current()).collect(Collectors.toList());
				}
			}
		//}
		return cdcfPeriodList;
	}

	
	public void setCdcfPeriodList(List<CdcfRptPeriod> cdcfPeriodList)
	{
		this.cdcfPeriodList = cdcfPeriodList;
	}
	
	public String getFilteredPeriod()
	{
		return filteredPeriod;
	}

	
	public void setFilteredPeriod(String filteredPeriod)
	{
		this.filteredPeriod = filteredPeriod;
	}
	
	
	//Filter Department
	private List<FacDeptDesc> filterAccessibleDepartments(List<FacDeptDesc> allDepts, List<String> accessibleDepts) {
	    return allDepts.stream()
	                   .filter(dept -> accessibleDepts.contains(dept.getDept_code()))
	                   .collect(Collectors.toList());
	}
	
	private SelectItem createSelectItem(FacDeptDesc dept) {
	    String displayText = dept.getDepartment_name() + " (" + dept.getDept_code() + ")";
	    return new SelectItem(displayText);
	}

	public List<SelectItem> getDeptfilterList() throws SQLException
	{
		
		if (deptfilterList == null) {
			deptfilterList = new ArrayList<SelectItem>();
			List<FacDeptDesc> list = fDao.getDeptDescByEligStaffList();
			List<FacDeptDesc> filterList = new ArrayList<>();
			filterList = list;
			/*accessDeptList = getAccessIconnectDeptList();
			

			//System.out.println("list:" + list);
			//accessDeptList = getAccessDeptList();
			//logger.log(Level.INFO, "accessDeptList:"+accessDeptList);
			
			if (accessDeptList != null) 
				filterList = filterAccessibleDepartments(list,accessDeptList);*/
				//filterList = filterAccessibleDepartments(list,accessDeptList);
				

			List<SelectItem> itemList = new ArrayList<SelectItem>();
			SelectItemGroup itemGroup = new SelectItemGroup(SELECT_PROMPT);
			if (filterList.size() > 0) {
				itemList.add(new SelectItem("All"));
				for (int j = 0; j < filterList.size(); j++) 
					itemList.add(createSelectItem(filterList.get(j)));
			}
			SelectItem[] itemArr = new SelectItem[itemList.size()];
			itemGroup.setSelectItems(itemList.toArray(itemArr));
			
			deptfilterList.add(itemGroup);	
		}
		


		return deptfilterList;
	}


	
	public void setDeptfilterList(List<SelectItem> deptfilterList)
	{
		this.deptfilterList = deptfilterList;
	}

	public String getSelectedDept()
	{
		if (!Strings.isNullOrEmpty(selectedDept)) {
			if (selectedDept.contains("(") && selectedDept.contains(")")) {
				
			    int startIndex 	= selectedDept.indexOf("(") + 1;
		        int endIndex 	= selectedDept.indexOf(")");
		        
				selectedDept = selectedDept.substring(startIndex,endIndex);
				selectedDept = "Academic".equalsIgnoreCase(selectedDept)?"VP(AC)":selectedDept;
				selectedDept = "Research and Development".equalsIgnoreCase(selectedDept)?"VP(RD)":selectedDept;
				//System.out.println(selectedDept);
			}
		}
		return selectedDept;
	}


	
	public void setSelectedDept(String selectedDept)
	{
		this.selectedDept = selectedDept;
	}


	public Date getSelectedStartDate()
	{
		return selectedStartDate;
	}

	
	public void setSelectedStartDate(Date selectedStartDate)
	{
		this.selectedStartDate = selectedStartDate;
	}

	
	public Date getSelectedEndDate()
	{
		return selectedEndDate;
	}

	
	public void setSelectedEndDate(Date selectedEndDate)
	{
		this.selectedEndDate = selectedEndDate;
	}


	
	
	@SuppressWarnings("deprecation")
	public void generateReport (String reportCode) throws ParseException, SQLException
	{
		//Button Reaction
		//getAccessFacDeptList();
		
			try{
				 if (reportCode.equalsIgnoreCase("A2") || 
						 	reportCode.equalsIgnoreCase("A3") ||
						 	reportCode.equalsIgnoreCase("A4")||
						 	reportCode.equalsIgnoreCase("B2") ||
				 			reportCode.equalsIgnoreCase("B3"))
					{	
						 reportFilteringField filter = new reportFilteringField();
						 	if( selectedStartDate != null && selectedEndDate != null ) {
							
									filter.setD_period(Boolean.TRUE);
									filter.setD_from_year(selectedStartDate.getYear()	+	YEAR_ADJUSTMENT);
									filter.setD_from_month(selectedStartDate.getMonth() +	MONTH_ADJUSTMENT);
									filter.setD_to_year(selectedEndDate.getYear()	+	YEAR_ADJUSTMENT);
									filter.setD_to_month(selectedEndDate.getMonth()	+	MONTH_ADJUSTMENT);
						 	}
						 	else 
						 		filter.setD_period(Boolean.FALSE);
						
						
							if (selectedCdcfPeriods != null) {
								
									List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
									int from_y = filterPeriod.get(0).getDate_from().getYear() + 1900;
									int from_m = filterPeriod.get(0).getDate_from().getMonth()+1;
									int to_y = filterPeriod.get(0).getDate_to().getYear() + 1900;
									int to_m = filterPeriod.get(0).getDate_to().getMonth()+1;
									
									filter.setP_period(Boolean.TRUE);
									filter.setP_from_year(from_y);
									filter.setP_from_month(from_m);
									filter.setP_to_year(to_y);
									filter.setP_to_month(to_m);
									filter.setP_id_no(selectedCdcfPeriods);
									
			
									setFilteredPeriod( filterPeriod.get(0).getPeriod_desc());
									if (reportCode.equalsIgnoreCase("A2"))
										createRO1BarModel(filter);
									else if (reportCode.equalsIgnoreCase("A3")) 
										createRO2BarModel(filter,true);
									else if (reportCode.equalsIgnoreCase("A4"))
										createRO2BarModel(filter,false);
									else if (reportCode.equalsIgnoreCase("B2"))
										createB0203BarModel(filter,false);
									else if (reportCode.equalsIgnoreCase("B3"))
										createB0203BarModel(filter,true);
										
										
							}
							else 
								filter.setP_period(Boolean.FALSE);
					}
			}
			catch (Exception e){
				String message = reportCode + " Report is NOT Successfully Generated";
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
				
			}
			finally {
				String message = reportCode + " Report is Successfully Generated";
				FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));	
				isChartGenerated = true;
			}
	}

	
	public BarChartModel getOutputLastThreeZeroBarModel()
	{
		return outputLastThreeZeroBarModel;
	}
	
	public void setOutputLastThreeZeroBarModel(BarChartModel outputLastThreeZeroBarModel)
	{
		this.outputLastThreeZeroBarModel = outputLastThreeZeroBarModel;
	}
	
	
	
	public BarChartModel getFundingIntExtBarModel()
	{
		return fundingIntExtBarModel;
	}


	
	public void setFundingIntExtBarModel(BarChartModel fundingIntExtBarModel)
	{
		this.fundingIntExtBarModel = fundingIntExtBarModel;
	}


	public BarChartModel getOutputLastThreeBarModel()
	{
		return outputLastThreeBarModel;
	}

	
	public void setOutputLastThreeBarModel(BarChartModel outputLastThreeBarModel)
	{
		this.outputLastThreeBarModel = outputLastThreeBarModel;
	}

	public BarChartModel getoutputCountBarModel()
	{
		return outputCountBarModel;
	}

	
	public void setoutputCountBarModel(BarChartModel outputCountBarModel)
	{
		this.outputCountBarModel = outputCountBarModel;
	}
	
	public Integer getSelectedCdcfPeriods()
	{
		return selectedCdcfPeriods;
	}

	
	public void setSelectedCdcfPeriods(Integer selectedCdcfPeriods)
	{
		this.selectedCdcfPeriods = selectedCdcfPeriods;
	}
	
	public String getSelectedRptnote() throws SQLException
	{
		if (!Strings.isNullOrEmpty(selectedRpt))
		{
			PreRpt tmp = preRptDao.getPreRpt(selectedRpt);
			selectedRptnote = tmp.getRpt_note();
		}else {
			selectedRptnote = "";
		}
		return selectedRptnote;
	}

	
	public void setSelectedRptnote(String selectedRptnote)
	{
		this.selectedRptnote = selectedRptnote;
	}
	
	public String getPreRptType()
	{
		String result = "";
		if (getPreRptList() != null && !Strings.isNullOrEmpty(getSelectedRpt())) {

			List<PreRpt> tmp = preRptList.stream()
								.filter(y -> y.getRpt_code().equals(selectedRpt))
								.collect(Collectors.toList());
			if (tmp.size() > 0) {
				result = tmp.get(0).getRpt_type();
			}
		}
		return result;
	}
	
	public void createRO2BarModel(reportFilteringField filter, Boolean a3) throws ParseException, SQLException {
		
		
		String labelName = "No. of academic staff with refereed item <1 in 3 years";
		String whereCause = " < 1 ";
		
		if(a3) {
			outputLastThreeBarModel = new BarChartModel();
		}
		else {
			outputLastThreeZeroBarModel = new BarChartModel();
			labelName = "No. of academic staff with 0 refereed item in 3 years";
			whereCause = " = 0 ";
		}
		
		ChartData data = new ChartData();
		
		//No. of academic Staff with refereed item <1 in 3 years.
		BarChartDataSet barDataSet = new BarChartDataSet();
		barDataSet.setLabel(labelName);
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();

        
        //Remove total
        
        List<String> label = getAllAccessFacDeptList().stream().filter(a-> a != "Total").collect(Collectors.toList());
        List<String> lab_A2_A3 = new ArrayList<>();   
        
     
       // System.out.println(lab_A2_A3);
        
        last3YearCountList = AppPeriodReportDAO.lastYearCount_Dept(whereCause,filter);
        
        if ( lab_A2_A3 != null ) {
        	for(int i= 0 ;  i < label.size() ; i++) {
        		
        		int deptCount = 0;
        		int index = i;
        		
        		List<last3YearCount> target_count = null;
        		target_count = last3YearCountList.stream().filter(item-> item.getDept_code().equals(label.get(index))).collect(Collectors.toList());
        		/*
        		if (target_count.size() == 0 ) {
        			dataVal.add(deptCount);
        		}
        		else {

	        		deptCount = Integer.parseInt(target_count.get(0).getCount_staff());
        			dataVal.add(deptCount);
        		}*/
         		if (target_count.size() != 0 ) {
         			deptCount = Integer.parseInt(target_count.get(0).getCount_staff());
        		}
         		lab_A2_A3.add(label.get(i));
         		dataVal.add(deptCount);
        		
        		
        	}
        }
        
        barDataSet.setData(dataVal);
        
        data.addChartDataSet(barDataSet);
 
        data.setLabels(lab_A2_A3);
        
        //SET DATA
        if(a3) 
        	outputLastThreeBarModel.setData(data);
        else 
        	outputLastThreeZeroBarModel.setData(data);
        
        
        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        CartesianLinearTicks ticks = new CartesianLinearTicks();
        
        
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        linearAxes.setTicks(ticks);
        
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        

        //options.setScales(cScales);
        
        Legend legend = new Legend();
        legend.setDisplay(true);
        legend.setPosition("top");
        LegendLabel legendLabels = new LegendLabel();
        legendLabels.setFontStyle("italic");
        legendLabels.setFontColor("#2980B9");
        legendLabels.setFontSize(24);
        legend.setLabels(legendLabels);
        options.setLegend(legend); 
        
        Animation animation = new Animation();
        animation.setDuration(0);
        options.setAnimation(animation);

        if (a3) {
        	outputLastThreeBarModel.setOptions(options);
    		outputLastThreeBarModel.setExtender("barChartExtender_2");
        }
        else {
        	outputLastThreeZeroBarModel.setOptions(options);
        	outputLastThreeZeroBarModel.setExtender("barChartExtender_a4");
        }
	}
	
	
	public void createRO1BarModel(reportFilteringField filter) throws ParseException, SQLException {
		
		outputCountBarModel = new BarChartModel();
		ChartData data = new ChartData();

		 //below 1 item
		BarChartDataSet barDataSet = new BarChartDataSet();
		barDataSet.setLabel("below 1 item");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        
        //1 item or above
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("1 item or above");
        barDataSet2.setBackgroundColor("#0F9D58");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        List<String> labels = getAllAccessFacDeptList();
        List<String> formatted_labels = new ArrayList<>();
        
        labels.add("Total");
        
        
        int below1Total = 0;
  		int above1Total = 0;
  		
  		deptLbaelCountList =  AppPeriodReportDAO.getDeptLabelCount(filter);
  		//System.out.println("LABEL COUNT:" + deptLbaelCountList );
  		
  		if (labels != null) {
  			for(int i= 0; i< labels.size() && labels.get(i) != "Total" ; i++) {
  				int below1Dept = 0;
  				int above1Dept = 0;
  				int index = i;
  				
  				
  				List<DeptLabelCount> target_count = null;
  				
  				target_count = deptLbaelCountList.stream().filter(item-> item.getDept_code().equals(labels.get(index))).collect(Collectors.toList());
  				
  				
				below1Dept = Integer.parseInt(target_count.stream().filter(item -> item.getLabel().equals("below 1 item")).collect(Collectors.toList()).get(0).getCount_staff());
				above1Dept = Integer.parseInt(target_count.stream().filter(item -> item.getLabel().equals("1 item or above")).collect(Collectors.toList()).get(0).getCount_staff());
					
  				
  				//System.out.println( below1Dept+"||"+above1Dept);
  				
  				int base = below1Dept + above1Dept;
  				if(base != 0) {
					formatted_labels.add(labels.get(index) + "(" + below1Dept + " : " + above1Dept + ")");	
					dataVal.add(  (int) roundAvoid(below1Dept * 100.0 / base, 0 ) );
					dataVal2.add( (int) roundAvoid(above1Dept * 100.0 / base, 0 ) );
  				}
  			
  				below1Total += below1Dept;
  				above1Total  += above1Dept;
  				
  				
  			}
  		}
  		int base = below1Total + above1Total;
  		
		dataVal.add(  (int) roundAvoid (below1Total * 100.0  / base,0) );
		dataVal2.add( (int) roundAvoid (above1Total * 100.0  / base,0) );
		formatted_labels.add("Total" + "(" + below1Total + " : " + above1Total + ")");

        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);
        
        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);
 
        data.setLabels(formatted_labels);
        outputCountBarModel.setData(data);
       
        
      //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        CartesianLinearTicks ticks = new CartesianLinearTicks();
        
        
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        linearAxes.setTicks(ticks);
        
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        

        //options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(true);
        title.setText("Total of Yes: " + below1Total + ", Total of No: " + above1Total);
        options.setTitle(title);
        
        Legend legend = new Legend();
        legend.setDisplay(true);
        legend.setPosition("top");
        LegendLabel legendLabels = new LegendLabel();
        legendLabels.setFontStyle("italic");
        legendLabels.setFontColor("#2980B9");
        legendLabels.setFontSize(24);
        legend.setLabels(legendLabels);
        options.setLegend(legend);
        
        
        Animation animation = new Animation();
        animation.setDuration(0);
        options.setAnimation(animation);
        
        outputCountBarModel.setOptions(options);
        outputCountBarModel.setExtender("barChartExtender");
    }
	
	
	
	public void createB0203BarModel(reportFilteringField filter,boolean b3) throws ParseException, SQLException {
			
			try {
			
			fundingIntExtBarModel = new BarChartModel();
			ChartData data = new ChartData();
			String [] labelStr = {"External Funding for R&D Porjects","Internal Funding for R&D Porjects"};
			
			if(b3) {
				labelStr[0] = "General Research Fund";
				labelStr[1] = "Early Career Scheme";
			}
	
			 //label 1
			BarChartDataSet barDataSet = new BarChartDataSet();
			barDataSet.setLabel(labelStr[0]);
	        barDataSet.setBackgroundColor("#F4B400");
	        barDataSet.setStack("Stack 0");
	        List<Number> dataVal = new ArrayList<>();     
	        
	        //label 2
	        BarChartDataSet barDataSet2 = new BarChartDataSet();
	        barDataSet2.setLabel(labelStr[1]);
	        barDataSet2.setBackgroundColor("#0F9D58");
	        barDataSet2.setStack("Stack 1");
	        List<Number> dataVal2 = new ArrayList<>();
	        
	        //When B3 is created
			 //label3 
			BarChartDataSet barDataSet3 = new BarChartDataSet();
			barDataSet3.setLabel("Public Policy Research Funding Scheme");
			barDataSet3.setBackgroundColor("#8998cd");
			barDataSet3.setStack("Stack 2");
	        List<Number> dataVal3 = new ArrayList<>();     
	        	

	        
	        List<String> labels =  getAllAccessFacDeptList().stream().filter(a-> a != "Total").collect(Collectors.toList());
	        
	        //System.out.println(labels);
	        
	  		
	        deptFundingIntExtList =  AppPeriodReportDAO.deptFunding_Int_Ext(filter,true,getSelectedCdcfPeriods());
	        
	        
	        String [] filterStr = {"EXT","INT"};
	        
	        if(b3) {
	        	filterStr[0] = "GRF";
	        	filterStr[1] = "ECS";
	        	deptFundingIntExtList =  AppPeriodReportDAO.deptFunding_Int_Ext(filter,false,getSelectedCdcfPeriods());
	        }
	        
	      
	  		
	  		
	  		if (labels != null) {
	  			for(int i= 0; i< labels.size(); i++) {
	  				Double internal = 0.0;
	  				Double external = 0.0;
	  				Double ppr_c = 0.0;
	  				Double pprs_c = 0.0;
	  				
	  				int index = i;
	  				
	  				
	  				List<deptFundingIntExt> amountList = null;
	  				
	  				amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index)) 
	  								&& filterStr[0].equals(item.getFund_type())).collect(Collectors.toList());
	  				
	  				//System.out.println(amountList);
	  				
	  				if (! amountList.isEmpty())
	  					external = Double.parseDouble(amountList.get(0).getAmount());

	  				
	  				amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index)) 
								&& filterStr[1].equals(item.getFund_type())).collect(Collectors.toList());
	  				
	  				if (! amountList.isEmpty())
	  					internal = Double.parseDouble(amountList.get(0).getAmount());
	  				
	  				//System.out.println("External: "+external + "|| internal: " +  internal);
	  				
	  				dataVal.add(external);
	  				dataVal2.add(internal);

	  				
	  				if(b3) {
	  					
	  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index)) 
								&& "PPR".equals(item.getFund_type())).collect(Collectors.toList());
	  					if (! amountList.isEmpty())
	  						ppr_c 	 = Double.parseDouble(amountList.get(0).getAmount());

		  				dataVal3.add(ppr_c);
	  				}

	  			}
	  		}
	  		
	
	        barDataSet.setData(dataVal);
	        barDataSet2.setData(dataVal2);
	        
	        data.addChartDataSet(barDataSet);
	        data.addChartDataSet(barDataSet2);
	        
	        
	        
	        if(b3) {
	        	barDataSet3.setData(dataVal3);
		        data.addChartDataSet(barDataSet3);
	        }
	        
	        data.setLabels(labels);
	        
	        
	 
	       
	        fundingIntExtBarModel.setData(data);
	       
	        
	      //Options
	        BarChartOptions options = new BarChartOptions();
	        CartesianScales cScales = new CartesianScales();
	        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
	        CartesianLinearTicks ticks = new CartesianLinearTicks();
	        
	        
	        linearAxes.setStacked(true);
	        linearAxes.setOffset(true);
	        linearAxes.setTicks(ticks);
	        
	        cScales.addXAxesData(linearAxes);
	        cScales.addYAxesData(linearAxes);
	
	        //options.setScales(cScales);
	        
	        Legend legend = new Legend();
	        legend.setDisplay(true);
	        legend.setPosition("top");
	        LegendLabel legendLabels = new LegendLabel();
	        legendLabels.setFontStyle("italic");
	        legendLabels.setFontColor("#2980B9");
	        legendLabels.setFontSize(24);
	        legend.setLabels(legendLabels);
	        options.setLegend(legend);
	        
	        
	        Animation animation = new Animation();
	        animation.setDuration(0);
	        options.setAnimation(animation);
	        
	        fundingIntExtBarModel.setOptions(options);
	      	fundingIntExtBarModel.setExtender("barChartExtender_B02");
			
			
	    }	catch (Exception e)
		{
			
	    	String message = " B2 B3 Diagram is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
	}

	
	public List<last3YearCount> getLast3YearCountList()
	{
		return last3YearCountList;
	}

	public void setLast3YearCountList(List<last3YearCount> last3YearCountList)
	{
		this.last3YearCountList = last3YearCountList;
	}
	
	
	public List<DeptLabelCount> getDeptLbaelCountList()
	{
		return deptLbaelCountList;
	}


	
	public void setDeptLbaelCountList(List<DeptLabelCount> deptLbaelCountList)
	{
		this.deptLbaelCountList = deptLbaelCountList;
	}


	public List<PreRpt> getPreRptList()
	{
		if (preRptList == null) {
			preRptList =  preRptDao.getPreRptList("1");
		}
		return preRptList;
	}

	
	public void setPreRptList(List<PreRpt> preRptList)
	{
		this.preRptList = preRptList;
	}

	
	
	public List<PreRpt> getAllPreRptList()
	{
		if (allPreRptList == null) {
			allPreRptList =  preRptDao.getPreRptList(null);
		}
		return allPreRptList;
	}


	
	public void setAllPreRptList(List<PreRpt> allPreRptList)
	{
		this.allPreRptList = allPreRptList;
	}


	public List<PreRpt> getPreRptListByCat()
	{
		if (getPreRptList() != null) {
			if (!Strings.isNullOrEmpty(getSelectedRptCat())) {
				if (getIsRdoAdmin()) {
					preRptListByCat = preRptList.stream()
							.filter(y -> y.getRpt_cat().equals(selectedRptCat) && y.getEnabled_flag().equals("1"))
							.collect(Collectors.toList());
				}else {
					preRptListByCat = preRptList.stream()
							.filter(y -> y.getRpt_cat().equals(selectedRptCat) && y.getEnabled_flag().equals("1") && y.getIs_rdo().equals("0"))
							.collect(Collectors.toList());
				}
			}
			
			//Comment first for fixing A1 A2 A3 A4 Notes
			
			/*if (preRptListByCat.size() > 0 && Strings.isNullOrEmpty(getSelectedRpt())){
				selectedRpt = preRptListByCat.get(0).getRpt_code();
			}else {
				System.out.println("Pass1");
				selectedRpt = "";
			}*/
		}else {
			selectedRpt = "";
		}
		return preRptListByCat;
	}


	
	public void setPreRptListByCat(List<PreRpt> preRptListByCat)
	{
		this.preRptListByCat = preRptListByCat;
	}


	public List<PreRptCat> getPreRptCatList()
	{
		if (preRptCatList == null) {
			String isRdo = (getIsRdoAdmin())?"1":"0";
			preRptCatList = preRptDao.getPreRptCatList("1", isRdo);
		}
		return preRptCatList;
	}


	
	public void setPreRptCatList(List<PreRptCat> preRptCatList)
	{
		this.preRptCatList = preRptCatList;
	}


	
	public List<PreRptCat> getAllPreRptCatList()
	{
		if (allPreRptCatList == null) {
			allPreRptCatList = preRptDao.getPreRptCatList(null, "1");
		}
		return allPreRptCatList;
	}


	
	public void setAllPreRptCatList(List<PreRptCat> allPreRptCatList)
	{
		this.allPreRptCatList = allPreRptCatList;
	}


	public String getSelectedRpt()
		
	{
		return selectedRpt;
	}


	
	public void setSelectedRpt(String selectedRpt)
	{
		this.selectedRpt = selectedRpt;
	}


	
	
	public PreRptCat getSelectedPreRptCat()
	{
		return selectedPreRptCat;
	}


	
	public void setSelectedPreRptCat(PreRptCat selectedPreRptCat)
	{
		this.selectedPreRptCat = selectedPreRptCat;
	}


	
	public PreRptCat getRemovePreRptCat()
	{
		return removePreRptCat;
	}


	
	public void setRemovePreRptCat(PreRptCat removePreRptCat)
	{
		this.removePreRptCat = removePreRptCat;
	}


	public String getSelectedRptCat()
	{
		if (Strings.isNullOrEmpty(selectedRptCat)) {
			if (getPreRptCatList()!= null) {
				if (preRptCatList.size()>0) {
					selectedRptCat = preRptCatList.get(0).getRpt_cat();
				}
			}
		}
		return selectedRptCat;
	}


	
	public void setSelectedRptCat(String selectedRptCat)
	{
		this.selectedRptCat = selectedRptCat;
	}

	public String getRptCatDesc(String cat)
	{
		String result = "";
		if (cat != null) {
			PreRptCat c = preRptDao.getPreRptCat(cat);
			if (c != null) {
				result = c.getRpt_cat_desc();
			}
		}
		return result;
	}

	
	public void exportReport(String reportCode) throws SQLException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();	
		getAccessFacDeptList();
		try 
		{
    		String reportPeriod = "";
    		List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
    		if (filterPeriod != null) {
    			reportPeriod = filterPeriod.get(0).getChart_desc_2()+"_";
    		}
			
			if(reportCode.equals("CIRD_RP_002")) {
				createProj002Docx(reportPeriod);
				return;
			}
			Workbook wb = null;
    		wb = new XSSFWorkbook();
    		

    		//File name define
    		DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = "";
			
			reportFilteringField filter = new reportFilteringField();
			String periodDesc = "";
			if (selectedCdcfPeriods != null) {
				
				int from_y = filterPeriod.get(0).getDate_from().getYear() + 1900;
				int from_m = filterPeriod.get(0).getDate_from().getMonth()+1;
				int to_y = filterPeriod.get(0).getDate_to().getYear() + 1900;
				int to_m = filterPeriod.get(0).getDate_to().getMonth()+1;
				periodDesc = filterPeriod.get(0).getPeriod_desc();
				
				filter.setP_period(Boolean.TRUE);
				filter.setP_from_year(from_y);
				filter.setP_from_month(from_m);
				filter.setP_to_year(to_y);
				filter.setP_to_month(to_m);
				filter.setP_id_no(selectedCdcfPeriods);
			}
			else {
				filter.setP_period(Boolean.FALSE);
			}
			if( selectedStartDate != null && selectedEndDate != null ) {
				
				filter.setD_period(Boolean.TRUE);
				filter.setD_from_year(selectedStartDate.getYear()+1900);
				filter.setD_from_month(selectedStartDate.getMonth() +1);
				filter.setD_to_year(selectedEndDate.getYear()+1900);
				filter.setD_to_month(selectedEndDate.getMonth()+1);		
			}
			else {
				filter.setD_period(Boolean.FALSE);		
			}
			PreRpt preRpt = preRptDao.getPreRpt(reportCode);
			fileName = preRpt.getRpt_file_name() + reportPeriod+ dateFormat.format(new Date()) + ".xlsx";
			switch (reportCode) {
			    case "A1":
			    	createA1DataSheet(wb, filter, periodDesc);
			        break;
			    case "A2":
			    	createA2DataSheet(wb, filter);
			    	break;
			    case "A5":
			    	createA5DataSheet(wb, filter);
			        break;   
			    case "A6":
			    	createA6DataSheet(wb, filter);
			        break;
			    case "A7":
			    	createA7DataSheet(wb, filter);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;   
			    case "B1":
			    	createB1DataSheet(wb);
			        break;
			    case "B2":
			    	createB2B3DataSheet(wb,false,filter);
			        break;
			    case "B3":
			    	createB2B3DataSheet(wb,true,filter);
			        break;			        
			    case "B4":
			    	createB4DataSheet(wb,filter);
			        break;
			    case "CHRM_AWARD":
			    	createChrmAwardDataSheet(wb);
			        break;   
			    case "CHRM_OUTPUT":
			    	createChrmAcadProfDataSheet(wb);
			        break;   
			    case "CIRD_RP_001":
			    	createChrm001DataSheet(wb);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;  
			    case "CIRD_RP_004":
			    	createChrm004DataSheet(wb);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;
			    case "CIRD_RP_009":
			    	createChrm009DataSheet(wb);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;
			    case "CIRD_RP_011":
			    	createChrm011DataSheet(wb);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;
			    case "CIRD_RP_012":
			    	createChrm012DataSheet(wb);
			        break;
			    case "CIRD_RP_015":
			    	createChrm015DataSheet(wb);
			        break;
			    case "CRD_1":
			    	createCrd001DataSheet(wb);
			        break;
			    case "CRD_2":
			    	createCrd002DataSheet(wb);
			        break;
			    case "CRD_3":
			    	createCrd003DataSheet(wb);
			        break;
			    case "CRD_4":
			    	createCrd004DataSheet(wb);
			        break;
			    case "CHRM_PROJECT":
			    	createChrmProjDataSheet(wb);
			        break;   
			    case "CIRD_RP_005":
			    	createChrm005DataSheet(wb);
			        break;   
			    case "CIRD_RP_010":
			    	createChrm010DataSheet(wb);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;   
			    case "CIRD_RP_013":
			    	createChrm013DataSheet(wb);
			    	fileName = preRpt.getRpt_file_name() + reportPeriod +selectedDept+"_"+dateFormat.format(new Date())+".xlsx";
			        break;   
			    case "CIRD_RP_014":
			    	createChrm014DataSheet(wb);
			        break;   
			    case "CIRD_RP_016":
			    	createChrm016DataSheet(wb);
			        break;   
			    case "AMIS_01":
			    	createAmis001DataSheet(wb);
			        break;   
			    case "AMIS_02":
			    	createAmis002DataSheet(wb);
			        break;   
			}
			

    		// Get the byte array of the Workbook
	    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			eCtx.responseReset();

			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");		        
			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

			// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
			//setPrimeFacesDownloadCompleted("exportRptBtn");
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
		
			fCtx.responseComplete();

		}
		catch (IOException e) 
    	{
			
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		catch (Exception e)
		{
			
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		finally {
			String message = reportCode + " Report is Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));	
		}
	}
	
	//Export A2 - A4 Reports in PNG
	
	public void exportChart() throws Exception {
		
		String reportPeriod = "";
		String fileName = null;
		DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
		
		if(selectedRpt == null) {
			String message = "You have not select any report.";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			return;
		}
		
		
		List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
		if (filterPeriod != null) {
			reportPeriod = filterPeriod.get(0).getChart_desc_2()+"_";
		}

		fileName = selectedRpt + "_" + reportPeriod + dateFormat.format(new Date()) + ".png";

		
		try {
			if(chartImageBase64 != null) {
				String base64 = chartImageBase64.split(",")[1];
				//System.out.println(chartImageBase64);
				byte[] imageBytes = javax.xml.bind.DatatypeConverter.parseBase64Binary(base64);
				
				FacesContext fCtx = FacesContext.getCurrentInstance();
				ExternalContext eCtx = fCtx.getExternalContext();
				
				eCtx.responseReset();
	
				eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
				eCtx.setResponseHeader("Expires", "-1");
				eCtx.setResponseHeader("Pragma", "private");		        
				eCtx.setResponseContentType(new Tika().detect(fileName));
				eCtx.setResponseContentLength(imageBytes.length);
				eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");
				
				OutputStream os = eCtx.getResponseOutputStream();
				os.write(imageBytes);
				//System.out.println("Testing");
				os.close();
				fCtx.responseComplete();
			}
		}catch (Exception e){
			
		}finally {
			selectedRpt = null;
		}
	}
	
	private void setPrimeFacesDownloadCompleted(String string)
	{
		// TODO Auto-generated method stub
		
	}


	public void createA1DataSheet(Workbook wb,reportFilteringField filter, String periodDesc) throws SQLException, ParseException
	{

		String sheetName = "App A1";
		Sheet sheet = wb.createSheet(sheetName);
		String [] titleName = {"Common Data Collection Format",
								"Number of Refereed Research Output by Departments for "+ periodDesc+" (Academic Staff Only)",
								"Research Output of \"Scholarly Books, Monographs and Chapters\", \"Journal Publications\" and \"Non-Traditional Outputs#\""};
		
		String [] facultyName = {"Faculty of Liberal Arts and Social Sciences (FLASS)",
				"Faculty of Education and Human Development (FEHD)",
				"Faculty of Humanities (FHM)"}; 
		
		//if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
		

			
		
		KtFormCDCFReportGroup reportGroup = new KtFormCDCFReportGroup();
		
		reportGroup.init(wb);
		
		int startIndex = 2;
		int endIndex = reportGroup.getFlassDeptNames().size() * 2 - 1;
		
		
		
		org.apache.poi.ss.usermodel.Font font=  wb.createFont();
		font.setBold(true);
		
		CellStyle headerStyle = wb.createCellStyle();
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		headerStyle.setFont(font);
		
		Row row4 = sheet.createRow(4);
		Row row5 = sheet.createRow(5);
		
		//Header
		for (int i = 0 ; i <titleName.length;i++) {		
			Row h_row = sheet.createRow(i);
			Cell cell = h_row.createCell(0);
			cell.setCellStyle(headerStyle);
			cell.setCellValue(titleName[i]);
			sheet.addMergedRegion(new CellRangeAddress(h_row.getRowNum(),h_row.getRowNum() , 0 , 40));
		}
		
		
		reportGroup.appendCollegeCollumns(row4,row5, startIndex,endIndex,filter ,sheetName,facultyName[0],"FLASS Total",reportGroup.getFlassDeptNames());
		
		
		startIndex = reportGroup.getFlassDeptNames().size() * 2 + 2 + 2;
		endIndex =  reportGroup.getFehdDeptNames().size() * 2 -1;

		
		reportGroup.appendCollegeCollumns(row4,row5,startIndex,endIndex,filter ,sheetName,facultyName[1],"FEHD Total",reportGroup.getFehdDeptNames());
		
		startIndex = (reportGroup.getFlassDeptNames().size() + reportGroup.getFehdDeptNames().size() + 3 ) * 2;
		endIndex =  reportGroup.getFhmDeptNames().size() * 2 -1;
		
		reportGroup.appendCollegeCollumns(row4,row5,startIndex,endIndex,filter ,sheetName,facultyName[2],"FHM Total",reportGroup.getFhmDeptNames());
		
		
		if (getIsRdoAdmin()){
			sheetName = "ALL";
			sheet = wb.createSheet(sheetName);
			String [] allCollName = {"Dept","Surname","Other name", 
								"Ranking","Total No. of refereed items (Scholarly Books, Monographs and Chapters & Journal Publications)",
								"Category" };
			reportGroup.appendDataCollumns_all(sheetName, filter, allCollName);
		}
	}
	
	public static double roundAvoid(double value, int places) {
		BigDecimal bd = new BigDecimal(Double.toString(value));
		bd = bd.setScale(places, RoundingMode.HALF_UP);
	    return bd.doubleValue();
	}
	
	public void createA5DataSheet(Workbook wb,reportFilteringField filter) throws SQLException, ParseException
	{
		List <String> deptestList  =  getAccessFacDeptList().stream().filter(a-> a != "Total").collect(Collectors.toList());

		
		try {
			
			//Create the header row
			String sheetName = null;
			Sheet sheet = null;
			Row row1 = null;
			KtFormCDCFinalReportGroup reportGroup = new KtFormCDCFinalReportGroup();	
			
			
			if (getIsRdoAdmin()) {
				sheetName = "ALL";
				sheet = wb.createSheet(sheetName);
				row1 = sheet.createRow(0);
				reportGroup = new KtFormCDCFinalReportGroup();
				reportGroup.init(wb,filter.getP_from_year());
				
				reportGroup.appendDataCollumns_all(sheetName,filter.getP_to_year(),selectedCdcfPeriods);
			}
			
			
			
			for(int i = 0 ;i<3;i++) {
				String facName = "FLASS";
				
				if (i == 1 )
					facName = "FEHD";
				else if ( i ==2 )
					facName = "FHM";
				
				if (! getAvilDeptList(deptestList,facName).isEmpty()) {
					for(int j = 0 ; j< getAvilDeptList(deptestList,facName).size() ; j++) {
						sheetName = getAvilDeptList(deptestList,facName).get(j);
						sheet = wb.createSheet(sheetName);
						row1 = sheet.createRow(0);
						reportGroup = new KtFormCDCFinalReportGroup();
						reportGroup.init(wb,filter.getP_from_year());
						reportGroup.appendDataCollumns_Dept(sheet, facName,getAvilDeptList(deptestList,facName).get(j),filter.getP_to_year(),selectedCdcfPeriods);
					}
				}
				
			}
			
			
		}
		catch (Exception e) {
			String message = " A5 Report is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		
	}
	
	
	
	public void createB4DataSheet(Workbook wb,reportFilteringField filter) throws SQLException, ParseException
	{

		List <String> deptestList  =  getAccessFacDeptList().stream().filter(a-> a != "Total").collect(Collectors.toList());

		
		try {
			
			
			//Create the header row
			String sheetName = null;
			Sheet sheet = null;
			Row row1 = null;
			KtFormProjStaffReportGroup reportGroup = new KtFormProjStaffReportGroup();	
			
			String header_year = filter.getP_from_year()+ "/"+filter.getP_to_year();
			
			
		    String sDate1="01/"+filter.getP_from_month()+"/"+filter.getP_from_year();  
		    Date from_period =new SimpleDateFormat("dd/M/yyyy").parse(sDate1);  
		    String pattern = "d MMMM yyyy";
		    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
		    String date1 = simpleDateFormat.format(from_period);
		    
		    String sDate2="30/"+filter.getP_to_month()+"/"+filter.getP_to_year();  
		    Date to_period = new SimpleDateFormat("dd/M/yyyy").parse(sDate2);  
		    String date2 = simpleDateFormat.format(to_period);
		    
		    
		    String footerPeriod= date1 + " to "+ date2;
		    

			
			if (getIsRdoAdmin()) {
				sheetName = "ALL";
				sheet = wb.createSheet(sheetName);
				row1 = sheet.createRow(0);
				reportGroup = new KtFormProjStaffReportGroup();
				reportGroup.init(wb,filter.getP_to_year());
				reportGroup.appendCollegeData(sheetName,header_year,"",filter,true,footerPeriod);

			}

			
			for(int i = 0 ;i<3;i++) {
				String facName = "FLASS";
				
				if (i == 1 )
					facName = "FEHD";
				else if ( i ==2 )
					facName = "FHM";
				
				if (! getAvilDeptList(deptestList,facName).isEmpty()) {
					for (int j =0; j<getAvilDeptList(deptestList,facName).size() ; j++) {
						sheetName = getAvilDeptList(deptestList,facName).get(j);
						sheet = wb.createSheet(sheetName);
						//System.out.println(sheetName);
						row1 = sheet.createRow(0);
						reportGroup = new KtFormProjStaffReportGroup();
						reportGroup.init(wb,filter.getP_to_year());
						reportGroup.appendCollegeData(sheetName,header_year,getAvilDeptList(deptestList,facName).get(j),filter,false,footerPeriod);
					}
				}
			}
			
		
		}
		catch (Exception e) {
			String message = " B4 Report is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		
	}
	
	public List <String> getAvilDeptList (List <String> i_access_dept, String College) throws SQLException{
		
		List <String> default_dept_list =  AppPeriodReportDAO.getFacultyDept(College);
		
		return default_dept_list.stream().filter(x->i_access_dept.contains(x)).collect(Collectors.toList());
	}
	
	public void createA6DataSheet(Workbook wb,reportFilteringField filter) throws SQLException, ParseException
	{

		List <String> deptestList  =  getAccessFacDeptList().stream().filter(a-> a != "Total").collect(Collectors.toList());

		try {
			
			String sheetName = null;
			Sheet sheet = null;
			KtFormCDCFSummaryGroup reportGroup = new KtFormCDCFSummaryGroup();
			
			String header_year = filter.getP_from_year()+ "/"+filter.getP_to_year();
			
			
			
			for (int i =0; i <3;i++) {
				
				String college = "FLASS";
				if (i == 1 )
					college = "FEHD";
				else if (i == 2 )
					college = "FHM";
				
				if (! getAvilDeptList(deptestList,college).isEmpty()) {
					for (int j =0; j<getAvilDeptList(deptestList,college).size() ; j++) {
						sheetName = getAvilDeptList(deptestList,college).get(j);
						
						sheet = wb.createSheet(sheetName);
						reportGroup = new KtFormCDCFSummaryGroup();
						reportGroup.init(wb,filter.getP_to_year());
						reportGroup.appendDataColumn(sheetName,header_year,getAvilDeptList(deptestList,college).get(j),filter,false);
					}
				}	
			}

			if (getIsRdoAdmin()) {
				sheetName = "ALL";
				sheet = wb.createSheet(sheetName);
				Row row1 = sheet.createRow(0);
				reportGroup = new KtFormCDCFSummaryGroup();
				reportGroup.init(wb,filter.getP_to_year());
				reportGroup.appendDataColumn(sheetName,header_year,"",filter,true);
			}
			
		}
		catch (Exception e) {
			String message = " A6 Report is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		
	}
	
	public void createA7DataSheet(Workbook wb, reportFilteringField filter) throws SQLException, ParseException
	{
		
		try {

				
				KtFormDeptSummaryGroup reportGroup = new KtFormDeptSummaryGroup();
				reportGroup.init(wb);
				
				//System.out.println("Dept:" + getSelectedDept());
				
				List<staffNumberName> staffDeptList = AppPeriodReportDAO.getStaffName(getSelectedDept());
				
				//System.out.println("staffDeptList:" + staffDeptList);
				
				String deptHeader  = getSelectedDept().equals("All")? "All" : AppPeriodReportDAO.getDeptDesc("", getSelectedDept(), true, true).get(0).getDepartment_name();
				
				//System.out.println("deptHeader:" + deptHeader);
				
				for ( int i = 0; i < staffDeptList.size();i++) {

					String staffName = staffDeptList.get(i).getFull_name();
					Sheet sheet = wb.createSheet(staffName);
					sheet.setColumnWidth(0, 8*256);
					sheet.setColumnWidth(1, 60*256);
					sheet.setColumnWidth(2, 15*256);
					sheet.setColumnWidth(3, 20*256);
					sheet.setColumnWidth(4, 15*256);

					reportGroup.appendDataHeader(wb,sheet,getSelectedDept(),deptHeader,staffDeptList.get(i),filter);
					
				}
				

				
		}
		catch (Exception e) {
			String message = " A7 Report is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		
	}

	public void createA2DataSheet(Workbook wb, reportFilteringField filter) throws SQLException, ParseException
	{

	
		List<String> labels = getAllAccessFacDeptList();
		
		try {
				
				String sheet_name = "App A2";

				Sheet sheet = wb.createSheet(sheet_name);
				
				sheet.setColumnWidth(0, 15*256);
				sheet.setColumnWidth(1, 15*256);
				
				for( int i = 0 ; i<labels.size();i++)
					sheet.setColumnWidth(i+2, 10*256);
				sheet.setColumnWidth(labels.size()+2, 10*256);

				String header_year = filter.getP_from_year()+ "/"+filter.getP_to_year();
				
				String curYearDesc = AppPeriodReportDAO.getCdcfRptPeriod(filter.getP_id_no()).get(0).getPeriod_desc();
				
				String [] header = {"Common Data Collection Format",
									"Number of Academic Staff with Refereed Research Output below 1 item and 1 item or above by Departments for " +curYearDesc,
									"Research Output of \"Scholarly Books, Monograph and Chapters\", \"Journal Publication\" and \"Non-Traditional Outputs #\""};
				
				
				
				String [] row_header = {"No of Staff", "Percentage"};
				
				String [] row_header_2 = {"Below 1 item", "1 item or above"};
				
				
				
				//Style
				
				
				org.apache.poi.ss.usermodel.Font font=  wb.createFont();
				font.setBold(true);
				
				CellStyle headerStyle = wb.createCellStyle();
				headerStyle.setFont(font);
				
				CellStyle colHStyle = wb.createCellStyle();
				colHStyle.setAlignment(HorizontalAlignment.CENTER);
				colHStyle.setVerticalAlignment(VerticalAlignment.CENTER);
				colHStyle.setFont(font);
				colHStyle.setBorderBottom(BorderStyle.THIN);
				colHStyle.setBorderLeft(BorderStyle.THIN);
				colHStyle.setBorderRight(BorderStyle.THIN);
				colHStyle.setBorderTop(BorderStyle.THIN);

				CellStyle rowHStyle = wb.createCellStyle();
				rowHStyle.setAlignment(HorizontalAlignment.LEFT);
				rowHStyle.setVerticalAlignment(VerticalAlignment.CENTER);
				rowHStyle.setWrapText(true);
				rowHStyle.setBorderBottom(BorderStyle.THIN);
				rowHStyle.setBorderLeft(BorderStyle.THIN);
				rowHStyle.setBorderRight(BorderStyle.THIN);
				rowHStyle.setBorderTop(BorderStyle.THIN);	
				
				CellStyle dataStyle = wb.createCellStyle();
				dataStyle.setAlignment(HorizontalAlignment.CENTER);
				dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
				dataStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0"));
				dataStyle.setBorderBottom(BorderStyle.THIN);
				dataStyle.setBorderLeft(BorderStyle.THIN);
				dataStyle.setBorderRight(BorderStyle.THIN);
				dataStyle.setBorderTop(BorderStyle.THIN);
			

		        for (int i = 0 ; i<3;i++) {
		        	Row h_row = sheet.createRow(i);
					Cell cell = h_row.createCell(0);
					cell.setCellStyle(headerStyle);
					cell.setCellValue(header[i]);
		        }
		        
		        Row c_h_row = sheet.createRow(4);
		        
		        List<String> lab_A2 =  labels.stream().filter(a-> a != "Total").collect(Collectors.toList());
		        
		        //Column header
		        for (int i = 0 ; i < lab_A2.size();i++) {	
					Cell cell = c_h_row.createCell(i+2);
					cell.setCellStyle(colHStyle);
					cell.setCellValue(lab_A2.get(i));
		        }
		        
		        
		        
		        Cell total_cell = c_h_row.createCell(lab_A2.size()+2);
		        total_cell.setCellStyle(colHStyle);
		        total_cell.setCellValue("TOTAL");
		        
		        
		        //System.out.println(labels);
		        Row d_row = null;
		        List<DeptLabelCount> target_count = null;
		        List<DeptLabelCount> staffCountList =  AppPeriodReportDAO.getDeptLabelCount(filter);
		        int below_total = 0;
		        int above_total = 0;
		        

		        	   for(int i = 0 ;i<4;i++) {

			  				d_row = sheet.createRow(5+i);
							Cell cell ;
							int total_count =0;
							
							for (int j = 0 ; j < lab_A2.size();j++) 
							{
								int index = j;
				  				target_count = staffCountList.stream().filter(x->x.getDept_code().equalsIgnoreCase(lab_A2.get(index))).collect(Collectors.toList());
				  				
				  				double tar_count = 0.0;
				  				
				  				cell = d_row.createCell(j+2);
				  				cell.setCellStyle(dataStyle);
				  				
				  				if (i==0) {
				  					tar_count = Double.parseDouble(target_count.stream().filter(item -> "below 1 item".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff());
				  				}
				  				else if (i==1) {
				  					tar_count = Double.parseDouble(target_count.stream().filter(item -> "1 item or above".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff());
				  				}
				  				else if (i==2) {
				  					tar_count = Double.parseDouble(target_count.stream().filter(item -> "below 1 item".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff()) * 100
				  							/( Double.parseDouble(target_count.stream().filter(item -> "1 item or above".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff())
				  							+  Double.parseDouble(target_count.stream().filter(item -> "below 1 item".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff()) );
				  				}
				  				else if (i ==3) {
				  					tar_count = tar_count = Double.parseDouble(target_count.stream().filter(item -> "1 item or above".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff()) * 100
				  							/( Double.parseDouble(target_count.stream().filter(item -> "1 item or above".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff())
				  							+  Double.parseDouble(target_count.stream().filter(item -> "below 1 item".equals(item.getLabel())).collect(Collectors.toList()).get(0).getCount_staff()) );
				  				}
		
				  				
				  				tar_count = (int) roundAvoid(tar_count,0);
				  				
				  				if ( i <2)
				  					cell.setCellValue(tar_count);
				  				else
				  					cell.setCellValue((int)tar_count+"%");
				  				
				  				total_count += tar_count;
							}
							
							double total_per = 0.0;
							

							
							if (i==0) {
								cell = d_row.createCell(0);
								cell.setCellStyle(rowHStyle);
								cell.setCellValue(row_header[0]);
								
								cell = d_row.createCell(1);
								cell.setCellStyle(rowHStyle);
								cell.setCellValue(row_header_2[0]);
								sheet.addMergedRegion(new CellRangeAddress(d_row.getRowNum(),d_row.getRowNum()+1 , 0 , 0 ));
								
								below_total = total_count;
							}
							else if (i==1) {
								cell = d_row.createCell(0);
								cell.setCellStyle(rowHStyle);
								
								cell = d_row.createCell(1);
								cell.setCellStyle(rowHStyle);
								cell.setCellValue(row_header_2[1]);
								
								above_total = total_count;
							}
							else if (i==2) {
								
								cell = d_row.createCell(0);
								cell.setCellStyle(rowHStyle);
								cell.setCellValue(row_header[1]);
								
								cell = d_row.createCell(1);
								cell.setCellStyle(rowHStyle);
								cell.setCellValue(row_header_2[0]);
								sheet.addMergedRegion(new CellRangeAddress(d_row.getRowNum(),d_row.getRowNum()+1 , 0 , 0 ));
								
								total_per = below_total *100 / (below_total + above_total);
							}
							else {
								
								cell = d_row.createCell(0);
								cell.setCellStyle(rowHStyle);
								
								cell = d_row.createCell(1);
								cell.setCellStyle(rowHStyle);
								cell.setCellValue(row_header_2[1]);
								
								total_per = above_total *100 / (below_total + above_total);
							}
							
							
							cell = d_row.createCell(lab_A2.size()+2);
							cell.setCellStyle(dataStyle);
							if (i==0)
								cell.setCellValue(below_total);
							else if (i==1)
								cell.setCellValue(above_total);
							else
								cell.setCellValue((int) roundAvoid(total_per,0)+"%");
								
				        }
		        	   
		        	   
		        //Footer 
		        d_row = sheet.createRow(10);
		        Cell footer = d_row.createCell(0);
		        footer.setCellValue("# The non-traditional outputs of CCA are included for consideration.");
		        
		}
		catch (Exception e) {
			String message = " A2 Report is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		
	}
	
	public void createB2B3DataSheet(Workbook wb,boolean b3,reportFilteringField filter) throws SQLException, ParseException
	{

	
		List<String> labels = getAllAccessFacDeptList().stream().filter(a-> a != "Total").collect(Collectors.toList());
		
		try {
				
				String sheet_name = "App B2";
				if (b3)
					sheet_name = "App B3";

				Sheet sheet = wb.createSheet(sheet_name);
				sheet.setColumnWidth(0, 22*256);
				
				for( int i = 0 ; i<labels.size();i++)
					sheet.setColumnWidth(i+1, 15*256);
				sheet.setColumnWidth(labels.size()+1, 15*256);

				String header_year = filter.getP_from_year()+ "/"+filter.getP_to_year();
				
				
				String [] header = {"Common Data Collection Format",
									"Amount of Research and Development Projects Funded by Internal and External Funding",
									"by Departments for "+header_year+" (Academic Staff Only)"};
				
				if(b3) {
					header[1] = "Amount of Current Research Projects Funded by General Research Fund, Early Career Scheme and";
					header[2] = "Public Policy Research Funding Scheme by Departments for " + header_year +" (Academic Staff Only)";
				}
				
				
				String [] row_header = {"External Funding for R&D Projects",
										"Internal Funding for R&D Projects",
										"Grand Total","No. of academic staff","Average Funding per academic staff"};
				
				String [] row_header_2 = {"General Research Fund",
										  "Early Career Scheme",
										  "Public Policy Research Funding Scheme",
										  "Grand Total","No. of academic staff","Average Funding per academic staff"};
				
				
				
				//Style
				
				
				org.apache.poi.ss.usermodel.Font font=  wb.createFont();
				font.setBold(true);
				
				CellStyle headerStyle = wb.createCellStyle();
				headerStyle.setFont(font);
				
				CellStyle colHStyle = wb.createCellStyle();
				colHStyle.setAlignment(HorizontalAlignment.CENTER);
				colHStyle.setVerticalAlignment(VerticalAlignment.TOP);
				colHStyle.setFont(font);
				colHStyle.setBorderBottom(BorderStyle.THIN);
				colHStyle.setBorderLeft(BorderStyle.THIN);
				colHStyle.setBorderRight(BorderStyle.THIN);
				colHStyle.setBorderTop(BorderStyle.THIN);

				CellStyle rowHStyle = wb.createCellStyle();
				rowHStyle.setAlignment(HorizontalAlignment.LEFT);
				rowHStyle.setVerticalAlignment(VerticalAlignment.TOP);
				rowHStyle.setWrapText(true);
				rowHStyle.setBorderBottom(BorderStyle.THIN);
				rowHStyle.setBorderLeft(BorderStyle.THIN);
				rowHStyle.setBorderRight(BorderStyle.THIN);
				rowHStyle.setBorderTop(BorderStyle.THIN);	
				
				CellStyle dataStyle = wb.createCellStyle();
				dataStyle.setAlignment(HorizontalAlignment.RIGHT);
				dataStyle.setVerticalAlignment(VerticalAlignment.TOP);
				dataStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0"));
				dataStyle.setBorderBottom(BorderStyle.THIN);
				dataStyle.setBorderLeft(BorderStyle.THIN);
				dataStyle.setBorderRight(BorderStyle.THIN);
				dataStyle.setBorderTop(BorderStyle.THIN);

				

		        for (int i = 0 ; i<3;i++) {
		        	Row h_row = sheet.createRow(i);
					Cell cell = h_row.createCell(0);
					cell.setCellStyle(headerStyle);
					cell.setCellValue(header[i]);
		        }
		        
		        Row c_h_row = sheet.createRow(4);
		        //Column header
		        for (int i = 0 ; i < labels.size();i++) {	
					Cell cell = c_h_row.createCell(i+1);
					cell.setCellStyle(colHStyle);
					cell.setCellValue(labels.get(i));
		        }
		        Cell total_cell = c_h_row.createCell(labels.size()+1);
		        total_cell.setCellStyle(colHStyle);
		        total_cell.setCellValue("TOTAL");
		        
		        
		        deptFundingIntExtList =  AppPeriodReportDAO.deptFunding_Int_Ext(filter,true,getSelectedCdcfPeriods());
		        //System.out.println(labels);
		        Row d_row = null;
		        List<deptFundingIntExt> amountList = null;
		        List<AppStaffCount> deptStaffCountList = null;
		        List<AppStaffCount> staffCountList = AppPeriodReportDAO.getAppStaffCount_List("","",false);
		        
		        
		        if (b3) {
		        	deptFundingIntExtList =  AppPeriodReportDAO.deptFunding_Int_Ext(filter,false,getSelectedCdcfPeriods());
					double grand_total_sum = 0;
					double staff_no_sum = 0;
		        	
		        	for(int i = 0 ;i<row_header_2.length;i++) {

		  				d_row = sheet.createRow(5+i);
						Cell cell = d_row.createCell(0);
						cell.setCellStyle(rowHStyle);
						cell.setCellValue(row_header_2[i]);
						

						double total_count =0;
						for (int j = 0 ; j < labels.size();j++) 
						{
							int index = j;
			  				amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))).collect(Collectors.toList());
			  				deptStaffCountList = staffCountList.stream().filter(x->x.getDept_code().equalsIgnoreCase(labels.get(index))).collect(Collectors.toList());
			  				
			  				double tar_count = 0;
			  				
			  				cell = d_row.createCell(j+1);
			  				cell.setCellStyle(dataStyle);
			  				
			  				if (i==0) {
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "GRF".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count = Double.parseDouble(amountList.get(0).getAmount());
			  				}
			  				else if (i==1) {
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "ECS".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count = Double.parseDouble(amountList.get(0).getAmount());
			  					
			  				}
			  				else if (i==2) {
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "PPR".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count = Double.parseDouble(amountList.get(0).getAmount());
			  					
			  				}
			  				else if(i==3) {
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "GRF".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count += Double.parseDouble(amountList.get(0).getAmount());
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "ECS".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count += Double.parseDouble(amountList.get(0).getAmount());
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "PPR".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count += Double.parseDouble(amountList.get(0).getAmount());
			  				}
			  				else if(i==4) {
			  					tar_count = deptStaffCountList.get(0).getStaff_count();
			  				}
			  				else if (i==5) {
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "GRF".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count += Double.parseDouble(amountList.get(0).getAmount());
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "ECS".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count += Double.parseDouble(amountList.get(0).getAmount());
			  					
			  					amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))
			  							&& "PPR".equals(item.getFund_type()) ).collect(Collectors.toList());
			  					if (! amountList.isEmpty())
			  						tar_count += Double.parseDouble(amountList.get(0).getAmount());		  					
			  					
			  					tar_count = (int)roundAvoid(tar_count / deptStaffCountList.get(0).getStaff_count() *1.0,0);
			  					
			  				}
			  				
			  				
			  				
			  				cell.setCellValue(tar_count);
			  				total_count += tar_count;
			  				
			  				if(j== labels.size() - 1) {
			  					if(i == 3)
			  						grand_total_sum = total_count;
			  					if(i == 4)
			  						staff_no_sum = total_count;
			  				}
			  				
						}
						
						cell = d_row.createCell(labels.size()+1);
						cell.setCellStyle(dataStyle);
						
						if(i == row_header_2.length-1)
							cell.setCellValue(grand_total_sum/staff_no_sum);
						else
							cell.setCellValue(total_count);
			        }
		        	
		        	
		        	
		        }
		        else {
					double grand_total_sum = 0;
					double staff_no_sum = 0;
					
		        	for(int i = 0 ;i<row_header.length;i++) {

			  				d_row = sheet.createRow(5+i);
							Cell cell = d_row.createCell(0);
							cell.setCellStyle(rowHStyle);
							cell.setCellValue(row_header[i]);
	
							

							double total_count =0;
							for (int j = 0 ; j < labels.size();j++) 
							{
								int index = j;
				  				amountList = deptFundingIntExtList.stream().filter(item-> item.getDept_code().equals(labels.get(index))).collect(Collectors.toList());
				  				deptStaffCountList = staffCountList.stream().filter(x->x.getDept_code().equalsIgnoreCase(labels.get(index))).collect(Collectors.toList());
				  				
				  				double tar_count = 0;
				  				
				  				cell = d_row.createCell(j+1);
				  				cell.setCellStyle(dataStyle);
				  				
				  				if (i==0) {
				  					tar_count = Double.parseDouble(amountList.stream().filter(item -> "EXT".equals(item.getFund_type())).collect(Collectors.toList()).get(0).getAmount());
				  				}
				  				else if (i==1) {
				  					tar_count = Double.parseDouble(amountList.stream().filter(item -> "INT".equals(item.getFund_type())).collect(Collectors.toList()).get(0).getAmount());
				  				}
				  				else if (i==2) {
				  					tar_count = Double.parseDouble(amountList.stream().filter(item -> "INT".equals(item.getFund_type())).collect(Collectors.toList()).get(0).getAmount()) +
				  							Double.parseDouble(amountList.stream().filter(item -> "EXT".equals(item.getFund_type())).collect(Collectors.toList()).get(0).getAmount());
				  				}
				  				else if (i ==3) {
				  					tar_count = deptStaffCountList.get(0).getStaff_count();
				  				}
				  				else if(i==4) {
				  					tar_count = (Double.parseDouble(amountList.stream().filter(item -> "INT".equals(item.getFund_type())).collect(Collectors.toList()).get(0).getAmount()) +
				  							Double.parseDouble(amountList.stream().filter(item -> "EXT".equals(item.getFund_type())).collect(Collectors.toList()).get(0).getAmount())) 
				  							/ deptStaffCountList.get(0).getStaff_count(); 
				  				}
				  				
				  				tar_count = (int) roundAvoid(tar_count,0);
				  				
				  				cell.setCellValue(tar_count);
				  				
				  				total_count += tar_count;
				  				if(j== labels.size() - 1) {
				  					if(i== 2)
				  						grand_total_sum = total_count;
				  					if(i==3)
				  						staff_no_sum = total_count;
				  				}
							}
							
							cell = d_row.createCell(labels.size()+1);
							cell.setCellStyle(dataStyle);
							if(i == row_header.length-1)
								cell.setCellValue(grand_total_sum/staff_no_sum);
							else
								cell.setCellValue(total_count);
				        }

		        }
		        
		     
				
		}
		catch (Exception e) {
			String message = " B2 Report is NOT Successfully Generated";
			if(b3)
				message = " B3 Report is NOT Successfully Generated";
			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}
		
	}
	

	private void createB1DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		String periodDesc = "";
		String lastYearPeriodDesc = "";
		Integer lastYearPeriodId = null;
		Boolean isCurrentYear = false;
		Boolean hasPastYearData = false;
		
		Date pastYear = stringToDate("01/07/2022");
		String periodStartString = dateToStringWithFormat(getReportStartDate(), "dd MMM yyyy");
		String periodEndString = dateToStringWithFormat(getReportEndDate(), "dd MMM yyyy");
		
		
		if (selectedCdcfPeriods != null) {
			List<CdcfRptPeriod> periodList = cdcfRptDao.getCdcfRptPeriodList();
			for (int i = 0; i < periodList.size(); i++) {
				if (periodList.get(i).getPeriod_id() == selectedCdcfPeriods) {
					if (periodList.get(i).getIs_current()) {
						isCurrentYear = true;
					}
					if (pastYear.compareTo(periodList.get(i).getDate_from()) == -1) {
						hasPastYearData = true;
					}
					periodDesc = periodList.get(i).getChart_desc();
					lastYearPeriodDesc = periodList.get(i+1).getChart_desc();
					lastYearPeriodId = periodList.get(i+1).getPeriod_id();
				}
			}
		}
		
		String sheetYearString = dateToStringWithFormat(getReportEndDate(), "yyyy");
		Sheet sheet = workbook.createSheet("App B1_"+sheetYearString);
		
    	sheet.createFreezePane(0, 4);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
    	hStyle.setVerticalAlignment(VerticalAlignment.TOP);
        hStyle.setFont(font);
        
        CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headerRowStyle.setWrapText(true);
		headerRowStyle.setVerticalAlignment(VerticalAlignment.TOP);
		headerRowStyle.setFont(font);
		headerRowStyle.setBorderBottom(BorderStyle.THIN);
		headerRowStyle.setBorderLeft(BorderStyle.THIN);
		headerRowStyle.setBorderRight(BorderStyle.THIN);
		headerRowStyle.setBorderTop(BorderStyle.THIN);
        
		CellStyle deptCellStyle = workbook.createCellStyle();
		deptCellStyle.setAlignment(HorizontalAlignment.CENTER);
		deptCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
		deptCellStyle.setFont(font);
		deptCellStyle.setBorderBottom(BorderStyle.THIN);
		deptCellStyle.setBorderLeft(BorderStyle.THIN);
		deptCellStyle.setBorderRight(BorderStyle.THIN);
		deptCellStyle.setBorderTop(BorderStyle.THIN);
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		textCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
		textCellStyle.setBorderBottom(BorderStyle.THIN);
		textCellStyle.setBorderLeft(BorderStyle.THIN);
		textCellStyle.setBorderRight(BorderStyle.THIN);
		textCellStyle.setBorderTop(BorderStyle.THIN);
		
		CellStyle noteCellStyle = workbook.createCellStyle();
		noteCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
		
        CellStyle valueStyle=null;
        valueStyle=workbook.createCellStyle();
        valueStyle.setWrapText(true);
        valueStyle.setVerticalAlignment(VerticalAlignment.TOP);
        valueStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
        valueStyle.setBorderBottom(BorderStyle.THIN);
        valueStyle.setBorderLeft(BorderStyle.THIN);
        valueStyle.setBorderRight(BorderStyle.THIN);
        valueStyle.setBorderTop(BorderStyle.THIN);
		
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		getAllAccessFacDeptList();
		String[] headerArray = accessFacDeptList.toArray(new String[0]);
		

		
		String[] totalArray = {"Total"};
		headerArray = ArrayUtils.addAll(headerArray, totalArray);	
		
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue("Common Data Collection Format");
		cell.setCellStyle(hStyle);  
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		
		cell = row.createCell(0);
		cell.setCellValue("Summary of Statistics on Research and Development Projects for "+periodDesc+" (Academic Staff Only)");
		cell.setCellStyle(hStyle);  
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n+1);
    		cell.setCellValue(headerArray[n]);
    		cell.setCellStyle(deptCellStyle);   		
    		sheet.autoSizeColumn(n+1);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	String[] typeColumns = {"No of current projects (Note 1)", "Value of current projects($) (Note 2)", "No of new projects (Note 3)", "Value of new projects($) (Note 4)"};
    	String[] totalTypeColumns = {"Total no of current projects", "Total value of current projects($)", "No. of Staff (Note 5)", "Average Value of Current Projects ($) per staff", "Total no of new projects", "Total value of new projects($)"};
    	String[][] fundSources = {{"9997"}, 
    								{"10005"}, 
    								{"10007","110","150","10010","10014","10008","1560","10011","10006","10019","10009","10013","10012","10004","1550","1520","115","10032","200","1590","1530","1535","2030","250","10021","10015","1500","1604","1605"},
    								{"310"},
    								{"925","510"},
    								{"10001"},
    								{"1621","10022","610","410","10023","1635","1645","710","10024","10025","10026","10027"},
    								{"10028","10029","10030","10031","1680"},
    								{"900"},
    								{"830","880","1570"},
    								{"1400","2000"},
    								{"1581","1015","10003"},
    								{"890","920","10016","10020","10017","9998"},
    								{"10002"},
    								{"980","1600","950","9999","935","940","1540","1650","855","1510","10018","970","990","1100","1660","1515","1300","1010","1200","2020"}};
    	String[] fundSourceName = {"RGC - General Research Fund (GRF)",
    			"RGC - Early Career Scheme (ECS)",
    			"UGC - Other Specific Funds/Earmarked Grants (Collaborative Research Fund (CRF) / Joint Research Scheme (JRS) / Prestigious Fellowship Scheme under Humanities and Social Science Panel (HSSPFS) / Research Impact Fund (RIF) / Research Matching Grant (RMG) / Others)",
    			"HKSAR Govt - Quality Education Fund (QEF)",
    			"HKSAR Govt - Education Bureau",
    			"HKSAR Govt-  Innovative and Technology Fund (ITF)",
    			"HKSAR Government (Agriculture, Fisheries & Conservation Department (AFCD) / Environmental and Conservation Fund (ECF) / Public Policy Research Funding Scheme (PPR) / Public Policy Research Funding Scheme (PPR) (Special Round) / Hong Kong Arts Development Council / Others)",
    			"HKSAR Government Related Organizations (Equal Opportunities Commission (EOC) / Health and Medical Research Fund (HMRF) / Health Care and Promotion Fund (HCPF) / Lord Wilson Heritage Trust (LWHT))",
    			"HK Private Fund - Charities/Foundations",
    			"HK Private Fund - Industries/Others",
    			"Non-HK",
    			"Funding from Other UGC-funded Universities",
    			"EdUHK - Internal Research Grant (Internal Research Grant / Seed Funding Grant (SFG) / Internal Research Fund (IRF) for Theme-based Research Scheme / Collaborative Research Fund / Research Cluster Fund (RCF) / Start-up Research Grant)",
    			"EdUHK - Knowledge Transfer Fund",
    			"EdUHK - Departmental Fund (Departmental Funding / Departmental Small Scale Research Fund), Faculty Fund (Faculty Research Fund / Block Grant Faculty Fund) and Others"};
    	String[] noteArray = {"Note 1: The total no. of research projects which have not been completed prior to current financial year. All new research projects are also included.",
				    			"Note 2: The total value of current research projects, which are on the books of EdUHK from "+periodStartString+" to "+periodEndString,
				    			"Note 3: The total no. of research projects which have been awarded to start in the period of "+periodStartString+" to "+periodEndString,
				    			"Note 4: The total value of new research projects, which have been awarded to start in the period of "+periodStartString+" to "+periodEndString,
				    			"Note 5: It refers to all regular academic staff who are eligible to participate in the CDCF Exercise."};
    	List<ProjectReport> dataList = new ArrayList<ProjectReport>();
    	List<ProjectReport> lastYearDataList = new ArrayList<ProjectReport>();
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		List<String> fundSrcList = new ArrayList<>();
		
		for (int x = 0; x < fundSources.length; x++) {
			String[]fundSourceArray = fundSources[x];
			row = sheet.createRow(sheet.getLastRowNum()+1);
	    	cell = row.createCell(0);
	    	String typeName = "";
	    	fundSrcList = new ArrayList<>();
	    	for(int y = 0; y < fundSourceArray.length; y++) {
	    		fundSrcList.add(fundSourceArray[y]);
	    		/*if (y == 0) {
	    			typeName = typeName + projDao.getFundSourceName(fundSourceArray[y]);
	    		}else {
	    			typeName = typeName + "/" + projDao.getFundSourceName(fundSourceArray[y]);
	    		}*/
	    	}
			cell.setCellValue(fundSourceName[x]);
			cell.setCellStyle(headerRowStyle);  
			cell = row.createCell(headerArray.length);
			cell.setCellStyle(headerRowStyle);  
			sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum(),sheet.getLastRowNum(),0,headerArray.length));
			
			
			if (isCurrentYear) {
				dataList = projDao.getProjectReportList("C", null, null, selectedCdcfPeriods ,getAllAccessFacDeptList(), fundSrcList);
			}else {
				dataList = projDao.getPastYearProjectReportList("C", null, null, selectedCdcfPeriods ,getAllAccessFacDeptList(), fundSrcList);
			}
			
			//System.out.println(dataList);
			//accessFacDeptList;
			//No of current projects
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
			cell.setCellValue(typeColumns[0]);
			cell.setCellStyle(textCellStyle); 
			
			int totalCurrentNum = 0;

			
			
			
			for (int i = 0 ; i < accessFacDeptList.size(); i++) {
				cell = row.createCell(i+1);
				Integer c = 0;
				String dept = accessFacDeptList.get(i);
				if (dataList != null) {
					c = dataList.stream()
							.filter(d -> dept.equals(d.getDept()))
							.mapToInt(d -> Integer.valueOf(d.getNum()))
							.sum();
				}
				totalCurrentNum += c;
				cell.setCellValue(c);

				cell.setCellStyle(textCellStyle); 
			}
			cell = row.createCell(accessFacDeptList.size()+1);
			
			
			
			
			cell.setCellValue(totalCurrentNum);
			cell.setCellStyle(textCellStyle); 
			
			//Value of current projects
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
			cell.setCellValue(typeColumns[1]);
			cell.setCellStyle(textCellStyle); 
			
			Double totalCurrentValue = 0.0;
			for (int i = 0 ; i < accessFacDeptList.size(); i++) {
				cell = row.createCell(i+1);
				Double c = 0.0;
				String dept = accessFacDeptList.get(i);
				if (dataList != null) {				
					c = dataList.stream()
							.filter(d -> dept.equals(d.getDept()))
							.mapToDouble(d -> d.getValue())
							.sum();	
				}
				totalCurrentValue += c;
				cell.setCellValue(c);
				cell.setCellStyle(valueStyle);  
			}
			
		
			cell = row.createCell(accessFacDeptList.size()+1);
			cell.setCellValue(totalCurrentValue);
			cell.setCellStyle(valueStyle); 
			
			if (isCurrentYear) {
				dataList = projDao.getProjectReportNewList("C", null, null, selectedCdcfPeriods, getAllAccessFacDeptList(), fundSrcList);
			}else {
				dataList = projDao.getPastYearProjectReportNewList("C", null, null, selectedCdcfPeriods, getAllAccessFacDeptList(), fundSrcList);
			}
			
			//No of new projects
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
			cell.setCellValue(typeColumns[2]);
			cell.setCellStyle(textCellStyle); 
			
			int totalNewNum = 0;
			
			//System.out.println("ACCESS: " + accessFacDeptList);
			
			for (int i = 0 ; i < accessFacDeptList.size(); i++) {
				cell = row.createCell(i+1);
				Integer c = 0;
				String dept = accessFacDeptList.get(i);
				if (dataList != null) {
					c = dataList.stream()
							.filter(d -> dept.equals(d.getDept()))
							.mapToInt(d -> Integer.valueOf(d.getNum()))
							.sum();
				}
				totalNewNum += c;
				cell.setCellValue(c);
				cell.setCellStyle(textCellStyle); 
			}
			cell = row.createCell(accessFacDeptList.size()+1);
			cell.setCellValue(totalNewNum);
			cell.setCellStyle(textCellStyle); 
			
			//Value of new projects
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
			cell.setCellValue(typeColumns[3]);
			cell.setCellStyle(textCellStyle); 
			
			Double totalNewValue = 0.0;
			for (int i = 0 ; i < accessFacDeptList.size(); i++) {
				cell = row.createCell(i+1);
				Double c = 0.0;
				String dept = accessFacDeptList.get(i);
				if (dataList != null) {				
					c = dataList.stream()
							.filter(d -> dept.equals(d.getDept()))
							.mapToDouble(d -> d.getValue())
							.sum();	
				}
				totalNewValue += c;
				cell.setCellValue(c);
				cell.setCellStyle(valueStyle); 
			}
			cell = row.createCell(accessFacDeptList.size()+1);
			cell.setCellValue(totalNewValue);
			cell.setCellStyle(valueStyle);  
		}
		
		
	
		row = sheet.createRow(sheet.getLastRowNum()+1);
    	cell = row.createCell(0);
		cell.setCellValue("Total");
		cell.setCellStyle(headerRowStyle);  
		sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum(),sheet.getLastRowNum(),0,headerArray.length));
		

		//Get No. of Staff
		
		List<AppStaffCount> countStaffList = fDao.getAppStaffCount_AllList();
		
		
    	for (int n=0;n<totalTypeColumns.length;n++)
    	{  
    		
    		//System.out.println("N: "+ n + " length: " +totalTypeColumns.length );
    		
    		row = sheet.createRow(sheet.getLastRowNum()+1);
    		cell = row.createCell(0);
    		cell.setCellValue(totalTypeColumns[n]);
    		cell.setCellStyle(textCellStyle); 
    		// #3 length is the longest
    		if (n == 3)
    			sheet.setDefaultColumnWidth(totalTypeColumns[n].length());
    		
    		
    		//System.out.println("DEPT:" + accessFacDeptList);
    		char startLetter = 'B';
    		switch(n){
    			  case 0:
		    		for (int m = 0; m <= accessFacDeptList.size(); m++)
		        	{ 
		    			
		    			//System.out.println("Case 0 m: "+ m  + " SIZE : " + accessFacDeptList.size());
		    			Cell cell2 = row.createCell(m+1);
		        		int startCell = 6;
			    		String cellTotal = "";
			    		
			    		for (int c = 0; c < fundSources.length; c++) {
			    			cellTotal = cellTotal + startLetter+startCell;

			    			if (c < fundSources.length - 1) {
			    				cellTotal = cellTotal +"+";
			    			}
			    			startCell = startCell + 5;
			    		}
			    		cell2.setCellFormula(cellTotal);
			    		cell2.setCellStyle(textCellStyle); 
			    		startLetter++;
			    		//System.out.println("Letter: "+ startLetter);
		        	}
		    		break;
    			  case 1:
    				  for (int m = 0; m <= accessFacDeptList.size(); m++)
	  		        	{ 
	  		    			Cell cell2 = row.createCell(m+1);
	  		        		int startCell = 7;
	  			    		String cellTotal = "";
	  			    		
	  			    		for (int c = 0; c < fundSources.length; c++) {
	  			    			cellTotal = cellTotal + startLetter+startCell;
	  			    			if (c < fundSources.length - 1) {
	  			    				cellTotal = cellTotal +"+";
	  			    			}
	  			    			startCell = startCell + 5;
	  			    		}
	  			    		
	  			    		cell2.setCellFormula(cellTotal);
	  			    		cell2.setCellStyle(valueStyle); 
	  			    		sheet.autoSizeColumn(m+1);
	  			    		startLetter++;
	  		        	}
	  		    		break;
	  		      //No. of Staff
	  		    
    			  case 2:
    				  for (int m = 0; m <= accessFacDeptList.size(); m++)
    				  {
    					  if (m < accessFacDeptList.size()) {
    					  	Cell cell2 = row.createCell(m+1);
    						Integer c = 0;
    						String dept = accessFacDeptList.get(m);
    						
    						if (countStaffList.size() > 0) {				
    							c = countStaffList.stream()
    									.filter(d -> dept.equals(d.getDept_code()))
    									.mapToInt(d -> Integer.valueOf(d.getStaff_count()))
    									.sum();	
    						}
    						cell2.setCellValue(c);
    						cell2.setCellStyle(textCellStyle); 
    					  }else {
    						  Cell cell2 = row.createCell(m+1);
		  		        		int startRow = 83;
		  		        		int startCol = 1;
		  			    		String cellTotal = "";
		  			    		for (int c = 0; c < accessFacDeptList.size(); c++) {
		  			    			cellTotal = cellTotal + str(startCol)+startRow;
		  			    			if (c < accessFacDeptList.size() - 1) {
		  			    				cellTotal = cellTotal +"+";
		  			    			}
		  			    			startCol = startCol + 1;
		  			    		}
		  			    		cell2.setCellFormula(cellTotal);
		  			    		cell2.setCellStyle(textCellStyle); 
    					  }
    					}
	  		    		break; 
	  		      //Average Value of Current Projects ($) per staff
    			  case 3:
    				  for (int m = 0; m <= accessFacDeptList.size(); m++)
	  		        	{ 
	  		    			Cell cell2 = row.createCell(m+1);
	  			    		cell2.setCellFormula(startLetter+"82/"+startLetter+"83");
	  			    		cell2.setCellStyle(valueStyle); 
	  			    		startLetter++;
	  		        	}
	  		    		break; 
    			  case 4:
    				  for (int m = 0; m <= accessFacDeptList.size(); m++)
	  		        	{ 
	  		    			Cell cell2 = row.createCell(m+1);
	  		        		int startCell = 8;
	  			    		String cellTotal = "";
	  			    		for (int c = 0; c < fundSources.length; c++) {
	  			    			cellTotal = cellTotal + startLetter+startCell;
	  			    			if (c < fundSources.length - 1) {
	  			    				cellTotal = cellTotal +"+";
	  			    			}
	  			    			startCell = startCell + 5;
	  			    		}
	  			    		cell2.setCellFormula(cellTotal);
	  			    		cell2.setCellStyle(textCellStyle); 
	  			    		startLetter++;
	  		        	}
	  		    		break; 
    			  case 5:
    				  for (int m = 0; m <= accessFacDeptList.size(); m++)
	  		        	{ 
	  		    			Cell cell2 = row.createCell(m+1);
	  		        		int startCell = 9;
	  			    		String cellTotal = "";
	  			    		for (int c = 0; c < fundSources.length; c++) {
	  			    			cellTotal = cellTotal + startLetter+startCell;
	  			    			if (c < fundSources.length - 1) {
	  			    				cellTotal = cellTotal +"+";
	  			    			}
	  			    			startCell = startCell + 5;
	  			    		}
	  			    		cell2.setCellFormula(cellTotal);
	  			    		cell2.setCellStyle(valueStyle);  
	  			    		startLetter++;
	  		        	}
	  		    		break; 
    		}
    		
    	}
    	

		
    	//Notes
    	row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
    	for (int n=0;n<noteArray.length;n++)
    	{  
    		row = sheet.createRow(sheet.getLastRowNum()+1);
    		cell = row.createCell(0);
    		cell.setCellValue(noteArray[n]);
    		cell.setCellStyle(noteCellStyle); 
    	}
    	
    	//Sheet2
    	
    	
    	hasPastYearData = false;
    	
    	
    	if (hasPastYearData) {
	    	sheetYearString = lastYearPeriodDesc.substring(2) + "_" + periodDesc.substring(2);
	    	sheetYearString = sheetYearString.replaceAll("/", "");
	    	sheet = workbook.createSheet("App B1_"+sheetYearString);
			
	    	sheet.createFreezePane(0, 5);
	    	
	    	row = sheet.createRow(0);
	    	cell = null;
	    	
	    	//First row header
	    	cell = row.createCell(0);
			cell.setCellValue("Common Data Collection Format");
			cell.setCellStyle(hStyle);  
			
			row = sheet.createRow(sheet.getLastRowNum()+1);
			
			cell = row.createCell(0);
			cell.setCellValue("Summary of Statistics on Research and Development Projects - A comparison of "+lastYearPeriodDesc + " & " + periodDesc+" (Academic Staff Only)");
			cell.setCellStyle(hStyle);  
			
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
			
	    	row = sheet.createRow(sheet.getLastRowNum()+1);
	    	int deptCol = 1;
	    	for (int n=0;n<headerArray.length;n++)
	    	{    		
	    		cell = row.createCell(deptCol);
	    		cell.setCellValue(headerArray[n]);
	    		cell.setCellStyle(deptCellStyle);
	    		
	    		cell = row.createCell(deptCol+1);
	    		cell.setCellStyle(deptCellStyle);
	    		sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum(),sheet.getLastRowNum(),deptCol,deptCol+1));
	    		deptCol = deptCol +2;
	    	}
	    	
	    	row = sheet.createRow(sheet.getLastRowNum()+1);
	    	String lastYearString = lastYearPeriodDesc.substring(2);
	    	String thisYearString = periodDesc.substring(2);
	    	deptCol = 1;
	    	for (int n=0;n<headerArray.length;n++)
	    	{    		
	    		cell = row.createCell(deptCol);
	    		cell.setCellValue(lastYearString);
	    		cell.setCellStyle(deptCellStyle); 
	    		
	    		cell = row.createCell(deptCol+1);
	    		cell.setCellValue(thisYearString);
	    		cell.setCellStyle(deptCellStyle); 
	    		deptCol = deptCol +2;
	    	}
	    	
	    	for (int x = 0; x < fundSources.length; x++) {
				String[]fundSourceArray = fundSources[x];
				row = sheet.createRow(sheet.getLastRowNum()+1);
		    	cell = row.createCell(0);
		    	String typeName = "";
		    	fundSrcList = new ArrayList<>();
		    	for(int y = 0; y < fundSourceArray.length; y++) {
		    		fundSrcList.add(fundSourceArray[y]);
		    		/*if (y == 0) {
		    			typeName = typeName + projDao.getFundSourceName(fundSourceArray[y]);
		    		}else {
		    			typeName = typeName + "/" + projDao.getFundSourceName(fundSourceArray[y]);
		    		}*/
		    	}
				cell.setCellValue(fundSourceName[x]);
				cell.setCellStyle(headerRowStyle);  
				sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum(),sheet.getLastRowNum(),0,headerArray.length *2));
				
				if (isCurrentYear) {
					dataList = projDao.getProjectReportList("C", null, null, selectedCdcfPeriods ,getAllAccessFacDeptList(), fundSrcList);
				}else {
					dataList = projDao.getPastYearProjectReportList("C", null, null, selectedCdcfPeriods ,getAllAccessFacDeptList(), fundSrcList);
				}
				lastYearDataList = projDao.getPastYearProjectReportList("C", null, null, lastYearPeriodId ,getAllAccessFacDeptList(), fundSrcList);
				
				//No of current projects
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue(typeColumns[0]);
				cell.setCellStyle(textCellStyle); 
				
				int totalCurrentNum = 0;
				int lastYearTotalCurrentNum = 0;
				int col = 1;
				for (int i = 0 ; i < accessFacDeptList.size(); i++) {
					cell = row.createCell(col++);
					Integer c = 0;
					Integer lc = 0;
					String dept = accessFacDeptList.get(i);
					if (lastYearDataList != null) {
						lc = lastYearDataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToInt(d -> Integer.valueOf(d.getNum()))
								.sum();
					}
					lastYearTotalCurrentNum += lc;
					cell.setCellValue(lc);
					cell.setCellStyle(textCellStyle); 
					
					cell = row.createCell(col++);
					if (dataList != null) {
						c = dataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToInt(d -> Integer.valueOf(d.getNum()))
								.sum();
					}
					totalCurrentNum += c;
					cell.setCellValue(c);
					cell.setCellStyle(textCellStyle); 
				}
				
				cell = row.createCell(col++);
				cell.setCellValue(lastYearTotalCurrentNum);
				cell.setCellStyle(textCellStyle); 
				
				cell = row.createCell(col++);
				cell.setCellValue(totalCurrentNum);
				cell.setCellStyle(textCellStyle); 
				
				//Value of current projects
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue(typeColumns[1]);
				
				Double totalCurrentValue = 0.0;
				Double lastYearTotalCurrentValue = 0.0;
				col = 1;
				for (int i = 0 ; i < accessFacDeptList.size(); i++) {
					cell = row.createCell(col++);
					Double c = 0.0;
					Double lc = 0.0;
					String dept = accessFacDeptList.get(i);
					if (lastYearDataList != null) {				
						lc = lastYearDataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToDouble(d -> d.getValue())
								.sum();	
					}
					lastYearTotalCurrentValue += lc;
					cell.setCellValue(lc);
					cell.setCellStyle(valueStyle);  
					cell = row.createCell(col++);
					if (dataList != null) {				
						c = dataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToDouble(d -> d.getValue())
								.sum();	
					}
					totalCurrentValue += c;
					cell.setCellValue(c);
					cell.setCellStyle(valueStyle);  
				}
				cell = row.createCell(col++);
				cell.setCellValue(lastYearTotalCurrentValue);
				cell.setCellStyle(valueStyle); 
				
				cell = row.createCell(col++);
				cell.setCellValue(totalCurrentValue);
				cell.setCellStyle(valueStyle); 
				
				if (isCurrentYear) {
					dataList = projDao.getProjectReportNewList("C", null, null, selectedCdcfPeriods, getAllAccessFacDeptList(), fundSrcList);
				}else {
					dataList = projDao.getPastYearProjectReportNewList("C", null, null, selectedCdcfPeriods, getAllAccessFacDeptList(), fundSrcList);
				}
				lastYearDataList = projDao.getPastYearProjectReportNewList("C", null, null, lastYearPeriodId, getAllAccessFacDeptList(), fundSrcList);
				
				//No of new projects
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue(typeColumns[2]);
				cell.setCellStyle(textCellStyle); 
				
				int totalNewNum = 0;
				int lastYearTotalNewNum = 0;
				col = 1;
				for (int i = 0 ; i < accessFacDeptList.size(); i++) {
					cell = row.createCell(col++);
					Integer c = 0;
					Integer lc = 0;
					String dept = accessFacDeptList.get(i);
					if (lastYearDataList != null) {
						lc = lastYearDataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToInt(d -> Integer.valueOf(d.getNum()))
								.sum();
					}
					lastYearTotalNewNum += lc;
					cell.setCellValue(lc);
					cell.setCellStyle(textCellStyle); 
					
					cell = row.createCell(col++);
					if (dataList != null) {
						c = dataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToInt(d -> Integer.valueOf(d.getNum()))
								.sum();
					}
					totalNewNum += c;
					cell.setCellValue(c);
					cell.setCellStyle(textCellStyle); 
				}
				cell = row.createCell(col++);
				cell.setCellValue(lastYearTotalNewNum);
				cell.setCellStyle(textCellStyle); 
				
				cell = row.createCell(col++);
				cell.setCellValue(totalNewNum);
				cell.setCellStyle(textCellStyle); 
				
				//Value of new projects
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue(typeColumns[3]);
				cell.setCellStyle(textCellStyle); 
				
				Double totalNewValue = 0.0;
				Double lastYearTotalNewValue = 0.0;
				col = 1;
				for (int i = 0 ; i < accessFacDeptList.size(); i++) {
					cell = row.createCell(col++);
					Double c = 0.0;
					Double lc = 0.0;
					String dept = accessFacDeptList.get(i);
					if (lastYearDataList != null) {				
						lc = lastYearDataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToDouble(d -> d.getValue())
								.sum();	
					}
					lastYearTotalNewValue += lc;
					cell.setCellValue(lc);
					cell.setCellStyle(valueStyle); 
					
					cell = row.createCell(col++);
					if (dataList != null) {				
						c = dataList.stream()
								.filter(d -> dept.equals(d.getDept()))
								.mapToDouble(d -> d.getValue())
								.sum();	
					}
					totalNewValue += c;
					cell.setCellValue(c);
					cell.setCellStyle(valueStyle); 
				}
				cell = row.createCell(col++);
				cell.setCellValue(lastYearTotalNewValue);
				cell.setCellStyle(valueStyle);  
				
				cell = row.createCell(col++);
				cell.setCellValue(totalNewValue);
				cell.setCellStyle(valueStyle);  
			}
	    	
	    	row = sheet.createRow(sheet.getLastRowNum()+1);
	    	cell = row.createCell(0);
	    	cell.setCellValue("Total");
			cell.setCellStyle(headerRowStyle);  
			sheet.addMergedRegion(new CellRangeAddress(sheet.getLastRowNum(),sheet.getLastRowNum(),0,headerArray.length *2));
			
			//Get No. of Staff
			countStaffList = fDao.getAppStaffCount_AllList();
			
	    	for (int n=0;n<totalTypeColumns.length;n++)
	    	{  
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		cell = row.createCell(0);
	    		cell.setCellValue(totalTypeColumns[n]);
	    		cell.setCellStyle(textCellStyle); 
	    		sheet.setDefaultColumnWidth(totalTypeColumns[n].length());
	    		
	    		int startLetter = 1;
	    		int countDepts = accessFacDeptList.size() *2;
	    		switch(n){
	    			  case 0:
			    		for (int m = 0; m <= countDepts+1; m++)
			        	{ 
			    			Cell cell2 = row.createCell(m+1);
			        		int startCell = 7;
				    		String cellTotal = "";
				    		for (int c = 0; c < fundSources.length; c++) {
				    			cellTotal = cellTotal + str(startLetter)+startCell;
				    			if (c < fundSources.length - 1) {
				    				cellTotal = cellTotal +"+";
				    			}
				    			startCell = startCell + 5;
				    		}
				    		cell2.setCellFormula(cellTotal);
				    		cell2.setCellStyle(textCellStyle); 
				    		startLetter++;
			        	}
			    		break;
	    			  case 1:
	    				  for (int m = 0; m <= countDepts+1; m++)
		  		        	{ 
		  		    			Cell cell2 = row.createCell(m+1);
		  		        		int startCell = 8;
		  			    		String cellTotal = "";
		  			    		for (int c = 0; c < fundSources.length; c++) {
		  			    			cellTotal = cellTotal + str(startLetter)+startCell;
		  			    			if (c < fundSources.length - 1) {
		  			    				cellTotal = cellTotal +"+";
		  			    			}
		  			    			startCell = startCell + 5;
		  			    		}
		  			    		cell2.setCellFormula(cellTotal);
		  			    		cell2.setCellStyle(valueStyle); 
		  			    		startLetter++;
		  		        	}
		  		    		break;
		  		      //No. of Staff
	    			  case 2:
	    				  for (int m = 0; m <= countDepts; m++)
	    				  {
	    					  if (m < countDepts) {
	    					  	Cell cell2 = row.createCell(m+1);
	    						Integer c = 0;
	    						String dept = "";
	    						if (m > 1) {
	    							int z = m/2;
	    							dept = accessFacDeptList.get(z);
	    						}else {
	    							dept = accessFacDeptList.get(0);
	    						}
	    						String currentDept = dept;
	    						if (countStaffList.size() > 0) {				
	    							c = countStaffList.stream()
	    									.filter(d -> currentDept.equals(d.getDept_code()))
	    									.mapToInt(d -> Integer.valueOf(d.getStaff_count()))
	    									.sum();	
	    						}
	    						cell2.setCellValue(c);
	    						cell2.setCellStyle(textCellStyle); 
	    					  }else {
	
			  		    			Cell cell2 = row.createCell(m+1);
			  		        		int startRow = 84;
			  		        		int startCol = 1;
			  			    		String cellTotal = "";
			  			    		for (int c = 0; c < accessFacDeptList.size(); c++) {
			  			    			cellTotal = cellTotal + str(startCol)+startRow;
			  			    			if (c < accessFacDeptList.size() - 1) {
			  			    				cellTotal = cellTotal +"+";
			  			    			}
			  			    			startCol = startCol + 2;
			  			    		}
			  			    		cell2.setCellFormula(cellTotal);
			  			    		cell2.setCellStyle(textCellStyle); 
			  			    		
			  			    		cell2 = row.createCell(m+2);
			  		        		startCol = 2;
			  			    		cellTotal = "";
			  			    		for (int c = 0; c < accessFacDeptList.size(); c++) {
			  			    			cellTotal = cellTotal + str(startCol)+startRow;
			  			    			if (c < accessFacDeptList.size() - 1) {
			  			    				cellTotal = cellTotal +"+";
			  			    			}
			  			    			startCol = startCol + 2;
			  			    		}
			  			    		cell2.setCellFormula(cellTotal);
			  			    		cell2.setCellStyle(textCellStyle); 
	    					  }
	    					}
		  		    		break; 
	    			  case 3:
	    				  for (int m = 0; m <= countDepts+1; m++)
		  		        	{ 
		  		    			Cell cell2 = row.createCell(m+1);
		  			    		cell2.setCellFormula(str(startLetter)+"83/"+str(startLetter)+"84");
		  			    		cell2.setCellStyle(valueStyle); 
		  			    		startLetter++;
		  		        	}
		  		    		break; 
	    			  case 4:
	    				  for (int m = 0; m <= countDepts+1; m++){ 
		  		    			Cell cell2 = row.createCell(m+1);
		  		        		int startCell = 9;
		  			    		String cellTotal = "";
		  			    		for (int c = 0; c < fundSources.length; c++) {
		  			    			cellTotal = cellTotal + str(startLetter)+startCell;
		  			    			if (c < fundSources.length - 1) {
		  			    				cellTotal = cellTotal +"+";
		  			    			}
		  			    			startCell = startCell + 5;
		  			    		}
		  			    		cell2.setCellFormula(cellTotal);
		  			    		cell2.setCellStyle(textCellStyle); 
		  			    		startLetter++;
		  		        	}
		  		    		break; 
	    			  case 5:
	    				  for (int m = 0; m <= countDepts+1; m++)
		  		        	{ 
		  		    			Cell cell2 = row.createCell(m+1);
		  		        		int startCell = 10;
		  			    		String cellTotal = "";
		  			    		for (int c = 0; c < fundSources.length; c++) {
		  			    			cellTotal = cellTotal + str(startLetter)+startCell;
		  			    			if (c < fundSources.length - 1) {
		  			    				cellTotal = cellTotal +"+";
		  			    			}
		  			    			startCell = startCell + 5;
		  			    		}
		  			    		cell2.setCellFormula(cellTotal);
		  			    		cell2.setCellStyle(valueStyle);  
		  			    		sheet.autoSizeColumn(m+1);
		  			    		startLetter++;
		  		        	}
		  		    		break; 
	    		}
	    		
	    	}
	    	
	    	//System.out.println("Testing 6");
			
	    	//Notes
	    	row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
	    	for (int n=0;n<noteArray.length;n++)
	    	{  
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		cell = row.createCell(0);
	    		cell.setCellValue(noteArray[n]);
	    		cell.setCellStyle(noteCellStyle); 
	    	}
    	}
	}
	
	private void createChrmAwardDataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
    	String[] headerArray = {"Award Number","Staff Number","Employee Name","Department Code","DCC","DCC percentage","Staff Grade","Granted Date",
    				"Name of Prize/Award","No. of recipients","Name of Organization(s) Conferred","CDCF status"};
    	
    	PreRpt preRpt = preRptDao.getPreRpt("CHRM_AWARD");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+2);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+3);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.setColumnWidth(n, 30* 256);
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
				
		List<AwardReport> dataList = awardDao.getAwardReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessIconnectDeptList());
		if (dataList != null) {
			// Create data rows
			for(AwardReport d:dataList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(d.getAward_no());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getStaff_number());
	    		
				cell = row.createCell(i++);
	    		cell.setCellValue(d.getEmployee_name());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDept());

	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDcc());
	    			    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDcc_percentage());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getStaff_grade());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getGranted_date());
	    		cell.setCellStyle(cellDateStyle2); 
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getAward_name());
	    		
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getNo_recipients());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOrg_name());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCdcf_status());
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrmAcadProfDataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
    	String[] headerArray = {"Output No", "Census Date", "Output Title 1", "Output Title 2", "Output Type", 
    			"Types of Research Activities", "Name of Journal/Edited Book/Conference/Newspaper", 
    			"Name of all editors in order appeared on output", "Volume/Issue", "Page No", "City", 
    			"From (MM)", "From (YYYY)", "To (MM)", "To (YYYY)", "Publisher", "Other Details 1", 
    			"Other Details 2", "Language", "Name of all authors in order appeared on output", 
    			"Exceed Char Indicator", "Is RGC Funded", "RGC Project No", "Open Access Status", 
    			"Is APC Required", "Is Other Than APC", "Is Affiliated", "APC to Publisher", "Article acceptance date", 
    			"Embargo end date", "Embargo period", "Calculated embargo end date", "Is Calculated Embargo After Census", 
    			"Is Open Access Delayed", "Is Transformative Agreement", "Amount to Publisher for Open Access", "Accessibility", 
    			"Key Research Areas", "Other Key Research Areas", "Education Sector", "Education Sector Details", 
    			"Research Areas", "Research Areas Details", "No. of Author(s) and the details", "Ranking of Research Activities", 
    			"Disciplinary Areas", "Disciplinary Area Details", "Others Disciplinary Area Details"};
    	
		List<String> headerList = new ArrayList<String>(Arrays.asList(headerArray));
		
		List<Publication> dataList = publicationDao.getCHRMAcadProfReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"));
		int maxNoDtl = 0;
		
		for (Publication pub : dataList) {
			if(pub.getNoPDtl() > maxNoDtl) maxNoDtl = pub.getNoPDtl();
		}
		for(int i=0 ; i < maxNoDtl ; ++i) {
			headerList.add("Line No");
			headerList.add("Staff No");
			headerList.add("Staff Name");
			headerList.add("Authorship Type");
			headerList.add("Non-IEd Staff");
		}
		headerArray = headerList.toArray(new String[0]);
		
		PreRpt preRpt = preRptDao.getPreRpt("CHRM_OUTPUT");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (dataList != null) {
			// Create data rows
			for(Publication d:dataList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(d.getOutputNo());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getPublishedCensusDate());
	    		cell.setCellStyle(cellDateStyle2); 

	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getArticleTitle());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getArticleTitleContinue());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSapOutputType());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSapReferedJournal());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTitle());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getNameOtherEditors());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getVolIssue());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getPageNum());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCity());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getFromMonth());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getFromYear());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getToMonth());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getToYear());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getPublisher());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOtherDetails());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOtherDetailsContinue());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getLanguage());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getAuthorListWithDeptAndAuthType());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getExceed_char_ind());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getIs_rgc_proj());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getRgc_proj_num());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_statStr());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_apc());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_apc_payment());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_payment());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getApc_val());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_art_acc_date());
	    		cell.setCellStyle(cellDateStyle3); 
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_emb_end_date());
	    		cell.setCellStyle(cellDateStyle3); 
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOpen_access_emb_period_date());
	    		cell.setCellStyle(cellDateStyle2); 
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCal_emb_end_date());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getEmb_date_after_census());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDelay_open_access());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getIs_tran_agrt());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTran_agrt_val());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getAccessibility());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getKeyResearchAreas());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getKeyResearchAreasOther());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSchCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSchDtlCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getResearchArea());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getResearchAreaDetail());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getNoPDtl());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getResearchActivityRanking());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDaCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDaDtlCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOtherDaDtl());
	    		
	    		for(OutputDetails_P dtl : d.getpDtlList()) {
	    			cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getPk().getLine_no());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getAuthorship_staff_no());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getAuthorship_name());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getAuthorship_type());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getNon_ied_staff_flag());
	    		}

	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	//CHRM Academic Professional Output By Department By Individual Staff
	private void createChrm001DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
    	String[] headerArray = {"Output No", "Output Title", "Output Category", "Output Type", 
    			"Weighting", "Authorship", "Exceed Char Indicator"};
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_001");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, Map<String, List<Publication>>> dataList = publicationDao.getCHRM001ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessIconnectDeptList(),getSelectedDept());

		
		if (dataList != null) {
			// Create data rows
			for(Map.Entry<String, Map<String, List<Publication>>> dept : dataList.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+2);
				
				cell = row.createCell(0);
	    		cell.setCellValue("Department Name: " + dept.getKey());
	    		
	    		numOfRows += 2;
	    		
	    		for(Map.Entry<String, List<Publication>> author : dept.getValue().entrySet()) {
	    			row = sheet.createRow(sheet.getLastRowNum()+2);
	    			
	    			cell = row.createCell(0);
		    		cell.setCellValue("Author: " + author.getKey());
		    		numOfRows += 3;
		    		boolean first = true;
		    		for(Publication d : author.getValue()) {
		    			int i = 0;
		    			if(first) {
		    				row = sheet.createRow(sheet.getLastRowNum()+2);
		    				first = false;
		    			}
		    			else row = sheet.createRow(sheet.getLastRowNum()+1);
		    			
		    			cell = row.createCell(i++);
			    		cell.setCellValue(d.getOutputNo());
			    		
			    		cell = row.createCell(i++);
			    		cell.setCellValue(d.getApaCitation());
			    		
			    		cell = row.createCell(i++);
			    		cell.setCellValue(d.getSapOutputType());
			    		
			    		cell = row.createCell(i++);
			    		cell.setCellValue(d.getSapReferedJournal());
			    		
			    		cell = row.createCell(i++);
			    		cell.setCellValue(roundAvoid(d.getWeighting(),4));
			    		
			    		cell = row.createCell(i++);
			    		
			    		String authorship = d.getAuthorListWithDeptAndAuthType();
			    		
			    		
			    		//System.out.println("001 SIZE: " + authorship.length());
			    		if (authorship.length() > 10000) 
			    			authorship = authorship.substring(0, StringUtils.ordinalIndexOf(authorship, ";", 20)+1) + "*";
			    		
			    		
			    		cell.setCellValue(authorship);
			    		
			    		cell = row.createCell(i++);
			    		if ( d.getAuthorListWithDeptAndAuthType().length() > 10000 ) 
			    			cell.setCellValue(  "Yes" );
			    		else
			    			cell.setCellValue(d.getExceed_char_ind());
			    		
			    		numOfRows ++;
		    		}
	    		}
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm004DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		CellStyle valueStyle=null;
        valueStyle=workbook.createCellStyle();
        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("#,##0.00"));
		
		List<String> orgUnitList = publicationDao.getOrgUnitList(getAccessIconnectDeptList(),getSelectedDept());
		
		
		List<String> researchTypeList = publicationDao.getResearchTypeList();
		
		
		Map<String, Integer> noStaffMap = publicationDao.getOrgUnitNoStaffMap(selectedCdcfPeriods, getAccessIconnectDeptList(),getSelectedDept());		
		
		List<String> headerList = new ArrayList<String>(orgUnitList);
		headerList.add(0, "");
		headerList.add("TOTAL");
		
    	String[] headerArray = headerList.toArray(new String[0]);
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_004");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		//if(n!=0) sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		

		Map<String, Map<String, Map<String, Double>>> dataList = publicationDao.getCHRM004ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), 
					dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessIconnectDeptList(),getSelectedDept());
		

		
		
		if (! dataList.isEmpty()) {
			
			Map<String, Double> totalMap = new HashMap<String, Double>();
			// Create data rows
			for(Map.Entry<String, Map<String, Map<String, Double>>> outputType : dataList.entrySet()) {
				
				
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(0);
	    		cell.setCellValue(outputType.getKey());
	    		
	    		for(String sap : researchTypeList) {
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			cell = row.createCell(0);
		    		cell.setCellValue(sap);
		    		
		    		Map<String, Double> weight = outputType.getValue().get(sap);
		    		
		    		int i = 1;
		    		Double sum = 0.0;
		    		for(String d : orgUnitList) {
		    			
		    			
		    			if(totalMap.get(d) == null) totalMap.put(d, 0.0);
		    			cell = row.createCell(i++);
//		    			cell.setCellStyle(valueStyle);  
		    			if(weight == null || weight.get(d) == null)
		    				cell.setCellValue(0.0);
		    			else {
		    				cell.setCellValue(roundAvoid(weight.get(d),5));
		    				sum += weight.get(d);
		    				totalMap.put(d, totalMap.get(d) + weight.get(d));
		    			}
		    		}
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(roundAvoid(sum,5));
	    		}
	    		numOfRows += 5;
			}
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("Total");
    		int i = 1;
    		Double sum = 0.0;
    		for(String d : orgUnitList) {
    			cell = row.createCell(i++);
    			cell.setCellValue(roundAvoid(totalMap.get(d),5));
    			sum += totalMap.get(d);
    		}
    		
    		Double outputSumCount = sum;
    		
    		cell = row.createCell(i);
    		cell.setCellValue(roundAvoid(sum,5));
    		numOfRows ++;
    		row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("No of Staff");
    		i=1;
    		sum = 0.0;
    		
    		
    		for(String d : orgUnitList) {
    			
       			if(noStaffMap.containsKey(d)) {
	    			cell = row.createCell(i++);
	    			cell.setCellValue(noStaffMap.get(d));
	    			
	    			sum += noStaffMap.get(d);
    			}
    			else {
    				cell = row.createCell(i++);
	    			cell.setCellValue(0);
    			}
    		}
    		
    		
    		cell = row.createCell(i);
    		cell.setCellValue(roundAvoid(sum,5));
    		
    		Double staffSumCount = sum;
    		
    		
    		numOfRows ++;
    		row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("No of Output per Staff");
    		i = 1;
    		sum = 0.0;
    		for(String d : orgUnitList) {
    			cell = row.createCell(i++);
    			
    			if(noStaffMap.containsKey(d)) {
	    			if(noStaffMap.get(d) != 0.0 ) {
	    				cell.setCellValue(roundAvoid(totalMap.get(d)/noStaffMap.get(d),5));
	    				sum += totalMap.get(d)/noStaffMap.get(d);
	    			}
	    			else
	    				cell.setCellValue(0.0);
    			}
    			else 
    				cell.setCellValue(0.0);
    		}
    		cell = row.createCell(i);
    		cell.setCellValue(roundAvoid(outputSumCount/staffSumCount,5));
    		numOfRows ++;
    		
    		
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm009DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 2);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/MM/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		CellStyle valueStyle=null;
        valueStyle=workbook.createCellStyle();
        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("#,##0.00"));
		
		Map<String, String> outputTypeMap = publicationDao.getOutputTypeLvl1List();
		
		PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_009");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, Map<String, Map<String, Double>>> dataList = publicationDao.getCHRM009ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessIconnectDeptList(),getSelectedDept());
		
		
		if (dataList != null) {
			// Create data rows
			for(Map.Entry<String, Map<String, Map<String, Double>>> dept : dataList.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				int deptIndex = dept.getKey().indexOf("_"); 
				
				cell = row.createCell(0);
	    		cell.setCellValue(dept.getKey().substring(0,deptIndex));
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(0);
	    		cell.setCellValue("Name");
	    		
	    		cell = row.createCell(1);
	    		cell.setCellValue("Staff Number");
	    		
	    		cell = row.createCell(2);
	    		cell.setCellValue("Dept");
	    		
	    		int i=3;
	    		for(String type : outputTypeMap.keySet()) {
	    			cell = row.createCell(i);
		    		cell.setCellValue(type);
		    		i++;
	    		}
	    		numOfRows += 2;
	    		Map<String, Double> totalMap = new HashMap<String, Double>();
	    		for(Map.Entry<String, Map<String,Double>> name : dept.getValue().entrySet()) {
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			int nameIndex = name.getKey().indexOf("_");
	    			
	    			cell = row.createCell(0);
		    		cell.setCellValue(name.getKey().substring(0,nameIndex));
		    		
		    		cell = row.createCell(1);
		    		cell.setCellValue(name.getKey().substring(nameIndex+1));
		    		
		    		cell = row.createCell(2);
		    		cell.setCellValue(dept.getKey().substring(deptIndex+1));
		    		
		    		Map<String,Double> weight = name.getValue();
	    			i = 3;
		    		for(String type : outputTypeMap.keySet()) {
		    			if(totalMap.get(type) == null) totalMap.put(type, 0.0);
		    			
		    			cell = row.createCell(i++);
		    			if(weight == null || weight.get(outputTypeMap.get(type)) == null)
		    				cell.setCellValue("");
		    			else {
		    				cell.setCellValue(roundAvoid(weight.get(outputTypeMap.get(type)),5));
		    				totalMap.put(type, totalMap.get(type) + weight.get(outputTypeMap.get(type)));
		    			}
		    		}
		    		numOfRows ++;
	    		}
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		cell = row.createCell(0);
	    		cell.setCellValue("Total");
	    		
	    		i = 3;
	    		for(String type : outputTypeMap.keySet()) {
	    			cell = row.createCell(i++);
	    			if(totalMap == null || totalMap.get(type) == 0.0)
	    				cell.setCellValue("");
	    			else {
	    				cell.setCellValue(roundAvoid(totalMap.get(type),5));
	    			}
	    		}
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		cell = row.createCell(0);
	    		cell.setCellValue("");
	    		
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm011DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 2);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		CellStyle valueStyle=null;
        valueStyle=workbook.createCellStyle();
        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("#,##0.00"));
		
		Map<String, String> outputTypeMap = publicationDao.getOutputTypeLvl1List();
		
		PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_011");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, Map<String, Map<String, Map<String, Double>>>> dataList = publicationDao.getCHRM011ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessIconnectDeptList(),getSelectedDept());
		
		if (dataList != null) {
			// Create data rows
			for(Map.Entry<String, Map<String, Map<String, Map<String, Double>>>> dept : dataList.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+2);
				
				int indexOfCut = dept.getKey().indexOf("---");
				String deptName =  dept.getKey().substring(0,indexOfCut);
				String deptCode = dept.getKey().substring(indexOfCut+3);
				
				cell = row.createCell(0);
	    		cell.setCellValue(deptName);
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(0);
	    		cell.setCellValue("Name");
	    		
	    		cell = row.createCell(1);
	    		cell.setCellValue("Staff No.");
	    		
	    		cell = row.createCell(2);
	    		cell.setCellValue("Dept Code");
	    		
	    		int i=3;
	    		for(String type : outputTypeMap.keySet()) {
	    			cell = row.createCell(i);
		    		cell.setCellValue(type);
		    		i += 4;
	    		}
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			i=3;
    			for(int j = 0 ; j < outputTypeMap.keySet().size()*4 ; j++) {
    				cell = row.createCell(i++);
    				if(j%4 == 0)
    					cell.setCellValue("R");
    				else if(j%4 == 1)
    					cell.setCellValue("NR");
    				else if(j%4 == 2)
    					cell.setCellValue("C");
    				else if(j%4 == 3)
    					cell.setCellValue("O");
    			}
	    		
	    		numOfRows += 3;
	    		Map<String, Map<String, Double>> totalMap = new HashMap<String, Map<String, Double>>();
	    		for(Map.Entry<String, Map<String,Map<String, Double>>> name : dept.getValue().entrySet()) {
	    			
	    			indexOfCut = name.getKey().indexOf("---");
	    			String staffName = name.getKey().substring(0,indexOfCut);
	    			String staffNo = name.getKey().substring(indexOfCut+3);
	    			
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			cell = row.createCell(0);
		    		cell.setCellValue(staffName);
		    		
		    		cell = row.createCell(1);
		    		cell.setCellValue(staffNo.replace("-", ""));
		    		
		    		cell = row.createCell(2);
		    		cell.setCellValue(deptCode);
		    		
		    		Map<String,Map<String, Double>> typeMap = name.getValue();
	    			i = 3;
		    		for(String type : outputTypeMap.keySet()) {
		    			Map<String,Double> weight = typeMap.get(outputTypeMap.get(type));
		    			if(totalMap.get(type) == null) totalMap.put(type, new HashMap<String, Double>());
		    			
		    			for (int j=0 ; j < 4 ; j++) {
		    				String sap = "";
			    			if(j%4 == 0)
		    					sap = "Y";
		    				else if(j%4 == 1)
		    					sap = "N";
		    				else if(j%4 == 2)
		    					sap = "C";
		    				else if(j%4 == 3)
		    					sap = "99";
		    				if(totalMap.get(type).get(sap) == null) totalMap.get(type).put(sap, 0.0);
			    			
		    				cell = row.createCell(i++);
			    			if(weight == null || weight.get(sap) == null)
			    				cell.setCellValue(0.0);
			    			else {
			    				cell.setCellValue(roundAvoid(weight.get(sap),5));
			    				totalMap.get(type).put(sap, totalMap.get(type).get(sap)+ roundAvoid(weight.get(sap),5));
			    			}
		    			}
		    		}
		    		numOfRows ++;
	    		}
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		cell = row.createCell(0);
	    		cell.setCellValue("Total");
	    		
	    		i = 3;
	    		for(String type : outputTypeMap.keySet()) {
	    			for (int j=0 ; j < 4 ; j++) {
	    				String sap = "";
		    			if(j%4 == 0)
	    					sap = "Y";
	    				else if(j%4 == 1)
	    					sap = "N";
	    				else if(j%4 == 2)
	    					sap = "C";
	    				else if(j%4 == 3)
	    					sap = "99";
		    			cell = row.createCell(i++);
		    			if(totalMap.get(type) == null || totalMap.get(type).get(sap) == null)
		    				cell.setCellValue(0.0);
		    			else {
		    				cell.setCellValue(roundAvoid(totalMap.get(type).get(sap),5));
		    			}
	    			}
	    		}
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm012DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
    	String[] headerArray = {"Outputs", "Period", "Author(s)", "Research Output Category", 
    			"Types of Output", "Weighting", "Staff in this DCC"};
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_012");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String,List<Publication>> dataList = publicationDao.getCHRM012ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessFacDeptList());
		
		if (dataList != null) {
			// Create data rows
			for(Map.Entry<String, List<Publication>> dept : dataList.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(0);
	    		cell.setCellValue("Cost Centre: " + dept.getKey());
	    		
	    		numOfRows ++;
	    		
	    		for(Publication d : dept.getValue()) {
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			int i = 0;
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(d.getApaCitation());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(d.getFromYear());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(d.getNameOtherPos());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(d.getSapOutputType());
		    		
		    		cell = row.createCell(i++);
		    		String sap = "";
		    		switch (d.getSapReferedJournal()) {
		    			case "Y" :
		    				sap = "A";
		    				break;
		    			case "N" :
		    				sap = "B";
		    				break;
		    			case "C" :
		    				sap = "C";
		    				break;
		    			case "99" :
		    				sap = "D";
		    				break;
	    				default :
	    					sap = "error";
		    		}
		    		cell.setCellValue(sap);
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(roundAvoid(d.getWeighting(),5));
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(d.getStaffFullname());
		    		
		    		numOfRows ++;
		    		
	    		}
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm015DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
    	String[] headerArray = {"ROItem", "Researcher", "CResearcher", "DCC", 
    			"OutCat", "Rtype","RGC","Accessibility","APC"};
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_015");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, Map<String, Map<String, Map<String, Map<String,Map<String,List<Double> >>>>>> dataList = 
					publicationDao.getCHRM015ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessIconnectDeptList());
		
		//System.out.println("OUTPUT: "+ dataList);
		
		if (dataList != null) {
			// Create data rows
    		for(Map.Entry<String, Map<String, Map<String, Map<String, Map<String,Map<String,List<Double> >>>>>> researcher : dataList.entrySet()) {
    			for(Map.Entry<String, Map<String, Map<String, Map<String,Map<String,List<Double> >>>>> dcc : researcher.getValue().entrySet()) {
    				for(Map.Entry<String, Map<String, Map<String,Map<String,List<Double> >>>> outputCat : dcc.getValue().entrySet()) {
    					for(Map.Entry<String, Map<String,Map<String,List<Double> >>> researchType : outputCat.getValue().entrySet()) {
    						for(Map.Entry<String,Map<String,List<Double> >> rgc : researchType.getValue().entrySet()) {
    							for(Map.Entry<String,List<Double>> accessibility : rgc.getValue().entrySet()) {
    								
    							
    								row = sheet.createRow(sheet.getLastRowNum()+1);
			    			
					    			int i = 0;
						    		
						    		cell = row.createCell(i++);
						    		cell.setCellValue(roundAvoid(accessibility.getValue().get(0),5));
						    		
						    		cell = row.createCell(i++);
						    		cell.setCellValue(researcher.getKey());
						    		
						    		cell = row.createCell(i++);
						    		cell.setCellValue("");
						    		
						    		cell = row.createCell(i++);
						    		cell.setCellValue(dcc.getKey());
						    		
						    		cell = row.createCell(i++);
						    		cell.setCellValue(outputCat.getKey());
						    		
						    		cell = row.createCell(i++);
						    		String sap = "";
						    		switch (researchType.getKey()) {
						    			case "Y" :
						    				sap = "A";
						    				break;
						    			case "N" :
						    				sap = "B";
						    				break;
						    			case "C" :
						    				sap = "C";
						    				break;
						    			case "99" :
						    				sap = "D";
						    				break;
					    				default :
					    					sap = "error";
						    		}
						    		cell.setCellValue(sap);
						    		
						    		//RGC
						    		cell = row.createCell(i++);
						    		String rgcStr = "";
						    		if(rgc.getKey().equals("Y"))
						    			rgcStr = "1";
						    		else 
						    			rgcStr = "2";
						    		cell.setCellValue(rgcStr);
						    		
						    		//Accessibility
						    		cell = row.createCell(i++);
						    		cell.setCellValue(accessibility.getKey());
						    		
						    		//APC
						    		cell = row.createCell(i++);
						    		
						    		cell.setCellValue(Double.valueOf(accessibility.getValue().get(1)));
					
						    		numOfRows ++;
    							}
    						}
    					}
    				}
    			}
    		}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createCrd001DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfSheets = 0;
		
		List<String> facList = publicationDao.getFacListWithoutOther();
		Map<String, String> facNameMap = publicationDao.getFacNameMap();
		Map<String, String> outTypeMap = publicationDao.getOutTypeMap();
		Map<String, Integer> noStaffMap = publicationDao.getOrgUnitNoStaffMap(selectedCdcfPeriods, null,"All");
		List<String> outTypeList = Arrays.asList("1100", "1200");
		List<String> rankTitleList = Arrays.asList("A*", "A", "B", "C", "Unclassified");
		List<String> sjrTitleList = Arrays.asList("Q1", "Q2", "Q3", "Q4");
		
		Map<String, Map<String, Map<String, Map<String, Integer>>>> rankList = publicationDao.getCRD001RankReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), facList, getAccessFacDeptList(), outTypeList);
		Map<String, Map<String, Map<String, Map<String, Integer>>>> sjrList = publicationDao.getCRD001SJRReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), facList, getAccessFacDeptList(), outTypeList);
		
		
		long startTime = System.currentTimeMillis();
		if(rankList != null || sjrList != null) {
			for(String fac : facList) {
				Map<String, String> deptMap = publicationDao.getDeptMapByFac(fac, getAccessFacDeptList());
				if(deptMap.isEmpty()) continue;
				Sheet sheet = workbook.createSheet(fac);
		    	
				numOfSheets++;
		    	Row row = sheet.createRow(0);
		    	Cell cell = null;
		    	
		    	CellStyle hStyle=null;
		    	// Creating a font
		        Font font= workbook.createFont();
		        //font.setFontName("Arial");
		        font.setColor(IndexedColors.BLACK.getIndex());
		        font.setBold(true);
		    	hStyle=workbook.createCellStyle();
		        hStyle.setFont(font);
		        
		        CreationHelper createHelper = workbook.getCreationHelper();
		        CellStyle cellDateStyle = workbook.createCellStyle();
				cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
				
				CellStyle cellDateStyle2 = workbook.createCellStyle();
				cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
				
				CellStyle cellDateStyle3 = workbook.createCellStyle();
				cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
				
				CellStyle valueStyle=null;
		        valueStyle=workbook.createCellStyle();
		        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("0.000%"));
				
		        PreRpt preRpt = preRptDao.getPreRpt("CRD_1");
		    	//First row header
		    	cell = row.createCell(0);
				cell.setCellValue(preRpt.getRpt_desc());
				cell.setCellStyle(hStyle);  
				
				cell = row.createCell(2);
				Date date = new Date();
				cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
				
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
				
				cell = row.createCell(2);
				cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
				
				// Create data rows
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(3);
	    		cell.setCellValue(facNameMap.get(fac));
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(2);
	    		cell.setCellValue("Department");
	    		
	    		int i=3;
	    		for(String dept : deptMap.keySet()) {
	    			cell = row.createCell(i);
		    		cell.setCellValue(dept);
		    		i += 4;
	    		}
	    		cell = row.createCell(i);
	    		cell.setCellValue("Total");
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(2);
	    		cell.setCellValue("Research Output");
	    		
	    		i=3;
	    		for(int j = 0 ; j < deptMap.keySet().size()+1 ; j++) {
	    			for(String outType : outTypeList) {
	    				cell = row.createCell(i);
			    		cell.setCellValue(outTypeMap.get(outType));
			    		i += 2;
	    			}
	    		}
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		cell = row.createCell(0);
	    		cell.setCellValue("Quality Level");
	    		
	    		cell = row.createCell(1);
	    		cell.setCellValue("EdUHK");
	    		
	    		// RANK
	    		Map<String, Map<String, Integer>> subTotalMap = new HashMap<String, Map<String, Integer>>();
	    		Map<String, Map<String, Integer>> qualityTotalMap = new HashMap<String, Map<String, Integer>>();
	    		for(String rank : rankTitleList) {
	    			Map<String, Integer> rankTotalMap = new HashMap<String, Integer>();
	    			for(String outType : outTypeList) {
	    				rankTotalMap.put(outType, 0);
	    			}
	    			for(String dept : deptMap.keySet()) {
	    				for(String outType : outTypeList) {
	    					Integer val = 0;
	    					if(rankList.get(fac) != null && 
		    						rankList.get(fac).get(rank) != null && 
		    						rankList.get(fac).get(rank).get(dept) != null && 
		    						rankList.get(fac).get(rank).get(dept).get(outType) != null)
	    						val = rankList.get(fac).get(rank).get(dept).get(outType);
		    				rankTotalMap.put(outType, rankTotalMap.get(outType) + val);
		    				if(subTotalMap.get(dept) == null) subTotalMap.put(dept, new HashMap<String, Integer>());
		    				if(subTotalMap.get(dept).get(outType) == null)subTotalMap.get(dept).put(outType, 0);
		    				subTotalMap.get(dept).put(outType, subTotalMap.get(dept).get(outType) + val);
	    				}
	    			}
	    			for(String outType : outTypeList) {
	    				if(qualityTotalMap.get(rank) == null) qualityTotalMap.put(rank, new HashMap<String, Integer>());
	    				if(qualityTotalMap.get(rank).get(outType) == null)qualityTotalMap.get(rank).put(outType, 0);
	    				qualityTotalMap.get(rank).put(outType, rankTotalMap.get(outType));
	    				
	    				if(subTotalMap.get("total") == null) subTotalMap.put("total", new HashMap<String, Integer>());
	    				if(subTotalMap.get("total").get(outType) == null)subTotalMap.get("total").put(outType, 0);
	    				subTotalMap.get("total").put(outType, subTotalMap.get("total").get(outType) + rankTotalMap.get(outType));
	    			}
	    		}
	    		
	    		boolean first = true;
	    		i = 2;
	    		for(String rank : rankTitleList) {
	    			if(first) first = false;
	    			else row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			cell = row.createCell(i++);
		    		cell.setCellValue(rank);
		    		
	    			for(String dept : deptMap.keySet()) {
	    				for(String outType : outTypeList) {
	    					Integer val = 0;
		    				if(rankList.get(fac) != null && 
		    						rankList.get(fac).get(rank) != null && 
		    						rankList.get(fac).get(rank).get(dept) != null && 
		    						rankList.get(fac).get(rank).get(dept).get(outType) != null)
		    					val = rankList.get(fac).get(rank).get(dept).get(outType);
		    				cell = row.createCell(i++);
		    				cell.setCellValue(val);
		    				cell = row.createCell(i++);
		    				cell.setCellStyle(valueStyle);
		    				if(qualityTotalMap.get(rank).get(outType) != 0)
		    					cell.setCellValue(((float)(val)/qualityTotalMap.get(rank).get(outType)));
		    				else
		    					cell.setCellValue(0.0);
	    				}
	    			}
	    			for(String outType : outTypeList) {
	    				cell = row.createCell(i++);
		    			cell.setCellValue(qualityTotalMap.get(rank).get(outType));
		    			cell = row.createCell(i++);
	    				cell.setCellStyle(valueStyle);
	    				cell.setCellValue(qualityTotalMap.get(rank).get(outType) != 0 ? 1.0 : 0.0);
	    			}
	    			i = 2;
	    		}
	    		i = 2;
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(i++);
	    		cell.setCellValue("Sub-total");
	    		
	    		for(String dept : deptMap.keySet()) {
	    			for(String outType : outTypeList) {
	    				Integer val = subTotalMap.get(dept).get(outType);
	    				cell = row.createCell(i++);
	    				cell.setCellValue(val);
	    				cell = row.createCell(i++);
	    				cell.setCellStyle(valueStyle);
	    				if(subTotalMap.get(dept).get(outType) != 0)
	    					cell.setCellValue(((float)(val)/subTotalMap.get("total").get(outType)));
	    				else
	    					cell.setCellValue(0.0);
	    			}
	    		}
	    		for(String outType : outTypeList) {
	    			cell = row.createCell(i++);
		    		cell.setCellValue(subTotalMap.get("total").get(outType) );
	    			cell = row.createCell(i++);
					cell.setCellStyle(valueStyle);
					cell.setCellValue((subTotalMap.get("total").get(outType) != 0 ? 1.0 : 0.0));
	    		}
				
    			// SJR
				row = sheet.createRow(sheet.getLastRowNum()+1);
    			cell = row.createCell(1);
	    		cell.setCellValue("SCImago Journal Rank(SJR)");
    			
    			subTotalMap = new HashMap<String, Map<String, Integer>>();
	    		qualityTotalMap = new HashMap<String, Map<String, Integer>>();
	    		for(String sjr : sjrTitleList) {
	    			Map<String, Integer> sjrTotalMap = new HashMap<String, Integer>();
	    			for(String outType : outTypeList) {
	    				sjrTotalMap.put(outType, 0);
	    			}
	    			for(String dept : deptMap.keySet()) {
	    				for(String outType : outTypeList) {
	    					Integer val = 0;
	    					if(sjrList.get(fac) != null && 
		    						sjrList.get(fac).get(sjr) != null && 
		    						sjrList.get(fac).get(sjr).get(dept) != null && 
		    						sjrList.get(fac).get(sjr).get(dept).get(outType) != null)
	    						val = sjrList.get(fac).get(sjr).get(dept).get(outType);
		    				sjrTotalMap.put(outType, sjrTotalMap.get(outType) + val);
		    				if(subTotalMap.get(dept) == null) subTotalMap.put(dept, new HashMap<String, Integer>());
		    				if(subTotalMap.get(dept).get(outType) == null)subTotalMap.get(dept).put(outType, 0);
		    				subTotalMap.get(dept).put(outType, subTotalMap.get(dept).get(outType) + val);
	    				}
	    			}
	    			for(String outType : outTypeList) {
	    				if(qualityTotalMap.get(sjr) == null) qualityTotalMap.put(sjr, new HashMap<String, Integer>());
	    				if(qualityTotalMap.get(sjr).get(outType) == null)qualityTotalMap.get(sjr).put(outType, 0);
	    				qualityTotalMap.get(sjr).put(outType, sjrTotalMap.get(outType));
	    				
	    				if(subTotalMap.get("total") == null) subTotalMap.put("total", new HashMap<String, Integer>());
	    				if(subTotalMap.get("total").get(outType) == null)subTotalMap.get("total").put(outType, 0);
	    				subTotalMap.get("total").put(outType, subTotalMap.get("total").get("1100") + sjrTotalMap.get(outType));
	    			}
	    		}
	    		
    			first = true;
	    		i = 2;
	    		for(String sjr : sjrTitleList) {
	    			if(first) first = false;
	    			else row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			cell = row.createCell(i++);
		    		cell.setCellValue(sjr);
		    		
	    			for(String dept : deptMap.keySet()) {
	    				for(String outType : outTypeList) {
	    					Integer val = 0;
		    				if(sjrList.get(fac) != null && 
		    						sjrList.get(fac).get(sjr) != null && 
		    						sjrList.get(fac).get(sjr).get(dept) != null && 
		    						sjrList.get(fac).get(sjr).get(dept).get(outType) != null)
		    					val = sjrList.get(fac).get(sjr).get(dept).get(outType);
		    				cell = row.createCell(i++);
				    		cell.setCellValue(val);
		    				cell = row.createCell(i++);
		    				cell.setCellStyle(valueStyle);
		    				if(qualityTotalMap.get(sjr).get(outType) != 0)
		    					cell.setCellValue(((float)(val)/qualityTotalMap.get(sjr).get(outType)));
		    				else
		    					cell.setCellValue(0.0);
	    				}
	    			}
	    			for(String outType : outTypeList) {
	    				cell = row.createCell(i++);
		    			cell.setCellValue(qualityTotalMap.get(sjr).get(outType) );
		    			cell = row.createCell(i++);
	    				cell.setCellStyle(valueStyle);
	    				cell.setCellValue((qualityTotalMap.get(sjr).get(outType) != 0 ? 1.0 : 0.0));
	    			}
	    			i = 2;
	    		}
	    		i = 2;
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(i++);
	    		cell.setCellValue("Sub-total");
	    		
	    		for(String dept : deptMap.keySet()) {
	    			for(String outType : outTypeList) {
	    				Integer val = subTotalMap.get(dept).get(outType);
	    				cell = row.createCell(i++);
			    		cell.setCellValue(val);
			    		cell = row.createCell(i++);
	    				cell.setCellStyle(valueStyle);
	    				if(subTotalMap.get(dept).get(outType) != 0)
	    					cell.setCellValue(((float)(val)/subTotalMap.get("total").get(outType)));
	    				else
	    					cell.setCellValue(0.0);
	    			}
	    		}
	    		for(String outType : outTypeList) {
	    			cell = row.createCell(i++);
	    			cell.setCellValue(subTotalMap.get("total").get(outType));
	    			cell = row.createCell(i++);
					cell.setCellStyle(valueStyle);
					cell.setCellValue((subTotalMap.get("total").get(outType) != 0 ? 1.0 : 0.0));
	    		}
    			
    			// no. of academic staff
    			i = 2;
    			row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(i++);
	    		cell.setCellValue("Total no. of academic staff");
	    		
	    		Integer total = 0;
	    		for(String dept : deptMap.keySet()) {
	    			cell = row.createCell(i);
	    			cell.setCellValue(noStaffMap.get(dept));
	    			total += noStaffMap.get(dept);
	    			i += 4;
	    		}
	    		cell = row.createCell(i);
    			cell.setCellValue(total);
    			
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfSheets + " sheets=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createCrd002DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfSheets = 0;
		
		List<String> facList = publicationDao.getFacListWithoutOther();
		Map<String, String> outTypeMap = publicationDao.getOutTypeMap();
		List<String> outTypeList = Arrays.asList("1100", "1200");
		List<String> rankTitleList = Arrays.asList("A*", "A", "B", "C", "Unclassified");
		List<String> sjrTitleList = Arrays.asList("Q1", "Q2", "Q3", "Q4");
		
		List<Map<String, Map<String, Map<String, Map<String, Integer>>>>> crd002List = publicationDao.getCRD002ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), facList, getAccessFacDeptList(), outTypeList);
		Map<String, Map<String, Map<String, Map<String, Integer>>>> rankList = crd002List.get(0);
		Map<String, Map<String, Map<String, Map<String, Integer>>>> sjrList = crd002List.get(1);
		
		long startTime = System.currentTimeMillis();
		if(rankList != null) {
			for(String fac : facList) {
				Map<String, String> deptMap = publicationDao.getDeptMapByFac(fac, getAccessFacDeptList());
				if(deptMap.isEmpty()) continue;
				Sheet sheet = workbook.createSheet(fac);
		    	
				sheet.createFreezePane(0, 5);
				numOfSheets++;
		    	Row row = sheet.createRow(0);
		    	Cell cell = null;
		    	
		    	CellStyle hStyle=null;
		    	// Creating a font
		        Font font= workbook.createFont();
		        //font.setFontName("Arial");
		        font.setColor(IndexedColors.BLACK.getIndex());
		        font.setBold(true);
		    	hStyle=workbook.createCellStyle();
		        hStyle.setFont(font);
		        
		        CreationHelper createHelper = workbook.getCreationHelper();
		        CellStyle cellDateStyle = workbook.createCellStyle();
				cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
				
				CellStyle cellDateStyle2 = workbook.createCellStyle();
				cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
				
				CellStyle cellDateStyle3 = workbook.createCellStyle();
				cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
				
				CellStyle valueStyle=null;
		        valueStyle=workbook.createCellStyle();
		        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("0.000%"));
				
		        PreRpt preRpt = preRptDao.getPreRpt("CRD_2");
		    	//First row header
		    	cell = row.createCell(0);
				cell.setCellValue(preRpt.getRpt_desc());
				cell.setCellStyle(hStyle);  
				
				cell = row.createCell(2);
				Date date = new Date();
				cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
				
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
				
				cell = row.createCell(2);
				cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
				
				// Create data rows
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
	    		int i=4;
	    		for(String outType : outTypeList) {
					cell = row.createCell(i);
		    		cell.setCellValue(outTypeMap.get(outType));
		    		i+= rankTitleList.size() + 1 + sjrTitleList.size() + 1 + 1;
	    		}
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
	    		i=4;
	    		for(String outType : outTypeList) {
	    			cell = row.createCell(i);
		    		cell.setCellValue("Quality Level - EdUHK");
		    		i+= rankTitleList.size() + 1 ;
		    		cell = row.createCell(i);
		    		cell.setCellValue("Quality Level - SCImago Journal Rank(SJR)");
		    		i+= sjrTitleList.size() + 1 + 1;
	    		}
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
	    		i=0;
    			cell = row.createCell(i++);
	    		cell.setCellValue("Last Name");
	    		cell = row.createCell(i++);
	    		cell.setCellValue("First Name");
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Department");
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Rank");
	    		for(String outType : outTypeList) {
	    			for(String rank : rankTitleList) {
	    				cell = row.createCell(i++);
	    	    		cell.setCellValue(rank);
	    			}
	    			cell = row.createCell(i++);
    	    		cell.setCellValue("Total");
    	    		for(String sjr : sjrTitleList) {
	    				cell = row.createCell(i++);
	    	    		cell.setCellValue(sjr);
	    			}
	    			cell = row.createCell(i++);
    	    		cell.setCellValue("Total");
    	    		cell = row.createCell(i++);
    	    		cell.setCellValue("Total No. of Citations");
	    		}
	    		Map<String, Map<String, Integer>> totalMap = new HashMap<String, Map<String, Integer>>();
	    		for(String outType : outTypeList) {
	    			totalMap.put(outType, new HashMap<String, Integer>());
	    		}
	    		
	    		for(Map.Entry<String, Map<String, Map<String, Integer>>> name : rankList.get(fac).entrySet()) {
	    			
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			i = 0;
	    			String[] nameArr = name.getKey().split("/");
	    			for (String n : nameArr) {
		    			cell = row.createCell(i++);
	    	    		cell.setCellValue(n);
	    			}
	    			Map<String, Integer> valMap = null;
	    			Integer total = 0;
	    			
	    			for(String outType : outTypeList) {
		    			valMap = name.getValue().get(outType);
		    			total = 0;
		    			for(String rank : rankTitleList) {
		    				cell = row.createCell(i++);
		    				if(totalMap.get(outType).get(rank) == null)
	    						totalMap.get(outType).put(rank, 0);
		    				if(valMap != null && valMap.get(rank) != null) {
		    					cell.setCellValue(valMap.get(rank));
		    					total += valMap.get(rank);
		    					totalMap.get(outType).put(rank, totalMap.get(outType).get(rank) + valMap.get(rank));
		    				}
		    				else
		    					cell.setCellValue(0);
		    			}
		    			cell = row.createCell(i++);
		    			cell.setCellValue(total);
		    			if(totalMap.get(outType).get("rankTotal") == null)
    						totalMap.get(outType).put("rankTotal", 0);
    					totalMap.get(outType).put("rankTotal", totalMap.get(outType).get("rankTotal") + total);
		    			
		    			valMap = sjrList.get(fac).get(name.getKey()).get(outType);
		    			total = 0;
		    			for(String sjr : sjrTitleList) {
		    				cell = row.createCell(i++);
		    				if(totalMap.get(outType).get(sjr) == null)
	    						totalMap.get(outType).put(sjr, 0);
		    				if(valMap != null && valMap.get(sjr) != null) {
		    					cell.setCellValue(valMap.get(sjr));
		    					total += valMap.get(sjr);
		    					totalMap.get(outType).put(sjr, totalMap.get(outType).get(sjr) + valMap.get(sjr));
		    				}
		    				else
		    					cell.setCellValue(0);
		    			}
		    			cell = row.createCell(i++);
		    			cell.setCellValue(total);
		    			if(totalMap.get(outType).get("sjrTotal") == null)
    						totalMap.get(outType).put("sjrTotal", 0);
    					totalMap.get(outType).put("sjrTotal", totalMap.get(outType).get("sjrTotal") + total);
		    			
    					if(valMap != null)
    						total += valMap.get(null);
		    			cell = row.createCell(i++);
		    			cell.setCellValue(total);
		    			if(totalMap.get(outType).get("citation") == null)
    						totalMap.get(outType).put("citation", 0);
    					totalMap.get(outType).put("citation", totalMap.get(outType).get("citation") + total);
	    			}
	    		}
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		i = 3;
	    		cell = row.createCell(i++);
    			cell.setCellValue("Total");
    			for(String outType : outTypeList) {
    				for(String rank : rankTitleList) {
    					cell = row.createCell(i++);
    	    			cell.setCellValue(totalMap.get(outType).get(rank));
    				}
    				cell = row.createCell(i++);
	    			cell.setCellValue(totalMap.get(outType).get("rankTotal"));
	    			for(String sjr : sjrTitleList) {
    					cell = row.createCell(i++);
    	    			cell.setCellValue(totalMap.get(outType).get(sjr));
    				}
	    			cell = row.createCell(i++);
	    			cell.setCellValue(totalMap.get(outType).get("sjrTotal"));
	    			cell = row.createCell(i++);
	    			cell.setCellValue(totalMap.get(outType).get("citation"));
    			}
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfSheets + " sheets=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createCrd003DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfSheets = 0;
		
		List<String> facList = publicationDao.getFacListWithoutOther();
		List<String> noAmountList = Arrays.asList("No.", "Amount");
		Map<String, String> facNameMap = publicationDao.getFacNameMap();
		Map<String, Integer> noStaffMap = publicationDao.getOrgUnitNoStaffMap(selectedCdcfPeriods, null,null);
		Map<String, String> fundTypeMap = new LinkedHashMap<String, String>();
		fundTypeMap.put("1005", "ECS");
		fundTypeMap.put("997", "GRF");
		fundTypeMap.put("10006", "HSSPFS");
		fundTypeMap.put("10007", "CRF");
		fundTypeMap.put("10019", "RIF");
		fundTypeMap.put("10012", "ANR/RGC JRS");
		fundTypeMap.put("10011", "NSFC/RGC JRS");
		fundTypeMap.put("10014", "EU/RGC");
		fundTypeMap.put("310", "QEF");
		fundTypeMap.put("10001", "ITF");
		fundTypeMap.put("900", "Research-related Donation(Private Fund)");
		Set<String> fundUnionTypeSet = new HashSet<String>(fundTypeMap.keySet());
		fundUnionTypeSet.add("830");
		fundUnionTypeSet.add("880");
		fundUnionTypeSet.add("1570");
		
		Map<String, Map<String, Map<String, Map<String, Double>>>> projList = projDao.getCRD003ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), facList, getAccessFacDeptList(), fundUnionTypeSet);
		
		long startTime = System.currentTimeMillis();
		if(projList != null) {
			for(String fac : facList) {
				Map<String, String> deptMap = publicationDao.getDeptMapByFac(fac, getAccessFacDeptList());
				if(deptMap.isEmpty()) continue;
				Sheet sheet = workbook.createSheet(fac);
		    	
				numOfSheets++;
		    	Row row = sheet.createRow(0);
		    	Cell cell = null;
		    	
		    	CellStyle hStyle=null;
		    	// Creating a font
		        Font font= workbook.createFont();
		        //font.setFontName("Arial");
		        font.setColor(IndexedColors.BLACK.getIndex());
		        font.setBold(true);
		    	hStyle=workbook.createCellStyle();
		        hStyle.setFont(font);
		        
		        CreationHelper createHelper = workbook.getCreationHelper();
		        CellStyle cellDateStyle = workbook.createCellStyle();
				cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
				
				CellStyle cellDateStyle2 = workbook.createCellStyle();
				cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
				
				CellStyle cellDateStyle3 = workbook.createCellStyle();
				cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
				
				CellStyle valueStyle=null;
		        valueStyle=workbook.createCellStyle();
		        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("0.000%"));
				
		        PreRpt preRpt = preRptDao.getPreRpt("CRD_3");
		    	//First row header
		    	cell = row.createCell(0);
				cell.setCellValue(preRpt.getRpt_desc());
				cell.setCellStyle(hStyle);  
				
				cell = row.createCell(2);
				Date date = new Date();
				cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
				
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
				
				cell = row.createCell(2);
				cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
				
				// Create data rows
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(2);
	    		cell.setCellValue(facNameMap.get(fac));
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(1);
	    		cell.setCellValue("Department");
	    		
	    		int i=2;
	    		for(String dept : deptMap.keySet()) {
	    			cell = row.createCell(i);
		    		cell.setCellValue(dept);
		    		i += 4;
	    		}
	    		cell = row.createCell(i);
	    		cell.setCellValue("Total");
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		i=2;
	    		for(int j = 0 ; j < deptMap.keySet().size()+1 ; j++) {
	    			for(String s : noAmountList) {
	    				cell = row.createCell(i);
			    		cell.setCellValue(s);
			    		i += 2;
	    			}
	    		}
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		cell = row.createCell(0);
	    		cell.setCellValue("Funding Scheme");
	    		
	    		// fund
	    		Map<String, Map<String, Double>> subTotalMap = new HashMap<String, Map<String, Double>>();
	    		Map<String, Map<String, Double>> qualityTotalMap = new HashMap<String, Map<String, Double>>();
	    		for(String fund : fundTypeMap.keySet()) {
	    			Map<String, Double> fundTotalMap = new HashMap<String, Double>();
	    			for(String noAmount : noAmountList) {
	    				fundTotalMap.put(noAmount, 0.0);
	    			}
	    			for(String dept : deptMap.keySet()) {
	    				for(String noAmount : noAmountList) {
	    					Double val = 0.0;
	    					if(projList.get(fac) != null && 
		    						projList.get(fac).get(fund) != null && 
		    						projList.get(fac).get(fund).get(dept) != null && 
		    						projList.get(fac).get(fund).get(dept).get(noAmount) != null)
	    						val = projList.get(fac).get(fund).get(dept).get(noAmount);
		    				fundTotalMap.put(noAmount, fundTotalMap.get(noAmount) + val);
		    				if(subTotalMap.get(dept) == null) subTotalMap.put(dept, new HashMap<String, Double>());
		    				if(subTotalMap.get(dept).get(noAmount) == null)subTotalMap.get(dept).put(noAmount, 0.0);
		    				subTotalMap.get(dept).put(noAmount, subTotalMap.get(dept).get(noAmount) + val);
	    				}
	    			}
	    			for(String noAmount : noAmountList) {
	    				if(qualityTotalMap.get(fund) == null) qualityTotalMap.put(fund, new HashMap<String, Double>());
	    				if(qualityTotalMap.get(fund).get(noAmount) == null)qualityTotalMap.get(fund).put(noAmount, 0.0);
	    				qualityTotalMap.get(fund).put(noAmount, fundTotalMap.get(noAmount));
	    				
	    				if(subTotalMap.get("total") == null) subTotalMap.put("total", new HashMap<String, Double>());
	    				if(subTotalMap.get("total").get(noAmount) == null)subTotalMap.get("total").put(noAmount, 0.0);
	    				subTotalMap.get("total").put(noAmount, subTotalMap.get("total").get(noAmount) + fundTotalMap.get(noAmount));
	    			}
	    		}
	    		
	    		boolean first = true;
	    		i = 1;
	    		for(String fund : fundTypeMap.keySet()) {
	    			if(first) first = false;
	    			else row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			cell = row.createCell(i++);
		    		cell.setCellValue(fundTypeMap.get(fund));
		    		
	    			for(String dept : deptMap.keySet()) {
	    				for(String noAmount : noAmountList) {
	    					Double val = 0.0;
		    				if(projList.get(fac) != null && 
		    						projList.get(fac).get(fund) != null && 
		    						projList.get(fac).get(fund).get(dept) != null && 
		    						projList.get(fac).get(fund).get(dept).get(noAmount) != null)
		    					val = projList.get(fac).get(fund).get(dept).get(noAmount);
		    				cell = row.createCell(i++);
		    				cell.setCellValue(val);
		    				cell = row.createCell(i++);
		    				cell.setCellStyle(valueStyle);
		    				if(qualityTotalMap.get(fund).get(noAmount) != 0)
		    					cell.setCellValue(((val)/qualityTotalMap.get(fund).get(noAmount)));
		    				else
		    					cell.setCellValue(0.0);
	    				}
	    			}
	    			for(String noAmount : noAmountList) {
	    				cell = row.createCell(i++);
		    			cell.setCellValue(qualityTotalMap.get(fund).get(noAmount));
		    			cell = row.createCell(i++);
	    				cell.setCellStyle(valueStyle);
	    				cell.setCellValue(qualityTotalMap.get(fund).get(noAmount) != 0 ? 1.0 : 0.0);
	    			}
	    			i = 1;
	    		}
	    		i = 0;
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(i);
	    		cell.setCellValue("Sub-total");
	    		i += 2;
	    		
	    		for(String dept : deptMap.keySet()) {
	    			for(String noAmount : noAmountList) {
	    				Double val = subTotalMap.get(dept).get(noAmount);
	    				cell = row.createCell(i++);
	    				cell.setCellValue(val);
	    				cell = row.createCell(i++);
	    				cell.setCellStyle(valueStyle);
	    				if(subTotalMap.get(dept).get(noAmount) != 0)
	    					cell.setCellValue(((val)/subTotalMap.get("total").get(noAmount)));
	    				else
	    					cell.setCellValue(0.0);
	    			}
	    		}
	    		for(String noAmount : noAmountList) {
	    			cell = row.createCell(i++);
		    		cell.setCellValue(subTotalMap.get("total").get(noAmount) );
	    			cell = row.createCell(i++);
					cell.setCellStyle(valueStyle);
					cell.setCellValue((subTotalMap.get("total").get(noAmount) != 0 ? 1.0 : 0.0));
	    		}
				
    			// no. of academic staff
    			i = 0;
    			row = sheet.createRow(sheet.getLastRowNum()+1);
    			
    			cell = row.createCell(i);
	    		cell.setCellValue("Total no. of academic staff");
	    		i += 2;
	    		
	    		Integer total = 0;
	    		for(String dept : deptMap.keySet()) {
	    			cell = row.createCell(i);
	    			cell.setCellValue(noStaffMap.get(dept));
	    			total += noStaffMap.get(dept);
	    			i += 4;
	    		}
	    		cell = row.createCell(i);
    			cell.setCellValue(total);
    			
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfSheets + " sheets=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createCrd004DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfSheets = 0;
		
		List<String> facList = publicationDao.getFacListWithoutOther();
		List<String> noAmountList = Arrays.asList("No.", "Amount");
		Map<String, String> facNameMap = publicationDao.getFacNameMap();
		Map<String, String> fundTypeMap = new LinkedHashMap<String, String>();
		fundTypeMap.put("1005", "ECS");
		fundTypeMap.put("997", "GRF");
		fundTypeMap.put("10006", "HSSPFS");
		fundTypeMap.put("10007", "CRF");
		fundTypeMap.put("10019", "RIF");
		fundTypeMap.put("10012", "ANR/RGC JRS");
		fundTypeMap.put("10011", "NSFC/RGC JRS");
		fundTypeMap.put("10014", "EU/RGC");
		fundTypeMap.put("310", "QEF");
		fundTypeMap.put("10001", "ITF");
		fundTypeMap.put("900", "Research-related Donation(Private Fund)");
		Set<String> fundUnionTypeSet = new HashSet<String>(fundTypeMap.keySet());
		fundUnionTypeSet.add("830");
		fundUnionTypeSet.add("880");
		fundUnionTypeSet.add("1570");
		
		Map<String, Map<String, Map<String, Map<String, Double>>>> projList = projDao.getCRD004ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), facList, getAccessFacDeptList(), fundUnionTypeSet);
		
		
		long startTime = System.currentTimeMillis();
		if(projList != null) {
			for(String fac : facList) {
				Map<String, String> deptMap = publicationDao.getDeptMapByFac(fac, getAccessFacDeptList());
				if(deptMap.isEmpty()) continue;
				Sheet sheet = workbook.createSheet(fac);
		    	
				sheet.createFreezePane(0, 5);
				numOfSheets++;
		    	Row row = sheet.createRow(0);
		    	Cell cell = null;
		    	
		    	CellStyle hStyle=null;
		    	// Creating a font
		        Font font= workbook.createFont();
		        //font.setFontName("Arial");
		        font.setColor(IndexedColors.BLACK.getIndex());
		        font.setBold(true);
		    	hStyle=workbook.createCellStyle();
		        hStyle.setFont(font);
		        
		        CreationHelper createHelper = workbook.getCreationHelper();
		        CellStyle cellDateStyle = workbook.createCellStyle();
				cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
				
				CellStyle cellDateStyle2 = workbook.createCellStyle();
				cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
				
				CellStyle cellDateStyle3 = workbook.createCellStyle();
				cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
				
				CellStyle valueStyle=null;
		        valueStyle=workbook.createCellStyle();
		        valueStyle.setDataFormat(createHelper.createDataFormat().getFormat("0.000%"));
				
		        PreRpt preRpt = preRptDao.getPreRpt("CRD_4");
		    	//First row header
		    	cell = row.createCell(0);
				cell.setCellValue(preRpt.getRpt_desc());
				cell.setCellStyle(hStyle);  
				
				cell = row.createCell(2);
				Date date = new Date();
				cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
				
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(0);
				cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
				
				cell = row.createCell(2);
				cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
				
				// Create data rows
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
	    		int i=4;
	    		
				cell = row.createCell(i);
	    		cell.setCellValue("Funding Scheme");
	    		
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
	    		i=4;
	    		for(String fund : fundTypeMap.keySet()) {
	    			cell = row.createCell(i);
		    		cell.setCellValue(fundTypeMap.get(fund));
		    		i+= 2 ;
	    		}
	    		cell = row.createCell(i);
	    		cell.setCellValue("Total");
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
    			
	    		i=0;
    			cell = row.createCell(i++);
	    		cell.setCellValue("Last Name");
	    		cell = row.createCell(i++);
	    		cell.setCellValue("First Name");
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Department");
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Rank");
	    		for(int j = 0 ; j < fundTypeMap.keySet().size() + 1 ; j++) {
	    			for(String n : noAmountList) {
	    				cell = row.createCell(i++);
	    	    		cell.setCellValue(n);
	    			}
	    		}
	    		Map<String, Map<String, Double>> totalMap = new HashMap<String, Map<String, Double>>();
	    		for(String fund : fundTypeMap.keySet()) {
	    			totalMap.put(fund, new HashMap<String, Double>());
	    		}
	    		
	    		for(Map.Entry<String, Map<String, Map<String, Double>>> name : projList.get(fac).entrySet()) {
	    			
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
	    			i = 0;
	    			String[] nameArr = name.getKey().split("/");
	    			for (String n : nameArr) {
		    			cell = row.createCell(i++);
	    	    		cell.setCellValue(n);
	    			}
	    			Map<String, Double> valMap = null;
	    			Map<String, Double> rowTotalMap = new HashMap<String, Double>();
	    			
	    			
	    			for(String fund : fundTypeMap.keySet()) {
		    			valMap = name.getValue().get(fund);
		    			for(String noAmount : noAmountList) {
		    				cell = row.createCell(i++);
		    				if(totalMap.get(fund).get(noAmount) == null)
	    						totalMap.get(fund).put(noAmount, 0.0);
		    				if(rowTotalMap.get(noAmount) == null)
		    					rowTotalMap.put(noAmount, 0.0);
		    				if(valMap != null && valMap.get(noAmount) != null) {
		    					cell.setCellValue(valMap.get(noAmount));
		    					rowTotalMap.put(noAmount, rowTotalMap.get(noAmount) + valMap.get(noAmount));
		    					totalMap.get(fund).put(noAmount, totalMap.get(fund).get(noAmount) + valMap.get(noAmount));
		    				}
		    				else
		    					cell.setCellValue(0);
		    			}
	    			}
	    			for(String noAmount : noAmountList) {
	    				cell = row.createCell(i++);
		    			cell.setCellValue(rowTotalMap.get(noAmount));
		    			if(totalMap.get("total") == null)
    						totalMap.put("total", new HashMap<String, Double>());
		    			if(totalMap.get("total").get(noAmount) == null)
    						totalMap.get("total").put(noAmount, 0.0);
    					totalMap.get("total").put(noAmount, totalMap.get("total").get(noAmount) + rowTotalMap.get(noAmount));
	    			}
	    		}
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		
	    		i = 3;
	    		cell = row.createCell(i++);
    			cell.setCellValue("Total");
    			for(String fund : fundTypeMap.keySet()) {
    				for(String noAmount : noAmountList) {
    					cell = row.createCell(i++);
    	    			cell.setCellValue(totalMap.get(fund).get(noAmount));
    				}
    			}
    			for(String noAmount : noAmountList) {
					cell = row.createCell(i++);
	    			cell.setCellValue(totalMap.get("total").get(noAmount));
				}
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfSheets + " sheets=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrmProjDataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
    	String[] headerArray = {"Project No", "By DCC Report/Research EXCEL/Summary Statistic", 
    			"Staff Census Date", "Activity Code", "Project Title 1", "Project Title 2", "Project Title 3", 
    			"Project Title 4", "Project Summary 1", "Project Summary 2", "Role Inst for T630", 
    			"Released Value", "Approved Amount", "From (DD)", "From (MM)", "From (YYYY)", 
    			"To (DD)", "To (MM)", "To (YYYY)", "Funding Source", "Key Research Areas", 
    			"Other Key Research Areas", "Name of Collaborative Partner", 
    			"Collaborative Partner (Country)", "Collaborative Partner (City)", 
    			"Education Sector", "Education Sector Details", "Research Areas", 
    			"Research Areas Details", "No. of Investigator(s) and the details", 
    			"Disciplinary Areas", "Disciplinary Area Details", "Others Disciplinary Area Details", 
    			"Project Type", "Output Title", "Output Type", "Names of Inst Appeared on Output", 
    			"Actual / Expected Date", "Collaborative Partner(s) with Other UGC-funded Institution(s)", 
    			"Collaborative Partner(s) with Non-local Institution(s)", 
    			"Eligible for reporting T630 Research Grants/Contracts", 
    			"Eligible for reporting T690 Number of Active Research Collaborative with Non-local Institutions", 
    			"Role Inst for T690"};
    	
		List<String> headerList = new ArrayList<String>(Arrays.asList(headerArray));
		
		List<ProjectSummary> dataList = projDao.getCHRMProjReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"));
		int maxNoDtl = 0;
		
		for (ProjectSummary proj : dataList) {
			if(proj.getNoPDtl() > maxNoDtl) maxNoDtl = proj.getNoPDtl();
		}
		for(int i=0 ; i < maxNoDtl ; ++i) {
			headerList.add("Line No");
			headerList.add("Staff No");
			headerList.add("Staff Name");
			headerList.add("Type");
			headerList.add("Non-IEd Staff");
		}
		headerArray = headerList.toArray(new String[0]);
		
		PreRpt preRpt = preRptDao.getPreRpt("CHRM_PROJECT");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    		sheet.setDefaultColumnWidth(headerArray[n].length());
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (dataList != null) {
			// Create data rows
			for(ProjectSummary d:dataList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(d.getProjectNo());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDccStat());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCensusDate());
	    		cell.setCellStyle(cellDateStyle2); 
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getActivity_code());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTitle1());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTitle2());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTitle3());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTitle4());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getProject_summary());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getProject_summary_2());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getRoleInstT630());

	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getReleased_val());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSap_grant_amt());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getFromDay());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getFromMonth());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getFromYear());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getToDay());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getToMonth());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getToYear());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSap_funding_source());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getKeyResearchAreas());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOtherKeyResearchAreas());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getName_of_collaborative_partner());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollaborativePartnerCountryStr(true));
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollaborative_partner_city());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSchCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getSchDtlCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getRsCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getRsDtlCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getTotalNoOfInvestigator());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDaCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getDaDtlCode());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getOtherDaDtl());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getProject_type());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollab_output_title());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollab_output_typeStr());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollab_output_inst());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollab_output_date());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollab_partner_ugc());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollab_partner_non_ins());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollabT630());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getCollabT690());
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(d.getRoleInstT690());
	    		
	    		for(ProjectDetails_P dtl : d.getpDtlList()) {
	    			cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getPk().getLine_no());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getInvestigator_staff_no());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getInvestigator_name());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getInvestigator_type());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(dtl.getNon_ied_staff_flag());
	    		}

	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
		
	public void createProj002Docx(String reportPeriod ) throws Exception {
    	StringBuilder htmlCode = new StringBuilder();
    	
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_002");
    	Map<String, String> invesTypeMap = projDao.getInvestigatorTypeList();
    	Map<String, Map<String, List<ProjectSummary>>> dataList = projDao.getCHRMProj002ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"),getSelectedDept());
    	DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_TIME_FORMAT);
		String period_str = dateToStringWithFormat(getReportStartDate(), "yyyy") +"-" + dateToStringWithFormat(getReportEndDate(), "yy");
		
 
		
       	//HTML Head
       	htmlCode.append("<!DOCTYPE html >" +
				"<html>" + 
					"<head>" + 
					"</head>");
       	
    	//Report Header
    	htmlCode.append("<body style='font-size:14px;font-family:\"Times New Roman\";'>");
    	
		//Report information

		htmlCode.append(""+preRpt.getRpt_desc()+"<br/>");
		htmlCode.append("Report Date: "+ dateFormat.format(new Date()) +"<br/>");
		dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT);
		
		
		htmlCode.append("Reporting Period From: "+dateFormat.format(getReportStartDate())
			+" , Reporting Period To: " + dateFormat.format(getReportEndDate()) +"<br/>");
    	
    	
    	for(Map.Entry<String, Map<String, List<ProjectSummary>>> dept : dataList.entrySet()) {

    		htmlCode.append("<p>Department Name: "+dept.getKey()+"</p>");
    		

    		
    		for(Map.Entry<String, List<ProjectSummary>> name : dept.getValue().entrySet()) {
    			
    	
    			
    			htmlCode.append("<b>"+name.getKey().replace("_", " ")+"</b><br/>");
    			
    			
    			
    			int cdcf_size = name.getValue().stream().filter(x -> x.getCDCFStatus().equals(PreRptView.CDCF_GENERATED)).collect(Collectors.toList()).size();
    			int cdcf_not_size = name.getValue().stream().filter(x -> x.getCDCFStatus().equals(PreRptView.CDCF_NOT_SEL)).collect(Collectors.toList()).size();
    			int countLast = 1;
    			
    			
    			for(ProjectSummary proj : name.getValue()) {
    				
    			
    				
    				if (proj.getCDCFStatus().equals(PreRptView.CDCF_GENERATED))
    					htmlCode.append("<b>"+proj.getProjTitle()+"</b>");
    				else
    					htmlCode.append("<b>##"+proj.getProjTitle()+"</b>");
    				
    				htmlCode.append("<p>Project Type: "+proj.getProject_type()+"</p>");
    				
    				String whiteSpace5 = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
    				String whiteSpace16 = whiteSpace5 + whiteSpace5 + whiteSpace5 + "&nbsp;";
    				
    				
    				//htmlCode.append("<p>Project Type: "+proj.getProject_type()+"</p>");
    				htmlCode.append("<p style='text-align: justify;'>"+(StringUtils.isBlank(proj.getProject_summary())?"..":proj.getProject_summary())+"</p>");
    				if (!StringUtils.isBlank(proj.getProject_summary()) || proj.getProject_summary() != null) {
    					if (proj.getSap_funding_source() != null)
    						htmlCode.append("<p>Source of Funding:"	+"&nbsp;&nbsp;"+whiteSpace5+
    								whiteSpace5+whiteSpace5+whiteSpace16+proj.getSap_funding_source()+"</p>");
    				}
    				
    				htmlCode.append("<p>Commencement Date:"+"&nbsp;&nbsp;"+whiteSpace5+
    						whiteSpace5+whiteSpace16+proj.getFromYear()+"</p>");
    				htmlCode.append("<p>Completion Date:"+"&nbsp;&nbsp;&nbsp;&nbsp;"+whiteSpace5+
    						whiteSpace5+whiteSpace5+whiteSpace16+proj.getToYear()+"</p>");
    				List<ProjectDetails_P> chiefList = new ArrayList<ProjectDetails_P>();
    				List<ProjectDetails_P> coList = new ArrayList<ProjectDetails_P>();
    				List<ProjectDetails_P> teamMemList = new ArrayList<ProjectDetails_P>();
    				List<ProjectDetails_P> collabList = new ArrayList<ProjectDetails_P>();
    				List<ProjectDetails_P> otherList = new ArrayList<ProjectDetails_P>();

    				
    				for(ProjectDetails_P dtl : proj.getpDtlList()) {
    					if(dtl.getInvestigator_type().equals("PRINCIPAL INVESTIGATOR"))
    						chiefList.add(dtl);
    					else if(dtl.getInvestigator_type().equals("CO-INVESTIGATOR"))
    						coList.add(dtl);
    					else if(dtl.getInvestigator_type().equals("COLLABORATOR") )
    						collabList.add(dtl);
    					else if (dtl.getInvestigator_type().equals("TEAM MEMBER"))
    						teamMemList.add(dtl);	
    					else
    						otherList.add(dtl);
    				}
    	
    				
    				
    				boolean first = true;
    				if(!chiefList.isEmpty()) {
    					for(ProjectDetails_P dtl : chiefList) {
    						String nameStr = dtl.getFullName();
    						
    						if (dtl.getNon_ied_staff_flag().equals("Y") || dtl.getNon_ied_staff_flag().equals("F"))
    							nameStr += "*";
    						
    						if(first) {
    							htmlCode.append("<p>Principal Investigator(s):"+"&nbsp;&nbsp;&nbsp;"+whiteSpace5+whiteSpace16+nameStr+"</p>");
    							first = false;
    						}
    						else {
    							htmlCode.append("<p>");
			    				//for space
	    							for(int i = 0 ;i<4;i++)
			    					htmlCode.append(whiteSpace16);
    							htmlCode.append(nameStr+"</p>");
		    				}
    						
    					}
    				}
    				
    	
    				
    				first = true;
    				if(!otherList.isEmpty()) {
    					for(ProjectDetails_P dtl : otherList) {
    						
    						String nameStr = dtl.getFullName();
    						if (dtl.getNon_ied_staff_flag().equals("Y") || dtl.getNon_ied_staff_flag().equals("F") )
    							nameStr += "*";
    						
    						String type = invesTypeMap.get(dtl.getInvestigator_type());
    						if(type.equals("Others")) type = "";
    						if (type.equals("Co-Principal Investigator")) {
	    						if(first) {
	    							htmlCode.append("<p>Co-Principal Investigator(s):"+"&nbsp;&nbsp;"+whiteSpace16+nameStr+"</p>");
	    							first = false;
	    						}
	    						else {
	    							htmlCode.append("<p>");
	    		    				//for space
	    		    				for(int i = 0 ;i<4;i++)
	    		    					htmlCode.append(whiteSpace16);
	    							htmlCode.append(nameStr+"</p>");
	    						}
    						}
    						
    					}
    				}
  
    				
    				first = true;
    				if(!coList.isEmpty()) {
    					for(ProjectDetails_P dtl : coList) {
       						String nameStr = dtl.getFullName();
    						if (dtl.getNon_ied_staff_flag().equals("F") || dtl.getNon_ied_staff_flag().equals("F") )
    							nameStr += "*";
    						
    						
    						if(first) {
    							htmlCode.append("<p>Co-Investigator(s):"+"&nbsp;&nbsp;"+whiteSpace16+whiteSpace16+nameStr+"</p>");
    							first = false;
    						}
    						else {
    							htmlCode.append("<p>");
			    				//for space
			    				for(int i = 0 ;i<4;i++)
			    					htmlCode.append(whiteSpace16);
								htmlCode.append(nameStr+"</p>");
    						}
    						
    					}
    				}
    				
    				//Team Member(s)
    				
     				first = true;
    				if(!teamMemList.isEmpty()) {
    					for(ProjectDetails_P dtl : teamMemList) {
       						String nameStr = dtl.getFullName();
    						if (dtl.getNon_ied_staff_flag().equals("Y") || dtl.getNon_ied_staff_flag().equals("F") )
    							nameStr += "*";
    						
    						
    						if(first) {
    							htmlCode.append("<p>Team Member(s):"+whiteSpace16+whiteSpace16+"&nbsp;&nbsp;&nbsp;"+nameStr+"</p>");
    							first = false;
    						}
    						else {
    							htmlCode.append("<p>");
			    				//for space
			    				for(int i = 0 ;i<4;i++)
			    					htmlCode.append(whiteSpace16);
								htmlCode.append(nameStr+"</p>");
    						}
    						
    					}
    				}
    				
    				// Collaborator(s):
    				
    				first = true;
    				if(!collabList.isEmpty()) {
    					for(ProjectDetails_P dtl : collabList) {
       						String nameStr = dtl.getFullName();
    						if (dtl.getNon_ied_staff_flag().equals("Y") || dtl.getNon_ied_staff_flag().equals("F") )
    							nameStr += "*";
    						
    						
    						if(first) {
    							htmlCode.append("<p>Collaborator(s):"+whiteSpace16+whiteSpace16+whiteSpace5+"&nbsp;&nbsp;"+nameStr+"</p>");
    							first = false;
    						}
    						else {
    							htmlCode.append("<p>");
			    				//for space
			    				for(int i = 0 ;i<4;i++)
			    					htmlCode.append(whiteSpace16);
								htmlCode.append(nameStr+"</p>");
    						}
    						
    					}
    				}
    	
    				
    				if (proj.getName_of_collaborative_partner() != null) {
	    				htmlCode.append("<u>Collaborative Partner(s) in Mainland or Overseas"+"</u>");
    					String[] nameArr = proj.getName_of_collaborative_partner().split("_");	
    					String[] countryArr = proj.getCollaborativePartnerCountryStr(true).split("_");
    					
    					if(nameArr.length == countryArr.length) {
	    					for(int i = 0 ; i < nameArr.length ; ++i) {
	    						htmlCode.append("<p>"+whiteSpace16+(i+1)+"Name of Non-local Institution:"+whiteSpace16+nameArr[i]+"</p>");
	    						
	    						htmlCode.append("<p>"+whiteSpace16+"Non-local Institution (Country):"+whiteSpace16+countryArr[i]+"**</p>");
	    					}
    					}
    				}
    				
    					
    				if (proj.getCollab_output_typeStr().length() != 0) {
	    				htmlCode.append("<u>Producing Research Output"+"</u>");
	    				String[] typeArr = proj.getCollab_output_typeStr().split("_");
	    				String[] inStitutionArr = proj.getCollab_output_inst().split("_");
	    				if(typeArr.length == inStitutionArr.length) {
	    					for(int i = 0 ; i<typeArr.length ;i++ ) {
			    				htmlCode.append("<p>Output Type:"+whiteSpace16+typeArr[i]+"</p>");
			    				htmlCode.append("<p>Name of Non-local Institution Appeared/ Will be Appeared on the Output:<br/>"
			    						+inStitutionArr[i]+"</p>");
	    					}
	    				}
    				}
    				
    				htmlCode.append("<u>Reference for RDO"+"</u>");
    				htmlCode.append("<p>Eligible for reporting T630 :"+"&nbsp;&nbsp;&nbsp;"+whiteSpace16+proj.getCollabT630() +"</p>");
    				htmlCode.append("<p>Eligible for reporting T690 :"+"&nbsp;&nbsp;&nbsp;"+whiteSpace16+proj.getCollabT690() +"</p>");
  
    				
    				
    				
    				String project_str = "" ;
    				
    				String deptCode = dept.getKey().substring(dept.getKey().indexOf("("));
    				String signName = name.getKey().substring(0, name.getKey().indexOf("_")) + deptCode;
    				
    				
    				
    				htmlCode.append("<br/>");
    				if(proj.getCDCFStatus().equals(PreRptView.CDCF_NOT_SEL) &&  countLast == cdcf_not_size )
    				{
    					
    					project_str = cdcf_not_size == 1? " project " :" projects " ;
    					
    	    			htmlCode.append("<p>Confirmed that "+cdcf_not_size+project_str+"NOT to be reported to the UGC in "+period_str+" CDCF.</p>");
    					htmlCode.append("<p>");
    					//for space
    					for(int i = 0 ;i<5;i++)
    						htmlCode.append(whiteSpace16);
    					htmlCode.append("Agreed by:_______________________"+"</p>");
    					
    	    			htmlCode.append("<p>");
    	    			for(int i = 0 ;i<6;i++)
    						htmlCode.append(whiteSpace16);
		    			htmlCode.append(signName+"</p>");
		    			
						htmlCode.append("<p>");
						//for space
						for(int i = 0 ;i<16;i++)
							htmlCode.append(whiteSpace16);
						htmlCode.append("Date:_______________________"+"</p>");
    				}
    				else if (proj.getCDCFStatus().equals(PreRptView.CDCF_GENERATED) &&  countLast == cdcf_size+cdcf_not_size ) {
    					
    					project_str = cdcf_size == 1? " project " :" projects " ;
    					
    	    			htmlCode.append("<p>Confirmed that "+cdcf_size+project_str+"to be reported to the UGC in "+period_str+" CDCF.</p>");
    					htmlCode.append("<p>");
    					//for space
    					for(int i = 0 ;i<5;i++)
    						htmlCode.append(whiteSpace16);
    					htmlCode.append(whiteSpace5+"&nbsp;&nbsp;");
    					htmlCode.append("Verified by:_______________________"+"</p>");
    					
    	    			htmlCode.append("<p>");
    	    			for(int i = 0 ;i<6;i++)
    	    						htmlCode.append(whiteSpace16);
    	    			htmlCode.append(signName+"</p>");
    					htmlCode.append("<p>");
    					//for space
    					for(int i = 0 ;i<16;i++)
    						htmlCode.append(whiteSpace16);
    					htmlCode.append("Date:_______________________"+"</p>");
    					
    					
    	    			htmlCode.append("<p>Endorsed to confirm "+cdcf_size+project_str+"to be reported to the UGC in "+period_str+" CDCF.</p>");
    					htmlCode.append("<p>");
    					//for space
    					for(int i = 0 ;i<5;i++)
    						htmlCode.append(whiteSpace16);
    					htmlCode.append(whiteSpace5+"&nbsp;&nbsp;");
    					htmlCode.append("Endorsed by:_______________________"+"</p>");
    					
    	    			htmlCode.append("<p>");
    	    			for(int i = 0 ;i<6;i++)
    	    						htmlCode.append(whiteSpace16);
    	    			htmlCode.append("Head</p>");
    					htmlCode.append("<p>");
    					//for space
    					for(int i = 0 ;i<16;i++)
    						htmlCode.append(whiteSpace16);
    					htmlCode.append("Date:_______________________"+"</p>");	
    				}
    					
    				countLast ++;
    			
    				
    			}
    			
    			htmlCode.append("<p style=\"line-height: 100%; margin-bottom: 0mm; page-break-before: always\">");
    		}
    	}
    	
    	htmlCode.append("</body></html>");
       	Document htmlDocument = Jsoup.parse(htmlCode.toString());
    	htmlDocument.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
    	
    	
    	
    	File cvDocx = new File(preRpt.getRpt_file_name()+".docx");

		WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();
        XHTMLImporterImpl XHTMLImporter = new XHTMLImporterImpl(wordMLPackage);

		wordMLPackage.getMainDocumentPart().getContent().addAll(XHTMLImporter.convert(htmlDocument.outerHtml().replace("nbsp", "#160"), null));
		
		wordMLPackage.save(cvDocx);
		FileInputStream is = new FileInputStream(cvDocx);
		
		dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
		Faces.sendFile(is, preRpt.getRpt_file_name()+"_"+getSelectedDept()+"_"+reportPeriod+ dateFormat.format(new Date())+".docx", false);
		is.close();
	}
	
	private void createChrm005DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_005");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	List<String> orgUnitList = publicationDao.getOrgUnitList(getAccessIconnectDeptList(),"All");
    	int i = 1;
    	for (String dept : orgUnitList)
    	{    		
    		cell = row.createCell(i++);
    		cell.setCellValue(dept);
    	}
    	cell = row.createCell(i++);
		cell.setCellValue("total");
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, String> fundTypeMap = projDao.getFundTypeList();
		Map<String, Map<String, List<Double>>> currList = projDao.getCHRMProj005CurrReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), getAccessIconnectDeptList());
		Map<String, Map<String, List<Double>>> newList = projDao.getCHRMProj005NewReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), getAccessIconnectDeptList());
		
		if (currList != null || newList != null) {
			boolean first = true;
			// Create data rows
			Map<Integer, Map<String, Double>> deptTotalMap = new HashMap<Integer, Map<String, Double>>();
			for(int j = 0 ; j < 4 ; j++) {
				deptTotalMap.put(j, new HashMap<String, Double>());
				for (String dept : orgUnitList)
		    	{    		
					deptTotalMap.get(j).put(dept, 0.0);
		    	}
				deptTotalMap.get(j).put("total", 0.0);
			}
			
			for(Map.Entry<String, String> fund : fundTypeMap.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(0);
	    		cell.setCellValue(fund.getValue());
	    		
	    		for(int j = 0 ; j < 4 ; j++) {
	    			Double fundTotal = 0.0;
	    			
	    			row = sheet.createRow(sheet.getLastRowNum()+1);
	    			
		    		i = 0;
		    		
		    		String type = "";
		    		if(first) {
			    		if(j==0) type = "No of current projects*";
			    		else if(j==1) type = "Value of current projects($)**";
			    		else if(j==2) type = "No of new projects#";
			    		else if(j==3) type = "Value of new projects($)##";
		    		}
		    		else {
			    		if(j==0) type = "No of current projects";
			    		else if(j==1) type = "Value of current projects($)";
			    		else if(j==2) type = "No of new projects";
			    		else if(j==3) type = "Value of new projects($)";
		    		}
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(type);
		    		
		    		for (String dept : orgUnitList) {
		    			double val = 0.0;
		    			if(j==0 && currList.get(fund.getValue()) != null && currList.get(fund.getValue()).get(dept) != null) 
		    				val = currList.get(fund.getValue()).get(dept).get(0);
			    		else if(j==1 && currList.get(fund.getValue()) != null && currList.get(fund.getValue()).get(dept) != null) 
			    			val = currList.get(fund.getValue()).get(dept).get(1);
			    		else if(j==2 && newList.get(fund.getValue()) != null && newList.get(fund.getValue()).get(dept) != null) 
			    			val = newList.get(fund.getValue()).get(dept).get(0);
			    		else if(j==3 && newList.get(fund.getValue()) != null && newList.get(fund.getValue()).get(dept) != null) 
			    			val = newList.get(fund.getValue()).get(dept).get(1);
		    			cell = row.createCell(i++);
		    			if(val != 0.0) {
		    				fundTotal += val;
		    				deptTotalMap.get(j).put(dept, deptTotalMap.get(j).get(dept) + val);
		    				cell.setCellValue(val);
		    			}
		    			else
		    				cell.setCellValue("");
		    		}
		    		cell = row.createCell(i++);
		    		deptTotalMap.get(j).put("total", deptTotalMap.get(j).get("total") + fundTotal);
		    		if(fundTotal != 0)
		    			cell.setCellValue(fundTotal);
		    		else
		    			cell.setCellValue("");
		    		numOfRows ++;
	    		}
	    		first = false;
	    		numOfRows ++;
			}
			
			for(int j = 0 ; j < 4 ; j++) {
				i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				String type = "";
	    		if(j==0) type = "Total no of Current projects";
	    		else if(j==1) type = "Total value of Current projects($)";
	    		else if(j==2) type = "Total no of new projects";
	    		else if(j==3) type = "Total value of new projects($)";
	    		cell = row.createCell(i++);
	    		cell.setCellValue(type);
	    		
				for (String dept : orgUnitList) {
					cell = row.createCell(i++);
		    		cell.setCellValue(deptTotalMap.get(j).get(dept));
				}
				cell = row.createCell(i++);
	    		cell.setCellValue(deptTotalMap.get(j).get("total"));
				numOfRows ++;
			}
			row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("* The total no. of research projects which have not been completed prior to "
    				+ "current financial year. All new research projects are also included.");
    		
    		row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("** The total value of current research projects, which are on the books of HKIEd from "
    				+ dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy") + " to " 
    				+ dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
    		
    		row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("# The total no. of research projects which have been awarded to start in the period of "
    				+ dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy") + " to " 
    				+ dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
    		
    		row = sheet.createRow(sheet.getLastRowNum()+1);
			cell = row.createCell(0);
    		cell.setCellValue("## The total value of new research projects, which have been awarded to start in the period of "
    				+ dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy") + " to " 
    				+ dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm010DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_010");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	Map<String, String> fundTypeMap = projDao.getFundTypeList();
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, String> orgUnitMap = publicationDao.getDeptNameMap();
		Map<String, Map<String, Map<String, List<Double>>>> dataList = projDao.getCHRMProj010ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), getAccessFacDeptList(),getSelectedDept());
		
		
		
		if (dataList != null) {
			// Create data rows
			
			for(Map.Entry<String, Map<String, Map<String, List<Double>>>> dept : dataList.entrySet()) {
				
				
				Map<Integer, Map<String, Double>> fundTotalMap = new HashMap<Integer, Map<String, Double>>();
				for(int j = 0 ; j < 2 ; j++) {
					fundTotalMap.put(j, new HashMap<String, Double>());
					for (String fund : fundTypeMap.keySet())
			    	{    		
						fundTotalMap.get(j).put(fund, 0.0);
			    	}
					fundTotalMap.get(j).put("total", 0.0);
				}
				
				
				// EACH DEPT HEADER
				/*-------------------------------------------------------------------------------*/
				row = sheet.createRow(sheet.getLastRowNum()+2);
				
				cell = row.createCell(0);
	    		cell.setCellValue(orgUnitMap.get(dept.getKey()));
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		int i = 4;
	    		for (Map.Entry<String,String> fund : fundTypeMap.entrySet()) {
	    			cell = row.createCell(i);
	    			cell.setCellValue(fund.getValue());
	    			i += 2;
	    		}
				cell = row.createCell(i);
	    		cell.setCellValue("Total");
	    		
	    		row = sheet.createRow(sheet.getLastRowNum()+1);
	    		i = 0;
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Name of Principal Investigator");
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Staff Number");
	    		    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Dept");
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue("Note");
	    		
	        	for (String fund : fundTypeMap.keySet()) {    		
	        		for(int j = 0 ; j < 2 ; j++) {
		        		cell = row.createCell(i++);
		        		if(j==0)
		        			cell.setCellValue("No of Projects");
		        		if(j==1)
		        			cell.setCellValue("Value of Projects");
	        		}
	        	}
	        	cell = row.createCell(i++);
	        	cell.setCellValue("Total No of Projects");
	        	cell = row.createCell(i++);
	        	cell.setCellValue("Total Value of Projects");
	        	
	        	/*-------------------------------------------------------------------------------*/
	        	
	        	Map<Integer, Double> staffTotal = new HashMap<Integer, Double>();
	        	double totalNo = 0.0;
	        	double totalValue = 0.0;
	        	
	        	

	        	for(Map.Entry<String, Map<String, List<Double>>> name : dept.getValue().entrySet()) {
	        		staffTotal.put(0, 0.0);
		        	staffTotal.put(1, 0.0);
		        	
	        		row = sheet.createRow(sheet.getLastRowNum()+1);
	        		i = 0;
	        		
	        		int nameIndex = name.getKey().indexOf("_");
	        		String staffNumber = name.getKey().substring(nameIndex+1);
	        		
	        		int numberIndex = staffNumber.indexOf("_");
	        		String multiPL = staffNumber.substring(numberIndex+1).equals("null") || 
	        					staffNumber.substring(numberIndex+1).equals("1")  ? "" : "1" ;
	        		
	        		
	        		//NAME
	        		cell = row.createCell(i++);
		        	cell.setCellValue(name.getKey().substring(0,nameIndex));
		        	
		        	
		        	//STAFF NUMBER
		        	cell = row.createCell(i++);
		        	cell.setCellValue(staffNumber.substring(0,numberIndex));
		        	
		        	//DEPT
		        	cell = row.createCell(i++);
		        	cell.setCellValue(dept.getKey());
		        	
		        	//MULTI PLs
		        	cell = row.createCell(i++);
		        	cell.setCellValue(multiPL);
		        	//System.out.println("NO: " + name);
		        	
		        	for (Map.Entry<String,String> fund : fundTypeMap.entrySet()) {
		        		
		        		for(int j = 0 ; j < 2 ; j++) {
		        			cell = row.createCell(i++);
		        			Double val = 0.0;
		        			if(name.getValue().get(fund.getValue()) != null) {
		        					int listSize = name.getValue().get(fund.getValue()).size();
		        					val = name.getValue().get(fund.getValue()).get(j);
		        					
					        		if (listSize > 2 ) {
					        			if (j == 0) {
					        				val += name.getValue().get(fund.getValue()).get(2);
					        			}
					        			else {
					        				val += name.getValue().get(fund.getValue()).get(3);
					        			}
					        		}
					        		if (listSize > 4)
					        		{
					        			if (j == 0) {
					        				val += name.getValue().get(fund.getValue()).get(4);
					        			}
					        			else {
					        				val += name.getValue().get(fund.getValue()).get(5);
					        			}
					        		}
		        			}
		        			if(val != 0.0) {
		        				//Detect new project
		        				if (name.getValue().get(fund.getValue()).size() > 2 && j == 0)
		        					cell.setCellValue("*"+ (int)Math.round(val) );
		        				else
		        					cell.setCellValue(val);
		        				
		        				if (! multiPL.equals("1")) {
			        				staffTotal.put(j, staffTotal.get(j) + val);
			        				fundTotalMap.get(j).put(fund.getKey(), fundTotalMap.get(j).get(fund.getKey()) + val);
		        				}
		        				else if(j == 0 && multiPL.equals("1")) 
		        					fundTotalMap.get(j).put(fund.getKey(), fundTotalMap.get(j).get(fund.getKey()) + val);
		        	
		        			}
		        			else
		        				cell.setCellValue("");
		        		}
		        	}

		        	//added the total column value into total row;
		        	fundTotalMap.get(0).put("total", fundTotalMap.get(0).get("total") + staffTotal.get(0));
		        	fundTotalMap.get(1).put("total", fundTotalMap.get(1).get("total") + staffTotal.get(1));
		        	
		        	cell = row.createCell(i++);
	        		cell.setCellValue(staffTotal.get(0));
	        	
	        		cell = row.createCell(i++);
	        		cell.setCellValue(staffTotal.get(1));
		        	numOfRows ++;
	        	}
        		
	        	row = sheet.createRow(sheet.getLastRowNum()+1);
        		i = 3;
        		cell = row.createCell(i++);
        		cell.setCellValue("Total");
        		for (String fund : fundTypeMap.keySet()) {
        			for(int j = 0 ; j < 2 ; j++) {
        				cell = row.createCell(i++);
		        		cell.setCellValue(fundTotalMap.get(j).get(fund));
        			}
        		}
        		cell = row.createCell(i++);
        		cell.setCellValue(fundTotalMap.get(0).get("total"));
        		cell = row.createCell(i++);
        		cell.setCellValue(fundTotalMap.get(1).get("total"));
        		
        		numOfRows ++ ;

        		
        		row = sheet.createRow(sheet.getLastRowNum()+2);
        		i = 0;
        		cell = row.createCell(i);
        		cell.setCellValue("Note 1: The project involved more than one principal investigator and was recorded in the category of the first principal investigator.");
        		
        		numOfRows ++ ;
        		
        		row = sheet.createRow(sheet.getLastRowNum()+1);
        		cell = row.createCell(i);
        		cell.setCellValue("* New research projects which have been awarded to start in the period of "+dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy")+" to "+ dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
        		
        		numOfRows ++ ;
        		
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm013DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_013");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	String[] headerArray = {"","Project Title", "Principal Investigator", "Amount of Granted (HK$)", 
    			"Received Amount(HK$)", "Start Date", "Completion Date", "Funding Source", "Activity Code"};
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    	}
    	
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, List<ProjectSummary>> dataList = projDao.getCHRMProj013ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), getAccessFacDeptList(),getSelectedDept());
		
		//System.out.println("OUTPUT:"+ dataList);
		
		if (dataList != null) {
			// Create data rows			
			for(Map.Entry<String, List<ProjectSummary>> dcc : dataList.entrySet()) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				cell = row.createCell(1);
	    		cell.setCellValue("Cost Centre: "+ dcc.getKey());
				
				for(ProjectSummary proj : dcc.getValue()) {
		    		row = sheet.createRow(sheet.getLastRowNum()+1);
		    		int i = 0;
		    		
					cell = row.createCell(i++);
		    		cell.setCellValue(proj.getDccStat().equals("1") ? "*" : "");
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getProjTitle());
		    		
		    		cell = row.createCell(i++);
		    		String prinInves = "";
		    		for(ProjectDetails_P dtl : proj.getpDtlList()) {
		    			if(dtl.getInvestigator_type().equals("PRINCIPAL INVESTIGATOR")) {
		    				
		    				if(!StringUtils.isBlank(prinInves)) prinInves += ", ";
		    				if(dtl.getFullName().contains("[")) {
		    					prinInves += dtl.getFullName().substring(0, dtl.getFullName().indexOf("["));
		    				}
		    				else
		    					prinInves += dtl.getFullName();
		    			}
		    		}
		    		
		    		//remove dept name;
		    		cell.setCellValue(prinInves);
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getSap_grant_amt());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getReleased_val());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getFromYear());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getToYear());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getSap_funding_source());
		    		
		    		cell = row.createCell(i++);
		    		cell.setCellValue(proj.getActivity_code());
				}
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm014DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_014");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	String[] headerArray = {"DCC", "Fund Source", "Project Type", "Role Inst", "Project No", 
    			"PjSYear", "PjEnd", "New No", "New Value", "New Val_NRel", "Cur No", "Cur Value", "On Val_NRel"};
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    	}
    	
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, List<String>> dataList = projDao.getCHRMProj014ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), getAccessFacDeptList());
		
		if (dataList != null) {
			// Create data rows			
			for(Map.Entry<String, List<String>> key : dataList.entrySet()) {
				int i = 0;
				
				String[] keyArr = key.getKey().split("_");
				List<String> valList = key.getValue();
				
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(keyArr[0]);
	    		
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(keyArr[1]);
	    		
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(keyArr[2]);
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(keyArr[3]);
	    		

	    		cell = row.createCell(i++);
	    		cell.setCellValue(valList.get(0).replace(",", ", "));
	    		//System.out.println(valList.get(0).replace(",", ", "));
	    		
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(keyArr[4]);
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(keyArr[5]);
	    		
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(Double.valueOf(valList.get(1)));
			
	    		cell = row.createCell(i++);
	    		cell.setCellValue(Double.valueOf(valList.get(2)));
	    		
	    		cell = row.createCell(i++);
	    		cell.setCellValue(Double.valueOf(valList.get(3)) - Double.valueOf(valList.get(2)));
	    		
	    		
	    		cell = row.createCell(i++);
	    		if(valList.get(1).equals("0"))
	    			cell.setCellValue(Double.valueOf(valList.get(4)));
	    		else
	    			cell.setCellValue(Double.valueOf(valList.get(1)));
	    		
	    		
	    		cell = row.createCell(i++);
	    		if(valList.get(1).equals("0"))
	    			cell.setCellValue(Double.valueOf(valList.get(2)));
	    		else
	    			cell.setCellValue(Double.valueOf(valList.get(5)));
	    		
	    		cell = row.createCell(i++);
	    		if(valList.get(1).equals("0"))
	    			cell.setCellValue(Double.valueOf(valList.get(6)) - Double.valueOf(valList.get(5)));
	    		else
	    			cell.setCellValue(Double.valueOf(valList.get(3)) - Double.valueOf(valList.get(2)));
	    		
	    		
	    		
	    		numOfRows++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createChrm016DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("CIRD_RP_016");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	String[] headerArray = {"Project No.", "HEI", "CensusDate", "Serial", "ProjName", "Country", "InstName", 
    			"PjSYear", "PjEnd", "Role_inst", "Released_value", "Approved_amount", "Remarks"};
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    	}
    	
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		Map<String, List<String>> dataList = projDao.getCHRMProj016ReportList("C", dateToStringWithFormat(getReportStartDate(), "dd/MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "dd/MM/yyyy"), getAccessFacDeptList());
		
		if (dataList != null) {
			// Create data rows			
			for(Map.Entry<String, List<String>> proj : dataList.entrySet()) {
				List<String> valList = proj.getValue();
				String[] countryArr = {""};
				String[] instArr = {""};
				if(valList.get(5) != null) {
					countryArr = valList.get(5).split("_");
					instArr = valList.get(6).split("_");
				}
//				//System.out.println("projectNo: "+valList.get(0)+"; countryArr: "+valList.get(5)+"; instArr: "+valList.get(6));
				for(int j = 0 ; j < countryArr.length ; ++j) {
					row = sheet.createRow(sheet.getLastRowNum()+1);
					for(int i = 0 ; i < valList.size() ; ++i) {
						cell = row.createCell(i);
						if(i == 5 ) cell.setCellValue(countryArr[j]);
						else if (i == 6) cell.setCellValue(instArr[j]);
						else if((i == 10 || i == 11) && !valList.get(i).equals("NULL")) cell.setCellValue(Double.valueOf(valList.get(i)));
						else cell.setCellValue(valList.get(i));
					}
					numOfRows++;
				}
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createAmis001DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("AMIS_01");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	String[] headerArray = {"Year", "Output No.", "Output Area"};
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    	}
    	
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		List<List<String>> dataList = publicationDao.getAMIS001ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessFacDeptList());
		
		if (dataList != null) {
			// Create data rows			
			for(List<String> rowData : dataList) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				int i = 0;
				for(String data : rowData) {
					cell = row.createCell(i++);
					cell.setCellValue(data);
				}
				numOfRows++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private void createAmis002DataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet();
    	sheet.createFreezePane(0, 3);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellDateStyle = workbook.createCellStyle();
		cellDateStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy HH:mm"));
		
		CellStyle cellDateStyle2 = workbook.createCellStyle();
		cellDateStyle2.setDataFormat(createHelper.createDataFormat().getFormat("d-mmm-yy"));
		
		CellStyle cellDateStyle3 = workbook.createCellStyle();
		cellDateStyle3.setDataFormat(createHelper.createDataFormat().getFormat("mmm-yy"));
		
		
    	PreRpt preRpt = preRptDao.getPreRpt("AMIS_02");
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(preRpt.getRpt_desc());
		cell.setCellStyle(hStyle);  
		
		cell = row.createCell(2);
		Date date = new Date();
		cell.setCellValue("Report Date: " + dateToStringWithFormat(date, "dd-MMM-yyyy HH:mm"));
		
		row = sheet.createRow(sheet.getLastRowNum()+1);
		cell = row.createCell(0);
		cell.setCellValue("Reporting Period From: " + dateToStringWithFormat(getReportStartDate(), "dd-MMM-yyyy"));
		
		cell = row.createCell(2);
		cell.setCellValue("Reporting Period To: " + dateToStringWithFormat(getReportEndDate(), "dd-MMM-yyyy"));
		
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	String[] headerArray = {"Year", "Department", "Education Area Output Weighting", "Other Disciplinary Area Outputs Weighting", 
    			"Award Application in the Annual Exercise for RGC-funded Projects", "RGC-funded Ongoing Research Projects",
    			"Total Awarded Amount of RGC-funded Ongoing Research Projects"};
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    	}
    	
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		List<List<Object>> dataList = publicationDao.getAMIS002ReportList("C", dateToStringWithFormat(getReportStartDate(), "MM/yyyy"), dateToStringWithFormat(getReportEndDate(), "MM/yyyy"), getAccessFacDeptList());
		
		if (dataList != null) {
			// Create data rows			
			for(List<Object> rowData : dataList) {
				row = sheet.createRow(sheet.getLastRowNum()+1);
				int i = 0;
				for(Object data : rowData) {
					cell = row.createCell(i++);
					if(data instanceof String)
						cell.setCellValue((String) data);
					else if(data instanceof Integer)
						cell.setCellValue((Integer) data);
					else if(data != null)
						cell.setCellValue(data.toString());
					else
						cell.setCellValue("");
				}
				numOfRows++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
	
	private Date getReportStartDate()
	{
		Date tmpStartDate = null;
		if (getSelectedCdcfPeriods() != null) {
			List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
			for (int i = 0; i < filterPeriod.size(); i++) {
				if (tmpStartDate == null) {
					tmpStartDate = filterPeriod.get(i).getDate_from();
				}
				if (tmpStartDate.after(filterPeriod.get(i).getDate_from())) {
					tmpStartDate = filterPeriod.get(i).getDate_from();
				}
			}
		}		
		if (getSelectedStartDate() != null) {
//			if (tmpStartDate == null) {
//				tmpStartDate = selectedStartDate;
//			}
//			if (selectedStartDate.before(tmpStartDate)) {
//				tmpStartDate = selectedStartDate;
//			}
			tmpStartDate = selectedStartDate;
		}
		return tmpStartDate;
		
	}
	
	private Date getReportEndDate()
	{
		Date tmpEndDate = null;
		if (getSelectedCdcfPeriods() != null) {
			List<CdcfRptPeriod> filterPeriod = AppPeriodReportDAO.getCdcfRptPeriod(selectedCdcfPeriods);
			for (int i = 0; i < filterPeriod.size(); i++) {
				if (tmpEndDate == null) {
					tmpEndDate = filterPeriod.get(i).getDate_to();
				}
				if (tmpEndDate.before(filterPeriod.get(i).getDate_to())) {
					tmpEndDate = filterPeriod.get(i).getDate_to();
				}
			}
		}		
		if (getSelectedEndDate() != null) {
//			if (tmpEndDate == null) {
//				tmpEndDate = selectedEndDate;
//			}
//			if (selectedEndDate.before(tmpEndDate)) {
//				tmpEndDate = selectedEndDate;
//			}
			tmpEndDate = selectedEndDate;
		}
		return tmpEndDate;
		
	}


	
	public PreRpt getSelectedPreRpt()
	{
		return selectedPreRpt;
	}


	
	public void setSelectedPreRpt(PreRpt selectedPreRpt)
	{
		this.selectedPreRpt = selectedPreRpt;
	}
	
	
	public PreRpt getRemovePreRpt()
	{
		return removePreRpt;
	}


	
	public void setRemovePreRpt(PreRpt removePreRpt)
	{
		this.removePreRpt = removePreRpt;
	}


	public void onRowEdit(RowEditEvent<PreRpt> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
     	try {
 			//Check report is unique
 			int count = 0;
 			for (PreRpt r: allPreRptList){
 				if (event.getObject().getRpt_id().equals(r.getRpt_id())) {
 					count++;
 				}
 			}
 			if (count > 1) {
 				isDuplicateKey = true;
 			}
 			
 			if (isDuplicateKey) {
 				String param = "Report";
 				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
 				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 			}
 			
 			////Check data
 			if (event.getObject().getRpt_code().isEmpty()) {
     			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Report code cannot be null", ""));
     		}
  
 
 			//Update rpt
     		if (fCtx.getMessageList().isEmpty()) {
     			event.getObject().setUserstamp(getLoginUserId());
     			preRptDao.updatePreRpt(event.getObject());
     			if (removePreRpt != null) {
     				preRptDao.deletePreRpt(removePreRpt.getRpt_id());
     				removePreRpt = null;
      			}
     			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
     			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getRpt_code());
        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
        		selectedPreRpt = null;
        		allPreRptList = null;
     		}
     	}
     	catch (IllegalStateException ise)
 		{
 			logger.log(Level.WARNING, "", ise);
 			String param = bundle.getString("Report");
 			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
 		catch (OptimisticLockException ole)
 		{
 			message = bundle.getString("msg.err.optimistic.lock");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}
     	catch (Exception e)
 		{
 			logger.log(Level.WARNING, "", e);
 			message = bundle.getString("msg.err.unexpected");
 			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
 		}       
	}
	
	public void onRowEditRptCat(RowEditEvent<PreRptCat> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
  	try {
			//Check report is unique
			int count = 0;
			for (PreRptCat r: allPreRptCatList){
				if (event.getObject().getRpt_cat().equals(r.getRpt_cat())) {
					count++;
				}
			}
			if (count > 1) {
				isDuplicateKey = true;
			}
			
			if (isDuplicateKey) {
				String param = "Report Cat ";
				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
			
			////Check data
			if (event.getObject().getRpt_cat().isEmpty()) {
  			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Report cat cannot be null", ""));
  		}


			//Update rpt
  		if (fCtx.getMessageList().isEmpty()) {
  			event.getObject().setUserstamp(getLoginUserId());
  			preRptDao.updatePreRptCat(event.getObject());
  			if (removePreRptCat != null) {
  				preRptDao.deletePreRptCat(removePreRptCat.getRpt_cat());
  				removePreRptCat = null;
   			}
  			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
  			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getRpt_cat());
     		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
     		fCtx.getExternalContext().getFlash().setKeepMessages(true);
     		selectedPreRptCat = null;
     		allPreRptCatList = null;
  		}
  	}
  	catch (IllegalStateException ise)
		{
			logger.log(Level.WARNING, "", ise);
			String param = bundle.getString("Report Cat");
			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
		catch (OptimisticLockException ole)
		{
			message = bundle.getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
  	catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
			message = bundle.getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}       
	}
	
	public void onRowCancel(RowEditEvent<PreRpt> event) {
	     FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getRpt_code()));
	     FacesContext.getCurrentInstance().addMessage(null, msg);
	 }
	
	public void onRowCancelRptCat(RowEditEvent<PreRptCat> event) {
	     FacesMessage msg = new FacesMessage("Edit Cancelled", "Cat: "+String.valueOf(event.getObject().getRpt_cat()));
	     FacesContext.getCurrentInstance().addMessage(null, msg);
	 }
	
	 public void onCellEdit(CellEditEvent event) {
	     Object oldValue = event.getOldValue();
	     Object newValue = event.getNewValue();
	 }
	 
	 public void onCellEditRptCat(CellEditEvent event) {
	     Object oldValue = event.getOldValue();
	     Object newValue = event.getNewValue();
	 }
	 
	 public void onAddNew() {
	 	PreRpt newItem = new PreRpt();
	 	allPreRptList.add(0, newItem);
	 }
	
	 public void onAddNewRptCat() {
		 	PreRptCat newItem = new PreRptCat();
		 	allPreRptCatList.add(0, newItem);
		 }
	 
	 public void keyChangedListener(ValueChangeEvent event) {
	 	if (event.getOldValue() != null) {
	 		removePreRpt =  preRptDao.getPreRptById((Integer)event.getOldValue());
	 	}
	 } 
	 
	 public void deletePreRpt() {
    	if (selectedPreRpt != null) {
    		try {
    			if (selectedPreRpt.getRpt_id() != null) {
    				preRptDao.deletePreRpt(selectedPreRpt.getRpt_id());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedPreRpt.getRpt_code());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
    			allPreRptList.remove(selectedPreRpt);
    			selectedPreRpt = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedPreRpt.getRpt_code());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
	 
	 public void deletePreRptCat() {
	    	if (selectedPreRptCat != null) {
	    		try {
	    			if (selectedPreRptCat.getRpt_cat() != null) {
	    				preRptDao.deletePreRptCat(selectedPreRptCat.getRpt_cat());
				    	
				        String message = "msg.success.delete.x";
		    			message = MessageFormat.format(getResourceBundle().getString(message), selectedPreRptCat.getRpt_cat());
		        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
		        		FacesContext.getCurrentInstance().addMessage(null, msg);
		        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
	    			}
	    			allPreRptCatList.remove(selectedPreRptCat);
	    			selectedPreRptCat = null;
	    		}
	    		catch(IllegalArgumentException e){
	    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedPreRptCat.getRpt_cat());
	    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
	    		}
	    	}
	    }
	 
	public void reloadPreRptList() 
	{
		allPreRptList = null;
		allPreRptCatList = null;
	}


	//Filter by access data list
	public List<String> getAccessFacDeptList() throws SQLException
	{	
		accessFacDeptList = AppPeriodReportDAO.getAccessFacultyDeptList(getAccessDeptList());
		
		//System.out.println("DEPT: " +getAccessDeptList());
		return accessFacDeptList;
	}
	
	
	public List<String> getAccessIconnectDeptList() throws SQLException
	{
		
		accessFacDeptList = AppPeriodReportDAO.getAccessIconnectUnitList(getAccessDeptList());

		return accessFacDeptList;
	}
	
	
	//ALL DATA DEPT LIST
	public List<String> getAllAccessFacDeptList() throws SQLException
	{	
		accessFacDeptList = AppPeriodReportDAO.getALLFacultyDeptList();
		
		//System.out.println("DEPT: " +getAccessDeptList());
		return accessFacDeptList;
	}
	
	


	
	public void setAccessFacDeptList(List<String> accessFacDeptList)
	{
		this.accessFacDeptList = accessFacDeptList;
	}


	
	public List<String> getAccessDeptList() throws SQLException
	{
		if (accessDeptList == null) {
			accessDeptList = getRiAdminDeptList();
			if (!accessDeptList.isEmpty()) {
				LookupValueDAO dao = LookupValueDAO.getCacheInstance();
				StaffDAO staffDao = StaffDAO.getInstance();
				List<String> facDeptList = new ArrayList<>();
				List<LookupValue> facList = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
				for (LookupValue f:facList) {
					if (accessDeptList.contains(f.getPk().getLookup_code())) {
						List<FacDept> tmpList = staffDao.getFacDeptByFac(f.getPk().getLookup_code());
						for (FacDept d:tmpList) {
							if (!facDeptList.contains(d.getFac_dept())) {
								facDeptList.add(d.getFac_dept());
							}
						}
					}
				}
				Set<String> accessDeptSet = new LinkedHashSet<>(accessDeptList);
				accessDeptSet.addAll(facDeptList);
				accessDeptList = new ArrayList<>(accessDeptSet);
			}
		}
		//System.out.println("accessDeptList:"+accessDeptList);
		return accessDeptList;
	}


	
	public void setAccessDeptList(List<String> accessDeptList)
	{
		this.accessDeptList = accessDeptList;
	}


	
	public String getChartImageBase64()
	{
		return chartImageBase64;
	}


	
	public void setChartImageBase64(String chartImageBase64)
	{
		this.chartImageBase64 = chartImageBase64;
	}
	
	
	public boolean getIsChartGenerated()
	{
		return isChartGenerated;
	}


	
	public void setIsChartGenerated(boolean isChartGenerated)
	{
		this.isChartGenerated = isChartGenerated;
	}
	
	public void disableDownloadButton(){
		isChartGenerated = false;
	}
	
	public void updateSelectedRpt(){
		selectedRpt = "";
	}
}
