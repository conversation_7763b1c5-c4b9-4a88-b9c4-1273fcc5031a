package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.ejb.EJB;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.LookupFormValue;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.form.FormDAO;
import hk.eduhk.rich.entity.form.KtForm;
import hk.eduhk.rich.entity.form.KtFormCPD_P;
import hk.eduhk.rich.entity.form.KtFormCntProj_P;
import hk.eduhk.rich.entity.form.KtFormCons_P;
import hk.eduhk.rich.entity.form.KtFormDetails_Q;
import hk.eduhk.rich.entity.form.KtFormEA_P;
import hk.eduhk.rich.entity.form.KtFormIP_P;
import hk.eduhk.rich.entity.form.KtFormInn_P;
import hk.eduhk.rich.entity.form.KtFormInvAward_P;
import hk.eduhk.rich.entity.form.KtFormProfConf_P;
import hk.eduhk.rich.entity.form.KtFormProfEngmt_P;
import hk.eduhk.rich.entity.form.KtFormSem_P;
import hk.eduhk.rich.entity.form.KtFormService;
import hk.eduhk.rich.entity.form.KtFormSocEngmt_P;
import hk.eduhk.rich.entity.form.KtFormStaffEngmt_P;
import hk.eduhk.rich.entity.form.KtFormStartup_P;
import hk.eduhk.rich.entity.form.KtFormSummary;
import hk.eduhk.rich.entity.operation.UploadStatus;
import hk.eduhk.rich.entity.operation.UploadStatusPK;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.entity.report.KtRptSum;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.report.form.*;
import hk.eduhk.rich.report.upload.*;

@ManagedBean(name = "uploadKtActView")
@ViewScoped
@SuppressWarnings("serial")
public class UploadKtActivitiesView extends BaseView
{
	@EJB
	private KtFormService ktFormService;
	
	private static Logger logger = Logger.getLogger(UploadKtActivitiesView.class.getName());
	
	private static String fileUploadExtension = "xls, xlsx";
	
	private static String invalidStartEndDateMsg = "Start Date cannot be greater than End Date.";
	private static String invalidDataMsg = "Invalid value in {0}.";
	
	private static boolean validateStartEndDate = false;
	
	private List<KtRptPeriod> periodList = null;

	private List<KtForm> formList = null;
	
	private Map<String, List<String>> facDeptMap = null;
	private List<String> deptList = null;
	
	private List<LookupFormValue> lookupFormValueList = null;
	
	private List<KtFormSummary> ktFormSummaryList = null;
	
	private Map<Integer, List<UploadStatus>> uploadStatusMap = null;
	
//	private UploadedFile uploadedFile = null;
//	private long uploadedFileSize;
//	private InputStream uploadedFileIS;
//	private String uploadedFileName = null;
	
	private Map<Integer, UploadedFile> uploadedFileMap = null;
	private Map<Integer, Workbook> workbookMap = null;
	
	private FormDAO fDao = FormDAO.getInstance();
	
	private KtRptDAO dao = KtRptDAO.getInstance();
	
	public void fileUploadListener(FileUploadEvent event) throws IOException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		try 
		{
			UploadedFile uploadedFile = event.getFile();
			Integer paramPeriodId = (Integer) event.getComponent().getAttributes().get("periodId");
			
			
			
			String message = "";
			if(uploadedFile == null)
			{
				message = getResourceBundle().getString("msg.err.invalid.file.upload.incomplete");
				throw new Exception(message);
			}
			
			if(uploadedFile.getFileName().lastIndexOf(".") == -1)
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), fileUploadExtension);
				throw new Exception(message);
			}
			
			int index = uploadedFile.getFileName().lastIndexOf(".");
			String fileExtension = uploadedFile.getFileName().substring(index+1);
			List<String> extensionsList = new ArrayList<String>(Arrays.asList(fileUploadExtension.split(", ")));
			if(!extensionsList.contains(fileExtension))
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), fileUploadExtension);
				throw new Exception(message);
			}
			
			if(uploadedFile!=null && paramPeriodId!=null && getUploadedFileMap()!=null && getWorkbookMap()!=null) 
			{
				uploadedFileMap.put(paramPeriodId, uploadedFile);
				Workbook wb = new XSSFWorkbook(uploadedFile.getInputStream());
				workbookMap.put(paramPeriodId, wb);
			}
		}
		catch(Exception e)
		{
			String msg = e.getMessage();
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, ""));
			
			logger.log(Level.WARNING, msg);
		}
		
//		uploadedFileSize = uploadedFile.getSize();
//		uploadedFileIS = uploadedFile.getInputStream();
//		uploadedFileName = FilenameUtils.getName(uploadedFile.getFileName());

    }
	
	public String getUploadedFileName(UploadedFile file) 
	{
		if(file!=null) 
		{
			return FilenameUtils.getName(file.getFileName());
		}
		return null;
	}
	
		
	public Map<Integer, UploadedFile> getUploadedFileMap()
	{
		if(uploadedFileMap == null)
			uploadedFileMap = new HashMap<Integer, UploadedFile>();
			
		return uploadedFileMap;
	}
	

	public void setUploadedFileMap(Map<Integer, UploadedFile> uploadedFileMap)
	{
		this.uploadedFileMap = uploadedFileMap;
	}
	

	
	public Map<Integer, Workbook> getWorkbookMap()
	{
		if(workbookMap == null)
			workbookMap = new HashMap<Integer, Workbook>();
		
		return workbookMap;
	}

	
	public void setWorkbookMap(Map<Integer, Workbook> workbookMap)
	{
		this.workbookMap = workbookMap;
	}

	public List<KtFormSummary> getKtFormSummaryList()
	{
		return ktFormSummaryList;
	}

	
	public void setKtFormSummaryList(List<KtFormSummary> ktFormSummaryList)
	{
		this.ktFormSummaryList = ktFormSummaryList;
	}
	
	public List<KtRptPeriod> getPeriodList()
	{
		if (periodList == null) {
			periodList = dao.getKtRptPeriodList();
			//Hide period_id: = 1, 1 = SelectItem: All
			periodList = periodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return periodList;
	}
	
	public void setPeriodList(List<KtRptPeriod> periodList)
	{
		this.periodList = periodList;
	}
	
	
	public List<KtForm> getFormList()
	{
		if (formList == null) {
			formList = FormDAO.getInstance().getKtFormListWithCon(true);
			//summary();
		}
		return formList;
	}

	public void setFormList(List<KtForm> formList)
	{
		this.formList = formList;
	}
		
	
	public Map<Integer, List<UploadStatus>> getUploadStatusMap()
	{
		if(uploadStatusMap == null) 
		{
			uploadStatusMap = new HashMap<Integer, List<UploadStatus>>();
			
			List<UploadStatus> uploadStatusList = FormDAO.getCacheInstance().getUploadStatusList();
			
			if(CollectionUtils.isNotEmpty(uploadStatusList)) 
			{
				for(UploadStatus obj : uploadStatusList) 
				{
					List<UploadStatus> objList = uploadStatusMap.get(obj.getPk().getPeriodId());
					if(objList == null) objList = new ArrayList<UploadStatus>();
					objList.add(obj);
					uploadStatusMap.put(obj.getPk().getPeriodId(), objList);
				}
			}
		}
		
		return uploadStatusMap;
	}

	
	public void setUploadStatusMap(Map<Integer, List<UploadStatus>> uploadStatusMap)
	{
		this.uploadStatusMap = uploadStatusMap;
	}

	public Map<String, List<String>> getFacDeptMap()
	{
		if(facDeptMap==null) 
		{
			facDeptMap = new HashMap<String, List<String>>();
			
			LookupValueDAO dao = LookupValueDAO.getCacheInstance();
			List<LookupValue> l1List = dao.getLookupValueList("ORGANIZATION_UNIT_L1", "US", "Y");
			List<LookupValue> l2List = dao.getLookupValueList("ORGANIZATION_UNIT_L2", "US", "Y");
			l2List.addAll(dao.getLookupValueList("ORGANIZATION_UNIT_L3", "US", "Y"));
			
			if(CollectionUtils.isNotEmpty(l1List))
			{
				for(LookupValue facLookupValue : l1List)
				{
					String fac = facLookupValue.getPk().getLookup_code();
					
					if(fac!=null) 
					{
						facDeptMap.put(fac, new ArrayList<String>());
					}
				}
				
				if(CollectionUtils.isNotEmpty(l2List)) 
				{
					for(LookupValue deptLookupValue : l2List)
					{
						String dept = deptLookupValue.getPk().getLookup_code();
						String parentFac = deptLookupValue.getParent_lookup_code();
						if(dept!=null && parentFac!=null) 
						{
							if(facDeptMap.get(parentFac)!=null) 
							{
								List<String> deptList = facDeptMap.get(parentFac);
								deptList.add(dept);
								facDeptMap.put(parentFac, deptList);
							}
						}
					}
				}
			}
		}
		return facDeptMap;
	}

	
	public void setFacDeptMap(Map<String, List<String>> facDeptMap)
	{
		this.facDeptMap = facDeptMap;
	}

	
	
	public List<String> getDeptList()
	{
		if(deptList == null) 
		{
			deptList = new ArrayList<String>();
			
			if(MapUtils.isNotEmpty(getFacDeptMap())) 
			{
				for(List<String> dList : facDeptMap.values())
				{ 
					deptList.addAll(dList);
				}
			}
		}
		return deptList;
	}

	
	public void setDeptList(List<String> deptList)
	{
		this.deptList = deptList;
	}

	public void exportKtReport(KtRptPeriod selectedPeriod) 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		
		if(selectedPeriod!=null) 
		{
			FormDAO formDAO = FormDAO.getCacheInstance();
			
			//List<String> formCodeList = getFormList().stream().map(f->f.getForm_code()).collect(Collectors.toList());
			
			
			String dataLevel = "C";
			
			List<KtFormSummary> ktFormSummaryList = null;
			
			ktFormSummaryList = formDAO.getKtFormSummaryListByDataLevel(dataLevel, null, null, 0);

			Map<String, List<Integer>> formNumberMap = new HashMap<String, List<Integer>>();
			
			for(KtFormSummary obj : ktFormSummaryList)
			{
				if(obj!=null) 
				{
					List<Integer> objList = formNumberMap.get(obj.getFormCode());
					if(objList == null) objList = new ArrayList<Integer>();
					objList.add(obj.getPk().getFormNo());
					
					formNumberMap.put(obj.getFormCode(), objList);
				}
			}
			
			//System.out.println("ktFormSummaryList = "+ktFormSummaryList);
			try 
			{
				Workbook wb = null;
	    		wb = new XSSFWorkbook();
	    		
	    		
		    	
		    	if(CollectionUtils.isNotEmpty(getFormList())) 
		    	{
		    		//KtForm
		    		createKtFormDataSheet(wb, getFormList(), formNumberMap, dataLevel, selectedPeriod);
			    	
		    		//Summary
		    		createKtFormSummaryDataSheet(wb, getFormList(), selectedPeriod);
		    		
		    		createLookupMappingDataSheet(wb, getFormList(), getLookupFormValueList());
		    	}
		    	
		    	// Get the byte array of the Workbook
		    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
				wb.write(baos);
				
				// Dispose of temporary files backing this workbook on disk
				if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
				
				wb.close();
				byte[] wbBytes = baos.toByteArray();
				
				// Set the response header
				eCtx.responseReset();
				//eCtx.addResponseHeader("Cache-control", "no-cache");
				//eCtx.addResponseHeader("Pragma", "no-cache");
	
				eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
				eCtx.setResponseHeader("Expires", "-1");
				eCtx.setResponseHeader("Pragma", "private");
	
				DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
				String fileName = "DataExport-" + dateFormat.format(new Date()) + ".xlsx";		        
				
				eCtx.setResponseContentType(new Tika().detect(fileName));
				eCtx.setResponseContentLength(wbBytes.length);
				eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

				// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
				//setPrimeFacesDownloadCompleted("exportRptBtn");
				
				// Send the bytes to response OutputStream
				OutputStream os = eCtx.getResponseOutputStream();
				os.write(wbBytes);
			
				fCtx.responseComplete();

			}
			catch (IOException e) 
	    	{
				String message = "Cannot send Workbook bytes to response OutputStream ";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			catch (Exception e)
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
			
		}
		
	}
	
	
	public void createKtFormDataSheet(Workbook wb, List<KtForm> formList, Map<String, List<Integer>> formNumberMap, String dataLevel, KtRptPeriod period)
	{
		for(KtForm form : getFormList())
    	{
    		List<Integer> formCodeNumList = formNumberMap.get(form.getForm_code());
    		
    		if(StringUtils.equals(form.getForm_code(), KtFormIP_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormIP_P> objList = fDao.getKtFormIP_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			if(CollectionUtils.isNotEmpty(objList)) 
    			{
    				if(!period.getPeriod_id().equals(1)) 
    				{
    					List<KtFormIP_P> tempList = new ArrayList<KtFormIP_P>();
	    				
	    				for(KtFormIP_P obj : objList)
	    				{
	    					boolean valid = false;
    						
    						if(obj.getStart_date()!=null) 
    						{
    							if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
    							{
    								valid = true;
    							}
    							
    							if(valid) tempList.add(obj);
	    					}
	    					
	    					objList = tempList;
	    				}
    				}
    			}
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormIPReportGroup reportGroup = new KtFormIPReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormIP_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    			
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormCntProj_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormCntProj_P> objList = fDao.getKtFormCntProj_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());

    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormCntProjReportGroup reportGroup = new KtFormCntProjReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormCntProj_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}	
    		else if(StringUtils.equals(form.getForm_code(), KtFormInn_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormInn_P> objList = fDao.getKtFormInn_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			

    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormInnReportGroup reportGroup = new KtFormInnReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormInn_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormCons_P.REPORT_FORM_CODE))
    		{
    			List<KtFormCons_P> objList = fDao.getKtFormCons_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
 
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormConsultReportGroup reportGroup = new KtFormConsultReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormCons_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormEA_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormEA_P> objList = fDao.getKtFormEA_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormEaReportGroup reportGroup = new KtFormEaReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormEA_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormStartup_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormStartup_P> objList = fDao.getKtFormStartup_PList(dataLevel, formCodeNumList);
    			
    			if(CollectionUtils.isNotEmpty(objList)) 
    			{
    				if(!period.getPeriod_id().equals(1)) 
    				{
    					List<KtFormStartup_P> tempList = new ArrayList<KtFormStartup_P>();
    					
    					for(KtFormStartup_P obj : objList)
    					{
    						boolean valid = false;
    						
    						if(obj.getStart_date()!=null) 
    						{
    							if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
    							{
    								valid = true;
    							}
    						}
    						
    						if(valid) tempList.add(obj);
    					}
    					
    					objList = tempList;
    				}
    			}
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormStartupReportGroup reportGroup = new KtFormStartupReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormStartup_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormInvAward_P.REPORT_FORM_CODE))
    		{
    			List<KtFormInvAward_P> objList = fDao.getKtFormInvAward_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormInvAwardReportGroup reportGroup = new KtFormInvAwardReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormInvAward_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormSocEngmt_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormSocEngmt_P> objList = fDao.getKtFormSocEngmt_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    	
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormSocEngmtReportGroup reportGroup = new KtFormSocEngmtReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormSocEngmt_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormCPD_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormCPD_P> objList = fDao.getKtFormCPD_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    		
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormCPDReportGroup reportGroup = new KtFormCPDReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormCPD_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormProfConf_P.REPORT_FORM_CODE)) 
    		{
				List<KtFormProfConf_P> objList = fDao.getKtFormProfConf_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    		
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormProfConfReportGroup reportGroup = new KtFormProfConfReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormProfConf_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormSem_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormSem_P> objList = fDao.getKtFormSem_PList(dataLevel, formCodeNumList);
    			
    			if(CollectionUtils.isNotEmpty(objList)) 
    			{
    				if(!period.getPeriod_id().equals(1)) 
    				{
    					List<KtFormSem_P> tempList = new ArrayList<KtFormSem_P>();
    					
    					for(KtFormSem_P obj : objList)
    					{
    						boolean valid = false;
    						
    						if(obj.getStart_date()!=null) 
    						{
    							if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
    							{
    								valid = true;
    							}
    						}
    						
    						if(valid) tempList.add(obj);
    					}
    					
    					objList = tempList;
    				}
    			}
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormSeminarReportGroup reportGroup = new KtFormSeminarReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormSem_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormProfEngmt_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormProfEngmt_P> objList = fDao.getKtFormProfEngmt_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    		
    			
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormProfEngmtReportGroup reportGroup = new KtFormProfEngmtReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormProfEngmt_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    		else if(StringUtils.equals(form.getForm_code(), KtFormStaffEngmt_P.REPORT_FORM_CODE)) 
    		{
    			List<KtFormStaffEngmt_P> objList = fDao.getKtFormStaffEngmt_PList(dataLevel, formCodeNumList, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
    			
    	
    			String sheetName = form.getForm_short_desc();
    			Sheet sheet = wb.createSheet(sheetName);
    			sheet.createFreezePane(3, 0);
    			String titleName = "Form "+form.getForm_short_desc()+" "+form.getForm_full_desc();
    			
    			if(period.getPeriod_id()!=1) titleName = titleName+" "+period.getPeriod_desc();
    			
    			//Create the header row
    			Row row1 = sheet.createRow(0);
    			Row row2 = sheet.createRow(1);
    			
    			KtFormStaffEngmtReportGroup reportGroup = new KtFormStaffEngmtReportGroup();
    			reportGroup.setKtAdmin(true);
    			reportGroup.setRdoAdmin(true);
    			reportGroup.init(wb);
    			reportGroup.appendTitleColumns(row1, row2, reportGroup, sheetName, titleName);
    			reportGroup.appendHeaderColumns(row2, reportGroup);
    			
    			Row row = null;
    			
    			for(KtFormStaffEngmt_P obj : objList)
    			{
    				 row = sheet.createRow(sheet.getLastRowNum()+1);
    				 reportGroup.appendDataColumns(row, obj);
    			}
    		}
    	}
	}
	
	public void createKtFormSummaryDataSheet(Workbook workbook, List<KtForm> formList, KtRptPeriod period) 
	{
		String sheetName = "Summary";
		Sheet sheet = workbook.createSheet(sheetName);
		
		//Create the header row
		Row row = sheet.createRow(1);
		Cell cell = null;
		
		//Font style of bold
		Font fontBold = workbook.createFont();
		fontBold.setBold(true);
		
		CellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setFont(fontBold);
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle dollarCellStyle = workbook.createCellStyle();
		//dollarCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("$#,##0.00"));
		dollarCellStyle.setDataFormat(workbook.createDataFormat().getFormat("$#,##0.00_);($#,##0.00)"));
		
		cell = row.createCell(1);
		cell.setCellValue("Summary of Performance Indicators");
		cell.setCellStyle(titleStyle);
		
		//Header row
		row = sheet.createRow(5);
		cell = row.createCell(3);
		cell.setCellValue(period.getPeriod_desc());
		sheet.autoSizeColumn(cell.getColumnIndex());
		
		sheet.setAutoFilter(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, row.getLastCellNum()-1));
		
		// Summary Description column width
		row.getSheet().setColumnWidth(2, 10000);
		
		int count = 1;
		
		//Update Summary
		summary(period);
		
		for(KtForm form : formList)
		{
			row = sheet.createRow(sheet.getLastRowNum()+1);
			int formStartRow = row.getRowNum(); 
					
			List<KtRptSum> sumList = form.getSum();
			
			cell = row.createCell(0);
			cell.setCellValue(count);
			
			cell = row.createCell(1);
			cell.setCellValue(form.getForm_full_desc());
			cell.setCellStyle(textCellStyle);
			sheet.autoSizeColumn(cell.getColumnIndex());
			
			if(CollectionUtils.isNotEmpty(sumList)) 
			{
				for(int i=0; i<sumList.size(); i++)
				{
					KtRptSum summary = sumList.get(i);
					
					cell = row.createCell(2);
					cell.setCellValue(summary.getSum_desc());
					cell.setCellStyle(textCellStyle);
					
					cell = row.createCell(3);
					cell.setCellValue(summary.getSum_value());
					
					if(StringUtils.equals(summary.getSum_type(), "dollar")) 
					{
						cell.setCellStyle(dollarCellStyle);
					}
					else if(StringUtils.equals(summary.getSum_type(), "num")) 
					{
						cell.setCellStyle(numberCellStyle);
					}
					
					if(i<sumList.size()-1) row = sheet.createRow(sheet.getLastRowNum()+1);
				}
				
				if(sumList.size()>1) 
				{
					// seq column
					sheet.addMergedRegion(new CellRangeAddress(formStartRow,row.getRowNum(),0,0));
					
					// form desc column
					sheet.addMergedRegion(new CellRangeAddress(formStartRow,row.getRowNum(),1,1));
				}
				
			}
			count++;
		}
	}
	
	
	public void createLookupMappingDataSheet(Workbook workbook, List<KtForm> formList, List<LookupFormValue> lookupFormValueList) 
	{
		if(CollectionUtils.isNotEmpty(formList) && CollectionUtils.isNotEmpty(lookupFormValueList)) 
		{
			
			String sheetName = "Lookup Mapping";
			Sheet sheet = workbook.createSheet(sheetName);
			
			//Create the header row
			Row row = sheet.createRow(0);
			Cell cell = null;
			
			//Font style of bold
			Font fontBold = workbook.createFont();
			fontBold.setBold(true);
			
			CellStyle titleStyle = workbook.createCellStyle();
			titleStyle.setFont(fontBold);
			
			CellStyle textCellStyle = workbook.createCellStyle();
			textCellStyle.setWrapText(true);
			
			cell = row.createCell(0);
			cell.setCellValue("Form");
			cell.setCellStyle(titleStyle);
			
			cell = row.createCell(1);
			cell.setCellValue("Form Column");
			cell.setCellStyle(titleStyle);
			
			cell = row.createCell(2);
			cell.setCellValue("Lookup Value");
			cell.setCellStyle(titleStyle);

			cell = row.createCell(3);
			cell.setCellValue("Description");
			cell.setCellStyle(titleStyle);
			
			Map<String, List<LookupFormValue>> lookupMap = new HashMap<String, List<LookupFormValue>>();

			Map<String, List<LookupValue>> valueMap = new HashMap<String, List<LookupValue>>();
			
			for(LookupFormValue obj : lookupFormValueList)
			{
				String formCode = obj.getFormCode();
				List<LookupFormValue> objList = lookupMap.get(formCode);
				
				if(objList == null) objList = new ArrayList<LookupFormValue>();
				
				objList.add(obj);
				
				lookupMap.put(formCode, objList);
			}
			
			List<String> lookupTypeList = lookupFormValueList.stream().map(v->v.getLookupType()).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(lookupTypeList)) 
			{
				List<LookupValue> valueList = LookupValueDAO.getCacheInstance().getLookupValueList(lookupTypeList, "US", "Y");
				
				
				for(LookupValue value : valueList)
				{
					List<LookupValue> objList = valueMap.get(value.getPk().getLookup_type());
					
					if(objList == null) objList = new ArrayList<LookupValue>();
					objList.add(value);
					
					valueMap.put(value.getPk().getLookup_type(), objList);
				}
				

				for(KtForm form : formList)
				{
					List<LookupFormValue> objList = lookupMap.get(form.getForm_code());
					
					if(CollectionUtils.isNotEmpty(objList)) 
					{
						for(LookupFormValue obj : objList)
						{
							String lookupType = obj.getLookupType();
							List<LookupValue> lookupValueList = valueMap.get(lookupType);
							
							if(CollectionUtils.isNotEmpty(lookupValueList)) 
							{
								for(LookupValue value : lookupValueList)
								{
									row = sheet.createRow(sheet.getLastRowNum()+1);
									cell = row.createCell(0);
									cell.setCellValue(form.getForm_full_desc());
									cell.setCellStyle(textCellStyle);
									sheet.autoSizeColumn(cell.getColumnIndex());
									
									cell = row.createCell(1);
									cell.setCellValue(obj.getColumnName());
									cell.setCellStyle(textCellStyle);
									sheet.autoSizeColumn(cell.getColumnIndex());
									
									cell = row.createCell(2);
									cell.setCellValue(value.getPk().getLookup_code());
									cell.setCellStyle(textCellStyle);
									sheet.autoSizeColumn(cell.getColumnIndex());
									
									cell = row.createCell(3);
									cell.setCellValue(value.getDescription());
									cell.setCellStyle(textCellStyle);
									sheet.autoSizeColumn(cell.getColumnIndex());
								}
							}
							
						}	
					}
				}
				
			}
			
		}
		
	}
	
	public void uploadFile(Integer periodId) throws Exception
	{
		String message = "";
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		
		if(periodId != null) 
		{
			try
			{
				UploadedFile uploadedFile = getUploadedFileMap().get(periodId);
				
				Workbook workbook = getWorkbookMap().get(periodId);
				
				Map<String, Map<Integer, Object>> importObjMap = new HashMap<String, Map<Integer, Object>>();
				Map<String, Map<Integer, List<String>>> errMsgMap = new HashMap<String, Map<Integer, List<String>>>();
				Map<String, ExcelReportFile> excelRptFileMap = new HashMap<String,ExcelReportFile>();
				
				Map<String, Map<String, List<String>>> lookupCodeMap = getLookupCodeMap();
				
				if(CollectionUtils.isNotEmpty(getFormList()))
				{
					for(KtForm form : formList)
					{
						String sheetName = form.getForm_short_desc();
						
						if(!GenericValidator.isBlankOrNull(sheetName) && workbook.getSheet(sheetName)!=null) 
						{
							if(StringUtils.equals(form.getForm_code(), KtFormIP_P.REPORT_FORM_CODE)) 
							{
								KtFormIpExcelRptFile excelRptFile = new KtFormIpExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{

									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										if(MapUtils.isNotEmpty(lookupCodeMap) && lookupCodeMap.get(form.getForm_code())!=null) 
										{
											KtFormIP_P obj = (KtFormIP_P) objMap.get(rowNum);
											
											if(obj!=null) 
											{
												lookupCodeList = validationMap.get("KT_IP_CAT");
												targetValue = obj.getCat();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Category of IP"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												lookupCodeList = validationMap.get("KT_IP_FUND_SRC");
												targetValue = obj.getIncome_cat();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Source of Income (Category)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												lookupCodeList = validationMap.get("KT_IP_ORG_TYPE");
												targetValue = obj.getOrg_type();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Type of Organization Providing the Income"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												targetValue = obj.getSoftware_lic();
												
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "Y") && !StringUtils.equals(targetValue, "N")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Software License (Y/N)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);

												}
												
												if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
												
											}
										}
									}
								}
							
								
								importObjMap.put(KtFormIP_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormCntProj_P.REPORT_FORM_CODE)) 
							{
								KtFormCntProjExcelRptFile excelRptFile = new KtFormCntProjExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormCntProj_P obj = (KtFormCntProj_P) objMap.get(rowNum);
										
										if(obj!=null) 
										{
											if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
											{
												if(!isValidStartEndDate(obj.getStart_date(), obj.getEnd_date())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(invalidStartEndDateMsg);
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
										}
										
									}
								}
								
								importObjMap.put(KtFormCntProj_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormInn_P.REPORT_FORM_CODE)) 
							{
								KtFormInnExcelRptFile excelRptFile = new KtFormInnExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormInn_P obj = (KtFormInn_P) objMap.get(rowNum);
										
										if(obj!=null)
										{
											if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
											{
												if(!isValidStartEndDate(obj.getStart_date(), obj.getEnd_date())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(invalidStartEndDateMsg);
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
										}
										
									}
								}
								
								importObjMap.put(KtFormInn_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormCons_P.REPORT_FORM_CODE)) 
							{
								KtFormConsultExcelRptFile excelRptFile = new KtFormConsultExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormCons_P obj = (KtFormCons_P) objMap.get(rowNum);
										
										if(obj!=null) 
										{

											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
											
											if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
											{
												if(!isValidStartEndDate(obj.getStart_date(), obj.getEnd_date())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(invalidStartEndDateMsg);
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											lookupCodeList = validationMap.get("KT_CONS_FUND_SRC");
											targetValue = obj.getFund_src_type();
											
											if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
											{
												if(!lookupCodeList.contains(targetValue)) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Type of Funding Source"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											lookupCodeList = validationMap.get("KT_CONS_ORG_TYPE");
											targetValue = obj.getFund_src_org();
											
											if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
											{
												if(!lookupCodeList.contains(targetValue)) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Type of Organization Providing the Funding"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											lookupCodeList = validationMap.get("KT_CONS_ROLE");
											targetValue = obj.getEduhk_role();
											
											if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
											{
												if(!lookupCodeList.contains(targetValue)) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Role of EdUHK:Coordinating/Participating (C/P)"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
										}
									}
								}
								
								importObjMap.put(KtFormCons_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormEA_P.REPORT_FORM_CODE)) 
							{
								KtFormEaExcelRptFile excelRptFile = new KtFormEaExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										if(MapUtils.isNotEmpty(lookupCodeMap) && lookupCodeMap.get(form.getForm_code())!=null) 
										{
											KtFormEA_P obj = (KtFormEA_P) objMap.get(rowNum);
											
											if(obj!=null) 
											{
												if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);

												}
												
												if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
												
												lookupCodeList = validationMap.get("KT_EA_ACT_TYPE");
												targetValue = obj.getAct_type();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													//Act_type is mutli selection and split by ,
													String[] actTypes = targetValue.split(",");
													List<String> actTypeList = Arrays.asList(actTypes);
													
													for(String actType : actTypeList)
													{
														String act = actType.trim();
														if(!lookupCodeList.contains(act)) 
														{
															List<String> errMsgList = invalidMsgMap.get(rowNum);
															if(errMsgList == null) errMsgList = new ArrayList<String>();
															errMsgList.add(MessageFormat.format(invalidDataMsg, "Type of Activity"));
															
															invalidMsgMap.put(rowNum, errMsgList);
															break;
														}
													}
												}
												
												targetValue = obj.getRegion();
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "L") && !StringUtils.equals(targetValue, "I")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Local / International (L/I)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
											}
										}
									}
								}
								
								importObjMap.put(KtFormEA_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
								
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormStartup_P.REPORT_FORM_CODE)) 
							{
								KtFormStartupExcelRptFile excelRptFile = new KtFormStartupExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										if(MapUtils.isNotEmpty(lookupCodeMap) && lookupCodeMap.get(form.getForm_code())!=null) 
										{
											
											KtFormStartup_P obj = (KtFormStartup_P) objMap.get(rowNum);
											
											if(obj!=null) 
											{
												if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);

												}
												
												if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}												
												
												lookupCodeList = validationMap.get("KT_STARTUP_ACT_TYPE");
												targetValue = obj.getAct_type();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Type(s) of Support Provided"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
											}
										}
									}
								}
								
								importObjMap.put(KtFormStartup_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
								
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormInvAward_P.REPORT_FORM_CODE)) 
							{
								KtFormInvAwardExcelRptFile excelRptFile = new KtFormInvAwardExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormInvAward_P obj = (KtFormInvAward_P) objMap.get(rowNum);
										
										if(obj!=null) 
										{
											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
											
											targetValue = obj.getRegion();
											if(targetValue!=null) 
											{
												if(!StringUtils.equals(targetValue, "I") && !StringUtils.equals(targetValue, "R") && !StringUtils.equals(targetValue, "L")) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "International/ Regional/ Local (I/R/L)"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
										}
									}
								}
								
								importObjMap.put(KtFormInvAward_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
								
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormSocEngmt_P.REPORT_FORM_CODE)) 
							{
								KtFormSocEngmtExcelRptFile excelRptFile = new KtFormSocEngmtExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										if(MapUtils.isNotEmpty(lookupCodeMap) && lookupCodeMap.get(form.getForm_code())!=null) 
										{
											
											KtFormSocEngmt_P obj = (KtFormSocEngmt_P) objMap.get(rowNum);
											
											if(obj!=null) 
											{
												if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);

												}
												
												if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
												
												lookupCodeList = validationMap.get("KT_SOC_ENGMT_ACT_TYPE");
												targetValue = obj.getEvent_type();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Type of Event"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												targetValue = obj.getFree_charge();
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "F") && !StringUtils.equals(targetValue, "C")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Free/ Chargeable (F/C)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												targetValue = obj.getPerform();
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "Y") && !StringUtils.equals(targetValue, "N")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Performance/ Exhibition of Creative Works by EdUHK Staff or Students (Y/N)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
											}
										}
									}
								}
								
								importObjMap.put(KtFormSocEngmt_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
								
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormCPD_P.REPORT_FORM_CODE)) 
							{
								KtFormCPDExcelRptFile excelRptFile = new KtFormCPDExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormCPD_P obj = (KtFormCPD_P) objMap.get(rowNum);
										
										if(obj!=null) 
										{
											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
											
											if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
											{
												if(!isValidStartEndDate(obj.getStart_date(), obj.getEnd_date())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(invalidStartEndDateMsg);
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											targetValue = obj.getFree_charge();
											if(targetValue!=null) 
											{
												if(!StringUtils.equals(targetValue, "F") && !StringUtils.equals(targetValue, "C")) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Free/ Chargeable (F/C)"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
										}
										
									}
								}
								
								importObjMap.put(KtFormCPD_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormProfConf_P.REPORT_FORM_CODE)) 
							{
								KtFormProfConfExcelRptFile excelRptFile = new KtFormProfConfExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										if(MapUtils.isNotEmpty(lookupCodeMap) && lookupCodeMap.get(form.getForm_code())!=null) 
										{
											KtFormProfConf_P obj = (KtFormProfConf_P) objMap.get(rowNum);
											
											if(obj!=null) 
											{
												if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);

												}
												
												if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
												
												lookupCodeList = validationMap.get("KT_PROF_CONF_TARGET_PAX");
												targetValue = obj.getTarget_pax();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Target Participants"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												

												targetValue = obj.getFree_charge();
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "F") && !StringUtils.equals(targetValue, "C")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Free/ Chargeable (F/C)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												targetValue = obj.getRegion();
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "L") && !StringUtils.equals(targetValue, "L")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Local / International (L/I)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
											}
										}
									}
								}
								
								importObjMap.put(KtFormProfConf_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
								
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormSem_P.REPORT_FORM_CODE)) 
							{
								KtFormSeminarExcelRptFile excelRptFile = new KtFormSeminarExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										if(MapUtils.isNotEmpty(lookupCodeMap) && lookupCodeMap.get(form.getForm_code())!=null) 
										{
											KtFormSem_P obj = (KtFormSem_P) objMap.get(rowNum);
											
											if(obj!=null) 
											{
												if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);

												}
												
												if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
												
												lookupCodeList = validationMap.get("KT_SEM_TARGET_PAX");
												targetValue = obj.getTarget_pax();
												
												if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
												{
													if(!lookupCodeList.contains(targetValue)) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Target Participants"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
												
												targetValue = obj.getFree_charge();
												if(targetValue!=null) 
												{
													if(!StringUtils.equals(targetValue, "F") && !StringUtils.equals(targetValue, "C")) 
													{
														List<String> errMsgList = invalidMsgMap.get(rowNum);
														if(errMsgList == null) errMsgList = new ArrayList<String>();
														errMsgList.add(MessageFormat.format(invalidDataMsg, "Free/ Chargeable (F/C)"));
														
														invalidMsgMap.put(rowNum, errMsgList);
													}
												}
											}
										}
									}
								}
								
								importObjMap.put(KtFormSem_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
								
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormProfEngmt_P.REPORT_FORM_CODE)) 
							{
								KtFormProfEngmtExcelRptFile excelRptFile = new KtFormProfEngmtExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormProfEngmt_P obj = (KtFormProfEngmt_P) objMap.get(rowNum);
										
										if(obj!=null) 
										{
											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
											
											if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
											{
												if(!isValidStartEndDate(obj.getStart_date(), obj.getEnd_date())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(invalidStartEndDateMsg);
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
										}
										
									}
								}
								
								importObjMap.put(KtFormProfEngmt_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							else if(StringUtils.equals(form.getForm_code(), KtFormStaffEngmt_P.REPORT_FORM_CODE)) 
							{
								KtFormStaffEngmtExcelRptFile excelRptFile = new KtFormStaffEngmtExcelRptFile();
								excelRptFile.init(workbook);
								Map<Integer, Object> objMap = excelRptFile.getDataMap(sheetName);
								Map<Integer, List<String>> invalidMsgMap = excelRptFile.getInvalidMsgMap();
								
								if(MapUtils.isNotEmpty(objMap)) 
								{
									Map<String, List<String>> validationMap = lookupCodeMap.get(form.getForm_code());
									List<String> lookupCodeList;
									String targetValue;
									
									Set<Integer> rowNumSet = objMap.keySet();
									for(Integer rowNum : rowNumSet)
									{
										KtFormStaffEngmt_P obj = (KtFormStaffEngmt_P) objMap.get(rowNum);
										
										if(obj!=null) 
										{
											if(obj.getFac()!=null && !isValidFac(obj.getFac())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Faculty/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);

											}
											
											if(obj.getDept()!=null && !isValidDept(obj.getFac(), obj.getDept())) 
											{
												List<String> errMsgList = invalidMsgMap.get(rowNum);
												if(errMsgList == null) errMsgList = new ArrayList<String>();
												errMsgList.add(MessageFormat.format(invalidDataMsg, "Department/ Centre/ Unit"));
												
												invalidMsgMap.put(rowNum, errMsgList);
											}
											
											if(obj.getStart_date()!=null && obj.getEnd_date()!=null) 
											{
												if(!isValidStartEndDate(obj.getStart_date(), obj.getEnd_date())) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(invalidStartEndDateMsg);
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											lookupCodeList = validationMap.get("KT_STAFF_ENGMT_NATURE");
											targetValue = obj.getExt_body_nature();
											
											if(targetValue!=null && CollectionUtils.isNotEmpty(lookupCodeList))
											{
												if(!lookupCodeList.contains(targetValue)) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Nature of External Body"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
											targetValue = obj.getRegion();
											
											if(targetValue!=null) 
											{
												if(!StringUtils.equals(targetValue, "L") && !StringUtils.equals(targetValue, "R")&& !StringUtils.equals(targetValue, "G")) 
												{
													List<String> errMsgList = invalidMsgMap.get(rowNum);
													if(errMsgList == null) errMsgList = new ArrayList<String>();
													errMsgList.add(MessageFormat.format(invalidDataMsg, "Local / Regional / Global (L/R/G)"));
													
													invalidMsgMap.put(rowNum, errMsgList);
												}
											}
											
										}
									}
								}
								
								importObjMap.put(KtFormStaffEngmt_P.REPORT_FORM_CODE, objMap);
								if(MapUtils.isNotEmpty(invalidMsgMap))	errMsgMap.put(form.getForm_short_desc(), invalidMsgMap);
								excelRptFileMap.put(form.getForm_short_desc(), excelRptFile);
							}
							
						}
					}
					
					if(MapUtils.isNotEmpty(importObjMap)) 
					{
//						// importObjMap<REPORT_FORM_CODE, Map<Row Num, Object>>
//						System.out.println("importObjMap = "+importObjMap);
//						System.out.println("errMsgMap = "+errMsgMap);
						//Only upload the data when no error message
						if(MapUtils.isEmpty(errMsgMap))
						{
							//Perform upload
							if(MapUtils.isNotEmpty(importObjMap)) 
							{
								Map<String, List<Object>> targetImportObjMap = new HashMap<String, List<Object>>();
								
								Set<String> rptFormCodeSet = importObjMap.keySet();
								
								for(String rptFormCode : rptFormCodeSet)
								{
									Map<Integer, Object> objMap = importObjMap.get(rptFormCode);
									
									if(MapUtils.isNotEmpty(objMap)) 
									{
										List<Object> objList = new ArrayList<Object>();
										objList.addAll(objMap.values());
										
										targetImportObjMap.put(rptFormCode, objList);
									}
								}
								 
								UploadStatus uploadStatus = new UploadStatus();
								UploadStatusPK uploadStatusPk = new UploadStatusPK();
								uploadStatusPk.setPeriodId(periodId);
								uploadStatusPk.setType("KT_FORM");
								uploadStatus.setPk(uploadStatusPk);
								uploadStatus.setFileName(uploadedFileMap.get(periodId).getFileName());
								uploadStatus.setUserstamp(getCurrentUserId());
								
								ktFormService.updateKtFormList(uploadStatus, "C", targetImportObjMap, getCurrentUserId(), "IMPORTED");
								
								uploadStatusMap = null;
								uploadedFileMap.remove(periodId);
								workbookMap.remove(periodId);
								
								message = "Successfully uploaded.";
								fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
							}
						}
						else
						{
							// return error message
							appendErrMsgDataSheet(workbook, errMsgMap, excelRptFileMap);
							
							// Get the byte array of the Workbook
					    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
							workbook.write(baos);
							
							// Dispose of temporary files backing this workbook on disk
							if (workbook instanceof SXSSFWorkbook) ((SXSSFWorkbook) workbook).dispose();
							
							workbook.close();
							byte[] wbBytes = baos.toByteArray();
							
							// Set the response header
							eCtx.responseReset();
							//eCtx.addResponseHeader("Cache-control", "no-cache");
							//eCtx.addResponseHeader("Pragma", "no-cache");
				
							eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
							eCtx.setResponseHeader("Expires", "-1");
							eCtx.setResponseHeader("Pragma", "private");
				
							DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
							String fileName = "Upload Failed-" + dateFormat.format(new Date()) + ".xlsx";		        
							
							eCtx.setResponseContentType(new Tika().detect(fileName));
							eCtx.setResponseContentLength(wbBytes.length);
							eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");

							// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
							//setPrimeFacesDownloadCompleted("exportRptBtn");
							
							// Send the bytes to response OutputStream
							OutputStream os = eCtx.getResponseOutputStream();
							os.write(wbBytes);
						
							fCtx.responseComplete();
						}
						
					}
					
				}
				

				
			}
			catch(Exception e) 
			{
				message = "Unable to upload file. Exception = "+e.getMessage();
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				logger.log(Level.SEVERE, message, e);
			}
						
		}
	}
	
	public boolean isValidStartEndDate(Date startDate, Date endDate) 
	{
		if (validateStartEndDate) {
			if(startDate != null && endDate != null) 
			{
				return startDate.before(endDate);
			}
			return false;
		}else {
			return true;
		}
		
	}
	
	
	public void appendErrMsgDataSheet(Workbook workbook, Map<String, Map<Integer, List<String>>> errMsgMap, Map<String, ExcelReportFile> excelRptFileMap)
	{
		Set<String> sheetNameSet = errMsgMap.keySet();
		
		for(String sheetName : sheetNameSet) 
		{
			Sheet sheet = workbook.getSheet(sheetName);
			
			ExcelReportFile rptFileObj = excelRptFileMap.get(sheetName);
			Map<Integer, List<String>> objMap = errMsgMap.get(sheetName);
			
			if(MapUtils.isNotEmpty(objMap)) 
			{
				Set<Integer> rowNumSet = objMap.keySet();
				
				Integer headerSize = rptFileObj.getHeaderSize()-1;
				
				for(Integer rowNum : rowNumSet)
				{
					Row row = sheet.getRow(rowNum);
					int errMsgCellNum = (row.getLastCellNum() > headerSize) ? row.getLastCellNum() : (headerSize+1);
					
					Cell cell = row.createCell(errMsgCellNum);
					
					List<String> errMsgList = objMap.get(rowNum);
					
					if(CollectionUtils.isNotEmpty(errMsgList)) 
					{
						String errMsgValue = null;
						for(String errMsg : errMsgList)
						{
							if(errMsgValue==null)
							{
								errMsgValue = errMsg;
							}
							else
							{
								errMsgValue = errMsgValue+"\n"+errMsg;
							}
						}
						cell.setCellValue(errMsgValue);
						
						CellStyle cs = workbook.createCellStyle();  
				        cs.setWrapText(true);  
				        cell.setCellStyle(cs);  
						row.setRowStyle(cs);
						sheet.autoSizeColumn(cell.getColumnIndex());
						
					}
				}
			}
			
		}
	}

	
	public List<LookupFormValue> getLookupFormValueList()
	{
		if(lookupFormValueList == null && getFormList()!=null) 
		{
			List<String> formCodeList = formList.stream().map(f->f.getForm_code()).collect(Collectors.toList());
			lookupFormValueList = LookupValueDAO.getCacheInstance().getLookupFormValueList(formCodeList);
		}
		
		return lookupFormValueList;
	}

	
	public void setLookupFormValueList(List<LookupFormValue> lookupFormValueList)
	{
		this.lookupFormValueList = lookupFormValueList;
	}

	public Map<String, Map<String, List<LookupValue>>> getLookupValueMap()
	{
		//Map<formCode, Map<LookupType, List<LookupValue>>
		Map<String, Map<String, List<LookupValue>>> objMap = new LinkedHashMap<String, Map<String, List<LookupValue>>>();
		LookupValueDAO lookupValueDAO = LookupValueDAO.getCacheInstance();
		
		if(CollectionUtils.isNotEmpty(getLookupFormValueList())) 
		{
			for(LookupFormValue lookupFormValue : lookupFormValueList)
			{
				String formCode = lookupFormValue.getPk().getFormCode();
				String lookupType = lookupFormValue.getPk().getLookupType();
				
				Map<String, List<LookupValue>> lookupMap = objMap.get(formCode);
				
				if(lookupMap == null) lookupMap = new LinkedHashMap<String, List<LookupValue>>();
				
				List<LookupValue> lookupValueList = lookupValueDAO.getLookupValueList(lookupFormValue.getLookupType(), "US", "Y");
				lookupMap.put(lookupFormValue.getLookupType(), lookupValueList);
				
				objMap.put(formCode, lookupMap);
			}
		}
//		
//		
//		LookupValueDAO lookupValueDao = LookupValueDAO.getCacheInstance();
//		
//		List<LookupValue> lookupValueList;
//		
//		String lookupType = "";
//		String formCode = "";
//		
//		//KT_IP
//		{
//			formCode = "KT_IP";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_IP_FUND_SRC";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//		
//			lookupType = "KT_IP_CAT";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			lookupType = "KT_IP_ORG_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_CONSULT
//		{
//			formCode = "KT_CONSULT";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_CONS_ORG_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			lookupType = "KT_CONS_ROLE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			lookupType = "KT_CONS_FUND_SRC";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_EA
//		{
//			formCode = "KT_EA";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_EA_ACT_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_STARTUP
//		{
//			formCode = "KT_STARTUP";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_STARTUP_ACT_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_SOC_ENGMT
//		{
//			formCode = "KT_SOC_ENGMT";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_SOC_ENGMT_ACT_TYPE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_PROF_CONF
//		{
//			formCode = "KT_PROF_CONF";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_PROF_CONF_TARGET_PAX";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_SEMINAR
//		{
//			formCode = "KT_SEMINAR";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_SEM_TARGET_PAX";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
//		
//		//KT_STAFF_ENGMT
//		{
//			formCode = "KT_STAFF_ENGMT";
//			Map<String, List<LookupValue>> lookupValueMap = new LinkedHashMap<String, List<LookupValue>>();
//			
//			lookupType = "KT_STAFF_ENGMT_NATURE";
//			lookupValueList = lookupValueDao.getLookupValueList(lookupType, "US", "Y");
//			if(CollectionUtils.isNotEmpty(lookupValueList)) 
//			{
//				lookupValueMap.put(lookupType, lookupValueList);
//			}
//			
//			objMap.put(formCode, lookupValueMap);
//		}
		return objMap;
	}
	
	public Map<String, Map<String, List<String>>> getLookupCodeMap()
	{
		//Map<formCode, Map<LookupType, List<LookupCode>>>
		Map<String, Map<String, List<String>>> objMap = new HashMap<String, Map<String, List<String>>>();
		
		List<String> objList;
		
		Map<String, Map<String, List<LookupValue>>> lookupValueMap = getLookupValueMap();
		
		if(MapUtils.isNotEmpty(lookupValueMap))
		{
			Set<String> formCodeSet = lookupValueMap.keySet();
			
			for(String formCode : formCodeSet)
			{
				Map<String, List<LookupValue>> lookupMap = lookupValueMap.get(formCode);
				Map<String, List<String>> valueMap = new LinkedHashMap<String,List<String>>();
				
				if(MapUtils.isNotEmpty(lookupMap)) 
				{
					Set<String> lookupTypeSet = lookupMap.keySet();
					
					for(String lookupType : lookupTypeSet)
					{
						List<LookupValue> lookupValueList = lookupMap.get(lookupType);
						objList = lookupValueList.stream().map(v->v.getPk().getLookup_code()).collect(Collectors.toList());
						if(CollectionUtils.isNotEmpty(objList)) 
						{
							valueMap.put(lookupType, objList);
						}
					}
				}
				objMap.put(formCode, valueMap);
			}
			
			
		}
		
		return objMap;
	}
	
	public boolean isValidFac(String obj) 
	{
		if(MapUtils.isNotEmpty(getFacDeptMap()) && obj!=null) 
		{
			return facDeptMap.containsKey(obj);
		}
		return false;
	}

	public boolean isValidDept(String fac, String dept) 
	{
		if(MapUtils.isNotEmpty(getFacDeptMap()) && fac!=null && dept!=null) 
		{
			if(facDeptMap.get(fac)!=null) 
			{
				return facDeptMap.get(fac).contains(dept);
			}
		}
		return false;
	}
	
	public void summary(KtRptPeriod period)
	{
		if (getFormList() != null && period != null) {
			//getOldDate();
			HashSet unique=new HashSet();
			
			List<KtFormSummary> ktFormSummaryListByFormCode = null;
			
			String summaryDataLevel = "C";
			ktFormSummaryList = FormDAO.getCacheInstance().getKtFormSummaryListByDataLevel(summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
			
			
			for (KtForm f:formList) {
				switch(f.getForm_code()) {
					case SysParam.PARAM_KT_FORM_CPD:
						List<KtFormCPD_P> cpd_headerList = new ArrayList<KtFormCPD_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormCPD_P obj = fDao.getKtFormCPD_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) cpd_headerList.add(obj);
							}
						}

						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cpd_headerList.size()));
									break;
								case "income_total":
									Double income_total = cpd_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cpd_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
								case "num_hr":
									Integer num_hr = cpd_headerList.stream().filter(a -> a.getNum_stu_contact_hr() != null).mapToInt(a -> a.getNum_stu_contact_hr()).sum();
									s.setSum_value(Double.valueOf(num_hr));
									break;
							}	
						}
						break;
					case SysParam.PARAM_KT_FORM_PROF_CONF:
						List<KtFormProfConf_P> prof_conf_headerList = new ArrayList<KtFormProfConf_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormProfConf_P obj = fDao.getKtFormProfConf_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) prof_conf_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(prof_conf_headerList.size()));
									break;
								case "income_total":
									Double income_total = prof_conf_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = prof_conf_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
								case "num_present":
									Integer num_present = prof_conf_headerList.stream().filter(a -> a.getNum_presentation() != null).mapToInt(a -> a.getNum_presentation()).sum();
									s.setSum_value(Double.valueOf(num_present));
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_SEM:
						List<KtFormSem_P> sem_headerList = new ArrayList<KtFormSem_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormSem_P obj = fDao.getKtFormSem_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) sem_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(sem_headerList.size()));
									break;
								case "income_total":
									Double income_total = sem_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = sem_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_CNT_PROJ:
						List<KtFormCntProj_P> cnt_proj_headerList = new ArrayList<KtFormCntProj_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());

						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormCntProj_P obj = fDao.getKtFormCntProj_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) cnt_proj_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cnt_proj_headerList.size()));
									break;
								case "income_total":
									Double income_total = cnt_proj_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = cnt_proj_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_INN:
						List<KtFormInn_P> inn_headerList = new ArrayList<KtFormInn_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormInn_P obj = fDao.getKtFormInn_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) inn_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(inn_headerList.size()));
									break;
								case "income_total":
									Double income_total = inn_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
								case "num_key":
									Integer num_key = inn_headerList.stream().filter(a -> a.getNum_key_partner() != null).mapToInt(a -> a.getNum_key_partner()).sum();
									s.setSum_value(Double.valueOf(num_key));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_CONS:
						List<KtFormCons_P> cons_headerList = new ArrayList<KtFormCons_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormCons_P obj = fDao.getKtFormCons_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) cons_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(cons_headerList.size()));
									break;
								case "income_total":
									Double income_total = cons_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_PROF_ENGMT:
						List<KtFormProfEngmt_P> prof_engmt_headerList = new ArrayList<KtFormProfEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormProfEngmt_P obj = fDao.getKtFormProfEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) prof_engmt_headerList.add(obj);
							}
						}
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(prof_engmt_headerList.size()));
									break;
								case "num_teacher":
									Integer num_teacher = prof_engmt_headerList.stream().filter(a -> a.getNum_teacher() != null).mapToInt(a -> a.getNum_teacher()).sum();
									s.setSum_value(Double.valueOf(num_teacher));
									break;
								case "num_principal":
									Integer num_principal = prof_engmt_headerList.stream().filter(a -> a.getNum_principal() != null).mapToInt(a -> a.getNum_principal()).sum();
									s.setSum_value(Double.valueOf(num_principal));
									break;
								case "num_other":
									Integer num_other = prof_engmt_headerList.stream().filter(a -> a.getNum_other() != null).mapToInt(a -> a.getNum_other()).sum();
									s.setSum_value(Double.valueOf(num_other));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_IP:
						List<KtFormIP_P> ip_headerList = new ArrayList<KtFormIP_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormIP_P obj = fDao.getKtFormIP_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) ip_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(ip_headerList.size()));
									break;
								case "income_total":
									Double income_total = ip_headerList.stream().filter(a -> a.getIncome_rpt_unit() != null).mapToDouble(a -> a.getIncome_rpt_unit()).sum();
									s.setSum_value(Double.valueOf(income_total));
									break;
							}
						}
						
						break;
					case SysParam.PARAM_KT_FORM_SOC_ENGMT:
						List<KtFormSocEngmt_P> soc_engmt_headerList = new ArrayList<KtFormSocEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormSocEngmt_P obj = fDao.getKtFormSocEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) soc_engmt_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_attendee":
									Integer num_attendee = soc_engmt_headerList.stream().filter(a -> a.getNum_attendee() != null).mapToInt(a -> a.getNum_attendee()).sum();
									s.setSum_value(Double.valueOf(num_attendee));
									break;
								case "num_perform":
									Long num_perform = soc_engmt_headerList.stream().filter(a -> "Y".equals(a.getPerform())).count();
									s.setSum_value(Double.valueOf(num_perform));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_STAFF_ENGMT:
						List<KtFormStaffEngmt_P> staff_engmt_headerList = new ArrayList<KtFormStaffEngmt_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormStaffEngmt_P obj = fDao.getKtFormStaffEngmt_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) staff_engmt_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(staff_engmt_headerList.size()));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_EA:
						List<KtFormEA_P> ea_headerList = new ArrayList<KtFormEA_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormEA_P obj = fDao.getKtFormEA_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) ea_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(ea_headerList.size()));
									break;
							}
						}
						break;
					case SysParam.PARAM_KT_FORM_STARTUP:
						List<KtFormStartup_P> startup_headerList = new ArrayList<KtFormStartup_P>();
//						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
//														.filter(y -> y.getFormCode().equals(f.getForm_code()))
//														.collect(Collectors.toList());
						
						break;
					case SysParam.PARAM_KT_FORM_INV_AWARD:
						List<KtFormInvAward_P> inv_award_headerList = new ArrayList<KtFormInvAward_P>();
						ktFormSummaryListByFormCode = ktFormSummaryList.stream()
														.filter(y -> y.getFormCode().equals(f.getForm_code()))
														.collect(Collectors.toList());
						for (KtFormSummary d:ktFormSummaryListByFormCode) 
						{
							KtFormInvAward_P obj = fDao.getKtFormInvAward_P(d.getPk().getFormNo(), summaryDataLevel, period.getDate_from(), period.getDate_to(), period.getPeriod_id());
							
							if (obj != null) 
							{
								boolean valid = false;

								if(obj.getStart_date()!=null) 
								{
									if(obj.getStart_date().after(period.getDate_from()) && obj.getStart_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(obj.getEnd_date()!=null) 
								{
									if(obj.getEnd_date().after(period.getDate_from()) && obj.getEnd_date().before(period.getDate_to())) 
									{
										valid = true;
									}
								}

								if(valid) inv_award_headerList.add(obj);
							}
						}
						
						for (KtRptSum s:f.getSum()) {
							switch(s.getSum_key()) {
								case "num_total":
									s.setSum_value(Double.valueOf(inv_award_headerList.size()));
									break;
								case "num_filed":
									Integer num_filed = inv_award_headerList.stream().filter(a -> a.getNum_pat_filed() != null).mapToInt(a -> a.getNum_pat_filed()).sum();
									s.setSum_value(Double.valueOf(num_filed));
									break;
								case "num_granted":
									Integer num_granted = inv_award_headerList.stream().filter(a -> a.getNum_pat_granted() != null).mapToInt(a -> a.getNum_pat_granted()).sum();
									s.setSum_value(Double.valueOf(num_granted));
									break;	
							}
						}
						break;
					case "":
						break;
				}
			}
		}
	}
}
