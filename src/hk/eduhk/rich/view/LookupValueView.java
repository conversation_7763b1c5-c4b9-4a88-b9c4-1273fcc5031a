package hk.eduhk.rich.view;

import java.text.MessageFormat;
import java.util.List;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.persistence.OptimisticLockException;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.form.FormDAO;

@ManagedBean(name = "lookupValueView")
@ViewScoped
@SuppressWarnings("serial")
public class LookupValueView extends BaseView
{
	private static Logger logger = Logger.getLogger(LookupValue.class.getName());
	private List<LookupValue> valueList = null;
	private LookupValue selectedValue;
	private LookupValue removeValue;
	private LookupValueDAO vDao = LookupValueDAO.getInstance();
	
	public void reloadValueList() {
		valueList = null;
	}
	
	public List<LookupValue> getValueList()
	{
		if (valueList == null)
		{
			valueList = vDao.getLookupValueFullList();
		}
		return valueList;
	}
	


	
	public LookupValue getSelectedValue()
	{
		return selectedValue;
	}

	
	public void setSelectedValue(LookupValue selectedValue)
	{
		this.selectedValue = selectedValue;
	}

	
	public LookupValue getRemoveValue()
	{
		return removeValue;
	}

	
	public void setRemoveValue(LookupValue removeValue)
	{
		this.removeValue = removeValue;
	}

	public void onRowEdit(RowEditEvent<LookupValue> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
        	try {
    			//Check Primary key is unique
    			int count = 0;
    			for (LookupValue r: valueList){
    				if (event.getObject().getPk().equals(r.getPk())) {
    					count++;
    				}
    			}
    			if (count > 1) {
    				isDuplicateKey = true;
    			}
    			
    			if (isDuplicateKey) {
    				String param = "Primary key";
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    			
    			////Check data
    			if (event.getObject().getPk().getLookup_code().isEmpty()) {
        			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Code cannot be null", ""));
        		}
     
    			if (event.getObject().getPk().getLookup_type().isEmpty()) {
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Type cannot be null", ""));
    			}
    			
    			if (event.getObject().getDescription().length() > 1000) {
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Description is too long", ""));
    			}
    			
    			//Update lookup value
        		if (fCtx.getMessageList().isEmpty()) {
        			event.getObject().setUserstamp(getLoginUserId());
        			vDao.updateLookupValue(event.getObject());
        			if (removeValue != null) {
         				vDao.deleteLookupValue(removeValue.getPk().getLookup_type(), removeValue.getPk().getLookup_code(), removeValue.getPk().getLanguage());
         				removeValue = null;
         			}
        			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
        			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getPk().getLookup_code());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedValue = null;
	        		valueList = null;
        		}
        	}
        	catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("Lookup");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
	            
	        
    }
	
	public void onRowCancel(RowEditEvent<LookupValue> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getPk().getLookup_code()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();

 
    }
    
    public void onAddNew() {
    	LookupValue newLookup = new LookupValue();
    	valueList.add(0, newLookup);
    }
   
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		//removeValue =  vDao.getLookupValue((String)event.getOldValue());
    	}
    }    
    
    public void deleteValue() throws Exception {
    	if (selectedValue != null) {
    		try {
    			if (selectedValue.getPk().getLookup_code() != null) {
			    	vDao.deleteLookupValue(selectedValue.getPk().getLookup_type(), selectedValue.getPk().getLookup_code(), selectedValue.getPk().getLanguage());
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedValue.getPk().getLookup_code());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		valueList.remove(selectedValue);
		        selectedValue = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedValue.getPk().getLookup_code());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
}
