package hk.eduhk.rich.view;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.persistence.OptimisticLockException;

import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.axes.cartesian.CartesianScales;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearAxes;
import org.primefaces.model.charts.axes.cartesian.linear.CartesianLinearTicks;
import org.primefaces.model.charts.bar.BarChartDataSet;
import org.primefaces.model.charts.bar.BarChartModel;
import org.primefaces.model.charts.bar.BarChartOptions;
import org.primefaces.model.charts.optionconfig.animation.Animation;
import org.primefaces.model.charts.optionconfig.legend.Legend;
import org.primefaces.model.charts.optionconfig.legend.LegendLabel;
import org.primefaces.model.charts.optionconfig.title.Title;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.primefaces.model.charts.pie.PieChartOptions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.event.CloseEvent;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.entity.ManageRIView;
import hk.eduhk.rich.entity.Summary;
import hk.eduhk.rich.entity.project.FundSource;
import hk.eduhk.rich.entity.project.ProjectDAO;
import hk.eduhk.rich.entity.project.ProjectDetails_P;
import hk.eduhk.rich.entity.report.CdcfRptDAO;
import hk.eduhk.rich.entity.report.CdcfRptPeriod;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name = "viewProjectSumView")
@ViewScoped
@SuppressWarnings("serial")
public class ViewProjectSumView extends ViewSumView
{
	private static Logger logger = Logger.getLogger(ViewProjectSumView.class.getName());
	
	private BarChartModel projectYrTotalBarModel;
	private BarChartModel projectFundedProjBarModel;
	private BarChartModel projectTypeBarModel;
	private BarChartModel projectTechBarModel;
	private PieChartModel projectFundBodyPieCurModel;
	private PieChartModel projectFundBodyPieNewModel;
	private PieChartModel projectTypePieModel;
	private PieChartModel projectTechPieModel;
	
	private BarChartModel projectSectorBarModel;
	
	private List<FundSource> fundSource_list;
	private List<FundSource> fundSource_cat_list  = new ArrayList<FundSource>();
	private String[] colors = {"#619ED6", "#6BA547", "#F7D027", "#E48F18", "#B77EA3", "#E64345", "#60CEED", "#9CF168", "#F7EA4A", "#FBC543", "#FFC9ED", "#E6696E"};
	private ProjectDAO projDao = ProjectDAO.getInstance();
	

	@PostConstruct
    public void init() throws ParseException {
		staffDetail = getStaffDetail(getParamPid(), null, true);
		//updateChart();
    }

	public void updateChart() throws SQLException, ParseException
	{
		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		projectCount = null;
		projectCountList = null;

    	createProjectYrTotalBarModel();
    	createProjectFundedProjBarModel();
    	createProjectTypeBarModel();
    	createProjectTechBarModel();
    	//createProjectFundBodyPieCurModel();
    	createProjectFundBodyPieNewModel();
    	//createProjectTypePieModel();
    	//createProjectTechPieModel();
	}

	
	private void createProjectFundBodyPieNewModel() throws ParseException, SQLException 
	{
		projectFundBodyPieNewModel = new PieChartModel();
        ChartData data = new ChartData();

        PieChartDataSet dataSet = new PieChartDataSet();
        List<Number> values = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());

  		fundSource_cat_list = getFundSource_cat_list();
  		List<String> bgColors = new ArrayList<>();
  		List<String> labels = new ArrayList<>();
  		if (getRiChartPeriodList() != null) {
	  		for (int i = 0; i < fundSource_cat_list.size(); i++) {
	  			int count1 = 0;
	  	        List<Summary>countList1 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(i).getPk().getLookup_code());
	  	        if (countList1 != null) {
	  	        	for (CdcfRptPeriod p:riChartPeriodList) {
	  	        		//List<Summary> match1 = countList1.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	  	        		Double c1 = countList1.stream()
								.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
	  	        		int count = (int) Math.round(c1);
	  	        		count1 += count;
	  	        	}
	  	        }
	  	        values.add(count1);
	  	      bgColors.add(colors[i]);
	  	    labels.add(fundSource_cat_list.get(i).getPk().getLookup_code());
	  		}
  		}
        dataSet.setData(values);
        dataSet.setBackgroundColor(bgColors);

        data.addChartDataSet(dataSet);
        data.setLabels(labels);
        
        PieChartOptions options = new PieChartOptions();
        Title title = new Title();
        title.setDisplay(true);
        title.setText("Funding Body");
        options.setTitle(title);
        
        projectFundBodyPieNewModel.setOptions(options);
        projectFundBodyPieNewModel.setExtender("pieChartExtender");
        projectFundBodyPieNewModel.setData(data);
    }
	
	private void createProjectTypePieModel() throws ParseException, SQLException 
	{
		projectTypePieModel = new PieChartModel();
        ChartData data = new ChartData();

        PieChartDataSet dataSet = new PieChartDataSet();
        List<Number> values = new ArrayList<>();
        long researchRelated = getProjectTypeCount("R");
        long notResearchRelated = getProjectTypeCount("N");
        values.add(researchRelated);
        values.add(notResearchRelated);
        dataSet.setData(values);

        List<String> bgColors = new ArrayList<>();
        bgColors.add("#fe8a7d");
        bgColors.add("#0ac282");
        //bgColors.add("#fe5d70");
        //bgColors.add("#01a9ac");
        dataSet.setBackgroundColor(bgColors);

        data.addChartDataSet(dataSet);
        List<String> labels = new ArrayList<>();
        labels.add("Research Related");
        labels.add("Not Research Related");
        data.setLabels(labels);
        
        PieChartOptions options = new PieChartOptions();
        Title title = new Title();
        title.setDisplay(true);
        title.setText("Total of Research Related: " + researchRelated + ", Total of Not Research Related: " + notResearchRelated);
        options.setTitle(title);
        
        projectTypePieModel.setOptions(options);
        projectTypePieModel.setExtender("pieChartExtender");
        projectTypePieModel.setData(data);
    }
	
	//create is funded project
	//bar chart
	public void createProjectFundedProjBarModel() throws ParseException, SQLException
	{
		projectFundedProjBarModel = new BarChartModel();

        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();     
        barDataSet.setLabel("Yes (Current Proj.)");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("No (Current Proj.)");
        barDataSet2.setBackgroundColor("#fccc44");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        BarChartDataSet barDataSet3 = new BarChartDataSet();   
        barDataSet3.setLabel("Yes (New Proj.)");
        barDataSet3.setBackgroundColor("#0F9D58");
        barDataSet3.setStack("Stack 3");
        List<Number> dataVal3 = new ArrayList<>();
        
        BarChartDataSet barDataSet4 = new BarChartDataSet();
        barDataSet4.setLabel("No (New Proj.)");
        barDataSet4.setBackgroundColor("#23c275");
        barDataSet4.setStack("Stack 4");
        List<Number> dataVal4 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> projectCountYCList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), "Y", null, null, null);
  		List<Summary> projectCountNCList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), "N", null, null, null);
  		List<Summary> projectCountYNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), "Y", null, null, null);
  		List<Summary> projectCountNNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), "N", null, null, null);
  		int countYCTotal = 0;
  		int countNCTotal = 0;
  		int countYNTotal = 0;
  		int countNNTotal = 0; 
        if (getRiChartPeriodList() != null && projectCountYCList != null && projectCountNCList != null && projectCountYNList != null && projectCountNNList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            //List<Summary> match1 = projectCountYCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	  	        Double c1 = projectCountYCList.stream()
							.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
  	            int countYC = (int) Math.round(c1);
  	            dataVal.add(countYC);
  	            countYCTotal += countYC;
  	            
	            //List<Summary> match2 = projectCountNCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c2 = projectCountNCList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countNC = (int) Math.round(c2);
	            dataVal2.add(countNC);
	            countNCTotal += countNC;
	            
	            //List<Summary> match3 = projectCountYNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c3 = projectCountYNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
  	            int countYN = (int) Math.round(c3);
  	            dataVal3.add(countYN);
  	            countYNTotal += countYN;
  	            
	            //List<Summary> match4 = projectCountNNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c4 = projectCountNNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countNN = (int) Math.round(c4);
	            dataVal4.add(countNN);
	            countNNTotal += countNN;
        	}
         }

        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);
        barDataSet3.setData(dataVal3);
        barDataSet4.setData(dataVal4);
        
        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);
        data.addChartDataSet(barDataSet3);
        data.addChartDataSet(barDataSet4);

        data.setLabels(labels);
        projectFundedProjBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(true);
        //title.setText("Total of Yes: " + countYTotal + ", Total of No: " + countNTotal);
        options.setTitle(title);


        //outputIntConfBarModel.setOptions(options);
        projectFundedProjBarModel.setExtender("countBarChartExtender");
	}
	
	//create Project Type
	//bar chart
	public void createProjectTypeBarModel() throws ParseException, SQLException
	{
		projectTypeBarModel = new BarChartModel();

        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();     
        barDataSet.setLabel("Research Related (Current Proj.)");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("Not Research Related (Current Proj.)");
        barDataSet2.setBackgroundColor("#fccc44");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        BarChartDataSet barDataSet3 = new BarChartDataSet();   
        barDataSet3.setLabel("Research Related (New Proj.)");
        barDataSet3.setBackgroundColor("#0F9D58");
        barDataSet3.setStack("Stack 3");
        List<Number> dataVal3 = new ArrayList<>();
        
        BarChartDataSet barDataSet4 = new BarChartDataSet();
        barDataSet4.setLabel("Not Research Related (New Proj.)");
        barDataSet4.setBackgroundColor("#23c275");
        barDataSet4.setStack("Stack 4");
        List<Number> dataVal4 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> projectCountYCList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, "R", null, null);
  		List<Summary> projectCountNCList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, "N", null, null);
  		List<Summary> projectCountYNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, "R", null, null);
  		List<Summary> projectCountNNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, "N", null, null);
  		int countYCTotal = 0;
  		int countNCTotal = 0;
  		int countYNTotal = 0;
  		int countNNTotal = 0; 
        if (getRiChartPeriodList() != null && projectCountYCList != null && projectCountNCList != null && projectCountYNList != null && projectCountNNList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            //List<Summary> match1 = projectCountYCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
  	            Double c1 = projectCountYCList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
  	            int countYC = (int) Math.round(c1);
  	            dataVal.add(countYC);
  	            countYCTotal += countYC;
  	            
	            //List<Summary> match2 = projectCountNCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
  	            Double c2 = projectCountNCList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countNC = (int) Math.round(c2);
	            dataVal2.add(countNC);
	            countNCTotal += countNC;
	            
	            //List<Summary> match3 = projectCountYNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c3 = projectCountYNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
  	            int countYN = (int) Math.round(c3);
  	            dataVal3.add(countYN);
  	            countYNTotal += countYN;
  	            
	            //List<Summary> match4 = projectCountNNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c4 = projectCountNNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countNN = (int) Math.round(c4);
	            dataVal4.add(countNN);
	            countNNTotal += countNN;
        	}
         }

        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);
        barDataSet3.setData(dataVal3);
        barDataSet4.setData(dataVal4);
        
        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);
        data.addChartDataSet(barDataSet3);
        data.addChartDataSet(barDataSet4);

        data.setLabels(labels);
        projectTypeBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(true);
        //title.setText("Total of Yes: " + countYTotal + ", Total of No: " + countNTotal);
        options.setTitle(title);


        //outputIntConfBarModel.setOptions(options);
        projectTypeBarModel.setExtender("countBarChartExtender");
	}
		
	//create Is it an Innovation/Technology (創科) related project?
	//bar chart
	public void createProjectTechBarModel() throws ParseException, SQLException
	{
		projectTechBarModel = new BarChartModel();

        ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();     
        barDataSet.setLabel("Yes (Current Proj.)");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("No (Current Proj.)");
        barDataSet2.setBackgroundColor("#fccc44");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        BarChartDataSet barDataSet3 = new BarChartDataSet();   
        barDataSet3.setLabel("Yes (New Proj.)");
        barDataSet3.setBackgroundColor("#0F9D58");
        barDataSet3.setStack("Stack 3");
        List<Number> dataVal3 = new ArrayList<>();
        
        BarChartDataSet barDataSet4 = new BarChartDataSet();
        barDataSet4.setLabel("No (New Proj.)");
        barDataSet4.setBackgroundColor("#23c275");
        barDataSet4.setStack("Stack 4");
        List<Number> dataVal4 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> projectCountYCList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, "Y", null);
  		List<Summary> projectCountNCList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, "N", null);
  		List<Summary> projectCountYNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, "Y", null);
  		List<Summary> projectCountNNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, "N", null);
  		int countYCTotal = 0;
  		int countNCTotal = 0;
  		int countYNTotal = 0;
  		int countNNTotal = 0; 
        if (getRiChartPeriodList() != null && projectCountYCList != null && projectCountNCList != null && projectCountYNList != null && projectCountNNList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            //List<Summary> match1 = projectCountYCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
  	            Double c1 = projectCountYCList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
  	            int countYC = (int) Math.round(c1);
  	            dataVal.add(countYC);
  	            countYCTotal += countYC;
  	            
	            //List<Summary> match2 = projectCountNCList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c2 = projectCountNCList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countNC = (int) Math.round(c2);
	            dataVal2.add(countNC);
	            countNCTotal += countNC;
	            
	            //List<Summary> match3 = projectCountYNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c3 = projectCountYNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countYN = (int) Math.round(c3);
  	            dataVal3.add(countYN);
  	            countYNTotal += countYN;
  	            
	            //List<Summary> match4 = projectCountNNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c4 = projectCountNNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countNN = (int) Math.round(c4);
	            dataVal4.add(countNN);
	            countNNTotal += countNN;
        	}
         }

        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);
        barDataSet3.setData(dataVal3);
        barDataSet4.setData(dataVal4);
        
        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);
        data.addChartDataSet(barDataSet3);
        data.addChartDataSet(barDataSet4);

        data.setLabels(labels);
        projectTechBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(true);
        //title.setText("Total of Yes: " + countYTotal + ", Total of No: " + countNTotal);
        options.setTitle(title);


        //outputIntConfBarModel.setOptions(options);
        projectTechBarModel.setExtender("countBarChartExtender");
	}
	
	
	private void createProjectTechPieModel() throws ParseException, SQLException 
	{
		projectTechPieModel = new PieChartModel();
        ChartData data = new ChartData();

        PieChartDataSet dataSet = new PieChartDataSet();
        List<Number> values = new ArrayList<>();
        long countYes = getProjectTechCount("Y");
        long countNo = getProjectTechCount("N");
        values.add(countYes);
        values.add(countNo);
        dataSet.setData(values);

        List<String> bgColors = new ArrayList<>();
        bgColors.add("#fe8a7d");
        bgColors.add("#0ac282");
        //bgColors.add("#fe5d70");
        //bgColors.add("#01a9ac");
        dataSet.setBackgroundColor(bgColors);

        data.addChartDataSet(dataSet);
        List<String> labels = new ArrayList<>();
        labels.add("Yes");
        labels.add("No");
        data.setLabels(labels);
        
        PieChartOptions options = new PieChartOptions();
        Title title = new Title();
        title.setDisplay(true);
        title.setText("Total of Yes: " + countYes + ", Total of No: " + countNo);
        options.setTitle(title);
        
        projectTechPieModel.setOptions(options);
        projectTechPieModel.setExtender("pieChartExtender");
        projectTechPieModel.setData(data);
    }
	
	public long getProjectTypeCount(String code) throws ParseException, SQLException
	{
		int countTotal = 0;
		if (!Strings.isNullOrEmpty(code)) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
	  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
			List<Summary> tmpList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, code, null, null);
			if (getRiChartPeriodList() != null && tmpList != null) {
	  			for (CdcfRptPeriod p:riChartPeriodList) {
	  				//List<Summary> match1 = tmpList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	  				Double c1 = tmpList.stream()
							.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
	  	            int count = (int) Math.round(c1);
	  	            countTotal += count;
	  			}
			}
		}
		return countTotal;
	}
	
	public long getProjectTechCount(String code) throws ParseException, SQLException
	{
		int countTotal = 0;
		if (!Strings.isNullOrEmpty(code)) {
			String startDate = DateToMonthYearFormat(getSelectedStartDate());
	  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
			List<Summary> tmpList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, code, null);
			if (getRiChartPeriodList() != null && tmpList != null) {
	  			for (CdcfRptPeriod p:riChartPeriodList) {
	  				//List<Summary> match1 = tmpList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	  				Double c1 = tmpList.stream()
							.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
	  	            int count = (int) Math.round(c1);
	  	            countTotal += count;
	  			}
			}
		}
		return countTotal;
	}

	
	public void createProjectYrTotalBarModel() throws ParseException, SQLException 
	{
		projectYrTotalBarModel = new BarChartModel();
		
		ChartData data = new ChartData();

        BarChartDataSet barDataSet = new BarChartDataSet();
                
        barDataSet.setLabel("Current Projects");
        barDataSet.setBackgroundColor("#F4B400");
        barDataSet.setStack("Stack 0");
        List<Number> dataVal = new ArrayList<>();
        
        BarChartDataSet barDataSet2 = new BarChartDataSet();
        barDataSet2.setLabel("New Projects");
        barDataSet2.setBackgroundColor("#0F9D58");
        barDataSet2.setStack("Stack 1");
        List<Number> dataVal2 = new ArrayList<>();
        
        List<String> labels = new ArrayList<>();
        String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
        List<Summary> projectCountYList = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, null, null);
  		List<Summary> projectCountNList = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), true, getSelectedFundingSourceList(), null, null, null, null);
        int countYTotal = 0;
  		int countNTotal = 0;
        if (getRiChartPeriodList() != null && projectCountYList != null && projectCountNList != null) {
  			for (CdcfRptPeriod p:riChartPeriodList) {
  	            labels.add(p.getChart_desc());
  	            //List<Summary> match1 = projectCountYList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	  	        Double c1 = projectCountYList.stream()
							.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
  	            
  	            int countY = (int) Math.round(c1);
  	            dataVal.add(countY);
  	            countYTotal += countY;
  	            
	            //List<Summary> match2 = projectCountNList.stream().filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id())).collect(Collectors.toList());
	            Double c2 = projectCountNList.stream()
						.filter(d -> String.valueOf(p.getPeriod_id()).equals(d.getPeriod_id()))
						.mapToDouble(d -> Double.parseDouble(d.getCount()))
						.sum();
	            int countN = (int) Math.round(c2);
	            dataVal2.add(countN);
	            countNTotal += countN;
        	}
         }

        barDataSet.setData(dataVal);
        barDataSet2.setData(dataVal2);

        data.addChartDataSet(barDataSet);
        data.addChartDataSet(barDataSet2);


        data.setLabels(labels);
        projectYrTotalBarModel.setData(data);

        //Options
        BarChartOptions options = new BarChartOptions();
        CartesianScales cScales = new CartesianScales();
        CartesianLinearAxes linearAxes = new CartesianLinearAxes();
        linearAxes.setStacked(true);
        linearAxes.setOffset(true);
        cScales.addXAxesData(linearAxes);
        cScales.addYAxesData(linearAxes);
        options.setScales(cScales);

        Title title = new Title();
        title.setDisplay(true);
        title.setText("Total of New: " + countYTotal + ", Total of Current: " + countNTotal);
        options.setTitle(title);


        //outputIntConfBarModel.setOptions(options);
        projectYrTotalBarModel.setExtender("barChartExtender");
	}

	
	public BarChartModel getProjectYrTotalBarModel()
	{
		return projectYrTotalBarModel;
	}


	
	public void setProjectYrTotalBarModel(BarChartModel projectYrTotalBarModel)
	{
		this.projectYrTotalBarModel = projectYrTotalBarModel;
	}


	
	
	public BarChartModel getProjectTypeBarModel()
	{
		return projectTypeBarModel;
	}

	
	public void setProjectTypeBarModel(BarChartModel projectTypeBarModel)
	{
		this.projectTypeBarModel = projectTypeBarModel;
	}

	
	public BarChartModel getProjectTechBarModel()
	{
		return projectTechBarModel;
	}

	
	public void setProjectTechBarModel(BarChartModel projectTechBarModel)
	{
		this.projectTechBarModel = projectTechBarModel;
	}

	public PieChartModel getProjectTypePieModel()
	{
		return projectTypePieModel;
	}


	
	public void setProjectTypePieModel(PieChartModel projectTypePieModel)
	{
		this.projectTypePieModel = projectTypePieModel;
	}
	
	
	public PieChartModel getProjectTechPieModel()
	{
		return projectTechPieModel;
	}


	
	public void setProjectTechPieModel(PieChartModel projectTechPieModel)
	{
		this.projectTechPieModel = projectTechPieModel;
	}


	


	
	public PieChartModel getProjectFundBodyPieCurModel()
	{
		return projectFundBodyPieCurModel;
	}

	
	public void setProjectFundBodyPieCurModel(PieChartModel projectFundBodyPieCurModel)
	{
		this.projectFundBodyPieCurModel = projectFundBodyPieCurModel;
	}

	
	public PieChartModel getProjectFundBodyPieNewModel()
	{
		return projectFundBodyPieNewModel;
	}

	
	public void setProjectFundBodyPieNewModel(PieChartModel projectFundBodyPieNewModel)
	{
		this.projectFundBodyPieNewModel = projectFundBodyPieNewModel;
	}

	public BarChartModel getProjectFundedProjBarModel()
	{
		return projectFundedProjBarModel;
	}


	
	public void setProjectFundedProjBarModel(BarChartModel projectFundedProjBarModel)
	{
		this.projectFundedProjBarModel = projectFundedProjBarModel;
	}


	
	public BarChartModel getProjectSectorBarModel()
	{
		return projectSectorBarModel;
	}


	
	public void setProjectSectorBarModel(BarChartModel projectSectorBarModel)
	{
		this.projectSectorBarModel = projectSectorBarModel;
	}
	
	public String getSelectedFundingSourceString()
	{
		String listString = "";
		if (ArrayUtils.isEmpty(getSelectedFundingSources()) == false) {
			listString = String.join(",", getSelectedFundingSources());
		}
		return listString;
	}

	public List<FundSource> getFundSource_list()
	{
		if (fundSource_list == null) {
			fundSource_list = projDao.getFundSourceList(1);
		}
		return fundSource_list;
	}

	
	public void setFundSource_list(List<FundSource> fundSource_list)
	{
		this.fundSource_list = fundSource_list;
	}
	
	public List<FundSource> getFundSource_cat_list()
	{
		if (fundSource_cat_list.isEmpty()) {
			fundSource_list = getFundSource_list();
			HashSet unique=new HashSet();
			for (FundSource f:fundSource_list) {
				String catArray[] = f.getDescription().split(" - ");
				if (catArray.length > 0) {
					String cat = catArray[0].trim();
					if (unique.add(cat)) {
						FundSource tmp = new FundSource();
						tmp.getPk().setLookup_code(cat);
						//tmp.setDescription(cat.replace("Govt", "Government"));
						fundSource_cat_list.add(tmp);
					}
				}
				
			}
		}
		return fundSource_cat_list;
	}

	
	public void setFundSource_cat_list(List<FundSource> fundSource_cat_list)
	{
		this.fundSource_cat_list = fundSource_cat_list;
	}
	
	public void exportSummary() 
	{
		selectedDepts = null;
		selectedFacs = null;
		riChartPeriodList = null;
		ktChartPeriodList = null;
		projectCount = null;
		projectCountList = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();

		try 
		{
			Workbook wb = null;
    		wb = new XSSFWorkbook();
    		

    		//Total
    		createDataSheetByFilter(wb, "total");
    		//Funded Project?
    		createDataSheetByFilter(wb, "fundedProj");
    		//Project Type
    		createDataSheetByFilter(wb, "projType");
    		//Is it an Innovation/Technology (創科) related project?
    		createDataSheetByFilter(wb, "tech");
    		//Funding Body
    		//createDataSheetByFilter(wb, "funding_body_cur");
    		//Funding Body
    		createDataSheetByFilter(wb, "funding_body_new");
    		
	    	// Get the byte array of the Workbook
	    	ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			eCtx.responseReset();
			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");

			DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = "DataExport-" + dateFormat.format(new Date()) + ".xlsx";		        
			
			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
		
			fCtx.responseComplete();
		}
		catch (IOException e) 
    	{
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.unexpected");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			logger.log(Level.SEVERE, message, e);
		}
				
	}
		
	/*private void createDataSheet(Workbook workbook) throws NumberFormatException, ParseException, SQLException
	{
		int numOfRows = 0;
			
		Sheet sheet = workbook.createSheet("Total no. of Projects");
    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
        String sheetHeader = "Total no. of Projects";
    	String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null) {
    			depts = selectedDepts.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}
    	String[] headerLastArray = {"Total no."};
    	headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());
	    		
	    		if ("Y".equals(getParamAdmin())) {
	    			if (depts != null) {
	    				for (int j = 0; j < depts.length; j++) {
	    					String facDept = depts[j];
	    					Double c = projectCountList.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c);
	    				}
	    			}
	    		}
	    		
	    		Double count = getProjectTotalCount(r.getPeriod_id(), false);
	    		cell = row.createCell(i++);
	    		cell.setCellValue(count);
	    		
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}*/
	
	private void createDataSheetByFilter(Workbook workbook, String type) throws NumberFormatException, ParseException, SQLException
	{
		
		List<String> columnName = new ArrayList<>();
		String sheetName = "";
		String sheetHeader = "";
		int countListNo = 0;
		List<Summary> countList1 = new ArrayList<>();
		List<Summary> countList2 = new ArrayList<>();
		List<Summary> countList3 = new ArrayList<>();
		List<Summary> countList4 = new ArrayList<>();
		List<Summary> countList5 = new ArrayList<>();
		List<Summary> countList6 = new ArrayList<>();
		List<Summary> countList7 = new ArrayList<>();
		List<Summary> countList8 = new ArrayList<>();
  		String startDate = DateToMonthYearFormat(getSelectedStartDate());
  		String endDate = DateToMonthYearFormat(getSelectedEndDate());
		switch(type) {
			case "total":
				countListNo = 2;
				sheetName = "Total no";
				sheetHeader = "Total no. of Projects";
				columnName.add("Current Projects");
				columnName.add("New Projects");
		  		countList1 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, null);
		  		countList2 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, null);
		        break;
			case "fundedProj":
				countListNo = 4;
				sheetName = "Funded Project";
				sheetHeader = "Funded Project?";
				columnName.add("Yes (Current Projects)");
				columnName.add("No (Current Projects)");
				columnName.add("Yes (New Projects)");
				columnName.add("No (New Projects)");
		  		countList1 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), "Y", null, null, null);
		  		countList2 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), "N", null, null, null);
		  		countList3 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), "Y", null, null, null);
		  		countList4 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), "N", null, null, null);
		        break;
			case "projType":
				countListNo = 4;
				sheetName = "Project Type";
				sheetHeader = "Project Type";
				columnName.add("Research Related (Current Projects)");
				columnName.add("Not Research Related (Current Projects)");
				columnName.add("Research Related (New Projects)");
				columnName.add("Not Research Related (New Projects)");
				countList1 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, "R", null, null);
		  		countList2 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, "N", null, null);
		  		countList3 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, "R", null, null);
		  		countList4 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, "N", null, null);
		  		break;
			case "tech":
				countListNo = 4;
				sheetName = "Technology related";
				sheetHeader = "Is it an Innovation/Technology (創科) related project?";
				columnName.add("Yes (Current Projects)");
				columnName.add("No (Current Projects)");
				columnName.add("Yes (New Projects)");
				columnName.add("No (New Projects)");
				countList1 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, "Y", null);
		  		countList2 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, "N", null);
		  		countList3 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, "Y", null);
		  		countList4 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, "N", null);
		  		break;
			case "funding_body_cur":
				countListNo = 8;
				sheetName = "Current Funding Body";
				sheetHeader = "Funding Body (Current Project)";
				if (getFundSource_cat_list() != null) {
					if (fundSource_cat_list.size() == countListNo) {
						for (int i = 0; i < fundSource_cat_list.size(); i++) {
							columnName.add(fundSource_cat_list.get(i).getPk().getLookup_code());
						}
						countList1 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(0).getPk().getLookup_code());
						countList2 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(1).getPk().getLookup_code());
						countList3 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(2).getPk().getLookup_code());
						countList4 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(3).getPk().getLookup_code());
						countList5 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(4).getPk().getLookup_code());
						countList6 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(5).getPk().getLookup_code());
						countList7 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(6).getPk().getLookup_code());
						countList8 = projDao.getProjectSummaryCountList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(7).getPk().getLookup_code());
					}
				}
				break;
			case "funding_body_new":
				countListNo = 8;
				sheetName = "New Funding Body";
				sheetHeader = "Funding Body (New Project)";
				if (getFundSource_cat_list() != null) {
					if (fundSource_cat_list.size() == countListNo) {
						for (int i = 0; i < fundSource_cat_list.size(); i++) {
							columnName.add(fundSource_cat_list.get(i).getPk().getLookup_code());
						}
						countList1 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(0).getPk().getLookup_code());
						countList2 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(1).getPk().getLookup_code());
						countList3 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(2).getPk().getLookup_code());
						countList4 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(3).getPk().getLookup_code());
						countList5 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(4).getPk().getLookup_code());
						countList6 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(5).getPk().getLookup_code());
						countList7 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(6).getPk().getLookup_code());
						countList8 = projDao.getProjectSummaryCountNewList(getSumStaffNo(), getRiSumDataLevel(), startDate, endDate, getSelectedDepts(), false, getSelectedFundingSourceList(), null, null, null, fundSource_cat_list.get(7).getPk().getLookup_code());
					}
				}
				break;
			case "":
				break;
		}
		
		int numOfRows = 0;
		
		Sheet sheet = workbook.createSheet(sheetName);

    	sheet.createFreezePane(0, 1);
    	
    	Row row = sheet.createRow(0);
    	Cell cell = null;
    	
    	CellStyle hStyle=null;
    	// Creating a font
        Font font= workbook.createFont();
        //font.setFontName("Arial");
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setBold(true);
    	hStyle=workbook.createCellStyle();
        hStyle.setFont(font);
        
  		String[] headerArray = {"Reporting Year"};
    	String[] depts = new String[0];
    	List<String> deptsColsHeader = new ArrayList<>();
    	if ("Y".equals(getParamAdmin())) {
    		if (getSelectedDepts() != null) {
    			for (int i = 0; i < selectedDepts.size(); i++) {
    				for (int j = 0; j < columnName.size(); j++) {
    					deptsColsHeader.add(columnName.get(j) +" ("+ selectedDepts.get(i)+")");
    				}
    			}
    			depts = deptsColsHeader.toArray(new String[0]);
    		}
    		headerArray = ArrayUtils.addAll(headerArray, depts);	
    	}else {
    		List<String> headerLastList = new ArrayList<>();
        	for (int j = 0; j < columnName.size(); j++) {
        		headerLastList.add("Total no. of "+columnName.get(j));
    		}
        	String[] headerLastArray = headerLastList.toArray(new String[0]);
        	headerArray = ArrayUtils.addAll(headerArray, headerLastArray);
    	}
    	
    	
    	//First row header
    	cell = row.createCell(0);
		cell.setCellValue(sheetHeader);
		cell.setCellStyle(hStyle);  
    	row = sheet.createRow(sheet.getLastRowNum()+1);
    	
    	for (int n=0;n<headerArray.length;n++)
    	{    		
    		cell = row.createCell(n);
    		cell.setCellValue(headerArray[n]);
    		sheet.autoSizeColumn(n);
    	}
    	
    	// for logging purpose
		long startTime = System.currentTimeMillis();
		
		if (getRiChartPeriodList() != null) {
			// Create data rows
			for(CdcfRptPeriod r:riChartPeriodList) {
				int i = 0;
				row = sheet.createRow(sheet.getLastRowNum()+1);
				
				cell = row.createCell(i++);
	    		cell.setCellValue(r.getPeriod_desc());

	    		if ("Y".equals(getParamAdmin())) {
	    			if (selectedDepts != null) {
	    				for (int j = 0; j < selectedDepts.size(); j++) {
	    					String facDept = selectedDepts.get(j);
	    					Double c1 = 0.0;
    						c1 = countList1.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c1);
	    		    		
	    					Double c2 = 0.0;
    						c2 = countList2.stream()
	    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
	    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
	    							.sum();
	    					cell = row.createCell(i++);
	    		    		cell.setCellValue(c2);
	    		    		
	    		    		if (countListNo > 2) {
	    		    			Double c3 = 0.0;
	    						c3 = countList3.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
		    					cell = row.createCell(i++);
		    		    		cell.setCellValue(c3);
		    		    		
		    					Double c4 = 0.0;
	    						c4 = countList4.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
		    					cell = row.createCell(i++);
		    		    		cell.setCellValue(c4);
	    		    		}
	    		    		if (countListNo > 4) {
	    		    			Double c5 = 0.0;
	    						c5 = countList5.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
		    					cell = row.createCell(i++);
		    		    		cell.setCellValue(c5);
		    		    		
		    					Double c6 = 0.0;
	    						c6 = countList6.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
		    					cell = row.createCell(i++);
		    		    		cell.setCellValue(c6);
		    		    		
		    		    		Double c7 = 0.0;
	    						c7 = countList7.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
		    					cell = row.createCell(i++);
		    		    		cell.setCellValue(c7);
		    		    		
		    					Double c8 = 0.0;
	    						c8 = countList8.stream()
		    							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()) && facDept.equals(d.getFacDept()))
		    							.mapToDouble(d -> Double.parseDouble(d.getCount()))
		    							.sum();
		    					cell = row.createCell(i++);
		    		    		cell.setCellValue(c8);
	    		    		}
	    				}
	    			}
	    		}else {
	    			Double count1 = countList1.stream()
							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count1);
		    		
		    		Double count2 = countList2.stream()
							.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
							.mapToDouble(d -> Double.parseDouble(d.getCount()))
							.sum();
		    		cell = row.createCell(i++);
		    		cell.setCellValue(count2);
		    		
		    		if (countListNo > 2) {
		    			Double c3 = 0.0;
						c3 = countList3.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
						cell = row.createCell(i++);
			    		cell.setCellValue(c3);
			    		
						Double c4 = 0.0;
						c4 = countList4.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
						cell = row.createCell(i++);
			    		cell.setCellValue(c4);
		    		}
		    		if (countListNo > 4) {
		    			Double c5 = 0.0;
						c5 = countList5.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
						cell = row.createCell(i++);
			    		cell.setCellValue(c5);
			    		
						Double c6 = 0.0;
						c6 = countList6.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
						cell = row.createCell(i++);
			    		cell.setCellValue(c6);
			    		
			    		Double c7 = 0.0;
						c7 = countList7.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
						cell = row.createCell(i++);
			    		cell.setCellValue(c7);
			    		
						Double c8 = 0.0;
						c8 = countList8.stream()
								.filter(d -> String.valueOf(r.getPeriod_id()).equals(d.getPeriod_id()))
								.mapToDouble(d -> Double.parseDouble(d.getCount()))
								.sum();
						cell = row.createCell(i++);
			    		cell.setCellValue(c8);
		    		}
	    		}
    			
	    		numOfRows ++;
			}
		}
		getLogger().log(Level.FINER, "Time to process " + numOfRows + " rows=" + (System.currentTimeMillis() - startTime) + "ms");
	}
}


