package hk.eduhk.rich.view;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.faces.bean.ManagedBean;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.access.AccessCacheDAO;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.access.UserRole;
import hk.eduhk.rich.access.UserSessionView;
import hk.eduhk.rich.entity.Department;
import hk.eduhk.rich.entity.LookupValue;
import hk.eduhk.rich.entity.LookupValueDAO;
import hk.eduhk.rich.entity.publication.FundingSource;
import hk.eduhk.rich.entity.publication.OutputType;
import hk.eduhk.rich.entity.publication.OutputType_PK;
import hk.eduhk.rich.entity.publication.PublicationDAO;
import hk.eduhk.rich.entity.query.ExStaffQueryChain;
import hk.eduhk.rich.entity.query.RIQueryChain;
import hk.eduhk.rich.entity.query.StaffQueryChain;
import hk.eduhk.rich.entity.rae.RaeStaffDAO;
import hk.eduhk.rich.entity.rae.RaeUOA;
import hk.eduhk.rich.entity.rae.RaeUOADAO;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffIdentity;
import hk.eduhk.rich.entity.staff.StaffPast;
import hk.eduhk.rich.entity.staff.StaffRank;
import hk.eduhk.rich.util.PersistenceManager;
import hk.eduhk.rich.entity.staff.SecDataUser;;


@SuppressWarnings("serial")
public class RISearchPanel implements Serializable
{
	public static final String SELECT_ALL_VALUE = "all";
	public static final String SELECT_ALL_LABEL = "Select All";
	public static final String SELECT_LABEL = "Select";
	public static final String YES_VALUE = "Y";
	public static final String YES_LABEL = "Yes";
	public static final String NO_VALUE = "N";
	public static final String NO_LABEL = "No";
	public static final String EMPTY_VALUE = "empty";
	public static final String EMPTY_LABEL = " ";
	public static final String UNCONFIRM_VALUE = "U";
	public static final String UNCONFIRM_LABEL = "Unconfirm";
	
	public static final String RI_TYPE_OUTPUT = "Output";
	public static final String RI_TYPE_PROJECT = "Project";
	public static final String RI_TYPE_AWARD = "Award";
	public static final String RI_TYPE_PATENT = "Patent";
	public static final String RI_TYPE_RAE = "RAE Output";
	
	public static final String RICH_EDITION_VALUE = "P";
	public static final String RICH_EDITION_LABEL = "RICH Edition";
	public static final String CDCF_SNAPSHOT_VALUE = "C";
	public static final String CDCF_SNAPSHOT_LABEL = "CDCF Snapshot";
	
	public static final String CDCF_PENDING_VALUE = "CDCF_PENDING";
	public static final String CDCF_PENDING_LABEL = "CDCF Pending";
	public static final String CDCF_PROCESSED_VALUE = "CDCF_PROCESSED";
	public static final String CDCF_PROCESSED_LABEL = "CDCF Processed";
	public static final String CDCF_GENERATED_VALUE = "CDCF_GENERATED";
	public static final String CDCF_GENERATED_LABEL = "CDCF Generated";
	public static final String CDCF_NOT_SELECTED_VALUE = "CDCF_NOT_SEL";
	public static final String CDCF_NOT_SELECTED_LABEL = "CDCF Not Selected";
	
	public static final String LIST_TYPE_RI_VALUE = "ri";
	public static final String LIST_TYPE_RI_LABEL = "By RI";
	public static final String LIST_TYPE_STAFF_VALUE = "staff";
	public static final String LIST_TYPE_STAFF_LABEL = "By Staff";
	
	public static final String SORT_COL_DEFAULT_VALUE = "default";
	public static final String SORT_COL_DEFAULT_LABEL = "Default";
	public static final String SORT_COL_RI_NO_VALUE = "riNo";
	public static final String SORT_COL_RI_NO_LABEL = "RI No.";
	public static final String SORT_COL_RI_NAME_VALUE = "riName";
	public static final String SORT_COL_RI_NAME_LABEL = "Title";
	public static final String SORT_COL_CONTRIBUTOR_VALUE = "contributor";
	public static final String SORT_COL_CONTRIBUTOR_LABEL = "Contributor Name";
	public static final String SORT_COL_FROM_DATE_VALUE = "fromDate";
	public static final String SORT_COL_FROM_DATE_LABEL = "From Date";
	public static final String SORT_COL_TO_DATE_VALUE = "toDate";
	public static final String SORT_COL_TO_DATE_LABEL = "To Date";
	public static final String SORT_COL_CDCF_STAT_VALUE = "cdcfStat";
	public static final String SORT_COL_CDCF_STAT_LABEL = "CDCF Status";
	
	
	public static final String RAE_SEL_NOSEL_TYPE_VALUE ="NS";
	public static final String RAE_SEL_NOSEL_TYPE_LABEL ="Not Selected";
	public static final String RAE_SEL_SEL_TYPE_VALUE ="S";
	public static final String RAE_SEL_SEL_TYPE_LABEL ="Selected";
	public static final String RAE_SEL_DW1_TYPE_VALUE ="DW1";
	public static final String RAE_SEL_DW1_TYPE_LABEL ="Double-weighted 1";
	public static final String RAE_SEL_BDW1_TYPE_VALUE ="DWB1";
	public static final String RAE_SEL_BDW1_TYPE_LABEL ="Reserve for Double-weighted 1";
	public static final String RAE_SEL_DW2_TYPE_VALUE ="DW2";
	public static final String RAE_SEL_DW2_TYPE_LABEL ="Double-weighted 2";
	public static final String RAE_SEL_BDW2_TYPE_VALUE ="DWB2";
	public static final String RAE_SEL_BDW2_TYPE_LABEL ="Reserve for Double-weighted 2";
	
	
	public static final String RAE_IES_VALUE = "IES";
	public static final String RAE_IES_LABEL = "Ineligible staff";
	public static final String RAE_IEO_VALUE = "IEO";
	public static final String RAE_IEO_LABEL = "Ineligible output";
	public static final String RAE_NIL_VALUE = "NIL";
	public static final String RAE_NIL_LABEL = "N/A";
	
	
	
	public static final String SORT_ORDER_ASC_VALUE = "ASC";
	public static final String SORT_ORDER_ASC_LABEL = "Ascending";
	public static final String SORT_ORDER_DESC_VALUE = "DESC";
	public static final String SORT_ORDER_DESC_LABEL = "Descending";

	// Selection lists - Person
	private List<String> staffNameList;
	private List<StaffRank> rankList;
	private List<Department> departmentList;
	
	// Selection lists - ex-IEd staff	
	private List<String> exStaffNameList;
	
	// Selection lists - ri
	private List<String> riTypeList;
	private List<SelectItem> outputTypeList;
	private List<LookupValue> sdgList;
	private List<SelectItem> viewTypeList;
	private List<SelectItem> cdcfStatusList;
	private List<SelectItem> consentIndicatorList;
	private List<SelectItem> fundingSrcList;
	
	//Selection Lists - RAE
	private List<SelectItem> uoaList;
	private List<SelectItem> raeFacList;
	private List<SelectItem> raeOutputTypeList;
	private List<SelectItem> raeSelTypeList;
	private List<SelectItem> raeInfoComList;
	private List<SelectItem> raeStatusList;
	private List<SelectItem> raeStatusSpecList;
	private List<SelectItem> raeStatusFullList;
	private List<SelectItem> raeStatusOtherList;
	private List<SelectItem> raeStatusInetList;
	private List<SelectItem> raeCitationList;
	private List<SelectItem> raeCopyrightList;
	
	
	// Selection lists - listing type
	private List<SelectItem> listingTypeList;
	private List<SelectItem> sortColList;
	private List<SelectItem> sortOrderList;
	
	
	// Selection lists - share
	private List<SelectItem> allYesNoEmptyList;
	
	// Selected values - Person	
	private String staffName;
	private List<String> selectedRankList;
	private String acadStaff = EMPTY_VALUE;
	private List<String> selectedDepartmentList;
	
	// Selected values - ex-IEd staff	
	private String exStaffName;
	private String formStaffNum;
	
	// Selected values - ri
	private String riType = null;
	private String riNo;
	private List<String> selectedOutputTypeList;
	private List<String> selectedSDGList;
	private String viewType = RICH_EDITION_VALUE;
	private String keyword;
	private String smartSearch;
	private String journalName;
	private String cdcfStatus = EMPTY_VALUE;
	private String insDecIndicator = EMPTY_VALUE;
	private String consentIndicator = EMPTY_VALUE;
	private String chgAfterProcess = EMPTY_VALUE;
	
	//selected values - RAE
	private List<String> selectedUOAList ;
	private String raeFac = EMPTY_VALUE;
	private String raeOutputType = EMPTY_VALUE;
	private String raeSelType = EMPTY_VALUE;
	private String raeInfoCom = EMPTY_VALUE;
	private String raeStatus = EMPTY_VALUE;
	private String raeStatusSpec = EMPTY_VALUE;
	private String raeStatusFull = EMPTY_VALUE;
	private String raeStatusOther = EMPTY_VALUE;
	private String raeStatusInel = EMPTY_VALUE;
	private String raeCitation = EMPTY_VALUE;
	private String raeCopyright = EMPTY_VALUE;
	
	private Date riDateFrom;
	private Date riDateTo;
	private Date riFirstPublishFrom;
	private Date riFirstPublishTo;
	private Date riLastPublishFrom;
	private Date riLastPublishTo;
	private List<String> selectedFundingSrcList;
	private String enhTLInHighEdu = EMPTY_VALUE;
	
	// Selected values - listing type
	private String listingType = LIST_TYPE_RI_VALUE;
	private String sortCol = SORT_COL_DEFAULT_VALUE;
	private String sortOrder = SORT_ORDER_DESC_VALUE;
	
	// userId for restricting user access to department
	private String userId;
	private List<String> deptCodeList;
	
	private String paramStaffName;
	private String paramBasic;
	private String paramOutputType;
	private String paramFundingSource;
	private String paramStartDate;
	private String paramEndDate;
	private String paramFacDept;
	
	
	private LookupValueDAO lookupDao = LookupValueDAO.getInstance();
	
	public String getParamBasic()
	{
		return paramBasic;
	}


	public void setParamBasic(String paramBasic)
	{
		this.paramBasic = paramBasic;
	}


	
	public String getParamStaffName()
	{
		return paramStaffName;
	}

	
	public void setParamStaffName(String paramStaffName)
	{
		this.paramStaffName = paramStaffName;
	}



	public String getParamFacDept()
	{
		return paramFacDept;
	}



	
	public void setParamFacDept(String paramFacDept)
	{
		this.paramFacDept = paramFacDept;
	}



	public String getParamOutputType()
	{
		return paramOutputType;
	}


	
	public void setParamOutputType(String paramOutputType)
	{
		this.paramOutputType = paramOutputType;
	}


	
	public String getParamFundingSource()
	{
		return paramFundingSource;
	}
	

	private AccessDAO getAccessDAO()
	{
		return AccessCacheDAO.getInstance();
	}
	
	public List<String> getStaffUoaList()
	{
		List<String> staffUoaList = RaeStaffDAO.getInstance().getRaeStaffUoaList();
		return staffUoaList;
	}
	
	public String getCurrentUserId()
	{
		return UserSessionView.getCurrentInstance().getUserId();
	}
	
	public List<SelectItem> getUoaList()
	{
		if(uoaList == null) {
			
			List<RaeUOA> uoaFullList  = RaeUOADAO.getInstance().getRaeUOAList();
			
			List <SecDataUser> userUoaList = getAccessDAO().getDataCodeByUserId(getCurrentUserId(), "UOA");
			
			if (! CollectionUtils.isEmpty(userUoaList)) {
				Set<String> selectedUoaSet = userUoaList.stream().map(SecDataUser -> SecDataUser.getPk().getData_code()).collect(Collectors.toSet());
				uoaFullList = uoaFullList.stream().filter(a -> selectedUoaSet.contains(a.getUoaCode())).collect(Collectors.toList());
				uoaFullList = uoaFullList.stream().filter(a -> getStaffUoaList().contains(a.getUoaCode())).collect(Collectors.toList());
			}

			
			uoaList = new ArrayList<SelectItem>();
			//uoaList.add(new SelectItem(EMPTY_VALUE, SELECT_LABEL));
			
			for (RaeUOA uoa:uoaFullList)
				uoaList.add(new SelectItem(uoa.getUoaCode(), "("+uoa.getUoaCode()+ ") "+uoa.getUoaDesc()));	
		}
		return uoaList;
	}


	
	public void setUoaList(List<SelectItem> uoaList)
	{
		this.uoaList = uoaList;
	}
	
	public List<LookupValue> getSdgList()
	{
		if(sdgList == null) {
			sdgList = lookupDao.getLookupValueList ("SDG","US","Y");
		}
		return sdgList;
	}

	
	public void setSdgList(List<LookupValue> sdgList)
	{
		this.sdgList = sdgList;
	}


	
	public List<SelectItem> getRaeFacList()
	{
		if(raeFacList == null) {
			List<String> facList = RaeStaffDAO.getInstance().getRaeStaffFacList();
			raeFacList = new ArrayList<SelectItem>();
			raeFacList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			
			for(String fac : facList) {
				raeFacList.add(new SelectItem(fac, fac));
			}
			//raeFacList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		
		
		return raeFacList;
	}


	
	public void setRaeFacList(List<SelectItem> raeFacList)
	{
		this.raeFacList = raeFacList;
	}
	
	
	
	public List<String> getSelectedUOAList()
	{
		if(selectedUOAList == null || CollectionUtils.isEmpty(selectedUOAList)) {
			
			List<RaeUOA> uoaFullList  = RaeUOADAO.getInstance().getRaeUOAList();
			
			List <SecDataUser> userUoaList = getAccessDAO().getDataCodeByUserId(getCurrentUserId(), "UOA");
			
			if (! CollectionUtils.isEmpty(userUoaList)) {
				Set<String> selectedUoaSet = userUoaList.stream().map(SecDataUser -> SecDataUser.getPk().getData_code()).collect(Collectors.toSet());
				uoaFullList = uoaFullList.stream().filter(a -> selectedUoaSet.contains(a.getUoaCode())).collect(Collectors.toList());
				uoaFullList = uoaFullList.stream().filter(a -> getStaffUoaList().contains(a.getUoaCode())).collect(Collectors.toList());
			}

			
			selectedUOAList = new ArrayList<String>();
			//uoaList.add(new SelectItem(EMPTY_VALUE, SELECT_LABEL));
			
			for (RaeUOA uoa:uoaFullList)
				selectedUOAList.add(uoa.getUoaCode());	
		}
		return selectedUOAList;
	}


	
	public void setSelectedUOAList(List<String> selectedUOAList)
	{
		this.selectedUOAList = selectedUOAList;
	}


	
	public String getRaeFac()
	{
		return raeFac;
	}


	
	public void setRaeFac(String raeFac)
	{
		this.raeFac = raeFac;
	}


	
	public String getRaeOutputType()
	{
		return raeOutputType;
	}


	
	public void setRaeOutputType(String raeOutputType)
	{
		this.raeOutputType = raeOutputType;
	}


	
	public String getRaeSelType()
	{
		return raeSelType;
	}


	
	public void setRaeSelType(String raeSelType)
	{
		this.raeSelType = raeSelType;
	}


	
	public String getRaeInfoCom()
	{
		return raeInfoCom;
	}


	
	public void setRaeInfoCom(String raeInfoCom)
	{
		this.raeInfoCom = raeInfoCom;
	}


	
	public String getRaeStatus()
	{
		return raeStatus;
	}


	
	public void setRaeStatus(String raeStatus)
	{
		this.raeStatus = raeStatus;
	}


	
	public String getRaeStatusSpec()
	{
		return raeStatusSpec;
	}


	
	public void setRaeStatusSpec(String raeStatusSpec)
	{
		this.raeStatusSpec = raeStatusSpec;
	}


	
	public String getRaeStatusFull()
	{
		return raeStatusFull;
	}


	
	public void setRaeStatusFull(String raeStatusFull)
	{
		this.raeStatusFull = raeStatusFull;
	}


	
	public String getRaeStatusOther()
	{
		return raeStatusOther;
	}


	
	public void setRaeStatusOther(String raeStatusOther)
	{
		this.raeStatusOther = raeStatusOther;
	}


	
	public String getRaeStatusInel()
	{
		return raeStatusInel;
	}


	
	public void setRaeStatusInel(String raeStatusInel)
	{
		this.raeStatusInel = raeStatusInel;
	}


	
	public String getRaeCitation()
	{
		return raeCitation;
	}


	
	public void setRaeCitation(String raeCitation)
	{
		this.raeCitation = raeCitation;
	}


	
	public String getRaeCopyright()
	{
		return raeCopyright;
	}


	
	public void setRaeCopyright(String raeCopyright)
	{
		this.raeCopyright = raeCopyright;
	}


	public List<SelectItem> getRaeOutputTypeList()
	{
		if(raeOutputTypeList == null) {
			raeOutputTypeList = new ArrayList<SelectItem>();
			raeOutputTypeList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_OUTPUT_TYPE", "US", "Y");
			for(LookupValue type : typeList) 
				raeOutputTypeList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			
			//raeOutputTypeList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		
		return raeOutputTypeList;
	}


	
	public void setRaeOutputTypeList(List<SelectItem> raeOutputTypeList)
	{
		this.raeOutputTypeList = raeOutputTypeList;
	}


	
	public List<SelectItem> getRaeSelTypeList()
	{		
		if(raeSelTypeList == null) {
			raeSelTypeList = new ArrayList<SelectItem>();
			raeSelTypeList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			raeSelTypeList.add(new SelectItem(RAE_SEL_NOSEL_TYPE_VALUE, RAE_SEL_NOSEL_TYPE_LABEL));
			raeSelTypeList.add(new SelectItem(RAE_SEL_SEL_TYPE_VALUE, RAE_SEL_SEL_TYPE_LABEL));
			raeSelTypeList.add(new SelectItem(RAE_SEL_DW1_TYPE_VALUE, RAE_SEL_DW1_TYPE_LABEL));
			raeSelTypeList.add(new SelectItem(RAE_SEL_BDW1_TYPE_VALUE, RAE_SEL_BDW1_TYPE_LABEL));
			raeSelTypeList.add(new SelectItem(RAE_SEL_DW2_TYPE_VALUE, RAE_SEL_DW2_TYPE_LABEL));
			raeSelTypeList.add(new SelectItem(RAE_SEL_BDW2_TYPE_VALUE, RAE_SEL_BDW2_TYPE_LABEL));
			//raeSelTypeList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		
		return raeSelTypeList;
	}


	
	public void setRaeSelTypeList(List<SelectItem> raeSelTypeList)
	{
		this.raeSelTypeList = raeSelTypeList;
	}


	
	public List<SelectItem> getRaeInfoComList()
	{
		if(raeInfoComList == null) {
			raeInfoComList = new ArrayList<SelectItem>();
			raeInfoComList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			raeInfoComList.add(new SelectItem(YES_VALUE, YES_LABEL));
			raeInfoComList.add(new SelectItem(NO_VALUE, NO_LABEL));
			//raeInfoComList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return raeInfoComList;
	}


	
	public void setRaeInfoComList(List<SelectItem> raeInfoComList)
	{
		this.raeInfoComList = raeInfoComList;
	}


	
	public List<SelectItem> getRaeStatusList()
	{
		
		if(raeStatusList == null) {
			raeStatusList = new ArrayList<SelectItem>();
			raeStatusList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_STATUS", "US", "Y");
			for(LookupValue type : typeList) 
				raeStatusList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			//raeStatusList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		
		return raeStatusList;
	}


	
	public void setRaeStatusList(List<SelectItem> raeStatusList)
	{
		this.raeStatusList = raeStatusList;
	}


	
	public List<SelectItem> getRaeStatusSpecList()
	{
		
		if(raeStatusSpecList == null) {
			raeStatusSpecList = new ArrayList<SelectItem>();
			raeStatusSpecList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_STATUS", "US", "Y");
			for(LookupValue type : typeList) 
				raeStatusSpecList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			//raeStatusSpecList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		
		return raeStatusSpecList;
	}


	
	public void setRaeStatusSpecList(List<SelectItem> raeStatusSpecList)
	{
		this.raeStatusSpecList = raeStatusSpecList;
	}


	
	public List<SelectItem> getRaeStatusFullList()
	{
		if(raeStatusFullList == null) {
			raeStatusFullList = new ArrayList<SelectItem>();
			raeStatusFullList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_STATUS", "US", "Y");
			for(LookupValue type : typeList) 
				raeStatusFullList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			//raeStatusFullList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return raeStatusFullList;
	}


	
	public void setRaeStatusFullList(List<SelectItem> raeStatusFullList)
	{
		this.raeStatusFullList = raeStatusFullList;
	}


	
	public List<SelectItem> getRaeStatusOtherList()
	{
		if(raeStatusOtherList == null) {
			raeStatusOtherList = new ArrayList<SelectItem>();
			raeStatusOtherList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_STATUS", "US", "Y");
			for(LookupValue type : typeList) 
				raeStatusOtherList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			//raeStatusOtherList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return raeStatusOtherList;
	}


	
	public void setRaeStatusOtherList(List<SelectItem> raeStatusOtherList)
	{
		this.raeStatusOtherList = raeStatusOtherList;
	}


	
	public List<SelectItem> getRaeStatusInetList()
	{
		if(raeStatusInetList == null) {
			raeStatusInetList = new ArrayList<SelectItem>();
			raeStatusInetList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			raeStatusInetList.add(new SelectItem(RAE_IES_VALUE, RAE_IES_LABEL));
			raeStatusInetList.add(new SelectItem(RAE_IEO_VALUE, RAE_IEO_LABEL));
			raeStatusInetList.add(new SelectItem(RAE_NIL_VALUE, RAE_NIL_LABEL));
		}
		return raeStatusInetList;
	}


	
	public void setRaeStatusInetList(List<SelectItem> raeStatusInetList)
	{
		this.raeStatusInetList = raeStatusInetList;
	}


	
	public List<SelectItem> getRaeCitationList()
	{
		if(raeCitationList == null) {
			raeCitationList = new ArrayList<SelectItem>();
			raeCitationList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_CITATION_CHK", "US", "Y");
			for(LookupValue type : typeList) 
				raeCitationList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			//raeStatusOtherList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return raeCitationList;
	}


	
	public void setRaeCitationList(List<SelectItem> raeCitationList)
	{
		this.raeCitationList = raeCitationList;
	}


	
	public List<SelectItem> getRaeCopyrightList()
	{
		if(raeCopyrightList == null) {
			raeCopyrightList = new ArrayList<SelectItem>();
			raeCopyrightList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			List<LookupValue> typeList = LookupValueDAO.getInstance().getLookupValueList("RAE_COPYRIGHT_CLR", "US", "Y");
			for(LookupValue type : typeList) 
				raeCopyrightList.add( new SelectItem(type.getPk().getLookup_code(),type.getDescription()));
			//raeStatusOtherList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return raeCopyrightList;
	}


	
	public void setRaeCopyrightList(List<SelectItem> raeCopyrightList)
	{
		this.raeCopyrightList = raeCopyrightList;
	}


	public void setParamFundingSource(String paramFundingSource)
	{
		this.paramFundingSource = paramFundingSource;
	}



	
	public String getParamStartDate()
	{
		return paramStartDate;
	}



	
	public void setParamStartDate(String paramStartDate)
	{
		this.paramStartDate = paramStartDate;
	}



	
	public String getParamEndDate()
	{
		return paramEndDate;
	}



	
	public void setParamEndDate(String paramEndDate)
	{
		this.paramEndDate = paramEndDate;
	}



	public List<String> getStaffNameList()
	{
		if(staffNameList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			List<StaffIdentity> staffList = dao.getAcadStaffList(null);
			if(staffList != null)
				staffNameList = staffList.stream().map(staff -> staff.getFullname()).collect(Collectors.toList());
		}
		return staffNameList;
	}
	

	public List<StaffRank> getRankList()
	{
		if(rankList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			rankList = new ArrayList<StaffRank>();
			List<StaffRank> staffRankList = dao.getRankList();
			List<String> EmploymentRankList = dao.getEmploymentRankList();
			for(StaffRank rank : staffRankList) {
				if(EmploymentRankList.contains(rank.getRank_code()))
					rankList.add(rank);
			}
		}
		return rankList;
	}
	
	
	public List<SelectItem> getAllYesNoEmptyList()
	{
		if(allYesNoEmptyList == null) {
			allYesNoEmptyList = new ArrayList<SelectItem>();
			allYesNoEmptyList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			allYesNoEmptyList.add(new SelectItem(YES_VALUE, YES_LABEL));
			allYesNoEmptyList.add(new SelectItem(NO_VALUE, NO_LABEL));
			allYesNoEmptyList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return allYesNoEmptyList;
	}
	
	
	public List<String> getExStaffNameList()
	{
		
		if(exStaffNameList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			List<StaffPast> staffList = dao.getPastStaffList();
			if(staffList != null)
				exStaffNameList = staffList.stream().map(staff -> staff.getFullname()).collect(Collectors.toList());
		}
		return exStaffNameList;
	}


	public List<Department> getDepartmentList()
	{
		if(departmentList == null) {
			StaffDAO dao = StaffDAO.getCacheInstance();
			List<Department> allDepartmentList = dao.getDepartmentListHavingData();
			StaffDAO staffdao = StaffDAO.getCacheInstance();
			List<String> accessRight = staffdao.getDataCodeByUserId(userId);
			if ("Y".equals(getParamBasic())) {
				accessRight = new ArrayList<>();	
				accessRight.add(SecDataUser.allData);
			}
			departmentList = new ArrayList<Department>();
			deptCodeList = new ArrayList<String>();
			if(accessRight != null) {
				if(accessRight.contains(SecDataUser.allData)) {
					departmentList = allDepartmentList;
					deptCodeList.add("xxx"+SecDataUser.allData+"xxx");
				}
				else {
					for (Department dept : allDepartmentList) {
						if(accessRight.contains(dept.getDepartmentCode())) {
							departmentList.add(dept);
							deptCodeList.add(dept.getDepartmentCode());
						}
					}
				}
			}
		}
		return departmentList;
	}

	public List<UserRole> getUserRoleList(){
		List<UserRole> userRoleList = AccessDAO.getInstance().getUserRoleListByUserId(getUserId());
		return userRoleList;
	}
	
	public Boolean getIsRdoAdmin() 
	{
		boolean isRdoAdmin = false;
		List<UserRole> userRoleList = getUserRoleList();
		List<UserRole> tmpList = userRoleList.stream()
				.filter(y -> y.getRoleId().equals("rdoAdmin"))
				.collect(Collectors.toList());
		isRdoAdmin = (!tmpList.isEmpty())?true:false;

		return isRdoAdmin;
	}
	
	public Boolean getIsLibAdmin()
	{
		boolean isLibAdmin = false;
		List<UserRole> userRoleList = getUserRoleList();
		List<UserRole> tmpList = userRoleList.stream()
				.filter(y -> y.getRoleId().equals("libAdmin"))
				.collect(Collectors.toList());
		isLibAdmin = (!tmpList.isEmpty())?true:false;
		
		return isLibAdmin;
	}
	
	public Boolean getIsRaeAdmin()
	{
		boolean isRaeAdmin = false;
		List<UserRole> userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("raeAdmin"))
					.collect(Collectors.toList());
			isRaeAdmin = (!tmpList.isEmpty())?true:false;
		return isRaeAdmin;
	}
	
	public Boolean getIsDeptAdmin()
	{
		boolean isRaeAdmin = false;
		List<UserRole> userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("deptAdmin"))
					.collect(Collectors.toList());
			isRaeAdmin = (!tmpList.isEmpty())?true:false;
		return isRaeAdmin;
	}
	
	public Boolean getIsUoaAdmin()
	{
		boolean isUoaAdmin = false;
		List<UserRole> userRoleList = getUserRoleList();
			List<UserRole> tmpList = userRoleList.stream()
					.filter(y -> y.getRoleId().equals("uoaAdmin"))
					.collect(Collectors.toList());
			isUoaAdmin = (!tmpList.isEmpty())?true:false;
		return isUoaAdmin;
	}
	
	public List<String> getRiTypeList()
	{
		if(riTypeList == null) {
			riTypeList = new ArrayList<String>();
			if (getIsRdoAdmin() || getIsLibAdmin() || getIsDeptAdmin()){
				riTypeList.add(RI_TYPE_OUTPUT);
				riTypeList.add(RI_TYPE_PROJECT);
				riTypeList.add(RI_TYPE_AWARD);
				riTypeList.add(RI_TYPE_PATENT);
			}
			if (getIsRaeAdmin() || getIsLibAdmin() || getIsUoaAdmin()){
				riTypeList.add(RI_TYPE_RAE);
			}
		}
		return riTypeList;
	}

	
	public List<SelectItem> getOutputTypeList()
	{
		if(outputTypeList == null) {
			PublicationDAO dao = PublicationDAO.getCacheInstance();
			List<OutputType> level1OutputType = dao.getOutputTypeList(1);
			List<OutputType> level2OutputType = dao.getOutputTypeList(2);
			outputTypeList = new ArrayList<>();
			for (OutputType outputType1 : level1OutputType) {
				SelectItemGroup group = new SelectItemGroup(outputType1.getDescription());
				List<SelectItem> itemList = new ArrayList<SelectItem>();
				for(OutputType outputType2 : level2OutputType) {
					if(outputType2.getParent_lookup_code().equals(outputType1.getPk().getLookup_code())) {
						itemList.add(new SelectItem(outputType2.getPk().getLookup_code(), outputType2.getDescription()));
					}
				}
				SelectItem[] itemArr = itemList.toArray(new SelectItem[0]);
				group.setSelectItems(itemArr);
				outputTypeList.add(group);
			}
		}
		return outputTypeList;
	}

	
	public List<SelectItem> getViewTypeList()
	{
		if(viewTypeList == null) {
			viewTypeList = new ArrayList<SelectItem>();
			viewTypeList.add(new SelectItem(RICH_EDITION_VALUE, RICH_EDITION_LABEL));
			viewTypeList.add(new SelectItem(CDCF_SNAPSHOT_VALUE, CDCF_SNAPSHOT_LABEL));
		}
		return viewTypeList;
	}

	
	public List<SelectItem> getCdcfStatusList()
	{
		if(cdcfStatusList == null) {
			cdcfStatusList = new ArrayList<SelectItem>();
			cdcfStatusList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			cdcfStatusList.add(new SelectItem(CDCF_PENDING_VALUE, CDCF_PENDING_LABEL));
			cdcfStatusList.add(new SelectItem(CDCF_PROCESSED_VALUE, CDCF_PROCESSED_LABEL));
			cdcfStatusList.add(new SelectItem(CDCF_GENERATED_VALUE, CDCF_GENERATED_LABEL));
			cdcfStatusList.add(new SelectItem(CDCF_NOT_SELECTED_VALUE, CDCF_NOT_SELECTED_LABEL));
			cdcfStatusList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return cdcfStatusList;
	}
	
	public List<SelectItem> getConsentIndicatorList()
	{
		if(consentIndicatorList == null) {
			consentIndicatorList = new ArrayList<SelectItem>();
			consentIndicatorList.add(new SelectItem(SELECT_ALL_VALUE, SELECT_ALL_LABEL));
			consentIndicatorList.add(new SelectItem(YES_VALUE, YES_LABEL));
			consentIndicatorList.add(new SelectItem(NO_VALUE, NO_LABEL));
			consentIndicatorList.add(new SelectItem(UNCONFIRM_VALUE, UNCONFIRM_LABEL));
			consentIndicatorList.add(new SelectItem(EMPTY_VALUE, EMPTY_LABEL));
		}
		return consentIndicatorList;
	}

	
	public List<SelectItem> getFundingSrcList()
	{
		if(fundingSrcList == null) {
			PublicationDAO dao = PublicationDAO.getCacheInstance();
			List<FundingSource> level1FundingSrc = dao.getFundingSrcList(1);
			fundingSrcList = new ArrayList<>();
			for(FundingSource fundingSrc1 : level1FundingSrc) {
				fundingSrcList.add(new SelectItem(fundingSrc1.getPk().getLookup_code(), fundingSrc1.getDescription()));
			}
		}
		return fundingSrcList;
	}

	
	public List<SelectItem> getListingTypeList()
	{
		if(listingTypeList == null) {
			listingTypeList = new ArrayList<SelectItem>();
			listingTypeList.add(new SelectItem(LIST_TYPE_RI_VALUE, LIST_TYPE_RI_LABEL));
			listingTypeList.add(new SelectItem(LIST_TYPE_STAFF_VALUE, LIST_TYPE_STAFF_LABEL));
		}
		return listingTypeList;
	}


	
	public List<SelectItem> getSortColList()
	{
		if(sortColList == null) {
			sortColList = new ArrayList<SelectItem>();
			sortColList.add(new SelectItem(SORT_COL_DEFAULT_VALUE, SORT_COL_DEFAULT_LABEL));
			sortColList.add(new SelectItem(SORT_COL_RI_NO_VALUE, SORT_COL_RI_NO_LABEL));
			sortColList.add(new SelectItem(SORT_COL_RI_NAME_VALUE, SORT_COL_RI_NAME_LABEL));
			sortColList.add(new SelectItem(SORT_COL_CONTRIBUTOR_VALUE, SORT_COL_CONTRIBUTOR_LABEL));
			sortColList.add(new SelectItem(SORT_COL_FROM_DATE_VALUE, SORT_COL_FROM_DATE_LABEL));
			sortColList.add(new SelectItem(SORT_COL_TO_DATE_VALUE, SORT_COL_TO_DATE_LABEL));
			//sortColList.add(new SelectItem(SORT_COL_CDCF_STAT_VALUE, SORT_COL_CDCF_STAT_LABEL));
		}
		return sortColList;
	}


	
	public List<SelectItem> getSortOrderList()
	{
		if(sortOrderList == null) {
			sortOrderList = new ArrayList<SelectItem>();
			sortOrderList.add(new SelectItem(SORT_ORDER_DESC_VALUE, SORT_ORDER_DESC_LABEL));
			sortOrderList.add(new SelectItem(SORT_ORDER_ASC_VALUE, SORT_ORDER_ASC_LABEL));
		}
		return sortOrderList;
	}


	public String getStaffName()
	{
		if (!Strings.isNullOrEmpty(getParamStaffName()) && "Y".equals(getParamBasic())) {
			staffName = paramStaffName; 
		}
		return staffName != null ? staffName.trim() : null;
	}

	
	public void setStaffName(String staffName)
	{
		this.staffName = staffName;
	}
	
	
	public List<String> getSelectedRankList()
	{
		return selectedRankList;
	}
	
	
	public void setSelectedRankList(List<String> selectedRankList)
	{
		this.selectedRankList = selectedRankList;
	}
	
	
	public String getAcadStaff()
	{
		return acadStaff;
	}

	
	public void setAcadStaff(String acadStaff)
	{
		this.acadStaff = acadStaff;
	}

	
	public List<String> getSelectedDepartmentList()
	{
		if (!Strings.isNullOrEmpty(getParamFacDept())) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramFacDept.split(",")));
			selectedDepartmentList = tmp;
		}
		return selectedDepartmentList;
	}

	
	public void setSelectedDepartmentList(List<String> selectedDepartmentList)
	{
		this.selectedDepartmentList = selectedDepartmentList;
	}

	
	public String getExStaffName()
	{
		return exStaffName != null ? exStaffName.trim() : null;
	}

	
	public void setExStaffName(String exStaffName)
	{
		this.exStaffName = exStaffName;
	}
	
	
	public String getFormStaffNum()
	{
		return formStaffNum != null ? formStaffNum.trim() : null;
	}
	

	
	public void setFormStaffNum(String formStaffNum)
	{
		this.formStaffNum = formStaffNum;
	}


	
	public String getRiType() {
		if (riType == null) {
			if (getRiTypeList() != null) {
				if (getRiTypeList().size() > 0) {
					riType = getRiTypeList().get(0);
				}
			}
		}
	    return riType;
	}


	
	public void setRiType(String riType)
	{
		this.riType = riType;
	}


	
	public String getRiNo()
	{
		return riNo != null ? riNo.trim() : null;
	}


	
	public void setRiNo(String riNo)
	{
		this.riNo = riNo;
	}

	
	public List<String> getSelectedSDGList()
	{
		return selectedSDGList;
	}


	
	public void setSelectedSDGList(List<String> selectedSDGList)
	
	{
		this.selectedSDGList = selectedSDGList;
	}


	public List<String> getSelectedOutputTypeList()
	{
		if (!Strings.isNullOrEmpty(getParamOutputType())) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramOutputType.split(",")));
			selectedOutputTypeList = tmp;
		}
		return selectedOutputTypeList;
	}


	
	public void setSelectedOutputTypeList(List<String> selectedOutputTypeList)
	{
		this.selectedOutputTypeList = selectedOutputTypeList;
	}
	
	
	public String getViewType()
	{
		return viewType;
	}


	
	public void setViewType(String viewType)
	{
		this.viewType = viewType;
	}

	
	public String getKeyword()
	{
		return keyword;
	}


	
	public void setKeyword(String keyword)
	{
		this.keyword = keyword;
	}

	
	
	public String getSmartSearch()
	{
		return smartSearch;
	}


	
	public void setSmartSearch(String smartSearch)
	{
		this.smartSearch = smartSearch;
	}


	public String getJournalName()
	{
		return journalName != null ? journalName.trim() : null;
	}
	
	
	public String getCdcfStatus()
	{
		return cdcfStatus;
	}
	
	
	public String getInsDecIndicator()
	{
		return insDecIndicator;
	}


	
	public void setInsDecIndicator(String insDecIndicator)
	{
		this.insDecIndicator = insDecIndicator;
	}


	
	public String getConsentIndicator()
	{
		return consentIndicator;
	}


	
	public void setConsentIndicator(String consentIndicator)
	{
		this.consentIndicator = consentIndicator;
	}


	public void setCdcfStatus(String cdcfStatus)
	{
		this.cdcfStatus = cdcfStatus;
	}


	
	public String getChgAfterProcess()
	{
		return chgAfterProcess;
	}


	
	public void setChgAfterProcess(String chgAfterProcess)
	{
		this.chgAfterProcess = chgAfterProcess;
	}


	public void setJournalName(String journalName)
	{
		this.journalName = journalName;
	}

	
	public Date getRiDateFrom() throws ParseException
	{
		if (!Strings.isNullOrEmpty(getParamStartDate())) {
			riDateFrom = new SimpleDateFormat("dd/MM/yyyy").parse(paramStartDate); 
		}
		return riDateFrom;
	}


	
	public void setRiDateFrom(Date riDateFrom)
	{
		this.riDateFrom = riDateFrom;
	}


	
	public Date getRiDateTo() throws ParseException
	{
		if (!Strings.isNullOrEmpty(getParamEndDate())) {
			riDateTo = new SimpleDateFormat("dd/MM/yyyy").parse(paramEndDate); 
		}
		return riDateTo;
	}
	
	
	public Date getRiFirstPublishFrom()
	{
		return riFirstPublishFrom;
	}


	
	public void setRiFirstPublishFrom(Date riFirstPublishFrom)
	{
		this.riFirstPublishFrom = riFirstPublishFrom;
	}


	
	public Date getRiFirstPublishTo()
	{
		return riFirstPublishTo;
	}


	
	public void setRiFirstPublishTo(Date riFirstPublishTo)
	{
		this.riFirstPublishTo = riFirstPublishTo;
	}


	
	public Date getRiLastPublishFrom()
	{
		return riLastPublishFrom;
	}


	
	public void setRiLastPublishFrom(Date riLastPublishFrom)
	{
		this.riLastPublishFrom = riLastPublishFrom;
	}


	
	public Date getRiLastPublishTo()
	{
		return riLastPublishTo;
	}


	
	public void setRiLastPublishTo(Date riLastPublishTo)
	{
		this.riLastPublishTo = riLastPublishTo;
	}


	public void setRiDateTo(Date riDateTo)
	{
		this.riDateTo = riDateTo;
	}

	
	
	public List<String> getSelectedFundingSrcList()
	{
		if (!Strings.isNullOrEmpty(getParamFundingSource())) {
			List<String> tmp = new ArrayList<String>(Arrays.asList(paramFundingSource.split(",")));
			selectedFundingSrcList = tmp;
		}
		return selectedFundingSrcList;
	}


	
	public void setSelectedFundingSrcList(List<String> selectedFundingSrcList)
	{
		this.selectedFundingSrcList = selectedFundingSrcList;
	}


	public String getEnhTLInHighEdu()
	{
		return enhTLInHighEdu;
	}


	
	public void setEnhTLInHighEdu(String enhTLInHighEdu)
	{
		this.enhTLInHighEdu = enhTLInHighEdu;
	}


	
	public String getListingType()
	{
		return listingType;
	}


	
	public void setListingType(String listingType)
	{
		this.listingType = listingType;
	}


	
	public String getSortCol()
	{
		return sortCol;
	}


	
	public void setSortCol(String sortCol)
	{
		this.sortCol = sortCol;
	}


	
	public String getSortOrder()
	{
		return sortOrder;
	}


	
	public void setSortOrder(String sortOrder)
	{
		this.sortOrder = sortOrder;
	}


	public static String getRiTypeOutput()
	{
		return RI_TYPE_OUTPUT;
	}


	
	public static String getRiTypeProject()
	{
		return RI_TYPE_PROJECT;
	}


	
	public static String getRiTypeAward()
	{
		return RI_TYPE_AWARD;
	}


	
	public static String getRiTypePatent()
	{
		return RI_TYPE_PATENT;
	}

	
	
	public static String getRiTypeRae()
	{
		return RI_TYPE_RAE;
	}


	public static String getRichEditionValue()
	{
		return RICH_EDITION_VALUE;
	}
	


	
	public static String getListTypeRiValue()
	{
		return LIST_TYPE_RI_VALUE;
	}


	
	public static String getListTypeStaffValue()
	{
		return LIST_TYPE_STAFF_VALUE;
	}


	
	public String getUserId()
	{
		return userId;
	}


	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}


	
	public List<String> getDeptCodeList()
	{
		if(deptCodeList == null) getDepartmentList();
		return deptCodeList;
	}


	public List<String> nameAutoComplete(String query) {
		String queryLowerCase = query.toLowerCase();
        return getStaffNameList().stream().filter(t -> t.toLowerCase().contains(queryLowerCase)).collect(Collectors.toList());
	}
	
	public List<String> exNameAutoComplete(String query) {
		String queryLowerCase = query.toLowerCase();
        return getExStaffNameList().stream().filter(t -> t.toLowerCase().contains(queryLowerCase)).collect(Collectors.toList());
	}

	
	public RIQueryChain getQueryChain() throws ParseException
	{
		RIQueryChain chain = new RIQueryChain(getRiType());
		
		if (!getRiType().equals(RI_TYPE_RAE)) {
			// Restricted user access of department if not searching former staff
			if(StringUtils.isBlank(getExStaffName()) && StringUtils.isBlank(getFormStaffNum()) 
					&& !getDeptCodeList().contains("xxx"+SecDataUser.allData+"xxx")) {
				if(!CollectionUtils.isEmpty(getDeptCodeList())) 
					chain = chain.department(getDeptCodeList());
				else
					chain = chain.end();
			}
		}
		
	
		
		// Selected values - Person	
		if (StringUtils.isNotBlank(getStaffName())) chain = chain.staffName(getStaffName());
		if (!CollectionUtils.isEmpty(getSelectedRankList())) {
			chain = chain.staffRank(getSelectedRankList());
		}
		if (StringUtils.isNotBlank(getAcadStaff()) && !getAcadStaff().equals(EMPTY_VALUE)) chain = chain.isAcadStaff(getAcadStaff());
		// department selection unvalid when searching former staff
		if(StringUtils.isBlank(getExStaffName()) && StringUtils.isBlank(getFormStaffNum())) {
			if (!CollectionUtils.isEmpty(getSelectedDepartmentList())) {
				chain = chain.department(getSelectedDepartmentList());
			}
		}
		
		// Selected values - sdg list
		
		if (!CollectionUtils.isEmpty(getSelectedSDGList())) chain = chain.sdgList(String.join(",", getSelectedSDGList()));
	
		
		// Selected values - ex-IEd staff	
		if (StringUtils.isNotBlank(getExStaffName())) chain = chain.exStaffName(getExStaffName());
		if (StringUtils.isNotBlank(getFormStaffNum())) chain = chain.formStaffNum(getFormStaffNum());
		
		// Selected values - ri
		if (StringUtils.isNotBlank(getRiNo())) chain = chain.riNo(getRiNo());
		if (!CollectionUtils.isEmpty(getSelectedOutputTypeList())) {
			chain = chain.outputType(getSelectedOutputTypeList().toArray(new String[0]));
		}
		
		if (!getRiType().equals(RI_TYPE_RAE)) {
			if (StringUtils.isNotBlank(getViewType())) chain = chain.dataLevel(getViewType());
			if (StringUtils.isNotBlank(getKeyword())) chain = chain.keyword(getKeyword());
			if (StringUtils.isNotBlank(getSmartSearch())) chain = chain.smartSearch(getSmartSearch());
			if (StringUtils.isNotBlank(getJournalName())) chain = chain.journalName(getJournalName());
			if (StringUtils.isNotBlank(getCdcfStatus())) chain = chain.cdcfStatus(getCdcfStatus());
			if (StringUtils.isNotBlank(getInsDecIndicator()) && !getInsDecIndicator().equals(EMPTY_VALUE)) chain = chain.insDecIndicator(getInsDecIndicator());
			if (StringUtils.isNotBlank(getConsentIndicator()) && !getConsentIndicator().equals(EMPTY_VALUE)) chain = chain.consentIndicator(getConsentIndicator());
			if (StringUtils.isNotBlank(getChgAfterProcess())) chain = chain.chgAfterProcess(getChgAfterProcess());
		}
		
		
		//RAE Search
		if (getRiType().equals(RI_TYPE_RAE)) {
			if (!CollectionUtils.isEmpty(getSelectedUOAList())) chain = chain.uoaList(getSelectedUOAList());
			if (StringUtils.isNotBlank(getRaeFac())) chain = chain.singleFac(getRaeFac());
			if (StringUtils.isNotBlank(getRaeOutputType())) chain = chain.researchType(getRaeOutputType());
			if (StringUtils.isNotBlank(getRaeSelType())) chain = chain.selectType(getRaeSelType());
			if (StringUtils.isNotBlank(getRaeInfoCom())) chain = chain.infoCompleted(getRaeInfoCom());
			if (StringUtils.isNotBlank(getRaeStatus())) chain = chain.outputStatus(getRaeStatus());
			if (StringUtils.isNotBlank(getRaeStatusSpec())) chain = chain.panelStatus(getRaeStatusSpec());
			if (StringUtils.isNotBlank(getRaeStatusFull())) chain = chain.fullStatus(getRaeStatusFull());
			if (StringUtils.isNotBlank(getRaeStatusOther())) chain = chain.otherStatus(getRaeStatusOther());
			if (StringUtils.isNotBlank(getRaeStatusInel())) chain = chain.ineligibleStatus(getRaeStatusInel());
			if (StringUtils.isNotBlank(getRaeCitation())) chain = chain.citationChk(getRaeCitation());
			if (StringUtils.isNotBlank(getRaeCopyright())) chain = chain.copyrightClear(getRaeCopyright());
		}
		if (getRiDateFrom() != null || getRiDateTo() != null) chain = chain.riDate(getRiDateFrom(), getRiDateTo(),getViewType());
		if (getViewType().equals(RICH_EDITION_VALUE) && (getRiFirstPublishFrom() != null || getRiFirstPublishTo() != null)) 
			chain = chain.riFirstPublish(getRiFirstPublishFrom(), getRiFirstPublishTo());
		if (getRiLastPublishFrom() != null || getRiLastPublishTo() != null) chain = chain.riLastPublish(getRiLastPublishFrom(), getRiLastPublishTo());
		if (!CollectionUtils.isEmpty(getSelectedFundingSrcList())) {
			chain = chain.fundingSrc(getSelectedFundingSrcList().toArray(new String[0]));
		}
		if (StringUtils.isNotBlank(getEnhTLInHighEdu()) && !getEnhTLInHighEdu().equals(EMPTY_VALUE)) chain = chain.enhTLInHighEdu(getEnhTLInHighEdu());

		
		return chain;
		
	}
	
	public StaffQueryChain getStaffQueryChain() {
		StaffQueryChain chain = new StaffQueryChain();
		
		// Restricted user access of department
		if(!getDeptCodeList().contains("xxx"+SecDataUser.allData+"xxx")) {
			if(!CollectionUtils.isEmpty(getDeptCodeList())) 
				chain = chain.department(getDeptCodeList());
			else
				chain = chain.end();
		}
		
		// Selected values - Person	
		if (StringUtils.isNotBlank(getStaffName())) chain = chain.staffName(getStaffName());
		if (!CollectionUtils.isEmpty(getSelectedRankList())) {
			chain = chain.staffRank(getSelectedRankList());
		}
		if (StringUtils.isNotBlank(getAcadStaff()) && !getAcadStaff().equals(EMPTY_VALUE)) chain = chain.isAcadStaff(getAcadStaff());
		if (!CollectionUtils.isEmpty(getSelectedDepartmentList())) {
			chain = chain.department(getSelectedDepartmentList());
		}
		return chain;
	}
	
	public ExStaffQueryChain getExStaffQueryChain() {
		ExStaffQueryChain chain = new ExStaffQueryChain();
		
		// Selected values - ex-IEd staff	
		if (StringUtils.isNotBlank(getExStaffName())) chain = chain.exStaffName(getExStaffName());
		if (StringUtils.isNotBlank(getFormStaffNum())) chain = chain.formStaffNum(getFormStaffNum());
				
		return chain;
	}
	
}
