package hk.eduhk.rich.view;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Logger;

import hk.eduhk.rich.entity.importRI.*;


@SuppressWarnings("serial")
public class ImportRIOutput implements Serializable
{	
	private List<ImportRIOutputV> outputList;
	private Map<ImportRIOutputV_PK, ImportRIOutputV> outputMap;
	private ImportRIOutputV_PK selectedPreview;
	private String paramPid;
	
	
	private ImportRIStatus_PK selectedIgnoreOutput;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	public List<ImportRIOutputV> getOutputList() {
		
		if(outputList == null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			outputList = dao.getNewImportRIOutputV(getParamPid());
			outputMap = new HashMap<ImportRIOutputV_PK, ImportRIOutputV>();
			if(outputList == null) {
				outputList = new ArrayList<ImportRIOutputV>();
			}
			else {
				for(ImportRIOutputV v : outputList) {
					outputMap.put(v.getPk(), v);
				}
			}
				
		}
		
		return outputList;
	}
	
	
	public void setOutputList(List<ImportRIOutputV> outputList)
	{
		this.outputList = outputList;
	}



	public void setSelectedPreview(String area_code, String source_id, String staff_number) {
		selectedPreview = new ImportRIOutputV_PK();
		selectedPreview.setArea_code(area_code);
		selectedPreview.setSource_id(source_id);
		selectedPreview.setStaff_number(staff_number);
	}
	
	
	public ImportRIOutputV_PK getSelectedPreview()
	{
		return selectedPreview;
	}

	public Map<ImportRIOutputV_PK, ImportRIOutputV> getOutputMap()
	{
		return outputMap;
	}
	
	
	public ImportRIOutputV getOutput() {
		if(getOutputMap() != null)
			return getOutputMap().get(getSelectedPreview());
		else
			return new ImportRIOutputV();
	}
	
	public void setSelectedIgnoreOutput(String area_code, String source_id, String staff_number) {
		selectedIgnoreOutput = new ImportRIStatus_PK();
		selectedIgnoreOutput.setArea_code(area_code);
		selectedIgnoreOutput.setSource_id(source_id);
		selectedIgnoreOutput.setStaff_number(staff_number);
	}
	
	public void setSelectedIgnoreOutput() {
		selectedIgnoreOutput = new ImportRIStatus_PK();
		selectedIgnoreOutput.setArea_code(selectedPreview.getArea_code());
		selectedIgnoreOutput.setSource_id(selectedPreview.getSource_id());
		selectedIgnoreOutput.setStaff_number(selectedPreview.getStaff_number());
	}
	
	
	public ImportRIStatus_PK getSelectedIgnoreOutput()
	{
		return selectedIgnoreOutput;
	}


	
	public String getParamPid()
	{
		return paramPid;
	}


	
	public void setParamPid(String paramPid)
	{
		this.paramPid = paramPid;
	}
		
	
}
