package hk.eduhk.rich.view;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.model.file.UploadedFile;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.access.AccessDAO;
import hk.eduhk.rich.data.DataMatrix;
import hk.eduhk.rich.data.DataMatrixExtractor;
import hk.eduhk.rich.entity.importRI.*;
import hk.eduhk.rich.entity.staff.StaffDAO;


@SuppressWarnings("serial")
@ManagedBean(name = "uploadRIView")
@ViewScoped
public class UploadRIView extends BaseView
{	
	private String uploadSrc;
	private List<SelectItem> uploadSrcList;
	private String batchKey;
	private String remarks;
	private List<ImportRIBatch> batchList;
	private transient UploadedFile uploadedFile = null;
	
	Logger logger = Logger.getLogger(this.getClass().getName());

	
	
	
	public String getUploadSrc()
	{
		if(uploadSrc == null && !CollectionUtils.isEmpty(getUploadSrcList())) {
			uploadSrc = getUploadSrcList().get(0).getValue().toString();
		}
		return uploadSrc;
	}

	
	public void setUploadSrc(String uploadSrc)
	{
		this.uploadSrc = uploadSrc;
		batchList = null;
	}

	
	public List<SelectItem> getUploadSrcList()
	{
		if(uploadSrcList == null) {
			ImportRIDAO dao = ImportRIDAO.getCacheInstance();
			List<ImportRICA> caList = null;
			if(isSysAdmin())
				caList = dao.getImportRICAList(null);
			else {
				StaffDAO staffDao = StaffDAO.getCacheInstance();
				List<String> accessRight = staffDao.getDataCodeByUserId(getLoginUserId(), "CA");
				if(accessRight != null)
					caList = dao.getImportRICAList(accessRight);
			}
			uploadSrcList = new ArrayList<SelectItem>();
			if(!CollectionUtils.isEmpty(caList)) {
				for(ImportRICA ca :caList) {
					uploadSrcList.add(new SelectItem(ca.getArea_code(), ca.getDescription()));
				}
			}
		}
		return uploadSrcList;
	}

	
	public String getBatchKey()
	{
		return batchKey;
	}


	
	public void setBatchKey(String batchKey)
	{
		this.batchKey = batchKey;
	}


	
	public String getRemarks()
	{
		return remarks;
	}


	
	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}


	
	public List<ImportRIBatch> getBatchList()
	{
		if(batchList == null) {
			batchList = new ArrayList<ImportRIBatch>();
			if(getUploadSrc() != null) {
				ImportRIDAO dao = ImportRIDAO.getCacheInstance();
				batchList = dao.getImportRIBatchList(getUploadSrc());
			}
		}
		return batchList;
	}


	
	public void setBatchList(List<ImportRIBatch> batchList)
	{
		this.batchList = batchList;
	}

	public UploadedFile getUploadedFile()
	{
		return uploadedFile;
	}
	
	
	public void setUploadedFile(UploadedFile uploadedFile)
	{
		this.uploadedFile = uploadedFile;
	}
	
	public String upload() {
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ImportRIDAO dao = ImportRIDAO.getCacheInstance();
		
		String destUrl = redirect("uploadRI") + "&uploadSrc=" + getUploadSrc();
		String filename = null;
		
		// Must have Upload Source
		if(StringUtils.isBlank(getUploadSrc()))
		{
			String message = "Upload Source cannot be null";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}
		
		// Must have Batch Key and unique
		
		if(StringUtils.isBlank(batchKey))
		{
			String message = "Batch Key cannot be null";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}else {
			if(!CollectionUtils.isEmpty(getBatchList())) {
				for(ImportRIBatch batch : getBatchList()) {
					if(batch.getBatch_key().equals(batchKey)) {
						String message = "Batch Key must be unique";
						fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
						fCtx.getExternalContext().getFlash().setKeepMessages(true);
						return destUrl;
					}
				}
			}
		}
		
		if (getUploadedFile() != null && 
				getUploadedFile().getContent() != null &&
				getUploadedFile().getContent().length > 0)
		{
			
			// Validate the file type
			filename = getUploadedFile().getFileName();
	        String extension = FilenameUtils.getExtension(filename);
	        
			if (!"xlsx".equalsIgnoreCase(extension) && !"xls".equalsIgnoreCase(extension))
			{
				String message = "Invalid file. The file must be in Excel format (xlsx or xls).";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
			// Get the Sheet
			DataMatrixExtractor extractor = new DataMatrixExtractor(uploadedFile);
			DataMatrix dataMatrix = null;
			try
			{
				dataMatrix = extractor.getDataMatrix();
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e.getCause());
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
			// Validate the header
			if (dataMatrix != null)
			{
				// Get uploaded File headers
				List<String> colHeaderList = null;
				List<String> srcHeaderList = getUploadSrcHeaders(getUploadSrc());
				int rowNum = dataMatrix.getLastRowNum();
	
	    		// Create the mapping table between header name and column number
	    		// using the 1st row
	    		if (rowNum > 0)
	    		{
	    			colHeaderList = new ArrayList<String>();
	    			for(String header : dataMatrix.getRowValueList(0)) {
		    			if(!StringUtils.isBlank(header)) colHeaderList.add(header);
		    		}
	    		}
	    		
	    		
	    		boolean headerError = false;
	    		if(colHeaderList != null && colHeaderList.size() != 0)
	    		{
	    			if(colHeaderList.size() != srcHeaderList.size()) 
	    				headerError = true;
	    			for (int colIdx=0;colIdx<colHeaderList.size();colIdx++)
	    			{
	    				if(colHeaderList.get(colIdx) == null || !colHeaderList.get(colIdx).equals(srcHeaderList.get(colIdx)))
	    				{
	    					headerError = true;
	    					break;
	    				}
	    			}
	    		} else headerError = true;
	    		
	    		if(headerError)
	    		{
					String message = "The headers of the excel file do not match.";
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
					return destUrl;
	    		}
	    		
	    		// Create batch
	    		ImportRIBatch batch = new ImportRIBatch();
	    		batch.setArea_code(getUploadSrc());
	    		batch.setBatch_key(batchKey);
	    		batch.setFile_name(filename);
	    		batch.setRemarks(remarks);
	    		batch.setFile_size((int) getUploadedFile().getSize());
	    		batch.setCreator(getLoginUserId());
	    		batch.setUserstamp(getLoginUserId());
	    		batch = dao.updateBatch(batch);
	    		Integer dataRowNum = 0;
	    		
	    		
	    		// Starts from the 2nd row, as the 1st row is header row
	    		for (int rowIdx=1;rowIdx<=rowNum;rowIdx++)
	    		{
	    			if (dataMatrix.isEmptyRow(rowIdx)) continue;
	    			dataRowNum++;
	    			ImportRIStore store = new ImportRIStore();
	    			ImportRIStore_PK pk = new ImportRIStore_PK();
	    			store.setRow_num(rowIdx);
	    			pk.setArea_code(getUploadSrc());
	    			pk.setBatch_id(batch.getBatch_id());
	    			store.setPk(pk);
	    			store.setCreator(getLoginUserId());
	    			store.setUserstamp(getLoginUserId());
	    			for (int colIdx=0;colIdx<colHeaderList.size();colIdx++)
	    			{
	    				String strCellValue = dataMatrix.getValue(rowIdx, colIdx);
	    				if(getUploadSrc().equals("OUTPUT_PURE")) {
		    				switch(colIdx+1) {
		    					case 1 :
		    						pk.setSource_id(strCellValue);
		    						break;
		    					case 2 :
		    						store.setContributor_id(strCellValue);
		    						break;
		    					case 3 :
		    						store.setCol_1(strCellValue);
		    						break;
		    					case 4 :
		    						store.setCol_2(strCellValue);
		    						break;
		    					case 5:
		    						store.setCol_3(strCellValue);
		    						break;
		    					case 6 :
		    						store.setCol_4(strCellValue);
		    						break;
		    					case 7 :
		    						store.setCol_5(strCellValue);
		    						break;
		    					case 8 :
		    						store.setCol_6(strCellValue);
		    						break;
		    					case 9 :
		    						store.setCol_7(strCellValue);
		    						break;
		    					case 10 :
		    						store.setCol_8(strCellValue);
		    						break;
		    					case 11 :
		    						store.setCol_9(strCellValue);
		    						break;
		    					case 12 :
		    						store.setCol_10(strCellValue);
		    						break;
		    					case 13 :
		    						store.setCol_11(strCellValue);
		    						break;
		    					case 14 :
		    						store.setCol_12(strCellValue);
		    						break;
		    					case 15 :
		    						store.setCol_13(strCellValue);
		    						break;
		    					case 16 :
		    						store.setCol_14(strCellValue);
		    						break;
		    					case 17 :
		    						store.setCol_15(strCellValue);
		    						break;
		    					case 18 :
		    						store.setCol_16(strCellValue);
		    						break;
		    					case 19 :
		    						store.setCol_17(strCellValue);
		    						break;
		    					case 20 :
		    						store.setCol_18(strCellValue);
		    						break;
		    					case 21 :
		    						store.setCol_19(strCellValue);
		    						break;
		    					case 22 :
		    						store.setCol_20(strCellValue);
		    						break;
		    					case 23 :
		    						store.setCol_21(strCellValue);
		    						break;
		    				}
	    				}
	    				else if(getUploadSrc().equals("PROJECT_HREC")) {
	    					switch(colIdx+1) {
		    					case 1 :
		    						pk.setSource_id(strCellValue);
		    						break;
		    					case 2 :
		    						store.setCol_1(strCellValue);
		    						break;
		    					case 3 :
		    						store.setCol_2(strCellValue);
		    						break;
		    					case 4:
		    						store.setCol_3(strCellValue);
		    						break;
		    					case 5 :
		    						store.setCol_4(strCellValue);
		    						break;
		    					case 6 :
		    						store.setCol_5(strCellValue);
		    						break;
		    					case 7 :
		    						store.setCol_6(strCellValue);
		    						break;
		    					case 8 :
		    						store.setCol_7(strCellValue);
		    						break;
		    					case 9 :
		    						store.setCol_91(strCellValue);
		    						break;
		    					case 10 :
		    						store.setCol_8(strCellValue);
		    						break;
		    					case 11 :
		    						store.setCol_9(strCellValue);
		    						break;
		    				}
	    				}
	    				else if(getUploadSrc().equals("PATENT_KT")) {
	    					switch(colIdx+1) {
		    					case 1 :
		    						pk.setSource_id(strCellValue);
		    						break;
		    					case 2 :
		    						store.setCol_1(strCellValue);
		    						break;
		    					case 3 :
		    						store.setCol_2(strCellValue);
		    						break;
		    					case 4:
		    						store.setCol_3(strCellValue);
		    						break;
		    					case 5 :
		    						store.setCol_4(strCellValue);
		    						break;
		    					case 6 :
		    						store.setCol_5(strCellValue);
		    						break;
		    					case 7 :
		    						store.setCol_6(strCellValue);
		    						break;
		    					case 8 :
		    						store.setCol_7(strCellValue);
		    						break;
		    					case 9 :
		    						store.setCol_8(strCellValue);
		    						break;
		    					case 10 :
		    						store.setCol_9(strCellValue);
		    						break;
		    					case 11 :
		    						store.setCol_10(strCellValue);
		    						break;
		    					case 12 :
		    						store.setCol_11(strCellValue);
		    						break;
		    					case 13 :
		    						store.setCol_12(strCellValue);
		    						break;
		    					case 14 :
		    						store.setCol_13(strCellValue);
		    						break;
		    					case 15 :
		    						store.setCol_14(strCellValue);
		    						break;
		    					case 16 :
		    						store.setCol_15(strCellValue);
		    						break;
		    					case 17 :
		    						store.setCol_16(strCellValue);
		    						break;
		    					case 18 :
		    						store.setCol_17(strCellValue);
		    						break;
		    					case 19 :
		    						store.setCol_18(strCellValue);
		    						break;
		    					case 20 :
		    						store.setCol_19(strCellValue);
		    						break;
		    					case 21 :
		    						store.setCol_20(strCellValue);
		    						break;
		    					case 22 :
		    						store.setCol_21(strCellValue);
		    						break;
		    					case 23 :
		    						store.setCol_22(strCellValue);
		    						break;
		    					case 24 :
		    						store.setCol_23(strCellValue);
		    						break;
		    					case 25 :
		    						store.setCol_24(strCellValue);
		    						break;
		    					case 26 :
		    						store.setCol_25(strCellValue);
		    						break;
		    					case 27 :
		    						store.setCol_26(strCellValue);
		    						break;
		    					case 28 :
		    						store.setCol_27(strCellValue);
		    						break;
		    					case 29 :
		    						store.setCol_28(strCellValue);
		    						break;
		    					case 30 :
		    						store.setCol_29(strCellValue);
		    						break;
		    					case 31 :
		    						store.setCol_30(strCellValue);
		    						break;
		    					case 32 :
		    						store.setCol_31(strCellValue);
		    						break;
		    					case 33 :
		    						store.setCol_32(strCellValue);
		    						break;
		    					case 34 :
		    						store.setCol_33(strCellValue);
		    						break;
		    					case 35 :
		    						store.setCol_34(strCellValue);
		    						break;
		    					case 36 :
		    						store.setCol_35(strCellValue);
		    						break;
		    					case 37 :
		    						store.setCol_36(strCellValue);
		    						break;
		    					case 38 :
		    						store.setCol_37(strCellValue);
		    						break;
		    					case 39 :
		    						store.setCol_38(strCellValue);
		    						break;
		    				}
	    				}
	    				else
	    				{
	    					String message = "Invalid upload source.";
	    					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
	    					fCtx.getExternalContext().getFlash().setKeepMessages(true);
	    					return destUrl;
	    	    		}
	    			}
	    			dao.updateStore(store);
	    		}
	    		batch.setRow_num(dataRowNum);
	    		batch = dao.updateBatch(batch);
	    		try {
	    			dao.runBuiltStoreStaff(getUploadSrc());
	    		}
	    		catch (Exception e) {
	    			String message = "Procedure RICH.RH_BUILT_STORE_STAFF_P Error";
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
					return destUrl;
	    		}
	    		String message = "Batch: " + batch.getBatch_key() + " upload success";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			else
			{
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
				return destUrl;
			}
			
		}
		else {
			String message = "Upload File cannot be null";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return destUrl;
		}
		
		return destUrl;
	}

	public List<String> getUploadSrcHeaders(String uploadSrc) {
		if(uploadSrc.equals("OUTPUT_PURE")) {
			List<String> rtnList = new ArrayList<String>();
			rtnList.add("OUTPUT_NO_LIB");
			rtnList.add("AUTHORSHIP_EMAIL");
			rtnList.add("AUTHORSHIP_NAME");
			rtnList.add("TITLE_JOUR_BOOK");
			rtnList.add("OUTPUT_CATEGORY");
			rtnList.add("SAP_OUTPUT_TYPE");
			rtnList.add("TITLE_PAPER_ART");
			rtnList.add("VOL_ISSUE");
			rtnList.add("PAGE_NUM");
			rtnList.add("CITY");
			rtnList.add("FROM_MONTH");
			rtnList.add("FROM_YEAR");
			rtnList.add("PUBLISHER");
			rtnList.add("LANGUAGE");
			rtnList.add("APA_CITATION");
			rtnList.add("ISSN");
			rtnList.add("PRINT_ISBN");
			rtnList.add("ELECTRONIC_ISBN");
			rtnList.add("FULLTEXT_URL");
			rtnList.add("DOI");
			rtnList.add("NAME_OTHER_EDITORS");
			rtnList.add("ARTICLE_NUM");
			rtnList.add("LAST_UPDATE_DATE");
			return rtnList;
		}
		else if (uploadSrc.equals("PROJECT_HREC")) {
			List<String> rtnList = new ArrayList<String>();
			rtnList.add("Reference Number");
			rtnList.add("PI ID");
			rtnList.add("Other Contributors ID");
			rtnList.add("Type");
			rtnList.add("Status");
			rtnList.add("Type of Funding");
			rtnList.add("Title of the Funding Source");
			rtnList.add("Project Title");
			rtnList.add("Project Summary");
			rtnList.add("Project From");
			rtnList.add("Project To");
			return rtnList;
		}
		else if (uploadSrc.equals("PATENT_KT")) {
			List<String> rtnList = new ArrayList<String>();
			rtnList.add("Source ID");
			rtnList.add("Type\n" + 
					"(Patent application/\n" + 
					"patent granted)");
			rtnList.add("Name of Patent");
			rtnList.add("Patent Number");
			rtnList.add("Patent Application Number");
			rtnList.add("Country / Region");
			rtnList.add("Description of Patent");
			rtnList.add("Date of Application");
			rtnList.add("Date of Grant ");
			rtnList.add("Inventor 1 (Contribution %)");
			rtnList.add("Inventor 1 ID");
			rtnList.add("Department of Inventor 1");
			rtnList.add("Inventor 2 (Contribution %)");
			rtnList.add("Inventor 2 ID");
			rtnList.add("Department of Inventor 2");
			rtnList.add("Inventor 3 (Contribution %)");
			rtnList.add("Inventor 3 ID");
			rtnList.add("Department of Inventor 3");
			rtnList.add("Inventor 4 (Contribution %)");
			rtnList.add("Inventor 4 ID");
			rtnList.add("Department of Inventor 4");
			rtnList.add("Inventor 5 (Contribution %)");
			rtnList.add("Inventor 5 ID");
			rtnList.add("Department of Inventor 5");
			rtnList.add("Inventor 6 (Contribution %)");
			rtnList.add("Inventor 6 ID");
			rtnList.add("Department of Inventor 6");
			rtnList.add("Inventor 7 (Contribution %)");
			rtnList.add("Inventor 7 ID");
			rtnList.add("Department of Inventor 7");
			rtnList.add("Inventor 8 (Contribution %)");
			rtnList.add("Inventor 8 ID");
			rtnList.add("Department of Inventor 8");
			rtnList.add("Inventor 9 (Contribution %)");
			rtnList.add("Inventor 9 ID");
			rtnList.add("Department of Inventor 9");
			rtnList.add("Inventor 10 (Contribution %)");
			rtnList.add("Inventor 10 ID");
			rtnList.add("Department of Inventor 10");
			return rtnList;
		}
		else return null;
	}
		
	public boolean isSysAdmin()
	{
		boolean rtnVal = false;
		Set<String> roleSet = AccessDAO.getCacheInstance().getRoleIdSetByUserId(getLoginUserId());
		if(roleSet.contains("sysAdmin"))
			rtnVal = true;
		else
			rtnVal = false;
		
		return rtnVal;
	}
}
