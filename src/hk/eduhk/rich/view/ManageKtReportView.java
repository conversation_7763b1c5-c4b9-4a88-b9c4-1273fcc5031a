package hk.eduhk.rich.view;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.entity.report.KtRptDAO;
import hk.eduhk.rich.entity.report.KtRptPeriod;
import hk.eduhk.rich.param.SysParamCacheDAO;
import hk.eduhk.rich.param.SysParamDAO;


@ManagedBean(name = "manageKtReportView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageKtReportView extends BaseView
{
	private static Logger logger = Logger.getLogger(KtRptPeriod.class.getName());
	private List<KtRptPeriod> periodList = null;
	private KtRptPeriod selectedPeriod;
	private String paramPeriodId;
	
	private KtRptDAO dao = KtRptDAO.getInstance();
	

	public String getParamPeriodId()
	{
		return paramPeriodId;
	}

	
	public void setParamPeriodId(String paramPeriodId)
	{
		this.paramPeriodId = paramPeriodId;
	}

	public List<KtRptPeriod> getPeriodList()
	{
		if (periodList == null) {
			periodList = dao.getKtRptPeriodList();
			//Hide period_id: = 1, 1 = SelectItem: All
			periodList = periodList.stream().filter(a -> !a.getPeriod_id().equals(1)).collect(Collectors.toList());
		}
		return periodList;
	}
	
	public void setPeriodList(List<KtRptPeriod> periodList)
	{
		this.periodList = periodList;
	}
	
	public KtRptPeriod getSelectedPeriod()
	{
		if (selectedPeriod == null) {
			if (!Strings.isNullOrEmpty(getParamPeriodId())) {
				Integer period_id = Integer.valueOf(paramPeriodId);
				selectedPeriod = dao.getKtRptPeriod(period_id);
			}
			if (selectedPeriod == null) {
				selectedPeriod = new KtRptPeriod();
				KtRptPeriod lastPeriod = dao.getLastKtRptPeriod();
				if (lastPeriod != null) {
					Calendar c = Calendar.getInstance();
					
					c.setTime(lastPeriod.getDate_from());
					c.add(Calendar.YEAR, 1);
					selectedPeriod.setDate_from(c.getTime());
					
					c.setTime(lastPeriod.getDate_to());
					c.add(Calendar.YEAR, 1);
					selectedPeriod.setDate_to(c.getTime());
					
					selectedPeriod.setPeriod_desc(lastPeriod.getPeriod_desc());
				}
			}
		}
		return selectedPeriod;
	}
	
	public void setSelectedPeriod(KtRptPeriod selectedPeriod)
	{
		this.selectedPeriod = selectedPeriod;
	}
	
	public boolean validPeriodData()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		boolean result = true;
		String message = "";
		if (selectedPeriod != null) {
			if(selectedPeriod.getDate_from().compareTo(selectedPeriod.getDate_to()) > 0) {
				result = false;
				message = "Period Date To must greater that Period Date From.";
				fCtx.addMessage("dataForm:date_to", new FacesMessage(FacesMessage.SEVERITY_ERROR, message, message));
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}else {
			result = false;
		}
		return result;
	}
	
	public String updatePeriod()  
	{
		if (selectedPeriod != null) {
			FacesContext fCtx = FacesContext.getCurrentInstance();
			if (validPeriodData()) {
				boolean isNew = (selectedPeriod.getCreationDate() == null);
				try
				{
					selectedPeriod.setUserstamp(getLoginUserId());
					DateFormat df_yy = new SimpleDateFormat("yy");
					DateFormat df_yyyy = new SimpleDateFormat("yyyy");
					String chartDesc1 = df_yyyy.format(selectedPeriod.getDate_from())+"/"+df_yy.format(selectedPeriod.getDate_to());
					selectedPeriod.setChart_desc(chartDesc1);
					String chartDesc2 = df_yyyy.format(selectedPeriod.getDate_from())+"-"+df_yyyy.format(selectedPeriod.getDate_to());
					selectedPeriod.setChart_desc_2(chartDesc2);
					selectedPeriod = dao.updateKtRptPeriod(selectedPeriod);
					if (getPeriodList() != null && selectedPeriod.getIs_current() == true) {
						for (KtRptPeriod p:periodList) {
							if (p.getIs_current() == true && !p.getPeriod_id().equals(selectedPeriod.getPeriod_id())) {
								p.setIs_current(false);
								dao.updateKtRptPeriod(p);
							}
						}
					}
					// Success message
					String message = (isNew) ? "msg.success.create.x" : "msg.success.update.x";
					message = MessageFormat.format(getResourceBundle().getString(message), "KT Report Period");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
				}
				catch (OptimisticLockException ole)
				{
					String message = getResourceBundle().getString("msg.err.optimistic.lock");
					fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
					fCtx.getExternalContext().getFlash().setKeepMessages(true);
					return "";
				}
			}else {
				return "";
			}
		}
		return redirect("ktRptPeriodList");
	}
	
	public String deletePeriod(Integer period_id)  
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		if (period_id != null) {
			try
			{
				dao.deleteKtRptPeriod(period_id);
				// Success message
				String message = "msg.success.delete.x";
				message = MessageFormat.format(getResourceBundle().getString(message), "KT Report Period");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (OptimisticLockException ole)
			{
				String message = getResourceBundle().getString("msg.err.optimistic.lock");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
		}else {
			// Failed message
			String message = "msg.err.not.exist";
			message = MessageFormat.format(getResourceBundle().getString(message), "Selected KT Report Period");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
		}	
		return redirect("ktRptPeriodList");
	}
	
	public String gotoPeriodEditPage(Integer period_id) 
	{
		return redirect("ktRptPeriodEdit")+"&periodId="+period_id;
	}
}


