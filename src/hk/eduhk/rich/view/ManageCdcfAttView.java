package hk.eduhk.rich.view;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.*;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;

import org.apache.commons.text.StringEscapeUtils;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;

import hk.eduhk.rich.BaseView;
import hk.eduhk.rich.Constant;
import hk.eduhk.rich.param.SysParam;
import hk.eduhk.rich.param.SysParamDAO;


@SuppressWarnings("serial")
@ManagedBean(name = "cdcfAttView")
@ViewScoped
public class ManageCdcfAttView extends BaseView implements Serializable {
	
	private String htmlContent;
    private String currentFile = "I"; // Default to Attachment I.htm
    private List<String> availableFiles = Arrays.asList(
            "I", 
            "II", 
            "III",
            "Table 7_Classification of Research Output from UGC Guideline"
        );
    private String htmlFilePath;

    @PostConstruct
    public void init() {
        //loadFile(currentFile);
    }

    public void loadFile(String fileName) {
        try {
            this.currentFile = fileName;
            ServletContext servletContext = (ServletContext) FacesContext.getCurrentInstance()
                .getExternalContext().getContext();
            
            // Handle special filename for the 4th file
            String fileSuffix = fileName.startsWith("Table 7") ? 
                fileName + ".htm" : "Attachment " + fileName + ".htm";
                
            htmlFilePath = servletContext.getRealPath("/user/" + fileSuffix);
            htmlContent = loadHtmlContent();
            
        } catch (IOException e) {
            addErrorMessage("Error loading file", e.getMessage());
        }
    }

    public String loadHtmlContent() throws IOException {
        Path path = Paths.get(htmlFilePath);
        byte[] encoded = Files.readAllBytes(path);
        String content = new String(encoded, StandardCharsets.UTF_8);
        
        // Convert tabs to visual spaces for editor
        return content.replace("\t", "    ");
    }

    public void save() {
        try {
            // Convert visual spaces back to tabs and clean content
            String content = htmlContent
                .replace("    ", "\t") // Convert spaces back to tabs
                .replace("\r\n", "\n") // Normalize line endings
                .replaceAll("^(\\n)+", ""); // Remove leading newlines
            
            // Ensure proper HTML structure
            if (!content.toLowerCase().contains("<html")) {
                content = "<!DOCTYPE html><html><head>" +
                         "<meta charset=\"UTF-8\">" +
                         "<style>" +
                         "body { white-space: pre-wrap; tab-size: 8; font-family: Arial; }" +
                         ".ql-align-center { text-align: center; }" +
                         "p {margin: 0.1em;}" +
                         "</style></head><body>" + 
                         content + "</body></html>";
            }
            
            saveHtmlContent(content);
            addSuccessMessage("Success", currentFile + ".htm saved successfully");
        } catch (IOException e) {
            addErrorMessage("Error", "Failed to save " + currentFile + ".htm");
        }
    }

    private void saveHtmlContent(String content) throws IOException {
        Path path = Paths.get(htmlFilePath);
        try (BufferedWriter writer = Files.newBufferedWriter(
                path, 
                StandardCharsets.UTF_8,
                StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING)) {
            
            writer.write("\uFEFF"); // UTF-8 BOM
            writer.write(content);
        }
    }

    public String getHtmlContentForPreview() {
        return htmlContent.replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;");
    }

    // Getters and Setters
    public String getHtmlContent() {
    	return htmlContent; 
    }
    public void setHtmlContent(String htmlContent) {
    	this.htmlContent = htmlContent; 
    }
    public String getCurrentFile() {
    	return currentFile; 
    }
    public void setCurrentFile(String currentFile) {
        this.currentFile = currentFile;
    }
    public List<String> getAvailableFiles() {
    	return availableFiles; 
    }
    
    private void addSuccessMessage(String summary, String detail) {
        FacesContext.getCurrentInstance().addMessage(null, 
            new FacesMessage(FacesMessage.SEVERITY_INFO, summary, detail));
    }
    
    private void addErrorMessage(String summary, String detail) {
        FacesContext.getCurrentInstance().addMessage(null, 
            new FacesMessage(FacesMessage.SEVERITY_ERROR, summary, detail));
    }

    public void handleFileUpload(FileUploadEvent event, String attachmentName) {
	        try {
	            // Validate input
	            if (event == null || event.getFile() == null) {
	                throw new IllegalArgumentException("No file was uploaded");
	            }
	            
	            // Get the uploaded file
	            UploadedFile uploadedFile = event.getFile();
	            String fileName = uploadedFile.getFileName();
	            
	            // Validate file name
	            if (fileName == null || fileName.trim().isEmpty()) {
	                throw new IllegalArgumentException("Invalid file name");
	            }
	
	            // Define the target path
	            String targetDir = getFilePath();
	            
	            // Ensure attachment name has proper extension
	            String targetFileName = attachmentName.endsWith(".htm") ? attachmentName : attachmentName + ".htm";
	            Path targetPath = Paths.get(targetDir, targetFileName);
	
	            // Create directories if they don't exist
	            Files.createDirectories(targetPath.getParent());
	
	            // Save the file, overwriting the existing one
	            try (InputStream input = uploadedFile.getInputStream()) {
	                Files.copy(input, targetPath, StandardCopyOption.REPLACE_EXISTING);
	            }
	
	            // Show success message
	            addFacesMessage(FacesMessage.SEVERITY_INFO, "Success", 
	                String.format("File %s uploaded successfully.", targetFileName));
	            
	        } catch (Exception e) {
	            // Handle errors
	            addFacesMessage(FacesMessage.SEVERITY_ERROR, "Error", 
	                String.format("Failed to upload file: %s", e.getMessage()));
	        }
	    }
	
	    // Helper method to add Faces messages
    	private void addFacesMessage(FacesMessage.Severity severity, String summary, String detail) {
	        FacesContext.getCurrentInstance()
	            .addMessage(null, new FacesMessage(severity, summary, detail));
	    }
	
	    public String getFilePath(){
			String code = Constant.isLocalEnv() ? SysParam.PARAM_FILE_PATH_LOCAL : SysParam.PARAM_FILE_PATH;
			SysParamDAO paramDAO = SysParamDAO.getCacheInstance();
			return paramDAO.getSysParamValueByCode(code);
		}

		// Specific handlers that call the common method
	    public void handleFileUploadAtt1(FileUploadEvent event) {
	        handleFileUpload(event, "Attachment_I.htm");
	    }
	
	    public void handleFileUploadAtt2(FileUploadEvent event) {
	        handleFileUpload(event, "Attachment_II.htm");
	    }
	    
	    public void handleFileUploadAtt3(FileUploadEvent event) {
	        handleFileUpload(event, "Attachment_III.htm");
	    }
	    
	    public void handleFileUploadAtt4(FileUploadEvent event) {
	        handleFileUpload(event, "Table_7_Classification_of_Research_Output_from_UGC_Guideline.htm");
	    }
	    
	    
}
