package hk.eduhk.rich.report.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.report.ReportGroup;

public class KtFormStaffEngmtReportGroup extends ReportGroup
{
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	
	public KtFormStaffEngmtReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook)  
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		List<Function> functionList = new ArrayList<Function>();
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		headerStyle = headerRowStyle;
		activeHeaderFilter = true;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;
		
		columnNames = new String[] {"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
									"Name of EdUHK Staff", "Administrative / Technical Staff?",
									"Start Date (MM-YYYY)", "End Date (MM-YYYY)", 
									"Name of External Body", getResourceBundle().getString("kt.form.label.region"),  "Nature of External Body", "Post Engaged",
									"Remarks"};
		
		if(isKtAdmin()) 
		{
			fieldNames = new String[] {	"pk.form_no", "fac", "dept",
										"staff_name", "admin_tech",
										"start_date", "end_date", 
										"ext_body_name", "regionValue", "ext_body_nature", "post_engaged",
										"remarks_staff"};
		}
		else
		{
			fieldNames = new String[] {	"pk.form_no", "fac", "dept", 
										"staff_name", "admin_tech",
										"start_date", "end_date", 
										"ext_body_name", "regionValue", "extBodyNatureValue", "post_engaged",
										"remarks_staff"};
		}
		
		
		cellStyles = new CellStyle[] {	null, textCellStyle, textCellStyle, 
										textCellStyle,textCellStyle,
										dateCellStyle, dateCellStyle, 
										textCellStyle, textCellStyle, textCellStyle, textCellStyle,
										textCellStyle};
		if(isRdoAdmin) {
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Note (RDO)")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "remarks_kt")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
		}
		
	}
}
