package hk.eduhk.rich.report.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.report.ReportGroup;

public class KtFormSocEngmtReportGroup extends ReportGroup
{
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	
	public KtFormSocEngmtReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook)  
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("DD-MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		List<Function> functionList = new ArrayList<Function>();
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		headerStyle = headerRowStyle;
		activeHeaderFilter = true;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;
		
		columnNames = new String[] {"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
									"Entrepreneurship Element?", "Performance Arts?",
									"Title of the Event", getResourceBundle().getString("kt.form.label.act.code"), 
									getResourceBundle().getString("kt.form.label.num.sub.ses"), 
									getResourceBundle().getString("kt.form.label.kt.pi"), 
									getResourceBundle().getString("kt.form.label.budget.holder"),	"Mode",				
									getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"),
									"Total Number of Activity Days", "Total Number of Activity Days in the Reporting Year",
									getResourceBundle().getString("kt.form.label.eduhk.org"),  getResourceBundle().getString("kt.form.label.region"),  getResourceBundle().getString("kt.form.label.free"),  
									getResourceBundle().getString("kt.form.label.budget.approval"),  getResourceBundle().getString("kt.form.label.budget.total"), 
									getResourceBundle().getString("kt.form.label.income"),getResourceBundle().getString("kt.form.label.expend"),
									getResourceBundle().getString("kt.form.label.num.key"), 
									getResourceBundle().getString("kt.form.label.num.spe"), "Target Participants",
									getResourceBundle().getString("kt.form.label.num.tea"), getResourceBundle().getString("kt.form.label.num.pri"), 
									getResourceBundle().getString("kt.form.label.num.oth"), getResourceBundle().getString("kt.form.label.num.stu"), 
									getResourceBundle().getString("kt.form.label.num.sch"), 
									"Remarks"};
		
		if(isKtAdmin()) 
		{
			fieldNames = new String[] {	"pk.form_no", "fac", "dept",
										"ent_element", "perf_art",
										"title", "act_code", 
										"num_subsessions", "pi", 
										"budget_holder", "act_mode", "start_date", "end_date",
										"num_proj_day", "num_proj_day_in_yr",
										"eduhk_org", "regionValue", "free_charge", 
										"budget_approval", "budget",
										"income_rpt_unit", "expnd_rpt_unit",
										"num_key_partner", 
										"num_speakers", "target_pax",
										"num_teacher", "num_principal",
										"num_other", "num_stu",
										"num_school",
										"remarks_staff"};
		}
		else
		{
			fieldNames = new String[] {	"pk.form_no", "fac", "dept",
										"ent_element", "perf_art",
										"title", "act_code", 
										"num_subsessions", "pi", 
										"budget_holder", "act_mode", "start_date", "end_date",
										"num_proj_day", "num_proj_day_in_yr",
										"eduhk_org", "regionValue", "free_charge", 
										"budget_approval", "budget",
										"income_rpt_unit", "expnd_rpt_unit",
										"num_key_partner", 
										"num_speakers", "target_pax",
										"num_teacher", "num_principal",
										"num_other", "num_stu",
										"num_school",
										"remarks_staff"};
		}
		
		
		cellStyles = new CellStyle[] {	null, textCellStyle, textCellStyle, 
										textCellStyle, textCellStyle,
										textCellStyle, textCellStyle,
										numberCellStyle, textCellStyle,
										textCellStyle, textCellStyle, dateCellStyle, dateCellStyle, 
										textCellStyle,textCellStyle,
										textCellStyle, textCellStyle, textCellStyle, 
										textCellStyle, numberCellStyle,
										numberCellStyle, numberCellStyle,
										numberCellStyle, 
										numberCellStyle,textCellStyle,
										numberCellStyle, numberCellStyle,
										numberCellStyle, numberCellStyle,
										numberCellStyle,
										textCellStyle};
		if(isRdoAdmin) {
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.income.fo"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_fo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.income.fo.remark"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_fo_rem")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.income.rdo"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_rdo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.expend.fo"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "expnd_fo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.expend.fo.remark"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "expnd_fo_rem")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.expend.rdo"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "expnd_rdo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Note (RDO)")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "remarks_kt")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
		}
	}
}
