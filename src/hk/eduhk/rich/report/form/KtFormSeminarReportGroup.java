package hk.eduhk.rich.report.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.report.ReportGroup;

public class KtFormSeminarReportGroup extends ReportGroup
{
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	
	public KtFormSeminarReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook)  
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		List<Function> functionList = new ArrayList<Function>();
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		headerStyle = headerRowStyle;
		activeHeaderFilter = true;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;
		
		columnNames = new String[] {"Form Number", "Seminar/ Workshop Title", "Date (MM-YYYY)", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
									"Organizer", "Contact Person", "Target Participants", getResourceBundle().getString("kt.form.label.act.code"), "Free/ Chargeable (F/C)",
									"Income (HK$)", "Income (HK$) (FO)", "Income (HK$) (Remarks fr FO)", "Income (HK$) (RDO)",
									"Expenditure (Direct Cost)", "Expenditure (Direct Cost) (FO)", "Expenditure (Direct Cost) (Remarks fr FO)", "Expenditure (Direct Cost) (RDO)",
									"Number of Key Partners", "Number of Particpants", "Check # of Participants", "Number of Teacher Participants",
									"Number of Principal Participants", "Number of Other Stakeholder Participants (e.g. parents)",
									"Number of Academic and Research Staff Involved (Man-days) [For those classified as (1) or (2) in column 'Target Participants' ONLY]",
									"Number of External Professionals Engaged", "Remarks"};
		
		
		if(isKtAdmin()) 
		{
			fieldNames = new String[] {	"pk.form_no", "title", "start_date", "fac", "dept",
										"organizer", "ct_person", "target_pax", "act_code", "free_charge",
										"income_rpt_unit", "income_fo", "income_fo_rem", "income_rdo",
										"expnd_rpt_unit", "expnd_fo", "expnd_fo_rem", "expnd_rdo",
										"num_key_partner", "num_pax", "check_pax", "num_teacher",
										"num_principal", "num_other",
										"staff_man_day",
										"num_ext_prof", "remarks_staff"};
		}
		else
		{
			fieldNames = new String[] {	"pk.form_no", "title", "start_date", "fac", "dept",
										"organizer", "ct_person", "targetPaxValue", "act_code", "free_charge",
										"income_rpt_unit", "income_fo", "income_fo_rem", "income_rdo",
										"expnd_rpt_unit", "expnd_fo", "expnd_fo_rem", "expnd_rdo",
										"num_key_partner", "num_pax", "check_pax", "num_teacher",
										"num_principal", "num_other",
										"staff_man_day",
										"num_ext_prof", "remarks_staff"};
		}
		
		
		cellStyles = new CellStyle[] {	null, textCellStyle, dateCellStyle, textCellStyle, textCellStyle,
										textCellStyle, textCellStyle, textCellStyle, textCellStyle, textCellStyle,
										numberCellStyle, numberCellStyle, textCellStyle, numberCellStyle,
										numberCellStyle, numberCellStyle, textCellStyle, numberCellStyle,
										numberCellStyle, numberCellStyle, textCellStyle, numberCellStyle,
										numberCellStyle, numberCellStyle,
										numberCellStyle,
										numberCellStyle, textCellStyle};
		if(isRdoAdmin) {
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Note (RDO)")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "remarks_kt")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
		}
	}
}
