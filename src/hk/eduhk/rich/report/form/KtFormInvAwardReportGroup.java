package hk.eduhk.rich.report.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.report.ReportGroup;

public class KtFormInvAwardReportGroup extends ReportGroup
{
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	
	public KtFormInvAwardReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook)  
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		List<Function> functionList = new ArrayList<Function>();
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

		headerStyle = headerRowStyle;
		activeHeaderFilter = true;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;
		
		columnNames = new String[] {"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
									"Name of the Innovative Product/ Invention", "Name of Competition / Exhibition / Event",
									"Title of Award", "Award Type", "Date of Receiving the Award(s)", 
									"Name of Principal Inventor/ Awardee", "Name(s) of Other Inventor(s)/ Awardee(s)", 
									"Number of Patent Filed", "Name of the Invention", "Application Number", "Date of Application (MM/YYYY)", "Country/ Region Where the Patent is filed",
									"Number of Patent Granted", "Name of the Patent", "Patent Number", "Date of Patent Granted (MM/YYYY)", "Country/ Region Granting the Patent", "Remarks"};
		
		fieldNames = new String[] {	"pk.form_no", "fac", "dept",
									"ip_name", "event_name",
									"award_title", "award_type", "start_date", 
									"name_pi", "name_other", 
									"num_pat_filed", "patent_filed_name", "patent_filed_num", "patent_filed_date", "patent_filed_country",
									"num_pat_granted", "patent_granted_name", "patent_granted_num", "patent_granted_date", "patent_granted_country", "remarks_staff"};
		
		cellStyles = new CellStyle[] {	null, textCellStyle, textCellStyle,
										textCellStyle, textCellStyle,
										textCellStyle, textCellStyle, dateCellStyle, 
										textCellStyle, textCellStyle, 
										textCellStyle, textCellStyle, textCellStyle, textCellStyle, textCellStyle, 
										textCellStyle, textCellStyle, textCellStyle, textCellStyle, textCellStyle,  textCellStyle};
		
		if(isRdoAdmin) {
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Note (RDO)")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "remarks_kt")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
		}
	}
}
