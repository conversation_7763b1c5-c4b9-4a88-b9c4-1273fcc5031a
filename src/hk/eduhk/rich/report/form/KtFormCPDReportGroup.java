package hk.eduhk.rich.report.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.logging.Level;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import hk.eduhk.rich.Constant;
import hk.eduhk.rich.report.ReportGroup;

public class KtFormCPDReportGroup extends ReportGroup
{
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	
	public KtFormCPDReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("DD-MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		List<Function> functionList = new ArrayList<Function>();
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		headerStyle = headerRowStyle;
		activeHeaderFilter = true;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;
		
		columnNames = new String[] {"Form Number",getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"), 
									"Any Research Element in the Course?", "Is It a Course/ Programme?", "Entrepreneurship Element?",
									"Course Title", getResourceBundle().getString("kt.form.label.act.code"), 
									getResourceBundle().getString("kt.form.label.kt.pi"), "Funding Source", 
									"Mode", 
									getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"), 
									"Total Number of Course Days", "Total Number of Course Days in the Reporting Year",
									getResourceBundle().getString("kt.form.label.eduhk.org"), getResourceBundle().getString("kt.form.label.region"), 
									"Free/ Chargeable (F/C)",
									getResourceBundle().getString("kt.form.label.budget.total"), 
									getResourceBundle().getString("kt.form.label.income"), 
									getResourceBundle().getString("kt.form.label.expend"), 
									getResourceBundle().getString("kt.form.label.num.key"), getResourceBundle().getString("kt.form.label.num.tea"), getResourceBundle().getString("kt.form.label.num.pri"), 
									getResourceBundle().getString("kt.form.label.num.oth.stakeholder"), getResourceBundle().getString("kt.form.label.num.sch"), 
									"Number of Student Contact Hours",  "Remarks"};
		
		fieldNames = new String[] {	"pk.form_no",  "fac", "dept", 
									"research_element", "crse_prog", "ent_element",
									"title", "act_code", 
									"pi", "fund_src", 
									"act_mode", 
									"start_date", "end_date",
									"num_proj_day", "num_proj_day_in_yr",
									"eduhk_org", "regionValue",
									"free_charge",
									"budget",
									"income_rpt_unit", 
									"expnd_rpt_unit",
									"num_key_partner", "num_teacher", "num_principal",
									"num_other", "num_school",
									"num_stu_contact_hr", "remarks_staff"};
		
		cellStyles = new CellStyle[] {	null,textCellStyle,textCellStyle,
										textCellStyle,textCellStyle,textCellStyle,
										textCellStyle,textCellStyle,
										textCellStyle,textCellStyle,
										textCellStyle,
										dateCellStyle,dateCellStyle,
										textCellStyle, textCellStyle,
										textCellStyle, textCellStyle,
										textCellStyle,
										numberCellStyle,
										numberCellStyle,
										numberCellStyle,	
										null,null,null,
										null,null,
										null, textCellStyle};
		
		//convertFunctions = new Function[] {null,null};
		if(isRdoAdmin) {		
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Income Remarks From FO ")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_fo_rem")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.income.rdo"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_rdo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Total Expenditure from the Event (Remarks from FO) ")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "expnd_fo_rem")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Total Expenditure from the Event (RDO) ")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "expnd_rdo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Note (RDO)")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "remarks_kt")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
		}
	}
	
}
