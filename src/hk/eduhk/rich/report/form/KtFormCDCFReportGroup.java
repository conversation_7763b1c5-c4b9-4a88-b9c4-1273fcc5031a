package hk.eduhk.rich.report.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.stream.Collectors;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import com.itextpdf.text.Font;

import hk.eduhk.rich.entity.report.AppPeriodReport;
import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.FacultyDeptStaffCount;
import hk.eduhk.rich.entity.report.employeeCount;
import hk.eduhk.rich.entity.report.employeeCountGrade;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.report.ReportGroup;
import hk.eduhk.rich.view.PreRptView;


public class KtFormCDCFReportGroup extends ReportGroup
{
	
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	private List<String> flassDeptNames;
	private List<String> fehdDeptNames;
	private List<String> fhmDeptNames;
	private String[] rowNames;
	
	
	private List<employeeCountGrade> employeeCountGradeList ;
	
	private int [][]   dept_count_list ;
	private int [][]   fehd_count_list ;
	private int [][]   fhm_count_list ;
	
	private List<FacultyDeptStaffCount> staffList_count;
	
	private List<AppStaffCount> dept_total_count;
	
	
	public List<String> getFlassDeptNames()
	{
		return flassDeptNames;
	}


	
	public void setFlassDeptNames(List<String> flassDeptNames)
	{
		this.flassDeptNames = flassDeptNames;
	}


	
	public List<String> getFehdDeptNames()
	{
		return fehdDeptNames;
	}


	
	public void setFehdDeptNames(List<String> fehdDeptNames)
	{
		this.fehdDeptNames = fehdDeptNames;
	}


	
	public List<String> getFhmDeptNames()
	{
		return fhmDeptNames;
	}


	
	public void setFhmDeptNames(List<String> fhmDeptNames)
	{
		this.fhmDeptNames = fhmDeptNames;
	}

	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}


	public KtFormCDCFReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook) 
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		
		headerStyle = headerRowStyle;
		
		
		activeHeaderFilter = false;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;		
		
		columnNames = new String[] {"Unique ID","Faculty","Deparment","Number of items band","LV", "Period ID", "Count Staff"};
		
			
		rowNames = new String[] {"0",">0 to <1" , "Sub-total",">=1 to 2" , ">2 to 3",">3 to 4",">4 to 5", ">5 to 6","over 6" , "Sub-total","Total no. of academic staff"};
		fieldNames = new String[] {"report_id","faculty", "dept_code", "no_of_item_band", "lv", "count_staff"}; 		
		cellStyles = new CellStyle[] {null,null, null, null, null,null,null};

		try {
			//Todo: Department call db 
			flassDeptNames  = AppPeriodReportDAO.getFacultyDept("FLASS");
			fehdDeptNames  = AppPeriodReportDAO.getFacultyDept("FEHD");
			fhmDeptNames  = AppPeriodReportDAO.getFacultyDept("FHM");
		}
		catch (Exception e)
		{
			logger.log(Level.INFO, "Cannot get name for Department");
		}

		 dept_count_list  = new int [rowNames.length][flassDeptNames.size()];
		 fehd_count_list  = new int [rowNames.length][fehdDeptNames.size()];
		 fhm_count_list   = new int [rowNames.length][fhmDeptNames.size()];
	
	}
	
	public void appendCollegeCollumns (Row row1, Row row2, 
										int sindex, int eindex, reportFilteringField filter,
										String sheetName,String collName,String totalName, List<String> deptList)
	{	
			
			Cell cell;
			try
			{
				//Style: Center
				CellStyle cellDataStyle = combinStyle(IndexedColors.PALE_BLUE.index,false);

				//Style: Blue, Center 
				CellStyle cellDataStyle_2 = combinStyle(IndexedColors.PALE_BLUE.index,true);
				
				//Style: Orange, Center
				CellStyle cellDataStyle_3 = combinStyle(IndexedColors.LIGHT_ORANGE.index,true);
				
				//Style: Orange, Center
				CellStyle cellDataStyle_4 = combinStyle(IndexedColors.GREY_25_PERCENT.index,true);

						
				if (totalName =="FLASS Total") {
					for (int i = 0 ;i < 15;i++) {
					Cell cell_style = row1.createCell(i);	
					cell_style.setCellStyle(cellDataStyle);
					}
				}
				
				//College Row
				cell = row1.createCell(sindex-1);	
				cell.setCellStyle(cellDataStyle);
				cell.setCellValue(collName);

				Sheet sheet = workbook.getSheet(sheetName);
				
				sheet.setColumnWidth(0,15*256);
				
				int startCellNum = cell.getColumnIndex();
				int endCellNum = startCellNum+ eindex;
				

				int [] sub_total_1 = new int[flassDeptNames.size()];
				int [] sub_total_2 = new int[flassDeptNames.size()];
				
				int [] fehd_total_1 = new int[fehdDeptNames.size()];
				int [] fehd_total_2 = new int[fehdDeptNames.size()];
				
				int[] fhm_total_1 = new int[fhmDeptNames.size()];
				int[] fhm_total_2 = new int[fhmDeptNames.size()];
				
				int aps_count_1 = 0;

				// Get data from staff list
				staffList_count = AppPeriodReportDAO.getAppStaff_weight(filter);
				

				if (totalName =="FLASS Total") {
					
					for (int i = 15 ;i < 40;i++) {
						cell = row1.createCell(i);	
						cell.setCellStyle(cellDataStyle);
					}

					
					Cell cellstart = row2.createCell(0);
					cellstart.setCellStyle(cellDataStyle);
					cellstart.setCellValue("No. of item");
					

						Row row = null;
						int []sum_row = new int[3];
						int [] sum_college = new int[3];
						int sum_row_total = 0;
						int sum_college_total = 0;
						
						
						
						for (int i = 0 ; i<rowNames.length;i++)
						{
							
								row = sheet.createRow(i+6);
								
								//Each row should started at Col 1
								
								Cell cellData = row.createCell(0);
								cellData.setCellStyle(cellDataStyle);
								if(i==2 || i==9)
									cellData.setCellStyle(cellDataStyle_3);
								cellData.setCellValue(rowNames[i]);
													
								sum_row[0] = 0 ;
								sum_college [0] = 0;
								
								
								//FLASS
								for (int j = 0 ;j < flassDeptNames.size();j++)
								{
									dept_total_count = AppPeriodReportDAO.getAppStaffCount_List("FLASS",flassDeptNames.get(j),true);
									
									aps_count_1 = dept_total_count.get(0).getStaff_count();
									sum_college[0] += aps_count_1;
									int columnValue = 1+j*2;
									cellstart = row.createCell(columnValue);
									
								
									if (i<2) {
										sub_total_1[j] += appendDataColumns(row,columnValue,rowNames[i],flassDeptNames.get(j),i,j,aps_count_1);
										//System.out.println("dept_count _list : "+dept_count_list[i][j] + " | i row"+ i);
									}
									else if (i==2) {
										
											dept_count_list[i][j] = sub_total_1[j];
											cellstart.setCellStyle(cellDataStyle_3);
											cellstart.setCellValue(sub_total_1[j]);
											
											//System.out.println("dept_countlist: "+dept_count_list[i][j]);
											cellstart = row.createCell(columnValue+1);
											cellstart.setCellStyle(cellDataStyle_3);
											cellstart.setCellValue("("+ (int) PreRptView.roundAvoid((sub_total_1[j]) * 100.0 / aps_count_1,0)+"%)");
									}
									else if (i== 9) {
										dept_count_list[i][j] = sub_total_2[j];
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue(sub_total_2[j]);
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue("("+ (int) PreRptView.roundAvoid(sub_total_2[j] * 100.0 /  aps_count_1,0)+"%)");
									}
									else if ( i == 10)
									{
										dept_count_list[i][j] = aps_count_1;
										
										cellstart.setCellStyle(cellDataStyle_4);
										cellstart.setCellValue(aps_count_1);
										
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_4);
										
										
										sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),columnValue,columnValue+1));
									}
									else {
										sub_total_2[j] += appendDataColumns(row,columnValue,rowNames[i],flassDeptNames.get(j),i,j,aps_count_1);
									}
									sum_row[0] += dept_count_list[i][j];
									
									
								}
								
								
								cellstart = row.createCell(flassDeptNames.size() *2+1);
								
								if ( flassDeptNames.size() *2+1!= 2 && flassDeptNames.size() *2+1 != 9 )
									cellstart.setCellStyle(cellDataStyle_2);
								
								if (i == 2 || i== 9 )
									cellstart.setCellStyle(cellDataStyle_3);
								
								
								cellstart.setCellValue(sum_row[0]);
								
								cellstart = row.createCell(flassDeptNames.size() *2+2);
								cellstart.setCellStyle(cellDataStyle_2);
								
								
								if (i == 10) 
									sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),flassDeptNames.size() *2+1 ,flassDeptNames.size() *2+2) );
								else {
									//Total Percentage
									cellstart = row.createCell(flassDeptNames.size() *2+2 );
									
									if ( flassDeptNames.size()*2+2 != 2 && flassDeptNames.size()*2+2 != 9 )
										cellstart.setCellStyle(cellDataStyle_2);
									
									if (i == 2 || i== 9)
										cellstart.setCellStyle(cellDataStyle_3);
									cellstart.setCellValue("("+ (int) PreRptView.roundAvoid(sum_row[0]*100.0 /sum_college[0],0)+"%)");
								
								}
								
								//FEHD
								sum_college[1] = 0;
								sum_row[1] = 0;

								
								for (int j = 0 ;j < fehdDeptNames.size();j++)
								{
									
									dept_total_count = AppPeriodReportDAO.getAppStaffCount_List("FEHD",fehdDeptNames.get(j),true);
									aps_count_1 = dept_total_count.get(0).getStaff_count();
									sum_college[1] += aps_count_1;
									int columnValue = (flassDeptNames.size() + 2 +j)*2-1;
									cellstart = row.createCell(columnValue);
								
									if (i<2) {
										
										fehd_total_1[j] += appendDataColumns(row,columnValue,rowNames[i],fehdDeptNames.get(j),i,j,aps_count_1);
										//System.out.println("fehd_count_list : "+fehd_count_list[i][j] + " | i row"+ i);
										
									}
									else if ( i == 2) {
										fehd_count_list[i][j] =fehd_total_1[j] ;
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue(fehd_total_1[j]);
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue("("+ (int) PreRptView.roundAvoid((fehd_total_1[j]) * 100.0 / aps_count_1,0)+"%)");
									}
									else if (i == 9) {
										fehd_count_list[i][j] = fehd_total_2[j];
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue(fehd_total_2[j]);
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue("("+ (int) PreRptView.roundAvoid(fehd_total_2[j] * 100.0 / aps_count_1,0)+"%)");
									}
									else if ( i == 10) {
										//when i = 10
										fehd_count_list[i][j] = aps_count_1;
										cellstart.setCellStyle(cellDataStyle_4);
										cellstart.setCellValue(aps_count_1);
										
										
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_4);
										
										sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),columnValue,columnValue+1));
									}
									else {
										fehd_total_2[j] += appendDataColumns(row,columnValue,rowNames[i],fehdDeptNames.get(j),i,j,aps_count_1);
									}
									sum_row[1] += fehd_count_list[i][j];
								}
								
								int c1 = fehdDeptNames.size() * 2 + flassDeptNames.size() * 2 + 3 ;
								cellstart = row.createCell(c1);
								if ( c1 != 2 && c1 != 9 )
									cellstart.setCellStyle(cellDataStyle_2);
								if ( i==2 || i==9 )
									cellstart.setCellStyle(cellDataStyle_3);
								
								//System.out.println("sum_row : "+sum_row[1] + " | i row"+ i);
								cellstart.setCellValue(sum_row[1]);
								
								
								cellstart = row.createCell(c1+1);
								cellstart.setCellStyle(cellDataStyle_2);
								
								if (i == 10)
									sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),c1,c1+1));
								else {
									cellstart = row.createCell(c1 + 1 );
									if ( c1 != 2 && c1 != 9 )
										cellstart.setCellStyle(cellDataStyle_2);
									if ( i==2 || i==9 )
										cellstart.setCellStyle(cellDataStyle_3);
									cellstart.setCellValue("("+ (int) PreRptView.roundAvoid( sum_row[1]*100.0 /sum_college[1],0)+"%)");
								}

								
								//FHM
								
								sum_college[2] = 0;
								sum_row[2] = 0;

								for (int j = 0 ;j < fhmDeptNames.size();j++)
								{
									dept_total_count = AppPeriodReportDAO.getAppStaffCount_List("FHM",fhmDeptNames.get(j),true);
									aps_count_1 = dept_total_count.get(0).getStaff_count();
									sum_college[2] += aps_count_1;
									int columnValue = (fehdDeptNames.size() + flassDeptNames.size()  + 3 + j)*2-1;
									cellstart = row.createCell(columnValue);
									
								
									if (i<2) {
										fhm_total_1[j] += appendDataColumns(row,columnValue,rowNames[i],fhmDeptNames.get(j),i,j,aps_count_1);
										
									}
									else if (i == 2) {
										fhm_count_list[i][j] = fhm_total_1[j];
										cellstart.setCellValue(fhm_total_1[j]);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue("("+ (int) PreRptView.roundAvoid((fhm_total_1[j]) * 100.0 / aps_count_1,0)+"%)");
									}
									else if (i == 9) {
										fhm_count_list[i][j] = fhm_total_2[j];
										cellstart.setCellValue(fhm_total_2[j]);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_3);
										cellstart.setCellValue("("+ (int) PreRptView.roundAvoid(fhm_total_2[j] * 100.0 / aps_count_1,0)+"%)");
										
									}
									else if ( i == 10) {
										fhm_count_list[i][j] = aps_count_1;
										cellstart.setCellStyle(cellDataStyle_4);
										cellstart.setCellValue(aps_count_1);
										
										cellstart = row.createCell(columnValue+1);
										cellstart.setCellStyle(cellDataStyle_4);
																				
										sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),columnValue,columnValue+1));
									}
									else {
										fhm_total_2[j] += appendDataColumns(row,columnValue,rowNames[i],fhmDeptNames.get(j),i,j,aps_count_1);
									}
									sum_row[2] += fhm_count_list[i][j];
								}
				
								int c2 = (fehdDeptNames.size() + flassDeptNames.size()+ fhmDeptNames.size()+3) *2-1  ;
								
								cellstart = row.createCell(c2);
								
								if ( c2 != 2 && c2 != 9 )
									cellstart.setCellStyle(cellDataStyle_2);
								if (i == 2 || i == 9)
									cellstart.setCellStyle(cellDataStyle_3);
								
								cellstart.setCellValue(sum_row[2]);
								
								cellstart = row.createCell(c2+1);
								cellstart.setCellStyle(cellDataStyle_2);
								
								if (i == 10)
									sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),c2,c2+1));
								else {
									
									cellstart = row.createCell(c2 +1 );
									if ( c2 != 2 && c2 != 9 )
										cellstart.setCellStyle(cellDataStyle_2);
									if (i == 2 || i == 9)
										cellstart.setCellStyle(cellDataStyle_3);
									cellstart.setCellValue("("+(int) PreRptView.roundAvoid(sum_row[2]*100.0/sum_college[2],0)+"%)");
								}
								
								
								sum_row_total = sum_row[0] + sum_row[1] + sum_row[2];
								sum_college_total = sum_college[0] + sum_college[1] + sum_college[2];
								
								//real total
								cellstart = row.createCell(c2 + 2 );
								//if (i == 2 || i == 9 || i==10)
								cellstart.setCellStyle(cellDataStyle_4);
								cellstart.setCellValue(sum_row_total);
								
								cellstart = row.createCell(c2 + 3 );
								cellstart.setCellStyle(cellDataStyle_4);
								
								// real total %
								if(i == 10)
									sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum(),c2+2,c2+3));
								else {
									cellstart = row.createCell(c2 + 3);
									cellstart.setCellStyle(cellDataStyle_4);
									cellstart.setCellValue("("+ (int) PreRptView.roundAvoid( sum_row_total*100.0/sum_college_total,0)+"%)");
								}
						}
						
						//Remark
						row = sheet.createRow(rowNames.length+7);
						Cell cellRemark = row.createCell(0);
						cellRemark.setCellValue("* Value of count of individual item may not add up to the value of sub-total/total due to rounding of figures.");
						sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum() , 0 , 40));
						
						row = sheet.createRow(rowNames.length+8);
						cellRemark = row.createCell(0);
						cellRemark.setCellValue("# The non-traditional outputs of CCA are included for consideration.");
						sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum() , 0 , 40));
	
				}
				else if (totalName =="FHM Total") {
					Cell cellend = row1.createCell(endCellNum+3);
					cellend.setCellStyle(cellDataStyle);
					cellend.setCellValue("Total");
					
					cellend = row1.createCell(endCellNum+4);
					cellend.setCellStyle(cellDataStyle);
					
					cellend = row2.createCell(endCellNum+3);
					cellend.setCellStyle(cellDataStyle);
					
					cellend = row2.createCell(endCellNum+4);
					cellend.setCellStyle(cellDataStyle);
					sheet.addMergedRegion(new CellRangeAddress(row1.getRowNum(),row1.getRowNum()+1,endCellNum+3,endCellNum+4));
				}
				
									
				if(endCellNum>startCellNum) {
					sheet.addMergedRegion(new CellRangeAddress(row1.getRowNum(),row1.getRowNum(),startCellNum,endCellNum));
				}
				

				//Dept Row
				for(int i = 0; i < deptList.size();i++)
				{
					
					Cell cell2 = row2.createCell(sindex+i*2-1);
					cell2.setCellStyle(cellDataStyle);
					cell2.setCellValue(deptList.get(i));
					
					cell2 = row2.createCell(sindex+i*2);
					cell2.setCellStyle(cellDataStyle);
					
					int startDeptCell = startCellNum + i*2;
					int endDeptCell = startDeptCell + 1;
					sheet.addMergedRegion(new CellRangeAddress(row2.getRowNum(),row2.getRowNum(),startDeptCell,endDeptCell));
					
				}
				
				Cell cell3 = row1.createCell(endCellNum+1);

				cell3.setCellStyle(cellDataStyle_2);
				cell3.setCellValue(totalName);
				
				cell3 = row1.createCell(endCellNum+2);
				cell3.setCellStyle(cellDataStyle_2);
				
				sheet.addMergedRegion(new CellRangeAddress(row1.getRowNum(),row1.getRowNum()+1,endCellNum+1,endCellNum+2));
			}					
		
			catch (Exception e)
			{
				logger.log(Level.INFO, "Cannot get name for college name "+collName);
			}
	}
	
	
	public int appendDataColumns (Row row,int sindex, String count_type,String department,int index,int column_val,int total)
	{
		List<FacultyDeptStaffCount> target_count = null;
		
		//Style:  Center
		CellStyle cellDataStyle =combinStyle(IndexedColors.PALE_BLUE.index,false);
		
		
		target_count = staffList_count.stream().filter(item-> item.getDept_code().equals(department) && 
				item.getNo_of_item_band().equals(count_type)).collect(Collectors.toList());
		try {
			//System.out.println(target_count.size());
			Boolean fehd_cond = (sindex >(flassDeptNames.size() * 2 + 2) && sindex < (flassDeptNames.size() + fehdDeptNames.size() + 3 ) * 2 -1);
			Boolean fhm_cond = (sindex >= (flassDeptNames.size()  + fehdDeptNames.size() + 3 ) * 2 -1);
			
			Cell cell = row.createCell(sindex);
			if(target_count.size() != 0)
			{
				int staff_count = Integer.parseInt(target_count.get(0).getCount_staff());
				cell.setCellStyle(cellDataStyle);
				cell.setCellValue(staff_count);
				
				if (fehd_cond)
					fehd_count_list[index][column_val] = staff_count;
				else if (fhm_cond )
					fhm_count_list[index][column_val] = staff_count;
				else
					dept_count_list[index][column_val] = staff_count;
				
				cell = row.createCell(sindex+1);
				cell.setCellStyle(cellDataStyle);
				cell.setCellValue("("+ (int) PreRptView.roundAvoid( staff_count * 100.0 / total , 0 )+"%)");

				return staff_count;
				
			}
			else {
				if (fehd_cond)
					fehd_count_list[index][column_val] = 0;
				else if (fhm_cond)
					fhm_count_list[index][column_val] = 0;
				else
					dept_count_list[index][column_val] = 0;
				
				cell.setCellValue(0);
				cell = row.createCell(sindex+1);
				cell.setCellStyle(cellDataStyle);
				cell.setCellValue("(%)");
				
			}
					
		}
		catch (Exception e)
		{
			logger.log(Level.INFO, "Cannot get data of the staff count ");
		}
		return 0;	
	}
	
	

	public void appendTitleColumn_a1(Row row1,Row row2, Object obj, String sheetName, String titleString)
	{
		if (row1 != null)
		{
			Cell cell = row1.createCell(row2.getLastCellNum() == -1? 0: row2.getLastCellNum());
			if (obj != null && titleString!=null)
			{
				try
				{	CellStyle cellDataStyle = workbook.createCellStyle();
					cellDataStyle.setAlignment(HorizontalAlignment.CENTER );
					cell.setCellStyle(cellDataStyle);
					cell.setCellValue(titleString);
					Sheet sheet = workbook.getSheet(sheetName);
					int startCellNum = cell.getColumnIndex();
					int endCellNum = startCellNum+ (flassDeptNames.size() + fehdDeptNames.size() +fhmDeptNames.size()+4)*2;
					
					if(showSeq) endCellNum = endCellNum+1;
					
					if(endCellNum>startCellNum) {
						sheet.addMergedRegion(new CellRangeAddress(row1.getRowNum(),row1.getRowNum(),startCellNum, endCellNum));
					}
					
				}
				
				catch (Exception e)
				{
					logger.log(Level.INFO, "Cannot get title " + titleString + " from class " + obj.getClass());
				}
			}
		}
	}
	
	
	
	public void appendDataCollumns_all (String sheetName,reportFilteringField filter , String [] columnAll)
	{			
			try
			
			{
				
				employeeCountGradeList = AppPeriodReportDAO.getEmployeeCountGrade(filter);	
				//System.out.println(filter.getP_id_no());
				
				List<employeeCountGrade> FlassCount = employeeCountGradeList.stream().filter(x->flassDeptNames.contains(x.getDept_code())).collect(Collectors.toList());
				List<employeeCountGrade> fehdCount 	= employeeCountGradeList.stream().filter(x->fehdDeptNames.contains(x.getDept_code())).collect(Collectors.toList());
				List<employeeCountGrade> fhmCount 	= employeeCountGradeList.stream().filter(x->fhmDeptNames.contains(x.getDept_code())).collect(Collectors.toList());
				
				
				Sheet sheet = workbook.getSheet(sheetName); 
				
				sheet.setColumnWidth(0,10*256);
				sheet.setColumnWidth(1,15*256);
				sheet.setColumnWidth(2,25*256);
				sheet.setColumnWidth(3,15*256);
				sheet.setColumnWidth(4,30*256);
				sheet.setColumnWidth(5,15*256);
				
				
				Row row  = sheet.createRow(0);
				for (int j=0 ; j<columnAll.length ; j++) {
					Cell cellData = row.createCell(j);
					cellData.setCellStyle(combinStyle(IndexedColors.TAN.index,false));
					cellData.setCellValue(columnAll[j]);
				}
				
				printExcelData_all(FlassCount,sheet, 1 , columnAll);
				printExcelData_all(fehdCount,sheet, 1+FlassCount.size(),columnAll);
				printExcelData_all(fhmCount,sheet, 1+FlassCount.size()+fehdCount.size(),columnAll);
				
			}					
		
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot import the data into excel sheet");
			}
	}
	
	public CellStyle combinStyle (short colour, boolean coloured) {
		
		CellStyle fcn_dataStyle =workbook.createCellStyle();
		fcn_dataStyle.setAlignment(HorizontalAlignment.CENTER);
		fcn_dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		fcn_dataStyle.setWrapText(true);
		fcn_dataStyle.setBorderBottom(BorderStyle.THIN);
		fcn_dataStyle.setBorderLeft(BorderStyle.THIN);
		fcn_dataStyle.setBorderRight(BorderStyle.THIN);
		fcn_dataStyle.setBorderTop(BorderStyle.THIN);
		
		if( coloured ) {
			fcn_dataStyle.setFillBackgroundColor(colour);
			fcn_dataStyle.setFillForegroundColor(colour);
			fcn_dataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		}
		
		return fcn_dataStyle;
	}
	
	
	
	
	public void printExcelData_all (List<employeeCountGrade> importList,  Sheet insheet, int sind, String [] columnAll) 
	{
		try {
			for (int i = 0 ; i < importList.size();i++) {
					
				Row row  = insheet.createRow(i+sind);
				//CellStyle cellDataStyle = workbook.createCellStyle();
				Double countingCell = 0.0;
				
				for (int j=0 ; j<columnAll.length ; j++) {
					Cell cellData = row.createCell(j);
					cellData.setCellStyle(combinStyle(IndexedColors.TAN.index,false));
						
					if (j==0)
						cellData.setCellValue(importList.get(i).getDept_code());
					else if ( j == 1 )
						cellData.setCellValue(importList.get(i).getLast_name());
					else if ( j == 2)
						cellData.setCellValue(importList.get(i).getOther_name());
					else if ( j == 3)
						cellData.setCellValue(importList.get(i).getPost_rank_code());
					else if (j == 4)
						cellData.setCellValue(Double.parseDouble(importList.get(i).getTotal_number()));
					else
						cellData.setCellValue(importList.get(i).getGrade());
				}
			}
		}
		
		catch (Exception e) {
			logger.log(Level.WARNING,"Cannot print the data into Excel sheet ALL");
		}
	}

}
