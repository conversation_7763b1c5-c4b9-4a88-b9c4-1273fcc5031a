package hk.eduhk.rich.report.form;

import java.awt.Font;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.stream.Collectors;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.usermodel.BorderStyle;

import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.employeeCount;
import hk.eduhk.rich.entity.report.employeeCount_detail;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.report.ReportGroup;
import hk.eduhk.rich.view.PreRptView;


public class KtFormCDCFSummaryGroup extends ReportGroup
{
	
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;

	
	private List<employeeCount_detail> employeeCount ;
	
	private List<AppStaffCount> dept_total_count;
	
	




	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}


	public KtFormCDCFSummaryGroup() 
	{
		
	}
	
	public void init(Workbook workbook, int cur) 
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		
		
		headerStyle = headerRowStyle;
		
		
		activeHeaderFilter = false;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;		

		
		columnNames = new String[] {"For Sorting","Name", "Ranking","Total No. of refereed items (Scholarly Books, Monographs and Chapters & Journal Publications)" };
		
			
		fieldNames = new String[] {"dept_code","employee_number", "last_name", "first_name", "post_rank_code", "output_type","research_type","count_output"}; 		
		cellStyles = new CellStyle[] {null,null, null, null};
	}
	
	
	public void appendDataColumn (String sheetName,String header_year ,String i_dept_list, reportFilteringField filter,boolean all)
	{			
			try
			{
				
				String [] header = {"Common Data Collection Format",
						"Summary of Statistics on Research Output by Staff for "+ header_year +" (Academic Staff Only)"};
				
				String [] footer = { "Starting form 2003/2004, the research outputs of various functional holders including President, Vice Presidents, Deans and Heads of Centres were listed according to their affiliated departments. \r\n"
						+ "The research outputs of Prof Cheung Yan Leung, Stephen (P) were included under APS.",
						"R refers to \"Academic Research: Refereed\", NR refers to \"Academic Research: Non-refereed\", C refers to \"Contract Research\" and O refers to \"Other Outputs\"",
						"In accordance with the UGC's CDCF guidelines, the co-authorship is taken into account by pro-rating the count by the number of authors. For example, a paper with four authors will count as 0.25 for each author.",
						"Value of count of individual item may not add up to the value of sub-total/total due to rounding of figures." };
				
				String [] columnHeader_1 = {"","Name", "Ranking","Total No. of refereed items (Scholarly Books, Monographs and Chapters & Journal Publications)",
										  "Scholarly Books, Monographs and Chapters","Journal Publications",
										  "Conference Papers","Creative and Literary Works, Consulting Reports and Case Studies",
										  "Patents, Agreements, Assignments and Companies","All Other Outputs"};
				
				String [] columnHeader_2 = {"R","NR","C","O"};
				
				employeeCount = AppPeriodReportDAO.getEmployeeCount_details(filter);
				
				org.apache.poi.ss.usermodel.Font font=  workbook.createFont();
				font.setBold(true);
				
				CellStyle headerStyle = workbook.createCellStyle(); 
				headerStyle.setAlignment(HorizontalAlignment.LEFT);
				headerStyle.setFont(font);
				
				CellStyle chStyle = workbook.createCellStyle(); 
				chStyle.setAlignment(HorizontalAlignment.CENTER);
				chStyle.setVerticalAlignment(VerticalAlignment.CENTER);
				chStyle.setBorderBottom(BorderStyle.THIN);
				chStyle.setBorderLeft(BorderStyle.THIN);
				chStyle.setBorderRight(BorderStyle.THIN);
				chStyle.setBorderTop(BorderStyle.THIN);
				chStyle.setFont(font);
				chStyle.setWrapText(true);

				int total_counts = 0;
				
				List<employeeCount_detail> FlassCount;
				if (all) 
						FlassCount = employeeCount;
				else
					 	FlassCount = employeeCount.stream().filter(x->x.getDept_code().equalsIgnoreCase(i_dept_list)).collect(Collectors.toList());
				
				Sheet sheet = workbook.getSheet(sheetName); 
				
				sheet.setColumnWidth(1,10*256);
				sheet.setColumnWidth(2,25*256);
				sheet.setColumnWidth(3,15*256);
				sheet.setColumnWidth(4,30*256);
				
				Row row = sheet.createRow(0);
				Cell cellData = row.createCell(0);
				cellData.setCellStyle(headerStyle);
				cellData.setCellValue(header[0]);
				
				row = sheet.createRow(1);
				cellData = row.createCell(0);
				cellData.setCellStyle(headerStyle);
				cellData.setCellValue(header[1]);
				
				row = sheet.createRow(2);
				cellData = row.createCell(0);
				cellData.setCellStyle(headerStyle);
				if (all)  
					cellData.setCellValue("ALL Deparments");
				else {
					String deptHeader  = AppPeriodReportDAO.getDeptDesc("", i_dept_list, true, true).get(0).getDepartment_name();
					cellData.setCellValue(deptHeader);
				}
					
				
				row = sheet.createRow(total_counts+4);
				row.setHeightInPoints(2*20);
				Row row_2 = sheet.createRow(total_counts+5);
				
				for (int j = 0 ; j < columnHeader_1.length ; j ++ ) {
					
					if(j == 0 || j == 1 ) {
						cellData = row.createCell(2);
						cellData.setCellStyle(chStyle);

						cellData = row_2.createCell(j);
						cellData.setCellStyle(chStyle);
						
						cellData = row.createCell(j);
						
						
					}
					else if (j == 2 || j == 3) {
						
						cellData = row_2.createCell(j+1);
						cellData.setCellStyle(chStyle);
						
						cellData = row.createCell(j+1);
					}
					else {
						for(int k = 0; k< columnHeader_2.length ;k++) {
							Cell cellRNCO = row_2.createCell(j+ 3*(j-4)+1 +k);
							cellRNCO.setCellStyle(chStyle);
							cellRNCO.setCellValue(columnHeader_2[k]);
							
							cellRNCO = row.createCell(j+ 3*(j-4)+1 +k);
							cellRNCO.setCellStyle(chStyle);
							
						}
						cellData = row.createCell(j+ 3*(j-4)+1);
						
					}
					
					cellData.setCellStyle(chStyle);
					cellData.setCellValue(columnHeader_1[j]);
					
					if (j == 3  && i_dept_list.equalsIgnoreCase("CCA") )
						cellData.setCellValue("Total No. of refereed items (Scholarly Books, Monographs and Chapters & Journal Publications & Non-traditional outputs)");
					
					
					if (j==1)
						sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j , j+1 ));
					else if (j==0)
						sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j , j ));
					else if (j== 2 || j == 3)
						sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j+1 , j+1 ));
					else
						sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum() , j+ 3*(j-4)+1 , j+ 3*(j-4)+4 ));
				}

				printExcelData_all(FlassCount,sheet,total_counts+6,all);
				
				if( ! all) {

				total_counts += FlassCount.size();
				
				row = sheet.createRow(total_counts+8);
				cellData = row.createCell(0);
				cellData.setCellValue("Notes");
				
				row = sheet.createRow(total_counts+9);
				cellData = row.createCell(0);
				cellData.setCellValue("Note 1:");
				cellData = row.createCell(1);
				/*if (i_dept_list.equalsIgnoreCase("APS"))
					cellData.setCellValue(footer[0]);
				else*/
					cellData.setCellValue(footer[1]);
				
				
				row = sheet.createRow(total_counts+10);
				cellData = row.createCell(0);
				cellData.setCellValue("Note 2:");
				cellData = row.createCell(1);
				/*if (i_dept_list.equalsIgnoreCase("APS"))
					cellData.setCellValue(footer[1]);
				else*/
					cellData.setCellValue(footer[2]);
				
				row = sheet.createRow(total_counts+11);
				cellData = row.createCell(0);
				cellData.setCellValue("Note 3:");
				cellData = row.createCell(1);
				/*if (i_dept_list.equalsIgnoreCase("APS"))
					cellData.setCellValue(footer[2]);
				else*/
					cellData.setCellValue(footer[3]);
				
				/*if (i_dept_list.equalsIgnoreCase("APS")) {
					row = sheet.createRow(total_counts+12);
					cellData = row.createCell(0);
					cellData.setCellValue("Note 4:");
					cellData = row.createCell(1);
					cellData.setCellValue(footer[3]);
				}*/
				}
			}					
		
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot import the data into excel sheet");
			}
	}
	
	
	
	
	
	public void printExcelData_all (List<employeeCount_detail> importList,  Sheet insheet, int sind , boolean all) 
	{
		try {
			
			double[] totalCounter = new double[24];
			double totlaRow = 0.0;
			
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			cellStyle.setBorderBottom(BorderStyle.THIN);
			cellStyle.setBorderLeft(BorderStyle.THIN);
			cellStyle.setBorderRight(BorderStyle.THIN);
			cellStyle.setBorderTop(BorderStyle.THIN);
			
				
			CellStyle exceptStyle = workbook.createCellStyle();
			exceptStyle.setAlignment(HorizontalAlignment.LEFT);
			exceptStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			exceptStyle.setBorderBottom(BorderStyle.THIN);
			exceptStyle.setBorderLeft(BorderStyle.THIN);
			exceptStyle.setBorderRight(BorderStyle.THIN);
			exceptStyle.setBorderTop(BorderStyle.THIN);
			
			
			for (int i = 0 ; i < importList.size();i++) {
				
					Row row  = insheet.createRow(i+sind);
					double totalRefer = 0.0;
					
					for (int j=0 ; j < 29 ; j++) {
						
						Cell cellData = row.createCell(j);
						cellData.setCellStyle(cellStyle);
						String dataValue ="";

						if (j == 0)
							if (all) 
								cellData.setCellValue(importList.get(i).getDept_code());
							else 
								cellData.setCellValue(i+1);
						else if ( j == 1 ) {
							
							cellData.setCellStyle(exceptStyle);
							cellData.setCellValue(importList.get(i).getLast_name());
						}
						else if ( j == 2) {
							cellData.setCellStyle(exceptStyle);
							cellData.setCellValue(importList.get(i).getFirst_name());
						}
						else if ( j == 3)
							cellData.setCellValue(importList.get(i).getPost_rank_code());
						
						//Scholarly Books, Monographs and Chapters
						if (j > 4) {
							if (j == 5) {
								dataValue = importList.get(i).getSr_output();
								totalRefer += Double.parseDouble(dataValue);
								totalCounter[0] += Double.parseDouble(dataValue);
							}
								
							else if (j == 6) {
								dataValue = importList.get(i).getSnr_output();
								totalCounter[1] += Double.parseDouble(dataValue);
							}
							else if (j == 7) {
								dataValue = importList.get(i).getSc_output();
								totalCounter[2] += Double.parseDouble(dataValue);
							}
							else if (j == 8) {
								dataValue = importList.get(i).getSo_output();
								totalCounter[3] += Double.parseDouble(dataValue);
							}
							
							//Journal Publications
							else if (j == 9) {
								dataValue = importList.get(i).getJr_output();
								
								//System.out.println(dataValue);
								totalRefer += Double.parseDouble(dataValue);
								totalCounter[4] += Double.parseDouble(dataValue);
							
							}
							else if (j == 10) {
								dataValue = importList.get(i).getJnr_output();
								totalCounter[5] += Double.parseDouble(dataValue);
							}
							else if (j == 11) {
								dataValue = importList.get(i).getJc_output();
								totalCounter[6] += Double.parseDouble(dataValue);
							}
							else if (j == 12) {
								dataValue = importList.get(i).getJo_output();
								totalCounter[7] += Double.parseDouble(dataValue);
							}
							//Conference Papers
							else if (j == 13) {
								dataValue = importList.get(i).getCr_output();
								totalCounter[8] += Double.parseDouble(dataValue);
							}
							else if (j == 14) {
								dataValue = importList.get(i).getCnr_output();
								totalCounter[9] += Double.parseDouble(dataValue);
							}
							else if (j == 15) {
								dataValue = importList.get(i).getCc_output();
								totalCounter[10] += Double.parseDouble(dataValue);
							}
							else if (j == 16) {
								dataValue = importList.get(i).getCo_output();
								totalCounter[11] += Double.parseDouble(dataValue);
							}
							
							// Creative and Literary Works, Consulting Reports and Case Studies
							else if (j == 17) {
								dataValue = importList.get(i).getCrr_output();
								totalCounter[12] += Double.parseDouble(dataValue);
								
								if (importList.get(i).getDept_code().equalsIgnoreCase("CCA"))
									totalRefer += Double.parseDouble(dataValue);
									
							}
							else if (j == 18) {
								dataValue = importList.get(i).getCrnr_output();
								totalCounter[13] += Double.parseDouble(dataValue);
							}
							else if (j == 19) {
								dataValue = importList.get(i).getCrc_output();
								totalCounter[14] += Double.parseDouble(dataValue);
							}
							else if (j == 20) {
								dataValue = importList.get(i).getCro_output();
								totalCounter[15] += Double.parseDouble(dataValue);
							}
							
							//Patents, Agreements, Assignments and Companies
							else if (j == 21) {
								dataValue = importList.get(i).getPr_output();
								totalCounter[16] += Double.parseDouble(dataValue);
							}
							else if (j == 22) {
								dataValue = importList.get(i).getPnr_output();
								totalCounter[17] += Double.parseDouble(dataValue);
							}
							else if (j == 23) {
								dataValue = importList.get(i).getPc_output();
								totalCounter[18] += Double.parseDouble(dataValue);
							}
							else if (j == 24) {
								dataValue = importList.get(i).getPo_output();
								totalCounter[19] += Double.parseDouble(dataValue);
							}
							
							//All other outputs	
							else if (j == 25) {
								dataValue = importList.get(i).getOr_output();
								totalCounter[20] += Double.parseDouble(dataValue);
							}
							else if (j == 26) {
								dataValue = importList.get(i).getOnr_output();
								totalCounter[21] += Double.parseDouble(dataValue);
							}
							else if (j == 27) {
								dataValue = importList.get(i).getOc_output();
								totalCounter[22] += Double.parseDouble(dataValue);
							}
							else if (j == 28) {
								dataValue = importList.get(i).getOo_output();
								totalCounter[23] += Double.parseDouble(dataValue);
							}						
							if (dataValue.equalsIgnoreCase("0"))
								cellData.setCellValue("");
							else
								cellData.setCellValue(PreRptView.roundAvoid(Double.parseDouble(dataValue),2));
						}
					}
					Cell totalRefCell = row.createCell(4);
					totalRefCell.setCellStyle(cellStyle);
					totalRefCell.setCellValue(PreRptView.roundAvoid(totalRefer,2));
					totlaRow += totalRefer;
			
			}
			
			Row row_total  = insheet.createRow(importList.size()+sind);
			
			
			Cell totalRowCell ;
			for (int i = 1 ; i <4;i++) {
				totalRowCell= row_total.createCell(i);
				totalRowCell.setCellStyle(cellStyle);
			}
			
			totalRowCell= row_total.createCell(1);
			totalRowCell.setCellStyle(cellStyle);
			totalRowCell.setCellValue("Total");
			
			insheet.addMergedRegion(new CellRangeAddress(row_total.getRowNum(),row_total.getRowNum() , 1 , 3 ));
			
			totalRowCell = row_total.createCell(4);
			totalRowCell.setCellStyle(cellStyle);
			totalRowCell.setCellValue(PreRptView.roundAvoid(totlaRow,2));
			
			for (int i = 0 ; i<totalCounter.length ; i++) {
				totalRowCell = row_total.createCell(5+i);
				totalRowCell.setCellStyle(cellStyle);
				totalRowCell.setCellValue(PreRptView.roundAvoid(totalCounter[i],2));
			}
		}
		catch (Exception e) {
			logger.log(Level.WARNING,"Cannot print the data into Excel sheet ALL");
		}
	}
	
	
	@Override
	public void init(Workbook workbook)
	{
		// TODO Auto-generated method stub
		
	}
	
	
	
	
	
}
