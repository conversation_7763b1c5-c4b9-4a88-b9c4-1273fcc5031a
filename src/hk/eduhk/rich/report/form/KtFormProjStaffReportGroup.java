package hk.eduhk.rich.report.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.stream.Collectors;


import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;


import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.StaffProjCount;
import hk.eduhk.rich.entity.report.a4ReportCountAmt;
import hk.eduhk.rich.entity.report.a4ReportField;
import hk.eduhk.rich.entity.report.employeeCount_detail;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.entity.report.staffNumberName;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffEligible;
import hk.eduhk.rich.report.ReportGroup;
import hk.eduhk.rich.view.PreRptView;


public class KtFormProjStaffReportGroup extends ReportGroup
{
	
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;

	
	private List<StaffProjCount> 	employeeCount ;
	private List<staffNumberName> 	employeeList;
	
	
	private List<AppStaffCount> dept_total_count;
	
	

	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	


	public KtFormProjStaffReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook, int cur_year) 
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		Font font = workbook.createFont();
		font.setBold(true);
		headerRowStyle.setFont(font);
				
		headerStyle = headerRowStyle;
		
		
		activeHeaderFilter = false;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;		
		
		columnNames = new String[] {"Department","Last Name","First Name", "Ranking", "No. of Project","Value of Project"};
		fieldNames = new String[] {"dept_code","last_name", "first_name", "post_rank_code", "proj_count", "proj_amt"}; 		
		cellStyles = new CellStyle[] {null,null, null, null, null,null,null};
	}
	
	
	
	public void appendCollegeData (String sheetName, String header_year, String i_dept,reportFilteringField filter,boolean all, String footer_period) 
	{
		try {
			
			String h_department = "";
			List<a4ReportField> employeeCount_enh =  new ArrayList();
			
			String [] header = {"Common Data Collection Format",
								"Summary of Statistics on Research and Development Projects by Staff for "+header_year+" (Academic Staff)"};
			
	    	String[] columnHeader_1 = {"","Name of Principal Investigator ",	"Ranking ","New Project* (Y/N) ",
	    			"RGC - General Research Fund (GRF) ",
	    			"RGC - Early Career Scheme (ECS) ",
	    			"UGC - Other Specific Funds/Earmarked Grants ",
	    			"HKSAR Govt - Quality Education Fund (QEF) ",
	    			"HKSAR Govt - Education Bureau ",
	    			"HKSAR Govt-  Innovative and Technology Fund (ITF) ",
	    			"HKSAR Government ",
	    			"HKSAR Government Related Organizations ",
	    			"HK Private Fund - Charities/Foundations ",
	    			"HK Private Fund - Industries ",
	    			"HK Private Fund - Others ",
	    			"Non-HK ",
	    			"Funding from Other UGC-funded Universities ",
	    			"EdUHK - Internal Research Grant (from UGC Block Grant) ",
	    			"EdUHK - Departmental Fund ",
	    			"EdUHK - Faculty  Fund ",
	    			"EdUHK - Knowledge Transfer Fund ",
	    			"EdUHK - Others ",
	    			"Total "};
	    	
	    	
	    	String [] columnHeader_2 = {"No of Project ","Value of Project "};
	    	
	    	
	    	String[] columnHeader_3 = {"",
	    			"RGC - General Research Fund (GRF) ",
	    			"RGC - Early Career Scheme (ECS) ",
	    			"UGC - Other Specific Funds/Earmarked Grants ",
	    			"HKSAR Govt - Quality Education Fund (QEF) ",
	    			"HKSAR Govt - Education Bureau ",
	    			"HKSAR Govt-  Innovative and Technology Fund (ITF) ",
	    			"HKSAR Government ",
	    			"HKSAR Government Related Organizations ",
	    			"HK Private Fund - Charities/Foundations ",
	    			"HK Private Fund - Industries ",
	    			"HK Private Fund - Others ",
	    			"Non-HK ",
	    			"Funding from Other UGC-funded Universities ",
	    			"EdUHK - Internal Research Grant (from UGC Block Grant) ",
	    			"EdUHK - Departmental Fund ",
	    			"EdUHK - Faculty  Fund ",
	    			"EdUHK - Knowledge Transfer Fund ",
	    			"EdUHK - Others ",
	    			"Total "};
	    	
	    	String [] fundingSource = {	"'9997'",
										"'10005'",
										"'10007','110','150','10010','10014','10008','1560','10011','10006','10019','10009','10013','10012','10004','1550','1520','115','10032','200','1590','1530','1535','2030','250','10021','10015','1500','1604','1605'",
										"'310'",
										"'925','510'",
										"'10001'",
										"'1621','10022','610','410','10023','1635','1645','710','10024','10025','10026','10027'",
										"'10028','10029','10030','10031','1680'",
										"'900'",
										"'830'",
										"'880','1570'",
										"'1400','2000'",
										"'1581','1015','10003'",
										"'890','920','10016','10020','10017','9998'",
										"'1600','950','980'",
										"'940','935','9999'",
										"'10002'",
										"'1540','1650','855','1510','10018','970','990','1100','1660','1515','1300','1010','1200','2020'"
	    	};
	    	
			int total_counts = 0;
				
				// Write Header
				
				Sheet sheet = workbook.getSheet(sheetName); 

				sheet.setColumnWidth(0,7*256);
				
				if(!all) {
					sheet.setColumnWidth(1,15*256);
					sheet.setColumnWidth(2,25*256);
					sheet.setColumnWidth(3,15*256);
					/*sheet.setColumnWidth(4,20*256);
					
					for(int i=5;i<43;i++) 
						sheet.setColumnWidth(i,27*256);	*/
					
						
				}

				Row row = sheet.createRow(total_counts);
				Cell cellData = row.createCell(0);
				cellData.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,true,false));
				cellData.setCellValue(header[0]);
				
				
				row = sheet.createRow(total_counts+1);
				cellData = row.createCell(0);
				cellData.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,true,false));
				cellData.setCellValue(header[1]);
				
				if (! all)
				{
					row = sheet.createRow(total_counts+2);
					cellData = row.createCell(0);
					
					String deptHeader  = AppPeriodReportDAO.getDeptDesc("", i_dept, true, true).get(0).getDepartment_name();
					
					cellData.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,true,false));
					cellData.setCellValue(deptHeader);
				}
				
				
				//---------------------------------------------
				//Write Column header//
				
				row = sheet.createRow(total_counts+4);
				row.setHeight((short) (65*20));
				Row row_2 = sheet.createRow(total_counts+5);
				if (!all) {
					for (int j = 0 ; j < columnHeader_1.length ; j ++ ) {
						
						if(j == 0 || j == 1 )
							cellData = row.createCell(j);
						else if (j == 2||j == 3) {
							cellData = row.createCell(j+1);
						}
						else {
							cellData = row.createCell(j+ (j-3));
								for(int k = 0; k< columnHeader_2.length ;k++) {
								Cell cellRNCO = row_2.createCell(j+ (j-3)+k);
								cellRNCO.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
								cellRNCO.setCellValue(columnHeader_2[k]);
							}
							
						}
						cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
						cellData.setCellValue(columnHeader_1[j]);	
						
						if (j==1) {
							cellData = row.createCell(j+1);
							cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
							cellData = row_2.createCell(j+1);
							cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
							sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j , j+1 ));
						}
						else if (j==0) {
							cellData = row_2.createCell(j);
							cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
							sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j , j ));
						}
						else if (j==2||j==3) {
							cellData = row_2.createCell(j+1);
							cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
							sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j+1 , j+1 ));
						}
						else {
								cellData = row.createCell(j+ (j-3)+1);
								cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
								sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum() , j+ (j-3) , j+ (j-3)+1 ));
						}
					}
				}
				else {
					for (int j = 0 ; j < columnHeader_3.length ; j ++ ) {
						if(j == 0  )
							cellData = row.createCell(j);
						else {
							cellData = row.createCell(2*j-1);
							for(int k = 0; k< columnHeader_2.length ;k++) {
								Cell cellRNCO = row_2.createCell(2*j-1+k);
								cellRNCO.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
								cellRNCO.setCellValue(columnHeader_2[k]);
							}
							
						}
						cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
						cellData.setCellValue(columnHeader_3[j]);	
	
						
						if (j==0) {
							cellData = row_2.createCell(j);
							cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
							sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , j , j ));
						}
						else {
							cellData = row.createCell(2*j);
							cellData.setCellStyle(combinStyle(IndexedColors.WHITE.index,true,true,true));
							sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum() , 2*j-1 , 2*j ));
						}
					}
				}
					
				for(int i = 0; i< fundingSource.length ;i++) {
					List<a4ReportField> tmpReportFieldList = AppPeriodReportDAO.getStaffProjCount_enh(filter,i_dept,fundingSource[i],all);
					for(int j = 0 ; j < tmpReportFieldList.size();j++) {
						if (i==0)
							employeeCount_enh.add(tmpReportFieldList.get(j));
						else
							employeeCount_enh.get(j).getFund_source().add(tmpReportFieldList.get(j).getFund_source().get(0));
					}
				}
				printDataRow(employeeCount_enh,sheet,total_counts+6,all);
				total_counts = employeeCount_enh.size() +2 ;

				if (! all) {
					row = sheet.createRow(total_counts+6);
					cellData = row.createCell(0);
					cellData.setCellValue("* New research projects which have been awarded to start in the period of "+footer_period);
				}
		}					
		
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot import the data into excel sheet");
		}
	}
	
	
	public void printDataRow (List<a4ReportField> importList, Sheet insheet, int sind,boolean all) 
	{
		try {
			
			int [] totalColumnCounter = new int[19];
			double []  totalColumnAmt = new double[19];

			
			String dup_staff = "";
			int dup_count = 0;
			
			
			for (int i = 0 ; i < importList.size();i++) {
					
					Row row  = insheet.createRow(i+sind);
					int row_counter  = 0;
					double row_amt   = 0.0;
					int index = i + 1 - dup_count;
					int m = 4;
					int j_index = 43;
					
					if(! all) {
						//Chk duplicated
						if (! dup_staff.equalsIgnoreCase(importList.get(i).getEmployee_number()))
							dup_staff = importList.get(i).getEmployee_number();
						else {
							index = i-dup_count;
							dup_count ++;
						} 
					}
					else {
						m = 0;
						j_index = 39;
					}
						
					
					for (int j=0 ; j < j_index ; j++) {
							Cell cellData = row.createCell(j);
							cellData.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,false,true));
							String dataValue ="";
							
							if(! all) {
								if (j == 0)
									dataValue = String.valueOf(index);
								else if ( j == 1 )
									dataValue = importList.get(i).getLast_name();
								else if ( j == 2)
									dataValue = importList.get(i).getFirst_name();
								else if ( j == 3)
									dataValue = importList.get(i).getPost_rank_code();
								else if ( j == 4)
									dataValue = importList.get(i).getNew_project();
								
							}
							else if ( all && j == 0 )
									dataValue = importList.get(i).getDept_code();									

							if (j == m + 1) {
								
								dataValue = importList.get(i).getFund_source().get(0).getCount();
								totalColumnCounter[0] += Integer.parseInt(dataValue);
							}
							
							if (j == m + 2) {
								dataValue = importList.get(i).getFund_source().get(0).getAmount();
								totalColumnAmt[0] += Double.parseDouble(dataValue);
							}
	
								
							if (j == m + 3) {
								dataValue = importList.get(i).getFund_source().get(1).getCount();
								totalColumnCounter[1] += Integer.parseInt(dataValue);
							}
							if (j == m + 4) {
								dataValue = importList.get(i).getFund_source().get(1).getAmount();
								totalColumnAmt[1] += Double.parseDouble(dataValue);
							}
							
							if (j == m + 5) {
								dataValue = importList.get(i).getFund_source().get(2).getCount();
								totalColumnCounter[2] += Integer.parseInt(dataValue);
							}
							else if (j == m + 6) {
								dataValue = importList.get(i).getFund_source().get(2).getAmount();
								totalColumnAmt[2] += Double.parseDouble (dataValue);
							}
							
							else if (j == m + 7) {
								dataValue = importList.get(i).getFund_source().get(3).getCount();
								totalColumnCounter[3] += Integer.parseInt(dataValue);
							}
							else if (j == m + 8) {
								dataValue = importList.get(i).getFund_source().get(3).getAmount();
								totalColumnAmt[3] += Double.parseDouble(dataValue);
							}
							
							else if (j == m + 9) {
								dataValue = importList.get(i).getFund_source().get(4).getCount();
								totalColumnCounter[4] += Integer.parseInt(dataValue);
							}
							else if (j == m + 10) {
								dataValue = importList.get(i).getFund_source().get(4).getAmount();
								totalColumnAmt[4] += Double.parseDouble(dataValue);
							}
							
							else if (j == m + 11) {
								dataValue = importList.get(i).getFund_source().get(5).getCount();
								totalColumnCounter[5] += Integer.parseInt(dataValue);
							}
							else if (j == m + 12) {
								dataValue = importList.get(i).getFund_source().get(5).getAmount();
								totalColumnAmt[5] += Double.parseDouble(dataValue);
							}
							
							else if (j == m + 13) {
								dataValue = importList.get(i).getFund_source().get(6).getCount();
								totalColumnCounter[6] += Integer.parseInt(dataValue);
							}
							else if (j == m + 14) {
								dataValue = importList.get(i).getFund_source().get(6).getAmount();
								totalColumnAmt[6] += Double.parseDouble(dataValue);
							}
							
							else if (j == m + 15) {
								dataValue = importList.get(i).getFund_source().get(7).getCount();
								totalColumnCounter[7] += Integer.parseInt(dataValue);
							}
							else if (j == m + 16) {
								dataValue = importList.get(i).getFund_source().get(7).getAmount();
								totalColumnAmt[7] += Double.parseDouble(dataValue);
							}
							
							else if (j == m + 17) {
								dataValue = importList.get(i).getFund_source().get(8).getCount();
								totalColumnCounter[8] += Integer.parseInt(dataValue);
							}
							else if (j == m + 18) {
								dataValue = importList.get(i).getFund_source().get(8).getAmount();
								totalColumnAmt[8] += Double.parseDouble(dataValue);
							}
							
							else if (j == m + 19) {
								dataValue = importList.get(i).getFund_source().get(9).getCount();
								totalColumnCounter[9] += Integer.parseInt(dataValue);
							}
							else if (j == m + 20) {
								dataValue = importList.get(i).getFund_source().get(9).getAmount();
								totalColumnAmt[9] 		+= Double.parseDouble(dataValue);
							}
								
							else if (j == m + 21) {
								dataValue = importList.get(i).getFund_source().get(10).getCount();
								totalColumnCounter[10] 	+= 	Integer.parseInt(dataValue);
							}
							else if (j == m + 22) {
								dataValue = importList.get(i).getFund_source().get(10).getAmount();
								totalColumnAmt[10] 		+= 	Double.parseDouble(dataValue);
							}
							
							else if (j == m + 23) {
								dataValue = importList.get(i).getFund_source().get(11).getCount();
								totalColumnCounter[11] 	+= 	Integer.parseInt(dataValue);
							}
							else if (j == m + 24) {
								dataValue = importList.get(i).getFund_source().get(11).getAmount();
								totalColumnAmt[11] 		+= 	Double.parseDouble(dataValue);
							}	
							
							else if (j == m + 25) {
								dataValue = importList.get(i).getFund_source().get(12).getCount();
								totalColumnCounter[12] 	+= 	Integer.parseInt(dataValue);
							}
							else if (j == m + 26) {
								dataValue = importList.get(i).getFund_source().get(12).getAmount();
								totalColumnAmt[12] 		+= 	Double.parseDouble(dataValue);
							}
							
							else if (j == m + 27) {
								dataValue = importList.get(i).getFund_source().get(13).getCount();
								totalColumnCounter[13] 	+= Integer.parseInt(dataValue);
							}
							else if (j == m + 28) {
								dataValue = importList.get(i).getFund_source().get(13).getAmount();
								totalColumnAmt[13] 		+= Double.parseDouble(dataValue);
							}	
							
							else if (j == m + 29) {
								dataValue = importList.get(i).getFund_source().get(14).getCount();
								totalColumnCounter[14] 	+= Integer.parseInt(dataValue);
							}
							else if (j == m + 30) {
								dataValue = importList.get(i).getFund_source().get(14).getAmount();
								totalColumnAmt[14] 		+= Double.parseDouble(dataValue);
							}	
							else if (j == m + 31) {
								dataValue = importList.get(i).getFund_source().get(15).getCount();
								totalColumnCounter[15] 	+= Integer.parseInt(dataValue);
							}
							else if (j == m + 32) {
								dataValue = importList.get(i).getFund_source().get(15).getAmount();
								totalColumnAmt[15] 		+= Double.parseDouble(dataValue);
							}	
							else if (j == m + 33) {
								dataValue = importList.get(i).getFund_source().get(16).getCount();
								totalColumnCounter[16] += Integer.parseInt(dataValue);
							}
							else if (j == m + 34) {
								dataValue = importList.get(i).getFund_source().get(16).getAmount();
								totalColumnAmt[16] 		+= Double.parseDouble(dataValue);
							}	
							else if (j == m + 35) {
								dataValue = importList.get(i).getFund_source().get(17).getCount();
								totalColumnCounter[17] 	+= Integer.parseInt(dataValue);
							}
							else if (j == m + 36) {
								dataValue = importList.get(i).getFund_source().get(17).getAmount();
								totalColumnAmt[17] 	+= Double.parseDouble(dataValue);
							}
							
							
							//Display control
							if (dataValue.equalsIgnoreCase("0"))
								cellData.setCellValue("");
							else {
								if (! all) {
									if ( j > m && j <=40 ) {
										
											if ((j % 2)!= 0 ) {
												row_counter += Integer.parseInt(dataValue);									
												cellData.setCellValue(Integer.parseInt(dataValue));					
											}
											else {
												
												row_amt += Double.parseDouble(dataValue);
												cellData.setCellValue(PreRptView.roundAvoid(Double.parseDouble(dataValue),0));
											}	
									}
									else if(j == 0)
										cellData.setCellValue(Integer.parseInt(dataValue));
									else
										cellData.setCellValue(dataValue);
								}
								else {
									if ( j > 0 && j < 37 ) {
										
										if ((j % 2) != 0 ) {
											row_counter += Integer.parseInt(dataValue);									
											cellData.setCellValue(Integer.parseInt(dataValue));					
										}
										else {
											
											row_amt += Double.parseDouble(dataValue);
											cellData.setCellValue(PreRptView.roundAvoid(Double.parseDouble(dataValue),0));
										}	
									}
									else
										cellData.setCellValue(dataValue);
								}
						   }
					
					}
					
					if (!all) 
						m = 41;
					else 
						m = 37;
					
					
					Cell totalRefCell = row.createCell(m);
					totalRefCell.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,false,true));
					totalColumnCounter[18] += row_counter;
					totalRefCell.setCellValue(row_counter);
					
					totalRefCell = row.createCell(m+1);
					totalRefCell.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,false,true));
					totalColumnAmt[18] += row_amt;
					totalRefCell.setCellValue(PreRptView.roundAvoid(row_amt,0));
			}
			
			
			
			Row row_total  = insheet.createRow(importList.size()+sind);
			
			int totalRowCellPos = 2;
			if (all)
				totalRowCellPos = 0;
			Cell totalRowCell = row_total.createCell(totalRowCellPos);
			totalRowCell.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,false,true));
			totalRowCell.setCellValue("Total");
			
			int m = 0;
			
			if (!all) 
				m = 5;
			else 
				m = 1;
			
			for (int i = 0 ; i<totalColumnCounter.length ; i++) {
				totalRowCell = row_total.createCell(m+i*2);
				totalRowCell.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,false,true));
				totalRowCell.setCellValue(totalColumnCounter[i]);
			}
			for (int i = 0 ; i<totalColumnAmt.length ; i++) {
				totalRowCell = row_total.createCell(m+1+i*2);
				totalRowCell.setCellStyle(combinStyle(IndexedColors.PALE_BLUE.index,false,false,true));
				totalRowCell.setCellValue(PreRptView.roundAvoid(totalColumnAmt[i],0));
			}
		}
		catch (Exception e) {
			//CAse One : Integer Error on String 
			
			logger.log(Level.WARNING,"Cannot print the data into Excel sheet ALL");
		}
	}
	
	
	public CellStyle combinStyle (short colour, boolean coloured,boolean bolded, boolean bordered) {
		
		org.apache.poi.ss.usermodel.Font font=  workbook.createFont();
		font.setBold(true);
		
		CellStyle fcn_dataStyle =workbook.createCellStyle();
		
		
		fcn_dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		


		
		if( coloured ) {
			fcn_dataStyle.setFillBackgroundColor(colour);
			fcn_dataStyle.setFillForegroundColor(colour);
			fcn_dataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			fcn_dataStyle.setWrapText(true);
		}
		
		if (bolded) 
			fcn_dataStyle.setFont(font);
		
		
		if (bordered) {
			fcn_dataStyle.setAlignment(HorizontalAlignment.CENTER);
			fcn_dataStyle.setBorderBottom(BorderStyle.THIN);
			fcn_dataStyle.setBorderLeft(BorderStyle.THIN);
			fcn_dataStyle.setBorderRight(BorderStyle.THIN);
			fcn_dataStyle.setBorderTop(BorderStyle.THIN);
			//fcn_dataStyle.setWrapText(true);
		}
		
		return fcn_dataStyle;
	}
		


	
	@Override
	public void init(Workbook workbook)
	{
		// TODO Auto-generated method stub
		
	}
	
	
	
	
	
}
