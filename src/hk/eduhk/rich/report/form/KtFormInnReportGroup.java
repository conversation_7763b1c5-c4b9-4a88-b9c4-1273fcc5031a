package hk.eduhk.rich.report.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.report.ReportGroup;

public class KtFormInnReportGroup extends ReportGroup
{
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}
	
	public KtFormInnReportGroup()
	{
		
	}
	
	public void init(Workbook workbook)  
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("DD-MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		List<Function> functionList = new ArrayList<Function>();
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		headerStyle = headerRowStyle;
		activeHeaderFilter = true;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;
		
		columnNames = new String[] {"Form Number" ,getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
									"Any Research Element in the Project?", "Ownership of IP Right", "Percentage of IP Right Ownership", "Project Title", 
									"Name(s) of Collaborator(s)", getResourceBundle().getString("kt.form.label.act.code"), 
									getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"),
									"Total Number of Project Days", "Total Number of Project Days in the Reporting Year",
									"Principal Investigator", "Funding Source", "Total Approved Budget (HK$)",
									"Income (HK$)", 
									"Number of Key Partners", "Number of Teachers Benefited", "Number of Principals Benefited", "Number of Other Stakeholders Benefited (e.g. parents)",
									"Number of Schools Benefited", "Number of Students Benefited", "Remarks"};
		
		fieldNames = new String[] {	"pk.form_no", "fac", "dept", 
									"research_element", "ownership_ip_right", "percentage_ownership", "title", 
									"name_colla", "act_code", 
									"start_date" , "end_date",
									"num_proj_day", "num_proj_day_in_yr",
									"principal_inves", "fund_src", "budget",
									"income_rpt_unit", 
									"num_key_partner", "num_teacher", "num_principal", "num_stakeholder",
									"num_school", "num_stu", "remarks_staff"};
		
		cellStyles = new CellStyle[] {	null, textCellStyle, textCellStyle, 
										textCellStyle, textCellStyle, numberCellStyle, textCellStyle, 
										textCellStyle, textCellStyle, 
										dateCellStyle, dateCellStyle,
										textCellStyle, textCellStyle,
										textCellStyle, numberCellStyle, numberCellStyle, 
										numberCellStyle, 
										numberCellStyle, numberCellStyle, numberCellStyle, numberCellStyle,
										numberCellStyle, numberCellStyle, textCellStyle};
		if(isRdoAdmin) {
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Supported by FO?")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "support_fo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Income Remarks From FO")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_fo_rem")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Income Matching Grant")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_grant")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, getResourceBundle().getString("kt.form.label.income.rdo"))).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "income_rdo")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, numberCellStyle)).toArray(new CellStyle[0]);
			
			columnNames = Arrays.asList(arrAdd(columnNames.length, columnNames, "Note (RDO)")).toArray(new String[0]);
			fieldNames = Arrays.asList(arrAdd(fieldNames.length, fieldNames, "remarks_kt")).toArray(new String[0]);
			cellStyles = Arrays.asList(arrAdd(cellStyles.length, cellStyles, textCellStyle)).toArray(new CellStyle[0]);
		}
	}
}
