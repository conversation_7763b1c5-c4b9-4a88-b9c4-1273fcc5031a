package hk.eduhk.rich.report.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.stream.Collectors;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.util.CellRangeAddress;



import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.employeeCount;
import hk.eduhk.rich.entity.staff.StaffDAO;
import hk.eduhk.rich.entity.staff.StaffEligible;
import hk.eduhk.rich.report.ReportGroup;
import hk.eduhk.rich.view.PreRptView;


public class KtFormCDCFinalReportGroup extends ReportGroup
{
	
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;
	private List<String> flassDeptNames;
	private List<String> fehdDeptNames;
	private List<String> fhmDeptNames;
	
	private List<employeeCount> employeeCount ;
	
	private List<AppStaffCount> dept_total_count;
	
	
	public List<String> getFlassDeptNames()
	{
		return flassDeptNames;
	}


	
	public void setFlassDeptNames(List<String> flassDeptNames)
	{
		this.flassDeptNames = flassDeptNames;
	}


	
	public List<String> getFehdDeptNames()
	{
		return fehdDeptNames;
	}


	
	public void setFehdDeptNames(List<String> fehdDeptNames)
	{
		this.fehdDeptNames = fehdDeptNames;
	}


	
	public List<String> getFhmDeptNames()
	{
		return fhmDeptNames;
	}


	
	public void setFhmDeptNames(List<String> fhmDeptNames)
	{
		this.fhmDeptNames = fhmDeptNames;
	}

	
	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}


	public KtFormCDCFinalReportGroup() 
	{
		
	}
	
	public void init(Workbook workbook, int cur_year) 
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		headerRowStyle.setFillBackgroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillForegroundColor(IndexedColors.TAN.index);
		headerRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		Font font = workbook.createFont();
		font.setBold(true);
		headerRowStyle.setFont(font);
				
		headerStyle = headerRowStyle;
		
		
		activeHeaderFilter = false;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;		

		
		columnNames = new String[] {"For sorting","Dept","Surname","Last Name","Full Name", "Ranking"};
		
			
		fieldNames = new String[] {"dept_code","employee_number", "last_name", "first_name", "full_name", "post_rank_code","count_output"}; 		
		cellStyles = new CellStyle[] {null,null, null, null, null,null,null};

		try {
			//Todo: Department call db 
			flassDeptNames  = AppPeriodReportDAO.getFacultyDept("FLASS");
			fehdDeptNames  = AppPeriodReportDAO.getFacultyDept("FEHD");
			fhmDeptNames  = AppPeriodReportDAO.getFacultyDept("FHM");
		}
		catch (Exception e)
		{
			logger.log(Level.INFO, "Cannot get name for Department");
		}
	}
	
	
	public void appendDataCollumns_all (String sheetName,int cur_year,int i_period_id)
	{			
			try
			{
				employeeCount = AppPeriodReportDAO.getEmployeeCount(cur_year,0, i_period_id);
				
				
				List<employeeCount> FlassCount 	= employeeCount.stream().filter(x->flassDeptNames.contains(x.getDept_code())).collect(Collectors.toList());
				List<employeeCount> fehdCount 	= employeeCount.stream().filter(x->fehdDeptNames.contains(x.getDept_code())).collect(Collectors.toList());
				List<employeeCount> fhmCount 	= employeeCount.stream().filter(x->fhmDeptNames.contains(x.getDept_code())).collect(Collectors.toList());
				
				String fristY =  Integer.toString(cur_year-1) + "-" + Integer.toString(cur_year) + " Refereed items (Books & Journals)" ;
				String secY =    Integer.toString(cur_year-2) + "-" + Integer.toString(cur_year-1) + " Refereed items (Books & Journals)" ;
				String thirdY =  Integer.toString(cur_year-3) + "-" + Integer.toString(cur_year-2) + " Refereed items (Books & Journals)" ;
				String fourY =   Integer.toString(cur_year-4) + "-" + Integer.toString(cur_year-3) + " Refereed items (Books & Journals)" ;
				String fifY =  	 Integer.toString(cur_year-5) + "-" + Integer.toString(cur_year-4) + " Refereed items (Books & Journals)" ;
				
				String [] header = new String[] {"For sorting","Dept","Surname","Last Name","Full Name", "Ranking", fristY,secY,thirdY,fourY,fifY};

				
				Sheet sheet = workbook.getSheet(sheetName); 
				
				sheet.setColumnWidth(0,10*256);
				sheet.setColumnWidth(1,6*256);
				sheet.setColumnWidth(2,10*256);
				sheet.setColumnWidth(3,25*256);
				sheet.setColumnWidth(4,35*256);
				sheet.setColumnWidth(5,30*256);
				sheet.setColumnWidth(6, 20*256);
				sheet.setColumnWidth(7, 20*256);
				sheet.setColumnWidth(8, 20*256);
				sheet.setColumnWidth(9, 20*256);
				sheet.setColumnWidth(10,20*256);
				
				Row row  = sheet.createRow(0);
				row.setHeightInPoints(3*20);
				
				for(int i = 0 ; i < header.length ; i++) {
					Cell cellData = row.createCell(i);
					cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,true,true));
					cellData.setCellValue(header[i]);
				}	
				
				printExcelData_all(FlassCount,sheet, 1, 1,i_period_id );
				printExcelData_all(fehdCount,sheet, 1+FlassCount.size(),2,i_period_id);
				printExcelData_all(fhmCount,sheet, 1+FlassCount.size()+fehdCount.size(),3,i_period_id );

			}					
		
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot import the data into excel sheet");
			}
	}
	
	
	
	



	public void printExcelData_all (List<employeeCount> importList,  Sheet insheet, int sind  ,int sorting, int i_period_id) 
	{
		try {
			CellStyle style = workbook.createCellStyle();
			CellStyle exceptStyle = workbook.createCellStyle();
								
			switch(sorting) {
				  case 1:
					  exceptStyle.setFillBackgroundColor(IndexedColors.LIGHT_GREEN.index);
					  exceptStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.index);
					  style.setFillBackgroundColor(IndexedColors.LIGHT_GREEN.index);
					  style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.index);
				    break;
				  case 2:
					  exceptStyle.setFillBackgroundColor(IndexedColors.LIGHT_ORANGE.index);
					  exceptStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.index);
					  style.setFillBackgroundColor(IndexedColors.LIGHT_ORANGE.index);
					  style.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.index);
				    break;
				  case 3:
					  exceptStyle.setFillBackgroundColor(IndexedColors.LIGHT_YELLOW.index);
					  exceptStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);
					  style.setFillBackgroundColor(IndexedColors.LIGHT_YELLOW.index);
					  style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);
				  break;
			}
			
			style.setAlignment(HorizontalAlignment.CENTER);
			style.setBorderBottom(BorderStyle.THIN);
			style.setBorderLeft(BorderStyle.THIN);
			style.setBorderRight(BorderStyle.THIN);
			style.setBorderTop(BorderStyle.THIN);
			style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			
			
			exceptStyle.setBorderBottom(BorderStyle.THIN);
			exceptStyle.setBorderLeft(BorderStyle.THIN);
			exceptStyle.setBorderRight(BorderStyle.THIN);
			exceptStyle.setBorderTop(BorderStyle.THIN);
			exceptStyle.setAlignment(HorizontalAlignment.LEFT);
			exceptStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			
			CellStyle below1style = workbook.createCellStyle();
			below1style.setFillBackgroundColor(IndexedColors.PALE_BLUE.index);
			below1style.setFillForegroundColor(IndexedColors.PALE_BLUE.index);
			below1style.setBorderBottom(BorderStyle.THIN);
			below1style.setBorderLeft(BorderStyle.THIN);
			below1style.setBorderRight(BorderStyle.THIN);
			below1style.setBorderTop(BorderStyle.THIN);
			below1style.setAlignment(HorizontalAlignment.CENTER);
			below1style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			
			CellStyle naStyle = workbook.createCellStyle();
			naStyle.setFillBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			naStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
			naStyle.setBorderBottom(BorderStyle.THIN);
			naStyle.setBorderLeft(BorderStyle.THIN);
			naStyle.setBorderRight(BorderStyle.THIN);
			naStyle.setBorderTop(BorderStyle.THIN);
			naStyle.setAlignment(HorizontalAlignment.CENTER);
			naStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			
			for (int i = 0 ; i < importList.size();i++) {

				Row row  = insheet.createRow(i+sind);
				
				for (int j = 0 ; j< 11 ; j++) {
					Cell cellData = row.createCell(j);
					int staff_period = Integer.parseInt(importList.get(i).getPeriod_id());
					
					if (j <=5 ) {
						cellData.setCellStyle(style);
						
						if (j == 0)
							cellData.setCellValue(sorting);
						else if (j==1)
							cellData.setCellValue(importList.get(i).getDept_code());

						else if ( j == 2 ) {
							cellData.setCellStyle(exceptStyle);
							cellData.setCellValue(importList.get(i).getLast_name());
						}
							
						else if ( j == 3) {
							cellData.setCellStyle(exceptStyle);
							cellData.setCellValue(importList.get(i).getFirst_name());
						}
						else if ( j == 4) {
							cellData.setCellStyle(exceptStyle);
							cellData.setCellValue(importList.get(i).getFull_name());
						}
						else
							cellData.setCellValue(importList.get(i).getPost_rank_code());
							
					}
					else {
						boolean nothing = false;
						Double countingCell = 0.0;
						if (j == 6) 	{
							
							countingCell = Double.parseDouble(importList.get(i).getCount_output());
							if (staff_period > i_period_id)
								nothing = true;
						}
						else if (j == 7) 	{
							countingCell = Double.parseDouble(importList.get(i).getCount_output_2());
							if (staff_period > i_period_id - 1) 
								nothing = true;
						}
						else if ( j == 8 )	{
							countingCell = Double.parseDouble(importList.get(i).getCount_output_3());
							if (staff_period > i_period_id - 2) 
								nothing = true;
						}
						else if ( j == 9) 	{
							countingCell = Double.parseDouble(importList.get(i).getCount_output_4());
							if (staff_period > i_period_id - 3) 
								nothing = true;
						}
						else  {
							countingCell = Double.parseDouble(importList.get(i).getCount_output_5());
							if (staff_period > i_period_id - 4) 
								nothing = true;
						}
						Double countingCellDouble = PreRptView.roundAvoid(countingCell,2);
						if (nothing) {
							cellData.setCellValue("n/a");
							cellData.setCellStyle(naStyle);
						}
						else {
							cellData.setCellValue(countingCellDouble);
							if (countingCellDouble < 1.0) 
								cellData.setCellStyle(below1style);
							else
								cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,false,true));
						
						}
	
					}
				}
			
			}
		}
		catch (Exception e) {
			logger.log(Level.WARNING,"Cannot print the data into Excel sheet ALL");
		}
	}
	public void appendDataCollumns_Dept (Sheet inSheet, String College, String i_dept_list ,int cur_year, int cur_year_id) throws SQLException
	{			
		
					
				 List<employeeCount> empList; 
				 List<FacDeptDesc> DeptDesc = AppPeriodReportDAO.getDeptDesc (College,"",false,false);
				
				 
				 String count_H_A = "List of Academics who have Consistently Produced Refereed Output Below 1 Item per year in the Past ";
				 String count_H_A_1 = count_H_A + "Five Years" ;
				 String count_H_A_2 = count_H_A + "Three Years" ;
				 String count_H_A_3 = count_H_A + "Two Years";
				 String count_H_A_4 = count_H_A + "Two, Three and Five Years";
				 
				 
				 int five_counts = 0;
				 int three_counts = 0;
				 int two_counts = 0;
				 int total_counts = 0;
				 
				 inSheet.setColumnWidth(0,5*256);
				 inSheet.setColumnWidth(1,35*256);
				 inSheet.setColumnWidth(2,15*256);
				 inSheet.setColumnWidth(3,25*256);
				 inSheet.setColumnWidth(4,15*256);
				 inSheet.setColumnWidth(5,15*256);
				 inSheet.setColumnWidth(6,15*256);
				 inSheet.setColumnWidth(7,15*256);
				 inSheet.setColumnWidth(8,15*256);
				 
				 String count_H_B_1 = "";
				 
				//TODO: check 5 year
				//TODO define the user list
				count_H_B_1 = DeptDesc.stream().filter(x->x.getDept_code().equalsIgnoreCase(i_dept_list)).collect(Collectors.toList()).get(0).getDepartment_name();
				
				employeeCount = AppPeriodReportDAO.getEmployeeCount(cur_year,5,cur_year_id);
				


				if(employeeCount.size() > 0) {

					empList 	 = employeeCount.stream().filter(x->x.getDept_code().equalsIgnoreCase(i_dept_list) &&
								Integer.parseInt(x.getPeriod_id()) <= cur_year_id - 4  ).collect(Collectors.toList());
					if(empList.size() > 0 ) {
						printListPerPeriod(empList,inSheet,1+total_counts,5,cur_year_id,count_H_A_1,count_H_B_1,i_dept_list);
						five_counts = empList.size()+7;
						total_counts += five_counts;
					}

				}

				
				//TODO: check 3 year
				//TODO define the user list
				employeeCount = AppPeriodReportDAO.getEmployeeCount(cur_year,3,cur_year_id);
				if(employeeCount.size() > 0) {
					empList 	 = employeeCount.stream().filter(x->x.getDept_code().equalsIgnoreCase(i_dept_list)
							&&	Integer.parseInt(x.getPeriod_id()) <= cur_year_id - 2 ).collect(Collectors.toList());
					if(empList.size() > 0 ) {
						printListPerPeriod(empList,inSheet,1+total_counts,3,cur_year_id,count_H_A_2,count_H_B_1,i_dept_list);
						three_counts = empList.size()+7 ;
						total_counts += three_counts;
					}
				}
				
				//TODO: check 2 year
				//TODO define the user list
				employeeCount = AppPeriodReportDAO.getEmployeeCount(cur_year,2,cur_year_id);
				if(employeeCount.size() > 0) {
					empList 	 = employeeCount.stream().filter(x->x.getDept_code().equalsIgnoreCase(i_dept_list)
							&& Integer.parseInt(x.getPeriod_id()) <= cur_year_id - 1 ).collect(Collectors.toList());
					//System.out.println(empList);
					if(empList.size() > 0 ) {
						printListPerPeriod(empList,inSheet,1+total_counts,2,cur_year_id,count_H_A_3,count_H_B_1,i_dept_list);
						two_counts = empList.size()+7;
						total_counts += two_counts;
					}
					else {
						printListPerPeriod(empList,inSheet,1+total_counts,2,cur_year_id,count_H_A_4,count_H_B_1,i_dept_list);
						two_counts = empList.size()+1+7;
						total_counts += two_counts;
				}
				}
				
				Row row  = inSheet.createRow(total_counts );
				Cell cellData = row.createCell(0);
				cellData.setCellStyle(dataStyle(HorizontalAlignment.LEFT,false,false));
				cellData.setCellValue("*In accordance with the UGC's CDCF guidelines, the co-authorship is taken into account by pro-rating the count by the number of authors. For example, a paper with four authors will count as 0.25 for each author.");
				inSheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , 0 , 8 ));

	}
	
	
	public void printListPerPeriod (List<employeeCount> empList, Sheet inSheet, int sind, int years, int curY,String header_1, String header_2,String i_dept_code) {
		
		
		String curYearDesc = AppPeriodReportDAO.getCdcfRptPeriod(curY).get(0).getChart_desc();
		
		//Cant be negative
		String FinYearDesc = AppPeriodReportDAO.getCdcfRptPeriod(curY-years+1).get(0).getChart_desc();
		String column_4 = "Average No. of Refereed Items in the Past "+ Integer.toString(years) +" Years ("+FinYearDesc+"-"+curYearDesc+")";
		
		// Setting of 1 st Columns
		String [] columnList = {"No.","Name","Ranking",column_4,"Total No. of Refereed Items*(Scholarly Books, Monographs and Chapters & Journal Publications)"};
		
		if (i_dept_code.equalsIgnoreCase("CCA"))
			columnList[4] = "Total No. of Refereed Items* (Scholarly Books, Monographs and Chapters, Journal Publications and Non-Traditional Outputs)";
		
		// 2nd Columns
		String [] PeriodList = { AppPeriodReportDAO.getCdcfRptPeriod(curY).get(0).getChart_desc_2(),
								AppPeriodReportDAO.getCdcfRptPeriod(curY-1).get(0).getChart_desc_2(),
								AppPeriodReportDAO.getCdcfRptPeriod(curY-2).get(0).getChart_desc_2(),
								AppPeriodReportDAO.getCdcfRptPeriod(curY-3).get(0).getChart_desc_2(),
								AppPeriodReportDAO.getCdcfRptPeriod(curY-4).get(0).getChart_desc_2() };
		
		
		
		Cell cellData;
		//Block header 1 row 2 ?
		Row row  = inSheet.createRow(sind);
		cellData = row.createCell(0);
		cellData.setCellValue(header_1);
		cellData.setCellStyle(getBoldStyle());
		//block header 2
		row  = inSheet.createRow(sind+1);
		cellData = row.createCell(0);
		cellData.setCellValue(header_2);
		cellData.setCellStyle(getBoldStyle());
		
		
		
		//create header of column
		row  = inSheet.createRow(sind+3);
		Row row_border  = inSheet.createRow(sind+4);
		
		//row height 
		row.setHeightInPoints(3*20);
		
		if ( empList.size() > 0) {
		
			for ( int i =0 ; i<columnList.length ; i++) {
				
				
				Cell cell_border = row_border.createCell(i);
				cell_border.setCellStyle(dataStyle(HorizontalAlignment.CENTER,true,true));
				
				if (i == 5){
					cellData = row.createCell(i+years-1);
				}
				else
					cellData = row.createCell(i);
				
				//column header style
				cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,true,true));
				cellData.setCellValue(columnList[i]);
				
				if (i == 4 ) {
					for(int j = 1; j < years; j++) {
						cellData = row.createCell(i+years-j);
						cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,true,true));
					}
					inSheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum() , i , i+years-1 ));
				}
				else if ( i == 5 ) {
					for(int j = 1; j < years; j++) {
						cellData = row.createCell(i+years-j);
						cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,true,true));
					}
					inSheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , i+years-1 , i+years-1 ));
				}
				else {
					
					inSheet.addMergedRegion(new CellRangeAddress(row.getRowNum(),row.getRowNum()+1 , i , i ));
				}
			}
			
			for ( int i=0;i<years ; i++) {
				cellData = row_border.createCell(4+i);
				cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,true,true));
				cellData.setCellValue(PeriodList[i]);
			}
	
			//write data in cell
			for (int i =0; i<empList.size() ;i++) {
				
				row = inSheet.createRow(sind+i+5);
				double avg_items = 0.00;
				
				int staff_period = Integer.parseInt(empList.get(i).getPeriod_id());
				
				if (staff_period <=  curY - years + 1) {
					for(int j = 0 ; j< (columnList.length - 1 + years) ; j ++ ) {
								cellData = row.createCell(j);
								cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,false,true));
	
						
						
								if (j < 3 ) {
									if (j == 0 )
										cellData.setCellValue(i+1);
									if (j == 1) {
										cellData.setCellStyle(dataStyle(HorizontalAlignment.LEFT,false,true));
										cellData.setCellValue(empList.get(i).getFull_name());
										inSheet.autoSizeColumn(j);
									}
									if ( j == 2 )
										cellData.setCellValue(empList.get(i).getPost_rank_code());
									
								}
								else if ( j > 3){
									Double countingCell = 0.0;
			
									
									// Seperate number of years
									
									//When Past 2 years
									if (years > 1)
									{
										if ( j == 4 ) 
											countingCell =   Double.parseDouble(empList.get(i).getCount_output());
										if ( j == 5 )	
											countingCell =   Double.parseDouble(empList.get(i).getCount_output_2());
									}
									
									//When Past 3 years
									if(years > 2) {
										if ( j == 6 ) 	
											countingCell =   Double.parseDouble(empList.get(i).getCount_output_3());
									}
									
									//When Past 5 years
									if(years > 4) {
										if ( j == 7 ) 
											countingCell =   Double.parseDouble(empList.get(i).getCount_output_4());
										if ( j == 8 ) 	
											countingCell =   Double.parseDouble(empList.get(i).getCount_output_5());
									}
									
			
			
									avg_items += countingCell;
									cellData.setCellValue(PreRptView.roundAvoid(countingCell,2));
									
								}
							
					}
					//counting avg when J = 32
					// Sum up the counting until the avg get
					cellData = row.createCell(3);
					cellData.setCellStyle(dataStyle(HorizontalAlignment.CENTER,false,true));
					cellData.setCellValue(PreRptView.roundAvoid(avg_items/years,2));
				}
			}
		}
		else {
			cellData = row.createCell(1);
			cellData.setCellValue("Nil");
		}
		
	}
	
	
	public CellStyle dataStyle (HorizontalAlignment pos, boolean bolded, boolean border ) {
		
		org.apache.poi.ss.usermodel.Font font=  workbook.createFont();
		font.setBold(true);
		
		CellStyle chStyle = workbook.createCellStyle(); 
		
		chStyle.setAlignment(pos);
		chStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		if (border) {
			chStyle.setBorderBottom(BorderStyle.THIN);
			chStyle.setBorderLeft(BorderStyle.THIN);
			chStyle.setBorderRight(BorderStyle.THIN);
			chStyle.setBorderTop(BorderStyle.THIN);
		}
		
		if (bolded) 
			chStyle.setFont(font);
		
		chStyle.setWrapText(true);
		
		return chStyle;
	}
	

	
	@Override
	public void init(Workbook workbook)
	{
		// TODO Auto-generated method stub
		
	}
	
	
	
	
	
}
