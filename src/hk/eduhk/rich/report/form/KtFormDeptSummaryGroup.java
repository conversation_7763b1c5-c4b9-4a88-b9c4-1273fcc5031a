package hk.eduhk.rich.report.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;


import hk.eduhk.rich.entity.report.AppPeriodReportDAO;
import hk.eduhk.rich.entity.report.AppStaffCount;
import hk.eduhk.rich.entity.report.FacDeptDesc;
import hk.eduhk.rich.entity.report.employeeCount;
import hk.eduhk.rich.entity.report.employeeCount_detail;
import hk.eduhk.rich.entity.report.last3YearSummary;
import hk.eduhk.rich.entity.report.reportFilteringField;
import hk.eduhk.rich.entity.report.staffNumberName;
import hk.eduhk.rich.report.ReportGroup;
import hk.eduhk.rich.view.PreRptView;


public class KtFormDeptSummaryGroup extends ReportGroup
{
	
	// Indicator of whether the user is KT Admin
	// If the user contains KT Admin role, show the lookup code value
	// Otherwise, show the description of the lookup value
	private boolean isKtAdmin;

	
	private List<last3YearSummary>  staffDetails ;
	
	
	public List<last3YearSummary> getStaffDetails()
	{
		return staffDetails;
	}


	
	public void setStaffDetails(List<last3YearSummary> staffDetails)
	{
		this.staffDetails = staffDetails;
	}



	public boolean isKtAdmin()
	{
		return isKtAdmin;
	}
	
	
	public void setKtAdmin(boolean isKtAdmin)
	{
		this.isKtAdmin = isKtAdmin;
	}


	public KtFormDeptSummaryGroup() 
	{
		
	}
	
	public void init(Workbook workbook ) 
	{
		this.workbook = workbook;
		
		CellStyle dateCellStyle = workbook.createCellStyle();
		CreationHelper createHelper = workbook.getCreationHelper();
		dateCellStyle.setDataFormat(createHelper.createDataFormat().getFormat("MM-YYYY"));
		
		CellStyle numberCellStyle = workbook.createCellStyle();
		numberCellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
		
		CellStyle textCellStyle = workbook.createCellStyle();
		textCellStyle.setWrapText(true);
		
		CellStyle headerRowStyle = workbook.createCellStyle();
		
		
		headerStyle = headerRowStyle;
		
		
		activeHeaderFilter = false;
		
		showSeq = true;
		seqStartRowNum = 2;
		seqCellNum = 0;		

		
		columnNames = new String[] {"For Sorting","Name", "Ranking","Total No. of refereed items (Scholarly Books, Monographs and Chapters & Journal Publications)" };
		
			
		fieldNames = new String[] {"dept_code","employee_number", "last_name", "first_name", "post_rank_code", "output_type","research_type","count_output"}; 		
		cellStyles = new CellStyle[] {null,null, null, null};
		
	}
	
	
	public void appendDataHeader (Workbook workbook,Sheet sheet, String dept_code,String dept_name,staffNumberName staffInfo, reportFilteringField filter)
	{			
			try
			{
				int from_year = filter.getP_from_year();
				int to_year	  = filter.getP_to_year();
				
				
				
			
				String to_year_p_header 	= Integer.toString(from_year) + '/' + Integer.toString(to_year);
				String from_year_p_header   = Integer.toString(from_year-2) + '/' + Integer.toString(to_year-2); 
				
				
				String output_type_header	= "\"Scholarly Books, Monograph and Chapters\" and \"Journal Publications\" (Source: CDCF Returns)";
				
				String [] header = {"Summary Report on Refereed Research Outputs from "+from_year_p_header+" to "+to_year_p_header,output_type_header, dept_name,""};
				
				String [] footer = { "* In accordance with the UGC's CDCF guidelines, the co-authorship is taken into account by pro-rating the count by the number of authors. For example, a paper with four authors will count as 0.25 for each author." };
				
				String [] columnHeader = {"No.","Title/Brief Description", "Author","Research Output Category","Weighting"};
				
				header[3] = staffInfo.getFull_name();
				
				if(dept_code.equalsIgnoreCase("CCA"))
					header[1]= "\"Scholarly Books, Monograph and Chapters\", \"Journal Publications\" and Non-traditional Outputs (Source: CDCF Returns)";
				
				//Header
				for (int i = 0 ; i <4;i++) {		
					Row h_row = sheet.createRow(i);
					Cell cell = h_row.createCell(0);
					cell.setCellValue(header[i]);
					cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.LEFT,true,false));
					sheet.addMergedRegion(new CellRangeAddress(h_row.getRowNum(),h_row.getRowNum() , 0 , 4));
				}
				
				Row c_row = sheet.createRow(5);
				//Column header
				for (int i = 0 ; i<columnHeader.length;i++) {
					Cell cell = c_row.createCell(i);
					cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.CENTER,true,true));
					cell.setCellValue(columnHeader[i]);
				}
				
				
				
				//1 st year
				int total = 0;
				for (int k = 2 ; k >= 0;k--) 
				{
					staffDetails = AppPeriodReportDAO.getDeptStaffDetails(to_year-k,dept_code);
					List <last3YearSummary> staffOutput = staffDetails.stream().filter(x->x.getEmployee_number().equalsIgnoreCase(staffInfo.getEmployee_number())).collect(Collectors.toList());
					
					if(staffOutput.size() != 0) {
						
						Row d_row = sheet.createRow(6+total);
						Cell cell = d_row.createCell(0);
						cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.LEFT,true,true));
						String period = Integer.toString(to_year-k-1) +"-"+Integer.toString(to_year-k);
						cell.setCellValue(period);
						
						cell = d_row.createCell(4);
						cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.LEFT,false,true));
						sheet.addMergedRegion(new CellRangeAddress(d_row.getRowNum(),d_row.getRowNum() , 0 , 4));
						
						for (int i= 0 ; i < staffOutput.size() ; i++) {
							d_row = sheet.createRow(7+i+ total);
							for (int j= 0;j < columnHeader.length;j++) {
								cell = d_row.createCell(j);
								cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.LEFT,false,true));
								if(j==0) {
									cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.CENTER,false,true));
									cell.setCellValue(i+1);
								}
								else if(j== 1 ) {
									cell.setCellValue(staffOutput.get(i).getApa_citation());
								}
								else if(j== 2 ) {
									cell.setCellValue(staffOutput.get(i).getName_other_pos());
								}
								else if(j== 3 ) {
									cell.setCellValue(staffOutput.get(i).getDescription());
								}
								else if(j == 4 ) {
									cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.CENTER,false,true));
									cell.setCellValue(PreRptView.roundAvoid(Double.parseDouble(staffOutput.get(i).getCdcf_weighting()),2));
								}
							}	
						}
						
						total += staffOutput.size()+1;
					}
				}
				Row f_row = sheet.createRow(8+total);
				Cell f_cell = f_row.createCell(0);
				f_cell.setCellValue(footer[0]);
				sheet.addMergedRegion(new CellRangeAddress(f_row.getRowNum(),f_row.getRowNum()+1 , 0 , 4));
				f_cell.setCellStyle(dataStyle(workbook,HorizontalAlignment.LEFT,false,false));

			}					
		
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot import the data into excel sheet");
			}
	}
	
	public static CellStyle dataStyle (Workbook workbook,HorizontalAlignment pos, boolean bolded, boolean border ) {
		

		org.apache.poi.ss.usermodel.Font font=  workbook.createFont();
		font.setBold(true);
		
		CellStyle chStyle = workbook.createCellStyle(); 
		
		chStyle.setAlignment(pos);
		chStyle.setVerticalAlignment(VerticalAlignment.TOP);
		if (border) {
			chStyle.setBorderBottom(BorderStyle.THIN);
			chStyle.setBorderLeft(BorderStyle.THIN);
			chStyle.setBorderRight(BorderStyle.THIN);
			chStyle.setBorderTop(BorderStyle.THIN);
		}
		
		if (bolded) 
			chStyle.setFont(font);
		
		chStyle.setWrapText(true);
		
		return chStyle;
	}
	
	
}
