package hk.eduhk.rich.report;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.beanutils.NestedNullException;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFFontFormatting;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import hk.eduhk.rich.bundle.MessageBundle;


public abstract class ReportGroup
{

	protected String processId;

	protected String name;
	
	protected String[] columnNames;
	
	protected CellStyle[] columnStyles;
	
	protected String[] fieldNames;
	
	protected Function<?,?>[] convertFunctions;
	
	protected CellStyle[] cellStyles;
	
	protected Logger logger = Logger.getLogger(this.getClass().getName());
	
	protected Workbook workbook;
	
	protected Boolean showProgram = false;
	
	private static Map<String, Class<?>> fieldClassMap = new HashMap<String, Class<?>>();
	
	// Header Row Style
	protected CellStyle headerStyle = null;
	
	// Bold Style
	protected CellStyle boldStyle = null;
		
	// Activate Header Row Filter
	protected Boolean activeHeaderFilter = false;
	
	// Activate display sequence for data row
	protected Boolean showSeq = false;
	
	// Header Value for sequence column 
	// Activate when showSeq == true
	protected String seqHeaderValue = "";
	
	// Sequence start row 
	protected Integer seqStartRowNum;
	
	// Sequence cell index
	protected Integer seqCellNum;
	
	// check is RDO Admin
	protected boolean isRdoAdmin;
	
	public abstract void init(Workbook workbook);
	
	
	public void setHeaderStyle(CellStyle headerStyle) 
	{
		this.headerStyle = headerStyle;
	}
	
	public void setShowProgram(Boolean showProgram) {
		this.showProgram = showProgram;
	}
	
	public void setActiveHeaderFilter(Boolean activeHeaderFilter) 
	{
		this.activeHeaderFilter = activeHeaderFilter;
	}
	
	public void setShowSeq(Boolean showSeq) 
	{
		this.showSeq = showSeq;
	}
	
	public void appendColumnName(String[] newColumnNames)
	{
		if (newColumnNames != null)
		{
			columnNames = (String[]) ArrayUtils.addAll(columnNames, newColumnNames);
		}
	}
	
	
	public void appendColumnField(String[] newFieldNames, String[] newStrCellStyles)
	{
		if (newFieldNames != null && newStrCellStyles != null && 
			newFieldNames.length == newStrCellStyles.length)
		{
			fieldNames = (String[]) ArrayUtils.addAll(fieldNames, newFieldNames);
			CellStyle[] newCellStyles = new CellStyle[newStrCellStyles.length];
			
			CellStyle cellStyle = workbook.createCellStyle();
			CreationHelper createHelper = workbook.getCreationHelper();
			
			for (int i = 0; i < newStrCellStyles.length; i++)
			{
				if(newStrCellStyles[i] != null)
				{
					cellStyle.setDataFormat(createHelper.createDataFormat().getFormat(newStrCellStyles[i]));
					newCellStyles[i] = cellStyle;
				}
				else
				{
					newCellStyles[i] = null;
				}
			}
			
			cellStyles = (CellStyle[]) ArrayUtils.addAll(cellStyles, newCellStyles);
		}
	}
	
	
	public void appendHeaderColumns(Row row, Object obj)
	{
		
		if (row != null)
		{			
			for (int n=0;n<columnNames.length;n++)
			{
				if(showSeq) 
				{
					int cellNum = (row.getLastCellNum() == -1? 0: row.getLastCellNum());
					
					if(seqCellNum.equals(cellNum)) 
					{
						Cell cell = row.createCell(cellNum);
						cell.setCellValue(seqHeaderValue);

						if(headerStyle != null) cell.setCellStyle(headerStyle);
					}
				}
				
				Cell cell = row.createCell(row.getLastCellNum() == -1? 0: row.getLastCellNum());
				if (obj != null)
				{
					String column = columnNames[n];
					
					try
					{
						cell.setCellValue(column);
						
						row.getSheet().autoSizeColumn(cell.getColumnIndex());
						
						if(activeHeaderFilter) row.getSheet().setColumnWidth(cell.getColumnIndex(), row.getSheet().getColumnWidth(cell.getColumnIndex())+600);
						
						if(headerStyle != null) cell.setCellStyle(headerStyle);
					}
					catch (Exception e)
					{
						logger.log(Level.INFO, "Cannot get column " + column + " from class " + obj.getClass());
					}
				}
				
			}
			
			if(activeHeaderFilter) row.getSheet().setAutoFilter(new CellRangeAddress(row.getRowNum(),row.getRowNum(),row.getFirstCellNum(),row.getLastCellNum()-1));
			
		}
	}
	
	
	public void appendDataColumns(Row row, Object obj)
	{
		if (row != null)
		{			
			for (int n=0;n<fieldNames.length;n++)
			{
				if(showSeq) 
				{
					if(row.getRowNum()>=seqStartRowNum) 
					{
						int cellNum = (row.getLastCellNum() == -1? 0: row.getLastCellNum());
						
						if(seqCellNum.equals(cellNum)) 
						{
							Cell cell = row.createCell(cellNum);
							Integer seq = row.getRowNum() - seqStartRowNum + 1 ;
							cell.setCellValue((String)seq.toString());
						}
					}
				}
				
				Cell cell = row.createCell(row.getLastCellNum() == -1? 0: row.getLastCellNum());
				if (obj != null)
				{
					String field = fieldNames[n];
					Object value = null;
					
					try 
					{
						value = PropertyUtils.getProperty(obj, field);
						Class<?> propertyClass = getFieldClass(obj, field);
						
						// Perform the conversion if defined
						if (ArrayUtils.isNotEmpty(convertFunctions) && n < convertFunctions.length && convertFunctions[n] != null)
						{
							if (value != null)
							{
								Function func = convertFunctions[n];
								value = func.apply(value);
								propertyClass = value.getClass();
							}
						}
						
						if (propertyClass != null && value!=null)
						{
							if(propertyClass.equals(java.util.Date.class))
							{
								cell.setCellValue((java.util.Date) value);
							}
							else if(propertyClass.equals(java.lang.String.class))
							{
								cell.setCellValue((String)value);
							}
							else if(propertyClass.equals(java.lang.Integer.class) || propertyClass.equals(int.class))
							{
								cell.setCellValue((Integer)value);
							}
							else if(propertyClass.equals(java.lang.Double.class) || propertyClass.equals(double.class))
							{
								cell.setCellValue((Double)value);
							}
							else if(Collection.class.isAssignableFrom(propertyClass))
							{
								// Remove the bracket from the String
								String strValue = value.toString();
								strValue = strValue.substring(1, strValue.length()-1);
								cell.setCellValue(strValue);
							}
							else
							{
								cell.setCellValue(value.toString());
							}
						}
						else
						{
							if(value == null)
							{
								cell.setCellValue("");
							}
							else
							{
								cell.setCellValue(value.toString());
							}
						}
						
						if(cellStyles[n] != null)
							cell.setCellStyle(cellStyles[n]);
						
					}
					catch (NestedNullException nne)
					{
						// Cannot extract the target field from the bean
					}
					catch (Exception e)
					{
						logger.log(Level.INFO, e.getClass() + " - Cannot get field " + field + " from " + obj.getClass());
					}
				}
				else
				{
					cell.setCellValue("");
				}
			}
		}
	}
	
	
	public void appendTitleColumns(Row row1,Row row2, Object obj, String sheetName, String titleString)
	{
		if (row1 != null)
		{
			Cell cell = row1.createCell(row2.getLastCellNum() == -1? 0: row2.getLastCellNum());
			if (obj != null && titleString!=null)
			{
				try
				{
					cell.setCellValue(titleString);
					Sheet sheet = workbook.getSheet(sheetName);
					int startCellNum = cell.getColumnIndex();
					int endCellNum = startCellNum+fieldNames.length-1;
					
					if(showSeq) endCellNum = endCellNum+1;
					
					if(endCellNum>startCellNum) {
						sheet.addMergedRegion(new CellRangeAddress(row1.getRowNum(),row1.getRowNum(),startCellNum,+endCellNum));
					}
					
				}
				catch (Exception e)
				{
					logger.log(Level.INFO, "Cannot get title " + titleString + " from class " + obj.getClass());
				}
			}
		}
	}
	
	public int getColumnSize()
	{
		return columnNames != null ? columnNames.length : 0;
	}
	
	
	public static Class<?> getFieldClass(Object obj, String fieldName) throws ReflectiveOperationException
	{
		Class<?> fieldClass = null;
		
		if (obj != null && StringUtils.isNotBlank(fieldName))
		{
			String className = obj.getClass().getName();
			String lookupName = className + "." + fieldName;
			
			fieldClass = fieldClassMap.get(lookupName);
			
			// Lookup the class from the Map first
			if (fieldClass == null)
			{
				// Retrieve the value from the getter first
				String getterName = "get" + StringUtils.capitalize(fieldName);
				Method method = MethodUtils.getMatchingMethod(obj.getClass(), getterName, new Class<?>[] {});
				fieldClass = (method != null) ? method.getReturnType() : null;
				
				// Cannot invoke the getter, get the field from the instance
				if (fieldClass == null)
				{
					fieldClass = PropertyUtils.getPropertyType(obj, fieldName);
				}
				
				// Save the result in the Map
				if (fieldClass != null)
				{
					fieldClassMap.put(lookupName, fieldClass);
				}
			}
		}
		
		return fieldClass;
	}
	
	public static Object[] arrAdd(int n, Object arr[], Object x)
	{
		Object newarr[] = new Object[n + 1];
       
	 	for (int i = 0; i < n; i++)
	 		newarr[i] = arr[i];
   
	 	newarr[n] = x;
   
		return newarr;
    }


	
	public boolean isRdoAdmin()
	{
		return isRdoAdmin;
	}


	
	public void setRdoAdmin(boolean isRdoAdmin)
	{
		this.isRdoAdmin = isRdoAdmin;
	}

	public ResourceBundle getResourceBundle()
	{
		ResourceBundle bundle = MessageBundle.getResourceBundle();
		return bundle;
	}


	
	public CellStyle getBoldStyle()
	{
		CellStyle boldStyle = workbook.createCellStyle();
		
		Font font = workbook.createFont();
		font.setBold(true);
		boldStyle.setFont(font);

		return boldStyle;
	}


	
	public void setBoldStyle(CellStyle boldStyle)
	{
		this.boldStyle = boldStyle;
	}
	
	
}