package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormStartup_P;

public class KtFormStartupExcelRptFile extends ExcelReportFile
{
	public KtFormStartupExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormStartup_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
										"Form Number", "Name of Team/Startup Supported", "Type(s) of Support Provided", 
										"Date (MM/YYYY)", "Faculty/ Centre/ Unit", "Department/ Centre/ Unit",
										"Organizer", "Contact Person",
										"Number of UG Students in the Team / Startup", "Number of PG Students in the Team / Startup",
										"Number of Staff in the Team / Startup", "Number of Alumni in the Team / Startup",
										"Remarks"};
		
		targetDataFields = new String[] {	null,
											"pk.form_no", "title", "act_type",
											"start_date", "fac", "dept",
											"organizer", "ct_person",
											"num_stu_ug", "num_stu_pg",
											"num_staff", "num_alumni",
											"remarks_staff"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, false, 
													false, false, 
													false, false, 
													false, false, 
													true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, true, 
				true, true, 
				true, true, 
				true, true, 
				true};
	}
}
