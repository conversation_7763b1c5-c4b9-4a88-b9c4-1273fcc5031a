package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormSem_P;

public class KtFormSeminarExcelRptFile extends ExcelReportFile
{
	public KtFormSeminarExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormSem_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
										"Form Number", "Seminar/ Workshop Title", "Date (MM-YYYY)", "Faculty/ Centre/ Unit", "Department/ Centre/ Unit",
										"Organizer", "Contact Person", "Target Participants", "Activity Code/ Project Code:", "Free/ Chargeable (F/C)",
										"Income (HK$)", "Income (HK$) (FO)", "Income (HK$) (Remarks fr FO)", "Income (HK$) (RDO)",
										"Expenditure (Direct Cost)", "Expenditure (Direct Cost) (FO)", "Expenditure (Direct Cost) (Remarks fr FO)", "Expenditure (Direct Cost) (RDO)",
										"Number of Key Partners", "Number of Particpants", "Check # of Participants", "Number of Teacher Participants",
										"Number of Principal Participants", "Number of Other Stakeholder Participants (e.g. parents)",
										"Number of Academic and Research Staff Involved (Man-days) [For those classified as (1) or (2) in column 'Target Participants' ONLY]",
										"Number of External Professionals Engaged", "Remarks"};

		targetDataFields = new String[] {	null,
											"pk.form_no", "title", "start_date", "fac", "dept",
											"organizer", "ct_person", "target_pax", "act_code", "free_charge",
											"income_rpt_unit", "income_fo", "income_fo_rem", "income_rdo",
											"expnd_rpt_unit", "expnd_fo", "expnd_fo_rem", "expnd_rdo",
											"num_key_partner", "num_pax", "check_pax", "num_teacher",
											"num_principal", "num_other",
											"staff_man_day",
											"num_ext_prof", "remarks_staff"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, false, false, 
													false, false, false, false, false, 
													false, false, false, false, 
													false, false, false, false, 
													false, false, false, false, 
													false, false, 
													false, 
													false, true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, true, true, 
				true, true, true, true, true, 
				true, true, true, true, 
				true, true, true, true, 
				true, true, true, true, 
				true, true, 
				true, 
				true, true};
	}
}
