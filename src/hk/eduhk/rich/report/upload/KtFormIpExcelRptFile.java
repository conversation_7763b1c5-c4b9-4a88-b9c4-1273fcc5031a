package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormIP_P;

public class KtFormIpExcelRptFile extends ExcelReportFile
{
	public KtFormIpExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormIP_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"), 
				"Product/ IP Title", "Category of IP", "Software License (Y/N)", 
				getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"), 
				"Name of EdUHK Staff who Developed the Product/ IP",
				"Name of Licensee", "HK Licensee/ Non-HK Licensee?", "HK Government/ HK Private Sector?", "Which HK Private Sector?",
				"Category of Licensee", "Total Amount of Upfront Incurred", "Total Amount of Royalty Incurred", "Debit Note Number(s)", getResourceBundle().getString("kt.form.label.income"), 
				"Remarks", 
				"Note (RDO)"};

		targetDataFields = new String[] {	null,
				"pk.form_no", "fac", "dept", 
				"title", "cat", "software_lic", 
				"start_date", "end_date", 
				"staff_name",
				"income_name", "hk_lic", "hk_gov", "hk_pri",
				"org_type", "amt_upfront", "amt_royalty", "debit_note", "income_rpt_unit",
				"remarks_staff", 
				"remarks_kt"}; 
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, false, 
													false, false, 
													false, 
													false, false, false, false, 
													false, false, false, false, false, 
													true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, true, 
				true, true, 
				true, 
				true, true, true, true, 
				true, true, true, true, true, 
				true,
				true};
	}
}
