package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormInvAward_P;

public class KtFormInvAwardExcelRptFile extends ExcelReportFile
{
	public KtFormInvAwardExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormInvAward_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
				"Name of the Innovative Product/ Invention", "Name of Competition / Exhibition / Event",
				"Title of Award", "Award Type", "Date of Receiving the Award(s)", 
				"Name of Principal Inventor/ Awardee", "Name(s) of Other Inventor(s)/ Awardee(s)", 
				"Number of Patent Filed", "Name of the Invention", "Application Number", "Date of Application (MM/YYYY)", "Country/ Region Where the Patent is filed",
				"Number of Patent Granted", "Name of the Patent", "Patent Number", "Date of Patent Granted (MM/YYYY)", "Country/ Region Granting the Patent", "Remarks", 
				"Note (RDO)"};

		targetDataFields = new String[] {	null,
				"pk.form_no", "fac", "dept",
				"ip_name", "event_name",
				"award_title", "award_type", "start_date", 
				"name_pi", "name_other", 
				"num_pat_filed", "patent_filed_name", "patent_filed_num", "patent_filed_date", "patent_filed_country",
				"num_pat_granted", "patent_granted_name", "patent_granted_num", "patent_granted_date", "patent_granted_country", "remarks_staff", 
				"remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, 
													false, false, false, 
													false, false, 
													false, false, false, false, false, 
													false, false, false, false, false, true };*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, 
				true, true, true, 
				true, true, 
				true, true, true, true, true, 
				true, true, true, true, true, true,
				true};
	}
}
