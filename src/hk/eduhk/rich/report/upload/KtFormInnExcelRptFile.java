package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormInn_P;

public class KtFormInnExcelRptFile extends ExcelReportFile
{
	public KtFormInnExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormInn_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number" ,getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
				"Any Research Element in the Project?", "Ownership of IP Right", "Percentage of IP Right Ownership", "Project Title", 
				"Name(s) of Collaborator(s)", getResourceBundle().getString("kt.form.label.act.code"), 
				getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"),
				"Total Number of Project Days", "Total Number of Project Days in the Reporting Year",
				"Principal Investigator", "Funding Source", "Total Approved Budget (HK$)",
				"Income (HK$)", 
				"Number of Key Partners", "Number of Teachers Benefited", "Number of Principals Benefited", "Number of Other Stakeholders Benefited (e.g. parents)",
				"Number of Schools Benefited", "Number of Students Benefited", "Remarks", 
				"Supported by FO?", "Income Remarks From FO", "Income Matching Grant", getResourceBundle().getString("kt.form.label.income.rdo"), "Note (RDO)"};

		targetDataFields = new String[] {	null,
				"pk.form_no", "fac", "dept", 
				"research_element", "ownership_ip_right", "percentage_ownership", "title", 
				"name_colla", "act_code", 
				"start_date" , "end_date",
				"num_proj_day", "num_proj_day_in_yr",
				"principal_inves", "fund_src", "budget",
				"income_rpt_unit", 
				"num_key_partner", "num_teacher", "num_principal", "num_stakeholder",
				"num_school", "num_stu", "remarks_staff", 
				"support_fo", "income_fo_rem", "income_grant", "income_rdo", "remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, false, false, 
													false, false, 
													false, false, 
													false, false, 
													false, false, false, 
													false, 
													false, false, false, false, 
													false, false, true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, true, true, 
				true, true, 
				true, true, 
				true, true, 
				true, true, true, 
				true, 
				true, true, true, true, 
				true, true, true,
				true, true, true, true, true};
	}
}
