package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormProfEngmt_P;

public class KtFormProfEngmtExcelRptFile extends ExcelReportFile
{
	public KtFormProfEngmtExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormProfEngmt_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
										"Form Number", "Programme Title", "Name of EdUHK Staff Conducting the Programme (e.g. Dr. CHAN Tai Man)",
										"Start Date (MM-YYYY)", "End Date (MM-YYYY)", "Faculty/ Centre/ Unit", "Department/ Centre/ Unit", "Organizer",
										"Check # of Engagement", "Number of Teachers Engaged", "Number of Principals Engaged", "Number of Social/Professional Leaders Engaged",
										"Number of Other Professionals Engaged", "Number of Participants Benefited", "Remarks"};

		targetDataFields = new String[] {	null,
											"pk.form_no", "title", "cond_staff",
											"start_date", "end_date", "fac", "dept", "organizer",
											"check_engage", "num_teacher", "num_principal", "num_leader",
											"num_other", "num_pax", "remarks_staff"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, false, false, false, 
													false, false, false, false, 
													false, false, true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, true, true, true, 
				true, true, true, true, 
				true, true, true};
	}
}

