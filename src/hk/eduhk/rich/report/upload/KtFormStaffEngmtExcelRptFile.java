package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormStaffEngmt_P;

public class KtFormStaffEngmtExcelRptFile extends ExcelReportFile
{
	public KtFormStaffEngmtExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormStaffEngmt_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
				"Name of EdUHK Staff", "Administrative / Technical Staff?",
				"Start Date (MM-YYYY)", "End Date (MM-YYYY)", 
				"Name of External Body", getResourceBundle().getString("kt.form.label.region"),  "Nature of External Body", "Post Engaged",
				"Remarks", 
				"Note (RDO)"};
		
		targetDataFields = new String[] {	null,
				"pk.form_no", "fac", "dept",
				"staff_name", "admin_tech",
				"start_date", "end_date", 
				"ext_body_name", "regionValue", "ext_body_nature", "post_engaged",
				"remarks_staff", 
				"remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, 
													false, true,
													false, false, false, false, 
													true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, 
				true, true,
				true, true, true, true, 
				true,
				true};
		
	}
}
