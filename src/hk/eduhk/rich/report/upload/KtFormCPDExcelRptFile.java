package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormCPD_P;

public class KtFormCPDExcelRptFile extends ExcelReportFile
{
	public KtFormCPDExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormCPD_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number",getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"), 
				"Any Research Element in the Course?", "Is It a Course/ Programme?", "Entrepreneurship Element?",
				"Course Title", getResourceBundle().getString("kt.form.label.act.code"), "Mode", 
				getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"), 
				"Total Number of Course Days", "Total Number of Course Days in the Reporting Year",
				getResourceBundle().getString("kt.form.label.eduhk.org"), getResourceBundle().getString("kt.form.label.region"), 
				"Free/ Chargeable (F/C)",
				getResourceBundle().getString("kt.form.label.budget.total"), 
				getResourceBundle().getString("kt.form.label.income"), 
				getResourceBundle().getString("kt.form.label.expend"), 
				getResourceBundle().getString("kt.form.label.num.key"), getResourceBundle().getString("kt.form.label.num.tea"), getResourceBundle().getString("kt.form.label.num.pri"), 
				getResourceBundle().getString("kt.form.label.num.oth.stakeholder"), getResourceBundle().getString("kt.form.label.num.sch"), 
				"Number of Student Contact Hours",  "Remarks", 
				"Income Remarks From FO ", getResourceBundle().getString("kt.form.label.income.rdo"), "Total Expenditure from the Event (Remarks from FO) ", "Total Expenditure from the Event (RDO) ", "Note (RDO)"};

		targetDataFields = new String[] {	null,
				"pk.form_no",  "fac", "dept", 
				"research_element", "crse_prog", "ent_element",
				"title", "act_code", "act_mode", 
				"start_date", "end_date",
				"num_proj_day", "num_proj_day_in_yr",
				"eduhk_org", "regionValue",
				"free_charge",
				"budget",
				"income_rpt_unit", 
				"expnd_rpt_unit",
				"num_key_partner", "num_teacher", "num_principal",
				"num_other", "num_school",
				"num_stu_contact_hr", "remarks_staff", 
				"income_fo_rem", "income_rdo", "expnd_fo_rem", "expnd_rdo", "remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, false,
													false, false, false, 
													false, false,
													false, false, 
													false, false, 
													false,  
													false, 
													false, 
													false, 
													false, false, false, 
													false, false, 
													false, true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, true,
				true, true, true, 
				true, true,
				true, true, 
				true, true, 
				true,  
				true, 
				true, 
				true, 
				true, true, true, 
				true, true, 
				true, true,
				true, true, true, true, true};
	}
}
