package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormCons_P;

public class KtFormConsultExcelRptFile extends ExcelReportFile
{
	public KtFormConsultExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormCons_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"), 
				"Any Research Element in the Project?", "Is It a Course/ Programme for Educational Professionals?", "Project Title", getResourceBundle().getString("kt.form.label.act.code"), 
				getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"),
				"Total Number of Project Days", "Total Number of Project Days in the Reporting Year",
				"Principal Investigator", "Name of Funding Source", "HK Fund/ Non-HK Fund?", "HK Government Fund/ HK Private Fund?", "Which type of HK Private Fund?",
				"Type of Organization Providing the Funding", "Joint Consultancy Project?",
				"Role of EdUHK:Coordinating/Participating (C/P)", 
				"Name of the Coordinating Institution", "Funding Source of the Coordinating Institution", "Project Title of the Coordinating Institution", "Principal Investigator of the Coordinating Institution",
				"Total Approved Budget (HK$)",
				getResourceBundle().getString("kt.form.label.income"), 
				getResourceBundle().getString("kt.form.label.num.key"), 
				"Number of Teachers Benefited", "Number of Principals Benefited", 
				"Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)",getResourceBundle().getString("kt.form.label.num.stu"), 
				getResourceBundle().getString("kt.form.label.num.sch"), "Remarks", 
				"Supported by FO?", "Cumulative Income (FO)", "Income Remarks From FO", "Income Matching Grant", getResourceBundle().getString("kt.form.label.income.rdo"), "Note (RDO)"};

		targetDataFields = new String[] {	null,
				"pk.form_no", "fac", "dept", 
				"research_element", "edu_crse", "title", "act_code",
				"start_date" , "end_date",
				"num_proj_day", "num_proj_day_in_yr",
				"principal_inves", "fund_src", "hk_fund", "hk_gov_fund", "hk_pri_fund", 
				"fund_src_org", "joint_proj",
				"eduhk_role", 
				"name_coor", "fund_src_coor", "proj_title_coor", "pi_coor", 
				"budget",
				"income_rpt_unit", 
				"num_key_partner",
				"num_teacher", "num_principal", 
				"num_stakeholder", "num_stu",
				"num_school","remarks_staff", 
				"support_fo", "cumu_income_fo", "income_fo_rem", "income_grant", "income_rdo", "remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, false, false, 
													false, false, 
													false, false, 
													false, false, false, false, true, 
													false, false, 
													true, 
													true, true, true, true, 
													false, 
													false, 
													false, 
													false, false, 
													false, false, 
													false, true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, true, true, 
				true, true, 
				true, true, 
				true, true, true, true, true, 
				true, true, 
				true, 
				true, true, true, true, 
				true, 
				true, 
				true, 
				true, true, 
				true, true, 
				true, true,
				true, true, true, true, true, true};
	}
}
