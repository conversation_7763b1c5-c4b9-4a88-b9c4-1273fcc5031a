package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormProfConf_P;

public class KtFormProfConfExcelRptFile extends ExcelReportFile
{
	public KtFormProfConfExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormProfConf_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {	null,
				"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"),
				"Entrepreneurship Element?", "Performance Arts?",
				"Activity Title", "Type of Activity", "Held at Museum/ Galleries Owned by EdUHK?", "Mode",
					getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"), 
					"Total Number of Activity Days", "Total Number of Activity Days in the Reporting Year",
					getResourceBundle().getString("kt.form.label.eduhk.org"),
					getResourceBundle().getString("kt.form.label.region"), 
					"Target Participants", getResourceBundle().getString("kt.form.label.act.code"), getResourceBundle().getString("kt.form.label.budget.holder"), getResourceBundle().getString("kt.form.label.free"), 
					getResourceBundle().getString("kt.form.label.budget.approval"), getResourceBundle().getString("kt.form.label.budget.total"), 
					getResourceBundle().getString("kt.form.label.income"), 
					getResourceBundle().getString("kt.form.label.expend"),
					getResourceBundle().getString("kt.form.label.num.key"),  "Number of Student Participants", "Number of Teacher Participants", "Number of Principal Participants", "Number of Other Participants (e.g. Parents)", "Number of Schools Benefited", 
					"Remarks", 
					getResourceBundle().getString("kt.form.label.income.fo"), getResourceBundle().getString("kt.form.label.income.fo.remark"), getResourceBundle().getString("kt.form.label.income.rdo"), getResourceBundle().getString("kt.form.label.expend.fo"), getResourceBundle().getString("kt.form.label.expend.fo.remark"), getResourceBundle().getString("kt.form.label.expend.rdo"), "Note (RDO)"};

		targetDataFields = new String[] {	null,
				"pk.form_no", "fac", "dept",
				"ent_element", "perf_art",
				"title", "event_type", "museum", "act_mode", "start_date", "end_date", 
				"num_proj_day", "num_proj_day_in_yr",
				"eduhk_org",
				"regionValue", 
				"target_pax", "act_code", "budget_holder", "free_charge",
				"budget_approval", "budget",
				"income_rpt_unit", 
				"expnd_rpt_unit", 
				"num_key_partner", "num_stu", "num_teacher", "num_principal", "num_other", "num_school",
				"remarks_staff", 
				"income_fo", "income_fo_rem", "income_rdo", "expnd_fo", "expnd_fo_rem", "expnd_rdo", "remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true,
													true, false, false, 
													false, false, 
													false, false, false, false, false, false, 
													false, false, 
													false, 
													false, 
													false, false, false, false, 
													false, false,  
													false, 
													false,
													false, false, false, false, false, false,
													true};*/
		nullableTargetDataFields = new boolean[] {	true,
				true, true, true, 
				true, true, 
				true, true, true, true, true, true, 
				true, true, 
				true, 
				true, 
				true, true, true, true, 
				true, true,  
				true, 
				true,
				true, true, true, true, true, true,
				true,
				true, true, true, true, true, true, true};
	}
}
