package hk.eduhk.rich.report.upload;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.*;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.bundle.MessageBundle;
import nl.basjes.shaded.org.springframework.util.ObjectUtils;

public abstract class ExcelReportFile
{
	protected Workbook workbook;
	
	protected String targetClassName;
	
	protected String[] headerNames;
	
	protected String[] targetDataFields;
	
	protected Class<?>[] targetDataTypes;
	
	protected boolean[] nullableTargetDataFields;
	
	protected int headerRowNum;
	
	private static Map<String, Class<?>> fieldClassMap = new HashMap<String, Class<?>>();
	
	private Map<Integer, List<String>> invalidMsgMap;
	
	public abstract void init(Workbook workbook);

	public int getHeaderSize() 
	{
		return headerNames.length;
	}
	
	public int getHeaderRowNum()
	{
		return headerRowNum;
	}

	
	public void setHeaderRowNum(int headerRowNum)
	{
		this.headerRowNum = headerRowNum;
	}
	
	public Map<Integer, List<String>> getInvalidMsgMap()
	{
		if(invalidMsgMap == null) 
		{
			invalidMsgMap = new LinkedHashMap<Integer, List<String>>();
		}
		return invalidMsgMap;
	}
	
	
	public void setinValidMsgMap(Map<Integer, List<String>> invalidMsgMap) 
	{
		this.invalidMsgMap = invalidMsgMap;
	}
	
	
	public boolean isValidHeader(String sheetName) 
	{
		if(sheetName != null) 
		{
			Sheet sheet = workbook.getSheet(sheetName);
			Row row = sheet.getRow(headerRowNum);
			
			for(int n=0; n<headerNames.length; n++)
			{
				if(headerNames[n]!=null)
				{
					Cell cell = row.getCell(n);
					if(cell!=null && cell.getStringCellValue()!=null) 
					{
						if(!StringUtils.equals(headerNames[n], cell.getStringCellValue())) 
						{
							if(getInvalidMsgMap()!=null) 
							{
								List<String> errMsgList = getInvalidMsgMap().get(headerRowNum);
								if(errMsgList == null) errMsgList = new ArrayList<String>();
								
								String msg = "Invalid header.";
								
								errMsgList.add(msg);
								invalidMsgMap.put(headerRowNum, errMsgList);
								return false;
							}
						}
					}
					
				}
			}
		}
		
		return true;
	}
	
	public Map<Integer, Object> getDataMap(String sheetName)
	{
		Map<Integer, Object> objMap = new LinkedHashMap<Integer, Object>();
		try 
		{
			Sheet sheet = workbook.getSheet(sheetName);
			isValidHeader(sheetName);
			
			Iterator<Row> itr = sheet.iterator();
			
			while(itr.hasNext()) 
			{
				Row row = itr.next();
				
				if(row.getRowNum()<=headerRowNum) continue;
				
				Object obj = null;
				
				Class<?> objClass = Class.forName(targetClassName);
				Constructor<?> constr = objClass.getConstructor();
				obj = constr.newInstance();
				
				for(int n=0; n<targetDataFields.length; n++)
				{
					String field = targetDataFields[n];
					
					String fieldName = (GenericValidator.isBlankOrNull(headerNames[n])) ? field : headerNames[n];

					try 
					{
						if(field != null) 
						{
							Cell valueCell = row.getCell(n);
							
							//Object valueObj = getCellValue(valueCell);
							
							Class<?> propertyClass = getFieldClass(obj, field);
							
							Object objValue = getCellValue(valueCell);
							
							if(propertyClass != null && objValue != null && !ObjectUtils.isEmpty(objValue)) 
							{
								if(propertyClass.equals(java.lang.String.class)) 
								{
									String strObjValue = String.valueOf(objValue);
									PropertyUtils.setProperty(obj, field, strObjValue);
								}
								else if(propertyClass.equals(java.util.Date.class))
								{
									PropertyUtils.setProperty(obj, field, ((java.util.Date)getCellValue(valueCell, true)));
								}
								else if(propertyClass.equals(java.lang.Integer.class) || propertyClass.equals(int.class))
								{
									//TODO
									Double value = (Double)objValue;
									if(isInteger(value))
									{
										PropertyUtils.setProperty(obj, field, value.intValue());
									}
									else
									{
										invalidInput(row.getRowNum(), fieldName, false);
									}
									
								}
								else if(propertyClass.equals(java.lang.Double.class) || propertyClass.equals(double.class))
								{
									PropertyUtils.setProperty(obj, field, ((Double)objValue));
								}
								else
								{
									PropertyUtils.setProperty(obj, field, objValue.toString());
								}
							}
							else
							{
								if(objValue == null || ObjectUtils.isEmpty(objValue)) 
								{
									boolean nullable = nullableTargetDataFields[n];
									if(!nullable)
										invalidInput(row.getRowNum(), fieldName, true);
								}
								else
								{
									PropertyUtils.setProperty(obj, field, getCellValue(valueCell).toString());
								}
							}
							
						}
					}
					catch(Exception e) 
					{
						invalidInput(row.getRowNum(), fieldName, false);
					}
					
				}
				//objList.add(obj);
				objMap.put(row.getRowNum(), obj);
			}
			
		}
		catch(Exception e) 
		{
			e.printStackTrace();
		}
		
		return objMap;
	}
	
	private Object getCellValue(Cell cell) 
	{
		return getCellValue(cell, false);
	}
	
	private Object getCellValue(Cell cell, boolean isDateCell) 
	{
		Object obj = null;
		
		if(cell!=null) 
		{
			CellType cellType = cell.getCellType();
			
			if(cellType.equals(CellType.STRING)) 
			{
				obj = cell.getStringCellValue();
			}
			else if(cellType.equals(CellType.NUMERIC)) 
			{
				obj = (isDateCell) ? cell.getDateCellValue() : cell.getNumericCellValue();
			}
			else if(cellType.equals(CellType.BOOLEAN)) 
			{
				obj = cell.getBooleanCellValue();
			}
			else if(cellType.equals(CellType.ERROR)) 
			{
				obj = cell.getErrorCellValue();
			}
		}
		return obj;
	}
	
	public static Class<?> getFieldClass(Object obj, String fieldName) throws ReflectiveOperationException
	{
		Class<?> fieldClass = null;
		
		if (obj != null && StringUtils.isNotBlank(fieldName))
		{
			String className = obj.getClass().getName();
			String lookupName = className + "." + fieldName;
			
			fieldClass = fieldClassMap.get(lookupName);
			
			// Lookup the class from the Map first
			if (fieldClass == null)
			{
				// Retrieve the value from the getter first
				String getterName = "get" + StringUtils.capitalize(fieldName);
				Method method = MethodUtils.getMatchingMethod(obj.getClass(), getterName, new Class<?>[] {});
				fieldClass = (method != null) ? method.getReturnType() : null;
				
				// Cannot invoke the getter, get the field from the instance
				if (fieldClass == null)
				{
					fieldClass = PropertyUtils.getPropertyType(obj, fieldName);
				}
				
				// Save the result in the Map
				if (fieldClass != null)
				{
					fieldClassMap.put(lookupName, fieldClass);
				}
			}
		}
		
		return fieldClass;
	}
	
	public Date getDateValue(double valueObj) 
	{
		Date obj = null;
		
		return obj;
	}
	
	private boolean isInteger(Double value) 
	{
		if(value != null) 
		{
			return (value % 1 ==0);
		}
	
		return false;
	}
	
	private void invalidInput(Integer rowNum, String fieldName, boolean isEmptyOrNull) 
	{
		if(getInvalidMsgMap()!=null) 
		{
			List<String> errMsgList = getInvalidMsgMap().get(rowNum);
			if(errMsgList == null) errMsgList = new ArrayList<String>();
			
			String msg = "";
			
			if(isEmptyOrNull)
			{
				msg = fieldName +" cannot be null.";
			}
			else
			{
				msg = "Invalid input on "+fieldName+".";
			}
			errMsgList.add(msg);
			invalidMsgMap.put(rowNum, errMsgList);
		}
	}
	
	public ResourceBundle getResourceBundle()
	{
		ResourceBundle bundle = MessageBundle.getResourceBundle();
		return bundle;
	}
}
