package hk.eduhk.rich.report.upload;

import org.apache.poi.ss.usermodel.Workbook;

import hk.eduhk.rich.entity.form.KtFormCntProj_P;

public class KtFormCntProjExcelRptFile extends ExcelReportFile
{
	public KtFormCntProjExcelRptFile()
	{
		
	}
	
	public void init(Workbook workbook)
	{
		this.workbook = workbook;
		
		targetClassName = KtFormCntProj_P.class.getName();
		
		headerRowNum = 1;
		
		headerNames = new String[] {null,"Form Number", getResourceBundle().getString("kt.form.label.kt.fac"), getResourceBundle().getString("kt.form.label.kt.dept"), 
				"Any Research Element in the Project?", "Ownership of IP Right", "Project Title", getResourceBundle().getString("kt.form.label.act.code"), 
				getResourceBundle().getString("kt.form.label.date.start"), getResourceBundle().getString("kt.form.label.date.end"),
				"Total Number of Project Days", "Total Number of Project Days in the Reporting Year",
				"Principal Investigator", "Funding Source", "Total Approved Budget (HK$)",
				getResourceBundle().getString("kt.form.label.income"), 
				"Number of Key Partners", "Number of Teachers Benefited", "Number of Principals Benefited", "Number of Other Stakeholders Benefited (e.g. parents)",
				"Number of Students Benefited", "Number of Schools Benefited", "Remarks", 
				"Supported by FO?", "Income Remarks From FO", "Income Matching Grant", getResourceBundle().getString("kt.form.label.income.rdo"), "Note (RDO)"};

		targetDataFields = new String[] {	null,"pk.form_no", "fac", "dept", 
				"research_element", "ownership_ip_right", "title", "act_code", 
				"start_date" , "end_date",
				"num_proj_day", "num_proj_day_in_yr",
				"principal_inves", "fund_src", "budget",
				"income_rpt_unit", 
				"num_key_partner", "num_teacher", "num_principal", "num_stakeholder",
				"num_stu", "num_school", "remarks_staff", 
				"support_fo", "income_fo_rem", "income_grant", "income_rdo", "remarks_kt"};
		
		/*nullableTargetDataFields = new boolean[] {	true, true, false, false, 
													false, false, false, false, 
													false, false,
													false, false, 
													false, false, false,  
													false, 
													false, false, false, false, 
													false, false, true};*/
		nullableTargetDataFields = new boolean[] {	true, true, true, true, 
				true, true, true, true, 
				true, true,
				true, true, 
				true, true, true,  
				true, 
				true, true, true, true, 
				true, true, true,
				true, true, true, true, true};
	}
}
