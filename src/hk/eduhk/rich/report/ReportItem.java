package hk.eduhk.rich.report;

import java.util.*;
import java.util.function.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;


public class ReportItem<T, K>
{
	
	private T itemKey;
	
	private Integer itemIndex = null;
	
	private List<ReportField<T, ?>> fieldList = new ArrayList<>();
	
	private int startCellNum;
	
	
	/**
	 * Constructor
	 * 
	 * @param itemIdx
	 */
	public ReportItem(T itemKey)
	{
		this.itemKey = itemKey;
	}
	
	
	public T getItemKey()
	{
		return itemKey;
	}
	
	
	public Integer getItemIndex()
	{
		return itemIndex;
	}

	
	public void setItemIndex(Integer itemIndex)
	{
		this.itemIndex = itemIndex;
	}


	public void addField(String name, Function<T, ?> getterFunc)
	{
		addField(new ReportField<>(name, getterFunc));
	}


	public void addField(ReportField<T, ?> field)
	{
		fieldList.add(field);
	}
	
	
	public List<ReportField<T, ?>> getFieldList()
	{
		return fieldList;
	}
		
	
	public int getStartCellNum()
	{
		return startCellNum;
	}

	
	public void setStartCellNum(int startCellNum)
	{
		this.startCellNum = startCellNum;
	}


	public int getCellCount()
	{
		return CollectionUtils.size(fieldList);
	}
	
	
	void writeHeader(Row row)
	{
		// Set cell values of the 3rd row (header)
		for (int n=0;n<fieldList.size();n++)
		{
			int cellIdx = getStartCellNum() + n;
			Cell cell = row.getCell(cellIdx, MissingCellPolicy.CREATE_NULL_AS_BLANK);
			
			ReportField<T,?> field = fieldList.get(n);
			cell.setCellValue(field.getName());
		}
	}
		
	
	public void writeData(Sheet sheet, int rowNum, T data)
	{
		if (CollectionUtils.isNotEmpty(fieldList) && rowNum >= 0)
		{
			Row row = sheet.getRow(rowNum);
			if (row != null)
			{
				for (int n=0;n<fieldList.size();n++)
				{
					ReportField<T,?> field = fieldList.get(n);
					Cell cell = row.getCell(getStartCellNum()+n, MissingCellPolicy.CREATE_NULL_AS_BLANK);
					field.writeCell(cell, data);
				}
			}
		}
	}


	@Override
	public String toString()
	{
		return "ReportItem [itemIdx=" + itemIndex + "]";
	}

}
