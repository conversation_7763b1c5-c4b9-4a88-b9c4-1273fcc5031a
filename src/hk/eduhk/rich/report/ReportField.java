package hk.eduhk.rich.report;

import java.util.Collection;
import java.util.function.Function;

import org.apache.poi.ss.usermodel.*;


public class ReportField<T, R>
{
	
	// Field name
	private String name;
	
	// Function that extract the target value from the data instance.
	private Function<T, R> valueGetter; 
	
	// Cell style that applies to the cell
	private CellStyle cellStyle;
	
	private Integer columnWidth = null;
	private boolean autoSizeColumn = false;
	
	
	public ReportField(String name, Function<T, R> valueGetter)
	{
		this(name, valueGetter, null);
	}

	
	public ReportField(String name, Function<T, R> valueGetter, CellStyle cellStyle)
	{
		if (valueGetter == null) throw new NullPointerException("valueGetter cannot be null");
		
		this.name = name;
		this.valueGetter = valueGetter;
		this.cellStyle = cellStyle;
	}
	
	
	public String getName()
	{
		return name;
	}

	
	public ReportField<T, R> setName(String name)
	{
		this.name = name;
		return this;
	}

	
	public Function<T, R> getValueGetter()
	{
		return valueGetter;
	}

	
	public ReportField<T, R> setValueGetter(Function<T, R> valueGetter)
	{
		this.valueGetter = valueGetter;
		return this;
	}

	
	public CellStyle getCellStyle()
	{
		return cellStyle;
	}

	
	public ReportField<T, R> setCellStyle(CellStyle cellStyle)
	{ 
		this.cellStyle = cellStyle;
		return this;
	}
			
	
	public Integer getColumnWidth()
	{
		return columnWidth;
	}

	
	/**
	 * Set the column width (in unit of a character width)
	 * 
	 * @param columnWidth
	 * @return
	 */
	public ReportField<T, R> setColumnWidth(Integer columnWidth)
	{
		this.columnWidth = columnWidth;
		return this;
	}


	public boolean isAutoSizeColumn()
	{
		return autoSizeColumn;
	}

	
	public ReportField<T, R> setAutoSizeColumn(boolean autoSizeColumn)
	{
		this.autoSizeColumn = autoSizeColumn;
		return this;
	}


	void writeCell(Cell cell, T data)
	{
		if (cell != null)
		{
			// Write the field value to the cell
			R value = (data != null) ? valueGetter.apply(data) : null;
			
			if (value != null)
			{
				Class<?> valueClass = value.getClass();
				if (valueClass != null)
				{
					// Date classes including java.sql.Timestamp
					if (java.util.Date.class.isAssignableFrom(valueClass))
					{
						cell.setCellValue((java.util.Date) value);
					}
					else if (valueClass.equals(java.lang.String.class))
					{
						cell.setCellValue((String)value);
					}
					else if (valueClass.equals(java.lang.Integer.class) || valueClass.equals(int.class))
					{
						cell.setCellValue((Integer)value);
					}
					else if (valueClass.equals(java.lang.Double.class) || valueClass.equals(double.class))
					{
						cell.setCellValue((Double)value);
					}
					else if (Collection.class.isAssignableFrom(valueClass))
					{
						// Remove the bracket from the String
						String strValue = value.toString();
						strValue = strValue.substring(1, strValue.length()-1);
						cell.setCellValue(strValue);
					}
					else
					{
						cell.setCellValue(value.toString());
					}
				}
			}
			
			// Set cell style
			if (cellStyle != null)
			{
				cell.setCellStyle(cellStyle);
			}
		}
	}
	
}