package hk.eduhk.rich.report;


public class ReportDataWrapper<T, K>
{

	private T data;
	private int rowNum;
	private ReportItem<T, K> item;
	
	
	public ReportDataWrapper(T data, int rowNum, ReportItem<T, K> item)
	{
		this.data = data;
		this.rowNum = rowNum;
		this.item = item;
	}
	
	
	/**
	 * Get the wrapped data.
	 * 
	 * @return
	 */
	public T getData()
	{
		return data;
	}
	
	
	/**
	 * Get the row number of this data. 
	 * 
	 * @return
	 */
	public int getRowNum()
	{
		return rowNum;
	}
	
	
	
	/**
	 * Get the ReportItem that will use for writing this data.
	 * 
	 * @return
	 */
		
	public ReportItem<T, K> getReportItem()
	{
		return item;
	}

}
