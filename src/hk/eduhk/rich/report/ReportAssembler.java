package hk.eduhk.rich.report;

import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.ss.util.CellRangeAddress;


public class ReportAssembler<K>
{
	
	private Workbook workbook;
		
	private List<ReportSection<?,K>> sectionList = new ArrayList<>();
	private List<K> rowKeyList;
	
	private int headerRowCount;
	private boolean includeSectionHeader = true;
	private boolean includeItemHeader = true;
	private List<String> headerNotes;

	
	public ReportAssembler(Workbook workbook)
	{
		super();
		this.workbook = workbook;
	}
	
	
	public void addSection(ReportSection<?,K> section)
	{
		section.setWorkbook(workbook);
		section.setIncludeSectionHeader(includeSectionHeader);
		section.setIncludeItemHeader(includeItemHeader);
		sectionList.add(section);
	}
	
	
	public boolean isIncludeSectionHeader()
	{
		return includeSectionHeader;
	}

	
	public void setIncludeSectionHeader(boolean includeSectionHeader)
	{
		this.includeSectionHeader = includeSectionHeader;
	}

	
	public boolean isIncludeItemHeader()
	{
		return includeItemHeader;
	}

	
	public void setIncludeItemHeader(boolean includeItemHeader)
	{
		this.includeItemHeader = includeItemHeader;
	}


	
	public List<String> getHeaderNotes()
	{
		return headerNotes;
	}


	
	public void setHeaderNotes(List<String> headerNotes)
	{
		this.headerNotes = headerNotes;
	}


	public List<K> getRowKeyList()
	{
		return rowKeyList;
	}

	
	public void setRowKeyList(List<K> rowKeyList)
	{
		this.rowKeyList = rowKeyList;
		
		if (CollectionUtils.isNotEmpty(sectionList))
		{
			for (ReportSection<?,K> section : sectionList)
			{
				section.setRowKeyList(rowKeyList);
			}
		}
	}
	
	
	/**
	 * Write each ReportSection to the sheet including header rows and data rows. 
	 * 
	 * @param sheet
	 */
	public void write(Sheet sheet)
	{
		if (sheet == null) throw new NullPointerException("sheet cannot be null");
		
		headerRowCount = 1 + (includeSectionHeader ? 1 : 0) + (includeItemHeader ? 1 : 0);
		
		// Determine total number of rows in the sheet
		// Total rows = header rows + data rows
		int totalRowCount = headerRowCount + CollectionUtils.size(getRowKeyList());
		
		// Initialize all Rows in the Sheet
		for (int n=0;n<totalRowCount;n++)
		{
			Row row = sheet.getRow(n);
			if (row == null) row = sheet.createRow(n);
		}
		
		int startCellNum = 0;
		
		if (CollectionUtils.isNotEmpty(sectionList))
		{
			for (int n=0;n<sectionList.size();n++)
			{
				// Determine the start cell number for writing the current ReportSection
				ReportSection<?, K> prevSect = (n > 0) ? sectionList.get(n-1) : null;
				startCellNum += (prevSect != null) ? prevSect.getCellCount() : 0;
				
				// Write the section content to the Sheet
				ReportSection<?, K> section = sectionList.get(n);
				section.setRowKeyList(getRowKeyList());
				section.write(sheet, startCellNum);
				section.dispose();
			}
			
			// Set auto filter
	    	Row row = sheet.getRow(headerRowCount-1+ (headerNotes!=null?headerNotes.size():0) );
			sheet.setAutoFilter(new CellRangeAddress(row.getRowNum(), row.getRowNum(), row.getFirstCellNum(), row.getLastCellNum()-1));
			
			// Adjust column width of each section
			for (ReportSection<?, K> section : sectionList)
			{
				section.adjustColumnWidth(sheet, true);
			}
		}
		
		// Freeze header rows
    	sheet.createFreezePane(0, headerRowCount+(headerNotes!=null?headerNotes.size():0));
    	
    	// Chaufy added: add header Notes
    	if (headerNotes != null)
    	{
    		sheet.shiftRows(0, totalRowCount, headerNotes.size());
    		int rowCount = 0;
    		for (String note : headerNotes)
    		{
    			Row row = sheet.getRow(rowCount);
    			if (row == null) row = sheet.createRow(rowCount);
    			Cell cell = row.getCell(0, MissingCellPolicy.CREATE_NULL_AS_BLANK);
    			cell.setCellValue(note);
    			CellStyle cs = workbook.createCellStyle();
    			cs.setWrapText(true);
    			cell.setCellStyle(cs);
    			sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, 20));
    			if(rowCount==1)row.setHeight((short) 2000);
    			rowCount++;
    		}
    	}
	}
	
}