package hk.eduhk.rich.report;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.ss.util.CellRangeAddress;


public abstract class ReportSection<T, K>
{
	
	public static final int WIDTH_FILTER_BUTTON = 1024;
	
	protected Workbook workbook;
	
	// Default Comparator
	private Comparator<T> defaultComparator = new Comparator<T>() {public int compare(T o1, T o2) {return 0;}};  
	
	private List<K> rowKeyList;
	
	// Mapping of row key to the corresponding row number in the Sheet
	private Map<K, Integer> rowKeyIdxMap;

	private List<ReportItem<T, K>> itemList;
	
	// Data collection
	private List<ReportDataWrapper<T, K>> dataWrapperList;
	
	// Indicate whether the report should show ReportSection header, 
	// even there is no data retrieved.
	private boolean includeEmptyItem = true;
	
	private int headerRowCount;
	private boolean includeSectionHeader = true;
	private boolean includeItemHeader = true;
	
	// First cell number in the Sheet for this ReportSection
	private int startCellNum = 0;
	
	// Number of cells included in this ReportCategory
	// This number is the sum of cells included in all ReportItems
	private Integer cellCount = null;
	
	private Logger logger = Logger.getLogger(this.getClass().getName());
	
	
	/**
	 * Default constructor.
	 */
	public ReportSection()
	{
		super();
	}
		
	
	public Workbook getWorkbook()
	{
		return workbook;
	}

	
	public void setWorkbook(Workbook workbook)
	{
		this.workbook = workbook;
	}


	/**
	 * Get the section name. This is for building the section header in the report.
	 * 
	 * @return
	 */
	public abstract String getSectionName();
	
	
	/**
	 * Get the item name, given the key. This is for building the item header in the report.
	 * 
	 * @param itemKey
	 * @return
	 */
	public abstract String getItemName(T itemKey);

	
	public List<K> getRowKeyList()
	{
		return rowKeyList;
	}

	
	public void setRowKeyList(List<K> rowKeyList)
	{
		this.rowKeyList = (rowKeyList != null) ? rowKeyList : Collections.emptyList();
		this.rowKeyIdxMap = null;
	}
	
		
	public boolean isIncludeSectionHeader()
	{
		return includeSectionHeader;
	}

	
	public void setIncludeSectionHeader(boolean includeSectionHeader)
	{
		this.includeSectionHeader = includeSectionHeader;
	}

	
	public boolean isIncludeItemHeader()
	{
		return includeItemHeader;
	}

	
	public void setIncludeItemHeader(boolean includeItemHeader)
	{
		this.includeItemHeader = includeItemHeader;
	}


	public boolean isIncludeEmptyItem()
	{
		return includeEmptyItem;
	}

	
	public void setIncludeEmptyItem(boolean includeEmptyItem)
	{
		this.includeEmptyItem = includeEmptyItem;
	}


	/**
	 * Get the target row index of the data.
	 * 
	 * @param data
	 * @return
	 */
	public Integer getRowKeyIndex(K rowKey)
	{
		// Initialize the mapping
		if (rowKeyIdxMap == null)
		{
			rowKeyIdxMap = new HashMap<>();
			
			for (int n=0;n<rowKeyList.size();n++)
			{
				rowKeyIdxMap.put(rowKeyList.get(n), n);
			}
		}
		
		return rowKeyIdxMap.get(rowKey);
	}
	
	
	/**
	 * Get the row key from the provided data.
	 * The row key is for determining the actual row number in the Sheet.
	 * 
	 * @param data
	 * @return
	 */
	public abstract Function<T, K> getRowKeyFunction();
	
	
	/**
	 * Default Comparator which considering two input arguments are always equals.
	 */
	public Comparator<T> getItemComparator()
	{
		return defaultComparator;
	}
		
	
	/**
	 * Default implementation which returns an empty list. 
	 * The implementation class must override this method to provide the data.
	 * 
	 * @return
	 */
	public abstract Collection<T> queryData();

	
	public abstract void initReportItem(ReportItem<T, K> item, T itemKey);
	
	
	/**
	 * Get the total number cells included in this ReportCategory 
	 * 
	 * @return
	 */
	public int getCellCount()
	{
		return cellCount;
	}
	
	
	/**
	 * Write headers and data to the Sheet from the 1st cell.
	 * 
	 * @param sheet
	 * @param startCellNum
	 */
	public void write(Sheet sheet)
	{
		write(sheet, 0);
	}
	
		
	/**
	 * Write headers and data to the Sheet, starting from the cell at startCellNum
	 * 
	 * @param sheet
	 * @param startCellNum
	 */
	public void write(Sheet sheet, int startCellNum)
	{
		this.startCellNum = startCellNum;
		
		// Reset the count
		cellCount = 0;
		
		// Temporary storage of ReportItem for different itemKey
		Map<T, List<ReportItem<T, K>>>itemListMap = new TreeMap<>(getItemComparator());
		
		Map<K, Map<T, Integer>> rowKeyItemDupMap = new LinkedHashMap<>();
		
		dataWrapperList = new ArrayList<>();
		
		// Iteration before actual writing
		// This is for transforming the data into the target form.
		Collection<T> dataCol = queryData();
		for (T data : dataCol)
		{
			K rowKey = getRowKeyFunction().apply(data);
			
			// Get the target row number of this data
			Integer rowNum = (rowKey != null) ? getRowKeyIndex(rowKey) : null;
			
			// Handle it if the row can be mapped.
			if (rowNum != null)
			{
				// Update the duplicate count of itemKey * rowKey
				// This is for determining how many ReportIterm have to create
				Map<T, Integer> itemKeyDupMap = rowKeyItemDupMap.get(rowKey);
				if (itemKeyDupMap == null) rowKeyItemDupMap.put(rowKey, itemKeyDupMap = new TreeMap<>(getItemComparator()));
				
				// Find out the maximum duplication of the given itemKey
				Integer count = itemKeyDupMap.get(data);
				count = (count != null) ? count + 1 : 0;
				itemKeyDupMap.put(data,  count);
				
				List<ReportItem<T, K>> itemList = itemListMap.get(data);
				if (itemList == null) itemListMap.put(data, (itemList = new ArrayList<>()));
				ReportItem<T, K> item = (count < itemList.size()) ? itemList.get(count) : null;
				
				if (item == null)
				{
					// Instantiate a new ReportItem
					item = new ReportItem<T, K>(data);
					
					// Set the itemIndex if there is duplicate.
					if (itemList.size() > 0)
					{
						itemList.get(0).setItemIndex(0);
						item.setItemIndex(itemList.size());
					}
					
					initReportItem(item, data);
					itemList.add(item);
					
					// Sum up to derive the total cell count for this ReportSection
					cellCount += item.getCellCount();
				}
				
				ReportDataWrapper<T, K> dataWrapper = new ReportDataWrapper<T, K>(data, rowNum, item);
				dataWrapperList.add(dataWrapper);
			}
		}
		
		// Convert the temporary storage to a plain List<ReportItem<T, K>>
		itemList = new ArrayList<>();
		itemListMap.values().stream().forEach(l -> {itemList.addAll(l);});
		
		// Handling of empty data, but includesEmptyItem
		if (includeEmptyItem && itemList.isEmpty())
		{
			// Instantiate a dummy ReportItem
			ReportItem<T, K> item = new ReportItem<T, K>(null);
			initReportItem(item, null);
			itemList.add(item);
			
			cellCount = item.getCellCount();
		}
		
		// Write to Sheet
		writeHeader(sheet);
		writeData(sheet);
	}

	
	private void writeHeader(Sheet sheet)
	{
		int sectionHdrRowNum = (includeSectionHeader) ? 0 : -1; 
		int itemHdrRowNum = sectionHdrRowNum + (includeItemHeader ? 1 : 0);
		int fieldHdrRowNum = itemHdrRowNum + 1;
		headerRowCount = 1 + (includeSectionHeader ? 1 : 0) + (includeItemHeader ? 1 : 0);
		
		Row fieldHdrRow = sheet.getRow(fieldHdrRowNum);
		
		int startItemCellNum = startCellNum;
		
		// Iterate each ReportItem
		for (int n=0;n<itemList.size();n++)
		{
			ReportItem<T, K> item = itemList.get(n);

			// Set the value of the 3rd row (Field Header)
			item.setStartCellNum(startItemCellNum);
			item.writeHeader(fieldHdrRow);
			
			// Set the value of the 2nd row (Item Header)
			if (includeItemHeader)
			{
				String itemParam = (item.getItemIndex() != null) ? "(" + (item.getItemIndex()+1) + ")" : "";
				String itemName = MessageFormat.format(getItemName(item.getItemKey()), itemParam);
				
				Row itemHdrRow = sheet.getRow(itemHdrRowNum);
				Cell cell = itemHdrRow.getCell(startItemCellNum, MissingCellPolicy.CREATE_NULL_AS_BLANK);
				cell.setCellValue(itemName);
				
				// Merge cells
				try
				{
					sheet.addMergedRegion(new CellRangeAddress(itemHdrRow.getRowNum(), itemHdrRow.getRowNum(), startItemCellNum, startItemCellNum+item.getCellCount()-1));
				}
				catch (IllegalArgumentException iae)
				{
					// Ignore this exception
				}
			}
			
			// Keep track of the total cell count and the start index
			startItemCellNum += item.getCellCount();
		}		
		
		// Set the value of the 1st row (Section Header)
		if (includeSectionHeader && CollectionUtils.isNotEmpty(itemList))
		{
			Row sectionHdr = sheet.getRow(sectionHdrRowNum);
			Cell cell = sectionHdr.getCell(startCellNum, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
			cell.setCellValue(getSectionName());
			
			// Merge cells
			try
			{
				sheet.addMergedRegion(new CellRangeAddress(sectionHdr.getRowNum(), sectionHdr.getRowNum(), startCellNum, startCellNum+cellCount-1));
			}
			catch (IllegalArgumentException iae)
			{
				// Ignore this exception
			}
		}
	}
	
	
	private void writeData(Sheet sheet)
	{
		// Iterate each data and populate to the corresponding cells
		for (ReportDataWrapper<T, K> dataWrapper : dataWrapperList)
		{
			ReportItem<T, K> item = dataWrapper.getReportItem();
			if (item != null)
			{
				item.writeData(sheet, headerRowCount + dataWrapper.getRowNum(), dataWrapper.getData());
			}
		}
	}
	
	
	/**
	 * Auto resize column if the field is enabled this option.
	 * 
	 * @param sheet
	 * @param hasAutoFilter Indicate whether the header has auto filter. 
	 * 						If this is enabled, extra column width is added to the column
	 */
	public void adjustColumnWidth(Sheet sheet, boolean hasAutoFilter)
	{
		try
		{
			int currentCellNum = startCellNum;
			
			for (ReportItem<T, K> item : itemList)
			{
				List<ReportField<T,?>> fieldList = item.getFieldList();
				for (int n=0;n<fieldList.size();n++)
				{
					ReportField<T, ?> field = fieldList.get(n);
					if (field.isAutoSizeColumn())
					{
						sheet.autoSizeColumn(currentCellNum + n);
						
						// Add additional column width when auto filter is enabled
						// This ensures the header text can be shown completely.
						if (hasAutoFilter)
						{
							int targetColNum = currentCellNum + n;
							sheet.setColumnWidth(targetColNum, sheet.getColumnWidth(targetColNum) + WIDTH_FILTER_BUTTON);
						}
					}
					
					// Column width in POI is of 1/256th character width
					if (field.getColumnWidth() != null)
					{
						sheet.setColumnWidth(currentCellNum + n, field.getColumnWidth() * 256);
					}
				}
				
				currentCellNum += item.getCellCount();
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot resize column, exception=" + e.getMessage());
		}
	}
	
	
	/**
	 * Clear all data queried in this instance
	 */
	public void dispose()
	{
		dataWrapperList = null;
		rowKeyIdxMap = null;
	}
		
}
