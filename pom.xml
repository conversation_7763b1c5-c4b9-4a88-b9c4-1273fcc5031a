<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	
	<modelVersion>4.0.0</modelVersion>
	<groupId>hk.eduhk.rich</groupId>
	<artifactId>rich</artifactId>
	<version>1.0</version>
	<packaging>war</packaging>
	<name>rich</name>
	<description>RICH</description>
	
	<properties>
		<project.encoding>UTF-8</project.encoding>
		<version.jackson>2.11.1</version.jackson>
		<version.junit>5.6.2</version.junit>
		<version.poi>4.1.2</version.poi>
		<version.quartz>2.2.3</version.quartz>
		<version.google.http.client>1.36.0</version.google.http.client>
	</properties>
	
	<build>
		<finalName>rich</finalName>
		<sourceDirectory>src</sourceDirectory>
		
		<resources>
			<resource>
				<directory>src</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>resources</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>WebContent/WEB-INF</directory>
				<targetPath>../rich/WEB-INF</targetPath>
				<includes>
					<include>version.properties</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
		
		<plugins>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<encoding>${project.encoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>${project.encoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.0</version>
				<configuration>
					<warSourceDirectory>WebContent</warSourceDirectory>
					<failOnMissingWebXml>false</failOnMissingWebXml>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>3.0.0-M1</version>
			</plugin>
			<plugin>
	            <groupId>org.wildfly.plugins</groupId>
	            <artifactId>wildfly-maven-plugin</artifactId>
	            <version>2.0.2.Final</version>
	            <configuration>
	                <hostname>${app.server}</hostname>
	                <username>${app.server.username}</username>
	                <password>${app.server.password}</password>
	            </configuration>
	        </plugin>
		</plugins>
	</build>
	
	<repositories>
		<repository>
			<id>prime-repo</id>
			<name>PrimeFaces Maven Repository</name>
			<url>http://repository.primefaces.org/</url>
			<layout>default</layout>
		</repository>
	</repositories>
		
	<dependencies>
	
		<!-- Dependency for compilation. Classes are provided by the application container in runtime -->
		<dependency>
			<groupId>org.jboss.spec</groupId>
			<artifactId>jboss-javaee-all-7.0</artifactId>
			<version>1.1.0.Final</version>
			<scope>provided</scope>
		</dependency>
		
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>
 				com.googlecode.owasp-java-html-sanitizer
 			</groupId>
			<artifactId>owasp-java-html-sanitizer</artifactId>
			<version>20220608.1</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces.extensions</groupId>
			<artifactId>primefaces-extensions</artifactId>
			<version>10.0.6</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces.extensions</groupId>
			<artifactId>resources-ckeditor</artifactId>
			<version>10.0.5</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.omnifaces</groupId>
			<artifactId>omnifaces</artifactId>
			<version>2.7</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces.themes</groupId>
			<artifactId>all-themes</artifactId>
			<version>1.0.10</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>org.tuckey</groupId>
			<artifactId>urlrewritefilter</artifactId>
			<version>4.0.3</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces</groupId>
			<artifactId>primefaces</artifactId>
			<version>10.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.ua-parser</groupId>
			<artifactId>uap-java</artifactId>
			<version>1.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${version.quartz}</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz-jobs</artifactId>
			<version>${version.quartz}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${version.jackson}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${version.jackson}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${version.jackson}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.8.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.9.3</version>
		</dependency>
		<dependency>
		  <groupId>org.jsoup</groupId>
		  <artifactId>jsoup</artifactId>
		  <version>1.18.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.8.1</version>
		</dependency>
		<dependency>
			<groupId>org.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>3.7.1</version>
		</dependency>
		<dependency>
			<groupId>org.webjars</groupId>
			<artifactId>font-awesome</artifactId>
			<version>6.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.sun.istack</groupId>
			<artifactId>istack-commons-runtime</artifactId>
			<version>3.0.7</version>
		</dependency>
		<dependency>
			<groupId>nl.basjes.parse.useragent</groupId>
			<artifactId>yauaa</artifactId>
			<version>5.18</version>
		</dependency>
		<dependency>
			<groupId>org.jboss.ejb3</groupId>
			<artifactId>jboss-ejb3-ext-api</artifactId>
			<version>2.2.0.Final</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1</version>
		</dependency>
		<dependency>
	    <groupId>org.jdom</groupId>
	    <artifactId>jdom2</artifactId>
	    <version>2.0.6</version>
	</dependency>
		<dependency>
			<groupId>de.danielbechler</groupId>
			<artifactId>java-object-diff</artifactId>
			<version>0.95</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
			<version>1.24</version>
		</dependency>
		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client</artifactId>
			<version>${version.google.http.client}</version>
		</dependency>
		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client-jackson2</artifactId>
			<version>${version.google.http.client}</version>
		</dependency>
		
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.56</version>
		</dependency>
		
		<dependency>
		    <groupId>com.itextpdf</groupId>
		    <artifactId>itextpdf</artifactId>
		    <version>5.5.10</version>
		</dependency>


		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-core</artifactId>
			<version>1.0.10</version>
		</dependency>
		
		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-pdfbox</artifactId>
			<version>1.0.10</version>
		</dependency>
		
		<dependency>
		    <groupId>org.docx4j</groupId>
		    <artifactId>docx4j-ImportXHTML</artifactId>
		    <version>6.0.0</version>
		    <exclusions>
				<exclusion>
					<groupId>antlr</groupId>
					<artifactId>antlr</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.antlr</groupId>
					<artifactId>antlr-runtime</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
		    <groupId>org.json</groupId>
		    <artifactId>json</artifactId>
		    <version>20240303</version>
		</dependency>

	</dependencies>	
</project>