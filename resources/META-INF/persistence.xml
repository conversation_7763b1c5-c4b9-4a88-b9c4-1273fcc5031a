<persistence xmlns="http://java.sun.com/xml/ns/persistence"
		      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		      xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd"
		      version="2.0">

	<persistence-unit name="richEmf" transaction-type="JTA">

		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:jboss/datasources/richDS</jta-data-source>

		<class>hk.eduhk.rich.access.UserRole</class> 
		<class>hk.eduhk.rich.access.SecFuncLock</class> 
		<class>hk.eduhk.rich.access.SecFuncLockUser</class> 
		
		<class>hk.eduhk.rich.entity.Authorship</class>	
		<class>hk.eduhk.rich.entity.Department</class>
		<class>hk.eduhk.rich.entity.FacDept</class>
		<class>hk.eduhk.rich.entity.LookupValue</class>	
		<class>hk.eduhk.rich.entity.LookupFormValue</class>
		<class>hk.eduhk.rich.entity.LookupFNDValue</class>
		<class>hk.eduhk.rich.entity.EduSector</class>	
		<class>hk.eduhk.rich.entity.SubAuthorship</class>	
		<class>hk.eduhk.rich.entity.ReportFile</class>	
		<class>hk.eduhk.rich.entity.JournalRank</class>	
		
		<class>hk.eduhk.rich.entity.award.AwardHeader</class>
		<class>hk.eduhk.rich.entity.award.AwardHeader_P</class>
		<class>hk.eduhk.rich.entity.award.AwardDetails_P</class>
		<class>hk.eduhk.rich.entity.award.AwardHeader_Q</class>
		<class>hk.eduhk.rich.entity.award.AwardDetails_Q</class>
		
		<class>hk.eduhk.rich.entity.elsevier.Researcher</class>	
		<class>hk.eduhk.rich.entity.elsevier.ResearcherMetrics</class>	
		<class>hk.eduhk.rich.entity.elsevier.ResearcherOutput</class>	
		<class>hk.eduhk.rich.entity.elsevier.Output</class>	
		<class>hk.eduhk.rich.entity.elsevier.OutputMetrics</class>	
		<class>hk.eduhk.rich.entity.elsevier.Topic</class>	
		<class>hk.eduhk.rich.entity.elsevier.TopicCluster</class>	
		
		<class>hk.eduhk.rich.entity.email.EmailTemplate</class> 
		
		<class>hk.eduhk.rich.entity.importRI.ImportRIBatch</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRICA</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRIAwardV</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRIOutputV</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRIProjectV</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRAEOutputV</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRIPatentV</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRIStatus</class>
		<class>hk.eduhk.rich.entity.importRI.ImportRIStore</class>
		
		<class>hk.eduhk.rich.entity.operation.UploadStatus</class>
		<class>hk.eduhk.rich.entity.operation.UploadForm</class>
		
		<class>hk.eduhk.rich.entity.patent.PatentCountry</class>
		<class>hk.eduhk.rich.entity.patent.PatentType</class>
		<class>hk.eduhk.rich.entity.patent.PatentHeader_P</class>
		<class>hk.eduhk.rich.entity.patent.PatentDetails_P</class>
		<class>hk.eduhk.rich.entity.patent.PatentHeader_Q</class>
		<class>hk.eduhk.rich.entity.patent.PatentDetails_Q</class>
		<class>hk.eduhk.rich.entity.patent.PatentHeader_P</class>
		
		<class>hk.eduhk.rich.entity.project.Capacity</class>
		<class>hk.eduhk.rich.entity.project.FundSource</class>
		<class>hk.eduhk.rich.entity.project.PartnerCountry</class>
		<class>hk.eduhk.rich.entity.project.ProjectHeader</class>
		<class>hk.eduhk.rich.entity.project.ProjectHeader_P</class>
		<class>hk.eduhk.rich.entity.project.ProjectDetails_P</class>
		<class>hk.eduhk.rich.entity.project.ProjectHeader_Q</class>
		<class>hk.eduhk.rich.entity.project.ProjectDetails_Q</class>
		
		<class>hk.eduhk.rich.entity.publication.DisciplinaryArea</class>
		<class>hk.eduhk.rich.entity.publication.FundingSource</class>
		<class>hk.eduhk.rich.entity.publication.OutputHeader_P</class>
		<class>hk.eduhk.rich.entity.publication.OutputDetails_P</class>
		<class>hk.eduhk.rich.entity.publication.OutputAddl_P</class>
		<class>hk.eduhk.rich.entity.publication.OutputHeader_Q</class>
		<class>hk.eduhk.rich.entity.publication.OutputDetails_Q</class>
		<class>hk.eduhk.rich.entity.publication.OutputType</class>
		<class>hk.eduhk.rich.entity.publication.PubDiscipline</class>
		<class>hk.eduhk.rich.entity.publication.ResearchType</class>
		
		<class>hk.eduhk.rich.entity.rae.RaeAttachment</class>	
		<class>hk.eduhk.rich.entity.rae.RaeOutput</class>
		<class>hk.eduhk.rich.entity.rae.RaeOutputHeader</class>	
		<class>hk.eduhk.rich.entity.rae.RaeOutputDetails</class>	
		<class>hk.eduhk.rich.entity.rae.RaeOutputSelect</class>	
		<class>hk.eduhk.rich.entity.rae.RaeOutputStatus</class>	
		<class>hk.eduhk.rich.entity.rae.RaePanel</class>
		<class>hk.eduhk.rich.entity.rae.RaeReport</class>	
		<class>hk.eduhk.rich.entity.rae.RaeStaff</class>
		<class>hk.eduhk.rich.entity.rae.RaeUOA</class>	
		
		<class>hk.eduhk.rich.entity.staff.Assistant</class>	
		<class>hk.eduhk.rich.entity.staff.InternetUserInfo</class>	
		<class>hk.eduhk.rich.entity.staff.PureDept</class>	
		<class>hk.eduhk.rich.entity.staff.PureMap</class>	
		<class>hk.eduhk.rich.entity.staff.PurePerson</class>	
		<class>hk.eduhk.rich.entity.staff.PurePersonDept</class>	
		<class>hk.eduhk.rich.entity.staff.SecDataUser</class>
		<class>hk.eduhk.rich.entity.staff.StaffEligible</class>	
		<class>hk.eduhk.rich.entity.staff.StaffEmployment</class>		
		<class>hk.eduhk.rich.entity.staff.StaffIdentity</class>	
		<class>hk.eduhk.rich.entity.staff.StaffInfo</class>		
		<class>hk.eduhk.rich.entity.staff.StaffPast</class>	
		<class>hk.eduhk.rich.entity.staff.StaffProfileDisplay</class>	
		<class>hk.eduhk.rich.entity.staff.StaffRank</class>	
		
		<class>hk.eduhk.rich.entity.employment.EmploymentHistory</class>
		<class>hk.eduhk.rich.entity.employment.EmploymentHistory_edit</class>
		
		<class>hk.eduhk.rich.entity.report.AdhocRpt</class>	
		<class>hk.eduhk.rich.entity.report.CdcfRptPeriod</class>			
		<class>hk.eduhk.rich.entity.report.KtRptPeriod</class>	
		<class>hk.eduhk.rich.entity.report.KtRptSum</class>	
		<class>hk.eduhk.rich.entity.report.PreRpt</class>	
		<class>hk.eduhk.rich.entity.report.PreRptCat</class>	
		<class>hk.eduhk.rich.entity.report.AppPeriodReport</class>	
		<class>hk.eduhk.rich.entity.report.AppStaffCount</class>
		<class>hk.eduhk.rich.entity.report.AppStaffWeight</class>
			
		
		<class>hk.eduhk.rich.entity.form.KtForm</class>	
		<class>hk.eduhk.rich.entity.form.KtFormDetails_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormHeader_Q</class>	
		<class>hk.eduhk.rich.entity.form.KtFormDetails_Q</class>	
		<class>hk.eduhk.rich.entity.form.KtFormState_Q</class>	
		
		<class>hk.eduhk.rich.entity.form.KtFormCPD_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormProfConf_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormSem_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormCntProj_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormInn_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormCons_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormProfEngmt_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormIP_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormSocEngmt_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormStaffEngmt_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormEA_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormStartup_P</class>	
		<class>hk.eduhk.rich.entity.form.KtFormInvAward_P</class>	
		
		<class>hk.eduhk.rich.entity.form.KtFormSummary</class>
		
		<class>hk.eduhk.rich.entity.form.PureKtFormHeader</class>	
		<class>hk.eduhk.rich.entity.form.PureKtFormDetails</class>	
		
		<class>hk.eduhk.rich.param.SysParam</class>
		<class>hk.eduhk.rich.scheduler.SchedulerJobLog</class>
		
		<class>hk.eduhk.rich.cv.StaffCv</class>	

		<shared-cache-mode>ENABLE_SELECTIVE</shared-cache-mode>
		<validation-mode>CALLBACK</validation-mode>
		<properties>
		
			<property name="hibernate.current_session_context_class" value="thread"/>
			<property name="hibernate.dialect" value="org.hibernate.dialect.Oracle10gDialect"/>
			<property name="hibernate.format_sql" value="true"/>
			<property name="hibernate.show_sql" value="false"/>
			<property name="hibernate.hbm2ddl.auto" value="none"/>
			<property name="hibernate.generate_statistics" value="false"/>
			<property name="hibernate.transaction.jta.platform" value="org.hibernate.service.jta.platform.internal.JBossAppServerJtaPlatform" />
		  
			<!-- 
			<property name="hibernate.cache.use_query_cache" value="true"/>
			<property name="hibernate.cache.use_second_level_cache" value="true"/>
			<property name="hibernate.cache.region.factory_class" value="org.hibernate.cache.jcache.JCacheRegionFactory"/>
			<property name="hibernate.javax.cache.provider" value="org.ehcache.jsr107.EhcacheCachingProvider"/>
			-->
			<property name="hibernate.enable_lazy_load_no_trans" value="true"/>
		</properties>

	</persistence-unit>

	<persistence-unit name="amisDWEmf" transaction-type="JTA">

		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:jboss/datasources/amisDwDS</jta-data-source>
		
		<class>hk.eduhk.rich.banner.BanClass</class>
		<class>hk.eduhk.rich.banner.BanClassGroup</class>
		<class>hk.eduhk.rich.banner.BanClassGroupInstructor</class>
		<class>hk.eduhk.rich.banner.BanClassGroupMeeting</class>
		<class>hk.eduhk.rich.banner.BanClassGroupSchedule</class>
		<class>hk.eduhk.rich.banner.BanClassMeetingInstructor</class>
		<class>hk.eduhk.rich.banner.BanCourse</class>
		<class>hk.eduhk.rich.banner.BanMajor</class>
		<class>hk.eduhk.rich.banner.BanOrganizationUnit</class>
		<class>hk.eduhk.rich.banner.BanPerson</class>
		<class>hk.eduhk.rich.banner.BanProgram</class>
		<class>hk.eduhk.rich.banner.BanProgramAttribute</class>
		<class>hk.eduhk.rich.banner.BanStudentClassRegistration</class>
		<class>hk.eduhk.rich.banner.BanStudentProgram</class>
		<class>hk.eduhk.rich.banner.BanTerm</class>
		<class>hk.eduhk.rich.banner.BanViewOrganizationUnit</class>
		
		<exclude-unlisted-classes>true</exclude-unlisted-classes>
		
		<shared-cache-mode>ENABLE_SELECTIVE</shared-cache-mode>
		<validation-mode>CALLBACK</validation-mode>
		<properties>
			<property name="hibernate.current_session_context_class" value="thread"/>
			<property name="hibernate.dialect" value="org.hibernate.dialect.SQLServer2012Dialect"/>
			<property name="hibernate.format_sql" value="true"/>
			<property name="hibernate.show_sql" value="false"/>
			<property name="hibernate.hbm2ddl.auto" value="none"/>
			<property name="hibernate.generate_statistics" value="false"/>
			<property name="hibernate.transaction.jta.platform" value="org.hibernate.service.jta.platform.internal.JBossAppServerJtaPlatform" />

			<!-- 
			<property name="hibernate.cache.use_query_cache" value="true"/>
			<property name="hibernate.cache.use_second_level_cache" value="true"/>
			<property name="hibernate.cache.region.factory_class" value="org.hibernate.cache.jcache.JCacheRegionFactory"/>
			<property name="hibernate.javax.cache.provider" value="org.ehcache.jsr107.EhcacheCachingProvider"/>
			 -->
			 <property name="hibernate.enable_lazy_load_no_trans" value="true"/>
		</properties>
      
	</persistence-unit>	

</persistence>