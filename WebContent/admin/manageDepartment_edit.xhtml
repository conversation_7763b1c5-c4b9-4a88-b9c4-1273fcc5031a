<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="deptCode" value="#{pureDeptView.paramDeptCode}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<p:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<p:panel id="contentPanel" >
		
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="#{pureDeptView.selectedPureDept.creationDate != null ? bundle['action.edit.x'] : bundle['action.new.x']}">
   				<f:param value="Department" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		
		<p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
			<p:autoUpdate/>
		</p:messages>
		
		<p:panelGrid styleClass="edit-panel">
		
			<!-- Department code -->
			<p:row>
			<p:column>
				Department Code
			</p:column>
			<p:column>
				<p:inputText id="department_code" label="Department Code" required="true" value="#{pureDeptView.selectedPureDept.department_code}" style="width: 70%;">
					<f:validateLength maximum="20"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="department_codeMsg" for="department_code" />
				</div>
			</p:column>
		
			<!-- Department name -->
			<p:column>
				Name
			</p:column>
			<p:column>
				<p:inputText id="department_name" label="Name" value="#{pureDeptView.selectedPureDept.department_name}" style="width: 70%;">
					<f:validateLength maximum="240"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="department_nameMsg" for="department_name" />
				</div>
			</p:column>
			</p:row>
			
			<!-- Department Abbreviation -->
			<p:row>
			<p:column>
				Abbreviation
			</p:column>
			<p:column>
				<p:inputText id="department_abbr" label="Abbreviation" value="#{pureDeptView.selectedPureDept.department_abbr}" style="width: 70%;">
					<f:validateLength maximum="50"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="department_abbrMsg" for="department_abbr" />
				</div>
			</p:column>			
			
			<!-- Department chinese name -->
			<p:column>
				Chinese Name
			</p:column>
			<p:column>
				<p:inputText id="department_chi_name" label="Chinese Name" value="#{pureDeptView.selectedPureDept.department_chi_name}" style="width: 70%;">
					<f:validateLength maximum="80"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="department_chi_nameMsg" for="department_chi_name" />
				</div>
			</p:column>
			</p:row>
					
			<!-- url -->
			<p:row>
			<p:column>
				URL
			</p:column>
			<p:column colspan="3">
				<p:inputText id="url" label="URL" value="#{pureDeptView.selectedPureDept.url}" style="width: 80%;">
					<f:validateLength maximum="400"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="urlMsg" for="url" />
				</div>
			</p:column>
			</p:row>					
						
			<!-- PURE Source ID -->
			<p:row>
			<p:column>
				PURE Source ID
			</p:column>
			<p:column>
				<p:inputText id="pure_source_id" label="PURE Source ID" value="#{pureDeptView.selectedPureDept.pure_source_id}" style="width: 70%;">
					<f:validateLength maximum="50"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="pure_source_idMsg" for="pure_source_id" />
				</div>
			</p:column>							
					
			<!-- PURE Parent ID -->
			<p:column>
				PURE Parent ID
			</p:column>
			<p:column>
				<p:inputText id="parent_source_id" label="PURE Parent ID" value="#{pureDeptView.selectedPureDept.parent_source_id}" style="width: 70%;">
					<f:validateLength maximum="50"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="parent_source_idMsg" for="parent_source_id" />
				</div>
			</p:column>
			</p:row>					
						
		</p:panelGrid>
		
		<br/>
		<h:panelGroup styleClass="button-panel">
			<p:button value="#{bundle['action.back']}" outcome="manageDepartment" icon="pi pi-arrow-left" style="margin-right:5px;"/>
			<p:commandButton value="#{bundle['action.create']}"
					  		 action="#{pureDeptView.updateForm()}"
					  		 update="@form" 
					  		 rendered="#{pureDeptView.selectedPureDept.creationDate == null}">
			</p:commandButton>
			
			<p:commandButton value="#{bundle['action.update']}" 
					  		 action="#{pureDeptView.updateForm()}" 
					  		 update="@form" 
					  		 rendered="#{pureDeptView.selectedPureDept.creationDate != null}">
			</p:commandButton>
		</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>