<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets">


		<h:form id="exclusiveUseForm">
			<p:toolbar>
	            <p:toolbarGroup>
	                <p:commandButton id="btn_setEx" value="#{secFuncLockView.selectedDeptBtnMsg}" 
	                				action="#{secFuncLockView.getSelectedDeptBtnMsg()}"
	                                 icon="pi pi-pencil"
	                                 styleClass="ui-button-success" disabled="#{!secFuncLockView.hasSelectedDepts()}"
	                                 oncomplete="PF('manageDeptDialog').show()"
	                                 update="manage-dept-content">
	                </p:commandButton>
	            </p:toolbarGroup>
	        </p:toolbar>
			<p:dataTable id="dataTable" var="data" value="#{secFuncLockView.deptList}"
							rowKey="#{data.getPk().getLookup_code()}"
							selection="#{secFuncLockView.selectedDepts}"
							paginator="false"
	                    	rowSelectMode="add">
	                    	 
                    	<p:ajax event="rowSelect" update=":formTab:exclusiveUseForm:btn_setEx"/>
			            <p:ajax event="rowUnselect" update=":formTab:exclusiveUseForm:btn_setEx"/>
			            <p:ajax event="rowSelectCheckbox" update=":formTab:exclusiveUseForm:btn_setEx"/>
			            <p:ajax event="rowUnselectCheckbox" update=":formTab:exclusiveUseForm:btn_setEx"/>
			            <p:ajax event="toggleSelect" update=":formTab:exclusiveUseForm:btn_setEx"/>
			            
                    	<p:column selectionMode="multiple" exportable="false" width="20px"></p:column>
                     	<p:column headerText="Faculty/Department" id="dept" sortBy="#{data.getPk().getLookup_code()}" width="12%">
                     		<p:tag value="#{data.getParent_lookup_code()}" styleClass="p-mr-2" rendered="#{data.getParent_lookup_code() ne null}" style="margin-right: 2px;" severity="success"/>
                     		<p:tag value="#{data.getPk().getLookup_code()}" styleClass="p-mr-2" rendered="#{data.getParent_lookup_code() eq null}" style="margin-right: 2px;" severity="warning"/>
			                <p:tag value="#{data.getPk().getLookup_code()}" styleClass="p-mr-2" rendered="#{data.getParent_lookup_code() ne null}" style="margin-right: 2px;"/>
			            </p:column>
			            <p:column headerText="Exclusive" id="lock_status" sortBy="#{data.getExKt().lock_status}" width="10%">
			                <h:outputText value="#{data.getExKt().lock_status == 'Y'? 'Exclusive':'Not Exclusive'}" 
			                					style="#{data.getExKt().lock_status == 'Y'? 'background-color:#EA4335; color:#fff; padding:3px 8px 3px 8px; border-radius: 25px;':'background-color:#34A853; color:#fff; padding:3px 8px 3px 8px; border-radius: 25px;'}"/>
			            </p:column>
			            <p:column headerText="Message" id="lock_msg">
			                #{data.getExKt().lock_msg}
			            </p:column>
			            <p:column headerText="Updated by" id="userstamp" sortBy="#{data.getExKt().userstamp}" width="10%">
			                #{data.getExKt().userstamp}
			            </p:column>
             </p:dataTable>
       
       <p:dialog header="Set Exclusive Use" showEffect="fade" modal="true" widgetVar="manageDeptDialog" responsive="true">
                  <p:outputPanel id="manage-dept-content" class="ui-fluid">
                  		<div class="p-formgrid p-grid">
							<div class="p-field p-col">
								<p:outputLabel class="riForm-item-title">Selected Faculties/Departments</p:outputLabel>
							</div>
							<div class="p-field p-col">
								<div class="card">
								 	<p:repeat var="d" value="#{secFuncLockView.selectedDepts}">
								 		<p:tag value="#{d.getPk().getLookup_code()}" styleClass="p-mr-2" style="margin-bottom: 5px;" rendered="#{d.getParent_lookup_code() ne null}"/>
								 		<p:tag value="#{d.getPk().getLookup_code()}" styleClass="p-mr-2" style="margin-bottom: 5px;" rendered="#{d.getParent_lookup_code() eq null}" severity="warning"/>
								 	</p:repeat>
							 	</div>
							</div>
							<div class="p-field p-col">
								<p:outputLabel class="riForm-item-title">Status</p:outputLabel>
								<p:selectOneRadio id="kt_lock_status" value="#{secFuncLockView.selectedSecFuncLock.lock_status}" unselectable="false">
								    <f:selectItem itemLabel="Exclusive" itemValue="Y"/>
								    <f:selectItem itemLabel="Not Exclusive" itemValue="N"/>
								</p:selectOneRadio>
							</div>
							<div class="p-field p-col">
								<p:outputLabel class="riForm-item-title">Message</p:outputLabel>
							     <p:inputTextarea id = "kt_lock_msg" rows="7" cols="70" counter="kt_lock_msg_display" maxlength="1000" value="#{secFuncLockView.selectedSecFuncLock.lock_msg}"
							         					 counterTemplate="{0} characters remaining." autoResize="false"/>
								<br/>
								<h:outputText id="kt_lock_msg_display" class="p-d-block" />
							 </div>
	                    </div>

	                </p:outputPanel>
	                <f:facet name="footer">
	                <p:commandButton value="#{bundle['action.save']}" icon="pi pi-check" action="#{secFuncLockView.updateSelectedDepts}"
	                                 update="@form"/>
	                <p:commandButton value="Cancel" icon="pi pi-times" onclick="PF('manageDeptDialog').hide()"
	                                 class="ui-button-secondary"/>
	            </f:facet>
       </p:dialog>
     
		<br/>
		<h:panelGroup styleClass="button-panel">
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
				
		</h:panelGroup>
				
	</h:form>
</ui:composition>
   