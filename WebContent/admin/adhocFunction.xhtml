<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">


	<ui:define name="html_head">
		<style>
			.ui-dialog-content{
				padding:1px !important;
			}
		</style>
	</ui:define>			
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
		
		<h:form id="accessForm">
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
				<p:autoUpdate/>
			</p:messages>
			<div class="card">
				<span class="admin-content-title">Adhoc Function</span>
				<div class="card text-secondary">		
				<p:ajaxStatus styleClass="block mb-4">
			        <f:facet name="default">
			            <h:outputText value="Status: StandBy" />
			        </f:facet>
			
			        <f:facet name="start">
			            <i class="pi pi-spin pi-spinner loading-icon" aria-hidden="true"></i>
			        </f:facet>
			
			        <f:facet name="complete">
			            <h:outputText value="Status: Completed" />
			        </f:facet>
		    </p:ajaxStatus>
		
		    <p:ajaxStatus onstart="PF('statusDialog').show()" onsuccess="PF('statusDialog').hide()"/>
		
			    <p:dialog widgetVar="statusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
			        <div style="padding:2px">
			            <i class="pi pi-spin pi-spinner loading-icon" aria-hidden="true"></i>
			        </div>
			    </p:dialog>
			   </div>
			   <br/>
				<p:commandButton id="btn_updateCenseDateFromSAP" value="Update Cense Date From SAP" icon="pi pi-caret-right"
								  style="margin-right:5px;"
								  ajax = "true"
						  		  actionListener="#{adhocFuncView.updateCenseDateFromSAP}"
						  		  oncomplete="window.scrollTo(0,0);">					  		  
				</p:commandButton>
				
				<br/>
           		<br/>
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
            </div>
		</h:form>
		
		<br/>

	</p:panel>
	</ui:define>
</ui:composition>