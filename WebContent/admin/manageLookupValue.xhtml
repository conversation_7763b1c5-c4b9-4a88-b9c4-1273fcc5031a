<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Manage Lookup Value</span>
	
	<p:messages id="msgs" showDetail="false" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:toolbar style="background:#fff; border:0px solid #fff; padding:0;">
			<p:toolbarGroup>
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
			</p:toolbarGroup>
			<p:toolbarGroup align="left">
                <p:commandButton id="btn_download" value="Export" icon="pi pi-download" ajax="false" styleClass="btn-back">
                    <p:dataExporter type="xlsx" target="vTable" fileName="dataExport"/>
                </p:commandButton>
            </p:toolbarGroup>
        </p:toolbar>
		<p:dataTable id="vTable" var="v" value="#{lookupValueView.valueList}" editable="true" sortMode="single"
						rowKey="#{v.pk.lookup_type}-#{v.pk.lookup_code}-#{v.pk.language}"
						selection="#{lookupValueView.selectedValue}" selectionMode="single"
						 reflow="true"
						 paginator="true"
						 rows="50"
						 currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
                         paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                  		 tableStyle="table-layout:auto;"
                     	widgetVar="valueWidget">
			<p:ajax event="rowEdit" listener="#{lookupValueView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{lookupValueView.onRowCancel}" update=":msgs"/>
			
			<p:column headerText="Lookup Type" id="lookup_type" sortBy="#{v.pk.lookup_type}" filterBy="#{v.pk.lookup_type}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.pk.lookup_type}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.pk.lookup_type}" required="true" style="width:100%" valueChangeListener="#{lookupValueView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
			<p:column headerText="Lookup Code" id="lookup_code" sortBy="#{v.pk.lookup_code}" filterBy="#{v.pk.lookup_code}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.pk.lookup_code}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.pk.lookup_code}" required="true" style="width:100%" valueChangeListener="#{lookupValueView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Language" id="language" sortBy="#{v.pk.language}" filterBy="#{v.pk.language}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.pk.language}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.pk.language}" required="true" style="width:100%" valueChangeListener="#{lookupValueView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Short Description" id="short_desc" sortBy="#{v.short_desc}" filterBy="#{v.short_desc}" style="width:10%">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.short_desc}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.short_desc}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Description" id="description" sortBy="#{v.description}" filterBy="#{v.description}" style="width:20%">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.description}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.description}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
        
        <p:column headerText="Print Order" id="print_order" sortBy="#{v.print_order}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.print_order}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.print_order}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
        
        <p:column headerText="Enabled Flag" id="enabled_flag" sortBy="#{v.enabled_flag}" filterBy="#{v.enabled_flag}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.enabled_flag}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.enabled_flag}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Parent Lookup Code" id="parent_lookup_code" sortBy="#{v.parent_lookup_code}" filterBy="#{v.parent_lookup_code}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.parent_lookup_code}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.parent_lookup_code}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column style="width:4rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="vTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:vTable :msgs" action="#{lookupValueView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:vTable" listener="#{lookupValueView.reloadValueList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{lookupValueView.selectedValue.pk}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{lookupValueView.deleteValue}"
								 update=":dataForm:vTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>