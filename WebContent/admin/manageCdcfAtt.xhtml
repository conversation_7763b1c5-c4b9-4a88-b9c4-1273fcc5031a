<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">


	<ui:define name="html_head">
		<style>
		</style>
	</ui:define>			
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
		<span class="admin-content-title">Manage About CDCF Attachment</span>
		<p:messages id="messages" showDetail="true" autoUpdate="true" />
		<h:form enctype="multipart/form-data">
	    	<p:fileUpload id="uploadFileAtt1" style="padding:2px;"
						                      mode="advanced" auto="true"
						                      process="@this" 
						                      update="messages"
						                      label="Upload Attachment I"
						                      listener="#{cdcfAttView.handleFileUploadAtt1}">
			</p:fileUpload>
			<p:fileUpload id="uploadFileAtt2" style="padding:2px;"
						                      mode="advanced" auto="true"
						                      process="@this" 
						                      update="messages"
						                      label="Upload Attachment II"
						                      listener="#{cdcfAttView.handleFileUploadAtt2}">
			</p:fileUpload>
			<p:fileUpload id="uploadFileAtt3" style="padding:2px;"
						                      mode="advanced" auto="true"
						                      process="@this" 
						                      update="messages"
						                      label="Upload Attachment III"
						                      listener="#{cdcfAttView.handleFileUploadAtt3}">
			</p:fileUpload>
			<p:fileUpload id="uploadFileAtt4" style="padding:2px;"
						                      mode="advanced" auto="true"
						                      process="@this" 
						                      update="messages"
						                      label="Upload Table 7 Guideline"
						                      listener="#{cdcfAttView.handleFileUploadAtt4}">
			</p:fileUpload>
	</h:form>

	</p:panel>
	</ui:define>
</ui:composition>