<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="group" value="#{sysParamView.selectedGroup}" />
		<f:viewParam name="paramCode" value="#{sysParamView.paramCode}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="#{sysParamView.selectedSysParam.creationDate != null ? bundle['action.edit.x'] : bundle['action.new.x']}">
   				<f:param value="System Parameter" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		
		<p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
			<p:autoUpdate/>
		</p:messages>
		
		<h:panelGrid columns="2" styleClass="edit-panel">
		
			<!-- Group -->
			<h:panelGroup>
				Group
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="group"
							 value="#{sysParamView.selectedSysParam.group}"
							 maxlength="#{sysParamView.getFieldMaxLength('group')}">
					<f:validator validatorId="hk.eduhk.rich.param.SysParamValidator"/>
					<f:attribute name="validateField" value="group" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="groupMsg" for="group"/>
				</div>
			</h:panelGroup>
		
			<!-- Code -->
			<h:panelGroup>
				Code
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="code"
							 value="#{sysParamView.selectedSysParam.code}" 
							 maxlength="#{sysParamView.getFieldMaxLength('code')}"  
							 rendered="#{sysParamView.selectedSysParam.creationDate == null}">
					<f:validator validatorId="hk.eduhk.rich.param.SysParamValidator"/>
					<f:attribute name="validateField" value="code" />
				</p:inputText>
							 
				<h:outputText value="#{sysParamView.selectedSysParam.code}" 
							  rendered="#{sysParamView.selectedSysParam.creationDate != null}"/>
        		<div class="input-validation-message">
					<p:message id="codeMsg" for="code"/>
				</div>
			</h:panelGroup>
		
			<!-- Description -->
			<h:panelGroup>
				Description
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="description"
							 value="#{sysParamView.selectedSysParam.description}"
							 maxlength="#{sysParamView.getFieldMaxLength('description')}"  
							 style="width:80%;">
					<f:validator validatorId="hk.eduhk.rich.param.SysParamValidator"/>
					<f:attribute name="validateField" value="description" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="descriptionMsg" for="description"/>
				</div>
			</h:panelGroup>
			
			<!-- Encrypted -->
			<h:panelGroup>
				Encrypted
			</h:panelGroup>
			<h:panelGroup>
				<p:selectOneMenu id="encrypted" 
							 	 value="#{sysParamView.selectedSysParam.encrypted}">
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="#{true}"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="#{false}"/>
					<p:ajax event="change" update="valueInput" listener="#{sysParamView.selectedSysParam.clearValue}"/>
				</p:selectOneMenu>
				<div class="input-validation-message">
					<p:message id="encryptedMsg" for="encrypted"/>
				</div>
			</h:panelGroup>

			<!-- Value -->
			<h:panelGroup>
				Value
			</h:panelGroup>
			<h:panelGroup id="valueInput">
				<p:inputTextarea id="value"
							 value="#{sysParamView.selectedSysParam.value}"
							 maxlength="#{sysParamView.getFieldMaxLength('value')}"  
							 style="width:80%;"
							 rows="7" counter="display_value" 
							 counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
							 rendered="#{!sysParamView.selectedSysParam.encrypted}">
					<f:validator validatorId="hk.eduhk.rich.param.SysParamValidator"/>
					<f:attribute name="validateField" value="value" />
				</p:inputTextarea>
				<br/>
		      	<h:outputText id="display_value" class="p-d-block" />
		      	
				<p:inputTextarea id="decryptedValue"
							 value="#{sysParamView.selectedSysParam.decryptedValue}"
							 maxlength="#{sysParamView.getFieldMaxLength('value')}"
							 style="width:80%;"
							 rows="7" counter="display_decryptedValue" 
							 counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
							 rendered="#{sysParamView.selectedSysParam.encrypted}">
					<f:validator validatorId="hk.eduhk.rich.param.SysParamValidator"/>
					<f:attribute name="validateField" value="value" />
				</p:inputTextarea>
				<br/>
		      	<h:outputText id="display_decryptedValue" class="p-d-block" />
				<div class="input-validation-message">
					<p:message id="valueMsg" for="value"/>
					<p:message id="decryptedValueMsg" for="decryptedValue"/>
				</div>
			</h:panelGroup>
			
		</h:panelGrid>
		
		<br/>
		<h:panelGroup styleClass="button-panel">
		
			<!-- For optimistic locking -->
			<h:inputHidden id="timestamp" value="#{sysParamView.selectedSysParam.timestamp}">
				<f:convertDateTime dateStyle="full" type="both" />
			</h:inputHidden>
			<p:button value="#{bundle['action.back']}" outcome="sysParamList?group=#{sysParamView.selectedGroup}" icon="pi pi-arrow-left" style="margin-right:5px;"/>
			<p:commandButton value="#{bundle['action.create']}"
					  		 action="#{sysParamView.updateSysParam}"
					  		 update="@form" 
					  		 rendered="#{sysParamView.selectedSysParam.creationDate == null}">
			</p:commandButton>
			
			<p:commandButton value="#{bundle['action.update']}" 
					  		 action="#{sysParamView.updateSysParam}" 
					  		 update="@form" 
					  		 rendered="#{sysParamView.selectedSysParam.creationDate != null}">
			</p:commandButton>

		</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>