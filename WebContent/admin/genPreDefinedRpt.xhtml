<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
			
				
				
<ui:define name="html_head">
	<style>
		.ui-datatable .ui-datatable-data > tr .ui-rowgroup-toggler .ui-rowgroup-toggler-icon{
			color: #d91010 !important;
		}
		
		.ui-selectonemenu .ui-selectonemenu-label {
		    white-space: inherit;
		}

		.jqplot-grid-shadowCanvas {
		  background:blue;
		}
	</style>
</ui:define>
	<ui:define name="mainContent"> 
		<p:panel id="contentPanel">
		
			 <h:outputScript>
			 	const canvasBackground = {
				  id: 'customCanvasBackgroundColor',
				  beforeDraw: (chart, args, options) => {
				    const {ctx} = chart;
				    ctx.save();
				    ctx.globalCompositeOperation = 'destination-over';
				    ctx.fillStyle = options.color || '#fff';
				    ctx.fillRect(0, 0, chart.width, chart.height);
				    ctx.restore();
				  }
				};
				
		        function barChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels, canvasBackground],
				      options: {
				         plugins: {
				         //remove the legend
			                legend: {
			                   display: true
			                },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
					               color: '#fff',
					               formatter: function(value) {
							          return value + '%';
							        }
				            },
				     		customCanvasBackgroundColor: {
        						color: '#fff'
      					    }
				         },
				         scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Department'
				         			}
				         		},
			                    y: {
			                          min: 0,
        							  max: 100,
        							  title: {
		                                display: true,
		                                text: 'Precentage'
		                            	}
					             }
                		}
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#fff'
				            }
				         }]
				      }
			   };

			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       
		       
		        function barChartExtender_B02() {
		           var options = {
				      plugins: [ChartDataLabels, canvasBackground],
				      options: {
				     
				         plugins: {
				         //remove the legend
			                  legend: {
			                     display: true
			                  },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
					               color: '#000',
					               anchor: 'center',
					               align : 'end',
					               rotation: 270,
					               
					                // Only display labels when the value is not zero
				                    display: function(context) {
				                        return context.dataset.data[context.dataIndex] != 0;
				                    }
					               
				            },
				            customCanvasBackgroundColor: {
        						color: '#fff'
      					    }
				         },
				      
				         scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Department'
				         			}
				         		},
			                    y: {
			                          type: 'logarithmic',
			                          ticks: {
							                callback: function(value, index, values) {
							                    if (value === 100000 ) return '0';
							                    return value;
							                }
							            },
							        	title: {
			                                display: true,
			                                text: 'Amount (HK$)'
		                            	}
					             }
                		}
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#000'
				            }
				         }]
				      }
			   };

			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       

		       function barChartExtender_2() {
		           var options = {
		           	  exporting: {
						  chartOptions: {
						    chart: {
						      backgroundColor: '#9E9E9E'
						    }
						  }
						},
				      plugins: [ChartDataLabels, canvasBackground],
				      options: {
				         plugins: {
				         //remove the legend
			                legend: {
			                   display: true
			                },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
					               color: '#fff'
				            },				            
				            customCanvasBackgroundColor: {
        						color: '#fff'
      					    },
				         },
				         scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Department'
				         			}
				         		},
			                    y: {
			                          beginAtZero: true,
			                          max: 7,
			                          ticks: {
							                precision: 0
							            },
        							  title: {
		                                display: true,
		                                text: 'No. of Academic Staff'
		                            	}
					             }
                		 },

				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#fff'
				            }
				         }]
				      },

				      			      
			   };
		   
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
			   };
			   
			   
			   function barChartExtender_a4() {
		           var options = {
		           	  exporting: {
						  chartOptions: {
						    chart: {
						      backgroundColor: '#9E9E9E'
						    }
						  }
						},
				      plugins: [ChartDataLabels, canvasBackground],
				      options: {
				         plugins: {
				         //remove the legend
			                legend: {
			                   display: true
			                },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
					               color: '#fff'
				            },				            
				            customCanvasBackgroundColor: {
        						color: '#fff'
      					    },
				         },
				         scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Department'
				         			}
				         		},
			                    y: {
			                          beginAtZero: true,
			                          max: 2,
			                          ticks: {
							                precision: 0
							            },
        							  title: {
		                                display: true,
		                                text: 'No. of Academic Staff'
		                            	}
					             }
                		 },

				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#fff'
				            }
				         }]
				      },

				      			      
			   };
		   
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
			   };
			   
			   
			   
			   
		      function saveChart(report) {
			       //save to png
			       var id = 'dataForm:' + report + '_canvas';
			       const chart = document.getElementById(id);
			       document.getElementById("dataForm:chartImageBase64").value = chart.toDataURL('image/png');
  			   };
		      	       
			</h:outputScript>
		
			<span class="admin-content-title"><i class="fa-solid fa-chart-bar"></i> Generate Pre-defined Report</span>
			<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
			<h:form id="dataForm">
				<p:panel>
					<f:facet name="header">
						<div>
							<span style="color:#1f1645;">Report</span>
						</div>
					</f:facet>
					<div class="ui-g">
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="Category" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="reportCat" title="reportCat" label="Report Category" value="#{preRptView.selectedRptCat}" style="margin-top:4px;margin-right:10px;">
									<f:selectItems value="#{preRptView.preRptCatList}" var="c" itemLabel="#{c.rpt_cat_desc}" itemValue="#{c.rpt_cat}" />
									<p:ajax event="change" listener="#{preRptView.updateSelectedRpt}"
									        update="dataForm  reportName reportNote generateBtn downloadChart exportExcelBtn exportDocxBtn reportDeptTitle reportDept cdcfPeriod"/>
							</p:selectOneMenu>	
						</div>
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="Name" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="reportName" title="reportName" label="Report Name" value="#{preRptView.selectedRpt}" 
									style="margin-top:4px;margin-right:10px;">
									<f:selectItem itemLabel="-- Please select --" itemValue=""/>
									<f:selectItems value="#{preRptView.preRptListByCat}" var="d" 
										itemLabel="#{d.rpt_desc}"
										itemValue="#{d.rpt_code}" />
									<p:ajax event="change"  listener="#{preRptView.disableDownloadButton}" update=" reportNote generateBtn exportExcelBtn exportDocxBtn downloadChart reportDeptTitle reportDept cdcfPeriod"/>	
							</p:selectOneMenu>	
						</div>
						
						
						
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<h:panelGroup id="reportNoteGroup">
								<p:outputLabel id="reportNote" value="#{preRptView.selectedRptnote}" escape="false"></p:outputLabel>
							</h:panelGroup>	
						</div>

					</div>
				</p:panel>
				<br/>
				<p:panel>
				<f:facet name="header">
					<div>
						<span style="color:#1f1645;">Filtering Criteria</span>
					</div>
				</f:facet>
					<div class="ui-g">
						<div class="ui-g-12 ui-md-2 ui-lg-2" >
							<p:outputLabel id="reportDeptTitle" value="Department" 
								style="#{(preRptView.getSelectedRpt() eq 'A7') || (preRptView.getSelectedRpt() eq 'CIRD_RP_002') 
									|| (preRptView.getSelectedRpt() eq 'CIRD_RP_001') || (preRptView.getSelectedRpt() eq 'CIRD_RP_004') ||
									(preRptView.getSelectedRpt() eq 'CIRD_RP_013') || (preRptView.getSelectedRpt() eq 'CIRD_RP_009')
									|| (preRptView.getSelectedRpt() eq 'CIRD_RP_010')  || (preRptView.getSelectedRpt() eq 'CIRD_RP_011')    ?
									'vertical-align: -webkit-baseline-middle; font-weight:700;':'display:none;'}"/>
						</div>
					
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="reportDept"  value="#{preRptView.selectedDept}" 
								style="#{(preRptView.getSelectedRpt() eq 'A7') || (preRptView.getSelectedRpt() eq 'CIRD_RP_002') 
								|| (preRptView.getSelectedRpt() eq 'CIRD_RP_001') || (preRptView.getSelectedRpt() eq 'CIRD_RP_004') ||
								(preRptView.getSelectedRpt() eq 'CIRD_RP_013') || (preRptView.getSelectedRpt() eq 'CIRD_RP_009')
								|| (preRptView.getSelectedRpt() eq 'CIRD_RP_010') || (preRptView.getSelectedRpt() eq 'CIRD_RP_011')    ?
										'margin-top:4px; margin-right:20px;':'display:none;'}" 
									filter="true" filterMatchMode="contains">
									<f:selectItems value="#{preRptView.deptfilterList}"/>							
							</p:selectOneMenu>	
						</div>
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="cdcfPeriod"  value="#{preRptView.selectedCdcfPeriods}" style="margin-top:4px; margin-right:20px;"	filter="true"
							filterMatchMode = "startsWith">
									<f:selectItem itemLabel="-- Please select --" itemValue="10000" itemDisabled="true"/>
									<f:selectItems value="#{preRptView.cdcfPeriodList}" var="o" itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />	
									<p:ajax event="change" listener="#{preRptView.disableDownloadButton}" update="downloadChart"/>							
							</p:selectOneMenu>	
						</div>
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="RI Date Period" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-1 ui-lg-1">
							<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-3 ui-lg-3">
							<p:datePicker id="riStartDate" view="date"
														title="Start Date (dd/MM/YYYY)" 
														label="Start Date (dd/MM/YYYY)" 
														value="#{preRptView.selectedStartDate}" 
														pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"/>	
						</div>
						<div class="ui-g-12 ui-md-1 ui-lg-1">
							<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-5 ui-lg-5">
							<p:datePicker id="riEndDate" view="date"
														title="End Date (dd/MM/YYYY)" 
														label="End Date (dd/MM/YYYY)" 
														value="#{preRptView.selectedEndDate}" 
														pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"/>	
						</div>
					
					

	
	            </div> 
	          </p:panel>
	          <br/>
			<h:panelGroup id="buttonGroup">
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
				
			    <p:commandButton id="generateBtn"  value="Generate" icon="pi pi-chart-bar"  widgetVar="generateBtnWV"
			    	style="#{(preRptView.getPreRptType() eq 'Chart')|| (preRptView.getPreRptType() eq 'Both') ?'':'display:none;'}"
			    	styleClass="btn-action"
           			action="#{preRptView.generateReport(preRptView.selectedRpt)}" update="@form msgs"
           								 onclick="PF('generateBtnWV').disable();PF('generateBtnDialog').show();"
										 oncomplete="PF('generateBtnWV').enable();PF('generateBtnDialog').hide();saveChart('#{preRptView.selectedRpt}')" />
										 										 					 
	           		<p:dialog widgetVar="generateBtnDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
						        <div>
					            	<h5>Generating</h5>
				        			<p:progressBar id="progressBarIndeterminate" 
				        				style="height:20px; width:250px;" mode="indeterminate"/>
					            </div>
					</p:dialog>
				
				
				<p:commandButton id="exportExcelBtn" value="Export the Excel" icon="pi pi-download" widgetVar="exportExcelBtnWV"
								style="#{(preRptView.getPreRptType() eq 'Excel')|| (preRptView.getPreRptType() eq 'Both')?'':'display:none;'}"
								styleClass="btn-action"
								escape="True"
								ajax="false"
						  		action="#{preRptView.exportReport(preRptView.selectedRpt)}" />
						  		
				<p:commandButton id="exportDocxBtn" value="Export the Docx" icon="pi pi-download" widgetVar="exportDocxBtnWV"
								style="#{(preRptView.getPreRptType() eq 'Docx')?'':'display:none;'}"
								styleClass="btn-action"
								escape="True"
								ajax="false"
						  		action="#{preRptView.exportReport(preRptView.selectedRpt)}" />
				
				<p:commandButton id="downloadChart" value="Download Chart" icon="pi pi-download"  widgetVar="downloadChartWV" 
			    	style="#{(preRptView.isChartGenerated == true) ?'':'display:none;'};margin-left:5px;margin-right:5px;" 
			    	update = "@form msgs" ajax = "false"
           			action = "#{preRptView.exportChart()}" />	
				
					
				<p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		        </p:confirmDialog>
	        </h:panelGroup>
	        
	        <h:inputHidden id = "chartImageBase64" value = "#{preRptView.chartImageBase64}" />
            <h:panelGroup  rendered="#{preRptView.selectedRpt eq 'A2'}">
					<h3><b>Common Data Collection Format</b> <br/>
					<b>Number of Academic Staff with Refereed Research Output below 1 item and 1 item or above by Departments for #{preRptView.filteredPeriod} </b><br/>
					<b>Research Output of "Scholarly Books, Monograph and Chapters", "Journal Publication" and "Non-Traditional Outputs <sup>#</sup>"</b></h3> 
					<h5>Remark: # The non-traditional outputs of CCA are included for consideration</h5>	                        
	            <p:panel id="outputIntConfBar" header ="Number of Academic Staff with Refereed Research Output below 1 item and 1 item or above by Departments for #{preRptView.filteredPeriod}">
					<p:barChart id="A2" model="#{preRptView.outputCountBarModel}" style="width: 100%; height: 500px; background:'white';drawBorder:false;'"/>
	            </p:panel>
           </h:panelGroup>
	                 
            <h:panelGroup rendered="#{preRptView.selectedRpt eq 'A3'}">
					<h3><b>Common Data Collection Format</b> <br/>
					<b>Number of Academic Staff with Refereed Research Output Consistently below 1 item per year in the last 3 years by Departments</b><br/>
					<b>Research Output of "Scholarly Books, Monograph and Chapters", "Journal Publication" and "Non-Traditional Outputs <sup>#</sup>"</b></h3>
					<h5>Remark: # The non-traditional outputs of CCA are included for consideration</h5> 	                        
	            <p:panel id="outputLast3year" header ="Number of Academic Staff with Refereed Research Output Consistently below 1 item per year in the last 3 years by Departments">
					<p:barChart id="A3" model="#{preRptView.outputLastThreeBarModel}" style="width: 100%; height: 500px;"/>		
	            </p:panel>
            </h:panelGroup>
            
            <h:panelGroup rendered="#{preRptView.selectedRpt eq 'A4'}">
					<h3><b>Common Data Collection Format</b> <br/>
					<b>Number of Academic Staff with Refereed Research Output Consistently 0 item in the last 3 years by Departments</b><br/>
					<b>Research Output of "Scholarly Books, Monograph and Chapters", "Journal Publication" and "Non-Traditional Outputs <sup>#</sup>"</b></h3>
					<h5>Remark: # The non-traditional outputs of CCA are included for consideration</h5> 	                        
	            <p:panel id="outputLast3year_2" header ="Number of Academic Staff with Refereed Research Output Consistently 0 item in the last 3 years by Departments">
					<p:barChart id="A4" model="#{preRptView.outputLastThreeZeroBarModel}" style="width: 100%; height: 500px;"/>
	            </p:panel>
            </h:panelGroup>
            
            <h:panelGroup rendered="#{preRptView.selectedRpt eq 'B2'}">
					<h3><b>Common Data Collection Format</b> <br/>
					<b>Amount of Research and Development Projects Funded by Internal and External Funding</b><br/>
					<b>by Departments for #{preRptView.filteredPeriod} (Academic Staff Only)</b></h3> 	                        
	            <p:panel id="amtRDIntExt" header ="Amount of Research and Development Projects Funded by Internal and External Funding by Departments for #{preRptView.filteredPeriod} (Academic Staff Only)">
					<p:barChart id="B2" model="#{preRptView.fundingIntExtBarModel}" style="width: 100%; height: 500px;"/>
	            </p:panel>
            </h:panelGroup>
            
             <h:panelGroup rendered="#{preRptView.selectedRpt eq 'B3'}">
					<h3><b>Common Data Collection Format</b> <br/>
					<b>Amount of Current Research Projects Funded by General Research Fund, Early Career Scheme and</b><br/>
					<b>Public Policy Research Funding Scheme by Departments for #{preRptView.filteredPeriod} (Academic Staff Only)</b></h3> 	                        
	            <p:panel id="amtCurrProj" 
	            header ="Amount of Current Research Projects Funded by General Research Fund, Early Career Scheme and Public Policy Research Funding Scheme by Departments for #{preRptView.filteredPeriod} (Academic Staff Only)">
					<p:barChart id="B3" model="#{preRptView.fundingIntExtBarModel}" style="width: 100%; height: 500px;"/>
	            </p:panel>
            </h:panelGroup>
            
            
			</h:form>
		</p:panel>
   </ui:define>
</ui:composition>