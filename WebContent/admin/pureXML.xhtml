<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">


	<ui:define name="mainContent">

	<p:panel id="contentPanel">
	
		<h:form id="pureXmlForm">

			<span class="admin-content-title">View Pure XML</span>
			
			<p:menubar>
				<p:menuitem value="Award XML" outcome="/export/pureAwardXML.xhtml" icon="pi pi-external-link"/>
				<p:divider layout="vertical"/>
				<p:menuitem value="Person XML" outcome="/export/purePersonXML.xhtml" icon="pi pi-external-link"/>
				<p:divider layout="vertical"/>
				<p:menuitem value="Project XML" outcome="/export/pureProjectXML.xhtml" icon="pi pi-external-link"/>
				<p:divider layout="vertical"/>
				<p:menuitem value="KT Activities XML" outcome="/export/pureKtActXML.xhtml" icon="pi pi-external-link"/>
			</p:menubar>

			<br/><br/>
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		</h:form>
		
		<br/>
	
	</p:panel>
	</ui:define>
</ui:composition>