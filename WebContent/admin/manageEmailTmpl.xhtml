<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

<ui:define name="mainContent">
		
	<span class="admin-content-title">Manage Email Template</span>
	
	<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
		<p:autoUpdate/>
	</p:messages>
	<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	<br/>
	
	<p:panel>
		<f:facet name="header">
			Redirection
		</f:facet>
		<p style="line-height:1.5em;">
			Manage email template function is located in another system.<br/>
			Please click <a href="/messaging/admin/emailTmlList.xhtml" target="_blank">this</a> to redirect to the corresponding system.
		</p> 
	</p:panel> 
	
	<br/>
 		
</ui:define>
</ui:composition>