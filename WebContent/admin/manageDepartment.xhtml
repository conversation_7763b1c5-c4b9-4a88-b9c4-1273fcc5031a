<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<h:panelGroup styleClass="admin-content-title">Manage Department</h:panelGroup>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:dataTable id="dataTable" var="data" value="#{pureDeptView.pureDeptList}" editable="true" sortMode="single"
						rowKey="#{data.department_code}-" tableStyle="table-layout: fixed;" stickyHeader="true"
						selection="#{pureDeptView.selectedPureDept}" selectionMode="single"
                     	widgetVar="dataWidget">
			<p:ajax event="rowEdit" listener="#{pureDeptView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{pureDeptView.onRowCancel}" update=":msgs"/>

			<p:column headerText="Department Code" id="department_code" sortBy="#{data.department_code}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.department_code}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.department_code}" style="width:100%" valueChangeListener="#{pureDeptView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Department Name" id="department_name" sortBy="#{data.department_name}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.department_name}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.department_name}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
        
            <p:column headerText="Pure Source ID" id="pure_source_id" sortBy="#{data.pure_source_id}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.pure_source_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.pure_source_id}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
        
            <p:column headerText="Parent Source ID" id="parent_source_id" sortBy="#{data.parent_source_id}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.parent_source_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.parent_source_id}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            <p:column style="width:6rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="dataTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" action="#{pureDeptView.gotoNewPage()}"/>
        	<p:menuitem value="#{bundle['action.edit']}" action="#{pureDeptView.gotoEditPage()}" />
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:dataTable" listener="#{pureDeptView.reloadPureDeptList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{pureDeptView.selectedPureDept.department_code}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{pureDeptView.deletePureDept}"
								 update=":dataForm:dataTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>
   