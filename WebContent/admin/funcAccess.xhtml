<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">
	
	<f:metadata>
		<f:viewParam name="userId" value="#{functionAccessView.selectedUserId}" />
	</f:metadata>
				
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
	
		<h:form id="accessForm">
			<span class="admin-content-title">Set Functional Access</span>
		
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
				<p:autoUpdate/>
			</p:messages>
		
			<p:focus context="accessForm"/>
		
			<!-- User ID selection -->
			<h:panelGrid columns="2">

				<h:panelGroup>
					User ID
					<h:outputText value="&#160;"/>
				</h:panelGroup>
				
				<h:panelGroup>
					<p:selectOneMenu id="userId" value="#{functionAccessView.selectedUserId}"
									 filter="true" filterMatchMode="contains" caseSensitive="false"
									 editable="true">
						<f:selectItem itemLabel="" itemValue=""/>
						<f:selectItems value="#{functionAccessView.userIdList}" var="userId" 
										 itemLabel="#{userId}" itemValue="#{userId}"/>
						<f:validator validatorId="hk.eduhk.rich.access.LDAPUserValidator"/>
						<f:attribute name="validateField" value="userId" />
						<f:ajax event="change" execute="@this" render="funcListPanel userIdMsg dataPanel"/>
					</p:selectOneMenu> 
					<p:message id="userIdMsg" for="userId"/>
				</h:panelGroup>
			
			</h:panelGrid>
			
			<h:panelGroup id="funcListPanel">

				<!-- Button Panel -->
				<h:panelGrid id="buttonPanel" rendered="#{not empty functionAccessView.selectedUserId}">
					<br/>
					<h:panelGroup styleClass="button-panel">
						<p:defaultCommand target="dummy"/>
						<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
						<p:commandButton value="Apply Changes" action="#{functionAccessView.updateUserRoles}" update="userId funcGrid" style="margin-right:5px;"/>
						<p:commandButton value="Select All" action="#{functionAccessView.selectAll()}" update="funcGrid" style="margin-right:5px;"/>
						<p:commandButton value="Unselect All" action="#{functionAccessView.unselectAll()}" update="funcGrid" style="margin-right:5px;"/>
					</h:panelGroup>
				</h:panelGrid>
				
				<!-- Function List -->			
				<h:panelGroup id="funcGrid" rendered="#{not empty functionAccessView.selectedUserId}">
					<p:selectManyCheckbox id="userGroup"
										  value="#{functionAccessView.selectedRoles}"
										  converter="hk.eduhk.rich.access.RoleConverter" 
										  layout="grid" columns="1">
						<f:selectItems value="#{functionAccessView.roleList}" 
									   var="role" itemLabel="#{role.name}" itemValue="#{role}" />
					</p:selectManyCheckbox>
				</h:panelGroup>
							
			</h:panelGroup>
			<br/>
			
			<h:panelGroup id="dataPanel" rendered="#{functionAccessView.userInfoList != null}">
				<br/>
				<p:toolbar>
					<p:toolbarGroup>
					</p:toolbarGroup>
					<p:toolbarGroup align="right">
		                <p:commandButton value="Export" icon="pi pi-download" ajax="false">
		                    <p:dataExporter type="xlsx" target="dataTable" fileName="userRoleList"/>
		                </p:commandButton>
		            </p:toolbarGroup>
		        </p:toolbar>
				<p:dataTable id="dataTable" style="width:100%;" rows="10" rowsPerPageTemplate="10,20,50,100" stripedRows="true" size="small"
								 var="dataRow" widgetVar="dtUser"
								 value="#{functionAccessView.userInfoList}" 
								 reflow="true"
								 paginator="true"
								 paginatorPosition="bottom"
								paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								currentPageReportTemplate="Total Number of User(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
		                  		 tableStyle="table-layout:auto;">
					<f:facet name="header">
		                <div style="display:flex; justify-content:space-between;">
		                    <span style="font-size:20px">User Role List</span>
		
		                    <span class="filter-container ui-input-icon-left">
		                        <i class="pi pi-search"></i>
		                        <p:inputText id="globalFilter" onkeyup="PF('dtUser').filter()" placeholder="Search"/>
		                    </span>
		
		                </div>
		            </f:facet>
	                <p:column headerText="Role" sortBy="#{dataRow.roleId}" filterBy="#{dataRow.role.name}">
						<h:outputText value="#{dataRow.role.name}"/>
					</p:column>
					<p:column headerText="User ID" sortBy="#{dataRow.userId}" filterBy="#{dataRow.userId}">
						<h:outputText value="#{dataRow.userId}"/>
					</p:column>			
					<p:column filterBy="#{dataRow.staff.fullname}">
						<f:facet name="header">Name</f:facet>
						<h:outputText value="#{dataRow.staff.fullname}"/>
					</p:column>		
					<p:column filterBy="#{dataRow.staff.dept_code}">
						<f:facet name="header">Dept.</f:facet>
						<h:outputText value="#{dataRow.staff.dept_code}"/>
					</p:column>				
					<p:column>
						<f:facet name="header">Creation Date</f:facet>
						<h:outputText value="#{dataRow.creationDate}">
						<f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</p:column>		
	            </p:dataTable>
				<p:blockUI block="dataTable" trigger="dataTable">
		            <i class="pi pi-spin pi-spinner" style="font-size: 3rem"></i>
		        </p:blockUI>
			</h:panelGroup>
			
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		</h:form>
		
		<br/>
	
	</p:panel>
	</ui:define>
</ui:composition>