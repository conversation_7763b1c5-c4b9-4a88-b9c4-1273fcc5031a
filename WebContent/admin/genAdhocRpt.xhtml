<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:component="http://java.sun.com/jsf/composite/component"
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="html_head">
		<link rel="stylesheet" media="screen, print" type="text/css" href="#{request.contextPath}/resources/css/bootstrap.min.css" />
		<style>
			a:link { text-decoration: none; }
			a:visited { text-decoration: none; }
			a:hover { text-decoration: none; }
			a:active { text-decoration: none; }
		</style>
	</ui:define>
	<ui:define name="mainContent">	
		<p:panel id="contentPanel">
			<span class="admin-content-title"><i class="fa-solid fa-chart-column"></i> Generate Ad hoc Report</span>
			<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
			<h:form id="dataForm">
				<p:panel>
					<f:facet name="header">
						<div>
							<span style="color:#1f1645;">Report</span>
						</div>
					</f:facet>
					<div class="ui-g">
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="Name" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="reportName" title="reportName" label="Report Name" value="#{adhocRptView.selectedRpt}" 
									style="margin-top:4px;margin-right:10px;">
									<f:selectItem itemLabel="-- Please select --" itemValue=""/>
									<f:selectItems value="#{adhocRptView.adhocRptList}" var="d" 
										itemLabel="#{d.rpt_desc}"
										itemValue="#{d.rpt_code}" />
									<p:ajax event="change"  listener="#{adhocRptView.disableDownloadButton}" update="reportNote generateBtn exportExcelBtn downloadChart reportDeptTitle reportDept"/>	
							</p:selectOneMenu>	
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<h:panelGroup id="reportNoteGroup">
								<p:outputLabel id="reportNote" value="#{adhocRptView.selectedRptnote}" escape="false"></p:outputLabel>
							</h:panelGroup>	
						</div>

					</div>
				</p:panel>
				<br/>
				<p:panel>
				<f:facet name="header">
					<div>
						<span style="color:#1f1645;">Filtering Criteria</span>
					</div>
				</f:facet>
					<div class="ui-g">
						<div class="ui-g-12 ui-md-2 ui-lg-2" >
							<p:outputLabel id="reportDeptTitle" value="Department" style="#{(adhocRptView.getSelectedRpt() eq 'A7')?'vertical-align: -webkit-baseline-middle; font-weight:700;':'display:none;'}"/>
						</div>
					
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="reportDept"  value="#{adhocRptView.selectedDept}" style="#{(adhocRptView.getSelectedRpt() eq 'A7')?'margin-top:4px; margin-right:20px;':'display:none;'}" filter="true" filterMatchMode="contains">
									<f:selectItems value="#{adhocRptView.deptfilterList}"/>							
							</p:selectOneMenu>	
						</div>
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10">
							<p:selectOneMenu id="cdcfPeriod"  value="#{adhocRptView.selectedCdcfPeriods}" style="margin-top:4px; margin-right:20px;"	filter="true"
							filterMatchMode = "startsWith">
									<f:selectItem itemLabel="-- Please select --" itemValue="10000" itemDisabled="true"/>
									<f:selectItems value="#{cvView.cdcfPeriodList}" var="o" itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />	
									<p:ajax event="change" listener="#{adhocRptView.disableDownloadButton}" update="downloadChart"/>							
							</p:selectOneMenu>	
						</div>
						<div class="ui-g-12 ui-md-2 ui-lg-2">
							<p:outputLabel value="RI Date Period" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-1 ui-lg-1">
							<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-3 ui-lg-3">
							<p:datePicker id="riStartDate" view="date"
														title="Start Date (dd/MM/YYYY)" 
														label="Start Date (dd/MM/YYYY)" 
														value="#{adhocRptView.selectedStartDate}" 
														pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"/>	
						</div>
						<div class="ui-g-12 ui-md-1 ui-lg-1">
							<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
						</div>
						<div class="ui-g-12 ui-md-5 ui-lg-5">
							<p:datePicker id="riEndDate" view="date"
														title="End Date (dd/MM/YYYY)" 
														label="End Date (dd/MM/YYYY)" 
														value="#{adhocRptView.selectedEndDate}" 
														pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"/>	
						</div>
					
					

	
	            </div> 
	          </p:panel>
	          <br/>
			<h:panelGroup id="buttonGroup">
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
				
			    <p:commandButton id="generateBtn"  value="Generate" icon="pi pi-chart-bar"  widgetVar="generateBtnWV"
			    	style="#{(adhocRptView.getAdhocRptType() eq 'Chart')|| (adhocRptView.getAdhocRptType() eq 'Both') ?'':'display:none;'}"
			    	styleClass="btn-action"
           			action="#{adhocRptView.generateReport(adhocRptView.selectedRpt)}" update="@form msgs"
           								 onclick="PF('generateBtnWV').disable();PF('generateBtnDialog').show();"
										 oncomplete="PF('generateBtnWV').enable();PF('generateBtnDialog').hide();saveChart('#{adhocRptView.selectedRpt}')" />
										 										 					 
	           		<p:dialog widgetVar="generateBtnDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
						        <div>
					            	<h5>Generating</h5>
				        			<p:progressBar id="progressBarIndeterminate" 
				        				style="height:20px; width:250px;" mode="indeterminate"/>
					            </div>
					</p:dialog>
				
						  		
				<p:commandButton id="exportExcelBtn" value="Export the Excel" icon="pi pi-download" widgetVar="exportExcelBtnWV"
								style="#{(adhocRptView.getAdhocRptType() eq 'Excel')|| (adhocRptView.getAdhocRptType() eq 'Both')?'':'display:none;'}"
								styleClass="btn-action"
								escape="True"
								ajax="false"
						  		action="#{adhocRptView.exportReport(adhocRptView.selectedRpt)}" />
				
				<p:commandButton id="downloadChart" value="Download Chart" icon="pi pi-download"  widgetVar="downloadChartWV" 
			    	style="#{(adhocRptView.isChartGenerated == true) ?'':'display:none;'};margin-left:5px;margin-right:5px;" 
			    	update = "@form msgs" ajax = "false"
           			action = "#{adhocRptView.exportChart()}" />	
				
					
				<p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		        </p:confirmDialog>
	        </h:panelGroup>

            
            
			</h:form>
		</p:panel>
	</ui:define>
</ui:composition>