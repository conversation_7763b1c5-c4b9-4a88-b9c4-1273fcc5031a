<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">
			Java Function Testing Environment
		</h:panelGroup>
		<br/>
		<p:messages id="msgs"  autoUpdate="true" closable="true"/>
		
		<h:form id="form" enctype="multipart/form-data">
		

			<p:focus context="form"/>
			
			<div class="ui-g edit-panel">
					
				<!-- FILE -->
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					File Input
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:fileUpload value="#{javaTestView.file}" mode="simple" skinSimple="true"/> 
				</div>
			</div>
			
			
			<div class="ui-g edit-panel">
					
				<!-- URL -->
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					URL Input
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:inputText id="url" value="#{javaTestView.url}"/>
				</div>
				
			</div>
				
				
			
			<br/>
	
			<h:panelGroup id="buttonPanel" styleClass="button-panel">
				 <p:commandButton value="Submit FILE" ajax="false" action="#{javaTestView.upload}" styleClass="mt-3 ui-button-outlined block"
				 update=":msgs"/>
				 
				  <p:commandButton value="Submit URL" ajax="false" action="#{javaTestView.extractTextFromUrl(javaTestView.url)}" styleClass="mt-3 ui-button-outlined block"
				 update=":msgs"/>
				 
			</h:panelGroup>
			

			<br/>
			
			
			
		</h:form>
		<br/> 
 			
	</p:panel>
	</ui:define>
		
</ui:composition>