<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:o="http://omnifaces.org/ui"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="riType" value="#{riListingView.searchPanel.riType}" />
		<f:viewParam name="dataLevel" value="#{riListingView.searchPanel.viewType}" />
		<f:viewParam name="startDate" value="#{riListingView.searchPanel.paramStartDate}" />
		<f:viewParam name="endDate" value="#{riListingView.searchPanel.paramEndDate}" />
		<f:viewParam name="facDept" value="#{riListingView.searchPanel.paramFacDept}" />
		<f:viewParam name="outputType" value="#{riListingView.searchPanel.paramOutputType}" />
		<f:viewParam name="fundingSource" value="#{riListingView.searchPanel.paramFundingSource}" />
		<f:viewParam name="basic" value="#{riListingView.searchPanel.paramBasic}" />
		<f:viewParam name="name" value="#{riListingView.searchPanel.paramStaffName}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<style>
		.ui-datatable thead th {
			background: #135178 !important;
			color: #ffffff !important;
			max-height: 200px;
		} 
		.ui-datatable td {
			vertical-align: top;
		} 
	</style>
	<h:outputScript>		
		$(document).ready(function() {
			let searchParams = new URLSearchParams(window.location.search);
			searchParams.has('search');
			let paramSearch = searchParams.get('search');
			if (paramSearch == 'Y'){
				document.getElementById('dataForm:searchButton').click();
				return false;
			}
		});
		function disableOutputDownloadBtn()
		{
			setTimeout(function() {PF('outputDownloadBtn').disable();}, 50);
		}		
		
		function enableOutputDownloadBtn()
		{
			PF('outputDownloadBtn').enable();
		}

		function disableProjectDownloadBtn()
		{
			setTimeout(function() {PF('projectDownloadBtn').disable();}, 50);
		}		
		
		function enableProjectDownloadBtn()
		{
			PF('projectDownloadBtn').enable();
		}
		
		function disableAwardDownloadBtn()
		{
			setTimeout(function() {PF('awardDownloadBtn').disable();}, 50);
		}		
		
		function enableAwardDownloadBtn()
		{
			PF('awardDownloadBtn').enable();
		}
		
		function disablePatentDownloadBtn()
		{
			setTimeout(function() {PF('patentDownloadBtn').disable();}, 50);
		}		
		
		function enablePatentDownloadBtn()
		{
			PF('patentDownloadBtn').enable();
		}
		
	</h:outputScript>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>

	<h:form style="width:100%; max-width: 97vw;" 
			onkeypress="if (event.keyCode == 13) { document.getElementById('dataForm:searchButton').click(); return false; }">
		<component:riSearchPanel searchPanel="#{riListingView.searchPanel}"
		                         isRdoLib="#{riListingView.isRdoLib}"
		                         isDeptAdmin="#{riListingView.isDeptAdmin}"
		                         isUoaAdmin="#{riListingView.isUoaAdmin}"
		                         isAcadStaff="#{riListingView.isAcadStaff}"/>
	</h:form>
	<h:form id="dataForm">
		<c:set var="isRdoLib" value="#{riListingView.getIsRdoLib() == true}"/>
		<c:set var="isDeptAdmin" value="#{riListingView.getIsDeptAdmin() == true}"/>
		<c:set var="isAcadStaff" value="#{riListingView.getIsAcadStaff() == true}"/>
		<c:set var="isUoaAdmin" value="#{riListingView.getIsUoaAdmin() == true}"/>
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="searchButton" value="Search" update="resultPanels" icon="pi pi-search" style="#{riListingView.getIsAcadStaff()?'display:none;':''}"
						 widgetVar="searchBut" onclick="PF('searchBut').disable();PF('searchDialog').show();"
						 oncomplete="PF('searchBut').enable();PF('searchDialog').hide();"
						 actionListener="#{riListingView.setFirstSearch(true)}" ></p:commandButton><p:spacer width="10"/>
		<p:dialog widgetVar="searchDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
	        <div>
            	<h5>Searching</h5>
       			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
            </div>
	    </p:dialog>
		
		<h:panelGroup id="resultPanels">
			<h:panelGroup id="outputResultPanel"
						  rendered="#{riListingView.isFirstSearch() and (riListingView.searchPanel.getRiType() == riListingView.searchPanel.getRiTypeOutput())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download" 
								 widgetVar="outputDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="outputDataTable" fileName="outputData" options="#{riListingView.excelOpt}" 
                    				postProcessor="#{riListingView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="outputDataTable"
					 value="#{riListingView.getOutputList()}" 
					 var="output"
					 styleClass="default-dataTable long-dataTable"
					 stripedRows="true" size="small" style="font-size:12px;" showGridlines="true"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_10_100}"
                     tableStyle="table-layout:auto;"
					 >
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="RI No."/></f:facet>
						<h:outputText value="#{output.outputNo}" />
					</p:column>
					
					<p:column style="min-width:10em;" 
							  rendered="#{riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Staff"/></f:facet>
						<h:outputText value="#{output.name}" />
					</p:column>
					
					<!-- <p:column width="3em;" 
							  rendered="#{riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Authorship Type"/></f:facet>
						<h:outputText value="#{output.authorshipType}" />
					</p:column>
					
					<p:column width="3em;" 
							  rendered="#{riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Authorship"/></f:facet>
						<h:outputText value="#{output.authorship}" />
					</p:column>-->
					
					<p:column width="3em;" 
							  rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Staff Number"/></f:facet>
						<h:outputText value="#{output.staffNumber}" />
					</p:column>
					
					<p:column style="min-width:10em;"  
							  rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Department"/></f:facet>
						<h:outputText value="#{output.department}" />
					</p:column>
					
					<p:column style="min-width:14em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Author List"/></f:facet>
						<h:outputText value="#{output.authorListWithDeptAndAuthType}" escape="false" />
					</p:column>
					
					<!-- 2.x -->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Display Language"/></f:facet>
						<h:outputText value="#{output.language}" />
					</p:column>
					
					<p:column >
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Research Output Category"/></f:facet>
						<h:outputText value="#{output.sapOutputCat}" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Research Output Type"/></f:facet>
						<h:outputText value="#{output.sapOutputType}" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Journal Publication Discipline"/></f:facet>
						<h:outputText value="#{output.journalPublicationDiscipline}" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Type of Research Activity"/></f:facet>
						<h:outputText value="#{output.sapReferedJournal}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Author Publication By-line EdUHK"/></f:facet>
						<h:outputText value="#{output.getIEdWorkInd()}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Enh. T&amp;L in High Edu."/></f:facet>
						<h:outputText value="#{output.isEnhHighEdu}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Is the project(s) for creating the research output fully/partially funded by RGC?"/></f:facet>
						<h:outputText value="#{output.is_rgc_proj}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Project number(s) of the RGC funded project(s)"/></f:facet>
						<h:outputText value="#{output.rgc_proj_num}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="International Conference"/></f:facet>
						<h:outputText value="#{output.isIntlConf}" />
					</p:column>
					
					<!-- 3.x -->
					
					<p:column style="min-width:10em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Preview in APA format"/></f:facet>
						<h:outputText value="#{output.apaCitation}" />
					</p:column>
					
					<!-- 4.x -->
					
					<p:column style="min-width:10em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Title of Research Output"/></f:facet>
						<h:outputText value="#{output.articleTitle}" />
					</p:column>
					
					<p:column style="min-width:10em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Name of Publication/ Conference/ Journal in which the output appears"/></f:facet>
						<h:outputText value="#{output.title}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Rank"/></f:facet>
						<h:outputText value="#{output.researchActivityRanking}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib and output.department != 'GO' }">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="No. of contributors"/></f:facet>
						<h:outputText value="#{output.totalNoOfAuthor}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="ISSN"/></f:facet>
						<h:outputText value="#{output.issn}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="EISSN"/></f:facet>
						<h:outputText value="#{output.eissn}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="ISBN"/></f:facet>
						<h:outputText style="word-break: break-all;" value="#{output.isbn}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="DOI"/></f:facet>
						<h:outputText style="word-break: break-all;" value="#{output.doi}" />
					</p:column>
					
					<p:column style="min-width:10em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="URL"/></f:facet>
						<h:outputText style="word-break: break-all;" value="#{output.url}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Volume (Issue)"/></f:facet>
						<h:outputText value="#{output.volIssue}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Page No."/></f:facet>
						<h:outputText value="#{output.pageNum}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Page No. From"/></f:facet>
						<h:outputText value="#{output.pageNumFrom}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Page No. To"/></f:facet>
						<h:outputText value="#{output.pageNumTo}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="From Date"/></f:facet>
						<h:outputText value="#{output.fromMonth}/#{output.fromYear}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="To Date"/></f:facet>
						<h:outputText value="#{output.toMonth}/#{output.toYear}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Article Number"/></f:facet>
						<h:outputText value="#{output.articleNo}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Place"/></f:facet>
						<h:outputText value="#{output.city}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Open Access Status"/></f:facet>
						<h:outputText value="#{output.open_access_statStr}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Is Article Processing Charge (APC) required for publishing the output in open access model?"/></f:facet>
						<h:outputText value="#{output.open_access_apc}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Is the open access article upon the payment made under financial arrangement(s) other than Article Processing Charge (APC), e.g. Open Access Membership?"/></f:facet>
						<h:outputText value="#{output.open_access_apc_payment}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Any payment made by EdUHK/ author(s) affiliated to EdUHK?"/></f:facet>
						<h:outputText value="#{output.open_access_payment}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Actual amount of Article Processing Charge (APC) paid to the publisher (in HK dollars)"/></f:facet>
						<h:outputText value="#{output.apc_val}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Article Acceptance Date"/></f:facet>
						<h:outputText value="#{output.open_access_art_acc_date}"/>
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Embargo End Date"/></f:facet>
						<h:outputText value="#{output.open_access_emb_end_date}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Embargo Period"/></f:facet>
						<h:outputText value="#{output.open_access_emb_period_date}"/>
					</p:column>
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Census Date"/></f:facet>
						<h:outputText value="#{sysParamView.getValue('CENSUS_DATE')}" />
					</p:column>
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Calculated Embargo End Date"/></f:facet>
						<h:outputText value="#{output.cal_emb_end_date}" rendered="#{output.cal_emb_end_date ne null}">
							<f:convertDateTime pattern="dd/MM/yyyy" />
						</h:outputText>
					</p:column>
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Is the calculated embargo end date after census date?"/></f:facet>
						<h:outputText value="#{output.emb_date_after_census}" />
					</p:column>
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="The open access will be delayed within one year after reporting period"/></f:facet>
						<h:outputText value="#{output.delay_open_access}" />
					</p:column>
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Under Transformative Agreement?"/></f:facet>
						<h:outputText value="#{output.is_tran_agrt}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Actual Amount Paid to the Publisher for Open Access Publishing under Transformative Agreement"/></f:facet>
						<h:outputText value="#{output.tran_agrt_val}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Publisher/ Conference Organiser(s)/ Others"/></f:facet>
						<h:outputText value="#{output.publisher}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Details of the Output"/></f:facet>
						<h:outputText value="#{output.otherDetails}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="List of editor(s) in sequential order as appeared in the research output"/></f:facet>
						<h:outputText value="#{output.nameOtherEditors}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="List of author(s) / contributor(s) in sequential order as appeared in the research output"/></f:facet>
						<h:outputText value="#{output.nameOtherPos}" />
					</p:column>
					
					<!-- 6.x -->
					
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Sector"/></f:facet>
						<h:outputText value="#{output.schDtlCode}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Disciplinary Area of the Output"/></f:facet>
						<h:outputText value="#{output.daDtlCode}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Disciplinary Area Other"/></f:facet>
						<h:outputText value="#{output.otherDaDtl}" />
					</p:column>
					
					<!--  <p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="CDCF Selected"/></f:facet>
						<h:outputText value="#{output.cdcf_selected_ind}" />
					</p:column>-->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="SDG"/></f:facet>
						<h:outputText value="#{output.sdg_str}" />
					</p:column>
					
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="CDCF Status"/></f:facet>
						<h:outputText value="#{output.CDCFStatus}" />
					</p:column>
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="projectResultPanel" 
						  rendered="#{riListingView.isFirstSearch() and (riListingView.searchPanel.getRiType() == riListingView.searchPanel.getRiTypeProject())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="projectDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="projectDataTable" fileName="projectData" options="#{riListingView.excelOpt}" 
                    				postProcessor="#{riListingView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="projectDataTable"
					 value="#{riListingView.getProjectList()}" 
					 var="project"
					 styleClass="default-dataTable long-dataTable"
					 stripedRows="true" size="small" style="font-size:12px;" showGridlines="true"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_10_100}"
                     tableStyle="table-layout:auto;"
					 >
					<p:column width="3em;" rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="RI No."/></f:facet>
						<h:outputText value="#{project.projectNo}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Staff"/></f:facet>
						<h:outputText value="#{project.staffName}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Staff Number"/></f:facet>
						<h:outputText value="#{project.staffNumber}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Department"/></f:facet>
						<h:outputText value="#{project.department}" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Principal Investigator"/></f:facet>
						<h:outputText value="#{project.prin_inves_author_list}" escape="false" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Co-Principal Investigator"/></f:facet>
						<h:outputText value="#{project.co_prin_inves_author_list}" escape="false" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Co-Investigator"/></f:facet>
						<h:outputText value="#{project.co_inves_author_list}" escape="false" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Other Member"/></f:facet>
						<h:outputText value="#{project.other_author_list}" escape="false" />
					</p:column>
					
					<!-- 2.x -->
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Investigator Project By-line EdUHK"/></f:facet>
						<h:outputText value="#{project.ied_work_ind}" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Project Type"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.project_type}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Activity Code"/></f:facet>
						<h:outputText value="#{project.activity_code}" />
					</p:column>
					
					<p:column width="5em;">
						<f:facet name="header">
							<h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Is it an Innovation/Technology related project?"/>
						</f:facet>
						<h:outputText value="#{project.tech_proj}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Project number"/></f:facet>
						<h:outputText value="#{project.rgc_proj_num}" />
					</p:column>
					
					<!-- 4.x -->
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Project Title"/></f:facet>
						<h:outputText value="#{project.projTitle}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="A Brief Description/Summary of Project"/></f:facet>
						<h:outputText value="#{project.generateConcatProjectSummary()}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Commencement Date"/></f:facet>
						<h:outputText value="#{project.fromdate}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="(Expected) Completion Date"/></f:facet>
						<h:outputText value="#{project.todate}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Project Duration (by Month)"/></f:facet>
						<h:outputText value="#{project.durationMonth}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Project Duration (by Year)"/></f:facet>
						<h:outputText value="#{project.durationYear}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Funded Project"/></f:facet>
						<h:outputText value="#{project.funded_proj}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Funding Body"/></f:facet>
						<h:outputText value="#{project.funding_body}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Funding Organization"/></f:facet>
						<h:outputText value="#{project.funding_org}" />
					</p:column>
					
					<p:column>
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Funding Source"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.sap_funding_source}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Name of Funding"/></f:facet>
						<h:outputText value="#{project.funding_others}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Funding Amount of the Project (HKD)"/></f:facet>
						<h:outputText value="#{project.sap_grant_amt}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Actual Amount Received by EdUHK(HKD)"/></f:facet>
						<h:outputText value="#{project.released_val}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Collaborate with UGC Funded Institute(s)?"/></f:facet>
						<h:outputText value="#{project.collab_partner_ugc}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Role of EdUHK (Collaborate with Partner(s) of Other UGC Funded Institution(s))"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.collabT630}" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Collaborate with Mainland or Oversea Institute(s)?"/></f:facet>
						<h:outputText value="#{project.collab_partner_non_ins}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Role of EdUHK (Collaborate with Partner(s) in Mainland or Overseas)"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.collabT690}" />
					</p:column>
					
					<!-- <p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Details of Non-local Collaborative Partner(s)"/></f:facet>
						<h:outputText value="#{project.getCollaborativePartnerStr()}" escape="false" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Details of Research Output(s)"/></f:facet>
						<h:outputText value="#{project.getCollaborativeOutputStr()}" escape="false" />
					</p:column>-->
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Name of Non-local Institute(s)"/></f:facet>
						<h:outputText value="#{project.getName_of_collaborative_partner()}" escape="false" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="City of Non-local Institute(s)"/></f:facet>
						<h:outputText value="#{project.getCollaborative_partner_city()}" escape="false" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Country of Non-local Institute(s)"/></f:facet>
						<h:outputText value="#{project.getCollaborativePartnerCountryStr(false)}" escape="false" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Title of Output(s)"/></f:facet>
						<h:outputText value="#{project.getCollab_output_title()}" escape="false" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="the Actual/Expected Date of Producing Output(s)"/></f:facet>
						<h:outputText value="#{project.getCollab_output_date()}" escape="false" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Output Type"/></f:facet>
						<h:outputText value="#{project.getCollab_output_typeStr()}" escape="false" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Name of Non-local Institute(s) will be/has been appeared on the output"/></f:facet>
						<h:outputText value="#{project.getCollab_output_inst()}" escape="false" />
					</p:column>
					
					<p:column width="3em;" rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Sector"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.schDtlCode}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Disciplinary Area of the Project"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.daCode} - #{project.daDtlCode}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Disciplinary Area Other"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.otherDaDtl}" />
					</p:column>
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="Keywords"/></f:facet>
						<h:outputText style="word-break: normal;" value="#{project.keyword}" />
					</p:column>
					
					<!-- <p:column width="3em;">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="CDCF Selected"/></f:facet>
						<h:outputText value="#{project.cdcf_selected_ind}" />
					</p:column>-->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="SDG"/></f:facet>
						<h:outputText value="#{project.sdg_str}" />
					</p:column>
					
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header"><h:outputText style="transform: rotate(180deg); writing-mode : vertical-rl;" value="CDCF Status"/></f:facet>
						<h:outputText value="#{project.CDCFStatus}" />
					</p:column>
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="awardResultPanel" 
						  rendered="#{riListingView.isFirstSearch() and (riListingView.searchPanel.getRiType() == riListingView.searchPanel.getRiTypeAward())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="awardDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="awardDataTable" fileName="awardData" options="#{riListingView.excelOpt}" 
                    				postProcessor="#{riListingView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="awardDataTable"
					 value="#{riListingView.getAwardList()}" 
					 var="award"
					 styleClass="default-dataTable long-dataTable"
					 stripedRows="true" size="small" style="font-size:12px;" showGridlines="true"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_10_100}"
                     tableStyle="table-layout:auto;"
					 >
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText value="RI No."/></f:facet>
						<h:outputText value="#{award.riNo}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header">Staff</f:facet>
						<h:outputText value="#{award.staffFullname}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{award.staffNumber}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{award.staffDept}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Recipient List</f:facet>
						<h:outputText value="#{award.recipient_list}" escape="false" />
					</p:column>
					
					<!-- 2.x -->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header">Recipient Award By-line EdUHK</f:facet>
						<h:outputText value="#{award.IEdWorkInd}" />
					</p:column>
					
					<!-- 4.x -->
					<p:column>
						<f:facet name="header">Name of Prize/Award</f:facet>
						<h:outputText value="#{award.awardName}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Name of Organization Conferring the Prize/Award</f:facet>
						<h:outputText value="#{award.orgName}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Brief Description of Prize/Award</f:facet>
						<h:outputText value="#{award.shortDesc}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Detail Description of Prize/Award
					</f:facet>
						<h:outputText value="#{award.fullDesc}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Date of Receipt</f:facet>
						<h:outputText value="#{award.awardDate}" />
					</p:column>
					
					<!-- <p:column>
						<f:facet name="header">CDCF Selected</f:facet>
						<h:outputText value="#{award.cdcf_selected_ind}" />
					</p:column>-->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header">CDCF Status</f:facet>
						<h:outputText value="#{award.CDCFStatus}" />
					</p:column>
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="patentResultPanel" 
						  rendered="#{riListingView.isFirstSearch() and (riListingView.searchPanel.getRiType() == riListingView.searchPanel.getRiTypePatent())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="patentDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="patentDataTable" fileName="patentData" options="#{riListingView.excelOpt}" 
                    				postProcessor="#{riListingView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="patentDataTable"
					 value="#{riListingView.getPatentList()}" 
					 var="patent"
					 styleClass="default-dataTable long-dataTable"
					 stripedRows="true" size="small" style="font-size:12px;" showGridlines="true"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_10_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					<p:column rendered="#{isRdoLib or isDeptAdmin}">
						<f:facet name="header"><h:outputText value="RI No."/></f:facet>
						<h:outputText value="#{patent.riNo}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header">Staff</f:facet>
						<h:outputText value="#{patent.staffFullname}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{patent.staffNumber}" />
					</p:column>
					
					<p:column rendered="#{(riListingView.searchPanel.getListingType() == riListingView.searchPanel.getListTypeStaffValue()) and (isRdoLib or isDeptAdmin)}">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{patent.staffDept}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Inventor</f:facet>
						<h:outputText value="#{patent.inventor_list}" escape="false" />
					</p:column>
					
					<!-- 2.x -->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header">Inventor Patent By-line EdUHK</f:facet>
						<h:outputText value="#{patent.IEdWorkInd}" />
					</p:column>
					
					<!-- 4.x -->
					<p:column>
						<f:facet name="header">Patent Type</f:facet>
						<h:outputText value="#{patent.patentGranted}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Name of Patent</f:facet>
						<h:outputText value="#{patent.patentName}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Serial Number of Patent</f:facet>
						<h:outputText value="#{patent.serialNo}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Country/Place Granting the National Patent</f:facet>
						<h:outputText value="#{patent.country}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Type of Patent</f:facet>
						<h:outputText value="#{patent.patentType}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Brief Description of Patent</f:facet>
						<h:outputText value="#{patent.shortDesc}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Detail Description of Patent
					</f:facet>
						<h:outputText value="#{patent.fullDesc}" />
					</p:column>
					
					<p:column>
						<f:facet name="header">Date of Application/Grant</f:facet>
						<h:outputText value="#{patent.patentDate}" />
					</p:column>
					
					<!-- <p:column>
						<f:facet name="header">CDCF Selected</f:facet>
						<h:outputText value="#{patent.cdcf_selected_ind}" />
					</p:column>-->
					
					<p:column rendered="#{isRdoLib}">
						<f:facet name="header">CDCF Status</f:facet>
						<h:outputText value="#{patent.CDCFStatus}" />
					</p:column>
					
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="raeResultPanel" 
						  rendered="#{riListingView.isFirstSearch() and (riListingView.searchPanel.getRiType() == riListingView.searchPanel.getRiTypeRae())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="raeDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="raeDataTable" fileName="raeData" options="#{riListingView.excelOpt}" 
                    				postProcessor="#{riListingView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="raeDataTable"
					 value="#{riListingView.getRaeOutputList()}" 
					 var="rae"
					 styleClass="default-dataTable long-dataTable"
					 stripedRows="true" size="small" style="font-size:12px;" showGridlines="true"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_10_100}"
                     tableStyle="table-layout:auto;" >					
					
					<p:column style="min-width:5em;" sortBy="#{rae.output_no}" >
						<f:facet name="header">RO No.</f:facet>
						<h:outputText value="#{rae.output_no}" />
					</p:column>
					
					<p:column style="min-width:5em;" sortBy="#{rae.staff_number}" >
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{rae.staff_number}" />
					</p:column>
					
					<p:column style="min-width:15em;" sortBy="#{rae.staff_name}" >
						<f:facet name="header">Staff Name</f:facet>
						<h:outputText value="#{rae.staff_name}" />
					</p:column>
					
					<p:column style="min-width:15em;" sortBy="#{rae.uoa}">
						<f:facet name="header">UoA</f:facet>
						<h:outputText value="#{rae.uoa}" />
					</p:column>
					
					<!--  JCR 2022 -->
					<p:column style="min-width:3em;" sortBy="#{rae.jcr}" >
						<f:facet name="header">JCR 2022</f:facet>
						<h:outputText value="#{rae.jcr}" />
					</p:column>
					
					<!--  SJR 2022 -->
					<p:column style="min-width:3em;" sortBy="#{rae.sjr}" >
						<f:facet name="header">SJR 2022</f:facet>
						<h:outputText value="#{rae.sjr}" />
					</p:column>
					
					<!-- Internal RAE Rating -->
					<p:column style="min-width:3em;" sortBy="#{rae.sjr}" >
						<f:facet name="header">Internal RAE Rating</f:facet>
						<h:outputText value="#{rae.int_rae_rate}" />
					</p:column>
					
					<p:column style="min-width:3em;" sortBy="#{rae.faculty}" >
						<f:facet name="header">Faculty</f:facet>
						<h:outputText value="#{rae.faculty}" />
					</p:column>
										
					<p:column style="min-width:3em;"  sortBy="#{rae.department}"  >
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{rae.department}" />
					</p:column>

					<p:column width="3em;"  sortBy="#{rae.info_comp}" >
						<f:facet name="header">Completion</f:facet>
						<h:outputText value="#{rae.info_comp}" />
					</p:column>
					
					<p:column width="3em;"  sortBy="#{rae.rae_status_code}" >
						<f:facet name="header">Status - RO Info</f:facet>
						<h:outputText value="#{rae.rae_status_code}" />
					</p:column>
					
					<p:column width="3em;"  sortBy="#{rae.rae_status_code_panel}" >
						<f:facet name="header">Status - Panel Info</f:facet>
						<h:outputText value="#{rae.rae_status_code_panel}" />
					</p:column>
				
					<p:column width="3em;"  sortBy="#{rae.rae_status_code_full_ver}" >
						<f:facet name="header">Status - Full Ver.</f:facet>
						<h:outputText value="#{rae.rae_status_code_full_ver}" />
					</p:column>
					
					<p:column width="3em;"  sortBy="#{rae.rae_status_code_oth_info}" >
						<f:facet name="header">Status - Oth. Info</f:facet>
						<h:outputText value="#{rae.rae_status_code_oth_info}" />
					</p:column>	

					<p:column width="3em;"  sortBy="#{rae.rae_status_ineligible}" >
						<f:facet name="header">Status - Ineligible</f:facet>
						<h:outputText value="#{rae.rae_status_ineligible}" />
					</p:column>	

					<p:column width="3em;"  sortBy="#{rae.citation_chk_code}" >
						<f:facet name="header">Citation Checking</f:facet>
						<h:outputText value="#{rae.citation_chk_code}" />
					</p:column>	
					
					<p:column width="3em;"  sortBy="#{rae.citation_chk_abs_code}" >
						<f:facet name="header">Abstract Checking</f:facet>
						<h:outputText value="#{rae.citation_chk_abs_code}" />
					</p:column>	

					<p:column width="3em;"  sortBy="#{rae.citation_chk_fulltext_code}" >
						<f:facet name="header">Full Text Checking</f:facet>
						<h:outputText value="#{rae.citation_chk_fulltext_code}" />
					</p:column>	

					<p:column width="3em;"  sortBy="#{rae.copyright_clr_code}" >
						<f:facet name="header">Copyright Clearance</f:facet>
						<h:outputText value="#{rae.copyright_clr_code}" />
					</p:column>	

					<p:column width="3em;"  sortBy="#{rae.ro_remarks}" >
						<f:facet name="header">Remarks (for RDO)</f:facet>
						<h:outputText value="#{rae.ro_remarks}" />
					</p:column>	
							
					<p:column width="3em;"  sortBy="#{rae.ro_remarks_lib}" >
						<f:facet name="header">Remarks (for LIB)</f:facet>
						<h:outputText value="#{rae.ro_remarks_lib}" />
					</p:column>						
					
					<p:column width="3em;"  sortBy="#{rae.apa_citation}" >
						<f:facet name="header">Preview</f:facet>
						<h:outputText value="#{rae.apa_citation}" />
					</p:column>							
					
					<p:column width="3em;" sortBy="#{rae.sel_type}" >
						<f:facet name="header">Selection Type</f:facet>
						<h:outputText value="#{rae.sel_type}" />
					</p:column>					

					<p:column width="3em;" sortBy="#{rae.just_dw_request}" >
						<f:facet name="header">Justification for Double-weighting</f:facet>
						<h:outputText value="#{rae.just_dw_request}" />
					</p:column>	
					
					<p:column width="3em;" sortBy="#{rae.non_trad_output_ind}" >
						<f:facet name="header">Non-trad. RO</f:facet>
						<h:outputText value="#{rae.non_trad_output_ind}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.info_of_non_trad_output}" >
						<f:facet name="header">Supp. Info on Non-tri. RO</f:facet>
						<h:outputText value="#{rae.info_of_non_trad_output}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.researchAreaOfRo}" >
						<f:facet name="header">Research Area</f:facet>
						<h:outputText value="#{rae.researchAreaOfRo}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.sub_disc_code}" >
						<f:facet name="header">Sub-discipline</f:facet>
						<h:outputText value="#{rae.sub_disc_code}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.cat_code}" >
						<f:facet name="header">Category Code</f:facet>
						<h:outputText value="#{rae.cat_code}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.output_type}" >
						<f:facet name="header">Output Type (RAE)</f:facet>
						<h:outputText value="#{rae.output_type}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.oth_output_type}" >
						<f:facet name="header">Other Output Type</f:facet>
						<h:outputText value="#{rae.oth_output_type}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.sap_refered_journal}" >
						<f:facet name="header">Type of Research Activity</f:facet>
						<h:outputText value="#{rae.sap_refered_journal}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.inter_research}" >
						<f:facet name="header">Interdisciplinary</f:facet>
						<h:outputText value="#{rae.inter_research}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.pri_research_area_inter}" >
						<f:facet name="header">Pri. Area</f:facet>
						<h:outputText value="#{rae.pri_research_area_inter}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.pri_research_area_cat}" >
						<f:facet name="header">Pri. Area Cat</f:facet>
						<h:outputText value="#{rae.pri_research_area_cat}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.sec_research_area_inter}" >
						<f:facet name="header">Sec. Area</f:facet>
						<h:outputText value="#{rae.sec_research_area_inter}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.sec_research_area_cat}" >
						<f:facet name="header">Sec. Area Cat</f:facet>
						<h:outputText value="#{rae.sec_research_area_cat}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.ai_tool_desc}" >
						<f:facet name="header">AI Tool</f:facet>
						<h:outputText value="#{rae.ai_tool_desc}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.rel_lang}" >
						<f:facet name="header">Output Lang.</f:facet>
						<h:outputText value="#{rae.rel_lang}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.oth_lang}" >
						<f:facet name="header">Other Lang.</f:facet>
						<h:outputText value="#{rae.oth_lang}" />
					</p:column>
					<p:column style="min-width:20em;" sortBy="#{rae.title_of_eng_output}" >
						<f:facet name="header">Title (ENG)</f:facet>
						<h:outputText value="#{rae.title_of_eng_output}" />
					</p:column>
					<p:column style="min-width:20em;" sortBy="#{rae.title_of_non_eng_output}" >
						<f:facet name="header">Title (Non ENG)</f:facet>
						<h:outputText value="#{rae.title_of_non_eng_output}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.no_of_co_author}" >
						<f:facet name="header">No. of Co-author(s)</f:facet>
						<h:outputText value="#{rae.no_of_co_author}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.explanation_of_author_ctb}" >
						<f:facet name="header">Explan. of Author's Contribution</f:facet>
						<h:outputText value="#{rae.explanation_of_author_ctb}" />
					</p:column>
					<p:column style="min-width:15em;" sortBy="#{rae.list_of_author_eng}" >
						<f:facet name="header">List of Author (ENG)</f:facet>
						<h:outputText value="#{rae.list_of_author_eng}" />
					</p:column>
					<p:column style="min-width:15em;" sortBy="#{rae.list_of_author_non_eng}" >
						<f:facet name="header">List of Author (Non ENG)</f:facet>
						<h:outputText value="#{rae.list_of_author_non_eng}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.pseudonym}" >
						<f:facet name="header">Pseudonym</f:facet>
						<h:outputText value="#{rae.pseudonym}" />
					</p:column>
					
					<!-- <p:column width="3em;" sortBy="#{rae.sub_staff_role}" >
						<f:facet name="header">Role of Sub. Staff</f:facet>
						<h:outputText value="#{rae.sub_staff_role}" />
					</p:column> -->
					<p:column width="3em;" sortBy="#{rae.stmt_ori_sign}" >
						<f:facet name="header">Statement on Originality and Significance</f:facet>
						<h:outputText value="#{rae.stmt_ori_sign}" />
					</p:column>
					<!-- <p:column width="3em;" sortBy="#{rae.irc_has_result}" >
						<f:facet name="header">Intl. Research Collab.</f:facet>
						<h:outputText value="#{rae.irc_has_result}" />
					</p:column> -->
					<p:column width="3em;" sortBy="#{rae.published_census_date}" >
						<f:facet name="header">Published as of Census Date</f:facet>
						<h:outputText value="#{rae.published_census_date}" />
					</p:column>
										
										
					<p:column width="3em;"  sortBy="#{rae.fromDate}"  >
						<f:facet name="header">Publication Date</f:facet>
						<h:outputText value="#{rae.fromDate}" />
					</p:column>
					
					<p:column width="3em;"  sortBy="#{rae.supp_count}"  >
						<f:facet name="header">File: Supporting Doc / Letter of Acceptance</f:facet>
						<h:outputText value="#{rae.supp_count}" />
					</p:column>
					
					<p:column style="min-width:20em;" sortBy="#{rae.book_title}" >
						<f:facet name="header">Book / Journal Title</f:facet>
						<h:outputText value="#{rae.book_title}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.isbn}" >
						<f:facet name="header">ISBN</f:facet>
						<h:outputText value="#{rae.isbn}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.issn}" >
						<f:facet name="header">ISSN</f:facet>
						<h:outputText value="#{rae.issn}" />
					</p:column>

					<p:column width="3em;" sortBy="#{rae.eissn}" >
						<f:facet name="header">eISSN</f:facet>
						<h:outputText value="#{rae.eissn}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.vol_issue}" >
						<f:facet name="header">Volume (Issue)</f:facet>
						<h:outputText value="#{rae.vol_issue}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.page_num}" >
						<f:facet name="header">Page No.</f:facet>
						<h:outputText value="#{rae.page_num}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.article_no}" >
						<f:facet name="header">Article No.</f:facet>
						<h:outputText value="#{rae.article_no}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.publisher}" >
						<f:facet name="header">Publisher / Manufacturer</f:facet>
						<h:outputText value="#{rae.publisher}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.funder}" >
						<f:facet name="header">Funder(s) and Funding Programme(s)</f:facet>
						<h:outputText value="#{rae.funder}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.colNameLoc}" >
						<f:facet name="header">Name and Location of Collaborator(s)</f:facet>
						<h:outputText value="#{rae.colNameLoc}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.intColInd}" >
						<f:facet name="header">International Research Collaboration</f:facet>
						<h:outputText value="#{rae.intColInd}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.desc_loc_output}" >
						<f:facet name="header">Description on Location</f:facet>
						<h:outputText value="#{rae.desc_loc_output}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10RevInd}" >
						<f:facet name="header">Panel 10 - No Peer-review</f:facet>
						<h:outputText value="#{rae.panel10RevInd}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10RevExplain}" >
						<f:facet name="header">Panel 10 - Explan. of Quality Review</f:facet>
						<h:outputText value="#{rae.panel10RevExplain}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10RoInd}" >
						<f:facet name="header">Panel 10 - Post-pub. Review</f:facet>
						<h:outputText value="#{rae.panel10RoInd}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.p10_count}" >
						<f:facet name="header">File: Panel 10 - Post-pub. Review</f:facet>
						<h:outputText value="#{rae.p10_count}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.panel10PtbInd}" >
						<f:facet name="header">Panel 10 - Pratice-based</f:facet>
						<h:outputText value="#{rae.panel10PtbInd}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10ExplainPtb}" >
						<f:facet name="header">Panel 10 - Explan. of Practice-based or Commissioned RO</f:facet>
						<h:outputText value="#{rae.panel10ExplainPtb}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10Explanation}" >
						<f:facet name="header">Panel 10 - Explan. of Conference Paper and Report</f:facet>
						<h:outputText value="#{rae.panel10Explanation}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10Explanation}" >
						<f:facet name="header">Panel 10 - Published Report</f:facet>
						<h:outputText value="#{rae.panel10PubInd}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel10Explanation}" >
						<f:facet name="header">Panel 10 - Explan. for published Report</f:facet>
						<h:outputText value="#{rae.panel10PubDesc}" />
					</p:column>
					
					<p:column style="min-width:20em;"  sortBy="#{rae.p12_count}" >
						<f:facet name="header">File: Panel 12 - Body of Evidence</f:facet>
						<h:outputText value="#{rae.p12_count}" />
					</p:column>
		
					<p:column width="3em;" sortBy="#{rae.panel12UrlEvidence}" >
						<f:facet name="header">Panel 12 - URL: Body of Evidence</f:facet>
						<h:outputText value="#{rae.panel12UrlEvidence}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.panel12MulUrl}" >
						<f:facet name="header">Panel 12 - URL: Multi-Component Output</f:facet>
						<h:outputText value="#{rae.panel12MulUrl}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.format_full_ver_submit}" >
						<f:facet name="header">Format of Full Ver. Submission</f:facet>
						<h:outputText value="#{rae.format_full_ver_submit}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.medium_count}" >
						<f:facet name="header">No. of Medium used</f:facet>
						<h:outputText value="#{rae.medium_count}" />
					</p:column>

					<p:column width="3em;" sortBy="#{rae.phy_audio_qty}" >
						<f:facet name="header">Medium - Audio Tape</f:facet>
						<h:outputText value="#{rae.phy_audio_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_cd_qty}" >
						<f:facet name="header">Medium - CD</f:facet>
						<h:outputText value="#{rae.phy_cd_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_dvd_qty}" >
						<f:facet name="header">Medium - DVD</f:facet>
						<h:outputText value="#{rae.phy_dvd_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_photo_qty}" >
						<f:facet name="header">Medium - Photograph</f:facet>
						<h:outputText value="#{rae.phy_photo_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_book_qty}" >
						<f:facet name="header">Medium - Book</f:facet>
						<h:outputText value="#{rae.phy_book_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_usb_qty}" >
						<f:facet name="header">Medium - USB</f:facet>
						<h:outputText value="#{rae.phy_usb_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_other_qty}" >
						<f:facet name="header">Medium - Other</f:facet>
						<h:outputText value="#{rae.phy_other_qty}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.phy_other_type}" >
						<f:facet name="header">Medium - Other (Desc.)</f:facet>
						<h:outputText value="#{rae.phy_other_type}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.full_count}" >
						<f:facet name="header">File: Full Ver. Submission</f:facet>
						<h:outputText value="#{rae.full_count}" />
					</p:column>

					<p:column width="3em;" sortBy="#{rae.url_full_ver}" >
						<f:facet name="header">URL: Full Ver. Submission (Open)</f:facet>
						<h:outputText value="#{rae.url_full_ver}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.ro_with_doi_ind}" >
						<f:facet name="header">DOI Ind.</f:facet>
						<h:outputText value="#{rae.ro_with_doi_ind}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.doi}" >
						<f:facet name="header">DOI</f:facet>
						<h:outputText value="#{rae.doi}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.toc}" >
						<f:facet name="header">Presence of Abstract or TOC</f:facet>
						<h:outputText value="#{rae.toc}" />
					</p:column>
					<p:column style="min-width:5em;"  sortBy="#{rae.toc_count}" >
						<f:facet name="header">File: Abstract or TOC</f:facet>
						<h:outputText value="#{rae.toc_count}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.url_toc}" >
						<f:facet name="header">URL: Abstract or TOC</f:facet>
						<h:outputText value="#{rae.url_toc}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.toc_oth}" >
						<f:facet name="header">Presence of Abstract or TOC (Other)</f:facet>
						<h:outputText value="#{rae.toc_oth}" />
					</p:column>
					<p:column style="min-width:5em;"  sortBy="#{rae.toc_oth_count}" >
						<f:facet name="header">File: Abstract or TOC (Other)</f:facet>
						<h:outputText value="#{rae.toc_oth_count}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.url_toc_oth}" >
						<f:facet name="header">URL: Abstract or TOC (Other)</f:facet>
						<h:outputText value="#{rae.url_toc_oth}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.additional_info}" >
						<f:facet name="header">Add. Info. on New Insights</f:facet>
						<h:outputText value="#{rae.additional_info}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.keyword_output_1}" >
						<f:facet name="header">Keyword 1</f:facet>
						<h:outputText value="#{rae.keyword_output_1}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.keyword_output_2}" >
						<f:facet name="header">Keyword 2</f:facet>
						<h:outputText value="#{rae.keyword_output_2}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.keyword_output_3}" >
						<f:facet name="header">Keyword 3</f:facet>
						<h:outputText value="#{rae.keyword_output_3}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.keyword_output_4}" >
						<f:facet name="header">Keyword 4</f:facet>
						<h:outputText value="#{rae.keyword_output_4}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.keyword_output_5}" >
						<f:facet name="header">Keyword 5</f:facet>
						<h:outputText value="#{rae.keyword_output_5}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.no_sgl_co_work}" >
						<f:facet name="header">No. of part(s) of Single Coherent Work</f:facet>
						<h:outputText value="#{rae.no_sgl_co_work}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.scw_count}" >
						<f:facet name="header">File: Other Part(s) of Single coherent work</f:facet>
						<h:outputText value="#{rae.scw_count}" />
					</p:column>

					<p:column width="3em;" sortBy="#{rae.url_ref}" >
						<f:facet name="header">URL: Other Part(s) of Single coherent work</f:facet>
						<h:outputText value="#{rae.url_ref}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.uni_endorse_conf_ind}" >
						<f:facet name="header">University Endorse. Form</f:facet>
						<h:outputText value="#{rae.uni_endorse_conf_ind}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.just_sgl_co_work}" >
						<f:facet name="header">Justification and Supporting Argument</f:facet>
						<h:outputText value="#{rae.just_sgl_co_work}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.info_count}" >
						<f:facet name="header">File: University Endorse. Form</f:facet>
						<h:outputText value="#{rae.info_count}" />
					</p:column>

					<p:column width="3em;" sortBy="#{rae.userstamp}" >
						<f:facet name="header">Userstamp</f:facet>
						<h:outputText value="#{rae.userstamp}" />
					</p:column>
					<p:column width="3em;" sortBy="#{rae.timestamp}" >
						<f:facet name="header">Timestamp</f:facet>
						<h:outputText value="#{rae.timestamp}" />
					</p:column>

										
				</p:dataTable>
			</h:panelGroup>
			
			
		</h:panelGroup>
		
	</h:form>
   </ui:define>
</ui:composition>