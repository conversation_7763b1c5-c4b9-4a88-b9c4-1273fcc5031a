<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:o="http://omnifaces.org/ui"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
	</f:metadata>

	<ui:define name="mainContent"> 
	<h:outputScript>
	
		function disableOutputDownloadBtn()
		{
			setTimeout(function() {PF('outputDownloadBtn').disable();}, 50);
		}		
		
		function enableOutputDownloadBtn()
		{
			PF('outputDownloadBtn').enable();
		}

		function disableProjectDownloadBtn()
		{
			setTimeout(function() {PF('projectDownloadBtn').disable();}, 50);
		}		
		
		function enableProjectDownloadBtn()
		{
			PF('projectDownloadBtn').enable();
		}
		
		function disableAwardDownloadBtn()
		{
			setTimeout(function() {PF('awardDownloadBtn').disable();}, 50);
		}		
		
		function enableAwardDownloadBtn()
		{
			PF('awardDownloadBtn').enable();
		}
		
		function disablePatentDownloadBtn()
		{
			setTimeout(function() {PF('patentDownloadBtn').disable();}, 50);
		}		
		
		function enablePatentDownloadBtn()
		{
			PF('patentDownloadBtn').enable();
		}
		
	</h:outputScript>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<h:form style="width:100%; max-width: 97vw;"
			onkeypress="if (event.keyCode == 13) { document.getElementById('dataForm:searchButton').click(); return false; }">
		<component:riSearchPanel searchPanel="#{manageInstituteRIView.searchPanel}"
									isAcadStaff="#{riListingView.getIsAcadStaff() == true}"/>
	</h:form>
	<h:form id="dataForm">
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="searchButton" value="Search" update="resultPanels" icon="pi pi-search"
						 widgetVar="searchBut" onclick="PF('searchBut').disable();PF('searchDialog').show();"
						 oncomplete="PF('searchBut').enable();PF('searchDialog').hide();"
						 actionListener="#{manageInstituteRIView.setFirstSearch(true)}" ></p:commandButton><p:spacer width="10"/>
			<p:dialog widgetVar="searchDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
		        <div>
	            	<h5>Searching</h5>
        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
	            </div>
		    </p:dialog>
	    
		<h:panelGroup id="resultPanels" style="width:100%">
		
			<h:panelGroup id="outputResultPanel"
						  rendered="#{manageInstituteRIView.isFirstSearch() and (manageInstituteRIView.searchPanel.getRiType() == manageInstituteRIView.searchPanel.getRiTypeOutput())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="outputDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="outputDataTable" fileName="outputData" options="#{manageInstituteRIView.excelOpt}" 
                    				postProcessor="#{manageInstituteRIView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="outputDataTable"
					 value="#{manageInstituteRIView.getOutputList()}" 
					 var="output"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					 
					<p:column exportable="false" width="1em;">
		               	<h:link outcome="/user/manageOutput_edit" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="no" value="#{output.outputNo}"/> 
		               		<f:param name="dataLevel" value="#{manageInstituteRIView.searchPanel.getViewType()}"/> 
		               	</h:link>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">RI No.</f:facet>
						<h:outputText value="#{output.outputNo}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff</f:facet>
						<h:outputText value="#{output.name}" />
					</p:column>
					
					<p:column width="3em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{output.staffNumber}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{output.department}" />
					</p:column>
					
					<p:column style="min-width:20em;" >
						<f:facet name="header">Author List</f:facet>
						<h:outputText value="#{output.authorListWithDeptAndAuthType}" escape="false" />
					</p:column>
					
					<p:column style="min-width:35em;">
						<f:facet name="header">Output APA</f:facet>
						<h:outputText value="#{output.apaCitation}" />
					</p:column>
					
					<p:column style="min-width:15em;">
						<f:facet name="header">Research Output Type</f:facet>
						<h:outputText value="#{output.sapOutputType}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">From Date (MM/YYYY)</f:facet>
						<h:outputText value="#{output.fromMonth}/#{output.fromYear}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">To Date (MM/YYYY)</f:facet>
						<h:outputText value="#{output.toMonth}/#{output.toYear}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">CDCF Status</f:facet>
						<h:outputText value="#{output.CDCFStatus}" />
					</p:column>
					
				</p:dataTable>
			</h:panelGroup>
			
			
			<h:panelGroup id="projectResultPanel" 
						  rendered="#{manageInstituteRIView.isFirstSearch() and (manageInstituteRIView.searchPanel.getRiType() == manageInstituteRIView.searchPanel.getRiTypeProject())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="projectDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="projectDataTable" fileName="projectData" options="#{manageInstituteRIView.excelOpt}" 
                    				postProcessor="#{manageInstituteRIView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="projectDataTable"
					 value="#{manageInstituteRIView.getProjectList()}" 
					 var="project"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
					 >
					 
					<p:column exportable="false" width="1em;">
		               	<h:link outcome="/user/manageProject_edit" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="no" value="#{project.projectNo}"/> 
		               		<f:param name="dataLevel" value="#{manageInstituteRIView.searchPanel.getViewType()}"/> 
		               	</h:link>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">RI No.</f:facet>
						<h:outputText value="#{project.projectNo}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff</f:facet>
						<h:outputText value="#{project.staffName}" />
					</p:column>
					
					<p:column width="3em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{project.staffNumber}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{project.department}" />
					</p:column>
					
					<p:column style="min-width:20em;">
						<f:facet name="header">Principal Investigator</f:facet>
						<h:outputText value="#{project.prin_inves_author_list}" escape="false" />
					</p:column>
					
					<p:column style="min-width:20em;">
						<f:facet name="header">Co-Investigator</f:facet>
						<h:outputText value="#{project.co_inves_author_list}" escape="false" />
					</p:column>
					
					<p:column style="min-width:25em;">
						<f:facet name="header">Project Title</f:facet>
						<h:outputText value="#{project.projTitle}" />
					</p:column>
					
					<p:column style="min-width:15em;">
						<f:facet name="header">Funding Source</f:facet>
						<h:outputText value="#{project.sap_funding_source}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">From Date (MM/YYYY)</f:facet>
						<h:outputText value="#{project.fromMonth}/#{project.fromYear}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">To Date (MM/YYYY)</f:facet>
						<h:outputText value="#{project.toMonth}/#{project.toYear}" />
					</p:column>
					
					<p:column width="10em;" style="word-break: break-all;">
						<f:facet name="header">CDCF Status</f:facet>
						<h:outputText value="#{project.CDCFStatus}" />
					</p:column>
					
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="awardResultPanel" 
						  rendered="#{manageInstituteRIView.isFirstSearch() and (manageInstituteRIView.searchPanel.getRiType() == manageInstituteRIView.searchPanel.getRiTypeAward())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="awardDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="awardDataTable" fileName="awardData" options="#{manageInstituteRIView.excelOpt}" 
                    				postProcessor="#{manageInstituteRIView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="awardDataTable"
					 value="#{manageInstituteRIView.getAwardList()}" 
					 var="award"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					<p:column exportable="false" width="1em;">
		               	<h:link outcome="/user/manageAward_edit" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="no" value="#{award.riNo}"/> 
		               		<f:param name="dataLevel" value="#{manageInstituteRIView.searchPanel.getViewType()}"/> 
		               	</h:link>
					</p:column>
					
					
					<p:column width="3em;">
						<f:facet name="header">RI No.</f:facet>
						<h:outputText value="#{award.riNo}" />
					</p:column>
					
					<p:column style="min-width:15em;"
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff</f:facet>
						<h:outputText value="#{award.staffFullname}" />
					</p:column>
					
					<p:column width="3em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{award.staffNumber}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{award.staffDept}" />
					</p:column>
					
					<p:column style="min-width:20em;">
						<f:facet name="header">Recipent</f:facet>
						<h:outputText value="#{award.recipient_list}" escape="false" />
					</p:column>
					
					<p:column style="min-width:15em;">
						<f:facet name="header">Name of Prize/Award</f:facet>
						<h:outputText value="#{award.awardName}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Date of Receipt</f:facet>
						<h:outputText value="#{award.awardDate}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">CDCF Status</f:facet>
						<h:outputText value="#{award.CDCFStatus}" />
					</p:column>
					
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="patentResultPanel" 
						  rendered="#{manageInstituteRIView.isFirstSearch() and (manageInstituteRIView.searchPanel.getRiType() == manageInstituteRIView.searchPanel.getRiTypePatent())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="patentDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="patentDataTable" fileName="patentData" options="#{manageInstituteRIView.excelOpt}" 
                    				postProcessor="#{manageInstituteRIView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="patentDataTable"
					 value="#{manageInstituteRIView.getPatentList()}" 
					 var="patent"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					<p:column exportable="false" width="1em;">
		               	<h:link outcome="/user/managePatent_edit" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="no" value="#{patent.riNo}"/> 
		               		<f:param name="dataLevel" value="#{manageInstituteRIView.searchPanel.getViewType()}"/> 
		               	</h:link>
					</p:column>
					
					
					<p:column width="3em;">
						<f:facet name="header">RI No.</f:facet>
						<h:outputText value="#{patent.riNo}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff</f:facet>
						<h:outputText value="#{patent.staffFullname}" />
					</p:column>
					
					<p:column width="3em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{patent.staffNumber}" />
					</p:column>
					
					<p:column style="min-width:15em;" 
							  rendered="#{manageInstituteRIView.searchPanel.getListingType() == manageInstituteRIView.searchPanel.getListTypeStaffValue()}">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{patent.staffDept}" />
					</p:column>
					
					<p:column style="min-width:20em;" >
						<f:facet name="header">Inventor</f:facet>
						<h:outputText value="#{patent.inventor_list}" escape="false" />
					</p:column>
					
					<p:column style="min-width:15em;" >
						<f:facet name="header">Name of Patent</f:facet>
						<h:outputText value="#{patent.patentName}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Date of Application/Grant</f:facet>
						<h:outputText value="#{patent.patentDate}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">CDCF Status</f:facet>
						<h:outputText value="#{patent.CDCFStatus}" />
					</p:column>
					
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="raeResultPanel" 
						  rendered="#{manageInstituteRIView.isFirstSearch() and (manageInstituteRIView.searchPanel.getRiType() == manageInstituteRIView.searchPanel.getRiTypeRae())}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="raeDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="raeDataTable" fileName="raeData" options="#{manageInstituteRIView.excelOpt}" 
                    				postProcessor="#{manageInstituteRIView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="raeDataTable"
					 value="#{manageInstituteRIView.getRaeOutputList()}" 
					 var="rae"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     allowUnsorting="true" sortMode="single"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					<p:column exportable="false" width="1em;">
		               	<h:link outcome="/user/manageRAEOutput_edit" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="no" value="#{rae.output_no}"/> 
		               	</h:link>
					</p:column>
					
					
					<p:column style="min-width:5em;" sortBy="#{rae.output_no}" >
						<f:facet name="header">RI No.</f:facet>
						<h:outputText value="#{rae.output_no}" />
					</p:column>
					
					<p:column style="min-width:3em;" sortBy="#{rae.faculty}" >
						<f:facet name="header">Faculty</f:facet>
						<h:outputText value="#{rae.faculty}" />
					</p:column>
					
					<p:column style="min-width:3em;"  sortBy="#{rae.department}"  >
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{rae.department}" />
					</p:column>

					<p:column style="min-width:15em;" sortBy="#{rae.uoa}">
						<f:facet name="header">UoA</f:facet>
						<h:outputText value="#{rae.uoa}" />
					</p:column>
					
					<p:column style="min-width:15em;" sortBy="#{rae.staff_name}" >
						<f:facet name="header">Author Name</f:facet>
						<h:outputText value="#{rae.staff_name}" />
					</p:column>
					
					<p:column style="min-width:20em;"  sortBy="#{rae.concatenated_author_name}" >
						<f:facet name="header">Author List</f:facet>
						<h:outputText  escape= "false" value="#{rae.concatenated_author_name}" />
					</p:column>
					
					
					<p:column width="3em;" sortBy="#{rae.apa_citation}" >
						<f:facet name="header">Output APA</f:facet>
						<h:outputText value="#{rae.apa_citation}" />
					</p:column>
					
					
					<p:column style="min-width:15em;" sortBy="#{rae.sap_output_type}"  >
						<f:facet name="header">Output Type (RICH)</f:facet>
						<h:outputText value="#{rae.sap_output_type}" />
					</p:column>
					
					
					<p:column width="3em;" sortBy="#{rae.output_type}" >
						<f:facet name="header">Output Type (RAE)</f:facet>
						<h:outputText value="#{rae.output_type}" />
					</p:column>
					
					
					<p:column width="3em;"  sortBy="#{rae.fromDate}"  >
						<f:facet name="header">From Date</f:facet>
						<h:outputText value="#{rae.fromDate}" />
					</p:column>
					
					<p:column width="3em;" sortBy="#{rae.sel_type}" >
						<f:facet name="header">Selected Type</f:facet>
						<h:outputText value="#{rae.sel_type}" />
					</p:column>
					
					<p:column width="3em;"  sortBy="#{rae.info_comp}" >
						<f:facet name="header">Information Completed</f:facet>
						<h:outputText value="#{rae.info_comp}" />
					</p:column>
										
				</p:dataTable>
			</h:panelGroup>
			
			
		</h:panelGroup>
			
	</h:form>
   </ui:define>
</ui:composition>