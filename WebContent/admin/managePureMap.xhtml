<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
				xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>

	<span class="admin-content-title">Manage Pure Mapping</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:dataTable id="pureTable" var="r" value="#{pureMapView.pureMapList}" editable="true" sortMode="single"
						rowKey="#{r.pure_id}-" tableStyle="table-layout: fixed;"
						selection="#{pureMapView.selectedPureMap}" selectionMode="single"
						paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
	                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
	                     rows="30"
	                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                     	widgetVar="rWidget">
			<p:ajax event="rowEdit" listener="#{pureMapView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{pureMapView.onRowCancel}" update=":msgs"/>

			<p:column headerText="Pure ID" id="pure_id" sortBy="#{r.pure_id}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{r.pure_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{r.pure_id}" style="width:100%" valueChangeListener="#{pureMapView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="PID" id="pid" sortBy="#{r.pid}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{r.pid}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{r.pid}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
        
         <p:column headerText="UUID" id="uuid" sortBy="#{r.pure_uuid}">
             <p:cellEditor>
                 <f:facet name="output">
                     <h:outputText value="#{r.pure_uuid}"/>
                 </f:facet>
                 <f:facet name="input">
                     <p:inputText value="#{r.pure_uuid}" style="width:100%" />
                 </f:facet>
             </p:cellEditor>
         </p:column>
        
        <p:column headerText="Email" id="email" sortBy="#{r.pure_email}">
             <p:cellEditor>
                 <f:facet name="output">
                     <h:outputText value="#{r.pure_email}"/>
                 </f:facet>
                 <f:facet name="input">
                     <p:inputText value="#{r.pure_email}" style="width:100%" />
                 </f:facet>
             </p:cellEditor>
         </p:column>
            
          <p:column headerText="Last Name" id="last_name" sortBy="#{r.pure_last_name}">
             <p:cellEditor>
                 <f:facet name="output">
                     <h:outputText value="#{r.pure_last_name}"/>
                 </f:facet>
                 <f:facet name="input">
                     <p:inputText value="#{r.pure_last_name}" style="width:100%" />
                 </f:facet>
             </p:cellEditor>
         </p:column>
         
         <p:column headerText="First Name" id="first_name" sortBy="#{r.pure_first_name}">
             <p:cellEditor>
                 <f:facet name="output">
                     <h:outputText value="#{r.pure_first_name}"/>
                 </f:facet>
                 <f:facet name="input">
                     <p:inputText value="#{r.pure_first_name}" style="width:100%" />
                 </f:facet>
             </p:cellEditor>
         </p:column>
         
         <p:column headerText="Pure Source ID" id="pure_source_id" sortBy="#{r.pure_source_id}">
             <p:cellEditor>
                 <f:facet name="output">
                     <h:outputText value="#{r.pure_source_id}"/>
                 </f:facet>
                 <f:facet name="input">
                     <p:inputText value="#{r.pure_source_id}" style="width:100%" />
                 </f:facet>
             </p:cellEditor>
         </p:column>
         
         <p:column headerText="Pure Visibility" id="pure_visibility" sortBy="#{r.pure_visibility}">
             <p:cellEditor>
                 <f:facet name="output">
                     <h:outputText value="#{r.pure_visibility}"/>
                 </f:facet>
                 <f:facet name="input">
                     <p:inputText value="#{r.pure_visibility}" style="width:100%" />
                 </f:facet>
             </p:cellEditor>
         </p:column>
         
            <p:column style="width:6rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="pureTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:pureTable :msgs" action="#{pureMapView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:pureTable" listener="#{pureMapView.reloadList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="the selected row"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{pureMapView.deletePureMap}"
								 update=":dataForm:pureTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>