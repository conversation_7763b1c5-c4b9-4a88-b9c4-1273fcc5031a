<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
			
				
				
<ui:define name="html_head">
	<style>
		.ui-datatable .ui-datatable-data > tr .ui-rowgroup-toggler .ui-rowgroup-toggler-icon{
			color: #d91010 !important;
		}
		
		.jqplot-grid-shadowCanvas {
  background:blue;
}
	</style>
</ui:define>
	<ui:define name="mainContent"> 
		<p:panel id="contentPanel">
		
			<span class="admin-content-title"><i class="fa-solid fa-users-viewfinder"></i> Take Snapshot of Outputs and Projects</span>
			<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
			<h:form id="dataForm">
			<h:panelGroup id="buttonGroup">
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
				<br/><br/>
				<p:commandButton id="takeOutputSnapshotBtn" value="Take Outputs Snapshot of current reporting year" widgetVar="takeOutputSnapshotBtnWV"
								styleClass="btn-action"
								escape="True"
								ajax="false"
						  		action="#{takeSnapshotView.takeOutputSnapshot()}" />
						  		
				<p:commandButton id="takeProjectSnapshotBtn" value="Take Projects Snapshot of current reporting year" widgetVar="takeProjectSnapshotBtnWV"
								styleClass="btn-action"
								escape="True"
								ajax="false"
						  		action="#{takeSnapshotView.takeProjectSnapshot()}" />
				
	        </h:panelGroup>
	        <br/><br/>
            <p:dataTable id="logTable"
						 value="#{takeSnapshotView.snapShotLogList}" var="log" 
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 rowIndexVar="rowIndex"
						 rowKey="#{log.log_id}"
						 reflow="true"
                  		 tableStyle="table-layout:auto;">
				<p:column sortBy="#{log.log_id}">
					<f:facet name="header">Log ID</f:facet>
					<h:outputText value="#{log.log_id}"/>
				</p:column>
				<p:column sortBy="#{log.period_id}">
					<f:facet name="header">SnapShot Period</f:facet>
					<h:outputText value="#{takeSnapshotView.getCdcfRptPeriodDesc(log.period_id)}"/>
				</p:column>
				<p:column sortBy="#{log.s_type}">
					<f:facet name="header">SnapShot Type</f:facet>
					<h:outputText value="Outputs" rendered="#{log.s_type eq 'O'}"/>
					<h:outputText value="Projects" rendered="#{log.s_type eq 'P'}"/>
				</p:column>
				<p:column sortBy="#{log.userstamp}">
					<f:facet name="header">Creator</f:facet>
					<h:outputText value="#{log.userstamp}"/>
				</p:column>
				<p:column sortBy="#{log.timestamp}">
					<f:facet name="header">Timestamp</f:facet>
					<h:outputText value="#{log.timestamp}"/>
				</p:column>
				</p:dataTable>
			</h:form>
		</p:panel>
   </ui:define>
</ui:composition>