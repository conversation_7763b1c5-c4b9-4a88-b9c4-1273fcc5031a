<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
	</h:outputScript>
	<span class="admin-content-title">Manage RAEES Report</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:dataTable id="rptTable" var="r" value="#{raeRptView.raeReportList}" editable="true" sortMode="single"
						rowKey="#{r.report_id}-" tableStyle="table-layout: fixed;"
						selection="#{raeRptView.selectedRaeReport}" selectionMode="single"
                     	widgetVar="tableWidget">
			<p:ajax event="rowEdit" listener="#{raeRptView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{raeRptView.onRowCancel}" update=":msgs"/>

			<p:column headerText="Report ID" id="report_id" sortBy="#{r.report_id}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{r.report_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{r.report_id}" style="width:100%" valueChangeListener="#{raeRptView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Report Name" id="report_name" sortBy="#{r.report_name}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{r.report_name}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{r.report_name}" style="width:100%">
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Table Name" id="table_name" sortBy="#{r.table_name}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{r.table_name}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{r.table_name}" style="width:100%">
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="File Name" id="file_name" sortBy="#{r.file_name}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{r.file_name}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{r.file_name}" style="width:100%">
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column style="width:6rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="rptTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:rptTable :msgs" action="#{raeRptView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>        
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:rptTable" listener="#{raeRptView.reloadList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>
	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{raeRptView.selectedRaeReport.report_id}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{raeRptView.deleteRaeReport}"
								 update=":dataForm:rptTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>