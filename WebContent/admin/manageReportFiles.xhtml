<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Manage Report Files</span>
	<p:messages id="message"  autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm" enctype="multipart/form-data">

		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			File Category
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:inputTextarea id="fileType"
							rows = "2" cols = "50"
						 	value="#{manageReportFilesView.uploadedFileCat}"
						 	maxlength = "200" />
		</div>
		
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			File Description
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:inputTextarea id="FileDesc"
							 rows = "3" cols = "100"
						 	 value="#{manageReportFilesView.uploadedFileDesc}"
						 	 counter = "display"
						 	 counterTemplate="{0} characters remaining."
						 	 maxlength = "1000">
			</p:inputTextarea>
			<br/>
			<h:outputText id="display" class="block" />
		</div>

		
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			File upload
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:fileUpload id="uploadedFile" value="#{manageReportFilesView.uploadedFile}" mode="simple" skinSimple=""
								  binding="#{uploadedFileObj}"/>
		</div>
		<br/>
		
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back" />

		<p:commandButton id="uploadbtn" value="Upload" icon="pi pi-upload" ajax="false"
						 update="contentPanel" 
						 action="#{manageReportFilesView.validateFile()}" /><p:spacer width="10"/>
						 
		 <p:dataTable id="reportTable"
					  value="#{manageReportFilesView.reportFileList}" 
					  var="file" rowKey="#{file.fileId}" widgetVar = "table"
					  stripedRows="true" size="small" style="font-size:14px;"
					  reflow="true"
					  sortMode = "single" selection = "{manageReportFilesView.selectedFile}"
					  filteredValue = "#{manageReportFilesView.filteredList}"
                 	  tableStyle="table-layout:auto;"
                 	  paginator="true" rows="30"
                 	  currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
		              paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
		              rowsPerPageTemplate = "30, 50, 100">
		             
		    <p:growl id="confirmMessage" showDetail="true"/>
		
			<p:column width = "23%" sortBy = "#{file.originalName}" filterBy = "#{file.originalName}" filterMode = "match">
				<f:facet name="header">File Name</f:facet>
				<h:outputText value="#{file.originalName}" />
			</p:column>
			
			<p:column width = "7%" sortBy = "#{file.fileType}" filterBy = "#{file.fileType}" filterMode = "match">
				<f:facet name="header">Type</f:facet>
				<h:outputText value="#{file.fileType}" />
			</p:column>
			
			<p:column width = "20%" sortBy = "#{file.fileCat}" filterBy = "#{file.fileCat}" filterMode = "match">
				<f:facet name="header">Category</f:facet>
				<h:outputText value="#{file.fileCat}" />
			</p:column>
			
			<p:column width = "23%" sortBy = "#{file.fileDesc}" filterBy = "#{file.fileDesc}" filterMode = "match">
				<f:facet name="header">Description</f:facet>
				<h:outputText value="#{file.fileDesc}" />
			</p:column>
			
			<p:column width = "7%" sortBy = "#{file.fileSize}" filterBy = "#{file.fileSize}" filterMode = "match">
				<f:facet name="header">Size</f:facet>
				<h:outputText value="#{file.fileSize} KB" />
			</p:column>
			
			<p:column width = "10%" sortBy = "#{file.userstamp}" filterBy = "#{file.userstamp}" filterMode = "match">
				<f:facet name="header">Uploaded By</f:facet>
				<h:outputText value="#{file.userstamp}" />
			</p:column>
			
			<p:column>
				<f:facet width = "10%" name="header">Uploaded Datetime</f:facet>
				<h:outputText value="#{file.timestamp}" ><f:convertDateTime pattern="dd-MM-yyyy" /></h:outputText>
			</p:column>
			
			<p:column style = "margin-left: auto; margin-right: auto;">
				<f:facet width = "5%" name="header">Download</f:facet>
				<p:commandButton action = "#{manageReportFilesView.downloadSelectedFile(file)}" 
              	                 title = "contentPanel"                 					 
              					 icon = "fas fa-download"
              					 ajax = "false"
              					 update = "contentPanel">
              	</p:commandButton>
			</p:column>
			
			<p:column>
				<f:facet width = "5%" name="header">Delete</f:facet>
				<p:commandButton action = "#{manageReportFilesView.deleteSelectedFile(file)}" 
              	                 title = "Delete"                 					 
              					 icon = "fas fa-times"
              					 ajax = "true"
              					 process = "@this"
              					 update = "contentPanel"  oncomplete = "table.filter()">
              		<p:confirm header="Delete the file?" message="The file cannot be recovered once it is deleted." icon="pi pi-info-circle"/>
              	</p:commandButton>
              	
              	<p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true" width="350">
		            <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no ui-button-flat" />
		            <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes" update = "contentPanel"/>           
       			</p:confirmDialog>	
              	
			</p:column>
			
		</p:dataTable>
		
				

	</h:form>

	</p:panel>
    </ui:define>
</ui:composition>