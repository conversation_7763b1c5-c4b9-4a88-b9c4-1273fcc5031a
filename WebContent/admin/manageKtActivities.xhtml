<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:o="http://omnifaces.org/ui"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
	</f:metadata>

	<ui:define name="mainContent"> 
	<h:outputScript>
	
		function disableOutputDownloadBtn()
		{
			setTimeout(function() {PF('outputDownloadBtn').disable();}, 50);
		}		
		
		function enableOutputDownloadBtn()
		{
			PF('outputDownloadBtn').enable();
		}

		function disableProjectDownloadBtn()
		{
			setTimeout(function() {PF('projectDownloadBtn').disable();}, 50);
		}		
		
		function enableProjectDownloadBtn()
		{
			PF('projectDownloadBtn').enable();
		}
		
		function disableAwardDownloadBtn()
		{
			setTimeout(function() {PF('awardDownloadBtn').disable();}, 50);
		}		
		
		function enableAwardDownloadBtn()
		{
			PF('awardDownloadBtn').enable();
		}
		
		function disablePatentDownloadBtn()
		{
			setTimeout(function() {PF('patentDownloadBtn').disable();}, 50);
		}		
		
		function enablePatentDownloadBtn()
		{
			PF('patentDownloadBtn').enable();
		}
		
	</h:outputScript>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<h:form style="width:100%; max-width: 97vw;"
			onkeypress="if (event.keyCode == 13) { document.getElementById('dataForm:searchButton').click(); return false; }">
		<component:ktSearchPanel searchPanel="#{manageKtActivitesView.searchPanel}"/>
	</h:form>
	<h:form id="dataForm">
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="searchButton" value="Search" update="resultPanels" icon="pi pi-search"
						 widgetVar="searchBut" onclick="PF('searchBut').disable();PF('searchDialog').show();"
						 oncomplete="PF('searchBut').enable();PF('searchDialog').hide();"
						 actionListener="#{manageKtActivitesView.setFirstSearch(true)}" ></p:commandButton><p:spacer width="10"/>
			<p:dialog widgetVar="searchDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
		        <div>
	            	<h5>Searching</h5>
        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
	            </div>
		    </p:dialog>
	    
		<h:panelGroup id="resultPanels" style="width:100%">
		
			<h:panelGroup id="outputResultPanel"
						  rendered="#{manageKtActivitesView.isFirstSearch()}" >
				<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
								 widgetVar="outputDownloadBtn"
								 ajax="false">
                    <p:dataExporter type="xlsx" target="outputDataTable" fileName="outputData" options="#{manageKtActivitesView.excelOpt}" 
                    				postProcessor="#{manageKtActivitesView.postPrsc}"/>
                </p:commandButton>
				<p:dataTable id="outputDataTable"
					 value="#{manageKtActivitesView.getKtActivitiesList()}" 
					 var="act"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					 
					<p:column exportable="false" width="1em;">
		               	<h:link outcome="/user/ktForm" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="no" value="#{act.form_no}"/> 
		               		<f:param name="dataLevel" value="#{manageKtActivitesView.searchPanel.getViewType()}"/> 
		               		<f:param name="form" value="#{manageKtActivitesView.searchPanel.getKtType()}"/>
		               	</h:link>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">KT Activity No.</f:facet>
						<h:outputText value="#{act.form_no}" />
					</p:column>
					
					<p:column style="min-width:20em;" >
						<f:facet name="header">Author List</f:facet>
						<h:outputText value="#{act.authorList}" escape="false" />
					</p:column>
					
					<p:column style="min-width:8em;">
						<f:facet name="header">Faculty</f:facet>
						<h:outputText value="#{act.fac}" />
					</p:column>
					
					<p:column style="min-width:8em;">
						<f:facet name="header">Department</f:facet>
						<h:outputText value="#{act.dept}" />
					</p:column>
					
					<p:column style="min-width:15em;">
						<f:facet name="header">Title</f:facet>
						<h:outputText value="#{act.title}" />
					</p:column>
					
					<p:column style="min-width:8em;">
						<f:facet name="header">Activity Code</f:facet>
						<h:outputText value="#{act.act_code}" />
					</p:column>
					
					<p:column width="8em;">
						<f:facet name="header">Start Date</f:facet>
						<h:outputText value="#{act.start_date}" />
					</p:column>
					
					<p:column width="8em;">
						<f:facet name="header">End Date</f:facet>
						<h:outputText value="#{act.end_date}" />
					</p:column>
					
				</p:dataTable>
			</h:panelGroup>
			
		</h:panelGroup>
			
	</h:form>
   </ui:define>
</ui:composition>