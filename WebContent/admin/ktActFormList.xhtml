<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
				
	
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
			
		<h:form id="dataForm">
			<span class="admin-content-title"><i class="fa-solid fa-list-ul"></i> KT Activity Form List</span>
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			</p:messages>

			<div class="ui-g">
				<h:panelGroup id="dataPanel" style="width:99%">
					<br/>
					<p:dataTable id="dataTable" 
									 var="p"
									 value="#{manageKtFormView.formList}"
									 rowKey="#{p.form_code}"
									 reflow="true"
									 tableStyle="table-layout:auto;"
			                  		 >
						<p:column style="width:2rem">
			                <p:rowToggler/>
			            </p:column>
						<p:column headerText="Form Code" sortBy="#{p.form_code}" width="10%">
							<h:outputText value="#{p.form_code}" style="word-break: break-all;"/>
						</p:column>
						
		                <p:column headerText="Form Short Desc." sortBy="#{p.form_short_desc}" width="8%">
							<h:outputText value="#{p.form_short_desc}" style="word-break: break-all;"/>
						</p:column>
						
						<p:column headerText="Form Full Desc." sortBy="#{p.form_full_desc}" >
							<h:outputText value="#{p.form_full_desc}" style="word-break: break-all;"/>
						</p:column>
						
						<p:column headerText="Form Brief" sortBy="#{p.form_brief}" width="22%">
							<h:outputText value="#{p.form_brief}" style="word-break: break-all;"/>
						</p:column>
						
						<p:column headerText="Display Order" sortBy="#{p.display_order}" width="8%">
							<h:outputText value="#{p.display_order}" style="word-break: break-all;"/>
						</p:column>

						<p:column headerText="Enable" sortBy="#{p.is_enabled}" width="8%">
							<i class="fa-regular fa-circle-check fa-xl" style="#{p.is_enabled?'color:#0F9D58;':'display:none;'}"></i>
							<i class="fa-regular fa-circle-xmark fa-xl" style="#{p.is_enabled eq false?'color:#DB4437;':'display:none;'}"></i>
						</p:column>
						
						<p:column width="6em">
							<p:commandButton  action="#{manageKtFormView.gotoFormEditPage(p.form_code)}" class="ui-button-success rounded-button" icon="fa-regular fa-pen-to-square" style="margin-right:4px;"></p:commandButton>	
							<!--  <p:commandButton  action="#{manageKtFormView.deleteForm(p.form_code)}" class="ui-button-warning rounded-button" icon="fa-regular fa-trash-can"
									  				 update=":dataForm:dataTable" immediate="true">
									   <p:confirm header="Confirmation"
													message="Are you sure you want to delete this form?"
													icon="pi pi-info-circle" />
							</p:commandButton>	-->
						</p:column>
						
						<p:rowExpansion>
							<div class="ui-g">
								<div class="ui-g-12 ui-md-12 ui-lg-12">			
									<p:outputLabel class="riForm-item-title" value="Note 1: " />
									<p:outputLabel value="#{p.note_1}" escape="false"/>
								</div>
								<div class="ui-g-12 ui-md-12 ui-lg-12">			
									<p:outputLabel class="riForm-item-title" value="Note 2: " />
									<p:outputLabel value="#{p.note_2}" escape="false"/>
								</div>
							</div>
			            </p:rowExpansion>
            
		            </p:dataTable>

	        		<br/>
		            
	            </h:panelGroup>
			</div>
			
			<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Submit" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
			</p:confirmDialog>
				
		</h:form>
		
		<p:button value="#{bundle['action.back']}" outcome="/user/dashboard" icon="pi pi-arrow-left" styleClass="btn-back"/>
	</p:panel>
			
	</ui:define>
</ui:composition>