<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="userId" value="#{dataAccessView.selectedUserId}" />
	</f:metadata>
				
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
	
		<h:form id="accessForm">
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
				<p:autoUpdate/>
			</p:messages>
			<div class="card">
				<span class="admin-content-title">Set Data Access</span>
				<p:tabView id="accessFormTab">
            		<p:tab title="Manage data access">
			
						<p:focus context="accessForm"/>
							
						<!-- User ID selection -->
						<h:panelGrid columns="2">
			
							<h:panelGroup>
								User ID
								<h:outputText value="&#160;"/>
							</h:panelGroup>
							
							<h:panelGroup>
								<p:selectOneMenu id="userId" value="#{dataAccessView.selectedUserId}"
												 filter="true" filterMatchMode="contains" caseSensitive="false"
												 editable="true">
									<f:selectItem itemLabel="" itemValue=""/>
									<f:selectItems value="#{dataAccessView.userIdList}" var="userId" 
													 itemLabel="#{userId}" itemValue="#{userId}"/>
									<f:validator validatorId="hk.eduhk.rich.access.LDAPUserValidator"/>
									<f:attribute name="validateField" value="userId" />
									<f:ajax event="change" execute="@this" render="listPanel funcListPanel caListPanel userIdMsg ktdListPanel rptListPanel uoaPanel "/>
								</p:selectOneMenu> 
								<p:message id="userIdMsg" for="userId"/>
							</h:panelGroup>
						
						</h:panelGrid>
						<br/>
						<p:tabView id="listPanel">
						<p:tab title="Content Area">
						<h:panelGroup id="caListPanel">
								
							<!-- Button Panel -->
							<h:panelGrid id="caButtonPanel" rendered="#{not empty dataAccessView.selectedUserId}">
								<br/>
								<h:panelGroup styleClass="button-panel">
									<p:defaultCommand target="caDummy"/>
									<p:commandButton id="caDummy" process="@none" global="false" style="display:none;"/>
									<p:commandButton value="Apply Changes" action="#{dataAccessView.updateUserCas}" update=":accessForm:accessFormTab:userId caGrid" style="margin-right:5px;"/>
									<p:commandButton value="Select All" action="#{dataAccessView.selectAllCa()}" update="caGrid" style="margin-right:5px;"/>
									<p:commandButton value="Unselect All" action="#{dataAccessView.unselectAllCa()}" update="caGrid" style="margin-right:5px;"/>
								</h:panelGroup>
							</h:panelGrid>
								
							<!-- Content Area List -->			
							<h:panelGroup id="caGrid" rendered="#{not empty dataAccessView.selectedUserId}">
								<p:selectManyCheckbox id="caUserGroup"
													  value="#{dataAccessView.selectedCas}"
													  converter="hk.eduhk.rich.access.CaConverter" 
													  layout="grid" columns="2">
									<f:selectItems value="#{dataAccessView.caList}" 
												   var="ca" itemLabel="#{ca.area_code} (#{ca.description})" itemValue="#{ca}" />
								</p:selectManyCheckbox>
							</h:panelGroup>
				
						</h:panelGroup>
						</p:tab>
						<p:tab title="Research Information Unit">	
						<h:panelGroup id="funcListPanel">
								
							<!-- Button Panel -->
							<h:panelGrid id="buttonPanel" rendered="#{not empty dataAccessView.selectedUserId}">
								<br/>
								<h:panelGroup styleClass="button-panel">
									<p:defaultCommand target="dummy"/>
									<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
									<p:commandButton value="Apply Changes" action="#{dataAccessView.updateUserDepts}" update=":accessForm:accessFormTab:userId funcGrid" style="margin-right:5px;"/>
									<p:commandButton value="Select All" action="#{dataAccessView.selectAll()}" update="funcGrid" style="margin-right:5px;"/>
									<p:commandButton value="Unselect All" action="#{dataAccessView.unselectAll()}" update="funcGrid" style="margin-right:5px;"/>
								</h:panelGroup>
							</h:panelGrid>
								
							<!-- Function List -->			
							<h:panelGroup id="funcGrid" rendered="#{not empty dataAccessView.selectedUserId}">
								<p:selectManyCheckbox id="userGroup"
													  value="#{dataAccessView.selectedDepts}"
													  converter="hk.eduhk.rich.access.DeptConverter" 
													  layout="grid" columns="2">
									<f:selectItems value="#{dataAccessView.deptList}" 
												   var="dept" itemLabel="#{dept.department_name} (#{dept.department_code})" itemValue="#{dept}" />
								</p:selectManyCheckbox>
							</h:panelGroup>
				
						</h:panelGroup>
						</p:tab>
						<p:tab title="KT Activities Unit">
						<h:panelGroup id="ktdListPanel">
								
							<!-- Button Panel -->
							<h:panelGrid id="ktdButtonPanel" rendered="#{not empty dataAccessView.selectedUserId}">
								<br/>
								<h:panelGroup styleClass="button-panel">
									<p:defaultCommand target="ktdDummy"/>
									<p:commandButton id="ktdDummy" process="@none" global="false" style="display:none;"/>
									<p:commandButton value="Apply Changes" action="#{dataAccessView.updateUserKtds}" update=":accessForm:accessFormTab:userId ktdGrid" style="margin-right:5px;"/>
									<p:commandButton value="Select All" action="#{dataAccessView.selectAllKtd()}" update="ktdGrid" style="margin-right:5px;"/>
									<p:commandButton value="Unselect All" action="#{dataAccessView.unselectAllKtd()}" update="ktdGrid" style="margin-right:5px;"/>
								</h:panelGroup>
							</h:panelGrid>
								
							<!-- KT Department List -->			
							<h:panelGroup id="ktdGrid" rendered="#{not empty dataAccessView.selectedUserId}">
								<p:selectManyCheckbox id="ktdUserGroup"
													  value="#{dataAccessView.selectedKtds}"
													  layout="grid" columns="2">
									<f:selectItems value="#{dataAccessView.ktdList}" 
												   var="ktd" itemLabel="#{ktd.description} (#{ktd.pk.lookup_code})" itemValue="#{ktd.pk.lookup_code}" />
								</p:selectManyCheckbox>
							</h:panelGroup>
				
						</h:panelGroup>
						</p:tab>
						<p:tab title="Report Unit">
						<h:panelGroup id="rptListPanel">
								
							<!-- Button Panel -->
							<h:panelGrid id="rptButtonPanel" rendered="#{not empty dataAccessView.selectedUserId}">
								<br/>
								<h:panelGroup styleClass="button-panel">
									<p:defaultCommand target="rptDummy"/>
									<p:commandButton id="rptDummy" process="@none" global="false" style="display:none;"/>
									<p:commandButton value="Apply Changes" action="#{dataAccessView.updateUserRpts}" update=":accessForm:accessFormTab:userId rptGrid" style="margin-right:5px;"/>
									<p:commandButton value="Select All" action="#{dataAccessView.selectAllRpt()}" update="rptGrid" style="margin-right:5px;"/>
									<p:commandButton value="Unselect All" action="#{dataAccessView.unselectAllRpt()}" update="rptGrid" style="margin-right:5px;"/>
								</h:panelGroup>
							</h:panelGrid>
								
							<!--RPT Department List -->			
							<h:panelGroup id="rptGrid" rendered="#{not empty dataAccessView.selectedUserId}">
								<p:selectManyCheckbox id="rptUserGroup"
													  value="#{dataAccessView.selectedRpts}"
													  layout="grid" columns="2">
									<f:selectItems value="#{dataAccessView.rptList}" 
												   var="rpt" itemLabel="#{rpt.description} (#{rpt.pk.lookup_code})" itemValue="#{rpt.pk.lookup_code}" />
								</p:selectManyCheckbox>
							</h:panelGroup>
				
						</h:panelGroup>
						</p:tab>
						
						<p:tab title="UoA">
						<h:panelGroup id="uoaPanel">
								
							<!-- Button Panel -->
							<h:panelGrid id="uoaButtonPanel" rendered="#{not empty dataAccessView.selectedUserId}">
								<br/>
								<h:panelGroup styleClass="button-panel">
									<p:defaultCommand target="uoaDummy"/>
									<p:commandButton id="uoaDummy" process="@none" global="false" style="display:none;"/>
									<p:commandButton value="Apply Changes" action="#{dataAccessView.updateUserUoa }" update=":accessForm:accessFormTab:userId uoaGrid" style="margin-right:5px;"/>
									<p:commandButton value="Select All" action="#{dataAccessView.selectAllUoa()}" update="uoaGrid" style="margin-right:5px;"/>
									<p:commandButton value="Unselect All" action="#{dataAccessView.unselectAllUoa()}" update="uoaGrid" style="margin-right:5px;"/>
								</h:panelGroup>
							</h:panelGrid>
								
							<!--UOA Department List -->			
							<h:panelGroup id="uoaGrid" rendered="#{not empty dataAccessView.selectedUserId}">
								<p:selectManyCheckbox id="uoaUserGroup"
													  value="#{dataAccessView.selectedUoa}"
													  layout="grid" columns="2">
									<f:selectItems value="#{dataAccessView.uoaList}" 
												   var="uoa" itemLabel="#{uoa.uoaCode} #{uoa.uoaDesc}" itemValue="#{uoa.uoaCode}" />
								</p:selectManyCheckbox>
							</h:panelGroup>
				
						</h:panelGroup>
						</p:tab>
						
						
						
						
						</p:tabView>
					</p:tab>
            		<p:tab title="View record">
            			<p:commandButton id="btn_download" value="Export" action="#{dataAccessView.download}"
												  icon="pi pi-download"
												  ajax="false">
						</p:commandButton>
            			<p:dataTable id="columnTable" class="riFormTable"
											value="#{dataAccessView.secDataUserList}" var="col"
											rows="20" reflow="true"
											rowsPerPageTemplate="10,20,50" paginator="true"
											paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
											currentPageReportTemplate="Total: {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
											style="max-width:99%;" rowIndexVar="rowIndex">
						
											<p:column style="text-align:left;" sortBy="#{col.pk.user_id}" filterBy="#{col.pk.user_id}">
												<f:facet name="header">User ID</f:facet>
												#{col.pk.user_id}
											</p:column>
											<p:column style="text-align:left;" sortBy="#{col.staff.fullname}" filterBy="#{col.staff.fullname}">
												<f:facet name="header">User Name</f:facet>
												#{col.staff.fullname}
											</p:column>
											<p:column style="text-align:left;" sortBy="#{col.staff.dept_code}" filterBy="#{col.staff.dept_code}">
												<f:facet name="header">User Dept.</f:facet>
												#{col.staff.dept_code}
											</p:column>
											<p:column style="text-align:left;" sortBy="#{col.pk.data_code}" filterBy="#{col.pk.data_code}">
												<f:facet name="header">Access Dept.</f:facet>
												#{col.pk.data_code eq 'ALL_DATA'?'All':col.pk.data_code}
											</p:column>
											<p:column style="text-align:left;" sortBy="#{col.dept.department_name}" filterBy="#{col.dept.department_name}">
												<f:facet name="header">Access Dept. Name</f:facet>
												#{col.dept.department_name}
											</p:column>
						</p:dataTable>
            		</p:tab>
           		</p:tabView>
           		<br/>
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
            </div>
		</h:form>
		
		<br/>
	
	</p:panel>
	</ui:define>
</ui:composition>