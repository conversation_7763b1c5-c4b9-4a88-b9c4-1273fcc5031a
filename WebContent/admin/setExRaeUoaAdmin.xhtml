<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets">


		<h:form id="exclusiveUseUoaForm">

		<p:panelGrid id="exclusiveUsePanel" styleClass="edit-panel">
			<p:row>
				<p:column>
					<h:panelGroup styleClass="form-title">
						Current Status
					</h:panelGroup>
				</p:column>
				<p:column>
					#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.lock_status == 'Y'? 'Exclusive':'Not Exclusive'}
			</p:column>
			</p:row>
			<p:row rendered="#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.lock_status == 'Y'}">
				<p:column>
					<h:panelGroup styleClass="form-title">
						Suspended by
					</h:panelGroup>
				</p:column>
				<p:column>
					#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.creator}
			</p:column>
			</p:row>
			<p:row rendered="#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.lock_status == 'Y'}">
				<p:column>
					<h:panelGroup styleClass="form-title">
						Suspended Date
					</h:panelGroup>
				</p:column>
				<p:column>
					<h:outputText value="#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.timestamp}">
						<f:convertDateTime pattern="dd-MM-yyyy" />
					</h:outputText>
			</p:column>
			</p:row>	
			<p:row>
				<p:column>
					<h:panelGroup styleClass="form-title">
						Status
					</h:panelGroup>
				</p:column>
				<p:column>
					<p:selectOneRadio id="console" value="#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.lock_status}" unselectable="true">
			            <f:selectItem itemLabel="Exclusive" itemValue="Y"/>
			            <f:selectItem itemLabel="Not Exclusive" itemValue="N"/>
			        </p:selectOneRadio>
			</p:column>
			</p:row>	
			<p:row>
				<p:column>
					<h:panelGroup styleClass="form-title">
						Message
					</h:panelGroup>
				</p:column>
				<p:column>
					<p:inputTextarea rows="7" cols="70" counter="display" maxlength="1000" value="#{secFuncLockView.selectedSecFuncLockRaeUoaAdmin.lock_msg}"
                        					 counterTemplate="{0} characters remaining." autoResize="false"/>
                      <br/>
      				  <h:outputText id="display" class="p-d-block" />
			</p:column>
			</p:row>	
		</p:panelGrid>
		<br/>
		<h:panelGroup styleClass="button-panel">
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	

			<p:commandButton value="#{bundle['action.clear']}" update="@form" action="#{secFuncLockView.clearRaeForm('UOAADMIN')}" style="margin-right:5px;">
				<p:confirm header="Confirm Box" icon="pi pi-exclamation-triangle"
								   message="Are you sure to clear the data?">
				</p:confirm>	
			</p:commandButton>
			<p:commandButton value="#{bundle['action.save']}"
							  		 action="#{secFuncLockView.updateRaeSecFuncLock('UOAADMIN')}"
							  		 update="@form">
			</p:commandButton>
			<!-- Confirm Reset Dialog -->
			<p:confirmDialog id="resetDialog" widgetVar="resetDialogObj" 
							 global="true" showEffect="fade" hideEffect="fade"
							 severity="alert" closable="false" visible="false">
		       	<p:commandButton value="#{bundle['action.yes']}" type="button" styleClass="ui-confirmdialog-yes"  />
		       	<p:commandButton value="#{bundle['action.no']}" type="button" styleClass="ui-confirmdialog-no"  />
		   	</p:confirmDialog> 	
				
		</h:panelGroup>
				
	</h:form>
</ui:composition>
   