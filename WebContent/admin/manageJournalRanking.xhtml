<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<ui:define name="html_head">
		<style>
			.ui-fileupload-filename {
			    display: inline-block !important;
			}
			a.button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;

    text-decoration: none;
    color: initial;
}
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	
	<span class="admin-content-title"><i class="fas fa-star"></i> Manage Journal Ranking</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm" enctype="multipart/form-data">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:toolbar>
			<p:toolbarGroup align="left">
				<p:fileUpload id="uploadedFile" value="#{manageJournalRankingView.uploadedFile}" label="Choose an Excel file" mode="simple" skinSimple="true" style="margin-right:4px;"
							binding="#{uploadedFileObj}"/>
				<p:commandButton id="uploadButton" value="Upload" icon="pi pi-upload" ajax="false" 
						 update="@form"
						 action="#{manageJournalRankingView.upload()}" ></p:commandButton>		
            </p:toolbarGroup>
            
            <p:toolbarGroup align= "right">
            	<a href="../web/jnl_ranking_template.xlsx" download="jnl_ranking_template.xlsx" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon-left" 
					role="button" style="margin-right:4px;">
	 			<span class="ui-button-icon-left ui-icon ui-c pi pi-download"></span>
	 			<span class="ui-button-text ui-c">Download Template</span>
	 			</a>
               <p:commandButton id="exportTable" action="#{manageJournalRankingView.export()}" icon="pi pi-download" value="Export to Excel" styleClass="mr-2 mb-2" ajax="false" />
            </p:toolbarGroup>
            
        </p:toolbar>
		<p:dataTable id="journalRankTable" var="journal" value="#{manageJournalRankingView.journalRankList}" sortMode="single" 
						rowKey="#{journal.title} #{journal.issn_1}" tableStyle="table-layout: fixed; font-size:12px; word-wrap: break-word; "
						styleClass="default-dataTable" exportable = "true" filteredValue = "#{manageJournalRankingView.filteredJournalRank}"
						rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						rowIndexVar="rowIndex"
                     	paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						paginator="true"
						rows="30"
						currentPageReportTemplate="{startRecord}-{endRecord} of {totalRecords} records">

			<p:column headerText="Research Journal Title" id="title" style = "width: 40%;"
					  sortBy="#{journal.title}" filterBy="#{journal.title}" filterMatchMode="contains">
                <h:outputText value="#{journal.title}"/>
            </p:column>
            
            <p:column headerText="ISSN_1" id="issn_1" style = "width: 10%;"
					  sortBy="#{journal.issn_1}" filterBy="#{journal.issn_1}" filterMatchMode="contains">
                <h:outputText value="#{journal.issn_1}"/>
            </p:column>
            
            <p:column headerText="ISSN_2" id="issn_2" style = "width: 10%;"
					  sortBy="#{journal.issn_2}" filterBy="#{journal.issn_2}" filterMatchMode="contains">
                <h:outputText value="#{journal.issn_2}"/>
            </p:column>
            
            <p:column headerText="ISSN_3" id="issn_3" style = "width: 10%;"
					  sortBy="#{journal.issn_3}" filterBy="#{journal.issn_3}" filterMatchMode="contains">
                <h:outputText value="#{journal.issn_3}"/>
            </p:column>
            
            <p:column headerText="Rank" id="rank" style = "width: 5%;"
					  sortBy="#{journal.rank}" filterBy="#{journal.rank}" filterMatchMode="contains">
                <h:outputText value="#{journal.rank}"/>
            </p:column>
            
            <p:column headerText="JCR Best Quartile" id="jcr" style = "width: 10%;"
					  sortBy="#{journal.jcr}" filterBy="#{journal.jcr}" filterMatchMode="contains">
                <h:outputText value="#{journal.jcr}"/>
            </p:column>
            
            <p:column headerText="SJR Best Quartile" id="sjr" style = "width: 10%;"
					  sortBy="#{journal.sjr}" filterBy="#{journal.sjr}" filterMatchMode="contains">
                <h:outputText value="#{journal.sjr}"/>
            </p:column>
            
            <p:column headerText="Year" id="year" style = "width: 5%;"
					  sortBy="#{journal.year}" filterBy="#{journal.year}" filterMatchMode="contains">
                <h:outputText value="#{journal.year}"/>
            </p:column>
            
        </p:dataTable>

	</h:form>

	</p:panel>
   </ui:define>
</ui:composition>