<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="termCode" value="#{createBatchScheduleView.selectedTermCode}" /> 
		<f:viewParam name="groupCode" value="#{createBatchScheduleView.selectedGroupCode}" /> 
		<f:viewParam name="courseCode" value="#{createBatchScheduleView.selectedCourseCode}" /> 
		<f:viewParam name="dept" value="#{createBatchScheduleView.selectedDepartment}" /> 
		<f:viewParam name="subOrCrse" value="#{createBatchScheduleView.selectedSubOrCrse}" />
	</f:metadata> 

	<ui:define name="mainContent">
	
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	
	
	<h:outputScript target="head">
	
		function confirmOptOut(xhr, status, args)
		{
			if (args != null &amp;&amp; args.confirmRequired)
			{
				PF('delConfirmWidget').show();
			}
		}
	
	</h:outputScript>
    
    <p:ajaxStatus onstart="PF('statusDialog').show()" onsuccess="PF('statusDialog').hide()" />
             
	<p:dialog widgetVar="statusDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
	    <p:graphicImage name="/image/loading.gif" /> 
	    <h:outputText value="Please wait. Form batch is being created..." />
	</p:dialog>
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">Create Form Batch - Schedule List</h:panelGroup><br/>
		
		<!--  <p:growl id="growl" showDetail="false" sticky="false" />-->
		
		 
		<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			<p:autoUpdate/>
		</p:messages>
		 
		
		<h:form id="dataForm">
		
			<div class="ui-g course-info-panel">

				<!-- Department -->
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Semester
				</div>
				
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					#{createBatchScheduleView.selectedTerm.description}
				</div>

				<!-- Course -->
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Course 
				</div>
				
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					#{createBatchScheduleView.selectedCourse.courseCode} - #{createBatchScheduleView.selectedCourse.title}
				</div>

				<!-- Cross List Code -->
           		<h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 caption"
           					  rendered="#{createBatchScheduleView.selectedClassGroup.crossListed}"> 
					Cross List Code
				</h:panelGroup>
				
            	<h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 content"
           					  rendered="#{createBatchScheduleView.selectedClassGroup.crossListed}"> 
					#{createBatchScheduleView.selectedClassGroup.groupCode}
				</h:panelGroup>

				<!-- No. of Section -->
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					No. of Section 
				</div>
				
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					#{createBatchScheduleView.selectedClassGroup.crnCount}
				</div>

				<!-- No. of Enrollment -->
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					No. of Enrollment 
				</div>
				
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					#{createBatchScheduleView.selectedClassGroup.groupEnroll}
				</div>
				
			</div>
			
			<br/> 
		
			<h:panelGroup id="schdList" layout="block" class="ui-g">
			<p:repeat id="schdIter"
					  var="schd" value="#{createBatchScheduleView.classGroupScheduleList}" 
					  varStatus="varStatus">

                <div class="ui-g-12 ui-md-6 ui-lg-4" style="padding-bottom:0.3em;">
                <p:panel id="schdPanel" header="Schedule Code: #{schd.scheduleTypeCode}"
                		 styleClass="course-block #{createBatchScheduleView.isBatchCreated ? 'processed' : ''}"
                		 style="background-color:lightyellow;">
                
                	<div class="ui-g">
                		
                		<!--  
                		<div class="ui-g-4 ui-md-4 ui-lg-5" style="font-weight:bold;">
                			Schedule Code
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-7">
                			#{schd.scheduleTypeCode} 
                		</div>
                		-->

                		<div class="ui-g-4 ui-md-4 ui-lg-4 caption">
                			Section
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-8 content">
                			#{schd.sectionDesc} 
                		</div>

                		<div class="ui-g-4 ui-md-4 ui-lg-4 caption">
                			CRN
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-8 content">
                			#{schd.crnDesc}
                		</div>

                		<div class="ui-g-4 ui-md-4 ui-lg-4 caption">
                			No. of Section
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-8 content">
                			#{schd.crnCount}
                		</div>

                		<div class="ui-g-4 ui-md-4 ui-lg-4 caption">
                			No. of Enrollment
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-8 content">
                			<p:link outcome="banStudentList" target="_blank">
                				#{schd.groupEnroll}
                				<f:param name="termCode" value="#{schd.termCode}"/>
                				<f:param name="courseCRNs" value="#{schd.crnDesc}"/>
                				<f:param name="info" value="#{createBatchScheduleView.getSchdInfoJSON(schd)}"/>
                			</p:link>
                		</div>
 
                		<div class="ui-g-4 ui-md-4 ui-lg-4 caption">
                			Teacher
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-8 content">
                			<h:panelGroup rendered="#{!empty schd.instructorPersonSet}">
                				<table>
	                			<p:repeat var="person" value="#{schd.instructorPersonSet}">
	                				<tr>
	                					<td style="white-space:nowrap;">
	                					
	                					 	<!-- If batch not created, let user choose which instructor to create -->
	                						<h:panelGroup rendered="#{person != null and person.userId != null and 
	                						createBatchScheduleView.isBatchCreated == false}">
		                						<p:commandLink actionListener="#{createBatchScheduleView.setInstructorCreateOrNot(true, schd, person)}"
		                									   update=":dataForm:schdIter:#{varStatus.index}:schdPanel"
		                									   styleClass="icon-checkbox"
		                									   ajax="false"
		                									   rendered="#{createBatchScheduleView.getInstructorCreateOrNot(schd, person) == null or createBatchScheduleView.getInstructorCreateOrNot(schd, person).equals('0')}">
		                							<span class="fa-stack">
		                								<i class="far fa-circle fa-stack-1x" style="color:lightgrey;"/>
		                								<i class="fas fa-check fa-stack-1x" style="color:lightgrey;"/>
		                							</span>
		                						</p:commandLink>
	
		                						<h:panelGroup styleClass="icon-checkbox"
		                									  rendered="#{createBatchScheduleView.getInstructorCreateOrNot(schd, person).equals('1')}">
		                							<span class="fa-stack">
		                								<i class="fas fa-circle fa-stack-1x" style="color:darkgreen;"/>
		                								<i class="fas fa-check fa-stack-1x" style="color:white;"/>
		                							</span>
		                						</h:panelGroup>
		                						
		                						<p:commandLink actionListener="#{createBatchScheduleView.setInstructorCreateOrNot(false, schd, person)}"
		                									   update=":dataForm:schdIter:#{varStatus.index}:schdPanel"
		                									   styleClass="icon-checkbox"
		                									   ajax="false"
		                									   rendered="#{createBatchScheduleView.getInstructorCreateOrNot(schd, person) == null or createBatchScheduleView.getInstructorCreateOrNot(schd, person).equals('1')}">
		                							<span class="fa-stack">
		                								<i class="far fa-circle fa-stack-1x" style="color:lightgrey;"/>
		                								<i class="fas fa-times fa-stack-1x" style="color:lightgrey;"/>
		                							</span>
		                						</p:commandLink>
		                						
		                						<h:panelGroup styleClass="icon-checkbox"
		                									  rendered="#{createBatchScheduleView.getInstructorCreateOrNot(schd, person).equals('0')}">
		                							<span class="fa-stack">
		                								<i class="fas fa-circle fa-stack-1x" style="color:red;"/>
		                								<i class="fas fa-times fa-stack-1x" style="color:white;"/>
		                							</span>
		                						</h:panelGroup>
	                						</h:panelGroup>
	                						
	                						<!-- If batch already created, show which instructor is created -->
	                						<h:panelGroup rendered="#{person != null and person.userId != null and 
	                						createBatchScheduleView.isBatchCreated == true}">
		                						<h:panelGroup class="icon-checkbox" 
		                									  rendered="#{createBatchScheduleView.checkInstructorCreated(schd.scheduleTypeCode ,person) == false}" >
		                							<span class="fa-stack">
		                								<i class="far fa-circle fa-stack-1x" style="color:lightgrey;"/>
		                								<i class="fas fa-check fa-stack-1x" style="color:lightgrey;"/>
		                							</span>
		                						</h:panelGroup>
	
		                						<h:panelGroup styleClass="icon-checkbox"
		                									  rendered="#{createBatchScheduleView.checkInstructorCreated(schd.scheduleTypeCode ,person) == true}">
		                							<span class="fa-stack">
		                								<i class="fas fa-circle fa-stack-1x" style="color:darkgreen;"/>
		                								<i class="fas fa-check fa-stack-1x" style="color:white;"/>
		                							</span>
		                						</h:panelGroup>
		                						
		                						<h:panelGroup class="icon-checkbox" 
		                									  rendered="#{createBatchScheduleView.checkInstructorCreated(schd.scheduleTypeCode ,person) == true}" >
		                							<span class="fa-stack">
		                								<i class="far fa-circle fa-stack-1x" style="color:lightgrey;"/>
		                								<i class="fas fa-times fa-stack-1x" style="color:lightgrey;"/>
		                							</span>
		                						</h:panelGroup>
		                						
		                						<h:panelGroup styleClass="icon-checkbox"
		                									  rendered="#{createBatchScheduleView.checkInstructorCreated(schd.scheduleTypeCode ,person) == false}">
		                							<span class="fa-stack">
		                								<i class="fas fa-circle fa-stack-1x" style="color:red;"/>
		                								<i class="fas fa-times fa-stack-1x" style="color:white;"/>
		                							</span>
		                						</h:panelGroup>
	                						</h:panelGroup>
	                						
	                					</td>
	                					<td>
	                						<h:panelGroup rendered="#{person != null and person.userId != null}">
	                							#{person.name} (#{person.instructorDepartment})
	                						</h:panelGroup>
	                					</td>
	                				</tr>
	                			</p:repeat>
	                			</table>
                			</h:panelGroup>
                		</div>

                		<div class="ui-g-4 ui-md-4 ui-lg-4 caption">
                			Time &amp; Venue
                		</div>
                		<div class="ui-g-8 ui-md-8 ui-lg-8 content">
                		
                			<div style="font-size:1.3em;">
	                			<h:link outcome="banGroupMeeting"
			                			styleClass="icon-checkbox"
	                					disabled="#{empty schd.meetingSet}"
	                					target="_blank">

	       							<span class="fa-stack">
	       								<i class="fas fa-map-marker fa-stack-1x" style="color:#{!empty schd.meetingSet ? '#960a0a' : 'grey'};"/>
	       								<i class="fas fa-map-marker-alt fa-stack-1x" style="color:#{!empty schd.meetingSet ? '#eb5548' : 'darkgrey'};"/>
	       							</span>
	                					  
	                				<f:param name="termCode" value="#{schd.termCode}"/>
	                				<f:param name="groupCode" value="#{schd.groupCode}"/>
	                				<f:param name="courseCode" value="#{schd.courseCode}"/>
	                				<f:param name="scheduleTypeCode" value="#{schd.scheduleTypeCode}"/>
	                				<f:param name="info" value="#{createBatchScheduleView.getSchdInfoJSON(schd)}"/>
	                			</h:link>
                			</div>
                			
                		</div>

                	</div>
                	
                </p:panel>
                </div>
                		
			</p:repeat>
			</h:panelGroup>
			
			
			<!-- Opt out confirmation dialog -->
			<p:confirmDialog id="createConfirm" widgetVar="delConfirmWidget" 
							 header="Confirm create?" 
							 message="Are you sure to create form batches for the classes selected above?"
							 style="white-space: pre;">
			
				<p:commandButton value="#{bundle['action.ok']}"
								 action="#{createBatchScheduleView.createBatch()}"
								 rendered="#{createBatchScheduleView.selectedClassGroup != null}">
					<p:ajax event="click" oncomplete="PF('delConfirmWidget').hide()" update=":messages"/>
				</p:commandButton>

				<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('delConfirmWidget').hide()" type="button"/>
			
			</p:confirmDialog>
				
			
			<br/>
			
			<h:panelGroup id="buttonPanel">
			
				<p:commandButton value="Back"
						  		 action="#{createBatchScheduleView.gotoCreateBatchGroup()}" 
					    		 ajax="false">
				</p:commandButton>
				
				&#160;
				
				<p:commandButton value="Back to Course List"
						  		 action="#{createBatchScheduleView.gotoCreateBatchCourse()}" 
					    		 ajax="false">
				</p:commandButton>
				
				&#160;
				
				<p:commandButton value="Create Batch"
					  		 	 rendered="#{createBatchScheduleView.isBatchCreated == false}"
					  		 	 onclick="PF('delConfirmWidget').initPosition();PF('delConfirmWidget').show();">
				</p:commandButton>
			
			</h:panelGroup>
			
			<br/><br/>
			
		</h:form>
 		
 		
	</p:panel>
	</ui:define>
		
</ui:composition>