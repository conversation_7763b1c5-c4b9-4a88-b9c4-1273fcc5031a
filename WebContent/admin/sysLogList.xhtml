<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<ui:define name="mainContent">

	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<p:panel id="contentPanel">
		
		<span class="admin-content-title">View System Log</span>
		
		<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			<p:autoUpdate/>
		</p:messages>
		
		<h:form id="listForm">
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
			<p:dataTable id="sysLogTable"
						 value="#{sysLogView.sysLogList}" var="sysLog" 
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 rowIndexVar="rowIndex"
						 rowKey="#{sysLog.name}"
						 selection="#{sysLogView.selectedFileName}"
						 selectionMode="single"
						 sortBy="#{sysLog[sysLogView.sortBy]}"
						 sortOrder="#{sysLogView.sortOrder}"
						 reflow="true"
                  		 tableStyle="table-layout:auto;"
						 >
					
				<p:column sortBy="#{sysLog.name}">
					<f:facet name="header">Name</f:facet>
					<h:outputText value="#{sysLog.name}"/>
				</p:column>
					
				<p:column sortBy="#{sysLog.size}">
					<f:facet name="header">Size (Bytes)</f:facet>
					<h:outputText value="#{sysLog.size}">
						<f:convertNumber groupingUsed="true"/>
					</h:outputText>
				</p:column>

				<p:column sortBy="#{sysLog.lastModified}" priority="3">
					<f:facet name="header">Last Modified</f:facet>
					<h:outputText value="#{sysLog.lastModified}">
						<f:convertDateTime pattern="#{const.DEFAULT_DATE_TIME_FORMAT}" />
					</h:outputText>
				</p:column>
				
				<!-- 
				<p:column>
					<h:commandLink action="#{sysLogView.downloadSysLog(sysLog.name)}">
					<h:graphicImage value="resources/style/icon-download.png" border="0"
									alt="#{pfView.i18n['action.download']}" title="#{pfView.i18n['action.download']}"/>
					</h:commandLink>
				</p:column>
				 -->
	
			</p:dataTable>
			
			<p:contextMenu id="contextMenu" for="sysLogTable" 
						   event="contextmenu #{userSessionView.mobileBrowser ? (userSessionView.chromeBrowser ? 'taphold' : 'click') : ''}">
				<p:menuitem value="#{bundle['action.download']}" action="#{sysLogView.downloadSysLog}" ajax="false" onclick="window.onbeforeunload=null"/>  
			</p:contextMenu>

		</h:form>
		<br/>
 		
	</p:panel>
	</ui:define>
		
</ui:composition>