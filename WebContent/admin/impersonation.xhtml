<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">
			Change View
		</h:panelGroup>
		
		<h:form id="form">
		
			<p:messages globalOnly="true"/>

			<p:focus context="form"/>
					
			<h:panelGrid columns="3" styleClass="edit-panel">
					
				<!-- User ID -->
				<h:panelGroup>
					Target User ID
				</h:panelGroup>
				<h:panelGroup>
					<p:autoComplete id="userId"
								 value="#{impersonationView.impersonateUserId}"
								 completeMethod="#{impersonationView.completeUserId}"
								 maxlength="30">
						<f:validator validatorId="hk.eduhk.rich.access.LDAPUserValidator"/>
						<f:attribute name="validateField" value="userId" />
						<p:ajax execute="@this" update="userIdMsg"/> 
					</p:autoComplete>
	        		<div class="input-validation-message">
						<p:message id="userIdMsg" for="userId"/>
					</div>
				</h:panelGroup>
				<h:panelGroup>
				</h:panelGroup>

			</h:panelGrid>
			<br/>
	
			<h:panelGroup id="buttonPanel" styleClass="button-panel">
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
				<p:commandButton value="#{bundle['action.activate']}"
					  		 	 action="#{impersonationView.activateImpersonation}"
					  		 	 update="@form"/>
			</h:panelGroup>
			
		</h:form>
		<br/> 
 			
	</p:panel>
	</ui:define>
		
</ui:composition>