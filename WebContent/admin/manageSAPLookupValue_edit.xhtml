<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel" >
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="#{ bundle['action.new.x']}">
   				<f:param value="lookupType" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		
		<p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
			<p:autoUpdate/>
		</p:messages>
		
		<p:panelGrid styleClass="edit-panel">
		
			
			<p:row>
			<p:column>
				Lookup Type
			</p:column>
			<p:column>
				<p:inputText id="lookup_type" label="Lookup Type" required="true" value="#{lookupValueFNDView.selectedValue.pk.lookup_type}" style="width: 50%;">
					<f:validateLength maximum="30"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="lookup_typeMsg" for="lookup_type" />
				</div>
			</p:column>
		
			
			<p:column>
				Lookup Code
			</p:column>
			<p:column>
				<p:inputText id="lookup_code" label="Lookup Code" value="#{lookupValueFNDView.selectedValue.pk.lookup_code}" style="width: 50%;">
					<f:validateLength maximum="30"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="lookup_codeMsg" for="lookup_code" />
				</div>
			</p:column>
			</p:row>
			
			
			<p:row>
			<p:column>
				Language
			</p:column>
			<p:column>
				<p:inputText id="language" label="Language" value="#{lookupValueFNDView.selectedValue.pk.language}" style="width: 50%;">
					<f:validateLength maximum="30"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="languageMsg" for="language" />
				</div>
			</p:column>			
			
		
			<p:column>
				Enabled Flag
			</p:column>
			<p:column>
				<p:inputText id="enabled_flag" label="Enabled Flag" value="#{lookupValueFNDView.selectedValue.enabled_flag}" style="width: 50%;">
					<f:validateLength maximum="1"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="enabled_flagMsg" for="enabled_flag" />
				</div>
			</p:column>
			</p:row>
					
			<!-- url -->
			<p:row>
			<p:column>
				Description
			</p:column>
			<p:column>
				<p:inputText id="description" label="Description" value="#{lookupValueFNDView.selectedValue.description}" style="width: 80%;">
					<f:validateLength maximum="240"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="descriptionMsg" for="description" />
				</div>
			</p:column>
			</p:row>					
						
			
			<p:row>
			<p:column>
				Funding code
			</p:column>
			<p:column>
				<p:inputText id="attribute_1" label="Attribute 1" value="#{lookupValueFNDView.selectedValue.attribute_1}" style="width: 50%;">
					<f:validateLength maximum="150"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="attribute_1Msg" for="attribute_1" />
				</div>
			</p:column>							
					
			
			<p:column>
				Funding type
			</p:column>
			<p:column>
				<p:inputText id="attribute_2" label="Attribute 2" value="#{lookupValueFNDView.selectedValue.attribute_2}" style="width: 50%;">
					<f:validateLength maximum="150"/>
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="attribute_2Msg" for="attribute_2" />
				</div>
			</p:column>
			</p:row>					
						
		</p:panelGrid>
		
		<br/>
		<h:panelGroup styleClass="button-panel">
			<p:button value="#{bundle['action.back']}" outcome="manageSAPLookupValue" icon="pi pi-arrow-left" style="margin-right:5px;"/>
			<p:commandButton value="#{bundle['action.create']}"
					  		 action="#{lookupValueFNDView.updateForm()}"
					  		 update="@form" >
			</p:commandButton>
			

		</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
</ui:composition>