<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">

	<span class="admin-content-title">Generate RAEES Report</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<br/><br/>
		
		<div class="ui-g">
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<h:outputText class="riForm-item-title" style="vertical-align:middle;" value="Unit of Assessment:"/>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-10">
				<p:selectOneMenu id="uoa" title="Unit of Assessment" value="#{raeRptView.selectedUoA}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
					<f:selectItems value="#{raeOutputView.uoaList}"  var="uoa" itemLabel="(#{uoa.uoaCode}) #{uoa.uoaDesc}" itemValue="#{uoa.uoaCode}" />
				</p:selectOneMenu>
			</div>
		</div>
		<p:dataTable id="rptTable" var="r" value="#{raeRptView.raeReportList}" stripedRows="true"
						sortMode="single"
						reflow="true"
						rowKey="#{r.report_id}-" tableStyle="table-layout: fixed;">
            
            <p:column headerText="Report ID" id="report_id" sortBy="#{r.report_id}">
			    <h:outputText value="#{r.report_id}"/>
			</p:column>
			
            <p:column headerText="Report Name" id="file_name" sortBy="#{r.file_name}">
			    <h:outputText value="#{fn:substringBefore(r.file_name, '_')}"/>
			</p:column>
            
            <p:column headerText="Updated By" id="report_userstamp" sortBy="#{r.report_userstamp}">
                <h:outputText value="#{r.report_userstamp}"/>
            </p:column>
            
            <p:column headerText="Last Updated Date" id="report_timestamp" sortBy="#{r.report_timestamp}">
			    <h:outputText value="#{r.report_timestamp != null ? r.report_timestamp : 'N/A'}">
			        <f:convertDateTime pattern="yyyy/M/d HH:mm:ss" />
			    </h:outputText>
			</p:column>
			            
            <p:column headerText="Action" >
				<p:commandButton value="Generate" styleClass="btn-action-yellow"
				                 update="@form :generateDialog" 
				                 oncomplete="PF('generateDialogObj').show()">
				    <f:setPropertyActionListener target="#{raeRptView.selectedReportId}" 
				                                 value="#{r.report_id}" />
				</p:commandButton>
            </p:column>
            
            <p:column headerText="Download" >
                <p:commandButton value="Download" escape="True"
								ajax="false"
				                 action="#{raeRptView.exportTableToExcel(r.table_name, r.report_id, r.file_name)}">
				</p:commandButton>
            </p:column>
        </p:dataTable>

	</h:form>
	<p:confirmDialog id="generateDialog" widgetVar="generateDialogObj" 
						 header="Confirm generate report?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.generate.x']}">
						<f:param value="Report ID #{raeRptView.selectedReportId}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="generateForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{raeRptView.generateReport}"
								 update=":dataForm:rptTable :msgs"
								 oncomplete="PF('generateDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('generateDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>