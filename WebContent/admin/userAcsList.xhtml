<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="userId" value="#{userAcsView.selectedUserId}" />
	</f:metadata>

	<ui:define name="mainContent">
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">Manage User Access Right</h:panelGroup><br/>
		
		<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			<p:autoUpdate/>
		</p:messages>
		
		<h:form id="dataForm">
		
			<!-- Selection -->
			<h:panelGrid columns="2">
				<h:panelGroup>
					User ID 
					<h:outputText value="&#160;"/>
				</h:panelGroup>
				<h:panelGroup>
					<p:selectOneMenu id="selectedUserId" value="#{userAcsView.selectedUserId}">
						<f:selectItem itemLabel="All" itemValue="ALL"/>
						<f:selectItems value="#{userAcsView.userIdList}" var="userId" 
									   itemLabel="#{userId}" 
									   itemValue="#{userId}"/>
						<f:ajax event="change" execute="@this" render="dataTable"/>										 
					</p:selectOneMenu>
					<p:message id="selectedUserIdMsg" for="selectedUserId"/>
				</h:panelGroup>
			</h:panelGrid>
			
			<br/>
			
			<p:dataTable id="dataTable"
						 value="#{userAcsView.userAcsList}" var="userAcs"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 rowIndexVar="rowIndex"
						 rowKey="#{userAcs}"
						 selection="#{userAcsView.selectedUserAcs}"
						 selectionMode="single"
						 sortMode="single"
						 reflow="true"
                  		 tableStyle="table-layout:auto;"
						 >
			
				<p:column sortBy="#{userAcs.userId}">
					<f:facet name="header">User ID</f:facet>
					<h:outputText value="#{userAcs.userId}"/>
				</p:column>
			
				<p:column sortBy="#{userAcs.crseSubjCode}">
					<f:facet name="header">Subject Code</f:facet>
					<h:outputText value="#{userAcs.crseSubjCode}"/>
				</p:column>

				<p:column sortBy="#{userAcs.crseNum}">
					<f:facet name="header">Course Number</f:facet>
					<h:outputText value="#{userAcs.crseNum}"/>
				</p:column> 

			</p:dataTable>
			
			<p:contextMenu id="contextMenu" for="dataTable" 
						   event="contextmenu #{userSessionView.mobileBrowser ? (userSessionView.chromeBrowser ? 'taphold' : 'click') : ''}">
				<p:menuitem value="#{bundle['action.new']}" update="dataTable" action="#{userAcsView.gotoNewUserAcsPage}"/>
				<p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
			</p:contextMenu>
			
			<br/>
			<p:commandButton value="#{bundle['action.new']}" 
							 action="#{userAcsView.gotoNewUserAcsPage}"
							 ajax="false"
							 rendered="#{empty userAcsView.userAcsList}"/>
			
		</h:form>
		<br/>
		
		<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{userAcsView.selectedUserAcs.getAllthreeCol()}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{userAcsView.deleteUserAcs}"
								 update=":dataForm:dataTable :messages"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
 		
	</p:panel>
	</ui:define>
		
</ui:composition>