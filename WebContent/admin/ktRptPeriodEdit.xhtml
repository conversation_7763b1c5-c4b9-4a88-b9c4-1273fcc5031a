<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata> 
		<f:viewParam name="periodId" value="#{manageKtReportView.paramPeriodId}" />
	</f:metadata>


	<ui:define name="mainContent">
	
		<p:panel >
			<h:panelGroup styleClass="admin-content-title"><i class="fa-solid fa-timeline"></i>
				<h:outputFormat value="#{manageKtReportView.selectedPeriod.creator != null ? bundle['action.edit.x'] : bundle['action.new.x']}" style="margin-left:6px;">
	   				<f:param value="KT Reporting Period" />
				</h:outputFormat>
			</h:panelGroup>
			<h:form id="dataForm">
				<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Period Date From" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="date_from"/>
						<p:datePicker id="date_from" required="true"
												title="Period Date From" 
												label="Period Date From" 
												value="#{manageKtReportView.selectedPeriod.date_from}" 
												pattern="yyyy-M-d" showIcon="true"/>			
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Period Date To" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="date_to"/>
						<p:datePicker id="date_to" required="true"
												title="Period Date To"
												label="Period Date To"
												value="#{manageKtReportView.selectedPeriod.date_to}" 
												pattern="yyyy-M-d" showIcon="true"/>			
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Period Description" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="period_desc_msg" for="period_desc" />
						<p:inputText id="period_desc" required="true"
							label="Period Description" maxlength="100"
							value="#{manageKtReportView.selectedPeriod.period_desc}"
							style="width: 90%;"/>
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Remarks" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:inputTextarea id="remarks" style="width: 90%;" rows="7"
							counter="remarks_display" maxlength="4000"
							value="#{manageKtReportView.selectedPeriod.remarks}"
							counterTemplate="#{bundle['form.remaining.characters']} {0}"
							autoResize="false" />
						<br />
						<h:outputText id="remarks_display" class="p-d-block" />
					</div>
					
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:message id="is_current_msg" for="is_current" />
						<p:selectBooleanCheckbox id="is_current" style="font-weight: 700; color:#e1104a;" value="#{manageKtReportView.selectedPeriod.is_current}" itemLabel="This is current period."/>
					</div>
				</div>
				<p:button value="#{bundle['action.back']}" outcome="ktRptPeriodList" icon="pi pi-arrow-left" styleClass="btn-back"/>
				<p:commandButton value="#{bundle['action.create']}"
						  		 action="#{manageKtReportView.updatePeriod}"
						  		 update="@form" 
						  		 rendered="#{manageKtReportView.selectedPeriod.creationDate == null}">
				</p:commandButton>
				
				<p:commandButton value="#{bundle['action.update']}" 
						  		 action="#{manageKtReportView.updatePeriod}" 
						  		 update="@form" 
						  		 rendered="#{manageKtReportView.selectedPeriod.creationDate != null}">
				</p:commandButton>

			</h:form>
		</p:panel>	

	</ui:define>
		
</ui:composition>