<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata> 
		<f:viewParam name="formCode" value="#{manageKtFormView.paramFormCode}" />
	</f:metadata>


	<ui:define name="mainContent">
	
		<p:panel >
			<h:panelGroup styleClass="admin-content-title"><i class="fa-solid fa-list-ul"></i>
				<h:outputFormat value="#{manageKtFormView.selectedForm.creator != null ? bundle['action.edit.x'] : bundle['action.new.x']}" style="margin-left:6px;">
	   				<f:param value="KT Activity Form" />
				</h:outputFormat>
			</h:panelGroup>
			<h:form id="dataForm">
				<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Form Code" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="form_code"/>
						<p:inputText id="form_code" required="true" disabled="#{manageKtFormView.selectedForm.creator != null}"
									label="Form Code" maxlength="100"
									value="#{manageKtFormView.selectedForm.form_code}"
									style="width: 90%;"/>		
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Short Desc." />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="form_short_desc"/>
						<p:inputText id="form_short_desc" required="true"
									label="Short Desc." maxlength="30"
									value="#{manageKtFormView.selectedForm.form_short_desc}"
									style="width: 90%;"/>		
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Full Desc." />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="form_full_desc"/>
						<p:inputText id="form_full_desc" required="true"
									label="Full Desc." maxlength="100"
									value="#{manageKtFormView.selectedForm.form_full_desc}"
									style="width: 90%;"/>		
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Brief" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="form_brief"/>
						<p:inputText id="form_brief" required="true"
									label="Brief" maxlength="500"
									value="#{manageKtFormView.selectedForm.form_brief}"
									style="width: 90%;"/>		
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Note 1" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:textEditor widgetVar="note_1" value="#{manageKtFormView.selectedForm.note_1}" height="200">
				            <f:facet name="toolbar">
				             <span class="ql-formats">
				                <button class="ql-bold"></button>
				                <button class="ql-italic"></button>
				                <button class="ql-underline"></button>
				                <button class="ql-strike"></button>
				            </span>
				            </f:facet>
				        </p:textEditor>
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Note 2" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:textEditor widgetVar="note_2" value="#{manageKtFormView.selectedForm.note_2}" height="200">
				            <f:facet name="toolbar">
				             <span class="ql-formats">
				                <button class="ql-bold"></button>
				                <button class="ql-italic"></button>
				                <button class="ql-underline"></button>
				                <button class="ql-strike"></button>
				            </span>
				            </f:facet>
				        </p:textEditor>
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="Display Order" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="display_order_msg" for="display_order" />
						<p:inputNumber id="display_order" decimalPlaces="0" required="true"
								value="#{manageKtFormView.selectedForm.display_order}"
								emptyValue="0" minValue="0" maxValue="9999" />
					</div>
					
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:message id="is_enabled_msg" for="is_enabled" />
						<p:selectBooleanCheckbox id="is_enabled" style="font-weight: 700; color:#055588;" value="#{manageKtFormView.selectedForm.is_enabled}" itemLabel="Enable"/>
					</div>
				</div>
				<br/>
				<p:button value="#{bundle['action.back']}" outcome="ktActFormList" icon="pi pi-arrow-left" styleClass="btn-back"/>
				<p:commandButton value="#{bundle['action.create']}"
						  		 action="#{manageKtFormView.updateForm}"
						  		 update="@form" 
						  		 rendered="#{manageKtFormView.selectedForm.creationDate == null}">
				</p:commandButton>
				
				<p:commandButton value="#{bundle['action.update']}" 
						  		 action="#{manageKtFormView.updateForm}" 
						  		 update="@form" 
						  		 rendered="#{manageKtFormView.selectedForm.creationDate != null}">
				</p:commandButton>

			</h:form>
		</p:panel>	

	</ui:define>
		
</ui:composition>