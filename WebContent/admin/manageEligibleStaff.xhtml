<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<ui:define name="html_head">
		<style>
			.ui-fileupload-filename {
			    display: inline-block !important;
			}
			a.button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;

    text-decoration: none;
    color: initial;
}
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	
	<span class="admin-content-title"><i class="fas fa-users-viewfinder"></i> Manage Eligible Staff List</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm" enctype="multipart/form-data">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:toolbar>
			<p:toolbarGroup align="left">
				<p:fileUpload id="uploadedFile" value="#{staffEligibleView.uploadedFile}" label="Choose an Excel file" mode="simple" skinSimple="true" style="margin-right:4px;"
							binding="#{uploadedFileObj}"/>
				<p:commandButton id="uploadButton" value="Upload" icon="pi pi-upload" ajax="false" 
						 update="@form"
						 action="#{staffEligibleView.upload()}" ></p:commandButton>		
	 			

            </p:toolbarGroup>
			<p:toolbarGroup align="right">
				<a href="../web/staff_list_template.xlsx" download="staff_list_template.xlsx" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon-left" 
					role="button" style="margin-right:4px;">
	 			<span class="ui-button-icon-left ui-icon ui-c pi pi-download"></span>
	 			<span class="ui-button-text ui-c">Download Template</span>
	 			</a>
				<p:commandButton value="Export to Excel" icon="pi pi-download" ajax="false" immediate="true" rendered="#{staffEligibleView.staffEligibleList ne null}" >
            	 	<p:dataExporter type="xlsx" target="staffTable" fileName="#{staffEligibleView.getFileName()}" />
            	 </p:commandButton>
            </p:toolbarGroup>
        </p:toolbar>
		<p:dataTable id="staffTable" var="staff" value="#{staffEligibleView.staffEligibleList}" sortMode="single" 
						rowKey="#{staff.pk}" tableStyle="table-layout: fixed; font-size:12px; word-wrap: break-word; "
						styleClass="default-dataTable"
						rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						rowIndexVar="rowIndex"
						selection="#{staffEligibleView.selectedStaff}" selectionMode="single"
                     	paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						paginator="true"
						rows="30"
						currentPageReportTemplate="{startRecord}-{endRecord} of {totalRecords} records">

			<p:column headerText="Reporting Period" id="reporting_period" sortBy="#{staff.reporting_period}" style="width:5rem" filterBy="#{staff.reporting_period}" filterMatchMode="contains">
                <h:outputText value="#{staff.reporting_period}"/>
            </p:column>
            
			<p:column headerText="Employee Number" id="employee_number" sortBy="#{staff.pk.employee_number}" style="width:5rem" filterBy="#{staff.pk.employee_number}" filterMatchMode="contains">
                <h:outputText value="#{staff.pk.employee_number}"/>
            </p:column>
            
            <p:column headerText="Assignment Number" id="assignment_number" sortBy="#{staff.assignment_number}" style="display:none" filterBy="#{staff.assignment_number}" filterMatchMode="contains">
                <h:outputText value="#{staff.assignment_number}"/>
            </p:column>
            
            <p:column headerText="Title" id="title" sortBy="#{staff.title}" style="width:3rem" filterBy="#{staff.title}" filterMatchMode="contains">
                <h:outputText value="#{staff.title}"/>
            </p:column>

            <p:column headerText="Husband Last Name" id="husband_last_name" sortBy="#{staff.husband_last_name}" style="display:none" filterBy="#{staff.husband_last_name}" filterMatchMode="contains">
                <h:outputText value="#{staff.husband_last_name}" rendered="#{staff.husband_last_name ne null}"/>
                <h:outputText value="NULL" rendered="#{staff.husband_last_name eq null}"/>
            </p:column>
            
            <p:column headerText="Last Name" id="last_name" sortBy="#{staff.last_name}" style="width:5rem" filterBy="#{staff.last_name}" filterMatchMode="contains">
                <h:outputText value="#{staff.last_name}"/>
            </p:column>
            
            <p:column headerText="First Name" id="first_name" sortBy="#{staff.first_name}" style="width:7rem" filterBy="#{staff.first_name}" filterMatchMode="contains">
                <h:outputText value="#{staff.first_name}"/>
            </p:column>
            
            <p:column headerText="Chinese Name" id="chinese_name" sortBy="#{staff.chinese_name}" style="width:5rem" filterBy="#{staff.chinese_name}" filterMatchMode="contains">
                <h:outputText value="#{staff.chinese_name}"/>
            </p:column>
            
            <p:column headerText="Department Code" id="dept" sortBy="#{staff.dept_code}" style="width:4rem" filterBy="#{staff.dept_code}" filterMatchMode="contains">
                <h:outputText value="#{staff.dept_code}"/>
            </p:column>

            <p:column headerText="Post Rank Code" id="post_rank_code" sortBy="#{staff.post_rank_code}" style="width:4rem" filterBy="#{staff.post_rank_code}" filterMatchMode="contains">
                <h:outputText value="#{staff.post_rank_code}"/>
            </p:column>

			<p:column headerText="Staff Grade" id="staff_grade" sortBy="#{staff.staff_grade}" style="width:3rem" filterBy="#{staff.staff_grade}" filterMatchMode="contains">
                <h:outputText value="#{staff.staff_grade}"/>
            </p:column>
                        
            <p:column headerText="DCC" id="dcc" sortBy="#{staff.pk.dcc}" style="width:3rem" filterBy="#{staff.pk.dcc}" filterMatchMode="contains">
                <h:outputText value="#{staff.pk.dcc}"/>
            </p:column>
            
            <p:column headerText="DCC Percentage" id="dcc_percentage" sortBy="#{staff.dcc_percentage}" style="width:3rem" filterBy="#{staff.dcc_percentage}" filterMatchMode="contains">
                <h:outputText value="#{staff.dcc_percentage}"/>
            </p:column>


            <p:column headerText="Terms Of Appointment" id="terms_of_appointment" sortBy="#{staff.terms_of_appointment}" style="display:none" filterBy="#{staff.terms_of_appointment}" filterMatchMode="contains">
                <h:outputText value="#{staff.terms_of_appointment}"/>
            </p:column>
            
            <p:column headerText="Post Type" id="post_type" sortBy="#{staff.post_type}" style="width:4rem" filterBy="#{staff.post_type}" filterMatchMode="contains">
                <h:outputText value="#{staff.post_type}"/>
            </p:column>
            
            <p:column headerText="Last Termination Date" id="last_termination_date" sortBy="#{staff.last_termination_date}" style="display:none" filterBy="#{staff.last_termination_date}" filterMatchMode="contains">
                <h:outputText value="#{staff.last_termination_date}" rendered="#{staff.last_termination_date ne null}">
                <f:convertDateTime dateStyle="full" pattern="dd-MMM-yyyy" timeZone="Hongkong"/>
                </h:outputText>
                <h:outputText value="NULL" rendered="#{staff.last_termination_date eq null}"/>
            </p:column>
            
            <p:column headerText="Principal Assignment Flag" id="principal_assignment_flag" sortBy="#{staff.principal_assignment_flag}" style="width:5rem" filterBy="#{staff.principal_assignment_flag}" filterMatchMode="contains">
                <h:outputText value="#{staff.principal_assignment_flag}"/>
            </p:column>
            
            <p:column headerText="Mapped Dept." id="mapped_dept" sortBy="#{staff.mapped_dept}" style="width:4rem" filterBy="#{staff.mapped_dept}" filterMatchMode="contains">
                <h:outputText value="#{staff.mapped_dept}"/>
            </p:column>
            
            <p:column headerText="Start Period" id="start_period" sortBy="#{staff.start_period}" style="width:4rem" filterBy="#{staff.start_period}" filterMatchMode="contains">
                <h:outputText value="#{staff.start_period}"/>
            </p:column>
            
            <p:column headerText="End Period" id="end_period" sortBy="#{staff.end_period}" style="width:4rem" filterBy="#{staff.end_period}" filterMatchMode="contains">
                <h:outputText value="#{staff.end_period}"/>
            </p:column>
            
            <p:column headerText="Remarks" id="remarks" sortBy="#{staff.remarks}" style="width:7rem" filterBy="#{staff.remarks}" filterMatchMode="contains">
                <h:outputText value="#{staff.remarks}"/>
            </p:column>
        </p:dataTable>

	</h:form>

	</p:panel>
   </ui:define>
</ui:composition>