<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	
	<!--
	<ui:define name="title">
		Administer Form
	</ui:define>
	-->
	
	<ui:define name="mainContent">
	
		<p:panel id="contentPanel">
		
			<p:growl id="growl" life="10000" showDetail="false" sticky="false"/>  
					
			<h:form id="form">
				<span class="admin-content-title">Administer Form</span>
				
				<component:impersonationPanel id="impersonationPanel" viewBean="#{formListView}"/>
    			
					<h:panelGroup id="dataPanel">
						<p:dataTable id="dataTable" 
									 var="dataRow"
									 value="#{formView.formList}" 
									 rowKey="#{dataRow.formId}"
									 rowIndexVar="rowIndex"
									 selection="#{formView.selectedForm}"
									 selectionMode="single"
									 sortMode="single"
									 reflow="true"
									 paginator="true"
									 currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
			                         paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
			                  		 tableStyle="table-layout:auto;"
			                  		 >

							<p:column sortBy="#{dataRow.formId}" style="width: 5em;">
								<f:facet name="header">ID</f:facet>
								<h:outputText value="#{dataRow.formId}"/>
							</p:column>

							<!--  This is obsolete due to the removal of the column form_grp from the table FA_FRM
							<p:column sortBy="#{dataRow.formGroup}" style="width: 5em;">
								<f:facet name="header">Group</f:facet>
								<h:outputText value="#{dataRow.formGroup}"/>
							</p:column>
		  					-->

							<p:column sortBy="#{dataRow.code}" style="width: 10em;">
								<f:facet name="header">Code</f:facet>
								<h:outputText value="#{dataRow.code}"/>
							</p:column>
							
							<p:column sortBy="#{dataRow.name}" style="width: 25em;">
								<f:facet name="header">Name</f:facet>
								<h:outputText value="#{dataRow.name}"/>
							</p:column>
		
							<p:column sortBy="#{dataRow.description}" style="width: 30em;">
								<f:facet name="header">Description</f:facet>
								<h:outputText value="#{dataRow.description}"/>
							</p:column>
		
							<p:column sortBy="#{dataRow.formOrder}" style="width: 5em;">
								<f:facet name="header">Order</f:facet>
								<h:outputText value="#{dataRow.formOrder}"/>
							</p:column>							

							<p:column sortBy="#{dataRow.classifiedInfo}" style="width: 15em;">
								<f:facet name="header">Classified Info.</f:facet>
								<h:outputText value="#{dataRow.classifiedInfo}"/>
							</p:column>
		
							<p:column sortBy="#{dataRow.allowDuplicate}" style="width: 5em;">
								<f:facet name="header">Allow Duplicate</f:facet>
								<h:outputText rendered="#{dataRow.allowDuplicate}" value="#{bundle['val.yes']}"/>
								<h:outputText rendered="#{! dataRow.allowDuplicate}" value="#{bundle['val.no']}"/>
							</p:column>		
																					
			            </p:dataTable>
						<p:contextMenu id="contextMenu" for="dataTable"  widgetVar="ctxMenu"
									   event="contextmenu #{formView.mobileBrowser ? (formView.chromeBrowser ? 'taphold' : 'click') : ''}">
							<p:menuitem value="#{bundle['action.edit']}" action="#{formView.gotoFormEdit}" rendered="#{not empty formView.formList}"/>  
						</p:contextMenu>			            
		            </h:panelGroup>
		            <br/>
			    	<p:commandButton value="Back" action="#{formView.gotoDashboard}"/>
			</h:form>
			
		</p:panel>
				
		
	</ui:define>
</ui:composition>