<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="staff_number" value="#{researcherInfoView.paramStudNumber}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel" >
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="Edit Researcher IDs">
   				<f:param value="pid" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		
		<p:messages id="msgs" showDetail="true" closable="true" globalOnly="true">
			<p:autoUpdate/>
		</p:messages>
		
		<p:panelGrid styleClass="edit-panel">
			<!-- PID -->
			<p:row>
				<p:column><b>Pid</b></p:column>
				<p:column>
					<h:outputText id="researcher_pid" value="#{researcherInfoView.selectedresearcherInfo.pid}" style="width: 50%;"/>
				</p:column>
				
				<!-- ORCiD -->
				<p:column>
					<b>ORCiD</b>
					 <a href="https://orcid.org/" title="Click to ORCiD website" target="_blank" style="color:red">
					 	<i class="far fa-question-circle"></i>
					 </a>
				</p:column>
				<p:column>
					<p:inputText id="orcid" label="ORCiD" required="true"
						value="#{researcherInfoView.selectedresearcherInfo.staffInfo.orcid}" 
						maxlength="19" style="width:50%;">
						<f:validateLength maximum="19"/>
					</p:inputText>
	        		<div class="input-validation-message">
						<p:message id="researcher_orcidMsg" for="orcid" />
					</div>
				</p:column>
			</p:row>

			<p:row>
			<!-- English Name -->
				<p:column><b>English Name</b></p:column>
				<p:column>
					<h:outputText id="researcher_name" value="#{researcherInfoView.selectedresearcherInfo.fullname}" 
						style="width: 50%;"/>
				</p:column>
				
				<!-- Scopus Author ID [1]  -->
				<p:column><b>Scopus Author ID [1]</b>
					<a href="https://www.scopus.com/freelookup/form/author.uri" title="Click to Scopus website" target="_blank" style="color:red">
						<i class="far fa-question-circle"></i>
					</a>
				</p:column>
				<p:column>
					<p:inputText id="researcher_scopus_id_1" label="Scopus Author ID [1]" 
						value="#{researcherInfoView.selectedresearcherInfo.staffInfo.scopus_id_1}" 
						style="width: 50%;" 
						validatorMessage="Scopus Author ID [1] should be 10 to 11 digits" >
						<f:validateRegex pattern="^([0-9]{10,11})?$"/>
					</p:inputText>
	        		<div class="input-validation-message">
						<p:message id="researcher_scopus_id_1Msg" for="researcher_scopus_id_1" />
					</div>
				</p:column>
			</p:row>
			
			<p:row>	
				<!-- Email  -->
				<p:column><b>Email</b></p:column>
				<p:column>
					<h:outputText id="researcher_email" 
						value="#{researcherInfoView.selectedresearcherInfo.email}" 
						style="width: 50%;"/>
				</p:column>	
	
				<!-- Scopus Author ID [2] -->
				<p:column><b>Scopus Author ID [2]</b>
					<a href="https://www.scopus.com/freelookup/form/author.uri" title="Click to Scopus website" target="_blank" style="color:red">
						<i class="far fa-question-circle"></i>
					</a>
				</p:column>
				<p:column>
					<p:inputText  id="researcher_scopus_id_2" label="Scopus Author ID [2]" 
						value="#{researcherInfoView.selectedresearcherInfo.staffInfo.scopus_id_2}" 
						style="width: 50%;"   validatorMessage="Scopus Author ID [2] should be 10 to 11 digits" >
						<f:validateRegex pattern="^([0-9]{10,11})?$"/>
					</p:inputText>
	        		<div class="input-validation-message">
						<p:message id="researcher_scopus_id_2Msg" for="researcher_scopus_id_2" />
					</div>
				</p:column>
			</p:row>							
			<p:row>
				<!-- Last Updated Datetime -->
				<p:column><b>Last Updated Datetime</b></p:column>
				<p:column>
					<h:outputText id="last_updated_time" value="#{researcherInfoView.selectedresearcherInfo.staffInfo.timestamp}" style="width: 50%;">
						<f:convertDateTime dateStyle="full" pattern="dd/MM/yyyy HH:mm:ss" timeZone="Hongkong"/>
					</h:outputText>
				</p:column>	
			
				<!-- ResearchID -->
				<p:column><b>ResearchID</b>
					<a href="http://www.researcherid.com/" title="Click to ResearcherID website" target="_blank" style="color:red">
						<i class="far fa-question-circle"></i>
					</a>
				</p:column>
				<p:column>
					<p:inputMask id="researcher_id" label="ResearchID" value="#{researcherInfoView.selectedresearcherInfo.staffInfo.researcherid}"
					mask="a-9999-9999" validateMask="true"
					maxlength="11" style="width: 50%;">
					</p:inputMask>
	        		<div class="input-validation-message">
						<p:message id="researcher_idMsg" for="researcher_id" />
					</div>
				</p:column>
			</p:row>					
						
		</p:panelGrid>
		
		<br/>
			<h:panelGroup styleClass="button-panel">
				<p:button value="#{bundle['action.back']}" 
						outcome="researcherIDList" 
						   icon="pi pi-arrow-left" 
						   style="margin-right:5px;"/>
				<p:commandButton value="#{bundle['action.update']}" 
								action="#{researcherInfoView.updateForm()}" 
								update="@form"></p:commandButton>
			</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>