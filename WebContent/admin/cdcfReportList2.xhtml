<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"><head id="j_idt2">
		
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1" /><link type="text/css" rel="stylesheet" href="/rich/javax.faces.resource/theme.css.xhtml?ln=primefaces-saga&amp;v=10.0.0&amp;e=10.0.6" /><link type="text/css" rel="stylesheet" href="/rich/javax.faces.resource/primeicons/primeicons.css.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6" /><link type="text/css" rel="stylesheet" href="/rich/javax.faces.resource/primeicons/primeicons.css.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6" /><script type="text/javascript" src="/rich/javax.faces.resource/jquery/jquery.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript" src="/rich/javax.faces.resource/font-awesome/6.2.0/js/all.js.xhtml?ln=webjars"></script><script type="text/javascript" src="/rich/javax.faces.resource/fixviewstate.js.xhtml?ln=omnifaces&amp;v=2.7"></script><script type="text/javascript">$=jQuery;</script><script type="text/javascript">
		
		$(document).ready
		(
			function()
			{ 
				winUtil.trackHistory();
				
				var originalPrimeFacesAjaxUtilsSend = PrimeFaces.ajax.Request.send;
				PrimeFaces.ajax.Request.send = function(cfg) 
				{
				    // Add default error handler if no error handler 
				    if (!cfg.onerror) 
				    {
				        cfg.onerror = function(event, jqXHR, ajaxSettings, err) 
				        {
				        	// Session timeout, redirect to OAM
				        	if (event.status == 302)
				        	{
				        		window.location.replace("/rich/sessionTimeout.xhtml");
				        	}
				        };
				    }
				    originalPrimeFacesAjaxUtilsSend.apply(this, arguments);
				};

			}
			
		);
	
		
		
	</script><script type="text/javascript" src="/rich/javax.faces.resource/core.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript" src="/rich/javax.faces.resource/idlemonitor/idlemonitor.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><link type="text/css" rel="stylesheet" href="/rich/javax.faces.resource/components.css.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6" /><script type="text/javascript" src="/rich/javax.faces.resource/jquery/jquery-plugins.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript" src="/rich/javax.faces.resource/components.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript" src="/rich/javax.faces.resource/inputmask/inputmask.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript" src="/rich/javax.faces.resource/filedownload/filedownload.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript" src="/rich/javax.faces.resource/touch/touchswipe.js.xhtml?ln=primefaces&amp;v=10.0.0&amp;e=10.0.6"></script><script type="text/javascript">if(window.PrimeFaces){PrimeFaces.settings.locale='en';PrimeFaces.settings.viewId='/admin/ktListing.xhtml';PrimeFaces.settings.contextPath='/rich';PrimeFaces.settings.cookiesSecure=false;PrimeFaces.settings.projectStage='Development';}</script>
	<title>RICH - Research Information Core Hub
	</title>
	<link rel="shortcut icon" type="image/x-icon" href="/rich/javax.faces.resource/image/favicon.ico.xhtml" />
	<link rel="stylesheet" media="screen" type="text/css" href="/rich/resources/css/app.css" />
	<link rel="stylesheet" media="screen" type="text/css" href="/rich/resources/css/app_admin.css" />
	<link rel="stylesheet" media="print" type="text/css" href="/rich/resources/css/app_print.css" />
	<link rel="stylesheet" media="screen" type="text/css" href="/rich/resources/css/primeflex.css" />
	<link rel="stylesheet" media="screen" type="text/css" href="/rich/resources/css/font-awesome-animation.css" />
	<script type="text/javascript" src="/rich/resources/js/util.js"></script></head><body><table class="inner-col" cellpadding="0" cellspacing="0" style="width:100%;">
<tfoot>
<tr><td class="foot" colspan="1">
	<br /><br /><br /><div id="bottomPanel" class="ui-panel ui-widget ui-widget-content ui-corner-all frame-bottom noprint" style="overflow:hidden; position: fixed; width: 99%; border:0; bottom: 0; z-index:1;" data-widget="widget_bottomPanel"><div id="bottomPanel_content" class="ui-panel-content ui-widget-content">
		<div class="row">
			<div class="cell">© 2022 
			The Education University of Hong Kong. All Rights Reserved.</div>
		</div>

		<div style="width:100%; text-align:right; color: #ffffff;">
			
		</div></div></div><script id="bottomPanel_s" type="text/javascript">$(function(){PrimeFaces.cw("Panel","widget_bottomPanel",{id:"bottomPanel"});});</script></td></tr>
</tfoot>
<tbody>
<tr>
<td>
<form id="topForm" name="topForm" method="post" action="/rich/admin/ktListing.xhtml" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="topForm" value="topForm" />
<script id="topForm:j_idt20_s" type="text/javascript">$(function(){PrimeFaces.cw("IdleMonitor","widget_topForm_j_idt20",{id:"topForm:j_idt20",timeout:3600000,multiWindowSupport:false,onidle:function(){PF('idleDialog').show();}});});</script><div id="topForm:confirmDialog" class="ui-confirm-dialog ui-dialog ui-widget ui-widget-content ui-corner-all ui-shadow ui-hidden-container"><div class="ui-dialog-titlebar ui-widget-header ui-helper-clearfix ui-corner-top"><span id="topForm:confirmDialog_title" class="ui-dialog-title">Session Timeout</span><a href="#" class="ui-dialog-titlebar-icon ui-dialog-titlebar-close ui-corner-all" aria-label="Close"><span class="ui-icon ui-icon-closethick"></span></a></div><div class="ui-dialog-content ui-widget-content" id="topForm:confirmDialog_content"><span class="ui-icon ui-icon-alert ui-confirm-dialog-severity"></span><span class="ui-confirm-dialog-message">Sorry, your session has expired.</span></div><div class="ui-dialog-buttonpane ui-dialog-footer ui-widget-content ui-helper-clearfix"><button id="topForm:confirm" name="topForm:confirm" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only" onclick="PrimeFaces.ab({s:&quot;topForm:confirm&quot;,f:&quot;topForm&quot;,onco:function(xhr,status,args,data){location.reload();;}});return false;" type="submit"><span class="ui-button-text ui-c">Ok</span></button><script id="topForm:confirm_s" type="text/javascript">$(function(){PrimeFaces.cw("CommandButton","widget_topForm_confirm",{id:"topForm:confirm"});});</script></div></div><script id="topForm:confirmDialog_s" type="text/javascript">$(function(){PrimeFaces.cw("ConfirmDialog","idleDialog",{id:"topForm:confirmDialog"});});</script><div id="topForm:j_idt21" class="ui-panel ui-widget ui-widget-content ui-corner-all banner" data-widget="widget_topForm_j_idt21"><div id="topForm:j_idt21_content" class="ui-panel-content ui-widget-content">
		<table>
			<tr>
				<td width="1">
					<div class="banner-eduhk-logo after-space"><img src="/rich/resources/image/logo_eduhk.png" />
					</div>
				</td>
				<td width="99%" align="center">
					<a href="/rich/user/index.xhtml" style="text-decoration: none;">
						<div class="title">
							<span class="name">RICH - Research Information Core Hub</span> <span style="font-size: 70%;">v3.0</span>
						</div>
					</a>						
				</td>
 				
				<td width="1"><a id="topForm:j_idt25" href="#" class="ui-commandlink ui-widget" onclick="PrimeFaces.ab({s:&quot;topForm:j_idt25&quot;,f:&quot;topForm&quot;,g:false});return false;"><span id="topForm:menuLink">
							<i class="fas fa-bars fa-3x" style="color: #186ba0; padding-right:5pt;"></i></span></a><div id="topForm:j_idt27" class="ui-menu ui-menu-dynamic ui-widget ui-widget-content ui-corner-all ui-helper-clearfix ui-shadow" role="menu"><div tabindex="0" class="ui-helper-hidden-accessible"></div><ul class="ui-menu-list ui-helper-reset"><li class="ui-widget-header ui-corner-all"><h3>Academic Staff Console</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageInfo.xhtml"><span class="ui-menuitem-text">Manage My Info.</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageProfile.xhtml"><span class="ui-menuitem-text">Manage Profile Preferences</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageAsst.xhtml"><span class="ui-menuitem-text">Manage Assistant Right</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/importRI.xhtml"><span class="ui-menuitem-text">Import RI</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageOutput.xhtml"><span class="ui-menuitem-text">Manage Research Outputs</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageProject.xhtml"><span class="ui-menuitem-text">Manage Projects</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageAward.xhtml"><span class="ui-menuitem-text">Manage Prizes and Awards</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/managePatent.xhtml"><span class="ui-menuitem-text">Manage Patents</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/ktFormSum.xhtml"><span class="ui-menuitem-text">Manage KT Activities</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/consentRI.xhtml?consent=U"><span class="ui-menuitem-text">Consent RI</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Assistant Console</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/asstRequest.xhtml"><span class="ui-menuitem-text">Apply Assistant Right </span></a></li><li class="ui-widget-header ui-corner-all"><h3>Faculty/ Department/ Centre Console</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/ktFormSum.xhtml?admin=Y"><span class="ui-menuitem-text">Manage Fac./Dept./Ctr. KT Activities</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Other Console</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageOutput.xhtml"><span class="ui-menuitem-text">Manage Research Outputs</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageProject.xhtml"><span class="ui-menuitem-text">Manage Projects</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/manageAward.xhtml"><span class="ui-menuitem-text">Manage Prizes and Awards</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/managePatent.xhtml"><span class="ui-menuitem-text">Manage Patents</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/ktFormSum.xhtml"><span class="ui-menuitem-text">Manage KT Activities</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Reference Material</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/aboutCdcf.xhtml"><span class="ui-menuitem-text">About CDCF</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Configuration</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageDepartment.xhtml"><span class="ui-menuitem-text">Manage Department</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageEmailTmpl.xhtml"><span class="ui-menuitem-text">Manage Email Template</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageRank.xhtml"><span class="ui-menuitem-text">Manage Rank</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageExclusiveUse.xhtml"><span class="ui-menuitem-text">Set Exclusive Use of RI</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageExKt.xhtml"><span class="ui-menuitem-text">Set Exclusive of KT Activities</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/ktRptPeriodList.xhtml"><span class="ui-menuitem-text">Manage KT Reporting Period</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/ktActFormList.xhtml"><span class="ui-menuitem-text">Manage KT Activity Forms</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/managePureMap.xhtml"><span class="ui-menuitem-text">Manage Pure Mapping</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageEligibleStaff.xhtml"><span class="ui-menuitem-text">Manage Eligible Staff List</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageSAPLookupValue.xhtml"><span class="ui-menuitem-text">Manage Lookup Value</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Library Administration</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/pureXML.xhtml"><span class="ui-menuitem-text">View Pure XML</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Report/Inquiry</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/researchInformationListing.xhtml"><span class="ui-menuitem-text">Research Information Listing</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/ktListing.xhtml"><span class="ui-menuitem-text">KT Activities Listing</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/cdcfReportList.xhtml"><span class="ui-menuitem-text">Generate Report/ Statistic</span></a></li><li class="ui-widget-header ui-corner-all"><h3>Processing</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageInstituteRI.xhtml"><span class="ui-menuitem-text">Manage Institute RI</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/searchProfile.xhtml"><span class="ui-menuitem-text">Search Staff Profile</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/uploadRI.xhtml"><span class="ui-menuitem-text">Upload RI</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageKtActivities.xhtml"><span class="ui-menuitem-text">Manage KT Activities</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/uploadKtActivities.xhtml"><span class="ui-menuitem-text">Upload KT Activities</span></a></li><li class="ui-widget-header ui-corner-all"><h3>System Administration</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/dataAccess.xhtml"><span class="ui-menuitem-text">Set Data Access</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/funcAccess.xhtml"><span class="ui-menuitem-text">Set Functional Access</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/impersonation.xhtml"><span class="ui-menuitem-text">Change View</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/cacheManage.xhtml"><span class="ui-menuitem-text">Manage Cache</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/manageLookupValue.xhtml"><span class="ui-menuitem-text">Manage Lookup Value</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/schedulerJobList.xhtml"><span class="ui-menuitem-text">Manage Scheduler Job</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/sysParamList.xhtml"><span class="ui-menuitem-text">Manage System Parameter</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/sysLogList.xhtml"><span class="ui-menuitem-text">View System Log</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/changelog.xhtml"><span class="ui-menuitem-text">View Change Log</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/dbTableList.xhtml"><span class="ui-menuitem-text">View Database Data</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/admin/jenkins.xhtml"><span class="ui-menuitem-text">Jenkins</span></a></li><li class="ui-widget-header ui-corner-all"><h3>User Account</h3></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" role="menuitem" class="ui-menuitem-link ui-corner-all" href="/rich/user/signout.xhtml"><span class="ui-menuitem-text">Logout</span></a></li></ul></div><script id="topForm:j_idt27_s" type="text/javascript">$(function(){PrimeFaces.cw("PlainMenu","widget_topForm_j_idt27",{id:"topForm:j_idt27",overlay:true,my:"left top",at:"left bottom",trigger:"topForm:menuLink",triggerEvent:"click",collision:"flip"});});</script>
				</td>
			</tr>
		</table></div></div><script id="topForm:j_idt21_s" type="text/javascript">$(function(){PrimeFaces.cw("Panel","widget_topForm_j_idt21",{id:"topForm:j_idt21"});});</script><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="-6297270098594114117:1127216928377119976" autocomplete="off" />
</form>
<form id="userInfoForm" name="userInfoForm" method="post" action="/rich/admin/ktListing.xhtml" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="userInfoForm" value="userInfoForm" />
<div class="user-info-panel">Welcome, Wendy Lo</div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="-6297270098594114117:1127216928377119976" autocomplete="off" />
</form></td>
</tr>
<tr>
<td> 
	<style>
		.ui-datatable thead th {
			background: #135178 !important;
			color: #ffffff !important;
			max-height: 200px;
		} 
		.ui-datatable td {
			vertical-align: top;
		} 
	</style></td>
</tr>
<tr>
<td><script type="text/javascript">
	
		function disableOutputDownloadBtn()
		{
			setTimeout(function() {PF('outputDownloadBtn').disable();}, 50);
		}		
		
		function enableOutputDownloadBtn()
		{
			PF('outputDownloadBtn').enable();
		}

		function disableProjectDownloadBtn()
		{
			setTimeout(function() {PF('projectDownloadBtn').disable();}, 50);
		}		
		
		function enableProjectDownloadBtn()
		{
			PF('projectDownloadBtn').enable();
		}
		
		function disableAwardDownloadBtn()
		{
			setTimeout(function() {PF('awardDownloadBtn').disable();}, 50);
		}		
		
		function enableAwardDownloadBtn()
		{
			PF('awardDownloadBtn').enable();
		}
		
		function disablePatentDownloadBtn()
		{
			setTimeout(function() {PF('patentDownloadBtn').disable();}, 50);
		}		
		
		function enablePatentDownloadBtn()
		{
			PF('patentDownloadBtn').enable();
		}
		
	</script></td>
</tr>
<tr>
<td>
<form id="j_idt45" name="j_idt45" method="post" action="/rich/admin/ktListing.xhtml" enctype="application/x-www-form-urlencoded" onkeypress="if (event.keyCode == 13) { document.getElementById('dataForm:searchButton').click(); return false; }" style="width:100%; max-width: 97vw;">
<input type="hidden" name="j_idt45" value="j_idt45" />
<div id="j_idt45:j_idt46:j_idt47" class="ui-accordion ui-widget ui-helper-reset ui-hidden-container" role="tablist" data-widget="multiple"><div id="j_idt45:j_idt46:j_idt47:PersonPanel_header" class="ui-accordion-header ui-helper-reset ui-state-default ui-state-active ui-corner-top" role="tab" aria-expanded="true" aria-selected="true" tabindex="0" style="background:#f6f7f9"><span class="ui-icon ui-icon-triangle-1-s"></span>Search by Person</div><div id="j_idt45:j_idt46:j_idt47:PersonPanel" class="ui-accordion-content ui-helper-reset ui-widget-content" role="tabpanel" aria-hidden="false">
	
			<div class="ui-g input-panel">
	
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Staff Name
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid"><span id="j_idt45:j_idt46:j_idt47:staffName" class="ui-autocomplete"><input id="j_idt45:j_idt46:j_idt47:staffName_input" name="j_idt45:j_idt46:j_idt47:staffName_input" type="text" class="ui-autocomplete-input ui-inputfield ui-widget ui-state-default ui-corner-all " autocomplete="off" onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:staffName&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:staffName&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:staffName&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:staffName&quot;});" /><span id="j_idt45:j_idt46:j_idt47:staffName_panel" class="ui-autocomplete-panel ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay" role="listbox" tabindex="-1"></span></span><script id="j_idt45:j_idt46:j_idt47:staffName_s" type="text/javascript">$(function(){PrimeFaces.cw("AutoComplete","widget_j_idt45_j_idt46_j_idt47_staffName",{id:"j_idt45:j_idt46:j_idt47:staffName",delay:300,scrollHeight:250,appendTo:"@(body)",queryMode:"server",moreText:"...",behaviors:{itemSelect:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:staffName",e:"itemSelect",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:staffName"},ext);}}});});</script>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Academic Staff Rank
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content"><div id="j_idt45:j_idt46:j_idt47:rank" class="ui-selectcheckboxmenu-multiple ui-selectcheckboxmenu ui-widget ui-state-default ui-corner-all " role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible"><input id="j_idt45:j_idt46:j_idt47:rank_focus" name="j_idt45:j_idt46:j_idt47:rank_focus" type="text" readonly="readonly" aria-hidden="true" /></div><div class="ui-helper-hidden"><input id="j_idt45:j_idt46:j_idt47:rank:0" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="AsstProf" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:0">Assistant Professor</label><input id="j_idt45:j_idt46:j_idt47:rank:1" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="AssoProf" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:1">Associate Professor</label><input id="j_idt45:j_idt46:j_idt47:rank:2" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="ChairProf" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:2">Chair Professor</label><input id="j_idt45:j_idt46:j_idt47:rank:3" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="L" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:3">Lecturer (L)</label><input id="j_idt45:j_idt46:j_idt47:rank:4" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="Lect" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:4">Lecturer (Lect)</label><input id="j_idt45:j_idt46:j_idt47:rank:5" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="LI(ETT)" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:5">Lecturer I</label><input id="j_idt45:j_idt46:j_idt47:rank:6" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="LII(ETT)" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:6">Lecturer II</label><input id="j_idt45:j_idt46:j_idt47:rank:7" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="P" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:7">President</label><input id="j_idt45:j_idt46:j_idt47:rank:8" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="PL" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:8">Principal Lecturer</label><input id="j_idt45:j_idt46:j_idt47:rank:9" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="PL(ETT)" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:9">Principal Lecturer</label><input id="j_idt45:j_idt46:j_idt47:rank:10" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="Prof" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:10">Professor</label><input id="j_idt45:j_idt46:j_idt47:rank:11" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="RAssoProf" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:11">Research Associate Professor</label><input id="j_idt45:j_idt46:j_idt47:rank:12" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="RChairProf" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:12">Research Chair Professor</label><input id="j_idt45:j_idt46:j_idt47:rank:13" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="SL" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:13">Senior Lecturer</label><input id="j_idt45:j_idt46:j_idt47:rank:14" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="SLI(ETT)" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:14">Senior Lecturer I</label><input id="j_idt45:j_idt46:j_idt47:rank:15" name="j_idt45:j_idt46:j_idt47:rank" type="checkbox" autocomplete="off" value="SLII(ETT)" data-escaped="true" /><label for="j_idt45:j_idt46:j_idt47:rank:15">Senior Lecturer II</label></div><ul class="ui-selectcheckboxmenu-multiple-container ui-widget ui-inputfield ui-state-default ui-corner-all"></ul><div class="ui-selectcheckboxmenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s"></span></div></div><script id="j_idt45:j_idt46:j_idt47:rank_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectCheckboxMenu","widget_j_idt45_j_idt46_j_idt47_rank",{id:"j_idt45:j_idt46:j_idt47:rank",multiple:true,appendTo:"@(body)",filter:true,filterMatchMode:"contains",behaviors:{toggleSelect:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:rank",e:"toggleSelect",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:rank"},ext);},change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:rank",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:rank"},ext);}}});});</script>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Academic Staff
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content"><div id="j_idt45:j_idt46:j_idt47:acadStaff" class="ui-selectonemenu ui-widget ui-state-default ui-corner-all " aria-owns="j_idt45:j_idt46:j_idt47:acadStaff_panel" style="width:auto; max-width:100%;" role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible"><input id="j_idt45:j_idt46:j_idt47:acadStaff_focus" name="j_idt45:j_idt46:j_idt47:acadStaff_focus" type="text" autocomplete="off" /></div><div class="ui-helper-hidden-accessible"><select id="j_idt45:j_idt46:j_idt47:acadStaff_input" name="j_idt45:j_idt46:j_idt47:acadStaff_input" tabindex="-1" autocomplete="off" aria-hidden="true" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:acadStaff&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:acadStaff&quot;});"><option value="all" data-escape="true">Select All</option><option value="Y" data-escape="true">Yes</option><option value="N" data-escape="true">No</option><option value="empty" selected="selected" data-escape="true">&amp;nbsp;</option></select></div><label id="j_idt45:j_idt46:j_idt47:acadStaff_label" class="ui-selectonemenu-label ui-inputfield ui-corner-all">and</label><div class="ui-selectonemenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s ui-c"></span></div><div id="j_idt45:j_idt46:j_idt47:acadStaff_panel" class="ui-selectonemenu-panel ui-widget ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay"><div class="ui-selectonemenu-items-wrapper" style="max-height:200px"></div></div></div><script id="j_idt45:j_idt46:j_idt47:acadStaff_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectOneMenu","widget_j_idt45_j_idt46_j_idt47_acadStaff",{id:"j_idt45:j_idt46:j_idt47:acadStaff",appendTo:"@(body)",renderPanelContentOnClient:true,behaviors:{change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:acadStaff",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:acadStaff"},ext);}}});});</script>
				</div>
			</div></div><div id="j_idt45:j_idt46:j_idt47:exStaffPanel_header" class="ui-accordion-header ui-helper-reset ui-state-default ui-corner-all" role="tab" aria-expanded="false" aria-selected="false" tabindex="0" style="background:#f6f7f9"><span class="ui-icon ui-icon-triangle-1-e"></span>Search by ex-EduHK staff</div><div id="j_idt45:j_idt46:j_idt47:exStaffPanel" class="ui-accordion-content ui-helper-reset ui-widget-content ui-helper-hidden" role="tabpanel" aria-hidden="true">
	
			<div class="ui-g input-panel">
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Former Staff Name
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid"><span id="j_idt45:j_idt46:j_idt47:exStaffName" class="ui-autocomplete"><input id="j_idt45:j_idt46:j_idt47:exStaffName_input" name="j_idt45:j_idt46:j_idt47:exStaffName_input" type="text" class="ui-autocomplete-input ui-inputfield ui-widget ui-state-default ui-corner-all " autocomplete="off" onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:exStaffName&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:exStaffName&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:exStaffName&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:exStaffName&quot;});" /><span id="j_idt45:j_idt46:j_idt47:exStaffName_panel" class="ui-autocomplete-panel ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay" role="listbox" tabindex="-1"></span></span><script id="j_idt45:j_idt46:j_idt47:exStaffName_s" type="text/javascript">$(function(){PrimeFaces.cw("AutoComplete","widget_j_idt45_j_idt46_j_idt47_exStaffName",{id:"j_idt45:j_idt46:j_idt47:exStaffName",delay:300,scrollHeight:250,appendTo:"@(body)",queryMode:"server",moreText:"...",behaviors:{itemSelect:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:exStaffName",e:"itemSelect",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:exStaffName"},ext);}}});});</script>
				</div>
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Former Staff Number
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content"><input id="j_idt45:j_idt46:j_idt47:formStaffNum" name="j_idt45:j_idt46:j_idt47:formStaffNum" type="text" class="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all " onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:formStaffNum&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:formStaffNum&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:formStaffNum&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:formStaffNum&quot;});" /><script id="j_idt45:j_idt46:j_idt47:formStaffNum_s" type="text/javascript">$(function(){PrimeFaces.cw("InputText","widget_j_idt45_j_idt46_j_idt47_formStaffNum",{id:"j_idt45:j_idt46:j_idt47:formStaffNum"});});</script>
					</div>
			</div></div>
			<div id="j_idt45:j_idt46:j_idt47:riPanel_header" class="ui-accordion-header ui-helper-reset ui-state-default ui-corner-all" role="tab" aria-expanded="false" aria-selected="false" tabindex="0" style="background:#f6f7f9"><span class="ui-icon ui-icon-triangle-1-e"></span>Search by Reports</div>
			<div id="j_idt45:j_idt46:j_idt47:riPanel" class="ui-accordion-content ui-helper-reset ui-widget-content ui-helper-hidden" role="tabpanel" aria-hidden="true">
			<span id="j_idt45:j_idt46:j_idt47:riInnerPanel" class="ui-g input-panel">
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Type of Report
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
				<div id="j_idt45:j_idt46:j_idt47:ktType" class="ui-selectonemenu ui-widget ui-state-default ui-corner-all " aria-owns="j_idt45:j_idt46:j_idt47:ktType_panel" style="width:auto; max-width:100%;" role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible">
				<input id="j_idt45:j_idt46:j_idt47:ktType_focus" name="j_idt45:j_idt46:j_idt47:ktType_focus" type="text" autocomplete="off" /></div><div class="ui-helper-hidden-accessible">
				<select id="j_idt45:j_idt46:j_idt47:ktType_input" name="j_idt45:j_idt46:j_idt47:ktType_input" tabindex="-1" autocomplete="off" aria-hidden="true" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:ktType&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:ktType&quot;,u:&quot;j_idt45:j_idt46:j_idt47:riInnerPanel&quot;});">
				<option value="KT_CNT_PROJ" selected="selected" data-escape="true">CIRD_RP_001 CHRM Academic Professional Output By Department By Individual Staff</option>
				<option value="KT_INNOVATION" data-escape="true">CIRD_RP_004 Report "Summary of Statistics on Research Output Categories"</option>
				<option value="KT_CONSULT" data-escape="true">CIRD_RP_009 CHRM Summary of Statistics on Research Output by Department</option>
				<option value="KT_INV_AWARD" data-escape="true">CIRD_RP_011 Report "Summary of Statistics on Output Categories by Department"</option>
				<option value="KT_IP" data-escape="true">CIRD_RP_012 CHRM Academic and Professional Outputs by DCC</option>
				<option value="KT_EA" data-escape="true">CIRD_RP_015 CHRM Research Output Excel</option>
				<option value="KT_SOC_ENGMT" data-escape="true">CIRD_RP_002 Research Project by Department of staff</option>
				<option value="KT_CPD" data-escape="true">CIRD_RP_005 CHRM Summary of Statistics on Research Projects</option>
				<option value="KT_PROF_CONF" data-escape="true">CIRD_RP_010 CHRM Summary of Statistics on Research Project by Department</option>
				<option value="KT_STAFF_ENGMT" data-escape="true">CIRD_RP_013 CHRM Research and Development Projects by DCC</option>
				<option value="KT_STAFF_ENGMT" data-escape="true">CIRD_RP_014 CHRM Research Grants/Contract Excel</option>
				<option value="KT_STAFF_ENGMT" data-escape="true">CIRD_RP_016 CHRM Number of Active Research Collaborative with Non-local Institutions Excel </option>
				</select>
				</div>
				<label id="j_idt45:j_idt46:j_idt47:ktType_label" class="ui-selectonemenu-label ui-inputfield ui-corner-all">and</label><div class="ui-selectonemenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s ui-c"></span></div><div id="j_idt45:j_idt46:j_idt47:ktType_panel" class="ui-selectonemenu-panel ui-widget ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay"><div class="ui-selectonemenu-items-wrapper" style="max-height:200px"></div></div></div><script id="j_idt45:j_idt46:j_idt47:ktType_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectOneMenu","widget_j_idt45_j_idt46_j_idt47_ktType",{id:"j_idt45:j_idt46:j_idt47:ktType",appendTo:"@(body)",renderPanelContentOnClient:true,behaviors:{change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:ktType",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:ktType",u:"j_idt45:j_idt46:j_idt47:riInnerPanel"},ext);}}});});</script>
				</div>
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Type of View
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content"><div id="j_idt45:j_idt46:j_idt47:viewType" class="ui-selectonemenu ui-widget ui-state-default ui-corner-all " aria-owns="j_idt45:j_idt46:j_idt47:viewType_panel" style="width:auto; max-width:100%;" role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible"><input id="j_idt45:j_idt46:j_idt47:viewType_focus" name="j_idt45:j_idt46:j_idt47:viewType_focus" type="text" autocomplete="off" /></div><div class="ui-helper-hidden-accessible"><select id="j_idt45:j_idt46:j_idt47:viewType_input" name="j_idt45:j_idt46:j_idt47:viewType_input" tabindex="-1" autocomplete="off" aria-hidden="true" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:viewType&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:viewType&quot;,u:&quot;j_idt45:j_idt46:j_idt47:j_idt63&quot;});"><option value="P" selected="selected" data-escape="true">RICH Edition</option><option value="C" data-escape="true">CDCF Snapshot</option><option value="D" data-escape="true">Department Edition</option></select></div><label id="j_idt45:j_idt46:j_idt47:viewType_label" class="ui-selectonemenu-label ui-inputfield ui-corner-all">and</label><div class="ui-selectonemenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s ui-c"></span></div><div id="j_idt45:j_idt46:j_idt47:viewType_panel" class="ui-selectonemenu-panel ui-widget ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay"><div class="ui-selectonemenu-items-wrapper" style="max-height:200px"></div></div></div><script id="j_idt45:j_idt46:j_idt47:viewType_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectOneMenu","widget_j_idt45_j_idt46_j_idt47_viewType",{id:"j_idt45:j_idt46:j_idt47:viewType",appendTo:"@(body)",renderPanelContentOnClient:true,behaviors:{change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:viewType",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:viewType",u:"j_idt45:j_idt46:j_idt47:j_idt63"},ext);}}});});</script>
					</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Report No.
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid"><input id="j_idt45:j_idt46:j_idt47:riNo" name="j_idt45:j_idt46:j_idt47:riNo" type="text" class="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all " onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:riNo&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:riNo&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:riNo&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:riNo&quot;});" /><script id="j_idt45:j_idt46:j_idt47:riNo_s" type="text/javascript">$(function(){PrimeFaces.cw("InputText","widget_j_idt45_j_idt46_j_idt47_riNo",{id:"j_idt45:j_idt46:j_idt47:riNo"});});</script>
				</div><span style="width:100%">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Title
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid"><input id="j_idt45:j_idt46:j_idt47:smartSearch" name="j_idt45:j_idt46:j_idt47:smartSearch" type="text" class="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all " onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:smartSearch&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:smartSearch&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:smartSearch&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:smartSearch&quot;});" /><script id="j_idt45:j_idt46:j_idt47:smartSearch_s" type="text/javascript">$(function(){PrimeFaces.cw("InputText","widget_j_idt45_j_idt46_j_idt47_smartSearch",{id:"j_idt45:j_idt46:j_idt47:smartSearch"});});</script>
					</div></span>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Faculty(s) and Department(s)
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content"><div id="j_idt45:j_idt46:j_idt47:facDept" class="ui-selectcheckboxmenu-multiple ui-selectcheckboxmenu ui-widget ui-state-default ui-corner-all " role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible"><input id="j_idt45:j_idt46:j_idt47:facDept_focus" name="j_idt45:j_idt46:j_idt47:facDept_focus" type="text" readonly="readonly" aria-hidden="true" /></div><div class="ui-helper-hidden"><input id="j_idt45:j_idt46:j_idt47:facDept:0" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FEHD" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:0">Faculty of Education and Human Development (FEHD)</label><input id="j_idt45:j_idt46:j_idt47:facDept:1" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AADO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:1">Alumni Affairs and Development Office (AADO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:2" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AHKS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:2">Academy of Hong Kong Studies (AHKS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:3" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APCLC" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:3">The Joseph Lau Luen Hung Charitable Trust Asia Pacific Centre for Leadership and Change (APCLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:4" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:4">Department of Asian and Policy Studies (APS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:5" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ARC" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:5">Analytics\Assessment Research Centre (ARC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:6" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="C&amp;I" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:6">Department of Curriculum and Instruction (C&amp;I)</label><input id="j_idt45:j_idt46:j_idt47:facDept:7" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCA" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:7">Department of Cultural and Creative Arts (CCA)</label><input id="j_idt45:j_idt46:j_idt47:facDept:8" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCFS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:8">Centre for Child and Family Science (CCFS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:9" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CEES" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:9">Centre for Education in Environmental Sustainability (CEES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:10" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CELT" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:10">Centre for Excellence in Learning and Teaching (CELT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:11" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CGCS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:11">Centre for Greater China Studies (CGCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:12" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CHL" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:12">Department of Chinese Language Studies (CHL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:13" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CLE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:13">Centre for Language in Education (CLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:14" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:14">Communications Office (CO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:15" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPCH" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:15">Centre for Popular Culture in the Humanities (CPCH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:16" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPH" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:16">Centre for Psychosocial Health (CPH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:17" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRCLE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:17">Centre for Research on Chinese Language and Education (CRCLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:18" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRLLS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:18">Centre for Reseach on Linguistics and Language Studies (CRLLS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:19" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRSE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:19">Centre for Religious and Spirituality Education (CRSE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:20" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CSENIE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:20">Centre for Special Educational Needs and Inclusive Education (CSENIE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:21" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ECE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:21">Department of Early Childhood Education (ECE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:22" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ELE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:22">Department of English Language Education (ELE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:23" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:23">Estate Office (EO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:24" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EPL" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:24">Department of Educational Policy and Leadership (EPL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:25" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:25">Finance Office (FO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:26" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GAO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:26">Global Affairs Office (GAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:27" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GEO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:27">General Education Office (GEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:28" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:28">Graduate School (GS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:29" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HPE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:29">Department of Health and Physical Education (HPE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:30" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HRO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:30">Human Resources Office (HRO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:31" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="I-WELL" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:31">Integrated Centre for Wellbeing (I-WELL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:32" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="IE" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:32">Department of International Education (IE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:33" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LCS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:33">Department of Literature and Cultural Studies (LCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:34" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LIB" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:34">Library (LIB)</label><input id="j_idt45:j_idt46:j_idt47:facDept:35" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LML" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:35">Department of Linguistics and Modern Language Studies (LML)</label><input id="j_idt45:j_idt46:j_idt47:facDept:36" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LTTC" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:36">Center for Learning, Teaching and Technology (LTTC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:37" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="MIT" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:37">Department of Mathematics and Information Technology (MIT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:38" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="OCIO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:38">Office of the Chief Information Officer (OCIO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:39" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="P" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:39">President (P)</label><input id="j_idt45:j_idt46:j_idt47:facDept:40" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PROJ-ASP" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:40">Project Aspire (PROJ-ASP)</label><input id="j_idt45:j_idt46:j_idt47:facDept:41" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PS" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:41">Department of Psychological Studies (PS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:42" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCCLLC" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:42">Research Centre for Chinese Literature and Literary Culture (RCCLLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:43" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCTCO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:43">Research Centre for Transmission of Cantonese Opera (RCTCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:44" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RDO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:44">Research and Development Office (RDO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:45" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="REG" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:45">Registry (REG)</label><input id="j_idt45:j_idt46:j_idt47:facDept:46" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SAO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:46">Student Affairs Office (SAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:47" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SCO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:47">Study Centre Office (SCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:48" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SEC" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:48">Department of Special Education and Counselling (SEC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:49" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SES" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:49">Department of Science and Environmental Studies (SES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:50" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SPFEO" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:50">School Partnership and Field Experience Office (SPFEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:51" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SSC" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:51">Department of Social Sciences (SSC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:52" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(AC)&amp;Pr" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:52">Vice President (Academic) and Provost (VP(AC)&amp;Pr)</label><input id="j_idt45:j_idt46:j_idt47:facDept:53" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(ADM)" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:53">Vice President (Administration) (VP(ADM))</label><input id="j_idt45:j_idt46:j_idt47:facDept:54" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(RD)" data-escaped="true" group-label="Faculty of Education and Human Development (FEHD)" /><label for="j_idt45:j_idt46:j_idt47:facDept:54">Vice President (Research and Development) (VP(RD))</label><input id="j_idt45:j_idt46:j_idt47:facDept:55" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FHM" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:55">Faculty of Humanities (FHM)</label><input id="j_idt45:j_idt46:j_idt47:facDept:56" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AADO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:56">Alumni Affairs and Development Office (AADO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:57" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AHKS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:57">Academy of Hong Kong Studies (AHKS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:58" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APCLC" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:58">The Joseph Lau Luen Hung Charitable Trust Asia Pacific Centre for Leadership and Change (APCLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:59" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:59">Department of Asian and Policy Studies (APS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:60" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ARC" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:60">Analytics\Assessment Research Centre (ARC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:61" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="C&amp;I" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:61">Department of Curriculum and Instruction (C&amp;I)</label><input id="j_idt45:j_idt46:j_idt47:facDept:62" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCA" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:62">Department of Cultural and Creative Arts (CCA)</label><input id="j_idt45:j_idt46:j_idt47:facDept:63" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCFS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:63">Centre for Child and Family Science (CCFS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:64" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CEES" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:64">Centre for Education in Environmental Sustainability (CEES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:65" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CELT" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:65">Centre for Excellence in Learning and Teaching (CELT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:66" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CGCS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:66">Centre for Greater China Studies (CGCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:67" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CHL" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:67">Department of Chinese Language Studies (CHL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:68" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CLE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:68">Centre for Language in Education (CLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:69" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:69">Communications Office (CO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:70" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPCH" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:70">Centre for Popular Culture in the Humanities (CPCH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:71" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPH" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:71">Centre for Psychosocial Health (CPH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:72" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRCLE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:72">Centre for Research on Chinese Language and Education (CRCLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:73" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRLLS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:73">Centre for Reseach on Linguistics and Language Studies (CRLLS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:74" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRSE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:74">Centre for Religious and Spirituality Education (CRSE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:75" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CSENIE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:75">Centre for Special Educational Needs and Inclusive Education (CSENIE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:76" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ECE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:76">Department of Early Childhood Education (ECE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:77" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ELE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:77">Department of English Language Education (ELE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:78" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:78">Estate Office (EO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:79" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EPL" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:79">Department of Educational Policy and Leadership (EPL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:80" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:80">Finance Office (FO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:81" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GAO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:81">Global Affairs Office (GAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:82" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GEO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:82">General Education Office (GEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:83" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:83">Graduate School (GS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:84" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HPE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:84">Department of Health and Physical Education (HPE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:85" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HRO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:85">Human Resources Office (HRO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:86" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="I-WELL" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:86">Integrated Centre for Wellbeing (I-WELL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:87" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="IE" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:87">Department of International Education (IE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:88" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LCS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:88">Department of Literature and Cultural Studies (LCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:89" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LIB" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:89">Library (LIB)</label><input id="j_idt45:j_idt46:j_idt47:facDept:90" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LML" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:90">Department of Linguistics and Modern Language Studies (LML)</label><input id="j_idt45:j_idt46:j_idt47:facDept:91" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LTTC" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:91">Center for Learning, Teaching and Technology (LTTC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:92" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="MIT" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:92">Department of Mathematics and Information Technology (MIT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:93" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="OCIO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:93">Office of the Chief Information Officer (OCIO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:94" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="P" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:94">President (P)</label><input id="j_idt45:j_idt46:j_idt47:facDept:95" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PROJ-ASP" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:95">Project Aspire (PROJ-ASP)</label><input id="j_idt45:j_idt46:j_idt47:facDept:96" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PS" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:96">Department of Psychological Studies (PS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:97" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCCLLC" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:97">Research Centre for Chinese Literature and Literary Culture (RCCLLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:98" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCTCO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:98">Research Centre for Transmission of Cantonese Opera (RCTCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:99" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RDO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:99">Research and Development Office (RDO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:100" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="REG" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:100">Registry (REG)</label><input id="j_idt45:j_idt46:j_idt47:facDept:101" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SAO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:101">Student Affairs Office (SAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:102" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SCO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:102">Study Centre Office (SCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:103" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SEC" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:103">Department of Special Education and Counselling (SEC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:104" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SES" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:104">Department of Science and Environmental Studies (SES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:105" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SPFEO" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:105">School Partnership and Field Experience Office (SPFEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:106" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SSC" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:106">Department of Social Sciences (SSC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:107" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(AC)&amp;Pr" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:107">Vice President (Academic) and Provost (VP(AC)&amp;Pr)</label><input id="j_idt45:j_idt46:j_idt47:facDept:108" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(ADM)" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:108">Vice President (Administration) (VP(ADM))</label><input id="j_idt45:j_idt46:j_idt47:facDept:109" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(RD)" data-escaped="true" group-label="Faculty of Humanities (FHM)" /><label for="j_idt45:j_idt46:j_idt47:facDept:109">Vice President (Research and Development) (VP(RD))</label><input id="j_idt45:j_idt46:j_idt47:facDept:110" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FLASS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:110">Faculty of Liberal Arts and Social Sciences (FLASS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:111" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AADO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:111">Alumni Affairs and Development Office (AADO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:112" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AHKS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:112">Academy of Hong Kong Studies (AHKS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:113" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APCLC" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:113">The Joseph Lau Luen Hung Charitable Trust Asia Pacific Centre for Leadership and Change (APCLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:114" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:114">Department of Asian and Policy Studies (APS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:115" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ARC" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:115">Analytics\Assessment Research Centre (ARC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:116" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="C&amp;I" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:116">Department of Curriculum and Instruction (C&amp;I)</label><input id="j_idt45:j_idt46:j_idt47:facDept:117" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCA" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:117">Department of Cultural and Creative Arts (CCA)</label><input id="j_idt45:j_idt46:j_idt47:facDept:118" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCFS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:118">Centre for Child and Family Science (CCFS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:119" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CEES" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:119">Centre for Education in Environmental Sustainability (CEES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:120" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CELT" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:120">Centre for Excellence in Learning and Teaching (CELT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:121" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CGCS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:121">Centre for Greater China Studies (CGCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:122" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CHL" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:122">Department of Chinese Language Studies (CHL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:123" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CLE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:123">Centre for Language in Education (CLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:124" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:124">Communications Office (CO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:125" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPCH" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:125">Centre for Popular Culture in the Humanities (CPCH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:126" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPH" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:126">Centre for Psychosocial Health (CPH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:127" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRCLE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:127">Centre for Research on Chinese Language and Education (CRCLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:128" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRLLS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:128">Centre for Reseach on Linguistics and Language Studies (CRLLS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:129" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRSE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:129">Centre for Religious and Spirituality Education (CRSE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:130" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CSENIE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:130">Centre for Special Educational Needs and Inclusive Education (CSENIE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:131" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ECE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:131">Department of Early Childhood Education (ECE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:132" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ELE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:132">Department of English Language Education (ELE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:133" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:133">Estate Office (EO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:134" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EPL" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:134">Department of Educational Policy and Leadership (EPL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:135" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:135">Finance Office (FO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:136" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GAO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:136">Global Affairs Office (GAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:137" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GEO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:137">General Education Office (GEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:138" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:138">Graduate School (GS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:139" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HPE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:139">Department of Health and Physical Education (HPE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:140" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HRO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:140">Human Resources Office (HRO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:141" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="I-WELL" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:141">Integrated Centre for Wellbeing (I-WELL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:142" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="IE" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:142">Department of International Education (IE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:143" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LCS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:143">Department of Literature and Cultural Studies (LCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:144" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LIB" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:144">Library (LIB)</label><input id="j_idt45:j_idt46:j_idt47:facDept:145" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LML" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:145">Department of Linguistics and Modern Language Studies (LML)</label><input id="j_idt45:j_idt46:j_idt47:facDept:146" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LTTC" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:146">Center for Learning, Teaching and Technology (LTTC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:147" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="MIT" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:147">Department of Mathematics and Information Technology (MIT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:148" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="OCIO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:148">Office of the Chief Information Officer (OCIO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:149" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="P" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:149">President (P)</label><input id="j_idt45:j_idt46:j_idt47:facDept:150" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PROJ-ASP" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:150">Project Aspire (PROJ-ASP)</label><input id="j_idt45:j_idt46:j_idt47:facDept:151" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PS" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:151">Department of Psychological Studies (PS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:152" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCCLLC" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:152">Research Centre for Chinese Literature and Literary Culture (RCCLLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:153" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCTCO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:153">Research Centre for Transmission of Cantonese Opera (RCTCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:154" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RDO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:154">Research and Development Office (RDO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:155" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="REG" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:155">Registry (REG)</label><input id="j_idt45:j_idt46:j_idt47:facDept:156" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SAO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:156">Student Affairs Office (SAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:157" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SCO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:157">Study Centre Office (SCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:158" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SEC" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:158">Department of Special Education and Counselling (SEC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:159" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SES" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:159">Department of Science and Environmental Studies (SES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:160" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SPFEO" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:160">School Partnership and Field Experience Office (SPFEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:161" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SSC" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:161">Department of Social Sciences (SSC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:162" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(AC)&amp;Pr" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:162">Vice President (Academic) and Provost (VP(AC)&amp;Pr)</label><input id="j_idt45:j_idt46:j_idt47:facDept:163" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(ADM)" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:163">Vice President (Administration) (VP(ADM))</label><input id="j_idt45:j_idt46:j_idt47:facDept:164" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(RD)" data-escaped="true" group-label="Faculty of Liberal Arts and Social Sciences (FLASS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:164">Vice President (Research and Development) (VP(RD))</label><input id="j_idt45:j_idt46:j_idt47:facDept:165" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="OTHERS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:165">Others (OTHERS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:166" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AADO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:166">Alumni Affairs and Development Office (AADO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:167" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AHKS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:167">Academy of Hong Kong Studies (AHKS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:168" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APCLC" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:168">The Joseph Lau Luen Hung Charitable Trust Asia Pacific Centre for Leadership and Change (APCLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:169" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:169">Department of Asian and Policy Studies (APS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:170" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ARC" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:170">Analytics\Assessment Research Centre (ARC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:171" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="C&amp;I" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:171">Department of Curriculum and Instruction (C&amp;I)</label><input id="j_idt45:j_idt46:j_idt47:facDept:172" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCA" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:172">Department of Cultural and Creative Arts (CCA)</label><input id="j_idt45:j_idt46:j_idt47:facDept:173" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCFS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:173">Centre for Child and Family Science (CCFS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:174" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CEES" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:174">Centre for Education in Environmental Sustainability (CEES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:175" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CELT" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:175">Centre for Excellence in Learning and Teaching (CELT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:176" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CGCS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:176">Centre for Greater China Studies (CGCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:177" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CHL" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:177">Department of Chinese Language Studies (CHL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:178" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CLE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:178">Centre for Language in Education (CLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:179" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:179">Communications Office (CO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:180" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPCH" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:180">Centre for Popular Culture in the Humanities (CPCH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:181" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPH" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:181">Centre for Psychosocial Health (CPH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:182" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRCLE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:182">Centre for Research on Chinese Language and Education (CRCLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:183" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRLLS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:183">Centre for Reseach on Linguistics and Language Studies (CRLLS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:184" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRSE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:184">Centre for Religious and Spirituality Education (CRSE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:185" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CSENIE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:185">Centre for Special Educational Needs and Inclusive Education (CSENIE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:186" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ECE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:186">Department of Early Childhood Education (ECE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:187" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ELE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:187">Department of English Language Education (ELE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:188" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:188">Estate Office (EO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:189" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EPL" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:189">Department of Educational Policy and Leadership (EPL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:190" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:190">Finance Office (FO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:191" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GAO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:191">Global Affairs Office (GAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:192" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GEO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:192">General Education Office (GEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:193" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:193">Graduate School (GS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:194" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HPE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:194">Department of Health and Physical Education (HPE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:195" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HRO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:195">Human Resources Office (HRO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:196" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="I-WELL" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:196">Integrated Centre for Wellbeing (I-WELL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:197" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="IE" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:197">Department of International Education (IE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:198" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LCS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:198">Department of Literature and Cultural Studies (LCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:199" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LIB" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:199">Library (LIB)</label><input id="j_idt45:j_idt46:j_idt47:facDept:200" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LML" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:200">Department of Linguistics and Modern Language Studies (LML)</label><input id="j_idt45:j_idt46:j_idt47:facDept:201" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LTTC" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:201">Center for Learning, Teaching and Technology (LTTC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:202" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="MIT" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:202">Department of Mathematics and Information Technology (MIT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:203" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="OCIO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:203">Office of the Chief Information Officer (OCIO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:204" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="P" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:204">President (P)</label><input id="j_idt45:j_idt46:j_idt47:facDept:205" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PROJ-ASP" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:205">Project Aspire (PROJ-ASP)</label><input id="j_idt45:j_idt46:j_idt47:facDept:206" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PS" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:206">Department of Psychological Studies (PS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:207" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCCLLC" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:207">Research Centre for Chinese Literature and Literary Culture (RCCLLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:208" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCTCO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:208">Research Centre for Transmission of Cantonese Opera (RCTCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:209" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RDO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:209">Research and Development Office (RDO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:210" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="REG" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:210">Registry (REG)</label><input id="j_idt45:j_idt46:j_idt47:facDept:211" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SAO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:211">Student Affairs Office (SAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:212" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SCO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:212">Study Centre Office (SCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:213" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SEC" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:213">Department of Special Education and Counselling (SEC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:214" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SES" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:214">Department of Science and Environmental Studies (SES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:215" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SPFEO" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:215">School Partnership and Field Experience Office (SPFEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:216" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SSC" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:216">Department of Social Sciences (SSC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:217" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(AC)&amp;Pr" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:217">Vice President (Academic) and Provost (VP(AC)&amp;Pr)</label><input id="j_idt45:j_idt46:j_idt47:facDept:218" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(ADM)" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:218">Vice President (Administration) (VP(ADM))</label><input id="j_idt45:j_idt46:j_idt47:facDept:219" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(RD)" data-escaped="true" group-label="Others (OTHERS)" /><label for="j_idt45:j_idt46:j_idt47:facDept:219">Vice President (Research and Development) (VP(RD))</label><input id="j_idt45:j_idt46:j_idt47:facDept:220" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AADO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:220">Alumni Affairs and Development Office (AADO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:221" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="AHKS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:221">Academy of Hong Kong Studies (AHKS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:222" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APCLC" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:222">The Joseph Lau Luen Hung Charitable Trust Asia Pacific Centre for Leadership and Change (APCLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:223" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="APS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:223">Department of Asian and Policy Studies (APS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:224" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ARC" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:224">Analytics\Assessment Research Centre (ARC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:225" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="C&amp;I" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:225">Department of Curriculum and Instruction (C&amp;I)</label><input id="j_idt45:j_idt46:j_idt47:facDept:226" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCA" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:226">Department of Cultural and Creative Arts (CCA)</label><input id="j_idt45:j_idt46:j_idt47:facDept:227" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CCFS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:227">Centre for Child and Family Science (CCFS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:228" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CEES" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:228">Centre for Education in Environmental Sustainability (CEES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:229" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CELT" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:229">Centre for Excellence in Learning and Teaching (CELT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:230" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CGCS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:230">Centre for Greater China Studies (CGCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:231" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CHL" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:231">Department of Chinese Language Studies (CHL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:232" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CLE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:232">Centre for Language in Education (CLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:233" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:233">Communications Office (CO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:234" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPCH" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:234">Centre for Popular Culture in the Humanities (CPCH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:235" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CPH" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:235">Centre for Psychosocial Health (CPH)</label><input id="j_idt45:j_idt46:j_idt47:facDept:236" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRCLE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:236">Centre for Research on Chinese Language and Education (CRCLE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:237" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRLLS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:237">Centre for Reseach on Linguistics and Language Studies (CRLLS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:238" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CRSE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:238">Centre for Religious and Spirituality Education (CRSE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:239" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="CSENIE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:239">Centre for Special Educational Needs and Inclusive Education (CSENIE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:240" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ECE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:240">Department of Early Childhood Education (ECE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:241" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="ELE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:241">Department of English Language Education (ELE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:242" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:242">Estate Office (EO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:243" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="EPL" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:243">Department of Educational Policy and Leadership (EPL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:244" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="FO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:244">Finance Office (FO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:245" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GAO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:245">Global Affairs Office (GAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:246" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GEO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:246">General Education Office (GEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:247" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="GS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:247">Graduate School (GS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:248" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HPE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:248">Department of Health and Physical Education (HPE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:249" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="HRO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:249">Human Resources Office (HRO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:250" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="I-WELL" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:250">Integrated Centre for Wellbeing (I-WELL)</label><input id="j_idt45:j_idt46:j_idt47:facDept:251" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="IE" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:251">Department of International Education (IE)</label><input id="j_idt45:j_idt46:j_idt47:facDept:252" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LCS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:252">Department of Literature and Cultural Studies (LCS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:253" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LIB" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:253">Library (LIB)</label><input id="j_idt45:j_idt46:j_idt47:facDept:254" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LML" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:254">Department of Linguistics and Modern Language Studies (LML)</label><input id="j_idt45:j_idt46:j_idt47:facDept:255" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="LTTC" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:255">Center for Learning, Teaching and Technology (LTTC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:256" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="MIT" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:256">Department of Mathematics and Information Technology (MIT)</label><input id="j_idt45:j_idt46:j_idt47:facDept:257" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="OCIO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:257">Office of the Chief Information Officer (OCIO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:258" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="P" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:258">President (P)</label><input id="j_idt45:j_idt46:j_idt47:facDept:259" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PROJ-ASP" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:259">Project Aspire (PROJ-ASP)</label><input id="j_idt45:j_idt46:j_idt47:facDept:260" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="PS" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:260">Department of Psychological Studies (PS)</label><input id="j_idt45:j_idt46:j_idt47:facDept:261" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCCLLC" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:261">Research Centre for Chinese Literature and Literary Culture (RCCLLC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:262" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RCTCO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:262">Research Centre for Transmission of Cantonese Opera (RCTCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:263" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="RDO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:263">Research and Development Office (RDO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:264" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="REG" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:264">Registry (REG)</label><input id="j_idt45:j_idt46:j_idt47:facDept:265" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SAO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:265">Student Affairs Office (SAO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:266" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SCO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:266">Study Centre Office (SCO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:267" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SEC" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:267">Department of Special Education and Counselling (SEC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:268" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SES" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:268">Department of Science and Environmental Studies (SES)</label><input id="j_idt45:j_idt46:j_idt47:facDept:269" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SPFEO" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:269">School Partnership and Field Experience Office (SPFEO)</label><input id="j_idt45:j_idt46:j_idt47:facDept:270" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="SSC" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:270">Department of Social Sciences (SSC)</label><input id="j_idt45:j_idt46:j_idt47:facDept:271" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(AC)&amp;Pr" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:271">Vice President (Academic) and Provost (VP(AC)&amp;Pr)</label><input id="j_idt45:j_idt46:j_idt47:facDept:272" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(ADM)" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:272">Vice President (Administration) (VP(ADM))</label><input id="j_idt45:j_idt46:j_idt47:facDept:273" name="j_idt45:j_idt46:j_idt47:facDept" type="checkbox" autocomplete="off" value="VP(RD)" data-escaped="true" group-label="Other" /><label for="j_idt45:j_idt46:j_idt47:facDept:273">Vice President (Research and Development) (VP(RD))</label></div><ul class="ui-selectcheckboxmenu-multiple-container ui-widget ui-inputfield ui-state-default ui-corner-all"></ul><div class="ui-selectcheckboxmenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s"></span></div></div><script id="j_idt45:j_idt46:j_idt47:facDept_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectCheckboxMenu","widget_j_idt45_j_idt46_j_idt47_facDept",{id:"j_idt45:j_idt46:j_idt47:facDept",multiple:true,appendTo:"@(body)",filter:true,filterMatchMode:"contains",behaviors:{toggleSelect:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:facDept",e:"toggleSelect",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:facDept"},ext);},change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:facDept",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:facDept"},ext);}}});});</script>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Date Period
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					From
					<span id="j_idt45:j_idt46:j_idt47:ktDateFrom" class="ui-calendar"><input id="j_idt45:j_idt46:j_idt47:ktDateFrom_input" name="j_idt45:j_idt46:j_idt47:ktDateFrom_input" type="text" class="ui-inputfield ui-widget ui-state-default ui-corner-all " onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:ktDateFrom&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:ktDateFrom&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:ktDateFrom&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:ktDateFrom&quot;});" /></span><script id="j_idt45:j_idt46:j_idt47:ktDateFrom_s" type="text/javascript">$(function(){PrimeFaces.cw("Calendar","widget_j_idt45_j_idt46_j_idt47_ktDateFrom",{id:"j_idt45:j_idt46:j_idt47:ktDateFrom",popup:true,locale:"en",dateFormat:"dd\/mm\/yy",showOn:"button",mask:"dd\/mm\/yyyy",behaviors:{dateSelect:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:ktDateFrom",e:"dateSelect",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:ktDateFrom"},ext);}}});});</script>
					/ To
					<span id="j_idt45:j_idt46:j_idt47:ktDateTo" class="ui-calendar"><input id="j_idt45:j_idt46:j_idt47:ktDateTo_input" name="j_idt45:j_idt46:j_idt47:ktDateTo_input" type="text" class="ui-inputfield ui-widget ui-state-default ui-corner-all " onkeypress="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:ktDateTo&quot;,e:&quot;keypress&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:ktDateTo&quot;});" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:ktDateTo&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:ktDateTo&quot;});" /></span><script id="j_idt45:j_idt46:j_idt47:ktDateTo_s" type="text/javascript">$(function(){PrimeFaces.cw("Calendar","widget_j_idt45_j_idt46_j_idt47_ktDateTo",{id:"j_idt45:j_idt46:j_idt47:ktDateTo",popup:true,locale:"en",dateFormat:"dd\/mm\/yy",showOn:"button",mask:"dd\/mm\/yyyy",behaviors:{dateSelect:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:ktDateTo",e:"dateSelect",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:ktDateTo"},ext);}}});});</script>
				</div></span></div><div id="j_idt45:j_idt46:j_idt47:OtherPanel_header" class="ui-accordion-header ui-helper-reset ui-state-default ui-corner-all" role="tab" aria-expanded="false" aria-selected="false" tabindex="0" style="background:#f6f7f9"><span class="ui-icon ui-icon-triangle-1-e"></span>Sorting</div><div id="j_idt45:j_idt46:j_idt47:OtherPanel" class="ui-accordion-content ui-helper-reset ui-widget-content ui-helper-hidden" role="tabpanel" aria-hidden="true">
	
			<div class="ui-g input-panel">
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Sort by Column
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content"><div id="j_idt45:j_idt46:j_idt47:sortCol" class="ui-selectonemenu ui-widget ui-state-default ui-corner-all " aria-owns="j_idt45:j_idt46:j_idt47:sortCol_panel" style="width:auto; max-width:100%;" role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible"><input id="j_idt45:j_idt46:j_idt47:sortCol_focus" name="j_idt45:j_idt46:j_idt47:sortCol_focus" type="text" autocomplete="off" /></div><div class="ui-helper-hidden-accessible"><select id="j_idt45:j_idt46:j_idt47:sortCol_input" name="j_idt45:j_idt46:j_idt47:sortCol_input" tabindex="-1" autocomplete="off" aria-hidden="true" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:sortCol&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:sortCol&quot;});"><option value="default" selected="selected" data-escape="true">Default</option><option value="formNo" data-escape="true">Form No.</option><option value="fromDate" data-escape="true">Start Date</option><option value="toDate" data-escape="true">End Date</option></select></div><label id="j_idt45:j_idt46:j_idt47:sortCol_label" class="ui-selectonemenu-label ui-inputfield ui-corner-all">and</label><div class="ui-selectonemenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s ui-c"></span></div><div id="j_idt45:j_idt46:j_idt47:sortCol_panel" class="ui-selectonemenu-panel ui-widget ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay"><div class="ui-selectonemenu-items-wrapper" style="max-height:200px"></div></div></div><script id="j_idt45:j_idt46:j_idt47:sortCol_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectOneMenu","widget_j_idt45_j_idt46_j_idt47_sortCol",{id:"j_idt45:j_idt46:j_idt47:sortCol",appendTo:"@(body)",renderPanelContentOnClient:true,behaviors:{change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:sortCol",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:sortCol"},ext);}}});});</script>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Sort Order
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content"><div id="j_idt45:j_idt46:j_idt47:sortOrder" class="ui-selectonemenu ui-widget ui-state-default ui-corner-all " aria-owns="j_idt45:j_idt46:j_idt47:sortOrder_panel" style="width:auto; max-width:100%;" role="combobox" aria-haspopup="listbox" aria-expanded="false"><div class="ui-helper-hidden-accessible"><input id="j_idt45:j_idt46:j_idt47:sortOrder_focus" name="j_idt45:j_idt46:j_idt47:sortOrder_focus" type="text" autocomplete="off" /></div><div class="ui-helper-hidden-accessible"><select id="j_idt45:j_idt46:j_idt47:sortOrder_input" name="j_idt45:j_idt46:j_idt47:sortOrder_input" tabindex="-1" autocomplete="off" aria-hidden="true" onchange="PrimeFaces.ab({s:&quot;j_idt45:j_idt46:j_idt47:sortOrder&quot;,e:&quot;change&quot;,f:&quot;j_idt45&quot;,p:&quot;j_idt45:j_idt46:j_idt47:sortOrder&quot;});"><option value="DESC" selected="selected" data-escape="true">Descending</option><option value="ASC" data-escape="true">Ascending</option></select></div><label id="j_idt45:j_idt46:j_idt47:sortOrder_label" class="ui-selectonemenu-label ui-inputfield ui-corner-all">and</label><div class="ui-selectonemenu-trigger ui-state-default ui-corner-right"><span class="ui-icon ui-icon-triangle-1-s ui-c"></span></div><div id="j_idt45:j_idt46:j_idt47:sortOrder_panel" class="ui-selectonemenu-panel ui-widget ui-widget-content ui-corner-all ui-helper-hidden ui-shadow ui-input-overlay"><div class="ui-selectonemenu-items-wrapper" style="max-height:200px"></div></div></div><script id="j_idt45:j_idt46:j_idt47:sortOrder_s" type="text/javascript">$(function(){PrimeFaces.cw("SelectOneMenu","widget_j_idt45_j_idt46_j_idt47_sortOrder",{id:"j_idt45:j_idt46:j_idt47:sortOrder",appendTo:"@(body)",renderPanelContentOnClient:true,behaviors:{change:function(ext,event) {PrimeFaces.ab({s:"j_idt45:j_idt46:j_idt47:sortOrder",e:"change",f:"j_idt45",p:"j_idt45:j_idt46:j_idt47:sortOrder"},ext);}}});});</script>
				</div>
				
			</div></div><input type="hidden" id="j_idt45:j_idt46:j_idt47_active" name="j_idt45:j_idt46:j_idt47_active" value="0" autocomplete="off" /></div><script id="j_idt45:j_idt46:j_idt47_s" type="text/javascript">$(function(){PrimeFaces.cw("AccordionPanel","multiple",{id:"j_idt45:j_idt46:j_idt47",multiple:true});});</script><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="-6297270098594114117:1127216928377119976" autocomplete="off" />
</form></td>
</tr>
<tr>
<td>
<form id="dataForm" name="dataForm" method="post" action="/rich/admin/ktListing.xhtml" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="dataForm" value="dataForm" />

		<br /><span id="dataForm:j_idt83" class="ui-linkbutton btn-back ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon-left"><a href="/rich/user/dashboard.xhtml"><span class="ui-button-icon-left ui-icon ui-c pi pi-arrow-left"></span><span class="ui-button-text ui-c">Back to Dashboard</span></a></span><script id="dataForm:j_idt83_s" type="text/javascript">$(function(){PrimeFaces.cw("LinkButton","widget_dataForm_j_idt83",{id:"dataForm:j_idt83"});});</script><button id="dataForm:searchButton" name="dataForm:searchButton" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon-left" onclick="PrimeFaces.bcn(this,event,[function(event){PF('searchBut').disable();PF('searchDialog').show();},function(event){PrimeFaces.ab({s:&quot;dataForm:searchButton&quot;,f:&quot;dataForm&quot;,u:&quot;dataForm:resultPanels&quot;,onco:function(xhr,status,args,data){PF('searchBut').enable();PF('searchDialog').hide();;}});return false;}]);" type="submit"><span class="ui-button-icon-left ui-icon ui-c pi pi-search"></span><span class="ui-button-text ui-c">Search</span></button><script id="dataForm:searchButton_s" type="text/javascript">$(function(){PrimeFaces.cw("CommandButton","searchBut",{id:"dataForm:searchButton"});});</script><img id="dataForm:j_idt84" width="10" height="1" alt="" src="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==" /><div id="dataForm:j_idt85" class="ui-dialog ui-widget ui-widget-content ui-corner-all ui-shadow ui-hidden-container"><div class="ui-dialog-content ui-widget-content" id="dataForm:j_idt85_content">
	        <div>
            	<h5>Searching</h5><div id="dataForm:progressBarIndeterminate" class="ui-progressbar ui-widget ui-widget-content ui-corner-all ui-progressbar-indeterminate" style="height:20px; width:250px;"><div class="ui-progressbar-value ui-widget-header ui-corner-all"></div><div class="ui-progressbar-label"></div></div><script id="dataForm:progressBarIndeterminate_s" type="text/javascript">$(function(){PrimeFaces.cw("ProgressBar","widget_dataForm_progressBarIndeterminate",{id:"dataForm:progressBarIndeterminate",initialValue:0,ajax:false,animationDuration:500});});</script>
            </div></div></div><script id="dataForm:j_idt85_s" type="text/javascript">$(function(){PrimeFaces.cw("Dialog","searchDialog",{id:"dataForm:j_idt85",draggable:false,resizable:false,modal:true});});</script><span id="dataForm:resultPanels"></span><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:3" value="-6297270098594114117:1127216928377119976" autocomplete="off" />
</form></td>
</tr>
</tbody>
</table>
</body>
</html>