<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="userId" value="#{userAcsView.selectedUserId}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="#{bundle['action.new.x']}">
   				<f:param value="User Access Right" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		
		<p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
			<p:autoUpdate/>
		</p:messages>
		
		<h:panelGrid columns="2" styleClass="edit-panel">
		
			<!-- UserId -->
			<h:panelGroup>
				User ID
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="userId"
							 value="#{userAcsView.selectedUserAcs.userId}"
							 maxlength="#{userAcsView.getFieldMaxLength('userId')}">
					<f:validator validatorId="hk.eduhk.rich.param.UserAcsValidator"/>
					<f:attribute name="validateField" value="userId" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="userIdMsg" for="userId"/>
				</div>
			</h:panelGroup>
		
			<!-- Course Subject Code -->
			<h:panelGroup>
				Course Subject Code
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="crseSubjCode"
							 value="#{userAcsView.selectedUserAcs.crseSubjCode}" 
							 maxlength="#{userAcsView.getFieldMaxLength('crseSubjCode')}">
					<f:validator validatorId="hk.eduhk.rich.param.UserAcsValidator"/>
					<f:attribute name="validateField" value="crseSubjCode" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="crseSubjCodeMsg" for="crseSubjCode"/>
				</div>
			</h:panelGroup>
		
			<!-- Course Number -->
			<h:panelGroup>
				Course Number
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="crseNum"
							 value="#{userAcsView.selectedUserAcs.crseNum}"
							 maxlength="#{userAcsView.getFieldMaxLength('crseNum')}" >
					<f:validator validatorId="hk.eduhk.rich.param.UserAcsValidator"/>
					<f:attribute name="validateField" value="crseNum" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="crseNumMsg" for="crseNum"/>
				</div>
			</h:panelGroup>
			
		</h:panelGrid>
		
		<br/>
		<h:panelGroup styleClass="button-panel">
		
			<p:commandButton value="#{bundle['action.create']}"
					  		 action="#{userAcsView.insertUserAcs}"
					  		 update="@form">
			</p:commandButton>
			
			<h:outputText value="&#160;"/>
			<p:button value="#{bundle['action.back']}" outcome="userAcsList?userId=#{userAcsView.selectedUserId}"/>
			
		</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>