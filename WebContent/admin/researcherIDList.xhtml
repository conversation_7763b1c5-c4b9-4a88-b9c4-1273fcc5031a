<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
	  			xmlns:pe="http://primefaces.org/ui/extensions"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<ui:define name="mainContent">
	
	<p:panel id="contentPanel">
		<h:panelGroup styleClass="admin-content-title">Manage Researcher IDs</h:panelGroup>
		<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
		
		<h:form id="dataForm">
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
			<p:dataTable   id="dataTable" var="data" value="#{researcherInfoView.researcherInfoList}" 
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							paginator="true" rows="10" currentPageReportTemplate="{startRecord}-{endRecord} of {totalRecords} records"
							rowsPerPageTemplate="10,15,20,25"
						sortMode="single" rowKey="#{data.pid}" tableStyle="table-layout: fixed;"
                     	selection="#{researcherInfoView.selectedresearcherInfo}" selectionMode="single">
			    
			    <p:column headerText="Pid" id="researchList_pid" style="width:6rem" sortBy="#{data.pid}" filterBy="#{data.pid}" filterMatchMode="contains" >
	                 <h:outputText value="#{data.pid}"/>
           		</p:column>
           		
           		 <p:column headerText="English Name" id="researchList_name" sortBy="#{data.fullname}" filterBy="#{data.fullname}" filterMatchMode="contains" >
	             	<h:outputText value="#{data.fullname}"/>
	             </p:column>
           		<p:column headerText="Email" id="researchList_email" sortBy="#{data.email}" filterBy="#{data.email}" filterMatchMode="contains">
        			<h:outputText value="#{data.email}"/>
           		</p:column>
           		<p:column headerText="ORCiD"  id="researchList_Orcid" sortBy="#{data.staffInfo.orcid}" filterBy="#{data.staffInfo.orcid}"  filterMatchMode="contains">
	                 <h:outputText value="#{data.staffInfo.orcid}"/>
	            </p:column>
           		
           		<p:column headerText="Scopus Author ID [1]"  id="researchList_sc_author_id_1" sortBy="#{data.staffInfo.scopus_id_1}" filterBy="#{data.staffInfo.scopus_id_1}" filterMatchMode="contains">
	                 <h:outputText value="#{data.staffInfo.scopus_id_1}"/>
           		</p:column>
           		
           		<p:column headerText="Scopus Author ID [2]"  id="researchList_sc_author_id_2" sortBy="#{data.staffInfo.scopus_id_2}" filterBy="#{data.staffInfo.scopus_id_2}" filterMatchMode="contains">
	                 <h:outputText value="#{data.staffInfo.scopus_id_2}"/>
           		</p:column>
           		
           		<p:column headerText="ResearchID" id="researchList_researcherid" sortBy="#{data.staffInfo.researcherid}" filterBy="#{data.staffInfo.researcherid}" filterMatchMode="contains">
	                   <h:outputText value="#{data.staffInfo.researcherid}"/>
           		</p:column>
           		
           		<p:column headerText="Last Updated Datetime" id="researchList_last_updated_date" sortBy="#{data.staffInfo.timestamp}">
						<h:outputText value="#{data.staffInfo.timestamp}">
							<f:convertDateTime dateStyle="full" pattern="dd/MM/yyyy HH:mm:ss" timeZone="Hongkong"/>
						</h:outputText>
           		</p:column>	
			</p:dataTable>
			<p:contextMenu for="dataTable" widgetVar="cMenu">
	        	<p:menuitem value="#{bundle['action.edit']}" action="#{researcherInfoView.gotoEditPage()}" />
	    	</p:contextMenu>
		</h:form>
		<br/>
	</p:panel>
	</ui:define>
		
</ui:composition>