<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="jobName" value="#{schedulerView.jobName}" />
	</f:metadata>	

	<ui:define name="mainContent"> 
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="#{schedulerView.selectedJob.jobName != null ? bundle['action.edit.x'] : bundle['action.new.x']}">
   				<f:param value="Scheduler Job" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		<h:panelGrid columns="2" styleClass="edit-panel">
		
			<h:panelGroup>
				Job Name
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="jobName"
							 value="#{schedulerView.selectedJob.jobName}" 
							 maxlength="#{schedulerView.getFieldMaxLength('jobName')}"  
							 rendered="#{schedulerView.selectedJob.jobName == null}"
							 style="width:15em;">
					<f:validator validatorId="hk.eduhk.rich.scheduler.SchedulerJobValidator"/>
					<f:attribute name="validateField" value="jobName" />
				</p:inputText>
							 
				<h:outputText value="#{schedulerView.selectedJob.jobName}" 
							  rendered="#{schedulerView.selectedJob.jobName != null}"/>
        		<div class="input-validation-message">
					<p:message id="jobNameMsg" for="jobName"/>
				</div>
			</h:panelGroup>


			<h:panelGroup>
				Class Name
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="className"
							 value="#{schedulerView.selectedJob.className}" 
							 maxlength="#{schedulerView.getFieldMaxLength('className')}"  
							 style="width:80%;">
					<f:validator validatorId="hk.eduhk.rich.scheduler.SchedulerJobValidator"/>
					<f:attribute name="validateField" value="className" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="classNameMsg" for="className"/>
				</div>
			</h:panelGroup>
			

			<h:panelGroup>
				Parameters
			</h:panelGroup>
			<h:panelGroup>
				<p:inputTextarea id="parameters"
								 value="#{schedulerView.selectedJob.parameters}"
								 binding="#{parametersObj}"
								 maxlength="#{schedulerView.getFieldMaxLength('parameters')}"
								 counter="parametersCharCount"
								 counterTemplate="{0} characters remaining"
								 style="width:95%;">
					<f:validator validatorId="hk.eduhk.rich.scheduler.SchedulerJobValidator"/>
					<f:attribute name="validateField" value="parameters" />
					<f:ajax event="change" render="@this"/>
				</p:inputTextarea>
				<div><h:outputText id="parametersCharCount"/></div>
				<h:panelGroup id="parametersMsg">
	        		<h:panelGroup class="input-validation-message" rendered="#{!parametersObj.valid}">
						<p:message for="parameters"/>
					</h:panelGroup>
				</h:panelGroup>
			</h:panelGroup>
			

			<h:panelGroup>
				Description
			</h:panelGroup>
			<h:panelGroup>
				<p:inputTextarea id="description"
								 value="#{schedulerView.selectedJob.description}"
								 binding="#{descriptionObj}"
								 maxlength="#{schedulerView.getFieldMaxLength('description')}"
								 counter="descriptionCharCount"
								 counterTemplate="{0} characters remaining"
								 style="width:95%;">
					<f:validator validatorId="hk.eduhk.rich.scheduler.SchedulerJobValidator"/>
					<f:attribute name="validateField" value="description" />
					<f:ajax event="change" render="@this"/>
				</p:inputTextarea>
				<div><h:outputText id="descriptionCharCount"/></div>
				<h:panelGroup id="descriptionMsg">
	        		<h:panelGroup class="input-validation-message" rendered="#{!descriptionObj.valid}">
						<p:message for="description"/>
					</h:panelGroup>
				</h:panelGroup>
			</h:panelGroup>
			

			<h:panelGroup>
				Cron Expression
			</h:panelGroup>
			<h:panelGroup> 
				<p:inputText id="cronExpression"
							 value="#{schedulerView.selectedJob.cronExpression}" 
							 maxlength="#{schedulerView.getFieldMaxLength('cronExpression')}">
					<f:validator validatorId="hk.eduhk.rich.scheduler.SchedulerJobValidator"/>
					<f:attribute name="validateField" value="cronExpression" />
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="cronExpressionMsg" for="cronExpression"/>
				</div>
			</h:panelGroup>
			

			<h:panelGroup>
				Loggable
			</h:panelGroup>
			<h:panelGroup>
				<p:selectOneMenu id="loggable" 
							 	 value="#{schedulerView.selectedJob.loggable}">
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="#{true}"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="#{false}"/>
				</p:selectOneMenu>
        		<div class="input-validation-message">
					<p:message id="loggableMsg" for="loggable"/>
				</div>
			</h:panelGroup>
			
		</h:panelGrid>
		
		<br/>
		<h:panelGroup styleClass="button-panel">
		
			<!-- For optimistic locking -->
			<!-- 
			<h:inputHidden id="timestamp" value="#{schedulerView.selectedJob.timestamp}">
				<f:convertDateTime dateStyle="full" type="both" />
			</h:inputHidden>
			 -->
		
			<p:commandButton value="#{bundle['action.create']}"
					  		 action="#{schedulerView.updateSchedulerJob}"
					  		 update="@form" 
					  		 rendered="#{schedulerView.selectedJob.jobName == null}">
			</p:commandButton>
			
			<p:commandButton value="#{bundle['action.update']}" 
					  		 action="#{schedulerView.updateSchedulerJob}" 
					  		 update="@form" 
					  		 rendered="#{schedulerView.selectedJob.jobName != null}">
			</p:commandButton>
			
			<h:outputText value="&#160;"/>
			<p:button value="#{bundle['action.back']}" outcome="schedulerJobList"/>
			
		</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>