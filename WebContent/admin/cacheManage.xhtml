<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<!-- 
	<f:metadata>
		<f:viewParam name="userId" value="#{cacheManageView.selectedUserId}" />
	</f:metadata>
	 -->
				
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
	
		<h:form id="accessForm">

			<span class="admin-content-title">Manage Cache</span>
		
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
				<p:autoUpdate/>
			</p:messages>
		
			
			<h:panelGroup id="cacheListPanel">

				<!-- Function List -->			
				<h:panelGroup id="cacheGrid">
					<br/><br/>
					<p:selectManyCheckbox id="userGroup"
										  value="#{cacheManageView.selectedCacheNameList}"
										  layout="grid" columns="1">
						<f:selectItems value="#{cacheManageView.cacheNameList}" 
									   var="cacheName" itemLabel="#{cacheName}" itemValue="#{cacheName}" />
					</p:selectManyCheckbox>
				</h:panelGroup>
							
				<!-- Button Panel -->
				<h:panelGrid id="buttonPanel">
					<br/>
					<h:panelGroup styleClass="button-panel">
						<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
						<p:commandButton value="Clear Selected" action="#{cacheManageView.clearSelected}" update="cacheGrid" style="margin-right:5px;"/>
						<p:commandButton value="Select All" action="#{cacheManageView.selectAll()}" update="cacheGrid" style="margin-right:5px;"/>
						<p:commandButton value="Unselect All" action="#{cacheManageView.unselectAll()}" update="cacheGrid" style="margin-right:5px;"/>
					</h:panelGroup>
				</h:panelGrid>
				
			</h:panelGroup>
			
		</h:form>
		
		<br/>
	
	</p:panel>
	</ui:define>
</ui:composition>