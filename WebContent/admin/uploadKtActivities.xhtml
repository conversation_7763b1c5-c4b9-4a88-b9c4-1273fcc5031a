<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
				
	
	<ui:define name="mainContent">
	<p:importConstants type="hk.eduhk.rich.Constant" var="Constant"/>
	
	<p:panel id="contentPanel">
		<style type="text/css">
		    .cheap {
		        background-color: #9e9e9e1a !important;
		    }
		</style>	
		<h:form id="dataForm">
			<span class="admin-content-title"><i class="fa-solid fa-timeline"></i> Upload KT Activities</span>
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			</p:messages>
			<div class="ui-g">
				<h:panelGroup id="dataPanel" style="width:99%">
				
					<p:dataTable id="dataTable" 
									 var="p"
									 value="#{uploadKtActView.periodList}"
									 rowKey="#{p.period_id}"
									 reflow="true"
									 rowStyleClass="#{p.is_current eq true ? 'cheap' : null}"
			                  		 >
						<p:column style="width:2rem">
			                <p:rowToggler/>
			            </p:column>
						<p:column headerText="Period From Date" sortBy="#{p.date_from}" width="12%">
							<h:outputText value="#{p.date_from}" >
								<f:convertDateTime pattern="dd/MM/yyyy" />
							</h:outputText>
						</p:column>
						
		                <p:column headerText="Period To Date" sortBy="#{p.date_to}" width="12%">
							<h:outputText value="#{p.date_to}" >
								<f:convertDateTime pattern="dd/MM/yyyy" />
							</h:outputText>
						</p:column>
						
						<p:column headerText="Description" sortBy="#{p.period_desc}" >
							<h:outputText value="#{p.period_desc}" />
						</p:column>
						
						<p:column headerText="Current Period" sortBy="#{p.is_current}" width="12%">
							<i class="fa-regular fa-circle-check fa-xl" style="#{p.is_current?'color:#0F9D58;':'display:none;'}"></i>
						</p:column>
						
						<p:rowExpansion>
							<div class="ui-g">
								<p:commandButton 	value="Export KT Activities" 
				                					icon="fas fa-download" 
				                					ajax="false" 
				                					action="#{uploadKtActView.exportKtReport(p)}"
				                					style="margin-bottom:20px;margin-right:20px;"/>
				                <p:commandButton 	value="Upload KT Activities" 
				                					icon="fas fa-upload" 
				                					style="margin-bottom:20px;"
				                					oncomplete="PF('uploadPanel_#{p.period_id}').toggle()"/>
				                					<br/><br/>
				               <p:panel 	id="uploadPanel" widgetVar="uploadPanel_#{p.period_id}" closable="true" toggleable="true" collapsed="true"
											style="padding:0; border-style: none;"
											class="ui-g-12">
									<p:panel>
										<div class="ui-g" style="padding-left:0em;">
											<div>
												File 
											</div>
											<div class="ui-g-12 ui-md-10 ui-lg-11 content">
											
												<p:fileUpload id="uploadedFile"
															  update="dataForm:messages content"
															  auto="true"
															  mode="advanced"
															  listener="#{uploadKtActView.fileUploadListener}">
													 <f:attribute name="periodId" value="#{p.period_id}" />
												</p:fileUpload>
												
												<p:panel id="content" style="border:none" >
													<h:outputText value="#{uploadKtActView.getUploadedFileName(uploadKtActView.uploadedFileMap[p.period_id])}" /> 
										        </p:panel>
											</div>
											 
											<div class="ui-g-12">
												<!--
													Must use clientId instead of uploadedFile in "process" attribute
													The description cannot be saved if use clientId. The reason behind is still unknown.
												 -->
												<p:commandButton id="uploadBtn"
																 value="#{bundle['action.upload']}"
																 partialSubmit="true"
																 action="#{uploadKtActView.uploadFile(p.period_id)}"
																 ajax="false"/>
											</div>
										</div>
									</p:panel>
								</p:panel>		
				               <br/><br/>
								<div class="ui-g">
								<p:dataTable var="uploadStatus" value="#{uploadKtActView.uploadStatusMap[p.period_id]}">
									<p:column style="width:5%" styleClass="table-column-center">
										<f:facet name="header">File Name</f:facet>
										<h:outputText value="#{uploadStatus.fileName}"/>
									</p:column>
									
									<p:column style="width:5%" styleClass="table-column-center">
										<f:facet name="header">Last Modified By</f:facet>
										<h:outputText value="#{uploadStatus.userstamp}"/>
									</p:column>
									
									<p:column style="width:5%" styleClass="table-column-center">
										<f:facet name="header">Date</f:facet>
										<h:outputText value="#{uploadStatus.timestamp}">
											<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT_MM}" />
										</h:outputText>
									</p:column>
								</p:dataTable>
								</div>
							</div>
			            </p:rowExpansion>
		            </p:dataTable>
 
	        		<br/>
	            </h:panelGroup>
			</div>
			
			<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Submit" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
			</p:confirmDialog>
				
		</h:form>
		
		<p:button value="#{bundle['action.back']}" outcome="/user/dashboard" icon="pi pi-arrow-left" styleClass="btn-back"/>
	</p:panel>
			
	</ui:define>
</ui:composition>