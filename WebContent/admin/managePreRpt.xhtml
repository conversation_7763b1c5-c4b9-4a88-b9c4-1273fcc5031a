<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title"><i class="fas fa-pencil-square"></i> Manage Pre-defined Reports</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:dataTable id="dataTable" var="data" value="#{preRptView.allPreRptList}" editable="true" sortMode="single"
						rowKey="#{data.rpt_id}" tableStyle="table-layout: fixed;"
						selection="#{preRptView.selectedPreRpt}" selectionMode="single"
                     	paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						paginator="true"
						rows="30"
						currentPageReportTemplate="{startRecord}-{endRecord} of {totalRecords} records">
			<p:ajax event="rowEdit" listener="#{preRptView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{preRptView.onRowCancel}" update=":msgs"/>
			
			<p:column headerText="Report ID" id="rpt_id" sortBy="#{data.rpt_id}" filterBy="#{data.rpt_id}" filterMatchMode="contains" style="width:6rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_id}" style="width:100%" valueChangeListener="#{preRptView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
			<p:column headerText="Report Code" id="rpt_code" sortBy="#{data.rpt_code}" filterBy="#{data.rpt_code}" filterMatchMode="contains" style="width:8rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_code}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_code}" style="width:100%" valueChangeListener="#{preRptView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Description" id="rpt_desc" sortBy="#{data.rpt_desc}" filterBy="#{data.rpt_desc}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_desc}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_desc}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Report Cat" id="rpt_cat" sortBy="#{data.rpt_cat}" filterBy="#{data.rpt_cat}" filterMatchMode="contains" style="width:6rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_cat}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_cat}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Report Type" id="rpt_type" sortBy="#{data.rpt_type}" filterBy="#{data.rpt_type}" filterMatchMode="contains" style="width:4rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_type}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_type}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Note" id="rpt_note" sortBy="#{data.rpt_note}" filterBy="#{data.rpt_note}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_note}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_note}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Report File Name" id="rpt_file_name" sortBy="#{data.rpt_file_name}" filterBy="#{data.rpt_file_name}" filterMatchMode="contains" style="width:10rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.rpt_file_name}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.rpt_file_name}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Print Order" id="print_order" sortBy="#{data.print_order}" filterBy="#{data.print_order}" filterMatchMode="contains" style="width:4rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.print_order}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.print_order}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Enabled" id="enabled_flag" sortBy="#{data.enabled_flag}" filterBy="#{data.enabled_flag}" filterMatchMode="contains" style="width:4rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.enabled_flag}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.enabled_flag}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Is RDO" id="is_rdo" sortBy="#{data.is_rdo}" filterBy="#{data.is_rdo}" filterMatchMode="contains" style="width:4rem">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.is_rdo}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.is_rdo}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column style="width:2rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="dataTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:dataTable :msgs" action="#{preRptView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
	    <h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:dataTable" listener="#{preRptView.reloadPreRptList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{preRptView.selectedPreRpt.rpt_code}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{preRptView.deletePreRpt}"
								 update=":dataForm:dataTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>