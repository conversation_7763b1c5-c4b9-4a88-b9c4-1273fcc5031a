<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:o="http://omnifaces.org/ui"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
	</f:metadata>

	<ui:define name="mainContent"> 
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<h:form style="width:100%; max-width: 97vw;"
			onkeypress="if (event.keyCode == 13) { document.getElementById('dataForm:searchButton').click(); return false; }" >
		<component:riSearchPanel searchPanel="#{searchProfileView.searchPanel}"
								 showRiPanel="false"
								 showOtherPanel="false"
								 isAcadStaff="false"/>
	</h:form>
	<h:form id="dataForm">
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="searchButton" value="Search" update="resultPanels" icon="pi pi-search"
						 widgetVar="searchBut" onclick="PF('searchBut').disable();PF('searchDialog').show();"
						 oncomplete="PF('searchBut').enable();PF('searchDialog').hide();"
						 actionListener="#{searchProfileView.setFirstSearch(true)}" ></p:commandButton>
		<div style="color: #00a2c7; margin-left:5px; vertical-align:middle; line-height:2; font-weight:700; display:inline;">Please input at least one filter criteria before search</div>
		<p:dialog widgetVar="searchDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
	            <div>
	            	<h5>Searching</h5>
        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
	            </div>
	    </p:dialog>
 
		<h:panelGroup id="resultPanels" style="width:100%">
			<h:panelGroup id="staffResultPanel"
						  rendered="#{searchProfileView.isFirstSearch() and (searchProfileView.isExStaff() == 0)}" >
				<p:dataTable id="staffDataTable"
					 value="#{searchProfileView.getStaffList()}" 
					 var="staff"
					 stripedRows="true"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					
					<p:column width="10%">
						<f:facet name="header">Photo</f:facet>
						<h:panelGroup rendered="#{searchProfileView.infoMap.get(staff.getStaff_number()).photoFile != null}" >
							<img src="data:image/jpg;base64,#{searchProfileView.infoMap.get(staff.getStaff_number()).photoFile}" width="120px"/>
						</h:panelGroup>
						<h:panelGroup rendered="#{searchProfileView.infoMap.get(staff.getStaff_number()).photoFile == null}" >
							<h:outputText value="Not having photo"/>
						</h:panelGroup>
					</p:column>
					
					<p:column width="45%">
						<f:facet name="header">Name</f:facet>
						<h:outputText style="font-size:28px; font-weight:700;" value="#{staff.title} #{staff.fullname} #{staff.chinesename}"/>
						<br/>
						<h:outputText value="#{searchProfileView.iUserInfoStringMap.get(staff.getStaff_number())}" escape="false"/>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Action</f:facet>
						<p:linkButton styleClass="default-linkButton ui-button-info"  outcome="/user/manageInfo" value="Manage Info." icon="fa fa-fw fa-user-circle" target="_blank" 
											rendered="#{staff.acad_staff_ind eq 'Y' and manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-info"  outcome="/user/manageProfile" value="Manage Profile Preference" icon="fa fa-fw fa-power-off" target="_blank" 
											rendered="#{staff.acad_staff_ind eq 'Y' and manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-info"  outcome="/user/manageAsst" value="Manage Assistant Right " icon="fa fa-fw fa-users" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/importRI" value="Import RI" icon="fa fa-fw fa-file-import" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageOutput" value="Manage Research Outputs" icon="fa fa-fw fa-chart-pie" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageProject" value="Manage Projects" icon="fa fa-fw fa-project-diagram" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageAward" value="Manage Prizes and Awards" icon="fa fa-fw fa-trophy" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/managePatent" value="Manage Patents" icon="fa fa-fw fa-award" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/consentRI" value="Consent RI" icon="fa fa-fw fa-check-circle" target="_blank"
											rendered="#{manageRIView.getIsRdoAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-success"  outcome="/user/ktFormSum" value="Manage KT Activities" icon="fa fa-fw fa-chalkboard-user" target="_blank">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageRAEOutput" value="Manage Rae Research Outputs" icon="fa fa-fw fa-chart-pie" target="_blank"
											rendered="#{manageRIView.getIsRaeAdmin()}">
							<f:param name="pid" value="#{staff.pid}"/>
						</p:linkButton>
					</p:column>
					
					<!--  <p:column width="3em;">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{staff.staff_number}" />
					</p:column>-->
					
				</p:dataTable>
			</h:panelGroup>
			
			<h:panelGroup id="exStaffResultPanel"
						  rendered="#{searchProfileView.isFirstSearch() and (searchProfileView.isExStaff() == 1)}" >
				<p:dataTable id="exStaffDataTable"
					 value="#{searchProfileView.getExStaffList()}"
					 var="exStaff"
					 style="width:1500px"
					 styleClass="default-dataTable"
					 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					
					<p:column width="10%">
						<f:facet name="header">Photo</f:facet>
						<h:panelGroup rendered="#{searchProfileView.infoMap.get(exStaff.getStaff_number()).photoFile != null}" >
							<img src="data:image/jpg;base64,#{searchProfileView.infoMap.get(exStaff.getStaff_number()).photoFile}" width="120px"/>
						</h:panelGroup>
						<h:panelGroup rendered="#{searchProfileView.infoMap.get(exStaff.getStaff_number()).photoFile == null}" >
							<h:outputText value="Not having photo"/>
						</h:panelGroup>
					</p:column>
					
					<p:column width="45%">
						<f:facet name="header">Name</f:facet>
						<h:outputText style="font-size:28px; font-weight:700;" value="#{exStaff.title} #{exStaff.fullname}"/>
						<br/>
						<h:outputText value="#{searchProfileView.iUserInfoStringMap.get(exStaff.getStaff_number())}" escape="false"/>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Action</f:facet>
						<!-- <p:linkButton styleClass="default-linkButton"  outcome="/user/manageAsst" value="Manage Assistant Right" icon="fa fa-fw fa-users" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<br/>-->
						<p:linkButton styleClass="default-linkButton"  outcome="/user/manageInfo" value="Manage Info." icon="fa fa-fw fa-user-circle" target="_blank" rendered="#{searchProfileView.infoMap.get(exStaff.getStaff_number()) != null}">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton"  outcome="/user/importRI" value="Import RI" icon="fa fa-fw fa-file-import" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton"  outcome="/user/manageOutput" value="Manage Research Outputs" icon="fa fa-fw fa-chart-pie" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton"  outcome="/user/manageProject" value="Manage Projects" icon="fa fa-fw fa-project-diagram" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<br/>
						<p:linkButton styleClass="default-linkButton"  outcome="/user/manageAward" value="Manage Prizes and Awards" icon="fa fa-fw fa-trophy" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton"  outcome="/user/managePatent" value="Manage Patents" icon="fa fa-fw fa-award" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
						<p:linkButton styleClass="default-linkButton"  outcome="/user/consentRI" value="Consent RI" icon="fa fa-fw fa-check-circle" target="_blank">
							<f:param name="pid" value="#{exStaff.pid}"/>
						</p:linkButton>
					</p:column>
					
					<!--  <p:column width="3em;">
						<f:facet name="header">Staff Number</f:facet>
						<h:outputText value="#{staff.staff_number}" />
					</p:column>-->
					
				</p:dataTable>
			</h:panelGroup>
			
			
		</h:panelGroup>
			
	</h:form>
   </ui:define>
</ui:composition>