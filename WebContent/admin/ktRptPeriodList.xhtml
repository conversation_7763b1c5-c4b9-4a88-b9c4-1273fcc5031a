<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
				
	
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
		<style type="text/css">
		    .cheap {
		        background-color: #9e9e9e1a !important;
		    }
		</style>	
		<h:form id="dataForm">
			<span class="admin-content-title"><i class="fa-solid fa-timeline"></i> KT Reporting Period List</span>
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			</p:messages>

			<div class="ui-g">
				<h:panelGroup id="dataPanel" style="width:99%">
					<p:linkButton outcome="ktRptPeriodEdit" value="Add New Period" style="margin-right:20px;">
				    </p:linkButton>
					<br/><br/>
					<p:dataTable id="dataTable" stickyHeader="true" stickyTopAt=".layout-topbar"
									 var="p"
									 value="#{manageKtReportView.periodList}"
									 rowKey="#{p.period_id}"
									 reflow="true"
									 rowStyleClass="#{p.is_current eq true ? 'cheap' : null}"
			                  		 >
						<p:column style="width:2rem">
			                <p:rowToggler/>
			            </p:column>
						<p:column headerText="Period From Date" sortBy="#{p.date_from}" width="12%">
							<h:outputText value="#{p.date_from}" >
								<f:convertDateTime pattern="dd/MM/yyyy" />
							</h:outputText>
						</p:column>
						
		                <p:column headerText="Period To Date" sortBy="#{p.date_to}" width="12%">
							<h:outputText value="#{p.date_to}" >
								<f:convertDateTime pattern="dd/MM/yyyy" />
							</h:outputText>
						</p:column>
						
						<p:column headerText="Description" sortBy="#{p.period_desc}" >
							<h:outputText value="#{p.period_desc}" />
						</p:column>
						
						<p:column headerText="Chart Description" sortBy="#{p.chart_desc}" >
							<h:outputText value="#{p.chart_desc}" />
						</p:column>
						
						<p:column headerText="Current Period" sortBy="#{p.is_current}" width="12%">
							<i class="fa-regular fa-circle-check fa-xl" style="#{p.is_current?'color:#0F9D58;':'display:none;'}"></i>
						</p:column>
						
						<p:column width="6em">
							<p:commandButton  action="#{manageKtReportView.gotoPeriodEditPage(p.period_id)}" class="ui-button-success rounded-button" icon="fa-regular fa-pen-to-square" style="margin-right:4px;"></p:commandButton>	
							<p:commandButton  action="#{manageKtReportView.deletePeriod(p.period_id)}" class="ui-button-warning rounded-button" icon="fa-regular fa-trash-can"
									  				 update=":dataForm:dataTable" immediate="true">
									   <p:confirm header="Confirmation"
													message="Are you sure you want to delete this period?"
													icon="pi pi-info-circle" />
							</p:commandButton>	
						</p:column>
						<p:rowExpansion>
							<div class="ui-g">
								<div class="ui-g-12 ui-md-12 ui-lg-12">			
									<p:outputLabel class="riForm-item-title" value="Remarks: " />
									<h:outputText value="#{p.remarks}" style="word-break: break-all;"/>
								</div>
							</div>
			            </p:rowExpansion>
		            </p:dataTable>

	        		<br/>
		            
	            </h:panelGroup>
			</div>
			
			<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Yes" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
			</p:confirmDialog>
				
		</h:form>
		
		<p:button value="#{bundle['action.back']}" outcome="/user/dashboard" icon="pi pi-arrow-left" styleClass="btn-back"/>
	</p:panel>
			
	</ui:define>
</ui:composition>