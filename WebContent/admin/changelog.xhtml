<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">

	
	<span class="admin-content-title">Change log</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
		<div class="card">
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		    <p:chronoline value="#{sysLogView.events}" var="event" align="alternate">
		        <p:card>
		            <f:facet name="title">
		                #{event.ver}
		            </f:facet>
		            <f:facet name="subtitle">
		                #{event.date}
		            </f:facet>
		            #{event.status}
		        </p:card>
		    </p:chronoline>
		</div>
	</p:panel>
   </ui:define>
</ui:composition>