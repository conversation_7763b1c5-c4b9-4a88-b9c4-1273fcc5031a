<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets">

	<h:outputScript>
	
	function reloadDataTable()
	{
		// Reload the datatable only if there is no input error in the datatable
		if ($('.ui-messages-error-icon').length == 0)
		{
			PF('reloadBtnWidget').getJQ().click();
		}
	}
	</h:outputScript>
	
	<h:form id="dataForm">
		<p:dataTable id="dataTable" var="data" value="#{secFuncLockView.secFuncLockUserList}" editable="true" sortMode="single"
						rowKey="#{data.pk.lock_code}_#{data.pk.func_id}_#{data.pk.user_id}" tableStyle="table-layout: fixed;" 
						selection="#{secFuncLockView.selectedSecFuncLockUser}" selectionMode="single"
                     	widgetVar="dataWidget">
			<p:ajax event="rowEdit" listener="#{secFuncLockView.onRowEdit}" update=":messages" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{secFuncLockView.onRowCancel}" update=":messages"/>
                        
            <p:column style="width:6rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
            
			<p:column headerText="User ID" id="user_id" sortBy="#{data.pk.user_id}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.pk.user_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.pk.user_id}" style="width:100%" valueChangeListener="#{secFuncLockView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Department" id="department_name" sortBy="#{secFuncLockView.getUserDept(data.pk.user_id)}">
                <h:outputText value="#{secFuncLockView.getUserDept(data.pk.user_id)}"/>
            </p:column>
        
            <p:column headerText="Name" id="user_name" sortBy="#{secFuncLockView.getUserName(data.pk.user_id)}">
                <h:outputText value="#{secFuncLockView.getUserName(data.pk.user_id)}"/>
            </p:column>
        
            <p:column headerText="Lock Status" id="lock_status" sortBy="#{data.lock_status}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="Extended" style="background-color:#EA4335; color:#fff; padding:3px 8px 3px 8px; border-radius: 25px;" rendered="#{data.lock_status eq 'Y'}"/>
                        <h:outputText value="Not Extended" style="background-color:#34A853; color:#fff; padding:3px 8px 3px 8px; border-radius: 25px;" rendered="#{data.lock_status eq 'N'}"/>
                    </f:facet>
                    <f:facet name="input">
                   	 	<p:selectOneMenu value="#{data.lock_status}"  style="width:100%">
		                    <f:selectItem itemLabel="Extended" itemValue="Y" />
		                    <f:selectItem itemLabel="Not Extended" itemValue="N" />
		                </p:selectOneMenu>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Remarks" id="remarks" sortBy="#{data.remarks}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.remarks}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.remarks}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>

        </p:dataTable>
        
         <p:contextMenu for="dataTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":formTab:dataForm:dataTable :messages" action="#{secFuncLockView.onAddNew()}"/>
        	<p:menuitem value="#{bundle['action.delete']}" update=":formTab:deleteDialog :formTab:dataForm:dataTable :messages" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":formTab:dataForm:dataTable" listener="#{secFuncLockView.reloadSecFuncLockUserList()}"/>
			</p:commandButton>		
			<br/><br/>
			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{secFuncLockView.selectedSecFuncLockUser.pk.user_id}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{secFuncLockView.deleteSecFuncLockUser}"
								 update=":formTab:dataForm:dataTable :messages"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
</ui:composition>
   