<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Manage UoA</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:dataTable id="dataTable" var="data" value="#{raeUoAView.raeUoAList}" editable="true" sortMode="single"
						rowKey="#{data.uoaCode}-" tableStyle="table-layout: fixed;"
						selection="#{raeUoAView.selectedRaeUoA}" selectionMode="single"
                     	widgetVar="dataWidget">
			<p:ajax event="rowEdit" listener="#{raeUoAView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{raeUoAView.onRowCancel}" update=":msgs"/>

			<p:column headerText="Code" id="uoaCode" sortBy="#{data.uoaCode}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.uoaCode}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.uoaCode}" style="width:100%" valueChangeListener="#{raeUoAView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
             <p:column headerText="Description" id="uoaDesc" sortBy="#{data.uoaDesc}">
			    <p:cellEditor>
			        <f:facet name="output">
			            <h:outputText value="#{data.uoaDesc}"/>
			        </f:facet>
			        <f:facet name="input">
			            <p:inputText value="#{data.uoaDesc}" style="width:100%" valueChangeListener="#{raeUoAView.keyChangedListener}">
			                <p:ajax/>
			            </p:inputText>
			        </f:facet>
			    </p:cellEditor>
			</p:column>
			
			<p:column headerText="Panel Code" id="panelCode" sortBy="#{data.panelCode}">
			    <p:cellEditor>
			        <f:facet name="output">
			            <h:outputText value="#{data.panelCode}"/>
			        </f:facet>
			        <f:facet name="input">
			            <p:inputText value="#{data.panelCode}" style="width:100%" valueChangeListener="#{raeUoAView.keyChangedListener}">
			                <p:ajax/>
			            </p:inputText>
			        </f:facet>
			    </p:cellEditor>
			</p:column>
			
			<p:column headerText="COORD FACULTY" id="coorFaculty" sortBy="#{data.coorFaculty}">
			    <p:cellEditor>
			        <f:facet name="output">
			            <h:outputText value="#{data.coorFaculty}"/>
			        </f:facet>
			        <f:facet name="input">
			            <p:inputText value="#{data.coorFaculty}" style="width:100%" valueChangeListener="#{raeUoAView.keyChangedListener}">
			                <p:ajax/>
			            </p:inputText>
			        </f:facet>
			    </p:cellEditor>
			</p:column>
            <p:column style="width:4rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="dataTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:dataTable :msgs" action="#{raeUoAView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:dataTable" listener="#{raeUoAView.reloadRaeUoAList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

		<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{raeUoAView.selectedRaeUoA.uoaCode}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{raeUoAView.deleteRaeUoA}"
								 update=":dataForm:dataTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>