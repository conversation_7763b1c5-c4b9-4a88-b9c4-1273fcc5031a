<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Manage RAE Staff</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		
		<p:commandButton value="Update Staff Serial"
						  		 action="#{raeStaffView.updateRaeStaffSerial}"
						  		 update=":dataForm:dataTable :msgs">
		</p:commandButton>
		
		<p:dataTable id="dataTable" var="data" value="#{raeStaffView.raeStaffList}" editable="true" sortMode="single"
						rowKey="#{data.staffNumber}-" tableStyle="table-layout: fixed;"
						selection="#{raeStaffView.selectedRaeStaff}" selectionMode="single"
                     	widgetVar="dataWidget">
			<p:ajax event="rowEdit" listener="#{raeStaffView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{raeStaffView.onRowCancel}" update=":msgs"/>

			<p:column headerText="Staff Number" id="staffNumber" sortBy="#{data.staffNumber}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.staffNumber}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.staffNumber}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
             <p:column headerText="Staff Title" id="staffTitle" sortBy="#{data.staffTitle}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.staffTitle}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.staffTitle}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Staff Name" id="staffName" sortBy="#{data.staffName}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.staffName}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.staffName}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="UOA Code" id="uoaCode" sortBy="#{data.uoaCode}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.uoaCode}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.uoaCode}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Faculty" id="faculty" sortBy="#{data.faculty}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.faculty}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.faculty}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Department" id="department" sortBy="#{data.department}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.department}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.department}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="No Reduced RI" id="noReducedRi" sortBy="#{data.noReducedRi}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.noReducedRi}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.noReducedRi}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Post" id="post" sortBy="#{data.post}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.post}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.post}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Staff Grade" id="staffGrade" sortBy="#{data.staffGrade}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.staffGrade}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.staffGrade}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Staff Serial" id="staffSerial" sortBy="#{data.staffSerial}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.staffSerial}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.staffSerial}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="CCA Output Req" id="ccaOutputReq" sortBy="#{data.ccaOutputReq}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.ccaOutputReq}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.ccaOutputReq}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Sel Remark" id="selRemark" sortBy="#{data.selRemark}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.selRemark}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.selRemark}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="RA Code" id="raCode" sortBy="#{data.raCode}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.raCode}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.raCode}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Surname" id="surname" sortBy="#{data.surname}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.surname}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.surname}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Given Name" id="givenName" sortBy="#{data.givenName}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.givenName}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.givenName}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="Chinese Name" id="chiName" sortBy="#{data.chiName}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.chiName}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.chiName}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="New Researcher" id="newResearcher" sortBy="#{data.newResearcher}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.newResearcher}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.newResearcher}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="SD Code" id="sdCode" sortBy="#{data.sdCode}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.sdCode}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.sdCode}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
<p:column headerText="COORD FACULTY" id="coordFaculty" sortBy="#{data.coordFaculty}">
    <p:cellEditor>
        <f:facet name="output">
            <h:outputText value="#{data.coordFaculty}"/>
        </f:facet>
        <f:facet name="input">
            <p:inputText value="#{data.coordFaculty}" style="width:100%" valueChangeListener="#{raeStaffView.keyChangedListener}">
                <p:ajax/>
            </p:inputText>
        </f:facet>
    </p:cellEditor>
</p:column>
            <p:column style="width:4rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="dataTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:dataTable :msgs" action="#{raeStaffView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:dataTable" listener="#{raeStaffView.reloadRaeStaffList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{raeStaffView.selectedRaeStaff.staffNumber}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{raeStaffView.deleteRaeStaff}"
								 update=":dataForm:dataTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>