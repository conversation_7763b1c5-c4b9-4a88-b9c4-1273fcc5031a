<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Manage Lookup Value For RDO</span>
	
	<p:messages id="msgs" showDetail="false" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:toolbar style="background:#fff; border:0px solid #fff; padding:0;">
			<p:toolbarGroup>
				<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
			</p:toolbarGroup>
			<p:toolbarGroup align="left">
                <p:commandButton id="btn_download" value="Export" icon="pi pi-download" ajax="false" styleClass="btn-back">
                    <p:dataExporter type="xlsx" target="vTable" fileName="dataExport"/>
                </p:commandButton>
            </p:toolbarGroup>
        </p:toolbar>
			<p:dataTable id="vTable" var="v" value="#{lookupValueFNDView.valueList}" editable="true" sortMode="single"
						rowKey="#{v.pk.lookup_type}-#{v.pk.lookup_code}-#{v.pk.language}"
						selection="#{lookupValueFNDView.selectedValue}" selectionMode="single"
						 reflow="true"	 paginator="true"	 rows="50"
						 currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
                         paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                  		 tableStyle="table-layout:auto;"
                     	widgetVar="valueWidget">
			<p:ajax event="rowEdit" listener="#{lookupValueFNDView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{lookupValueFNDView.onRowCancel}" update=":msgs"/>
			
			<p:column headerText="Lookup Type" id="lookup_type" sortBy="#{v.pk.lookup_type}" filterBy="#{v.pk.lookup_type}" filterMatchMode="exact">
				<f:facet name="filter">
                    <p:selectOneMenu onchange="PF('valueWidget').filter()" styleClass="ui-custom-filter">
                        <f:selectItem itemLabel="All" itemValue="#{null}" noSelectionOption="true" />
                        <f:selectItems value="#{lookupValueFNDView.lookupTypeList}" />
                    </p:selectOneMenu>
                </f:facet>
                 <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.pk.lookup_type}"/>
                    </f:facet>
                    <f:facet name="input">
                    	<p:selectOneMenu value="#{v.pk.lookup_type}" valueChangeListener="#{lookupValueFNDView.keyChangedListener}" required="true" label="Lookup Type">
                    		<f:selectItem itemLabel="--- select one ---" itemValue="#{null}" noSelectionOption="true" />	
	                        <f:selectItems value="#{lookupValueFNDView.lookupTypeList}" />
	                    </p:selectOneMenu>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
			<p:column headerText="Lookup Code" id="lookup_code" sortBy="#{v.pk.lookup_code}" filterBy="#{v.pk.lookup_code}" filterMatchMode="contains">
                 <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.pk.lookup_code}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.pk.lookup_code}" style="width:100%" valueChangeListener="#{lookupValueFNDView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Language" id="language" visible="false">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.pk.language}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.pk.language}" style="width:100%" valueChangeListener="#{lookupValueFNDView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            
            <p:column headerText="Description" id="description" sortBy="#{v.description}" filterBy="#{v.description}" filterMatchMode="contains" style="width:20%">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.description}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.description}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
          
        	<p:column headerText="Enabled Flag" id="enabled_flag" sortBy="#{v.enabled_flag}" filterBy="#{v.enabled_flag}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.enabled_flag}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.enabled_flag}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Funding code" id="attribute_1" sortBy="#{v.attribute1}" filterBy="#{v.attribute1}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.attribute1}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.attribute1}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Funding type" id="attribute_2" sortBy="#{v.attribute2}" filterBy="#{v.attribute2}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.attribute2}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.attribute2}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
           
          <p:column headerText="Research excel" id="attribute_3" sortBy="#{v.attribute3}" filterBy="#{v.attribute3}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.attribute3}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.attribute3}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
          
          <p:column headerText="User select" id="attribute_4" sortBy="#{v.attribute4}" filterBy="#{v.attribute4}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.attribute4}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.attribute4}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            
            <p:column headerText="Order" id="attribute_5" sortBy="#{v.attribute5}" filterBy="#{v.attribute5}" filterMatchMode="contains">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{v.attribute5}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{v.attribute5}" style="width:100%" />
                    </f:facet>
                </p:cellEditor>
            </p:column>
            <p:column headerText="Updated by">
                <h:outputText value="#{v.userstamp}"/>
            </p:column>
            <p:column headerText="Updated date">
                <h:outputText value="#{v.timestamp}"/>
            </p:column>
            <p:column style="width:4rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
              
        </p:dataTable>
        <p:contextMenu for="vTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}"  update=":dataForm:vTable :msgs" action="#{lookupValueFNDView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:vTable" listener="#{lookupValueFNDView.reloadValueList()}"/>
			</p:commandButton>		
		</h:panelGroup>
        
        
	</h:form>
		<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{lookupValueFNDView.selectedValue.pk}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{lookupValueFNDView.deleteValue}"
								 update=":dataForm:vTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>


	</p:panel>
   </ui:define>
</ui:composition>