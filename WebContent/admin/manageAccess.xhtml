<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">
	
				
	<ui:define name="mainContent">

	<p:panel id="contentPanel">
		<span class="admin-content-title"><i class="fa-solid fa-upload"></i> Manage Access (User Role + Set Function Access)</span>
		<p:messages id="messages" showDetail="true" closable="true" globalOnly="true" escape="false">
				<p:autoUpdate/>
			</p:messages>
		<h:form id="dataForm" enctype="multipart/form-data">
			<span style="color:red">* Each Excel file should contain only one type of data. Any existing data of the same type will be removed.</span>
	        <br/>
			Excel should have 4 columns: 			
			<div class="flex align-items-center flex-wrap">
	            <p:chip label="userId" styleClass="mr-2"/>
	            <p:chip label="roleId" styleClass="mr-2"/>
	            <p:chip label="dataType" styleClass="mr-2"/>
	            <p:chip label="dataCode" styleClass="mr-2"/>
	        </div><br/>
	        
		    <p:fileUpload id="uploadedFile" label="Choose your Excel (xlsx) file" value="#{dataAccessView.file}" mode="simple" skinSimple=""/>
		    <br/><br/>
		    <p:commandButton value="Upload" 
		                     action="#{dataAccessView.upload}" 
		                     ajax="false" />
		</h:form>
		
		<br/>
	
	</p:panel>
	</ui:define>
</ui:composition>