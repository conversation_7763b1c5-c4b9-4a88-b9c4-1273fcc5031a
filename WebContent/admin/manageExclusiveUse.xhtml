<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata> 
		<f:viewParam name="id" value="#{secFuncLockView.paramTabIndex}" />
		<f:viewAction action="#{secFuncLockView.setLockCode('EXCLUSIVE_RI')}"/>
	</f:metadata>
	<ui:define name="mainContent"> 
		<p:panel id="contentPanel">
		
			<h:panelGroup styleClass="admin-content-title">Set Exclusive Use of RI</h:panelGroup>			
			<p:messages id="messages" showDetail="true" autoUpdate="true" closable="true"/>		
			<p:tabView id="formTab" styleClass="formTab" activeIndex="#{secFuncLockView.paramTabIndex}">
		        <p:tab title="Exclusive Use">
		            <h:panelGrid columns="2" style="width: 100%">
						<ui:include page="/admin/setExclusiveUse.xhtml"/>	
					</h:panelGrid>        
		        </p:tab>
		        <p:tab title="Extended Access Users">
		            <h:panelGrid columns="2" style="width: 100%">
		                <ui:include page="/admin/setExtendedAccessUser.xhtml"/>
		            </h:panelGrid>
		        </p:tab>
		    </p:tabView>	
		    
		</p:panel>
   </ui:define>
</ui:composition>
   