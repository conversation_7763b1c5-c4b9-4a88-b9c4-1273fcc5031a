<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:o="http://omnifaces.org/ui"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
	</f:metadata>

	<ui:define name="mainContent"> 
	<style>
		.ui-datatable thead th {
			background: #135178 !important;
			color: #ffffff !important;
			max-height: 200px;
		} 
		.ui-datatable td {
			vertical-align: top;
		} 
	</style>
	<h:outputScript>
	
		function disableOutputDownloadBtn()
		{
			setTimeout(function() {PF('outputDownloadBtn').disable();}, 50);
		}		
		
		function enableOutputDownloadBtn()
		{
			PF('outputDownloadBtn').enable();
		}

		function disableProjectDownloadBtn()
		{
			setTimeout(function() {PF('projectDownloadBtn').disable();}, 50);
		}		
		
		function enableProjectDownloadBtn()
		{
			PF('projectDownloadBtn').enable();
		}
		
		function disableAwardDownloadBtn()
		{
			setTimeout(function() {PF('awardDownloadBtn').disable();}, 50);
		}		
		
		function enableAwardDownloadBtn()
		{
			PF('awardDownloadBtn').enable();
		}
		
		function disablePatentDownloadBtn()
		{
			setTimeout(function() {PF('patentDownloadBtn').disable();}, 50);
		}		
		
		function enablePatentDownloadBtn()
		{
			PF('patentDownloadBtn').enable();
		}
		
	</h:outputScript>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	
	<h:form style="width:100%; max-width: 97vw;" 
			onkeypress="if (event.keyCode == 13) { document.getElementById('dataForm:searchButton').click(); return false; }">
		<component:cdcfSearchPanel searchPanel="#{cdcfReportListView.searchPanel}"/>
	</h:form>
	<h:form id="dataForm">
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="searchButton" value="Search" update="resultPanels" icon="pi pi-search"
						 widgetVar="searchBut" onclick="PF('searchBut').disable();PF('searchDialog').show();"
						 oncomplete="PF('searchBut').enable();PF('searchDialog').hide();"
						 actionListener="#{cdcfReportListView.setFirstSearch(true)}" ></p:commandButton><p:spacer width="10"/>
		<p:dialog widgetVar="searchDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
	        <div>
            	<h5>Searching</h5>
       			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
            </div>
	    </p:dialog>
		
		<h:panelGroup id="resultPanels">
			<h:panelGroup id="outputResultPanel" rendered="#{cdcfReportListView.isFirstSearch()}">
				<ui:insert name="#{cdcfReportListView.searchPanel.ktType}">
					<ui:decorate template="../resources/component/ktForm/#{cdcfReportListView.searchPanel.ktType}_kt_listing.xhtml">
					</ui:decorate>
				</ui:insert>
			</h:panelGroup>
			
		</h:panelGroup>
		
	</h:form>
   </ui:define>
</ui:composition>