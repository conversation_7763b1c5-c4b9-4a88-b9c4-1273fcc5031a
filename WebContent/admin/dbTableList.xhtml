<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 

	<span class="admin-content-title">View Database Data</span>
	
	<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			<p:autoUpdate/>
	</p:messages>
	
	<h:form id="dataForm">
		<p:panel id="contentPanel" header="Selection">

			<!-- Selection -->
			<h:panelGrid style="width:99%" columns="2" columnClasses="ui-g-12 ui-md-4 ui-lg-2, ui-g-12 ui-md-8 ui-lg-4">	
				<!-- Table name -->
				<h:panelGroup>
					<h:outputText style="vertical-align:-moz-middle-with-baseline" value="Table Name"/>
				</h:panelGroup>
				<h:panelGroup>
					<p:message for="dbTableName" />
					<p:selectOneMenu id="dbTableName" title="Table Name" value="#{databaseView.selectedTable}" required="true">
						<f:selectItem itemLabel="Select One" itemValue="" itemDisabled="true"/>
						<f:selectItems value="#{databaseView.tableNameList}" var="m"
											itemLabel="#{m}" 
											itemValue="#{m}"/>		   
						<f:ajax event="change" execute="@this" render="dataForm"/>										 
					</p:selectOneMenu>
				</h:panelGroup>
				<!-- Column Name -->
				<h:panelGroup>
					<h:outputText style="vertical-align:-moz-middle-with-baseline" value="Column Name"/>
				</h:panelGroup>				
				<h:panelGroup>
					<p:selectOneMenu id="dbTableColumn" title="Column Name" value="#{databaseView.selectedTableColumn}">
						<f:selectItem itemLabel="N/A" itemValue="" />
						<f:selectItems value="#{databaseView.tableColumnList}" var="c"
											itemLabel="#{c}" 
											itemValue="#{c}"/>		   
						<f:ajax event="change" execute="@this" render="columnDataType"/>	
						<f:ajax event="change" execute="@this" render="panelColumnValue"/>	
					</p:selectOneMenu>
				</h:panelGroup>
				<!-- Data type -->
				<h:panelGroup>
					<h:outputText style="vertical-align:-moz-middle-with-baseline" value="Column Data Type"/>
				</h:panelGroup>				
				<h:panelGroup>
					<h:outputText id="columnDataType" style="vertical-align:-moz-middle-with-baseline" value="#{databaseView.dataType}"/>
				</h:panelGroup>		
				<!-- Value -->		
				<h:panelGroup>
					<h:outputText style="vertical-align:-moz-middle-with-baseline" value="Column Value"/>
				</h:panelGroup>				
				<h:panelGroup id="panelColumnValue">
						<p:message for="columnDateValue" />
						<p:message for="columnNumValue" />
				       <p:inputText id="columnStringValue" value="#{databaseView.columnValue}" rendered="#{databaseView.dataType eq 'VARCHAR2' || databaseView.dataType eq 'CHAR'}">
				       </p:inputText>
				       <p:datePicker id="columnDateValue" value="#{databaseView.columnDateValue}" pattern="yyyy-MM-dd" showIcon="true" rendered="#{databaseView.dataType eq 'DATE'}" required="true">
				       	</p:datePicker>
				       <p:inputText id="columnNumValue" value="#{databaseView.columnValue}" rendered="#{databaseView.dataType eq 'NUMBER'}" onkeypress="if(event.which &lt; 48 || event.which &gt; 57) return false;" required="true">
				       	</p:inputText>
				</h:panelGroup>	
				<!-- Order by -->
				<h:panelGroup>
					<h:outputText style="vertical-align:-moz-middle-with-baseline" value="Order by"/>
				</h:panelGroup>				
				<h:panelGroup>
					<p:selectOneMenu id="dbTableOrder" title="Order by" value="#{databaseView.selectedTableOrder}">
						<f:selectItem itemLabel="N/A" itemValue="" />
						<f:selectItems value="#{databaseView.tableColumnList}" var="o"
											itemLabel="#{o}" 
											itemValue="#{o}"/>		   
					</p:selectOneMenu>
				</h:panelGroup>

				<p:commandButton id="exportButton" icon="fas fa-file-download"
									 value="Export data" update="@form"
									 action="#{databaseView.exportData}" ajax="false"
									 >
				</p:commandButton>
			</h:panelGrid>
			</p:panel>

			<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>
		</h:form>


   </ui:define>
</ui:composition>
   