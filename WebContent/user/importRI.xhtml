<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata>
		<f:viewParam name="pid" value="#{importRIView.paramPid}"/>
	</f:metadata>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-file-import"></i> Import RI</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:tabView id="formTab">
	        <p:tab title="Research Outputs">
	        	<component:importRIOutput importRIOutput="#{importRIView.outputPanel}"
	        							  update=":dataForm"/>
	        </p:tab>
	        <p:tab title="Projects">
	        	<component:importRIProject importRIProject="#{importRIView.projectPanel}"
	        							   update=":dataForm"/>
	        </p:tab>	  
	        <p:tab title="Prizes and Awards">
	        	<component:importRIAward importRIAward="#{importRIView.awardPanel}"
	        							   update=":dataForm"/>
	        </p:tab>
	        <p:tab title="Patents">
	        	<component:importRIPatent importRIPatent="#{importRIView.patentPanel}"
	        							   update=":dataForm"/>
	        </p:tab>	     	              
		</p:tabView>
	</h:form>
	<h:form id="ignoreConfirmForm">
		<p:confirmDialog id="ignoreOutputConfirm" widgetVar="ignoreOutputConfirmWidget" 
						 header="Confirm ignore?" appendToBody="true"
						 message="Are you sure to ignore research output with ID: #{importRIView.outputPanel.getSelectedIgnoreOutput().source_id}?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 update=":dataForm:formTab"
							 actionListener="#{importRIView.ignoreOutput()}">
				<p:ajax event="click" oncomplete="PF('ignoreOutputConfirmWidget').hide()" update=":dataForm:formTab"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('ignoreOutputConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
		<p:confirmDialog id="ignoreProjectConfirm" widgetVar="ignoreProjectConfirmWidget" 
						 header="Confirm ignore?" appendToBody="true"
						 message="Are you sure to ignore project with ID: #{importRIView.projectPanel.getSelectedIgnoreProject().source_id}?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 update=":dataForm:formTab"
							 actionListener="#{importRIView.ignoreProject()}">
				<p:ajax event="click" oncomplete="PF('ignoreProjectConfirmWidget').hide()" update=":dataForm:formTab"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('ignoreProjectConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
		<p:confirmDialog id="ignoreAwardConfirm" widgetVar="ignoreAwardConfirmWidget" 
						 header="Confirm ignore?" appendToBody="true"
						 message="Are you sure to ignore research award with ID: #{importRIView.awardPanel.getSelectedIgnoreAward().source_id}?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 update=":dataForm:formTab"
							 actionListener="#{importRIView.ignoreAward()}">
				<p:ajax event="click" oncomplete="PF('ignoreAwardConfirmWidget').hide()" update=":dataForm:formTab"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('ignoreAwardConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
		<p:confirmDialog id="ignorePatentConfirm" widgetVar="ignorePatentConfirmWidget" 
						 header="Confirm ignore?" appendToBody="true"
						 message="Are you sure to ignore research patent with ID: #{importRIView.patentPanel.getSelectedIgnorePatent().source_id}?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 update=":dataForm:formTab"
							 actionListener="#{importRIView.ignorePatent()}">
				<p:ajax event="click" oncomplete="PF('ignorePatentConfirmWidget').hide()" update=":dataForm:formTab"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('ignorePatentConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
	</h:form>
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	
	</p:panel>
   </ui:define>
</ui:composition>