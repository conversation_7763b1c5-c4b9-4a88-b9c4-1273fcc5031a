<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	
	<f:metadata>
		<f:viewParam name="no" value="#{manageRIView.paramNo}" />
		<f:viewParam name="riType" value="#{manageRIView.paramRiType}" />
	</f:metadata>
	
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">

	
	<span class="admin-content-title">RI Data Comparison Report</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	<h:form id="editForm" rendered="#{manageRIView.isRdoAdmin == true}">
		<p:dataTable id="reportTable" value="#{manageRIView.comparisonReport}" var="col" reflow="true"
					 tableStyle="table-layout:auto"
					 rowIndexVar="rowIndex">
			<p:column>		
				<f:facet name="header">Published vs. Snapshot</f:facet>		
				<h:outputText escape="false" value="#{col}"></h:outputText>
			</p:column>
	 	</p:dataTable>
	 </h:form>
	</p:panel>
   </ui:define>
</ui:composition>