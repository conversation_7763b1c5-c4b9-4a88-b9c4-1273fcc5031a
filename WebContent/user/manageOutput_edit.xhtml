<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata>
		<f:event listener="#{manageOutputView.getCanModify}" type="preRenderView" />
		<f:event listener="#{manageOutputView.checkValid}" type="preRenderView" />
		<f:viewParam name="no" value="#{manageOutputView.paramNo}" />
		<f:viewParam name="pid" value="#{manageOutputView.paramPid}" />
		<f:viewParam name="dataLevel" value="#{manageOutputView.paramDataLevel}" />
		<f:viewParam name="consent" value="#{manageOutputView.paramConsent}" />
		<f:viewParam name="area_code" value="#{manageOutputView.paramArea_code}" />
		<f:viewParam name="source_id" value="#{manageOutputView.paramSource_id}" />
		<f:viewParam name="staff_number" value="#{manageOutputView.paramStaff_number}" />
	</f:metadata>
	<ui:define name="html_head">
		<script type="text/javascript" src="#{request.contextPath}/resources/js/jquery.chineselengthlimit.js"></script>
		<style>
			li.ui-state-disabled{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
	<h:outputScript>
	$(document).ready(function(){
		$('#editForm\\:btn_add').click(function () {
		    $('html, body').animate({scrollTop:$(document).height()}, 'slow');
		    return false;
		});
		
		$('.chinese-500').ChineseLengthLimit({ 
	      	 limitCount: 500, 
		      isByte: true
		   }); 
		
		 $('.chinese-500').trigger('checkLimit');
		 
		 $('.chinese-150').ChineseLengthLimit({ 
		       limitCount: 500, 
		      isByte: true
		   }); 
	
		 $('.chinese-150').trigger('checkLimit');
		 
		 

		 
		 
	});
	</h:outputScript>
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-chart-pie"></i> Manage Research Output</span>
	<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
	<p:scrollTop />
	<h:outputText value="You don't have access right." rendered="#{manageOutputView.hasAccessRight() == false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
	<h:form id="editForm" rendered="#{manageOutputView.hasAccessRight() == true}">
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-10">
				<!--  <f:event type="postValidate" listener="#{manageOutputView.postValidate}" />-->
				<p:linkButton value="#{bundle['action.back']}" outcome="manageOutput" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageOutputView.paramDataLevel eq 'M' &amp;&amp; manageOutputView.paramConsent eq ''}">		
					<f:param name="pid" value="#{manageOutputView.paramPid}"/>
				</p:linkButton>
				<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageOutputView.paramConsent ne ''}">		
					<f:param name="pid" value="#{manageOutputView.paramPid}"/>
					<f:param name="consent" value="#{manageOutputView.paramConsent}"/>
					<f:param name="tabpage" value="0"/>
				</p:linkButton>
				<p:defaultCommand target="dummy"/>
				<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
				<p:commandButton id="top_btn_sava" value="#{formBundle['form.save']}" 
										  rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel ne 'C'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageOutputView.save}"
								  		  oncomplete="window.scrollTo(0,0);">
				</p:commandButton>	
				<p:commandButton id="top_btn_savaAndGen" value="#{(manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}" 
										  rendered="#{manageOutputView.paramDataLevel eq 'C'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageOutputView.save}"
								  		  oncomplete="window.scrollTo(0,0);">
				</p:commandButton>
				<p:commandButton id="top_btn_savaAndPublish" value="#{bundle['action.saveAndPublish']}" 
										  rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageOutputView.saveAndPublishForm}"
								  		  oncomplete="window.scrollTo(0,0);">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.publish.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>			
				<p:commandButton id="top_btn_submit" value="#{formBundle['form.save']}" 
										  rendered="#{manageOutputView.isCreator == false &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageOutputView.submitConsent}"
								  		  oncomplete="window.scrollTo(0,0);">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>		
				<p:commandButton id="top_btn_snapshot" value="#{formBundle['form.take.snapshot']}" rendered="#{manageOutputView.paramDataLevel eq 'P'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  process="@this"
								  		  action="#{manageOutputView.takeSnapshot}"
								  		  oncomplete="window.scrollTo(0,0);">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.take.snapshot.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>	
				<p:commandButton id="top_btn_delete" value="#{formBundle['form.del']}" rendered="#{manageOutputView.canDelete == true &amp;&amp; manageOutputView.canModify == true}"
										  style="margin-right:5px; margin-bottom:1px; background:#D32F2F; border:1px solid #D32F2F;" 
										  process="@this"
								  		  action="#{manageOutputView.deleteForm}">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.del.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>	
				<p:linkButton id="btn_p_level" outcome="manageOutput_edit" rendered="#{manageOutputView.paramDataLevel eq 'P' &amp;&amp; manageOutputView.checkSnapshotExists()}" 
								value="#{formBundle['form.ri.goto.lv.c']}" icon="pi pi-chevron-right" style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
  					<f:param name="no" value="#{manageOutputView.selectedOutputHeader_q.output_no}"/>
  					<f:param name="dataLevel" value="C"/>
  				</p:linkButton>	
  				<p:linkButton id="btn_c_level" outcome="manageOutput_edit" rendered="#{manageOutputView.paramDataLevel eq 'C'}" 
  								value="#{formBundle['form.ri.goto.lv.p']}" icon="pi pi-chevron-right" style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
  					<f:param name="no" value="#{manageOutputView.selectedOutputHeader_q.output_no}"/>
  					<f:param name="dataLevel" value="P"/>
  				</p:linkButton>	
		        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true" width="350">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		        </p:confirmDialog>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-2" style="font-size:18px; font-weight:700; text-align: right;">
				<p:outputLabel value="#{formBundle['form.ri.status.lv.p']}" style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;" rendered="#{manageOutputView.paramDataLevel eq 'P'}"/>
				<p:outputLabel value="#{formBundle['form.ri.status.lv.c']}" style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;" rendered="#{manageOutputView.paramDataLevel eq 'C'}"/>
			</div>
		</div>	
		<br/>
		<div class="form-sub-title">
		<i class="fas fa-tag" style="margin-right:5px;"></i>#{formBundle['form.ri.status']}
		</div>
		<hr/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="background:#00a2c7; font-size:18px; font-weight:700;">
				<p:outputLabel style="color:#1f1645;" value="#{formBundle['form.header.status']} "/>
				<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{manageOutputView.selectedOutputHeader_q.publish_status}" rendered="#{manageOutputView.selectedOutputHeader_q.publish_status ne 'PUBLISHED'}"/>
				<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{manageOutputView.selectedOutputHeader_q.publish_status eq 'PUBLISHED'}"/>
			</div>

			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.at']} " />
				<h:outputText class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.last_modified_date}" >
					    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>	
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.by']} " />
				<p:outputLabel class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.last_modified_by}" />
			</div>
			
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.at']} " />
				<h:outputText class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.last_published_date}" >
				    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>
			</div>

			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.by']} " />
				<p:outputLabel class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.last_published_by}" />
			</div>
			
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			
			<div class="ui-g-6 ui-md-4 ui-lg-2">
			<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.institute']}" />
			</div>
			<div class="ui-g-6 ui-md-8 ui-lg-4">
				<p:message for="inst_display_ind"/>
				<p:selectOneMenu id="inst_display_ind" required="#{manageOutputView.isRdoAdmin == true}"
											disabled="#{manageOutputView.isRdoAdmin == false || manageOutputView.paramDataLevel eq 'M'
														|| manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_GENERATED'
														|| manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_NOT_SEL'
														|| manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_SPEC'}" 
											title="#{formBundle['form.header.display.institute']}" 
											label="#{formBundle['form.header.display.institute']}"
											value="#{manageOutputView.selectedOutputHeader_q.inst_display_ind}">
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>
			</div>
			
			<div class="ui-g-6 ui-md-4 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute']}" />
			</div>
			<div class="ui-g-6 ui-md-8 ui-lg-4">
				<p:message for="inst_verified_ind"/>
				<p:selectOneMenu id="inst_verified_ind" required="#{manageOutputView.isRdoAdmin == true}" 
											disabled="#{manageOutputView.isRdoAdmin == false || manageOutputView.paramDataLevel eq 'M'
														|| manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_GENERATED'
														|| manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_NOT_SEL'
														|| manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_SPEC'}" 
											title="#{formBundle['form.header.verify.institute']}" 
											label="#{formBundle['form.header.verify.institute']}"
											value="#{manageOutputView.selectedOutputHeader_q.inst_verified_ind}">
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>					
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   					
						<!--  <p:ajax event="valueChange" update="inst_verified_date"/>-->
					</p:selectOneMenu>		
			</div>
			<!--  
			<div class="ui-g-12 ui-md-3 ui-lg-2">			
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute.date']}" />
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<p:message for="inst_verified_date"/>
				<p:datePicker id="inst_verified_date" 
										title="#{formBundle['form.header.verify.institute.date']}" 
										label="#{formBundle['form.header.verify.institute.date']}" 
										value="#{manageOutputView.selectedOutputHeader_q.inst_verified_date}" 
										maxdate="#{manageOutputView.currentDate}"
										pattern="yyyy-M-d" showIcon="true" 
										disabled="#{manageOutputView.isRdoAdmin == false || manageOutputView.paramDataLevel eq 'M' || manageOutputView.selectedOutputHeader_q.inst_verified_ind eq 'N'}"/>			
			</div>-->
			
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.generate']}" />
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
				<p:outputLabel class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_gen_ind eq 'Y'?'Yes':'No'}"/>
				<p:outputLabel class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_gen_ind eq 'Y'?' — ':''}"/>
				<h:outputText class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_gen_date}" rendered="#{manageOutputView.selectedOutputHeader_q.cdcf_gen_ind eq 'Y'}">
				    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>
			</div>
			<div class="ui-g-4 ui-md-3 ui-lg-2">
					<h:outputText class="riForm-item-title" value="#{formBundle['form.cdcf.change']}" rendered="#{manageOutputView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
					<h:outputText class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_changed_ind eq 'Y'?'Yes':'No'}" rendered="#{manageOutputView.isRdoAdmin == true}"/>
					<p:linkButton id="btn_compare" outcome="riComparisonReport" rendered="#{manageOutputView.selectedOutputHeader_q.cdcf_changed_ind eq 'Y' &amp;&amp; manageOutputView.isRdoAdmin == true}" value="Compare Snapshot" style="margin-left:7px;" target="_blank">
	  					<f:param name="no" value="#{manageOutputView.selectedOutputHeader_q.output_no}"/>
	  					<f:param name="riType" value="output"/>
	  				</p:linkButton>	
			</div>
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.process']}" />
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
				<p:outputLabel class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_processed_ind eq 'Y'?'Yes':'No'}"/>
				<p:outputLabel class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_processed_ind eq 'Y'?' — ':''}"/>
				<h:outputText class="riForm-item-ans" value="#{manageOutputView.selectedOutputHeader_q.cdcf_processed_date}" rendered="#{manageOutputView.selectedOutputHeader_q.cdcf_processed_ind eq 'Y'}">
				    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>
			</div>
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.status']}" />
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
				<p:message for="cdcf_status"/>
				<p:selectOneMenu id="cdcf_status" title="#{formBundle['form.cdcf.status']}" label="#{formBundle['form.cdcf.status']}" value="#{manageOutputView.selectedOutputHeader_q.cdcf_status}" 
											required="#{manageOutputView.isRdoAdmin == true}" 
											disabled="#{manageOutputView.isRdoAdmin == false || manageOutputView.paramDataLevel eq 'M'}">
						<f:selectItems value="#{manageOutputView.cdcfStatusList}"/>
					</p:selectOneMenu>	
			</div>
			
		<!-- <div class="ui-g-4 ui-md-3 ui-lg-2"  style="#{manageOutputView.isRdoAdmin?'':'display: none;'}">
				<p:outputLabel class="riForm-item-title" value="AMIS selected" style="padding-right:20px;" rendered="#{manageOutputView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-10" style="#{manageOutputView.isRdoAdmin?'':'display: none;'}">
				<p:selectOneMenu title="AMIS Selected" value="#{manageOutputView.selectedOutputHeader_q.amis_selected_ind}" 
										disabled="#{manageOutputView.isRdoAdmin == false || manageOutputView.paramDataLevel eq 'M' || manageOutputView.hasSAP == true}">
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
				</p:selectOneMenu>
			</div>	 -->
			
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			
			<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageOutputView.paramDataLevel eq 'M'?'':'display:none;'}">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.ri.profile']}" style="padding-right:20px;" rendered="#{manageOutputView.paramDataLevel eq 'M'}"/>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageOutputView.paramDataLevel eq 'M'?'':'display:none;'}">
				<h:panelGroup id="displayRIGroup">
					<p:selectOneMenu title="#{formBundle['form.header.display.ri.profile']}" value="#{manageOutputView.selectedOutputDetails_q.display_ind}" 
											rendered="#{manageOutputView.paramDataLevel eq 'M'}"
											disabled="#{manageOutputView.canModify == false || manageOutputView.selectedOutputDetails_q.consent_ind ne 'Y'}">
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>
				</h:panelGroup>
			</div>	
			
			<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageOutputView.isContributor == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'?'':'display:none;'}">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.consent.ri']}" style="padding-right:20px;" rendered="#{manageOutputView.isContributor == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'}"/>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageOutputView.isContributor == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'?'':'display:none;'}">	
				<p:selectOneMenu title="#{formBundle['form.header.consent.ri']}" value="#{manageOutputView.selectedOutputDetails_q.consent_ind}" 
										rendered="#{manageOutputView.isContributor == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'}"
										disabled="#{manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="Unconfirmed" itemValue="U"/>
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					<p:ajax event="change" update="displayRIGroup" listener="#{manageOutputView.setDisplyRI()}"/>
				</p:selectOneMenu>
			</div>	
			
			<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageOutputView.isRdoAdmin?'':'display: none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.remarks']}" rendered="#{manageOutputView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageOutputView.isRdoAdmin?'':'display: none;'}">
					<p:message for="remarks"/>
					<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="7" counter="display" maxlength="1000" rendered="#{manageOutputView.isRdoAdmin == true}"
											value="#{manageOutputView.selectedOutputHeader_q.remarks}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
                      <br/>
      				  <h:outputText id="display" class="p-d-block" />
			</div>	
			<div class="ui-g-4 ui-md-3 ui-lg-2 riForm-item-title">
				Display Language
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-10">
				<p:selectOneMenu title="Display Language" value="#{manageOutputView.selectedOutputHeader_p.language}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="English" itemValue="E"/>	   
					<f:selectItem itemLabel="Chinese" itemValue="C"/>
					<p:ajax event="change" listener="#{manageOutputView.genCitation()}" update="apa"/>
				</p:selectOneMenu>
			</div>	
		</div>
		<br/>
		<div class="form-sub-title">
				<i class="fas fa-tag" style="margin-right:5px;"></i>General Information
		</div>
		<hr/>
		
		<div class="ui-g">
			<div class="ui-g-12 ui-md-3 ui-lg-4 riForm-item-title">
					Research Output Type
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-8">
					<p:message for="sap_output_type"/>
					<p:selectOneMenu id="sap_output_type" title="Research Output Type" label="Research Output Type" value="#{manageOutputView.selectedOutputHeader_p.sap_output_type}" 
											disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.outputTypeList}" var="o" 
						itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						<p:ajax event="change" 
								update="researchActivity title_paper_art vol_issue apa issn page_num_from page_num_to articleNo place publisher name_other_editors name_other_pos is_intl_conf columnTable
											open_access_stat_label open_access_stat_select open_access_payment_label open_access_payment_select  open_access_apc_label open_access_apc_select
											open_access_apc_payment_label open_access_apc_payment_select apc_val_label apc_val_box
											open_access_art_acc_date_label open_access_art_acc_date_box open_access_emb_end_date_label open_access_emb_end_date_box open_access_emb_period_label open_access_emb_period_box" 
								listener="#{manageOutputView.resetResearchActivityList()}"/>
					</p:selectOneMenu>
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-4 riForm-item-title">
					Journal Publication Discipline
					<br/>
					<span style="color:#0277BD;">(Optional, for Chinese Publication only.)</span>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-8">
					<p:selectOneMenu title="Journal Publication Discipline" value="#{manageOutputView.selectedOutputHeader_p.journal_publication_discipline}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.disciplineList}" var="o" 
						itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
					</p:selectOneMenu>
			</div>	
			
			<div class="ui-g-12 ui-md-3 ui-lg-4 riForm-item-title">
					Type of Research Activity
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-8">
					<h:panelGroup id="researchActivity">
						<p:message for="sap_refered_journal"/>
						<p:selectOneMenu id="sap_refered_journal" 
												title="Type of Research Activity" 
												label="Type of Research Activity" 
												value="#{manageOutputView.selectedOutputHeader_p.sap_refered_journal}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{manageOutputView.researchTypeList}" var="o" 
												itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" />
							<p:ajax event="valueChange" update="open_access_stat_label open_access_stat_select open_access_payment_label open_access_payment_select open_access_apc_label open_access_apc_select 
												open_access_apc_payment_label open_access_apc_payment_select apc_val_label apc_val_box
												open_access_art_acc_date_label open_access_art_acc_date_box open_access_emb_end_date_label open_access_emb_end_date_box open_access_emb_period_label open_access_emb_period_box"/>
						</p:selectOneMenu>
					</h:panelGroup>
			</div>	
			
			<div class="ui-g-12 ui-md-3 ui-lg-4 riForm-item-title">
					Author Publication By-line EdUHK
					<br/>
				<span style="color:#0277BD;">(the publication affiliated to EdUHK)</span>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-8">
				<h:panelGroup id="ied_work_ind_ans_n">
					<p:outputLabel id="ied_work_ind_ans_n_label" value="This output will not be counted in the CDCF." style="border-left:8px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:1px 4px; margin-bottom:5px; display:block; width:90%;" 
										rendered="#{manageOutputView.selectedOutputHeader_p.ied_work_ind eq 'N'}"/>
				</h:panelGroup>
				<p:message for="ied_work_ind"/>
				<p:selectOneMenu id="ied_work_ind" 
										title="Authory Publication By-line EdUHK" 
										label="Authory Publication By-line EdUHK" 
										value="#{manageOutputView.selectedOutputHeader_p.ied_work_ind}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					<p:ajax event="valueChange" update="ied_work_ind_ans_n"/>
				</p:selectOneMenu>	
			</div>	
			
			<div class="ui-g-12 ui-md-3 ui-lg-4 riForm-item-title">
					Is international conference?
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-8">
					<p:message for="is_intl_conf"/>
					<p:selectOneMenu id="is_intl_conf" 
											title="Is international conference?"
											label="Is international conference?"
											 value="#{manageOutputView.selectedOutputHeader_p.is_intl_conf}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	    
					</p:selectOneMenu>
			</div>		
			
			<div class="ui-g-12 ui-md-3 ui-lg-4 riForm-item-title">
					Is the project(s) for creating the research output fully/partially funded by RGC?
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-2">
					<p:message for="is_rgc_proj"/>
					<p:selectOneMenu id="is_rgc_proj" 
											title="Is the project(s) for creating the research output fully/partially funded by RGC?"
											label="Is the project(s) for creating the research output fully/partially funded by RGC?"
											 value="#{manageOutputView.selectedOutputHeader_p.is_rgc_proj}" 
											 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	    
						<p:ajax event="valueChange" 
									update="rgc_proj_num_label rgc_proj_num_input 
												open_access_stat_label open_access_stat_select open_access_payment_label open_access_payment_select  open_access_apc_label open_access_apc_select 
												open_access_apc_payment_label open_access_apc_payment_select apc_val_label apc_val_box
												open_access_art_acc_date_label open_access_art_acc_date_box open_access_emb_end_date_label open_access_emb_end_date_box open_access_emb_period_label open_access_emb_period_box"/>
					</p:selectOneMenu>
			</div>		
			
			<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
				<h:panelGroup id="rgc_proj_num_label">
					<p:outputLabel value="Project number(s) of the RGC funded project(s)." rendered="#{manageOutputView.selectedOutputHeader_p.is_rgc_proj eq 'Y'}"/>
					<p:outputLabel value="(e.g. C1006-20WX, R1039-20, 4021-PPR-09, EdUHK7018-SPPR-11, 18300933, etc)" style="color:#0277BD; font-size:smaller;" rendered="#{manageOutputView.selectedOutputHeader_p.is_rgc_proj eq 'Y'}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-3">
				<h:panelGroup id="rgc_proj_num_input">
					<p:message for="rgc_proj_num"/>
					<p:inputText id="rgc_proj_num" maxlength="200"
										label="Project number of the RGC funded project(s)" title="Project number of the RGC funded project(s)"
										style="width:99%;" value="#{manageOutputView.selectedOutputHeader_p.rgc_proj_num}"
										rendered="#{manageOutputView.selectedOutputHeader_p.is_rgc_proj eq 'Y'}"
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					</p:inputText>
				</h:panelGroup>
			</div>	
			
			<div class="ui-g-12 ui-md-12 ui-lg-8 riForm-item-title">
					Is this research output related to the enhancement of teaching and learning in higher education/teacher education?<br/>
					<span style="color:#0277BD;">(e.g. The research publication directly impacts higher education teaching and learning)</span>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-4">
					<p:message for="is_enh_high_edu"/>
					<p:selectOneMenu id="is_enh_high_edu" 
											title="Is this research output related to the enhancement of teaching and learning in higher education/teacher education?" 
											label="Is this research output related to the enhancement of teaching and learning in higher education/teacher education?" 
											value="#{manageOutputView.selectedOutputHeader_p.is_enh_high_edu}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>					
			</div>	
		</div>
		<br/>
		<div class="form-sub-title">
		<i class="fas fa-tag" style="margin-right:5px;"></i>Citation Preview
		</div>
		<hr/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12">
				<p:outputLabel style="color:#0277BD; font-weight:700;" value="(In #{manageOutputView.citation} format)"/>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="padding:0 !important;">
					<p:outputLabel id="apa" style="display: block; white-space: normal; color: #827717;" value="#{manageOutputView.defaultAPA_html}" escape="false"></p:outputLabel>
			</div>
		</div>
			
		<h:panelGroup id="customCitationPanel">
			<div class="ui-g" style="border-top:1px dashed #888;">
				<div class="ui-g-6 ui-md-6 ui-lg-6" style="text-align:left;">
					<p:outputLabel id="customCitationTitle" style="color:#0277BD; font-weight:700;" value="(In custom format)" rendered="#{manageOutputView.hasCustomCitation eq 'Y'}"/>
				</div>
				<div class="ui-g-6 ui-md-6 ui-lg-6" style="text-align:right;">
					<p:commandButton id="btn_addCustomCitation" value="Custom Citation" styleClass="ui-button-success ui-button-flat" rendered="#{manageOutputView.hasCustomCitation ne 'Y'}"
											action="#{manageOutputView.setHasCustomCitation('Y')}" update="customCitationPanel"/>
					<p:commandButton id="btn_removeCustomCitation" value="Remove Custom Citation" styleClass="ui-button-danger ui-button-flat" rendered="#{manageOutputView.hasCustomCitation eq 'Y'}"
											action="#{manageOutputView.setHasCustomCitation('N')}" update="customCitationPanel"/>
				</div>
				<div class="ui-g-12 ui-md-12 ui-lg-12" style="padding:0 !important;">
				<p:textEditor id="customCitationText" value="#{manageOutputView.selectedOutputAddl_p.custom_citation}" height="100" rendered="#{manageOutputView.hasCustomCitation eq 'Y'}">
					<f:facet name="toolbar">
			             <span class="ql-formats">
			                <button class="ql-bold"></button>
			                <button class="ql-italic"></button>
			                <button class="ql-underline"></button>
			              </span>
			        </f:facet>
				</p:textEditor>
				</div>
			</div>
		</h:panelGroup>

		<div class="form-sub-title">
				<i class="fas fa-tag" style="margin-right:5px;"></i>Output Information
		</div>
		<hr/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-title">
					Title of Research Output <span style="color:#0277BD">(e.g. title of published article)</span>
					<br/>
					<span style="color:#0277BD">(For Review of books or software, e.g. Reviewer of a book / Reviewer of software, or 書集評審／軟件評審)</span>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:message for="title_jour_book"/>
					<p:inputText id="title_jour_book" class="chinese-500" title="Title of Research Output 1st line" label="Title of Research Output 1st line" style="width:99%;" value="#{manageOutputView.selectedOutputHeader_p.title_jour_book}"
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
					<hr style="visibility:hidden; margin:0;"/>
					<p:message for="output_title_continue"/>
					<p:inputText id="output_title_continue" class="chinese-500" title="Title of Research Output 2nd line" label="Title of Research Output 2nd line" style="width:99%;" value="#{manageOutputView.selectedOutputHeader_p.output_title_continue}" maxlength="500" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>	
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-title">
					Name of Publication /Conference/Journal in which the output appears <span style="color:#0277BD">(e.g. title of the journal)</span>
					<!-- <br/>-->
					<!--  <span style="color:#0277BD">(For Conference Papers, e.g. Keynote speech presented at / Paper presented at Learning Conference" or e.g. 主講論文發表於 / 論文發表於學童文學研究會)</span>-->
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:message for="title_paper_art"/>
					<p:inputText id="title_paper_art" class="chinese-500" label="Name of Publication /Conference/Journal in which the output appears" style="width:99%" value="#{manageOutputView.selectedOutputHeader_p.title_paper_art}" maxlength="500" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
			</div>
		
			<div class="ui-g-4 ui-md-2 ui-lg-2 riForm-item-title">
					ISSN. <br/><span style="color:#0277BD">(e.g. 1050-124X)</span>
			</div>
			<div class="ui-g-8 ui-md-4 ui-lg-4">
					<p:message for="issn"/>
					<p:inputText id="issn" label="ISSN" title="ISSN.  For example: 1050-124X" value="#{manageOutputView.selectedOutputHeader_p.issn}" maxlength="9" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					</p:inputText>
			</div>
			<div class="ui-g-4 ui-md-2 ui-lg-2 riForm-item-title">
					EISSN. <br/><span style="color:#0277BD">(e.g. 1050-124X)</span>
			</div>
			<div class="ui-g-8 ui-md-4 ui-lg-4">
					<p:message for="eissn"/>
					<p:inputText id="eissn" label="EISSN" title="EISSN.  For example: 1050-124X" value="#{manageOutputView.selectedOutputHeader_p.eissn}" maxlength="9" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					</p:inputText>
			</div>

			<div class="ui-g-4 ui-md-3 ui-lg-2 riForm-item-title">
					ISBN.  <br/><span style="color:#0277BD">(e.g. 0937175595)</span>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
					<p:message for="isbn"/>
					<p:inputText id="isbn" label="ISBN" title="ISBN.  For example: 0937175595" value="#{manageOutputView.selectedOutputHeader_p.isbn}" 
										maxlength="13" 
										onkeypress="return (event.charCode != 45);"
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
					</p:inputText>
			</div>
			<div class="ui-g-4 ui-md-3 ui-lg-2 riForm-item-title">
					Article No.
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
					<p:message for="articleNo"/>
					<p:inputText id="articleNo" label="Article No." title="Article No." value="#{manageOutputView.selectedOutputHeader_p.article_num}" maxlength="20" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
			</div>
			
			
			<div class="ui-g-4 ui-md-3 ui-lg-2 riForm-item-title">
					Volume (Issue)
					<br/>
					<span style="color:#0277BD">(e.g. 27(4), 第24期)</span>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
					<p:message for="vol_issue"/>
					<p:inputText id="vol_issue" label="Volume (Issue)" title="Volume (Issue)" value="#{manageOutputView.selectedOutputHeader_p.vol_issue}" maxlength="30"
									disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>		
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Page No.
					<br/>
					<span style="color:#0277BD">(e.g. 1-10, 頁1-10)</span>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
					<p:message for="page_num_from"/>
					<p:message for="page_num_to"/>
					<p:inputText id="page_num_from" label="Page No. - From" title="Page No. - From" value="#{manageOutputView.selectedOutputHeader_p.page_num_from}" maxlength="20" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText> - 
					<p:inputText id="page_num_to" label="Page No. - To" title="Page No. - To" value="#{manageOutputView.selectedOutputHeader_p.page_num_to}" maxlength="20" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>		
			</div>					
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Date
					<br/>
					<span style="color:#0277BD">(mm/yyyy)</span>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
					<p:message for="from_month"/>
					<p:message for="from_year"/>
					<h:outputText style="display: inline-block; width:50px; vertical-align: super;" value="From:"/>
					<p:selectOneMenu id="from_month" title="From month" label="From month" value="#{manageOutputView.selectedOutputHeader_p.from_month}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.monthList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
						<p:ajax event="change" listener="#{manageOutputView.genCitation()}" update="apa columnTable"/>
					</p:selectOneMenu>
					<p:selectOneMenu id="from_year" title="From year" label="From year" value="#{manageOutputView.selectedOutputHeader_p.from_year}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.yearList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
						<p:ajax event="change" listener="#{manageOutputView.genCitation()}" update="apa from_month columnTable"/>					
					</p:selectOneMenu>
					<br/>
					<p:message for="to_month"/>
					<p:message for="to_year"/>
					<h:outputText style="display: inline-block; width:50px; vertical-align: super;" value="To:"/>
					<p:selectOneMenu id="to_month" title="To month" label="To month" value="#{manageOutputView.selectedOutputHeader_p.to_month}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.monthList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
					</p:selectOneMenu>
					<p:selectOneMenu id="to_year" title="To year" label="To year" value="#{manageOutputView.selectedOutputHeader_p.to_year}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.yearList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
					</p:selectOneMenu>
			</div>
			<div class="ui-g-4 ui-md-3 ui-lg-2 riForm-item-title">
					Place
					<br/>
					<span style="color:#0277BD">(e.g. "Paris, France", "Hong Kong", "香港，中國")</span>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
					<p:message for="place"/>
					<p:inputText id="place" label="Place" title="Place" value="#{manageOutputView.selectedOutputHeader_p.city}" maxlength="50" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>		
			</div>
		</div>
		<!-- Open Access -->
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			<!-- Open Access Status -->
			<div class="ui-g-4 ui-md-6 ui-lg-2 riForm-item-title" style="padding-bottom:0px;">
				<h:panelGroup id="open_access_stat_label">
					<p:outputLabel value="Open Access Status" rendered="#{manageOutputView.requireOpenAccess()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-10">
				<h:panelGroup id="open_access_stat_select" style="padding-bottom:0px;">
					<p:message for="open_access_stat"/>
					<p:selectOneMenu id="open_access_stat" 
											title="Open Access Status"
											label="Open Access Status"
											 value="#{manageOutputView.selectedOutputHeader_p.open_access_stat}" 
											 rendered="#{manageOutputView.requireOpenAccess()}"
											 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="Immediate Open Access" itemValue="I"/>
						<f:selectItem itemLabel="Embargoed Open Access" itemValue="E"/>	    
						<f:selectItem itemLabel="Non-open Access" itemValue="N"/>	
						<p:ajax event="valueChange" update="open_access_payment_label open_access_payment_select  open_access_apc_label open_access_apc_select open_access_apc_payment_label open_access_apc_payment_select apc_val_label apc_val_box open_access_art_acc_date_label open_access_art_acc_date_box open_access_emb_end_date_label open_access_emb_end_date_box open_access_emb_period_label open_access_emb_period_box"/>
					</p:selectOneMenu>	
				</h:panelGroup>
			</div>
			<!-- open_access_apc -->
			<div class="ui-g-12 ui-md-6 ui-lg-8 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_apc_label" style="#{manageOutputView.requireOpenAccessApc() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Is Article Processing Charge (APC) required for publishing the output in open access model?" 
										rendered="#{manageOutputView.requireOpenAccessApc()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_apc_select" style="#{manageOutputView.requireOpenAccessApc() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="open_access_apc"/>
					<p:selectOneMenu id="open_access_apc" 
											title="Is Article Processing Charge (APC) required for publishing the output in open access model?"
											label="Is Article Processing Charge (APC) required for publishing the output in open access model?"
											 value="#{manageOutputView.selectedOutputHeader_p.open_access_apc}" 
											 rendered="#{manageOutputView.requireOpenAccessApc()}"
											 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	    
						<p:ajax event="valueChange" update="open_access_apc_payment_label open_access_apc_payment_select apc_val_label apc_val_box"/>
					</p:selectOneMenu>	
				</h:panelGroup>
			</div>
			<!-- open_access_apc_payment -->
			<div class="ui-g-12 ui-md-6 ui-lg-8 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_apc_payment_label" style="#{manageOutputView.requireOpenAccessApcPayment() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Is the open access article upon the payment made under financial arrangement(s) other than Article Processing Charge (APC), e.g. Open Access Membership?" 
										rendered="#{manageOutputView.requireOpenAccessApcPayment()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_apc_payment_select" style="#{manageOutputView.requireOpenAccessApcPayment() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="open_access_apc_payment"/>
					<p:selectOneMenu id="open_access_apc_payment" 
											title="Is the open access article upon the payment made under financial arrangement(s) other than Article Processing Charge (APC), e.g. Open Access Membership?"
											label="Is the open access article upon the payment made under financial arrangement(s) other than Article Processing Charge (APC), e.g. Open Access Membership?"
											 value="#{manageOutputView.selectedOutputHeader_p.open_access_apc_payment}" 
											 rendered="#{manageOutputView.requireOpenAccessApcPayment()}"
											 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	    
						<p:ajax event="valueChange" update="open_access_payment_select"/>
					</p:selectOneMenu>	
				</h:panelGroup>
			</div>
			<!-- open_access_payment -->
			<div class="ui-g-12 ui-md-6 ui-lg-8 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_payment_label" style="#{manageOutputView.requireOpenAccessPayment() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Any payment made by EdUHK/author(s) affiliated to EdUHK?" 
										rendered="#{manageOutputView.requireOpenAccessPayment()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_payment_select" style="#{manageOutputView.requireOpenAccessPayment() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="open_access_payment"/>
					<p:selectOneMenu id="open_access_payment" 
											title="Any payment made by EdUHK/author(s) affiliated to EdUHK?"
											label="Any payment made by EdUHK/author(s) affiliated to EdUHK?"
											 value="#{manageOutputView.selectedOutputHeader_p.open_access_payment}" 
											 rendered="#{manageOutputView.requireOpenAccessPayment()}"
											 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P' || manageOutputView.disableOpenAccessPayment()}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	    
						<p:ajax event="valueChange" update=""/>
					</p:selectOneMenu>	
				</h:panelGroup>
			</div>
			<!-- Actual Amount of APC -->
			<div class="ui-g-12 ui-md-6 ui-lg-8 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="apc_val_label" style="#{manageOutputView.requireOpenAccessApcPayment() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Actual amount of Article Processing Charge (APC) paid to the publisher (in HK dollars)" 
										rendered="#{manageOutputView.requireOpenAccessApcPayment()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="apc_val_box" style="#{manageOutputView.requireOpenAccessApcPayment() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="apc_val"/>
					<p:inputNumber  id="apc_val" 
									title="Actual amount of Article Processing Charge (APC) paid to the publisher (in HK dollars)" 
									label="Actual amount of Article Processing Charge (APC) paid to the publisher (in HK dollars)" symbol="HKD"
									maxValue="9999999999" minValue="0" decimalPlaces="2" 
									value="#{manageOutputView.selectedOutputHeader_p.apc_val}" 
									rendered="#{manageOutputView.requireOpenAccessApcPayment()}"
									disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}"/>					    
				</h:panelGroup>
			</div>
			<!-- Article Acceptance Date -->
			<div class="ui-g-4 ui-md-6 ui-lg-8 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_art_acc_date_label" style="#{manageOutputView.requireOpenAccessEmb() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Article Acceptance Date" rendered="#{manageOutputView.requireOpenAccessEmb()}"/>
					<p:outputLabel value="(dd/mm/yyyy)" style="color:#0277BD" rendered="#{manageOutputView.requireOpenAccessEmb()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_art_acc_date_box" style="#{manageOutputView.requireOpenAccessEmb() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="open_access_art_acc_day"/>
					<p:message for="open_access_art_acc_month"/>
					<p:message for="open_access_art_acc_year"/>
					<p:selectOneMenu id="open_access_art_acc_day" title="Article Acceptance Date day" label="Article Acceptance Date day" 
											value="#{manageOutputView.selectedOutputHeader_p.open_access_art_acc_day}" 
											rendered="#{manageOutputView.requireOpenAccessEmb()}"
											disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.articleAcceptanceDayList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
						<p:ajax event="change"/>					
					</p:selectOneMenu>	
					<p:selectOneMenu id="open_access_art_acc_month" title="Article Acceptance Date month" label="Article Acceptance Date month" 
											value="#{manageOutputView.selectedOutputHeader_p.open_access_art_acc_month}" 
											rendered="#{manageOutputView.requireOpenAccessEmb()}"
											disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.monthList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
						<p:ajax event="change" update="open_access_art_acc_day"/>		
					</p:selectOneMenu>	
					<p:selectOneMenu id="open_access_art_acc_year" title="Article Acceptance Date year" label="Article Acceptance Date year" 
											value="#{manageOutputView.selectedOutputHeader_p.open_access_art_acc_year}" 
											rendered="#{manageOutputView.requireOpenAccessEmb()}"
											disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.yearList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
						<p:ajax event="change" update="open_access_art_acc_day"/>					
					</p:selectOneMenu>	
				</h:panelGroup>
			</div>
			<!-- Embargo End Date -->
			<div class="ui-g-4 ui-md-6 ui-lg-2 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_emb_end_date_label" style="#{manageOutputView.requireOpenAccessEmb() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Embargo End Date" rendered="#{manageOutputView.requireOpenAccessEmb()}"/>
					<p:outputLabel value="(mm/yyyy)" style="color:#0277BD" rendered="#{manageOutputView.requireOpenAccessEmb()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_emb_end_date_box" style="#{manageOutputView.requireOpenAccessEmb() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="open_access_emb_end_month"/>
					<p:message for="open_access_emb_end_year"/>
					<p:selectOneMenu id="open_access_emb_end_month" title="Embargo End Date month" label="Embargo End Date month" 
											value="#{manageOutputView.selectedOutputHeader_p.open_access_emb_end_month}" 
											rendered="#{manageOutputView.requireOpenAccessEmb()}"
											disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.monthList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
					</p:selectOneMenu>		
					<p:selectOneMenu id="open_access_emb_end_year" title="Embargo End Date year" label="Embargo End Date year" 
											value="#{manageOutputView.selectedOutputHeader_p.open_access_emb_end_year}" 
											rendered="#{manageOutputView.requireOpenAccessEmb()}"
											disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.yearList}" var="o" 
											itemLabel="#{o}" itemValue="#{o}" />
					</p:selectOneMenu>			 
				</h:panelGroup>
			</div>
			<!-- Embargo Period -->
			<div class="ui-g-4 ui-md-6 ui-lg-2 riForm-item-title" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_emb_period_label" style="#{manageOutputView.requireOpenAccessEmb() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:outputLabel value="Embargo Period" rendered="#{manageOutputView.requireOpenAccessEmb()}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-4" style="padding-bottom:0px; padding-top:0px;">
				<h:panelGroup id="open_access_emb_period_box" style="#{manageOutputView.requireOpenAccessEmb() ? 'display: block; padding-bottom:8px; padding-top:8px;':''}">
					<p:message for="open_access_emb_period_month"/>
					<p:message for="open_access_emb_period_year"/>
					<p:spinner id="open_access_emb_period_month" style="margin-right:7px; width:100px"
								 title="Embargo Period month" label="Embargo Period month"
								 value="#{manageOutputView.selectedOutputHeader_p.open_access_emb_period_month}" min="0" max="11"
								 rendered="#{manageOutputView.requireOpenAccessEmb()}"
								 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}"/>
					<p:outputLabel value="month," rendered="#{manageOutputView.requireOpenAccessEmb()}" style="margin-right:7px;"/>
					<p:spinner id="open_access_emb_period_year" style="margin-right:7px; width:120px"
								 title="Embargo Period year" label="Embargo Period year"
								 value="#{manageOutputView.selectedOutputHeader_p.open_access_emb_period_year}" min="0" max="999"
								 rendered="#{manageOutputView.requireOpenAccessEmb()}"
								 disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}"/>
					<p:outputLabel value="year" rendered="#{manageOutputView.requireOpenAccessEmb()}"/>
				</h:panelGroup>
			</div>
			<!-- Under Transformative Agreement? -->
			<div class="ui-g-12 ui-md-6 ui-lg-6 riForm-item-title" style="padding-bottom:0px;">
				<h:panelGroup id="is_tran_agrt_label">
					<p:outputLabel value="Under Transformative Agreement?" 
										rendered="#{manageOutputView.paramDataLevel eq 'C'}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-6" style="padding-bottom:0px;">
				<h:panelGroup id="is_tran_agrt_select">
					<p:message for="is_tran_agrt"/>
					<p:selectOneMenu id="is_tran_agrt" 
											title="Under Transformative Agreement?"
											label="Under Transformative Agreement?"
											 value="#{manageOutputView.selectedOutputHeader_p.is_tran_agrt}" 
											 rendered="#{manageOutputView.paramDataLevel eq 'C'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	    
					</p:selectOneMenu>	
				</h:panelGroup>
			</div>
			<!-- Actual Amount Paid to the Publisher for Open Access Publishing under Transformative Agreement -->
			<div class="ui-g-12 ui-md-6 ui-lg-6 riForm-item-title" style="padding-bottom:0px;">
				<h:panelGroup id="tran_agrt_val_label">
					<p:outputLabel value="Actual Amount Paid to the Publisher for Open Access Publishing under Transformative Agreement" 
										rendered="#{manageOutputView.paramDataLevel eq 'C'}"/>
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-6" style="padding-bottom:0px;">
				<h:panelGroup id="tran_agrt_val_box">
					<p:message for="tran_agrt_val"/>
					<p:inputNumber  id="tran_agrt_val" 
									title="Actual Amount Paid to the Publisher for Open Access Publishing under Transformative Agreement" 
									label="Actual Amount Paid to the Publisher for Open Access Publishing under Transformative Agreement" symbol="HKD"
									maxValue="9999999999" minValue="0" decimalPlaces="2" 
									value="#{manageOutputView.selectedOutputHeader_p.tran_agrt_val}" 
									rendered="#{manageOutputView.paramDataLevel eq 'C'}"/>					    
				</h:panelGroup>
			</div>
	</div>
	
	<div class="ui-g">
			<div class="ui-g-2 ui-md-3 ui-lg-2 riForm-item-title">
					DOI
			</div>
			<div class="ui-g-10 ui-md-9 ui-lg-10">
					<p:message for="doi"/>
					<p:inputText id="doi" label="DOI" title="DOI" style="width:90%" value="#{manageOutputView.selectedOutputHeader_p.doi}" maxlength="100" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
			</div>
		
			<div class="ui-g-2 ui-md-3 ui-lg-2 riForm-item-title">
					URL
			</div>
			<div class="ui-g-10 ui-md-9 ui-lg-10">
					<p:message for="url"/>
					<p:inputText id="url" label="URL" title="URL" style="width:90%" value="#{manageOutputView.selectedOutputHeader_p.fulltext_url}" maxlength="500" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
			</div>		
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Publisher/Conference Organiser(s)/Others
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="publisher"/>
					<p:inputText id="publisher" class="chinese-500" label="Publisher/Conference Organiser(s)/Others" title="Publisher/Conference Organiser(s)/Others" style="width:99%" value="#{manageOutputView.selectedOutputHeader_p.publisher}" maxlength="500" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
			</div>		
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Details of the Output
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-10">
					<p:message for="other_details"/>
					<p:inputText id="other_details" title="Details of the Output 1st line" label="Details of the Output 1st line" style="width:99%" 
										value="#{manageOutputView.selectedOutputHeader_p.other_details}" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>		
					<hr style="visibility:hidden; margin:0;"/>
					<p:message for="other_details_continue"/>
					<p:inputText id="other_details_continue" title="Details of the Output 2nd line" label="Details of the Output 2nd line" style="width:99%"
									value="#{manageOutputView.selectedOutputHeader_p.other_details_continue}" 
									disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>		
			</div>	
			<br/>
			<div class="ui-g-12 ui-md-12 ui-lg-4 riForm-item-title">
					List of editor(s) in sequential order as appeared in the research output
					<br/>
					<span style="color:#0277BD">(e.g. In C. Reynolds, &amp; A. Griffith (Eds.))</span>
					<br/>
					<span style="color:#0277BD">(e.g. 輯於黃加文、陳小詩編 or 輯於黃加文、陳小詩和鄧志強編)</span>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-8">
					<p:message for="name_other_editors"/>
					<p:inputText id="name_other_editors" title="List of editor(s) in sequential order as appeared in the research output" 
										label="List of editor(s) in sequential order as appeared in the research output" style="width:99%" 
										value="#{manageOutputView.selectedOutputHeader_p.name_other_editors}"
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>
			</div>	
			
			<div class="ui-g-12 ui-md-12 ui-lg-4 riForm-item-title">
					List of author(s) / contributor(s) in sequential order as appeared in the research output
					<br/>
					<span style="color:#0277BD">(e.g. Chan, S.S.F., &amp; Tang, N.C.K.)</span>
					<br/>
					<span style="color:#0277BD">(e.g. 黃加文、陳小詩 or 黃加文、陳小詩和鄧志強)</span>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-8">
					<p:message for="name_other_pos"/>
					<p:inputText id="name_other_pos" class="chinese-500" title="List of author(s) / contributor(s) in sequential order as appeared in the research output" 
										label="List of author(s) / contributor(s) in sequential order as appeared in the research output" style="width:99%" 
										value="#{manageOutputView.selectedOutputHeader_p.name_other_pos}" maxlength="500" 
										disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<p:ajax event="valueChange" listener="#{manageOutputView.genCitation()}" update="apa"/>
					</p:inputText>		
			</div>					
			
			<div class="ui-g-12 ui-md-12 ui-lg-4 riForm-item-title">
					Sector of the Output
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-8">
					<p:message for="sch_dtl_code"/>
					<p:selectOneMenu id="sch_dtl_code" title="Sector of the Output" label="Sector of the Output" value="#{manageOutputView.selectedOutputHeader_p.sch_dtl_code}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.eduSectorList}" var="o" 
											itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						<p:ajax event="change" listener="#{manageOutputView.resetDAList()}" update="da_dtl_code"/>
					</p:selectOneMenu>
			</div>	
	
					
			<div class="ui-g-12 ui-md-12 ui-lg-4 riForm-item-title">
					Disciplinary Area of the Output
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-8">
					<p:message for="da_dtl_code"/>
					<p:selectOneMenu id="da_dtl_code" title="Disciplinary Area of the Output" label="Disciplinary Area of the Output" value="#{manageOutputView.selectedOutputHeader_p.da_dtl_code}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.disAreaList}" var="o" 
											itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						<p:ajax event="change" update="other_da_dtl"/>
					</p:selectOneMenu>
			</div>							
				
			<div class="ui-g-12 ui-md-12 ui-lg-4 riForm-item-title">
					Other Disciplinary Area
			</div>
			
			<div class="ui-g-12 ui-md-12 ui-lg-8">
						<p:message for="other_da_dtl"/>
						<p:inputText id="other_da_dtl" label="Other Disciplinary Area" style="width:99%" value="#{manageOutputView.selectedOutputHeader_p.other_da_dtl}" 
											disabled="#{!manageOutputView.isOtherDisArea() || manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
	              		</p:inputText>		
			</div>
			
			<div class="ui-g-12 ui-md-12 ui-lg-4 riForm-item-title">
					SDGs Information
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-8">
					<p:message for="sdg_info"/>
					<p:selectCheckboxMenu  id="sdg_info" title="SDGs Information" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}"
							label="-- Please select --" 
							filter = "true" filterMatchMode= "startsWith" filterNormalize = "true" multiple = "true"
							style="min-width: 15rem"
							var="c"
							value="#{manageOutputView.selectedOutputHeader_p.sdg_code_list}">
						<f:selectItems value="#{manageOutputView.sdgList}" var ="sdg" itemLabel="#{sdg.pk.lookup_code} - #{sdg.description}" 
							itemValue="#{sdg.pk.lookup_code}" >
						  <p:column>
	                            <div class="select-item-content">
	                                <h:outputText value="#{sdg.pk.lookup_code}" />
	                            </div>
	                       </p:column>
						</f:selectItems>
					</p:selectCheckboxMenu>
			</div>
			
	
			
		

		</div>	
	<br/>
	<div class="form-sub-title">
			<i class="fas fa-tag" style="margin-right:5px;"></i>Collaborative Contributor(s) <span style="color:#0277BD">(please enter the collaborative name in chronological order to indicate the position in the research output)</span>
	</div>
	<hr/>
	<p:messages for="columnTable"/>
	
	<p:dataTable id="columnTable" value="#{manageOutputView.outputDetails_p_list}" var="col" widgetVar="columnTableWV" class="riFormTable"
					 rows="50" reflow="true"
					 rowsPerPageTemplate="10,20,50,100,200"
                     paginator="true"
                     paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     currentPageReportTemplate="Total Number of Contributor(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
					 tableStyle="table-layout:auto"
					 rowIndexVar="rowIndex">
					 	 
		<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel ne 'P'}">
			<f:facet name="header">Up</f:facet>
			<p:commandLink action="#{manageOutputView.moveColumnUp(rowIndex)}" rendered="#{rowIndex gt 0}"
						   update="columnTable" immediate="true"><i class="fas fa-caret-up icon-action" title="Move up"/>
			</p:commandLink>										
		</p:column>
		
		<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel ne 'P'}">
			<f:facet name="header">Dn.</f:facet>
			<p:commandLink action="#{manageOutputView.moveColumnDown(rowIndex)}" rendered="#{rowIndex lt manageOutputView.outputDetails_p_list.size()-1}"
						   update="columnTable" immediate="true"><i class="fas fa-caret-down icon-action" title="Move down"/>
			</p:commandLink>										
		</p:column>

		<p:column style="text-align:left; width:5em;">
			<f:facet name="header">No.</f:facet>
			<ui:fragment rendered="#{manageOutputView.isRiCreator(col.authorship_staff_no)}"><i class="fa fa-star faa-pulse animated-hover" title="No." style="font-size:9px; color:#f06524; vertical-align: middle;"></i></ui:fragment> #{rowIndex +1}							
		</p:column>
		
		<p:column style="text-align:left;">
			<f:facet name="header">#{formBundle['form.ri.creator.output']} Position in RI</f:facet>
			<p:message id="typeMsg" for="type"/>
			<p:selectOneMenu id="type" style="width:90%; white-space: pre-wrap;" value="#{col.non_ied_staff_flag}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
				<f:selectItem itemLabel="EdUHK Staff" itemValue="N"/>
				<f:selectItem itemLabel="Former Staff" itemValue="F"/>
				<f:selectItem itemLabel="Non EdUHK Staff" itemValue="Y"/>
				<f:selectItem itemLabel="EdUHK Student" itemValue="S"/>
				<p:ajax event="change" update="nameGroup cvGroup"/>
			</p:selectOneMenu>
		</p:column>		
		
		<p:column>
			<f:facet name="header">#{formBundle['form.ri.creator.output']}</f:facet>
			<h:panelGroup id="nameGroup">
			<p:message for="select_staff_recipient_name"/>
			<p:selectOneMenu id="select_staff_recipient_name" label="#{formBundle['form.ri.creator.output']}" style="width:90%" value="#{col.authorship_staff_no}" filter="true" filterMatchMode="startsWith" rendered="#{col.non_ied_staff_flag eq 'N'}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}" dynamic="true">
				<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
				<f:selectItems value="#{manageRIView.staffList}"/>
				<p:ajax event="change" update="cvGroup"/>
			</p:selectOneMenu>
			
			<p:message for="select_staff_past_name"/>
			<p:autoComplete id="select_staff_past_name" value="#{col.authorship_name}" style="#{manageOutputView.paramDataLevel eq 'C'?'width:70%':'width:90%'}"
							rendered="#{col.non_ied_staff_flag eq 'F'}"
							minQueryLength="3" 
							emptyMessage="No results"
							forceSelection="true"
							disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}"
                        	completeMethod="#{manageRIView.fsCompleteText}" scrollHeight="250">
                        	<p:ajax event="itemSelect" process="@this" />
                        	<p:ajax event="itemSelect" listener="#{manageOutputView.updateRowStaffNum(rowIndex)}" update="nameGroup"/>
            </p:autoComplete>
            <p:inputText id="select_staff_past_num" label="Staff No." style="#{manageOutputView.isRdoAdmin == true?'width:25%':'display:none'}" value="#{col.authorship_staff_no}" rendered="#{col.non_ied_staff_flag eq 'F'}" dynamic="true">
			</p:inputText>
			
			<p:message id="recipient_nameMsg" for="recipient_name"/>
			<p:inputText id="recipient_name" label="#{formBundle['form.ri.creator.output']}" style="width:90%" value="#{col.authorship_name}" 
								onkeypress="return (event.charCode != 59);"
								maxlength="80" rendered="#{col.non_ied_staff_flag ne 'N' &amp;&amp; col.non_ied_staff_flag ne 'F'}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
				<p:ajax/>
			</p:inputText>
			</h:panelGroup>
		</p:column>			
		<p:column>
			<f:facet name="header"><p:outputLabel id="authorshipTips" value="Authorship "><i class="fa fa-question-circle"/></p:outputLabel><p:tooltip for="authorshipTips" value="#{manageOutputView.authorshipTips}" showEffect="clip" hideEffect="fold" escape="false"/></f:facet>
			<p:selectOneMenu id="authorship" style="width:90%; white-space: pre-wrap;" value="#{col.authorship_type}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
				<f:selectItems value="#{manageOutputView.authorshipList}" var="a"
								   itemLabel="#{a.description} " itemValue="#{a.pk.lookup_code}"/>
				<p:ajax event="change" update="columnTable"/>
			</p:selectOneMenu>
			<p:selectOneMenu id="subAuthorship" style="width:90%; white-space: pre-wrap;" value="#{col.authorship_dtl_type}" rendered="#{manageOutputView.requireSubAuthorship(col.authorship_type) == true}" disabled="#{manageOutputView.isCreator == false || manageOutputView.canModify == false || manageOutputView.paramDataLevel eq 'P'}">
				<f:selectItems value="#{manageOutputView.subAuthorshipList}" var="b" 
								   itemLabel="#{b.description} " itemValue="#{b.pk.lookup_code}"/>
				<p:ajax event="change"/>
			</p:selectOneMenu>
		</p:column>			
		<p:column class="data-noprint" style="width:8em;">
			<h:panelGroup id="cvGroup">
				<p:linkButton id="btn_view_cv" outcome="/web/person" rendered="#{col.non_ied_staff_flag eq 'N' &amp;&amp; manageOutputView.getStaffProfilePid(col.authorship_staff_no) ne null}" value="#{formBundle['form.view.profile']}" target="_blank">
	  					<f:param name="pid" value="#{manageOutputView.getStaffProfilePid(col.authorship_staff_no)}"/>
	  			</p:linkButton>
  			</h:panelGroup>
		</p:column>	
		<p:column class="data-noprint" style="width:2em; text-align:left;" rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel ne 'P'}">
			<f:facet name="header">Del.</f:facet>
			<p:commandLink id="btn_delete" action="#{manageOutputView.deleteRow(rowIndex)}" 
						   update="columnTable" immediate="true"><i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
			</p:commandLink>	
			<p:blockUI block="contentPanel" trigger="btn_delete" />
		</p:column>									
	</p:dataTable>		
	<br/>
	<p:commandButton id="btn_add" icon="fas fa-plus" value="Add new contributor" 
											rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel ne 'P'}"
											 style="width:260px; margin-top:1px;"
											 action="#{manageOutputView.addRow()}"
											 update="columnTable"
											 immediate="true"
											 oncomplete="PF('columnTableWV').paginator.setPage(PF('columnTableWV').paginator.cfg.pageCount - 1);"/>
	<p:blockUI block="contentPanel" trigger="btn_add" />			
	<br/><br/>
	<h:panelGroup styleClass="button-panel">
		<p:linkButton value="#{bundle['action.back']}" outcome="manageOutput" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageOutputView.paramDataLevel eq 'M' &amp;&amp; manageOutputView.paramConsent eq ''}">		
			<f:param name="pid" value="#{manageOutputView.paramPid}"/>
		</p:linkButton>
		<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageOutputView.paramConsent ne ''}">		
			<f:param name="pid" value="#{manageOutputView.paramPid}"/>
			<f:param name="consent" value="#{manageOutputView.paramConsent}"/>
			<f:param name="tabpage" value="0"/>
		</p:linkButton>
		<p:commandButton id="btn_sava" value="#{formBundle['form.save']}" 
								  rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel ne 'C'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageOutputView.save}"
						  		  oncomplete="window.scrollTo(0,0);">
		</p:commandButton>	
		<p:commandButton id="btn_savaAndGen" value="#{(manageOutputView.selectedOutputHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}" 
								  rendered="#{manageOutputView.paramDataLevel eq 'C'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageOutputView.save}"
						  		  oncomplete="window.scrollTo(0,0);">
		</p:commandButton>			
		<p:commandButton id="btn_savaAndPublish" value="#{bundle['action.saveAndPublish']}" 
								  rendered="#{manageOutputView.isCreator == true &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageOutputView.saveAndPublishForm}"
						  		  oncomplete="window.scrollTo(0,0);">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.publish.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>			
		<p:commandButton id="btn_submit" value="#{formBundle['form.save']}" 
								  rendered="#{manageOutputView.isCreator == false &amp;&amp; manageOutputView.canModify == true &amp;&amp; manageOutputView.paramDataLevel eq 'M'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageOutputView.submitConsent}"
						  		  oncomplete="window.scrollTo(0,0);">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>		
		<p:commandButton id="btn_snapshot" value="#{formBundle['form.take.snapshot']}" rendered="#{manageOutputView.paramDataLevel eq 'P'}"
								  style="margin-right:5px;"
								  process="@this"
						  		  action="#{manageOutputView.takeSnapshot}"
						  		  oncomplete="window.scrollTo(0,0);">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.take.snapshot.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>	
		<p:commandButton id="btn_delete" value="#{formBundle['form.del']}" rendered="#{manageOutputView.canDelete == true &amp;&amp; manageOutputView.canModify == true}"
								  style="margin-right:5px; background:#D32F2F; border:1px solid #D32F2F;" 
								  process="@this"
						  		  action="#{manageOutputView.deleteForm}">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.del.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>	
        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
        </p:confirmDialog>
        <!--  <p:blockUI block="contentPanel" trigger="top_btn_sava" />
		<p:blockUI block="contentPanel" trigger="top_btn_savaAndPublish" />
		<p:blockUI block="contentPanel" trigger="top_btn_submit" />
		<p:blockUI block="contentPanel" trigger="top_btn_snapshot" />
		<p:blockUI block="contentPanel" trigger="top_btn_delete" />
		<p:blockUI block="contentPanel" trigger="btn_sava" />
		<p:blockUI block="contentPanel" trigger="btn_savaAndPublish" />
		<p:blockUI block="contentPanel" trigger="btn_submit" />
		<p:blockUI block="contentPanel" trigger="btn_snapshot" />
		<p:blockUI block="contentPanel" trigger="btn_delete" />-->
	</h:panelGroup>
	<p:scrollTop target="parent" threshold="100" styleClass="custom-scrolltop" icon="pi pi-arrow-up" />
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>