<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:event listener="#{manageProjectView.getCanModify}" type="preRenderView" />
		<f:event listener="#{manageProjectView.checkValid}" type="preRenderView" />
		<f:viewParam name="no" value="#{manageProjectView.paramNo}" />
		<f:viewParam name="pid" value="#{manageProjectView.paramPid}" />
		<f:viewParam name="dataLevel" value="#{manageProjectView.paramDataLevel}" />
		<f:viewParam name="consent" value="#{manageProjectView.paramConsent}" />
		<f:viewParam name="area_code" value="#{manageProjectView.paramArea_code}" />
		<f:viewParam name="source_id" value="#{manageProjectView.paramSource_id}" />
		<f:viewParam name="staff_number" value="#{manageProjectView.paramStaff_number}" />
	</f:metadata>
	<ui:define name="html_head">
		<script type="text/javascript" src="#{request.contextPath}/resources/js/jquery.chineselengthlimit.js"></script>
		<style>
			li.ui-state-disabled{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
	<h:outputScript>
	$(document).ready(function(){
		$('#editForm\\:btn_add').click(function () {
		    $('html, body').animate({scrollTop:$(document).height()}, 'slow');
		    return false;
		});
		$('.chinese-200').ChineseLengthLimit({ 
	       limitCount: 500, 
	      isByte: true
	   }); 
	
	 $('.chinese-200').trigger('checkLimit');
	 
	 $('.chinese-150').ChineseLengthLimit({ 
	       limitCount: 500, 
	      isByte: true
	   }); 
	
	 $('.chinese-150').trigger('checkLimit');
	});
	</h:outputScript>
	<p:panel id="contentPanel">
	<span class="admin-content-title" style="margin-bottom:0;"><i class="fas fa-project-diagram"></i> Manage Project</span>
	
	<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
	<p:scrollTop />
	<h:outputText value="You don't have access right." rendered="#{manageProjectView.hasAccessRight() == false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
	<h:form id="editForm" rendered="#{manageProjectView.hasAccessRight() == true}">
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-10">
				<p:linkButton value="#{bundle['action.back']}" outcome="manageProject" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageProjectView.paramDataLevel eq 'M' &amp;&amp; manageProjectView.paramConsent eq ''}">		
					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
				</p:linkButton>
				<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageProjectView.paramConsent ne ''}">		
					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
					<f:param name="consent" value="#{manageProjectView.paramConsent}"/>
					<f:param name="tabpage" value="1"/>
				</p:linkButton>
				<p:defaultCommand target="dummy"/>
				<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
				<p:commandButton id="top_btn_sava" value="#{formBundle['form.save']}" 
										  rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'C'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageProjectView.save}"
								  		  oncomplete="window.scrollTo(0,0);">
				</p:commandButton>	
				<p:commandButton id="top_btn_savaAndGen" value="#{(manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}" 
										  rendered="#{manageProjectView.paramDataLevel eq 'C'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageProjectView.save}"
								  		  oncomplete="window.scrollTo(0,0);">
				</p:commandButton>			
				<p:commandButton id="top_btn_savaAndPublish" value="#{bundle['action.saveAndPublish']}" 
										  rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageProjectView.saveAndPublishForm}"
								  		  oncomplete="window.scrollTo(0,0);">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.publish.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>			
				<p:commandButton id="top_btn_submit" value="#{formBundle['form.save']}" 
										  rendered="#{manageProjectView.isCreator == false &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{manageProjectView.submitConsent}"
								  		  oncomplete="window.scrollTo(0,0);">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>		
				<p:commandButton id="top_btn_snapshot" value="#{formBundle['form.take.snapshot']}" rendered="#{manageProjectView.paramDataLevel eq 'P'}"
										  style="margin-right:5px; margin-bottom:1px;"
										  process="@this"
								  		  action="#{manageProjectView.takeSnapshot}"
								  		  oncomplete="window.scrollTo(0,0);">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.take.snapshot.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>	
				<p:commandButton id="top_btn_delete" value="#{formBundle['form.del']}" rendered="#{manageProjectView.canDelete == true &amp;&amp; manageProjectView.canModify == true}"
										  style="margin-right:5px; margin-bottom:1px; background:#D32F2F; border:1px solid #D32F2F;" 
										  process="@this"
								  		  action="#{manageProjectView.deleteForm}">
					<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.del.desc']}" icon="pi pi-info-circle"/>
				</p:commandButton>	
				<p:linkButton id="btn_p_level" outcome="manageProject_edit" rendered="#{manageProjectView.paramDataLevel eq 'P' &amp;&amp; manageProjectView.checkSnapshotExists()}" 
								value="#{formBundle['form.ri.goto.lv.c']}" icon="pi pi-chevron-right" style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
  					<f:param name="pid" value="#{manageProjectView.getParamPid()}"/>
  					<f:param name="no" value="#{manageProjectView.selectedProjectHeader_q.project_no}"/>
  					<f:param name="dataLevel" value="C"/>
  				</p:linkButton>	
  				<p:linkButton id="btn_c_level" outcome="manageProject_edit" rendered="#{manageProjectView.paramDataLevel eq 'C'}" 
  								value="#{formBundle['form.ri.goto.lv.p']}" icon="pi pi-chevron-right" style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
  					<f:param name="pid" value="#{manageProjectView.getParamPid()}"/>
  					<f:param name="no" value="#{manageProjectView.selectedProjectHeader_q.project_no}"/>
  					<f:param name="dataLevel" value="P"/>
  				</p:linkButton>	
		        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true" width="350">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		        </p:confirmDialog>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-2" style="font-size:18px; font-weight:700; text-align: right;">
				<p:outputLabel value="#{formBundle['form.ri.status.lv.p']}" style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;" rendered="#{manageProjectView.paramDataLevel eq 'P'}"/>
				<p:outputLabel value="#{formBundle['form.ri.status.lv.c']}" style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;" rendered="#{manageProjectView.paramDataLevel eq 'C'}"/>
			</div>
		</div>
		<br/>
		<div class="form-sub-title">
		<i class="fas fa-tag" style="margin-right:5px;"></i>#{formBundle['form.ri.status']}
		</div>
		<hr/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="background:#00a2c7; font-size:18px; font-weight:700;">
				<p:outputLabel style="color:#1f1645;" value="#{formBundle['form.header.status']} " />
				<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{manageProjectView.selectedProjectHeader_q.publish_status}" rendered="#{manageProjectView.selectedProjectHeader_q.publish_status ne 'PUBLISHED'}"/>
				<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{manageProjectView.selectedProjectHeader_q.publish_status eq 'PUBLISHED'}"/>
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.at']} " />
				<h:outputText class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.last_modified_date}" >
					    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>	
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.by']} " />
				<p:outputLabel class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.last_modified_by}" />
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.at']} " />
				<h:outputText class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.last_published_date}" >
				    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>
			</div>

			<div class="ui-g-12 ui-md-3 ui-lg-3">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.by']} " />
				<p:outputLabel class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.last_published_by}" />
			</div>
			
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			
			<div class="ui-g-6 ui-md-4 ui-lg-2">
			<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.institute']}" />
			</div>
			<div class="ui-g-6 ui-md-8 ui-lg-4">
				<p:message for="inst_display_ind"/>
				<p:selectOneMenu id="inst_display_ind" required="#{manageProjectView.isRdoAdmin == true}"
											disabled="#{manageProjectView.isRdoAdmin == false || manageProjectView.paramDataLevel eq 'M'
														|| manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_GENERATED'
														|| manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_NOT_SEL'
														|| manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_SPEC'}" 
											title="#{formBundle['form.header.display.institute']}" 
											label="#{formBundle['form.header.display.institute']}"
											value="#{manageProjectView.selectedProjectHeader_q.inst_display_ind}">
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>
			</div>
			
			<div class="ui-g-6 ui-md-4 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute']}" />
			</div>
			<div class="ui-g-6 ui-md-8 ui-lg-4">
				<p:message for="inst_verified_ind"/>
				<p:selectOneMenu id="inst_verified_ind" required="#{manageProjectView.isRdoAdmin == true}" 
											disabled="#{manageProjectView.isRdoAdmin == false || manageProjectView.paramDataLevel eq 'M'
														|| manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_GENERATED'
														|| manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_NOT_SEL'
														|| manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_SPEC'}" 
											title="#{formBundle['form.header.verify.institute']}" 
											label="#{formBundle['form.header.verify.institute']}"
											value="#{manageProjectView.selectedProjectHeader_q.inst_verified_ind}">
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   					
						<!--  <p:ajax event="valueChange" update="inst_verified_date"/>-->
					</p:selectOneMenu>		
			</div>
			<!--
			<div class="ui-g-12 ui-md-3 ui-lg-2">			
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute.date']}" />
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:message for="inst_verified_date"/>
				<p:datePicker id="inst_verified_date" 
										title="#{formBundle['form.header.verify.institute.date']}" 
										label="#{formBundle['form.header.verify.institute.date']}" 
										value="#{manageProjectView.selectedProjectHeader_q.inst_verified_date}" 
										maxdate="#{manageProjectView.currentDate}"
										pattern="yyyy-M-d" showIcon="true" 
										disabled="#{manageProjectView.isRdoAdmin == false || manageProjectView.paramDataLevel eq 'M' || manageProjectView.selectedProjectHeader_q.inst_verified_ind eq 'N'}"/>			
			</div>
			-->
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.generate']}" />
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
				<p:outputLabel class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_gen_ind eq 'Y'?'Yes':'No'}"/>
				<p:outputLabel class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_gen_ind eq 'Y'?' — ':''}"/>
				<h:outputText class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_gen_date}" rendered="#{manageProjectView.selectedProjectHeader_q.cdcf_gen_ind eq 'Y'}">
				    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>
			</div>
			<div class="ui-g-4 ui-md-3 ui-lg-2">
					<h:outputText class="riForm-item-title" value="#{formBundle['form.cdcf.change']}" rendered="#{manageProjectView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
					<h:outputText class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_changed_ind eq 'Y'?'Yes':'No'}" rendered="#{manageProjectView.isRdoAdmin == true}"/>
					<p:linkButton id="btn_compare" outcome="riComparisonReport" rendered="#{manageProjectView.selectedProjectHeader_q.cdcf_changed_ind eq 'Y' &amp;&amp; manageProjectView.isRdoAdmin == true}" value="Compare Snapshot" style="margin-left:7px;" target="_blank">
	  					<f:param name="no" value="#{manageProjectView.selectedProjectHeader_q.project_no}"/>
	  					<f:param name="riType" value="project"/>
	  				</p:linkButton>	
			</div>
			
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.process']}" />
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
				<p:outputLabel class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_processed_ind eq 'Y'?'Yes':'No'}"/>
				<p:outputLabel class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_processed_ind eq 'Y'?' — ':''}"/>
				<h:outputText class="riForm-item-ans" value="#{manageProjectView.selectedProjectHeader_q.cdcf_processed_date}" rendered="#{manageProjectView.selectedProjectHeader_q.cdcf_processed_ind eq 'Y'}">
				    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>
			</div>
					
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.status']}" />
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-4">
				<p:message for="cdcf_status"/>
				<p:selectOneMenu id="cdcf_status" title="#{formBundle['form.cdcf.status']}" label="#{formBundle['form.cdcf.status']}" value="#{manageProjectView.selectedProjectHeader_q.cdcf_status}" 
											required="#{manageProjectView.isRdoAdmin == true}" 
											disabled="#{manageProjectView.isRdoAdmin == false || manageProjectView.paramDataLevel eq 'M'}">
						<f:selectItems value="#{manageProjectView.cdcfStatusList}"/>  
					</p:selectOneMenu>	
			</div>
			
			
			<div class="ui-g-4 ui-md-6 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="Eligible for reporting T630 Research Grants/Contracts" rendered="#{manageProjectView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-4">
				<p:selectOneMenu id="collab_t630" disabled="#{manageProjectView.isRdoAdmin == false || manageProjectView.paramDataLevel eq 'M'}"
											title="Eligible for reporting T630 Research Grants/Contracts" 
											label="Eligible for reporting T630 Research Grants/Contracts"
											value="#{manageProjectView.selectedProjectHeader_q.collab_t630}"
											rendered="#{manageProjectView.isRdoAdmin == true}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>			
			</div>
			
			<div class="ui-g-4 ui-md-6 ui-lg-2">
				<p:outputLabel class="riForm-item-title" value="Eligible for reporting T690 No. of Active Research Collaborative with Non-local Institutions" rendered="#{manageProjectView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-4">
				<p:selectOneMenu id="collab_t690" disabled="#{manageProjectView.isRdoAdmin == false || manageProjectView.paramDataLevel eq 'M'}"
											title="Eligible for reporting T690 No. of Active Research Collaborative with Non-local Institutions" 
											label="Eligible for reporting T690 No. of Active Research Collaborative with Non-local Institutions"
											rendered="#{manageProjectView.isRdoAdmin == true}"
											value="#{manageProjectView.selectedProjectHeader_q.collab_t690}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>			
			</div>
			
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			
			<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageProjectView.paramDataLevel eq 'M'?'':'display:none;'}">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.ri.profile']}" style="padding-right:20px;" rendered="#{manageProjectView.paramDataLevel eq 'M'}"/>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageProjectView.paramDataLevel eq 'M'?'':'display:none;'}">
				<h:panelGroup id="displayRIGroup">
					<p:selectOneMenu title="#{formBundle['form.header.display.ri.profile']}" value="#{manageProjectView.selectedProjectDetails_q.display_ind}" 
											rendered="#{manageProjectView.paramDataLevel eq 'M'}"
											disabled="#{manageProjectView.canModify == false || manageProjectView.selectedProjectDetails_q.consent_ind ne 'Y'}">
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>
				</h:panelGroup>
			</div>	
			
			<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageProjectView.isContributor == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'?'':'display:none;'}">
				<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.consent.ri']}" style="padding-right:20px;" rendered="#{manageProjectView.isContributor == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'}"/>
			</div>
			<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageProjectView.isContributor == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'?'':'display:none;'}">	
				<p:selectOneMenu title="#{formBundle['form.header.consent.ri']}" value="#{manageProjectView.selectedProjectDetails_q.consent_ind}" 
										rendered="#{manageProjectView.isContributor == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'}"
										disabled="#{manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="Unconfirmed" itemValue="U"/>
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					<p:ajax event="change" update="displayRIGroup" listener="#{manageProjectView.setDisplyRI()}"/>
				</p:selectOneMenu>
			</div>	
						
			<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageProjectView.isRdoAdmin?'':'display: none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.remarks']}" rendered="#{manageProjectView.isRdoAdmin == true}"/>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageProjectView.isRdoAdmin?'':'display: none;'}">
					<p:message for="remarks"/>
					<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="7" counter="display" maxlength="1000" rendered="#{manageProjectView.isRdoAdmin == true}"
											value="#{manageProjectView.selectedProjectHeader_q.remarks}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
                      <br/>
      				  <h:outputText id="display" class="p-d-block" />
			</div>								
		</div>
		
		<br/>
		<div class="form-sub-title">
				<i class="fas fa-tag" style="margin-right:5px;"></i>General Information
		</div>
		<hr/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Investigator Project By-line EdUHK
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<h:panelGroup id="ied_work_ind_ans_n">
					<p:outputLabel id="ied_work_ind_ans_n_label" value="This project will not be counted in the CDCF." 
					style="border-left:8px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:1px 4px; margin-bottom:5px; display:block; width:90%;"
					rendered="#{manageProjectView.selectedProjectHeader_p.ied_work_ind eq 'N'}"/>
				</h:panelGroup>
				<p:message for="ied_work_ind"/>
				<p:selectOneMenu id="ied_work_ind" title="Investigator Project By-line EdUHK" value="#{manageProjectView.selectedProjectHeader_p.ied_work_ind}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					<p:ajax event="valueChange" update="ied_work_ind_ans_n"/>
				</p:selectOneMenu>
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Project Type
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<h:panelGroup id="project_type_ans_n">
					<p:outputLabel id="project_type_ans_n_label" value="This project will not be counted in the CDCF." 
					style="border-left:8px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:1px 4px; margin-bottom:5px; display:block; width:90%;"
					rendered="#{manageProjectView.selectedProjectHeader_p.proj_type eq 'N'}"/>
				</h:panelGroup>
				<p:message for="project_type"/>
				<p:selectOneMenu id="project_type" title="Project Type" label="Project Type" value="#{manageProjectView.selectedProjectHeader_p.proj_type}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItem itemLabel="Researsch Related" itemValue="R"/>
					<f:selectItem itemLabel="Not Research Related" itemValue="N"/>	  
					<p:ajax event="valueChange" update="project_type_ans_n"/> 
				</p:selectOneMenu>
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Activity Code
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
					<p:message for="activity_code"/>
					<p:inputText id="activity_code" label="Activity Code" title="Activity Code" value="#{manageProjectView.selectedProjectHeader_p.activity_code}" maxlength="10" 
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}"/>
			</div>	
			
			<div class="ui-g-12 ui-md-6 ui-lg-6 riForm-item-title">
					Is it an Innovation/Technology (創科) related project?
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-6">
				<p:message for="tech_proj"/>
				<p:selectOneMenu id="tech_proj" title="Is it an Innovation/Technology (創科) related project?" 
										label="Is it an Innovation/Technology (創科) related project?" 
										value="#{manageProjectView.selectedProjectHeader_p.tech_proj}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItem itemLabel="Yes" itemValue="Y"/>
					<f:selectItem itemLabel="No" itemValue="N"/>	  
				</p:selectOneMenu>
			</div>
			
			<div class="ui-g-12 ui-md-6 ui-lg-6 riForm-item-title">
					Project number
					<span style="color:#0277BD; font-size:smaller;">(e.g. C1006-20WX, R1039-20, 4021-PPR-09, EdUHK7018-SPPR-11, 18300933, etc)</span>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-6">
					<p:message for="rgc_proj_num"/>
					<p:inputText id="rgc_proj_num" maxlength="200"
										label="Project number of the RGC funded project(s)" title="Project number of the RGC funded project(s)"
										style="width:99%;" value="#{manageProjectView.selectedProjectHeader_p.rgc_proj_num}"
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					</p:inputText>
			</div>	
		</div>
		<br/>
		<div class="form-sub-title">
				<i class="fas fa-tag" style="margin-right:5px;"></i>Project Information
		</div>
		<hr/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-title">
					Project Title
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
				<p:message for="title1"/>
				<p:inputText id="title1" label="1st line of Project Title" title="1st line of Project Title" style="width:99%" value="#{manageProjectView.selectedProjectHeader_p.title_1}"  
								disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				</p:inputText>
				<hr style="visibility:hidden; margin:0;"/>
				<p:message for="title2"/>
				<p:inputText id="title2" label="2nd line of Project Title" title="2nd line of Project Title" style="width:99%" value="#{manageProjectView.selectedProjectHeader_p.title_2}"  
								disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				</p:inputText>
				<hr style="visibility:hidden; margin:0;"/>
				<p:message for="title3"/>
				<p:inputText id="title3" label="3rd line of Project Title" title="3rd line of Project Title" style="width:99%" value="#{manageProjectView.selectedProjectHeader_p.title_3}"  
								disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				</p:inputText>
				<hr style="visibility:hidden; margin:0;"/>
				<p:message for="title4"/>
				<p:inputText id="title4" label="4th line of Project Title" title="4th line of Project Title" style="width:99%" value="#{manageProjectView.selectedProjectHeader_p.title_4}"  
								disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				</p:inputText>
			</div>	
			
			<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-title">
					A Brief Description/Summary of Project
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
				<p:message for="project_summary"/>
				<p:inputTextarea id="project_summary" label="1st paragraph of Brief Description/Summary of Project" title="1st paragraph of Brief Description/Summary of Project" style="width: 99%;" rows="4" counter="project_summary_display" maxlength="2000" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}"
										value="#{manageProjectView.selectedProjectHeader_p.project_summary}"
	                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
	            <br/>
	    		<h:outputText id="project_summary_display" class="p-d-block" />
	    	</div>
	    	<div class="ui-g-12 ui-md-12 ui-lg-12">
	    		<p:message for="project_summary_2"/>
				<p:inputTextarea id="project_summary_2" label="2nd paragraph of Brief Description/Summary of Project" title="2nd paragraph of Brief Description/Summary of Project" style="width: 99%;" rows="4" counter="project_summary_2_display" maxlength="2000" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}"
										value="#{manageProjectView.selectedProjectHeader_p.project_summary_2}"
	                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
	            <br/>
	    		<h:outputText id="project_summary_2_display" class="p-d-block" />
	    	</div>
	    	
	    	<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Commencement Date<br/><span style="color:#0277BD; font-size:smaller;">(DD/MM/YYYY)</span>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:message for="fromDay" />
				<p:message for="fromMonth" />
				<p:message for="fromYear" />
				<p:selectOneMenu id="fromDay" title="Commencement Date - Day" label="Commencement Date - Day" value="#{manageProjectView.selectedProjectHeader_p.from_day}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" >
					<f:selectItem itemLabel="" itemValue="#{null}" />
					<f:selectItems value="#{manageProjectView.fromDayList}" var="o" 
										itemLabel="#{o}" itemValue="#{o}" />
										<p:ajax event="change"/>
				</p:selectOneMenu>							
				<p:selectOneMenu id="fromMonth" title="Commencement Date - Month" label="Commencement Date - Month" value="#{manageProjectView.selectedProjectHeader_p.from_month}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItems value="#{manageProjectView.monthList}" var="o" 
										itemLabel="#{o}" itemValue="#{o}" />
					<p:ajax event="change" update="fromDay"/>	
				</p:selectOneMenu>
				<p:selectOneMenu id="fromYear" title="Commencement Date - Year" label="Commencement Date - Year" value="#{manageProjectView.selectedProjectHeader_p.from_year}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItems value="#{manageProjectView.yearList}" var="o" 
										itemLabel="#{o}" itemValue="#{o}" />	
					<p:ajax event="change" update="fromDay"/>		
				</p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					(Expected) Completion Date<br/><span style="color:#0277BD; font-size:smaller;">(DD/MM/YYYY)</span>
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:message for="toDay" />
				<p:message for="toMonth" />
				<p:message for="toYear" />
				<p:selectOneMenu id="toDay" title="(Expected) Completion Date - Day" label="(Expected) Completion Date - Day" value="#{manageProjectView.selectedProjectHeader_p.to_day}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" >
					<f:selectItem itemLabel="" itemValue="#{null}" />
					<f:selectItems value="#{manageProjectView.toDayList}" var="o" 
										itemLabel="#{o}" itemValue="#{o}" />
										<p:ajax event="change"/>
				</p:selectOneMenu>							
				<p:selectOneMenu id="toMonth" title="(Expected) Completion Date - Month" label="(Expected) Completion Date - Month" value="#{manageProjectView.selectedProjectHeader_p.to_month}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItems value="#{manageProjectView.monthList}" var="o" 
										itemLabel="#{o}" itemValue="#{o}" />
					<p:ajax event="change" update="toDay"/>	
				</p:selectOneMenu>
				<p:selectOneMenu id="toYear" title="(Expected) Completion Date - Year" label="(Expected) Completion Date - Year" value="#{manageProjectView.selectedProjectHeader_p.to_year}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItems value="#{manageProjectView.yearList}" var="o" 
										itemLabel="#{o}" itemValue="#{o}" />	
					<p:ajax event="change" update="toDay"/>		
				</p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Collaborative Partner(s) of Other UGC Funded Institutions?
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:selectOneMenu title="Collaborative Partner(s) of Other UGC Funded Institutions?" value="#{manageProjectView.selectedProjectHeader_p.collab_partner_ugc}"
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" >
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					<p:ajax event="change" update="role_inst_panel"/>	
				</p:selectOneMenu>
			</div>

			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Role of EdUHK collaborating with partner(s) of other UGC funded institution(s)
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<h:panelGroup id="role_inst_panel">
					<p:selectOneMenu id="select_role_inst" title="Collaborate with Partner(s) of other UGC Funded Institution(s)" label="Collaborate with Partner(s) of other UGC Funded Institution(s)" 
											value="#{manageProjectView.selectedProjectHeader_p.role_inst}" 
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" 
											rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_ugc eq 'Y'}">
						<f:selectItem itemLabel="Coordinating Institution" itemValue="C"/>
						<f:selectItem itemLabel="Participating Institution" itemValue="P"/>	   
					</p:selectOneMenu>
					<p:outputLabel style="color:#666;" value="Not Applicable" rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_ugc eq 'N'}"/>
				</h:panelGroup>
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Collaborative Partner(s) in Mainland or Overseas?
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:selectOneMenu title="Collaborative Partner(s) in Mainland or Overseas?" value="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins}"
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" >
					<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
					<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	
					<p:ajax event="change" update="overseasPanel outputPanel role_inst_for_t690_panel"/>   
				</p:selectOneMenu>
			</div>		
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Role of EdUHK collaborating with partner(s) in Mainland or Overseas
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<h:panelGroup id="role_inst_for_t690_panel">
					<p:selectOneMenu id="select_role_inst_for_t690" title="Collaborate with Partner(s) in Mainland or Overseas" label="Collaborate with Partner(s) in Mainland or Overseas" 
											value="#{manageProjectView.selectedProjectHeader_p.role_inst_for_t690}" 
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" 
											rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y'}">
						<f:selectItem itemLabel="Coordinating Institution" itemValue="C"/>
						<f:selectItem itemLabel="Participating Institution" itemValue="P"/>	   
					</p:selectOneMenu>
					<p:outputLabel style="color:#666;" value="Not Applicable" rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'N'}"/>
				</h:panelGroup>
			</div>
		</div>
		
		<h:panelGroup  id="overseasPanel" >	
			<h:outputText style="font-weight:700; font-size:18px; color:#055588; text-decoration:underline;" 
								value="Collaborators from non-local institution(s)"
								rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y'}"/>
			<p:messages for="overseasTable"/>	
			<p:commandButton id="btn_overseas_add" icon="fas fa-plus" value="Add" 
									rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y' &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}"
									 style="width:260px; margin-bottom:2px;"
									 action="#{manageProjectView.addOverseasRow()}"
									 update="overseasTable"
									 immediate="true"/>
			<p:blockUI block="contentPanel" trigger="btn_overseas_add" />
			<p:dataTable id="overseasTable" value="#{manageProjectView.projectOverseas_list}" rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y'}"
								class="riFormTable"
								reflow="true" var="col" widgetVar="overseasTableWV"
								style="max-width:99%;"
					 			rowIndexVar="rowIndex">
					 	 
				<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
					<f:facet name="header">Up</f:facet>
					<p:commandLink action="#{manageProjectView.moveOverseasColumnUp(rowIndex)}"
								   update="overseasTable" immediate="true"
								   rendered="#{rowIndex gt 0}"><i class="fas fa-caret-up icon-action" title="Move up"/>
					</p:commandLink>										
				</p:column>
				
				<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
					<f:facet name="header">Dn.</f:facet>
					<p:commandLink action="#{manageProjectView.moveOverseasColumnDown(rowIndex)}"
								   update="overseasTable" immediate="true"
								   rendered="#{rowIndex lt manageProjectView.projectOverseas_list.size()-1}"><i class="fas fa-caret-down icon-action" title="Move down"/>
					</p:commandLink>										
				</p:column>
		
				<p:column style="text-align:left; width:5em;">
					<f:facet name="header">No.</f:facet>
					#{rowIndex +1}							
				</p:column>
				 <p:column style="text-align:left;">
					<f:facet name="header">Institution</f:facet>
					<p:message for="institution"/>
					<p:inputText id="institution" title="Collaborative Partner(s) (Institution)" label="Collaborative Partner(s) (Institution)" style="width:90%" 
									value="#{col.institution}" 
									onkeypress="return (event.charCode != 189);"
									maxlength="200" 
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
									<p:ajax event="valueChange"/>
					</p:inputText>
				</p:column>
				 <p:column style="text-align:left;">
					<f:facet name="header">City</f:facet>
					<p:message for="city"/>
					<p:inputText id="city" title="Collaborative Partner(s) (City)" label="Collaborative Partner(s) (City)" style="width:90%" 
									value="#{col.city}" 
									onkeypress="return (event.charCode != 189);"
									maxlength="200" 
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
									<p:ajax event="valueChange"/>
					</p:inputText>
				</p:column>
				<p:column style="text-align:left;">
					<f:facet name="header">Country</f:facet>
					<p:message for="country"/>
					<p:selectOneMenu id="country" title="Collaborative Partner(s) (Country)" label="Collaborative Partner(s) (Country)" style="width:90%" 
											value="#{col.country}" filter="true" filterMatchMode="contains"
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageProjectView.partnerCountry_list}" var="o" 
											itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" />
						<p:ajax event="change"/>
					</p:selectOneMenu>
				</p:column>			
				<p:column  class="data-noprint" style="width:2em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
					<f:facet name="header">Del.</f:facet>
					<p:commandLink id="btn_overseas_delete" action="#{manageProjectView.deleteOverseasRow(rowIndex)}" 
									rendered="#{rowIndex gt 0}"
								   update="overseasTable" immediate="true">
						<i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
					</p:commandLink>	
					<p:blockUI block="contentPanel" trigger="btn_overseas_delete" />
				</p:column>	
			</p:dataTable>
		</h:panelGroup>
		<br/>
		<h:panelGroup  id="outputPanel" >		
			<h:outputText style="font-weight:700; font-size:18px; color:#055588; text-decoration:underline;" 
								value="Details of Research Output(s) (if any)"
								rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y'}"/>
			<h:outputText style="font-weight:700; font-size:16px; color:#0277BD; margin-left:10px;" 
								value="* The listed research output(s) will bear the name of EdUHK as well as the name(s) of non-local institution(s)."
								rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y'}"/>
			<p:messages for="outputTable"/>	
			<p:commandButton id="btn_output_add" icon="fas fa-plus" value="Add" 
									rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y' &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}"
									 style="width:260px; margin-bottom:2px;"
									 action="#{manageProjectView.addOutputRow()}"
									 update="outputTable"
									 immediate="true"/>
			<p:blockUI block="contentPanel" trigger="btn_output_add" />
			<p:dataTable id="outputTable" value="#{manageProjectView.researchOutput_list}" class="riFormTable" 
								rendered="#{manageProjectView.selectedProjectHeader_p.collab_partner_non_ins eq 'Y'}"
								reflow="true" var="col" widgetVar="outputTableWV"
								style="max-width:99%;"
						 		rowIndexVar="rowIndex">
					 	 
				<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
					<f:facet name="header">Up</f:facet>
					<p:commandLink action="#{manageProjectView.moveOutputColumnUp(rowIndex)}"
								   update="outputTable" immediate="true"
								   rendered="#{rowIndex gt 0}"><i class="fas fa-caret-up icon-action" title="Move up"/>
					</p:commandLink>										
				</p:column>
				
				<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
					<f:facet name="header">Dn.</f:facet>
					<p:commandLink action="#{manageProjectView.moveOutputColumnDown(rowIndex)}"
								   update="outputTable" immediate="true"
								   rendered="#{rowIndex lt manageProjectView.researchOutput_list.size()-1}"><i class="fas fa-caret-down icon-action" title="Move down"/>
					</p:commandLink>										
				</p:column>
		
				<p:column style="text-align:left; width:5em;">
					<f:facet name="header">No.</f:facet>
					#{rowIndex +1}							
				</p:column>
				 <p:column style="text-align:left;">
					<f:facet name="header">Title</f:facet>
					<p:message for="outputTitle"/>
					<p:inputText id="outputTitle" title="Title" label="Title" maxlength="300" style="width:90%"
									value="#{col.outputTitle}" 
									onkeypress="return (event.charCode != 59);"
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
									<p:ajax event="valueChange"/>
					</p:inputText>	
				</p:column>
				 <p:column style="text-align:left;">
					<f:facet name="header">Research output type</f:facet>
					<p:message for="outputType"/>
					<p:selectOneMenu id="outputType" title="Research output type" label="Research output type" value="#{col.outputType}" style="width:90%"
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageOutputView.outputTypeList}" var="o" 
						itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						<p:ajax event="change"/>
					</p:selectOneMenu>
				</p:column>
				<p:column style="text-align:left;">
					<f:facet name="header">Name(s) of the non-local institution(s) appeared/ will be appeared on the output</f:facet>
					<p:message for="outputInst"/>
					<p:inputText id="outputInst" title="Name(s) of the non-local institution(s) appeared/ will be appeared on the output" style="width:90%"
									label="Name(s) of the non-local institution(s) appeared/ will be appeared on the output" maxlength="300" 
									value="#{col.outputInst}" 
									onkeypress="return (event.charCode != 59);"
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
									<p:ajax event="valueChange"/>
					</p:inputText>	
				</p:column>			
				<p:column style="text-align:left;">
					<f:facet name="header">Actual / Expected date of producing the output</f:facet>
					<p:message for="outputDate"/>
					<p:calendar id="outputDate" 
										title="Actual / Expected date of producing the output" 
										label="Actual / Expected date of producing the output" 
										value="#{col.outputDate}" 
										pattern="dd/MM/yyyy" mask="true" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
										<p:ajax event="dateSelect"/>
					</p:calendar>		
				</p:column>							
				<p:column class="data-noprint" style="width:2em; text-align:left;">
					<f:facet name="header">Del.</f:facet>
					<p:commandLink id="btn_output_delete" action="#{manageProjectView.deleteOutputRow(rowIndex)}" 
									rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}"
								   update="outputTable" immediate="true"><i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
					</p:commandLink>	
					<p:blockUI block="contentPanel" trigger="btn_output_delete" />
				</p:column>	
			</p:dataTable>
		</h:panelGroup>
		
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
				<span style="font-weight:700; font-size:18px; color:#0f3852; text-decoration:underline;">Funding information</span>
			</div>	
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Funded Project?
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:selectOneMenu id="funded_proj" title="Funded Project?" value="#{manageProjectView.selectedProjectHeader_p.funded_proj}" 
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" >
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
						<p:ajax event="change" 
									listener="#{manageProjectView.resetFundingDetails(manageProjectView.selectedProjectHeader_p.funded_proj)}" 
									update="funding_body funding_source_panel sap_grant_amt released_val"/>		
					</p:selectOneMenu>
			</div>	
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Funding Body
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<p:message for="@next"/>
				<p:selectOneMenu id="funding_body" title="Funding Body" label="Funding Body"  
										value="#{manageProjectView.selectedProjectHeader_p.funding_body}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P' || manageProjectView.selectedProjectHeader_p.funded_proj eq 'N'}" >
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
					<f:selectItems value="#{manageProjectView.fundSource_cat_list}" var="c" 
										itemLabel="#{c.description}" itemValue="#{c.pk.lookup_code}"/>
					<p:ajax event="change" update="funding_source_panel funding_org_panel funding_org_label_panel funding_others_panel funding_others_label_panel" listener="#{manageProjectView.resetFundSourceSubCatList()}"/>		
				</p:selectOneMenu>
			</div>				
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Funding Source
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<h:panelGroup id="funding_source_panel">
					<p:message for="@next"/>
					<p:selectOneMenu id="sap_funding_source" title="Funding Source" label="Funding Source"  
											value="#{manageProjectView.selectedProjectHeader_p.sap_funding_source}" 
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P' || manageProjectView.selectedProjectHeader_p.funded_proj eq 'N'}" >
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageProjectView.fundSource_subCat_list}" var="sc" 
											itemLabel="#{sc.description}" itemValue="#{sc.pk.lookup_code}"/>
						<p:ajax event="change" update="funding_source_panel funding_org_panel funding_org_label_panel funding_others_panel funding_others_label_panel" />
					</p:selectOneMenu>
				</h:panelGroup>
			</div>		

			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				<h:panelGroup id="funding_others_label_panel">
					<p:outputLabel value="Name of Funding" rendered="#{manageProjectView.requireFundingName(manageProjectView.selectedProjectHeader_p.sap_funding_source)}"/>
					<p:outputLabel style="color:green" value="(e.g. The Charities Trust)" 
										rendered="#{manageProjectView.requireFundingName(manageProjectView.selectedProjectHeader_p.sap_funding_source)}"/>
				</h:panelGroup>
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<h:panelGroup id="funding_others_panel">
					<p:message for="funding_others"/>
					<p:inputText id="funding_others" title="Name of Funding" label="Name of Funding" maxlength="200" style="width:99%"
										value="#{manageProjectView.selectedProjectHeader_p.funding_others}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P' || manageProjectView.selectedProjectHeader_p.funded_proj eq 'N'}" 
										rendered="#{manageProjectView.requireFundingName(manageProjectView.selectedProjectHeader_p.sap_funding_source)}"/>			
				</h:panelGroup>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				<h:panelGroup id="funding_org_label_panel">
					<p:outputLabel value="Funding Organization" rendered="#{manageProjectView.requireFundingOrg(manageProjectView.selectedProjectHeader_p.sap_funding_source)}"/>
					<p:outputLabel style="color:green" value=" (e.g. The Hong Kong Jockey Club)" rendered="#{manageProjectView.requireFundingOrg(manageProjectView.selectedProjectHeader_p.sap_funding_source)}"/>
				</h:panelGroup>
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<h:panelGroup id="funding_org_panel">
					<p:message for="funding_org"/>
					<p:inputText id="funding_org" title="Funding Organization" label="Funding Organization" maxlength="200" style="width:99%"
										value="#{manageProjectView.selectedProjectHeader_p.funding_org}" 
										disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P' || manageProjectView.selectedProjectHeader_p.funded_proj eq 'N'}" 
										rendered="#{manageProjectView.requireFundingOrg(manageProjectView.selectedProjectHeader_p.sap_funding_source)}"/>		
				</h:panelGroup>
			</div>			
			
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Funding Amount of the Project (HKD)
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:message for="sap_grant_amt"/>
				<p:inputNumber  id="sap_grant_amt" title="Funding Amount of the Project (HKD)" label="Funding Amount of the Project (HKD)" symbol="$"
									maxValue="9999999999" minValue="0" decimalPlaces="2" 
									value="#{manageProjectView.selectedProjectHeader_p.sap_grant_amt}" 
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P' || manageProjectView.selectedProjectHeader_p.funded_proj eq 'N'}"/>		
			</div>

			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Actual Amount Received by EdUHK(HKD)
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-4">
				<p:message for="released_val"/>
				<p:inputNumber id="released_val" title="Actual Amount Received by EdUHK(HKD)" label="Actual Amount Received by EdUHK(HKD)" symbol="$"
									maxValue="9999999999" minValue="0" decimalPlaces="2" 
									value="#{manageProjectView.selectedProjectHeader_p.released_val}" 
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P' || manageProjectView.selectedProjectHeader_p.funded_proj eq 'N'}"/>
			</div>
		</div>	
		
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
			<div class="ui-g-12 ui-md-12 ui-lg-2 riForm-item-title">
					Sector of the Project
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-10">
					<p:message for="sch_dtl_code"/>
					<p:selectOneMenu id="sch_dtl_code" title="Sector of the Project" label="Sector of the Project" value="#{manageProjectView.selectedProjectHeader_p.sch_dtl_code}" 
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageProjectView.eduSectorList}" var="o" 
											itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
							<p:ajax event="change" listener="#{manageProjectView.resetDAList()}" update="da_dtl_code"/>
					</p:selectOneMenu>
			</div>	
	
					
			<div class="ui-g-12 ui-md-12 ui-lg-2 riForm-item-title">
					Disciplinary Area of the Project
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-10">
					<p:message for="da_dtl_code"/>
					<p:selectOneMenu id="da_dtl_code" title="Disciplinary Area of the Project" label="Disciplinary Area of the Project" value="#{manageProjectView.selectedProjectHeader_p.da_dtl_code}" 
											disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItems value="#{manageProjectView.disAreaList}" var="o"
											itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						<p:ajax event="change" update="other_da_dtl"/>
					</p:selectOneMenu>
			</div>							
				
			<div class="ui-g-12 ui-md-12 ui-lg-2 riForm-item-title">
					Other Disciplinary Area
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-10">
					<p:message for="other_da_dtl"/>
					<p:inputText id="other_da_dtl" class="chinese-150" label="Other Disciplinary Area" style="width:99%" value="#{manageProjectView.selectedProjectHeader_p.other_da_dtl}" maxlength="150" 
										disabled="#{!manageProjectView.isOtherDisArea() || manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
              		</p:inputText>		
			</div>
			
			<div class="ui-g-12 ui-md-12 ui-lg-2 riForm-item-title">
					SDGs Information 
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-10">
					<p:message for="sdg_info"/>
					<p:selectCheckboxMenu  id="sdg_info" title="SDGs Information" 
							label="-- Please select --" 
							filter = "true" filterMatchMode= "startsWith" filterNormalize = "true" multiple = "true"
							style="min-width: 15rem"
							var="c"
							value="#{manageProjectView.selectedProjectHeader_p.sdg_code_list}"
							disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
						<f:selectItems value="#{manageProjectView.sdgList}" var ="sdg" itemLabel="#{sdg.pk.lookup_code} - #{sdg.description}" 
						itemValue="#{sdg.pk.lookup_code}" >

						</f:selectItems>
					</p:selectCheckboxMenu>
			</div>
				
			<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
				Keywords <span style="color:#0277BD; font-size:smaller;">(Separated by semi-colon, e.g. education; psychology; assessment)</span>
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<p:message for="keywords"/>
				<p:inputText id="keywords" title="Keywords" label="Keywords" maxlength="2000" style="width:99%"
									value="#{manageProjectView.selectedProjectHeader_p.keywords}" 
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}"/>
			</div>			
		</div>
		<br/>
		<div class="form-sub-title">
			<i class="fas fa-tag" style="margin-right:5px;"></i>Name of Investigator(s) in sequential order
		</div>
		<hr/>
	<p:message for="columnTable"/>
	<p:dataTable id="columnTable" value="#{manageProjectView.projectDetails_p_list}" var="col" widgetVar="columnTableWV" class="riFormTable"
					 rows="50" reflow="true"
					 rowsPerPageTemplate="10,20,50,100,200"
                     paginator="true"
                     paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     currentPageReportTemplate="Total Number of Investigator(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
					 style="max-width:99%;"
					 rowIndexVar="rowIndex">
					 	 
		<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
			<f:facet name="header">Up</f:facet>
			<p:commandLink action="#{manageProjectView.moveColumnUp(rowIndex)}"
						   update="columnTable" immediate="true"
						   rendered="#{rowIndex gt 0}"><i class="fas fa-caret-up icon-action" title="Move up"/>
			</p:commandLink>										
		</p:column>
		
		<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
			<f:facet name="header">Dn.</f:facet>
			<p:commandLink action="#{manageProjectView.moveColumnDown(rowIndex)}"
						   update="columnTable" immediate="true"
						   rendered="#{rowIndex lt manageProjectView.projectDetails_p_list.size()-1}"><i class="fas fa-caret-down icon-action" title="Move down"/>
			</p:commandLink>										
		</p:column>

		<p:column style="text-align:left; width:5em;">
			<f:facet name="header">No.</f:facet>
			<ui:fragment rendered="#{manageProjectView.isRiCreator(col.investigator_staff_no)}"><i class="fa fa-star faa-pulse animated-hover" title="RI creator" style="font-size:9px; color:#f06524; vertical-align: middle;"></i></ui:fragment> #{rowIndex +1}							
		</p:column>
		<!-- Investigator Position in RI -->	
		<p:column style="text-align:left; width:15em;">
			<f:facet name="header">#{formBundle['form.ri.creator.project']} Position in RI</f:facet>
			<p:message id="typeMsg" for="type"/>
			<p:selectOneMenu id="type" style="width:90%;" value="#{col.non_ied_staff_flag}" disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				<f:selectItem itemLabel="EdUHK Staff" itemValue="N"/>
				<f:selectItem itemLabel="Former Staff" itemValue="F"/>
				<f:selectItem itemLabel="Non EdUHK Staff" itemValue="Y"/>
				<f:selectItem itemLabel="EdUHK Student" itemValue="S"/>
				<p:ajax event="change" update="nameGroup cvGroup"/>
			</p:selectOneMenu>
		</p:column>		
		<!-- Investigator -->	
		<p:column style="text-align:left; width:20em;">
			<f:facet name="header">#{formBundle['form.ri.creator.project']}</f:facet>
			<h:panelGroup id="nameGroup">
			<p:message for="select_staff_recipient_name"/>
			<p:selectOneMenu id="select_staff_recipient_name" label="#{formBundle['form.ri.creator.project']}" style="width:90%" value="#{col.investigator_staff_no}" filter="true" filterMatchMode="startsWith" rendered="#{col.non_ied_staff_flag eq 'N'}" dynamic="true" disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
				<f:selectItems value="#{manageRIView.staffList}"/>
				<p:ajax event="change" update="cvGroup"/>
			</p:selectOneMenu>

			<p:message for="select_staff_past_name"/>
			<p:autoComplete id="select_staff_past_name" value="#{col.investigator_name}" style="#{manageProjectView.paramDataLevel eq 'C'?'width:70%':'width:90%'}"
							rendered="#{col.non_ied_staff_flag eq 'F'}"
							minQueryLength="3" 
							emptyMessage="No results"
							forceSelection="true"
							disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}"
                        	completeMethod="#{manageRIView.fsCompleteText}" scrollHeight="250">
                        	<p:ajax event="itemSelect" process="@this" />
                        	<p:ajax event="itemSelect" listener="#{manageProjectView.updateRowStaffNum(rowIndex)}" update="nameGroup"/>
            </p:autoComplete>
            <p:inputText id="select_staff_past_num" label="Staff No." style="#{manageProjectView.isRdoAdmin == true?'width:25%':'display:none'}" value="#{col.investigator_staff_no}" rendered="#{col.non_ied_staff_flag eq 'F'}" dynamic="true">
			</p:inputText>
            
			<p:message for="@next"/>
			<p:inputText id="inv_name" label="#{formBundle['form.ri.creator.project']}" style="width:90%" value="#{col.investigator_name}" 
								onkeypress="return (event.charCode != 59);"
								maxlength="80" 
								rendered="#{col.non_ied_staff_flag ne 'N' &amp;&amp; col.non_ied_staff_flag ne 'F'}" 
								disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}">
				<p:ajax/>
			</p:inputText>
			</h:panelGroup>
		</p:column>		
		<!-- Role -->	
		<p:column style="text-align:left; width:20em;">
			<f:facet name="header">Role</f:facet>
			<p:selectOneMenu id="investigatorType" value="#{col.investigator_type}" style="width: 90% !important"
									disabled="#{manageProjectView.isCreator == false || manageProjectView.canModify == false || manageProjectView.paramDataLevel eq 'P'}" >
				<f:selectItems value="#{manageProjectView.capacity_list}" var="a"
								   itemLabel="#{a.description} " itemValue="#{a.pk.lookup_code}"/>
				<p:ajax event="change" update="columnTable"/>
			</p:selectOneMenu>
		</p:column>			
		<p:column class="data-noprint" style="width:8em;">
			<h:panelGroup id="cvGroup">
				<p:linkButton id="btn_view_cv" outcome="/web/person" rendered="#{col.non_ied_staff_flag eq 'N' &amp;&amp; manageProjectView.getStaffProfilePid(col.investigator_staff_no) ne null}" value="#{formBundle['form.view.profile']}" target="_blank">
	  					<f:param name="pid" value="#{manageProjectView.getStaffProfilePid(col.investigator_staff_no)}"/>
	  			</p:linkButton>
  			</h:panelGroup>
		</p:column>	
		<p:column  class="data-noprint" style="width:2em; text-align:left;" rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}">
			<f:facet name="header">Del.</f:facet>
			<p:commandLink id="btn_delete" action="#{manageProjectView.deleteRow(rowIndex)}"
						   update="columnTable" immediate="true"><i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
			</p:commandLink>	
			<p:blockUI block="contentPanel" trigger="btn_delete" />
		</p:column>									
	</p:dataTable>		
	<br/>
	<p:commandButton id="btn_add" icon="fas fa-plus" value="Add new investigator" 
							rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'P'}"
							 style="width:260px"
							 action="#{manageProjectView.addRow()}"
							 update="columnTable"
							 immediate="true"
							 oncomplete="PF('columnTableWV').paginator.setPage(PF('columnTableWV').paginator.cfg.pageCount - 1);"/>
	<p:blockUI block="contentPanel" trigger="btn_add" />			
	<br/><br/>
	<h:panelGroup styleClass="button-panel">
		<p:linkButton value="#{bundle['action.back']}" outcome="manageProject" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageProjectView.paramDataLevel eq 'M' &amp;&amp; manageProjectView.paramConsent eq ''}">		
			<f:param name="pid" value="#{manageProjectView.paramPid}"/>
		</p:linkButton>
		<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageProjectView.paramConsent ne ''}">		
			<f:param name="pid" value="#{manageProjectView.paramPid}"/>
			<f:param name="consent" value="#{manageProjectView.paramConsent}"/>
			<f:param name="tabpage" value="1"/>
		</p:linkButton>
		<p:commandButton id="btn_sava" value="#{formBundle['form.save']}" 
								  rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel ne 'C'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageProjectView.save}"
						  		  oncomplete="window.scrollTo(0,0);">
		</p:commandButton>	
		<p:commandButton id="btn_savaAndGen" value="#{(manageProjectView.selectedProjectHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}" 
								  rendered="#{manageProjectView.paramDataLevel eq 'C'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageProjectView.save}"
						  		  oncomplete="window.scrollTo(0,0);">
		</p:commandButton>			
		<p:commandButton id="btn_savaAndPublish" value="#{bundle['action.saveAndPublish']}" 
								  rendered="#{manageProjectView.isCreator == true &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageProjectView.saveAndPublishForm}"
						  		  oncomplete="window.scrollTo(0,0);">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.publish.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>			
		<p:commandButton id="btn_submit" value="#{formBundle['form.save']}" 
								  rendered="#{manageProjectView.isCreator == false &amp;&amp; manageProjectView.canModify == true &amp;&amp; manageProjectView.paramDataLevel eq 'M'}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{manageProjectView.submitConsent}"
						  		  oncomplete="window.scrollTo(0,0);">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>		
		<p:commandButton id="btn_snapshot" value="#{formBundle['form.take.snapshot']}" rendered="#{manageProjectView.paramDataLevel eq 'P'}"
								  style="margin-right:5px;"
								  process="@this"
						  		  action="#{manageProjectView.takeSnapshot}"
						  		  oncomplete="window.scrollTo(0,0);">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.take.snapshot.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>	
		<p:commandButton id="btn_delete" value="#{formBundle['form.del']}" rendered="#{manageProjectView.canDelete == true &amp;&amp; manageProjectView.canModify == true}"
								  style="margin-right:5px; background:#D32F2F; border:1px solid #D32F2F;" 
								  process="@this"
						  		  action="#{manageProjectView.deleteForm}">
			<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.del.desc']}" icon="pi pi-info-circle"/>
		</p:commandButton>	
        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true" width="350">
            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
        </p:confirmDialog>
        <!--  <p:blockUI block="contentPanel" trigger="top_btn_sava" />
		<p:blockUI block="contentPanel" trigger="top_btn_savaAndPublish" />
		<p:blockUI block="contentPanel" trigger="top_btn_submit" />
		<p:blockUI block="contentPanel" trigger="top_btn_snapshot" />
		<p:blockUI block="contentPanel" trigger="top_btn_delete" />
		<p:blockUI block="contentPanel" trigger="btn_sava" />
		<p:blockUI block="contentPanel" trigger="btn_savaAndPublish" />
		<p:blockUI block="contentPanel" trigger="btn_submit" />
		<p:blockUI block="contentPanel" trigger="btn_snapshot" />
		<p:blockUI block="contentPanel" trigger="btn_delete" />-->
	</h:panelGroup>
	<p:scrollTop target="parent" threshold="100" styleClass="custom-scrolltop" icon="pi pi-arrow-up" />
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>