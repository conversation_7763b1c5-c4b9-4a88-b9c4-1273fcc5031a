<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Select RAE Outputs for Staffs</span>
	<p:staticMessage severity="warn" summary="Important Note" detail="#{sysParamView.getValue('RAE_NOTE')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;"/>
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.getExRi(manageRIView.lockGrp).lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageRIView.canModifyRae == false}"/>
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<br/><br/>
		
		<div class="ui-g">
			<div class="ui-g-4 ui-md-3 ui-lg-2">
				<h:outputText class="riForm-item-title" style="vertical-align:middle;" value="Unit of Assessment:"/>
			</div>
			<div class="ui-g-8 ui-md-9 ui-lg-10">
				<p:selectOneMenu id="uoa" title="Unit of Assessment" value="#{raeOutputView.selectedUOA}">
					<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
					<f:selectItems value="#{raeOutputView.uoaList}"  var="uoa" itemLabel="(#{uoa.uoaCode}) #{uoa.uoaDesc}" itemValue="#{uoa.uoaCode}" />
					<f:ajax event="change" execute="@this" render="staffTable panel_att"/>
				</p:selectOneMenu>
			</div>
		</div>
		<br/>
		<h:panelGroup id="panel_att" layout="block" class="ui-g-12 ui-md-12 ui-lg-12">
			<h:outputText class="riForm-item-title" style="vertical-align:middle;" value="Total Attachments: #{raeOutputView.getCountTotalFilesByUoA(raeOutputView.selectedUOA)} "/>
           	<p:commandLink ajax="false" style="vertical-align:middle;" rendered="#{raeOutputView.getCountTotalFilesByUoA(raeOutputView.selectedUOA) gt 0}"><i class="fas fa-download icon-action" title="Download"/>
				<p:fileDownload value="#{raeOutputView.downloadFilesAsZipByUoA(raeOutputView.selectedUOA)}" />		 
           	</p:commandLink>
		</h:panelGroup>
		<p:dataTable id="staffTable" var="staff" value="#{raeOutputView.raeStaffList}" sortMode="single"
						rowKey="#{staff.staffNumber}-" 
						selection="#{staffRankView.selectedRank}" selectionMode="single"
						rows="50" reflow="true"
						rowsPerPageTemplate="10,20,50,100"
	                    paginator="true"
	                    paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
	                    currentPageReportTemplate="Total Number of Staff(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
						tableStyle="table-layout:auto"
						rowIndexVar="rowIndex">
			
			<p:column>
				#{rowIndex+1}
            </p:column>
            
			<p:column headerText="Name" id="name" sortBy="#{staff.staffName}" style="text-align: center;">
				#{staff.staffTitle} #{staff.staffName}  
            </p:column>
            
            <p:column style="width:12rem">
                <p:linkButton outcome="manageRAEOutput" value="Select RAE Output(s)" styleClass="btn-back" target="_blank">
                	<f:param name="pid" value="#{staff.pid}"/>
                </p:linkButton>	
            </p:column>
            
            <p:column headerText="Faculty" id="fac" sortBy="#{staff.faculty}" style="text-align: center;">
            	#{staff.faculty}
            </p:column>
            
            <p:column headerText="Department" id="dept" sortBy="#{staff.department}" style="text-align: center;">
            	#{staff.department}
            </p:column>
            
            <p:column headerText="No. of Outputs to be Submitted" id="ouputNoSub"  sortBy="#{staff.outSubNoStr}" style="text-align: center;">
            	#{staff.outSubNoStr}
            </p:column>
            
            <p:column headerText="Total No. of Outputs Selected" id="ouputTotalSel" sortBy="#{raeOutputView.getOutputCount (staff.staffNumber)}" 
            		style="text-align: center;">
            		#{raeOutputView.getOutputCount (staff.staffNumber)}
            </p:column>
            
            <p:column headerText="Double-weighted" id="weight"  sortBy="#{raeOutputView.getDWCount (staff.staffNumber)}"  style="text-align: center;">
           	 			#{raeOutputView.getDWCount (staff.staffNumber)}
            </p:column>
            
            <p:column headerText="Remarks" id="remarks" sortBy="#{staff.department}"  style="text-align: center;">
            	#{staff.selRemark}
            </p:column>
            
            <p:column headerText="Att." style="width:1em; text-align: center;">
            	#{raeOutputView.getCountTotalFilesByStaff(staff.staffNumber)}
	           	<p:commandLink ajax="false" rendered="#{raeOutputView.getCountTotalFilesByStaff(staff.staffNumber) gt 0}"><i class="fas fa-download icon-action" title="Download"/>
					<p:fileDownload value="#{raeOutputView.downloadFilesAsZip(staff.staffNumber)}" />		 
	           	</p:commandLink>
	        </p:column>

        </p:dataTable>

	</h:form>

	</p:panel>
   </ui:define>
</ui:composition>