<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata>
		<f:event listener="#{raeOutputView.getCanModifyRae}" type="preRenderView" />
		<f:event listener="#{raeOutputView.checkValidRae}" type="preRenderView" />
		<f:viewParam name="no" value="#{raeOutputView.paramNo}" />
		<f:viewParam name="pid" value="#{raeOutputView.paramPid}" />
	</f:metadata>
	<ui:define name="html_head">
		<p:importConstants type="hk.eduhk.rich.Constant" var="Constant"/>
		<script type="text/javascript" src="#{request.contextPath}/resources/js/jquery.chineselengthlimit.js"></script>
		<style>
			li.ui-state-disabled{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
			
			.raeMenu.ui-selectonemenu {
		          vertical-align: bottom!important;
		    }

		    #editForm\:formTab\:panel_phy_submit_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }     
			#editForm\:formTab\:panel_ele_submit_content {
		          border: 0px!important;
		          padding: 0px!important;
		    } 
		    #editForm\:formTab\:panel_toc_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }   
		    #editForm\:formTab\:panel_toc_att_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }   
		    #editForm\:formTab\:panel_toc_oth_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }  
		    #editForm\:formTab\:panel_toc_att_oth_content {
		          border: 0px!important;
		          padding: 0px!important;
		    } 
		    #editForm\:formTab\:panel_additional_info_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }   
		    #editForm\:formTab\:panel_scw_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }   
		    #editForm\:formTab\:panel_book_content {
		          border: 0px!important;
		          padding: 0px!important;
		    }
		    #editForm\:formTab\:uploadSupDocPanel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadSupDocPanel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		    #editForm\:formTab\:uploadP10Panel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadP10Panel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		    #editForm\:formTab\:uploadP12Panel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadP12Panel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		    #editForm\:formTab\:uploadFullVerPanel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadFullVerPanel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		    #editForm\:formTab\:uploadTocAttPanel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadTocAttPanel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		    #editForm\:formTab\:uploadScwPanel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadScwPanel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		    #editForm\:formTab\:uploadAddInfoPanel_content{
		    	border: 0px!important;
		    }
		    #editForm\:formTab\:uploadAddInfoPanel_content > div{
		    	border: 1px solid #dee2e6;
		    }
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
	<h:outputScript>
	$(document).ready(function(){
		$('#editForm\\:btn_add').click(function () {
		    $('html, body').animate({scrollTop:$(document).height()}, 'slow');
		    return false;
		});		
		$('.chinese-500').ChineseLengthLimit({ 
	      	 limitCount: 500, 
		      isByte: true
		   }); 
		
		 $('.chinese-500').trigger('checkLimit');
		 
		 $('.chinese-150').ChineseLengthLimit({ 
		       limitCount: 500, 
		      isByte: true
		   }); 
		 $('.chinese-150').trigger('checkLimit');
		 
		 triggerCountWords();
	});
	function scrollToTabTop() {
	    setTimeout(function() {
	        var tabView = document.getElementById('editForm:formTab');
	        if (tabView) {
	            var activeTab = tabView.querySelector('.ui-tabs-panel:not(.ui-tabs-hide)');
	            if (activeTab) {
	                activeTab.scrollIntoView({ behavior: 'smooth' });
	            }
	        }
	    }, 100); // Delay of 100 milliseconds
	}
	
	function triggerCountWords(){
		$('.countWords').trigger('keyup');
	}
	
	function countWords(countWordsId, val, num) {
	    var text = val.value.trim();
	
	    // Count words using Microsoft Word's method
	    // This regex matches sequences of characters separated by spaces or punctuation
	    // It treats hyphenated words and words with apostrophes (including Unicode apostrophes) as single words
	    // It ignores standalone punctuation marks
	    var words = text.match(/[\w’'-]+(?:-[\w’'-]+)*/g) || [];
	
	    // Total word count
	    var totalWords = words.length;
	
	    // Get the element where the warning and word count will be displayed
	    var wordCountElement = document.getElementById('editForm:formTab:' + countWordsId);
	
	    if (totalWords > num) {
	        // If the word count exceeds the limit, display a warning
	        wordCountElement.style.color = "red";
	        wordCountElement.innerText = "Word limit exceeded! You have " + (totalWords - num) + " word(s) over the limit.";
	    } else {
	        // If within the limit, display the remaining word count
	        wordCountElement.style.color = "#495057";
	        wordCountElement.innerText = "Words remaining: " + (num - totalWords) + " word(s)";
	    }
	}
	</h:outputScript>
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-chart-pie"></i> Manage RAE Research Output</span>
	<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
	<p:scrollTop />
	<h:outputText value="You don't have access right." rendered="#{raeOutputView.hasAccessRight eq false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
	<h:form id="editForm" rendered="#{raeOutputView.hasAccessRight eq true}">	
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-10">
				<p:linkButton value="#{bundle['action.back']} to RO list" outcome="manageRAEOutput" icon="pi pi-arrow-left" styleClass="btn-back" 
								rendered="#{raeOutputView.canBackRoList() eq true}">		
					<f:param name="pid" value="#{raeOutputView.paramPid}"/>
				</p:linkButton>
				<p:defaultCommand target="dummy"/>
				<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
				<p:commandButton id="top_btn_sava" value="#{formBundle['form.save']}" 
										  rendered="#{raeOutputView.canModifyRae == true}"
										  style="margin-right:5px; margin-bottom:1px;"
										  update="@form messages"
								  		  action="#{raeOutputView.saveRaeOutputForm}"
								  		  oncomplete="window.scrollTo(0,0);">
				</p:commandButton>	
		        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true" width="350">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		        </p:confirmDialog>
			</div>
		</div>	
		<br/>
		<!-- Staff Profile -->
		<div class="form-sub-title" style="background: #026539; color:#fff; padding: 2px 6px 2px 6px;">
		<i class="fa fa-id-card" style="margin-right:5px;"></i>Staff Profile
		</div>
		<hr style="margin:0"/>
		<div class="ui-g" style="background:#f8fff0">
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Name" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.staffTitle} #{raeOutputView.selectedRaeStaff.staffName}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Faculty/ Department" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.faculty}/ #{raeOutputView.selectedRaeStaff.department}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Panel" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.panelInfo}" escape="false"/>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Unit of Assessment" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.uoaInfo}" escape="false"/>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Research Area" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.raInfo}" escape="false"/>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="No. of Outputs to be Submitted" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.outSubNoStr}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Total No. of Outputs Selected" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.getOutputCount(raeOutputView.selectedRaeStaff.staffNumber)}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Double-weighted" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.getDWCount(raeOutputView.selectedRaeStaff.staffNumber)}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Remarks" />
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<p:message for="remarks"/>
				<p:inputTextarea id="remarks" label="Remarks" title="Remarks" style="width: 99%;" rows="2" counter="remarks_display" maxlength="1000" 
										value="#{raeOutputView.selectedRaeStaff.selRemark}"
										disabled="#{raeOutputView.canModifyRae == false}"
	                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
	            <br/>
	    		<h:outputText id="remarks_display" class="p-d-block" style="font-size:12px"/>
			</div>
		</div>
		<br/>
		<!-- Research Output Status -->
		<div class="form-sub-title" style="background: #29659b; color:#fff; padding: 2px 6px 2px 6px;">
		<i class="fa fa-bookmark" style="margin-right:5px;"></i>Research Output Status
		</div>
		<hr style="margin:0"/>
		<div class="ui-g" style="background:#e1fbf6">
			<div class="ui-g-12 ui-md-6 ui-lg-6">
				<p:outputLabel class="raeForm-item-title" value="RAE RO No　　"/>
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeOutputStatus.pk.output_no}"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-6">
				<p:outputLabel class="raeForm-item-title" value="Completion Status　　"/>
				<p:outputLabel class="raeForm-item-ans" style="background: green; color:#fff; font-weight:600; padding:5px; border-radius:4px;" value="Completed" rendered="#{raeOutputView.selectedRaeOutputStatus.infoComp eq 'Y'}"/>
				<p:outputLabel class="raeForm-item-ans" style="background: red; color:#fff; font-weight:600; padding:5px; border-radius:4px;" value="Incomplete" rendered="#{raeOutputView.selectedRaeOutputStatus.infoComp eq 'N'}"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-6">
				<p:outputLabel class="raeForm-item-title" value="RAE Selection Type　　"/>
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.getSelectedTypeDesc(raeOutputView.selectedRaeOutputStatus.selType)}"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Last Modified by　　" />
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeOutputStatus.userstamp}" />
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Last Modified at　　" />
				<h:outputText class="raeForm-item-ans" value="#{raeOutputView.selectedRaeOutputStatus.timestamp}" >
					    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
				</h:outputText>	
			</div>
		</div>
		<hr style="#{manageRIView.isRaeOrLibAdmin eq true?'border-style:solid; color:#ddcece':'display: none'}"/>
		<!-- for RDO and library only -->
		<div class="ui-g" style="#{manageRIView.isRaeOrLibAdmin eq true?'background:#f0f0f9':'display: none'}">
			<div class="ui-g-12 ui-md-12 ui-lg-12">
				<i class="fas fa-tag" style="margin-right:5px;"></i>For RAE admin only
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Research Output Information"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:selectOneMenu id="rae_status_code" styleClass="raeMenu" title="Research Output Information" 
								value="#{raeOutputView.selectedRaeOutputHeader.rae_status_code}"
								disabled="#{manageRIView.isRaeAdmin eq false}">
					<f:selectItems value="#{raeOutputView.statusList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Information for Specific Panels"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:selectOneMenu id="rae_status_code_panel" styleClass="raeMenu" title="Information for Specific Panels" 
								value="#{raeOutputView.selectedRaeOutputHeader.rae_status_code_panel}"
								disabled="#{manageRIView.isRaeAdmin eq false}">
					<f:selectItems value="#{raeOutputView.statusList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Full Version Submission of Research Output"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:selectOneMenu id="rae_status_code_full_ver" styleClass="raeMenu" title="Full Version Submission of Research Output" 
								value="#{raeOutputView.selectedRaeOutputHeader.rae_status_code_full_ver}"
								disabled="#{manageRIView.isRaeAdmin eq false}">
					<f:selectItems value="#{raeOutputView.statusList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Other Information"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:selectOneMenu id="rae_status_code_oth_info" styleClass="raeMenu" title="Other Information" 
								value="#{raeOutputView.selectedRaeOutputHeader.rae_status_code_oth_info}"
								disabled="#{manageRIView.isRaeAdmin eq false}">
					<f:selectItems value="#{raeOutputView.statusList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Ineligible"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-9">
				<p:selectOneMenu id="rae_status_ineligible" styleClass="raeMenu" title="Ineligible" 
								value="#{raeOutputView.selectedRaeOutputHeader.rae_status_ineligible}"
								disabled="#{manageRIView.isRaeAdmin eq false}">
					<f:selectItem itemLabel="N/A" itemValue="NA"/>
					<f:selectItem itemLabel="Ineligible staff" itemValue="IES"/>
					<f:selectItem itemLabel="Ineligible output" itemValue="IEO"/>
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Remarks (for RDO)" />
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-9">
				<p:message for="ro_remarks"/>
				<p:inputTextarea id="ro_remarks" label="Remarks (for RDO)" title="Remarks (for RDO)" style="width: 99%;" rows="2" counter="ro_remarks_display" maxlength="1000" 
										value="#{raeOutputView.selectedRaeOutputHeader.ro_remarks}"
										disabled="#{manageRIView.isRaeAdmin eq false}"
	                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
	            <br/>
	    		<h:outputText id="ro_remarks_display" class="p-d-block" style="font-size:12px"/>
			</div>
		</div>
		<hr style="#{manageRIView.isRaeOrLibAdmin eq true?'border-style:solid; color:#ddcece':'display: none'}"/>
		<div class="ui-g" style="#{manageRIView.isRaeOrLibAdmin eq true?'background:#f0f7f9':'display: none'}">
			<div class="ui-g-12 ui-md-12 ui-lg-12">
				<i class="fas fa-tag" style="margin-right:5px;"></i>For Library admin only
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Citation Checking"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-1">
				<p:selectOneMenu id="citation_chk_code" styleClass="raeMenu" title="Citation Checking" 
								value="#{raeOutputView.selectedRaeOutputHeader.citation_chk_code}"
								disabled="#{manageRIView.isLibAdmin eq false}">
					<f:selectItems value="#{raeOutputView.citationCheckingList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-1">
				<p:outputLabel class="raeForm-item-title" value="- Abstract"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-1">
				<p:selectOneMenu id="citation_chk_abs_code" styleClass="raeMenu" title="Citation Checking - Abstract" 
								value="#{raeOutputView.selectedRaeOutputHeader.citation_chk_abs_code}"
								disabled="#{manageRIView.isLibAdmin eq false}">
					<f:selectItems value="#{raeOutputView.citationCheckingList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-1">
				<p:outputLabel class="raeForm-item-title" value="- Fulltext"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-5">
				<p:selectOneMenu id="citation_chk_fulltext_code" styleClass="raeMenu" title="Citation Checking - Fulltext" 
								value="#{raeOutputView.selectedRaeOutputHeader.citation_chk_fulltext_code}"
								disabled="#{manageRIView.isLibAdmin eq false}">
					<f:selectItems value="#{raeOutputView.citationCheckingList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Copyright Clearance"/>
			</div>
			<div class="ui-g-12 ui-md-6 ui-lg-9">
				<p:selectOneMenu id="copyright_clr_code" styleClass="raeMenu" title="Copyright Clearance" 
								value="#{raeOutputView.selectedRaeOutputHeader.copyright_clr_code}"
								disabled="#{manageRIView.isLibAdmin eq false}">
					<f:selectItems value="#{raeOutputView.copyrightClearList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />
                </p:selectOneMenu>
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-3">
				<p:outputLabel class="raeForm-item-title" value="Remarks (for LIB)" />
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-9">
				<p:message for="ro_remarks_lib"/>
				<p:inputTextarea id="ro_remarks_lib" label="Remarks (for RDO)" title="Remarks (for LIB)" style="width: 99%;" rows="2" counter="ro_remarks_lib_display" maxlength="1000" 
										value="#{raeOutputView.selectedRaeOutputHeader.ro_remarks_lib}"
										disabled="#{manageRIView.isLibAdmin eq false}"
	                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
	            <br/>
	    		<h:outputText id="ro_remarks_lib_display" class="p-d-block" style="font-size:12px"/>
			</div>
		</div>
		<br/>
		<div class="form-sub-title"  style="background: #95113a; color:#fff; padding: 2px 6px 2px 6px;">
			<i class="fas fa-tag" style="margin-right:5px;"></i>Citation Preview
		</div>
		<hr style="margin:0"/>
		<div class="ui-g" style="background:#fff0f7;">
			<!-- <div class="ui-g-12 ui-md-12 ui-lg-12">
				<p:outputLabel style="color:#0277BD; font-weight:700;" value="(In #{raeOutputView.citation} format)"/>
			</div> -->
			<div class="ui-g-12 ui-md-12 ui-lg-12" >
				<p:outputLabel id="apa" style="display: block; padding-left: 0.5em; white-space: normal; color: #4c460e;" value="#{raeOutputView.selectedRaeOutputHeader.apa_citation}" escape="false"></p:outputLabel>
			</div>
		</div>	
		<br/>
		<hr style="border: 1px solid #678394; margin-top:0; margin-bottom:0;"/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="min-height: 400px; padding-top: 0px; padding-bottom: 0px;">
			<p:tabView id="formTab">
				<p:ajax event="tabChange" oncomplete="scrollToTabTop();" />
			<!-- Research Output Information -->
			<p:tab>
				<f:facet name="title">
					<i class="fa fa-pencil" style="margin-right:5px;"></i>Research Output Information <p:badge value="#{raeOutputView.countError('ro_info')}" severity="danger" class="ml-1" rendered="#{raeOutputView.countError('ro_info') gt 0}"></p:badge>
				</f:facet>
				<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
					<i class="fas fa-tag" style="margin-right:5px;"></i>Research Output Information
				</div>
				<hr style="margin:0"/>
				<!-- Research Area of Research Output -->
				<div class="ui-g" style="background:#fcfdf0">
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						<p:outputLabel class="raeForm-item-title" style="vertical-align: top;" value="Research Area of Research Output"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="researchAreaOfRo"/>
						<p:selectOneMenu id="researchAreaOfRo" styleClass="raeMenu" 
										style="width:90%; white-space: pre-wrap;" 
										escape="false"
										label="Research Area of Research Output" 
										title="Research Area of Research Output" 
										value="#{raeOutputView.selectedRaeOutputHeader.researchAreaOfRo}"
										disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{raeOutputView.raList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false" itemDisabled="#{d.lookup_level == 2}"/>
		                	<p:ajax event="change" update="title_sub_disc_code ans_sub_disc_code ans_cat_code"/>
		                </p:selectOneMenu>
					</div>
					<!-- Sub-disciplines of Research Output -->
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_sub_disc_code">
							<p:outputLabel class="raeForm-item-title" style="vertical-align: top;" value="Sub-disciplines of Research Output" rendered="#{fn:length(raeOutputView.subDiscList) gt 0}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_sub_disc_code">
							<p:message for="sub_disc_code"/>
							<p:selectOneMenu id="sub_disc_code" styleClass="raeMenu" 
												style="width:90%; white-space: pre-wrap;" 
												escape="false"
												title="Sub-disciplines of Research Output" 
												value="#{raeOutputView.selectedRaeOutputHeader.sub_disc_code}" 
												rendered="#{fn:length(raeOutputView.subDiscList) gt 0}"
												disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.subDiscList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false"/>
			                </p:selectOneMenu>
		                </h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Category Code(s) (Max. 2 Category Codes)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<h:panelGroup id="ans_cat_code">
							<p:message for="cat_code"/>
							<p:selectCheckboxMenu  id="cat_code" 
												title="Category Code(s)"
												label="-- Please select --"
												disabled="#{raeOutputView.canModifyRae == false}"
												filter = "true" filterMatchMode= "startsWith" filterNormalize = "true" multiple = "true"
												style="min-width: 15rem"
												var="c"
												value="#{raeOutputView.selectedRaeOutputHeader.cat_code_list}">
									<f:selectItems value="#{raeOutputView.getCatListByRa(raeOutputView.selectedRaeOutputHeader.researchAreaOfRo)}" var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false" itemDisabled="#{d.lookup_level == 3}"/>
							</p:selectCheckboxMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							Research Output Type (RICH)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
							<p:message for="sap_output_type"/>
							<p:selectOneMenu id="sap_output_type" title="Research Output Type (RICH)" label="Research Output Type (RICH)" value="#{raeOutputView.selectedRaeOutputHeader.sap_output_type}" 
													disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.outputTypeList}" var="o" 
								itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
								<p:ajax event="change" 
										update="researchActivity vol_issue editForm:apa panel_book publisher columnTable" 
										listener="#{raeOutputView.resetResearchActivityList()}"/>
							</p:selectOneMenu>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							Research Output Type (RAE)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
							<p:message for="output_type"/>
							<p:selectOneMenu id="output_type" title="Research Output Type (RAE)" label="Research Output Type (RAE)" 
												value="#{raeOutputView.selectedRaeOutputHeader.output_type}"
												disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.raeOutputTypeList}" var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}"/>
								<p:ajax event="change" update="title_oth_output_type ans_oth_output_type title_role_submit_staff ans_role_submit_staff panel_issn panel_isbn panel_article panel_vol panel_page panel_desc_loc_output panel_book panel_scw"/>
							</p:selectOneMenu>
					</div>
					<!-- Other Output Type (in English) -->
					<div class="ui-g-2 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_oth_output_type">
							<p:outputLabel class="raeForm-item-title" value="Other Output Type (in English)" rendered="#{raeOutputView.selectedRaeOutputHeader.output_type eq 'N'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-10 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_oth_output_type">
							<p:message for="oth_output_type"/>
							<p:inputText id="oth_output_type" title="Other Output Type (in English)" label="Other Output Type (in English)" style="width:90%" 
												value="#{raeOutputView.selectedRaeOutputHeader.oth_output_type}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.output_type eq 'N'}"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
						</h:panelGroup>
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							Type of Research Activity
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
							<h:panelGroup id="researchActivity">
								<p:message for="sap_refered_journal"/>
								<p:selectOneMenu id="sap_refered_journal" 
														title="Type of Research Activity" 
														label="Type of Research Activity" 
														value="#{raeOutputView.selectedRaeOutputHeader.sap_refered_journal}" 
														disabled="#{raeOutputView.canModifyRae == false}">
									<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
									<f:selectItems value="#{raeOutputView.researchTypeList}" var="o" 
														itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" />
								</p:selectOneMenu>
							</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							Non-traditional Research Output
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
							<p:message for="non_trad_output_ind"/>
							<p:selectOneMenu id="non_trad_output_ind" 
													title="Non-traditional Research Output"
													label="Non-traditional Research Output"
													 value="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind}" 
													 disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
								<p:ajax event="change" update="title_info_of_non_trad_output ans_info_of_non_trad_output panel_toc panel_toc_oth panel_additional_info"/> 
								<p:ajax event="change" oncomplete="triggerCountWords()"/>
							</p:selectOneMenu>
					</div>		
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_info_of_non_trad_output">
							<p:outputLabel class="raeForm-item-title" value="Supplementary Information on Non-traditional Research Output (Max. 300 words)" escape="false" rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'Y'}"/>
							<p:outputLabel class="raeForm-item-title" style="color:#0277BD; display:block;" value="Please provide additional information on (i) novelty of the work; (ii) the deliverables; and (iii) the dissemination method." 
											rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'Y'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_info_of_non_trad_output">
							<p:message for="info_of_non_trad_output"/>
							<p:inputTextarea id="info_of_non_trad_output" style="width: 99%;" rows="2" maxlength="3000" class="countWords"
													label="Supplementary Information on Non-traditional Research Output" 
													title="Supplementary Information on Non-traditional Research Output"  
													value="#{raeOutputView.selectedRaeOutputHeader.info_of_non_trad_output}"
													disabled="#{raeOutputView.canModifyRae == false}"
													rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'Y'}"
													onkeyup="countWords('wordCount_info_of_non_trad_output', this,300)"
													onload="countWords('wordCount_info_of_non_trad_output', this,300)"
													/>
			    			<h:outputText id="wordCount_info_of_non_trad_output" class="p-d-block" style="font-size:12px" rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'Y'}"/>
			    		</h:panelGroup>
					</div>
					<!-- Justification for Double-weighting Request -->
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="#{raeOutputView.isDoubledWeighted eq true?'':'display:none; padding-top: 0; padding-bottom: 0;'}">
						<p:outputLabel class="raeForm-item-title" value="Justification for Double-weighting Request (Max. 100 words)"/><br/>
						<p:outputLabel class="raeForm-item-title" style="color:#0277BD; display:block;"
							value="Please explain why the output merits double-weighting. E.g. How the research output (e.g. its scale or scope) required research effort equivalent to that required to produce two or more single outputs?" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="#{raeOutputView.isDoubledWeighted eq true?'':'display:none; padding-top: 0; padding-bottom: 0;'}">
							<p:message for="just_dw_request"/>
							<p:inputTextarea id="just_dw_request" style="width: 99%;" rows="2" maxlength="2000" class="countWords"
													label="Justification for Double-weighting Request" 
													title="Justification for Double-weighting Request"  
													value="#{raeOutputView.selectedRaeOutputHeader.just_dw_request}"
													disabled="#{raeOutputView.canModifyRae == false}"
													onkeyup="countWords('wordCount_just_dw_request', this, 100)"
													onload="countWords('wordCount_just_dw_request', this, 100)"
				                      				autoResize="false"/>
		
			    			<h:outputText id="wordCount_just_dw_request" class="p-d-block" style="font-size:12px"/>
					</div>
					<!-- Interdisciplinary -->
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Interdisciplinary
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" >
						<p:message for="inter_research"/>
						<p:selectOneMenu id="inter_research"
											title="Interdisciplinary"
											label="Interdisciplinary"
											disabled="#{raeOutputView.canModifyRae == false}"
											value="#{raeOutputView.selectedRaeOutputHeader.inter_research}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="title_pri_research_area_inter ans_pri_research_area_inter title_sec_research_area_inter ans_sec_research_area_inter title_pri_research_area_cat ans_pri_research_area_cat title_sec_research_area_cat ans_sec_research_area_cat"/> 
						</p:selectOneMenu>
					</div>
					<!-- Primary Area -->
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_pri_research_area_inter">
							<p:outputLabel class="raeForm-item-title" value="Primary Area" rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_pri_research_area_inter">
							<p:message for="pri_research_area_inter"/>
							<p:selectOneMenu id="pri_research_area_inter" 
												style="width:90%; white-space: pre-wrap;" 
												escape="false"
												title="Primary Area"
												label="Primary Area"
												value="#{raeOutputView.selectedRaeOutputHeader.pri_research_area_inter}"
												disabled="#{raeOutputView.canModifyRae == false}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.raList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false" itemDisabled="#{d.lookup_level == 2}"/>
								<p:ajax event="change" update="ans_pri_research_area_cat"/> 
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<!-- Primary Area Cat-->
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_pri_research_area_cat">
							<p:outputLabel class="raeForm-item-title" value="Category Code under Primary Research Area" rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_pri_research_area_cat">
							<p:message for="pri_research_area_cat"/>
							<p:selectOneMenu id="pri_research_area_cat" 
												style="width:90%; white-space: pre-wrap;" 
												escape="false"
												title="Category Code under Primary Research Area"
												label="Category Code under Primary Research Area"
												value="#{raeOutputView.selectedRaeOutputHeader.pri_research_area_cat}"
												disabled="#{raeOutputView.canModifyRae == false}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.getCatListByRa(raeOutputView.selectedRaeOutputHeader.pri_research_area_inter)}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false" itemDisabled="#{d.lookup_level == 2}"/>
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<!-- Secondary Area -->
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_sec_research_area_inter">
							<p:outputLabel class="raeForm-item-title" value="Secondary Area" rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_sec_research_area_inter">
							<p:message for="sec_research_area_inter"/>
							<p:selectOneMenu id="sec_research_area_inter" 
												style="width:90%; white-space: pre-wrap;" 
												escape="false"
												title="Secondary Area"
												label="Secondary Area"
												value="#{raeOutputView.selectedRaeOutputHeader.sec_research_area_inter}"
												disabled="#{raeOutputView.canModifyRae == false}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.raList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false" itemDisabled="#{d.lookup_level == 2}"/>
								<p:ajax event="change" update="ans_sec_research_area_cat"/> 
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<!-- Secondary Area Cat-->
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_sec_research_area_cat">
							<p:outputLabel class="raeForm-item-title" value="Category Code under Secondary Research Area" rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_sec_research_area_cat">
							<p:message for="sec_research_area_cat"/>
							<p:selectOneMenu id="sec_research_area_cat" 
												style="width:90%; white-space: pre-wrap;" 
												escape="false"
												title="Category Code under Secondary Research Area"
												label="Category Code under Secondary Research Area"
												value="#{raeOutputView.selectedRaeOutputHeader.sec_research_area_cat}"
												disabled="#{raeOutputView.canModifyRae == false}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.inter_research eq 'Y'}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItems value="#{raeOutputView.getCatListByRa(raeOutputView.selectedRaeOutputHeader.sec_research_area_inter)}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" itemLabelEscaped="false" itemDisabled="#{d.lookup_level == 2}"/>
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							Use of AI Tool
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="ai_tool_ind"/>
						<p:selectOneMenu id="ai_tool_ind" 
												title="Use of AI Tool"
												label="Use of AI Tool"
												 value="#{raeOutputView.selectedRaeOutputHeader.ai_tool_ind}" 
												 disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="title_ai_tool_desc ans_ai_tool_desc"/> 
							<p:ajax event="change" oncomplete="triggerCountWords()"/>
						</p:selectOneMenu>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_ai_tool_desc">
							<p:outputLabel class="raeForm-item-title" value="Description of Use of AI Tool (Max. 200 words)" rendered="#{raeOutputView.selectedRaeOutputHeader.ai_tool_ind eq 'Y'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_ai_tool_desc">
							<p:message for="ai_tool_desc"/>
							<p:inputTextarea id="ai_tool_desc" class="countWords"
												label="Description of Use of AI Tool (Max. 200 words)" 
												title="Description of Use of AI Tool (Max. 200 words)" 
												style="width: 99%;" rows="4" maxlength="4000" 
												value="#{raeOutputView.selectedRaeOutputHeader.ai_tool_desc}"
												disabled="#{raeOutputView.canModifyRae == false}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.ai_tool_ind eq 'Y'}"
												onkeyup="countWords('wordCount_ai_tool_desc', this,200)"
												onload="countWords('wordCount_ai_tool_desc', this,200)"
				                      			autoResize="false"/>
				    		<h:outputText id="wordCount_ai_tool_desc" class="p-d-block" style="font-size:12px" rendered="#{raeOutputView.selectedRaeOutputHeader.ai_tool_ind eq 'Y'}"/>
			    		</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Output Language
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="rel_lang"/>
						<p:selectOneMenu id="rel_lang"
											title="Output Language"
											label="Output Language"
											disabled="#{raeOutputView.canModifyRae == false}"
											value="#{raeOutputView.selectedRaeOutputHeader.rel_lang}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{raeOutputView.outputLangList}"  var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}" />   
							<p:ajax event="change" update="title_oth_lang ans_oth_lang title_list_of_author_non_eng ans_list_of_author_non_eng title_title_of_non_eng_output ans_title_of_non_eng_output"/> 
						</p:selectOneMenu>
					</div>
					<!-- Other Language / Multiple Languages -->
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_oth_lang">
							<p:outputLabel class="raeForm-item-title" value="Other Language / Multiple Languages" rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang eq 'MUL'}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_oth_lang">
							<p:message for="oth_lang"/>
							<p:selectCheckboxMenu  id="oth_lang" 
												title="Other Language / Multiple Languages"
												label="-- Please select --"
												disabled="#{raeOutputView.canModifyRae == false}"
												filter = "true" filterMatchMode= "startsWith" filterNormalize = "true" multiple = "true"
												style="min-width: 15rem"
												var="c"
												rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang eq 'MUL'}"
												value="#{raeOutputView.selectedRaeOutputHeader.oth_lang_list}">
									<f:selectItems value="#{raeOutputView.othLangList}" var="d" itemLabel="#{d.description}" itemValue="#{d.pk.lookup_code}"/>
							</p:selectCheckboxMenu>
						</h:panelGroup>
					</div>
				</div>

				<div class="ui-g" style="background:#fcfdf0">
					<div class="ui-g-12 ui-md-12 ui-lg-3 raeForm-item-title">
							Title of Research Output (in English)<br/>
							<span style="color:#e56507;">(Capitalize the first character of all major words)</span>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-9">
							<p:message for="title_of_eng_output"/>
							<p:inputTextarea id="title_of_eng_output" style="width: 99%;" rows="3" counter="title_of_eng_output_display" maxlength="500"
												label="Title of Research Output (in English)" 
												title="Title of Research Output (in English)"  
												value="#{raeOutputView.selectedRaeOutputHeader.title_of_eng_output}"
												disabled="#{raeOutputView.canModifyRae == false}"
			                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false">
			                      				<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
				            </p:inputTextarea>
				    		<h:outputText id="title_of_eng_output_display" class="p-d-block" style="font-size:12px"/>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-3" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="title_title_of_non_eng_output">
							<p:outputLabel class="raeForm-item-title" 
											value="Title of Research Output in Published Language" 
											rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"/>
							<p:outputLabel class="raeForm-item-title" 
											style="color:#e56507; display:block"
											value=" (e.g. in Chinese or in other language, if applicable)" 
											rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-9" style="padding-top: 0; padding-bottom: 0;">
						<h:panelGroup id="ans_title_of_non_eng_output">
							<p:message for="title_of_non_eng_output"/>
							<p:inputTextarea id="title_of_non_eng_output" style="width: 99%;" rows="3" counter="title_of_non_eng_output_display" maxlength="500"
												label="Title of Research Output in Published Language" 
												title="Title of Research Output in Published Language"  
												value="#{raeOutputView.selectedRaeOutputHeader.title_of_non_eng_output}"
												disabled="#{raeOutputView.canModifyRae == false}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"
			                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false">
			                      				<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
				            </p:inputTextarea>
				    		<h:outputText id="title_of_non_eng_output_display" class="p-d-block" style="font-size:12px"/>	
				    	</h:panelGroup>
					</div>
				</div>
				<!-- Submitting Staff -->
				<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
						<i class="fa fa-user" style="margin-right:5px;"></i>Submitting Staff
				</div>
				<hr style="margin:0"/>
				<div class="ui-g" style="background:#fcfdf0">
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="raeForm-item-title" value="Name" />
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.staffName}" />
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="raeForm-item-title" value="Pseudonym 筆名" />
						<p:outputLabel class="raeForm-item-title" style="color:#0277BD;" value=" (e.g. Dawen)(e.g. 大文)" /><br/>
						<p:outputLabel class="raeForm-item-title" style="color:#e56507;  display:block;" value=" (if the output is published under pseudonym)"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:message for="pseudonym"/>
						<p:inputText id="pseudonym" title="Pseudonym" label="Pseudonym" style="width:90%" 
										value="#{raeOutputView.selectedRaeOutputHeader.pseudonym}"
										disabled="#{raeOutputView.canModifyRae == false}">
						</p:inputText>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<h:panelGroup id="title_role_submit_staff">
							<p:outputLabel class="raeForm-item-title" value="Role of Submitting Staff" rendered="false"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<h:panelGroup id="ans_role_submit_staff">
							<p:inputText id="role_submit_staff" title="Role of Submitting Staff" label="Role of Submitting Staff" 
										value="#{raeOutputView.selectedRaeOutputHeader.role_submit_staff}" 
										rendered="false"
										disabled="#{raeOutputView.canModifyRae == false}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-6"></div>
					
					<!-- Statement on Originality and Significance, etc. -->
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="raeForm-item-title" value="Statement on Originality and Significance (Max. 100 words)" 
										rendered="#{raeOutputView.showStmt_ori_sign() eq true}"/>
						<p:outputLabel class="raeForm-item-title" value="(Mandatory for panels 11,12 and 13. Please refer to the Panel-specific Guidelines for the detailed requirement.)" 
										style="color:#e56507; display:block" rendered="#{raeOutputView.showStmt_ori_sign() eq true}"/>				
										
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<h:panelGroup id="ans_stmt_ori_sign">
							<p:message for="stmt_ori_sign"/>
							<p:inputTextarea id="stmt_ori_sign" style="width: 99%;" rows="3" maxlength="2000" class="countWords"
												label="Statement on Originality and Significance (Max. 100 words)" 
												title="Statement on Originality and Significance (Max. 100 words)"  
												value="#{raeOutputView.selectedRaeOutputHeader.stmt_ori_sign}"
												disabled="#{raeOutputView.canModifyRae == false}"
												onkeyup="countWords('wordCount_stmt_ori_sign', this,100)"
												onload="countWords('wordCount_stmt_ori_sign', this,100)"
												rendered="#{raeOutputView.showStmt_ori_sign() eq true}"
			                      				autoResize="false">
				            </p:inputTextarea>
				    		<h:outputText id="wordCount_stmt_ori_sign" class="p-d-block" style="font-size:12px"/>
						</h:panelGroup>
					</div>
				</div>

				
				
				<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
						<i class="fa fa-users" style="margin-right:5px;"></i>Authors Information
				</div>
				<hr style="margin:0"/>
				<div class="ui-g" style="background:#fcfdf0">
					<!-- No. of Author(s) -->
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						No. of Author(s)
						<!-- <span style="color:#e56507">(Include submitting author)</span> -->
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="no_of_co_author"/>
						<p:selectOneMenu id="no_of_co_author" title="No. of Author(s)" label="No. of Author(s)" 
										converter="javax.faces.Integer"
										value="#{raeOutputView.selectedRaeOutputHeader.no_of_co_author}" 
										disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItem itemLabel="Single Author" itemValue="0"/>
								<f:selectItems value="#{raeOutputView.noOfAuthorList}"/>
								<p:ajax event="change" update="title_explanation_of_author_ctb ans_explanation_of_author_ctb title_role_author_ind ans_role_author_ind"/>
						</p:selectOneMenu>
					</div>
					<!-- Role of Submitting Author -->
					<div class="ui-g-12 ui-md-12 ui-lg-3">
						<h:panelGroup id="title_role_author_ind">
							<p:outputLabel class="raeForm-item-title" value="Role of Submitting Author" 
											rendered="#{raeOutputView.showRoleSubmitAuthor(raeOutputView.selectedRaeOutputHeader.no_of_co_author) eq true}"/>
							<p:outputLabel class="raeForm-item-title" value="(Indicate whether the submitting author is a main author (i.e. first, corresponding or last, according to disciplinary conventions).)" 
											style="color:#e56507; display:block"
											rendered="#{raeOutputView.showRoleSubmitAuthor(raeOutputView.selectedRaeOutputHeader.no_of_co_author) eq true}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-9">
						<h:panelGroup id="ans_role_author_ind">
							<p:message for="role_author_ind"/>
							<p:selectOneMenu id="role_author_ind" title="Role of Submitting Author" 
											label="Role of Submitting Author" 
											value="#{raeOutputView.selectedRaeOutputHeader.role_author_ind}" 
											rendered="#{raeOutputView.showRoleSubmitAuthor(raeOutputView.selectedRaeOutputHeader.no_of_co_author) eq true}"
											disabled="#{raeOutputView.canModifyRae == false}">
									<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
									<f:selectItem itemLabel="Yes" itemValue="Y"/>
									<f:selectItem itemLabel="No" itemValue="N"/>
									<p:ajax event="change" update="title_explanation_of_author_ctb ans_explanation_of_author_ctb"/>
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<!-- Explanation of Submitting Author's Contribution -->
					<div class="ui-g-12 ui-md-12 ui-lg-3">
						<h:panelGroup id="title_explanation_of_author_ctb">
							<p:outputLabel class="raeForm-item-title" value="Explanation of Submitting Author's Contribution (Max. 100 words)" 
											rendered="#{raeOutputView.showExplanationOfAuthorContribution(raeOutputView.selectedRaeOutputHeader.no_of_co_author, raeOutputView.selectedRaeOutputHeader.role_author_ind) eq true}"/>
							<p:outputLabel class="raeForm-item-title" value="(Clarify the extent and character of your contribution to the research process leading to this output)" 
											style="color:#e56507; display:block"
											rendered="#{raeOutputView.showExplanationOfAuthorContribution(raeOutputView.selectedRaeOutputHeader.no_of_co_author, raeOutputView.selectedRaeOutputHeader.role_author_ind) eq true}"/>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-9">
						<h:panelGroup id="ans_explanation_of_author_ctb">
							<p:message for="explanation_of_author_ctb"/>
							<p:inputTextarea id="explanation_of_author_ctb" style="width: 99%;" rows="3" maxlength="2000" class="countWords"
												label="Explanation of Submitting Author's Contribution" 
												title="Explanation of Submitting Author's Contribution"  
												value="#{raeOutputView.selectedRaeOutputHeader.explanation_of_author_ctb}"
												disabled="#{raeOutputView.canModifyRae == false}"
												onkeyup="countWords('wordCount_explanation_of_author_ctb', this,100)"
												onload="countWords('wordCount_explanation_of_author_ctb', this,100)"
												rendered="#{raeOutputView.showExplanationOfAuthorContribution(raeOutputView.selectedRaeOutputHeader.no_of_co_author, raeOutputView.selectedRaeOutputHeader.role_author_ind) eq true}"
			                      				autoResize="false">
				            </p:inputTextarea>
				    		<h:outputText id="wordCount_explanation_of_author_ctb" class="p-d-block" style="font-size:12px" rendered="#{raeOutputView.showExplanationOfAuthorContribution(raeOutputView.selectedRaeOutputHeader.no_of_co_author, raeOutputView.selectedRaeOutputHeader.role_author_ind) eq true}"/>
						</h:panelGroup>
					</div>
					
				</div>
				<div class="ui-g" style="background:#fcfdf0">
					<!-- List of Author(s) (in English) -->
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						List of Author(s) (in English) <span style="color:#0277BD">(e.g. CHAN Kit; *CHAN Tai Man; LIN Jun Jie)</span>
						<p:outputLabel class="raeForm-item-title" 
										value="(1. Input names with no comma in publication order; 2. Capitalize Surname and the first character of Given name; 3. Add a * before submitting staff; 4. Separate by semi-colon)" 
										style="color:#e56507; display:block"/>
							
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:message for="list_of_author_eng"/>
						<p:inputText id="list_of_author_eng" label="List of Author(s) (in English)" 
										style="width:99%" 
										value="#{raeOutputView.selectedRaeOutputHeader.list_of_author_eng}" maxlength="4000" 
										disabled="#{raeOutputView.canModifyRae == false}">
							<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
						</p:inputText>
					</div>
					<!-- List of Author(s) (other than English) -->
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						<h:panelGroup id="title_list_of_author_non_eng">
						<p:outputLabel class="raeForm-item-title" value="List of Author(s) (other than English)"
										rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"/>
						<p:outputLabel class="raeForm-item-title" value="(e.g. 陳潔儀; *陳戴聞; 林俊杰)" style="color:#0277BD"
										rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"/>
						<p:outputLabel class="raeForm-item-title" 
										value="(1. Input names in publication order; 2. Capitalize Surname and the first character of Given name; 3. Add a * before submitting staff; 4. Separate by semi-colon)" 
										style="color:#e56507; display:block"
										rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"/>
						</h:panelGroup>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<h:panelGroup id="ans_list_of_author_non_eng">
						<p:message for="list_of_author_non_eng"/>
						<p:inputText id="list_of_author_non_eng" label="List of Author(s) (other than English)" 
										style="width:99%" 
										value="#{raeOutputView.selectedRaeOutputHeader.list_of_author_non_eng}" maxlength="4000" 
										rendered="#{raeOutputView.selectedRaeOutputHeader.rel_lang ne 'ENG' &amp;&amp; raeOutputView.selectedRaeOutputHeader.rel_lang ne null}"
										disabled="#{raeOutputView.canModifyRae == false}">
							<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
						</p:inputText>
						</h:panelGroup>
					</div>
				</div>
				<hr style="margin:0px"/>
				<div class="ui-g" style="background:#fcfdf0">
					<!-- Published as of Census Date -->
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="raeForm-item-title" value="Published as of Census Date"/>
						<p:outputLabel class="raeForm-item-title" value="(30 September 2025)" style="color:#e56507; display:block"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="published_census_date"/>
						<p:selectOneMenu id="published_census_date" title="Published as of Census Date" 
										value="#{raeOutputView.selectedRaeOutputHeader.published_census_date}" 
										disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItem itemLabel="A - Published" itemValue="A"/>
								<f:selectItem itemLabel="B - Publicly accessible, but not in published form" itemValue="B"/>
								<f:selectItem itemLabel="C - Effectively shared within the profession, but not in published form" itemValue="C"/>
								<f:selectItem itemLabel="D - Officially accepted for publication, but not yet published as at census date" itemValue="D"/>
								<p:ajax event="valueChange" update="panel_upload_sup_doc title_date"/>
						</p:selectOneMenu>
					</div>
					<!-- Upload Files: Supporting Document / Letter of Acceptance for Publishing the Research Output -->
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<!-- <p:messages id="panel_upload_rae_sup_doc_msg" rendered="#{raeOutputView.doSave eq false}"/> -->
						
						<h:panelGroup id="panel_upload_sup_doc">
							<p:messages for="uploadedSupDocTable"/>
							<p:outputLabel class="raeForm-item-title" style="font-size:18px !important" value="Supporting Document / Letter of Acceptance for Publishing the Research Output"
												rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date eq 'D'}"/>
							<br/>
							<p:outputLabel id="sup_doc_msg" value="#{raeOutputView.uploadErrMsgSupDoc}" style="color:#fff; background:#e55555; display:block;"
												rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date eq 'D'}"/>
							<p:fileUpload id="uploadedSupDocFile" style="padding:2px;"
					                      mode="simple" skinSimple="true" auto="true"
					                      disabled="#{raeOutputView.canModifyRae == false}"
							  	 		  rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date eq 'D'}"
					                      process="@this" 
					                      update="uploadedSupDocTable sup_doc_msg"
					                      listener="#{raeOutputView.fileUploadListener_sup_doc}">
							</p:fileUpload>
							<h:outputText value=" (File Type: PDF, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_SUP_DOC')}MB)" 
										rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date eq 'D'}"></h:outputText>  
							<p:dataTable id="uploadedSupDocTable"
										value="#{raeOutputView.uploadSupDocList}"
										var="file"
										reflow="true"
				                  	 	tableStyle="table-layout:auto;"
				                  	 	style="width:100%;"
				                  	 	rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date eq 'D'}"
										>
				
								<p:column style="width:1em;">
						           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
										<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
						           	</p:commandLink>
						        </p:column>
				       
								<p:column headerText="File Name#{raeOutputView.doSave}">
									<h:outputText value="#{file.file_display_name}" />
								</p:column>
								
								<p:column headerText="File Size (KB)" style="width:20%">
									<h:outputText value="#{file.fileKbSize}" >
										<f:convertNumber maxFractionDigits="0" />
									</h:outputText>
								</p:column>
								
								<p:column headerText="Upload Date" style="width:20%">
									<h:outputText value="#{file.creationDate}" >
										<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
									</h:outputText>
								</p:column>
								
								<p:column style="width:1em;" rendered="#{!readonly}">
						           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_sup_doc')}"
												   icon="fa fa-trash"
												   title="Delete File"
												   update="uploadedSupDocTable editForm:formTab:sup_doc_msg"
												   ><i class="fas fa-trash icon-action" title="Delete"/>
										<p:confirm header="Confirm deletion?" 
												   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
												   icon="pi pi-exclamation-triangle" />										
						           	</p:commandLink>
								</p:column>
								
							</p:dataTable>
					</h:panelGroup>
				</div>
			</div>
			<div class="ui-g" style="background:#fcfdf0">
				<p:panel id="panel_book" style="border:0px!important; width:100%">
					<!-- Book / Journal Title -->
					<h:panelGroup id="panel_book_title" rendered="#{raeOutputView.showBookTitle() eq true}">
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						Book / Journal Title
						<p:outputLabel class="raeForm-item-title" 
											value="(Capitalize the first character of all major words, e.g. Consumer-Driven Demand and Operations Management Model)" 
											style="color:#e56507; display:block"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:message for="book_title"/>
						<p:inputText id="book_title" label="Book / Journal Title" 
										style="width:99%" 
										value="#{raeOutputView.selectedRaeOutputHeader.book_title}" maxlength="1000" 
										disabled="#{raeOutputView.canModifyRae == false}">
							<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
						</p:inputText>
					</div>	
				</h:panelGroup>
				<h:panelGroup id="panel_issn">
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							ISSN. <br/><span style="color:#0277BD">(e.g. 1050-124X)</span><span style="color:#e56507"> (Please input N/A if not applicable.)</span>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-3">
							<p:message for="issn"/>
							<p:inputText id="issn" label="ISSN" title="ISSN.  For example: 1050-124X" value="#{raeOutputView.selectedRaeOutputHeader.issn}" maxlength="9" 
											disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText><br/><br/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							eISSN. <br/><span style="color:#0277BD">(e.g. 1050-124X)</span><span style="color:#e56507;"> (Please input N/A if not applicable.)</span>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-3">
							<p:message for="eissn"/>
							<p:inputText id="eissn" label="eISSN" title="eISSN  For example: 1050-124X" value="#{raeOutputView.selectedRaeOutputHeader.eissn}" maxlength="9" 
											disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText><br/><br/>
					</div>
				</h:panelGroup>
				<h:panelGroup id="panel_isbn">
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							ISBN.  <br/><span style="color:#0277BD">(e.g. 0937175595)</span><span style="color:#e56507"> (Please input N/A if not applicable.)</span>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-3">
							<p:message for="isbn"/>
							<p:inputText id="isbn" label="ISBN" title="ISBN.  For example: 0937175595" value="#{raeOutputView.selectedRaeOutputHeader.isbn}" 
												maxlength="13" 
												onkeypress="return (event.charCode != 45);"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
					</div>
				</h:panelGroup>
				<h:panelGroup id="panel_article" rendered="#{raeOutputView.showArticleNo() eq true}">
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
							Article No.
							<br/>
							<span style="color:#0277BD">(e.g. 1, 2-4, 15)</span><span style="color:#e56507"> (Please input N/A if not applicable.)</span>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-3">
							<p:message for="article_no"/>
							<p:inputText id="article_no" label="Article No." title="Article No." value="#{raeOutputView.selectedRaeOutputHeader.article_no}" maxlength="20" disabled="#{raeOutputView.canModifyRae == false}">
								<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
							</p:inputText>
					</div>
				</h:panelGroup>
				<h:panelGroup id="panel_vol" rendered="#{raeOutputView.showVol() eq true}">
						<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
								Volume (Issue)
								<br/>
								<span style="color:#0277BD">(e.g. 27(4), 第24期)</span><span style="color:#e56507"> (Please input N/A if not applicable.)</span>
						</div>
						<div class="ui-g-12 ui-md-9 ui-lg-3">
								<p:message for="vol_issue"/>
								<p:inputText id="vol_issue" label="Volume (Issue)" title="Volume (Issue)" value="#{raeOutputView.selectedRaeOutputHeader.vol_issue}" maxlength="30"
												disabled="#{raeOutputView.canModifyRae == false}">
									<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
								</p:inputText>		
						</div>
				</h:panelGroup>
				<h:panelGroup id="panel_page" rendered="#{raeOutputView.showPageNum() eq true}">
						<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
								Page No.
								<br/>
								<span style="color:#0277BD">(e.g. 1-10)</span>
						</div>
						<div class="ui-g-12 ui-md-9 ui-lg-3">
								<p:message for="page_num_from"/>
								<p:message for="page_num_to"/>
								<p:inputText id="page_num_from" label="Page No. - From" title="Page No. - From" value="#{raeOutputView.selectedRaeOutputHeader.page_num_from}" maxlength="20" style="width:30%" disabled="#{raeOutputView.canModifyRae == false}">
									<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
								</p:inputText> - 
								<p:inputText id="page_num_to" label="Page No. - To" title="Page No. - To" value="#{raeOutputView.selectedRaeOutputHeader.page_num_to}" maxlength="20" style="width:30%" disabled="#{raeOutputView.canModifyRae == false}">
									<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
								</p:inputText>		
						</div>					
				</h:panelGroup>
			</p:panel>
			</div>
			<div class="ui-g" style="background:#fcfdf0">
				<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
					<h:panelGroup id="title_date">
						Date <span style="color:#0277BD">(mm/yyyy)</span>
						<p:outputLabel class="raeForm-item-title" 
									value="(First published or made publicly available or effectively shared within the profession)" 
									style="color:#e56507; display:block"
									rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date ne 'D'}"/>
						<p:outputLabel class="raeForm-item-title" 
									value="(Official date of acceptance for publication)" 
									style="color:#e56507; display:block"
									rendered="#{raeOutputView.selectedRaeOutputHeader.published_census_date eq 'D'}"/>
					</h:panelGroup>	
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="from_month"/>
						<p:message for="from_year"/>
						<p:selectOneMenu id="from_month" title="From month" label="From month" value="#{raeOutputView.selectedRaeOutputHeader.from_month}" disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{raeOutputView.monthList}" var="o" 
												itemLabel="#{o}" itemValue="#{o}" />
							<p:ajax event="change" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
						</p:selectOneMenu>
						<p:selectOneMenu id="from_year" title="From year" label="From year" value="#{raeOutputView.selectedRaeOutputHeader.from_year}" disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{raeOutputView.yearList}" var="o" 
												itemLabel="#{o}" itemValue="#{o}" />
							<p:ajax event="change" listener="#{raeOutputView.genCitation()}" update="editForm:apa from_month"/>					
						</p:selectOneMenu>
				</div>
					
				<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
					<h:panelGroup id="panel_desc_loc_output">
						<p:outputLabel class="raeForm-item-title" rendered="#{raeOutputView.showDescLocOutput() eq true}"
										value="Description on Location of Output"/>
						<p:outputLabel class="raeForm-item-title" rendered="#{raeOutputView.showDescLocOutputRemarksE() eq true}"
									value=" (Please input accordingly.)" 
									style="color:#e56507;"/>
						<p:outputLabel class="raeForm-item-title" rendered="#{raeOutputView.showDescLocOutputRemarksF() eq true}"
									value=" (Please input Registration Number.)" 
									style="color:#e56507;"/>
						<p:message for="desc_loc_output"/>
						<p:inputText id="desc_loc_output" label="Description on Location of Output" title="Description on Location of Output" 
										value="#{raeOutputView.selectedRaeOutputHeader.desc_loc_output}" maxlength="1000"
										rendered="#{raeOutputView.showDescLocOutput() eq true}"
										disabled="#{raeOutputView.canModifyRae == false}">
						</p:inputText>	
						<h:panelGroup rendered="#{raeOutputView.showDescLocOutputRemarksE() eq true}">			
							<ol style="padding-left: 14px; color:#e56507; background:#f9f7b6;">
			                  <li>Proceedings published in book format 
			                  <br/><span class="example">e.g. Mathematics in Transport: Selected Proceedings of the 4th IMA International Conference on Mathematics in Transport: in Honour of Richard Allsop / Edited by Benjamin Heydecker. Oxford: Elsevier, 2016, p. 307-319</span><br/><br/>
			                  </li>
			                  <li>Proceedings published in journal 
			                  <br/><span class="example">e.g. Proceedings of the IEEE, v. 96, (1), 2016, Jan, p. 11-24</span><br/><br/>
			                  </li>
			                  <li>Symposium contribution that have not been formally published<br/>(Title of symposium (include the no. of meeting), location of meeting, date/month/year)
			                  <br/><span class="example">e.g. 5th Urban Research Symposium: Cities and Climate Change: Responding to an Urgent Agenda, Marseille, France, 28-30 Jun 2016</span>
			                  </li>
			                </ol> 
		                </h:panelGroup>
		              </h:panelGroup>
				</div>
		
				<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Publisher / Manufacturer (in English)
						<p:outputLabel class="raeForm-item-title" 
									value="(For non-English output, please input translated name in English with name in original language in (),e.g. Commercial Press (商務))" 
									style="color:#e56507; display:block;"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="publisher"/>
						<p:inputText id="publisher" label="Publisher / Manufacturer (in English)" title="Publisher / Manufacturer (in English)" style="width:99%" value="#{raeOutputView.selectedRaeOutputHeader.publisher}" maxlength="200" 
											disabled="#{raeOutputView.canModifyRae == false}">
							<p:ajax event="valueChange" listener="#{raeOutputView.genCitation()}" update="editForm:apa"/>
						</p:inputText>
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Funder(s) and Funding Programme(s) <span style="color:#0277BD">(e.g. Research Grants Council (General Research Fund))</span>
						<p:outputLabel class="raeForm-item-title" 
									value="(Please list the information with semi-colon (;) as the separator if there are multiple entries.  Please input N/A if not applicable.)" 
									style="color:#e56507; display:block;"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="funder"/>
						<p:inputText id="funder" label="Funder(s) and Funding Programme(s)" title="Funder(s) and Funding Programme(s)" style="width:99%" value="#{raeOutputView.selectedRaeOutputHeader.funder}" maxlength="500" 
											disabled="#{raeOutputView.canModifyRae == false}">
						</p:inputText>
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Name and Location of Collaborator(s)
						<p:outputLabel class="raeForm-item-title" 
									value="(Please list the information with semi-colon (;) as the separator if there are multiple entries.)" 
									style="color:#e56507; display:block;"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="colNameLoc"/>
						<p:inputText id="colNameLoc" label="Name and Location of Collaborator(s)" title="Name and Location of Collaborator(s)" style="width:99%" value="#{raeOutputView.selectedRaeOutputHeader.colNameLoc}" maxlength="500" 
											disabled="#{raeOutputView.canModifyRae == false}">
						</p:inputText>
				</div>
				
				<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
					International Research Collaboration<br/>
					<p:outputLabel class="raeForm-item-title" 
									value="(Please refer to the " 
									style="color:#e56507;" />
					<h:outputLink class="raeForm-item-title" style="text-decoration: underline;"
									value="../resources/pdf/RAE - International Research Collaboration.pdf" target="_blank">UGC’s document</h:outputLink>
					<p:outputLabel class="raeForm-item-title" 
									value=" for the relevant definition.)" 
									style="color:#e56507;" />
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-9">
					<p:message for="intColInd"/>
					<p:selectOneMenu id="intColInd" 
											title="International Research Collaboration"
											label="International Research Collaboration"
											 value="#{raeOutputView.selectedRaeOutputHeader.intColInd}" 
											 disabled="#{raeOutputView.canModifyRae == false}">
						<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
						<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
						<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
					</p:selectOneMenu>	
				</div>
			</div>
				
			<br/>
			<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
				<i class="fas fa-tag" style="margin-right:5px;"></i>Collaborative Contributor(s) <span style="color: #f2f3c0;">(please enter the collaborative name in chronological order to indicate the position in the research output)</span>
			</div>
			<hr style="margin:0"/>
			<p:messages for="columnTable"/>
			<p:dataTable id="columnTable" value="#{raeOutputView.selectedRaeOutputDetails}" var="col" widgetVar="columnTableWV" class="riFormTable"
								 rows="50" reflow="true" style="font-size:14px!important"
								 rowsPerPageTemplate="10,20,50,100,200"
			                     paginator="true"
			                     paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
			                     currentPageReportTemplate="Total Number of Contributor(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
								 rowIndexVar="rowIndex">
								 	 
					<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{raeOutputView.canModifyRae == true}">
						<f:facet name="header">Up</f:facet>
						<p:commandLink action="#{raeOutputView.moveColumnUp(rowIndex)}" rendered="#{rowIndex gt 0}"
									   update="columnTable" immediate="true"><i class="fas fa-caret-up icon-action" title="Move up"/>
						</p:commandLink>										
					</p:column>
					
					<p:column class="data-noprint" style="width:1em; text-align:left;" rendered="#{raeOutputView.canModifyRae == true}">
						<f:facet name="header">Dn.</f:facet>
						<p:commandLink action="#{raeOutputView.moveColumnDown(rowIndex)}" rendered="#{rowIndex lt raeOutputView.selectedRaeOutputDetails.size()-1}"
									   update="columnTable" immediate="true"><i class="fas fa-caret-down icon-action" title="Move down"/>
						</p:commandLink>										
					</p:column>
			
					<p:column style="text-align:left; width:2em;">
						<f:facet name="header">No.</f:facet>
						<ui:fragment rendered="#{raeOutputView.isRiCreator(col.authorship_staff_no)}"><i class="fa fa-star faa-pulse animated-hover" title="No." style="font-size:9px; color:#f06524; vertical-align: middle;"></i></ui:fragment> #{rowIndex +1}							
					</p:column>
					
					<p:column style="text-align:left; width:14em;">
						<f:facet name="header">#{formBundle['form.ri.creator.output']} Position in RI</f:facet>
						<p:message id="typeMsg" for="type"/>
						<p:selectOneMenu id="type" style="width:90%; white-space: pre-wrap;" value="#{col.non_ied_staff_flag}" disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="EdUHK Staff" itemValue="N"/>
							<f:selectItem itemLabel="Former Staff" itemValue="F"/>
							<f:selectItem itemLabel="Non EdUHK Staff" itemValue="Y"/>
							<f:selectItem itemLabel="EdUHK Student" itemValue="S"/>
							<p:ajax event="change" update="nameGroup cvGroup"/>
						</p:selectOneMenu>
					</p:column>		
					
					<p:column style="text-align:left; width:35em;">
						<f:facet name="header">#{formBundle['form.ri.creator.output']}</f:facet>
						<h:panelGroup id="nameGroup">
						<p:message for="select_staff_recipient_name"/>
						<p:selectOneMenu id="select_staff_recipient_name" label="#{formBundle['form.ri.creator.output']}" style="width:90%" value="#{col.authorship_staff_no}" filter="true" filterMatchMode="startsWith" rendered="#{col.non_ied_staff_flag eq 'N'}" disabled="#{raeOutputView.canModifyRae == false}" dynamic="true">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true"/>
							<f:selectItems value="#{manageRIView.staffList}"/>
							<p:ajax event="change" update="cvGroup"/>
						</p:selectOneMenu>
						
						<p:message for="select_staff_past_name"/>
						<p:autoComplete id="select_staff_past_name" value="#{col.authorship_name}" style="#{raeOutputView.isRaeAdmin == true?'width:70%':'width:90%'}"
										rendered="#{col.non_ied_staff_flag eq 'F'}"
										minQueryLength="3" 
										emptyMessage="No results"
										forceSelection="true"
										disabled="#{raeOutputView.canModifyRae == false}"
			                        	completeMethod="#{manageRIView.fsCompleteText}" scrollHeight="250">
			                        	<p:ajax event="itemSelect" process="@this" />
			                        	<p:ajax event="itemSelect" listener="#{raeOutputView.updateRowStaffNum(rowIndex)}" update="nameGroup"/>
			            </p:autoComplete>
			            <p:inputText id="select_staff_past_num" label="Staff No." style="#{raeOutputView.isRaeAdmin == true?'width:25%':'display:none'}" value="#{col.authorship_staff_no}" rendered="#{col.non_ied_staff_flag eq 'F'}" dynamic="true">
						</p:inputText>
						
						<p:message id="recipient_nameMsg" for="recipient_name"/>
						<p:inputText id="recipient_name" label="#{formBundle['form.ri.creator.output']}" style="width:90%" value="#{col.authorship_name}" 
											onkeypress="return (event.charCode != 59);"
											maxlength="80" rendered="#{col.non_ied_staff_flag ne 'N' &amp;&amp; col.non_ied_staff_flag ne 'F'}" disabled="#{raeOutputView.canModifyRae == false}">
							<p:ajax/>
						</p:inputText>
						</h:panelGroup>
					</p:column>			
					<p:column style="text-align:left; width:16em;">
						<f:facet name="header"><p:outputLabel id="authorshipTips" value="Authorship "/></f:facet>
						<p:selectOneMenu id="authorship" style="width:90%; white-space: pre-wrap;" value="#{col.authorship_type}" disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItems value="#{raeOutputView.authorshipList}" var="a"
											   itemLabel="#{a.description} " itemValue="#{a.pk.lookup_code}"/>
							<p:ajax event="change" update="columnTable"/>
						</p:selectOneMenu>
						<p:selectOneMenu id="subAuthorship" style="width:90%; white-space: pre-wrap;" value="#{col.authorship_dtl_type}" rendered="#{raeOutputView.requireSubAuthorship(col.authorship_type) == true}" disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItems value="#{raeOutputView.subAuthorshipList}" var="b" 
											   itemLabel="#{b.description} " itemValue="#{b.pk.lookup_code}"/>
							<p:ajax event="change"/>
						</p:selectOneMenu>
					</p:column>			
					<p:column class="data-noprint" style="width:3em;">
						<f:facet name="header">Profile</f:facet>
						<h:panelGroup id="cvGroup">
							<p:linkButton id="btn_view_cv" outcome="/web/person" rendered="#{col.non_ied_staff_flag eq 'N' &amp;&amp; raeOutputView.getStaffProfilePid(col.authorship_staff_no) ne null}" icon="pi pi-search" target="_blank">
				  					<f:param name="pid" value="#{raeOutputView.getStaffProfilePid(col.authorship_staff_no)}"/>
				  			</p:linkButton>
			  			</h:panelGroup>
					</p:column>	
					<p:column class="data-noprint" style="width:2em; text-align:left;" rendered="#{raeOutputView.canModifyRae == true}">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_delete" action="#{raeOutputView.deleteRow(rowIndex)}" 
									   update="columnTable" immediate="true"><i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
						</p:commandLink>	
					</p:column>									
				</p:dataTable>		
				<br/>
				<p:commandButton id="btn_add" icon="fas fa-plus" value="Add new contributor" 
														rendered="#{raeOutputView.canModifyRae == true}"
														 style="width:260px; margin-top:1px;"
														 action="#{raeOutputView.addRow()}"
														 update="columnTable"
														 immediate="true"
														 oncomplete="PF('columnTableWV').paginator.setPage(PF('columnTableWV').paginator.cfg.pageCount - 1);"/>
			</p:tab>
			<p:tab rendered="#{raeOutputView.getStaffPanel() eq '10'}">
				<f:facet name="title">
					<i class="fa fa-star" style="margin-right:5px;"></i>Information for Panel 10 <p:badge value="#{raeOutputView.countError('P10')}" severity="danger" class="ml-1" rendered="#{raeOutputView.countError('P10') gt 0}"></p:badge>
				</f:facet>
				<!-- Panel 10 -->
				<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
					<i class="fas fa-tag" style="margin-right:5px;"></i>Information for Panel 10
				</div>
				<hr style="margin:0"/>
				<div class="ui-g" style="background:#fcfdf0">	
					<div class="ui-g-12 ui-md-12 ui-lg-6 raeForm-item-title">
						Outputs that have not been subject to formal peer-review or refereeing processes
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-6">
						<p:message for="panel10RevInd"/>
						<p:selectOneMenu id="panel10RevInd" 
												title="Outputs that have not been subject to formal peer-review or refereeing processes"
												label="Outputs that have not been subject to formal peer-review or refereeing processes"
												 value="#{raeOutputView.selectedRaeOutputHeader.panel10RevInd}" 
												 disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="panel_panel10RevExplain"/> 
						</p:selectOneMenu>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						<h:panelGroup id="panel_panel10RevExplain" >
							<p:outputLabel value="A Note Explaining What Quality Review Has Been Carried Out on the Output" 
											class="raeForm-item-title"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10RevInd eq 'Y'}"/> 
							<p:message for="panel10RevExplain"/>
							<p:inputTextarea id="panel10RevExplain" 
											label="A Note Explaining What Quality Review Has Been Carried Out on the Output" 
											title="A Note Explaining What Quality Review Has Been Carried Out on the Output" 
											style="width: 99%;" rows="2" counter="panel10RevExplain_display" maxlength="2000" 
											value="#{raeOutputView.selectedRaeOutputHeader.panel10RevExplain}"
											disabled="#{raeOutputView.canModifyRae == false}"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10RevInd eq 'Y'}"
			                     			counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
				    		<h:outputText id="panel10RevExplain_display" class="p-d-block" style="font-size:12px; display:block;"/>
			    		</h:panelGroup>
					</div>
				</div>
				<hr style="margin:0px"/>
				<div class="ui-g" style="background:#fcfdf0">	
					<!-- Availability of evidence of post-publication review for research outputs in open repositories -->
					<div class="ui-g-12 ui-md-12 ui-lg-6">
						<p:outputLabel class="raeForm-item-title" value="Availability of evidence of post-publication review for research outputs in open repositories"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-6">
						<p:message for="panel10RoInd"/>
						<p:selectOneMenu id="panel10RoInd" 
												title="Availability of evidence of post-publication review for research outputs in open repositories"
												label="Availability of evidence of post-publication review for research outputs in open repositories"
												 value="#{raeOutputView.selectedRaeOutputHeader.panel10RoInd}" 
												 disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="panel_upload_p10"/> 
						</p:selectOneMenu>	
					</div>	
					
					<!-- Upload Files:  of Evidence of Post-publication Review for Research Output in Open Repository -->
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<h:panelGroup id="panel_upload_p10">
							<p:outputLabel id="p10_msg" value="#{raeOutputView.uploadErrMsgP10}" style="color:#fff; background:#e55555; display:block;"
												rendered="#{raeOutputView.selectedRaeOutputHeader.panel10RoInd eq 'Y'}"/>
							<p:fileUpload id="uploadedP10File" style="padding:2px;"
						                      mode="simple" skinSimple="true" auto="true"
						                      disabled="#{raeOutputView.canModifyRae == false}"
								  	 		  rendered="#{raeOutputView.selectedRaeOutputHeader.panel10RoInd eq 'Y'}"
						                      process="@this" 
						                      update="uploadedP10Table p10_msg"
						                      listener="#{raeOutputView.fileUploadListener_p10}">
							</p:fileUpload>					
							<h:outputText value=" (File Type: PDF, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_P10_EV')}MB)" 
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10RoInd eq 'Y'}"></h:outputText>  
						
							<p:messages for="uploadedP10Table"/>
							<p:dataTable id="uploadedP10Table"
										value="#{raeOutputView.uploadP10List}"
										var="file"
										reflow="true"
				                  	 	tableStyle="table-layout:auto;"
				                  	 	style="width:100%;"
				                  	 	rendered="#{raeOutputView.selectedRaeOutputHeader.panel10RoInd eq 'Y'}"
										>
				
								<p:column style="width:1em;">
						           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
										<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
						           	</p:commandLink>
						        </p:column>
				       
								<p:column headerText="File Name">
									<h:outputText value="#{file.file_display_name}" />
								</p:column>
								
								<p:column headerText="File Size (KB)" style="width:20%">
									<h:outputText value="#{file.fileKbSize}" >
										<f:convertNumber maxFractionDigits="0" />
									</h:outputText>
								</p:column>
								
								<p:column headerText="Upload Date" style="width:20%">
									<h:outputText value="#{file.creationDate}" >
										<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
									</h:outputText>
								</p:column>
								
								<p:column style="width:1em;" rendered="#{!readonly}">
						           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_p10')}"
												   icon="fa fa-trash"
												   title="Delete File"
												   update="uploadedP10Table editForm:formTab:p10_msg"
												   ><i class="fas fa-trash icon-action" title="Delete"/>
										<p:confirm header="Confirm deletion?" 
												   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
												   icon="pi pi-exclamation-triangle" />										
						           	</p:commandLink>
								</p:column>
							</p:dataTable>
						</h:panelGroup>
					</div>
				</div>
				<hr style="margin:0px"/>
				<div class="ui-g" style="background:#fcfdf0">	
					<div class="ui-g-12 ui-md-12 ui-lg-6 raeForm-item-title">
						Practice-based Output or Commissioned Research Output
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-6">
						<p:message for="panel10PtbInd"/>
						<p:selectOneMenu id="panel10PtbInd" 
												title="Practice-based Output or Commissioned Research Output"
												label="Practice-based Output or Commissioned Research Output"
												 value="#{raeOutputView.selectedRaeOutputHeader.panel10PtbInd}" 
												 disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="panel_panel10ExplainPtb"/> 
							<p:ajax event="change" oncomplete="triggerCountWords()"/>
						</p:selectOneMenu>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						<h:panelGroup id="panel_panel10ExplainPtb" >
							<p:outputLabel value="Short Text Note Explaining Practice-based Output or Commissioned Research Output (Max. 100 words)" 
											class="raeForm-item-title"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10PtbInd eq 'Y'}"/>
							<p:message for="panel10ExplainPtb"/>
							<p:inputTextarea id="panel10ExplainPtb" class="countWords"
											label="Short Text Note Explaining Practice-based Output or Commissioned Research Output" 
											title="Short Text Note Explaining Practice-based Output or Commissioned Research Output" 
											style="width: 99%;" rows="2" maxlength="2000" 
											value="#{raeOutputView.selectedRaeOutputHeader.panel10ExplainPtb}"
											disabled="#{raeOutputView.canModifyRae == false}"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10PtbInd eq 'Y'}"
											onkeyup="countWords('wordCount_panel10ExplainPtb', this,100)"
											onload="countWords('wordCount_panel10ExplainPtb', this,100)"
			                     			autoResize="false"/>
				    		<h:outputText id="wordCount_panel10ExplainPtb" class="p-d-block" style="font-size:12px; display:block;" rendered="#{raeOutputView.selectedRaeOutputHeader.panel10PtbInd eq 'Y'}"/>
			    		</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						<h:panelGroup id="panel_panel10Explanation" >
							<p:outputLabel value="Explanation for Conference Paper and Report (Max. 100 words)" 
											class="raeForm-item-title"
											rendered="#{raeOutputView.selectedRaeOutputHeader.output_type eq 'E'}"/>
							<p:message for="panel10Explanation"/>
							<p:inputTextarea id="panel10Explanation" class="countWords"
											label="Explanation for Conference Paper and Report" 
											title="Explanation for Conference Paper and Report" 
											style="width: 99%;" rows="2" maxlength="2000" 
											value="#{raeOutputView.selectedRaeOutputHeader.panel10Explanation}"
											disabled="#{raeOutputView.canModifyRae == false}"
											rendered="#{raeOutputView.selectedRaeOutputHeader.output_type eq 'E'}"
											onkeyup="countWords('wordCount_panel10Explanation', this,100)"
											onload="countWords('wordCount_panel10Explanation', this,100)"
			                     			autoResize="false"/>
				    		<h:outputText id="wordCount_panel10Explanation" class="p-d-block" style="font-size:12px; display:block;" rendered="#{raeOutputView.selectedRaeOutputHeader.output_type eq 'E'}"/>
			    		</h:panelGroup>
					</div>
				</div>	

				<!-- Published report -->
				<hr style="margin:0px"/>
				<div class="ui-g" style="background:#fcfdf0">	
					<div class="ui-g-12 ui-md-12 ui-lg-6 raeForm-item-title">
						Published Report
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-6">
						<p:message for="panel10PubInd"/>
						<p:selectOneMenu id="panel10PubInd" 
												title="Published Report"
												label="Published Report"
												 value="#{raeOutputView.selectedRaeOutputHeader.panel10PubInd}" 
												 disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="panel_panel10PubDesc"/> 
							<p:ajax event="change" oncomplete="triggerCountWords()"/>
						</p:selectOneMenu>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						<h:panelGroup id="panel_panel10PubDesc" >
							<p:outputLabel value="Explanation for published Report (Max. 100 words)" 
											class="raeForm-item-title"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10PubInd eq 'Y'}"/>
							<p:message for="panel10PubDesc"/>
							<p:inputTextarea id="panel10PubDesc" class="countWords"
											label="Explanation for published Report (Max. 100 words)" 
											title="Explanation for published Report (Max. 100 words)" 
											style="width: 99%;" rows="2" maxlength="2000" 
											value="#{raeOutputView.selectedRaeOutputHeader.panel10PubDesc}"
											disabled="#{raeOutputView.canModifyRae == false}"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel10PubInd eq 'Y'}"
											onkeyup="countWords('wordCount_panel10PubDesc', this,100)"
											onload="countWords('wordCount_panel10PubDesc', this,100)"
			                     			autoResize="false"/>
				    		<h:outputText id="wordCount_panel10PubDesc" class="p-d-block" style="font-size:12px; display:block;" rendered="#{raeOutputView.selectedRaeOutputHeader.panel10PubInd eq 'Y'}"/>
			    		</h:panelGroup>
					</div>
				</div>
				
				</p:tab>
				<!-- Panel 12 -->
				<p:tab rendered="#{raeOutputView.getStaffPanel() eq '12'}">
					<f:facet name="title">
						<i class="fa fa-star" style="margin-right:5px;"></i>Information for Panel 12 <p:badge value="#{raeOutputView.countError('P12')}" severity="danger" class="ml-1" rendered="#{raeOutputView.countError('P12') gt 0}"></p:badge>
					</f:facet>
					<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
						<i class="fas fa-tag" style="margin-right:5px;"></i>Information for Panel 12
					</div>
					<hr style="margin:0"/>
					<div class="ui-g" style="background:#fcfdf0">
						<p:outputLabel class="raeForm-item-title" value="URL on Publicly Accessible Internet Location for Body of Evidence"/>
						<p:message for="panel12UrlEvidence"/>
						<p:inputText id="panel12UrlEvidence" style="width:99%" 
									label="URL on Publicly Accessible Internet Location for Body of Evidence" 
									title="URL on Publicly Accessible Internet Location for Body of Evidence" 
									value="#{raeOutputView.selectedRaeOutputHeader.panel12UrlEvidence}"
									disabled="#{raeOutputView.canModifyRae == false}">
						</p:inputText>
					</div>

					<!-- Body of Evidence -->
					<div class="ui-g" style="background:#fcfdf0">
						<div class="ui-g-12 ui-md-12 ui-lg-12">
							<h:panelGroup id="panel_upload_p12">
								<p:outputLabel class="raeForm-item-title" value="Body of Evidence"/>
								<p:outputLabel id="p12_msg" value="#{raeOutputView.uploadErrMsgP12}" style="color:#fff; background:#e55555; display:block;"/>
								<p:fileUpload id="uploadedP12File" style="padding:2px;"
						                      mode="simple" skinSimple="true" auto="true"
						                      disabled="#{raeOutputView.canModifyRae == false}"
						                      process="@this" 
						                      update="uploadedP12Table p12_msg"
						                      listener="#{raeOutputView.fileUploadListener_p12}">
								</p:fileUpload>
								<h:outputText value=" (File Type: PDF, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_P12_EV')}MB)"></h:outputText>    
							
								<p:dataTable id="uploadedP12Table"
											value="#{raeOutputView.uploadP12List}"
											var="file"
											reflow="true"
					                  	 	tableStyle="table-layout:auto;"
					                  	 	style="width:100%;"
											>
					
									<p:column style="width:1em;">
							           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
											<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
							           	</p:commandLink>
							        </p:column>
					       
									<p:column headerText="File Name">
										<h:outputText value="#{file.file_display_name}" />
									</p:column>
									
									<p:column headerText="File Size (KB)" style="width:20%">
										<h:outputText value="#{file.fileKbSize}" >
											<f:convertNumber maxFractionDigits="0" />
										</h:outputText>
									</p:column>
									
									<p:column headerText="Upload Date" style="width:20%">
										<h:outputText value="#{file.creationDate}" >
											<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
										</h:outputText>
									</p:column>
									
									<p:column style="width:1em;" rendered="#{!readonly}">
							           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_p12')}"
													   icon="fa fa-trash"
													   title="Delete File"
													   update="uploadedP12Table editForm:formTab:p12_msg"
													   ><i class="fas fa-trash icon-action" title="Delete"/>
											<p:confirm header="Confirm deletion?" 
													   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
													   icon="pi pi-exclamation-triangle" />										
							           	</p:commandLink>
									</p:column>
									
								</p:dataTable>
						</h:panelGroup>
					</div>
				</div>
				<!-- Multi-component outputs -->
				<hr style="margin:0px"/>
				<div class="ui-g" style="background:#fcfdf0">	
					<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
						Multi-component Output
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="panel12MulInd"/>
						<p:selectOneMenu id="panel12MulInd" 
												title="Multi-component Output"
												label="Multi-component Output"
												 value="#{raeOutputView.selectedRaeOutputHeader.panel12MulInd}" 
												 disabled="#{raeOutputView.canModifyRae == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="panel_panel12MulUrl"/> 
							<p:ajax event="change" oncomplete="triggerCountWords()"/>
						</p:selectOneMenu>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
						<h:panelGroup id="panel_panel12MulUrl" >
							<p:outputLabel value="URL for the location of multi-component output" 
											class="raeForm-item-title"
											rendered="#{raeOutputView.selectedRaeOutputHeader.panel12MulInd eq 'Y'}"/>
							<p:message for="panel12MulUrl"/>
							<p:inputText id="panel12MulUrl" style="width:99%" 
										label="URL for the location of multi-component output" 
										title="URL for the location of multi-component output" 
										value="#{raeOutputView.selectedRaeOutputHeader.panel12MulUrl}"
										rendered="#{raeOutputView.selectedRaeOutputHeader.panel12MulInd eq 'Y'}"
										disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
			    		</h:panelGroup>
					</div>
				</div>
				</p:tab>
				<!-- Full Version Submission of Research Output -->
				<p:tab>
					<f:facet name="title">
						<i class="fa fa-paperclip" style="margin-right:5px;"></i>Full Version Submission of Research Output <p:badge value="#{raeOutputView.countError('full_ver_submit')}" severity="danger" class="ml-1" rendered="#{raeOutputView.countError('full_ver_submit') gt 0}"></p:badge>
					</f:facet>
					<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
						<i class="fas fa-tag" style="margin-right:5px;"></i>Full Version Submission of Research Output
					</div>
					<hr style="margin:0"/>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Format of Full Version Submission -->
						<div class="ui-g-12 ui-md-12 ui-lg-3 raeForm-item-title">
							Format of Full Version Submission
						</div>
						<div class="ui-g-12 ui-md-12 ui-lg-9">
							<p:message for="format_full_ver_submit"/>
							<p:selectOneMenu id="format_full_ver_submit" title="Format of Full Version Submission" label="Format of Full Version Submission" 
											value="#{raeOutputView.selectedRaeOutputHeader.format_full_ver_submit}" 
											disabled="#{raeOutputView.canModifyRae == false}">
									<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
									<f:selectItem itemLabel="1 - Electronic Format Only" itemValue="1"/>
									<f:selectItem itemLabel="2 - Physical Format Only" itemValue="2"/>
									<f:selectItem itemLabel="3 - Combined Mode (Partial) – Part in Electronic Format Plus Part in Physical Format" itemValue="3"/>
									<f:selectItem itemLabel="4 - Combined Mode (Whole) – Same Whole Unit in Both Electronic Format and Physical Format" itemValue="4"/>
									<p:ajax event="change" update="panel_phy_submit panel_ele_submit"/>
							</p:selectOneMenu>
						</div>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Physical Submission -->
						<p:panel id="panel_phy_submit" style="border:0px!important">
							<h:panelGroup rendered="#{raeOutputView.showPhySubmitPanel()}">
								<div class="ui-g-12 ui-md-12 ui-lg-12">
									<p:outputLabel class="form-sub-title" style="display:block; font-size:18px;" value="Physical Submission "/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#0277BD;" value="Please indicate the number of physical items for each medium type in the boxes below."/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#0277BD;" value="Meanwhile, you are required to prepare 4 physical copies (tentatively) for each medium type for submission to the UGC."/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#0277BD;" value="Exact number of physical copies required is subject to the UGC’s confirmation."/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#e56507;" value="Example"/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#e56507;" value="You wish to submit two DVDs, one is “Sleepy Baby (A)” and the other is “Sleepy Baby (B)”."/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#e56507;" value="Please select “2” for the quantity of “DVD”, and prepare 4 physical copies for each DVD."/>
								</div>
								<div class="ui-g-12 ui-md-12 ui-lg-12 raeForm-item-title">
									Medium Type (Quantity)
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									Audio tape
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3">
									<p:message for="phy_audio_qty"/>
									<p:selectOneMenu id="phy_audio_qty" title="Audio tape" label="Audio tape" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_audio_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									CD/CD-ROM
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3">
									<p:message for="phy_cd_qty"/>
									<p:selectOneMenu id="phy_cd_qty" title="CD/CD-ROM" label="CD/CD-ROM" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_cd_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									DVD
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3">
									<p:message for="phy_dvd_qty"/>
									<p:selectOneMenu id="phy_dvd_qty" title="DVD" label="DVD" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_dvd_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									Photographic record
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3">
									<p:message for="phy_photo_qty"/>
									<p:selectOneMenu id="phy_photo_qty" title="Photographic record" label="Photographic record" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_photo_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									Printed book
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3">
									<p:message for="phy_book_qty"/>
									<p:selectOneMenu id="phy_book_qty" title="Printed book" label="Printed book" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_book_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									USB flash drive
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-3">
									<p:message for="phy_usb_qty"/>
									<p:selectOneMenu id="phy_usb_qty" title="USB flash drive" label="USB flash drive" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_usb_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
								<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
									Other medium (please specify)
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-6">
									<p:message for="phy_other_type"/>
									<p:inputText id="phy_other_type" title="Other medium (please specify)" label="Other medium (please specify)" style="width:90%" 
													value="#{raeOutputView.selectedRaeOutputHeader.phy_other_type}"
													disabled="#{raeOutputView.canModifyRae == false}">
									</p:inputText>
								</div>
								<div class="ui-g-12 ui-md-3 ui-lg-3">
									<p:message for="phy_other_qty"/>
									<p:selectOneMenu id="phy_other_qty" title="Other medium" label="Other medium" 
													converter="javax.faces.Integer"
													value="#{raeOutputView.selectedRaeOutputHeader.phy_other_qty}" 
													disabled="#{raeOutputView.canModifyRae == false}">
											<f:selectItems value="#{raeOutputView.noOfPhyQtyList}"/>
									</p:selectOneMenu>
								</div>
							</h:panelGroup>
						</p:panel>
						<!-- Electronic Submission -->
						<p:panel id="panel_ele_submit" style="border:0px!important">
							<h:panelGroup rendered="#{raeOutputView.showEleSubmitPanel()}">
								<div class="ui-g-12 ui-md-12 ui-lg-12">
									<p:outputLabel class="form-sub-title" style="display:block; font-size:18px;" value="Electronic Submission "/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#0277BD;" value="If the research output file size exceeds 50Mb, please submit separately in a portable USB device in PHYSICAL format submission."/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#0277BD;" value="Each USB flash drive should contain the electronic copy of ONE research output only."/>
									<p:outputLabel class="raeForm-item-title" style="display:block; color:#0277BD;" value="For submissions involving recordings, images or photographs, the Panel would expect the contents are of good quality in at least MP3 standard audio and high definition 1280 × 720 video resolution for recordings and 300 dpi (dots per inch) for images/photographs respectively."/>
								</div>
								<div class="ui-g-12 ui-md-12 ui-lg-12">
									<h:panelGroup id="panel_upload_full_ver">
										<p:outputLabel id="full_ver_msg" value="#{raeOutputView.uploadErrMsgFullVer}" style="color:#fff; background:#e55555; display:block;"/>
										<p:fileUpload id="uploadedFullVerFile" style="padding:2px;"
								                      mode="simple" skinSimple="true" auto="true"
								                      disabled="#{raeOutputView.canModifyRae == false}"
								                      process="@this" 
								                      update="uploadedFullVerTable full_ver_msg"
								                      listener="#{raeOutputView.fileUploadListener_full_ver}">
										</p:fileUpload>
										<h:outputText value=" (File Type: #{sysParamView.getValue('RAE_FULL_VER_FILE_TYPES')}, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_FULL_VER_ENG')}MB)"></h:outputText>    
										
										<p:messages for="uploadedFullVerTable"/>
										<p:dataTable id="uploadedFullVerTable"
													value="#{raeOutputView.uploadFullVerList}"
													var="file"
													reflow="true"
							                  	 	tableStyle="table-layout:auto;"
							                  	 	style="width:100%;"
													>
							
											<p:column style="width:1em;">
									           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
													<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
									           	</p:commandLink>
									        </p:column>
							       
											<p:column headerText="File Name">
												<h:outputText value="#{file.file_display_name}" />
											</p:column>
											
											<p:column headerText="File Size (KB)" style="width:20%">
												<h:outputText value="#{file.fileKbSize}" >
													<f:convertNumber maxFractionDigits="0" />
												</h:outputText>
											</p:column>
											
											<p:column headerText="Upload Date" style="width:20%">
												<h:outputText value="#{file.creationDate}" >
													<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
												</h:outputText>
											</p:column>
											
											<p:column style="width:1em;" rendered="#{!readonly}">
									           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_full_ver')}"
															   icon="fa fa-trash"
															   title="Delete File"
															   update="uploadedFullVerTable editForm:formTab:full_ver_msg"
															   ><i class="fas fa-trash icon-action" title="Delete"/>
													<p:confirm header="Confirm deletion?" 
															   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
															   icon="pi pi-exclamation-triangle" />										
									           	</p:commandLink>
											</p:column>
											
										</p:dataTable>

								</h:panelGroup>
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
								URL to Open Access for Full Version of Research Output (Optional)
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-9">
								<p:message for="url_full_ver"/>
								<p:inputText id="url_full_ver" title="URL to Open Access for Full Version of Research Output" 
												label="URL to Open Access for Full Version of Research Output" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.url_full_ver}"
												update="editForm:apa"
												disabled="#{raeOutputView.canModifyRae == false}">
								</p:inputText>
							</div>
							</h:panelGroup>
						</p:panel>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- DOI -->
						<div class="ui-g-12 ui-md-3 ui-lg-3 raeForm-item-title">
								Digital Object Identifier (DOI)
								<p:outputLabel class="raeForm-item-title" style="display:block; color:#e56507;" value="(Input DOI number only (start with 10.))"/>
						</div>
						<div class="ui-g-12 ui-md-2 ui-lg-2 raeForm-item-title">
							<p:message for="ro_with_doi_ind"/>
							<p:selectOneMenu id="ro_with_doi_ind" 
													title="Digital Object Identifier (DOI)"
													label="Digital Object Identifier (DOI)"
													 value="#{raeOutputView.selectedRaeOutputHeader.ro_with_doi_ind}" 
													 disabled="#{raeOutputView.canModifyRae == false}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
								<p:ajax event="change" update="ans_doi"/> 
							</p:selectOneMenu>
						</div>
						<div class="ui-g-12 ui-md-12 ui-lg-7">
							<h:panelGroup id="ans_doi">
								<p:message for="doi"/>
								<p:inputText id="doi" title="Digital Object Identifier (DOI)" 
												label="Digital Object Identifier (DOI)" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.doi}"
												rendered="#{raeOutputView.selectedRaeOutputHeader.ro_with_doi_ind eq 'Y'}"
												disabled="#{raeOutputView.canModifyRae == false}">
								</p:inputText>
							</h:panelGroup>
						</div>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Presence of Abstract or Table of Content (TOC) -->
						<p:panel id="panel_toc" style="border:0px!important; width:100%">
							<h:panelGroup rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'N'}">
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									Presence of Abstract or Table of Content (TOC)
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-9">
									<p:message for="toc"/>
									<p:selectOneMenu id="toc" 
															title="Practice-based Output or Commissioned Research Output"
															label="Practice-based Output or Commissioned Research Output"
															 value="#{raeOutputView.selectedRaeOutputHeader.toc}" 
															 disabled="#{raeOutputView.canModifyRae == false}">
										<f:selectItem itemValue="#{null}" itemLabel="-- Please select --"/>
										<f:selectItem itemValue="A" itemLabel="A - Abstract provided"/>
										<f:selectItem itemValue="B" itemLabel="B - Table of Content (TOC) provided"/>	
										<f:selectItem itemValue="C" itemLabel="C - The output does not have a published or publicly made available abstract in English nor table of content (TOC) in English"/>	   
										<p:ajax event="change" update="panel_toc_att"/> 
									</p:selectOneMenu>	
								</div>
							</h:panelGroup>
						</p:panel>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Abstract or TOC -->
						<p:messages id="panel_upload_rae_toc_att_msg" rendered="#{raeOutputView.doSave eq false}"/>
						<p:panel id="panel_toc_att" style="border:0px!important; width:100%">
							<h:panelGroup id="panel_upload_toc_att" rendered="#{raeOutputView.showUploadTocAttPanel()}">
								<p:outputLabel id="toc_att_msg" value="#{raeOutputView.uploadErrMsgTocAtt}" style="color:#fff; background:#e55555; display:block;"/>
								<p:fileUpload id="uploadedTocAttFile" style="padding:2px;"
						                      mode="simple" skinSimple="true" auto="true"
						                      disabled="#{raeOutputView.canModifyRae == false}"
						                      process="@this" 
						                      update="uploadedTocAttTable toc_att_msg"
						                      listener="#{raeOutputView.fileUploadListener_toc_att}">
								</p:fileUpload>
								<h:outputText value=" (File Type: PDF, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_ABSTRACT')}MB)"></h:outputText>  
								<p:messages for="uploadedTocAttTable"/>
								<p:dataTable id="uploadedTocAttTable"
											value="#{raeOutputView.uploadTocAttList}"
											var="file"
											reflow="true"
					                  	 	tableStyle="table-layout:auto;"
					                  	 	style="width:100%;"
											>
									<p:column style="width:1em;">
							           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
											<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
							           	</p:commandLink>
							        </p:column>
					       
									<p:column headerText="File Name">
										<h:outputText value="#{file.file_display_name}" />
									</p:column>
									
									<p:column headerText="File Size (KB)" style="width:20%">
										<h:outputText value="#{file.fileKbSize}" >
											<f:convertNumber maxFractionDigits="0" />
										</h:outputText>
									</p:column>
									
									<p:column headerText="Upload Date" style="width:20%">
										<h:outputText value="#{file.creationDate}" >
											<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
										</h:outputText>
									</p:column>
									
									<p:column style="width:1em;" rendered="#{!readonly}">
							           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_toc_att')}"
													   icon="fa fa-trash"
													   title="Delete File"
													   update="uploadedTocAttTable editForm:formTab:panel_upload_rae_toc_att_msg"
													   ><i class="fas fa-trash icon-action" title="Delete"/>
											<p:confirm header="Confirm deletion?" 
													   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
													   icon="pi pi-exclamation-triangle" />										
							           	</p:commandLink>
									</p:column>
								</p:dataTable>
						</h:panelGroup>
						<h:panelGroup id="panel_url_toc" rendered="#{raeOutputView.selectedRaeOutputHeader.toc eq 'A' || raeOutputView.selectedRaeOutputHeader.toc eq 'B'}">
							<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
								URL to Open Access for Abstract or TOC
								<p:outputLabel class="raeForm-item-title"
												value=" (published or publicly made available abstract in English) (Optional)" 
												style="color:#e56507; display:block;"/>
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-9">
								<p:message for="url_toc"/>
								<p:inputText id="url_toc" title="URL to Open Access for Abstract or TOC" 
												label="URL to Open Access for Abstract or TOC" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.url_toc}"
												disabled="#{raeOutputView.canModifyRae == false}">
								</p:inputText>
							</div>
						</h:panelGroup>
						</p:panel>
					</div>
					
					<!-- TOC Other -->
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Presence of Abstract or Table of Content (TOC) -->
						<p:panel id="panel_toc_oth" style="border:0px!important; width:100%">
							<h:panelGroup rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'N'}">
								<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
									Presence of Abstract or Table of Content (TOC) in other widely-used language
								</div>
								<div class="ui-g-12 ui-md-6 ui-lg-9">
									<p:message for="toc_oth"/>
									<p:selectOneMenu id="toc_oth" 
															title="Practice-based Output or Commissioned Research Output in other widely-used language"
															label="Practice-based Output or Commissioned Research Output in other widely-used language"
															 value="#{raeOutputView.selectedRaeOutputHeader.toc_oth}" 
															 disabled="#{raeOutputView.canModifyRae == false}">
										<f:selectItem itemValue="#{null}" itemLabel="-- Please select --"/>
										<f:selectItem itemValue="Y" itemLabel="Yes"/>
										<f:selectItem itemValue="N" itemLabel="No"/>	
										<p:ajax event="change" update="panel_toc_att_oth"/> 
									</p:selectOneMenu>	
								</div>
							</h:panelGroup>
						</p:panel>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Abstract or TOC Other-->
						<p:messages id="panel_upload_rae_toc_att_oth_msg" rendered="#{raeOutputView.doSave eq false}"/>
						<p:panel id="panel_toc_att_oth" style="border:0px!important; width:100%">
							<h:panelGroup id="panel_upload_toc_att_oth" rendered="#{raeOutputView.showUploadTocAttOthPanel()}">
								<p:outputLabel id="toc_att_oth_msg" value="#{raeOutputView.uploadErrMsgTocAttOth}" style="color:#fff; background:#e55555; display:block;"/>
								<p:fileUpload id="uploadedTocAttOthFile" style="padding:2px;"
						                      mode="simple" skinSimple="true" auto="true"
						                      disabled="#{raeOutputView.canModifyRae == false}"
						                      process="@this" 
						                      update="uploadedTocAttOthTable toc_att_oth_msg"
						                      listener="#{raeOutputView.fileUploadListener_toc_att_oth}">
								</p:fileUpload>
								<h:outputText value=" (File Type: PDF, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_ABSTRACT')}MB)"></h:outputText>  
								<p:messages for="uploadedTocAttOthTable"/>
								<p:dataTable id="uploadedTocAttOthTable"
											value="#{raeOutputView.uploadTocAttOthList}"
											var="file"
											reflow="true"
					                  	 	tableStyle="table-layout:auto;"
					                  	 	style="width:100%;"
											>
									<p:column style="width:1em;">
							           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
											<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
							           	</p:commandLink>
							        </p:column>
					       
									<p:column headerText="File Name">
										<h:outputText value="#{file.file_display_name}" />
									</p:column>
									
									<p:column headerText="File Size (KB)" style="width:20%">
										<h:outputText value="#{file.fileKbSize}" >
											<f:convertNumber maxFractionDigits="0" />
										</h:outputText>
									</p:column>
									
									<p:column headerText="Upload Date" style="width:20%">
										<h:outputText value="#{file.creationDate}" >
											<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
										</h:outputText>
									</p:column>
									
									<p:column style="width:1em;" rendered="#{!readonly}">
							           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_toc_att_oth')}"
													   icon="fa fa-trash"
													   title="Delete File"
													   update="uploadedTocAttOthTable editForm:formTab:panel_upload_rae_toc_att_oth_msg"
													   ><i class="fas fa-trash icon-action" title="Delete"/>
											<p:confirm header="Confirm deletion?" 
													   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
													   icon="pi pi-exclamation-triangle" />										
							           	</p:commandLink>
									</p:column>
								</p:dataTable>
						</h:panelGroup>
						<h:panelGroup id="panel_url_toc_oth" rendered="#{raeOutputView.selectedRaeOutputHeader.toc_oth eq 'Y'}">
							<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
								URL to Open Access for Abstract or TOC in other widely-used language
								<p:outputLabel class="raeForm-item-title"
												value=" (published or publicly made available abstract in other widely-used language) (Optional)" 
												style="color:#e56507; display:block;"/>
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-9">
								<p:message for="url_toc_oth"/>
								<p:inputText id="url_toc_oth" title="URL to Open Access for Abstract or TOC in other widely-used language (Optional)" 
												label="URL to Open Access for Abstract or TOC in other widely-used language (Optional)" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.url_toc_oth}"
												disabled="#{raeOutputView.canModifyRae == false}">
								</p:inputText>
							</div>
						</h:panelGroup>
						</p:panel>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<p:panel id="panel_additional_info" style="border:0px!important; width:100%">
							<h:panelGroup rendered="#{raeOutputView.selectedRaeOutputHeader.non_trad_output_ind eq 'N'}">
								<div class="ui-g-12 ui-md-12 ui-lg-3 raeForm-item-title">
									Additional Information to Abstract on New Insights or Innovation Presented in the Output (Max. 100 words)
									<p:outputLabel class="raeForm-item-title"
														value="(Please input 'N/A' if the originally published or publicly made available abstract has indicated what new insights or innovation are presented in the research output.)" 
														style="color:#e56507; display:block;"/>
								</div>
								<div class="ui-g-12 ui-md-12 ui-lg-9">
									<p:message for="additional_info"/>
									<p:inputTextarea id="additional_info" style="width: 99%;" rows="3" maxlength="2000" class="countWords"
														label="Additional Information to Abstract on New Insights or Innovation Presented in the Output" 
														title="Additional Information to Abstract on New Insights or Innovation Presented in the Output" 
														value="#{raeOutputView.selectedRaeOutputHeader.additional_info}"
														disabled="#{raeOutputView.canModifyRae == false}"
														onkeyup="countWords('wordCount_additional_info', this, 100)"
														onload="countWords('wordCount_additional_info', this, 100)"
						                      			autoResize="false"/>
						            <br/>
						    		<h:outputText id="wordCount_additional_info" class="p-d-block" style="font-size:12px"/>
								</div>
							</h:panelGroup>
						</p:panel>
					</div>
				</p:tab>
				<!-- Other Information -->
				<p:tab>
					<f:facet name="title">
						<i class="fa fa-cubes" style="margin-right:5px;"></i>Other Information <p:badge value="#{raeOutputView.countError('other')}" severity="danger" class="ml-1" rendered="#{raeOutputView.countError('other') gt 0}"></p:badge>
					</f:facet>
					<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
						<i class="fas fa-tag" style="margin-right:5px;"></i>Keywords of Research Output (in English)
					</div>
					<hr style="margin:0"/>
					<div class="ui-g" style="background:#fcfdf0">
						<div class="ui-g-12 ui-md-6 ui-lg-2 raeForm-item-title">
							<p:outputLabel class="raeForm-item-title" value="Keyword 1"/>
							<p:outputLabel class="raeForm-item-title" value=" (Mandatory)" style="color:#e50907; "/>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<p:message for="keyword_output_1"/>
							<p:inputText id="keyword_output_1" title="Keyword 1" 
												label="Keyword 1" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.keyword_output_1}"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-2 raeForm-item-title">
							<p:outputLabel class="raeForm-item-title" value="Keyword 4"/>
							<p:outputLabel class="raeForm-item-title" value=" (Optional)" style="color:#04a75d; "/>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<p:message for="keyword_output_4"/>
							<p:inputText id="keyword_output_4" title="Keyword 4" 
												label="Keyword 4" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.keyword_output_4}"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-2 raeForm-item-title">
							<p:outputLabel class="raeForm-item-title" value="Keyword 2"/>
							<p:outputLabel class="raeForm-item-title" value=" (Mandatory)" style="color:#e50907; "/>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<p:message for="keyword_output_2"/>
							<p:inputText id="keyword_output_2" title="Keyword 2" 
												label="Keyword 2" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.keyword_output_2}"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-2 raeForm-item-title">
							<p:outputLabel class="raeForm-item-title" value="Keyword 5"/>
							<p:outputLabel class="raeForm-item-title" value=" (Optional)" style="color:#04a75d; "/>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<p:message for="keyword_output_5"/>
							<p:inputText id="keyword_output_5" title="Keyword 5" 
												label="Keyword 5" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.keyword_output_5}"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-2 raeForm-item-title">
							<p:outputLabel class="raeForm-item-title" value="Keyword 3"/>
							<p:outputLabel class="raeForm-item-title" value=" (Mandatory)" style="color:#e50907; "/>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<p:message for="keyword_output_3"/>
							<p:inputText id="keyword_output_3" title="Keyword 3" 
												label="Keyword 3" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.keyword_output_3}"
												disabled="#{raeOutputView.canModifyRae == false}">
							</p:inputText>
						</div>
						<div class="ui-g-12 ui-md-12 ui-lg-6 raeForm-item-title">
						</div>
					</div>
					<div class="ui-g" style="background:#fcfdf0">
						<!-- Additional Information for Single Coherent Work Published in Two or More Parts -->
						<p:panel id="panel_scw" style="border:0px!important; width:100%">
							<h:panelGroup id="panel_att_info_scw" rendered="#{raeOutputView.showAttInfoScw() eq true}">
								<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
									<i class="fas fa-tag" style="margin-right:5px;"></i>Additional Information for Single Coherent Work Published in Two or More Parts
								</div>
								<hr style="margin:0"/>
								<!-- Total No. of Part(s) of Single Coherent Work -->
								<div class="ui-g">
									<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
										<p:outputLabel class="raeForm-item-title" value="Total No. of Part(s) of Single Coherent Work"/>
										<p:outputLabel class="raeForm-item-title" value=" (including this RO)" style="color:#04a75d; "/>
									</div>
									<div class="ui-g-12 ui-md-6 ui-lg-9">
										<p:message for="no_sgl_co_work"/>
										<p:selectOneMenu id="no_sgl_co_work" title="Total No. of Part(s) of Single Coherent Work" label="Total No. of Part(s) of Single Coherent Work" 
														value="#{raeOutputView.selectedRaeOutputHeader.no_sgl_co_work}" 
														disabled="#{raeOutputView.canModifyRae == false}">
												<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
												<f:selectItems value="#{raeOutputView.noOfSglCoWorkList}"/>
										</p:selectOneMenu>
									</div>
								</div>
							</h:panelGroup>
							<!-- Other Part(s) of the Single Coherent Work -->
							<h:panelGroup id="panel_upload_scw" rendered="#{raeOutputView.showAttInfoScw() eq true}">
								<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
									<i class="fas fa-tag" style="margin-right:5px;"></i>Other Part(s) of the Single Coherent Work
								</div>
								<hr style="margin:0"/>
								<div class="ui-g">
								<div class="ui-g-12 ui-md-12 ui-lg-12">
									<p:outputLabel id="scw_msg" value="#{raeOutputView.uploadErrMsgScw}" style="color:#fff; background:#e55555; display:block;"/>
									<p:fileUpload id="uploadedScwFile" style="padding:2px;"
							                      mode="simple" skinSimple="true" auto="true"
							                      disabled="#{raeOutputView.canModifyRae == false}"
							                      process="@this" 
							                      update="uploadedScwTable scw_msg"
							                      listener="#{raeOutputView.fileUploadListener_scw}">
									</p:fileUpload>
									<h:outputText value=" (File Type: #{sysParamView.getValue('RAE_FULL_VER_FILE_TYPES')}, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_FULL_VER_ENG')}MB)"></h:outputText>  
									<p:messages for="uploadedScwTable"/>
									<p:dataTable id="uploadedScwTable"
												value="#{raeOutputView.uploadScwList}"
												var="file"
												reflow="true"
						                  	 	tableStyle="table-layout:auto;"
						                  	 	style="width:100%;"
												>
						
										<p:column style="width:1em;">
								           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
												<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
								           	</p:commandLink>
								        </p:column>
						       
										<p:column headerText="File Name">
											<h:outputText value="#{file.file_display_name}" />
										</p:column>
										
										<p:column headerText="File Size (KB)" style="width:20%">
											<h:outputText value="#{file.fileKbSize}" >
												<f:convertNumber maxFractionDigits="0" />
											</h:outputText>
										</p:column>
										
										<p:column headerText="Upload Date" style="width:20%">
											<h:outputText value="#{file.creationDate}" >
												<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
											</h:outputText>
										</p:column>
										
										<p:column style="width:1em;" rendered="#{!readonly}">
								           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_scw')}"
														   icon="fa fa-trash"
														   title="Delete File"
														   update="uploadedScwTable editForm:formTab:scw_msg"
														   ><i class="fas fa-trash icon-action" title="Delete"/>
												<p:confirm header="Confirm deletion?" 
														   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
														   icon="pi pi-exclamation-triangle" />										
								           	</p:commandLink>
										</p:column>
										
									</p:dataTable>
							</div>
							</div>
						</h:panelGroup>
						<h:panelGroup id="panel_scw_url_ref" rendered="#{raeOutputView.showAttInfoScw() eq true}">
							<div class="ui-g">
							<div class="ui-g-12 ui-md-6 ui-lg-3 raeForm-item-title">
								URL to Open Access for Other Part(s) of the Single Coherent Work (Optional)
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-9">
								<p:message for="url_ref"/>
								<p:inputText id="url_ref" title="URL to Open Access for Other Part(s) of the Single Coherent Work" 
												label="URL to Open Access for Other Part(s) of the Single Coherent Work" 
												style="width:100%" 
												value="#{raeOutputView.selectedRaeOutputHeader.url_ref}"
												disabled="#{raeOutputView.canModifyRae == false}">
								</p:inputText>
							</div>
							</div>
						</h:panelGroup>
						<!-- University Endorsement Form -->
						<h:panelGroup id="panel_scw_endorse" rendered="#{raeOutputView.showAttInfoScw() eq true}">
							<div class="form-sub-title" style="background: #656302; color:#fff; padding: 2px 6px 2px 6px;">
								<i class="fas fa-tag" style="margin-right:5px;"></i>University Endorsement Form
							</div>
							<hr style="margin:0"/>
							<div class="ui-g-12 ui-md-12 ui-lg-12">
								<p:message for="uni_endorse_conf_ind"/>
								<p:selectBooleanCheckbox id="uni_endorse_conf_ind" value="#{raeOutputView.isEndorsed}" 
														itemLabel="I hereby confirm that I have reviewed the following justification and supporting argument, and certify that the above research output constitutes a single body of work.  I also understand that if the Panel is not satisfied that the submitted parts form a single coherent work as submission of one research output, an unclassified grade may be used."/>
							</div>
							<div class="ui-g-12 ui-md-12 ui-lg-12">
								<p:outputLabel class="raeForm-item-title" value="Justification and Supporting Argument"/>
								<p:outputLabel class="raeForm-item-title"
												value=" (Please contact RDO in case of excess words)" 
												style="color:#e56507;"/>
							</div>
							<!-- Justification and Supporting Argument -->
							<div class="ui-g-12 ui-md-12 ui-lg-12">
								<p:message for="just_sgl_co_work"/>
								<p:inputTextarea id="just_sgl_co_work" class="countWords"
													label="Justification and Supporting Argument" 
													title="Justification and Supporting Argument" 
													style="width: 99%;" rows="4" maxlength="4000" 
													value="#{raeOutputView.selectedRaeOutputHeader.just_sgl_co_work}"
													disabled="#{raeOutputView.canModifyRae == false}"
													onkeyup="countWords('wordCount_just_sgl_co_work', this,600)"
													onload="countWords('wordCount_just_sgl_co_work', this,600)"
					                      			autoResize="false"/>
					            <br/>
					    		<h:outputText id="wordCount_just_sgl_co_work" class="p-d-block" style="font-size:12px"/>
							</div>
						</h:panelGroup>
						</p:panel>
					</div>	
					<!-- RAE or Lib admin only -->
					<div class="ui-g" style="background:#fcfdf0">
						<h:panelGroup id="panel_upload_add_info" rendered="#{raeOutputView.showAttInfoScw() eq true &amp;&amp; manageRIView.isRaeOrLibAdmin eq false}">
							<p:outputLabel id="add_info_msg" value="#{raeOutputView.uploadErrMsgAddInfo}" style="color:#fff; background:#e55555; display:block;"/>
							<p:fileUpload id="uploadedAddInfoFile" style="padding:2px;"
					                      mode="simple" skinSimple="true" auto="true"
					                      disabled="#{raeOutputView.canModifyRae == false}"
					                      process="@this" 
					                      update="uploadedAddInfoTable add_info_msg"
					                      listener="#{raeOutputView.fileUploadListener_add_info}">
							</p:fileUpload>
							<h:outputText value=" (File Type: PDF, Maximum File Size: #{sysParamView.getValue('RAE_UPLOAD_MAX_SIZE_ADD_INFO')}MB)"></h:outputText>
								<p:dataTable id="uploadedAddInfoTable"
											value="#{raeOutputView.uploadAddInfoList}"
											var="file"
											reflow="true"
					                  	 	tableStyle="table-layout:auto;"
					                  	 	style="width:100%;"
											>
					
									<p:column style="width:1em;">
							           	<p:commandLink ajax="false"><i class="fas fa-download icon-action" title="Download"/>
											<p:fileDownload value="#{raeOutputView.downloadFile(file)}" />		 
							           	</p:commandLink>
							        </p:column>
					       
									<p:column headerText="File Name">
										<h:outputText value="#{file.file_display_name}" />
									</p:column>
									
									<p:column headerText="File Size (KB)" style="width:20%">
										<h:outputText value="#{file.fileKbSize}" >
											<f:convertNumber maxFractionDigits="0" />
										</h:outputText>
									</p:column>
									
									<p:column headerText="Upload Date" style="width:20%">
										<h:outputText value="#{file.creationDate}" >
											<f:convertDateTime pattern="#{Constant.DEFAULT_DATE_TIME_FORMAT}" />
										</h:outputText>
									</p:column>
									
									<p:column style="width:1em;" rendered="#{!readonly}">
							           	<p:commandLink action="#{raeOutputView.deleteFile(file.file_id, 'rae_add_info')}"
													   icon="fa fa-trash"
													   title="Delete File"
													   update="uploadedAddInfoTable editForm:formTab:add_info_msg"
													   ><i class="fas fa-trash icon-action" title="Delete"/>
											<p:confirm header="Confirm deletion?" 
													   message="#{raeOutputView.formatMessage(bundle['msg.confirm.delete.x'], file.file_display_name)}"
													   icon="pi pi-exclamation-triangle" />										
							           	</p:commandLink>
									</p:column>
									
								</p:dataTable>
						</h:panelGroup>
					</div>	
				</p:tab>
			</p:tabView>
			
			</div>
		</div>
		

	<br/>
					
	<br/><br/>
	<h:panelGroup styleClass="button-panel">
		<p:linkButton value="#{bundle['action.back']} to RO list" outcome="manageRAEOutput" icon="pi pi-arrow-left" styleClass="btn-back" 
						rendered="#{raeOutputView.canBackRoList() eq true}">		
			<f:param name="pid" value="#{raeOutputView.paramPid}"/>
		</p:linkButton>

		<p:commandButton id="btn_sava" value="#{formBundle['form.save']}" 
								  rendered="#{raeOutputView.canModifyRae == true}"
								  style="margin-right:5px;"
								  update="@form messages"
						  		  action="#{raeOutputView.saveRaeOutputForm}"
						  		  oncomplete="window.scrollTo(0,0);">
		</p:commandButton>	
	


        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
        </p:confirmDialog>

	</h:panelGroup>
	<p:scrollTop target="parent" threshold="100" styleClass="custom-scrolltop" icon="pi pi-arrow-up" />
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>