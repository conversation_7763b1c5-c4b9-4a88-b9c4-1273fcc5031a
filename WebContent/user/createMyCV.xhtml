<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	
	<f:metadata>
		<!-- <f:viewParam name="pid" value="#{cvRGCTemplateView.paramPid}" /> -->
	</f:metadata>
	
	<f:event listener="#{cvDisplayView.checkValid}" type="preRenderView" />
	<ui:define name="html_head">
		<style>
			body .ui-chkbox:has(.ui-state-disabled) +label{
				font-weight:700; 
				color: #666666 !important;
				}
			.ui-state-disabled > label{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-selectonemenu-items {
			    display: inline !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
			.ui-datatable .publicationSelectAllChkbox .ui-chkbox-all{
			    display:none !important;
			}
			.selectSummary div table thead tr th {
				background-color:green !important;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent">
	<p:panel id="errorPanel" rendered="#{cvView.paramPid == ''}">
		<br/><h:outputText value = "The service is not available now. Please contact administrator if you have any inquiry." style = "color:red; font-weight: bold"/>
	</p:panel>
	
	<p:panel id="contentPanel" rendered="#{cvView.paramPid != ''}"> 
	<span class="admin-content-title"><i class="fas fa-user-edit"></i> Create My CV <br/></span>
	  <p:staticMessage severity="warn" summary="" detail="#{sysParamView.getValue('CREATE_MY_CV_NOTE')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;"/>

	<p:messages id="msgs" autoUpdate="true" closable="true"/>
	<h:form id="dataForm" target = "_blank">
	
		  <p:growl id="message" widgetVar = "message" showDetail="false" globalOnly = "true" life = "2000"/>
		  
          <p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Name</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
			     #{cvRGCTemplateView.sIdentity.fullname}
			     <p:spacer width="3"/>
			     #{cvRGCTemplateView.sIdentity.chinesename}
				</div>
			</div>
		</p:panel>
		<br/>	
		
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Email Address</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
			     #{cvRGCTemplateView.iUserInfo.email}
				</div>
			</div>
		</p:panel>
		<br/>	
		
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Academic Qualifications </span><span style="font-style: italic; font-size: small;"> (Source from “Personal Profile” in "Manage My Info.")</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
			     <h:outputText value="#{cvRGCTemplateView.sInfo.getProfile()}" escape = "false"/>
				</div>
			</div>
		</p:panel>
		<br/>
		
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Previous Academic Positions Held</span><span style="font-style: italic; font-size: small;"> (Source from “Career Overview” in "Manage My Info.")</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
			         <ui:repeat value="#{cvRGCTemplateView.employmentHistList_edit}" var="history">
			            <h:outputText value="#{history.company}"/><br/>
			        	<h:outputText value="#{history.job_title}"/><br/>
        				<h:outputText value="Date: #{cvRGCTemplateView.parseJobDate(history.from_date)} - #{cvRGCTemplateView.parseJobDate(history.to_date)}"/><br/>     				
        				<br/>
    				</ui:repeat>
				</div>
			</div>
		</p:panel>
		<br/>
		
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Present Academic Position</span><span style="font-style: italic; font-size: small;"> (Source from “Career Overview” in "Manage My Info.")</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<ui:repeat value="#{cvRGCTemplateView.currentJobInfoList}" var="currentJob">
			            <h:outputText value="#{currentJob.company}"/><br/>
			        	<h:outputText value="#{currentJob.job_title}"/><br/>
        				<h:outputText value="Date: #{cvRGCTemplateView.parseJobDate(currentJob.from_date)} - Present"/><br/>     				
        				<br/>
    				</ui:repeat>		     
				</div>
			</div>
		</p:panel>
		<br/>
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Research Interests</span><span style="font-style: italic; font-size: small;"> (Source from “Research Interests” in "Manage My Info.")</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
			     	<h:outputText value="#{cvRGCTemplateView.sInfo.research_interest}" escape = "false" />
				</div>
			</div>
		</p:panel>
		<br/>
		
	    <!--  Research Projects -->
		<p:panel id = "projSection">
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Relevant Research Grant Records</span><span style="font-style: italic; font-size: small;"> (Source from “Manage Projects")</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:dataTable id="projectTable" 
								 var="data" 
								 value="#{cvRGCTemplateView.projectList}" 
								 selection="#{cvRGCTemplateView.selectedProject}"
								 filteredValue = "#{cvRGCTemplateView.filteredProject}"
								 rowKey="#{data.pk.project_no}" 
								 rowSelectMode="checkbox" 
	                             rows="10"
	                             sortMode="single" 
	                             reflow="true" 
								 stripedRows="true"
	                             paginator = "true"
	                             currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
	                         	 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
	                         	 >
	                        
	                    <p:ajax event="rowSelectCheckbox" listener="#{cvRGCTemplateView.onProjectSelect}" update = ":dataForm"/>
	                    <p:ajax event="rowUnselectCheckbox" listener="#{cvRGCTemplateView.onProjectUnselect}" update = ":dataForm"/>	
	                    <p:ajax event="toggleSelect" listener="#{cvRGCTemplateView.selectAllProject}" update = ":dataForm"/>

	                    <p:column selectionMode="multiple" style="width:16px;text-align:center" />
	                    
						<p:column filterBy="#{data.projectHeader_p.title_1} #{data.projectHeader_p.title_2} #{data.projectHeader_p.title_3} #{data.projectHeader_p.title_4} #{data.investigator_type}"
						          filterMatchMode="contains"
								  sortBy="#{data.projectHeader_p.title_1} #{data.projectHeader_p.title_2} #{data.projectHeader_p.title_3} #{data.projectHeader_p.title_4} #{data.investigator_type}" 
								  style="width:40%; vertical-align: top;">
							<f:facet name="header">Project Title</f:facet>
			                <h:outputText value="#{data.projectHeader_p.title_1} #{data.projectHeader_p.title_2} #{data.projectHeader_p.title_3} #{data.projectHeader_p.title_4} (#{cvRGCTemplateView.convertRole(data.investigator_type)})"/>
			            </p:column> 
			              
			 			<p:column filterBy="#{data.projectHeader_p.from_year} #{data.projectHeader_p.from_month} #{data.projectHeader_p.to_year} #{data.projectHeader_p.to_month}"
			 					  filterMatchMode="contains"
			 					  sortBy="#{data.projectHeader_p.from_year}" 
			 					  style="width:12%; vertical-align: top;">
			 				<f:facet name="header">Period</f:facet>
			                <h:outputText value="#{cvRGCTemplateView.parseProjectDate(data, false)}" escape = "false"/>
			            </p:column>  
			                   
			            <p:column filterBy="#{cvRGCTemplateView.fundSourceMap.get(data.projectHeader_p.sap_funding_source)}" 
			            		  filterMatchMode = "contains"
			            		  sortBy="#{cvRGCTemplateView.fundSourceMap.get(data.projectHeader_p.sap_funding_source)}" 
			            		  style="width:30%; vertical-align: top;">
			            	<f:facet name="header">Funding Scheme</f:facet>
			                <h:outputText value="#{cvRGCTemplateView.fundSourceMap.get(data.projectHeader_p.sap_funding_source)}" />
			            </p:column>
			             
			            <p:column filterBy = "#{data.projectHeader_p.sap_grant_amt}"
			            		  filterMatchMode = "contains" 
			                      sortBy="#{data.projectHeader_p.sap_grant_amt}" style="width:30%; vertical-align: top;">
			                <f:facet name="header">Funding Amount (HKD)</f:facet>
			                <h:outputText value="#{cvRGCTemplateView.rephraseFundAmount(data.projectHeader_p.sap_grant_amt)}" rendered = "#{data.projectHeader_p.sap_grant_amt != null}"/>
			          		<!--  <h:outputText value="Not Applicable" rendered = "#{data.projectHeader_p.sap_grant_amt == null}"/>-->
			            </p:column> 
			                           
					</p:dataTable>
				</div>
			</div>
			<br/>
	
			<p:panel>
				<f:facet name="header" style = "background-color:green;">
					<div>
						<span style="color:#1f1645;">Selected Grant Records</span>
					</div>
				</f:facet>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12">
				
						<p:dataTable id="selectedProjTable" 
									 widgetVar = "selectedProjTable"
									 var="datarow" 
									 styleClass = "selectSummary"
									 value="#{cvRGCTemplateView.selectedProject}" ajax = "true"
									 sortMode="single" 
									 reflow="true" 
									 stripedRows="true"
									 rowKey="#{datarow.pk.project_no}" 
									 tableStyle="table-layout: fixed;"
									 emptyMessage = "You have not selected any projects."
									 rows="10"
	                             	 paginator = "true"
	                             	 currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
	                         	 	 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}">
		                             
							<p:column id="selectedProjTitle" headerText = "Project Title" 
									  sortBy="#{datarow.projectHeader_p.title_1} #{datarow.projectHeader_p.title_2} #{datarow.projectHeader_p.title_3} #{datarow.projectHeader_p.title_4} #{datarow.investigator_type}" 
									  style="width:40%; vertical-align: top;">
								
				                <h:outputText value="#{datarow.projectHeader_p.title_1} #{datarow.projectHeader_p.title_2} #{datarow.projectHeader_p.title_3} #{datarow.projectHeader_p.title_4} (#{cvRGCTemplateView.convertRole(datarow.investigator_type)})"/>
				            </p:column> 
				              
				 			<p:column headerText="Period"  id="selectedProjPeriod" 
				 			
				 				sortBy="#{datarow.projectHeader_p.from_year}" 
				 				style="width:12%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.parseProjectDate(datarow, false)}" escape = "false"/>
					        </p:column>   
					              
					        <p:column headerText="Funding Scheme" id="selectedProjFundSource" 
					        sortBy="#{cvRGCTemplateView.fundSourceMap.get(datarow.projectHeader_p.sap_funding_source)}" 
					        style="width:30%; vertical-align: top;">
					           <h:outputText value="#{cvRGCTemplateView.fundSourceMap.get(datarow.projectHeader_p.sap_funding_source)}" />
					        </p:column> 
					        
					        <p:column headerText="Funding Amount (HKD)" id="selectedProjFundAmount" sortBy="#{datarow.projectHeader_p.sap_grant_amt}" 
					        	style="width:30%; vertical-align: top;">
					            <h:outputText value="#{cvRGCTemplateView.rephraseFundAmount(datarow.projectHeader_p.sap_grant_amt)}" rendered = "#{datarow.projectHeader_p.sap_grant_amt != null}"/>
					         	<!-- <h:outputText value="Not Applicable" rendered = "#{datarow.projectHeader_p.released_val == null}"/> -->
					        </p:column>   
					                                    
						</p:dataTable>
					</div>
				</div>
			</p:panel>
		</p:panel>
		
		<br/>
		

		<!--  Research Output -->
		<p:panel id = "publicationSection">
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Publication Records</span>
					<span style="font-style: italic; font-size: small;"> (Source from “Manage Research Outputs")</span>
				</div>
			</f:facet>
			
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:commandButton value="Deselect ALL"
									 action="#{cvRGCTemplateView.resetPublicationList()}" update=":dataForm">
						<p:ajax event="click" oncomplete="PF('publicationTableWV').unselectAllRows()" update=":dataForm"/>
					</p:commandButton>
					<p:dataTable id="publicationTable" 
								widgetVar = "publicationTableWV"
								 var="data" 
								 value="#{cvRGCTemplateView.outputList}" 
								 sortMode="single" 
								 reflow="true" 
								 stripedRows="true"
								 selection="#{cvRGCTemplateView.selectedPublication}"
								 rowSelectMode="checkbox" 
								 filteredValue = "#{cvRGCTemplateView.filteredPublication}"
								 rowKey="#{data.pk.output_no}" 
	                             rows="10"
	                             paginator = "true"
	                             currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
	                         	 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}">
			                        
		                    <p:ajax event="rowSelectCheckbox" listener="#{cvRGCTemplateView.onPublicationSelect}" update = "contentPanel"/>
		                    <p:ajax event="rowUnselectCheckbox" listener="#{cvRGCTemplateView.onPublicationUnselect}" update = "contentPanel"/>  
		                    
		                    <p:column selectionMode="multiple" style="width:16px;text-align:center" styleClass="publicationSelectAllChkbox"/>
		                    	                    
							<p:column headerText="Citation" id="publicationATitle" filterBy="#{cvRGCTemplateView.removeCitationHtml(data)}" filterMatchMode="contains"
									  sortBy="#{cvRGCTemplateView.removeCitationHtml(data)}" 
									  style="width:70%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.removeCitationHtml(data)}" />
				            </p:column> 
				              
				 			 <p:column headerText="Output Type" id="publicationAType" filterBy="#{cvRGCTemplateView.outputTypeMap.get(data.outputHeader_p.sap_output_type)}" filterMatchMode="contains"
				 					  sortBy="#{data.outputHeader_p.sap_output_type}" style="width:20%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.outputTypeMap.get(data.outputHeader_p.sap_output_type)}" />
				            </p:column>  
				            
				            <p:column headerText="Publication Year" id="publicationAYear" filterBy="#{data.outputHeader_p.from_year}" filterMatchMode="contains"
				 					  sortBy="#{data.outputHeader_p.from_year}" style="width:10%; vertical-align: top;">
				                <h:outputText value="#{data.outputHeader_p.from_year}" />
				            </p:column>  
				            

				            
				            <!--  <p:column headerText="Authorship" id="authorship" sortBy="#{data.authorship_type}" style="width:20%; vertical-align: top;">
				            	<h:outputText value="#{cvRGCTemplateView.authorshipTypeMap.get(data.authorship_type)}" rendered = "#{data.authorship_type != null}"/>
				          		<h:outputText value="Not available" rendered = "#{data.authorship_type == null}"/>
				            </p:column> 
				             
				            <p:column headerText="Percentage of Contribution" id="percentage" sortBy="#{data.collab_percent}" style="width:15%; vertical-align: top;">
				            	<h:outputText value="#{data.collab_percent}%" rendered = "#{data.collab_percent != null}"/>
				          		<h:outputText value="Not available" rendered = "#{data.collab_percent == null}"/>
				            </p:column>-->
			                           
					</p:dataTable>
				</div>
			</div>		
			<br/>
			
			<p:panel id = "publicationASection">
				<f:facet name="header">
					<div>
						<span style="color:#1f1645;">Section A: Selected Publications in the recent five years</span>
					</div>
				</f:facet>
				<br/>

				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:dataTable id="selectedPublicationATable" 
									 styleClass = "selectSummary"
									 var="data" 
									 value="#{cvRGCTemplateView.selectedPublicationA}" 
									 sortMode="single" 
									 reflow="true" 
									 draggableRows="true"
									 stripedRows="true"
									 rowKey="#{data.pk.output_no}" 
									 tableStyle="table-layout: fixed;"
									 emptyMessage = "You have not selected any publications.">
									 
							<p:ajax event="rowReorder" listener="#{cvRGCTemplateView.onRowReorder('dataForm:selectedPublicationATable')}"/>
							
							<p:column headerText="Citation" id="publicationATitle" 
									  sortBy="#{cvRGCTemplateView.removeCitationHtml(data)}" 
									  style="width:70%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.removeCitationHtml(data)}"/>
				            </p:column> 
					              
				 			<p:column headerText="Output Type" id="publicationAType" 
				 					  sortBy="#{data.outputHeader_p.sap_output_type}" style="width:20%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.outputTypeMap.get(data.outputHeader_p.sap_output_type)}" />
				            </p:column>  
				            
				            <p:column headerText="Publication Year" id="publicationAYear" 
				 					  sortBy="#{data.outputHeader_p.from_year}" style="width:10%; vertical-align: top;">
				                <h:outputText value="#{data.outputHeader_p.from_year}" />
				            </p:column>  
				            
				            
				           <!-- <p:column headerText="Authorship" id="authorship" sortBy="#{data.authorship_type}" style="width:20%; vertical-align: top;">
				            	<h:outputText value="#{cvRGCTemplateView.authorshipTypeMap.get(data.authorship_type)}" rendered = "#{data.authorship_type != null}"/>
				          		<h:outputText value="Not available" rendered = "#{data.authorship_type == null}"/>
				            </p:column> 
				             
				            <p:column headerText="Percentage of Contribution" id="percentage" sortBy="#{data.collab_percent}" style="width:15%; vertical-align: top;">
				            	<h:outputText value="#{data.collab_percent}%" rendered = "#{data.collab_percent != null}"/>
				          		<h:outputText value="Not available" rendered = "#{data.collab_percent == null}"/>
				            </p:column> -->                       
						</p:dataTable>
					</div>
				</div>				
			</p:panel>
			<br/>

			
			<p:panel id = "publicationBSection">
				<f:facet name="header">
					<div>
						<span style="color:#1f1645;">Section B: Selected Publications beyond the recent five years</span>
					</div>
				</f:facet>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:dataTable id="selectedPublicationBTable" 
									 styleClass = "selectSummary"
									 var="data" 
									 value="#{cvRGCTemplateView.selectedPublicationB}" 
									 sortMode="single" 
									 draggableRows="true"
									 reflow="true" 
									 stripedRows="true"
									 rowKey="#{data.pk.output_no}" 
									 tableStyle="table-layout: fixed;"
									 emptyMessage = "You have not selected any publications."
		                             rows="5">
		                             
							<p:ajax event="rowReorder" listener="#{cvRGCTemplateView.onRowReorder('dataForm:selectedPublicationBTable')}"/>
							
							<p:column headerText="Citation" id="publicationATitle" 
									  sortBy="#{cvRGCTemplateView.removeCitationHtml(data)}" 
									  style="width:70%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.removeCitationHtml(data)}"/>
				            </p:column> 
				              
				 			<p:column headerText="Output Type" id="publicationAType" 
				 					  sortBy="#{data.outputHeader_p.sap_output_type}" style="width:20%; vertical-align: top;">
				                <h:outputText value="#{cvRGCTemplateView.outputTypeMap.get(data.outputHeader_p.sap_output_type)}" />
				            </p:column>  
				            
				            <p:column headerText="Publication Year" id="publicationAYear" 
				 					  sortBy="#{data.outputHeader_p.from_year}" style="width:10%; vertical-align: top;">
				                <h:outputText value="#{data.outputHeader_p.from_year}" />
				            </p:column>  
				            
				           <!-- <p:column headerText="Authorship" id="authorship" sortBy="#{data.authorship_type}" style="width:20%; vertical-align: top;">
				            	<h:outputText value="#{cvRGCTemplateView.authorshipTypeMap.get(data.authorship_type)}" rendered = "#{data.authorship_type != null}"/>
				          		<h:outputText value="Not available" rendered = "#{data.authorship_type == null}"/>
				            </p:column> 
				             
				            <p:column headerText="Percentage of Contribution" id="percentage" sortBy="#{data.collab_percent}" style="width:15%; vertical-align: top;">
				            	<h:outputText value="#{data.collab_percent}%" rendered = "#{data.collab_percent != null}"/>
				          		<h:outputText value="Not available" rendered = "#{data.collab_percent == null}"/>
				            </p:column>   -->                     
						</p:dataTable>
					</div>
				</div>
				<br/>			
			</p:panel>
		</p:panel>
		<br/>
		
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Research-related Prizes and Awards (e.g. National Awards)</span>
					<span style="font-style: italic; font-size: small;"> (Source from “Manage Prizes and Awards")</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">		
					<p:dataTable id="awardTable" 
								 var="data" 
								 value="#{cvRGCTemplateView.awardList}" 
								 sortMode="single" 
								 reflow="true" 
								 stripedRows="true"
								 selection="#{cvRGCTemplateView.selectedAward}"
								 rowSelectMode="checkbox" 
								 filteredValue="#{cvRGCTemplateView.filteredAward}"
								 rowKey="#{data.awardName}" 
								 tableStyle="table-layout: fixed;"
	                             rows="10"
	                             paginator = "true"
	                             currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
	                         	 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}">
	                         	 
	                         	 
			            <p:ajax event="rowSelectCheckbox" listener = "#{cvRGCTemplateView.onAwardSelect}" update = ":contentPanel"/>
			            <p:ajax event="rowUnselectCheckbox" listener = "#{cvRGCTemplateView.onAwardUnselect}" update = ":contentPanel"/>
			            <p:ajax event="toggleSelect" listener="#{cvRGCTemplateView.selectAllAward}" update = ":contentPanel" />	
	                    
	                    <p:column selectionMode="multiple" style="width:16px;text-align:center"/>
     
			                  	 
						<p:column headerText="Award Name" id="awardName" filterBy = "#{data.awardName}" filterMatchMode="contains" 
								  sortBy="#{data.awardName}" style="width:50%">
			                <h:outputText value="#{data.awardName}"/>
			            </p:column>   
			            
			            <p:column headerText="Org. Name" id="orgName" filterBy="#{data.orgName}" filterMatchMode="contains" 
			            		  sortBy="#{data.orgName}" style="width:30%">
			               <div class="left-align-column" ><h:outputText value="#{data.orgName}"/></div>
			            </p:column>
			               
			            <p:column headerText="Award Year" id="awardYear" filterBy="#{data.awardYear}" filterMatchMode="contains"  sortBy="#{data.awardYear}">
			                <div class="left-align-column"><h:outputText value="#{data.awardYear}"/></div>
			            </p:column>         
			 
					</p:dataTable>
					

				</div>
			</div>
			<br/>
			
			<p:panel>
				<f:facet name="header">
					<div>
						<span style="color:#1f1645;">Selected Prizes and Awards</span>
					</div>
				</f:facet>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:dataTable id="selectedAwardTable" 
						             styleClass = "selectSummary"
									 var="data" 
									 value="#{cvRGCTemplateView.selectedAward}" 
									 sortMode="single" 
									 reflow="true" 
									 draggableRows="true"
									 stripedRows="true"
									 rowKey="#{data.awardName}" 
									 tableStyle="table-layout: fixed;"
									 emptyMessage = "You have not selected any prizes and awards."
	                                 paginator = "true"
	                                 rows = "10"
	                                 currentPageReportTemplate="(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})"
	                             	 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}">
							
							<p:ajax event="rowReorder" listener="#{cvRGCTemplateView.onRowReorder('dataForm:selectedAwardTable')}"/>
							
							<p:column headerText="Award Name" id="awardName" sortBy="#{data.awardName}" style="width:50%">
				                <h:outputText value="#{data.awardName}"/>
				            </p:column>   
				            
				            <p:column headerText="Org. Name" id="orgName" sortBy="#{data.orgName}" style="width:30%">
				               <div class="left-align-column" ><h:outputText value="#{data.orgName}"/></div>
				            </p:column>
				               
				            <p:column headerText="Award Year" id="awardYear" sortBy="#{data.awardYear}">
				                <div class="left-align-column"><h:outputText value="#{data.awardYear}"/></div>
				            </p:column>                         
						</p:dataTable>
					</div>
				</div>
			</p:panel>
		</p:panel>
		<br/>
		
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Others (brief description of experience in consultancies, service as a referee in evaluating other grant applications, patents, PhD theses supervised)</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="7" counter="display" maxlength="4000"
											value="#{cvRGCTemplateView.other}" escape = "true"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
                      <br/>
      				  <h:outputText id="display" class="p-d-block" />
				</div>
			</div>
		</p:panel>
		<br/>	
			
		<p:linkButton outcome="exportMyData" value="Back" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		
		<p:commandButton id="btn_export_pdf" value="Export PDF File" icon="pi pi-download"
								  style="margin-right:5px;"
								  ajax = "false"
						  		  action="#{cvRGCTemplateView.exportCvInPdf()}"
						  		  oncomplete="window.scrollTo(0,0);">					  		  
		</p:commandButton>
		
		
		<p:commandButton id="btn_export_docx" value="Export Word File" icon="pi pi-download"
								  style="margin-right:5px;"
								  ajax = "false"
						  		  action="#{cvRGCTemplateView.exportCvInDocx()}"
						  		  oncomplete="window.scrollTo(0,0);">
		</p:commandButton>	
		
		<p:commandButton id="btn_save_option" value="Save" icon="pi pi-save"
								  style="margin-right:5px;"
								  update = ":dataForm"
								  ajax = "true"
								  action="#{cvRGCTemplateView.saveCurrentOption()}"	 
						  		  oncomplete="window.scrollTo(0,0)">
			<p:confirm header="Save CV" message="Please ensure that your CV complies with the requirements of the funding scheme you are applying." icon="pi pi-exclamation-triangle"/>
		</p:commandButton>
		
		<p:commandButton id="btn_clear_option" value="Clear" icon="pi pi-times"
								  style="margin-right:5px;background-color:rgba(200,0,0,1);"
								  update = ":dataForm"
								  ajax = "true"
								  action="#{cvRGCTemplateView.deleteSavedOption()}"	 
						  		  oncomplete="window.scrollTo(0,0)">
			<p:confirm header="Clear CV record" message="Do you want to clear the CV record?" icon="pi pi-exclamation-triangle"/>
		</p:commandButton>
		
		<p:confirmDialog global = "true" showEffect="fade" hideEffect="fade" responsive="true" width="500">
		     <p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		     <p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes" update = "contentPanel"/>           
        </p:confirmDialog>

			
		<p:panel styleClass = "no-border">
			<br/><h:outputText value = "*The format of the exported Word files may vary depending on the version of Word being used." style = "color:blue; font-weight: bold"/>
		</p:panel>
		
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>