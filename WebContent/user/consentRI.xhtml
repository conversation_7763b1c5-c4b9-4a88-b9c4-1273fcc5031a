<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata>
		<f:viewParam name="consent" value="#{manageOutputView.paramConsent}" />
		<f:viewParam name="consent" value="#{manageProjectView.paramConsent}" />
		<f:viewParam name="consent" value="#{manageAwardView.paramConsent}" />
		<f:viewParam name="consent" value="#{managePatentView.paramConsent}" />
		<f:viewParam name="pid" value="#{manageOutputView.paramPid}" />
		<f:viewParam name="pid" value="#{manageProjectView.paramPid}" />
		<f:viewParam name="pid" value="#{manageAwardView.paramPid}" />
		<f:viewParam name="pid" value="#{managePatentView.paramPid}" />
		<f:viewParam name="tabpage" value="#{manageRIView.paramTabpage}" />
	</f:metadata>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-check-circle"></i> Consent RI</span>
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.selectedSecFuncLock.lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageRIView.canModify == false}"/>
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:outputText value="You don't have access right." rendered="#{manageOutputView.canViewRIList() == false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
	<h:form id="dataForm" rendered="#{manageOutputView.canViewRIList() == true}">
		<p:tabView id="formTab" effect="fade" effectDuration="slow" dynamic="true" activeIndex="#{manageRIView.paramTabpage}">
		
			<!-- Research Outputs -->
	        <p:tab id="output">
	        	<f:facet name="title">
	               <h:outputText value="Research Outputs"/>
	               <p:badge value="#{manageOutputView.getTotalCount('')}" severity="danger">
				         <i class="pi pi-filter p-ml-2" style="font-size: 20px"/>
				     </p:badge>
	            </f:facet>

	        	<p:dataTable id="outputDataTable" var="data" value="#{manageOutputView.outputList}" sortBy="#{data.outputHeader_p.output_lookup_code}" stripedRows="true"
								tableStyle="table-layout: fixed;">
					<p:headerRow field="outputHeader_p.output_lookup_code" expandable="true">
						<p:column colspan="3" style="background:#c1def5; color: #186ba0;">
							<div style="vertical-align: middle; font-size:16px; display:contents;">
		                	<h:outputText value="#{manageOutputView.getSelectedOutputCatDesc(data.outputHeader_p.output_lookup_code)}"/>
		                	</div>
		                </p:column>
		            </p:headerRow>   
					<p:column headerText="Research Output" id="output" style="width:70%;">
		                <p:outputLabel value="#{data.outputHeader_p.apa_html}" style="#{data.outputDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}" escape="false"></p:outputLabel>
		            </p:column>   
		 			<p:column headerText="Status" id="status" style="text-align: center;">
		                <h:outputText style="font-weight:700; color:#1B5E20;" value="Published" rendered="#{data.outputDetails_q.creator_ind == 'Y' &amp;&amp; data.outputHeader_q.publish_status == 'PUBLISHED' &amp;&amp; data.outputDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#D50000;" value="Modified" rendered="#{data.outputDetails_q.creator_ind == 'Y' &amp;&amp; data.outputHeader_q.publish_status == 'MODIFIED' &amp;&amp; data.outputDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.outputDetails_q.creator_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind != 'U' &amp;&amp; data.outputDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.outputDetails_q.creator_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind == 'U'}"/>
		                 <h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.outputDetails_q.display_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind ne 'U'}"/>
		            </p:column>    
		  			<p:column headerText="Action" id="action" style="text-align: center;">
		  				<p:linkButton id="btn_modify" outcome="manageOutput_edit" rendered="#{data.outputDetails_q.creator_ind == 'Y' &amp;&amp; manageRIView.canModify}" value="Modify" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
		  					<f:param name="no" value="#{data.pk.output_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_view" outcome="manageOutput_edit" rendered="#{data.outputDetails_q.creator_ind == 'N' || manageRIView.canModify == false}" value="View" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
		  					<f:param name="no" value="#{data.pk.output_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_consent" outcome="manageOutput_edit" rendered="#{data.outputDetails_q.creator_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind == 'U' &amp;&amp; manageRIView.canModify}" value="Consent" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
		  					<f:param name="no" value="#{data.pk.output_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		            </p:column>                   
				</p:dataTable>
	        </p:tab>
	        <!-- Projects -->
	        <p:tab id="project">
	        	<f:facet name="title">
	               <h:outputText value="Projects"/>
	               <p:badge value="#{manageProjectView.getTotalCount()}" severity="success">
				         <i class="pi pi-filter p-ml-2" style="font-size: 20px"/>
				     </p:badge>
	            </f:facet>
	        	<p:dataTable id="projDataTable" var="data" value="#{manageProjectView.projectList}" sortMode="single" reflow="true" stripedRows="true"
						rowKey="#{data.pk}" tableStyle="table-layout: fixed;">
					<p:column headerText="Title" id="projTitle" sortBy="#{data.projectHeader_p.title_1}" style="width:25%">
		                <h:outputText value="#{data.projectHeader_p.title_1} #{data.projectHeader_p.title_2} #{data.projectHeader_p.title_3} #{data.projectHeader_p.title_4}" style="#{data.projectDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}"/>
		            </p:column>   
		 			<p:column headerText="Summary" id="summary" sortBy="#{data.projectHeader_p.project_summary}" style="width:30%">
		                <h:outputText value="#{data.projectHeader_p.project_summary}" style="#{data.projectDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}"/>
		            </p:column>  
		            <p:column headerText="Start Year" id="year" sortBy="#{data.projectHeader_p.from_year}">
		                <div class="left-align-column" ><h:outputText value="#{data.projectHeader_p.from_year}" style="#{data.projectDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}"/></div>
		            </p:column>  
		            <p:column headerText="Status" id="status" sortBy="#{data.projectHeader_q.publish_status}">
		            	<div class="left-align-column" >
		                <h:outputText style="font-weight:700; color:#1B5E20;" value="Published" rendered="#{data.projectDetails_q.creator_ind == 'Y' &amp;&amp; data.projectHeader_q.publish_status == 'PUBLISHED' &amp;&amp; data.projectDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#D50000;" value="Modified" rendered="#{data.projectDetails_q.creator_ind == 'Y' &amp;&amp; data.projectHeader_q.publish_status == 'MODIFIED' &amp;&amp; data.projectDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.projectDetails_q.creator_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind != 'U' &amp;&amp; data.projectDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.projectDetails_q.creator_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind == 'U'}"/>
		            	<h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.projectDetails_q.display_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind ne 'U'}"/>
		            	</div>
		            </p:column>         
		  			<p:column headerText="Action" id="action">
		  				<div class="left-align-column" >
		  				<p:linkButton id="btn_modify" outcome="manageProject_edit" rendered="#{data.projectDetails_q.creator_ind == 'Y' &amp;&amp; manageRIView.canModify}" value="Modify" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
		  					<f:param name="no" value="#{data.pk.project_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_view" outcome="manageProject_edit" rendered="#{data.projectDetails_q.creator_ind == 'N' || manageRIView.canModify == false}" value="View" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
		  					<f:param name="no" value="#{data.pk.project_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_consent" outcome="manageProject_edit" rendered="#{data.projectDetails_q.creator_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind == 'U' &amp;&amp; manageRIView.canModify}" value="Consent" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
		  					<f:param name="no" value="#{data.pk.project_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				</div>
		            </p:column>                              
				</p:dataTable>
	        </p:tab>	  
	        <!-- Prizes and Awards -->
	        <p:tab id="award">
	        	<f:facet name="title">
	               <h:outputText value="Prizes and Awards"/>
	               <p:badge value="#{manageAwardView.getTotalCount()}" severity="info">
				         <i class="pi pi-filter p-ml-2" style="font-size: 20px"/>
				     </p:badge>
	            </f:facet>
	        	<p:dataTable id="awardDataTable" var="data" value="#{manageAwardView.awardList}" sortMode="single" reflow="true" stripedRows="true"
								rowKey="#{data.pk}" tableStyle="table-layout: fixed;">
					<p:column headerText="Award Name" id="awardName" sortBy="#{data.awardHeader_p.award_name}" style="width:30%">
		                <h:outputText value="#{data.awardHeader_p.award_name}" style="#{data.awardDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0; font-weight:700;'}"/>
		            </p:column>   
		            <p:column headerText="Org. Name" id="orgName" sortBy="#{data.awardHeader_p.org_name}" style="width:20%">
		               <div class="left-align-column" ><h:outputText value="#{data.awardHeader_p.org_name}" style="#{data.awardDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0; font-weight:700;'}"/></div>
		            </p:column>   
		            <p:column headerText="Award Year" id="awardYear" sortBy="#{data.awardHeader_p.award_year}">
		                <div class="left-align-column"><h:outputText value="#{data.awardHeader_p.award_year}" style="#{data.awardDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0; font-weight:700;'}"/></div>
		            </p:column>         
		           <p:column headerText="Status" id="status" sortBy="#{data.awardHeader_q.publish_status}">
		                <h:outputText style="font-weight:700; color:#1B5E20;" value="Published" rendered="#{data.awardDetails_q.creator_ind == 'Y' &amp;&amp; data.awardHeader_q.publish_status == 'PUBLISHED' &amp;&amp; data.awardDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#D50000;" value="Modified" rendered="#{data.awardDetails_q.creator_ind == 'Y' &amp;&amp; data.awardHeader_q.publish_status == 'MODIFIED' &amp;&amp; data.awardDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.awardDetails_q.creator_ind == 'N' &amp;&amp; data.awardDetails_q.consent_ind != 'U' &amp;&amp; data.awardDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.awardDetails_q.creator_ind == 'N' &amp;&amp; data.awardDetails_q.consent_ind == 'U'}"/>
		            	 <h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.awardDetails_q.display_ind == 'N' &amp;&amp; data.awardDetails_q.consent_ind ne 'U'}"/>
		            </p:column>  
		  			<p:column headerText="Action" id="action">
		  				<p:linkButton id="btn_modify" outcome="manageAward_edit" rendered="#{data.awardDetails_q.creator_ind == 'Y' &amp;&amp; manageRIView.canModify}" value="Modify" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageAwardView.paramPid}"/>
		  					<f:param name="no" value="#{data.pk.award_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_view" outcome="manageAward_edit" rendered="#{data.awardDetails_q.creator_ind == 'N' || manageRIView.canModify == false}" value="View" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageAwardView.paramPid}"/>
		  					<f:param name="no" value="#{data.pk.award_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_consent" outcome="manageAward_edit" rendered="#{data.awardDetails_q.creator_ind == 'N' &amp;&amp; data.awardDetails_q.consent_ind == 'U' &amp;&amp; manageRIView.canModify}" value="Consent" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{manageAwardView.paramPid}"/>
		  					<f:param name="no" value="#{data.pk.award_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		            </p:column>                     
				</p:dataTable>
	        </p:tab>
	        <!-- Patents -->
	        <p:tab id="patent">
	        	<f:facet name="title">
	               <h:outputText value="Patents"/>
	               <p:badge value="#{managePatentView.getTotalCount()}" severity="warning">
				         <i class="pi pi-filter p-ml-2" style="font-size: 20px"/>
				     </p:badge>
	            </f:facet>
	        	<p:dataTable id="patentDataTable" var="data" value="#{managePatentView.patentList}" sortMode="multiple" reflow="true" stripedRows="true"
								  rowKey="#{data.pk}" tableStyle="table-layout: fixed;">
					<p:column headerText="Patent Name" id="patentName" sortBy="#{data.patentHeader_p.patent_name}" style="width:30%">
		                <h:outputText value="#{data.patentHeader_p.patent_name}" style="#{data.patentDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0; font-weight:700;'}"/>
		            </p:column>   
		            <p:column headerText="Patent Year" id="patentDate" sortBy="#{data.patentHeader_p.patent_year}" sortOrder="desc" sortPriority="1">
		                <div class="left-align-column" ><h:outputText value="#{data.patentHeader_p.patent_year}" style="#{data.patentDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0; font-weight:700;'}"/></div>
		            </p:column> 
		            <p:column headerText="Status" id="status" sortBy="#{data.patentHeader_q.publish_status}">
		            	<div class="left-align-column" >
		                <h:outputText style="font-weight:700; color:#1B5E20;" value="Published" rendered="#{data.patentDetails_q.creator_ind == 'Y' &amp;&amp; data.patentHeader_q.publish_status == 'PUBLISHED' &amp;&amp; data.patentDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#D50000;" value="Modified" rendered="#{data.patentDetails_q.creator_ind == 'Y' &amp;&amp; data.patentHeader_q.publish_status == 'MODIFIED' &amp;&amp; data.patentDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.patentDetails_q.creator_ind == 'N' &amp;&amp; data.patentDetails_q.consent_ind != 'U' &amp;&amp; data.patentDetails_q.display_ind == 'Y'}"/>
		                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.patentDetails_q.creator_ind == 'N' &amp;&amp; data.patentDetails_q.consent_ind == 'U'}"/>
		            	<h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.patentDetails_q.display_ind == 'N' &amp;&amp; data.patentDetails_q.consent_ind ne 'U'}"/>
		            	</div>
		            </p:column>         
		  			<p:column headerText="Action" id="action">
		  				<div class="left-align-column" >
		  				<p:linkButton id="btn_modify" outcome="managePatent_edit" rendered="#{data.patentDetails_q.creator_ind == 'Y' &amp;&amp; manageRIView.canModify}" value="Modify" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{managePatentView.getParamPid()}"/>
		  					<f:param name="no" value="#{data.pk.patent_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_view" outcome="managePatent_edit" rendered="#{data.patentDetails_q.creator_ind == 'N' || manageRIView.canModify == false}" value="View" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{managePatentView.getParamPid()}"/>
		  					<f:param name="no" value="#{data.pk.patent_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				<p:linkButton id="btn_consent" outcome="managePatent_edit" rendered="#{data.patentDetails_q.creator_ind == 'N' &amp;&amp; data.patentDetails_q.consent_ind == 'U' &amp;&amp; manageRIView.canModify}" value="Consent" style="margin-right:5px; margin-bottom:5px;">
		  					<f:param name="pid" value="#{managePatentView.getParamPid()}"/>
		  					<f:param name="no" value="#{data.pk.patent_no}"/>
		  					<f:param name="dataLevel" value="M"/>
		  					<f:param name="consent" value="U"/>
		  				</p:linkButton>	
		  				</div>
		            </p:column>                       
				</p:dataTable>
	        </p:tab>	     	              
		</p:tabView>
	</h:form>
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>
	</p:panel>
   </ui:define>
</ui:composition>