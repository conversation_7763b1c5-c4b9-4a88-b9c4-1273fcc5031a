<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=big5">
<meta name=ProgId content=Word.Document>
<meta name=Generator content="Microsoft Word 15">
<meta name=Originator content="Microsoft Word 15">
<link rel=File-List href="Attachment%20III_2024-25_files/filelist.xml">
<!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>HKIEd</o:Author>
  <o:LastAuthor>EdUHK</o:LastAuthor>
  <o:Revision>2</o:Revision>
  <o:TotalTime>449</o:TotalTime>
  <o:LastPrinted>2024-04-03T06:42:00Z</o:LastPrinted>
  <o:Created>2025-05-14T04:16:00Z</o:Created>
  <o:LastSaved>2025-05-14T04:16:00Z</o:LastSaved>
  <o:Pages>3</o:Pages>
  <o:Words>471</o:Words>
  <o:Characters>2691</o:Characters>
  <o:Lines>22</o:Lines>
  <o:Paragraphs>6</o:Paragraphs>
  <o:CharactersWithSpaces>3156</o:CharactersWithSpaces>
  <o:Version>16.00</o:Version>
 </o:DocumentProperties>
</xml><![endif]-->
<link rel=dataStoreItem href="Attachment%20III_2024-25_files/item0001.xml"
target="Attachment%20III_2024-25_files/props002.xml">
<link rel=themeData href="Attachment%20III_2024-25_files/themedata.thmx">
<link rel=colorSchemeMapping
href="Attachment%20III_2024-25_files/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:SpellingState>Clean</w:SpellingState>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves/>
  <w:TrackFormatting/>
  <w:PunctuationKerning/>
  <w:DrawingGridHorizontalSpacing>6 pt</w:DrawingGridHorizontalSpacing>
  <w:DrawingGridVerticalSpacing>6 pt</w:DrawingGridVerticalSpacing>
  <w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery>
  <w:DisplayVerticalDrawingGridEvery>3</w:DisplayVerticalDrawingGridEvery>
  <w:ValidateAgainstSchemas>false</w:ValidateAgainstSchemas>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotUnderlineInvalidXML/>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>EN-US</w:LidThemeOther>
  <w:LidThemeAsian>ZH-TW</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:DoNotShadeFormData/>
  <w:Compatibility>
   <w:SpaceForUL/>
   <w:BalanceSingleByteDoubleByteWidth/>
   <w:DoNotLeaveBackslashAlone/>
   <w:ULTrailSpace/>
   <w:DoNotExpandShiftReturn/>
   <w:AdjustLineHeightInTable/>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:DontGrowAutofit/>
   <w:SplitPgBreakAndParaMark/>
   <w:EnableOpenTypeKerning/>
   <w:DontFlipMirrorIndents/>
   <w:OverrideTableStyleHps/>
   <w:UseFELayout/>
  </w:Compatibility>
  <w:DocumentVariables>
   <w:__Grammarly_42____i>H4sIAAAAAAAEAKtWckksSQxILCpxzi/NK1GyMqwFAAEhoTITAAAA</w:__Grammarly_42____i>
   <w:__Grammarly_42___1>H4sIAAAAAAAEAKtWcslP9kxRslIyNDY2MjU3MbY0NDKwNDExMDBW0lEKTi0uzszPAykwNKoFAFManQMtAAAA</w:__Grammarly_42___1>
  </w:DocumentVariables>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="&#45;-"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
  DefSemiHidden="false" DefQFormat="false" DefPriority="99"
  LatentStyleCount="376">
  <w:LsdException Locked="false" Priority="0" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 9"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 1"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 2"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 3"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 4"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 5"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 6"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 7"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 8"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="header"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footer"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index heading"/>
  <w:LsdException Locked="false" Priority="35" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of figures"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope return"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="line number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="page number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of authorities"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="macro"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="toa heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 5"/>
  <w:LsdException Locked="false" Priority="10" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Closing"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Signature"/>
  <w:LsdException Locked="false" Priority="1" SemiHidden="true"
   UnhideWhenUsed="true" Name="Default Paragraph Font"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Message Header"/>
  <w:LsdException Locked="false" Priority="11" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Salutation"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Date"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Note Heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Block Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="FollowedHyperlink"/>
  <w:LsdException Locked="false" Priority="22" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" Priority="20" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Document Map"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Plain Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="E-mail Signature"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Top of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Bottom of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal (Web)"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Acronym"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Cite"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Code"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Definition"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Keyboard"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Preformatted"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Sample"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Typewriter"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Variable"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Table"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation subject"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="No List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Contemporary"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Elegant"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Professional"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Balloon Text"/>
  <w:LsdException Locked="false" Priority="59" Name="Table Grid"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Theme"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="1" QFormat="true" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Revision"/>
  <w:LsdException Locked="false" Priority="34" QFormat="true"
   Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="29" QFormat="true" Name="Quote"/>
  <w:LsdException Locked="false" Priority="30" QFormat="true"
   Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="19" QFormat="true"
   Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="21" QFormat="true"
   Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="31" QFormat="true"
   Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="32" QFormat="true"
   Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="37" SemiHidden="true"
   UnhideWhenUsed="true" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>
  <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>
  <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>
  <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>
  <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>
  <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>
  <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hashtag"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Unresolved Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Link"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:新細明體;
	panose-1:2 2 5 0 0 0 0 0 0 0;
	mso-font-alt:PMingLiU;
	mso-font-charset:136;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-1610611969 684719354 22 0 1048577 0;}
@font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-469750017 -1040178053 9 0 511 0;}
@font-face
	{font-family:Cambria;
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
@font-face
	{font-family:"\.\.\.\.o\.\.\.";
	panose-1:0 0 0 0 0 0 0 0 0 0;
	mso-font-alt:新細明體;
	mso-font-charset:136;
	mso-generic-font-family:roman;
	mso-font-format:other;
	mso-font-pitch:auto;
	mso-font-signature:1 134742016 16 0 1048576 0;}
@font-face
	{font-family:"\@新細明體";
	panose-1:2 1 6 1 0 1 1 1 1 1;
	mso-font-charset:136;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-1610611969 684719354 22 0 1048577 0;}
@font-face
	{font-family:"\@\.\.\.\.o\.\.\.";
	panose-1:0 0 0 0 0 0 0 0 0 0;
	mso-font-charset:136;
	mso-generic-font-family:roman;
	mso-font-format:other;
	mso-font-pitch:auto;
	mso-font-signature:1 134742016 16 0 1048576 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin:0cm;
	mso-pagination:none;
	font-size:12.0pt;
	mso-bidi-font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:1.0pt;
	mso-fareast-language:ZH-TW;}
p.MsoHeader, li.MsoHeader, div.MsoHeader
	{mso-style-priority:99;
	mso-style-link:"Header Char";
	margin:0cm;
	mso-pagination:none;
	tab-stops:center 207.65pt right 415.3pt;
	layout-grid-mode:char;
	font-size:10.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:1.0pt;
	mso-fareast-language:ZH-TW;}
p.MsoFooter, li.MsoFooter, div.MsoFooter
	{mso-style-priority:99;
	mso-style-link:"Footer Char";
	margin:0cm;
	mso-pagination:none;
	tab-stops:center 207.65pt right 415.3pt;
	layout-grid-mode:char;
	font-size:10.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:1.0pt;
	mso-fareast-language:ZH-TW;}
p.MsoAcetate, li.MsoAcetate, div.MsoAcetate
	{mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-link:"Balloon Text Char";
	margin:0cm;
	mso-pagination:none;
	font-size:8.0pt;
	font-family:"Cambria",serif;
	mso-fareast-font-family:新細明體;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:1.0pt;
	mso-fareast-language:ZH-TW;}
p.MsoRMPane, li.MsoRMPane, div.MsoRMPane
	{mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:"";
	margin:0cm;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	mso-bidi-font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:1.0pt;
	mso-fareast-language:ZH-TW;}
p.Default, li.Default, div.Default
	{mso-style-name:Default;
	mso-style-unhide:no;
	mso-style-parent:"";
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	color:black;
	mso-fareast-language:ZH-TW;}
p.CM18, li.CM18, div.CM18
	{mso-style-name:CM18;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM1, li.CM1, div.CM1
	{mso-style-name:CM1;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM15, li.CM15, div.CM15
	{mso-style-name:CM15;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM14, li.CM14, div.CM14
	{mso-style-name:CM14;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM16, li.CM16, div.CM16
	{mso-style-name:CM16;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM17, li.CM17, div.CM17
	{mso-style-name:CM17;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM2, li.CM2, div.CM2
	{mso-style-name:CM2;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM3, li.CM3, div.CM3
	{mso-style-name:CM3;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM4, li.CM4, div.CM4
	{mso-style-name:CM4;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:12.9pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM5, li.CM5, div.CM5
	{mso-style-name:CM5;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM7, li.CM7, div.CM7
	{mso-style-name:CM7;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM8, li.CM8, div.CM8
	{mso-style-name:CM8;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM9, li.CM9, div.CM9
	{mso-style-name:CM9;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:13.0pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM10, li.CM10, div.CM10
	{mso-style-name:CM10;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	line-height:14.55pt;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM12, li.CM12, div.CM12
	{mso-style-name:CM12;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
p.CM13, li.CM13, div.CM13
	{mso-style-name:CM13;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-parent:Default;
	mso-style-next:Default;
	margin:0cm;
	mso-pagination:none;
	mso-layout-grid-align:none;
	text-autospace:none;
	font-size:12.0pt;
	font-family:"Arial",sans-serif;
	mso-fareast-font-family:新細明體;
	mso-fareast-language:ZH-TW;}
span.HeaderChar
	{mso-style-name:"Header Char";
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-parent:"";
	mso-style-link:Header;
	mso-ansi-font-size:10.0pt;
	mso-bidi-font-size:10.0pt;}
span.FooterChar
	{mso-style-name:"Footer Char";
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-parent:"";
	mso-style-link:Footer;
	mso-ansi-font-size:10.0pt;
	mso-bidi-font-size:10.0pt;}
span.BalloonTextChar
	{mso-style-name:"Balloon Text Char";
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-parent:"";
	mso-style-link:"Balloon Text";
	mso-ansi-font-size:8.0pt;
	mso-bidi-font-size:8.0pt;
	font-family:"Cambria",serif;
	mso-ascii-font-family:Cambria;
	mso-fareast-font-family:新細明體;
	mso-hansi-font-family:Cambria;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:1.0pt;}
span.GramE
	{mso-style-name:"";
	mso-gram-e:yes;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;
	font-size:10.0pt;
	mso-ansi-font-size:10.0pt;
	mso-bidi-font-size:10.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-fareast-font-family:新細明體;
	mso-hansi-font-family:Calibri;
	mso-font-kerning:0pt;
	mso-ligatures:none;}
 /* Page Definitions */
 @page
	{mso-page-border-surround-header:no;
	mso-page-border-surround-footer:no;
	mso-footnote-separator:url("Attachment%20III_2024-25_files/header.htm") fs;
	mso-footnote-continuation-separator:url("Attachment%20III_2024-25_files/header.htm") fcs;
	mso-endnote-separator:url("Attachment%20III_2024-25_files/header.htm") es;
	mso-endnote-continuation-separator:url("Attachment%20III_2024-25_files/header.htm") ecs;}
@page WordSection1
	{size:21.0cm 841.95pt;
	margin:2.0cm 2.0cm 2.0cm 2.0cm;
	mso-header-margin:36.0pt;
	mso-footer-margin:36.0pt;
	mso-header:url("Attachment%20III_2024-25_files/header.htm") h1;
	mso-paper-source:0;}
div.WordSection1
	{page:WordSection1;}
 /* List Definitions */
 @list l0
	{mso-list-id:-1006996405;
	mso-list-type:hybrid;
	mso-list-template-ids:-1597906025 -1 -1 -1 -1 -1 -1 -1 -1 -1;}
@list l0:level1
	{mso-level-number-format:alpha-lower;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level2
	{mso-level-number-format:ideograph-digital;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level3
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level4
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level5
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level6
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level7
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level8
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l0:level9
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1
	{mso-list-id:-991858939;
	mso-list-type:hybrid;
	mso-list-template-ids:1448756060 -1 -1 -1 -1 -1 -1 -1 -1 -1;}
@list l1:level1
	{mso-level-number-format:alpha-lower;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level2
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level3
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level4
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level5
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level6
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level7
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level8
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l1:level9
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2
	{mso-list-id:662206096;
	mso-list-type:hybrid;
	mso-list-template-ids:-620560854 -1 -1 -1 -1 -1 -1 -1 -1 -1;}
@list l2:level1
	{mso-level-number-format:alpha-lower;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level2
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level3
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level4
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level5
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level6
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level7
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level8
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l2:level9
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3
	{mso-list-id:1439791544;
	mso-list-type:hybrid;
	mso-list-template-ids:-550418029 -1 -1 -1 -1 -1 -1 -1 -1 -1;}
@list l3:level1
	{mso-level-number-format:alpha-lower;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level2
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level3
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level4
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level5
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level6
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level7
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level8
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
@list l3:level9
	{mso-level-start-at:0;
	mso-level-text:"";
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	margin-left:0cm;
	text-indent:0cm;}
ol
	{margin-bottom:0cm;}
ul
	{margin-bottom:0cm;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Calibri",sans-serif;
	mso-bidi-font-family:"Times New Roman";}
table.MsoTableGrid
	{mso-style-name:"Table Grid";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-priority:59;
	mso-style-unhide:no;
	border:solid windowtext 1.0pt;
	mso-border-alt:solid windowtext .5pt;
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-border-insideh:.5pt solid windowtext;
	mso-border-insidev:.5pt solid windowtext;
	mso-para-margin:0cm;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Calibri",sans-serif;
	mso-bidi-font-family:"Times New Roman";}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="14337"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
</head>

<body lang=EN-US style='tab-interval:36.0pt;word-wrap:break-word;text-justify-trim:
punctuation'>

<div class=WordSection1>

<p class=CM13 align=center style='text-align:center;layout-grid-mode:char'><b><span
style='font-size:14.0pt;font-family:"Times New Roman",serif;mso-fareast-font-family:
"\.\.\.\.o\.\.\.";color:black'>(Extract from UGC’s 2024/25 CDCF Definitions and
Classifications)<o:p></o:p></span></b></p>

<p class=Default><o:p>&nbsp;</o:p></p>

<p class=Default align=center style='text-align:center'><b style='mso-bidi-font-weight:
normal'><u><span style='font-size:14.0pt;font-family:"Times New Roman",serif'>Explanatory
Note of Staff Grades<o:p></o:p></span></u></b></p>

<p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<p class=CM16 style='layout-grid-mode:char'><b><span style='font-size:14.0pt;
font-family:"Times New Roman",serif;color:black'>C Staff </span></b><span
style='font-size:14.0pt;font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></p>

<p class=CM14 style='layout-grid-mode:char'><b><i><span style='font-size:11.5pt;
font-family:"Times New Roman",serif;color:black'><o:p>&nbsp;</o:p></span></i></b></p>

<p class=CM14 style='layout-grid-mode:char'><b><i><span style='font-size:11.5pt;
font-family:"Times New Roman",serif;color:black'>C1 Academic Staff </span></i></b><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></p>

<p class=CM16 style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char'><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black'><o:p>&nbsp;</o:p></span></p>

<p class=CM16 style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char'><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black'>Academic staff comprises staff members of academic departments,
whose main functions of employment are on teaching and / or research. <o:p></o:p></span></p>

<p class=Default style='layout-grid-mode:char'><span style='font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<p class=CM14 style='layout-grid-mode:char'><b><i><span style='font-size:11.5pt;
font-family:"Times New Roman",serif;color:black'>C2 Staff Grades </span></i></b><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black;
mso-font-kerning:0pt'><o:p>&nbsp;</o:p></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black;
mso-font-kerning:0pt'>Without prejudice to the institutional autonomy with
respect to nomenclature of staff grades and titles, universities are encouraged
to align staff titles to their respective staff <span class=GramE>grade</span>
as far as practicable. Staff grades are grouped as follows:<o:p></o:p></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'><o:p>&nbsp;</o:p></span></p>

<p class=CM14 style='margin-left:15.7pt;mso-para-margin-left:1.31gd;layout-grid-mode:
char'><b><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black'>Academic Grades </span></b><span style='font-size:11.5pt;
font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></p>

<p class=CM18 style='margin-left:31.3pt;mso-para-margin-left:2.61gd;layout-grid-mode:
char'><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black;mso-bidi-font-weight:bold;mso-bidi-font-style:italic'>Academic
staff primarily undertake work at degree or higher level</span><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></p>

<p class=CM18 style='margin-top:0cm;margin-right:6.1pt;margin-bottom:0cm;
margin-left:31.3pt;margin-bottom:.0001pt;mso-para-margin-top:0cm;mso-para-margin-right:
.51gd;mso-para-margin-bottom:0cm;mso-para-margin-left:2.61gd;mso-para-margin-bottom:
.0001pt;layout-grid-mode:char'><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black;mso-bidi-font-weight:bold;mso-bidi-font-style:italic'>(i.e.
Academic staff spend 80% or more of their time on work at degree or higher
level) <o:p></o:p></span></p>

<p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<table class=MsoTableGrid border=0 cellspacing=0 cellpadding=0 width=287
 style='width:215.45pt;margin-left:70.9pt;border-collapse:collapse;border:none;
 mso-yfti-tbllook:1184;mso-padding-alt:0cm 5.4pt 0cm 5.4pt;mso-border-insideh:
 none;mso-border-insidev:none'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=132 colspan=2 valign=top style='width:99.2pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><i><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>Academic,
  Senior</span></i><span style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p></o:p></span></p>
  </td>
  <td width=155 valign=top style='width:116.25pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>A<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Professor<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>B<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Reader<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:3'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>C<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Senior Lecturer<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:4'>
  <td width=132 colspan=2 valign=top style='width:99.2pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><i><span style='font-size:
  11.5pt;font-family:"Times New Roman",serif'>Academic, Junior</span></i><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p></o:p></span></p>
  </td>
  <td width=155 valign=top style='width:116.25pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:5'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>G<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Lecturer<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:6;mso-yfti-lastrow:yes'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>I<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Assistant Lecturer<o:p></o:p></span></p>
  </td>
 </tr>
 <![if !supportMisalignedColumns]>
 <tr height=0>
  <td width=47 style='border:none'></td>
  <td width=85 style='border:none'></td>
  <td width=155 style='border:none'></td>
 </tr>
 <![endif]>
</table>

<p class=CM1 style='margin-left:28.3pt;mso-para-margin-left:2.36gd;line-height:
normal;layout-grid-mode:char'><b><i><span style='font-size:11.5pt;font-family:
"Times New Roman",serif;color:black'><o:p>&nbsp;</o:p></span></i></b></p>

<p class=CM1 style='margin-left:28.3pt;mso-para-margin-left:2.36gd;line-height:
normal;layout-grid-mode:char'><b><i><span style='font-size:11.5pt;font-family:
"Times New Roman",serif;color:black'>Academic staff <span class=GramE><u>not</u></span>
primarily undertake work at <u>degree or higher level</u></span></i></b><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'><o:p></o:p></span></p>

<p class=CM1 style='margin-left:28.3pt;mso-para-margin-left:2.36gd;line-height:
normal;layout-grid-mode:char'><b><i><span style='font-size:11.5pt;font-family:
"Times New Roman",serif;color:black'>(i.e. Academic staff spend <u>less than
80%</u> of their time on work at degree or higher level) <o:p></o:p></span></i></b></p>

<p class=Default style='layout-grid-mode:char'><span style='font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<table class=MsoTableGrid border=0 cellspacing=0 cellpadding=0 width=287
 style='width:215.45pt;margin-left:70.9pt;border-collapse:collapse;border:none;
 mso-yfti-tbllook:1184;mso-padding-alt:0cm 5.4pt 0cm 5.4pt;mso-border-insideh:
 none;mso-border-insidev:none'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=132 colspan=2 valign=top style='width:99.2pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><i><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>Academic,
  Senior</span></i><span style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p></o:p></span></p>
  </td>
  <td width=155 valign=top style='width:116.25pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>A<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Professor<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>B<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Reader<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:3'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>C<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Senior Lecturer <o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:4'>
  <td width=132 colspan=2 valign=top style='width:99.2pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><i><span style='font-size:
  11.5pt;font-family:"Times New Roman",serif'>Academic, Junior</span></i><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p></o:p></span></p>
  </td>
  <td width=155 valign=top style='width:116.25pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:5'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>G<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Lecturer <o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:6;mso-yfti-lastrow:yes'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>I<o:p></o:p></span></p>
  </td>
  <td width=240 colspan=2 valign=top style='width:180.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Assistant Lecturer<o:p></o:p></span></p>
  </td>
 </tr>
 <![if !supportMisalignedColumns]>
 <tr height=0>
  <td width=47 style='border:none'></td>
  <td width=85 style='border:none'></td>
  <td width=155 style='border:none'></td>
 </tr>
 <![endif]>
</table>

<p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<p class=CM18 style='margin-left:48.4pt;mso-para-margin-left:3.37gd;text-indent:
-7.95pt;mso-char-indent-count:-.69;layout-grid-mode:char'><b><i><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:black'>Academic
supporting staff <o:p></o:p></span></i></b></p>

<p class=Default style='layout-grid-mode:char'><span style='font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<table class=MsoTableGrid border=0 cellspacing=0 cellpadding=0 width=472
 style='width:354.15pt;margin-left:70.9pt;border-collapse:collapse;border:none;
 mso-yfti-tbllook:1184;mso-padding-alt:0cm 5.4pt 0cm 5.4pt;mso-border-insideh:
 none;mso-border-insidev:none'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=CM4 style='line-height:normal;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>J<o:p></o:p></span></p>
  </td>
  <td width=425 valign=top style='width:318.7pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Instructor<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>K<o:p></o:p></span></p>
  </td>
  <td width=425 valign=top style='width:318.7pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Demonstrator / Tutor / Teaching
  Assistant<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:2;mso-yfti-lastrow:yes'>
  <td width=47 valign=top style='width:35.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>L<o:p></o:p></span></p>
  </td>
  <td width=425 valign=top style='width:318.7pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Other, including Language Assistant,
  Field Work Supervisor, etc.<o:p></o:p></span></p>
  </td>
 </tr>
</table>

<p class=Default style='layout-grid-mode:char'><span style='font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<p class=CM14 style='margin-left:36.0pt;mso-para-margin-left:3.0gd;layout-grid-mode:
char'><b><i><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black'>Technical research staff </span></i></b><span style='font-size:
11.5pt;font-family:"Times New Roman",serif;color:black'>(Staff who spend
essentially all their time on research) <o:p></o:p></span></p>

<p class=Default style='layout-grid-mode:char'><span style='font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<div align=center>

<table class=MsoNormalTable border=1 cellspacing=0 cellpadding=0 width=534
 style='width:400.3pt;border-collapse:collapse;border:none;mso-padding-alt:
 0cm 5.4pt 0cm 5.4pt'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
  height:54.15pt'>
  <td width=47 valign=top style='width:35.45pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:54.15pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>M<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.7pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:54.15pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Senior <u>Technical</u> Research Staff <span
  style='letter-spacing:-.1pt'>(“leaders”, usually Post Doctoral)</span><o:p></o:p></span></p>
  </td>
  <td width=47 valign=top style='width:35.45pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:54.15pt'>
  <p class=Default style='text-align:justify;text-justify:inter-ideograph;
  layout-grid-mode:char'><span style='font-size:11.5pt;font-family:"Times New Roman",serif'>N<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.7pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:54.15pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Junior <u>Technical</u> Research Staff<o:p></o:p></span></p>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>(“followers”, usually Graduate)<o:p></o:p></span></p>
  </td>
 </tr>
</table>

</div>

<p class=Default style='layout-grid-mode:char'><b><span style='font-size:11.5pt;
font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></b></p>

<p class=Default style='margin-left:14.15pt;mso-para-margin-left:1.18gd;
layout-grid-mode:char'><b><span style='font-size:11.5pt;font-family:"Times New Roman",serif'>Non-academic
Grades<o:p></o:p></span></b></p>

<p class=Default style='margin-left:35.4pt;mso-para-margin-left:2.95gd;
layout-grid-mode:char'><b><span style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></b></p>

<div align=center>

<table class=MsoNormalTable border=1 cellspacing=0 cellpadding=0 width=534
 style='width:400.3pt;border-collapse:collapse;border:none;mso-padding-alt:
 0cm 5.4pt 0cm 5.4pt'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:18.8pt'>
  <td width=267 colspan=2 style='width:200.15pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:18.8pt'>
  <p class=Default style='margin-left:16.55pt;text-indent:-16.55pt;mso-char-indent-count:
  -1.44;layout-grid-mode:char'><i><u><span style='font-size:11.5pt;font-family:
  "Times New Roman",serif'>Non-academic, Senior</span></u></i><u><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p></o:p></span></u></p>
  </td>
  <td width=267 colspan=2 style='width:200.15pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:18.8pt'>
  <p class=Default style='layout-grid-mode:char'><i><u><span style='font-size:
  11.5pt;font-family:"Times New Roman",serif'>Non-academic, Junior</span></u></i><u><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p></o:p></span></u></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;height:1.0pt'>
  <td width=47 valign=top style='width:35.4pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>O<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.75pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Admin, senior <o:p></o:p></span></p>
  </td>
  <td width=47 valign=top style='width:35.45pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>P<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.7pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Admin, junior (including <o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:2;height:1.0pt'>
  <td width=47 valign=top style='width:35.4pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.75pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
  <td width=47 valign=top style='width:35.45pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.7pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>secretarial, clerical)<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:3;height:1.0pt'>
  <td width=47 valign=top style='width:35.4pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>Q<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.75pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Technical, senior<o:p></o:p></span></p>
  </td>
  <td width=47 valign=top style='width:35.45pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>R<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.7pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Technical, junior <o:p></o:p></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:4;mso-yfti-lastrow:yes;height:1.0pt'>
  <td width=47 valign=top style='width:35.4pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.75pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>
  </td>
  <td width=47 valign=top style='width:35.45pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default align=center style='text-align:center;layout-grid-mode:char'><span
  style='font-size:11.5pt;font-family:"Times New Roman",serif'>S<o:p></o:p></span></p>
  </td>
  <td width=220 valign=top style='width:164.7pt;border:none;padding:0cm 5.4pt 0cm 5.4pt;
  height:1.0pt'>
  <p class=Default style='layout-grid-mode:char'><span style='font-size:11.5pt;
  font-family:"Times New Roman",serif'>Other, including “Mod 1”<o:p></o:p></span></p>
  </td>
 </tr>
</table>

</div>

<p class=Default style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char'><span style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:windowtext'><o:p>&nbsp;</o:p></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif'>The distinction
between categories “O. Admin, senior” / “Q. Technical, senior” and “P. Admin,
junior” / “R. Technical, junior” is their relationship to the University Senior
Lecturer and Lecturer scales respectively. <u>Where a university has other new
staff grades / posts not covered by the list, the staff concerned should be
assigned to the nearest equivalent staff grade based on the reference salary
range as follows:<o:p></o:p></u></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif'><o:p>&nbsp;</o:p></span></p>

<p class=MsoNormal style='margin-right:2.75pt;text-align:justify;text-justify:
inter-ideograph;layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:
none'><span lang=EN-HK style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black;mso-font-kerning:0pt;mso-ansi-language:EN-HK;mso-fareast-language:
ZH-CN'>For academic staff grades A-K, the following table provides up-to-date
monthly salary ranges in accordance with a list of staff grades (adopted before
the deregulation of salary for university staff from civil service) for
reference.</span><span lang=EN-HK style='font-size:11.5pt;font-family:"Times New Roman",serif;
mso-font-kerning:0pt;mso-ansi-language:EN-HK;mso-fareast-language:ZH-CN'><o:p></o:p></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:none'><span
lang=EN-HK style='font-size:11.5pt;font-family:"Times New Roman",serif;
mso-ansi-language:EN-HK'><o:p>&nbsp;</o:p></span></p>

<p class=MsoNormal style='margin-right:2.75pt;text-align:justify;text-justify:
inter-ideograph;layout-grid-mode:char;mso-layout-grid-align:none;text-autospace:
none'><span lang=EN-HK style='font-size:11.5pt;font-family:"Times New Roman",serif;
color:black;mso-font-kerning:0pt;mso-ansi-language:EN-HK;mso-fareast-language:
ZH-CN'>It should be noted that the reference salary ranges are made with
reference to prevailing civil service pay scale and are solely for the purpose
of statistical categorization in novel or “outlying” cases. The mapping should
primarily be based on aligning with the staff grade.</span><span lang=EN-HK
style='font-size:11.5pt;font-family:"Times New Roman",serif;mso-font-kerning:
0pt;mso-ansi-language:EN-HK;mso-fareast-language:ZH-CN'><o:p></o:p></span></p>

<p class=MsoNormal style='text-align:justify;text-justify:inter-ideograph;
mso-layout-grid-align:none;text-autospace:none'><span lang=EN-HK
style='font-size:11.5pt;font-family:"Times New Roman",serif;mso-ansi-language:
EN-HK'><o:p>&nbsp;</o:p></span></p>

<p class=MsoNormal style='mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:red;
mso-font-kerning:0pt'><o:p>&nbsp;</o:p></span></p>

<div align=center>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width=558
 style='width:418.85pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 5.4pt 0cm 5.4pt'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:20.55pt'>
  <td width=348 colspan=2 rowspan=2 style='width:261.25pt;border:solid windowtext 1.0pt;
  mso-border-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;height:20.55pt'>
  <p class=MsoNormal style='margin-top:6.0pt;line-height:115%;mso-pagination:
  widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Staff grade<o:p></o:p></span></p>
  </td>
  <td width=210 rowspan=2 style='width:157.6pt;border:solid windowtext 1.0pt;
  border-left:none;mso-border-left-alt:solid windowtext .5pt;mso-border-alt:
  solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;height:20.55pt'>
  <p class=MsoNormal style='margin-top:6.0pt;line-height:115%;mso-pagination:
  widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Reference monthly salary range<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:20.55pt;border:none' width=0 height=27></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:1;height:15.85pt'>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:2;height:9.45pt'>
  <td width=348 colspan=2 style='width:261.25pt;border-top:none;border-left:
  solid windowtext 1.0pt;border-bottom:solid windowtext 1.0pt;border-right:
  none;mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-left-alt:solid windowtext .5pt;mso-border-bottom-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:9.45pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><u><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Academic, senior<o:p></o:p></span></u></p>
  </td>
  <td width=210 valign=top style='width:157.6pt;border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:9.45pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  lang=ZH-TW style='font-size:11.5pt;line-height:115%;font-family:"新細明體",serif;
  mso-ascii-font-family:"Times New Roman";mso-hansi-font-family:"Times New Roman";
  color:black;mso-font-kerning:0pt'>　</span><span style='font-size:11.5pt;
  line-height:115%;font-family:"Times New Roman",serif;color:black;mso-font-kerning:
  0pt'><o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:9.45pt;border:none' width=0 height=13></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:3;height:15.85pt'>
  <td width=55 rowspan=2 valign=top style='width:41.25pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>A<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$256,985 or above<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:4;height:15.85pt'>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Non-clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$170,916 or above<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:5;height:15.85pt'>
  <td width=55 rowspan=2 valign=top style='width:41.25pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>B<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$239,032 - $253,298<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:6;height:15.85pt'>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Non-clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$116,351 - $165,953<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:7;height:15.85pt'>
  <td width=55 rowspan=2 valign=top style='width:41.25pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>C<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$194,825 - $239,940<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:8;height:15.85pt'>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Non-clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$116,351 - $165,953<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:9;height:15.75pt'>
  <td width=348 colspan=2 style='width:261.25pt;border-top:none;border-left:
  solid windowtext 1.0pt;border-bottom:solid windowtext 1.0pt;border-right:
  none;mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-left-alt:solid windowtext .5pt;mso-border-bottom-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><u><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Academic, junior<o:p></o:p></span></u></p>
  </td>
  <td width=210 valign=top style='width:157.6pt;border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  lang=ZH-TW style='font-size:11.5pt;line-height:115%;font-family:"新細明體",serif;
  mso-ascii-font-family:"Times New Roman";mso-hansi-font-family:"Times New Roman";
  color:black;mso-font-kerning:0pt'>　</span><span style='font-size:11.5pt;
  line-height:115%;font-family:"Times New Roman",serif;color:black;mso-font-kerning:
  0pt'><o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.75pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:10;height:15.85pt'>
  <td width=55 rowspan=2 valign=top style='width:41.25pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-left-alt:solid windowtext .5pt;mso-border-bottom-alt:
  solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;padding:
  0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>G<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border:none;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$61,865 - $147,125<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:11;height:15.85pt'>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Non-clinical <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$58,805 - $128,831<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:12;height:15.85pt'>
  <td width=55 style='width:41.25pt;border:solid windowtext 1.0pt;border-top:
  none;mso-border-top-alt:solid windowtext .5pt;mso-border-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>I<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Assistant Lecturer <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.85pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$40,620 - $82,330<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.85pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:13;height:3.2pt'>
  <td width=348 colspan=2 style='width:261.25pt;border-top:none;border-left:
  solid windowtext 1.0pt;border-bottom:solid windowtext 1.0pt;border-right:
  none;mso-border-top-alt:solid windowtext .5pt;mso-border-top-alt:solid windowtext .5pt;
  mso-border-left-alt:solid windowtext .5pt;mso-border-bottom-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:3.2pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Academic supporting staff<o:p></o:p></span></p>
  </td>
  <td width=210 valign=top style='width:157.6pt;border-top:none;border-left:
  none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:3.2pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  lang=ZH-TW style='font-size:11.5pt;line-height:115%;font-family:"新細明體",serif;
  mso-ascii-font-family:"Times New Roman";mso-hansi-font-family:"Times New Roman";
  color:black;mso-font-kerning:0pt'>　</span><span style='font-size:11.5pt;
  line-height:115%;font-family:"Times New Roman",serif;color:black;mso-font-kerning:
  0pt'><o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:3.2pt;border:none' width=0 height=4></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:14;height:15.75pt'>
  <td width=55 style='width:41.25pt;border:solid windowtext 1.0pt;border-top:
  none;mso-border-left-alt:solid windowtext .5pt;mso-border-bottom-alt:solid windowtext .5pt;
  mso-border-right-alt:solid windowtext .5pt;padding:0cm 5.4pt 0cm 5.4pt;
  height:15.75pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>J<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Instructor/Senior Instructor <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$35,080 - $81,510<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.75pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
 <tr style='mso-yfti-irow:15;mso-yfti-lastrow:yes;height:15.75pt'>
  <td width=55 valign=top style='width:41.25pt;border:solid windowtext 1.0pt;
  border-top:none;mso-border-left-alt:solid windowtext .5pt;mso-border-bottom-alt:
  solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;padding:
  0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal align=center style='margin-top:6.0pt;margin-right:0cm;
  margin-bottom:6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:
  0cm;mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;text-align:center;
  line-height:115%;mso-pagination:widow-orphan;layout-grid-mode:char;
  mso-layout-grid-align:none'><span style='font-size:11.5pt;line-height:115%;
  font-family:"Times New Roman",serif;color:black;mso-font-kerning:0pt'>K<o:p></o:p></span></p>
  </td>
  <td width=293 style='width:220.0pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>Demonstrator/Tutor/Teaching Assistant <o:p></o:p></span></p>
  </td>
  <td width=210 style='width:157.6pt;border-top:none;border-left:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  mso-border-bottom-alt:solid windowtext .5pt;mso-border-right-alt:solid windowtext .5pt;
  padding:0cm 5.4pt 0cm 5.4pt;height:15.75pt'>
  <p class=MsoNormal style='margin-top:6.0pt;margin-right:0cm;margin-bottom:
  6.0pt;margin-left:0cm;mso-para-margin-top:.5gd;mso-para-margin-right:0cm;
  mso-para-margin-bottom:.5gd;mso-para-margin-left:0cm;line-height:115%;
  mso-pagination:widow-orphan;layout-grid-mode:char;mso-layout-grid-align:none'><span
  style='font-size:11.5pt;line-height:115%;font-family:"Times New Roman",serif;
  color:black;mso-font-kerning:0pt'>$30,866 - $53,257<o:p></o:p></span></p>
  </td>
  <![if !supportMisalignedRows]>
  <td style='height:15.75pt;border:none' width=0 height=21></td>
  <![endif]>
 </tr>
</table>

</div>

<p class=MsoNormal style='mso-layout-grid-align:none;text-autospace:none'><span
style='font-size:11.5pt;font-family:"Times New Roman",serif;color:red;
mso-font-kerning:0pt'><o:p>&nbsp;</o:p></span></p>

</div>

</body>

</html>
