<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 

	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{staffInfoView.paramPid}" />
		<f:viewParam name="tab" value="#{staffInfoView.paramTab}" />
	</f:metadata>
	<f:event listener="#{staffInfoView.checkValid}" type="preRenderView" /> 
	<ui:define name="html_head">
		<style>
			div#dataForm\:formTab\:j_idt66\:emp_hist_hro_header{
				background-color: #106434 ;
				color:white;
			}
			
			div#dataForm\:formTab\:j_idt66\:hroEmployment th,
			div#dataForm\:formTab\:j_idt66\:hroEmployment .ui-state-active,
			div#dataForm\:formTab\:j_idt66\:hroEmployment .ui-button
			{
				background-color: #d88c54 !important;
				border-color: #d88c54 !important;
			}
			div#dataForm\:formTab\:j_idt66\:hroEmployment .ui-state-highlight {
				background-color: #fcf4f4 !important;
			}
			
		</style>
	</ui:define>
	
	<ui:define name="mainContent"> 
	<h:outputScript target="head">

	$(document).ready(function() {
	    photoFunction();
	    

	
	});
	function photoFunction(){
		document.querySelector('input[type="file"]').addEventListener('change', handler);
	};
	function handler() {
	    if (this.files &amp;&amp; this.files[0]) {
	      var selectedFile = this.files[0];
	      selectedFile.convertToBase64().then(function(obj){
	            var img = document.querySelector('#photo');
				if (selectedFile.size/1024/1024 &gt; 1){
					alert('The image size is too large.');
					return false;
				}
	            img.onload = () => {
	              URL.revokeObjectURL(img.src);  // no longer needed, free memory
	         	 }
	         	  img.src = obj.result;
	         	  var result = document.getElementById("dataForm:b64photo")
	         	  result.value = obj.result; 
	          });
	      }
	}
	File.prototype.convertToBase64 = function(){
         return new Promise(function(resolve, reject) {
                var reader = new FileReader();
                reader.onloadend = function (e) {
                    resolve({
                      fileName: this.name,
                      result: e.target.result, 
                      error: e.target.error
                    });
                };   
                reader.readAsDataURL(this);
        }.bind(this)); 
    };

	</h:outputScript>

	
	<p:panel id="contentPanel" rendered="#{staffInfoView.hasAccessRight}">
	<span class="admin-content-title"><i class="far fa-user-circle"></i> Manage My Info.</span>
	<p:messages id="messages"  autoUpdate="true" closable="true"/>
	

	<h:form id="dataForm">
			
		<div class="ui-g">
		<style>

	        .my-heading-row {
	            font-weight: bold;
	        }
	        
	    </style>
			
			<div class="ui-g-12 ui-md-3 ui-lg-3" style="text-align:center;">
				<h:panelGroup id="imgGroup">
					<img id="photo" src="data:image/jpg;base64,#{staffInfoView.sInfo.photoFile}" width="120px" style="#{staffInfoView.showPhoto?'':'display: none;'}"/>
					<img src="#{request.contextPath}/resources/image/no-photo-icon.jpg" width="120px" style="#{staffInfoView.showPhoto?'display: none;':''}"/>
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="color:#666; font-weight:700;">(Max. file size: 1MB)</div>
					<div class="ui-g-4 ui-md-6 ui-lg-4" style="text-align:right; color:#186ba0; font-weight:700;">Show Photo</div>
					<div class="ui-g-2 ui-md-6 ui-lg-2" style="text-align:left;">
						<p:toggleSwitch value="#{staffInfoView.showPhoto}">
							<p:ajax oncomplete="photoFunction()" update="imgGroup"/>
						</p:toggleSwitch>
					</div>
					<div class="ui-g-6 ui-md-12 ui-lg-6"><p:fileUpload id="chooseFile" onclick="handler()" mode="simple" skinSimple="true" disabled="#{staffInfoView.showPhoto?'false':'true'}"/></div>
					<h:inputHidden id="b64photo" value="#{staffInfoView.sInfo.photoFile}" />
				</h:panelGroup>
			</div>		
			<div class="ui-g-12 ui-md-9 ui-lg-9">
				<div class="ui-g">
					<div class="ui-g-12 ui-md-8 ui-lg-9">
						<h:outputText style="font-size:22px; font-weight:700;" value="#{staffInfoView.sIdentity.title} #{staffInfoView.sIdentity.fullname} #{staffInfoView.sIdentity.chinesename}"/>
						<br/>
						<h:outputText value="#{staffInfoView.getStaffPost()}" escape="false"/> <br/>
						<h:outputText style="font-size:15px; font-weight:700;" value="SDGs Information" escape="false"/> <br/>
						<p:selectCheckboxMenu  id="sdg_info" title="SDGs Information" 
								label="-- Please select --" 
								filter = "true" filterMatchMode= "startsWith" filterNormalize = "true" multiple = "true"
								style="min-width: 15rem"
								var="c"
								value="#{staffInfoView.sInfo.sdg_list}">
							<f:selectItems value="#{manageProjectView.sdgList}" var ="sdg" itemLabel="#{sdg.pk.lookup_code} - #{sdg.description}" 
							itemValue="#{sdg.pk.lookup_code}" >
	
							</f:selectItems>
						</p:selectCheckboxMenu>
			
					</div>
					<div class="ui-g-12 ui-md-4 ui-lg-3">
						<a href="#{staffInfoView.getPureProfileUrl()}" target="_blank" style="color:#026539;"><i class="fas fa-external-link-alt"></i>	View your current Profile in EdUHK Research Repository</a>
						<hr style="border: 1px solid #c8d3d9;"/>
						<a href="manageProfile.xhtml?pid=#{staffInfoView.sInfo.pid}" target="_blank" style="color:#006B87;"><i class="fas fa-user-edit"></i>	Manage Profile Preferences</a>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="padding-top:0; padding-bottom:0;">
						<hr style="border: 1px dotted #666;"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="border-left:3px solid #186ba0!important; color:#186ba0">
						<i class="fas fa-phone"></i> Phone
						<br/>
						#{!empty staffInfoView.iUserInfo.phone ? staffInfoView.iUserInfo.phone : 'N/A'}
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="border-left:3px solid #186ba0!important; color:#186ba0">
						<i class="fas fa-fax"></i> Fax
						<br/>
						#{!empty staffInfoView.iUserInfo.fax ? staffInfoView.iUserInfo.fax : 'N/A'}
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="border-left:3px solid #186ba0!important; color:#186ba0">
						<i class="far fa-envelope"></i> Email
						<br/>
						#{!empty staffInfoView.iUserInfo.email ? staffInfoView.iUserInfo.email : 'N/A'}
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3" style="border-left:3px solid #186ba0!important; color:#186ba0">
						<i class="fas fa-link"></i> Website
						<br/>
						<h:panelGroup id="websiteGroup">
							<h:outputLink value="#{staffInfoView.websiteLink}" target="_blank" rendered="#{staffInfoView.showWebsite ne 'NA'}"><h:outputText value="(View)"></h:outputText></h:outputLink>
							<h:outputText value="N/A" rendered="#{staffInfoView.showWebsite eq 'NA'}"></h:outputText>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="margin-top: 2px;">
						(Contact Information is extracted from Communication Directory.  To update, please use <a href="https://uslinux3.eduhk.hk/webform/ipphone/itsf021.php" target="_blank" style="color:#026539">Staff Communication Directory Update Request Form</a>)
					</div>
				</div>
			</div>
		</div>
		<hr style="border: 1px solid #678394; margin-top:0; margin-bottom:0;"/>
			<div class="ui-g">
			<div class="ui-g-12 ui-md-12 ui-lg-12" style="min-height: 400px; padding-top: 0px; padding-bottom: 0px;">
			<p:tabView id="formTab" activeIndex="#{staffInfoView.paramTab}">
		        <p:tab title="Personal Profile">
		            <h:panelGrid style="width: 100%;">
						<p:textEditor value="#{staffInfoView.sInfo.profile}" height="280">
							<f:facet name="toolbar">
					             <span class="ql-formats">
					                <button class="ql-bold"></button>
					                <button class="ql-italic"></button>
					                <button class="ql-underline"></button>
					                <button class="ql-strike"></button>
					              </span>
					              <span class="ql-formats">
					                <button class="ql-list" value="ordered"></button>
					                <button class="ql-list" value="bullet"></button>
					                <button class="ql-link"></button>
					            </span>
					        </f:facet>
						</p:textEditor>
		            </h:panelGrid>
		        </p:tab>
		        <p:tab title="#{sysParamView.getValue('BETA_VER') eq 'Y'?'Career Overview (Beta)':'Career Overview'}" >
		        	<p:staticMessage severity="warn" detail="#{sysParamView.getValue('BETA_MSG_CAREER_OVERVIEW')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('BETA_VER') eq 'Y' and sysParamView.getValue('BETA_MSG_CAREER_OVERVIEW') ne null}"/>
		        
		           <p:accordionPanel multiple="true" widgetVar="Company" style="padding-bottom: 25px;">
		           		<p:tab title="Career Overview">
				            	<p:dataTable id="hroEmployment_edit" var="data_2" value="#{staffInfoView.employmentHistList_edit}" 
	            							rowKey="#{data_2.pk.job_seq}" selection="#{staffInfoView.selectedEmploymentHist_edit}" selectionMode="single">
		            				
			            			<p:column headerText="Organization" style=" width: 50px; vertical-align: top;">
						                <h:outputText value="#{data_2.company}" />
						            </p:column>
			            			<p:column headerText="Title" style=" width: 30px; vertical-align: top;" >
						                <h:outputText value="#{data_2.job_title}" />
						            </p:column>
						            
						            <p:column headerText="Description" style=" width: 100px; vertical-align: top;" >
						                <h:outputText value="#{data_2.job_details}" escape="false"/>
						            </p:column>
						            <p:column headerText="Date From" style="width: 30px; vertical-align: top;">
						                <h:outputText value="#{data_2.from_date}">
												<f:convertDateTime dateStyle="full" pattern="MM/yyyy" timeZone="Hongkong"/>
											</h:outputText>
						            </p:column>
						            <p:column headerText="Date To" style="width: 30px; vertical-align: top;">
						            	<h:outputText value="Present" rendered="#{data_2.is_current == 'Y'}"/>	
						                <h:outputText value="#{data_2.to_date}" rendered="#{data_2.is_current == 'N'}">
												<f:convertDateTime dateStyle="full" pattern="MM/yyyy" timeZone="Hongkong"/>
										</h:outputText>
						            </p:column>
						            <p:column headerText="Action" id="manage_action" style="text-align: center;  width: 30px; vertical-align: top;" >
							  				<p:linkButton id="btn_modify" outcome="employmentHistory_edit" value="Modify">
							  					<f:param name="pid" value="#{data_2.pk.pid}"/>
							  					<f:param name="job_seq" value="#{data_2.pk.job_seq}"/>
							  				</p:linkButton>	
					            	</p:column>  

					            	<f:facet name="footer">
					             		<p:commandButton  value="Add New Career Overview" action="#{staffInfoView.gotoNewPage()}"/>
					             	</f:facet>
					        	</p:dataTable>
						</p:tab>
	
	            		<p:tab title="HRO Record (Please select and copy to your career overview)" id="emp_hist_hro"  closable ="true">
	            			<p:dataTable id="hroEmployment" var="data" value="#{staffInfoView.employmentHistList}" 
	            				selection="#{staffInfoView.selectedEmployHistList}" 
	            				rowKey="#{data.hist_id}" selectionPageOnly="false">
	            				<p:column selectionMode="multiple" style="width: 20px; text-align:center"/>
		            			 <p:column headerText="Title" style="vertical-align: top;">
					                <h:outputText value="#{data.rank_full}" />
					            </p:column>
					            <p:column headerText="Date From" style="vertical-align: top;">
					                <h:outputText value="#{data.eff_from}">
											<f:convertDateTime dateStyle="full" pattern="MM/yyyy" timeZone="Hongkong"/>
										</h:outputText>
					            </p:column>
					            <p:column headerText="Date To" style="vertical-align: top;">
					            	<h:outputText value="Present" class="end_date" rendered="#{data.eff_to ge staffInfoView.currentDate}" />
									<h:outputText value="#{data.eff_to}" class="end_date" rendered="#{data.eff_to lt  staffInfoView.currentDate}">
											<f:convertDateTime dateStyle="full" pattern="MM/yyyy" timeZone="Hongkong"/>
									</h:outputText>
					            </p:column>
					            <f:facet name="footer">
					                 <p:commandButton  value="Copy" action="#{staffInfoView.insertHroHist()}"  style="text-align: center;" update="messages"/>
					            </f:facet>
				        	</p:dataTable>
						</p:tab>
				 </p:accordionPanel>
				
		        </p:tab>
		        <p:tab title="Research Interests">
		            <h:panelGrid style="width: 100%">
						<p:textEditor value="#{staffInfoView.sInfo.research_interest}" height="280">
							<f:facet name="toolbar">
					             <span class="ql-formats">
					                <button class="ql-bold"></button>
					                <button class="ql-italic"></button>
					                <button class="ql-underline"></button>
					                <button class="ql-strike"></button>
					              </span>
					              <span class="ql-formats">
					                <button class="ql-list" value="ordered"></button>
					                <button class="ql-list" value="bullet"></button>
					                <button class="ql-link"></button>
					            </span>
					        </f:facet>
						</p:textEditor>
		            </h:panelGrid>
		        </p:tab>
		        <p:tab title="Teaching Interests">
		            <h:panelGrid style="width: 100%">
						<p:textEditor value="#{staffInfoView.sInfo.teaching_interest}" height="280">
							<f:facet name="toolbar">
					             <span class="ql-formats">
					                <button class="ql-bold"></button>
					                <button class="ql-italic"></button>
					                <button class="ql-underline"></button>
					                <button class="ql-strike"></button>
					              </span>
					              <span class="ql-formats">
					                <button class="ql-list" value="ordered"></button>
					                <button class="ql-list" value="bullet"></button>
					                <button class="ql-link"></button>
					            </span>
					        </f:facet>
						</p:textEditor>
		            </h:panelGrid>
		        </p:tab>
		        <p:tab title="External Appointments">
		            <h:panelGrid style="width: 100%">
						<p:textEditor value="#{staffInfoView.sInfo.ext_appt}" height="280">
							<f:facet name="toolbar">
					             <span class="ql-formats">
					                <button class="ql-bold"></button>
					                <button class="ql-italic"></button>
					                <button class="ql-underline"></button>
					                <button class="ql-strike"></button>
					              </span>
					              <span class="ql-formats">
					                <button class="ql-list" value="ordered"></button>
					                <button class="ql-list" value="bullet"></button>
					                <button class="ql-link"></button>
					            </span>
					        </f:facet>
						</p:textEditor>
		            </h:panelGrid>
		        </p:tab>	
		        <p:tab title="Other Activities">
					<h:panelGrid style="width: 100%">
						<p:textEditor value="#{staffInfoView.sInfo.oth_activity}" height="280">
							<f:facet name="toolbar">
					             <span class="ql-formats">
					                <button class="ql-bold"></button>
					                <button class="ql-italic"></button>
					                <button class="ql-underline"></button>
					                <button class="ql-strike"></button>
					              </span>
					              <span class="ql-formats">
					                <button class="ql-list" value="ordered"></button>
					                <button class="ql-list" value="bullet"></button>
					                <button class="ql-link"></button>
					            </span>
					        </f:facet>
						</p:textEditor>
		            </h:panelGrid>
		        </p:tab>	
		        <p:tab title="Website Selection">
		        	<h:panelGrid columns="2" layout="grid" style="width:90%" columnClasses="ui-g-12 ui-md-2 ui-lg-2, ui-g-12 ui-md-10 ui-lg-10">
			        	<p:outputLabel value="Researcher CV" style="display: inline-block; "/>
			        	<h:outputLink value="#{staffInfoView.cvLink}" target="_blank" style="display: inline-block; margin-bottom:10px;">
			        	<h:outputText value="#{staffInfoView.cvLink}" escape="false"></h:outputText>
			        	</h:outputLink>
			        	<h:panelGroup id="homepageTitleGroup">
			        		<p:outputLabel value="Homepage URL" style="display: inline-block; " rendered="#{staffInfoView.showWebsite eq 'HOMEPAGE'}" />
			        	</h:panelGroup>
			        	<h:panelGroup id="homepageGroup">
			        		<p:message for="homepage_url"/>
				        	<p:inputText id="homepage_url" label="Homepage URL" value="#{staffInfoView.homepageLink}" 
				        						rendered="#{staffInfoView.showWebsite eq 'HOMEPAGE'}" 
					        					required="#{staffInfoView.showWebsite eq 'HOMEPAGE'}" 
												requiredMessage="Homepage URL is mandatory." 
												style="width: 70%;">
				        		<p:ajax event="valueChange" update="dataForm:websiteGroup"/>
								<f:validateLength maximum="200"/>
							</p:inputText>
						</h:panelGroup>
						<p:outputLabel value="Website Selection" style="display: inline-block; "/>
						<p:selectOneButton value="#{staffInfoView.showWebsite}" unselectable="false">
							 <f:selectItem itemLabel="Researcher CV" itemValue="CVPAGE"/>
				            <f:selectItem itemLabel="Homepage" itemValue="HOMEPAGE"/>
				            <f:selectItem itemLabel="Not Shown" itemValue="NA"/>
				            <p:ajax event="change" update="dataForm:websiteGroup homepageTitleGroup homepageGroup"/>
			        </p:selectOneButton>
					</h:panelGrid>
		        </p:tab>	        		        		        			
		        <p:tab title="Researcher IDs">
		            <h:panelGrid columns="2" layout="grid" style="width:90%" columnClasses="ui-g-12 ui-md-2 ui-lg-2, ui-g-12 ui-md-10 ui-lg-10">
						ORCiD <a href="https://orcid.org/" title="Click to ORCiD website" target="_blank" style="color:red"><i class="far fa-question-circle"></i></a>
						<p:inputText id="orcid" label="ORCiD" title="ORCiD" value="#{staffInfoView.sInfo.orcid}"/>
						Scopus Author ID [1]  <a href="https://www.scopus.com/freelookup/form/author.uri" title="Click to Scopus website" target="_blank" style="color:red"><i class="far fa-question-circle"></i></a>
						<p:inputText id="scopusID1" label="Scopus Author ID [1]" title="Scopus Author ID [1]" value="#{staffInfoView.sInfo.scopus_id_1}" validatorMessage="Scopus Author ID [1]  should be 10 to 11 digits" >
							<f:validateRegex pattern="^([0-9]{10,11})?$"/>
						</p:inputText>
						Scopus Author ID [2] <a href="https://www.scopus.com/freelookup/form/author.uri" title="Click to Scopus website" target="_blank" style="color:red"><i class="far fa-question-circle"></i></a>
						<p:inputText id="scopusID2" label="Scopus Author ID [2]" title="Scopus Author ID [2]" value="#{staffInfoView.sInfo.scopus_id_2}" validatorMessage="Scopus Author ID [2] should be 10 to 11 digits" >
							<f:validateRegex pattern="^([0-9]{10,11})?$"/>
						</p:inputText>
						ResearcherID <a href="http://www.researcherid.com/" title="Click to ResearcherID website" target="_blank" style="color:red"><i class="far fa-question-circle"></i></a>
						<p:inputMask id="researchID" label="ResearcherID" title="ResearcherID" value="#{staffInfoView.sInfo.researcherid}" mask="a-9999-9999" validateMask="true" />
					</h:panelGrid>        
		        </p:tab>
		    </p:tabView>			
		</div>
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>	
		<p:commandButton value="Save and Release" action="#{staffInfoView.updateInfo}" update="@form messages">
			<p:confirm header="Confirmation" message="Are you sure to save and release the info.?" icon="pi pi-info-circle"/>
		</p:commandButton>
		<div style="color: #00a2c7; margin-left:5px; vertical-align:middle; line-height:2; font-weight:700;">(The Information will be updated to your EdUHK Research Repository Profile within an hour)</div>
		<p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
	    </p:confirmDialog>

	</div>
	</h:form>
	
	<!-- Confirm Delete Dialog -->
	<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
										 header="Confirm deletion?"
										 severity="alert" closable="false" visible="false">
											  

			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{staffInfoView.deleteEmpHistory()}"
								 update="messages"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
	</p:confirmDialog>
	
	</p:panel>
   </ui:define>
</ui:composition>