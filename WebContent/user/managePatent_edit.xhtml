<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:component="http://java.sun.com/jsf/composite/component"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	template="/resources/template/template.xhtml">

	<f:metadata>
		<f:event listener="#{managePatentView.getCanModify}" type="preRenderView" />
		<f:event listener="#{managePatentView.checkValid}" type="preRenderView" />
		<f:viewParam name="no" value="#{managePatentView.paramNo}" />
		<f:viewParam name="pid" value="#{managePatentView.paramPid}" />
		<f:viewParam name="dataLevel" value="#{managePatentView.paramDataLevel}" />
		<f:viewParam name="consent" value="#{managePatentView.paramConsent}" />
		<f:viewParam name="area_code" value="#{managePatentView.paramArea_code}" />
		<f:viewParam name="source_id" value="#{managePatentView.paramSource_id}" />
		<f:viewParam name="staff_number" value="#{managePatentView.paramStaff_number}" />
	</f:metadata>
	<ui:define name="html_head">
		<style>
			li.ui-state-disabled{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-selectonemenu-items {
			    display: inline !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent">
		<p:importConstants type="hk.eduhk.rich.Constant" var="const" />
		<p:panel id="contentPanel">
			<span class="admin-content-title"><i class="fas fa-trophy"></i>
				Manage Patent</span>
			<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
			<p:scrollTop />
			<h:outputText value="You don't have access right." rendered="#{managePatentView.hasAccessRight() == false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
			<h:form id="editForm"
				rendered="#{managePatentView.hasAccessRight() == true}">
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:linkButton value="#{bundle['action.back']}" outcome="managePatent" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{managePatentView.paramDataLevel eq 'M' &amp;&amp; managePatentView.paramConsent eq ''}">		
							<f:param name="pid" value="#{managePatentView.paramPid}"/>
						</p:linkButton>
						<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{managePatentView.paramConsent ne ''}">		
							<f:param name="pid" value="#{managePatentView.paramPid}"/>
							<f:param name="consent" value="#{managePatentView.paramConsent}"/>
							<f:param name="tabpage" value="3"/>
						</p:linkButton>
						<p:defaultCommand target="dummy"/>
						<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
						<p:commandButton id="top_btn_sava"
												value="#{formBundle['form.save']}"
												rendered="#{managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel ne 'C'}"
												style="margin-right:5px; margin-bottom:1px;" update="@form messages"
												action="#{managePatentView.save}"
												oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="top_btn_savaAndGen"
													value="#{(managePatentView.selectedPatentHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}"
													rendered="#{managePatentView.paramDataLevel eq 'C'}"
													style="margin-right:5px; margin-bottom:1px;" update="@form messages"
													action="#{managePatentView.save}"
													oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="top_btn_savaAndPublish"
												value="#{bundle['action.saveAndPublish']}"
												rendered="#{managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel eq 'M'}"
												style="margin-right:5px; margin-bottom:1px;" update="@form messages"
												action="#{managePatentView.saveAndPublishForm}"
												oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.save.publish.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_submit"
												value="#{formBundle['form.save']}"
												rendered="#{managePatentView.isCreator == false &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel eq 'M'}"
												style="margin-right:5px; margin-bottom:1px;" update="@form messages"
												action="#{managePatentView.submitConsent}"
												oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
						</p:commandButton>
						<p:commandButton id="top_btn_snapshot" value="#{formBundle['form.take.snapshot']}"
												rendered="#{managePatentView.paramDataLevel eq 'P'}"
												style="margin-right:5px; margin-bottom:1px;" process="@this"
												action="#{managePatentView.takeSnapshot}"
												oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.take.snapshot.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_delete"
												value="#{formBundle['form.del']}"
												rendered="#{managePatentView.canDelete == true &amp;&amp; managePatentView.canModify == true}"
												style="margin-right:5px; margin-bottom:1px; background:#D32F2F; border:1px solid #D32F2F;"
												process="@this" action="#{managePatentView.deleteForm}">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.del.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:linkButton id="btn_p_level" outcome="managePatent_edit"
							rendered="#{managePatentView.paramDataLevel eq 'P' &amp;&amp; managePatentView.checkSnapshotExists()}"
							value="#{formBundle['form.ri.goto.lv.c']}" icon="pi pi-chevron-right"
							style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
							<f:param name="pid" value="#{managePatentView.getParamPid()}" />
							<f:param name="no"
								value="#{managePatentView.selectedPatentHeader_q.patent_no}" />
							<f:param name="dataLevel" value="C" />
						</p:linkButton>
						<p:linkButton id="btn_c_level" outcome="managePatent_edit"
							rendered="#{managePatentView.paramDataLevel eq 'C'}"
							value="#{formBundle['form.ri.goto.lv.p']}" icon="pi pi-chevron-right"
							style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
							<f:param name="pid" value="#{managePatentView.getParamPid()}" />
							<f:param name="no"
								value="#{managePatentView.selectedPatentHeader_q.patent_no}" />
							<f:param name="dataLevel" value="P" />
						</p:linkButton>
						<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true" width="350">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Yes" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
						</p:confirmDialog>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-2"
						style="font-size: 18px; font-weight: 700; text-align: right;">
						<p:outputLabel value="#{formBundle['form.ri.status.lv.p']}"
							style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;"
							rendered="#{managePatentView.paramDataLevel eq 'P'}" />
						<p:outputLabel value="#{formBundle['form.ri.status.lv.c']}"
							style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;"
							rendered="#{managePatentView.paramDataLevel eq 'C'}" />
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
							<i class="fas fa-tag" style="margin-right: 5px;"></i>#{formBundle['form.ri.status']}
				</div>
				<hr/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="background:#00a2c7; font-size:18px; font-weight:700;">
						<p:outputLabel style="color:#1f1645;" value="#{formBundle['form.header.status']} " />
						<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{managePatentView.selectedPatentHeader_q.publish_status}" rendered="#{managePatentView.selectedPatentHeader_q.publish_status ne 'PUBLISHED'}"/>
						<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{managePatentView.selectedPatentHeader_q.publish_status eq 'PUBLISHED'}"/>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.at']} " />
						<h:outputText class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.last_modified_date}" >
							    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.by']} " />
						<p:outputLabel class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.last_modified_by}" />
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.at']} " />
						<h:outputText class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.last_published_date}" >
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
		
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.by']} " />
						<p:outputLabel class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.last_published_by}" />
					</div>
					
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
					
					<div class="ui-g-6 ui-md-4 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.institute']}" />
					</div>
					<div class="ui-g-6 ui-md-8 ui-lg-4">
						<p:message for="inst_display_ind"/>
						<p:selectOneMenu id="inst_display_ind" required="#{managePatentView.isRdoAdmin == true}"
													disabled="#{managePatentView.isRdoAdmin == false || managePatentView.paramDataLevel eq 'M'}" 
													title="#{formBundle['form.header.display.institute']}" 
													label="#{formBundle['form.header.display.institute']}"
													value="#{managePatentView.selectedPatentHeader_q.inst_display_ind}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							</p:selectOneMenu>
					</div>
					
					<div class="ui-g-6 ui-md-4 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute']}" />
					</div>
					<div class="ui-g-6 ui-md-8 ui-lg-4">
						<p:message for="inst_verified_ind"/>
						<p:selectOneMenu id="inst_verified_ind" required="#{managePatentView.isRdoAdmin == true}" 
													disabled="#{managePatentView.isRdoAdmin == false || managePatentView.paramDataLevel eq 'M'}"
													title="#{formBundle['form.header.verify.institute']}" 
													label="#{formBundle['form.header.verify.institute']}"
													value="#{managePatentView.selectedPatentHeader_q.inst_verified_ind}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   					
								<!--  <p:ajax event="valueChange" update="inst_verified_date"/>-->
							</p:selectOneMenu>		
					</div>
					<!--
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute.date']}" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="inst_verified_date"/>
						<p:datePicker id="inst_verified_date" 
												title="#{formBundle['form.header.verify.institute.date']}" 
												label="#{formBundle['form.header.verify.institute.date']}" 
												value="#{managePatentView.selectedPatentHeader_q.inst_verified_date}" 
												maxdate="#{managePatentView.currentDate}"
												pattern="yyyy-M-d" showIcon="true" 
												disabled="#{managePatentView.isRdoAdmin == false || managePatentView.paramDataLevel eq 'M' || managePatentView.selectedPatentHeader_q.inst_verified_ind eq 'N'}"/>			
					</div>
					-->
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.generate']}" />
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
						<p:outputLabel class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_gen_ind eq 'Y'?'Yes':'No'}"/>
						<p:outputLabel class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_gen_ind eq 'Y'?' — ':''}"/>
						<h:outputText class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_gen_date}" rendered="#{managePatentView.selectedPatentHeader_q.cdcf_gen_ind eq 'Y'}">
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<h:outputText class="riForm-item-title" value="#{formBundle['form.cdcf.change']}" rendered="#{managePatentView.isRdoAdmin == true}"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
							<h:outputText class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_changed_ind eq 'Y'?'Yes':'No'}" rendered="#{managePatentView.isRdoAdmin == true}"/>
							<p:linkButton id="btn_compare" outcome="riComparisonReport" rendered="#{managePatentView.selectedPatentHeader_q.cdcf_changed_ind eq 'Y' &amp;&amp; managePatentView.isRdoAdmin == true}" value="Compare Snapshot" style="margin-left:7px;" target="_blank">
			  					<f:param name="no" value="#{managePatentView.selectedPatentHeader_q.patent_no}"/>
			  					<f:param name="riType" value="patent"/>
			  				</p:linkButton>	
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.process']}" />
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
						<p:outputLabel class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_processed_ind eq 'Y'?'Yes':'No'}"/>
						<p:outputLabel class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_processed_ind eq 'Y'?' — ':''}"/>
						<h:outputText class="riForm-item-ans" value="#{managePatentView.selectedPatentHeader_q.cdcf_processed_date}" rendered="#{managePatentView.selectedPatentHeader_q.cdcf_processed_ind eq 'Y'}">
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.status']}" />
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
						<p:message for="cdcf_status"/>
						<p:selectOneMenu id="cdcf_status" title="#{formBundle['form.cdcf.status']}" label="#{formBundle['form.cdcf.status']}" value="#{managePatentView.selectedPatentHeader_q.cdcf_status}" 
													required="#{managePatentView.isRdoAdmin == true}" 
													disabled="#{managePatentView.isRdoAdmin == false || managePatentView.paramDataLevel eq 'M'}">
								<f:selectItems value="#{managePatentView.cdcfStatusList}"/>
							</p:selectOneMenu>	
					</div>

					
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
					
					<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{managePatentView.paramDataLevel eq 'M'?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.ri.profile']}" style="padding-right:20px;" rendered="#{managePatentView.paramDataLevel eq 'M'}"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{managePatentView.paramDataLevel eq 'M'?'':'display:none;'}">
						<h:panelGroup id="displayRIGroup">
							<p:selectOneMenu title="#{formBundle['form.header.display.ri.profile']}" value="#{managePatentView.selectedPatentDetails_q.display_ind}" 
													rendered="#{managePatentView.paramDataLevel eq 'M'}"
													disabled="#{managePatentView.canModify == false || managePatentView.selectedPatentDetails_q.consent_ind ne 'Y'}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							</p:selectOneMenu>
						</h:panelGroup>
					</div>	
					
					<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{managePatentView.isContributor == true &amp;&amp; managePatentView.paramDataLevel eq 'M'?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.consent.ri']}" style="padding-right:20px;" rendered="#{managePatentView.isContributor == true &amp;&amp; managePatentView.paramDataLevel eq 'M'}"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{managePatentView.isContributor == true &amp;&amp; managePatentView.paramDataLevel eq 'M'?'':'display:none;'}">	
						<p:selectOneMenu title="#{formBundle['form.header.consent.ri']}" value="#{managePatentView.selectedPatentDetails_q.consent_ind}" 
												rendered="#{managePatentView.isContributor == true &amp;&amp; managePatentView.paramDataLevel eq 'M'}"
												disabled="#{managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Unconfirmed" itemValue="U"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="displayRIGroup" listener="#{managePatentView.setDisplyRI()}"/>
						</p:selectOneMenu>
					</div>	
										
					<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{managePatentView.isRdoAdmin?'':'display: none;'}">
							<p:outputLabel class="riForm-item-title" value="#{formBundle['form.remarks']}" rendered="#{managePatentView.isRdoAdmin == true}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{managePatentView.isRdoAdmin?'':'display: none;'}">
							<p:message for="remarks"/>
							<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="7" counter="display" maxlength="1000" rendered="#{managePatentView.isRdoAdmin == true}"
													value="#{managePatentView.selectedPatentHeader_q.remarks}"
		                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
		                      <br/>
		      				  <h:outputText id="display" class="p-d-block" />
					</div>	
				</div>
		
				<br/>
				<div class="form-sub-title">
							<i class="fas fa-tag" style="margin-right: 5px;"></i>Patent Details
				</div>
				<hr/>

				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Inventor Patent By-line
						EdUHK</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<h:panelGroup id="ied_work_ind_ans_n">
							<p:outputLabel id="ied_work_ind_ans_n_label"
								value="This patent will not be counted in the CDCF."
								style="border-left:8px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:1px 4px; margin-bottom:5px; display:block; width:90%;"
								rendered="#{managePatentView.selectedPatentHeader_p.ied_work_ind eq 'N'}" />
						</h:panelGroup>
						<p:message for="ied_work_ind" />
						<p:selectOneMenu id="ied_work_ind"
							title="Inventor Patent By-line EdUHK"
							value="#{managePatentView.selectedPatentHeader_p.ied_work_ind}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y" />
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N" />
							<p:ajax event="valueChange" update="ied_work_ind_ans_n" />
						</p:selectOneMenu>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Patent Type</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="patent_granted" />
						<p:selectOneMenu id="patent_granted"
							title="Patent Type"
							value="#{managePatentView.selectedPatentHeader_p.patent_granted}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Patent Application" itemValue="A" />
							<f:selectItem itemLabel="Patent Granted" itemValue="G" />
						</p:selectOneMenu>
					</div>
		
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Name of Patent</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="patent_nameMsg" for="patent_name" />
						<p:inputText id="patent_name" titla="Name of Patent"
							label="Name of Patent" maxlength="100"
							value="#{managePatentView.selectedPatentHeader_p.patent_name}"
							style="width: 90%;"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Serial Number of Patent</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="patent_sNoMsg" for="patent_sNo" />
						<p:inputText id="patent_sNo"
							label="Serial Number of Patent"
							maxlength="100"
							value="#{managePatentView.selectedPatentHeader_p.serial_no}"
							style="width: 90%;"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}" />
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Country/place Granting the National Patent</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="country" />
						<p:selectOneMenu id="country" filter="true" filterMatchMode="contains"
							title="Country/place Granting the National Patent"
							value="#{managePatentView.selectedPatentHeader_p.country}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{managePatentView.country_list}" var="o"
								itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						</p:selectOneMenu>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Type of Patent</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="patent_type" />
						<p:selectOneMenu id="patent_type" style="width:90%; white-space: pre-wrap;" escape="false"
							title="Type of Patent"
							value="#{managePatentView.selectedPatentHeader_p.patent_type}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{managePatentView.type_list}" var="o" itemLabelEscaped="false"
								itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}"/>
						</p:selectOneMenu>
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Brief Description of
						Patent (around 250 words)</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="patent_short_descMsg" for="patent_short_desc" />
						<p:inputText id="patent_short_desc"
							label="Brief Description of Patent" maxlength="250"
							value="#{managePatentView.selectedPatentHeader_p.short_desc}"
							style="width: 90%;"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Detail Description of Patent</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="patent_long_desc" />
						<p:inputTextarea id="patent_long_desc" style="width: 90%;" 
							label="Detail Description of Patent" title="Detail Description of Patent"
							rows="7"
							counter="patent_long_desc_display" maxlength="1000"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}"
							value="#{managePatentView.selectedPatentHeader_p.full_desc}"
							counterTemplate="#{bundle['form.remaining.characters']} {0}"
							autoResize="false">
							<f:validator validatorId="hk.eduhk.rich.validator.TextValidator"/>
              				<f:attribute name="validateField" value="patent_long_desc" />
              			</p:inputTextarea>
						<br />
						<h:outputText id="patent_long_desc_display" class="p-d-block" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Date of Application/Grant
						(dd/mm/yyyy)</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="patentDay" />
						<p:message for="patentMonth" />
						<p:message for="patentYear" />
						<p:selectOneMenu id="patentDay" title="Date of Application/Grant - Day"
							label="Date of Application/Grant - Day"
							value="#{managePatentView.selectedPatentHeader_p.patent_day}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="" itemValue="#{null}"/>
							<f:selectItems value="#{managePatentView.dayList}" var="o"
								itemLabel="#{o}" itemValue="#{o}" />
								<p:ajax event="change"/>
						</p:selectOneMenu>
						<p:selectOneMenu id="patentMonth" title="Date of Application/Grant - Month"
							label="Date of Application/Grant - Month"
							value="#{managePatentView.selectedPatentHeader_p.patent_month}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{managePatentView.monthList}" var="o"
								itemLabel="#{o}" itemValue="#{o}" />
							<p:ajax event="change" update="patentDay" />
						</p:selectOneMenu>
						<p:selectOneMenu id="patentYear" title="Date of Application/Grant - Year"
							label="Date of Application/Grant - Year"
							value="#{managePatentView.selectedPatentHeader_p.patent_year}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{managePatentView.yearList}" var="o"
								itemLabel="#{o}" itemValue="#{o}" />
							<p:ajax event="change" update="patentDay" />
						</p:selectOneMenu>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fas fa-tag" style="margin-right:5px;"></i>Name of Inventor(s) in sequential order
				</div>
				<hr/>
				<p:messages for="columnTable"/>
				
				<p:dataTable id="columnTable" class="riFormTable"
					value="#{managePatentView.patentDetails_p_list}" var="col"
					widgetVar="columnTableWV" rows="50" reflow="true"
					 rowsPerPageTemplate="10,20,50,100"
					paginator="true"
					paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
					currentPageReportTemplate="Total Number of Inventor(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
					style="max-width:99%;" rowIndexVar="rowIndex">

					<p:column class="data-noprint" style="width:1em; text-align:left;">
						<f:facet name="header">Up</f:facet>
						<p:commandLink action="#{managePatentView.moveColumnUp(rowIndex)}"
							update="columnTable" immediate="true"
							rendered="#{rowIndex gt 0 &amp;&amp; managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel ne 'P'}">
							<i class="fas fa-caret-up icon-action" title="Move up" />
						</p:commandLink>
					</p:column>

					<p:column class="data-noprint" style="width:1em; text-align:left;">
						<f:facet name="header">Dn.</f:facet>
						<p:commandLink
							action="#{managePatentView.moveColumnDown(rowIndex)}"
							update="columnTable" immediate="true"
							rendered="#{rowIndex lt managePatentView.patentDetails_p_list.size()-1 &amp;&amp; managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel ne 'P'}">
							<i class="fas fa-caret-down icon-action" title="Move down" />
						</p:commandLink>
					</p:column>

					<p:column style="text-align:left; width:5em;">
						<f:facet name="header">No.</f:facet>
						<ui:fragment
							rendered="#{managePatentView.isRiCreator(col.inventor_staff_no)}">
							<i class="fa fa-star faa-pulse animated-hover" title="RI creator"
								style="font-size: 9px; color: #f06524; vertical-align: middle;"></i>
						</ui:fragment> #{rowIndex +1}
		</p:column>

					<p:column style="width:15em; text-align:left;">
						<f:facet name="header">#{formBundle['form.ri.creator.patent']} Position in RI</f:facet>
						<p:message id="typeMsg" for="type" />
						<p:selectOneMenu id="type" value="#{col.non_ied_staff_flag}"
							disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="EdUHK Staff" itemValue="N" />
							<f:selectItem itemLabel="Former Staff" itemValue="F" />
							<f:selectItem itemLabel="Non EdUHK Staff" itemValue="Y" />
							<f:selectItem itemLabel="EdUHK Student" itemValue="S" />
							<p:ajax event="change" update="nameGroup cvGroup" />
						</p:selectOneMenu>
						<p:blockUI block="contentPanel" trigger="type" />
					</p:column>

					<p:column>
						<f:facet name="header">#{formBundle['form.ri.creator.patent']}</f:facet>
						<h:panelGroup id="nameGroup">
							<p:message for="select_staff_inventor_name" />
							<p:selectOneMenu id="select_staff_inventor_name" dynamic="true"
								label="#{formBundle['form.ri.creator.patent']}" style="width:90%" value="#{col.inventor_staff_no}"
								filter="true" filterMatchMode="startsWith"
								rendered="#{col.non_ied_staff_flag eq 'N'}"
								disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true" />
								<f:selectItems value="#{manageRIView.staffList}"/>
								<p:ajax event="change" update="cvGroup" />
							</p:selectOneMenu>								
							<p:message for="select_staff_past_name" />							
							<p:autoComplete id="select_staff_past_name" value="#{col.inventor_name}" style="#{managePatentView.paramDataLevel eq 'C'?'width:70%':'width:90%'}"
													rendered="#{col.non_ied_staff_flag eq 'F'}"
													minQueryLength="3" 
													emptyMessage="No results"
													forceSelection="true"
													disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}"
                               						completeMethod="#{manageRIView.fsCompleteText}" scrollHeight="250">
                               						<p:ajax event="itemSelect" process="@this" />
                               						<p:ajax event="itemSelect" listener="#{managePatentView.updateRowStaffNum(rowIndex)}" update="nameGroup"/>
                              </p:autoComplete>
                              <p:inputText id="select_staff_past_num" label="Staff No." style="#{managePatentView.isRdoAdmin == true?'width:25%':'display:none'}" value="#{col.inventor_staff_no}" rendered="#{col.non_ied_staff_flag eq 'F'}" dynamic="true">
							  </p:inputText>
                              
							<p:message id="inventor_nameMsg" for="inventor_name" />
							<p:inputText id="inventor_name" label="#{formBundle['form.ri.creator.patent']}"
								style="width:90%" value="#{col.inventor_name}"
								onkeypress="return (event.charCode != 59);" maxlength="80"
								rendered="#{col.non_ied_staff_flag ne 'N' &amp;&amp; col.non_ied_staff_flag ne 'F'}"
								disabled="#{managePatentView.isCreator == false || managePatentView.canModify == false || managePatentView.paramDataLevel eq 'P'}">
								<p:ajax />
							</p:inputText>
						</h:panelGroup>
					</p:column>
					<p:column class="data-noprint" style="width:8em;">
						<h:panelGroup id="cvGroup">
							<p:linkButton id="btn_view_cv" outcome="/web/person"
								rendered="#{col.non_ied_staff_flag eq 'N' &amp;&amp; managePatentView.getStaffProfilePid(col.inventor_staff_no) ne null}"
								value="#{formBundle['form.view.profile']}" target="_blank">
								<f:param name="pid"
									value="#{managePatentView.getStaffProfilePid(col.inventor_staff_no)}" />
							</p:linkButton>
						</h:panelGroup>
					</p:column>
					<p:column class="data-noprint" style="width:2em; text-align:left;">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_delete"
							action="#{managePatentView.deleteRow(rowIndex)}"
							rendered="#{managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel ne 'P'}"
							update="columnTable" immediate="true">
							<i class="fas fa-trash icon-action"
								title="#{formBundle['form.del']}" />
						</p:commandLink>
						<p:blockUI block="contentPanel" trigger="btn_delete" />
					</p:column>
				</p:dataTable>
				<br />
				<p:commandButton id="btn_add" icon="fas fa-plus"
					value="Add new inventor"
					rendered="#{managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel ne 'P'}"
					style="width:260px" action="#{managePatentView.addRow()}"
					update="columnTable" immediate="true" />
				<p:blockUI block="contentPanel" trigger="btn_add" />
				<br /><br />
				<h:panelGroup styleClass="button-panel">
					<p:linkButton value="#{bundle['action.back']}" outcome="managePatent" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{managePatentView.paramDataLevel eq 'M' &amp;&amp; managePatentView.paramConsent eq ''}">		
						<f:param name="pid" value="#{managePatentView.paramPid}"/>
					</p:linkButton>
					<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{managePatentView.paramConsent ne ''}">		
						<f:param name="pid" value="#{managePatentView.paramPid}"/>
						<f:param name="consent" value="#{managePatentView.paramConsent}"/>
						<f:param name="tabpage" value="3"/>
					</p:linkButton>
					<p:commandButton id="btn_sava" value="#{formBundle['form.save']}"
						rendered="#{managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel ne 'C'}"
						style="margin-right:5px;" update="@form messages"
						action="#{managePatentView.save}"
						oncomplete="window.scrollTo(0,0);">
					</p:commandButton>
					<p:commandButton id="btn_savaAndGen"
						value="#{(managePatentView.selectedPatentHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}"
						rendered="#{managePatentView.paramDataLevel eq 'C'}"
						style="margin-right:5px;" update="@form messages"
						action="#{managePatentView.save}"
						oncomplete="window.scrollTo(0,0);">
					</p:commandButton>
					<p:commandButton id="btn_savaAndPublish"
						value="#{bundle['action.saveAndPublish']}"
						rendered="#{managePatentView.isCreator == true &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel eq 'M'}"
						style="margin-right:5px;" update="@form messages"
						action="#{managePatentView.saveAndPublishForm}"
						oncomplete="window.scrollTo(0,0);">
						<p:confirm header="#{formBundle['form.confirm']}"
							message="#{formBundle['form.save.publish.desc']}"
							icon="pi pi-info-circle" />
					</p:commandButton>
					<p:commandButton id="btn_submit" value="#{formBundle['form.save']}"
						rendered="#{managePatentView.isCreator == false &amp;&amp; managePatentView.canModify == true &amp;&amp; managePatentView.paramDataLevel eq 'M'}"
						style="margin-right:5px;" update="@form messages"
						action="#{managePatentView.submitConsent}"
						oncomplete="window.scrollTo(0,0);">
						<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
					</p:commandButton>
					<p:commandButton id="btn_snapshot" value="#{formBundle['form.take.snapshot']}"
						rendered="#{managePatentView.paramDataLevel eq 'P'}"
						style="margin-right:5px;" process="@this"
						action="#{managePatentView.takeSnapshot}"
						oncomplete="window.scrollTo(0,0);">
						<p:confirm header="#{formBundle['form.confirm']}"
							message="#{formBundle['form.take.snapshot.desc']}"
							icon="pi pi-info-circle" />
					</p:commandButton>
					<p:commandButton id="btn_delete" value="#{formBundle['form.del']}"
						rendered="#{managePatentView.canDelete == true &amp;&amp; managePatentView.canModify == true}"
						style="margin-right:5px; background:#D32F2F; border:1px solid #D32F2F;"
						process="@this" action="#{managePatentView.deleteForm}">
						<p:confirm header="#{formBundle['form.confirm']}"
							message="#{formBundle['form.del.desc']}"
							icon="pi pi-info-circle" />
					</p:commandButton>
					<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
						responsive="true" width="350">
						<p:commandButton value="No" icon="pi pi-times" type="button"
							styleClass="ui-confirmdialog-no ui-button-flat" />
						<p:commandButton value="Yes" icon="pi pi-check" type="button"
							styleClass="ui-confirmdialog-yes" />
					</p:confirmDialog>
					<!--  <p:blockUI block="contentPanel" trigger="top_btn_sava" />
							<p:blockUI block="contentPanel" trigger="top_btn_savaAndPublish" />
							<p:blockUI block="contentPanel" trigger="top_btn_submit" />
							<p:blockUI block="contentPanel" trigger="top_btn_snapshot" />
							<p:blockUI block="contentPanel" trigger="top_btn_delete" />
							<p:blockUI block="contentPanel" trigger="btn_sava" />
							<p:blockUI block="contentPanel" trigger="btn_savaAndPublish" />
							<p:blockUI block="contentPanel" trigger="btn_submit" />
							<p:blockUI block="contentPanel" trigger="btn_snapshot" />
							<p:blockUI block="contentPanel" trigger="btn_delete" />-->
				</h:panelGroup>
				<p:scrollTop target="parent" threshold="100" styleClass="custom-scrolltop" icon="pi pi-arrow-up" />
			</h:form>

		</p:panel>
	</ui:define>

</ui:composition>