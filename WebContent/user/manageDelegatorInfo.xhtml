<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:o="http://omnifaces.org/ui"
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
	</f:metadata>

	<ui:define name="mainContent"> 
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-user-tag"></i> Manage Delegator Info.</span>
	<h:form id="dataForm">
		<h:panelGroup id="resultPanels" style="width:100%">
		
			<p:dataTable id="staffDataTable"
				 value="#{manageDelegatorView.getStaffList()}" 
				 var="staff"
				 styleClass="default-dataTable"
				 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
				 reflow="true"
				 paginator="true"
				 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                    paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                    rows="30"
                    rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                	 tableStyle="table-layout:auto;"
				 >
				
				<p:column width="15%">
					<f:facet name="header">Photo</f:facet>
					<h:panelGroup rendered="#{manageDelegatorView.infoMap.get(staff.getStaff_number()).photoFile != null}" >
						<img src="data:image/jpg;base64,#{manageDelegatorView.infoMap.get(staff.getStaff_number()).photoFile}" width="120px"/>
					</h:panelGroup>
					<h:panelGroup rendered="#{manageDelegatorView.infoMap.get(staff.getStaff_number()).photoFile == null}" >
						<h:outputText value="Not having photo"/>
					</h:panelGroup>
				</p:column>
				
				<p:column width="3em;">
					<f:facet name="header">Name</f:facet>
					<h:outputText style="font-size:28px; font-weight:700;" value="#{staff.title} #{staff.fullname} #{staff.chinesename}"/>
					<br/>
					<h:outputText value="#{manageDelegatorView.iUserInfoStringMap.get(staff.getStaff_number())}" escape="false"/>
				</p:column>
				
				<p:column width="3em;">
					<f:facet name="header">Link</f:facet>
					<p:linkButton styleClass="default-linkButton ui-button-info"  outcome="/user/manageInfo" value="Manage Info." icon="fa fa-fw fa-user-circle" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<p:linkButton styleClass="default-linkButton ui-button-info"  outcome="/user/manageProfile" value="Manage Profile Preference" icon="fa fa-fw fa-power-off" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<br/>
					<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageOutput" value="Manage Research Outputs" icon="fa fa-fw fa-chart-pie" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageProject" value="Manage Projects" icon="fa fa-fw fa-project-diagram" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<br/>
					<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/manageAward" value="Manage Prizes and Awards" icon="fa fa-fw fa-trophy" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<p:linkButton styleClass="default-linkButton ui-button-warning"  outcome="/user/managePatent" value="Manage Patents" icon="fa fa-fw fa-award" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<br/>
					<p:linkButton styleClass="default-linkButton ui-button-success"  outcome="/user/ktFormSum" value="Manage KT Activities" icon="fa fa-fw fa-chalkboard-user" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
					<br/>
					<p:linkButton styleClass="default-linkButton ui-button-danger"  outcome="/user/manageRAEOutput" value="Manage RAE Research Outputs" icon="fa fa-fw fa-book" target="_blank" style="margin-right:5px; margin-bottom:1px;"
									rendered="#{manageDelegatorView.raeStaffMap.get(staff.getStaff_number()) eq true}">
						<f:param name="pid" value="#{staff.pid}"/>
					</p:linkButton>
				</p:column>
				
				<!--  <p:column width="3em;">
					<f:facet name="header">Staff Number</f:facet>
					<h:outputText value="#{staff.staff_number}" />
				</p:column>-->
				
			</p:dataTable>
		</h:panelGroup>
		<br/>
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>		
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>