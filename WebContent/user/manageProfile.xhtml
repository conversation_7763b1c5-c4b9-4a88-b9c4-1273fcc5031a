<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{cvDisplayView.paramPid}" />
	</f:metadata>
	<f:event listener="#{cvDisplayView.checkValid}" type="preRenderView" />

	<ui:define name="mainContent"> 	
	<p:panel id="contentPanel" rendered="#{cvDisplayView.hasAccessRight}">

	
	<span class="admin-content-title"><i class="fas fa-user-edit"></i> Manage Profile Preferences</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="dataForm">
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Preferences for Researcher CV</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<!-- 1st -->
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Research Outputs Information</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:toggleSwitch value="#{cvDisplayView.showOutput}">
						<p:ajax update="displayOrderList"/>
					</p:toggleSwitch>
				</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Sub-category of Outputs Information</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3"><p:toggleSwitch value="#{cvDisplayView.showSubCatOutput}"/></div>
			<!-- 2nd -->
				<div class="ui-g-12 ui-md-6 ui-lg-3">Citation Format</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:selectOneButton value="#{cvDisplayView.citation}" unselectable="false">
						<f:selectItem itemLabel="APA" itemValue="APA"/>
			            <f:selectItem itemLabel="MLA" itemValue="MLA"/>
			            <f:selectItem itemLabel="Chicago" itemValue="Chicago"/>
			        </p:selectOneButton>
				</div>
				
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					CV Style <a href="#{cvDisplayView.getCvLink()}" target="_blank" style="color:#186ba0;">(<i class="fas fa-link"></i> View Current CV Page)</a>
				</div>
	
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:selectOneRadio  id="style_radio" value="#{cvDisplayView.cvStyle}" layout="custom">
				            <f:selectItem itemLabel="Style 1" itemValue="1"/>        
				            <f:selectItem itemLabel="Style 2" itemValue="2"/>	                       
			         </p:selectOneRadio>
					<h:panelGrid columns="6">
			             <p:radioButton id="ref1" for="style_radio" itemIndex="0"/>		
			         	 <p:outputLabel for="ref1">
			         	  <p:commandButton for="ref1" id="style_1_img" type="button" value="Dark" icon="pi pi-image" />
						        <p:overlayPanel   for="style_1_img" showEvent="mouseover" hideEvent="mouseout" hideEffect="fade" showCloseIcon="false" >
						           <img src="../resources/image/style1.png" style="width:250 px;height:250px;"/>
						        </p:overlayPanel>
					      </p:outputLabel>
				         
					
			            <p:radioButton id="ref2" for="style_radio" itemIndex="1"/>
			            <p:outputLabel for="ref2">
			            	<p:commandButton id="style_2_img" type="button" value="#{sysParamView.getValue('BETA_VER') eq 'Y'?'Light (Beta)':'Light'}" icon="pi pi-image"/>
					        <p:overlayPanel for="style_2_img" showEvent="mouseover" hideEvent="mouseout" hideEffect="fade" showCloseIcon="false">
					           <img src="../resources/image/style2.png" style="width:250 px;height:250px;"/>
					        </p:overlayPanel>
					    </p:outputLabel>
					   
			        </h:panelGrid>				
									
				</div>
				

			</div>
			<p:orderList id="displayOrderList" value="#{cvDisplayView.displayOrderList}" var="displayList" controlsLocation="right" itemValue="#{displayList}" converter="cvOrderListConverter" responsive="true">
			<f:facet name="caption"><div style="font-size:16px; text-align: left;">Order of Information</div></f:facet>
				<p:column style="width:40px; height:20px; padding-left:10px;">
	                <i class="fa #{displayList.itemIcon}"></i>
	            </p:column>
				<p:column><h:outputText value="#{displayList.itemName}"/></p:column>
		</p:orderList>
		</p:panel>
		<br/>
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Preferences for EdUHK Research Repository</span>
				</div>
			</f:facet>
			<br/>
			<h:panelGroup id="websiteGroup">
				<div class="ui-g">
					<!-- Website Selection -->
					<div class="ui-g-12 ui-md-6 ui-lg-3">Preference of website in Research Profile of the EdUHK Research Repository
							<h:outputLink value="#{cvDisplayView.websiteLink}" target="_blank" rendered="#{cvDisplayView.showWebsite ne 'NA'}" style="color:#186ba0;">(<i class="fas fa-link"></i> View)</h:outputLink>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-9">
						<p:selectOneButton value="#{cvDisplayView.showWebsite}" unselectable="false">
							<f:selectItem itemLabel="Researcher CV" itemValue="CVPAGE"/>
				            <f:selectItem itemLabel="Homepage" itemValue="HOMEPAGE"/>
				            <f:selectItem itemLabel="Not Shown" itemValue="NA"/>
				            <p:ajax event="change" update="dataForm:websiteGroup dataForm:homepageURLTitle dataForm:homepageURL"/>
				        </p:selectOneButton>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-3"><p:outputLabel id="homepageURLTitle" value="Homepage URL" rendered="#{cvDisplayView.showWebsite eq 'HOMEPAGE'}" /></div>
					<div class="ui-g-12 ui-md-6 ui-lg-9">
						<p:message for="homepageURL"/>
						<p:inputText id="homepageURL" value="#{cvDisplayView.homepageURL}" 
										rendered="#{cvDisplayView.showWebsite eq 'HOMEPAGE'}" 
										required="#{cvDisplayView.showWebsite eq 'HOMEPAGE'}" 
										requiredMessage="Homepage URL is mandatory.">
										<p:ajax event="change" update="dataForm:websiteGroup"/>
										<f:validateLength maximum="200"/>
						</p:inputText>
					</div>
				</div>
			</h:panelGroup>
		</p:panel>	
		<br/>	
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Preferences for both Research CV and the EdUHK Research Repository</span>
				</div>
			</f:facet>
			<br/>
			<div class="ui-g">		

				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Career Overview</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:toggleSwitch value="#{cvDisplayView.showEmploymentHistory}">
						<p:ajax update="displayOrderList"/>
					</p:toggleSwitch>
				</div>
				
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Projects Information</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:toggleSwitch value="#{cvDisplayView.showProject}">
						<p:ajax update="displayOrderList"/>
					</p:toggleSwitch>
				</div>
					
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Prize and Awards Information</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
						<p:toggleSwitch value="#{cvDisplayView.showAward}">
							<p:ajax update="displayOrderList"/>
						</p:toggleSwitch>
				</div>
					
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Patents Information</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:toggleSwitch value="#{cvDisplayView.showPatent}">
						<p:ajax update="displayOrderList"/>
					</p:toggleSwitch>
				</div>
				
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show KT Activities</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<p:toggleSwitch value="#{cvDisplayView.showKt}">
						<p:ajax update="displayOrderList"/>
					</p:toggleSwitch>
				</div>
			
				
				<div class="ui-g-12 ui-md-6 ui-lg-3">Show Photo</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3"><p:toggleSwitch value="#{cvDisplayView.showPhoto}"/></div>
			</div>	
			</p:panel>
		<p:panel>		
		
		</p:panel>
		<!-- <h:outputText style="color:#1f1645" value="(The information will be updated to your EdUHK Research Repository Profile within an hour.)"/>-->
		<br/>
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:commandButton id="btn_update"  action="#{cvDisplayView.updateStaffProfileDisplayList()}" update=":dataForm :msgs" value="Update" style="margin-right:5px;">
			<p:confirm header="Confirmation" message="Are you sure to update the profile preferences?" icon="pi pi-info-circle"/>
		</p:commandButton>
		<p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
	    </p:confirmDialog>
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>