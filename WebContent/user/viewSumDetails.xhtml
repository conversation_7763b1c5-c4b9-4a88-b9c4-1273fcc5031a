<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:component="http://java.sun.com/jsf/composite/component"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	template="/resources/template/template.xhtml">

	<ui:define name="html_head">
		<link rel="stylesheet" media="screen, print" type="text/css" href="#{request.contextPath}/resources/css/bootstrap.min.css" />
		<style>
			a:link { text-decoration: none; }
			a:visited { text-decoration: none; }
			a:hover { text-decoration: none; }
			a:active { text-decoration: none; }
			
		 /*.card .ui-panel, div.ui-state-hover {
	        margin: 10px;
	        width: 730px;
	    }*/
	
		    .card .ui-dashboard-column {
		        width: 750px;
		    }
		   	 body .ui-chkbox:has(.ui-state-disabled) +label{
					font-weight:700; 
					color: #666666 !important;
					}
				.ui-chkbox .ui-chkbox-box.ui-state-disabled{
					border: 0;
				}
				.ui-state-disabled > label{
					background-color: #d1e1eb !important;
					color:#1f1645 !important;
					opacity:1.0 !important;
					font-weight:700 !important;
				}
				.ui-selectonemenu-items {
				    display: inline !important;
				}
				.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
					width: 100%;
				}
				.ui-chart{
					width: 500px;
				}
		</style>
	</ui:define>

	<f:metadata> 
		<f:viewParam name="pid" value="#{viewSumView.paramPid}" />
		<f:viewParam name="sumType" value="#{viewSumView.paramSumType}" />
		<f:viewParam name="facDept" value="#{viewSumView.paramFacDept}" />
		<f:viewParam name="facDept" value="#{viewOutputSumView.paramFacDept}" />
		<f:viewParam name="facDept" value="#{viewProjectSumView.paramFacDept}" />
		<f:viewParam name="facDept" value="#{viewAwardSumView.paramFacDept}" />
		<f:viewParam name="facDept" value="#{viewPatentSumView.paramFacDept}" />
		<f:viewParam name="facDept" value="#{viewKtSumView.paramFacDept}" />
		<f:viewParam name="admin" value="#{viewSumView.paramAdmin}"/>
		<f:viewParam name="admin" value="#{viewOutputSumView.paramAdmin}"/>
		<f:viewParam name="admin" value="#{viewProjectSumView.paramAdmin}"/>
		<f:viewParam name="admin" value="#{viewAwardSumView.paramAdmin}"/>
		<f:viewParam name="admin" value="#{viewPatentSumView.paramAdmin}"/>
		<f:viewParam name="admin" value="#{viewKtSumView.paramAdmin}"/>
		<f:viewParam name="startDate" value="#{viewSumView.paramStartDate}"/>
		<f:viewParam name="startDate" value="#{viewOutputSumView.paramStartDate}"/>
		<f:viewParam name="startDate" value="#{viewProjectSumView.paramStartDate}"/>
		<f:viewParam name="startDate" value="#{viewAwardSumView.paramStartDate}"/>
		<f:viewParam name="startDate" value="#{viewPatentSumView.paramStartDate}"/>
		<f:viewParam name="startDate" value="#{viewKtSumView.paramStartDate}"/>
		<f:viewParam name="endDate" value="#{viewSumView.paramEndDate}"/>
		<f:viewParam name="endDate" value="#{viewOutputSumView.paramEndDate}"/>
		<f:viewParam name="endDate" value="#{viewProjectSumView.paramEndDate}"/>
		<f:viewParam name="endDate" value="#{viewAwardSumView.paramEndDate}"/>
		<f:viewParam name="endDate" value="#{viewPatentSumView.paramEndDate}"/>
		<f:viewParam name="endDate" value="#{viewKtSumView.paramEndDate}"/>
		<f:viewParam name="riPeriod" value="#{viewSumView.paramRiPeriod}"/>
		<f:viewParam name="riPeriod" value="#{viewOutputSumView.paramRiPeriod}"/>
		<f:viewParam name="riPeriod" value="#{viewProjectSumView.paramRiPeriod}"/>
		<f:viewParam name="riPeriod" value="#{viewAwardSumView.paramRiPeriod}"/>
		<f:viewParam name="riPeriod" value="#{viewPatentSumView.paramRiPeriod}"/>
		<f:viewParam name="riPeriod" value="#{viewKtSumView.paramRiPeriod}"/>
		<f:viewParam name="ktPeriod" value="#{viewSumView.paramKtPeriod}"/>
		<f:viewParam name="ktPeriod" value="#{viewKtSumView.paramKtPeriod}"/>
		
		
		
		
		
	</f:metadata>
	<ui:define name="mainContent">
		<p:importConstants type="hk.eduhk.rich.Constant" var="const" />
		<p:panel id="contentPanel">
			<h:outputScript>
				$(document).ready(function(){	
					diableEnterBtn();
				});
				function diableEnterBtn(){
					$('#form_output').on('keyup keypress', function(e) {
					  var keyCode = e.keyCode || e.which;
					  if (keyCode === 13) { 
					    e.preventDefault();
					    return false;
					  }
					});	
					$('#form_project').on('keyup keypress', function(e) {
					  var keyCode = e.keyCode || e.which;
					  if (keyCode === 13) { 
					    e.preventDefault();
					    return false;
					  }
					});	
					$('#form_award').on('keyup keypress', function(e) {
					  var keyCode = e.keyCode || e.which;
					  if (keyCode === 13) { 
					    e.preventDefault();
					    return false;
					  }
					});	
					$('#form_patent').on('keyup keypress', function(e) {
					  var keyCode = e.keyCode || e.which;
					  if (keyCode === 13) { 
					    e.preventDefault();
					    return false;
					  }
					});	
					$('#form_kt').on('keyup keypress', function(e) {
					  var keyCode = e.keyCode || e.which;
					  if (keyCode === 13) { 
					    e.preventDefault();
					    return false;
					  }
					});	
				};
				function countBarChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels],
				      options: {
				         plugins: {
				         //remove the legend
			                  legend: {
			                     display: true
			                  },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
				               color: '#000'
				            }
				         },
				      
				      scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Reporting Year'
				         			}
				         		},
			                    y: {
        							  title: {
		                                display: true,
		                                text: 'Count'
		                            	},
			                        ticks: {
			                            precision: 0,
			                            min: 0
			                        }
					           }
                		}
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#000'
				            }
				         }]
				      }
			   };
			
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       
		       function weightingBarChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels],
				      options: {
				         plugins: {
				         //remove the legend
			                  legend: {
			                     display: true
			                  },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
				               color: '#000'
				            }
				         },
				      
				      scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Reporting Year'
				         			}
				         		},
			                    y: {
			                          min: 0,
        							  title: {
		                                display: true,
		                                text: 'Weighting'
		                            	}
					             }
                		}
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#000'
				            }
				         }]
				      }
			   };
			
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       
		        function barChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels],
				      options: {
				         plugins: {
				         //remove the legend
			                  legend: {
			                     display: true
			                  },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
				               color: '#000'
				            }
				         },
				      
				      scales: {
				         		x: {
				         			title :{
				         				display: true,
				         				text: 'Reporting Year'
				         			}
				         		},
			                    y: {
			                          min: 0,
        							  title: {
		                                display: true,
		                                text: 'Number'
		                            	}
					             }
                		}
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#000'
				            }
				         }]
				      }
			   };
			
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       
		       function pieChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels],
				      options: {
				         plugins: {
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
				               color: '#000'
				            }
				         }
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#000'
				            }
				         }]
				      }
			   };
			
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       
		       function exportChart(chart_form, chart_id) {
		        //export image
		        $('#form_sum_details\\:imagePanel').empty().append(PF(chart_id).exportAsImage());
				
				let header = chart_id.replace("Chart", "_header");
				
		        //show the dialog
		        PF('dlg').show();
		        PF('dlg').getJQ().find(".ui-dialog-title").text($('#'+chart_form+'\\:'+header).text());
		        
		        $(document).ready(function() {
			        $("button").click(function() {
			            PF('blockUi').show();
			        });
			
			        $(document).on('pfAjaxComplete', function() {
			            PF('blockUi').hide();
			        });
		        });
		    }
			</h:outputScript>
			<ui:include src="#{viewSumView.getSumPath()}"/>
			<h:form id="form_sum_details">
				<p:dialog widgetVar="dlg" showEffect="fade" modal="true" header="" resizable="false">
				    <p:outputPanel id="imagePanel" layout="block" style="width:500px;height:500px"/>
				</p:dialog> 
			</h:form>
		</p:panel>
	</ui:define>
</ui:composition>