<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	  	xmlns:c="http://java.sun.com/jsp/jstl/core"
		xmlns:component="http://java.sun.com/jsf/composite/component" 
		xmlns:f="http://java.sun.com/jsf/core" 
		xmlns:h="http://java.sun.com/jsf/html" 
		xmlns:p="http://primefaces.org/ui"
		xmlns:o="http://omnifaces.org/ui"
		xmlns:ui="http://java.sun.com/jsf/facelets" 
		template="/resources/template/template.xhtml">
    
    <f:metadata> 
	</f:metadata>
	
	<ui:define name="mainContent">

	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>

	<div class="ui-g">
		<p:panelGrid>
				<p:repeat value = "#{dashboardView.menuItemMap.keySet()}" var="group">
					<div>
						<div class="ui-g-12" style="color:#fff; background-color:#{group.color}; background-image: linear-gradient(to right, #{group.color} , #fff); font-size:1.5em; display: flex; justify-content: space-between;">
							<div>#{group.name}</div><div style="#{group.menu eq ''?'display:none;':''}"><a href="#{dashboardView.getSysParamDesc(group.menu)}" style="color:#{group.color}"><i class="fas fa-file-pdf"></i> User Manual</a></div>
						</div>
						<div>
							<p:repeat value="#{dashboardView.menuItemMap.get(group)}" var="item">
								<div class="ui-g-6 ui-md-3 ui-lg-2 dashboard-item">
									<div class="dashboardInner">
										<a href="..#{dashboardView.funcMap.get(item.funcId).entryUrl}" class="btn btn-sq-lg btn-primary">
										<span class="dashboard-btn-txt" style="color:#{group.color}">#{dashboardView.funcMap.get(item.funcId).name}</span><br/>
						                <i class="fa #{item.icon} fa-5x" style="#{item.iconStyle}; padding-top:7px;"></i>
						                <h:panelGroup class="circleBtn" rendered="#{dashboardView.getImportRICount(item.funcId) != null}" >
											<p:badge value="#{dashboardView.getImportRICount(item.funcId)}" size="large" severity="danger"></p:badge>
										</h:panelGroup>
										<h:panelGroup class="circleBtn" rendered="#{dashboardView.getAsstRequestedCount(item.funcId) != null}" >
											<p:badge value="#{dashboardView.getAsstRequestedCount(item.funcId)}" size="large" severity="danger"></p:badge>
										</h:panelGroup>
										<h:panelGroup class="circleBtn" rendered="#{dashboardView.getUnconfirmRICount(item.funcId) != null}" >
											<p:badge value="#{dashboardView.getUnconfirmRICount(item.funcId)}" size="large" severity="danger"></p:badge>
										</h:panelGroup>
										<h:panelGroup rendered="#{item.funcId eq 'VIEW_MY_SUMMARY' or item.funcId eq 'EXPORT_MY_DATA'}" >
											#{sysParamView.getValue('BETA_VER') eq 'Y'?'(Beta)':''}
										</h:panelGroup>
										</a>
									</div>
								</div>
							</p:repeat>
						</div>
					</div>
				</p:repeat>
				<p:row><p:column style="border:0px solid #fff;"></p:column></p:row>
		</p:panelGrid>
	</div>
	
	</ui:define>
</ui:composition>