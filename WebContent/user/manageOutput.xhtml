<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{manageOutputView.paramPid}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-chart-pie"></i> Manage Research Outputs</span>
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.selectedSecFuncLock.lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageRIView.canModify == false}"/>
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="dataForm" rendered="#{manageOutputView.canViewRIList() == true}">
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:linkButton outcome="manageOutput_edit" value="New" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageRIView.canModify}">
			<f:param name="pid" value="#{manageOutputView.paramPid}"/>
		</p:linkButton>	
		<p:commandButton oncomplete="PF('externalSourceSideBar').show()" value="Import from External Source" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageRIView.canModify}"></p:commandButton>	
		<br/>
		<br/>
		<p:dataTable id="dataTable" var="data" value="#{manageOutputView.outputList}" sortBy="#{data.outputHeader_p.output_lookup_code}" stripedRows="true"
						tableStyle="table-layout: fixed;">
			<p:headerRow field="outputHeader_p.output_lookup_code" expandable="true">
				<p:column colspan="3" style="background:#c1def5; color: #186ba0;">
					<div style="vertical-align: middle; font-size:16px; display:contents;">
                	<h:outputText value="#{manageOutputView.getSelectedOutputCatDesc(data.outputHeader_p.output_lookup_code)}"/>
                	</div>
                </p:column>
            </p:headerRow>   
			<p:column headerText="Research Output" id="output" style="width:70%;">
                <p:outputLabel value="#{data.outputHeader_p.apa_html}" style="#{data.outputDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}" escape="false"></p:outputLabel>
            </p:column>   
 			<p:column headerText="Status" id="status" style="text-align: center;">
                <h:outputText style="font-weight:700; color:#1B5E20;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{data.outputDetails_q.creator_ind == 'Y' &amp;&amp; data.outputHeader_q.publish_status == 'PUBLISHED' &amp;&amp; data.outputDetails_q.display_ind == 'Y'}"/>
                <h:outputText style="font-weight:700; color:#D50000;" value="#{formBundle['form.ri.status.lv.m']}" rendered="#{data.outputDetails_q.creator_ind == 'Y' &amp;&amp; data.outputHeader_q.publish_status == 'MODIFIED' &amp;&amp; data.outputDetails_q.display_ind == 'Y'}"/>
                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.outputDetails_q.creator_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind != 'U' &amp;&amp; data.outputDetails_q.display_ind == 'Y'}"/>
                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.outputDetails_q.creator_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind == 'U'}"/>
                 <h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.outputDetails_q.display_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind ne 'U'}"/>
            </p:column>    
  			<p:column headerText="Action" id="action" style="text-align: center;">
  				<p:linkButton id="btn_modify" outcome="manageOutput_edit" rendered="#{data.outputDetails_q.creator_ind == 'Y' &amp;&amp; manageRIView.canModify}" value="Modify" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
  					<f:param name="no" value="#{data.pk.output_no}"/>
  					<f:param name="dataLevel" value="M"/>
  				</p:linkButton>	
  				<p:linkButton id="btn_view" outcome="manageOutput_edit" rendered="#{data.outputDetails_q.creator_ind == 'N' || manageRIView.canModify == false}" value="View" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
  					<f:param name="no" value="#{data.pk.output_no}"/>
  					<f:param name="dataLevel" value="M"/>
  				</p:linkButton>	
  				<p:linkButton id="btn_consent" outcome="manageOutput_edit" rendered="#{data.outputDetails_q.creator_ind == 'N' &amp;&amp; data.outputDetails_q.consent_ind == 'U' &amp;&amp; manageRIView.canModify}" value="Consent" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{manageOutputView.getParamPid()}"/>
  					<f:param name="no" value="#{data.pk.output_no}"/>
  					<f:param name="dataLevel" value="M"/>
  				</p:linkButton>	
            </p:column>      
            <p:summaryRow>
                <p:column colspan="2" style="text-align:right;">
                    <h:outputText value="Total Research Outputs:"/>
                </p:column>
                <p:column >
                    <h:outputText value="#{manageOutputView.getTotalCount(data.outputHeader_p.output_lookup_code)}"/>
                </p:column>
            </p:summaryRow>                 
		</p:dataTable>
		
	</h:form>
	
	<p:sidebar id="sideBar" styleClass="supplForm-select-sideBar" widgetVar="externalSourceSideBar" position="right" >
		 <h:form id="sideBarForm">
			
			<div class="title">
		    	<h:outputText value="External Sources (New Outputs)"/>
			</div>
			<component:importRIOutput importRIOutput="#{manageOutputView.outputPanel}"
									  update=":sideBarForm"/>
		</h:form> 	
	</p:sidebar>
	<h:form id="ignoreConfirmForm">
		<p:confirmDialog id="ignoreOutputConfirm" widgetVar="ignoreOutputConfirmWidget" 
						 header="Confirm ignore?" appendToBody="true"
						 message="Are you sure to ignore research output with ID: #{manageOutputView.outputPanel.getSelectedIgnoreOutput().source_id}?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 update=":sideBarForm"
							 actionListener="#{manageOutputView.ignoreOutput()}">
				<p:ajax event="click" oncomplete="PF('ignoreOutputConfirmWidget').hide()" update=":sideBarForm"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('ignoreOutputConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
	</h:form> 
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>