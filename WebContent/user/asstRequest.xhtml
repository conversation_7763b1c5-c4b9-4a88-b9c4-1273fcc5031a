<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-check-circle"></i> Apply Assistant Right</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="requestForm">
		<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:outputLabel value="Academic Staff:" style="color:#666; font-weight:700; font-size:18px;"/><br/>
					<p:outputLabel value="Please select an Academic Staff and click 'Request' to request the application." style="color:#444; font-weight:400; font-size:14px;"/>
				</div>
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:selectOneMenu id="acadStaff" value="#{asstView.selectedAcadStaff}" required="true"
										 filter="true" filterMatchMode="contains" caseSensitive="false"
										 editable="false" requiredMessage="Please select an academic staff.">
						<f:selectItem itemLabel="Please select one" itemValue=""/>
						<f:selectItems value="#{asstView.acadStaffList}" var="s" 
										 itemLabel="#{s.fullname} (#{s.email})" itemValue="#{s.pid}"/>
					</p:selectOneMenu> 
				</div>
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:commandButton value="#{bundle['action.request']}"
									  		 action="#{asstView.updateRequestAsst()}"
									  		 update=":dataForm :requestForm :msgs">
							<p:confirm header="Confirmation" message="Are you sure to request?" icon="pi pi-exclamation-triangle"/>
					</p:commandButton>
			</div>
		</div>

	</h:form>
	<br/><span class="admin-content-title"><i class="fas fa-database"></i> Request Record</span>
	<h:form id="dataForm">
		<p:dataTable id="dataTable" var="data" value="#{asstView.acadStaffListByAsst}" sortMode="single"
						rowKey="#{data.pk.pid}" tableStyle="table-layout: fixed;"
						selection="#{asstView.selectedRecord}" selectionMode="single"
                     	widgetVar="dataWidget">

			<p:column headerText="Academic Staff" id="acadStaffName" sortBy="#{asstView.getAcadStaffName(data.pk.pid)}">
                <h:outputText value="#{asstView.getAcadStaffName(data.pk.pid)}"/>
            </p:column>
            <p:column headerText="Status" id="status" sortBy="#{data.status}">
                <h:outputText value="#{data.status}"/>
            </p:column>            
           <p:column headerText="Requested date" id="requestedDate" sortBy="#{data.creationDate}">
                <h:outputText value="#{data.creationDate}">
                	<f:convertDateTime pattern="dd-MM-yyyy HH:mm" />
                </h:outputText>
            </p:column>  
            <p:column headerText="Last updated date" id="lastUpdatedDate" sortBy="#{data.timestamp}">
                <h:outputText value="#{data.timestamp}">
                	<f:convertDateTime pattern="dd-MM-yyyy HH:mm" />
                </h:outputText>
            </p:column>    
             <p:column headerText="Action">
                <p:commandButton id="btn_remove" action="#{asstView.removeAsstRequest(data)}" rendered="#{data.status == 'REQUESTED'}" update=":dataForm :msgs" icon="pi pi-times" styleClass="ui-button-danger" title="Delete">
                	<p:confirm header="Confirmation" message="Are you sure to delete the request?" icon="pi pi-exclamation-triangle"/>
                </p:commandButton>
            </p:column>   
        </p:dataTable>
         <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		 </p:confirmDialog>
	</h:form>
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>