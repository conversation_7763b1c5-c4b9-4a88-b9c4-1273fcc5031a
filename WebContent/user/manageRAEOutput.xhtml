<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{raeOutputView.paramPid}" />
	</f:metadata>
	<ui:define name="html_head">
		<p:importConstants type="hk.eduhk.rich.Constant" var="Constant"/>
		<script type="text/javascript" src="#{request.contextPath}/resources/js/jquery.chineselengthlimit.js"></script>
		<style>
			.ui-selectonemenu-item.ui-state-disabled {
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
	<h:outputScript>
	$(document).ready(function(){
		 triggerCountWords();
	});
	
	function triggerCountWords(){
		$('.countWords').trigger('keyup');
	}
	
	function countWords(val, num) {
	    var text = val.value.trim();
	    var attrId = val.getAttribute("name") + '_countWords';
		var words = text.match(/[\w’'-]+(?:-[\w’'-]+)*/g) || [];
		
		// Total word count
	    var totalWords = words.length;
	    
	    // Get the element where the word count will be displayed
	    var wordCountElement = document.getElementById(attrId);
	
	    if (totalWords > num) {
	        // If the word count exceeds the limit, display a warning
	        wordCountElement.style.color = "red";
	        wordCountElement.innerText = "Word limit exceeded! You have " + (totalWords - num) + " word(s) over the limit.";
	    } else {
	        // If within the limit, display the remaining word count
	        wordCountElement.style.color = "#495057";
	        wordCountElement.innerText = "Words remaining: " + (num - totalWords) + " word(s)";
	    }
	}
	</h:outputScript>
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-chart-pie"></i> Manage RAE Research Outputs</span>
	<p:staticMessage severity="warn" summary="Important Note" detail="#{sysParamView.getValue('RAE_NOTE')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;"/>
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.getExRi(manageRIView.lockGrp).lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageRIView.canModifyRae eq false}"/>
	<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
	<h:form id="dataForm" rendered="#{raeOutputView.canViewRAEList() == true}">
		<br/>
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:linkButton outcome="manageRAEOutput_edit" value="New Output" icon="pi pi-plus" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageRIView.canModifyRae}">
			<f:param name="pid" value="#{raeOutputView.paramPid}"/>
		</p:linkButton>	
		<p:commandButton oncomplete="PF('externalSourceSideBar').show()" value="Fill from Existing RI in RICH" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageRIView.canModifyRae}"></p:commandButton>
		
		<p:commandButton  action="#{raeOutputView.submit}" 
							oncomplete="window.scrollTo(0,0);" 
							value="Submit" style="margin-right:5px; 
							margin-bottom:1px;" 
							rendered="#{manageRIView.canModifyRae}"
							update="@form messages" >
		</p:commandButton>	
		<br/>
		<br/>
		<div class="ui-g">
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Name" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.staffTitle} #{raeOutputView.selectedRaeStaff.staffName}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Faculty/ Department" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.faculty}/ #{raeOutputView.selectedRaeStaff.department}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Unit of Assessment" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.uoaInfo}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="No. of Outputs to be Submitted" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel class="raeForm-item-ans" value="#{raeOutputView.selectedRaeStaff.outSubNoStr}"  />
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Total No. of Outputs Selected" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel id="totalSelCount" class="raeForm-item-ans" value="#{raeOutputView.getOutputCount(raeOutputView.selectedRaeStaff.staffNumber)}" />
			</div>
			
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Double-weighted" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-4">
				<p:outputLabel id="dbCount" class="raeForm-item-ans" value="#{raeOutputView.getDWCount(raeOutputView.selectedRaeStaff.staffNumber)}" />
			</div>
			<div class="ui-g-12 ui-md-3 ui-lg-2">
				<p:outputLabel class="raeForm-item-title" value="Remarks" />
			</div>
			<div class="ui-g-12 ui-md-9 ui-lg-10">
				<p:message for="selRemark"/>
					<p:inputTextarea id="selRemark" label="Remarks" title="Remarks" style="width: 100%;" rows="2" counter="selRemark_display" maxlength="1000" 
											value="#{raeOutputView.selectedRaeStaff.selRemark}"
											disabled="#{manageRIView.canModifyRae eq false}"
		                      				counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
		            <br/>
		    		<h:outputText id="selRemark_display" class="p-d-block" style="font-size:12px"/>
			</div>
		</div>
		<p:dataTable id="dataTable" var="data" value="#{raeOutputView.outputStatusList}" sortBy="#{data.raeOutputHeader.output_lookup_code}" stripedRows="true"
						tableStyle="table-layout: fixed;">
			<p:headerRow field="raeOutputHeader.output_lookup_code" expandable="true">
				<p:column colspan="7" style="background:#c1def5; color: #186ba0;">
					<div style="vertical-align: middle; font-size:16px; display:contents;">
                	<h:outputText value="#{raeOutputView.getSelectedOutputCatDesc(data.raeOutputHeader.output_lookup_code)}"/>
                	</div>
                	
                </p:column>
            </p:headerRow>   
            <!-- Selection Type -->
            <p:column headerText="Selection Type" id="select_rae" style="text-align: center; vertical-align: top;">
            	<p:selectOneMenu id="select_rae_option" value="#{data.selectType}" 
            					disabled="#{manageRIView.canModifyRae eq false || data.infoComp eq 'N'}">
                    <f:selectItems value="#{raeOutputView.selectTypeList}"/>
                    <p:ajax event="change" update="panel_dbl_wt_just"/> 
                    <p:ajax event="change" oncomplete="triggerCountWords()"/>
                </p:selectOneMenu>
                <!-- Interdisciplinary -->
               	<!-- <h:panelGroup id="panel_interDisp" style="display:block">
               		<p:message for="inter_disp_ind"/>
                	<p:selectBooleanCheckbox id="inter_disp_ind" value="#{data.interDisp}" itemLabel="Interdisciplinary" disabled="#{manageRIView.canModifyRae eq false || data.infoComp eq 'N'}"
                		class="raeForm-item-title" rendered="#{data.selectType ne 'NS'}">
                		<p:ajax event="change" update="panel_dbl_wt_just"/> 
                	</p:selectBooleanCheckbox>
                </h:panelGroup> -->
            </p:column> 
			<p:column headerText="Research Output" id="output" style="width:48%; vertical-align: top;">
				<!-- APA -->
                <p:outputLabel value="#{data.raeOutputHeader.apa_html}" escape="false"></p:outputLabel>
                <p:outputLabel value="(Book / Journal Title: #{data.raeOutputHeader.book_title})" escape="false" rendered="#{data.raeOutputHeader.apa_html eq null}"></p:outputLabel>
                <h:panelGroup id="panel_dbl_wt_just" layout="block" class="ui-g-12 ui-md-12 ui-lg-12">

					
					<!-- Justification -->
                	<p:outputLabel value="Justification (Max. 100 words)" rendered="#{data.selectType eq 'DW1' || data.selectType eq 'DW2'}" 
                					class="raeForm-item-title"/>
                	<p:outputLabel value="Please explain why the output merits double-weighting. E.g. How the research output (e.g. its scale or scope) required research effort equivalent to that required to produce two or more single outputs?" 
                					rendered="#{data.selectType eq 'DW1' || data.selectType eq 'DW2'}"
                					class="raeForm-item-title"
                					style="color:#0277BD; display:block;"/>
                	<p:message for="dbl_wt_just"/>
					<p:inputTextarea id="dbl_wt_just" label="Justification (Max. 100 words)" title="Justification (Max. 100 words)" 
											style="width: 100%;" rows="2" maxlength="2000" class="countWords"
											rendered="#{data.selectType eq 'DW1' || data.selectType eq 'DW2'}"
											disabled="#{manageRIView.canModifyRae eq false}"
											value="#{data.justifications}"
											onkeyup="countWords(this, 100)"
											onload="countWords(this, 100)"
		                      				autoResize="false"/>
		            <br/>
		    		<h:outputText id="dbl_wt_just_countWords" class="p-d-block" style="font-size:12px"/>
                 </h:panelGroup>
            </p:column>
            
            <p:column headerText="JCR 2022" id="jcr" style="text-align: center; width:4%; vertical-align: top;">
                <h:outputText style="font-weight:700; color:#1B5E20;" value="#{data.jcr}"/>
            </p:column> 
            
            <p:column headerText="SJR 2022" id="sjr" style="text-align: center; width:4%; vertical-align: top;">
                <h:outputText style="font-weight:700; color:#1B5E20;" value="#{data.sjr}"/>
            </p:column> 
            
           <p:column headerText="Internal RAE Rating" id="rate" style="text-align: center; width:4%; vertical-align: top;">
                <h:outputText style="font-weight:700; color:#1B5E20;" value="#{data.raeOutputHeader.int_rae_rate}"/>
            </p:column>       
            
 			<p:column headerText="Completion Status" id="status" style="text-align: center; width:6%; vertical-align: top; padding-top: 20px;">
                <h:outputText style="background: red; color:#fff; font-weight:600; padding:5px; border-radius:4px;" value="Incomplete" rendered="#{data.infoComp eq 'N'}"/>
                <h:outputText style="background: green; color:#fff; font-weight:600; padding:5px; border-radius:4px;" value="Completed" rendered="#{data.infoComp eq 'Y'}"/>
            </p:column>
            
  			<p:column headerText="Action" id="action" style="text-align: center; width:6%; vertical-align: top;">
  				<p:linkButton id="btn_modify" value="#{manageRIView.canModifyRae? 'Modify':'View'}" outcome="manageRAEOutput_edit" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{raeOutputView.getParamPid()}"/>
  					<f:param name="no" value="#{data.pk.output_no}"/>
  				</p:linkButton>	
            </p:column>      
            <p:summaryRow>
                <p:column colspan="2" style="text-align:right;">
                    <h:outputText value="Total Research Outputs:"/>
                </p:column>
                <p:column >
                    <h:outputText value="#{raeOutputView.getTotalCount(data.raeOutputHeader.output_lookup_code)}"/>
                </p:column>
            </p:summaryRow>                 
		</p:dataTable>
		<br/>
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:commandButton  action="#{raeOutputView.submit}" 
							oncomplete="window.scrollTo(0,0);" 
							value="Submit" style="margin-right:5px; 
							margin-bottom:1px;" 
							rendered="#{manageRIView.canModifyRae}"
							update="@form messages" >
		</p:commandButton>	
	</h:form>
	
	<p:sidebar id="sideBar" styleClass="supplForm-select-sideBar" widgetVar="externalSourceSideBar" position="right" >
		 <h:form id="sideBarForm">
			
			<div class="title">
		    	<h:outputText value="Existing RI in RICH"/>
			</div>
			<component:importRAEOutput importRaeOutput= "#{raeOutputView.getOutputPanel()}"
									  update=":sideBarForm"/>
			
		</h:form> 	
	</p:sidebar>
	
	
	</p:panel>
   </ui:define>
</ui:composition>