<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{manageProjectView.paramPid}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-project-diagram"></i> Manage Projects</span>
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.selectedSecFuncLock.lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageRIView.canModify == false}"/>
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="dataForm" rendered="#{manageProjectView.canViewRIList() == true}">
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:linkButton outcome="manageProject_edit" value="New" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageRIView.canModify}">
			<f:param name="pid" value="#{manageProjectView.paramPid}"/>
		</p:linkButton>	
		<p:commandButton oncomplete="PF('externalSourceSideBar').show()" value="Import from External Source" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageRIView.canModify}"></p:commandButton>	
		<br/><br/>
		<p:dataTable id="dataTable" var="data" value="#{manageProjectView.projectList}" sortMode="single" reflow="true" stripedRows="true"
						rowKey="#{data.pk}" tableStyle="table-layout: fixed;">
			<p:column headerText="Title" id="projTitle" sortBy="#{data.projectHeader_p.title_1}" style="width:25%; vertical-align: top;">
                <h:outputText value="#{data.projectHeader_p.title_1} #{data.projectHeader_p.title_2} #{data.projectHeader_p.title_3} #{data.projectHeader_p.title_4}" style="#{data.projectDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}"/>
            </p:column>   
 			<p:column headerText="Summary" id="summary" sortBy="#{data.projectHeader_p.project_summary}" style="width:30%; vertical-align: top;">
                <h:outputText value="#{data.projectHeader_p.project_summary}" style="#{data.projectDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}"/>
            </p:column>  
            <p:column headerText="Start Year" id="year" sortBy="#{data.projectHeader_p.from_year}" style="vertical-align: top;">
                <div class="left-align-column" ><h:outputText value="#{data.projectHeader_p.from_year}" style="#{data.projectDetails_q.display_ind == 'N'?'color:#888;':'color:#186ba0;'}"/></div>
            </p:column>  
            <p:column headerText="Status" id="status" sortBy="#{data.projectHeader_q.publish_status}" style="vertical-align: top;">
            	<div class="left-align-column" >
                <h:outputText style="font-weight:700; color:#1B5E20;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{data.projectDetails_q.creator_ind == 'Y' &amp;&amp; data.projectHeader_q.publish_status == 'PUBLISHED' &amp;&amp; data.projectDetails_q.display_ind == 'Y'}"/>
                <h:outputText style="font-weight:700; color:#D50000;" value="#{formBundle['form.ri.status.lv.m']}" rendered="#{data.projectDetails_q.creator_ind == 'Y' &amp;&amp; data.projectHeader_q.publish_status == 'MODIFIED' &amp;&amp; data.projectDetails_q.display_ind == 'Y'}"/>
                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.projectDetails_q.creator_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind != 'U' &amp;&amp; data.projectDetails_q.display_ind == 'Y'}"/>
                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.projectDetails_q.creator_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind == 'U'}"/>
            	<h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.projectDetails_q.display_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind ne 'U'}"/>
            	</div>
            </p:column>         
  			<p:column headerText="Action" id="action" style="vertical-align: top;">
  				<div class="left-align-column" >
  				<p:linkButton id="btn_modify" outcome="manageProject_edit" rendered="#{data.projectDetails_q.creator_ind == 'Y' &amp;&amp; manageRIView.canModify}" value="Modify" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
  					<f:param name="no" value="#{data.pk.project_no}"/>
  					<f:param name="dataLevel" value="M"/>
  				</p:linkButton>	
  				<p:linkButton id="btn_view" outcome="manageProject_edit" rendered="#{data.projectDetails_q.creator_ind == 'N' || manageRIView.canModify == false}" value="View" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
  					<f:param name="no" value="#{data.pk.project_no}"/>
  					<f:param name="dataLevel" value="M"/>
  				</p:linkButton>	
  				<p:linkButton id="btn_consent" outcome="manageProject_edit" rendered="#{data.projectDetails_q.creator_ind == 'N' &amp;&amp; data.projectDetails_q.consent_ind == 'U' &amp;&amp; manageRIView.canModify}" value="Consent" style="margin-right:5px; margin-bottom:5px;">
  					<f:param name="pid" value="#{manageProjectView.paramPid}"/>
  					<f:param name="no" value="#{data.pk.project_no}"/>
  					<f:param name="dataLevel" value="M"/>
  				</p:linkButton>	
  				</div>
            </p:column>                              
		</p:dataTable>
		
	</h:form>
	<p:sidebar id="sideBar" styleClass="supplForm-select-sideBar" widgetVar="externalSourceSideBar" position="right" >
		<h:form id="sideBarForm">
			
			<div class="title">
		    	<h:outputText value="External Sources (New Projects)"/>
			</div>
			<component:importRIProject importRIProject="#{manageProjectView.projectPanel}"
									  update=":sideBarForm"/>
		</h:form>
	</p:sidebar>
	<h:form id="ignoreConfirmForm">
		<p:confirmDialog id="ignoreProjectConfirm" widgetVar="ignoreProjectConfirmWidget" 
						 header="Confirm ignore?" appendToBody="true"
						 message="Are you sure to ignore project with ID: #{manageProjectView.projectPanel.getSelectedIgnoreProject().source_id}?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 update=":sideBarForm"
							 actionListener="#{manageProjectView.ignoreProject()}">
				<p:ajax event="click" oncomplete="PF('ignoreProjectConfirmWidget').hide()" update=":sideBarForm"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('ignoreProjectConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
	</h:form>
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>