<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-address-card"></i> Manage Individual Info.</span>
	
	<h:form id="dataForm">
		<p:dataTable id="dataTable" var="data" value="#{asstView.approvedAcadStaffListByAsst}" sortMode="single"
						rowKey="#{data.pk.pid}" tableStyle="table-layout: fixed;">
			<p:column headerText="Delegated by" id="acadStaffName" sortBy="#{asstView.getAcadStaffName(data.pk.pid)}">
                <h:outputText value="#{asstView.getAcadStaffName(data.pk.pid)}"/>
            </p:column>
            <p:column headerText="Manage" id="manage">
                <p:linkButton outcome="manageInfo" value="Manage Individual Info." icon="pi pi-user-edit" style="margin-right:20px;">
                	<f:param name="pid" value="#{data.pk.pid}"/>
                </p:linkButton>	
            </p:column>
         </p:dataTable>
    </h:form>
    <br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>