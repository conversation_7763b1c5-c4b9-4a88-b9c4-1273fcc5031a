<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{manageKtSumView.paramPid}" />
		<f:viewParam name="period" value="#{manageKtSumView.paramPeriod}" />
		<f:viewParam name="form" value="#{manageKtSumView.paramFormCode}" />
		<f:viewParam name="admin" value="#{manageKtSumView.paramAdmin}" />
		<f:viewParam name="facDept" value="#{manageKtSumView.selectedFacDept}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<style type="text/css">
		    .ui-datatable-header {
		        background: #186ba0 !important;
		        color: #fff !important;
		    }
		    .btn_action{
		    	margin-right:5px !important; 
		    	margin-bottom:5px !important;
		    	width:90%;
		    }
		    .btn_copy{
		    	<!--display:none;-->
		    }
		    .btn_del{
		    	<!--display:none;-->
		    }
		</style>	
	<span class="admin-content-title"><i class="fa-solid fa-chalkboard-user"></i> Form #{manageKtSumView.selectedForm.form_short_desc} #{manageKtSumView.selectedForm.form_full_desc}<pre style="margin: 0;"><span style="font-size:12px; color:#666; font-weight:400; ">#{manageKtSumView.selectedForm.form_brief}</span></pre></span>
	
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.getExKtByDept(manageKtSumView.selectedFacDept).lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageKtSumView.canModifyKt == false}"/>
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="dataForm" rendered="#{manageKtSumView.canViewKtActList() == true}">
		<p:linkButton outcome="ktFormSum" value="Back to Summary" icon="pi pi-arrow-left" styleClass="btn-back"
					  rendered="#{manageKtSumView.paramAdmin != 'Y'}">
			<f:param name="pid" value="#{manageKtSumView.paramPid}"/>		  
			<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
		</p:linkButton>	
		<p:linkButton outcome="ktFormSum" value="Back to Summary" icon="pi pi-arrow-left" styleClass="btn-back"
					  rendered="#{manageKtSumView.paramAdmin == 'Y'}">
			<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
			<f:param name="admin" value="#{manageKtSumView.paramAdmin}" />
			<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" />
		</p:linkButton>	
		<p:linkButton outcome="ktForm" value="New" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageKtSumView.canModifyKt and manageKtSumView.paramAdmin != 'Y'}">
			<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
			<f:param name="form" value="#{manageKtSumView.paramFormCode}"/>
			<f:param name="dataLevel" value="M"/>
			<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
		</p:linkButton>	
		<p:linkButton outcome="ktForm" value="New" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageKtSumView.canModifyKt and manageKtSumView.paramAdmin == 'Y'}">
			<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
			<f:param name="form" value="#{manageKtSumView.paramFormCode}"/>
			<f:param name="dataLevel" value="N"/>
			<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
			<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" />
		</p:linkButton>	
		<h:panelGroup rendered="#{manageKtSumView.paramAdmin == 'Y'}">
			<p:commandButton oncomplete="PF('collectFromStaffSideBar').show()" value="Collect From Staff" style="margin-right:5px; margin-bottom:1px;"></p:commandButton>
		</h:panelGroup>
		<h:panelGroup rendered="#{manageKtSumView.paramAdmin != 'Y' and 
								(manageKtSumView.paramFormCode == 'KT_CNT_PROJ' 
								or manageKtSumView.paramFormCode == 'KT_INNOVATION' 
								or manageKtSumView.paramFormCode == 'KT_CONSULT')}">
			<p:commandButton oncomplete="PF('importFromProjectSideBar').show()" value="Import From Project" style="margin-right:5px; margin-bottom:1px;"></p:commandButton>
		</h:panelGroup>
		<br/>
		<p:toolbar style="border:0px solid #fff; padding:0;">
			<p:toolbarGroup>
				<div style="display: flex; align-items: center;">
					<div style="display: inline-block;">
					<p:outputLabel for="@next" value="Reporting Period" style="color:#4c6f89; font-weight:700; margin-right:7px;" />
					</div>
					<div style="display: inline-block;">
						<p:selectOneMenu id="period" title="Reporting Period" label="Reporting Period" value="#{manageKtSumView.selectedPeriod}" style="margin-top:4px; margin-right:20px;">
								<f:selectItems value="#{manageKtSumView.periodList}" var="o" 
													itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />
								<p:ajax event="change" update="btn_period btn_dept"/>		
						</p:selectOneMenu>	
					</div>
					<h:panelGroup rendered="#{manageKtSumView.paramAdmin != 'Y'}">
						<div style="display: inline-block;">
							<p:linkButton id="btn_period" outcome="manageKtForm" value="Go" styleClass="ui-button-raised ui-button-success">
								<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
								<f:param name="form" value="#{manageKtSumView.paramFormCode}"/>
								<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
					    	</p:linkButton>
						</div>
					</h:panelGroup>
				</div>
				<h:panelGroup rendered="#{manageKtSumView.paramAdmin == 'Y'}">
					<br/>
					<div style="display: flex; align-items: center;">
						<div style="display: inline-block;">
						<p:outputLabel for="@next" value="Faculty/ Department/ Centre" style="color:#4c6f89; font-weight:700; margin-right:7px; margin-left:20px;" />
						</div>
						<div style="display: inline-block;">
						<p:selectOneMenu id="facDept" title="Faculty/ Department/ Centre" label="Faculty" value="#{manageKtSumView.selectedFacDept}" style="margin-top:4px;margin-right:20px;">
								<f:selectItems value="#{manageKtSumView.facDeptList}"/>
								<p:ajax event="change" update="facDept btn_dept"/>		
						</p:selectOneMenu>	
						</div>
						<div style="display: inline-block;">
							<p:linkButton id="btn_dept" outcome="manageKtForm" value="Go" styleClass="ui-button-raised ui-button-success">
								<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
								<f:param name="form" value="#{manageKtSumView.paramFormCode}"/>
								<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
								<f:param name="admin" value="#{manageKtSumView.paramAdmin}" />
								<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" />
					    	</p:linkButton>
						</div>
					</div>
				</h:panelGroup>
			</p:toolbarGroup>
			<p:toolbarGroup align="right">
                <p:commandButton value="Export to Excel" icon="fas fa-download" ajax="false" immediate="true" rendered="#{manageKtSumView.formSubList.size() > 0}" style="margin-bottom:4px;">
            	 	<p:dataExporter type="xlsx" target="dataTable" fileName="formExport" />
            	 </p:commandButton>
            </p:toolbarGroup>
        </p:toolbar>
        <h:panelGroup>
			<ui:insert name="#{manageKtFormView.paramFormCode}">
				<ui:decorate template="../resources/component/ktForm/#{manageKtSumView.paramFormCode}_list.xhtml">
				</ui:decorate>
			</ui:insert>
		</h:panelGroup>
        <!--  <h:panelGroup rendered="#{manageKtSumView.paramAdmin != 'Y'}">
			<ui:insert name="#{manageKtFormView.paramFormCode}">
				<ui:decorate template="../resources/component/ktForm/#{manageKtSumView.paramFormCode}_list.xhtml">
				</ui:decorate>
			</ui:insert>
		</h:panelGroup>
		<h:panelGroup rendered="#{manageKtSumView.paramAdmin == 'Y'}">
			<ui:insert name="#{manageKtFormView.paramFormCode}">
				<ui:decorate template="../resources/component/ktForm/#{manageKtSumView.paramFormCode}_dept_list.xhtml">
				</ui:decorate>
			</ui:insert>
		</h:panelGroup>-->
	</h:form>
	<p:sidebar id="sideBar" styleClass="supplForm-select-sideBar" widgetVar="collectFromStaffSideBar" position="right" >
		<h:form id="sideBarForm">
			
			<div class="title">
		    	<h:outputText value="Collect From Staff Form #{manageKtSumView.selectedForm.form_short_desc} #{manageKtSumView.selectedForm.form_full_desc}"/>
		    	<br/>
		    	<h:outputText value="#{manageKtSumView.getSideBarTitle()}"/>
			</div>
			<ui:insert name="#{manageKtFormView.paramFormCode}_import">
				<ui:decorate template="../resources/component/ktForm/#{manageKtSumView.paramFormCode}_import.xhtml">
				</ui:decorate>
			</ui:insert>
		</h:form>
	</p:sidebar>
	<p:sidebar id="importProjSideBar" styleClass="supplForm-select-sideBar" widgetVar="importFromProjectSideBar" position="right" >
		<h:form id="importProjSideBarForm">
			
			<div class="title">
		    	<h:outputText value="Import From Project"/>
			</div>
			<ui:insert name="#{manageKtFormView.paramFormCode}_import">
				<ui:decorate template="../resources/component/importKTProject.xhtml">
				</ui:decorate>
			</ui:insert>
		</h:form>
	</p:sidebar>
	<h:form id="collectConfirmForm">
		<p:confirmDialog id="collectConfirm" widgetVar="collectConfirmWidget" 
						 header="Confirm collect?" appendToBody="true"
						 message="Are you sure to collect from staff these #{manageKtSumView.importKTPanel.getCollectNum()} information(s)?"
						 style="white-space: pre;">
		
			<p:commandButton value="#{bundle['action.ok']}"
							 actionListener="#{manageKtSumView.importKTPanel.importKt()}"
							 process="@this"
							 ajax="false">
				<p:ajax event="click" oncomplete="PF('collectConfirmWidget').hide()" update=":sideBarForm"/>
			</p:commandButton>
	
			<p:commandButton value="#{bundle['action.cancel']}" onclick="PF('collectConfirmWidget').hide()" type="button"/>
		
		</p:confirmDialog>
	</h:form>

		<br/>
		<p:linkButton outcome="ktFormSum" value="Back to Summary" icon="pi pi-arrow-left" styleClass="btn-back"
		  			  rendered="#{manageKtSumView.paramAdmin != 'Y'}">
		  	<f:param name="pid" value="#{manageKtSumView.paramPid}"/>		  
			<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
		</p:linkButton>
		<p:linkButton outcome="ktFormSum" value="Back to Summary" icon="pi pi-arrow-left" styleClass="btn-back"
					  rendered="#{manageKtSumView.paramAdmin == 'Y'}">
			<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
			<f:param name="admin" value="#{manageKtSumView.paramAdmin}" />
			<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" />
		</p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>