<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	
	<f:metadata>
		<f:viewParam name="pid" value="#{viewSumView.paramPid}" />
		<f:viewParam name="admin" value="#{viewSumView.paramAdmin}" />
		<f:viewParam name="facDept" value="#{viewSumView.paramFacDept}" />
		<f:viewParam name="startDate" value="#{viewSumView.paramStartDate}" />
		<f:viewParam name="endDate" value="#{viewSumView.paramEndDate}" />
		<f:viewParam name="riPeriod" value="#{viewSumView.paramRiPeriod}" />
		<f:viewParam name="ktPeriod" value="#{viewSumView.paramKtPeriod}" />
		<f:event listener="#{viewSumView.updateChart(true)}" type="preRenderView"/>
	</f:metadata>
	<ui:define name="html_head">
		<style>
		    <!--.card .ui-panel, div.ui-state-hover {
		        margin: 10px;
		        width: 330px;
		    }-->
		    .card .ui-dashboard-column {
		        width: 320px;
		    }
		    body .panel_output >.ui-panel-titlebar{background: #EA4335!important; color: #fff;}
		    body .panel_project >.ui-panel-titlebar{background: #FBBC05!important;}
		    body .panel_award >.ui-panel-titlebar{background: #34A853!important; color: #fff;}
		    body .panel_patent >.ui-panel-titlebar{background: #4285F4!important; color: #fff;}
		    body .panel_kt >.ui-panel-titlebar{background: #c042f4!important; color: #fff;}
		</style>
	</ui:define>
	<ui:define name="mainContent"> 
		<h:outputScript target="head">	
			$(document).ready(function(){	
				diableEnterBtn();
			});
			function diableEnterBtn(){
				$('#form_summary').on('keyup keypress', function(e) {
				  var keyCode = e.keyCode || e.which;
				  if (keyCode === 13) { 
				    e.preventDefault();
				    return false;
				  }
				});	
			};
			function barChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels],
				      options: {
				         plugins: {
				         //remove the legend
			                  legend: {
			                     display: false
			                  },
				            // Change options for ALL labels of THIS CHART
				            datalabels: {
				               color: '#000'
				            }
				         }
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				               color: '#000'
				            }
				         }]
				      }
			   };
			
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       function pieChartExtender() {
		           var options = {
				      plugins: [ChartDataLabels],
				      options: {
				      	layout: {
					      padding: 0
					    },
				         plugins: {
				            legend: {
					            position: 'top',
					            align: 'top'
					          },
				            datalabels: {
				               color: '#000'
				            }
				         }
				      },
				      data: {
				         datasets: [{
				            // Change options only for labels of THIS DATASET
				            datalabels: {
				            	anchor: 'center',
				            	align: 'end',
				            	offset: 10,
				               color: '#000',
				               font: {
						          size: 16,
						        },
						        <!--  formatter: function(value, index, values) {
		                            if(value >0 ){
		                                value = value.toString();
		                                value = value.split(/(?=(?:...)*$)/);
		                                value = value.join(',');
		                                return value;
		                            }else{
		                                value = "";
		                                return value;
		                            }
		                        }-->
				            }
				         }]
				      }
			   };
			
			   //merge all options into the main chart options
			   $.extend(true, this.cfg.config, options);
		       };
		       
		       function exportChart(chart_form, chart_id) {
			        //export image
			        $('#form_sum_details\\:imagePanel').empty().append(PF(chart_id).exportAsImage());
					
					let header = chart_id.replace("Chart", "_header");
					
			        //show the dialog
			        PF('dlg').show();
			        PF('dlg').getJQ().find(".ui-dialog-title").text($('#'+chart_form+'\\:'+header).text());
			    }
			    
			    function exportChart_big(chart_form, chart_id) {
			        //export image
			        $('#form_sum_details\\:imagePanel_big').empty().append(PF(chart_id).exportAsImage());
					
					let header = chart_id.replace("Chart", "_header");
					
			        //show the dialog
			        PF('dlg_big').show();
			        PF('dlg_big').getJQ().find(".ui-dialog-title").text($('#'+chart_form+'\\:'+header).text());
			    }
		</h:outputScript>
	<p:panel id="contentPanel">
	<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'display:none;':''}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.acadStaff.countRI']}</span>
	<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'':'display:none;'}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.dept.countRI']}</span>
	<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_STAFF')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_STAFF') ne null and viewSumView.paramAdmin ne 'Y'}"/>
	<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_DEPT')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_DEPT') ne null and viewSumView.paramAdmin eq 'Y'}"/>
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<div class="card">
	    <h:form id="form_summary">	
	    	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
			<p:commandButton id="exportButton" value="Export Excel File" icon="pi pi-download" style="margin-right:5px; margin-bottom:1px;"
									 ajax = "false"
									 action="#{viewSumView.exportSummary()}" ></p:commandButton>
			<br/>						 
	    	<h:panelGroup>
				<p:panel>
						<f:facet name="header">
							<div>
								<span style="color:#1f1645;">Filtering Criteria</span>
							</div>
						</f:facet>

						<div class="ui-g">
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel for="@next" value="Faculty/ Department/ Centre" style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}" />
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="facDept" title="Faculty/ Department/ Centre" label="Faculty/ Department/ Centre" 
													multiple="true" filter="true" 
													filterMatchMode="contains"
													value="#{viewSumView.selectedFacDepts}" 
													style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}">
										<f:selectItems value="#{viewSumView.facDeptList}"/>
								</p:selectCheckboxMenu>	
							</div>
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="cdcfPeriod"  value="#{viewSumView.selectedCdcfPeriods}" 
																				 multiple="true" filter="true">
										<f:selectItems value="#{viewSumView.cdcfPeriodList}" var="o" 
															itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />				
								</p:selectCheckboxMenu>	
							</div>
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="RI Date Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-3 ui-lg-3">
								<p:datePicker id="riStartDate" view="date"
															title="Start Date (DD/MM/YYYY)" 
															label="Start Date (DD/MM/YYYY)" 
															value="#{viewSumView.selectedStartDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-5 ui-lg-5">
								<p:datePicker id="riEndDate" view="date"
															title="End Date (DD/MM/YYYY)" 
															label="End Date (DD/MM/YYYY)" 
															value="#{viewSumView.selectedEndDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>

			            </div>
						<p:commandButton id="filterButton" value="Apply" update="form_summary" icon="pi pi-filter"
										 widgetVar="filterButtonWV" onclick="PF('filterButtonWV').disable();PF('filterDialog').show();"
										 oncomplete="PF('filterButtonWV').enable();PF('filterDialog').hide();diableEnterBtn();"
										 actionListener="#{viewSumView.updateChart(false)}" ></p:commandButton><p:spacer width="10"/>
						<p:dialog widgetVar="filterDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
					        <div>
				            	<h5>Filtering</h5>
			        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
				            </div>
					    </p:dialog>
			          </p:panel>
			</h:panelGroup>
	
			
	  <div class="ui-g">
	     <div class="ui-g-12 ui-md-4 ui-lg-4">
	        <div class="ui-g-12 ui-md-12 ui-lg-12">
	            <p:panel id="output" header="Research Outputs" class="panel_output">
	            	<div class="ui-g" style="color:#1b5d76; font-size:14px;">
		            	<div class="ui-g-9 ui-md-9 ui-lg-9">
		            		<p:outputLabel style="font-weight:700;" value="Total no."/>
		            	</div>
		            	<div class="ui-g-3 ui-md-3 ui-lg-3">
		               	 	<h:outputText value="#{viewSumView.getOutputCount()}"/>         
		                </div>
		                <div class="ui-g-9 ui-md-9 ui-lg-9">
		            		<p:outputLabel style="font-weight:700;" value="By weighting"/>
		            	</div>
		            	<div class="ui-g-3 ui-md-3 ui-lg-3">
		               	 	<h:outputText value="#{viewSumView.getOutputWeighting()}"/>         
		                </div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		                	<p:linkButton outcome="viewSumDetails" value="More details" icon="pi pi-search">
		                		<f:param name="sumType" value="output"/>
								<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="facDept" value="#{viewSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="startDate" value="#{viewSumView.getStartDateString()}"/>
								<f:param name="endDate" value="#{viewSumView.getEndDateString()}"/>
								<f:param name="riPeriod" value="#{viewSumView.getSelectedRiPeriodString()}"/>
								<f:param name="ktPeriod" value="#{viewSumView.getSelectedKtPeriodString()}"/>
							</p:linkButton>
		                </div>
		                
	                </div>
	            </p:panel>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
	            <p:panel id="project" header="Projects" class="panel_project">
	            	<div class="ui-g" style="color:#1b5d76; font-size:14px;">
		            	<div class="ui-g-9 ui-md-9 ui-lg-9">
		            		<p:outputLabel style="font-weight:700;" value="Total no."/>
		            	</div>
		            	<div class="ui-g-3 ui-md-3 ui-lg-3">
		               	 	<h:outputText value="#{viewSumView.getProjectCount()}"/>         
		                </div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		            		<p:outputLabel style="font-weight:700;" value="　"/>
		            	</div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		                	<p:linkButton outcome="viewSumDetails" value="More details" icon="pi pi-search">
		                		<f:param name="sumType" value="project"/>
								<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="facDept" value="#{viewSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="startDate" value="#{viewSumView.getStartDateString()}"/>
								<f:param name="endDate" value="#{viewSumView.getEndDateString()}"/>
								<f:param name="riPeriod" value="#{viewSumView.getSelectedRiPeriodString()}"/>
								<f:param name="ktPeriod" value="#{viewSumView.getSelectedKtPeriodString()}"/>
							</p:linkButton>
		                </div>
	                </div>
	            </p:panel>
			</div>
			<div class="ui-g-12 ui-md-12ui-lg-12">
	            <p:panel id="award" header="Prizes and Awards" class="panel_award">
	            	<div class="ui-g" style="color:#1b5d76; font-size:14px;">
		            	<div class="ui-g-9 ui-md-9 ui-lg-9">
		            		<p:outputLabel style="font-weight:700;" value="Total no."/>
		            	</div>
		            	<div class="ui-g-3 ui-md-3 ui-lg-3">
		               	 	<h:outputText value="#{viewSumView.getAwardCount()}"/>         
		                </div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		            		<p:outputLabel style="font-weight:700;" value="　"/>
		            	</div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		                	<p:linkButton outcome="viewSumDetails" value="More details" icon="pi pi-search">
		                		<f:param name="sumType" value="award"/>
								<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="facDept" value="#{viewSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="startDate" value="#{viewSumView.getStartDateString()}"/>
								<f:param name="endDate" value="#{viewSumView.getEndDateString()}"/>
								<f:param name="riPeriod" value="#{viewSumView.getSelectedRiPeriodString()}"/>
								<f:param name="ktPeriod" value="#{viewSumView.getSelectedKtPeriodString()}"/>
							</p:linkButton>
		                </div>
	                </div>
	            </p:panel>
			</div>
			<div class="ui-g-12 ui-md-12 ui-lg-12">
	            <p:panel id="patent" header="Patents" class="panel_patent">
	            	<div class="ui-g" style="color:#1b5d76; font-size:14px;">
		            	<div class="ui-g-9 ui-md-9 ui-lg-9">
		            		<p:outputLabel style="font-weight:700;" value="Total no."/>
		            	</div>
		            	<div class="ui-g-3 ui-md-3 ui-lg-3">
		               	 	<h:outputText value="#{viewSumView.getPatentCount()}"/>         
		                </div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		            		<p:outputLabel style="font-weight:700;" value="　"/>
		            	</div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		                	<p:linkButton outcome="viewSumDetails" value="More details" icon="pi pi-search">
		                		<f:param name="sumType" value="patent"/>
								<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="facDept" value="#{viewSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="startDate" value="#{viewSumView.getStartDateString()}"/>
								<f:param name="endDate" value="#{viewSumView.getEndDateString()}"/>
								<f:param name="riPeriod" value="#{viewSumView.getSelectedRiPeriodString()}"/>
								<f:param name="ktPeriod" value="#{viewSumView.getSelectedKtPeriodString()}"/>
							</p:linkButton>
		                </div>
	                </div>
	            </p:panel>
	       </div>
	     </div>
	     
	       <!-- ri summary chart -->
			<div class="ui-g-12 ui-md-8 ui-lg-8">
				<p:panel id="summaryPie" header="Count RI" class="chart">
					<div style="padding-left:5vw; padding-right:5vw">
						<p:pieChart model="#{viewSumView.summaryPieModel}" style="width: 100%;" widgetVar="summaryPieChart"/>
						<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_summary', 'summaryPieChart')" style="display:none;"/>
					</div>
	            </p:panel>
	              
           </div>
           
           <div class="ui-g-12 ui-md-12 ui-lg-12">
           </div>
           <div class="ui-g-12 ui-md-4 ui-lg-4">
	            <p:panel id="kt" header="KT Activities" class="panel_kt">
	            	<div class="ui-g" style="color:#1b5d76; font-size:14px;">
		            	<div class="ui-g-9 ui-md-9 ui-lg-9">
		            		<p:outputLabel style="font-weight:700;" value="Total no."/>
		            	</div>
		            	<div class="ui-g-3 ui-md-3 ui-lg-3">
		               	 <h:outputText value="#{viewSumView.getKtCount('')}"/>         
		                </div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		            		<p:outputLabel style="font-weight:700;" value="　"/>
		            	</div>
		                <div class="ui-g-12 ui-md-12 ui-lg-12">
		                	<p:linkButton outcome="viewSumDetails" value="More details" icon="pi pi-search">
		                		<f:param name="sumType" value="kt"/>
								<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="facDept" value="#{viewSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
								<f:param name="startDate" value="#{viewSumView.getStartDateString()}"/>
								<f:param name="endDate" value="#{viewSumView.getEndDateString()}"/>
								<f:param name="riPeriod" value="#{viewSumView.getSelectedRiPeriodString()}"/>
								<f:param name="ktPeriod" value="#{viewSumView.getSelectedKtPeriodString()}"/>
							</p:linkButton>
		                </div>
	                </div>
	            </p:panel>
	       </div>
	       <div class="ui-g-12 ui-md-8 ui-lg-8">  
			<p:panel id="summaryKtPie" header="KT Activities" class="chart">
				<div style="padding-left:5vw; padding-right:5vw">
					<p:pieChart model="#{viewSumView.summaryKtPieModel}" style="width: 100%;" widgetVar="summaryKtPieChart"/>
					<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart_big('form_summary', 'summaryKtPieChart')" style="display:none;"/>
				</div>
            </p:panel>
           </div>

           
	     </div>
	    <div style="clear:both"/>
	  </h:form>
	  
	  	<h:form id="form_sum_details">
			<p:dialog widgetVar="dlg" showEffect="fade" modal="true" header="" resizable="true">
			    <p:outputPanel id="imagePanel" layout="block" style="width:400px;height:400px"/>
			</p:dialog> 
			<p:dialog widgetVar="dlg_big" showEffect="fade" modal="true" header="" resizable="true">
			    <p:outputPanel id="imagePanel_big" layout="block" style="width:720px;height:720px"/>
			</p:dialog> 
		</h:form>
	</div>
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>