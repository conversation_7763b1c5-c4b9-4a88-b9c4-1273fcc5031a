<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{asstView.paramPid}" />
	</f:metadata>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-users"></i> Manage Assistant Right</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="appForm">
		<div class="card">
			<div class="p-grid ui-fluid" >
				<div class="p-col-12 p-md-3">
					<div class="ui-inputgroup">
						<span class="ui-inputgroup-addon"><i class="pi pi-user"></i></span>
						<p:inputText id="formAsstId" label="Assistant ID" placeholder="Assistant ID" value="#{asstView.selectedAsst.pk.assistant_id}" required="true" requiredMessage="Assistant ID is required."/>
						<p:commandButton icon="pi pi-plus" styleClass="ui-button-danger" action="#{asstView.updateAppForm()}" update=":dataForm :appForm :msgs" title="Add new assistant"/>
	            	</div>
					<span style="font-size:smaller;">(e.g. annaHui)</span>
	            </div>
			</div>
		</div>
	</h:form>
	<br/>
	<h:form id="dataForm">
		<p:dataTable id="dataTable" var="data" value="#{asstView.asstListByAcadStaff}" sortMode="single" stripedRows="true"
						rowKey="#{data.pk.assistant_id}" tableStyle="table-layout: fixed;"
						selection="#{asstView.selectedRecord}" selectionMode="single"
                     	widgetVar="dataWidget">

			<p:column headerText="Assistant Name" id="asstName" sortBy="#{data.assistant_name}">
                <h:outputText value="#{data.assistant_name}"/>
            </p:column>
            <p:column headerText="Status" id="status" sortBy="#{data.status}">
                <h:outputText value="#{data.status}"/>
            </p:column>            
           <p:column headerText="Last updated date" id="requestedDate" sortBy="#{data.timestamp}">
                <h:outputText value="#{data.timestamp}">
                	<f:convertDateTime pattern="dd-MM-yyyy HH:mm" />
                </h:outputText>
            </p:column>   
            <p:column headerText="Action">
                <p:commandButton id="btn_accept" action="#{asstView.approveAsst(data)}" rendered="#{data.status == 'REJECTED' || data.status == 'REQUESTED'}" update=":dataForm :appForm :msgs" value="Accept" style="margin-right:5px;">
                	<p:confirm header="Confirmation" message="Are you sure you want to proceed?" icon="pi pi-exclamation-triangle"/>
                </p:commandButton>
                <p:commandButton id="btn_enable" action="#{asstView.enableAsst(data)}" rendered="#{data.status == 'DISABLED' }" update=":dataForm :appForm :msgs" value="Enable" style="margin-right:5px;">
                	<p:confirm header="Confirmation" message="Are you sure you want to proceed?" icon="pi pi-exclamation-triangle"/>
                </p:commandButton>
                <p:commandButton id="btn_reject" action="#{asstView.rejectAsst(data)}" rendered="#{data.status == 'REQUESTED'}" update=":dataForm :appForm :msgs" value="Reject" style="margin-right:5px;">
                	<p:confirm header="Confirmation" message="Are you sure you want to proceed?" icon="pi pi-exclamation-triangle"/>
                </p:commandButton>
                <p:commandButton id="btn_disable" action="#{asstView.disableAsst(data)}" rendered="#{data.status == 'APPROVED'}" update=":dataForm :appForm :msgs" value="Disable" style="margin-right:5px;">
                	<p:confirm header="Confirmation" message="Are you sure you want to proceed?" icon="pi pi-exclamation-triangle"/>
                </p:commandButton>
                <p:commandButton id="btn_remove" action="#{asstView.removeAsst(data)}" update=":dataForm :appForm :msgs" icon="pi pi-times" styleClass="ui-button-danger" title="Delete">
                	<p:confirm header="Confirmation" message="Are you sure you want to proceed?" icon="pi pi-exclamation-triangle"/>
                </p:commandButton>
            </p:column>   
        </p:dataTable>
        <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
		            <p:commandButton value="No" icon="pi pi-times" type="button" styleClass="ui-confirmdialog-no ui-button-flat"/>
		            <p:commandButton value="Yes" icon="pi pi-check" type="button" styleClass="ui-confirmdialog-yes" />
		 </p:confirmDialog>
	</h:form>
	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>