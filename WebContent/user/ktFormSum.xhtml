<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{manageKtSumView.paramPid}" />
		<f:viewParam name="period" value="#{manageKtSumView.paramPeriod}" />
		<f:viewParam name="admin" value="#{manageKtSumView.paramAdmin}" />
		<f:viewParam name="facDept" value="#{manageKtSumView.selectedFacDept}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<h:outputScript target="head">
            /*A property to set row groups to auto-collapse does not currently
             * exist with the primefaces component.  This will auto-collapse
             * every rowgroup, which is what I want in this case.*/
            function collapseDetailsRows() {
                var rowGroups = $('.ui-rowgroup-toggler');
                var hasCollapse = false;
                for (var i = 0; i &lt; rowGroups.length; i ++) {
                	if ($(rowGroups[i]).attr('aria-expanded') === "false"){
                		hasCollapse = true;
                	}
                }
                if (hasCollapse){
	                for (var i = 0; i &lt; rowGroups.length; i ++) {
	                	if ($(rowGroups[i]).attr('aria-expanded') === "false"){
	                		$(rowGroups[i]).trigger('click');
	                	}
	                }
                }else{
                	for (var i = 0; i &lt; rowGroups.length; i ++) {
	                	$(rowGroups[i]).trigger('click');
	                }
                }
            }
	</h:outputScript>
	<p:panel id="contentPanel">
	<style type="text/css">
		    .ui-datatable-header {
		        background: #186ba0 !important;
		        color: #fff !important;
		    }
		    .ui-datatable thead th {
		    	padding: 0 !important;
		    }
		    body .ui-datatable .ui-datatable-data > tr > td {
		    	padding: 4px 0px 4px 0px;
		    }
		    .ui-datatable .ui-datatable-data > tr .ui-rowgroup-toggler .ui-rowgroup-toggler-icon {
		    	color: #d71f1f !important;
		    }
		</style>	
	<span class="admin-content-title"><i class="fa-solid fa-chalkboard-user"></i> Manage KT Activities</span>
	<p:staticMessage severity="warn" summary="Exclusive Mode" detail="#{secFuncLockView.getExKtByDept(manageKtSumView.selectedFacDept).lock_msg}" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{manageKtSumView.canModifyKt == false}"/>
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<p:staticMessage severity="warn" summary="Note:" detail="#{sysParamView.getValue('KT_FORM_EMPTY_MSG')}" style="width: 100%; margin-bottom:6px;" escape="false" rendered="#{sysParamView.getValue('KT_FORM_EMPTY_MSG') ne null}"/>
	<h:form id="dataForm" rendered="#{manageKtSumView.canViewKtActList() == true}">
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<br/>
		<p:toolbar style="border:0px solid #fff; padding:0;">
			<p:toolbarGroup>
				<div style="display: flex; align-items: center;">
					<div style="display: inline-block;">
					<p:outputLabel for="@next" value="Reporting Period" style="color:#4c6f89; font-weight:700; margin-right:7px;" />
					</div>
					<div style="display: inline-block;">
						<p:selectOneMenu id="period" title="Reporting Period" label="Reporting Period" value="#{manageKtSumView.selectedPeriod}" style="margin-top:4px;margin-right:10px;">
								<f:selectItems value="#{manageKtSumView.periodList}" var="o" 
													itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />
								<p:ajax event="change" update="btn_period btn_dept"/>		
						</p:selectOneMenu>	
					</div>
					<h:panelGroup rendered="#{manageKtSumView.paramAdmin != 'Y'}">
						<div style="display: inline-block;">
							<p:linkButton id="btn_period" outcome="ktFormSum" value="Go" styleClass="ui-button-raised ui-button-success" style="margin-left:5px;">
								<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
								<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
					    	</p:linkButton>
						</div>
					</h:panelGroup>
				</div>
				<h:panelGroup rendered="#{manageKtSumView.paramAdmin == 'Y'}">
					<br/>
					<div style="display: flex; align-items: center;">
						<div style="display: inline-block;">
						<p:outputLabel for="@next" value="Faculty/ Department/ Centre" style="color:#4c6f89; font-weight:700; margin-right:7px; margin-left:20px;" />
						</div>
						<div style="display: inline-block;">
						<p:selectOneMenu id="facDept" title="Faculty/ Department/ Centre" label="Faculty" value="#{manageKtSumView.selectedFacDept}" style="margin-top:4px;margin-right:20px;">
								<f:selectItems value="#{manageKtSumView.facDeptList}"/>
								<p:ajax event="change" update="facDept btn_dept"/>		
						</p:selectOneMenu>	
						</div>
						<div style="display: inline-block;">
							<p:linkButton id="btn_dept" outcome="ktFormSum" value="Go" styleClass="ui-button-raised ui-button-success">
								<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
								<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
								<f:param name="admin" value="#{manageKtSumView.paramAdmin}" />
								<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" />
					    	</p:linkButton>
						</div>
					</div>
				</h:panelGroup>
			</p:toolbarGroup> 
			<p:toolbarGroup align="right">
			 	<p:commandButton 	value="Export to Excel (All Unit)" 
                					icon="fas fa-download" 
                					ajax="false" 
                					action="#{manageKtSumView.exportKtReport(true)}"
                					rendered="#{manageKtSumView.formList.size() > 0 and manageKtSumView.paramAdmin == 'Y' and manageKtSumView.getIsKtAdmin()}" 
                					style="margin-right:20px;"/>
                					
                <p:commandButton 	value="Export to Excel" 
                					icon="fas fa-download" 
                					ajax="false" 
                					action="#{manageKtSumView.exportKtReport(false)}"
                					rendered="#{manageKtSumView.formList.size() > 0}" />
               	
            </p:toolbarGroup>
        </p:toolbar>
		
		<p:dataTable id="dataTable" var="data" value="#{manageKtSumView.formList}" stripedRows="true"
						tableStyle="table-layout: fixed;"
						rowIndexVar="rowIndex">

			<f:facet name="header">
				<p:commandButton id="collapseAll" style="background:#bf3a3a; margin-right:10px;"
											type="button" onclick="collapseDetailsRows()"
											value="Expand / Collapse All"/>
	             <i class="fa-solid fa-bullseye" style="color:#FBC02D;"></i><h:outputText style="padding-left:7px; font-weight:700;" value="Summary of Performance Indicators"/>
	             - #{manageKtSumView.period.period_desc} 
	             <h:outputText class="facDeptText" value="#{manageKtSumView.selectedFacDept}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
	        </f:facet>       	
				<p:headerRow field="display_order" expandable="true" expanded="false">
					<p:column colspan="3" style="#{rowIndex mod 2 eq 0 ? 'background:#d8e8f5; color: #043554; padding: 4px 0px 4px 20px;':'background:#c1def5; color: #043554; padding: 4px 0px 4px 20px;'}">
						<div style="vertical-align: middle; font-size:16px; display:contents;">
	                	<h:outputText value="Form "/><h:outputText value="#{data.form_short_desc} "/><h:outputText value="#{data.form_full_desc} "/>
	                	<br/><h:outputText value="#{data.form_brief}" style="font-size:12px; color:#666; font-weight:400; padding: 4px 0px 4px 40px;"/>
	                	<h:outputText value="[Waiting for Consensus: #{data.countWaitConsent}]" style="color:#b5177d; margin-left:5px;" rendered="#{data.countWaitConsent gt 0}"/>
	                	</div>
	                </p:column>
	                <p:column colspan="1" style="#{rowIndex mod 2 eq 0 ? 'background:#d8e8f5; color: #186ba0; text-align: right; padding: 4px 25px 4px 0px;':'background:#c1def5; color: #186ba0; text-align: right; padding: 4px 25px 4px 0px;'}">
						<p:linkButton outcome="manageKtForm" value="Manage [#{data.countTotal}]" styleClass="ui-button-raised ui-button-warning"
									  rendered="#{manageKtSumView.paramAdmin != 'Y'}">
							<f:param name="pid" value="#{manageKtSumView.paramPid}"/>		  
							<f:param name="form" value="#{data.form_code}"/>
							<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
				    	</p:linkButton>
				    	<p:linkButton outcome="manageKtForm" value="Manage [#{data.countTotal}]" styleClass="ui-button-raised ui-button-warning"
				    				  rendered="#{manageKtSumView.paramAdmin == 'Y'}">
							<f:param name="form" value="#{data.form_code}"/>
							<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
							<f:param name="admin" value="#{manageKtSumView.paramAdmin}" />
							<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" />
				    	</p:linkButton>
	                </p:column>
	            </p:headerRow>   
				<p:column colspan="4">
					<p:dataTable var="v" value="#{data.getSum()}" stripedRows="true" widgetVar="subTable" tableStyle="table-layout: fixed;">
						<p:column style="width:75%; color:#d71f1f; font-size: 10px; font-weight:700; padding: 4px 0px 4px 40px; vertical-align: baseline;">
							<i class="fa-solid fa-square"></i><h:outputText style="padding-left:7px; color:#526827; font-size:16px;" value="#{v.sum_desc}"/>
						</p:column>
						<p:column style="background:#f9e958; color:#043554; font-weight:700; padding: 4px 4px; text-align:center; border-bottom: solid #fff;">
							<h:outputText value="#{v.sum_value}" rendered="#{v.sum_type eq 'num'}">
								<f:convertNumber groupingUsed="true" minFractionDigits="0" />
							</h:outputText>
							<h:outputText value="#{v.sum_value}" rendered="#{v.sum_type eq 'dollar'}">
								<f:convertNumber type="currency" currencySymbol="$"/>
							</h:outputText>
						</p:column>
					</p:dataTable>
				</p:column>
		</p:dataTable>
	</h:form>

	<br/>
	<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>