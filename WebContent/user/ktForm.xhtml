<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:component="http://java.sun.com/jsf/composite/component"
	xmlns:cc="http://java.sun.com/jsf/composite" 
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	template="/resources/template/template.xhtml">
<cc:interface>
	<cc:attribute name="searchPanel" type="hk.eduhk.rich.view.RISearchPanel" required="true"/>
	<cc:attribute name="update" type="java.lang.String"/>
	<cc:attribute name="showPersonPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showExStaffPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showRiPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showOtherPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isRdoLib" type="java.lang.Boolean" default="true"/>
</cc:interface>
	<f:metadata>
		<f:event listener="#{manageKtFormView.getCanModify}" type="preRenderView" />
		<f:event listener="#{manageKtFormView.checkValid}" type="preRenderView" />
		<f:viewParam name="no" value="#{manageKtFormView.paramNo}" />
		<f:viewParam name="pid" value="#{manageKtFormView.paramPid}" />
		<f:viewParam name="form" value="#{manageKtFormView.paramFormCode}" />
		<f:viewParam name="dataLevel" value="#{manageKtFormView.paramDataLevel}" />
		<f:viewParam name="consent" value="#{manageKtFormView.paramConsent}" />
		<f:viewParam name="area_code" value="#{manageKtFormView.paramArea_code}" />
		<f:viewParam name="source_id" value="#{manageKtFormView.paramSource_id}" />
		<f:viewParam name="staff_number" value="#{manageKtFormView.paramStaff_number}" />
		<f:viewParam name="period" value="#{manageKtFormView.paramPeriod}" />
		<f:viewParam name="admin" value="#{manageKtSumView.paramAdmin}" />
		<f:viewParam name="facDept" value="#{manageKtSumView.selectedFacDept}" />
		<f:viewParam name="facDept" value="#{manageKtFormView.paramFacDept}" />
		<f:viewParam name="riNo" value="#{manageKtFormView.paramRiNo}" />
	</f:metadata>
	<ui:define name="html_head">
		<style>
			li.ui-state-disabled{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			body .ui-radiobutton{
				display: table-cell;
			}
			.ui-selectoneradio label{
				display: table-cell;
				font-weight: 600;
				padding-left: 16px;
				padding-right: 16px;
			}
			.ui-selectoneradio td{
				padding-bottom: 8px;
			}
			ol.notes li{
				padding-left: 20px;
				padding-bottom: 8px;
			}
			
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}	
		</style>
	</ui:define>

	<ui:define name="mainContent">
		<p:importConstants type="hk.eduhk.rich.Constant" var="const" />
		<p:panel id="contentPanel">
			<span class="admin-content-title"><i class="fas fa-folder-open"></i>
				#{manageKtFormView.selectedForm.form_short_desc} #{manageKtFormView.selectedForm.form_full_desc}</span>
			<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
			<p:scrollTop />
			<h:outputText value="You don't have access right." rendered="#{manageKtFormView.hasAccessRight() == false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
			<h:form id="editForm"
				rendered="#{manageKtFormView.hasAccessRight() == true}">
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:linkButton value="#{bundle['action.back']}" outcome="manageKtForm" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageKtFormView.paramDataLevel eq 'M' &amp;&amp; manageKtFormView.paramConsent eq ''}">		
							<f:param name="pid" value="#{manageKtFormView.paramPid}"/>
							<f:param name="form" value="#{manageKtFormView.paramFormCode}"/>
							<f:param name="period" value="#{manageKtFormView.paramPeriod}"/>
						</p:linkButton>
						<p:linkButton value="#{bundle['action.back']}" outcome="manageKtForm" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageKtFormView.paramDataLevel eq 'N'}">		
							<f:param name="pid" value="#{manageKtFormView.paramPid}"/>
							<f:param name="form" value="#{manageKtFormView.paramFormCode}"/>
							<f:param name="period" value="#{manageKtFormView.paramPeriod}"/>
							<f:param name="admin" value="Y"/>
							<f:param name="facDept" value="#{manageKtFormView.paramFacDept}"/>
						</p:linkButton>
						<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageKtFormView.paramConsent ne ''}">		
							<f:param name="pid" value="#{manageKtFormView.paramPid}"/>
							<f:param name="consent" value="#{manageKtFormView.paramConsent}"/>
							<f:param name="tabpage" value="2"/>
						</p:linkButton>
						<p:defaultCommand target="dummy"/>
						<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
						<p:commandButton id="top_btn_sava"
							value="#{formBundle['form.save']}"
							rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.save}"
							oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="top_btn_savaAndGen"
							value="#{(manageKtFormView.selectedFormHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}"
							rendered="#{manageKtFormView.paramDataLevel eq 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.save}"
							oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="top_btn_savaAndPublish"
							value="#{bundle['action.saveAndPublish']}"
							rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.saveAndPublishForm}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.kt.save.publish.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_submit"
							value="#{formBundle['form.save']}"
							rendered="#{manageKtFormView.isCreator == false &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel eq 'M'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.submitConsent}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="Do you want to submit this record?"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_snapshot" value="#{formBundle['form.take.snapshot']}"
							rendered="#{manageKtFormView.paramDataLevel eq 'P' &amp;&amp; manageKtFormView.selectedFormHeader_q.pk.form_no ne null}"
							style="margin-right:5px; margin-bottom:1px;" process="@this"
							action="#{manageKtFormView.takeSnapshot}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.take.snapshot.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_delete"
							value="#{formBundle['form.del']}"
							rendered="#{manageKtFormView.canDelete == true &amp;&amp; manageKtSumView.canModifyKt == true}"
							style="margin-right:5px; margin-bottom:1px; background:#D32F2F; border:1px solid #D32F2F;"
							process="@this" action="#{manageKtFormView.deleteForm}">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.kt.del.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:linkButton id="btn_p_level" outcome="ktForm"
							rendered="#{manageKtFormView.paramDataLevel eq 'P' &amp;&amp; manageKtFormView.checkSnapshotExists()}"
							value="#{formBundle['form.ri.goto.lv.c']}" icon="pi pi-chevron-right"
							style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
							<f:param name="pid" value="#{manageKtFormView.getParamPid()}" />
							<f:param name="no"
								value="#{manageKtFormView.selectedFormHeader_q.pk.form_no}" />
							<f:param name="dataLevel" value="C" />
							<f:param name="form" value="#{manageKtFormView.paramFormCode}"/>
						</p:linkButton>
						<p:linkButton id="btn_c_level" outcome="ktForm"
							rendered="#{manageKtFormView.paramDataLevel eq 'C'}"
							value="#{formBundle['form.ri.goto.lv.p']}" icon="pi pi-chevron-right"
							style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
							<f:param name="pid" value="#{manageKtFormView.getParamPid()}" />
							<f:param name="no"
								value="#{manageKtFormView.selectedFormHeader_q.pk.form_no}" />
							<f:param name="dataLevel" value="P" />
							<f:param name="form" value="#{manageKtFormView.paramFormCode}"/>
						</p:linkButton>
						<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true" width="350">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Yes" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
						</p:confirmDialog>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-2"
						style="font-size: 18px; font-weight: 700; text-align: right;">
						<p:outputLabel value="#{formBundle['form.ri.status.lv.p']}"
							style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;"
							rendered="#{manageKtFormView.paramDataLevel eq 'P'}" />
						<p:outputLabel value="#{formBundle['form.ri.status.lv.c']}"
							style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;"
							rendered="#{manageKtFormView.paramDataLevel eq 'C'}" />
					</div>
				</div>
				<div class="form-sub-title">
				    <table width="100%">
				        <tr>
				            <td><i class="fas fa-tag" style="margin-right: 5px;"></i>#{formBundle['form.status']}</td>
				            <td align="right"><span class="facDeptText" style="background: #f06524; color: #fff;">#{manageKtFormView.paramFacDept}</span></td>
				        </tr>
				    </table>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g" style="background:#f4fbe5;">
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="background:#7b9d39; font-size:18px; font-weight:700;">
						<p:outputLabel style="color:#fff;" value="#{formBundle['form.header.status']} " />
						<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{manageKtFormView.selectedFormHeader_q.publish_status}" rendered="#{manageKtFormView.selectedFormHeader_q.publish_status ne 'PUBLISHED'}"/>
						<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{manageKtFormView.selectedFormHeader_q.publish_status eq 'PUBLISHED'}"/>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.at']} " />
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.last_modified_date}" >
							    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</p:outputLabel>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.by']} " />
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.last_modified_by}" />
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.at']} " />
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.last_published_date}" >
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</p:outputLabel>
					</div>
		
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.by']} " />
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.last_published_by}" />
					</div>
					
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #7b9d39; padding: 0;"></div>
					<!--  
					<div class="ui-g-6 ui-md-4 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.institute']}" />
					</div>
					<div class="ui-g-6 ui-md-8 ui-lg-4">
						<p:message for="inst_display_ind"/>
						<p:selectOneMenu id="inst_display_ind" required="#{manageKtFormView.isRdoAdmin == true}"
													disabled="#{manageKtFormView.isRdoAdmin == false || manageKtFormView.paramDataLevel eq 'M'}" 
													title="#{formBundle['form.header.display.institute']}" 
													label="#{formBundle['form.header.display.institute']}"
													value="#{manageKtFormView.selectedFormHeader_q.inst_display_ind}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							</p:selectOneMenu>
					</div>
					
					<div class="ui-g-6 ui-md-4 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute']}" />
					</div>
					<div class="ui-g-6 ui-md-8 ui-lg-4">
						<p:message for="inst_verified_ind"/>
						<p:selectOneMenu id="inst_verified_ind" required="#{manageKtFormView.isRdoAdmin == true}" 
													disabled="#{manageKtFormView.isRdoAdmin == false || manageKtFormView.paramDataLevel eq 'M'}"
													title="#{formBundle['form.header.verify.institute']}" 
													label="#{formBundle['form.header.verify.institute']}"
													value="#{manageKtFormView.selectedFormHeader_q.inst_verified_ind}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   					
							</p:selectOneMenu>		
					</div>
					-->
					<!--  
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #7b9d39; padding: 0;"></div>
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.generate']}" />
					</div>
					<div class="ui-g-8 ui-md-3 ui-lg-1">
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_gen_ind eq 'Y'?'Yes':'No'}"/>
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_gen_ind eq 'Y'?' — ':''}"/>
						<h:outputText class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_gen_date}" rendered="#{manageKtFormView.selectedFormHeader_q.cdcf_gen_ind eq 'Y'}">
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.change']}" rendered="#{manageKtFormView.isRdoAdmin == true}"/>
					</div>
					<div class="ui-g-8 ui-md-3 ui-lg-1">
							<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_changed_ind eq 'Y'?'Yes':'No'}" rendered="#{manageKtFormView.isRdoAdmin == true}"/>
							<p:linkButton id="btn_compare" outcome="riComparisonReport" rendered="#{manageKtFormView.selectedFormHeader_q.cdcf_changed_ind eq 'Y' &amp;&amp; manageKtFormView.isRdoAdmin == true}" value="Compare Snapshot" style="margin-left:7px;" target="_blank">
			  					<f:param name="no" value="#{manageKtFormView.selectedFormHeader_q.form_no}"/>
			  					<f:param name="riType" value="award"/>
			  				</p:linkButton>	
					</div>
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.process']}" />
					</div>
					<div class="ui-g-8 ui-md-3 ui-lg-1">
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_processed_ind eq 'Y'?'Yes':'No'}"/>
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_processed_ind eq 'Y'?' — ':''}"/>
						<h:outputText class="riForm-item-ans" value="#{manageKtFormView.selectedFormHeader_q.cdcf_processed_date}" rendered="#{manageKtFormView.selectedFormHeader_q.cdcf_processed_ind eq 'Y'}">
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
			
					<div class="ui-g-4 ui-md-3 ui-lg-1">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.status']}" />
					</div>
					<div class="ui-g-8 ui-md-3 ui-lg-2">
						<p:message for="cdcf_status"/>
						<p:selectOneMenu id="cdcf_status" title="#{formBundle['form.cdcf.status']}" label="#{formBundle['form.cdcf.status']}" value="#{manageKtFormView.selectedFormHeader_q.cdcf_status}" 
													required="#{manageKtFormView.isRdoAdmin == true}" 
													disabled="#{manageKtFormView.isRdoAdmin == false || manageKtFormView.paramDataLevel eq 'M'}">
								<f:selectItems value="#{manageKtFormView.cdcfStatusList}"/>  
							</p:selectOneMenu>	
					</div>
					-->
					
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #7b9d39; padding: 0;"></div>
					<div class="ui-g-4 ui-md-6 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="Reporting Year" style="padding-right:20px;"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9">
						<p:outputLabel class="riForm-item-ans" value="#{manageKtFormView.selectedKtRptPeriod.period_desc}"/>
					</div>
					<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageKtFormView.paramDataLevel eq 'M'?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.ri.profile']}" style="padding-right:20px;" rendered="#{manageKtFormView.paramDataLevel eq 'M'}"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageKtFormView.paramDataLevel eq 'M'?'':'display:none;'}">
						<h:panelGroup id="displayRIGroup">
							<p:selectOneMenu title="#{formBundle['form.header.display.ri.profile']}" value="#{manageKtFormView.selectedFormDetails_q.display_ind}" 
													rendered="#{manageKtFormView.paramDataLevel eq 'M'}"
													disabled="#{manageKtSumView.canModifyKt == false || manageKtFormView.selectedFormDetails_q.consent_ind ne 'Y'}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							</p:selectOneMenu>
						</h:panelGroup>
					</div>	
					
					<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageKtFormView.isContributor == true &amp;&amp; manageKtFormView.paramDataLevel eq 'M'?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.consent.kt']}" style="padding-right:20px;" rendered="#{manageKtFormView.isContributor == true &amp;&amp; manageKtFormView.paramDataLevel eq 'M'}"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageKtFormView.isContributor == true &amp;&amp; manageKtFormView.paramDataLevel eq 'M'?'':'display:none;'}">	
						<p:selectOneMenu title="#{formBundle['form.header.consent.kt']}" value="#{manageKtFormView.selectedFormDetails_q.consent_ind}" 
												rendered="#{manageKtFormView.isContributor == true &amp;&amp; manageKtFormView.paramDataLevel eq 'M'}"
												disabled="#{manageKtSumView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Unconfirmed" itemValue="U"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="displayRIGroup" listener="#{manageKtFormView.setDisplyRI()}"/>
						</p:selectOneMenu>
					</div>	
										
					<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageKtFormView.isRdoAdmin?'':'display: none;'}">
							<p:outputLabel class="riForm-item-title" value="#{formBundle['form.remarks']}" rendered="#{manageKtFormView.isRdoAdmin || manageKtFormView.isKtAdmin || manageKtFormView.isInputKtAdmin}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isRdoAdmin?'':'display: none;'}">
							<p:message for="remarks"/>
							<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="3" counter="display" maxlength="1000" rendered="#{manageKtFormView.isRdoAdmin || manageKtFormView.isKtAdmin || manageKtFormView.isInputKtAdmin}"
													value="#{manageKtFormView.selectedFormHeader_q.remarks}"
		                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
		                      <br/>
		      				  <h:outputText id="display" class="p-d-block" />
					</div>	
				</div>
				<br/>
				<div class="form-sub-title">
					<i class="fas fa-square-pen" style="margin-right:5px;"></i>Details
					<div class="riForm-item-note">
						<p:outputLabel value="#{manageKtFormView.selectedForm.note_1}" escape="false"/>
						<p:outputLabel value="#{manageKtFormView.selectedForm.note_2}" escape="false"/>
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<!-- Form Details -->
				<ui:insert name="#{manageKtFormView.paramFormCode}">
					<ui:decorate template="../resources/component/ktForm/#{manageKtFormView.paramFormCode}.xhtml">
					</ui:decorate>
				</ui:insert>
				<!-- selectedFormDetails_p_list -->
				<p:messages for="columnTable"/>
				<p:dataTable id="columnTable" class="riFormTable"
					value="#{manageKtFormView.selectedFormDetails_p_list}" var="col"
					widgetVar="columnTableWV" rows="50" reflow="true"
					 rowsPerPageTemplate="10,20,50,100"
					paginator="true"
					paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
					currentPageReportTemplate="Total Number of Contributor(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
					style="max-width:99%;" rowIndexVar="rowIndex">

					<p:column class="data-noprint" style="width:1em; text-align:left;">
						<f:facet name="header">Up</f:facet>
						<p:commandLink action="#{manageKtFormView.moveColumnUp(rowIndex)}"
							update="columnTable" immediate="true"
							rendered="#{rowIndex gt 0 &amp;&amp; manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}">
							<i class="fas fa-caret-up icon-action" title="Move up" />
						</p:commandLink>
					</p:column>

					<p:column class="data-noprint" style="width:1em; text-align:left;">
						<f:facet name="header">Dn.</f:facet>
						<p:commandLink
							action="#{manageKtFormView.moveColumnDown(rowIndex)}"
							update="columnTable" immediate="true"
							rendered="#{rowIndex lt manageKtFormView.selectedFormDetails_p_list.size()-1 &amp;&amp; manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}">
							<i class="fas fa-caret-down icon-action" title="Move down" />
						</p:commandLink>
					</p:column>

					<p:column style="text-align:left; width:5em;">
						<f:facet name="header">No.</f:facet>
						<ui:fragment
							rendered="#{manageKtFormView.isRiCreator(col.staff_no)}">
							<i class="fa fa-star faa-pulse animated-hover" title="RI creator"
								style="font-size: 9px; color: #f06524; vertical-align: middle;"></i>
						</ui:fragment> #{rowIndex +1}
					</p:column>

					<p:column style="width:15em; text-align:left;">
						<f:facet name="header">#{formBundle['form.ri.creator.output']} Position</f:facet>
						<p:message id="typeMsg" for="type" />
						<p:selectOneMenu id="type" value="#{col.flag}"
							disabled="#{manageKtFormView.isCreator == false || manageKtSumView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="EdUHK Staff" itemValue="N" />
							<f:selectItem itemLabel="Former Staff" itemValue="F" />
							<f:selectItem itemLabel="Non EdUHK Staff" itemValue="Y" />
							<f:selectItem itemLabel="EdUHK Student" itemValue="S" />
							<p:ajax event="change" update="nameGroup cvGroup" />
						</p:selectOneMenu>
						<p:blockUI block="contentPanel" trigger="type" />
					</p:column>

					<p:column>
						<f:facet name="header">#{formBundle['form.ri.creator.output']}</f:facet>
						<h:panelGroup id="nameGroup">
							<p:message for="select_staff_name" />
							<p:selectOneMenu id="select_staff_name" dynamic="true"
								label="#{formBundle['form.ri.creator.output']}" style="width:90%" value="#{col.staff_no}"
								filter="true" filterMatchMode="startsWith"
								rendered="#{col.flag eq 'N'}"
								disabled="#{manageKtFormView.isCreator == false || manageKtSumView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
								<f:selectItem itemLabel="-- Please select --" itemValue="#{null}" itemDisabled="true" />
								<f:selectItems value="#{manageRIView.staffList}"/>
								<p:ajax event="change" update="cvGroup" />
							</p:selectOneMenu>
							<p:blockUI block="contentPanel" trigger="select_staff_name" />
							
							<p:message for="select_staff_past_name" />
							<p:autoComplete id="select_staff_past_name" value="#{col.name}" style="#{manageKtFormView.paramDataLevel eq 'C'?'width:70%':'width:90%'}"
													rendered="#{col.flag eq 'F'}"
													minQueryLength="3" 
													emptyMessage="No results"
													forceSelection="true"
													disabled="#{manageKtFormView.isCreator == false || manageKtSumView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"
                               						completeMethod="#{manageRIView.fsCompleteText}" scrollHeight="250">
                               						<p:ajax event="itemSelect" process="@this" />
                              </p:autoComplete>

							<p:message id="nameMsg" for="name" />
							<p:inputText id="name" label="#{formBundle['form.ri.creator.output']}"
								style="width:90%" value="#{col.name}"
								onkeypress="return (event.charCode != 59);" maxlength="80"
								rendered="#{col.flag ne 'N' &amp;&amp; col.flag ne 'F'}"
								disabled="#{manageKtFormView.isCreator == false || manageKtSumView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
								<p:ajax />
							</p:inputText>
						</h:panelGroup>
					</p:column>
					<p:column class="data-noprint" style="width:8em;">
						<h:panelGroup id="cvGroup">
							<p:linkButton id="btn_view_cv" outcome="/web/person"
								rendered="#{col.flag eq 'N' &amp;&amp; manageKtFormView.getStaffProfilePid(col.staff_no) ne null}"
								value="#{formBundle['form.view.profile']}" target="_blank">
								<f:param name="pid"
									value="#{manageKtFormView.getStaffProfilePid(col.staff_no)}" />
							</p:linkButton>
						</h:panelGroup>
					</p:column>
					<p:column class="data-noprint" style="width:2em; text-align:left;">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_delete"
							action="#{manageKtFormView.deleteRow(rowIndex)}"
							rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}"
							update="columnTable" immediate="true">
							<i class="fas fa-trash icon-action"
								title="#{formBundle['form.del']}" />
						</p:commandLink>
						<p:blockUI block="contentPanel" trigger="btn_delete" />
					</p:column>
				</p:dataTable>
				<br />
				<p:commandButton id="btn_add" icon="fas fa-plus"
					value="Add new contributor"
					rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}"
					style="width:260px" action="#{manageKtFormView.addRow()}"
					update="columnTable" immediate="true" />
				<p:blockUI block="contentPanel" trigger="btn_add" />
				<br /><br />
				<h:panelGroup styleClass="button-panel">
					<p:linkButton value="#{bundle['action.back']}" outcome="manageKtForm" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageKtFormView.paramDataLevel eq 'M' &amp;&amp; manageKtFormView.paramConsent eq ''}">		
							<f:param name="pid" value="#{manageKtFormView.paramPid}"/>
							<f:param name="form" value="#{manageKtFormView.paramFormCode}"/>
							<f:param name="period" value="#{manageKtFormView.paramPeriod}"/>
						</p:linkButton>
						<p:linkButton value="#{bundle['action.back']}" outcome="manageKtForm" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageKtFormView.paramDataLevel eq 'N'}">		
							<f:param name="pid" value="#{manageKtFormView.paramPid}"/>
							<f:param name="form" value="#{manageKtFormView.paramFormCode}"/>
							<f:param name="period" value="#{manageKtFormView.paramPeriod}"/>
							<f:param name="admin" value="Y"/>
							<f:param name="facDept" value="#{manageKtFormView.paramFacDept}"/>
						</p:linkButton>
						<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageKtFormView.paramConsent ne ''}">		
							<f:param name="pid" value="#{manageKtFormView.paramPid}"/>
							<f:param name="consent" value="#{manageKtFormView.paramConsent}"/>
							<f:param name="tabpage" value="2"/>
						</p:linkButton>
					<p:commandButton id="btn_sava"
							value="#{formBundle['form.save']}"
							rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.save}"
							oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="btn_savaAndGen"
							value="#{(manageKtFormView.selectedFormHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}"
							rendered="#{manageKtFormView.paramDataLevel eq 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.save}"
							oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="btn_savaAndPublish"
							value="#{bundle['action.saveAndPublish']}"
							rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel ne 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.saveAndPublishForm}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.kt.save.publish.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="btn_submit"
							value="#{formBundle['form.save']}"
							rendered="#{manageKtFormView.isCreator == false &amp;&amp; manageKtSumView.canModifyKt == true &amp;&amp; manageKtFormView.paramDataLevel eq 'M'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageKtFormView.submitConsent}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="Do you want to submit this record?"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="btn_snapshot" value="#{formBundle['form.take.snapshot']}"
							rendered="#{manageKtFormView.paramDataLevel eq 'P' &amp;&amp; manageKtFormView.selectedFormHeader_q.pk.form_no ne null}"
							style="margin-right:5px; margin-bottom:1px;" process="@this"
							action="#{manageKtFormView.takeSnapshot}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.take.snapshot.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="btn_delete"
							value="#{formBundle['form.del']}"
							rendered="#{manageKtFormView.canDelete == true &amp;&amp; manageKtSumView.canModifyKt == true}"
							style="margin-right:5px; margin-bottom:1px; background:#D32F2F; border:1px solid #D32F2F;"
							process="@this" action="#{manageKtFormView.deleteForm}">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.kt.del.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
					<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
						responsive="true" width="350">
						<p:commandButton value="No" icon="pi pi-times" type="button"
							styleClass="ui-confirmdialog-no ui-button-flat" />
						<p:commandButton value="Yes" icon="pi pi-check" type="button"
							styleClass="ui-confirmdialog-yes" />
					</p:confirmDialog>
				</h:panelGroup>
				<p:scrollTop target="parent" threshold="100" styleClass="custom-scrolltop" icon="pi pi-arrow-up" />
			</h:form>

		</p:panel>
	</ui:define>

</ui:composition>