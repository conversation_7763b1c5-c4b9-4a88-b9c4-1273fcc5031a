<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions"
				xmlns:h="http://java.sun.com/jsf/html" 
				xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata>
		<f:viewParam name="id" value="#{batchEditView.batchId}"/>
	</f:metadata>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-file-import"></i> Batch Edit Page</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Upload Source
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			#{batchEditView.getCa().description}
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			File Name
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			#{batchEditView.getBatch().file_name}
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Batch Key
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			#{batchEditView.getBatch().batch_key}
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Remarks
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:inputText id="remarks" style="width:50%"
						 value="#{batchEditView.batch.remarks}">
			</p:inputText>
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Number of data
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			#{batchEditView.getDataNum()}
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Last Modified By
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			#{batchEditView.getBatch().userstamp}
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Last Modified Date
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			#{batchEditView.getBatch().timestamp}
		</div>
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="updateButton" value="Update" icon="pi pi-upload" ajax="false"
						 update="@form"
						 action="#{batchEditView.update()}" ></p:commandButton><p:spacer width="10"/>
		 <p:commandButton id="deleteButton" value="Delete" icon="pi pi-upload" ajax="false"
						 update="@form"
						 action="#{batchEditView.delete()}" ></p:commandButton><p:spacer width="10"/>
		<p:dataTable id="storeDataTable"
					 value="#{batchEditView.getStoreList()}" 
					 var="store"
					 styleClass="default-dataTable"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					
					<p:column width="3em">
						<f:facet name="header">Source ID</f:facet>
						<h:outputText value="#{store.pk.source_id}" />
					</p:column>
					
					<!-- Output PURE -->
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'OUTPUT_PURE'}">
						<f:facet name="header">Contributor ID</f:facet>
						<h:outputText value="#{store.getContributorIDWithoutEmail()}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'OUTPUT_PURE'}">
						<f:facet name="header">Title of Research Output</f:facet>
						<h:outputText value="#{store.col_2}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'OUTPUT_PURE'}">
						<f:facet name="header">Output Category</f:facet>
						<h:outputText value="#{store.col_3}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'OUTPUT_PURE'}">
						<f:facet name="header">Name of Publication/Conference/Journal in which the output appears</f:facet>
						<h:outputText value="#{store.col_5}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'OUTPUT_PURE'}">
						<f:facet name="header">Date</f:facet>
						<h:outputText value="#{store.col_9}/#{store.col_10}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'OUTPUT_PURE'}">
						<f:facet name="header">Output APA</f:facet>
						<h:outputText value="#{store.col_13}"/>
					</p:column>
					
					<!-- Project HREC -->
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PROJECT_HREC'}">
						<f:facet name="header">Investigator ID</f:facet>
						<h:outputText value="#{store.col_1}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PROJECT_HREC'}">
						<f:facet name="header">Title</f:facet>
						<h:outputText value="#{store.col_7}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PROJECT_HREC'}">
						<f:facet name="header">Funding Source</f:facet>
						<h:outputText value="#{store.col_5}-#{store.col_6}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PROJECT_HREC'}">
						<f:facet name="header">Project Summary</f:facet>
						<h:outputText value="#{store.col_91}#{store.col_92}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PROJECT_HREC'}">
						<f:facet name="header">From Date</f:facet>
						<h:outputText value="#{store.col_8}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PROJECT_HREC'}">
						<f:facet name="header">To Date</f:facet>
						<h:outputText value="#{store.col_9}"/>
					</p:column>
					
					<!-- Patent KT -->
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PATENT_KT'}">
						<f:facet name="header">Inventor ID</f:facet>
						<h:outputText value="#{store.col_10}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PATENT_KT'}">
						<f:facet name="header">Patent Type</f:facet>
						<h:outputText value="#{store.col_1}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PATENT_KT'}">
						<f:facet name="header">Patent Name</f:facet>
						<h:outputText value="#{store.col_2}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PATENT_KT'}">
						<f:facet name="header">Detail Description</f:facet>
						<h:outputText value="#{store.col_6}"/>
					</p:column>
					
					<p:column width="3em" rendered="#{batchEditView.getBatch().area_code == 'PATENT_KT'}">
						<f:facet name="header">Date</f:facet>
						<h:outputText value="#{store.col_7}#{store.col_8}"/>
					</p:column>
		</p:dataTable>
	</h:form>
	
	
	</p:panel>
   </ui:define>
</ui:composition>