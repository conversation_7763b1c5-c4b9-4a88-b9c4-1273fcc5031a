<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="pid" value="#{staffInfoView.paramPid}" />
		<f:viewParam name="job_seq" value="#{staffInfoView.paramJobSeq}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel" >
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="Edit Career Overview">
   				<f:param value="pid" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		
		<p:messages id="msgs" showDetail="true" closable="true" globalOnly="true">
			<p:autoUpdate/>
		</p:messages>
		
		<p:panelGrid styleClass="edit-panel">
			<!-- Company -->
			<p:row>
				<p:column><b>Organization</b></p:column>
				<p:column style="width:50%;">				
					<p:message for="edit_company"/>
					<p:inputText id="edit_company" required="true" 
						requiredMessage = "Organization is mandatory"
						value="#{staffInfoView.selectedEmploymentHist_edit.company}" style="width:70%;" >
						 <f:validateLength maximum="200"/>

					</p:inputText>


				</p:column>
				
				<!-- Date Form  -->
				<p:column><b>Date From</b></p:column>

				<p:column>
					<p:message id="dateFromMsg" for="edit_dateFrom" />
					<p:datePicker id="edit_dateFrom" label="Date From" required="true"
						requiredMessage = "Date From is mandatory" readonlyInput="true"
						value="#{staffInfoView.selectedEmploymentHist_edit.from_date}" 
						pattern="MM/yyyy" yearNavigator="true" view="month"  showButtonBar="true" yearRange="1900:2050"/>

				</p:column>
				

			</p:row>

			<p:row>

				
				<!-- Job Titile -->
				<p:column>
					<b>Title</b>
				</p:column>
				<p:column>	
					<p:message id="jobTitleMsg" for="edit_jobTitle" />
					<p:inputText id="edit_jobTitle" label="job Title" required="true" 
						requiredMessage = "Title is mandatory"
						value="#{staffInfoView.selectedEmploymentHist_edit.job_title}" style="width:70%;" >
						<f:validateLength maximum="200"/>
					</p:inputText>


				</p:column>
				
				
				<!-- Date To -->
				<p:column><b>Date To</b></p:column>
				<p:column>
					<p:message id="dateToMsg" for="edit_dateTo" />
					<p:datePicker id="edit_dateTo" readonlyInput="true" label="Date To"
						value="#{staffInfoView.selectedEmploymentHist_edit.to_date}"
						pattern="MM/yyyy" yearNavigator="true" view="month" showButtonBar="true" yearRange="1900:2050"/>
				</p:column>
			</p:row>
			
			<p:row>	
				<!-- Job Details-->
				<p:column style="vertical-align: top;">
					<b>Description</b>
				</p:column>
				<p:column style="vertical-align: top;">	
					<p:message id="jobDetailsMsg" for="edit_jobDetails" />
					<p:textEditor id="edit_jobDetails" label="Description" height="220" validatorMessage="Description is too long."
						 counter="display_count"
						 value="#{staffInfoView.selectedEmploymentHist_edit.job_details}" 
						 maxlength="1000"
						 style="width:70%;" counterTemplate="Characters remaining: {0}">
						 <f:facet name="toolbar">
				             <span class="ql-formats">
				                <button class="ql-bold"></button>
				                <button class="ql-italic"></button>
				                <button class="ql-underline"></button>
				              </span>
				        </f:facet>
						<f:validateLength maximum="1000"/>
					</p:textEditor>
					<div>
						<h:outputText id="display_count" class="block" />
					</div>
					
				</p:column>
				
				<!-- Is Current-->
			
				<p:column style="vertical-align: top;">
					<b>Is Current?</b>
				</p:column>
				<p:column style="vertical-align: top;">
					<p:selectOneMenu id="is_current" value="#{staffInfoView.selectedEmploymentHist_edit.is_current}" required="true">
						<f:selectItem itemLabel="NO" itemValue="N"/>
	                    <f:selectItem itemLabel="YES" itemValue="Y"/>
                	</p:selectOneMenu>
				</p:column>
				
				
	
			</p:row>							
						
		</p:panelGrid>
		
		<br/>
			<h:panelGroup styleClass="button-panel">
				<p:linkButton value="#{bundle['action.back']}" outcome="manageInfo" icon="pi pi-arrow-left"  style="margin-right:5px;">		
					<f:param name="tab" value="1"/>
				</p:linkButton>	
				<p:commandButton value="#{bundle['action.save']}" action="#{staffInfoView.updateEmpHistoryForm()}" update="@form"></p:commandButton>
				<p:commandButton  value="#{bundle['action.delete']}" 
									style="margin-left:5px; background:#D32F2F; border:1px solid #D32F2F;" 
									process="@this" action="#{staffInfoView.deleteSelectedEmploymentHist}">
								<p:confirm header="#{formBundle['form.confirm']}" 	message="Are you sure to delete this career overview?" icon="pi pi-info-circle" />
				</p:commandButton>
				<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true" width="350">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Yes" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
				</p:confirmDialog>
				
				
			</h:panelGroup>
			
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>