<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title">Academic Staff Console</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	  <p:linkButton outcome="manageInfo" value="Manage My Info." icon="pi pi-user-edit" style="margin-right:20px;">
	  	<f:param name="pid" value="#{staffInfoView.paramPid}"/>
	  </p:linkButton>

	<h:form id="dataForm">
		<p:panelGrid styleClass="edit-panel">
			<p:row>
				<p:column colspan="2">
					<h:outputText style="font-size:22px;" value="#{staffInfoView.sIdentity.title} #{staffInfoView.sIdentity.fullname} #{staffInfoView.sIdentity.chinesename}"/>
				</p:column>
			</p:row>
			<p:row>
				<p:column colspan="2">
			    	<h:outputText value="#{staffInfoView.getStaffPost()}" escape="false"/>
		        </p:column>
			</p:row>
			<p:row>
				<p:column>
					Email:
				</p:column>
				<p:column>
					#{staffInfoView.iUserInfo.email}
				</p:column>
			</p:row>
			<p:row>
				<p:column>
					Phone No.:
				</p:column>
				<p:column>
					#{staffInfoView.iUserInfo.phone}
				</p:column>
			</p:row>			
			<p:row>
				<p:column>
					Fax No.:
				</p:column>
				<p:column>
					#{!empty staffInfoView.iUserInfo.fax ? staffInfoView.iUserInfo.fax : 'N/A'}
				</p:column>
			</p:row>				
			<p:row>
				<p:column>
					URL:
				</p:column>
				<p:column>
					<ui:fragment rendered="#{!empty staffInfoView.sInfo.url}">
					<h:outputLink class="cv_website_url" value="http://#{staffInfoView.sInfo.url}"><h:outputText value="http://#{staffInfoView.sInfo.url}" escape="false"></h:outputText></h:outputLink>
					</ui:fragment>
				</p:column>
			</p:row>				
		</p:panelGrid>
	</h:form>	
	<br/><hr/>
	<p:linkButton outcome="manageAsst" value="Manage Assistant Right" icon="pi pi-users" style="margin-right:20px;">
	    </p:linkButton>	
	<h:form id="asstForm">
		<p:dataTable id="asstTable" var="data" value="#{staffInfoView.asstList}" sortMode="single"
						rowKey="#{data.pk.assistant_id}" tableStyle="table-layout: fixed;">
			<p:column headerText="Assistant Name" id="asstName" sortBy="#{data.assistant_name}">
                <h:outputText value="#{data.assistant_name}"/>
            </p:column>
             <p:column headerText="Access Right" id="access_right" sortBy="#{data.access_right}">
                <h:outputText value="#{data.access_right}"/>
            </p:column>
            <p:column headerText="Status" id="status" sortBy="#{data.status}">
                <h:outputText value="#{data.status}"/>
            </p:column>            
           <p:column headerText="Last updated date" id="requestedDate" sortBy="#{data.timestamp}">
                <h:outputText value="#{data.timestamp}">
                	<f:convertDateTime pattern="dd-MM-yyyy HH:mm" />
                </h:outputText>
            </p:column>              
		</p:dataTable>
	</h:form>
	<br/><hr/>
	<p:linkButton outcome="dashboard" value="Back" icon="pi pi-arrow-left" style="margin-right:20px;"></p:linkButton>	
	</p:panel>
   </ui:define>
</ui:composition>