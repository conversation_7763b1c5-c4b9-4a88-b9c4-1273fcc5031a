<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	
	<f:metadata>
		<!-- <f:viewParam name="pid" value="#{cvDisplayView.paramPid}" /> -->
	</f:metadata>
	
	<f:event listener="#{cvDisplayView.checkValid}" type="preRenderView" />
	<ui:define name="html_head">
		<style>
			body .ui-chkbox:has(.ui-state-disabled) +label{
				font-weight:700; 
				color: #666666 !important;
				}
			.ui-state-disabled > label{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-selectonemenu-items {
			    display: inline !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent">
	<p:panel id="errorPanel" rendered="#{cvView.paramPid == ''}">
		<br/><h:outputText value = "The service is not available now. Please contact administrator if you have any inquiry." style = "color:red; font-weight: bold"/>
	</p:panel>
	
	<p:panel id="contentPanel" rendered="#{cvView.paramPid != ''}"> 
	<span class="admin-content-title"><i class="fas fa-user-edit"></i> Export My Data</span>
	<p:staticMessage severity="warn" detail="#{sysParamView.getValue('BETA_MSG_EXPORT_MY_DATA')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('BETA_VER') eq 'Y' and sysParamView.getValue('BETA_MSG_EXPORT_MY_DATA') ne null}"/>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	<h:form id="dataForm" target = "_blank">
          <p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Exportable Information</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<!-- 1st -->
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:selectBooleanCheckbox style="font-weight:700; margin-left: 3px;" itemLabel="Select All" value="#{cvView.selectAllExportData}">
				        <f:ajax listener="#{cvView.onSelectAllExportData}" render="exportDataCheckbox" />
				    </p:selectBooleanCheckbox>
				    <p:selectManyCheckbox id="exportDataCheckbox" title="Exportable Information" label="Exportable Information" value="#{cvView.selectedOptions}"
												unselectable="true" layout="grid" columns="4" styleClass="grid-radio">
						<f:selectItems value="#{cvView.exportDataList}"/>
					</p:selectManyCheckbox>
				</div>
			</div>
		</p:panel>
		<p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Filtering Criteria</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-2 ui-lg-2">
					<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10">
					<p:selectCheckboxMenu id="cdcfPeriod"  value="#{cvView.selectedCdcfPeriods}" style="margin-top:4px; margin-right:20px;"
																	 multiple="true" filter="true">
									<f:selectItems value="#{cvView.cdcfPeriodList}" var="o" 
														itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />
					</p:selectCheckboxMenu>	
				</div>
				<div class="ui-g-12 ui-md-2 ui-lg-2">
					<p:outputLabel value="Output Type" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10">
					<p:selectCheckboxMenu id="outputType"  value="#{cvView.selectedOutputTypes}" style="margin-top:4px; margin-right:20px;"
																	 multiple="true" filter="true">
									<f:selectItems value="#{manageOutputView.outputTypeList}" var="o" 
														itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}" />
					</p:selectCheckboxMenu>	
				</div>
				<div class="ui-g-12 ui-md-2 ui-lg-2">
					<p:outputLabel value="RI Date Period" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
				</div>
				<div class="ui-g-12 ui-md-1 ui-lg-1">
					<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3">
					<p:datePicker id="riStartDate" view="date"
												title="Start Date (DD/MM/YYYY)" 
												label="Start Date (DD/MM/YYYY)" 
												value="#{cvView.selectedStartDate}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"/>	
				</div>
				<div class="ui-g-12 ui-md-1 ui-lg-1">
					<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
				</div>
				<div class="ui-g-12 ui-md-5 ui-lg-5">
					<p:datePicker id="riEndDate" view="date"
												title="End Date (DD/MM/YYYY)" 
												label="End Date (DD/MM/YYYY)" 
												value="#{cvView.selectedEndDate}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"/>	
				</div>
				<div class="ui-g-12 ui-md-2 ui-lg-2">
					<p:outputLabel value="Funding Source" style="vertical-align: -webkit-baseline-middle; font-weight:700;"/>
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10">
					<p:selectCheckboxMenu id="fundingSource"  value="#{cvView.selectedFundingSources}" style="margin-top:4px; margin-right:20px;"
																	 multiple="true" filter="true">
									<f:selectItems value="#{cvView.fundSource_list}" var="sc"  
														itemLabel="#{sc.description}" itemValue="#{sc.pk.lookup_code}" />
					</p:selectCheckboxMenu>	
				</div>
            </div>
          </p:panel>
          
          <p:panel>
			<f:facet name="header">
				<div>
					<span style="color:#1f1645;">Remarks</span>
				</div>
			</f:facet>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="7" counter="display" maxlength="4000"
											value="#{cvView.remarks}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
                      <br/>
      				  <h:outputText id="display" class="p-d-block" />
				</div>
			</div>
		</p:panel>
		<br/>
		<p:linkButton outcome="dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:commandButton id="btn_export_pdf" value="Export PDF File" icon="pi pi-download"
								  style="margin-right:5px;"
								  ajax = "false"
						  		  action="#{cvView.exportMyDataInPdf()}"
						  		  oncomplete="window.scrollTo(0,0);">					  		  
		</p:commandButton>
		<p:commandButton id="btn_export_docx" value="Export Word File" icon="pi pi-download"
								  style="margin-right:5px;"
								  ajax = "false"
						  		  action="#{cvView.exportMyDataInDocx()}"
						  		  oncomplete="window.scrollTo(0,0);">							  		  
		</p:commandButton>	
		
		<!--  <p:linkButton outcome="createMyCV" value="Create My CV" icon="pi pi-arrow-right"></p:linkButton>-->
				
		<p:panel styleClass = "no-border">
			<br/><h:outputText value = "*The format of the exported Word files may vary depending on the version of Word being used." style = "color:blue; font-weight: bold"/>
		</p:panel>
		
	</h:form>
	</p:panel>
   </ui:define>
</ui:composition>