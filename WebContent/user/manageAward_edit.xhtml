<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:component="http://java.sun.com/jsf/composite/component"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	template="/resources/template/template.xhtml">

	<f:metadata>
		<f:event listener="#{manageAwardView.getCanModify}"
			type="preRenderView" />
		<f:event listener="#{manageAwardView.checkValid}" type="preRenderView" />
		<f:viewParam name="no" value="#{manageAwardView.paramNo}" />
		<f:viewParam name="pid" value="#{manageAwardView.paramPid}" />
		<f:viewParam name="dataLevel" value="#{manageAwardView.paramDataLevel}" />
		<f:viewParam name="consent" value="#{manageAwardView.paramConsent}" />
		<f:viewParam name="area_code" value="#{manageAwardView.paramArea_code}" />
		<f:viewParam name="source_id" value="#{manageAwardView.paramSource_id}" />
		<f:viewParam name="staff_number" value="#{manageAwardView.paramStaff_number}" />
	</f:metadata>
	<ui:define name="html_head">
		<style>
			li.ui-state-disabled{
				background-color: #d1e1eb !important;
				color:#1f1645 !important;
				opacity:1.0 !important;
				font-weight:700 !important;
			}
			.ui-widget-header .ui-inputfield, .ui-widget-content .ui-inputfield{
				width: 100%;
			}
		</style>
	</ui:define>
	<ui:define name="mainContent">
		<p:importConstants type="hk.eduhk.rich.Constant" var="const" />
		<p:panel id="contentPanel">
			<span class="admin-content-title"><i class="fas fa-trophy"></i>
				Manage Prizes and Awards</span>
			<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
			<p:scrollTop />
			<h:outputText value="You don't have access right." rendered="#{manageAwardView.hasAccessRight() == false}" style="color:#DB4437; font-size:20px; font-weight:700;"/>
			<h:form id="editForm"
				rendered="#{manageAwardView.hasAccessRight() == true}">
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:linkButton value="#{bundle['action.back']}" outcome="manageAward" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageAwardView.paramDataLevel eq 'M' &amp;&amp; manageAwardView.paramConsent eq ''}">		
							<f:param name="pid" value="#{manageAwardView.paramPid}"/>
						</p:linkButton>
						<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageAwardView.paramConsent ne ''}">		
							<f:param name="pid" value="#{manageAwardView.paramPid}"/>
							<f:param name="consent" value="#{manageAwardView.paramConsent}"/>
							<f:param name="tabpage" value="2"/>
						</p:linkButton>
						<p:defaultCommand target="dummy"/>
						<p:commandButton id="dummy" process="@none" global="false" style="display:none;"/>
						<p:commandButton id="top_btn_sava"
							value="#{formBundle['form.save']}"
							rendered="#{manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true &amp;&amp; manageAwardView.paramDataLevel ne 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageAwardView.save}"
							oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="top_btn_savaAndGen"
							value="#{(manageAwardView.selectedAwardHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}"
							rendered="#{manageAwardView.paramDataLevel eq 'C'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageAwardView.save}"
							oncomplete="window.scrollTo(0,0);">
						</p:commandButton>
						<p:commandButton id="top_btn_savaAndPublish"
							value="#{bundle['action.saveAndPublish']}"
							rendered="#{manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageAwardView.saveAndPublishForm}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.save.publish.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_submit"
							value="#{formBundle['form.save']}"
							rendered="#{manageAwardView.isCreator == false &amp;&amp; manageAwardView.canModify == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'}"
							style="margin-right:5px; margin-bottom:1px;" update="@form messages"
							action="#{manageAwardView.submitConsent}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="Do you want to submit this record?"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_snapshot" value="#{formBundle['form.take.snapshot']}"
							rendered="#{manageAwardView.paramDataLevel eq 'P'}"
							style="margin-right:5px; margin-bottom:1px;" process="@this"
							action="#{manageAwardView.takeSnapshot}"
							oncomplete="window.scrollTo(0,0);">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.take.snapshot.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:commandButton id="top_btn_delete"
							value="#{formBundle['form.del']}"
							rendered="#{manageAwardView.canDelete == true &amp;&amp; manageAwardView.canModify == true}"
							style="margin-right:5px; margin-bottom:1px; background:#D32F2F; border:1px solid #D32F2F;"
							process="@this" action="#{manageAwardView.deleteForm}">
							<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.del.desc']}"
								icon="pi pi-info-circle" />
						</p:commandButton>
						<p:linkButton id="btn_p_level" outcome="manageAward_edit"
							rendered="#{manageAwardView.paramDataLevel eq 'P' &amp;&amp; manageAwardView.checkSnapshotExists()}"
							value="#{formBundle['form.ri.goto.lv.c']}" icon="pi pi-chevron-right"
							style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
							<f:param name="pid" value="#{manageAwardView.getParamPid()}" />
							<f:param name="no"
								value="#{manageAwardView.selectedAwardHeader_q.award_no}" />
							<f:param name="dataLevel" value="C" />
						</p:linkButton>
						<p:linkButton id="btn_c_level" outcome="manageAward_edit"
							rendered="#{manageAwardView.paramDataLevel eq 'C'}"
							value="#{formBundle['form.ri.goto.lv.p']}" icon="pi pi-chevron-right"
							style="margin-right:5px; margin-bottom:1px; background: #f06524; border:1px solid #f06524;">
							<f:param name="pid" value="#{manageAwardView.getParamPid()}" />
							<f:param name="no"
								value="#{manageAwardView.selectedAwardHeader_q.award_no}" />
							<f:param name="dataLevel" value="P" />
						</p:linkButton>
						<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
							responsive="true" width="350">
							<p:commandButton value="No" icon="pi pi-times" type="button"
								styleClass="ui-confirmdialog-no ui-button-flat" />
							<p:commandButton value="Yes" icon="pi pi-check" type="button"
								styleClass="ui-confirmdialog-yes" />
						</p:confirmDialog>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-2"
						style="font-size: 18px; font-weight: 700; text-align: right;">
						<p:outputLabel value="#{formBundle['form.ri.status.lv.p']}"
							style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;"
							rendered="#{manageAwardView.paramDataLevel eq 'P'}" />
						<p:outputLabel value="#{formBundle['form.ri.status.lv.c']}"
							style="font-size:18px; font-weight:700; text-align:right; border-left:10px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:5px 6px;"
							rendered="#{manageAwardView.paramDataLevel eq 'C'}" />
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
							<i class="fas fa-tag" style="margin-right: 5px;"></i>#{formBundle['form.ri.status']}
				</div>
				<hr/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="background:#00a2c7; font-size:18px; font-weight:700;">
						<p:outputLabel style="color:#1f1645;" value="#{formBundle['form.header.status']} " />
						<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{manageAwardView.selectedAwardHeader_q.publish_status}" rendered="#{manageAwardView.selectedAwardHeader_q.publish_status ne 'PUBLISHED'}"/>
						<p:outputLabel style="color:#fff; text-transform: uppercase;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{manageAwardView.selectedAwardHeader_q.publish_status eq 'PUBLISHED'}"/>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.at']} " />
						<h:outputText class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.last_modified_date}" >
							    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.modified.by']} " />
						<p:outputLabel class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.last_modified_by}" />
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.at']} " />
						<h:outputText class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.last_published_date}" >
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
		
					<div class="ui-g-12 ui-md-3 ui-lg-3">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.last.published.by']} " />
						<p:outputLabel class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.last_published_by}" />
					</div>
					
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
					
					<div class="ui-g-6 ui-md-4 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.institute']}" />
					</div>
					<div class="ui-g-6 ui-md-8 ui-lg-4">
						<p:message for="inst_display_ind"/>
						<p:selectOneMenu id="inst_display_ind" required="#{manageAwardView.isRdoAdmin == true}"
													disabled="#{manageAwardView.isRdoAdmin == false || manageAwardView.paramDataLevel eq 'M'}" 
													title="#{formBundle['form.header.display.institute']}" 
													label="#{formBundle['form.header.display.institute']}"
													value="#{manageAwardView.selectedAwardHeader_q.inst_display_ind}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							</p:selectOneMenu>
					</div>
					
					<div class="ui-g-6 ui-md-4 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute']}" />
					</div>
					<div class="ui-g-6 ui-md-8 ui-lg-4">
						<p:message for="inst_verified_ind"/>
						<p:selectOneMenu id="inst_verified_ind" required="#{manageAwardView.isRdoAdmin == true}" 
													disabled="#{manageAwardView.isRdoAdmin == false || manageAwardView.paramDataLevel eq 'M'}"
													title="#{formBundle['form.header.verify.institute']}" 
													label="#{formBundle['form.header.verify.institute']}"
													value="#{manageAwardView.selectedAwardHeader_q.inst_verified_ind}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   					
								<!--  <p:ajax event="valueChange" update="inst_verified_date"/>-->
							</p:selectOneMenu>		
					</div>
					<!--
					<div class="ui-g-12 ui-md-3 ui-lg-2">			
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.verify.institute.date']}" />
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="inst_verified_date"/>
						<p:datePicker id="inst_verified_date" 
												title="#{formBundle['form.header.verify.institute.date']}" 
												label="#{formBundle['form.header.verify.institute.date']}" 
												value="#{manageAwardView.selectedAwardHeader_q.inst_verified_date}" 
												maxdate="#{manageAwardView.currentDate}"
												pattern="yyyy-M-d" showIcon="true" 
												disabled="#{manageAwardView.isRdoAdmin == false || manageAwardView.paramDataLevel eq 'M' || manageAwardView.selectedAwardHeader_q.inst_verified_ind eq 'N'}"/>			
					</div>
					-->
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.generate']}" />
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
						<p:outputLabel class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_gen_ind eq 'Y'?'Yes':'No'}"/>
						<p:outputLabel class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_gen_ind eq 'Y'?' — ':''}"/>
						<h:outputText class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_gen_date}" rendered="#{manageAwardView.selectedAwardHeader_q.cdcf_gen_ind eq 'Y'}">
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<h:outputText class="riForm-item-title" value="#{formBundle['form.cdcf.change']}" rendered="#{manageAwardView.isRdoAdmin == true}"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
							<h:outputText class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_changed_ind eq 'Y'?'Yes':'No'}" rendered="#{manageAwardView.isRdoAdmin == true}"/>
							<p:linkButton id="btn_compare" outcome="riComparisonReport" rendered="#{manageAwardView.selectedAwardHeader_q.cdcf_changed_ind eq 'Y' &amp;&amp; manageAwardView.isRdoAdmin == true}" value="Compare Snapshot" style="margin-left:7px;" target="_blank">
			  					<f:param name="no" value="#{manageAwardView.selectedAwardHeader_q.award_no}"/>
			  					<f:param name="riType" value="award"/>
			  				</p:linkButton>	
					</div>
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.process']}" />
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
						<p:outputLabel class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_processed_ind eq 'Y'?'Yes':'No'}"/>
						<p:outputLabel class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_processed_ind eq 'Y'?' — ':''}"/>
						<h:outputText class="riForm-item-ans" value="#{manageAwardView.selectedAwardHeader_q.cdcf_processed_date}" rendered="#{manageAwardView.selectedAwardHeader_q.cdcf_processed_ind eq 'Y'}">
						    <f:convertDateTime pattern="yyyy-MM-dd HH:mm" />
						</h:outputText>
					</div>
			
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.cdcf.status']}" />
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-4">
						<p:message for="cdcf_status"/>
						<p:selectOneMenu id="cdcf_status" title="#{formBundle['form.cdcf.status']}" label="#{formBundle['form.cdcf.status']}" value="#{manageAwardView.selectedAwardHeader_q.cdcf_status}" 
													required="#{manageAwardView.isRdoAdmin == true}" 
													disabled="#{manageAwardView.isRdoAdmin == false || manageAwardView.paramDataLevel eq 'M'}">
								<f:selectItems value="#{manageAwardView.cdcfStatusList}"/>  
							</p:selectOneMenu>	
					</div>

					
					<div class="ui-g-12 ui-md-12 ui-lg-12" style="border-bottom:1px dashed #055588"></div>
					
					<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageAwardView.paramDataLevel eq 'M'?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.display.ri.profile']}" style="padding-right:20px;" rendered="#{manageAwardView.paramDataLevel eq 'M'}"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageAwardView.paramDataLevel eq 'M'?'':'display:none;'}">
						<h:panelGroup id="displayRIGroup">
							<p:selectOneMenu title="#{formBundle['form.header.display.ri.profile']}" value="#{manageAwardView.selectedAwardDetails_q.display_ind}" 
													rendered="#{manageAwardView.paramDataLevel eq 'M'}"
													disabled="#{manageAwardView.canModify == false || manageAwardView.selectedAwardDetails_q.consent_ind ne 'Y'}">
								<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
								<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							</p:selectOneMenu>
						</h:panelGroup>
					</div>	
					
					<div class="ui-g-4 ui-md-6 ui-lg-3" style="#{manageAwardView.isContributor == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.header.consent.ri']}" style="padding-right:20px;" rendered="#{manageAwardView.isContributor == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'}"/>
					</div>
					<div class="ui-g-8 ui-md-6 ui-lg-9" style="#{manageAwardView.isContributor == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'?'':'display:none;'}">	
						<p:selectOneMenu title="#{formBundle['form.header.consent.ri']}" value="#{manageAwardView.selectedAwardDetails_q.consent_ind}" 
												rendered="#{manageAwardView.isContributor == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'}"
												disabled="#{manageAwardView.canModify == false || manageAwardView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Unconfirmed" itemValue="U"/>
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y"/>
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N"/>	   
							<p:ajax event="change" update="displayRIGroup" listener="#{manageAwardView.setDisplyRI()}"/>
						</p:selectOneMenu>
					</div>	
										
					<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageAwardView.isRdoAdmin?'':'display: none;'}">
							<p:outputLabel class="riForm-item-title" value="#{formBundle['form.remarks']}" rendered="#{manageAwardView.isRdoAdmin == true}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageAwardView.isRdoAdmin?'':'display: none;'}">
							<p:message for="remarks"/>
							<p:inputTextarea id="remarks" label="#{formBundle['form.remarks']}" style="width: 90%;" rows="7" counter="display" maxlength="1000" rendered="#{manageAwardView.isRdoAdmin == true}"
													value="#{manageAwardView.selectedAwardHeader_q.remarks}"
		                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"/>
		                      <br/>
		      				  <h:outputText id="display" class="p-d-block" />
					</div>	
				</div>
		
				<br/>
				<div class="form-sub-title">
							<i class="fas fa-tag" style="margin-right: 5px;"></i>Prize/Award Details
				</div>
				<hr/>

				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Recipient Award By-line
						EdUHK</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<h:panelGroup id="ied_work_ind_ans_n">
							<p:outputLabel id="ied_work_ind_ans_n_label"
								value="This prize/award will not be counted in the CDCF."
								style="border-left:8px solid #f06524; background: #026539; color:#fff; border-radius:2px; padding:1px 4px; margin-bottom:5px; display:block; width:90%;"
								rendered="#{manageAwardView.selectedAwardHeader_p.ied_work_ind eq 'N'}" />
						</h:panelGroup>
						<p:message for="ied_work_ind" />
						<p:selectOneMenu id="ied_work_ind"
							title="Recipient Award By-line EdUHK"
							value="#{manageAwardView.selectedAwardHeader_p.ied_work_ind}"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
							<f:selectItem itemLabel="#{bundle['val.yes']}" itemValue="Y" />
							<f:selectItem itemLabel="#{bundle['val.no']}" itemValue="N" />
							<p:ajax event="valueChange" update="ied_work_ind_ans_n" />
						</p:selectOneMenu>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Name of Prize/Award</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="award_nameMsg" for="award_name" />
						<p:inputText id="award_name" titla="Name of Prize/Award"
							label="Name of Prize/Award" maxlength="100"
							value="#{manageAwardView.selectedAwardHeader_p.award_name}"
							style="width: 90%;"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Name of Organization
						Conferring the Prize/Award</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="award_orgMsg" for="award_org" />
						<p:inputText id="award_org"
							label="Name of Organization Conferring the Prize/Award"
							maxlength="100"
							value="#{manageAwardView.selectedAwardHeader_p.org_name}"
							style="width: 90%;"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Brief Description of
						Prize/Award (around 40 words)</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message id="award_short_descMsg" for="award_short_desc" />
						<p:inputText id="award_short_desc"
							label="Brief Description of Prize/Award" maxlength="250"
							value="#{manageAwardView.selectedAwardHeader_p.short_desc}"
							style="width: 90%;"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Detail Description of
						Prize/Award</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:inputTextarea id="award_long_desc" style="width: 90%;" rows="7"
							counter="award_long_desc_display" maxlength="1000"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}"
							value="#{manageAwardView.selectedAwardHeader_p.full_desc}"
							counterTemplate="#{bundle['form.remaining.characters']} {0}"
							autoResize="false" />
						<br />
						<h:outputText id="award_long_desc_display" class="p-d-block" />
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">Date of Receipt
						(dd/mm/yyyy)</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="awardDay" />
						<p:message for="awardMonth" />
						<p:message for="awardYear" />
						<p:selectOneMenu id="awardDay" title="Date of Receipt - Day"
							label="Date of Receipt - Day"
							value="#{manageAwardView.selectedAwardHeader_p.award_day}"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
							<f:selectItem itemLabel="" itemValue="#{null}"/>
							<f:selectItems value="#{manageAwardView.dayList}" var="o"
								itemLabel="#{o}" itemValue="#{o}" />
								<p:ajax event="change"/>
						</p:selectOneMenu>
						<p:selectOneMenu id="awardMonth" title="Date of Receipt - Month"
							label="Date of Receipt - Month"
							value="#{manageAwardView.selectedAwardHeader_p.award_month}"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{manageAwardView.monthList}" var="o"
								itemLabel="#{o}" itemValue="#{o}" />
							<p:ajax event="change" update="awardDay" />
						</p:selectOneMenu>
						<p:selectOneMenu id="awardYear" title="Date of Receipt - Year"
							label="Date of Receipt - Year"
							value="#{manageAwardView.selectedAwardHeader_p.award_year}"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
							<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
							<f:selectItems value="#{manageAwardView.yearList}" var="o"
								itemLabel="#{o}" itemValue="#{o}" />
							<p:ajax event="change" update="awardDay" />
						</p:selectOneMenu>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fas fa-tag" style="margin-right:5px;"></i>Name of #{formBundle['form.ri.creator.award']}(s) in sequential order
				</div>
				<hr/>
				<p:messages for="columnTable"/>
				
				<p:dataTable id="columnTable" class="riFormTable"
					value="#{manageAwardView.awardDetails_p_list}" var="col"
					widgetVar="columnTableWV" rows="50" reflow="true"
					 rowsPerPageTemplate="10,20,50,100"
					paginator="true"
					paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
					currentPageReportTemplate="Total Number of Recipient(s): {totalRecords} (Row: {startRecord} - {endRecord}, Page: {currentPage} / {totalPages})"
					style="max-width:99%;" rowIndexVar="rowIndex">

					<p:column class="data-noprint" style="width:1em; text-align:left;">
						<f:facet name="header">Up</f:facet>
						<p:commandLink action="#{manageAwardView.moveColumnUp(rowIndex)}"
							update="columnTable" immediate="true"
							rendered="#{rowIndex gt 0 &amp;&amp; manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true}">
							<i class="fas fa-caret-up icon-action" title="Move up" />
						</p:commandLink>
					</p:column>

					<p:column class="data-noprint" style="width:1em; text-align:left;">
						<f:facet name="header">Dn.</f:facet>
						<p:commandLink
							action="#{manageAwardView.moveColumnDown(rowIndex)}"
							update="columnTable" immediate="true"
							rendered="#{rowIndex lt manageAwardView.awardDetails_p_list.size()-1 &amp;&amp; manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true}">
							<i class="fas fa-caret-down icon-action" title="Move down" />
						</p:commandLink>
					</p:column>

					<p:column style="text-align:left; width:5em;">
						<f:facet name="header">No.</f:facet>
						<ui:fragment
							rendered="#{manageAwardView.isRiCreator(col.staff_no)}">
							<i class="fa fa-star faa-pulse animated-hover" title="RI creator"
								style="font-size: 9px; color: #f06524; vertical-align: middle;"></i>
						</ui:fragment> #{rowIndex +1}		
					</p:column>

					<p:column style="width:15em; text-align:left;">
						<f:facet name="header">#{formBundle['form.ri.creator.award']} Position in RI</f:facet>
						<p:message id="typeMsg" for="type" />
						<p:selectOneMenu id="type" value="#{col.flag}"
							disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
							<f:selectItem itemLabel="EdUHK Staff" itemValue="N" />
							<f:selectItem itemLabel="Former Staff" itemValue="F" />
							<f:selectItem itemLabel="Non EdUHK Staff" itemValue="Y" />
							<f:selectItem itemLabel="EdUHK Student" itemValue="S" />
							<p:ajax event="change" update="nameGroup cvGroup" />
						</p:selectOneMenu>
						<p:blockUI block="contentPanel" trigger="type" />
					</p:column>

					<p:column>
						<f:facet name="header">#{formBundle['form.ri.creator.award']}</f:facet>
						<h:panelGroup id="nameGroup">
							<p:message for="select_staff_recipient_name" />
							<p:selectOneMenu id="select_staff_recipient_name" dynamic="true"
								label="#{formBundle['form.ri.creator.award']}" style="width:90%" value="#{col.staff_no}"
								filter="true" filterMatchMode="startsWith"
								rendered="#{col.flag eq 'N'}"
								disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
								<f:selectItem itemLabel="-- Please select --"
									itemValue="#{null}" itemDisabled="true" />
								<f:selectItems value="#{manageRIView.staffList}"/>
								<p:ajax event="change" update="cvGroup" />
							</p:selectOneMenu>

							<p:message for="select_staff_past_name" />							
							<p:autoComplete id="select_staff_past_name" value="#{col.recipient_name}" style="#{manageAwardView.paramDataLevel eq 'C'?'width:70%':'width:90%'}"
											rendered="#{col.flag eq 'F'}"
											minQueryLength="3" 
											emptyMessage="No results"
											forceSelection="true"
											disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false || manageAwardView.paramDataLevel eq 'P'}"
				                        	completeMethod="#{manageRIView.fsCompleteText}" scrollHeight="250">
				                        	<p:ajax event="itemSelect" process="@this" />
				                        	<p:ajax event="itemSelect" listener="#{manageAwardView.updateRowStaffNum(rowIndex)}" update="nameGroup"/>
				            </p:autoComplete>							
							<p:inputText id="select_staff_past_num" label="Staff No." style="#{manageAwardView.isRdoAdmin == true?'width:25%':'display:none'}" value="#{col.staff_no}" rendered="#{col.flag eq 'F'}" dynamic="true">
							</p:inputText>
							
							<p:message id="recipient_nameMsg" for="recipient_name" />
							<p:inputText id="recipient_name" label="#{formBundle['form.ri.creator.award']}"
								style="width:90%" value="#{col.recipient_name}"
								onkeypress="return (event.charCode != 59);" maxlength="80"
								rendered="#{col.flag ne 'N' &amp;&amp; col.flag ne 'F'}"
								disabled="#{manageAwardView.isCreator == false || manageAwardView.canModify == false}">
								<p:ajax />
							</p:inputText>
						</h:panelGroup>
					</p:column>
					<p:column class="data-noprint" style="width:8em;">
						<h:panelGroup id="cvGroup">
							<p:linkButton id="btn_view_cv" outcome="/web/person"
								rendered="#{col.flag eq 'N' &amp;&amp; manageAwardView.getStaffProfilePid(col.staff_no) ne null}"
								value="#{formBundle['form.view.profile']}" target="_blank">
								<f:param name="pid"
									value="#{manageAwardView.getStaffProfilePid(col.staff_no)}" />
							</p:linkButton>
						</h:panelGroup>
					</p:column>
					<p:column class="data-noprint" style="width:2em; text-align:left;">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_delete"
							action="#{manageAwardView.deleteRow(rowIndex)}"
							rendered="#{manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true}"
							update="columnTable" immediate="true">
							<i class="fas fa-trash icon-action"
								title="#{formBundle['form.del']}" />
						</p:commandLink>
						<p:blockUI block="contentPanel" trigger="btn_delete" />
					</p:column>
				</p:dataTable>
				<br />
				<p:commandButton id="btn_add" icon="fas fa-plus"
					value="Add new recipient"
					rendered="#{manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true}"
					style="width:260px" action="#{manageAwardView.addRow()}"
					update="columnTable" immediate="true" />
				<p:blockUI block="contentPanel" trigger="btn_add" />
				<br /><br />
				<h:panelGroup styleClass="button-panel">
					<p:linkButton value="#{bundle['action.back']}" outcome="manageAward" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageAwardView.paramDataLevel eq 'M' &amp;&amp; manageAwardView.paramConsent eq ''}">		
						<f:param name="pid" value="#{manageAwardView.paramPid}"/>
					</p:linkButton>
					<p:linkButton value="#{bundle['action.back']}" outcome="consentRI" icon="pi pi-arrow-left" styleClass="btn-back" rendered="#{manageAwardView.paramConsent ne ''}">		
						<f:param name="pid" value="#{manageAwardView.paramPid}"/>
						<f:param name="consent" value="#{manageAwardView.paramConsent}"/>
						<f:param name="tabpage" value="2"/>
					</p:linkButton>
					<p:commandButton id="btn_sava" value="#{formBundle['form.save']}"
						rendered="#{manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true &amp;&amp; manageAwardView.paramDataLevel ne 'C'}"
						style="margin-right:5px;" update="@form messages"
						action="#{manageAwardView.save}"
						oncomplete="window.scrollTo(0,0);">
					</p:commandButton>
					<p:commandButton id="btn_savaAndGen"
						value="#{(manageAwardView.selectedAwardHeader_q.cdcf_status eq 'CDCF_GENERATED')? 'Save and Re-generate':'Save'}"
						rendered="#{manageAwardView.paramDataLevel eq 'C'}"
						style="margin-right:5px;" update="@form messages"
						action="#{manageAwardView.save}"
						oncomplete="window.scrollTo(0,0);">
					</p:commandButton>
					<p:commandButton id="btn_savaAndPublish"
						value="#{bundle['action.saveAndPublish']}"
						rendered="#{manageAwardView.isCreator == true &amp;&amp; manageAwardView.canModify == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'}"
						style="margin-right:5px;" update="@form messages"
						action="#{manageAwardView.saveAndPublishForm}"
						oncomplete="window.scrollTo(0,0);">
						<p:confirm header="#{formBundle['form.confirm']}"
							message="#{formBundle['form.save.publish.desc']}"
							icon="pi pi-info-circle" />
					</p:commandButton>
					<p:commandButton id="btn_submit" value="#{formBundle['form.save']}"
						rendered="#{manageAwardView.isCreator == false &amp;&amp; manageAwardView.canModify == true &amp;&amp; manageAwardView.paramDataLevel eq 'M'}"
						style="margin-right:5px;" update="@form messages"
						action="#{manageAwardView.submitConsent}"
						oncomplete="window.scrollTo(0,0);">
						<p:confirm header="#{formBundle['form.confirm']}" message="#{formBundle['form.save.desc']}" icon="pi pi-info-circle"/>
					</p:commandButton>
					<p:commandButton id="btn_snapshot" value="#{formBundle['form.take.snapshot']}"
						rendered="#{manageAwardView.paramDataLevel eq 'P'}"
						style="margin-right:5px;" process="@this"
						action="#{manageAwardView.takeSnapshot}"
						oncomplete="window.scrollTo(0,0);">
						<p:confirm header="#{formBundle['form.confirm']}"
							message="#{formBundle['form.take.snapshot.desc']}"
							icon="pi pi-info-circle" />
					</p:commandButton>
					<p:commandButton id="btn_delete" value="#{formBundle['form.del']}"
						rendered="#{manageAwardView.canDelete == true &amp;&amp; manageAwardView.canModify == true}"
						style="margin-right:5px; background:#D32F2F; border:1px solid #D32F2F;"
						process="@this" action="#{manageAwardView.deleteForm}">
						<p:confirm header="#{formBundle['form.confirm']}"
							message="#{formBundle['form.del.desc']}"
							icon="pi pi-info-circle" />
					</p:commandButton>
					<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
						responsive="true" width="350">
						<p:commandButton value="No" icon="pi pi-times" type="button"
							styleClass="ui-confirmdialog-no ui-button-flat" />
						<p:commandButton value="Yes" icon="pi pi-check" type="button"
							styleClass="ui-confirmdialog-yes" />
					</p:confirmDialog>
					<!--  <p:blockUI block="contentPanel" trigger="top_btn_sava" />
							<p:blockUI block="contentPanel" trigger="top_btn_savaAndPublish" />
							<p:blockUI block="contentPanel" trigger="top_btn_submit" />
							<p:blockUI block="contentPanel" trigger="top_btn_snapshot" />
							<p:blockUI block="contentPanel" trigger="top_btn_delete" />
							<p:blockUI block="contentPanel" trigger="btn_sava" />
							<p:blockUI block="contentPanel" trigger="btn_savaAndPublish" />
							<p:blockUI block="contentPanel" trigger="btn_submit" />
							<p:blockUI block="contentPanel" trigger="btn_snapshot" />
							<p:blockUI block="contentPanel" trigger="btn_delete" />-->
				</h:panelGroup>
				<p:scrollTop target="parent" threshold="100" styleClass="custom-scrolltop" icon="pi pi-arrow-up" />
			</h:form>

		</p:panel>
	</ui:define>

</ui:composition>