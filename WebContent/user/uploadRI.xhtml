<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions"
				xmlns:h="http://java.sun.com/jsf/html" 
				xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">
	<f:metadata>
		<f:viewParam name="uploadSrc" value="#{uploadRIView.uploadSrc}" />
	</f:metadata>
	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
	<span class="admin-content-title"><i class="fas fa-file-import"></i>Upload RI</span>
	
	<p:messages id="msgs"  autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm" enctype="multipart/form-data">
		<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Upload Source
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:selectOneMenu id="uploadSrc"
							 value="#{uploadRIView.uploadSrc}"
							 style="width:auto; max-width:100%;"
							 >
				<f:selectItems value="#{uploadRIView.uploadSrcList}"/>
				<p:ajax event="change" process="@this" update=":dataForm"/>
			</p:selectOneMenu>
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Batch Key
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:inputText id="batchKey"
						 value="#{uploadRIView.batchKey}"
						 disabled="#{uploadRIView.getUploadSrc() == null}">
			</p:inputText>
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			Remarks
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:inputText id="remarks" style="width:50%"
						 value="#{uploadRIView.remarks}"
						 disabled="#{uploadRIView.getUploadSrc() == null}">
			</p:inputText>
		</div>
		<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
			File upload
		</div>
		<div class="ui-g-12 ui-md-10 ui-lg-10 content">
			<p:fileUpload id="uploadedFile" value="#{uploadRIView.uploadedFile}" mode="simple" skinSimple=""
								  binding="#{uploadedFileObj}"
								  disabled="#{uploadRIView.getUploadSrc() == null}"/>
		</div>
		<br/>
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back">
		</p:linkButton>
		<p:commandButton id="uploadButton" value="Upload" icon="pi pi-upload" ajax="false"
						 update="@form" disabled="#{uploadRIView.getUploadSrc() == null}"
						 action="#{uploadRIView.upload()}" ></p:commandButton><p:spacer width="10"/>
		<p:dataTable id="batchDataTable"
					 value="#{uploadRIView.getBatchList()}" 
					 var="batch"
					 stripedRows="true" size="small" style="font-size:14px;"
					 reflow="true"
					 paginator="true"
					 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                     rows="30"
                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
                 	 tableStyle="table-layout:auto;"
					 >
					 
					<p:column exportable="false" width="8%">
		               	<h:link outcome="/user/batch_edit" target="_blank" style="color:#186ba0"><i class="fas fa-edit icon-action" title="Edit"/>
		               		<f:param name="id" value="#{batch.batch_id}"/>
		               	</h:link>
					</p:column>
					
					<p:column width="23%">
						<f:facet name="header">Batch Key</f:facet>
						<h:outputText value="#{batch.batch_key}" />
					</p:column>
					
					<p:column width="23%" >
						<f:facet name="header">Remarks</f:facet>
						<h:outputText value="#{batch.remarks}"/>
					</p:column>
					
					<p:column width="23%">
						<f:facet name="header">Last Modified by</f:facet>
						<h:outputText value="#{batch.creator}" />
					</p:column>
					
					<p:column width="23%">
						<f:facet name="header">Last Modified Date</f:facet>
						<h:outputText value="#{batch.userstamp}" />
					</p:column>
		</p:dataTable>
	</h:form>
	
	</p:panel>
   </ui:define>
</ui:composition>