<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
		 xmlns="http://java.sun.com/xml/ns/javaee" 
		 xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd" 
		 version="3.0">
  <display-name>RICH</display-name>
  
  <distributable/>
  <context-param>
    <param-name>app.LOCAL_USER_ID</param-name>
    <param-value>wcywong</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.PROJECT_STAGE</param-name>
    <param-value>Development</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.FACELETS_REFRESH_PERIOD</param-name>
    <param-value>0</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.FACELETS_SKIP_COMMENTS</param-name>
    <param-value>true</param-value>
  </context-param>

  <context-param>
    <param-name>javax.faces.DATETIMECONVERTER_DEFAULT_TIMEZONE_IS_SYSTEM_TIMEZONE</param-name>
    <param-value>true</param-value>
  </context-param>
  <context-param>
    <param-name>primefaces.UPLOADER</param-name>
    <param-value>auto</param-value>
  </context-param>
  <context-param>
    <param-name>primefaces.THEME</param-name>
    <param-value>saga</param-value>
  </context-param>
  <context-param>
	<param-name>resteasy.scan</param-name>
	<param-value>true</param-value>
  </context-param>
  <servlet>
	<servlet-name>Resteasy</servlet-name>
	<servlet-class>org.jboss.resteasy.plugins.server.servlet.HttpServletDispatcher</servlet-class>
  </servlet>
  <servlet-mapping>
	<servlet-name>Resteasy</servlet-name>
	<url-pattern>/user/formService/*</url-pattern>
  </servlet-mapping>
	

 <servlet>
		<servlet-name>ExportPureKtAct</servlet-name>
		<servlet-class>hk.eduhk.rich.export.ExportPureKtAct</servlet-class>
  </servlet>
  <servlet-mapping>
	<servlet-name>ExportPureKtAct</servlet-name>
	<url-pattern>/service/exportPureKtAct.xhtml</url-pattern>
  </servlet-mapping>	
  
  <servlet>
		<servlet-name>ExportPurePerson</servlet-name>
		<servlet-class>hk.eduhk.rich.export.ExportPurePerson</servlet-class>
  </servlet>
  <servlet-mapping>
	<servlet-name>ExportPurePerson</servlet-name>
	<url-pattern>/service/exportPurePerson.xhtml</url-pattern>
  </servlet-mapping>	
  
  <servlet>
		<servlet-name>ExportPureProject</servlet-name>
		<servlet-class>hk.eduhk.rich.export.ExportPureProject</servlet-class>
  </servlet>
  <servlet-mapping>
	<servlet-name>ExportPureProject</servlet-name>
	<url-pattern>/service/exportPureProject.xhtml</url-pattern>
  </servlet-mapping>
  
  <servlet>
		<servlet-name>ExportPureAward</servlet-name>
		<servlet-class>hk.eduhk.rich.export.ExportPureAward</servlet-class>
  </servlet>
  <servlet-mapping>
	<servlet-name>ExportPureAward</servlet-name>
	<url-pattern>/service/exportPureAward.xhtml</url-pattern>
  </servlet-mapping>
  
  
  <security-constraint>
    <web-resource-collection>
        <web-resource-name>purexml</web-resource-name>
        <url-pattern>/service/*</url-pattern>
    </web-resource-collection>
    <auth-constraint>
        <role-name>purexmluser</role-name>
    </auth-constraint>
  </security-constraint>
  
  <login-config>
      <auth-method>BASIC</auth-method>
      <realm-name>RealmUsersRoles</realm-name>
  </login-config>
  
  
  <filter>
    <filter-name>AdminFunctionAccessFilter</filter-name>
    <filter-class>hk.eduhk.rich.util.filter.AdminFunctionAccessFilter</filter-class>
  </filter>
  <filter>
    <filter-name>CORSFilter</filter-name>
    <filter-class>hk.eduhk.rich.util.filter.CORSFilter</filter-class>
  </filter>
  <filter>
    <filter-name>Http404Filter</filter-name>
    <filter-class>hk.eduhk.rich.util.filter.HttpResponseStatusFilter</filter-class>
    <init-param>
      <param-name>statusCode</param-name>
      <param-value>404</param-value>
    </init-param>
  </filter>
  <filter>
    <filter-name>GzipResponseFilter</filter-name>
    <filter-class>org.omnifaces.filter.GzipResponseFilter</filter-class>
  </filter>
  <filter>
    <filter-name>NoCacheFilter</filter-name>
    <filter-class>hk.eduhk.rich.util.filter.NoCacheFilter</filter-class>
  </filter>
  <filter>
    <filter-name>PrimeFaces FileUpload Filter</filter-name>
    <filter-class>org.primefaces.webapp.filter.FileUploadFilter</filter-class>
  </filter>
  <filter>
    <filter-name>RemoteUserFilter</filter-name>
    <filter-class>hk.eduhk.rich.util.filter.RemoteUserFilter</filter-class>
  </filter>
  <filter>
	<filter-name>UserAgentFilter</filter-name>
	<filter-class>hk.eduhk.rich.util.filter.UserAgentFilter</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>Http404Filter</filter-name>
    <url-pattern>*.bak</url-pattern>
    <url-pattern>/resources/component/*</url-pattern>
    <url-pattern>/resources/template/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>CORSFilter</filter-name>
    <url-pattern>/service/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>RemoteUserFilter</filter-name>
    <url-pattern>/admin/*</url-pattern>
    <url-pattern>/export/*</url-pattern>
    <url-pattern>/user/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>NoCacheFilter</filter-name>
    <url-pattern>/service/*</url-pattern>
    <url-pattern>/user/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>GzipResponseFilter</filter-name>
    <url-pattern>*.js</url-pattern>
    <url-pattern>/service/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>AdminFunctionAccessFilter</filter-name>
    <url-pattern>/admin/*</url-pattern>
    <url-pattern>/export/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>PrimeFaces FileUpload Filter</filter-name>
    <servlet-name>FacesServlet</servlet-name>
  </filter-mapping>
  <filter-mapping>
	<filter-name>UserAgentFilter</filter-name>
	<url-pattern>/admin/*</url-pattern>
	<url-pattern>/export/*</url-pattern>
	<url-pattern>/user/*</url-pattern>
  </filter-mapping>
  <listener>
    <listener-class>hk.eduhk.rich.util.listener.AppContextListener</listener-class>
  </listener>
  <listener>
    <listener-class>hk.eduhk.rich.util.listener.AppSessionAttributeListener</listener-class>
  </listener>
  <listener>
    <listener-class>org.jboss.resteasy.plugins.server.servlet.ResteasyBootstrap</listener-class>
  </listener>
  <session-config>
    <session-timeout>120</session-timeout>
    <cookie-config>
      <path>/</path>
      <http-only>true</http-only>
      <secure>true</secure>
    </cookie-config>
  </session-config>
  <security-constraint>
    <web-resource-collection>
      <web-resource-name>Deny access to PATCH, TRACE</web-resource-name>
      <url-pattern>/*</url-pattern>
      <http-method>PATCH</http-method>
      <http-method>TRACE</http-method>
    </web-resource-collection>
    <auth-constraint/>
  </security-constraint>


  
  <mime-mapping>
    <extension>gif</extension>
    <mime-type>image/gif</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>ico</extension>
    <mime-type>image/x-icon</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>jpg</extension>
    <mime-type>image/jpg</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>js</extension>
    <mime-type>application/javascript</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>png</extension>
    <mime-type>image/png</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xhtml</extension>
    <mime-type>text/html</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xls</extension>
    <mime-type>application/vnd.ms-office</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xlsx</extension>
    <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>eot</extension>
    <mime-type>application/vnd.ms-fontobject</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>otf</extension>
    <mime-type>font/opentype</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>ttf</extension>
    <mime-type>application/x-font-ttf</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>woff</extension>
    <mime-type>application/x-font-woff</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>woff2</extension>
    <mime-type>application/font-woff2</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>svg</extension>
    <mime-type>image/svg+xml</mime-type>
  </mime-mapping>
  <welcome-file-list>
    <welcome-file>index.htm</welcome-file>
    <welcome-file>index.html</welcome-file>
    <welcome-file>index.jsf</welcome-file>
    <welcome-file>index.jsp</welcome-file>
    <welcome-file>index.xhtml</welcome-file>
  </welcome-file-list>
</web-app>