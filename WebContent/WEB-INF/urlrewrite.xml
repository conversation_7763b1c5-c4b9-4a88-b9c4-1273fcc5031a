<!--
	Configuration file for UrlRewriteFilter
	http://www.tuckey.org/urlrewrite/
-->
<urlrewrite>

	<rule>
		<note>
		The rule means that requests to /pwa/form/ will be redirected to /pwa/index.html.
		</note>
		<from>^/pwa/form(/[a-z0-9-_.]+)+$</from>
		<to>/pwa/index.html</to>
	</rule>

	<rule>
		<note>
		The rule means that requests to /pwa/ will be redirected to /pwa/index.html.
		</note>
		<from>^/pwa(/[a-z0-9-_]+)+$</from>
		<to>/pwa/index.html</to>
	</rule>


<!-- 
	<rule>
		<note>
		The rule means that requests to /test/status/ will be redirected to /rewrite-status the url will be rewritten.
		</note>
		<from>/pwa/index.html</from>
		<to>null</to>
	</rule> 

	<rule match-type="wildcard">
		<note>
		The rule means that requests to /test/status/ will be redirected to /rewrite-status the url will be rewritten.
		</note>
		<from>/pwa/**</from>
		<to>/pwa/index.html</to>
	</rule> 
 -->

</urlrewrite>