<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<access-control>

	<role>
		<id>sysAdmin</id>
		<name>System Administrator</name>
	</role>
	<role>
		<id>libAdmin</id>
		<name>Library Admin</name>
	</role>
	<role>
		<id>superRdoAdmin</id>
		<name>Super RDO Admin</name>
	</role>
	<role>
		<id>rdoAdmin</id>
		<name>RDO Admin</name>
	</role>
	<role>
		<id>raeAdmin</id>
		<name>RAE Admin</name>
	</role>
	<role>
		<id>raeStaff</id>
		<name>RAE Staff</name>
	</role>
	<role>
		<id>ktAdmin</id>
		<name>KT Admin</name>
	</role>		
	<role>
		<id>inputktAdmin</id>
		<name>Input KT Admin</name>
	</role>	
	<role>
		<id>deptAdmin</id>
		<name>Department Admin</name>
	</role>	
	<role>
		<id>rptUser</id>
		<name>Report User</name>
	</role>
	<role>
		<id>acadStaff</id>
		<name>Academic Staff</name>
	</role>	
	<role>
		<id>asst</id>
		<name>Assistant</name>
	</role>	
	<role>
		<id>other</id>
		<name>Other Staff</name>
	</role>	
	<role>
		<id>cmsUser</id>
		<name>CMS User</name>
	</role>	
	<role>
		<id>uoaAdmin</id>
		<name>UoA Admin</name>
	</role>
	<function>
		<id>JENKINS</id>
		<name>Jenkins</name>
		<description>Jenkins</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/jenkins.xhtml</url>
		</access>
		<entry-url>/admin/jenkins.xhtml</entry-url>
	</function>	
	
	<function>
		<id>ASST_REQUEST</id>
		<name>Apply Assistant Right </name>
		<description>Apply Assistant Right</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.NoAccessCheckAuthorizer</class-name>
			</authorizer>
			<url>/user/asstRequest.xhtml</url>
		</access>
		<entry-url>/user/asstRequest.xhtml</entry-url>
	</function>	
	
	<function>
		<id>CONSENT_RI</id>
		<name>Consent RI</name>
		<description>Consent RI</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/consentRI.xhtml</url>
		</access>
		<entry-url>/user/consentRI.xhtml?consent=U</entry-url>
	</function>	
	
	<function>
		<id>DASHBOARD</id>
		<name>Dashboard</name>
		<description>Dashboard</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.NoAccessCheckAuthorizer</class-name>
			</authorizer>
			<url>/user/dashboard.xhtml</url>
			<url>/user/index.xhtml</url>
			<url>/user/signout.xhtml</url>
		</access>
		<entry-url>/user/dashboard.xhtml</entry-url>
	</function>

	<function>
		<id>ABOUT_CDCF</id>
		<name>About CDCF</name>
		<description>About CDCF</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.NoAccessCheckAuthorizer</class-name>
			</authorizer>
			<url>/user/aboutCdcf.xhtml</url>
			<url>/user/Attachment I_2021-22.htm</url>
			<url>/user/Table 7_Classification of Research Output from UGC Guideline.htm</url>
			<url>/user/Attachment II_2021-22.htm</url>
			<url>/user/Attachment III_2021-22.htm</url>
		</access>
		<entry-url>/user/aboutCdcf.xhtml</entry-url>
	</function>
	
	<function>
		<id>ADHOC_FUNC</id>
		<name>Adhoc Function</name>
		<description>Adhoc Function</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/adhocFunction.xhtml</url>
		</access>
		<entry-url>/admin/adhocFunction.xhtml</entry-url>
	</function>
	

	<function>
		<id>CACHE_MANAGE</id>
		<name>Manage Cache</name>
		<description>Manage Cache</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/cacheManage.xhtml</url>
		</access>
		<entry-url>/admin/cacheManage.xhtml</entry-url>
	</function>
	
	<function>
		<id>CHANGE_LOG</id>
		<name>View Change Log</name>
		<description>Change Log</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/changelog.xhtml</url>
		</access>
		<entry-url>/admin/changelog.xhtml</entry-url>
	</function>
	
	<function>
		<id>IMPERSONATION</id>
		<name>Change View</name>
		<description>Change View</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/impersonation.xhtml</url>
		</access>
		<entry-url>/admin/impersonation.xhtml</entry-url>
	</function>
	
	<function>
		<id>UPLOAD_RI</id>
		<name>Upload RI</name>
		<description>Upload RI</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, cmsUser</value>
				</param>
			</authorizer>
			<url>/user/uploadRI.xhtml</url>
			<url>/user/batch_edit.xhtml</url>
		</access>
		<entry-url>/user/uploadRI.xhtml</entry-url>
	</function>	
	
	<function>
		<id>IMPORT_RI</id>
		<name>Import RI</name>
		<description>Import RI</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/importRI.xhtml</url>
		</access>
		<entry-url>/user/importRI.xhtml</entry-url>
	</function>		
	
	<function>
		<id>UPLOAD_KT_ACT</id>
		<name>Upload KT Activities</name>
		<description>Upload KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, ktAdmin</value>
				</param>
			</authorizer>
			<url>/admin/uploadKtActivities.xhtml</url>
		</access>
		<entry-url>/admin/uploadKtActivities.xhtml</entry-url>
	</function>
	
	<function>
		<id>UPLOAD_AMIS_IND</id>
		<name>Upload AMIS Indicator</name>
		<description>Upload KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, cmsUser</value>
				</param>
			</authorizer>
			<url>/admin/uploadAmisInd.xhtml</url>
		</access>
		<entry-url>/admin/uploadAmisInd.xhtml</entry-url>
	</function>
	
	<function>
		<id>DATA_ACCESS</id>
		<name>Set Data Access</name>
		<description>Set Data Access</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/dataAccess.xhtml</url>
		</access>
		<entry-url>/admin/dataAccess.xhtml</entry-url>
	</function>
	
	<function>
		<id>FUNC_ACCESS</id>
		<name>Set Functional Access</name>
		<description>Set Functional Access</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/funcAccess.xhtml</url>
		</access>
		<entry-url>/admin/funcAccess.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_ASST</id>
		<name>Manage Assistant Right</name>
		<description>Manage Assistant Right</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/manageAsst.xhtml</url>
		</access>
		<entry-url>/user/manageAsst.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_DEPT</id>
		<name>Manage Department</name>
		<description>Manage Department</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageDepartment.xhtml</url>
			<url>/admin/manageDepartment_edit.xhtml</url>
		</access>
		<entry-url>/admin/manageDepartment.xhtml</entry-url>
	</function>		

	<function>
		<id>MANAGE_EMAIL_TMPL</id>
		<name>Manage Email Template</name>
		<description>Manage Email Template</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageEmailTmpl.xhtml</url>
		</access>
		<entry-url>/admin/manageEmailTmpl.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_INFO</id>
		<name>Manage My Info.</name>
		<description>Manage My Info.</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/manageInfo.xhtml</url>
			<url>/user/employmentHistory_edit.xhtml</url>
		</access>
		<entry-url>/user/manageInfo.xhtml</entry-url>
	</function>		
	
 	<function>
		<id>MANAGE_IND_INFO</id>
		<name>Manage Individual Info.</name>
		<description>Manage Individual Info.</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>asst</value>
				</param>
			</authorizer>
			<url>/user/manageAcadStaff.xhtml</url>
		</access>
		<entry-url>/user/manageAcadStaff.xhtml</entry-url>
	</function>		 
	
	<function>
		<id>MANAGE_KT_RPT_PERIOD</id>
		<name>Manage KT Reporting Period</name>
		<description>Manage KT Reporting Period</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>ktAdmin</value>
				</param>
			</authorizer>
			<url>/admin/ktRptPeriodList.xhtml</url>
			<url>/admin/ktRptPeriodEdit.xhtml</url>
		</access>
		<entry-url>/admin/ktRptPeriodList.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_KT_ACT_FORM</id>
		<name>Manage KT Activity Forms</name>
		<description>Manage KT Activity Forms</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>ktAdmin</value>
				</param>
			</authorizer>
			<url>/admin/ktActFormList.xhtml</url>
			<url>/admin/ktActFormEdit.xhtml</url>
		</access>
		<entry-url>/admin/ktActFormList.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_DELEGATOR_INFO</id>
		<name>Manage Delegator Info.</name>
		<description>Manage Delegator Info.</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>asst</value>
				</param>
			</authorizer>
			<url>/user/manageDelegatorInfo.xhtml</url>
		</access>
		<entry-url>/user/manageDelegatorInfo.xhtml</entry-url>
	</function>	
	
	<function>
		<id>ACAD_MANAGE_KT_ACT</id>
		<name>Manage KT Activities</name>
		<description>Manage KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/ktFormSum.xhtml</url>
			<url>/user/manageKtForm.xhtml</url>
		</access>
		<entry-url>/user/ktFormSum.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_KT_ACT_DEPT</id>
		<name>Manage Fac./Dept./Ctr. KT Activities</name>
		<description>Manage Faculty/Department/Centre KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>ktAdmin, inputktAdmin</value>
				</param>
			</authorizer>
			<url>/user/ktFormSum.xhtml</url>
			<url>/user/manageKtForm.xhtml</url>
		</access>
		<entry-url>/user/ktFormSum.xhtml?admin=Y</entry-url>
	</function>	
	
	<function>
		<id>OTH_MANAGE_KT_ACT</id>
		<name>Manage KT Activities</name>
		<description>Manage KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>other</value>
				</param>
			</authorizer>
			<url>/user/ktFormSum.xhtml</url>
			<url>/user/manageKtForm.xhtml</url>
		</access>
		<entry-url>/user/ktFormSum.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_RAE_OUTPUT</id>
		<name>Manage RAE Research Outputs</name>
		<description>Manage RAE Research Outputs</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>raeStaff</value>
				</param>
			</authorizer>
			<url>/user/manageRAEOutput.xhtml</url>
			<url>/user/manageRAEOutput_edit.xhtml</url>
		</access>
		<entry-url>/user/manageRAEOutput.xhtml</entry-url>
	</function>	
	
	<function>
		<id>APPROVE_RAE_OUTPUT</id>
		<name>Approve RAE Outputs</name>
		<description>Approve RAE Outputs</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>uoaAdmin, raeAdmin</value>
				</param>
			</authorizer>
			<url>/user/approveRAEOutput.xhtml</url>
		</access>
		<entry-url>/user/approveRAEOutput.xhtml</entry-url>
	</function>	
	
	<function>
		<id>SELECT_RAE_OUTPUT_FOR_TEACHER</id>
		<name>Select RAE Outputs for Staffs</name>
		<description>Select RAE Outputs for Staffs</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>uoaAdmin, raeAdmin</value>
				</param>
			</authorizer>
			<url>/user/selectRAEOutputForTeacher.xhtml</url>
		</access>
		<entry-url>/user/selectRAEOutputForTeacher.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_RAE_STAFF</id>
		<name>Manage RAE Staff</name>
		<description>Manage RAE Staff</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageRAEStaff.xhtml</url>
		</access>
		<entry-url>/admin/manageRAEStaff.xhtml</entry-url>
	</function>	
	
	
	<function>
		<id>MANAGE_RESEARCH_OUTPUT</id>
		<name>Manage Research Outputs</name>
		<description>Manage Research Outputs</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/manageOutput.xhtml</url>
		</access>
		<entry-url>/user/manageOutput.xhtml</entry-url>
	</function>		
		
	<function>
		<id>MANAGE_PROJECT</id>
		<name>Manage Projects</name>
		<description>Manage Projects</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/manageProject.xhtml</url>
		</access>
		<entry-url>/user/manageProject.xhtml</entry-url>
	</function>			
	
	<function>
		<id>MANAGE_PRIZE_AWARD</id>
		<name>Manage Prizes and Awards</name>
		<description>Manage Prizes and Awards</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/manageAward.xhtml</url>
		</access>
		<entry-url>/user/manageAward.xhtml</entry-url>
	</function>	
		
	<function>
		<id>MANAGE_PATENT</id>
		<name>Manage Patents</name>
		<description>Manage Patents</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/managePatent.xhtml</url>
		</access>
		<entry-url>/user/managePatent.xhtml</entry-url>
	</function>	

	<function>
		<id>OTH_MANAGE_RESEARCH_OUTPUT</id>
		<name>Manage Research Outputs</name>
		<description>Manage Research Outputs</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>other</value>
				</param>
			</authorizer>
			<url>/user/manageOutput.xhtml</url>
		</access>
		<entry-url>/user/manageOutput.xhtml</entry-url>
	</function>		
		
	<function>
		<id>OTH_MANAGE_PROJECT</id>
		<name>Manage Projects</name>
		<description>Manage Projects</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>other</value>
				</param>
			</authorizer>
			<url>/user/manageProject.xhtml</url>
		</access>
		<entry-url>/user/manageProject.xhtml</entry-url>
	</function>			
	
	<function>
		<id>OTH_MANAGE_PRIZE_AWARD</id>
		<name>Manage Prizes and Awards</name>
		<description>Manage Prizes and Awards</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>other</value>
				</param>
			</authorizer>
			<url>/user/manageAward.xhtml</url>
		</access>
		<entry-url>/user/manageAward.xhtml</entry-url>
	</function>	
		
	<function>
		<id>OTH_MANAGE_PATENT</id>
		<name>Manage Patents</name>
		<description>Manage Patents</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>other</value>
				</param>
			</authorizer>
			<url>/user/managePatent.xhtml</url>
		</access>
		<entry-url>/user/managePatent.xhtml</entry-url>
	</function>	
			
	<function>
		<id>MANAGE_PROFILE_PREFERENCES</id>
		<name>Manage Profile Preferences</name>
		<description>Manage Profile Preferences</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/manageProfile.xhtml</url>
		</access>
		<entry-url>/user/manageProfile.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_RANK</id>
		<name>Manage Rank</name>
		<description>Manage Rank</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageRank.xhtml</url>
		</access>
		<entry-url>/admin/manageRank.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_PURE_MAP</id>
		<name>Manage Pure Mapping</name>
		<description>Manage Pure Mapping</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/managePureMap.xhtml</url>
		</access>
		<entry-url>/admin/managePureMap.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_SEC_FUNC_LOCK</id>
		<name>Set Exclusive Use of RI</name>
		<description>Set Exclusive Use of RI</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageExclusiveUse.xhtml</url>
			<url>/admin/setExclusiveUse.xhtml</url>
			<url>/admin/setExtendedAccessUser.xhtml</url>
		</access>
		<entry-url>/admin/manageExclusiveUse.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_SEC_FUNC_LOCK_KT</id>
		<name>Set Exclusive of KT Activities</name>
		<description>Set Exclusive of KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, ktAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageExKt.xhtml</url>
			<url>/admin/setExKtDept.xhtml</url>
			<url>/admin/setExKtUser.xhtml</url>
		</access>
		<entry-url>/admin/manageExKt.xhtml</entry-url>
	</function>		
	
	<function>
		<id>MANAGE_SEC_FUNC_LOCK_RAE</id>
		<name>Set Exclusive of RAE</name>
		<description>Set Exclusive of RAE</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, raeAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageExRae.xhtml</url>
			<url>/admin/setExRae.xhtml</url>
			<url>/admin/setExRaeUoaAdmin.xhtml</url>
		</access>
		<entry-url>/admin/manageExRae.xhtml</entry-url>
	</function>		
	
	<function>
		<id>SCHEDULER</id>
		<name>Manage Scheduler Job</name>
		<description>Job Scheduler</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/schedulerJobEdit.xhtml</url>
			<url>/admin/schedulerJobList.xhtml</url>
		</access>
		<entry-url>/admin/schedulerJobList.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_ACCESS</id>
		<name>Manage Access</name>
		<description>Manage Access</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageAccess.xhtml</url>
		</access>
		<entry-url>/admin/manageAccess.xhtml</entry-url>
	</function>	
	
	<function>
		<id>SYS_LOG</id>
		<name>View System Log</name>
		<description>System Log</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/sysLogList.xhtml</url>
		</access>
		<entry-url>/admin/sysLogList.xhtml</entry-url>
	</function>
	
	<function>
		<id>SYS_PARAM</id>
		<name>Manage System Parameter</name>
		<description>Add/Modify/Delete System Parameter</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/sysParamList.xhtml</url>
			<url>/admin/sysParamEdit.xhtml</url>
		</access>
		<entry-url>/admin/sysParamList.xhtml</entry-url>
	</function>
	
	<function>
		<id>LOOKUP_VALUE</id>
		<name>Manage Lookup Value</name>
		<description>Manage Lookup Value</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageLookupValue.xhtml</url>
		</access>
		<entry-url>/admin/manageLookupValue.xhtml</entry-url>
	</function>
	
	<function>
		<id>USER_ACCESS</id>
		<name>Manage User Access Right</name>
		<description>Add / Delete User Access Right</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/userAcsList.xhtml</url>
			<url>/admin/userAcsEdit.xhtml</url>
		</access>
		<entry-url>/admin/userAcsList.xhtml</entry-url>
	</function>

	<function>
		<id>VIEW_DB</id>
		<name>View Database Data</name>
		<description>View Database Data</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/dbTableList.xhtml</url>
		</access>
		<entry-url>/admin/dbTableList.xhtml</entry-url>
	</function>	

	<function>
		<id>VIEW_PURE_XML</id>
		<name>View Pure XML</name>
		<description>View Pure XML</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>libAdmin</value>
				</param>
			</authorizer>
			<url>/admin/pureXML.xhtml</url>
			<url>/export/pureAwardXML.xhtml</url>
			<url>/export/purePersonXML.xhtml</url>
			<url>/export/pureProjectXML.xhtml</url>
			<url>/export/pureKtActXML.xhtml</url>
		</access>
		<entry-url>/admin/pureXML.xhtml</entry-url>
	</function>	
	
	<function>
		<id>RI_LISTING</id>
		<name>Research Information Listing</name>
		<description>Research Information Listing</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>libAdmin, raeAdmin, rdoAdmin, deptAdmin, uoaAdmin</value>
				</param>
			</authorizer>
			<url>/admin/researchInformationListing.xhtml</url>
		</access>
		<entry-url>/admin/researchInformationListing.xhtml</entry-url>
	</function>	
	
	<function>
		<id>RI_LISTING_ACAD_STAFF</id>
		<name>Research Information Listing</name>
		<description>Research Information Listing</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/admin/researchInformationListing.xhtml</url>
		</access>
		<entry-url>/admin/researchInformationListing.xhtml?basic=Y</entry-url>
	</function>	
	
	<function>
		<id>KT_LISTING</id>
		<name>KT Activities Listing</name>
		<description>KT Activities Listing</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>ktAdmin</value>
				</param>
			</authorizer>
			<url>/admin/ktListing.xhtml</url>
		</access>
		<entry-url>/admin/ktListing.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_INST_RI</id>
		<name>Manage Institute RI</name>
		<description>Manage Institute RI</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin, raeAdmin, libAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageInstituteRI.xhtml</url>
		</access>
		<entry-url>/admin/manageInstituteRI.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_KT_ACT</id>
		<name>Manage KT Activities</name>
		<description>Manage KT Activities</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>ktAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageKtActivities.xhtml</url>
		</access>
		<entry-url>/admin/manageKtActivities.xhtml</entry-url>
	</function>
	
	<function>
		<id>SEARCH_PROFILE</id>
		<name>Search Staff Profile</name>
		<description>Search Staff Profile</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin, ktAdmin, raeAdmin</value>
				</param>
			</authorizer>
			<url>/admin/searchProfile.xhtml</url>
		</access>
		<entry-url>/admin/searchProfile.xhtml</entry-url>
	</function>	
	
	<function>
		<id>GEN_REPORT</id>
		<name>Generate Report/ Statistic</name>
		<description>Generate Report/ Statistic</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/cdcfReportList2.xhtml</url>
		</access>
		<entry-url>/admin/cdcfReportList2.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_ELIGIBLE_STAFF</id>
		<name>Manage Eligible Staff List</name>
		<description>Manage Eligible Staff List</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>superRdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageEligibleStaff.xhtml</url>
		</access>
		<entry-url>/admin/manageEligibleStaff.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_CDCF_RPT_PERIOD</id>
		<name>Manage CDCF Reporting Period</name>
		<description>Manage CDCF Reporting Period</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/cdcfRptPeriodList.xhtml</url>
			<url>/admin/cdcfRptPeriodEdit.xhtml</url>
		</access>
		<entry-url>/admin/cdcfRptPeriodList.xhtml</entry-url>
	</function>	
	
	<function>
		<id>MANAGE_SAP_LOOKUP_VAL</id>
		<name>Manage Lookup Value For RDO</name>
		<description>Manage Lookup Value For RDO</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageSAPLookupValue.xhtml</url>
			<url>/admin/manageSAPLookupValue_edit.xhtml</url>
		</access>
		<entry-url>/admin/manageSAPLookupValue.xhtml</entry-url>
	</function>	
	<function>
		<id>VIEW_MY_SUMMARY</id>
		<name>Count My Research Information</name>
		<description>Count My Research Information</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/viewSum.xhtml</url>
			<url>/user/viewSumDetails.xhtml</url>
		</access>
		<entry-url>/user/viewSum.xhtml</entry-url>
	</function>	
	<function>
		<id>VIEW_DEPT_SUMMARY</id>
		<name>Count Research Information</name>
		<description>Count Research Information</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin, ktAdmin, deptAdmin</value>
				</param>
			</authorizer>
			<url>/user/viewSum.xhtml</url>
			<url>/user/viewSumDetails.xhtml</url>
		</access>
		<entry-url>/user/viewSum.xhtml?admin=Y</entry-url>
	</function>
	<function>
		<id>EXPORT_MY_DATA</id>
		<name>Export My Data</name>
		<description>Export My Data</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>acadStaff</value>
				</param>
			</authorizer>
			<url>/user/exportMyData.xhtml</url>
		</access>
		<entry-url>/user/exportMyData.xhtml</entry-url>
	</function>	
	<function>
		<id>TAKE_SNAPSHOT</id>
		<name>Take snapshot of outputs and projects</name>
		<description>Take snapshot of outputs and projects</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/takeSnapshot.xhtml</url>
			<url>/admin/takeSnapshot.xhtml</url>
		</access>
		<entry-url>/admin/takeSnapshot.xhtml</entry-url>
	</function>
	<function>
		<id>GEN_PRE_DEFINED_RPT</id>
		<name>Generate Pre-defined Reports</name>
		<description>Generate Pre-defined Reports</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin, rptUser</value>
				</param>
			</authorizer>
			<url>/admin/genPreDefinedRpt.xhtml</url>
			<url>/admin/genPreDefinedRpt.xhtml</url>
		</access>
		<entry-url>/admin/genPreDefinedRpt.xhtml</entry-url>
	</function>
	<function>
		<id>GEN_ADHOC_RPT</id>
		<name>Generate Ad-hoc Reports</name>
		<description>Generate Ad-hoc Reports</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/genAdhocRpt.xhtml</url>
			<url>/admin/genAdhocRpt.xhtml</url>
		</access>
		<entry-url>/admin/genAdhocRpt.xhtml</entry-url>
	</function>
	<function>
		<id>MAN_RES_IDS</id>
		<name>Manage Researcher IDs</name>
		<description>VManage Researcher IDs</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>libAdmin</value>
				</param>
			</authorizer>
			<url>/admin/researcherIDList.xhtml</url>
			<url>/admin/researcherIDList_edit.xhtml</url>
		</access>
		<entry-url>/admin/researcherIDList.xhtml</entry-url>
	</function>	
	<function>
		<id>MANAGE_PRE_DEFINED_RPT</id>
		<name>Manage Pre-defined Rpt</name>
		<description>Manage Pre-defined Rpt</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/managePreRpt.xhtml</url>
		</access>
		<entry-url>/admin/managePreRpt.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_PRE_DEFINED_RPT_CAT</id>
		<name>Manage Pre-defined Rpt Cat</name>
		<description>Manage Pre-defined Rpt Cat</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/managePreRptCat.xhtml</url>
		</access>
		<entry-url>/admin/managePreRptCat.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_REPORT_FILES</id>
		<name>Manage Report Files</name>
		<description>Manage Report Files</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageReportFiles.xhtml</url>
		</access>
		<entry-url>/admin/manageReportFiles.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_JOURNAL_RANKING</id>
		<name>Manage Journal Ranking</name>
		<description>Manage Journal Ranking</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>rdoAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageJournalRanking.xhtml</url>
		</access>
		<entry-url>/admin/manageJournalRanking.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_RAE_UOA</id>
		<name>Manage RAE UoA</name>
		<description>Manage RAE UoA</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, raeAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageUoA.xhtml</url>
		</access>
		<entry-url>/admin/manageUoA.xhtml</entry-url>
	</function>
	
	<function>
		<id>GEN_RAEES_RPT</id>
		<name>Generate RAEES Report</name>
		<description>Generate RAEES Report</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin, raeAdmin</value>
				</param>
			</authorizer>
			<url>/admin/genRaeesRpt.xhtml</url>
		</access>
		<entry-url>/admin/genRaeesRpt.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_RAEES_RPT</id>
		<name>Manage RAEES Report</name>
		<description>Manage RAEES Report</description>
		<access>
			<authorizer>
				<class-name>hk.eduhk.rich.access.authorizer.FunctionRoleAccessAuthorizer</class-name>
				<param>
					<name>authorizedRoles</name>
					<value>sysAdmin</value>
				</param>
			</authorizer>
			<url>/admin/manageRaeesRpt.xhtml</url>
		</access>
		<entry-url>/admin/manageRaeesRpt.xhtml</entry-url>
	</function>
</access-control>