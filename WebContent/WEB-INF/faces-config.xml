<?xml version="1.0" encoding="UTF-8"?>

<faces-config
    xmlns="http://java.sun.com/xml/ns/javaee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_2_1.xsd"
    version="2.1">
    
	<application>
	    
	    <locale-config>
	        <default-locale>en</default-locale>
	        <supported-locale>en</supported-locale>
	    </locale-config>
		
		<resource-bundle>
			<base-name>hk.eduhk.rich.bundle.MessageBundle</base-name>
			<var>bundle</var>
		</resource-bundle>

		<resource-bundle>
			<base-name>hk.eduhk.rich.bundle.FormBundle</base-name>
			<var>formBundle</var>
		</resource-bundle>

	</application>
	
	<factory>
        <exception-handler-factory>
            hk.eduhk.rich.AppExceptionHandlerFactory
        </exception-handler-factory>
 	</factory>

	<managed-bean>
	    <managed-bean-name>currentDate</managed-bean-name>
	    <managed-bean-class>java.util.Date</managed-bean-class>
	    <managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	
	<render-kit>
		<renderer>
			<display-name>SelectManyMenuRenderer</display-name>
			<component-family>org.primefaces.component</component-family>
			<renderer-type>org.primefaces.component.SelectManyMenuRenderer</renderer-type>
			<renderer-class>hk.eduhk.rich.SelectManyMenuRenderer</renderer-class>
		</renderer>
	</render-kit>
		
</faces-config>
