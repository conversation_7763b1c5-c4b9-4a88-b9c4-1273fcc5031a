<config xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns='http://www.ehcache.org/v3'
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.0.xsd
        					http://www.ehcache.org/v3/jsr107 http://www.ehcache.org/schema/ehcache-107-ext-3.0.xsd">

	<cache alias="app.acs">
		<key-type>java.lang.String</key-type> 
		<value-type>java.util.List</value-type>
		<expiry>
			<ttl unit="seconds">5</ttl>
		</expiry>
		<resources>
			<heap unit="entries">100</heap> 
		</resources>
	</cache>

	<cache alias="app.acsType">
		<key-type>java.lang.String</key-type> 
		<value-type>java.util.Map</value-type>
		<expiry>
			<ttl unit="seconds">5</ttl>
		</expiry>
		<resources>
			<heap unit="entries">100</heap> 
		</resources>
	</cache>
	
	<cache alias="app.sysParam">
		<key-type>java.lang.String</key-type> 
		<value-type>hk.eduhk.rich.param.SysParam</value-type>
		<expiry>
			<ttl unit="seconds">20</ttl>
		</expiry>
		<resources>
			<heap unit="entries">64</heap> 
		</resources>
	</cache>
	
	<cache alias="app.userRole">
		<key-type>java.lang.String</key-type> 
		<value-type>java.util.Set</value-type>
		<expiry>
			<ttl unit="seconds">5</ttl>
		</expiry>
		<resources>
			<heap unit="entries">20</heap> 
		</resources>
	</cache>

	<cache alias="ban.crse">
		<key-type>java.lang.String</key-type> 
		<value-type>hk.eduhk.rich.banner.BanCourse</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<listeners>
			<listener>
				<class>hk.eduhk.rich.cache.BanCourseCacheExpiredEventListener</class>
				<event-firing-mode>ASYNCHRONOUS</event-firing-mode>
				<event-ordering-mode>UNORDERED</event-ordering-mode>
				<events-to-fire-on>EXPIRED</events-to-fire-on>
			</listener>
		</listeners>
		<resources> 
			<heap unit="entries">10000</heap>
		</resources>
	</cache>

	<cache alias="ban.orgUnitMap">
		<key-type>java.lang.String</key-type> 
		<value-type>java.util.Map</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<resources>
			<heap unit="entries">16</heap>
		</resources>
	</cache>

	<cache alias="ban.person">
		<key-type>java.lang.Integer</key-type> 
		<value-type>hk.eduhk.rich.banner.BanPerson</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<listeners>
			<listener>
				<class>hk.eduhk.rich.cache.BanPersonCacheExpiredEventListener</class>
				<event-firing-mode>ASYNCHRONOUS</event-firing-mode>
				<event-ordering-mode>UNORDERED</event-ordering-mode>
				<events-to-fire-on>EXPIRED</events-to-fire-on>
			</listener>
		</listeners>
		<resources>
			<heap unit="entries">16384</heap>
		</resources>
	</cache>
	
	<cache alias="ban.person.userId">
		<key-type>java.lang.String</key-type> 
		<value-type>hk.eduhk.rich.banner.BanPerson</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<listeners>
			<listener>
				<class>hk.eduhk.rich.cache.BanPersonCacheExpiredEventListener</class>
				<event-firing-mode>ASYNCHRONOUS</event-firing-mode>
				<event-ordering-mode>UNORDERED</event-ordering-mode>
				<events-to-fire-on>EXPIRED</events-to-fire-on>
			</listener>
		</listeners>
		<resources>
			<heap unit="entries">16384</heap>
		</resources>
	</cache>

	<cache alias="ban.prog">
		<key-type>java.lang.String</key-type> 
		<value-type>hk.eduhk.rich.banner.BanProgram</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<listeners>
			<listener>
				<class>hk.eduhk.rich.cache.BanProgramCacheExpiredEventListener</class>
				<event-firing-mode>ASYNCHRONOUS</event-firing-mode>
				<event-ordering-mode>UNORDERED</event-ordering-mode>
				<events-to-fire-on>EXPIRED</events-to-fire-on>
			</listener>
		</listeners>
		<resources>
			<heap unit="entries">1024</heap>
		</resources>
	</cache>

	<cache alias="ban.progAttr">
		<key-type>java.lang.String</key-type> 
		<value-type>java.util.List</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<listeners>
			<listener>
				<class>hk.eduhk.rich.cache.BanProgramAttributeCacheExpiredEventListener</class>
				<event-firing-mode>ASYNCHRONOUS</event-firing-mode>
				<event-ordering-mode>UNORDERED</event-ordering-mode>
				<events-to-fire-on>EXPIRED</events-to-fire-on>
			</listener>
		</listeners>
		<resources>
			<heap unit="entries">1024</heap>
		</resources>
	</cache>

	<cache alias="ban.term">
		<key-type>java.lang.String</key-type> 
		<value-type>hk.eduhk.rich.banner.BanTerm</value-type>
		<expiry>
			<ttl unit="hours">8</ttl>
		</expiry>
		<listeners>
			<listener>
				<class>hk.eduhk.rich.cache.BanTermCacheExpiredEventListener</class>
				<event-firing-mode>ASYNCHRONOUS</event-firing-mode>
				<event-ordering-mode>UNORDERED</event-ordering-mode>
				<events-to-fire-on>EXPIRED</events-to-fire-on>
			</listener>
		</listeners>
		<resources>
			<heap unit="entries">128</heap>
		</resources>
	</cache>
	
</config>
