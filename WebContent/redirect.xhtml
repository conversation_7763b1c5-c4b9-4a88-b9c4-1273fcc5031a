<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:component="http://java.sun.com/jsf/composite/component" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core"      
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://java.sun.com/jsf/facelets"> 
    <h:head>
        <title>#{sysParamView.getValue('SYS_TITLE')}</title>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['image/favicon.ico']}" />
        <h:outputScript library="primefaces" name="jquery/jquery.js" target="head" />
        <h:outputScript>
        $(document).ready(
		    function() {
		    	let searchParams = new URLSearchParams(window.location.search);
				let paramPage = searchParams.get('page');
				let paramTitle = searchParams.get('title');
				if (paramTitle == null){
					paramTitle = "RICH";
				}
		        $('div.content').text('This process is automatic. Your browser will redirect to '+paramTitle+' shortly.');
		       window.setTimeout(function () {
			        window.location.href = paramPage;
			    }, 5000);
		        setInterval(updateTimer, 1000); 
		    }
		);
		var countdown = 4;
		function updateTimer(){
			if(countdown >= 0) {
				redirectTimeout.innerHTML =countdown;
				countdown--;
			}
  			
		}
	</h:outputScript>
    </h:head>
    <h:body>
    	<div style="font-weight:700" class="content"></div>
		<br/>
​		Please allow up to <h:outputText id="redirectTimeout" value="5"></h:outputText> seconds…
    </h:body>
</html>
