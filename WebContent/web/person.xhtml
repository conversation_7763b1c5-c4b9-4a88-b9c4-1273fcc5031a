<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:component="http://java.sun.com/jsf/composite/component" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core"      
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://java.sun.com/jsf/facelets"> 
    <h:head>
        <title>Profile of #{cvView.iUserInfo.title} #{cvView.iUserInfo.fullname} #{cvView.iUserInfo.chinesename} - The Education University of Hong Kong</title>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['image/favicon.ico']}" />
        <link rel="stylesheet" media="screen, print" type="text/css" href="#{request.contextPath}/resources/css/bootstrap.min.css" />
        <link rel="stylesheet" media="screen, print" type="text/css" href="#{request.contextPath}/resources/css/app_cv.css" />
        <link rel="stylesheet" media="screen, print" type="text/css" href="#{request.contextPath}/resources/css/texteditor.css" />

        
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Abril+Fatface"/>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Alegreya+SC"/>
        <h:outputScript library="webjars" name="font-awesome/6.2.0/js/all.js" target="head" />
        <h:outputScript library="primefaces" name="jquery/jquery.js" target="head" />

		<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="Pragma" content="no-cache" />
		<meta http-equiv="Expires" content="0" />        
		
		<!-- Google tag (gtag.js) -->
		<script async="async" src="https://www.googletagmanager.com/gtag/js?id=G-5L4KSMKYKR"></script>
		<script>
		  window.dataLayer = window.dataLayer || [];
		  function gtag(){dataLayer.push(arguments);}
		  gtag('js', new Date());
		
		  gtag('config', 'G-5L4KSMKYKR');
		</script>
    </h:head>
    <h:body>
    	<f:metadata>
			<f:viewParam name="pid" value="#{cvView.paramPid}" />
			<f:event listener="#{cvView.checkValidStaff}" type="preRenderView" />
		</f:metadata>
		 <ui:include src="#{cvView.getCvStylePage()}"/>
		
    </h:body>
</html>