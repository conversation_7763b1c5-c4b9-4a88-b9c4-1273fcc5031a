/* general */
body
{
	font-size: 0.9em;
	font-family: 'Open Sans', Arial, sans-serif;
	margin: 0.5em;
} 

pre 
{
	white-space: pre-wrap;       /* css-3 */
	white-space: -moz-pre-wrap;  /* Mozilla, since 1999 */
	white-space: -pre-wrap;      /* Opera 4-6 */
	white-space: -o-pre-wrap;    /* Opera 7 */
	word-wrap: break-word;       /* Internet Explorer 5.5+ */
	font-family: 'Open Sans', Arial, sans-serif;
	font-size: 0.9em;
}
a {text-decoration: none;}

optgroup {
    background-color: #d1e1eb;
    color: #055588;
    font-size:14px;
}
option {
    background-color: white;
    color: #495057;
}

/* IE specific */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) 
{
   .ui-g {display: block;}
}



/* Components
----------------------------------*/
.ui-datatable thead th{
	background:#2196F3 !important;
	border-width: 0 1px 0 1px !important;
	color:#fff !important;
	padding:0.4em 1em 0.4em 1em !important;
}
.ui-datatable .ui-datatable-data > tr > td {
    padding: 0;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-width: 0 0 1px 0;
}
.ui-datatable .ui-datatable-data > tr .ui-rowgroup-toggler .ui-rowgroup-toggler-icon{
	color:#fff !important;
}
.ui-datatable .ui-paginator{
	padding: 0 !important;
}

.ui-button-text{
	padding:4px 12px 4px 12px !important;
}   
.ui-button-text-icon-left .ui-button-text{
	padding:4px 12px 4px 35px !important;
}  
.ui-selectmanycheckbox label{display:inline;}
.ui-tabs.ui-tabs-top > .ui-tabs-nav li.ui-tabs-header.ui-state-active{
	background:#2196F3 !important;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-header.ui-state-active a{
	color:#fff !important;
}
.ui-fileupload-filename{
	display:none;
}

.ui-icon.fa {text-indent:0px; background-image:none;} 	/* For FontAwesome 4 icons */
.ui-icon.svg-inline--fa  {background-image:none;}  		/* For FontAwesome 5 icons */


.ui-menu.ui-menu-dynamic {width: initial; }
.ui-menu.ui-widget .ui-menuitem-link .ui-menuitem-text {margin-right: 0.4em;}
.ui-menu .ui-menu-list .ui-widget-header{
	background:#006B87 !important;
}
.ui-menu .ui-menu-list .ui-widget-header h3{
	color:#fff !important;
}
.ui-message{
	padding: 0 !important;
	display: block !important;
}
.ui-messages > div {
	padding: 0.7rem !important;
}
.ui-messages ul{
	margin: 0 30px !important;
}
.ui-orderlist.ui-grid-responsive .ui-orderlist-controls .ui-button{
	width: 20% !important;
}
.ui-paginator{
	font-weight: 700 !important;
}

.ui-panel .ui-panel-content{
	padding: .5em 1em !important;
}
.ui-scrolltop {
  background: #026539 !important;
}
.ui-scrolltop:hover {
    background: #f06524 !important; 
}
.ui-selectonemenu-item{
	color:#000 !important;
}
.ui-selectonemenu .ui-selectonemenu-label{
	white-space: pre-wrap;
}
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a,.ui-widget-header .ui-state-highlight a {
	color:#666666;
}
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
	color:#666666;
}
.ui-state-error a, .ui-widget-content .ui-state-error a, .ui-widget-header .ui-state-error a {
	color:#666666;
}
.ui-state-error-text, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error-text {
	color:#666666;
}


.admin-data-expansion .data-cell	{padding: 0.3em;}

.admin-content-title {
	color:#186ba0; 
	font-size:1.5em; 
	font-weight:bold; 
	padding-top:0; 
	padding-bottom:0.2em; 
	margin-bottom:10px; 
	display:block;
	border-bottom:1px solid #186ba0;
	}

.after-space {padding-right:15px;}

.badge 
{
	border: 4px solid #c1c1c1;
	min-width: 40px;
	max-width: 40px;
	min-height: 40px;
	max-height: 40px;
	text-align: center;
	line-height: 40px;
	border-radius: 24px;
	font-family: Kameron;
	font-weight: 700;
	font-size: 20px;
	color: #808080;
	background-color: #fff;
}
.btn-action{
	margin-right: 5px !important;
    margin-bottom: 1px !important;
}
.btn-action-pink{
    background: #a60261 !important;
    border: 1px solid #a60261 !important;
    color: #fff !important;
}
.btn-action-blue{
    background: #006b87 !important;
    border: 1px solid #006b87 !important;
    color: #fff !important;
}
.btn-action-yellow{
    background: #d3b10d !important;
    border: 1px solid #d3b10d !important;
    color: #fff !important;
}
.btn-action-red{
    background: #d71717 !important;
    border: 1px solid #d71717 !important;
    color: #fff !important;
}

.btn-action-lightpurple{
    background: #9975b9 !important;
    border: 1px solid #9975b9 !important;
    color: #fff !important;
}
.btn-action-purple{
    background: #3c1361 !important;
    border: 1px solid #3c1361 !important;
    color: #fff !important;
}

.btn-action-lightblue{
    background: #0da5d3 !important;
    border: 1px solid #0da5d3 !important;
    color: #fff !important;
}

.btn-action-darkgrey{
    background: #666 !important;
    border: 1px solid #666 !important;
    color: #fff !important;
}

.btn-action-orange{
    background: #ff5722 !important;
    border: 1px solid #ff5722 !important;
    color: #fff !important;
}
.btn-back{
	margin-right: 5px !important;
    margin-bottom: 1px !important;
    background: #186ba0 !important;
    border: 1px solid #c5ddf1 !important;
}


.banner {margin-bottom:0.3em; padding:0em;}
.banner .logo {text-align:left; padding:0em; height:80px;}
.banner .title {font-size:1.6em;  padding-right: 5px; vertical-align:middle; color:#186ba0; }
.banner .ui-panel-content {padding-left:0.5em; padding-top:0.2em; padding-bottom:0em; padding-right:0em;}
.banner table {width:100%; height:72px; margin:0em; padding:0em; border:0em; max-width: 97vw;}

.center-align-column {
	text-align:left;	 
	display: inline;
}
.left-align-column {
	text-align:left;	 
	display: inline;
}
.circleBtn {
	font-size: 1.0em;
	border-radius: 50%;  
	/*box-shadow: 2px 2px 4px #999999;*/
	background-color: #8C0000;
	color: white;
	position: absolute;
	right: 0; 
	bottom: 0; 
	width: 35px;
	height:35px;
	/* Safari, Opera, and Chrome */
	display:-webkit-box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	/* Internet Explorer 10 */
	display:-ms-flexbox;
	-ms-flex-pack:center;
	-ms-flex-align:center;
	margin-right:10px;
	margin-bottom:10px;
}

/*.circleBtnText{
	vertical-align: middle;
}*/

#contentPanel {border:none;}

#contentPanel .ui-panel-content
{
	padding-top: 0 !important;
}
.course-block.processed .ui-widget-header {background-color:#f06524; color: #444444;}
.course-block .caption {font-size:0.9em; display:flex; align-items:center; font-weight:bold;}
.course-block .content {font-size:0.9em; display:flex; align-items:center;}

.course-info-panel .caption {display:flex; align-items:center; font-weight:bold;}
.course-info-panel .content {display:flex; align-items:center;}


.dashboard-btn-txt {font-size:1.2em; display:inline-block; margin-bottom: 10px; margin-top: 10px;}

.dashboard-item {text-align:center; padding-bottom:1.5em;}

.dashboard-item .circle-button 
{
	font-size: 1.4em;
	border-radius: 50%;  
	box-shadow: 2px 2px 4px #999999;
	color: green;
	position: absolute;
	right: 0; 
	bottom: 0; 
	width: 2.2em;
	height: 2.2em;
	/* Safari, Opera, and Chrome */
	display:-webkit-box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	/* Internet Explorer 10 */
	display:-ms-flexbox;
	-ms-flex-pack:center;
	-ms-flex-align:center;
	margin-right:5px;
	margin-bottom:5px;
}

.dashboardInner {
	 position: relative;
	 width: 100%;
	 height: 170px;
	 margin: 0 auto;
	 border: 5px #444444;
	 border-radius: 10px;
	 box-shadow: 5px 5px 10px silver;
	 
	background: white; /* For browsers that do not support gradients */
	background: -webkit-linear-gradient(left top, #F6F6F6, white,  white); /* For Safari 5.1 to 6.0 */
	background: -o-linear-gradient(bottom right, #F6F6F6,  white,white); /* For Opera 11.1 to 12.0 */
	background: -moz-linear-gradient(bottom right, #F6F6F6,  white,white); /* For Firefox 3.6 to 15 */
	background: linear-gradient(to bottom right, #F6F6F6, white, white); /* Standard syntax */	 
	
	
}

.dashboardInner a:link, a:visited{
	display:block;
    width:100%;
    height:100%;	
	text-decoration: none;
	padding: 10px 0 0 0;
	color: #186ba0;
	font-weight:400;
}

.dashboardInner a:hover, a:active{
	font-weight:700;
	transition: all .2s;
}

.dashboardInner:hover{
	box-shadow: 5px 5px 10px #808080;
	transition: all .2s;
}

.dashboardSubTitle {
	color: #444444; 
	font-size:1.4em; 
	padding-bottom:1em; 
	vertical-align:middle; 
	text-align:center; 
	text-decoration: none;
}

.default-dataTable .filter {font-size:0.8em;}
.default-dataTable .filter > .ui-widget {font-size:0.8em;}
.long-dataTable .ui-paginator-top {text-align: left;}
.long-dataTable .ui-paginator-bottom {text-align: left;}

.default-linkButton {margin: 3px !important;}

.distr-assoc-panel {background-color:#F7F0D6; border-color:#DDDDDD;}

.distr-panel .ui-panel-content {padding:0em;}
.distr-panel .header {font-weight:bold; padding:0.5em;}
.distr-panel .courseCode {font-size:1.2em; color: darkorange; padding-bottom:0.2em;}
.distr-panel .courseName {color:#444444; padding-bottom:0.2em;}

.distr-panel.processed .ui-widget-header {background-color:green;}

.distr-panel .caption {font-weight:bold; padding:0;}
.distr-panel .caption table {width:100%; border:0; border-spacing:0;}
.distr-panel .caption table td {padding: 0;}
.distr-panel .caption table td:last-child {text-align:right;}

.distr-panel .content {display:flex; align-items:center;}
.distr-panel .content > div {font-size:0.9em; padding:0.3em;}

.distr-select-scroller li {display:inline;}

.distr-exceed-button {color:white !important; background:orange !important; padding:0em; font-size:0.9em !important;}
.distr-select-button {color:white !important; background:#006699 !important; padding:0em; font-size:0.9em !important;}

.distr-tab-view {border-bottom:none !important;}
.distr-tab-view .ui-tabs-panel {padding-left:0em; padding-right:0em; }

.edit-panel {width:100%;}
.edit-panel > tbody > tr {border: none;}
.edit-panel > tbody > tr td {border: none !important;}
.edit-panel > tbody > tr > td:first-child {white-space:nowrap; padding-right:1em; vertical-align:middle; width:1px; height:2em;}
.edit-panel > tbody > tr > td:nth-child(3) {white-space:nowrap; padding-right:1em; vertical-align:middle; width:1px; height:2em;}

.form-sub-title {
	font-size: 20px; font-weight:bold; color:#4d6502;
}

.form-field-title {font-size: 1.0em; font-weight:bold; color:#444444;}
.form-field-annotation {font-size: 1.0em;}

.field-title {font-weight:bold; color:#444444;}
.field-title-bottom-pad {padding-bottom:0.5em;}
.field-annotation {font-size: 0.8em;}

.filter-panel .caption 				{font-weight:bold;}
.filter-panel .input			 	{font-size:1em; padding-top:0em; padding-bottom:0.6em;}
.filter-panel .ui-panel-content 	{padding-left:0.2em !important; padding-right:0.2em !important;}
.filter-panel .ui-panel-titlebar 	{padding-left:0.5em !important; padding-right:0.5em !important;}

.first-letter-capitalize {display:inline-block; text-transform:lowercase;}
.first-letter-capitalize:first-letter {text-transform:uppercase;}

.form-batch-end {color : #ff0000; }
.form-batch-not-start {color : #000000; }
.form-is-submitted{ color : #006635; }
.form-is-not-submitted{ color : #ff0000; }

.form-title {color:#666666; font-weight:bold;}

.frame-bottom {margin-top: 0.5em; font-size: 1em;}
.frame-bottom td {padding: 0px;}
.scm-version 	{width:100%; text-align:right; color: #ffffff;}


.help_icon {text-align:right; color: #4C8C6A;}

.icon-checkbox {text-decoration:none;}
.icon-checkbox .fa-stack {width:0em; height:0.6em; padding-right:1em; right:0.3em; font-size:1.1em;}

.info-panel {width:100%;}
.info-panel > tbody > tr {border: none;}
.info-panel > tbody > tr td {border: none !important; white-space:nowrap; padding-right:1em; vertical-align:top;}

.input-validation-message {display: inline-block;}

.no-border .ui-widget-content {border:none !important;}

.noprint {display:block};

.notes {padding: 18px 2px}
.notes_desc {color: #4C8C6A;font-size: small; font-style: italic;}

.overlaypanel-nopad .ui-overlaypanel-content {padding:0}

.panel-1st-heading-row {
     background-image: linear-gradient(to right, #f06524 , #fff);
     color: #fff;
     font-weight: bold;
}



.panel-2nd-heading-row {
     background-image: linear-gradient(to right, #026539 , #fff);
     color: #fff;
     font-weight: bold;
}

.panel-3rd-heading-row {
     background-image: linear-gradient(to right, #FFD600 , #fff);
     color: #000;
     font-weight: bold;
}

.panel-4th-heading-row {
     background-image: linear-gradient(to right, #01579B , #fff);
     color: #fff;
     font-weight: bold;
}

.printonly { display: none; }

.page-break { display:none; } /* for print */

.prog-item {text-decoration: none;}
.prog-item .ui-panel.prog-selected {background-image: linear-gradient(to bottom right, #DDDDDD, white);}
.prog-item table {width: 100%;}
.prog-item .code-cell {color: #444444;}
.prog-item .count-cell {text-align:right; width:2em;}
.prog-item .update-cell {font-size:0.6em; color:grey;}

.prog-item .count-box {padding:0.3em; border-radius: 1em; width:2em; font-size:0.8em; font-weight:bold; color: white; text-align:center;}
.prog-item .count {background-color:green;}
.prog-item .count-none {background-color:red;} 


.report-selection-table {font-size:0.9em;}

.riForm-item-title{
	color:#055588; 
	font-weight:700;
}

.riForm-item-ans{
	color:#00a2c7; 
	font-weight:700;
}

.riForm-item-note{
	border-left: solid 3px #e91e63;
	bottom: -5px;
	color:#01564e; 
	font-size: 14px !important;
	font-weight:400;
	line-height: 1.1;
	padding: 0 0 0 8px;
	position: relative;
}

.riFormTable thead th{
	background:#186ba0 !important;
	border-width: 0 1px 0 1px !important;
	color:#fff !important;
	padding:0.4em 1em 0.4em 1em !important;
}

.raeForm-item-title{
	color:#07314c; 
	font-size: 14px !important;
	font-weight: 600;
	vertical-align: -webkit-baseline-middle;
}

.raeForm-item-ans{
	font-size: 18px !important;
	vertical-align: -webkit-baseline-middle;
	color: #3168af;
    font-weight: 600;
}

.raeForm-item-note{
	border-left: solid 3px #e91e63;
	bottom: -5px;
	color:#01564e; 
	font-size: 14px !important;
	font-weight:400;
	line-height: 1.1;
	padding: 0 0 0 8px;
	position: relative;
}

.deptCheckBoxLabel .ui-chkbox-label{padding-left:2em;}

.facDeptText
{
	background: #fff;
    font-weight: 700;
    margin-left: 10px;
    color: #d50082;
    padding: 1px 5px 1px 5px;
    border-radius: 4px;
}

.question-annotation
{
	color: #3879D9;
	margin-bottom: 10px;
	font-size: 15px!important;
}

.question-text
{
	font-size: 17px!important;
}
.question-radio-outer-circle 
{
	box-sizing: border-box;
	height: 20px;
	left: 0;
	top: 0;
	transition: border-color ease 280ms;
	width: 20px;
	border-width: 2px;
	border-style: solid;
	border-radius: 50%;
	border-color: rgba(0,0,0,.54);
	
}

.remaining{ font-size:0.8em;text-align: left; }

.shadow {text-shadow: 2px 2px 2px #ccc;}


.supplFormNewBtn 
{
	width:100%;
	color:white !important;
	background:#669900 !important;
}

.supplFormRemoveBtn
{
	width:115px;
	vertical-align:middle;
	color:white !important;
	background:red !important;
	font-size:0.9em !important;
	float: right;
}

.supplFormSelectBtn {height:22px; color:white !important; background:#006699 !important;}
.supplFormSelectBtn .ui-button-text{padding:0em;font-size:13px}

.supplForm-preview-panel {text-align:left; background-color:#FFFFEE; max-width:80%; left:0}
.supplForm-preview-panel-KT {overflow:auto; max-height:60%;}

.supplForm-select-sideBar {width:70%; overflow:scroll;}
.supplForm-select-sideBar .title {font-weight:bold; font-size: 1.2em;}
.supplForm-select-sideBar .instructions {font-size: 0.8em; color: #777777; padding-top: 1em; padding-bottom:1em;}

.question-list .no-header thead {display: none;}
.question-list .ui-datatable {}
.question-list .ui-datatable-even {background-color: #FFFFBF;}
.question-list .ui-datatable-odd {background-color: #FFFFDD;}

.table-column-center {text-align:center;}

.time-text-splitter { padding-right: 6px; }


.user-content-title {color: #444444; font-size:1.5em; font-weight:bold; padding-bottom:0.3em; vertical-align:middle; text-align:center;}
.user-content-subtitle {color: #00468C; font-size:1.3em; font-weight:bold; padding-bottom:0.3em; display:block; margin-bottom:0px; margin-top:15px;}

.user-info-panel {text-align:right; padding-bottom:0.5em; color: #186ba0; max-width: 97vw;}

.zero-size {visibility: hidden; padding: 0px; margin: 0px; border: 0px; width: 0px; height: 0px;}


.distrPanelConfirmDialog .ui-icon {display:none;}

.distrPanelConfirmDialogFieldName {font-weight:bold;}

			
.width-fifty-percent {
     width: 50%;
}
			
@media all and (min-width: 641px)
{	
	.statement-links {display: table;}
	.statement-links .row {display: table-row;}
	.statement-links .cell {display: table-cell;}
	.statement-links .separator {display:inline;}	
}

@media all and (max-width: 780px)
{
	.banner .title { padding-left: 62px; position: relative;}
	.banner .title .logo {font-size:0.5em;}
	
    .banner-eduhk-logo  { clip: rect(0px, 48px, 57px, 0px); position:absolute; top: 14px;} 		 	
}

@media all and (max-width: 640px)
{
    .statement-links .cell {display: block;}
	.statement-links .row {display: table-cell; font-size:1.2em;}
	.statement-links .separator {display:none;}
}


@media all and (max-width: 420px)
{
	body {margin:0.2em;} 
	
	.banner .title .logo {display:none;}
	.banner .title .name {font-size:0.9em;}
	
	#contentPanel .ui-panel-content {padding-left:0.2em; padding-right:0.2em;}
	
	.dashboard-item {text-align:center; padding-bottom: 1em;}
	
	.supplForm-select-sideBar {width:50%; overflow:scroll;}
	.supplForm-select-sideBar .title {font-weight: bold; font-size: 1em;}
	
	.user-info-panel {font-size:0.8em;}
}


/* use div tag as table */
.css_table {display:table;}
.css_tr {display: table-row;}
.css_td {display: table-cell;}


/* 
	Fix of Primefaces datatable reflow mode
	https://github.com/primefaces/primefaces/issues/1258 
*/
@media (max-width: 35em) 
{
	.ui-table-reflow.ui-responsive td, .ui-table-reflow.ui-responsive th 
	{
	    max-width: 100% !important;
	    min-width: 100% !important;
	    width: 100% !important;
	}
}

@media (max-width: 45em) 
{
	.ui-datatable-reflow .ui-datatable-data td[role="gridcell"]
	{
	    max-width: 100% !important;
	    min-width: 100% !important;
	    width: 100% !important;
	}
}

@media all and (min-width: 640px) 
{
	
	.center-align-column {
		text-align:center; 
		display: block;
	}
	.left-align-column {
		text-align:left; 
		display: block;
		padding-left:10px;
	}		
}
