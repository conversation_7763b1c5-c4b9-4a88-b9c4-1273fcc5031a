@page {
    size: A4;
    margin: 17mm 10mm 17mm 10mm;
}

@media print 
{
	
	* {s-webkit-print-color-adjust:exact; color-adjust:exact;}
	* {font-family: 標楷體, Arial, Helvetica, sans-serif; font-size: 8pt !important;}
	div {padding:0px;margin:0px;vertical-align: top;}
	.ui-g-12 {padding:1px;margin:0px;vertical-align: top;}
	.banner {margin-bottom:0.3em; padding:0em;}
	.banner .logo {text-align:left; padding:0em; height:80px;}
	.banner .name { font-size: 1.3em; font-weight: bold; padding-top:2em; text-align:center;} 
    .banner .language { vertical-align: top; width: 120px; }
	.banner .language .horizontal {display: inline; float : right; padding: 0px 0px;}
	.banner .language .ui-datalist-data {overflow:hidden;}
	.banner .language .ui-panel-content {padding:0em;}
	.banner .language .ui-widget-content { border:0; }
	.banner .language a {padding:0.1em;text-decoration: none;vertical-align: middle;}
	.banner .table {width:100%; margin:0em; padding:0em; border:0em;}
	.banner .ui-panel-content { padding:0.1em 0.5em; }
	
	.bold { font-weight: bold; }
	
	.bottom-line-level1 { border-bottom: 3px solid #f06524; }
	.bottom-line-level2 { border-bottom: 1px solid #d3d3d3; }
	
	.column-first { width: 60%; }
	.column-second { width: 40%; }
	
	.dbdata { color : #006635; font-style: italic; font-weight: bold; text-align: right; }
	
	.description { background: #c0c0c0; }
	
	.field-title {font-weight:bold; color:#444444;}
	
	.frame-bottom {margin-top: 0.5em; font-size: 1em;}
	.frame-bottom td {padding: 0px;}
	
	.page-break { page-break-after: always; visibility: hidden; }
		
	.section { width:100%; /* border: 1px solid #e6e6e6; */ }
	.section .description{ background: #f7f7f7; font-size: 0.8em; padding :1em; -moz-border-radius: 4px; -webkit-border-radius: 4px; border-radius: 4px;}
	.section .form-component {}
	.section .form-componentRadio5 {}
	.section .form-component input[type="radio"] + label { margin-right: 5em; white-space: nowrap; }
	.section .form-component input[type="radio"]:checked + label { font-weight: bold; font-style: italic; }
	.section .form-componentRadio5 input[type="radio"] + label { margin-right: 3em; white-space: nowrap; }
	.section .form-componentRadio5 input[type="radio"]:checked + label { font-weight: bold; font-style: italic; }
	
	.section .form-component tr{ display: inline-block; }
	.section .form-component tr td{ display: inline-block; width: 100%; }
	
	.section .form-component textarea { width:99% }
	.section .form-component textarea.bigger { height:9em; }
	.section .form-component textarea.biggest { height:18em; }
	
	.section .form-componentRadio5 tr{ display: inline-block; }
	.section .form-componentRadio5 tr td{ display: inline-block; width: 100%; }
		
	.section .infoPanel tr { display: inline-block; width: 100%;}
	.section .infoPanel tr td {width: 51%; margin-bottom: 0.4em;}
	.section .infoPanel tr td:nth-child(even){ display: inline-block; width: 48%; }
	
	.section .question {border-bottom: 1px solid #dddddd; margin-bottom: 1.5em; margin-top: 0.5em; padding-bottom: 0.5em; page-break-inside: avoid !important;}
	
	.section .title { font-weight: bold; padding-top: 1em; text-align: left; }
	.section .title h2 {font-size: 1em; margin:0}
	
	.ui-column-title{
		margin: 0 !important;
	}
	.ui-datatable .ui-datatable-data > tr > td {
   		padding: 2px !important;
	}
	.noprint { display: none;}
	.data-noprint { display: none !important;}
   	.printonly { display: block; 
         white-space: -moz-pre-wrap; /* Mozilla */
         white-space: -hp-pre-wrap; /* HP printers */
         white-space: -o-pre-wrap; /* Opera 7 */
         white-space: -pre-wrap; /* Opera 4-6 */
         white-space: pre-wrap; /* CSS 2.1 */
         white-space: pre-line; /* CSS 3 (and 2.1 as well, actually) */
         word-wrap: break-word; /* IE */
         word-break: break-all; 
    }


	.ui-panelgrid .ui-grid-responsive .ui-grid-row { border-top: 0; }
	.ui-panelgrid .ui-grid-responsive .ui-panelgrid-cell { padding: 0; }
	.ui-widget-content { border: 0; }
   
   	#confidential { position: absolute; top: 0px; right: 0px; }
	#confidential img {width: 128px;}
}
