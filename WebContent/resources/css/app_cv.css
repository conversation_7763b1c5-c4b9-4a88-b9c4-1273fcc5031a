/* general */
body
{
	font-size: 0.9em;
	font-family: 'Open Sans', Arial, sans-serif;
	margin: 0.5em;
} 

.content-hide{
	display:none;
}

.ui-datatable thead th{
	border:0;
	padding:0 !important;
}

.ui-datatable tbody td{
	border:0;
	border-width: 0;
	padding:0 !important;
}

.ui-panel-content{
	padding:0 !important;
}

.ui-widget-content
{
	border-top:3px solid transparent;
	background:transparent !important;
	padding:0;
}

.container-fluid
{
	padding: 0px 200px 0px 200px;
}
.cv_dot 
{
  height: 5px;
  width: 5px;
  background-color: #666;
  --border-radius: 50%;
  display: inline-block;
  margin-bottom: 2px;
  margin-right: 5px;
}
.cv_content
{
	background: #c0c0c0;
	color: #fff;
}

.cv_left_contact
{
	background: #000;
	color: #fff;
}
.cv_style2_left_contact_first
{
	background: #fff;
	color: #000;
	border-left: 2px solid #f06524;
	border-right: 2px solid #f06524;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
.cv_style2_left_contact_middle
{
	background: #fff;
	color: #000;
	border-left: 2px solid #f06524;
	border-right: 2px solid #f06524;
}
.cv_style2_left_contact_last
{
	background: #fff;
	color: #000;
	border-left: 2px solid #f06524;
	border-right: 2px solid #f06524;
	border-bottom: 2px solid #f06524;
}

.cv_right_employment_hist_blk
{
	padding-bottom: 30px;
}

.cv_website_url
{
	color: #fff;
}
.cv_style2_website_url
{
	color: #666;
}

.cv_website_url:hover {
  color: #fff;
  text-decoration: none;
}
.cv_style2_website_url:hover {
  color: #026539;
  text-decoration: none;
}

.cv_left_contact_header
{
	color: #fde853;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:right;
}
.cv_style2_left_contact_header
{
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align: center;
    background: #f06524;
    border-radius: 25px;
    margin-top: 10px;
}
.cv_left_contact_header_line
{
	border-width: 0;
	height: 5px;
	background-image: linear-gradient(90deg,
	#fff 0%,#fde853 50%,#e97a01 100%);
}

.cv_left_contact_info_title
{
	font-family: 'Alegreya SC', Arial;
	font-size: 18px;
	text-align:right;
}

.cv_style2_left_contact_info_title
{
	font-family: 'Alegreya SC', Arial;
	font-size: 18px;
	text-align:left;
	color: #026539;
}

.cv_left_contact_info_content
{
	font-size: 12px;
	color: #fff;
	text-align:right;
	padding-bottom:7px;
	word-wrap: break-word;
}
.cv_style2_left_contact_info_content
{
	font-size: 12px;
	color: #666;
	text-align:left;
	padding-bottom:7px;
	word-wrap: break-word;
}
.cv_left_contact_info_icon
{
	font-size: 18px;
	padding-top: 8px;
	padding-left: 0px;
	padding-right: 10px;
}
.cv_style2_left_contact_info_icon
{
	font-size: 16px;
	color: #387bab;
	padding-top: 8px;
	padding-left: 0px;
	padding-right: 10px;
}

.cv_style2_left_info_header
{
	background: #f9e0d3;
	border-top: 2px solid #f06524;
	border-left: 2px solid #f06524;
	border-right: 2px solid #f06524;	
	color: #000;
	font-family: 'Alegreya SC', Arial;
	font-size: 18px;
	margin-top: 7px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
}
.cv_style2_left_info_content
{
	border: 2px solid transparent;
	border-image: linear-gradient(to bottom, #f06524 0%, #f06524 100%);
	border-image-slice: 1;
	color: #000;
	font-size: 12px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
	word-wrap: break-word;
}

.cv_left_research_header
{
	--background: #e0cb54;
	background: #B1B7D1;
	color: #000;
	font-family: 'Alegreya SC', Arial;
	font-size: 20px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:right;
}

.cv_left_research_info_content
{
	--background: #666;
	background: #6770A7;
	
	color: #fff;
	font-size: 12px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
	word-wrap: break-word;
}

.cv_left_research_info_content a:link
{
	color:#FFF200;
}

.cv_left_teaching_header
{
	background: #bce166;
	color: #005e00;
	font-family: 'Alegreya SC', Arial;
	font-size: 20px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:right;
}

.cv_left_teaching_info_content
{
	background: #005e00;
	color: #fff;
	font-size: 12px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
	word-wrap: break-word;
}

.cv_left_teaching_info_content a:link
{
	color:#FFF200;
}

.cv_left_external_header
{
	background: #888;
	color: #000;
	font-family: 'Alegreya SC', Arial;
	font-size: 17px;
	padding-top: 7px;
	padding-bottom: 7px;		
	text-align:right;
}

.cv_left_external_info_content
{
	background: #c0c0c0;
	color: #000;
	font-size: 12px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
	word-wrap: break-word;
}

.cv_left_external_info_content a:link
{
	color:#FFF200;
}

.cv_left_oth_activity_header
{
	background: #1f1645;
	color: #B3E5FC;
	font-family: 'Alegreya SC', Arial;
	font-size: 17px;
	padding-top: 7px;
	padding-bottom: 7px;		
	text-align:right;
}

.cv_left_oth_activity_info_content
{
	background: #00a2c7;
	color: #fff;
	font-size: 12px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
	word-wrap: break-word;
}

.cv_left_oth_activity_info_content a:link
{
	color:#fed880;
}

.cv_name
{
	color:#000;
	font-family: "Abril Fatface", Arial;
	font-size:40px;
	font-weight: 400;
	margin-left: 5px;
	padding: 20px 0px 8px 0px;
	letter-spacing: 1px;
}
.cv_style2_name
{
	color:#000;
	font-family: "Abril Fatface", Arial;
	font-size:38px;
	font-weight: 400;
	margin-left: 5px;
	padding: 0px 0px 4px 0px;
	letter-spacing: 1px;
}
.cv_chi_name
{
	color:#282830;
	font-family: "Abril Fatface", Arial;
	letter-spacing: 5px;
	font-size:40px;
	font-weight: 700;
}
.cv_style2_chi_name
{
	color:#282830;
	font-family: "Abril Fatface", Arial;
	letter-spacing: 5px;
	font-size:32px;
	font-weight: 700;
}
.cv_photo
{
	padding: 0;
}

.cv_post_table
{
	border-radius: 5px;
	background: #000;
}
.cv_style2_post_table
{
	border-radius: 5px;
	background: #fff;
}
.cv_post
{
	padding: 0px 0px 0px 0px;	
	color:#fff;
	font-size:12px;
	font-weight: 400;
	margin-left: 20px;
}
.cv_style2_post
{
	padding: 3px 0px 3px 0px;	
	color:#f06524;
	font-size:14px;
	font-weight: 400;
	margin-left: 0px;
}
.cv_post_desc
{
	color:#D9BE55;
	font-size:12px;
	font-weight: 400;
	margin-left: 20px;
	padding: 0px 0px 0px 0px;
}
.cv_style2_post_desc
{
	color:#000;
	font-size:14px;
	font-weight: 400;
	margin-left: 20px;
	padding: 3px 0px 3px 0px;
}
.cv_right_profile_header
{
	background: #B59824;
	border-top: 5px solid #FFD24D;
	border-left: 5px solid #FFD24D;
	border-right: 5px solid #FFD24D;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	padding-bottom: 0px;
	letter-spacing: 3px;
}
.cv_style2_right_profile_header
{
	background: #fff;
	color: #000;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	padding-bottom: 0px;
	letter-spacing: 3px;
}
.cv_right_profile_header_2
{
	background: #B59824;
	border-left: 5px solid #FFD24D;
	border-right:5px solid #FFD24D;
	font-size: 22px;
	text-align:left;
	padding-bottom: 5px;
}
.cv_style2_right_profile_header_2
{
	background: #fff;
	font-size: 22px;
	text-align:left;
	padding-bottom: 5px;
}
.cv_right_profile_header_line
{
	border: 2px #FFD24D solid;
	margin-top: 0;
}
.cv_style2_right_profile_header_line
{
	border: 1px #666 dotted;
	margin-top: 0;
}
.cv_right_profile_info_content
{
	background: #B59824;
	border-bottom: 5px solid #FFD24D;
	border-left: 5px solid #FFD24D;
	border-right: 5px solid #FFD24D;
	color: #fff;
	font-size: 14px;
	padding-bottom: 10px;
	text-align:left;
}
.cv_style2_right_profile_info_content
{
	background: #fff;
	color: #000;
	font-size: 14px;
	padding-bottom: 10px;
	text-align:left;
}
.cv_right_profile_info_content span
{
	background: #B59824 !important;
	color: #fff !important;
	font-size: 14px !important;
	text-align:left !important;
}
.cv_right_research_header
{
	background: #9EA6C7;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	padding-bottom: 0px;
	letter-spacing: 3px;	
}
.cv_right_research_header_line
{
	border: 2px #fff solid;
	margin-top: 0;
}
.cv_right_research_info_content
{
	background: #9EA6C7;
	color: #fff;
	font-size: 14px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
}
.cv_right_research_info_content span
{
	background: #9EA6C7 !important;
	color: #fff !important;
	font-size: 14px !important;
	text-align:left !important;
}
.cv_right_oth_activity_header
{
	background: #1f1645;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	padding-bottom: 0px;
	letter-spacing: 3px;	
}
.cv_right_oth_activity_header_line
{
	border: 2px #fff solid;
	margin-top: 0;
}
.cv_right_oth_activity_info_content
{
	background: #1f1645;
	color: #fff;
	font-size: 14px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
}
.cv_right_oth_activity_info_content span
{
	color: #fff !important;
	font-size: 14px !important;
	text-align:left !important;
}
.cv_right_teaching_header
{
	background: #333;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	padding-bottom: 10px;
}
.cv_right_teaching_header_line
{
	border: 2px #fff solid;
	margin-top: 0;
}
.cv_right_teaching_info_content
{
	background: #333;
	color: #fff;
	font-size: 14px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
}
.cv_right_teaching_info_content span
{
	background: #333 !important;
	color: #fff !important;
	font-size: 14px !important;
	text-align:left !important;
}
.cv_right_external_header
{
	background: #888;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	padding-bottom: 0px;
	letter-spacing: 3px;	
}
.cv_right_external_header_line
{
	border: 2px #fff solid;
	margin-top: 0;
}
.cv_right_external_info_content
{
	background: #888;
	color: #fff;
	font-size: 14px;
	padding-top: 7px;
	padding-bottom: 7px;	
	text-align:left;
}
.cv_right_external_info_content span
{
	background: #888 !important;
	color: #fff !important;
	font-size: 14px !important;
	text-align:left !important;
}
.cv_right_selected_kt_header
{
	background: #ebd5d5;
	color: #444;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;	
}
.cv_right_selected_kt_content
{
	background: #e9d2d2;
	color: #000;
	font-size:14px;
}

.cv_style2_right_header
{
	background: #026539;
	border-radius: 8px;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;	
}
.cv_style2_right_content_title
{
	color: #444;
	font-size:18px;
	font-weight:400;
}
.cv_style2_right_content
{
	background: #fff;
	color: #000;
	font-size:14px;
}
.cv_style2_right_content_desc
{
	color: #666;
	font-size:12px;
	font-weight:400;
}

.cv_right_job_header
{
	background: #58553e;
	color: #fff;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;	
}

.cv_right_job_header_line
{
	border: 2px #c5b42f solid;
	margin-top: 0;
}

.cv_right_job_content
{
	background: #58553e;
	color: #fff;
	font-size:14px;
}

.cv_right_job_content_title
{
	color: #c0c0c0;
	font-size:16px;
	font-weight:700;
}

.cv_right_job_content_desc
{
	color: #fff;
	font-size:12px;
	font-weight:400;
}

.cv_right_selected_output_header
{
	background: #F0F0F0;
	color: #444;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;	
}

.cv_right_selected_output_header_line
{
	border: 2px #666 solid;
	margin-top: 0;
}

.cv_right_selected_output_content
{
	background: #F0F0F0;
	color: #000;
	font-size:14px;
}

.cv_right_selected_output_content_title
{
	color: #666;
	font-size:16px;
	font-weight:700;
}

.cv_right_selected_output_content_desc
{
	color: #666;
	font-size:12px;
	font-weight:400;
}

.cv_right_research_project_header
{
	background: #FFFFE6;
	color: #424200;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;
}

.cv_right_research_project_header_line
{
	border: 2px #424200 solid;
	margin-top: 0;
}

.cv_right_research_project_content_title
{
	color: #424200;
	font-size:18px;
	font-weight:400;
	margin-bottom: 5px;	
	display:inline-block;
	
}

.cv_right_research_project_content
{
	background: #FFFFE6;
	color: #545454;
	font-size:12px;
	margin-bottom: 5px;	
	display:inline-block;
		
}

.cv_right_research_project_content_desc
{
	color: #6a6a00;
	font-size:12px;
	font-weight:400;
	font-style: italic;
}

.cv_right_award_header
{
	background: #d8d8d8;
	color: #444;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;
	
}

.cv_right_award_header_line
{
	border: 2px #666 solid;
	margin-top: 0;
}

.cv_right_award_content
{
	font-size:12px;
	background: #d8d8d8;
	color: #666;
}

.cv_right_award_content_title
{
	color: #444;
	font-size:18px;
	font-weight:400;
}

.cv_right_award_content_desc
{
	color: #6a6a00;
	font-size:12px;
	font-weight:400;
	font-style: italic;
}

.cv_right_patent_header
{
	background: #e8ffb0;
	color: #295a42;
	font-family: 'Alegreya SC', Arial;
	font-size: 22px;
	text-align:left;
	letter-spacing: 3px;
}

.cv_right_patent_header_line
{
	border: 2px #295a42 solid;
	margin-top: 0;	
}

.cv_right_patent_content
{
	background: #e8ffb0;
	font-size:12px;
	color: #295a42;
	
}

.cv_right_patent_content_title
{
	color: #295a42;
	font-size:18px;
	font-weight:400;
}

.cv_right_patent_content_desc
{
	color: #295a42;
	font-size:12px;
	font-weight:700;
	font-style: italic;
}

.cv_top_contact
{
	color:#000;
	font-size:12px;
	font-weight: 400;
	margin-left: 20px;
	padding: 5px 0px 20px 0px;
}
.cv_style2_top_contact
{
	color:#000;
	font-size:12px;
	font-weight: 400;
	margin-left: 4px;
	padding: 5px 0px 20px 0px;
}
.eduhk_logo
{
	text-align:right;
}

.eduhk_logo_left
{
	text-align:left;
}

.top_separate_line
{
	background:orange;
	padding: 7px 0px 7px 0px;
}

@media all and (max-width: 1400px)
{
	.container-fluid
	{
		padding: 0px 20px 0px 20px;
	}		
	.cv_name
	{
		font-size:34px;
		margin-left: 0px;
		
	}
	.cv_style2_name
	{
		font-size:30px;
		margin-left: 0px;
		
	}
	.cv_chi_name
	{
		font-size:31px;
	}	
	.cv_style2_chi_name
	{
		font-size:28px;
	}	
	.cv_post
	{
		color:#fff;
		font-weight: 400;
		margin-left: 10px;
		padding: 3px 0px 3px 0px;
	}
	.cv_post_desc
	{
		color:#D9BE55;
		font-weight: 400;
		margin-left: 20px;
		padding: 3px 0px 3px 0px;

	}	
	.cv_top_contact
	{
		color:#000;
		font-weight: 400;
		margin-left: 10px;
		padding: 5px 0px 20px 0px;
	}
	.cv_left_contact_info_title
	{	
		font-size: 15px;
	}
	
	.cv_left_contact_info_content
	{	
		font-size: 9px;
	}
	.cv_left_contact_info_icon
	{
		font-size: 14px;
		padding: 5px 5px 0px 0px;
	}
	
	.cv_left_research_header
	{
		font-size: 18px;
	}


	
}

@media print {
	 * {-webkit-print-color-adjust:exact;}
	.container-fluid
	{
		padding: 0px;
	}
	a { text-decoration: none; }
}