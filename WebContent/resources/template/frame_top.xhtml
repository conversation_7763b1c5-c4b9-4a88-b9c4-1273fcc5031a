<ui:component xmlns="http://www.w3.org/1999/xhtml" 
			  xmlns:f="http://java.sun.com/jsf/core"
			  xmlns:ui="http://java.sun.com/jsf/facelets" 
			  xmlns:h="http://java.sun.com/jsf/html"
			  xmlns:p="http://primefaces.org/ui">
	
	<h:form id="topForm">
		<p:idleMonitor timeout="#{sysParamView.getValue('SYS_TIMEOUT')}" onidle="PF('idleDialog').show();"/>
			<p:confirmDialog id="confirmDialog" message="Sorry, your session has expired." header="Session Timeout" severity="alert" widgetVar="idleDialog">
			<p:commandButton id="confirm" value="Ok" 	oncomplete="location.reload();"/>
		</p:confirmDialog>
	<p:panel styleClass="banner" style="border-top: 1px solid #dee2e6 !important;">
		<table>
			<tr>
				<td width="1">
					<div class="banner-eduhk-logo after-space">
						<h:graphicImage value="/resources/image/logo_eduhk.png"/>
					</div>
				</td>
				<td width="99%" align="center">
					<a href="#{request.contextPath}/user/index.xhtml" style="text-decoration: none;">
						<div class="title">
							<span class="name">#{sysParamView.getValue('SYS_TITLE')}</span> <span style="font-size: 70%;">v4.1</span>
						</div>
					</a>						
				</td>
 				
				<td width="1">
					<p:commandLink global="false"
								   rendered="#{userSessionView.login}">
						<h:panelGroup id="menuLink">
							<i class="fas fa-bars fa-3x" style="color: #186ba0; padding-right:5pt;"></i>
						</h:panelGroup>
					</p:commandLink>
					<p:menu model="#{funcMenuView.userMenuModel}" 
							overlay="true" trigger="menuLink" my="left top" at="left bottom"
							rendered="#{userSessionView.login}"/>
				</td>
			</tr>
		</table> 

	</p:panel>
	</h:form>
	
	<!-- User information -->
	<h:form id="userInfoForm"> 

		<h:panelGroup layout="block" styleClass="user-info-panel" >
			<h:panelGroup rendered="#{!empty userSessionView.impersonateUserId}">
				<h:panelGroup rendered="#{userSessionView.person != null}">
					#{userSessionView.person.title} #{userSessionView.person.name} (#{userSessionView.currentUserId}) view
				</h:panelGroup> 
				<h:panelGroup rendered="#{userSessionView.person == null}">
					#{userSessionView.currentUserId} view
				</h:panelGroup> 
				( <h:commandLink actionListener="#{impersonationView.deactivateImpersonation()}">Deactivate</h:commandLink> )
			</h:panelGroup>
			
			<h:panelGroup rendered="#{empty userSessionView.impersonateUserId}"  >
				<h:outputText value="Welcome, #{userSessionView.person.name}" rendered="#{userSessionView.person != null}"/>
				<h:outputText value="Welcome (#{userSessionView.currentUserId})" rendered="#{userSessionView.person == null}"/>
			</h:panelGroup>
		
		</h:panelGroup>
		
	</h:form>
	 

</ui:component>