/**
 * Auto save module in FEFOS
 * 
 * <AUTHOR>
 *
*/

(function ($)
{
	
	var formId = null;
	var timer = null;
	
	var elementList = null;
	var previousElementList = null;
	
	var saveInterval = 60000;
	
	
	$.fn.autosave = function(options)
	{
		formId = _replaceId(this.attr('id'));
		
		if (options != null)
		{
			if (options.init != null) init = options.init;
			if (options.load != null) load = options.load;
			if (options.save != null) save = options.save;
			if (options.saveInterval != null) saveInterval = options.saveInterval * 1000;
		}
		
		// Initialization by calling load
		_loadWrapper(); 
		
		return $(this);
	},
	
	// Start the auto save
	start = function()
	{
		timer = setInterval(function() {_saveWrapper()}, saveInterval);
	},
	
	_loadWrapper = function()
	{
		load(elementList);
	},
	
	_saveWrapper = function()
	{
		// Backup the old list
		previousElementList = elementList;
		
		// Create a new list
		elementList = [];
		
		// Find all input elements
		// Use formId to fetch instead of storing the form object
		// Form object will be invalid once the language is changed in front-end
		var items = $("#" + formId).find(":text, :radio, :checkbox, select, textarea"); 
				
		// Retrieve values from the input elements
		items.each
		(
			function(idx, obj)
			{
				var element = $(obj);
				var inputNode = {"id": element.attr("id")};		
				if (element.is(":text") ||
					element.prop('tagName').toLowerCase() == 'select' ||
					element.prop('tagName').toLowerCase() == 'textarea')
				{				
					inputNode["value"] = element.val();
				}
				else if (element.is(":radio") || element.is(":checkbox"))
				{
					inputNode["checked"] = element.is(":checked");
				}
				
				elementList[elementList.length] = inputNode;
			}
		)

		// Save if there is any change.
		var elementJson = (elementList != null) ? JSON.stringify(elementList) : null;
		var previousElementJson = (previousElementList != null) ? JSON.stringify(previousElementList) : null;
		
		//console.info("saveWrapper elementJson="+elementJson);
		//console.info("saveWrapper previousElementJson="+previousElementJson);
		
		if (elementJson != previousElementJson)
		{
			save(elementList);
		}
	},
	
	load = function(elements)
	{
		// implement by the caller
	},
	
	save = function(elements)
	{
		// implement by the caller
	},
	
	populate = function(elements)
	{
		elementList = elements;
		previousElementList = elements;
		
		$(elements).each
		(
			function(idx, obj)
			{
				if (obj != null)
				{
					
					var widget = null;
					var triggerChange = true;

					var isSelectWidget = false;

					// Construct an ID that can be used by jQuery
					var jqId = "#" + _replaceId(obj.id);
					var inputObj = $(jqId);
					
					// Detect the p:selectOneRadio 
					if (obj.id.endsWith("_clone") && inputObj.is("input[type=radio]"))
					{
						if(obj.checked)
						{
							inputObj.parent().parent().children('.ui-radiobutton-box').click();

						}
							
					}
					
					if (obj.checked !== undefined)
					{
						inputObj.prop("checked", obj.checked);
						
					}
					else
					{
						
						//  Detect whether the input is a Primefaces select menu
						if (obj.id.endsWith("_input"))
						{
							var inputId = obj.id.substring(0, obj.id.length - 6);
							widget = PrimeFaces.getWidgetById(inputId);
							
							// Set widget variable to null, as it is not a select menu
							if (widget != null)
							{
								isSelectWidget = (widget.selectValue != undefined && widget.selectValue != null)
							}
						}
						
						if (isSelectWidget)
						{
							// Do NOT use widget.selectValue("")
							// It cannot reset to empty value
							// Set the value and then the label instead
							widget.input.val(obj.value);
							var label = widget.input.find(":selected").text();
							widget.setLabel(label == "" ? "&nbsp;" : label);
							
							//console.info("before: id="+obj.id + ",value="+obj.value + ", selectValue=" + widget.getSelectedValue());
							//inputObj.val(obj.value);
							//widget.selectValue(obj.value);
							//console.info("after: id="+obj.id + ",value="+obj.value + ", selectValue=" + widget.getSelectedValue());
							//triggerChange = false;
						}
						else
						{
							inputObj.val(obj.value);
						}
					}
					
					// Trigger keyup function to update character count of Primefaces TEXTAREA
					if (inputObj.prop("tagName") == "TEXTAREA")
					{
						inputObj.html(obj.value);
						_resizeTextArea(inputObj);
						inputObj.trigger("keyup");
					}

					// Trigger change function of the input component
					if (triggerChange) 
					{
						if (widget != null && widget.triggerChange)
						{
							widget.triggerChange();
						}
						else inputObj.trigger("change");
					}
					
					//if (widget != null && isSelectWidget)
					//console.info("after chg: id="+obj.id + ",value="+obj.value + ", selectValue=" + widget.getSelectedValue());
				}
			}
		);
	},
		
	_replaceId = function(id)
	{
		return id != undefined ? id.replace(new RegExp(":", "g"), "\\:") : id;
	},
	
	_resizeTextArea = function ($textarea)
	{
           var hiddenDiv = $('.hiddendiv').first();
           if (!hiddenDiv.length) {
               hiddenDiv = $('<div class="hiddendiv common"></div>');
               $('body').append(hiddenDiv);
           }
           
           var fontFamily = $textarea.css('font-family');
           var fontSize = $textarea.css('font-size');

           if (fontSize) { hiddenDiv.css('font-size', fontSize); }
           if (fontFamily) { hiddenDiv.css('font-family', fontFamily); }

           if ($textarea.attr('wrap') === "off") {
               hiddenDiv.css('overflow-wrap', "normal")
                   .css('white-space', "pre");
           }

           hiddenDiv.text($textarea.val() + '\n');
           var content = hiddenDiv.html().replace(/\n/g, '<br>');
           hiddenDiv.html(content);
           hiddenDiv.css('display', "none");

           // When textarea is hidden, width goes crazy.
           // Approximate with half of window size

           if ($textarea.is(':visible')) {
               hiddenDiv.css('width', $textarea.width());
           }
           else {
               hiddenDiv.css('width', $(window).width()/2);
           }

           $textarea.css('height', hiddenDiv.height());
    }

})(jQuery);