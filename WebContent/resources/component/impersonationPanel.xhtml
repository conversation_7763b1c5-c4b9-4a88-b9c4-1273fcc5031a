<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

<cc:interface>
	<cc:attribute name="viewBean" />
</cc:interface>

<cc:implementation>

	<h:panelGroup styleClass="noprint"
				  rendered="#{cc.attrs.viewBean.impersonateUserId != null and cc.attrs.viewBean.impersonateUserId != cc.attrs.viewBean.loginUserId}"> 
		<table class="impersonate-panel">
		  <tr>
			<td>
				Impersonate User: #{cc.attrs.viewBean.impersonateUserId}
			</td>
		  </tr>
		</table>
	</h:panelGroup>
			
</cc:implementation>

</html>
