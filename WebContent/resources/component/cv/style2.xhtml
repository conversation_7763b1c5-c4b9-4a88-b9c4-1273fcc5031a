<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<div class="container-fluid">
		<!-- top basic info -->
			<div class="row eduhk_logo_left">
				<div class="col-sm-12 col-md-12 col-lg-12">
					<h:graphicImage value="/resources/image/logo_eduhk.png"/>
				</div>
			</div>
			<div class="row">
				<div class="d-none d-md-block hidden-sm col-md-2 col-lg-2">
				<!-- left content -->
					<!-- contact -->
					<div class="row">
						<div class="col-sm-12 cv_style2_left_contact_header">Contact</div>
					</div>	
						<div class="row cv_style2_left_contact_first">
	
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="ORCiD" rendered="#{cvView.sInfo.orcid != null}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.orcid}" rendered="#{cvView.sInfo.orcid != null}" escape="false"></h:outputText></div>
								</div>
							</div>
							
						</div>
						<div class="row cv_style2_left_contact_middle">
				
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Phone" rendered="#{cvView.iUserInfo.phone != ''}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.phone}" rendered="#{cvView.iUserInfo.phone != ''}" escape="false"></h:outputText></div>
								</div>
							</div>
							
						</div>
						<div class="row cv_style2_left_contact_middle">
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Fax" rendered="#{cvView.iUserInfo.fax != ''}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.fax}" rendered="#{cvView.iUserInfo.fax != ''}" escape="false"></h:outputText></div>
								</div>
							</div>
								
						</div>
						<div class="row cv_style2_left_contact_middle">
	
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Email" rendered="#{cvView.iUserInfo.email != ''}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.email}" rendered="#{cvView.iUserInfo.email != ''}" escape="false"></h:outputText></div>
								</div>
							</div>
							
						</div>
						<div class="row cv_style2_left_contact_middle">
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Address" ></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="10 Lo Ping Road, Tai Po, New Territories, Hong Kong"  escape="false"></h:outputText></div>
								</div>
							</div>
								
						</div>
						<div class="row cv_style2_left_contact_middle">
						
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText  value="Website" rendered="#{cvView.websiteLink ne 'N/A'}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputLink class="cv_style2_website_url" value="#{cvView.websiteLink}" rendered="#{cvView.websiteLink ne 'N/A'}"><h:outputText value="#{cvView.websiteLink}" rendered="#{cvView.websiteLink ne 'N/A'}" escape="false"></h:outputText></h:outputLink></div>
								</div>
							</div>
						</div>
						<div class="row cv_style2_left_contact_middle">
	
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Scopus ID" rendered="#{cvView.sInfo.scopusId != null}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.scopusId}" rendered="#{cvView.sInfo.scopusId != null}" escape="false"></h:outputText></div>
								</div>
							</div>
								
						</div>
						
						
						<div class="row cv_style2_left_contact_middle">
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="SDGs" rendered="#{cvView.sInfo.sdg_code != null}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.sdg_info}" rendered="#{cvView.sInfo.sdg_code != null}" escape="false">
									</h:outputText></div>
								</div>
							</div>
								
						</div>
						
						
						
						
						<div class="row cv_style2_left_contact_last">
	
							<div class="col-sm-10">
								<div class="row">
									<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="ResearcherID" rendered="#{cvView.sInfo.researcherid != null}"></h:outputText></div>
									<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.researcherid}" rendered="#{cvView.sInfo.researcherid != null}" escape="false"></h:outputText></div>
								</div>
							</div>
									
						</div>
						<ui:fragment rendered="#{!empty cvView.sInfo.research_interest}">
							<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_header">Research Interests</div>
							</div>
							<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.research_interest}"  escape="false"></h:outputText></div>
							</div>
						</ui:fragment>
						<ui:fragment rendered="#{!empty cvView.sInfo.teaching_interest}">
							<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? 'content-hide' : ''}">
									<div class="col-sm-12 cv_style2_left_info_header">Teaching Interests</div>
							</div>			
							<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.teaching_interest}" escape="false"></h:outputText></div>
							</div>		
						</ui:fragment>	
						<ui:fragment rendered="#{!empty cvView.sInfo.ext_appt}">
							<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_header">External Appointments</div>
							</div>			
							<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.ext_appt}" escape="false"></h:outputText></div>
							</div>		
						</ui:fragment>
						<ui:fragment rendered="#{!empty cvView.sInfo.oth_activity}">
							<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_header">Other Activities</div>
							</div>			
							<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.oth_activity}" escape="false"></h:outputText></div>
							</div>		
						</ui:fragment>				
				</div>
				<div class="col-sm-14 col-md-10 col-lg-10">
					<div class="row">
						<div class="col-sm-14 col-md-10 col-lg-10" style = "padding-right:0px;">
							<div class="row cv_style2_name">
								<div class="col-sm-11">
									#{cvView.iUserInfo.title} #{cvView.iUserInfo.fullname}
									<br/>
									<span class="cv_style2_chi_name">
									#{cvView.iUserInfo.chinesename}
									<h:outputText value="博士" rendered="#{cvView.iUserInfo.title == 'Dr'}"></h:outputText>
									<h:outputText value="教授" rendered="#{cvView.iUserInfo.title == 'Prof'}"></h:outputText>
									<h:outputText value="先生" rendered="#{cvView.iUserInfo.title == 'Mr'}"></h:outputText>
									<h:outputText value="女士" rendered="#{cvView.iUserInfo.title == 'Mrs' or cvView.iUserInfo.title == 'Ms' or cvView.iUserInfo.title == 'Miss'}"></h:outputText>
									</span>
								</div>
							</div>
							<div class="row cv_post cv_style2_post_table">
								<p:dataTable var="i" value="#{cvView.iUserInfoList}" tableStyle="width:auto">
									<p:column>
								       <div class="col-sm-14 cv_style2_post">#{i.post}</div>
							        </p:column>
							        <p:column>
										<div class="col-sm-14 cv_style2_post_desc">#{i.deptdesc}</div>
							        </p:column>
								</p:dataTable>
							</div>
							
							<!--  Removed -->
							<!--  
							<div class="row cv_style2_top_contact">
								<div class="col-sm-12 col-lg-3"><h:outputText value="Phone No: #{cvView.iUserInfo.phone}" rendered="#{cvView.iUserInfo.phone != ''}" escape="false"></h:outputText></div>
								<div class="col-sm-12 col-lg-3"><h:outputText value="Fax No: #{cvView.iUserInfo.fax}" rendered="#{cvView.iUserInfo.fax != ''}" escape="false"></h:outputText></div>
								<div class="col-sm-12 col-lg-6"><h:outputText value="Email: #{cvView.iUserInfo.email}" rendered="#{cvView.iUserInfo.email != ''}" escape="false"></h:outputText></div>
							</div>
							-->
						</div>
						<div class="col-sm-3 col-md-2 col-lg-2 cv_photo align-self-end" >
							<ui:fragment rendered="#{!empty cvView.sInfo.photoFile}"><img src="data:image/jpg;base64,#{cvView.sInfo.photoFile}" height="90%" width="90%"/></ui:fragment>
						</div>
					</div>
					<div class="row" style="margin-left: 0px;">
						<!-- right content -->
						<!-- left content (mobile version)-->
						<div class="col-sm-12 d-md-none d-lg-none d-xl-none">
							<!-- contact -->
							<div class="row">
								<div class="col-sm-12 cv_style2_left_contact_header">Contact</div>
							</div>	
								<div class="row cv_style2_left_contact_first">
			
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="ORCiD" rendered="#{cvView.sInfo.orcid != null}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.orcid}" rendered="#{cvView.sInfo.orcid != null}" escape="false"></h:outputText></div>
										</div>
									</div>
									
								</div>
								<div class="row cv_style2_left_contact_middle">
						
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Phone" rendered="#{cvView.iUserInfo.phone != ''}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.phone}" rendered="#{cvView.iUserInfo.phone != ''}" escape="false"></h:outputText></div>
										</div>
									</div>
									
								</div>
								<div class="row cv_style2_left_contact_middle">
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Fax" rendered="#{cvView.iUserInfo.fax != ''}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.fax}" rendered="#{cvView.iUserInfo.fax != ''}" escape="false"></h:outputText></div>
										</div>
									</div>
										
								</div>
								<div class="row cv_style2_left_contact_middle">
			
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Email" rendered="#{cvView.iUserInfo.email != ''}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.email}" rendered="#{cvView.iUserInfo.email != ''}" escape="false"></h:outputText></div>
										</div>
									</div>
									
								</div>
								<div class="row cv_style2_left_contact_middle">
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Address" ></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="10 Lo Ping Road, Tai Po, New Territories, Hong Kong"  escape="false"></h:outputText></div>
										</div>
									</div>
										
								</div>
								<div class="row cv_style2_left_contact_middle">
								
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText  value="Website" rendered="#{cvView.websiteLink ne 'N/A'}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputLink class="cv_style2_website_url" value="#{cvView.websiteLink}" rendered="#{cvView.websiteLink ne 'N/A'}"><h:outputText value="#{cvView.websiteLink}" rendered="#{cvView.websiteLink ne 'N/A'}" escape="false"></h:outputText></h:outputLink></div>
										</div>
									</div>
								</div>
								<div class="row cv_style2_left_contact_middle">
			
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="Scopus ID" rendered="#{cvView.sInfo.scopusId != null}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.scopusId}" rendered="#{cvView.sInfo.scopusId != null}" escape="false"></h:outputText></div>
										</div>
									</div>
										
								</div>
								<div class="row cv_style2_left_contact_last">
			
									<div class="col-sm-10">
										<div class="row">
											<div class="col-sm-12 cv_style2_left_contact_info_title"><h:outputText value="ResearcherID" rendered="#{cvView.sInfo.researcherid != null}"></h:outputText></div>
											<div class="col-sm-12 cv_style2_left_contact_info_content"><h:outputText value="#{cvView.sInfo.researcherid}" rendered="#{cvView.sInfo.researcherid != null}" escape="false"></h:outputText></div>
										</div>
									</div>
											
								</div>
								<ui:fragment rendered="#{!empty cvView.sInfo.research_interest}">
									<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_header">Research Interests</div>
									</div>
									<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.research_interest}"  escape="false"></h:outputText></div>
									</div>
								</ui:fragment>
								<ui:fragment rendered="#{!empty cvView.sInfo.teaching_interest}">
									<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? 'content-hide' : ''}">
											<div class="col-sm-12 cv_style2_left_info_header">Teaching Interests</div>
									</div>			
									<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.teaching_interest}" escape="false"></h:outputText></div>
									</div>		
								</ui:fragment>	
								<ui:fragment rendered="#{!empty cvView.sInfo.ext_appt}">
									<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_header">External Appointments</div>
									</div>			
									<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.ext_appt}" escape="false"></h:outputText></div>
									</div>		
								</ui:fragment>
								<ui:fragment rendered="#{!empty cvView.sInfo.oth_activity}">
									<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_header">Other Activities</div>
									</div>			
									<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? 'content-hide' : ''}">
										<div class="col-sm-12 cv_style2_left_info_content"><h:outputText value="#{cvView.sInfo.oth_activity}" escape="false"></h:outputText></div>
									</div>		
								</ui:fragment>				
						</div>
						<div class="col-sm-12 col-md-12 col-lg-12">
							<!-- Professional Profile -->
							<ui:fragment rendered="#{!empty cvView.sInfo.profile}">
								<div class="row">
									<div class="col-sm-12 cv_style2_right_profile_header">Personal Profile</div>
								</div>		
								<div class="row">
									<div class="col-sm-12 cv_style2_right_profile_header_2"><p:separator class="cv_style2_right_profile_header_line"/></div>
								</div>	
								<div class="row">
									<div class="col-sm-12 cv_style2_right_content_desc"><h:outputText value="#{cvView.sInfo.profile}" escape="false"></h:outputText></div>
								</div>	
							</ui:fragment>
							<!-- Research Interest (display if content too long)-->
							<ui:fragment rendered="#{!empty cvView.sInfo.research_interest}">
								<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header">Research Interests</div>
								</div>		
								<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header"><p:separator class="cv_style2_right_profile_header_line"/></div>
								</div>	
								<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_content_desc"><h:outputText value="#{cvView.sInfo.research_interest}" escape="false"></h:outputText></div>
								</div>	
							</ui:fragment>
							<!-- Teaching Interest (display if content too long)-->
							<ui:fragment rendered="#{!empty cvView.sInfo.teaching_interest}">
								<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header">Teaching Interests</div>
								</div>		
								<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header"><p:separator class="cv_style2_right_profile_header_line"/></div>
								</div>	
								<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_content_desc"><h:outputText value="#{cvView.sInfo.teaching_interest}" escape="false"></h:outputText></div>
								</div>	
							</ui:fragment>
							<!-- External Appointment (display if content too long)-->
							<ui:fragment rendered="#{!empty cvView.sInfo.ext_appt}">
								<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header">External Appointments</div>
								</div>		
								<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header"><p:separator class="cv_style2_right_profile_header_line"/></div>
								</div>	
								<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_content_desc"><h:outputText value="#{cvView.sInfo.ext_appt}" escape="false"></h:outputText></div>
								</div>	
							</ui:fragment>
							<!-- Other Activities (display if content too long)-->
							<ui:fragment rendered="#{!empty cvView.sInfo.oth_activity}">
								<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header">Other Activities</div>
								</div>		
								<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_profile_header"><p:separator class="cv_style2_right_profile_header_line"/></div>
								</div>	
								<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? '' : 'content-hide'}">
									<div class="col-sm-12 cv_style2_right_content_desc"><h:outputText value="#{cvView.sInfo.oth_activity}" escape="false"></h:outputText></div>
								</div>	
							</ui:fragment>
							<!-- Research information -->
							<c:forEach  items="#{cvView.displayOrderList}" var="item">
							    <ui:include src="#{item}"/>
							</c:forEach>
						</div>
					</div>
				</div>
				
			</div>

			<div class="row top_separate_line"></div>
			
		</div>
		
	</ui:composition>

</html>
