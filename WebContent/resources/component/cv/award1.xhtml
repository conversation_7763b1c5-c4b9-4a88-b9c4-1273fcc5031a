<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{!empty cvView.awardList}">
			<div class="row">
				<div class="col-sm-12 cv_right_award_header">Prizes and awards</div>
			</div>		
			<div class="row">
				<div class="col-sm-12 cv_right_award_header"><p:separator class="cv_right_award_header_line"/></div>
			</div>	
			<div class="row">
				<div class="col-sm-12 cv_right_award_content">
						<p:dataTable var="a" value="#{cvView.awardList}">
					       <p:column>
						       <span class="cv_right_award_content_title">#{a.awardName}</span><br/>
						       <span class="cv_right_award_content">#{a.fullDesc}</span><br/>
						       <span class="cv_right_award_content">Date of receipt: #{a.awardDay}/#{a.awardMonth}/#{a.awardYear}, Conferred by: #{a.orgName}</span><br/>&nbsp;
					       </p:column>
					   </p:dataTable>
				</div>
			</div>
		</ui:fragment>
	</ui:composition>

</html>
