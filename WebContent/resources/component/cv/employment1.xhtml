<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition >		
		<ui:fragment rendered="#{!empty cvView.employmentHistList_edit}">
			<div class="row">
				<div class="col-sm-12 cv_right_job_header">Career Overview</div>
			</div>		
			<div class="row">
				<div class="col-sm-12 cv_right_job_header"><p:separator class="cv_right_job_header_line"/></div>
			</div>	
			<div class="row">
				<div class="col-sm-12 cv_right_job_content">
					<p:dataTable var="data" value="#{cvView.employmentHistList_edit}" style="background-color: transparent !important;">
						<p:column>
								<div class="cv_right_employment_hist_blk" >
					       			<b><h:outputText class="cv_right_job_content_title" value="#{data.company}" /></b><br/>
					       			<h:outputText class="cv_right_job_content_desc" value="#{data.job_title}" ></h:outputText><br/>
					       			<h:outputText class="cv_right_job_content_desc" value="#{data.job_details}" escape="false" rendered="#{!empty data.job_details}"/>
						            <div>
							            <h:outputText class="cv_right_job_content_desc" value="Date : "/>
							            <h:outputText class="cv_right_job_content_desc" value="#{data.from_date}">
													<f:convertDateTime dateStyle="full" pattern="MM/yyyy" timeZone="Hongkong"/>
										</h:outputText>
										<h:outputText class="cv_right_job_content_desc" value="-"	   rendered="#{!empty data.to_date and data.is_current != 'Y'}" />
										<h:outputText class="cv_right_job_content_desc" value="- Present" rendered="#{data.is_current == 'Y'}"/>	
										<h:outputText class="cv_right_job_content_desc" value="#{data.to_date}" rendered="#{data.is_current == 'N'}">
													<f:convertDateTime dateStyle="full" pattern="MM/yyyy" timeZone="Hongkong"/>
										</h:outputText><br/>
								  	</div>
								</div>
						 </p:column>
					</p:dataTable>
				</div>
			</div>		
		</ui:fragment>			
		
	</ui:composition>

</html>
