<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
				<ui:fragment rendered="#{!empty cvView.projectList}">
						<div class="row">
							<div class="col-sm-12 cv_style2_right_header">Projects</div>
						</div>		
						<div class="row">
							<div class="col-sm-12 cv_style2_right_content">
								<p:dataTable var="p" value="#{cvView.projectList}">
									<p:column>
									
									   <p:panel class="cv_style2_right_content_title" header="#{p.projectHeader_p.title_1} #{p.projectHeader_p.title_2} #{p.projectHeader_p.title_3} #{p.projectHeader_p.title_4}">
								   			<div style="padding:10px">
								   			<h:outputText class="cv_style2_right_content_desc" value=" #{p.projectHeader_p.project_summary}" escape="false"></h:outputText>
							       			<h:outputText class="cv_style2_right_content_desc" value=" #{p.projectHeader_p.project_summary_2}" escape="false"></h:outputText>		
							       			<br/><br/>
							       			<span class="cv_style2_right_content_desc">Project Start Year: #{p.projectHeader_p.from_year}, Principal Investigator(s): #{cvView.getPrincipalInvestigators(p.pk.project_no, cvView.iUserInfo.staff_number)}</span><br/>
							        		<h:outputText class="cv_style2_right_content_desc" value="SDGs Information: #{p.projectHeader_p.sdg_info}"  rendered="#{p.projectHeader_p.sdg_code != null}" >
							        			</h:outputText><br/>&nbsp;
							        		
							        		</div>
							        </p:panel>
								        <br/>
							        </p:column>
							       
								</p:dataTable>
							</div>
						</div>
					</ui:fragment>
	</ui:composition>

</html>
