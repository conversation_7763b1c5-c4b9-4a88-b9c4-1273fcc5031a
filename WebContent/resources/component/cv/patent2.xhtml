<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{!empty cvView.patentList}">
			<div class="row">
				<div class="col-sm-12 cv_style2_right_header">Patents</div>
			</div>		
			<div class="row">
				<div class="col-sm-12 cv_style2_right_content">
						<p:dataTable var="p" value="#{cvView.patentList}">
					       <p:column>
						       <span class="cv_style2_right_content_title">#{p.patentName}</span><br/>
						       <span class="cv_style2_right_content_desc">#{p.shortDesc}</span>
						       <span class="cv_style2_right_content_desc"> - #{p.patentGranted}/#{p.patentType}</span><br/>&nbsp;
					       </p:column>
					   </p:dataTable>
				</div>
			</div>				
		</ui:fragment>	
	</ui:composition>

</html>
