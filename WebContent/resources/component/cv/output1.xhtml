<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{!empty cvView.outputList}">
			<div class="row">
				<div class="col-sm-12 cv_right_selected_output_header">Research Outputs</div>
			</div>		
			<div class="row">
				<div class="col-sm-12 cv_right_selected_output_header"><p:separator class="cv_right_selected_output_header_line"/></div>
			</div>	
			<div class="row">
				<div class="col-sm-12 cv_right_selected_output_content">
					<p:dataTable var="p" value="#{cvView.selectedOutputTypeList}" style="background-color: transparent !important;">
						<p:column>
					       <h:outputText class="cv_right_selected_output_content_desc" value="#{cvView.getSelectedOutputDesc(p)}" escape="false"></h:outputText><br/>
				        </p:column>
					</p:dataTable>
				</div>
			</div>		
		</ui:fragment>			
		
	</ui:composition>

</html>
