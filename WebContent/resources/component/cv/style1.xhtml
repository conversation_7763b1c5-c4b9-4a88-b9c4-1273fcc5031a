<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<div class="container-fluid">
		<!-- top basic info -->
			<div class="row eduhk_logo">
				<div class="col-sm-12 d-block d-sm-none">
					<h:graphicImage value="/resources/image/logo_eduhk.png"/>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-4 col-md-3 col-lg-2 cv_photo align-self-end">
				<ui:fragment rendered="#{!empty cvView.sInfo.photoFile}"><img src="data:image/jpg;base64,#{cvView.sInfo.photoFile}" height="90%" width="90%"/></ui:fragment>
				</div>
				<div class="col-sm-8 col-md-9 col-lg-10">
					<div class="row eduhk_logo">
						<div class="col-md-12 d-none d-sm-block">
							<h:graphicImage value="/resources/image/logo_eduhk.png"/>
						</div>
					</div>
						<div class="row cv_name">
							<div class="col-sm-12">
								#{cvView.iUserInfo.title} #{cvView.iUserInfo.fullname}&nbsp;&nbsp;&nbsp;
								<span class="cv_chi_name">
									#{cvView.iUserInfo.chinesename}
									<h:outputText value="博士" rendered="#{cvView.iUserInfo.title == 'Dr'}"></h:outputText>
									<h:outputText value="教授" rendered="#{cvView.iUserInfo.title == 'Prof'}"></h:outputText>
									<h:outputText value="先生" rendered="#{cvView.iUserInfo.title == 'Mr'}"></h:outputText>
									<h:outputText value="女士" rendered="#{cvView.iUserInfo.title == 'Mrs' or cvView.iUserInfo.title == 'Ms' or cvView.iUserInfo.title == 'Miss'}"></h:outputText>
								</span>
							</div>
						</div>
						<div class="row cv_post cv_post_table">
							<p:dataTable var="i" value="#{cvView.iUserInfoList}" tableStyle="width:auto">
								<p:column>
							       <div class="col-sm-12 cv_post">#{i.post}</div>
						        </p:column>
						        <p:column>
									<div class="col-sm-12 cv_post_desc">#{i.deptdesc}</div>
						        </p:column>
							</p:dataTable>
						</div>
						<!-- Removed -->
						<!--  <div class="row cv_top_contact">
							<div class="col-sm-12 col-lg-3"><h:outputText value="Phone No: #{cvView.iUserInfo.phone}" rendered="#{cvView.iUserInfo.phone != ''}" escape="false"></h:outputText></div>
							<div class="col-sm-12 col-lg-3"><h:outputText value="Fax No: #{cvView.iUserInfo.fax}" rendered="#{cvView.iUserInfo.fax != ''}" escape="false"></h:outputText></div>
							<div class="col-sm-12 col-lg-6"><h:outputText value="Email: #{cvView.iUserInfo.email}" rendered="#{cvView.iUserInfo.email != ''}" escape="false"></h:outputText></div>
					
						</div>					
						-->

					
					
				</div>
			</div>
			<div class="row top_separate_line"></div>
			<div class="row cv_content">
				<!-- left content -->
				<div class="col-sm-4 col-md-3 col-lg-2">
					<!-- contact -->
					<div class="row cv_left_contact">
						<div class="col-sm-12 cv_left_contact_header">Contact</div>
					</div>	
					<div class="row cv_left_contact">
						<div class="col-sm-12 cv_left_contact_header_line"></div>
					</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="ORCiD" rendered="#{cvView.sInfo.orcid != null}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.sInfo.orcid}" rendered="#{cvView.sInfo.orcid != null}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.sInfo.orcid != null}">
									<span class="fa-stack">
									  <i class="fab fa-orcid fa-stack-2x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="Phone" rendered="#{cvView.iUserInfo.phone != ''}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.phone}" rendered="#{cvView.iUserInfo.phone != ''}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.iUserInfo.phone != ''}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="fas fa-phone fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="Fax" rendered="#{cvView.iUserInfo.fax != ''}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.fax}" rendered="#{cvView.iUserInfo.fax != ''}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.iUserInfo.fax != ''}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="fas fa-fax fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="Email" rendered="#{cvView.iUserInfo.email != ''}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.iUserInfo.email}" rendered="#{cvView.iUserInfo.email != ''}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.iUserInfo.email != ''}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="fas fa-envelope fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="Address" ></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="10 Lo Ping Road, Tai Po, New Territories, Hong Kong"  escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment>
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="fas fa-map-marker-alt fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText  value="Website" rendered="#{cvView.websiteLink ne 'N/A'}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputLink class="cv_website_url" value="#{cvView.websiteLink}" rendered="#{cvView.websiteLink ne 'N/A'}"><h:outputText value="#{cvView.websiteLink}" rendered="#{cvView.websiteLink ne 'N/A'}" escape="false"></h:outputText></h:outputLink></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.websiteLink ne 'N/A'}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="fas fa-mouse fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="Scopus ID" rendered="#{cvView.sInfo.scopusId != null}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.sInfo.scopusId}" rendered="#{cvView.sInfo.scopusId != null}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.sInfo.scopusId != null}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="far fa-id-badge fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="SDGs" 
										rendered="#{cvView.sInfo.sdg_code != null}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.sInfo.sdg_info}" 
										rendered="#{cvView.sInfo.sdg_code != null}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.sInfo.sdg_code != null}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="fa fa-globe fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>	
						</div>
						
						<div class="row cv_left_contact">
							<div class="col-sm-9">
								<div class="row">
									<div class="col-sm-12 cv_left_contact_info_title"><h:outputText value="ResearcherID" rendered="#{cvView.sInfo.researcherid != null}"></h:outputText></div>
									<div class="col-sm-12 cv_left_contact_info_content"><h:outputText value="#{cvView.sInfo.researcherid}" rendered="#{cvView.sInfo.researcherid != null}" escape="false"></h:outputText></div>
								</div>
							</div>
							<div class="col-sm-3 d-none d-sm-block cv_left_contact_info_icon">
								<ui:fragment rendered="#{cvView.sInfo.researcherid != null}">
									<span class="fa-stack">
									  <i class="far fa-circle fa-stack-2x"></i>
									  <i class="far fa-id-badge fa-stack-1x"></i>
									</span>
								</ui:fragment>
							</div>		
						</div>
						<ui:fragment rendered="#{!empty cvView.sInfo.research_interest}">
							<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_research_header">Research Interests</div>
							</div>
							<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_research_info_content"><h:outputText value="#{cvView.sInfo.research_interest}"  escape="false"></h:outputText></div>
							</div>
						</ui:fragment>
						<ui:fragment rendered="#{!empty cvView.sInfo.teaching_interest}">
							<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? 'content-hide' : ''}">
									<div class="col-sm-12 cv_left_teaching_header">Teaching Interests</div>
							</div>			
							<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_teaching_info_content"><h:outputText value="#{cvView.sInfo.teaching_interest}" escape="false"></h:outputText></div>
							</div>		
						</ui:fragment>	
						<ui:fragment rendered="#{!empty cvView.sInfo.ext_appt}">
							<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_external_header">External Appointments</div>
							</div>			
							<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_external_info_content"><h:outputText value="#{cvView.sInfo.ext_appt}" escape="false"></h:outputText></div>
							</div>		
						</ui:fragment>
						<ui:fragment rendered="#{!empty cvView.sInfo.oth_activity}">
							<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_oth_activity_header">Other Activities</div>
							</div>			
							<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? 'content-hide' : ''}">
								<div class="col-sm-12 cv_left_oth_activity_info_content"><h:outputText value="#{cvView.sInfo.oth_activity}" escape="false"></h:outputText></div>
							</div>		
						</ui:fragment>
				</div>
				<!-- right content -->
				<div class="col-sm-8 col-md-9 col-lg-10">
					<!-- Professional Profile -->
					<ui:fragment rendered="#{!empty cvView.sInfo.profile}">
						<div class="row">
							<div class="col-sm-12 cv_right_profile_header">Personal Profile</div>
						</div>		
						<div class="row">
							<div class="col-sm-12 cv_right_profile_header_2"><p:separator class="cv_right_profile_header_line"/></div>
						</div>	
						<div class="row">
							<div class="col-sm-12 cv_right_profile_info_content"><h:outputText value="#{cvView.sInfo.profile}" escape="false"></h:outputText></div>
						</div>	
					</ui:fragment>
					<!-- Research Interest (display if content too long)-->
					<ui:fragment rendered="#{!empty cvView.sInfo.research_interest}">
						<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_research_header">Research Interests</div>
						</div>		
						<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_research_header"><p:separator class="cv_right_research_header_line"/></div>
						</div>	
						<div class="row #{cvView.longContent(cvView.sInfo.research_interest) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_research_info_content"><h:outputText value="#{cvView.sInfo.research_interest}" escape="false"></h:outputText></div>
						</div>	
					</ui:fragment>
					<!-- Teaching Interest (display if content too long)-->
					<ui:fragment rendered="#{!empty cvView.sInfo.teaching_interest}">
						<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_teaching_header">Teaching Interests</div>
						</div>		
						<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_teaching_header"><p:separator class="cv_right_teaching_header_line"/></div>
						</div>	
						<div class="row #{cvView.longContent(cvView.sInfo.teaching_interest) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_teaching_info_content"><h:outputText value="#{cvView.sInfo.teaching_interest}" escape="false"></h:outputText></div>
						</div>	
					</ui:fragment>
					<!-- External Appointment (display if content too long)-->
					<ui:fragment rendered="#{!empty cvView.sInfo.ext_appt}">
						<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_external_header">External Appointments</div>
						</div>		
						<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_external_header"><p:separator class="cv_right_external_header_line"/></div>
						</div>	
						<div class="row #{cvView.longContent(cvView.sInfo.ext_appt) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_external_info_content"><h:outputText value="#{cvView.sInfo.ext_appt}" escape="false"></h:outputText></div>
						</div>	
					</ui:fragment>
					<!-- Other Activities (display if content too long)-->
					<ui:fragment rendered="#{!empty cvView.sInfo.oth_activity}">
						<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_oth_activity_header">Other Activities</div>
						</div>		
						<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_oth_activity_header"><p:separator class="cv_right_oth_activity_header_line"/></div>
						</div>	
						<div class="row #{cvView.longContent(cvView.sInfo.oth_activity) ? '' : 'content-hide'}">
							<div class="col-sm-12 cv_right_oth_activity_info_content"><h:outputText value="#{cvView.sInfo.oth_activity}" escape="false"></h:outputText></div>
						</div>	
					</ui:fragment>
					<!-- Research information -->
					<c:forEach  items="#{cvView.displayOrderList}" var="item">
					    <ui:include src="#{item}"/>
					</c:forEach>
				</div>
			</div>
		</div>
		
	</ui:composition>

</html>
