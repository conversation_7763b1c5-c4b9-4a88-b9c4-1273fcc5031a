<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
<cc:interface>
	<cc:attribute name="importRIPatent" type="hk.eduhk.rich.view.ImportRIPatent" required="true"/>
	<cc:attribute name="update" type="java.lang.String" required="true"/>
</cc:interface>
<cc:implementation>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
      	<p:overlayPanel id="patentPreviewPanel" widgetVar="patentPreviewPanelVar"
								dismissable="false"
								styleClass="supplForm-preview-panel">
					<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
						<table border="0" style="width:100%;">
							<tr>
								<td>
									<h:outputText value="#{cc.attrs.importRIPatent.getPatent().patent_name}" style="white-space: pre-wrap"/>
									
								</td>
								<td style="text-align:right;">
									<p:commandLink oncomplete="PF('patentPreviewPanelVar').hide()">
										<i class="fas fa-times"/>
									</p:commandLink>
								</td>
							</tr>
						</table>
					</h:panelGroup>
					<div class="ui-g input-panel">
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Source ID:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().pk.source_id}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Patent Inventor:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().inventor_name}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Patent Type:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().getPatent_granted_str()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Serial Number of Patent:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().serial_no}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Country:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().getCountry_desc()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Detail Description of Patent:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().full_desc}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Date of Application/Grant:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIPatent.getPatent().patent_day}/#{cc.attrs.importRIPatent.getPatent().patent_month}/#{cc.attrs.importRIPatent.getPatent().patent_year}
						</div>
					</div>
					<h:panelGroup>
						<!-- <h:outputText value="#{cc.attrs.importRIPatent.getPatent().apa_citation}" /> -->
						<table border="0" style="width:100%;">
							<tr>
								<td>
									<p:commandButton styleClass="default-linkButton" value="Ignore" update=":ignoreConfirmForm:ignorePatentConfirm"
													 style="background: grey; border:grey"
													 oncomplete="PF('ignorePatentConfirmWidget').show();"
													 actionListener="#{cc.attrs.importRIPatent.setSelectedIgnorePatent()}">
									</p:commandButton>
								</td>
								<td style="text-align:right;">
									<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('patentPreviewPanelVar').hide()">
									</p:commandButton>
									<p:linkButton styleClass="default-linkButton" value="Import" outcome="/user/managePatent_edit">
										<f:param name="area_code" value="#{cc.attrs.importRIPatent.getPatent().pk.area_code}"/> 
										<f:param name="source_id" value="#{cc.attrs.importRIPatent.getPatent().pk.source_id}"/> 
			               				<f:param name="staff_number" value="#{cc.attrs.importRIPatent.getPatent().pk.staff_number}"/> 
			               				<f:param name="pid" value="#{cc.attrs.importRIPatent.getPatent().pid}"/> 
									</p:linkButton >
								</td>
							</tr>
						</table>
					</h:panelGroup>
								
				</p:overlayPanel>
	        	<p:dataTable id="patentDataTable"
							 value="#{cc.attrs.importRIPatent.getPatentList()}" 
							 var="patent"
							 styleClass="default-dataTable"
							 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
							 reflow="true"
							 paginator="true"
							 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                     rows="30"
		                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
		                 	 tableStyle="table-layout:auto;"
							 >
					
					<p:column width="10%">
						<f:facet name="header">Sources</f:facet>
						<h:outputText value="#{patent.area_description}" />
					</p:column>
					
					<p:column width="10%" >
						<f:facet name="header">ID</f:facet>
						<h:outputText value="#{patent.pk.source_id}" escape="false" />
					</p:column>
					
					<p:column width="60%">
						<f:facet name="header">Patent Name</f:facet>
						<h:outputText value="#{patent.patent_name}" style="white-space: pre-wrap"/>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Actions</f:facet>
						<p:commandButton value="View" update="#{cc.attrs.update}"
										 styleClass="default-linkButton"
										 oncomplete="PF('patentPreviewPanelVar').show()"
										 actionListener="#{cc.attrs.importRIPatent.setSelectedPreview(patent.pk.area_code, patent.pk.source_id, patent.pk.staff_number)}">
						</p:commandButton>
						<p:linkButton value="Import" outcome="/user/managePatent_edit" styleClass="default-linkButton">
							<f:param name="area_code" value="#{patent.pk.area_code}"/> 
							<f:param name="source_id" value="#{patent.pk.source_id}"/> 
               				<f:param name="staff_number" value="#{patent.pk.staff_number}"/> 
               				<f:param name="pid" value="#{patent.pid}"/> 
						</p:linkButton >
						<br/>
						<p:commandButton value="Ignore" update=":ignoreConfirmForm:ignorePatentConfirm"
										 styleClass="default-linkButton"
										 style="background: grey; border:grey"
										 oncomplete="PF('ignorePatentConfirmWidget').show();PF('patentPreviewPanelVar').hide()"
										 actionListener="#{cc.attrs.importRIPatent.setSelectedIgnorePatent(patent.pk.area_code, patent.pk.source_id, patent.pk.staff_number)}">
						</p:commandButton>
					</p:column>
					
				</p:dataTable>
</cc:implementation>

</html>
	        