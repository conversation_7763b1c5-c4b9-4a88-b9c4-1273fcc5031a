<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.cnt.proj']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormCntProj_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormCntProj_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please continue to complete "Ownership of IP Right".<br/>
				If no, please report this project in either A3 (Consultancy) or A8 (CPD Courses).
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Any Research Element in the Project?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="cnt_proj_research_element"/>
					<p:selectOneRadio id="cnt_proj_research_element" title="Any Research Element in the Project?" label="Any Research Element in the Project?" value="#{manageKtFormView.selectedFormCntProj_p.research_element}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If the IP right is NOT owned by EdUHK, please report the project in this form.<br/>
				If the IP right is wholly/ partially owned by EdUHK, please report this project in A2 (Collaborative Research Project).
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Ownership of IP Right"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="cnt_proj_ownership_ip_right"/>
					<p:selectManyCheckbox id="cnt_proj_ownership_ip_right" title="Ownership of IP Right" label="Ownership of IP Right" value="#{manageKtFormView.selectedFormCntProj_p.ownership_ip_right_array}"
												unselectable="true" layout="grid" columns="4" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItems value="#{manageKtFormView.cnt_proj_ownershipIpRightList}"/>
					</p:selectManyCheckbox>
				</div>	
				
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Project Title"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cnt_proj_title"/>	
						<p:inputTextarea id="cnt_proj_title" label="Project Title" style="width: 90%;" rows="4" counter="cnt_proj_title_display" maxlength="500"
												value="#{manageKtFormView.selectedFormCntProj_p.title}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cnt_proj_title_display" class="p-d-block" />				
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.act.code']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cnt_proj_act_code"/>
						<p:inputText id="cnt_proj_act_code"
										label="#{formBundle['form.kt.act.code']}" maxlength="50"
										value="#{manageKtFormView.selectedFormCntProj_p.act_code}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.start']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="cnt_proj_start_date"/>
						<p:datePicker id="cnt_proj_start_date" view="date"
												title="#{formBundle['form.kt.date.start']}" 
												label="#{formBundle['form.kt.date.start']}" 
												value="#{manageKtFormView.selectedFormCntProj_p.start_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
									<p:ajax event="change" update="cnt_proj_count_project_day cnt_proj_count_project_day_in_year cnt_proj_income_rpt_unit"/>
							</p:datePicker>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.end']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="cnt_proj_end_date"/>
						<p:datePicker id="cnt_proj_end_date" view="date"
												title="#{formBundle['form.kt.date.end']}" 
												label="#{formBundle['form.kt.date.end']}" 
												value="#{manageKtFormView.selectedFormCntProj_p.end_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
								<p:ajax event="change" update="cnt_proj_count_project_day cnt_proj_count_project_day_in_year cnt_proj_income_rpt_unit"/>
						</p:datePicker>
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Project Days"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						 <h:outputText id="cnt_proj_count_project_day" value="#{manageKtFormView.countProjectDay(manageKtFormView.selectedFormCntProj_p.start_date, manageKtFormView.selectedFormCntProj_p.end_date)}" />
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Project Days in the Reporting Year"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<h:outputText id="cnt_proj_count_project_day_in_year" value="#{manageKtFormView.countProjectDayInYear(manageKtFormView.selectedFormCntProj_p.start_date, manageKtFormView.selectedFormCntProj_p.end_date)}" />
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Principal Investigator (e.g. Dr. CHAN Tai Man)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cnt_proj_principal_inves"/>
						<p:inputTextarea id="cnt_proj_principal_inves" label="Principal Investigator" style="width: 90%;" rows="4" counter="cnt_proj_principal_inves_display" maxlength="500"
												value="#{manageKtFormView.selectedFormCntProj_p.principal_inves}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cnt_proj_principal_inves_display" class="p-d-block" />
					</div>	
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Funding Source"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cnt_proj_fund_src"/>
						<p:inputText id="cnt_proj_fund_src"
										label="Funding Source" maxlength="500"
										value="#{manageKtFormView.selectedFormCntProj_p.fund_src}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Total Approved Budget (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cnt_proj_budget"/>
						<p:inputNumber  id="cnt_proj_budget" title="Total Approved Budget (HK$)" label="Total Approved Budget (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCntProj_p.budget}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<p:ajax event="change" update="cnt_proj_income_rpt_unit"/>
						</p:inputNumber>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<!-- 
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the total amount of money received before any expenses were deducted for the reporting period. Example: Registration fee, outside sponsorship, budget from department for a particular event etc. For a particular item under a big project, please provide an estimated amount for this item if no exact amount is available.
					</div>
					 -->
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:inputNumber  id="cnt_proj_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.countIncome(manageKtFormView.selectedFormCntProj_p.start_date, manageKtFormView.selectedFormCntProj_p.end_date, manageKtFormView.selectedFormCntProj_p.budget)}" 
											disabled="true"/>
						<!--  <p:message for="cnt_proj_income_rpt_unit"/>
						<p:inputNumber  id="cnt_proj_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCntProj_p.income_rpt_unit}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>-->
					</div>
					
					<!--  <div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (FO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cnt_proj_income_fo"/>
						<p:inputNumber  id="cnt_proj_income_fo" title="Income (HK$) (FO)" label="Income (HK$) (FO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCntProj_p.income_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					-->
					
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="Supported by FO?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">	
				<p:message for="cnt_proj_support_fo"/>
					<p:selectOneRadio id="cnt_proj_support_fo" title="Supported by FO?" label="Supported by FO?" value="#{manageKtFormView.selectedFormCntProj_p.support_fo}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Support" itemValue="Y"/>
						<f:selectItem itemLabel="Not Support" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
					<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="Income Remarks From FO"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cnt_proj_income_fo_rem"/>
						<p:inputText id="cnt_proj_income_fo_rem"
								label="Income Remarks From FO" maxlength="200"
								value="#{manageKtFormView.selectedFormCntProj_p.income_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="Income Matching Grant"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cnt_proj_income_grant"/>
						<p:inputText id="cnt_proj_income_grant"
								label="Income Matching Grant" maxlength="200"
								value="#{manageKtFormView.selectedFormCntProj_p.income_grant}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cnt_proj_income_rdo"/>
						<p:inputNumber  id="cnt_proj_income_rdo" title="Income (HK$) (RDO)" label="Income (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCntProj_p.income_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
					<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
					<div class="riForm-item-note">
					Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the number of external parties (other than the funding body) that were engaged in the project.  Key partners can be individuals or organisations.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Key Partners
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="cnt_proj_num_key_partner"/>
						<p:inputNumber  id="cnt_proj_num_key_partner" title="Number of Key Partners" label="Number of Key Partners"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_key_partner}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If a person has multiple roles as teacher/ principal/ student/ other stakeholder, please avoid double-counting that person for the number of teacher/ principal/ student/ other stakeholder. If vice-principals/ school management committee members were benefited from the project, please count them under the number of principal.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Teachers Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cnt_proj_num_teacher"/>
						<p:inputNumber  id="cnt_proj_num_teacher" title="Number of Teachers Benefited" label="Number of Teachers Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_teacher}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Principal Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cnt_proj_num_principal"/>
						<p:inputNumber  id="cnt_proj_num_principal" title="Number of Principal Benefited" label="Number of Principal Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_principal}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cnt_proj_num_stakeholder"/>
						<p:inputNumber  id="cnt_proj_num_stakeholder" title="Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)" label="Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_stakeholder}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Schools Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cnt_proj_num_school"/>
						<p:inputNumber  id="cnt_proj_num_school" title="Number of Schools Benefited" label="Number of Schools Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_school}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Students Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cnt_proj_num_stu"/>
						<p:inputNumber  id="cnt_proj_num_stu" title="Number of Students Benefited" label="Number of Students Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_stu}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-6"></div>
					<!-- 
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the engagement of experienced professionals, practical experts and social leaders as speakers, mentors, lecturers, designers etc. in our KT activities with a view to strengthening the quality and relevance of these activities to the profession and practices. Professionals who are just participants of the activity should be excluded.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of External Professionals Engaged
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cnt_proj_num_ext_prof"/>
						<p:inputNumber  id="cnt_proj_num_ext_prof" title="Number of External Professionals Engaged" label="Number of External Professionals Engaged"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCntProj_p.num_ext_prof}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					-->
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cnt_proj_remarks_staff"/>
						<p:inputTextarea id="cnt_proj_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="cnt_proj_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormCntProj_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cnt_proj_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cnt_proj_remarks_kt"/>
						<p:inputTextarea id="cnt_proj_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="cnt_proj_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormCntProj_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cnt_proj_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
