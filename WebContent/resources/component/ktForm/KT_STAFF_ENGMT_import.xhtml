<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
	<ui:composition>
		<ui:fragment>
	      	<p:overlayPanel id="ktPreviewPanel" widgetVar="ktPreviewPanelVar"
							dismissable="false"
							styleClass="supplForm-preview-panel supplForm-preview-panel-KT">
				<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
					<table border="0" style="width:100%;">
						<tr>
							<td>
								<h:outputText value="#{manageKtSumView.importKTPanel.getKtStaffEngmt().staff_name}" style="white-space: pre-wrap"/>
								
							</td>
							<td style="text-align:right;">
								<p:commandLink oncomplete="PF('ktPreviewPanelVar').hide()">
									<i class="fas fa-times"/>
								</p:commandLink>
							</td>
						</tr>
					</table>
				</h:panelGroup>
				<div class="ui-g input-panel">
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Contributor List:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<h:outputText value="#{manageKtSumView.importKTPanel.getAuthorListMap().get(manageKtSumView.importKTPanel.getKtStaffEngmt().getPk().getForm_no())}" escape="false" />
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Start Date:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().getStart_dateStr()}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						End Date:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().getEnd_dateStr()}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Name of EdUHK Staff:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().staff_name}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Name of External Body:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().ext_body_name}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						#{formBundle['form.kt.region']}:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().region}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Nature of External Body:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().ext_body_nature}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Post Engaged:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtStaffEngmt().post_engaged}
					</div>
					
				</div>
				<h:panelGroup>
					<!-- <h:outputText value="#{manageKtSumView.importKTPanel.getKtStaffEngmt().apa_citation}" /> -->
					<table border="0" style="width:100%;">
						<tr>
							<td style="text-align:right;">
								<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('ktPreviewPanelVar').hide()">
								</p:commandButton>
							</td>
						</tr>
					</table>
				</h:panelGroup>
			</p:overlayPanel>
			<h:panelGroup id="buttonPanel">
				<p:commandButton id="importBtn" styleClass="default-linkButton" value="Import" update=":collectConfirmForm:collectConfirm"
								 oncomplete="PF('collectConfirmWidget').show();" disabled="#{manageKtSumView.importKTPanel.selectedKtStaffEngmtList.isEmpty()}" >
				</p:commandButton>
			</h:panelGroup>
			
	       	<p:dataTable id="ktDataTable"
						 value="#{manageKtSumView.importKTPanel.getKtStaffEngmtList()}" 
						 var="kt"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
	                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
	                     rows="30"
	                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
	                 	 tableStyle="table-layout:auto;"
	                 	 selection="#{manageKtSumView.importKTPanel.selectedKtStaffEngmtList}"
	                 	 rowSelectMode="checkbox"
	                 	 rowKey="#{kt.pk.asKeyString}"
	                 	 rowIndexVar="rowIndex"
						 >
				<p:ajax event="toggleSelect" update="sideBarForm:buttonPanel"/>
				<p:ajax event="rowSelectCheckbox" update="sideBarForm:buttonPanel"/>
				<p:ajax event="rowUnselectCheckbox" update="sideBarForm:buttonPanel"/>
				
				
				<p:column selectionMode="multiple" exportable="false" style="width:1em;"/>
				
				<p:column width="20%" sortBy="#{kt.staff_name}"
						  filterBy="#{kt.staff_name}" filterMatchMode="contains">
					<f:facet name="header">Name of EdUHK Staff</f:facet>
					<h:outputText value="#{kt.staff_name}" />
				</p:column>
				
				<p:column width="20%" sortBy="#{manageKtSumView.importKTPanel.getAuthorListMap().get(kt.pk.form_no)}"
						  filterBy="#{manageKtSumView.importKTPanel.getAuthorListMap().get(kt.pk.form_no)}" filterMatchMode="contains">
					<f:facet name="header">Contributor List</f:facet>
					<h:outputText value="#{manageKtSumView.importKTPanel.getAuthorListMap().get(kt.pk.form_no)}" escape="false"/>
				</p:column>
				
				<p:column width="10%" sortBy="#{kt.start_date}"
						  filterBy="#{kt.getStart_dateStr()}" filterMatchMode="contains">
					<f:facet name="header">Start Date</f:facet>
					<h:outputText value="#{kt.getStart_dateStr()}"/>
				</p:column>
				
				<p:column width="10%" sortBy="#{kt.end_date}"
						  filterBy="#{kt.getEnd_dateStr()}" filterMatchMode="contains">
					<f:facet name="header">End Date</f:facet>
					<h:outputText value="#{kt.getEnd_dateStr()}"/>
				</p:column>
				
				<p:column headerText="Name of External Body" sortBy="#{kt.ext_body_name}"
						  filterBy="#{kt.ext_body_name}" filterMatchMode="contains">
	                <h:outputText value="#{kt.ext_body_name}"/>
	            </p:column> 
	            
	            <p:column headerText="Post Engaged" sortBy="#{kt.post_engaged}"
						  filterBy="#{kt.post_engaged}" filterMatchMode="contains">
	                <h:outputText value="#{kt.post_engaged}"/>
	            </p:column> 
				
				<p:column width="3em;">
					<f:facet name="header">Actions</f:facet>
					<p:commandButton value="View" update=":sideBarForm"
									 styleClass="default-linkButton"
									 oncomplete="PF('ktPreviewPanelVar').show('#{component.clientId}')"
									 actionListener="#{manageKtSumView.importKTPanel.setSelectedPreview(kt.pk.form_no, kt.pk.data_level)}">
					</p:commandButton>
				</p:column>
				
			</p:dataTable>
		</ui:fragment>
	</ui:composition>

</html>
	        