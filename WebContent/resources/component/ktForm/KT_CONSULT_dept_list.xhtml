<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
	<ui:composition>
		<ui:fragment>

	       	<p:dataTable id="ktDataTable"
						 value="#{manageKtSumView.importKTPanel.getImportedKtConsList()}" 
						 var="kt"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
	                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
	                     rows="30"
	                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
	                 	 tableStyle="table-layout:auto;"
	                 	 rowIndexVar="rowIndex"
						 >
				
				<p:column width="60%" headerText="Course Title" id="cnt_proj_crse_title" sortBy="#{kt.title}">
					<f:facet name="header">Course Title</f:facet>
					<h:outputText value="#{kt.title}" />
				</p:column>
				
				<p:column width="10%" headerText="Start Date" id="cnt_proj_startDate" sortBy="#{kt.getStart_dateStr()}">
					<f:facet name="header">Start Date</f:facet>
					<h:outputText value="#{kt.getStart_dateStr()}"/>
				</p:column>
				
				<p:column width="10%" headerText="End Date" id="cnt_proj_endDate" sortBy="#{kt.getEnd_dateStr()}">
					<f:facet name="header">End Date</f:facet>
					<h:outputText value="#{kt.getEnd_dateStr()}"/>
				</p:column>
				
				<p:column width="3em;" headerText="Action" id="action" >
					<p:linkButton id="btn_modify" outcome="ktForm" value="Modify" style="margin-right:5px; margin-bottom:5px;">
	  					<f:param name="no" value="#{kt.pk.form_no}"/>
	  					<f:param name="dataLevel" value="N"/>
	  					<f:param name="form" value="#{bundle['kt.form.cons']}"/>
	  					<f:param name="fac" value="#{manageKtSumView.importKTPanel.paramFacCode}" />
						<f:param name="dept" value="#{manageKtSumView.importKTPanel.paramDeptCode}" />
	  				</p:linkButton>	
				</p:column>
				
			</p:dataTable>
		</ui:fragment>
	</ui:composition>

</html>
	        