<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.sem']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormSem_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormSem_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Seminars/Workshop Title"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_title"/>
						<p:inputTextarea id="sem_title" label="Seminars/Workshop Title" style="width: 90%;" rows="4" counter="sem_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormSem_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="sem_title_display" class="p-d-block" />			
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.act.code']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_act_code"/>
						<p:inputText id="sem_act_code"
										label="#{formBundle['form.kt.act.code']}" maxlength="50"
										value="#{manageKtFormView.selectedFormSem_p.act_code}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Date (MM/YYYY)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_start_date"/>
						<p:datePicker id="sem_start_date" view="month"
												title="Date (MM/YYYY)" 
												label="Date (MM/YYYY)" 
												value="#{manageKtFormView.selectedFormSem_p.start_date}" 
												pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please only include those seminars/ workshops organized by EdUHK units. External seminars/ workshops attended by EdUHK members but not organized by EdUHK units should not be included. To avoid double-counting, seminars/ workshops jointly organized by different EdUHK units should be reported once only under the name of the main organizing unit.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Organizer"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_organizer"/>
						<p:inputTextarea id="sem_organizer" label="Organizer" style="width: 90%;" rows="4" counter="sem_organizer_display" maxlength="500"
												value="#{manageKtFormView.selectedFormSem_p.organizer}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="sem_organizer_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Contact Person"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_ct_person"/>
						<p:inputText id="sem_ct_person"
										label="Contact Person" maxlength="200"
										value="#{manageKtFormView.selectedFormSem_p.ct_person}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Target Participants"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="sem_target_pax"/>
						<p:selectOneRadio id="sem_target_pax" title="Target Participants" label="Target Participants" value="#{manageKtFormView.selectedFormSem_p.target_pax}"
													unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false}">
							<f:selectItems value="#{manageKtFormView.sem_targetPaxList}"/>
						</p:selectOneRadio>
					</div>		
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Free / Chargeable"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="sem_free_charge"/>
						<p:selectOneRadio id="sem_free_charge" title="Free / Chargeable" label="Free / Chargeable" value="#{manageKtFormView.selectedFormSem_p.free_charge}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false}">
							<f:selectItem itemLabel="Free" itemValue="F"/>
							<f:selectItem itemLabel="Chargeable" itemValue="C"/>	   
						</p:selectOneRadio>
					</div>		
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the total amount of money received before any expenses were deducted for the reporting period. Example: Registration fee, outside sponsorship, budget from department for a particular event etc. For a particular item under a big project, please provide an estimated amount for this item if no exact amount is available.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_income_rpt_unit"/>
						<p:inputNumber  id="sem_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSem_p.income_rpt_unit}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (FO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="sem_income_fo"/>
						<p:inputNumber  id="sem_income_fo" title="Income (HK$) (FO)" label="Income (HK$) (FO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSem_p.income_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="Income Remarks From FO"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="sem_income_fo_rem"/>
						<p:inputText id="sem_income_fo_rem"
								label="Income Remarks From FO" maxlength="200"
								value="#{manageKtFormView.selectedFormSem_p.income_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="sem_income_rdo"/>
						<p:inputNumber  id="sem_income_rdo" title="Income (HK$) (RDO)" label="Income (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSem_p.income_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
			</div>
			<br/>
			<div class="form-sub-title">
					<i class="fa-solid fa-sack-dollar" style="margin-right:5px;"></i>Expenditure
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				Only direct costs arising from the reported events/activities should be included. For example, costs directly related to the organization of the events (e.g. wages of staff hired for organizing the events, rentals of outside venues/facilities, etc.) should be included. Overhead expenses incurred regardless of the occurrence of the reported events should be excluded. If all costs arising from the reported events/activities are indirect costs, please fill in “0”.
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Expenditure (Direct Cose) (HK$)
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="sem_expnd_rpt_unit"/>
					<p:inputNumber  id="sem_expnd_rpt_unit" title="Expenditure (Direct Cose) (HK$)" label="Expenditure (Direct Cose) (HK$)" symbol="$"
										maxValue="9999999999" minValue="0" decimalPlaces="2" 
										value="#{manageKtFormView.selectedFormSem_p.expnd_rpt_unit}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					Expenditure (HK$) (FO)
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="sem_expnd_fo"/>
					<p:inputNumber  id="sem_expnd_fo" title="Expenditure (HK$) (FO)" label="Expenditure (HK$) (FO)" symbol="$"
										maxValue="9999999999" minValue="0" decimalPlaces="2" 
										value="#{manageKtFormView.selectedFormSem_p.expnd_fo}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="Expenditure Remarks From FO"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="sem_expnd_fo_rem"/>
					<p:inputText id="sem_expnd_fo_rem"
							label="Expenditure Remarks From FO" maxlength="200"
							value="#{manageKtFormView.selectedFormSem_p.expnd_fo_rem}"
							style="width: 90%;"
							disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					Expenditure (HK$) (RDO)
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="sem_expnd_rdo"/>
					<p:inputNumber  id="sem_expnd_rdo" title="Expenditure (HK$) (RDO)" label="Expenditure (HK$) (RDO)" symbol="$"
										maxValue="9999999999" minValue="0" decimalPlaces="2" 
										value="#{manageKtFormView.selectedFormSem_p.expnd_rdo}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>
			</div>
			<br/>
			<div class="form-sub-title">
				<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
				<div class="riForm-item-note">
				Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
				</div>
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Key Partners
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="sem_num_key_partner"/>
						<p:inputNumber  id="sem_num_key_partner" title="Number of Key Partners" label="Number of Key Partners"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.num_key_partner}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Total Number of Participants is equal to the sum of 1) Teacher Participants, 2) Principal Participants and 3) Other Participants. If the seminar/ workshop did not require registration, please estimate the number of its participants.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="sem_num_pax"/>
						<p:inputNumber  id="sem_num_pax" title="Number of Participants" label="Number of Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.num_pax}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-3" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
							<p:outputLabel class="riForm-item-title" value="Check # of Participants"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-9" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">	
					<p:message for="sem_check_pax"/>
						<p:selectOneRadio id="sem_check_pax" title="Check # of Participants" value="#{manageKtFormView.selectedFormSem_p.check_pax}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
							<f:selectItem itemValue="Y" itemLabel="Correct"/>
							<f:selectItem itemValue="N" itemLabel="Check"/>
						</p:selectOneRadio>
					</div>		
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If a participant has multiple roles as teacher/ principal/ other participant, please avoid double-counting that participant for the number of teacher/ principal/ other participants. If vice-principals/ school management committee members attended the seminar/ workshop, please count them under the number of principal participants.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Teacher Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="sem_num_teacher"/>
						<p:inputNumber  id="sem_num_teacher" title="Number of Teacher Participants" label="Number of Teacher Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.num_teacher}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Principal Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="sem_num_principal"/>
						<p:inputNumber  id="sem_num_principal" title="Number of Principal Participants" label="Number of Principal Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.num_principal}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Other Stakeholder Participants (e.g. parents)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="sem_num_other"/>
						<p:inputNumber  id="sem_num_other" title="Number of Other Stakeholder Participants (e.g. parents)" label="Number of Other Stakeholder Participants (e.g. parents)"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.num_other}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					a. Staff time should only be included for the event itself; time for preparation should not be included. Only EdUHK staff members who are speaker/ presenter and/or involved in the organization/ coordination of the conference should be included.
					<br/>
					b. Academic and Research Staff include (Research) Chair Professor, (Adjunct/ Visiting) Professor, (Adjunct/ Visiting) Associate Professor, Assistant Professor, Visiting Scholar, (Guest/ Principal/ Senior) Lecturer, (Senior)Instructor, (Senior) Teaching Fellow, Tutor, Postdoctoral Fellow and (Senior) Research Assistant.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Academic and Research Staff Involved (Man-day)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="sem_staff_man_day"/>
						<p:inputNumber  id="sem_staff_man_day" title="Number of Academic and Research Staff Involved (Man-day)" label="Number of Academic and Research Staff Involved (Man-day)"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.staff_man_day}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the engagement of experienced professionals, practical experts and social leaders as speakers, mentors, lecturers, designers etc. in our KT activities with a view to strengthening the quality and relevance of these activities to the profession and practices. Professionals who are just participants of the seminar/ workshop should be excluded.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of External Professionals Engaged
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="sem_num_ext_prof"/>
						<p:inputNumber  id="sem_num_ext_prof" title="Number of External Professionals Engaged" label="Number of External Professionals Engaged"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSem_p.num_ext_prof}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="sem_remarks_staff"/>
						<p:inputTextarea id="sem_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="sem_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormSem_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="sem_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="sem_remarks_kt"/>
						<p:inputTextarea id="sem_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="sem_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormSem_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="sem_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
