<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtSumView.paramFormCode eq bundle['kt.form.ip']}">
			<p:dataTable id="dataTable" var="data" value="#{manageKtSumView.formSubList}" stripedRows="true"
							tableStyle="table-layout: fixed;">  	
				<f:facet name="header">
	             	<i class="fa-solid fa-bullseye" style="color:#FBC02D;"></i><h:outputText style="padding-left:7px; font-weight:700;" value="#{manageKtSumView.period.period_desc}"/>
	             	<h:outputText class="facDeptText" value="#{manageKtSumView.selectedFacDept}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
	      		</f:facet>       	
				<p:column headerText="Product/ IP Title" id="ip_title" sortBy="#{data.getKtFormIP_p().title}" sortOrder="asc">
	                <h:outputText value="#{data.getKtFormIP_p().title}" style="#{data.getKtFormDetails_q().display_ind == 'N' and manageKtSumView.paramAdmin ne 'Y'?'color:#888;':'color:#186ba0; font-weight:700;'}"/>
	            </p:column>
	            <p:column headerText="Start Date" id="ip_start_date" sortBy="#{data.getKtFormIP_p().start_date}">
	                <h:outputText value="#{data.getKtFormIP_p().start_date}" style="#{data.getKtFormDetails_q().display_ind == 'N' and manageKtSumView.paramAdmin ne 'Y'?'color:#888;':'color:#186ba0; font-weight:700;'}" rendered="#{not fn:contains(data.getKtFormIP_p().start_date, '1900')}">
	                	<f:convertDateTime pattern="dd/MM/yyyy" />
	                </h:outputText>
	            </p:column>  
	            <p:column headerText="End Date" id="ip_end_date" sortBy="#{data.getKtFormIP_p().end_date}">
	                <h:outputText value="#{data.getKtFormIP_p().end_date}" style="#{data.getKtFormDetails_q().display_ind == 'N' and manageKtSumView.paramAdmin ne 'Y'?'color:#888;':'color:#186ba0; font-weight:700;'}" rendered="#{not fn:contains(data.getKtFormIP_p().end_date, '1900')}">
	                	<f:convertDateTime pattern="dd/MM/yyyy" />
	                </h:outputText>
	            </p:column>
	            <p:column headerText="Status" id="status" sortBy="#{data.getKtFormHeader_q().publish_status}" style="width:5%">
	                <h:outputText style="font-weight:700; color:#1B5E20;" value="#{formBundle['form.ri.status.lv.p']}" rendered="#{data.getKtFormDetails_q().creator_ind == 'Y' &amp;&amp; data.getKtFormHeader_q().publish_status == 'PUBLISHED' &amp;&amp; data.getKtFormDetails_q().display_ind == 'Y' and manageKtSumView.paramAdmin ne 'Y'}"/>
	                <h:outputText style="font-weight:700; color:#D50000;" value="#{formBundle['form.ri.status.lv.m']}" rendered="#{data.getKtFormDetails_q().creator_ind == 'Y' &amp;&amp; data.getKtFormHeader_q().publish_status == 'MODIFIED' &amp;&amp; data.getKtFormDetails_q().display_ind == 'Y' and manageKtSumView.paramAdmin ne 'Y'}"/>
	                <h:outputText style="font-weight:700; color:#827717;" value="Consented" rendered="#{data.getKtFormDetails_q().creator_ind == 'N' &amp;&amp; data.getKtFormDetails_q().consent_ind != 'U' &amp;&amp; data.getKtFormDetails_q().display_ind == 'Y' and manageKtSumView.paramAdmin ne 'Y'}"/>
	                <h:outputText style="font-weight:700; color:#E65100;" value="Waiting for Consensus" rendered="#{data.getKtFormDetails_q().creator_ind == 'N' &amp;&amp; data.getKtFormDetails_q().consent_ind == 'U' and manageKtSumView.paramAdmin ne 'Y'}"/>
	            	 <h:outputText style="font-weight:700; color:#888;" value="Hidden" rendered="#{data.getKtFormDetails_q().display_ind == 'N' &amp;&amp; data.getKtFormDetails_q().consent_ind ne 'U' and manageKtSumView.paramAdmin ne 'Y'}"/>
	            	<h:outputText style="font-weight:700; color:#0066ff;" value="#{formBundle['form.ri.status.lv.n.imported']}" rendered="#{data.getKtFormHeader_q().publish_status == 'IMPORTED' and manageKtSumView.paramAdmin eq 'Y'}"/>
	            	<h:outputText style="font-weight:700; color:#D50000;" value="#{formBundle['form.ri.status.lv.n']}" rendered="#{data.getKtFormHeader_q().publish_status == 'MODIFIED' and manageKtSumView.paramAdmin eq 'Y'}"/>
	            	<h:outputText style="font-weight:700; color:#1B5E20;" value="#{formBundle['form.ri.status.lv.d']}" rendered="#{data.getKtFormHeader_q().publish_status == 'PUBLISHED' and manageKtSumView.paramAdmin eq 'Y'}"/>
	            </p:column>  
	            <p:column headerText="Action" id="action" style="width:5%" exportable = "false">
	  				<p:linkButton id="btn_modify" outcome="ktForm" rendered="#{manageKtSumView.showModifyBtn(data.getKtFormDetails_q().creator_ind)}" value="Modify" styleClass="btn_action">
	  					<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
	  					<f:param name="no" value="#{data.pk.form_no}"/>
	  					<f:param name="dataLevel" value="#{manageKtSumView.getFormDataLevel()}"/>
	  					<f:param name="form" value="#{bundle['kt.form.ip']}"/>
	  					<f:param name="period" value="#{manageKtSumView.period.period_id}"/>
	  					<f:param name="admin" value="#{manageKtSumView.paramAdmin}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
						<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
	  				</p:linkButton>	
	  				<p:linkButton id="btn_view" outcome="ktForm" rendered="#{manageKtSumView.showViewBtn(data.getKtFormDetails_q().creator_ind)}" value="View" styleClass="btn_action">
	  					<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
	  					<f:param name="no" value="#{data.pk.form_no}"/>
	  					<f:param name="dataLevel" value="#{manageKtSumView.getFormDataLevel()}"/>
	  					<f:param name="form" value="#{bundle['kt.form.ip']}"/>
	  					<f:param name="period" value="#{manageKtSumView.period.period_id}"/>
	  					<f:param name="admin" value="#{manageKtSumView.paramAdmin}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
						<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
  				</p:linkButton>	
	  				<p:linkButton id="btn_consent" outcome="ktForm" rendered="#{data.getKtFormDetails_q().creator_ind eq 'N' and data.getKtFormDetails_q().consent_ind eq 'U' and manageRIView.canModify and manageKtSumView.paramAdmin ne 'Y'}" value="Consent" styleClass="btn_action">
  					<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
  					<f:param name="no" value="#{data.pk.form_no}"/>
  					<f:param name="dataLevel" value="#{manageKtSumView.getFormDataLevel()}"/>
  					<f:param name="form" value="#{bundle['kt.form.ip']}"/>
  					<f:param name="period" value="#{manageKtSumView.period.period_id}"/>
  					<f:param name="admin" value="#{manageKtSumView.paramAdmin}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
						<f:param name="facDept" value="#{manageKtSumView.selectedFacDept}" rendered="#{manageKtSumView.paramAdmin eq 'Y'}"/>
  				</p:linkButton>	
  				<p:commandButton id="btn_copy" 
  								rendered="#{manageKtSumView.showCopyBtn(data.getKtFormDetails_q().creator_ind) &amp;&amp; manageKtSumView.canModifyKt == true}" 
  								value="#{formBundle['form.copy']}" 
  								styleClass="ui-button-warning btn_action btn_copy"
								action="#{manageKtFormView.copyFormInList(manageKtSumView.paramPid, data.pk.form_no, bundle['kt.form.ip'], manageKtSumView.period.period_id, manageKtSumView.getFormDataLevel(), manageKtSumView.selectedFacDept)}">
					<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.kt.copy.desc']}"
								icon="pi pi-info-circle" />
				</p:commandButton>
  				<p:commandButton id="btn_delete" 
  								rendered="#{manageKtSumView.showDeleteBtn(data.getKtFormDetails_q().creator_ind, data.pk.form_no) &amp;&amp; manageKtSumView.canModifyKt == true}" 
  								value="#{formBundle['form.del']}" 
  								styleClass="ui-button-danger btn_action btn_del"
								action="#{manageKtFormView.deleteFormInList(manageKtSumView.paramPid, data.pk.form_no, bundle['kt.form.ip'], manageKtSumView.period.period_id, manageKtSumView.getFormDataLevel(), manageKtSumView.selectedFacDept)}">
					<p:confirm header="#{formBundle['form.confirm']}"
								message="#{formBundle['form.kt.del.desc']}"
								icon="pi pi-info-circle" />
				</p:commandButton>
				<p:confirmDialog global="true" showEffect="fade" hideEffect="fade"
					responsive="true" width="350">
					<p:commandButton value="No" icon="pi pi-times" type="button"
						styleClass="ui-confirmdialog-no ui-button-flat" />
					<p:commandButton value="Yes" icon="pi pi-check" type="button"
						styleClass="ui-confirmdialog-yes" />
				</p:confirmDialog>
	            </p:column>  
			</p:dataTable>
		</ui:fragment>			
		
	</ui:composition>

</html>
