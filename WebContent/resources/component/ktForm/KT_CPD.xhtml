<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment>
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormCPD_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormCPD_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please report this project in either A1 (Contract Research) or A2 (Collaborative Research Projects).<br/>
				If no, please continue to complete "Is It a Course/ Programme?"
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Any Research Element in the Course?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="cpd_research_element"/>
					<p:selectOneRadio id="cpd_research_element" title="Any Research Element in the Course?" label="Any Research Element in the Course?" value="#{manageKtFormView.selectedFormCPD_p.research_element}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please continue to complete "Entrepreneurship Element?"<br/>
				If no, please report this item in A3 (Consultancy).
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Is It a Course/ Programme?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="cpd_crse_prog"/>
					<p:selectOneRadio id="cpd_crse_prog" title="Is It a Course/ Programme?" label="Is It a Course/ Programme?" value="#{manageKtFormView.selectedFormCPD_p.crse_prog}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please report this item in A6 (Entrepreneurship Activity).<br/>
				If no, please continue to complete "Course Title".
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Entrepreneurship Element?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="cpd_ent_element"/>
					<p:selectOneRadio id="cpd_ent_element" title="Entrepreneurship Element?" label="Entrepreneurship Element?" value="#{manageKtFormView.selectedFormCPD_p.ent_element}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Course Title"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="cpd_title"/>
					<p:inputTextarea id="cpd_title" label="Course Title" style="width: 90%;" rows="4" counter="cpd_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormCPD_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="cpd_title_display" class="p-d-block" />						
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.act.code']}"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="cpd_act_code"/>
					<p:inputText id="cpd_act_code"
									label="#{formBundle['form.kt.act.code']}" maxlength="50"
									value="#{manageKtFormView.selectedFormCPD_p.act_code}"
									style="width: 90%;"
									disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
				</div>	
					
				<div class="ui-g-12 ui-md-3 ui-lg-2">
				    <p:outputLabel class="riForm-item-title" value="Principal Investigator/Person in charge"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
				    <p:message for="cpd_pi"/>	
				    <p:inputText id="cpd_pi" label="Principal Investigator/Person in charge" style="width: 90%;"
				                value="#{manageKtFormView.selectedFormCPD_p.pi}"
				                disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
				</div>	
					
				<div class="ui-g-12 ui-md-3 ui-lg-2">
				    <p:outputLabel class="riForm-item-title" value="Funding Source"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
				    <p:message for="cpd_fund_src"/>	
				    <p:inputText id="cpd_fund_src" label="Funding Source" style="width: 90%;"
				                value="#{manageKtFormView.selectedFormCPD_p.fund_src}"
				                disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
				</div>		
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Mode"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="cpd_act_mode"/>
						<p:selectOneRadio id="cpd_act_mode" title="Mode" label="Mode" value="#{manageKtFormView.selectedFormCPD_p.act_mode}"
													unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cpd_modeList}"/>
						</p:selectOneRadio>
					</div>	
				
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.start']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="cpd_start_date"/>
						<p:datePicker id="cpd_start_date" view="date"
												title="#{formBundle['form.kt.date.start']}" 
												label="#{formBundle['form.kt.date.start']}" 
												value="#{manageKtFormView.selectedFormCPD_p.start_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
												<p:ajax event="change" update="cpd_count_project_day cpd_count_project_day_in_year cpd_income_rpt_unit"/>
						</p:datePicker>				
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.end']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="cpd_end_date"/>
						<p:datePicker id="cpd_end_date" view="date"
												title="#{formBundle['form.kt.date.end']}" 
												label="#{formBundle['form.kt.date.end']}" 
												value="#{manageKtFormView.selectedFormCPD_p.end_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
												<p:ajax event="change" update="cpd_count_project_day cpd_count_project_day_in_year cpd_income_rpt_unit"/>
						</p:datePicker>		
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Course Days"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						 <h:outputText id="cpd_count_project_day" value="#{manageKtFormView.countProjectDay(manageKtFormView.selectedFormCPD_p.start_date, manageKtFormView.selectedFormCPD_p.end_date)}" />
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Course Days in the Reporting Year"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<h:outputText id="cpd_count_project_day_in_year" value="#{manageKtFormView.countProjectDayInYear(manageKtFormView.selectedFormCPD_p.start_date, manageKtFormView.selectedFormCPD_p.end_date)}" />
					</div>
				
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="EdUHK Organizer?"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="cpd_eduhk_org"/>
						<p:selectOneRadio id="cpd_eduhk_org" title="EdUHK Organizer?" label="EdUHK Organizer?" value="#{manageKtFormView.selectedFormCPD_p.eduhk_org}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Yes" itemValue="Y"/>
							<f:selectItem itemLabel="No" itemValue="N"/>	   
						</p:selectOneRadio>
					</div>	
					
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.region']}"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="cpd_region"/>
						<p:selectOneRadio id="cpd_region" title="#{formBundle['form.kt.region']}" label="#{formBundle['form.kt.region']}" value="#{manageKtFormView.selectedFormCPD_p.region}"
													unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Local" itemValue="L"/>
							<f:selectItem itemLabel="National" itemValue="N"/>
							<f:selectItem itemLabel="International" itemValue="I"/>	   
						</p:selectOneRadio>
					</div>
						
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Free / Chargeable"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="cpd_free_charge"/>
						<p:selectOneRadio id="cpd_free_charge" title="Free / Chargeable" label="Free / Chargeable" value="#{manageKtFormView.selectedFormCPD_p.free_charge}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Free" itemValue="F"/>
							<f:selectItem itemLabel="Chargeable" itemValue="C"/>	   
						</p:selectOneRadio>
					</div>		
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Total Approved Budget (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cpd_budget"/>
						<p:inputNumber  id="cpd_budget" title="Total Approved Budget (HK$)" label="Total Approved Budget (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCPD_p.budget}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<p:ajax event="change" update="cpd_income_rpt_unit"/>
						</p:inputNumber>
					</div>
				
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cpd_income_rpt_unit"/>
						<p:inputNumber  id="cpd_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.countIncome(manageKtFormView.selectedFormCPD_p.start_date, manageKtFormView.selectedFormCPD_p.end_date, manageKtFormView.selectedFormCPD_p.budget)}" 
											disabled="true"/>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (Remarks From FO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cpd_income_fo_rem"/>
						<p:inputText id="cpd_income_fo_rem"
								label="Income (Remarks From FO)" maxlength="200"
								value="#{manageKtFormView.selectedFormCPD_p.income_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cpd_income_rdo"/>
						<p:inputNumber  id="cpd_income_rdo" title="Income (HK$) (RDO)" label="Income (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCPD_p.income_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-sack-dollar" style="margin-right:5px;"></i>Expenditure
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Only direct costs arising from the reported events/activities should be included. For example, costs directly related to the organization of the events (e.g. wages of staff hired for organizing the events, rentals of outside venues/facilities, etc.) should be included. Overhead expenses incurred regardless of the occurrence of the reported events should be excluded.					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Expenditure (Direct Cost) (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="cpd_expnd_rpt_unit"/>
						<p:inputNumber  id="cpd_expnd_rpt_unit" title="Expenditure (Direct Cost) (HK$)" label="Expenditure (Direct Cost) (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCPD_p.expnd_rpt_unit}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>

					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Expenditure (Direct Cost) (HK$) (Remarks from FO)
					</div>	
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cpd_expnd_fo_rem"/>
						<p:inputText id="cpd_expnd_fo_rem"
								label="Expenditure (Direct Cost) (HK$) (Remarks from FO)" maxlength="200"
								value="#{manageKtFormView.selectedFormCPD_p.expnd_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Expenditure (Direct Cost) (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cpd_expnd_rdo"/>
						<p:inputNumber  id="cpd_expnd_rdo" title="Expenditure (Direct Cost) (HK$) (RDO)" label="Expenditure (Direct Cost) (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCPD_p.expnd_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
					<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
					<div class="riForm-item-note">
					Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the number of external parties (other than the funding body) that are engaged in the project.  Key partners can be individuals or organisations.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Key Partners
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="cpd_num_key_partner"/>
						<p:inputNumber  id="cpd_num_key_partner" title="Number of Key Partners" label="Number of Key Partners"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCPD_p.num_key_partner}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
						If a participant has multiple roles as teacher/ principal, please avoid double-counting that participant for the number of teacher/ principal participants. If vice-principals/ school management committee members attended the course, please count them under the number of principal participants.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Teacher Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cpd_num_teacher"/>
						<p:inputNumber  id="cpd_num_teacher" title="Number of Teacher Participants" label="Number of Teacher Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCPD_p.num_teacher}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Principal Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cpd_num_principal"/>
						<p:inputNumber  id="cpd_num_principal" title="Number of Principal Participants" label="Number of Principal Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCPD_p.num_principal}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Other Stakeholders Benefited (e.g. professionals, workers)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cpd_num_other"/>
						<p:inputNumber  id="cpd_num_other" title="Number of Other Stakeholders Benefited (e.g. professionals, workers)" label="Number of Other Stakeholders Benefited (e.g. professionals, workers)"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCPD_p.num_other}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Schools Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cpd_num_school"/>
						<p:inputNumber  id="cpd_num_school" title="Number of Schools Benefited" label="Number of Schools Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCPD_p.num_school}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Student Contact Hours
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="cpd_num_stu_contact_hr"/>
						<p:inputNumber  id="cpd_num_stu_contact_hr" title="Number of Student Contact Hours" label="Number of Student Contact Hours"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCPD_p.num_stu_contact_hr}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Remarks
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cpd_remarks_staff"/>
						<p:inputTextarea id="cpd_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="cpd_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormCPD_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cpd_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cpd_remarks_kt"/>
						<p:inputTextarea id="cpd_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="cpd_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormCPD_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cpd_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
		</ui:fragment>			
		
	</ui:composition>

</html>
