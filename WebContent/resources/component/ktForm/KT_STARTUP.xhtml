<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment>
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormStartup_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormStartup_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name of Team/Startup Supported"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="startup_title"/>
					<p:inputTextarea id="startup_title" label="Name of Team/Startup Supported" style="width: 90%;" rows="4" counter="startup_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormStartup_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="startup_title_display" class="p-d-block" />	
				</div>	
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Type(s) of Support Provided"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="startup_act_type"/>
					<p:selectManyCheckbox  id="startup_act_type" title="Type(s) of Support Provided" label="Type(s) of Support Provided" value="#{manageKtFormView.selectedFormStartup_p.act_type_list}"
												unselectable="true" layout="grid" columns="4" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItems value="#{manageKtFormView.startup_actTypeList}"/>
					</p:selectManyCheckbox >
				</div>			
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Date (MM/YYYY)"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="startup_start_date"/>
					<p:datePicker id="startup_start_date" view="month"
											title="Date (MM/YYYY)" 
											label="Date (MM/YYYY)" 
											value="#{manageKtFormView.selectedFormStartup_p.start_date}" 
											pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Organizer"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="startup_organizer"/>
					<p:inputTextarea id="startup_organizer" label="Organizer" style="width: 90%;" rows="4" counter="startup_organizer_display" maxlength="500"
											value="#{manageKtFormView.selectedFormStartup_p.organizer}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="startup_organizer_display" class="p-d-block" />
				</div>		
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Contact Person"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="startup_ct_person"/>
					<p:inputText id="startup_ct_person"
									label="Contact Person" maxlength="500"
									value="#{manageKtFormView.selectedFormStartup_p.ct_person}"
									style="width: 90%;"
									disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>	
			</div>
			<br/>
			<div class="form-sub-title">
				<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
				<div class="riForm-item-note">
				Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
				</div>
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of UG Students in the Team/ Startup
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="startup_num_stu_ug"/>
					<p:inputNumber  id="startup_num_stu_ug" title="Number of UG Students in the Team/ Startup" label="Number of UG Students in the Team/ Startup"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormStartup_p.num_stu_ug}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of PG Students in the Team/ Startup
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="startup_num_stu_pg"/>
					<p:inputNumber  id="startup_num_stu_pg" title="Number of PG Students in the Team/ Startup" label="Number of PG Students in the Team/ Startup"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormStartup_p.num_stu_pg}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Staff in the Team/ Startup
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="startup_num_staff"/>
					<p:inputNumber  id="startup_num_staff" title="Number of Staff in the Team/ Startup" label="Number of Staff in the Team/ Startup"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormStartup_p.num_staff}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Alumni in the Team/ Startup
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="startup_num_alumni"/>
					<p:inputNumber  id="startup_num_alumni" title="Number of Alumni in the Team/ Startup" label="Number of Alumni in the Team/ Startup"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormStartup_p.num_alumni}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>	
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<!-- Remarks -->
			<div class="ui-g">
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Remarks
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="startup_remarks_staff"/>
					<p:inputTextarea id="startup_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="startup_remarks_staff_display" maxlength="2000"
											value="#{manageKtFormView.selectedFormStartup_p.remarks_staff}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="startup_remarks_staff_display" class="p-d-block" />
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					Note (RDO)
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="startup_remarks_kt"/>
					<p:inputTextarea id="startup_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="startup_remarks_kt_display" maxlength="2000"
											value="#{manageKtFormView.selectedFormStartup_p.remarks_kt}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="startup_remarks_kt_display" class="p-d-block" />
				</div>	
			</div>
		<hr style="border-top: 1px dashed #7b9d39;"/>
		</ui:fragment>			
		
	</ui:composition>

</html>
