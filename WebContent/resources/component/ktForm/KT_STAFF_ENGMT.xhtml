<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.staff.engmt']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormStaffEngmt_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormStaffEngmt_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name of EdUHK Staff (e.g. Dr. CHAN Tai Man)"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="staff_engmt_staff_name"/>
					<p:inputTextarea id="staff_engmt_staff_name" label="Name of EdUHK Staff (e.g. Dr. CHAN Tai Man)" style="width: 90%;" rows="4" counter="staff_engmt_staff_name_display" maxlength="500"
										value="#{manageKtFormView.selectedFormStaffEngmt_p.staff_name}"
                       					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                       					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                     <br/>
     				  <h:outputText id="staff_engmt_staff_name_display" class="p-d-block" />			
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please report the post engaged by academic/ teaching/ project/ administrative/ technical staff.
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Administrative / Technical Staff?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="staff_engmt_admin_tech"/>
					<p:selectOneRadio id="staff_engmt_admin_tech" title="Administrative / Technical Staff?" label="Administrative / Technical Staff?" value="#{manageKtFormView.selectedFormStaffEngmt_p.admin_tech}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please leave it blank and clarify in "Remarks" if the staff is still engaging and without a specific end date.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Start Date (MM/YYYY)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="staff_engmt_start_date"/>
						<p:datePicker id="staff_engmt_start_date" view="month"
												title="Start Date (MM/YYYY)" 
												label="Start Date (MM/YYYY)" 
												value="#{manageKtFormView.selectedFormStaffEngmt_p.start_date}" 
												pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="End Date (MM/YYYY)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="staff_engmt_end_date"/>
						<p:datePicker id="staff_engmt_end_date" view="month"
												title="End Date (MM/YYYY)" 
												label="End Date (MM/YYYY)" 
												value="#{manageKtFormView.selectedFormStaffEngmt_p.end_date}" 
												pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Name of External Body"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="staff_engmt_ext_body_name"/>
						<p:inputText id="staff_engmt_ext_body_name"
										label="Name of External Body" maxlength="1000"
										value="#{manageKtFormView.selectedFormStaffEngmt_p.ext_body_name}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.region']}"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="staff_engmt_region"/>
						<p:selectOneRadio id="staff_engmt_region" title="#{formBundle['form.kt.region']}" label="#{formBundle['form.kt.region']}" value="#{manageKtFormView.selectedFormStaffEngmt_p.region}"
													unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Local" itemValue="L"/>
							<f:selectItem itemLabel="National" itemValue="N"/>
							<f:selectItem itemLabel="International" itemValue="I"/>	    
						</p:selectOneRadio>
					</div>		
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Country refers to the location of the headquarter or head office.
					</div>

					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Nature of External Body"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="staff_engmt_ext_body_nature"/>
						<p:selectOneRadio id="staff_engmt_ext_body_nature" title="Nature of External Body" label="Nature of External Body" value="#{manageKtFormView.selectedFormStaffEngmt_p.ext_body_nature}" unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.staff_engmt_natureList}"/>
						</p:selectOneRadio>
					</div>		
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Post Engaged: e.g. member, editor, advisor, consultant, convener, chairperson, director, president, others (please specify)
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Post Engaged"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="staff_engmt_post_engaged"/>
						<p:inputText id="staff_engmt_post_engaged"
											label="Post Engaged" maxlength="1000"
											value="#{manageKtFormView.selectedFormStaffEngmt_p.post_engaged}"
											style="width: 90%;"
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>		
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="staff_engmt_remarks_staff"/>
						<p:inputTextarea id="staff_engmt_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="staff_engmt_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormStaffEngmt_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="staff_engmt_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="staff_engmt_remarks_kt"/>
						<p:inputTextarea id="staff_engmt_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="staff_engmt_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormStaffEngmt_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="staff_engmt_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
