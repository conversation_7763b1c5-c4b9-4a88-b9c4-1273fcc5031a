<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment>
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormInvAward_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormInvAward_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name of the Innovative Product/ Invention"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="inv_award_ip_name"/>
					<p:inputTextarea id="inv_award_ip_name" label="Name of the Innovative Product/ Invention" style="width: 90%;" rows="4" counter="inv_award_ip_name_display" maxlength="500"
											value="#{manageKtFormView.selectedFormInvAward_p.ip_name}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="inv_award_ip_name_display" class="p-d-block" />					
				</div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				Please visit <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/International-Recognitions.html">https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/International-Recognitions.html</a> for details of the competitions, exhibitions and events coordinated by KT Sub-office.
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name of Competition / Exhibition / Event"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="inv_award_event_name"/>
					<p:inputTextarea id="inv_award_event_name" label="Name of Competition / Exhibition / Event" style="width: 90%;" rows="4" counter="inv_award_event_name_display" maxlength="500"
											value="#{manageKtFormView.selectedFormInvAward_p.event_name}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="inv_award_event_name_display" class="p-d-block" />					
				</div>
		</div>
		<br/>
		
		<div class="form-sub-title">
			<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Awards
		</div>
	<hr style="border-top: 1px dashed #7b9d39;"/>
	<div class="ui-g">
		<h:panelGroup  id="awardsPanel" >	
			<p:messages for="awardsTable"/>	
			<p:commandButton id="btn_awards_add" icon="fas fa-plus" value="Add" 
									 style="width:260px; margin-bottom:2px;"
									 action="#{manageKtFormView.addAwardsTableRow()}"
									 update="awardsTable"
									 immediate="true"/>
			<p:blockUI block="contentPanel" trigger="btn_awards_add" />
			<p:dataTable id="awardsTable" value="#{manageKtFormView.inv_award_awardDetails_list}" 
									class="riFormTable"
									reflow="true" var="col" widgetVar="awardsTableWV"
									style="max-width:99%;"
						 			rowIndexVar="rowIndex">
						 	 		
					<p:column style="text-align:left; width:5em;">
						<f:facet name="header">No.</f:facet>
						#{rowIndex +1}							
					</p:column>
					 <p:column style="text-align:left;">
						<f:facet name="header">Title of Award</f:facet>
						<p:message for="awardsTable_title"/>
						<p:inputText id="awardsTable_title" title="Title of Award" label="Title of Award" style="width:90%" 
										value="#{col.title}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>
					<p:column style="text-align:left;">
						<f:facet name="header">Award Type</f:facet>
						<p:message for="awardsTable_region"/>
						<p:selectOneRadio id="awardsTable_region" title="Award Type" label="Award Type" value="#{col.region}"
													unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Local" itemValue="L"/>
							<f:selectItem itemLabel="National" itemValue="N"/>
							<f:selectItem itemLabel="International" itemValue="I"/>
							<p:ajax event="valueChange"/>
						</p:selectOneRadio>
					</p:column>			
					<p:column  class="data-noprint" style="width:2em; text-align:left;" rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtFormView.canModify == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_awards_delete" action="#{manageKtFormView.deleteAwardsTableRow(rowIndex)}" rendered="#{rowIndex gt 0}"
									   update="awardsTable" immediate="true">
							<i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
						</p:commandLink>	
						<p:blockUI block="contentPanel" trigger="btn_awards_delete" />
					</p:column>	
				</p:dataTable><br/>
			</h:panelGroup>	

				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Date of Receiving the Award(s) (MM/YYYY)"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="inv_award_start_date"/>
					<p:datePicker id="inv_award_start_date" view="month"
											title="Date of Receiving the Award(s) (MM/YYYY)" 
											label="Date of Receiving the Award(s) (MM/YYYY)" 
											value="#{manageKtFormView.selectedFormInvAward_p.start_date}" 
											pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name of Principal Inventor/ Awardee"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="inv_award_name_pi"/>
					<p:inputTextarea id="inv_award_name_pi" label="Name of Principal Inventor/ Awardee" style="width: 90%;" rows="4" counter="inv_award_name_pi_display" maxlength="500"
											value="#{manageKtFormView.selectedFormInvAward_p.name_pi}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="inv_award_name_pi_display" class="p-d-block" />
				</div>		
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name(s) of Other Inventor(s)/ Awardee(s)"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="inv_award_name_other"/>
					<p:inputTextarea id="inv_award_name_other" label="Name(s) of Other Inventor(s)/ Awardee(s)" style="width: 90%;" rows="4" counter="inv_award_name_other_display" maxlength="500"
											value="#{manageKtFormView.selectedFormInvAward_p.name_other}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="inv_award_name_other_display" class="p-d-block" />
				</div>	
			</div>
		<br/>
		<div class="form-sub-title">
			<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Patents
		</div>
		<hr style="border-top: 1px dashed #7b9d39;"/>
		<h:panelGroup  id="patentFiledPanel" >	
			<div class="riForm-item-note">
					1. Please report the number of patent applications made during the reporting year.  The patent should be wholly or partially owned by EdUHK.<br/>
					2. Please visit <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html">https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html</a> for details of the patents filed via KT Sub-office.
			</div>
			<h:outputText style="font-weight:700; font-size:18px; color:#055588; text-decoration:underline;" value="Number of Patents Filed for the Invention: "/>
			<h:outputText id="inv_award_patentFiled_num" style="font-weight:700; font-size:18px; color:#055588; text-decoration:underline;" value="#{manageKtFormView.selectedFormInvAward_p.num_pat_filed}"/>
			<p:messages for="patentFiledTable"/>	
			<p:commandButton id="btn_patentFiled_add" icon="fas fa-plus" value="Add" 
									 style="width:260px; margin-bottom:2px;"
									 action="#{manageKtFormView.addPatentFiledTableRow()}"
									 update="patentFiledTable"
									 immediate="true"/>
			<p:blockUI block="contentPanel" trigger="btn_patentFiled_add" />
			<p:dataTable id="patentFiledTable" value="#{manageKtFormView.inv_award_patentFiled_list}" 
									class="riFormTable"
									reflow="true" var="col" widgetVar="patentFiledWV"
									style="max-width:99%;"
						 			rowIndexVar="rowIndex">
						 	 		
					<p:column style="text-align:left; width:5em;">
						<f:facet name="header">No.</f:facet>
						#{rowIndex +1}							
					</p:column>
					 <p:column style="text-align:left;">
						<f:facet name="header">Name of the Invention</f:facet>
						<p:message for="patentFiledTable_name"/>
						<p:inputText id="patentFiledTable_name" title="Name of the Invention" label="Name of the Invention" style="width:90%" 
										value="#{col.name}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="400" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>
					<p:column style="text-align:left;">
						<f:facet name="header">Application Number</f:facet>
						<p:message for="patentFiledTable_num"/>
						<p:inputText id="patentFiledTable_num" title="Application Number" label="Application Number" style="width:90%" 
										value="#{col.num}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>	
					<p:column style="text-align:left;">
						<f:facet name="header">Date of Application (MM/YYYY)</f:facet>
						<p:message for="patentFiledTable_date"/>
						<p:inputMask id="patentFiledTable_date" title="Date of Application (MM/YYYY)" label="Date of Application (MM/YYYY)" style="width:90%" mask="99/9999"
										value="#{col.date}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputMask>
						<p:watermark  value="MM/YYYY" for="patentFiledTable_date" />
					</p:column>					
					<p:column style="text-align:left;">
						<f:facet name="header">Country/ Region Where the Patent is filed</f:facet>
						<p:message for="patentFiledTable_country"/>
						<p:inputText id="patentFiledTable_country" title="Country/ Region Where the Patent is filed" label="Country/ Region Where the Patent is filed" style="width:90%" 
										value="#{col.country}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>
					<p:column  class="data-noprint" style="width:2em; text-align:left;" rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtFormView.canModify == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_patentFiled_delete" action="#{manageKtFormView.deletePatentFiledTableRow(rowIndex)}" 
										rendered="#{rowIndex gt -1}"
									   update="patentFiledTable" immediate="true">
							<i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
						</p:commandLink>	
						<p:blockUI block="contentPanel" trigger="btn_patentFiled_delete" />
					</p:column>	
				</p:dataTable>
			</h:panelGroup>	
			<br/>
			<h:panelGroup  id="patentGrantedPanel" >	
				<div class="riForm-item-note">
				1. Please report the number of patents granted during the reporting year.  The patent should be wholly or partially owned by EdUHK<br/>
				2. Please visit <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html">https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html</a> for details of the patents filed via KT Sub-office.
				</div>
			<h:outputText style="font-weight:700; font-size:18px; color:#055588; text-decoration:underline;" value="Number of Patents Granted for the Invention: "/>
			<h:outputText id="inv_award_patentGranted_num" style="font-weight:700; font-size:18px; color:#055588; text-decoration:underline;" value="#{manageKtFormView.selectedFormInvAward_p.num_pat_granted}"/>
			<p:messages for="patentGrantedTable"/>	
			<p:commandButton id="btn_patentGranted_add" icon="fas fa-plus" value="Add" 
									 style="width:260px; margin-bottom:2px;"
									 action="#{manageKtFormView.addPatentGrantedTableRow()}"
									 update="patentGrantedTable"
									 immediate="true"/>
			<p:blockUI block="contentPanel" trigger="btn_patentGranted_add" />
			<p:dataTable id="patentGrantedTable" value="#{manageKtFormView.inv_award_patentGranted_list}" 
									class="riFormTable"
									reflow="true" var="col" widgetVar="patentGrantedWV"
									style="max-width:99%;"
						 			rowIndexVar="rowIndex">
						 	 		
					<p:column style="text-align:left; width:5em;">
						<f:facet name="header">No.</f:facet>
						#{rowIndex +1}							
					</p:column>
					 <p:column style="text-align:left;">
						<f:facet name="header">Name of the Patent</f:facet>
						<p:message for="patentGrantedTable_name"/>
						<p:inputText id="patentGrantedTable_name" title="Name of the Patent" label="Name of the Patent" style="width:90%" 
										value="#{col.name}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="400" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>
					<p:column style="text-align:left;">
						<f:facet name="header">Patent Number</f:facet>
						<p:message for="patentGrantedTable_num"/>
						<p:inputText id="patentGrantedTable_num" title="Patent Number" label="Patent Number" style="width:90%" 
										value="#{col.num}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>	
					<p:column style="text-align:left;">
						<f:facet name="header">Date of Patent Granted (MM/YYYY)</f:facet>
						<p:message for="patentGrantedTable_date"/>
						<p:inputMask id="patentGrantedTable_date" title="Date of Patent Granted (MM/YYYY)" label="Date of Patent Granted (MM/YYYY)" style="width:90%" mask="99/9999"
										value="#{col.date}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputMask>
						<p:watermark  value="MM/YYYY" for="patentGrantedTable_date" />
					</p:column>					
					<p:column style="text-align:left;">
						<f:facet name="header">Country/ Region Granting the Patent</f:facet>
						<p:message for="patentGrantedTable_country"/>
						<p:inputText id="patentGrantedTable_country" title="Country/ Region Granting the Patent" label="Country/ Region Granting the Patent" style="width:90%" 
										value="#{col.country}" 
										onkeypress="return (event.charCode != 189);"
										maxlength="200" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModify == false || manageKtFormView.paramDataLevel eq 'P'}">
										<p:ajax event="valueChange"/>
						</p:inputText>
					</p:column>
					<p:column  class="data-noprint" style="width:2em; text-align:left;" rendered="#{manageKtFormView.isCreator == true &amp;&amp; manageKtFormView.canModify == true &amp;&amp; manageKtFormView.paramDataLevel ne 'P'}">
						<f:facet name="header">Del.</f:facet>
						<p:commandLink id="btn_patentGranted_delete" action="#{manageKtFormView.deletePatentGrantedTableRow(rowIndex)}" 
										rendered="#{rowIndex gt -1}"
									   update="patentGrantedTable" immediate="true">
							<i class="fas fa-trash icon-action" title="#{formBundle['form.del']}"/>
						</p:commandLink>	
						<p:blockUI block="contentPanel" trigger="btn_patentGranted_delete" />
					</p:column>	
				</p:dataTable>
			</h:panelGroup>	
				<!--
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					1. Please report the number of patent applications made during the reporting year.  The patent should be wholly or partially owned by EdUHK.<br/>
					2. Please visit <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html">https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html</a> for details of the patents filed via KT Sub-office.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Number of Patents Filed for the Invention
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="inv_award_num_pat_filed"/>
						<p:inputNumber  id="inv_award_num_pat_filed" title="Number of Patents Filed for the Invention" label="Number of Patents Filed for the Invention"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormInvAward_p.num_pat_filed}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
						</p:inputNumber>
					</div>

					
					<h:panelGroup id="inv_award_num_pat_filed_panel">
					<div class="ui-g" style="#{manageKtFormView.valueGreaterThanZero(manageKtFormView.selectedFormInvAward_p.num_pat_filed)?'':'display:none;'}">
						<div class="ui-g-12 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Name of the Invention"/>
						</div>
						<div class="ui-g-12 ui-md-9 ui-lg-10">
							<p:message for="inv_award_inv_name"/>
							<p:inputTextarea id="inv_award_inv_name" label="Name of the Invention" style="width: 90%;" rows="4" counter="inv_award_inv_name_display" maxlength="500"
													value="#{manageKtFormView.selectedFormInvAward_p.inv_name}"
		                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
		                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
		                      <br/>
		      				  <h:outputText id="inv_award_inv_name_display" class="p-d-block" />
						</div>		
						
						<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Application Number
						</div>		
						<div class="ui-g-12 ui-md-9 ui-lg-10">
							<p:message for="inv_award_app_num"/>
							<p:inputNumber  id="inv_award_app_num" title="Application Number" label="Application Number"
												maxValue="9999999999" minValue="0" decimalPlaces="0"  thousandSeparator=""
												value="#{manageKtFormView.selectedFormInvAward_p.app_num}" 
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
						</div>
						
						<div class="ui-g-12 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Date of Application (MM/YYYY)"/>
						</div>
						<div class="ui-g-12 ui-md-9 ui-lg-10">
							<p:message for="inv_award_app_date"/>
							<p:datePicker id="inv_award_app_date" view="month"
													title="Date of Application (MM/YYYY)" 
													label="Date of Application (MM/YYYY)" 
													value="#{manageKtFormView.selectedFormInvAward_p.app_date}" 
													pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
						</div>	
						
						<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Country/ Region Where the Patent is filed
						</div>		
						<div class="ui-g-12 ui-md-9 ui-lg-10">
							<p:message for="inv_award_patent_filed_country"/>
							<p:inputText id="inv_award_patent_filed_country"
									label="Country/ Region Where the Patent is filed" maxlength="200"
									value="#{manageKtFormView.selectedFormInvAward_p.patent_filed_country}"
									style="width: 90%;"
									disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
						</div>
					</div>
					</h:panelGroup>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					1. Please report the number of patents granted during the reporting year.  The patent should be wholly or partially owned by EdUHK<br/>
					2. Please visit <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html">https://www.eduhk.hk/KnowledgeTransfer/en/Innovation-Showcase/Patent/Patent-Record-Search.html</a> for details of the patents filed via KT Sub-office.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Number of Patents Granted for the Invention
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="inv_award_num_pat_granted"/>
						<p:inputNumber  id="inv_award_num_pat_granted" title="Number of Patents Granted for the Invention" label="Number of Patents Granted for the Invention"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormInvAward_p.num_pat_granted}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
											<p:ajax event="change" update="inv_award_num_pat_granted_panel"/>
							</p:inputNumber>
					</div>
				</div>
				<h:panelGroup id="inv_award_num_pat_granted_panel">
				<div class="ui-g" style="#{manageKtFormView.valueGreaterThanZero(manageKtFormView.selectedFormInvAward_p.num_pat_granted)?'':'display:none;'}">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Name of the Patent
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="inv_award_patent_name"/>
						<p:inputTextarea id="inv_award_patent_name" label="Name of the Patent" style="width: 90%;" rows="4" counter="inv_award_patent_name_display" maxlength="500"
												value="#{manageKtFormView.selectedFormInvAward_p.patent_name}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="inv_award_patent_name_display" class="p-d-block" />
					</div>		
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Patent Number
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="inv_award_patent_num"/>
						<p:inputNumber  id="inv_award_patent_num" title="Patent Number" label="Patent Number"
											maxValue="9999999999" minValue="0" decimalPlaces="0" thousandSeparator=""
											value="#{manageKtFormView.selectedFormInvAward_p.patent_num}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Date of Patent Granted (MM/YYYY)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="inv_award_patent_date"/>
						<p:datePicker id="inv_award_patent_date" view="month"
												title="Date of Patent Granted (MM/YYYY)" 
												label="Date of Patent Granted (MM/YYYY)" 
												value="#{manageKtFormView.selectedFormInvAward_p.patent_date}" 
												pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Country/ Region Granting the Patent
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="inv_award_patent_grant_country"/>
						<p:inputText id="inv_award_patent_grant_country"
								label="Country/ Region Granting the Patent" maxlength="200"
								value="#{manageKtFormView.selectedFormInvAward_p.patent_grant_country}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
				</div>
				</h:panelGroup>-->
				
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<!-- Remarks -->
			<div class="ui-g">
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Remarks
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="inv_award_remarks_staff"/>
					<p:inputTextarea id="inv_award_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="inv_award_remarks_staff_display" maxlength="2000"
											value="#{manageKtFormView.selectedFormInvAward_p.remarks_staff}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="inv_award_remarks_staff_display" class="p-d-block" />
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					Note (RDO)
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="inv_award_remarks_kt"/>
					<p:inputTextarea id="inv_award_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="inv_award_remarks_kt_display" maxlength="2000"
											value="#{manageKtFormView.selectedFormInvAward_p.remarks_kt}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="inv_award_remarks_kt_display" class="p-d-block" />
				</div>	
			</div>
		<hr style="border-top: 1px dashed #7b9d39;"/>
		</ui:fragment>			
		
	</ui:composition>

</html>
