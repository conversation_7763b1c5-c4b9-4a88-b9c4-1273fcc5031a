<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.cons']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormCons_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormCons_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please report this project in either A1 (Contract Research) or A2 (Collaborative Research Projects).<br/>
				If no, please continue to complete "Is It an Educational Course/ Programme?"
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Any Research Element in the Project?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="cons_research_element"/>
					<p:selectOneRadio id="cons_research_element" title="Any Research Element in the Project?" label="Any Research Element in the Project?" value="#{manageKtFormView.selectedFormCons_p.research_element}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio" 
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please report this item in A8 (CPD Courses).<br/>
				If no, please continue to complete "Project Title".
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Is It an Educational Course/ Programme?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="cons_edu_crse"/>
					<p:selectOneRadio id="cons_edu_crse" title="Is It an Educational Course/ Programme?" label="Is It an Educational Course/ Programme?" value="#{manageKtFormView.selectedFormCons_p.edu_crse}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Project Title"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_title"/>	
						<p:inputTextarea id="cons_title" label="Project Title" style="width: 90%;" rows="4" counter="cons_title_display" maxlength="500"
												value="#{manageKtFormView.selectedFormCons_p.title}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cons_title_display" class="p-d-block" />					
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.act.code']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_act_code"/>
						<p:inputText id="cons_act_code"
										label="#{formBundle['form.kt.act.code']}" maxlength="50"
										value="#{manageKtFormView.selectedFormCons_p.act_code}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.start']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="cons_start_date"/>
						<p:datePicker id="cons_start_date" view="date"
												title="#{formBundle['form.kt.date.start']}" 
												label="#{formBundle['form.kt.date.start']}" 
												value="#{manageKtFormView.selectedFormCons_p.start_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
								<p:ajax event="change" update="cons_count_project_day cons_count_project_day_in_year cons_income_rpt_unit"/>
						</p:datePicker>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.end']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="cons_end_date"/>
						<p:datePicker id="cons_end_date" view="date"
												title="#{formBundle['form.kt.date.end']}" 
												label="#{formBundle['form.kt.date.end']}" 
												value="#{manageKtFormView.selectedFormCons_p.end_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
								<p:ajax event="change" update="cons_count_project_day cons_count_project_day_in_year cons_income_rpt_unit"/>
							</p:datePicker>
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Project Days"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						 <h:outputText id="cons_count_project_day" value="#{manageKtFormView.countProjectDay(manageKtFormView.selectedFormCons_p.start_date, manageKtFormView.selectedFormCons_p.end_date)}" />
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Project Days in the Reporting Year"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<h:outputText id="cons_count_project_day_in_year" value="#{manageKtFormView.countProjectDayInYear(manageKtFormView.selectedFormCons_p.start_date, manageKtFormView.selectedFormCons_p.end_date)}" />
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Principal Investigator (e.g. Dr. CHAN Tai Man)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_principal_inves"/>
						<p:inputTextarea id="cons_principal_inves" label="Principal Investigator" style="width: 90%;" rows="4" counter="cons_principal_inves_display" maxlength="500"
												value="#{manageKtFormView.selectedFormCons_p.principal_inves}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cons_principal_inves_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Name of Funding Source
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_fund_src"/>
						<p:inputText id="cons_fund_src"
										label="Name of Funding Source" maxlength="500"
										value="#{manageKtFormView.selectedFormCons_p.fund_src}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If Non-HK Fund is selected, please jump to "Type of Organisation Providing the Funding".
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							HK Fund/ Non-HK Fund?
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_hk_fund"/>
						<p:selectOneRadio id="cons_hk_fund" title="HK Fund/ Non-HK Fund?" label="HK Fund/ Non-HK Fund?" value="#{manageKtFormView.selectedFormCons_p.hk_fund}" 
												unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cons_hkFundList}"/> 
						</p:selectOneRadio>	
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If HK Government/ Government-related Fund is selected, please jump to "Joint Consultancy Project?"
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							HK Government Fund/ HK Private Fund?
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_hk_gov_fund"/>
						<p:selectOneRadio id="cons_hk_gov_fund" title="HK Government Fund/ HK Private Fund?" label="HK Government Fund/ HK Private Fund?" value="#{manageKtFormView.selectedFormCons_p.hk_gov_fund}" 
												unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cons_hkGovFundList}"/> 
						</p:selectOneRadio>	
					</div>	
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
						Please jump to “Joint Consultancy Project?” after completing this item.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Which type of HK Private Fund?
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_hk_pri_fund"/>
						<p:selectOneRadio id="cons_hk_pri_fund" title="Which type of HK Private Fund?" label="Which type of HK Private Fund?" value="#{manageKtFormView.selectedFormCons_p.hk_pri_fund}" 
												unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cons_hkPriFundList}"/> 
						</p:selectOneRadio>	
					</div>	

					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Type of Organization Providing the Funding 
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_fund_src_org"/>
						<p:selectOneRadio id="cons_fund_src_org" title="Type of Organization Providing the Funding" label="Type of Organization Providing the Funding" value="#{manageKtFormView.selectedFormCons_p.fund_src_org}" 
												unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cons_orgTypeList}"/>    
						</p:selectOneRadio>
					</div>	
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If it is not a joint consultancy project, please jump to "Total Approved Budget".
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Joint Consultancy Project?
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_joint_proj"/>
						<p:selectOneRadio id="cons_joint_proj" title="Joint Consultancy Project?" label="Joint Consultancy Project?" value="#{manageKtFormView.selectedFormCons_p.joint_proj}" 
												unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cons_jointProjList}"/> 
						</p:selectOneRadio>	
					</div>	
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If EdUHK is the coordinating party of the project, please jump to "Total Approved Budget".
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Role of EdUHK
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_eduhk_role"/>
						<p:selectOneRadio id="cons_eduhk_role" title="Role of EdUHK" label="Role of EdUHK" value="#{manageKtFormView.selectedFormCons_p.eduhk_role}" 
												unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.cons_roleList}"/>    
						</p:selectOneRadio>
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Name of the Coordinating Institution
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_name_coor"/>
						<p:inputText id="cons_name_coor"
										label="Name of the Coordinating Institution" maxlength="500"
										value="#{manageKtFormView.selectedFormCons_p.name_coor}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Funding Source of the Coordinating Institution
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_fund_src_coor"/>
						<p:inputText id="cons_fund_src_coor"
										label="Funding Source of the Coordinating Institution" maxlength="500"
										value="#{manageKtFormView.selectedFormCons_p.fund_src_coor}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Project Title of the Coordinating Institution
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_proj_title_coor"/>
						<p:inputTextarea id="cons_proj_title_coor" label="Principal Investigator of the Coordinating Institution" style="width: 90%;" rows="4" counter="cons_proj_title_coor_display" maxlength="500"
												value="#{manageKtFormView.selectedFormCons_p.proj_title_coor}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cons_proj_title_coor_display" class="p-d-block" />
					</div>		
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Principal Investigator of the Coordinating Institution
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_pi_coor"/>
						<p:inputTextarea id="cons_pi_coor" label="Principal Investigator of the Coordinating Institution" style="width: 90%;" rows="4" counter="cons_pi_coor_display" maxlength="500"
												value="#{manageKtFormView.selectedFormCons_p.pi_coor}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cons_pi_coor_display" class="p-d-block" />
					</div>	
					
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Total Approved Budget (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_budget"/>
						<p:inputNumber  id="cons_budget" title="Total Approved Budget (HK$)" label="Total Approved Budget (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCons_p.budget}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">		
											<p:ajax event="change" update="cons_income_rpt_unit"/>
						</p:inputNumber>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_income_rpt_unit"/>
						<p:inputNumber  id="cons_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.countIncome(manageKtFormView.selectedFormCons_p.start_date, manageKtFormView.selectedFormCons_p.end_date, manageKtFormView.selectedFormCons_p.budget)}" 
											disabled="true"/>		
					</div>

					<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:outputLabel class="riForm-item-title" value="Supported by FO?"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">	
						<p:message for="cons_support_fo"/>
						<p:selectOneRadio id="cons_support_fo" title="Supported by FO?" label="Supported by FO?" value="#{manageKtFormView.selectedFormCons_p.support_fo}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Support" itemValue="Y"/>
							<f:selectItem itemLabel="Not Support" itemValue="N"/>	   
						</p:selectOneRadio>
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Cumulative Income (HK$) (FO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cons_cumu_income_fo"/>
						<p:inputNumber  id="cons_cumu_income_fo" title="Cumulative Income (HK$) (FO)" label="Cumulative Income (HK$) (FO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCons_p.cumu_income_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income Remarks From FO
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cons_income_fo_rem"/>
						<p:inputText id="cons_income_fo_rem"
								label="Income Remarks From FO" maxlength="200"
								value="#{manageKtFormView.selectedFormCons_p.income_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income Matching Grant
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cons_income_grant"/>
						<p:inputText id="cons_income_grant"
								label="Income Matching Grant" maxlength="200"
								value="#{manageKtFormView.selectedFormCons_p.income_grant}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cons_income_rdo"/>
						<p:inputNumber  id="cons_income_rdo" title="Income (HK$) (RDO)" label="Income (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormCons_p.income_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
					<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
					<div class="riForm-item-note">
					Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the number of external parties (other than the funding body) that are engaged in the project.  Key partners can be individuals or organisations.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Key Partners
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cons_num_key_partner"/>
						<p:inputNumber  id="cons_num_key_partner" title="Number of Key Partners" label="Number of Key Partners"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCons_p.num_key_partner}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If a person has multiple roles as teacher/ principal/ other stakeholder, please avoid double-counting that person for the number of teacher/ principal/ other stakeholder. If vice-principals/ school management committee members were benefited from the advisory/ consultancy services, please count them under the number of principal.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Teachers Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cons_num_teacher"/>
						<p:inputNumber  id="cons_num_teacher" title="Number of Teachers Benefited" label="Number of Teachers Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCons_p.num_teacher}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Principal Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cons_num_principal"/>
						<p:inputNumber  id="cons_num_principal" title="Number of Principal Benefited" label="Number of Principal Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCons_p.num_principal}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cons_num_stakeholder"/>
						<p:inputNumber  id="cons_num_stakeholder" title="Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)" label="Number of Other Stakeholders Benefited (e.g. parents, residents, general public, workers)"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCons_p.num_stakeholder}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Students Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cons_num_stu"/>
						<p:inputNumber  id="cons_num_stu" title="Number of Students Benefited" label="Number of Students Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCons_p.num_stu}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Schools Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="cons_num_school"/>
						<p:inputNumber  id="cons_num_school" title="Number of Schools Benefited" label="Number of Schools Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormCons_p.num_school}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="cons_remarks_staff"/>
						<p:inputTextarea id="cons_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="cons_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormCons_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cons_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="cons_remarks_kt"/>
						<p:inputTextarea id="cons_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="cons_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormCons_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="cons_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
