<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment>
			<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
							 widgetVar="outputDownloadBtn"
							 ajax="false">
                   <p:dataExporter type="xlsx" target="outputDataTable" fileName="outputData" options="#{ktListingView.excelOpt}" 
                   				   postProcessor="#{ktListingView.postPrsc}"/>
               </p:commandButton>
			<p:dataTable id="outputDataTable"
						 value="#{ktListingView.getKtActivitiesList()}" 
						 var="act"
						 stripedRows="true" size="small" style="font-size:14px;"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                 paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                 rows="30"
		                 rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
	                	 tableStyle="table-layout:auto;"
				 		 >
				
				<p:column width="3em;">
					<f:facet name="header">KT Activity No.</f:facet>
					<h:outputText value="#{act.form_no}" />
				</p:column>
				
				<p:column style="min-width:20em;" >
					<f:facet name="header">Contributor List</f:facet>
					<h:outputText value="#{act.authorList}" escape="false" />
				</p:column>
				
				<p:column style="min-width:8em;">
					<f:facet name="header">Faculty</f:facet>
					<h:outputText value="#{act.fac}" />
				</p:column>
				
				<p:column style="min-width:8em;">
					<f:facet name="header">Department</f:facet>
					<h:outputText value="#{act.dept}" />
				</p:column>
				
				<p:column style="min-width:15em;">
					<f:facet name="header">Project Title</f:facet>
					<h:outputText value="#{act.title}" />
				</p:column>
				
				<p:column style="min-width:8em;">
					<f:facet name="header">Activity / Project Code</f:facet>
					<h:outputText value="#{act.act_code}" />
				</p:column>
				
				<p:column width="8em;">
					<f:facet name="header">Start Date</f:facet>
					<h:outputText value="#{act.start_date}" />
				</p:column>
				
				<p:column width="8em;">
					<f:facet name="header">End Date</f:facet>
					<h:outputText value="#{act.end_date}" />
				</p:column>
				
				<p:column width="10em;">
					<f:facet name="header">Principal Investigator</f:facet>
					<h:outputText value="#{act.principal_inves}" />
				</p:column>
				
				<p:column width="10em;">
					<f:facet name="header">Name of Funding Source</f:facet>
					<h:outputText value="#{act.fund_src}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Type of Funding Source</f:facet>
					<h:outputText value="#{act.fund_src_type}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Type of Organization Providing the Funding</f:facet>
					<h:outputText value="#{act.fund_src_org}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Role of EdUHK: Coordinating / Participating (C/P)</f:facet>
					<h:outputText value="#{act.eduhk_role}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Total Approved Budget</f:facet>
					<h:outputText value="#{act.budget}" />
				</p:column>
				
				<p:column width="10em;">
					<f:facet name="header">Income</f:facet>
					<h:outputText value="#{act.income_rpt_unit}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Income (FO)</f:facet>
					<h:outputText value="#{act.income_fo}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Cumulative Income (FO)</f:facet>
					<h:outputText value="#{act.cumu_income_fo}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Income (Remarks fr FO)</f:facet>
					<h:outputText value="#{act.income_fo_rem}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Income (Matching Grant)</f:facet>
					<h:outputText value="#{act.income_grant}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Income (RDO)</f:facet>
					<h:outputText value="#{act.income_rdo}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Key Partners</f:facet>
					<h:outputText value="#{act.num_key_partner}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Teacher Participants</f:facet>
					<h:outputText value="#{act.num_teacher}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Principal Participants</f:facet>
					<h:outputText value="#{act.num_principal}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Other Stakeholders Benefited (e.g. parents)</f:facet>
					<h:outputText value="#{act.num_stakeholder}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Schools Benefited</f:facet>
					<h:outputText value="#{act.num_school}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Organizations Benefited</f:facet>
					<h:outputText value="#{act.num_org}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Advisory Bodies Benefited</f:facet>
					<h:outputText value="#{act.num_adv_body}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Students Benefited</f:facet>
					<h:outputText value="#{act.num_stu}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of External Professionals Engaged</f:facet>
					<h:outputText value="#{act.num_ext_prof}" />
				</p:column>
				
			</p:dataTable>
		</ui:fragment>			
		
	</ui:composition>

</html>
