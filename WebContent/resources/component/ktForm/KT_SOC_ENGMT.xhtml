<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.soc.engmt']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormSocEngmt_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormSocEngmt_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please report this activity in A6 (Entrepreneurship Activity).<br/>
				If no, please continue to complete "Performance Arts".
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Entrepreneurship Element?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="soc_engmt_ent_element"/>
					<p:selectOneRadio id="soc_engmt_ent_element" title="Entrepreneurship Element?" label="Entrepreneurship Element?" value="#{manageKtFormView.selectedFormSocEngmt_p.ent_element}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please continue to complete "Title of the Event"<br/>
				If no, please report this activity in A9 (Public Dissemination and Speeches).
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Performance Arts?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="soc_engmt_perf_art"/>
					<p:selectOneRadio id="soc_engmt_perf_art" title="Performance Arts?" label="Performance Arts?" value="#{manageKtFormView.selectedFormSocEngmt_p.perf_art}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Title of the Event"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="soc_engmt_title"/>
						<p:inputTextarea id="soc_engmt_title" label="Title of the Event" style="width: 90%;" rows="4" counter="soc_engmt_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormSocEngmt_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="soc_engmt_title_display" class="p-d-block" />			
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.act.code']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="soc_engmt_act_code"/>
						<p:inputText id="soc_engmt_act_code"
										label="#{formBundle['form.kt.act.code']}" maxlength="50"
										value="#{manageKtFormView.selectedFormSocEngmt_p.act_code}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please input "0" if the activity has no sub-sessions.			
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
					    <p:outputLabel class="riForm-item-title" value="Number of Sub-sessions"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
					    <p:message for="soc_engmt_num_subsessions"/>	
					    <p:inputNumber id="soc_engmt_num_subsessions" 
						               label="Number of Sub-sessions" 
						               inputStyle="width:30%"
						               value="#{manageKtFormView.selectedFormSocEngmt_p.num_subsessions}"
						               disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"
						               integerOnly="true" 
						               maxValue="9999999999" minValue="0" decimalPlaces="0"/>
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
					    <p:outputLabel class="riForm-item-title" value="Principal Investigator/Person in charge"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
					    <p:message for="soc_engmt_pi"/>	
					    <p:inputText id="soc_engmt_pi" label="Principal Investigator/Person in charge" style="width: 90%;"
					                value="#{manageKtFormView.selectedFormSocEngmt_p.pi}"
					                disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Budget Holder"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="soc_engmt_budget_holder"/>
						<p:inputText id="soc_engmt_budget_holder"
										label="Budget Holder" maxlength="200"
										value="#{manageKtFormView.selectedFormSocEngmt_p.budget_holder}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	
				
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Mode"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="soc_engmt_act_mode"/>
						<p:selectOneRadio id="soc_engmt_act_mode" title="Mode" label="Mode" value="#{manageKtFormView.selectedFormSocEngmt_p.act_mode}"
													unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.soc_engmt_modeList}"/>
						</p:selectOneRadio>
					</div>	
				
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.start']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="soc_engmt_start_date"/>
						<p:datePicker id="soc_engmt_start_date" view="date"
												title="#{formBundle['form.kt.date.start']}" 
												label="#{formBundle['form.kt.date.start']}" 
												value="#{manageKtFormView.selectedFormSocEngmt_p.start_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
												<p:ajax event="change" update="soc_engmt_count_project_day soc_engmt_count_project_day_in_year soc_engmt_income_rpt_unit"/>
						</p:datePicker>				
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.end']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="soc_engmt_end_date"/>
						<p:datePicker id="soc_engmt_end_date" view="date"
												title="#{formBundle['form.kt.date.end']}" 
												label="#{formBundle['form.kt.date.end']}" 
												value="#{manageKtFormView.selectedFormSocEngmt_p.end_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
												<p:ajax event="change" update="soc_engmt_count_project_day soc_engmt_count_project_day_in_year soc_engmt_income_rpt_unit"/>
						</p:datePicker>		
					</div>	
					
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Activity Days"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						 <h:outputText id="soc_engmt_count_project_day" value="#{manageKtFormView.countProjectDay(manageKtFormView.selectedFormSocEngmt_p.start_date, manageKtFormView.selectedFormSocEngmt_p.end_date)}" />
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Total Number of Activity Days in the Reporting Year"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<h:outputText id="soc_engmt_count_project_day_in_year" value="#{manageKtFormView.countProjectDayInYear(manageKtFormView.selectedFormSocEngmt_p.start_date, manageKtFormView.selectedFormSocEngmt_p.end_date)}" />
					</div>
						
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="EdUHK Organizer?"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="soc_engmt_eduhk_org"/>
						<p:selectOneRadio id="soc_engmt_eduhk_org" title="EdUHK Organizer?" label="EdUHK Organizer?" value="#{manageKtFormView.selectedFormSocEngmt_p.eduhk_org}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Yes" itemValue="Y"/>
							<f:selectItem itemLabel="No" itemValue="N"/>	   
						</p:selectOneRadio>
					</div>	
				
					<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Local/ National/ International"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="soc_engmt_region"/>
						<p:selectOneRadio id="soc_engmt_region" title="Local/ National/ International" label="Local/ National/ International" value="#{manageKtFormView.selectedFormSocEngmt_p.region}"
													unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Local" itemValue="L"/>
							<f:selectItem itemLabel="National" itemValue="N"/>
							<f:selectItem itemLabel="International" itemValue="I"/>	   
						</p:selectOneRadio>
					</div>		
				
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Free / Chargeable"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="soc_engmt_free_charge"/>
						<p:selectOneRadio id="soc_engmt_free_charge" title="Free / Chargeable" label="Free / Chargeable" value="#{manageKtFormView.selectedFormSocEngmt_p.free_charge}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false}">
							<f:selectItem itemLabel="Free" itemValue="F"/>
							<f:selectItem itemLabel="Chargeable" itemValue="C"/>	   
						</p:selectOneRadio>
					</div>		
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">	
					If no, please jump to "Expenditure".
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Budget Approval Sought for the Activity?"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
						<p:message for="soc_engmt_budget_approval"/>
						<p:selectOneRadio id="soc_engmt_budget_approval" title="Budget Approval Sought for the Activity?" label="Budget Approval Sought for the Activity?" value="#{manageKtFormView.selectedFormSocEngmt_p.budget_approval}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Yes" itemValue="Y"/>
							<f:selectItem itemLabel="No" itemValue="N"/>	   
							<p:ajax event="change" update="soc_engmt_income_rpt_unit soc_engmt_budget_panel"/>
						</p:selectOneRadio>
					</div>		
				</div>
				<h:panelGroup id="soc_engmt_budget_panel">
					<div class="ui-g" style="#{manageKtFormView.showField(manageKtFormView.selectedFormSocEngmt_p.budget_approval) ?'':'display:none;'}">
						<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
							Total Approved Budget (HK$)
						</div>		
						<div class="ui-g-12 ui-md-9 ui-lg-10">
							<p:message for="soc_engmt_budget"/>
							<p:inputNumber  id="soc_engmt_budget" title="Total Approved Budget (HK$)" label="Total Approved Budget (HK$)" symbol="$"
												maxValue="9999999999" minValue="0" decimalPlaces="2" 
												value="#{manageKtFormView.selectedFormSocEngmt_p.budget}" 
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
								<p:ajax event="change" update="soc_engmt_income_rpt_unit"/>
							</p:inputNumber>
						</div>
					</div>
					</h:panelGroup>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:inputNumber  id="soc_engmt_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.countIncomeWithFilter(manageKtFormView.selectedFormSocEngmt_p.start_date, manageKtFormView.selectedFormSocEngmt_p.end_date, manageKtFormView.selectedFormSocEngmt_p.budget, manageKtFormView.selectedFormSocEngmt_p.expnd_rpt_unit, manageKtFormView.selectedFormSocEngmt_p.budget_approval)}" 
											disabled="true"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (FO/ Budget Holder) 
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_income_fo"/>
						<p:inputNumber  id="soc_engmt_income_fo" title="Income (HK$) (FO/ Budget Holder) " label="Income (HK$) (FO/ Budget Holder) " symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.income_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (Remarks From FO/ Budget Holder)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_income_fo_rem"/>
						<p:inputText id="soc_engmt_income_fo_rem"
								label="Income (Remarks From FO/ Budget Holder)" maxlength="200"
								value="#{manageKtFormView.selectedFormSocEngmt_p.income_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	

					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_income_rdo"/>
						<p:inputNumber  id="soc_engmt_income_rdo" title="Income (HK$) (RDO)" label="Income (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.income_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-sack-dollar" style="margin-right:5px;"></i>Expenditure
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Only direct costs arising from the reported events/activities should be included. For example, costs directly related to the organization of the events (e.g. wages of staff hired for organizing the events, rentals of outside venues/facilities, etc.) should be included. Overhead expenses incurred regardless of the occurrence of the reported events should be excluded.					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Total Expenditure from the Event (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="soc_engmt_expnd_rpt_unit"/>
						<p:inputNumber  id="soc_engmt_expnd_rpt_unit" title="Total Expenditure from the Event (HK$)" label="Total Expenditure from the Event (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.expnd_rpt_unit}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
											<p:ajax event="change" update="soc_engmt_income_rpt_unit"/>
						</p:inputNumber>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Total Expenditure from the Event (HK$) (FO/ Budget Holder) 
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_expnd_fo"/>
						<p:inputNumber  id="soc_engmt_expnd_fo" title="Total Expenditure from the Event (HK$) (FO/ Budget Holder) " label="Total Expenditure from the Event (HK$) (FO/ Budget Holder) " symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.expnd_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Total Expenditure from the Event (HK$) (Remarks from FO/ Budget Holder) 
					</div>	
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_expnd_fo_rem"/>
						<p:inputText id="soc_engmt_expnd_fo_rem"
								label="Total Expenditure from the Event (HK$) (Remarks from FO/ Budget Holder) " maxlength="200"
								value="#{manageKtFormView.selectedFormSocEngmt_p.expnd_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Total Expenditure from the Event (HK$) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_expnd_rdo"/>
						<p:inputNumber  id="soc_engmt_expnd_rdo" title="Total Expenditure from the Event (HK$) (RDO)" label="Total Expenditure from the Event (HK$) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.expnd_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
					<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
					<div class="riForm-item-note">
					Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					This refers to the number of external parties (other than the funding body) that are engaged in the project.  Key partners can be individuals or organisations.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Key Partners
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_key_partner"/>
						<p:inputNumber  id="soc_engmt_num_key_partner" title="Number of Key Partners" label="Number of Key Partners"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_key_partner}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					
					<div class="ui-g-12 ui-md-12 ui-lg-6"></div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please input "0" if the activity has no speakers.				
					</div>
					
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Speakers
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_speakers"/>
						<p:inputNumber  id="soc_engmt_num_speakers" title="Number of Speakers" label="Number of Speakers"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_speakers}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-6"></div>
					
					
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Activities organised solely for participation of own and/or other higher education institution(s) should not be reported.
					</div>
					<div class="ui-g-4 ui-md-3 ui-lg-3">
							<p:outputLabel class="riForm-item-title" value="Target Participants"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-9">	
					<p:message for="soc_engmt_target_pax"/>
						<p:selectOneRadio id="soc_engmt_target_pax" title="Target Participants" label="Target Participants" value="#{manageKtFormView.selectedFormSocEngmt_p.target_pax}"
													unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.soc_engmt_targetPaxList}"/>
						</p:selectOneRadio>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Teacher Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_teacher"/>
						<p:inputNumber  id="soc_engmt_num_teacher" title="Number of Teacher Participants" label="Number of Teacher Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_teacher}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Principal Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_principal"/>
						<p:inputNumber  id="soc_engmt_num_principal" title="Number of Principal Participants" label="Number of Principal Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_principal}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Other Participants (e.g. Parents)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_other"/>
						<p:inputNumber  id="soc_engmt_num_other" title="Number of Other Participants (e.g. Parents)" label="Number of Other Participants (e.g. Parents)"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_other}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Student Participants
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_stu"/>
						<p:inputNumber  id="soc_engmt_num_stu" title="Number of Student Participants" label="Number of Student Participants"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_stu}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Schools Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="soc_engmt_num_school"/>
						<p:inputNumber  id="soc_engmt_num_school" title="Number of Schools Benefited" label="Number of Schools Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormSocEngmt_p.num_school}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="soc_engmt_remarks_staff"/>
						<p:inputTextarea id="soc_engmt_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="soc_engmt_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormSocEngmt_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="soc_engmt_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="soc_engmt_remarks_kt"/>
						<p:inputTextarea id="soc_engmt_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="soc_engmt_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormSocEngmt_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="soc_engmt_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
