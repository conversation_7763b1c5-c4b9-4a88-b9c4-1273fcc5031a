<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.ip']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormIP_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormIP_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Product/ IP Title"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_title"/>
						<p:inputTextarea id="ip_title" label="Product/ IP Title" style="width: 90%;" rows="4" counter="ip_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormIP_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="ip_title_display" class="p-d-block" />							
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Category of IP"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:message for="ip_cat"/>
						<p:selectOneRadio  id="ip_cat" title="Category of IP" label="Category of IP" value="#{manageKtFormView.selectedFormIP_p.cat}" unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.ip_catList}"/>
						</p:selectOneRadio >	
					</div>	
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Software License"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ip_software_lic"/>
						<p:selectOneRadio id="ip_software_lic" title="Software License" label="Software License" value="#{manageKtFormView.selectedFormIP_p.software_lic}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="Yes" itemValue="Y"/>
							<f:selectItem itemLabel="No" itemValue="N"/>	   
						</p:selectOneRadio>
					</div>		
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-certificate" style="margin-right:5px;"></i>Licensing Period
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.start']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="ip_start_date"/>
						<p:datePicker id="ip_start_date" view="date"
												title="#{formBundle['form.kt.date.start']}" 
												label="#{formBundle['form.kt.date.start']}" 
												value="#{manageKtFormView.selectedFormIP_p.start_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">		
												<p:ajax event="change" update="ip_income_rpt_unit"/>
						</p:datePicker>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.end']}"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="ip_end_date"/>
						<p:datePicker id="ip_end_date" view="date"
												title="#{formBundle['form.kt.date.end']}" 
												label="#{formBundle['form.kt.date.end']}" 
												value="#{manageKtFormView.selectedFormIP_p.end_date}" 
												pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">		
												<p:ajax event="change" update="ip_income_rpt_unit"/>
						</p:datePicker>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Name of EdUHK Staff who Developed the Product/ IP (e.g. Dr. CHAN Tai Man)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_staff_name"/>
						<p:inputTextarea id="ip_staff_name" label="Name of EdUHK Staff who Developed the Product/ IP (e.g. Dr. CHAN Tai Man)" style="width: 90%;" rows="4" counter="ip_staff_name_display" maxlength="4000"
												value="#{manageKtFormView.selectedFormIP_p.staff_name}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="ip_staff_name_display" class="p-d-block" />
					</div>	
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-certificate" style="margin-right:5px;"></i>Source of Licensing Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-4 ui-md-3 ui-lg-2">
							<p:outputLabel class="riForm-item-title" value="Name of Licensee"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ip_income_name"/>
					<p:inputText id="ip_income_name"
										label="Name of Licensee" maxlength="1000"
										value="#{manageKtFormView.selectedFormIP_p.income_name}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
					</div>		
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If Non-HK Licensee is selected, please jump to "Category of Licensee"
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="HK Licensee/ Non-HK Licensee?"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:message for="ip_hk_lic"/>
						<p:selectOneRadio  id="ip_hk_lic" title="HK Licensee/ Non-HK Licensee?" label="HK Licensee/ Non-HK Licensee?" value="#{manageKtFormView.selectedFormIP_p.hk_lic}" 
													unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.ip_hkLicList}"/>
						</p:selectOneRadio >	
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If HK Government/ Government-related Sector is selected, please jump to "Total Amount of Upfront Received"
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="HK Government/ HK Private Sector?"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:message for="ip_hk_gov"/>
						<p:selectOneRadio  id="ip_hk_gov" title="HK Government/ HK Private Sector?" label="HK Government/ HK Private Sector?" value="#{manageKtFormView.selectedFormIP_p.hk_gov}" 
													unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.ip_hkGovList}"/>
						</p:selectOneRadio >	
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					If HK Charities/ Foundations is selected, please jump to "Total Amount of Upfront Incurred"<br/>
					If HK Industries is selected, please continue to complete "Category of Licensee"<br/>
					If Other HK Private Sector is selected, please jumpt to "Total Amount of Upfront Incurred"
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Which HK Private Sector?"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:message for="ip_hk_pri"/>
						<p:selectOneRadio  id="ip_hk_pri" title="Which HK Private Sector?" label="Which HK Private Sector?" value="#{manageKtFormView.selectedFormIP_p.hk_pri}" 
													unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.ip_hkPriList}"/>
						</p:selectOneRadio >	
					</div>	
					
					<!--  <div class="ui-g-12 ui-md-12 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Source of Income (Category)"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:message for="ip_income_cat"/>
						<p:selectOneRadio  id="ip_income_cat" title="Source of Income (Category)" label="Source of Income (Category)" value="#{manageKtFormView.selectedFormIP_p.income_cat}" 
													unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.ip_fundSrcList}"/>
						</p:selectOneRadio >	
					</div>	-->
					
					<div class="ui-g-12 ui-md-12 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Category of Licensee"/>
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-10">
						<p:message for="ip_org_type"/>
						<p:selectOneRadio  id="ip_org_type" title="Category of Licensee" label="Category of Licensee" value="#{manageKtFormView.selectedFormIP_p.org_type}" unselectable="true" layout="grid" columns="1" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItems value="#{manageKtFormView.ip_orgTypeList}"/>  
						</p:selectOneRadio >	
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please report the total amount of upfront incurred during the reporting year.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Total Amount of Upfront Incurred
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_amt_upfront"/>
						<p:inputNumber  id="ip_amt_upfront" title="Total Amount of Upfront Incurred" label="Total Amount of Upfront Incurred" symbol="$"
											maxValue="**********" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormIP_p.amt_upfront}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
											<p:ajax event="change" update="ip_income_rpt_unit"/>
						</p:inputNumber>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please report the total amount of royalty incurred during the reporting year.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Total Amount of Royalty Incurred
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_amt_royalty"/>
						<p:inputNumber  id="ip_amt_royalty" title="Total Amount of Royalty Incurred" label="Total Amount of Royalty Incurred" symbol="$"
											maxValue="**********" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormIP_p.amt_royalty}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
											<p:ajax event="change" update="ip_income_rpt_unit"/>
						</p:inputNumber>	
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please input debit note number(s) for the upfront and royalty received during the reporting year.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Debit Note Number(s)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_debit_note"/>
						<p:inputTextarea id="ip_debit_note" label="Debit Note Number(s)" style="width: 90%;" rows="4" counter="ip_debit_note_display" maxlength="500"
												value="#{manageKtFormView.selectedFormIP_p.debit_note}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="ip_debit_note_display" class="p-d-block" />
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_income_rpt_unit"/>
						<p:inputNumber  id="ip_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="**********" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.countIncome2(manageKtFormView.selectedFormIP_p.amt_upfront, manageKtFormView.selectedFormIP_p.amt_royalty)}" 
											disabled="true"/>		
					</div>
				</div>
				<br/>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="ip_remarks_staff"/>
						<p:inputTextarea id="ip_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="ip_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormIP_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="ip_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ip_remarks_kt"/>
						<p:inputTextarea id="ip_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="ip_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormIP_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="ip_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
