<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment rendered="#{manageKtFormView.paramFormCode eq bundle['kt.form.prof.engmt']}">
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormProfEngmt_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormProfEngmt_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Programme Title"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="prof_engmt_title"/>
						<p:inputTextarea id="prof_engmt_title" label="Programme Title" style="width: 90%;" rows="4" counter="prof_engmt_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormProfEngmt_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="prof_engmt_title_display" class="p-d-block" />		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Name of EdUHK Staff Conducting the Programme (e.g. Dr. CHAN Tai Man)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="prof_engmt_cond_staff"/>
						<p:inputText id="prof_engmt_cond_staff"
										label="Name of EdUHK Staff Conducting the Programme (e.g. Dr. CHAN Tai Man)" maxlength="500"
										value="#{manageKtFormView.selectedFormProfEngmt_p.cond_staff}"
										style="width: 90%;"
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Start Date (MM/YYYY)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="prof_engmt_start_date"/>
						<p:datePicker id="prof_engmt_start_date" view="month"
												title="Start Date (MM/YYYY)" 
												label="Start Date (MM/YYYY)" 
												value="#{manageKtFormView.selectedFormProfEngmt_p.start_date}" 
												pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>			
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="End Date (MM/YYYY)"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-4">
						<p:message for="prof_engmt_end_date"/>
						<p:datePicker id="prof_engmt_end_date" view="month"
												title="End Date (MM/YYYY)" 
												label="End Date (MM/YYYY)" 
												value="#{manageKtFormView.selectedFormProfEngmt_p.end_date}" 
												pattern="MM/yyyy" yearNavigator="true" yearRange="2010:2050"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>			
					</div>	
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Please only include those Continuing Professional Development courses which are organized by EdUHK units.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Organizer"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="prof_engmt_organizer"/>
						<p:inputTextarea id="prof_engmt_organizer" label="Organizer" style="width: 90%;" rows="4" counter="prof_engmt_organizer_display" maxlength="500"
												value="#{manageKtFormView.selectedFormProfEngmt_p.organizer}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="prof_engmt_organizer_display" class="p-d-block" />
					</div>	
					<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
							<p:outputLabel class="riForm-item-title" value="Check # of Engagement"/>
					</div>
					<div class="ui-g-8 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="prof_engmt_check_engage"/>
						<p:selectOneRadio id="prof_engmt_check_engage" title="Check # of Engagement" value="#{manageKtFormView.selectedFormProfEngmt_p.check_engage}"
													unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
													disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
							<f:selectItem itemLabel="No problem" itemValue="Y"/>
							<f:selectItem itemLabel="Check" itemValue="N"/>	   
						</p:selectOneRadio>
					</div>		
				</div>
				<br/>
				<div class="form-sub-title">
					<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
					<div class="riForm-item-note">
					Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Professionals who are only participants of the programme should be excluded. If a person has multiple roles as teacher/ principal/ social or professional leader/ other professional, please avoid double-counting that person for the number of teacher/ principal/ social or professional leader/ other professional. If vice-principals/ school management committee members were engaged in the programme, please count them under number of principal.					
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Teachers Engaged
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="prof_engmt_num_teacher"/>
						<p:inputNumber  id="prof_engmt_num_teacher" title="Number of Teachers Engaged" label="Number of Teachers Engaged"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormProfEngmt_p.num_teacher}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Principals Engaged
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="prof_engmt_num_principal"/>
						<p:inputNumber  id="prof_engmt_num_principal" title="Number of Principals Engaged" label="Number of Principals Engaged"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormProfEngmt_p.num_principal}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Social/ Professional Leaders Engaged
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="prof_engmt_num_leader"/>
						<p:inputNumber  id="prof_engmt_num_leader" title="Number of Social/ Professional Leaders Engaged" label="Number of Social/ Professional Leaders Engaged"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormProfEngmt_p.num_leader}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Other Professionals Engaged
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="prof_engmt_num_other"/>
						<p:inputNumber  id="prof_engmt_num_other" title="Number of Other Professionals Engaged" label="Number of Other Professionals Engaged"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormProfEngmt_p.num_other}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Number of Participants Benefited
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-3">
						<p:message for="prof_engmt_num_pax_ben"/>
						<p:inputNumber  id="prof_engmt_num_pax_ben" title="Number of Participants Benefited" label="Number of Participants Benefited"
											maxValue="9999999999" minValue="0" decimalPlaces="0" 
											value="#{manageKtFormView.selectedFormProfEngmt_p.num_pax_ben}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
					</div>
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>

				<!-- Remarks -->
				<div class="ui-g">
					<div class="ui-g-12 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Remarks"/>
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:message for="prof_engmt_remarks_staff"/>
						<p:inputTextarea id="prof_engmt_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="prof_engmt_remarks_staff_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormProfEngmt_p.remarks_staff}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="prof_engmt_remarks_staff_display" class="p-d-block" />
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Note (RDO)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="prof_engmt_remarks_kt"/>
						<p:inputTextarea id="prof_engmt_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="prof_engmt_remarks_kt_display" maxlength="2000"
												value="#{manageKtFormView.selectedFormProfEngmt_p.remarks_kt}"
	                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
	                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
	                      <br/>
	      				  <h:outputText id="prof_engmt_remarks_kt_display" class="p-d-block" />
					</div>	
			</div>
		</ui:fragment>			
		
	</ui:composition>

</html>
