<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment>
			<div class="ui-g">
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.fac']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="fac"
										label="#{formBundle['form.kt.fac']}" maxlength="20"
										value="#{manageKtFormView.selectedFormEA_p.fac}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.dept']}" />
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-4" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:inputText id="dept"
										label="#{formBundle['form.kt.dept']}" maxlength="20"
										value="#{manageKtFormView.selectedFormEA_p.dept}"
										style="#{manageKtFormView.canModifyFacDept() ?'width: 30%;':'display:none;'}"
										disabled="#{manageKtFormView.canModifyFacDept() == false}"/>
				</div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				If yes, please continue to complete "Entrepreneurial Activity Organised".<br/>
				If no, please report this item in either A7 (Social, Community and Cultural Engagement) or A9 (Public Dissemination and Speeches).
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Entrepreneurship Element?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="ea_ent_element"/>
					<p:selectOneRadio id="ea_ent_element" title="Entrepreneurship Element?" label="Entrepreneurship Element?" value="#{manageKtFormView.selectedFormEA_p.ent_element}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Entrepreneurial Activity Organised"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="ea_title"/>	
					<p:inputTextarea id="ea_title" label="Entrepreneurial Activity Organised" style="width: 90%;" rows="4" counter="ea_title_display" maxlength="500"
											value="#{manageKtFormView.selectedFormEA_p.title}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="ea_title_display" class="p-d-block" />					
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				Please input "0" if the activity has no sub-sessions.			
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2">
				    <p:outputLabel class="riForm-item-title" value="Number of Sub-sessions"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
				    <p:message for="ea_num_subsessions"/>	
				    <p:inputNumber id="ea_num_subsessions" 
					               label="Number of Sub-sessions" 
					               inputStyle="width:30%"
					               value="#{manageKtFormView.selectedFormEA_p.num_subsessions}"
					               disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"
					               integerOnly="true" 
					               maxValue="9999999999" minValue="0" decimalPlaces="0"/>
				</div>
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
				    <p:outputLabel class="riForm-item-title" value="Principal Investigator/Person in charge"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
				    <p:message for="ea_pi"/>	
				    <p:inputText id="ea_pi" label="Principal Investigator/Person in charge" style="width: 90%;"
				                value="#{manageKtFormView.selectedFormEA_p.pi}"
				                disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
				</div>
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Name of Start-up Team Supported"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="ea_team_name"/>	
					<p:inputTextarea id="ea_team_name" label="Name of Start-up Team Supported" style="width: 90%;" rows="4" counter="ea_team_name_display" maxlength="500"
											value="#{manageKtFormView.selectedFormEA_p.team_name}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="ea_team_name_display" class="p-d-block" />					
				</div>	
				
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Type of Supported Provided / Activity Organised"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ea_act_type"/>
					<p:selectOneRadio id="ea_act_type" title="Type of Supported Provided / Activity Organised" label="Type of Supported Provided / Activity Organised" value="#{manageKtFormView.selectedFormEA_p.act_type}"
												unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItems value="#{manageKtFormView.ea_actTypeList}"/>
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Held at Museum/ Galleries Owned by EdUHK?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="ea_museum"/>
					<p:selectOneRadio id="ea_museum" title="Held at Museum/ Galleries Owned by EdUHK?" label="Held at Museum/ Galleries Owned by EdUHK?" value="#{manageKtFormView.selectedFormEA_p.museum}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="EdUHK Organizer?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
				<p:message for="ea_eduhk_org"/>
					<p:selectOneRadio id="ea_eduhk_org" title="EdUHK Organizer?" label="EdUHK Organizer?" value="#{manageKtFormView.selectedFormEA_p.eduhk_org}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Mode"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ea_act_mode"/>
					<p:selectOneRadio id="ea_act_mode" title="Mode" label="Mode" value="#{manageKtFormView.selectedFormEA_p.act_mode}"
												unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItems value="#{manageKtFormView.ea_modeList}"/>
					</p:selectOneRadio>
				</div>	
				
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Credit-bearing"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ea_credit_bearing"/>
					<p:selectOneRadio id="ea_credit_bearing" title="Credit-bearing" label="Credit-bearing" value="#{manageKtFormView.selectedFormEA_p.credit_bearing}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
					</p:selectOneRadio>
				</div>		

				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.act.code']}"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="ea_act_code"/>
					<p:inputText id="ea_act_code"
									label="#{formBundle['form.kt.act.code']}" maxlength="50"
									value="#{manageKtFormView.selectedFormEA_p.act_code}"
									style="width: 90%;"
									disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Budget Holder"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="ea_budget_holder"/>
					<p:inputText id="ea_budget_holder"
									label="Budget Holder" maxlength="200"
									value="#{manageKtFormView.selectedFormEA_p.budget_holder}"
									style="width: 90%;"
									disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>	
				</div>	
				
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Free / Chargeable"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ea_free_charge"/>
					<p:selectOneRadio id="ea_free_charge" title="Free / Chargeable" label="Free / Chargeable" value="#{manageKtFormView.selectedFormEA_p.free_charge}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Free" itemValue="F"/>
						<f:selectItem itemLabel="Chargeable" itemValue="C"/>	   
					</p:selectOneRadio>
				</div>	

				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.region']}"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ea_region"/>
					<p:selectOneRadio id="ea_region" title="Local/ National/ International" label="Local/ National/ International" value="#{manageKtFormView.selectedFormEA_p.region}"
												unselectable="true" layout="grid" columns="3" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Local" itemValue="L"/>
						<f:selectItem itemLabel="National" itemValue="N"/>
						<f:selectItem itemLabel="International" itemValue="I"/>	   
					</p:selectOneRadio>
				</div>		
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.start']}"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-4">
					<p:message for="ea_start_date"/>
					<p:datePicker id="ea_start_date" view="date"
											title="#{formBundle['form.kt.date.start']}" 
											label="#{formBundle['form.kt.date.start']}" 
											value="#{manageKtFormView.selectedFormEA_p.start_date}" 
											pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
											<p:ajax event="change" update="ea_count_project_day ea_count_project_day_in_year ea_income_rpt_unit"/>
					</p:datePicker>				
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="#{formBundle['form.kt.date.end']}"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-4">
					<p:message for="ea_end_date"/>
					<p:datePicker id="ea_end_date" view="date"
											title="#{formBundle['form.kt.date.end']}" 
											label="#{formBundle['form.kt.date.end']}" 
											value="#{manageKtFormView.selectedFormEA_p.end_date}" 
											pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050"
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">	
											<p:ajax event="change" update="ea_count_project_day ea_count_project_day_in_year ea_income_rpt_unit"/>
					</p:datePicker>		
				</div>	
				
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Total Number of Activity Days"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-4">
					 <h:outputText id="ea_count_project_day" value="#{manageKtFormView.countProjectDay(manageKtFormView.selectedFormEA_p.start_date, manageKtFormView.selectedFormEA_p.end_date)}" />
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-2">
					<p:outputLabel class="riForm-item-title" value="Total Number of Activity Days in the Reporting Year"/>
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-4">
					<h:outputText id="ea_count_project_day_in_year" value="#{manageKtFormView.countProjectDayInYear(manageKtFormView.selectedFormEA_p.start_date, manageKtFormView.selectedFormEA_p.end_date)}" />
				</div>
					
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">	
				If no, please jump to "Expenditure".
				</div>
				<div class="ui-g-4 ui-md-3 ui-lg-2">
						<p:outputLabel class="riForm-item-title" value="Budget Approval Sought for the Activity?"/>
				</div>
				<div class="ui-g-8 ui-md-9 ui-lg-10">	
					<p:message for="ea_budget_approval"/>
					<p:selectOneRadio id="ea_budget_approval" title="Budget Approval Sought for the Activity?" label="Budget Approval Sought for the Activity?" value="#{manageKtFormView.selectedFormEA_p.budget_approval}"
												unselectable="true" layout="grid" columns="2" styleClass="grid-radio"
												disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<f:selectItem itemLabel="Yes" itemValue="Y"/>
						<f:selectItem itemLabel="No" itemValue="N"/>	   
						<p:ajax event="change" update="ea_income_rpt_unit ea_budget_panel"/>
					</p:selectOneRadio>
				</div>		
			</div>
			<h:panelGroup id="ea_budget_panel">
			<div class="ui-g" style="#{manageKtFormView.showField(manageKtFormView.selectedFormEA_p.budget_approval) ?'':'display:none;'}">
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Total Approved Budget (HK$)
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="ea_budget"/>
					<p:inputNumber  id="ea_budget" title="Total Approved Budget (HK$)" label="Total Approved Budget (HK$)" symbol="$"
										maxValue="9999999999" minValue="0" decimalPlaces="2" 
										value="#{manageKtFormView.selectedFormEA_p.budget}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
						<p:ajax event="change" update="ea_income_rpt_unit"/>
					</p:inputNumber>
				</div>
			</div>
			</h:panelGroup>
			<br/>
			<div class="form-sub-title">
					<i class="fa-solid fa-hand-holding-dollar" style="margin-right:5px;"></i>Income
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
						Income (HK$)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10">
						<p:inputNumber  id="ea_income_rpt_unit" title="Income (HK$)" label="Income (HK$)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.countIncomeWithFilter(manageKtFormView.selectedFormEA_p.start_date, manageKtFormView.selectedFormEA_p.end_date, manageKtFormView.selectedFormEA_p.budget, manageKtFormView.selectedFormEA_p.expnd_rpt_unit, manageKtFormView.selectedFormEA_p.budget_approval)}" 
											disabled="true"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (FO/ Budget Holder)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ea_income_fo"/>
						<p:inputNumber  id="ea_income_fo" title="Income (FO/ Budget Holder)" label="Income (FO/ Budget Holder)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormEA_p.income_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (Remarks from FO/ Budget Holder)
					</div>
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ea_income_fo_rem"/>
						<p:inputText id="ea_income_fo_rem"
								label="Income (Remarks from FO/ Budget Holder)" maxlength="200"
								value="#{manageKtFormView.selectedFormEA_p.income_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Income (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-10" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ea_income_rdo"/>
						<p:inputNumber  id="ea_income_rdo" title="Income (RDO)" label="Income (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormEA_p.income_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
				</div>
				<br/>
				<div class="form-sub-title">
						<i class="fa-solid fa-sack-dollar" style="margin-right:5px;"></i>Expenditure
				</div>
				<hr style="border-top: 1px dashed #7b9d39;"/>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
					Only direct costs arising from the reported activities should be included. For example, costs directly related to the organization of the events (e.g. wages of staff hired for organizing the events, rentals of outside venues/facilities, etc.) should be included. Overhead expenses incurred regardless of the occurrence of the reported events should be excluded.
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
						Expenditure (Direct Cost)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9">
						<p:message for="ea_expnd_rpt_unit"/>
						<p:inputNumber  id="ea_expnd_rpt_unit" title="Expenditure (Direct Cost)" label="Expenditure (Direct Cost)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormEA_p.expnd_rpt_unit}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}">
											<p:ajax event="change" update="ea_income_rpt_unit"/>
						</p:inputNumber>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Expenditure (Direct Cost) (FO/ Budget Holder)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ea_expnd_fo"/>
						<p:inputNumber  id="ea_expnd_fo" title="Expenditure (Direct Cost) (FO/ Budget Holder)" label="Expenditure (Direct Cost) (FO/ Budget Holder)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormEA_p.expnd_fo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Expenditure (Direct Cost) (Remarks from FO/ Budget Holder)
					</div>	
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ea_expnd_fo_rem"/>
						<p:inputText id="ea_expnd_fo_rem"
								label="Expenditure (Direct Cost) (Remarks from FO/ Budget Holder)" maxlength="200"
								value="#{manageKtFormView.selectedFormEA_p.expnd_fo_rem}"
								style="width: 90%;"
								disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>	
					<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						Expenditure (Direct Cost) (RDO)
					</div>		
					<div class="ui-g-12 ui-md-9 ui-lg-9" style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
						<p:message for="ea_expnd_rdo"/>
						<p:inputNumber  id="ea_expnd_rdo" title="Expenditure (Direct Cost) (RDO)" label="Expenditure (Direct Cost) (RDO)" symbol="$"
											maxValue="9999999999" minValue="0" decimalPlaces="2" 
											value="#{manageKtFormView.selectedFormEA_p.expnd_rdo}" 
											disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
					</div>
			</div>
			<br/>
			<div class="form-sub-title">
				<i class="fa-solid fa-magnifying-glass-chart" style="margin-right:5px;"></i>Number
				<div class="riForm-item-note">
				Please indicate "0" to those Performance Indicator(s) which are not applicable to the KT activities.
				</div>
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<div class="ui-g">
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				This refers to the number of external parties (other than the funding body) that are engaged in the project.  Key partners can be individuals or organisations.
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Key Partners
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_key_partner"/>
					<p:inputNumber  id="ea_num_key_partner" title="Number of Key Partners" label="Number of Key Partners"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_key_partner}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				<div class="ui-g-12 ui-md-12 ui-lg-6"></div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12 riForm-item-note">
				Please input "0" if the activity has no speakers.				
				</div>
				
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Speakers
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_speakers"/>
					<p:inputNumber  id="ea_num_speakers" title="Number of Speakers" label="Number of Speakers"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_speakers}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				<div class="ui-g-12 ui-md-12 ui-lg-6"></div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12">
				EdUHK Participants<hr style="border-top: 1px dashed #7b9d39;"/>
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Undergraduate Students Participated
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_stu_ug"/>
					<p:inputNumber  id="ea_num_stu_ug" title="Number of Undergraduate Students Participated" label="Number of Undergraduate Students Participated"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_stu_ug}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Postgraduate Students Participated
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_stu_pg"/>
					<p:inputNumber  id="ea_num_stu_pg" title="Number of Postgraduate Students Participated" label="Number of Postgraduate Students Participated"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_stu_pg}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				
				
				
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Staff Participated
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_staff"/>
					<p:inputNumber  id="ea_num_staff" title="Number of Staff Participated" label="Number of Staff Participated"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_staff}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Alumni Participated
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_alumni"/>
					<p:inputNumber  id="ea_num_alumni" title="Number of Alumni Participated" label="Number of Alumni Participated"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_alumni}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>	
				
				<div class="ui-g-12 ui-md-12 ui-lg-12">
				External Participants<hr style="border-top: 1px dashed #7b9d39;"/>
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Teacher Participants
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_teacher"/>
					<p:inputNumber  id="ea_num_teacher" title="Number of Teacher Participants" label="Number of Teacher Participants"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_teacher}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Principal Participants
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_principal"/>
					<p:inputNumber  id="ea_num_principal" title="Number of Principal Participants" label="Number of Principal Participants"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_principal}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Other Participants (e.g. Students from Other Universities)
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_other"/>
					<p:inputNumber  id="ea_num_other" title="Number of Other Participants (e.g. Students from Other Universities)" label="Number of Other Participants (e.g. Students from Other Universities)"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_other}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-3 riForm-item-title">
					Number of Schools Benefited
				</div>		
				<div class="ui-g-12 ui-md-9 ui-lg-3">
					<p:message for="ea_num_school"/>
					<p:inputNumber  id="ea_num_school" title="Number of Schools Benefited" label="Number of Schools Benefited"
										maxValue="9999999999" minValue="0" decimalPlaces="0" 
										value="#{manageKtFormView.selectedFormEA_p.num_school}" 
										disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>		
				</div>	
			</div>
			<hr style="border-top: 1px dashed #7b9d39;"/>
			<!-- Remarks -->
			<div class="ui-g">
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title">
					Remarks
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10">
					<p:message for="ea_remarks_staff"/>
					<p:inputTextarea id="ea_remarks_staff" label="Remarks" style="width: 90%;" rows="4" counter="ea_remarks_staff_display" maxlength="2000"
											value="#{manageKtFormView.selectedFormEA_p.remarks_staff}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="ea_remarks_staff_display" class="p-d-block" />
				</div>	
				<div class="ui-g-12 ui-md-3 ui-lg-2 riForm-item-title"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					Note (RDO)
				</div>
				<div class="ui-g-12 ui-md-9 ui-lg-10"  style="#{manageKtFormView.isKtAdmin ?'':'display:none;'}">
					<p:message for="ea_remarks_kt"/>
					<p:inputTextarea id="ea_remarks_kt" label="Note (RDO)" style="width: 90%;" rows="4" counter="ea_remarks_kt_display" maxlength="2000"
											value="#{manageKtFormView.selectedFormEA_p.remarks_kt}"
                        					counterTemplate="#{bundle['form.remaining.characters']} {0}" autoResize="false"
                        					disabled="#{manageKtFormView.isCreator == false || manageKtFormView.canModifyKt == false || manageKtFormView.paramDataLevel eq 'P'}"/>
                      <br/>
      				  <h:outputText id="ea_remarks_kt_display" class="p-d-block" />
				</div>	
			</div>
		<hr style="border-top: 1px dashed #7b9d39;"/>
		</ui:fragment>			
		
	</ui:composition>

</html>
