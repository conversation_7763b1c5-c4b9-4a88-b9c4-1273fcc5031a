<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
	<ui:composition>
		<ui:fragment>
	      	<p:overlayPanel id="ktPreviewPanel" widgetVar="ktPreviewPanelVar"
							dismissable="false"
							styleClass="supplForm-preview-panel supplForm-preview-panel-KT">
				<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
					<table border="0" style="width:100%;">
						<tr>
							<td>
								<h:outputText value="#{manageKtSumView.importKTPanel.getKtCntProj().title}" style="white-space: pre-wrap"/>
								
							</td>
							<td style="text-align:right;">
								<p:commandLink oncomplete="PF('ktPreviewPanelVar').hide()">
									<i class="fas fa-times"/>
								</p:commandLink>
							</td>
						</tr>
					</table>
				</h:panelGroup>
				<div class="ui-g input-panel">
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						#{formBundle['form.kt.act.code']}:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().act_code}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Contributor List:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<h:outputText value="#{manageKtSumView.importKTPanel.getAuthorListMap().get(manageKtSumView.importKTPanel.getKtCntProj().getPk().getForm_no())}" escape="false" />
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Start Date:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().getStart_dateStr()}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						End Date:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().getEnd_dateStr()}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Principal Investigator:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().principal_inves}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Funding Source:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().fund_src}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Total Approved Budget:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().budget}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Income:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().income_rpt_unit}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Number of Key Partners:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().num_key_partner}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Number of Teacher Participants:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().num_teacher}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Number of Principal Participants:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().num_principal}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Number of Other Stakeholders Benefited (e.g. parents):
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().num_stakeholder}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Number of Schools Benefited:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().num_school}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Number of Students Benefited:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getKtCntProj().num_stu}
					</div>
					
					
				</div>
				<h:panelGroup>
					<!-- <h:outputText value="#{manageKtSumView.importKTPanel.getKtCntProj().apa_citation}" /> -->
					<table border="0" style="width:100%;">
						<tr>
							<td style="text-align:right;">
								<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('ktPreviewPanelVar').hide()">
								</p:commandButton>
							</td>
						</tr>
					</table>
				</h:panelGroup>
			</p:overlayPanel>
			<h:panelGroup id="buttonPanel">
				<p:commandButton id="importBtn" styleClass="default-linkButton" value="Import" update=":collectConfirmForm:collectConfirm"
								 oncomplete="PF('collectConfirmWidget').show();" disabled="#{manageKtSumView.importKTPanel.selectedKtCntProjList.isEmpty()}" >
				</p:commandButton>
			</h:panelGroup>
			
	       	<p:dataTable id="ktDataTable"
						 value="#{manageKtSumView.importKTPanel.getKtCntProjList()}" 
						 var="kt"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
	                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
	                     rows="30"
	                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
	                 	 tableStyle="table-layout:auto;"
	                 	 selection="#{manageKtSumView.importKTPanel.selectedKtCntProjList}"
	                 	 rowSelectMode="checkbox"
	                 	 rowKey="#{kt.pk.asKeyString}"
	                 	 rowIndexVar="rowIndex"
						 >
				<p:ajax event="toggleSelect" update="sideBarForm:buttonPanel"/>
				<p:ajax event="rowSelectCheckbox" update="sideBarForm:buttonPanel"/>
				<p:ajax event="rowUnselectCheckbox" update="sideBarForm:buttonPanel"/>
				
				
				<p:column selectionMode="multiple" exportable="false" style="width:1em;"/>
				
				<p:column width="40%" sortBy="#{kt.title}"
						  filterBy="#{kt.title}" filterMatchMode="contains">
					<f:facet name="header">Project Title</f:facet>
					<h:outputText value="#{kt.title}" />
				</p:column>
				
				<p:column width="20%" sortBy="#{manageKtSumView.importKTPanel.getAuthorListMap().get(kt.pk.form_no)}"
						  filterBy="#{manageKtSumView.importKTPanel.getAuthorListMap().get(kt.pk.form_no)}" filterMatchMode="contains">
					<f:facet name="header">Contributor List</f:facet>
					<h:outputText value="#{manageKtSumView.importKTPanel.getAuthorListMap().get(kt.pk.form_no)}" escape="false"/>
				</p:column>
				
				<p:column headerText="#{formBundle['form.kt.act.code']}" sortBy="#{kt.act_code}"
						  filterBy="#{kt.act_code}" filterMatchMode="contains">
	                <h:outputText value="#{kt.act_code}"/>
	            </p:column>  
				
				<p:column width="10%" sortBy="#{kt.start_date}"
						  filterBy="#{kt.getStart_dateStr()}" filterMatchMode="contains">
					<f:facet name="header">Start Date</f:facet>
					<h:outputText value="#{kt.getStart_dateStr()}"/>
				</p:column>
				
				<p:column width="10%" sortBy="#{kt.end_date}"
						  filterBy="#{kt.getEnd_dateStr()}" filterMatchMode="contains">
					<f:facet name="header">End Date</f:facet>
					<h:outputText value="#{kt.getEnd_dateStr()}"/>
				</p:column>
	            
	            <p:column headerText="Principal Investigator" sortBy="#{kt.principal_inves}"
						  filterBy="#{kt.principal_inves}" filterMatchMode="contains">
	                <h:outputText value="#{kt.principal_inves}"/>
	            </p:column> 
	            
	            <p:column headerText="Funding Source" sortBy="#{kt.fund_src}"
						  filterBy="#{kt.fund_src}" filterMatchMode="contains">
	                <h:outputText value="#{kt.fund_src}"/>
	            </p:column> 
				
				<p:column width="3em;">
					<f:facet name="header">Actions</f:facet>
					<p:commandButton value="View" update=":sideBarForm"
									 styleClass="default-linkButton"
									 oncomplete="PF('ktPreviewPanelVar').show('#{component.clientId}')"
									 actionListener="#{manageKtSumView.importKTPanel.setSelectedPreview(kt.pk.form_no, kt.pk.data_level)}">
					</p:commandButton>
				</p:column>
				
			</p:dataTable>
		</ui:fragment>
	</ui:composition>

</html>
	        