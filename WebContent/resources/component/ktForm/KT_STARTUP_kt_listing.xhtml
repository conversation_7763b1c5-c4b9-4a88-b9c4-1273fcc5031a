<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<ui:fragment>
			<p:commandButton value="Export excel" styleClass="p-mr-2 p-mb-2" icon="pi pi-download"
							 widgetVar="outputDownloadBtn"
							 ajax="false">
                   <p:dataExporter type="xlsx" target="outputDataTable" fileName="outputData" options="#{ktListingView.excelOpt}" 
                   				   postProcessor="#{ktListingView.postPrsc}"/>
               </p:commandButton>
			<p:dataTable id="outputDataTable"
						 value="#{ktListingView.getKtActivitiesList()}" 
						 var="act"
						 stripedRows="true" size="small" style="font-size:14px;"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                 paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                 rows="30"
		                 rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
	                	 tableStyle="table-layout:auto;"
				 		 >
				
				<p:column width="3em;">
					<f:facet name="header">KT Activity No.</f:facet>
					<h:outputText value="#{act.form_no}" />
				</p:column>
				
				<p:column style="min-width:20em;" >
					<f:facet name="header">Contributor List</f:facet>
					<h:outputText value="#{act.authorList}" escape="false" />
				</p:column>
				
				<p:column style="min-width:8em;">
					<f:facet name="header">Faculty</f:facet>
					<h:outputText value="#{act.fac}" />
				</p:column>
				
				<p:column style="min-width:8em;">
					<f:facet name="header">Department</f:facet>
					<h:outputText value="#{act.dept}" />
				</p:column>
				
				<p:column style="min-width:15em;">
					<f:facet name="header">Name of Team/Startup Supported</f:facet>
					<h:outputText value="#{act.title}" />
				</p:column>
				
				<p:column style="min-width:8em;">
					<f:facet name="header">Activity / Project Code</f:facet>
					<h:outputText value="#{act.act_code}" />
				</p:column>
				
				<p:column width="8em;">
					<f:facet name="header">Date</f:facet>
					<h:outputText value="#{act.start_date}" />
				</p:column>
				
				<p:column width="10em;">
					<f:facet name="header">Organizer</f:facet>
					<h:outputText value="#{act.organizer}" />
				</p:column>
				
				<p:column width="10em;">
					<f:facet name="header">Contact Person</f:facet>
					<h:outputText value="#{act.ct_person}" />
				</p:column>
				
				<p:column width="10em;">
					<f:facet name="header">Type(s) of Support Provided</f:facet>
					<h:outputText value="#{act.act_type}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of UG Students Participated</f:facet>
					<h:outputText value="#{act.num_stu_ug}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of PG Students Participated</f:facet>
					<h:outputText value="#{act.num_stu_pg}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Staff Participated</f:facet>
					<h:outputText value="#{act.num_staff}" />
				</p:column>
				<p:column width="10em;">
					<f:facet name="header">Number of Alumni Participated</f:facet>
					<h:outputText value="#{act.num_alumni}" />
				</p:column>
				
			</p:dataTable>
		</ui:fragment>			
		
	</ui:composition>

</html>
