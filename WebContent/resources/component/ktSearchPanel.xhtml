<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

<cc:interface>
	<cc:attribute name="searchPanel" type="hk.eduhk.rich.view.KTSearchPanel" required="true"/>
	<cc:attribute name="update" type="java.lang.String"/>
	<cc:attribute name="showPersonPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showExStaffPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showRiPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showOtherPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isRdoLib" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isDeptAdmin" type="java.lang.Boolean" default="true"/>
</cc:interface>
<cc:implementation>
	<p:accordionPanel multiple="true" widgetVar="multiple">
		<p:tab id="PersonPanel" title="Search by Person" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showPersonPanel}">
	
			<div class="ui-g input-panel">
	
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Staff Name
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
					<p:autoComplete id="staffName"
								 	value="#{cc.attrs.searchPanel.staffName}"
							     	completeMethod="#{cc.attrs.searchPanel.nameAutoComplete}"
							     	scrollHeight="250">
						<p:ajax event="itemSelect" process="@this" update="#{cc.attrs.update}" />
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}" 
								/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:autoComplete>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Academic Staff Rank
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectCheckboxMenu id="rank"
										  value="#{cc.attrs.searchPanel.selectedRankList}"
										  multiple="true"
										  filter="true" 
										  filterMatchMode="contains"
										  >
						<f:selectItems value="#{cc.attrs.searchPanel.rankList}"
									   var="rk"
									   itemValue="#{rk.rank_code}"
									   itemLabel="#{rk.rank_full}"
									   />
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
					</p:selectCheckboxMenu>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Academic Staff
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="acadStaff"
									 value="#{cc.attrs.searchPanel.acadStaff}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.allYesNoEmptyList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>
			</div>
		</p:tab>
		
		
		<p:tab id="exStaffPanel" title="Search by ex-EduHK staff" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showExStaffPanel}">
	
			<div class="ui-g input-panel">
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Former Staff Name
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
					<p:autoComplete id="exStaffName"
								 	value="#{cc.attrs.searchPanel.exStaffName}"
							     	completeMethod="#{cc.attrs.searchPanel.exNameAutoComplete}"
							     	scrollHeight="250">
						<p:ajax event="itemSelect" process="@this" update="#{cc.attrs.update}" />
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}" 
								/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:autoComplete>
				</div>
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Former Staff Number
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:inputText id="formStaffNum"
									 value="#{cc.attrs.searchPanel.formStaffNum}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
			</div>
		</p:tab>
		
		
		<p:tab id="riPanel" title="Search by KT Activities" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showRiPanel}">
	
			<h:panelGroup id="riInnerPanel" class="ui-g input-panel">
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Type of KT
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="ktType"
									 value="#{cc.attrs.searchPanel.ktType}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.ktTypeList}"
									   var="ktTypeVar"
									   itemValue="#{ktTypeVar.form_code}"
									   itemLabel="#{ktTypeVar.form_short_desc} #{ktTypeVar.form_full_desc}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update} @parent"/>
					</p:selectOneMenu>
				</div>
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Type of View
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="viewType"
										 value="#{cc.attrs.searchPanel.viewType}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.viewTypeList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update} @parent"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					KT Activity No.
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
					<p:inputText id="riNo"
								 value="#{cc.attrs.searchPanel.formNo}">
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:inputText>
				</div>
				
				<h:panelGroup style="width:100%">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Title
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<p:inputText id="smartSearch"
									 value="#{cc.attrs.searchPanel.title}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					KT Faculty(s) and Department(s)
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectCheckboxMenu id="facDept"
										  value="#{cc.attrs.searchPanel.selectedFacDeptList}"
										  multiple="true"
										  filter="true" 
										  filterMatchMode="contains"
										  >
						<f:selectItems value="#{cc.attrs.searchPanel.facDeptList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
					</p:selectCheckboxMenu>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					KT Date Period
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					From
					<p:calendar id="ktDateFrom" value="#{cc.attrs.searchPanel.ktDateFrom}"
								pattern="dd/MM/yyyy" 
								mask="true"
								timeInput="true"
								showOn="button"
								locale="en"
								>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:calendar>
					/ To
					<p:calendar id="ktDateTo" value="#{cc.attrs.searchPanel.ktDateTo}" 
								pattern="dd/MM/yyyy" 
								mask="true"
								timeInput="true"
								showOn="button"
								locale="en"
								>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:calendar>
				</div>		
				
			</h:panelGroup>
			
		</p:tab>
		
		
		<p:tab id="OtherPanel" title="Sorting" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showOtherPanel}">
	
			<div class="ui-g input-panel">
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Sort by Column
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="sortCol"
									 value="#{cc.attrs.searchPanel.sortCol}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.sortColList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Sort Order
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="sortOrder"
									 value="#{cc.attrs.searchPanel.sortOrder}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.sortOrderList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>
				
			</div>
			
		</p:tab>
	</p:accordionPanel>
	
	
</cc:implementation>

</html>