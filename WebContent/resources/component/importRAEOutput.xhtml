<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
<cc:interface>
	<cc:attribute name="importRaeOutput" type="hk.eduhk.rich.view.rae.ImportRAEOutput" required="true"/>
	<cc:attribute name="update" type="java.lang.String" required="true"/>
</cc:interface>
<cc:implementation>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
				<p:overlayPanel id="outputPreviewPanel" widgetVar="outputPreviewPanelVar"
								dismissable="false"
								styleClass="supplForm-preview-panel">
					<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
					
						<table border="0" style="width:100%;">
							<tr>
								<td>
									#{cc.attrs.importRaeOutput.getOutput().apa_citation}
								</td>
								<td style="text-align:right;">
									<p:commandLink oncomplete="PF('outputPreviewPanelVar').hide()">
										<i class="fas fa-times"/>
									</p:commandLink>
								</td>
							</tr>
						</table>
					</h:panelGroup>
					<div class="ui-g input-panel">
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							RI No.:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().output_no}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Research Output Type:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().sap_output_type_desc}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Display Language:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().language}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Title of Research Output:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().title_of_output}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Publisher/Conference Organiser(s)/Others:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().publisher}
						</div>

						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							List of Author(s):
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().name_other_pos}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Volume (Issue):
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().vol_issue}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Page No.:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().page_num}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Date:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRaeOutput.getOutput().from_month}/#{cc.attrs.importRaeOutput.getOutput().from_year}
						</div>
					</div>
					<h:panelGroup>
						<!-- <h:outputText value="#{cc.attrs.importRIOutput.getOutput().apa_citation}" /> -->
						<table border="0" style="width:100%;">
							<tr>
								<td style="text-align:right;">
									<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('outputPreviewPanelVar').hide()"></p:commandButton>
									<p:commandButton  action="#{cc.attrs.importRaeOutput.importRI(cc.attrs.importRaeOutput.getOutput().output_no)}" oncomplete="window.scrollTo(0,0);" value="Import" style="margin-right:5px; margin-bottom:1px;"
											update="@form messages" ></p:commandButton>	
								</td>
							</tr>
						</table>
					</h:panelGroup>
				</p:overlayPanel>
	        	<p:dataTable id="outputDataTable"
							 value="#{cc.attrs.importRaeOutput.getOutputList()}" 
							 var="output"
							 styleClass="default-dataTable"
							 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
							 reflow="true"
							 paginator="true"  allowUnsorting="true" sortMode="single"
							 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                     rows="5"
		                 	 tableStyle="table-layout:auto;"
							 >
					<!-- rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE}" -->		 		
					<p:column width="10%"  sortBy="#{output.output_no}" >
						<f:facet name="header">RI No.</f:facet>
						<h:outputText value="#{output.output_no}" escape="false" />
					</p:column>
					
					<p:column width="70%" sortBy="#{output.apa_citation}">
						<f:facet name="header">Summary</f:facet>
						<h:outputText value="#{output.apa_citation}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Actions</f:facet>
						<p:commandButton value="View" update="#{cc.attrs.update}"
										 styleClass="default-linkButton"
										 oncomplete="PF('outputPreviewPanelVar').show()"
										 actionListener="#{cc.attrs.importRaeOutput.setSelectedOutputNo(output.output_no)}"> </p:commandButton>
						<p:commandButton  action="#{cc.attrs.importRaeOutput.importRI(output.output_no)}" oncomplete="window.scrollTo(0,0);" value="Import" style="margin-right:5px; margin-bottom:1px;"
								update="@form messages" ></p:commandButton>	
					
					</p:column>
					
				</p:dataTable>
</cc:implementation>

</html>
	        