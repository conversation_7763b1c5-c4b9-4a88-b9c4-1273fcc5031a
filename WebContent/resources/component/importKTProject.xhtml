<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
	<ui:composition>
		<ui:fragment>
	      	<p:overlayPanel id="riPreviewPanel" widgetVar="riPreviewPanelVar"
							dismissable="false"
							styleClass="supplForm-preview-panel supplForm-preview-panel-KT">
				<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
					<table border="0" style="width:100%;">
						<tr>
							<td>
								<h:outputText value="#{manageKtSumView.importKTPanel.getRiProj().projTitle}" style="white-space: pre-wrap"/>
								
							</td>
							<td style="text-align:right;">
								<p:commandLink oncomplete="PF('riPreviewPanelVar').hide()">
									<i class="fas fa-times"/>
								</p:commandLink>
							</td>
						</tr>
					</table>
				</h:panelGroup>
				<div class="ui-g input-panel">
				
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						RI No.:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().projectNo}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Principal Investigator:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<h:outputText value="#{manageKtSumView.importKTPanel.getRiProj().prin_inves_author_list}" escape="false" />
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Co-Principal Investigator:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<h:outputText value="#{manageKtSumView.importKTPanel.getRiProj().co_prin_inves_author_list}" escape="false" />
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Co-Investigator:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<h:outputText value="#{manageKtSumView.importKTPanel.getRiProj().co_inves_author_list}" escape="false" />
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Other Member:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<h:outputText value="#{manageKtSumView.importKTPanel.getRiProj().other_author_list}" escape="false" />
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Project Type:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().project_type}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Activity Code:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().activity_code}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						A Brief Description/Summary of Project:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().generateConcatProjectSummary()}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Commencement Date:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().fromdate}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						(Expected) Completion Date:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().todate}
					</div>
					
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Funding Source:
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						#{manageKtSumView.importKTPanel.getRiProj().sap_funding_source}
					</div>
					
				</div>
				<h:panelGroup>
					<!-- <h:outputText value="#{manageKtSumView.importKTPanel.getKtCntProj().apa_citation}" /> -->
					<table border="0" style="width:100%;">
						<tr>
							<td style="text-align:right;">
								<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('riPreviewPanelVar').hide()">
								</p:commandButton>
								<p:linkButton outcome="ktForm" value="Import" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageKtSumView.canModifyKt and manageKtSumView.paramAdmin != 'Y'}">
									<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
									<f:param name="form" value="#{manageKtSumView.paramFormCode}"/>
									<f:param name="dataLevel" value="M"/>
									<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
									<f:param name="riNo" value="#{manageKtSumView.importKTPanel.getRiProj().projectNo}"/>
								</p:linkButton>	
							</td>
						</tr>
					</table>
				</h:panelGroup>
			</p:overlayPanel>
			
	       	<p:dataTable id="riDataTable"
						 value="#{manageKtSumView.importKTPanel.getRiProjList()}" 
						 var="ri"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
	                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
	                     rows="30"
	                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
	                 	 tableStyle="table-layout:auto;"
	                 	 rowIndexVar="rowIndex"
						 >
						 
				<p:column width="10%" sortBy="#{ri.projectNo}"
						  filterBy="#{ri.projectNo}" filterMatchMode="contains">
					<f:facet name="header">RI No.</f:facet>
					<h:outputText value="#{ri.projectNo}" />
				</p:column>
				
				<p:column width="60%" sortBy="#{ri.projTitle}"
						  filterBy="#{ri.projTitle}" filterMatchMode="contains">
					<f:facet name="header">Project Title</f:facet>
					<h:outputText value="#{ri.projTitle}" />
				</p:column>
				
				<p:column width="10%" sortBy="#{ri.getFromdateDate()}"
						  filterBy="#{ri.fromdate}" filterMatchMode="contains">
					<f:facet name="header">Commencement Date</f:facet>
					<h:outputText value="#{ri.fromdate}"/>
				</p:column>
				
				<p:column width="10%" sortBy="#{ri.getTodateDate()}"
						  filterBy="#{ri.todate}" filterMatchMode="contains">
					<f:facet name="header">(Expected) Completion Date</f:facet>
					<h:outputText value="#{ri.todate}"/>
				</p:column>
				
				<p:column width="3em;">
					<f:facet name="header">Actions</f:facet>
					<p:commandButton id="viewButton" value="View" update=":importProjSideBarForm"
									 styleClass="default-linkButton"
									 oncomplete="PF('riPreviewPanelVar').show('#{component.clientId}')"
									 actionListener="#{manageKtSumView.importKTPanel.setSelectedPreview(ri.projectNo, '', 'riProj')}">
					</p:commandButton>
					<p:linkButton outcome="ktForm" value="Import" style="margin-right:5px; margin-bottom:1px;" rendered="#{manageKtSumView.canModifyKt and manageKtSumView.paramAdmin != 'Y'}">
						<f:param name="pid" value="#{manageKtSumView.paramPid}"/>
						<f:param name="form" value="#{manageKtSumView.paramFormCode}"/>
						<f:param name="dataLevel" value="M"/>
						<f:param name="period" value="#{manageKtSumView.selectedPeriod}"/>
						<f:param name="riNo" value="#{ri.projectNo}"/>
					</p:linkButton>	
				</p:column>
				
			</p:dataTable>
		</ui:fragment>
	</ui:composition>

</html>
	        