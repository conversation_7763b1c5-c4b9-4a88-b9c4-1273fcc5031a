<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
	
	<ui:composition>	
		<f:event listener="#{viewOutputSumView.updateChart()}" type="preRenderView"/>	
			<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'display:none;':''}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.acadStaff.countRI']} - Research Outputs</span>
			<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'':'display:none;'}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.dept.countRI']} - Research Outputs</span>
			<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_OUTPUT_STAFF')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_OUTPUT_STAFF') ne null and viewSumView.paramAdmin ne 'Y'}"/>
			<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_OUTPUT_DEPT')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_OUTPUT_DEPT') ne null and viewSumView.paramAdmin eq 'Y'}"/>	
			
			<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
			<p:scrollTop />
			
				<h:form id="form_output">
					<p:linkButton outcome="viewSum" value="#{bundle['action.back']}" icon="pi pi-arrow-left" styleClass="btn-back">
						<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="facDept" value="#{viewOutputSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="startDate" value="#{viewOutputSumView.getStartDateString()}"/>
						<f:param name="endDate" value="#{viewOutputSumView.getEndDateString()}"/>
						<f:param name="riPeriod" value="#{viewOutputSumView.getSelectedRiPeriodString()}"/>
						<f:param name="ktPeriod" value="#{viewOutputSumView.getSelectedKtPeriodString()}"/>
					</p:linkButton>
					<p:linkButton outcome="/admin/researchInformationListing" value="View RI Listing" icon="pi pi-list" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="riType" value="Output"/>
						<f:param name="basic" value="#{viewSumView.paramAdmin == 'Y'?'N':'Y'}"/>
						<f:param name="name" value="#{viewSumView.paramStaffName}" rendered="#{viewSumView.paramAdmin ne 'Y'}"/>
						<f:param name="dataLevel" value="#{viewSumView.getRiSumDataLevel()}"/>
						<f:param name="facDept" value="#{viewOutputSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="startDate" value="#{viewOutputSumView.getListingStartDateString()}"/>
						<f:param name="endDate" value="#{viewOutputSumView.getListingEndDateString()}"/>
						<f:param name="outputType" value="#{viewOutputSumView.getSelectedOutputTypeString()}"/>
						<f:param name="search" value="Y"/>
					</p:linkButton>	
					<p:commandButton id="exportButton" value="Export Excel File" icon="pi pi-download" style="margin-right:5px; margin-bottom:1px;"
									 ajax = "false"
									 action="#{viewOutputSumView.exportSummary()}" ></p:commandButton>
					<p:panel>
						<f:facet name="header">
							<div>
								<span style="color:#1f1645;">Filtering Criteria</span>
							</div>
						</f:facet>

						<div class="ui-g">
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel for="@next" value="Faculty/ Department/ Centre" style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}" />
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="facDept" title="Faculty/ Department/ Centre" label="Faculty/ Department/ Centre" 
													multiple="true" filter="true" 
													filterMatchMode="contains"
													value="#{viewOutputSumView.selectedFacDepts}" 
													style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}">
										<f:selectItems value="#{viewSumView.facDeptList}"/>
								</p:selectCheckboxMenu>	
							</div>
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="cdcfPeriod"  value="#{viewOutputSumView.selectedCdcfPeriods}" 
																				 multiple="true" filter="true">
										<f:selectItems value="#{viewSumView.cdcfPeriodList}" var="o" 
															itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />				
								</p:selectCheckboxMenu>	
							</div>
							
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="RI Date Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-3 ui-lg-3">
								<p:datePicker id="riStartDate" view="date"
															title="Start Date (DD/MM/YYYY)" 
															label="Start Date (DD/MM/YYYY)" 
															value="#{viewOutputSumView.selectedStartDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-5 ui-lg-5">
								<p:datePicker id="riEndDate" view="date"
															title="End Date (DD/MM/YYYY)" 
															label="End Date (DD/MM/YYYY)" 
															value="#{viewOutputSumView.selectedEndDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Output Type" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="outputType"  value="#{viewOutputSumView.selectedOutputTypes}"
																				 multiple="true" filter="true">
												<f:selectItems value="#{viewOutputSumView.outputTypeList}" var="o" 
																	itemLabel="#{o.description}" itemValue="#{o.pk.lookup_code}" itemDisabled="#{o.pk.lookup_level == 1}" />
								</p:selectCheckboxMenu>	
							</div>
			            </div>
			            <p:commandButton id="filterButton" value="Apply" update="form_output" icon="pi pi-filter"
									 widgetVar="filterButtonWV" onclick="PF('filterButtonWV').disable();PF('filterDialog').show();"
									 oncomplete="PF('filterButtonWV').enable();PF('filterDialog').hide();diableEnterBtn();"
									 actionListener="#{viewOutputSumView.updateChart()}" ></p:commandButton><p:spacer width="10"/>
						<p:dialog widgetVar="filterDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
					        <div>
				            	<h5>Filtering</h5>
			        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
				            </div>
					    </p:dialog>
			          </p:panel>
			          
			        <div class="card">  
			            <p:panelGrid columns="2" id="board_output" style="width: 100%" columnClasses="width-fifty-percent width-fifty-percent">
							<p:panel id="outputYrBar" header="Total No. of Research Outputs">
								<p:barChart model="#{viewOutputSumView.outputYrTotalBarModel}" style="width: 100%;" widgetVar="outputYrBarChart"/>
								<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_output', 'outputYrBarChart')" style="display:none;"/>
				            </p:panel>
				            <p:panel id="outputWeightingYrBar" header="Research Outputs by Weighting">
								<p:barChart model="#{viewOutputSumView.outputWeightingYrTotalBarModel}" style="width: 100%;" widgetVar="outputWeightingYrBarChart"/>
								<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_output', 'outputWeightingYrBarChart')" style="display:none;"/>
				            </p:panel>
				            
				            <p:panel id="outputRBkRJrBar" header="No. of Refereed Books and Journals">
				                 <p:barChart model="#{viewOutputSumView.outputRBkRJrBarModel}" style="width: 100%;" widgetVar="outputRBkRJrBarChart"/>
				                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_output', 'outputRBkRJrBarChart')" style="display:none;"/>
				            </p:panel>
				            
				            <p:panel id="outputIntConfBar" header="Is international conference?">
								<p:barChart model="#{viewOutputSumView.outputIntConfBarModel}" style="width: 100%;" widgetVar="outputIntConfBarChart"/>
								<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_output', 'outputIntConfBarChart')" style="display:none;"/>
				            </p:panel>
				            
				            <p:panel id="outputRGCBar" header="Is the project(s) for creating the research output fully/partially funded by RGC?">
								<p:barChart model="#{viewOutputSumView.outputRGCBarModel}" style="width: 100%;" widgetVar="outputRGCBarChart"/>
								<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_output', 'outputRGCBarChart')" style="display:none;"/>
				            </p:panel>
				            
				            <p:panel id="outputHighEduBar" header="Is this related to the enhancement of teaching and learning in higher edu./teacher edu.?">
								<p:barChart model="#{viewOutputSumView.outputHighEduBarModel}" style="width: 100%;" widgetVar="outputHighEduBarChart"/>
								<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_output', 'outputHighEduBarChart')" style="display:none;"/>
				            </p:panel>				           
			            </p:panelGrid>
		            </div>
		            
				</h:form>
			
		</ui:composition>
</html>