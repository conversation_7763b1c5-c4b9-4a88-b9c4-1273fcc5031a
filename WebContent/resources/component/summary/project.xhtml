<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<f:event listener="#{viewProjectSumView.updateChart()}" type="preRenderView"/>
			<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'display:none;':''}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.acadStaff.countRI']} - Projects</span>
			<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'':'display:none;'}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.dept.countRI']} - Projects</span>
			<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_PROJECT_STAFF')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_PROJECT_STAFF') ne null and viewSumView.paramAdmin ne 'Y'}"/>
			<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_PROJECT_DEPT')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_PROJECT_DEPT') ne null and viewSumView.paramAdmin eq 'Y'}"/>	
			
			<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
			<p:scrollTop />
			
				<h:form id="form_project">
					<p:linkButton outcome="viewSum" value="#{bundle['action.back']}" icon="pi pi-arrow-left" styleClass="btn-back">
						<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="facDept" value="#{viewProjectSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="startDate" value="#{viewProjectSumView.getStartDateString()}"/>
						<f:param name="endDate" value="#{viewProjectSumView.getEndDateString()}"/>
						<f:param name="riPeriod" value="#{viewProjectSumView.getSelectedRiPeriodString()}"/>
						<f:param name="ktPeriod" value="#{viewProjectSumView.getSelectedKtPeriodString()}"/>
					</p:linkButton>	
					<p:linkButton outcome="/admin/researchInformationListing" value="View RI Listing" icon="pi pi-list" target="_blank" style="margin-right:5px; margin-bottom:1px;">
						<f:param name="riType" value="Project"/>
						<f:param name="basic" value="#{viewSumView.paramAdmin == 'Y'?'N':'Y'}"/>
						<f:param name="name" value="#{viewSumView.paramStaffName}" rendered="#{viewSumView.paramAdmin ne 'Y'}"/>
						<f:param name="dataLevel" value="#{viewSumView.getRiSumDataLevel()}"/>
						<f:param name="facDept" value="#{viewProjectSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="startDate" value="#{viewProjectSumView.getListingStartDateString()}"/>
						<f:param name="endDate" value="#{viewProjectSumView.getListingEndDateString()}"/>
						<f:param name="fundingSource" value="#{viewProjectSumView.getSelectedFundingSourceString()}"/>
						<f:param name="search" value="Y"/>
					</p:linkButton>	
					<p:commandButton id="exportButton" value="Export Excel File" icon="pi pi-download" style="margin-right:5px; margin-bottom:1px;"
									 ajax = "false"
									 action="#{viewProjectSumView.exportSummary()}" ></p:commandButton>
					<p:panel>
						<f:facet name="header">
							<div>
								<span style="color:#1f1645;">Filtering Criteria</span>
							</div>
						</f:facet>
						<div class="ui-g">
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel for="@next" value="Faculty/ Department/ Centre" style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}" />
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="facDept" title="Faculty/ Department/ Centre" label="Faculty/ Department/ Centre" 
													multiple="true" filter="true" 
													filterMatchMode="contains"
													value="#{viewProjectSumView.selectedFacDepts}" 
													style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}">
										<f:selectItems value="#{viewSumView.facDeptList}"/>
								</p:selectCheckboxMenu>	
							</div>
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="cdcfPeriod"  value="#{viewProjectSumView.selectedCdcfPeriods}"
																				 multiple="true" filter="true">
										<f:selectItems value="#{viewSumView.cdcfPeriodList}" var="o" 
															itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />			
								</p:selectCheckboxMenu>	
							</div>
							
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="RI Date Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-3 ui-lg-3">
								<p:datePicker id="riStartDate" view="date"
															title="Start Date (DD/MM/YYYY)" 
															label="Start Date (DD/MM/YYYY)" 
															value="#{viewProjectSumView.selectedStartDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-5 ui-lg-5">
								<p:datePicker id="riEndDate" view="date"
															title="End Date (DD/MM/YYYY)" 
															label="End Date (DD/MM/YYYY)" 
															value="#{viewProjectSumView.selectedEndDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Funding Source" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="fundingSource"  value="#{viewProjectSumView.selectedFundingSources}"
																				 multiple="true" filter="true">
												<f:selectItems value="#{viewSumView.fundSourceList}" var="sc"  
																	itemLabel="#{sc.description}" itemValue="#{sc.pk.lookup_code}"/>
								</p:selectCheckboxMenu>	
							</div>
			            </div>
			            <p:commandButton id="filterButton" value="Apply" update="form_project" icon="pi pi-filter"
									 widgetVar="filterButtonWV" onclick="PF('filterButtonWV').disable();PF('filterDialog').show();"
									 oncomplete="PF('filterButtonWV').enable();PF('filterDialog').hide();diableEnterBtn();"
									 actionListener="#{viewProjectSumView.updateChart()}" ></p:commandButton><p:spacer width="10"/>
						<p:dialog widgetVar="filterDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
					        <div>
				            	<h5>Filtering</h5>
			        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
				            </div>
					    </p:dialog>
			          </p:panel>
			          
			        <div class="card">  
					<p:panelGrid columns="2" id="board_project" style="width: 100%" columnClasses="width-fifty-percent width-fifty-percent">
						<p:panel id="projectYrBar" header="Total No. of Projects">
							<p:barChart model="#{viewProjectSumView.projectYrTotalBarModel}" style="width: 100%;" widgetVar="projectYrBarChart"/>
							<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectYrBarChart')" style="display:none;"/>
			            </p:panel>
						<p:panel id="projectFundedProjBar" header="Funded Project?">
			                 <p:barChart model="#{viewProjectSumView.projectFundedProjBarModel}" style="width: 100%;" widgetVar="projectFundedProjBarChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectFundedProjBarChart')" style="display:none;"/>
			            </p:panel>
			            <p:panel id="projectTypeBar" header="Project Type">
			                 <p:barChart model="#{viewProjectSumView.projectTypeBarModel}" style="width: 100%;" widgetVar="projectTypeBarChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectTypeBarChart')" style="display:none;"/>
			            </p:panel>
			            <p:panel id="projectTechBar" header="Is it an Innovation/Technology (創科) related project?">
			                 <p:barChart model="#{viewProjectSumView.projectTechBarModel}" style="width: 100%;" widgetVar="projectTechBarChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectTechBarChart')" style="display:none;"/>
			            </p:panel>
			            <p:panel id="projectFundBodyNewPie" header="Funding Body (New Project)">
			                 <p:pieChart model="#{viewProjectSumView.projectFundBodyPieNewModel}" style="width: 100%;" widgetVar="projectFundBodyPieNewChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectFundBodyPieNewChart')" style="display:none;"/>
			            </p:panel>
			            <!--<p:panel id="projectTypePie" header="Project Type">
			                 <p:pieChart model="#{viewProjectSumView.projectTypePieModel}" style="width: 100%;" widgetVar="projectTypePieChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectTypePieChart')" style="display:none;"/>
			            </p:panel>
			            <p:panel id="projectTechPie" header="Is it an Innovation/Technology (創科) related project?">
			                 <p:pieChart model="#{viewProjectSumView.projectTechPieModel}" style="width: 100%;" widgetVar="projectTechPieChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectTechPieChart')" style="display:none;"/>
			            </p:panel>-->
			           
			            <!-- <p:panel id="projectSectorBar" header="Sector of the Project">
			                 <p:pieChart model="#{viewProjectSumView.projectSectorBarModel}" style="width: 100%;" widgetVar="projectSectorBarChart"/>
			                 <p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_project', 'projectSectorBarChart')"/>
			            </p:panel>-->
		            </p:panelGrid>
		            </div>
				</h:form>
			
		</ui:composition>
</html>