<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<f:event listener="#{viewKtSumView.updateChart()}" type="preRenderView"/>
			<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'display:none;':''}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.acadStaff.countRI']} - KT Activities</span>
			<span class="admin-content-title" style="#{viewSumView.paramAdmin == 'Y'?'':'display:none;'}"><i class="fas fa-gauge-high"></i> #{bundle['function.title.dept.countRI']} - KT Activities</span>
			<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_KT_STAFF')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_KT_STAFF') ne null and viewSumView.paramAdmin ne 'Y'}"/>
			<p:staticMessage severity="warn" detail="#{sysParamView.getValue('NOTE_SUMMARY_KT_DEPT')}" escape="false" style="width: 100%; margin-bottom:6px; padding: 0.5rem 0.5rem !important;" rendered="#{sysParamView.getValue('NOTE_SUMMARY_KT_DEPT') ne null and viewSumView.paramAdmin eq 'Y'}"/>	
			
			<p:messages id="messages" showDetail="false" showSummary="true" globalOnly="true" escape="false"/>
			<p:scrollTop />

				<h:form id="form_kt">
					<p:linkButton outcome="viewSum" value="#{bundle['action.back']}" icon="pi pi-arrow-left" styleClass="btn-back">
						<f:param name="admin" value="#{viewSumView.paramAdmin}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="facDept" value="#{viewKtSumView.getSelectedFacDeptString()}" rendered="#{viewSumView.paramAdmin == 'Y'}"/>
						<f:param name="startDate" value="#{viewKtSumView.getListingStartDateString()}"/>
						<f:param name="endDate" value="#{viewKtSumView.getListingEndDateString()}"/>
					</p:linkButton>	
					<p:commandButton id="exportButton" value="Export Excel File" icon="pi pi-download" style="margin-right:5px; margin-bottom:1px;"
									 ajax = "false"
									 action="#{viewKtSumView.exportSummary()}" ></p:commandButton>
					<p:panel>
						<f:facet name="header">
							<div>
								<span style="color:#1f1645;">Filtering Criteria</span>
							</div>
						</f:facet>
						<div class="ui-g">
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel for="@next" value="Faculty/ Department/ Centre" style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}" />
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="facDept" title="Faculty/ Department/ Centre" label="Faculty/ Department/ Centre" 
													multiple="true" filter="true" 
													filterMatchMode="contains"
													value="#{viewKtSumView.selectedFacDepts}" 
													style="#{viewSumView.paramAdmin == 'Y'?'vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;':'display:none;'}">
										<f:selectItems value="#{viewSumView.facDeptList}"/>
								</p:selectCheckboxMenu>	
							</div>
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Reporting Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectCheckboxMenu id="ktPeriod"  value="#{viewKtSumView.selectedKtPeriods}" 
																				 multiple="true" filter="true">
										<f:selectItems value="#{viewSumView.ktPeriodList}" var="o" 
															itemLabel="#{o.period_desc}" itemValue="#{o.period_id}" />				
								</p:selectCheckboxMenu>	
							</div>
							
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="KT Date Period" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="From: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-3 ui-lg-3">
								<p:datePicker id="riStartDate" view="date"
															title="Start Date (DD/MM/YYYY)" 
															label="Start Date (DD/MM/YYYY)" 
															value="#{viewKtSumView.selectedStartDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							<div class="ui-g-12 ui-md-1 ui-lg-1">
								<p:outputLabel value="To: " style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-5 ui-lg-5">
								<p:datePicker id="riEndDate" view="date"
															title="End Date (DD/MM/YYYY)" 
															label="End Date (DD/MM/YYYY)" 
															value="#{viewKtSumView.selectedEndDate}" 
															pattern="dd/MM/yyyy" yearNavigator="true" yearRange="2010:2050">	
									</p:datePicker>
							</div>
							<div class="ui-g-12 ui-md-2 ui-lg-2">
								<p:outputLabel value="Kt Form" style="vertical-align: -webkit-baseline-middle; margin-bottom: 0px; font-weight:700; color:#4c6f89;"/>
							</div>
							<div class="ui-g-12 ui-md-10 ui-lg-10">
								<p:selectOneMenu id="ktForm"  value="#{viewKtSumView.selectedKtForm}">
										<f:selectItems value="#{viewSumView.ktFormList}" var="d" 
												itemLabel="#{d.form_short_desc} #{d.form_full_desc}" itemValue="#{d.form_code}" />
								</p:selectOneMenu>	
								
							</div>
			            </div>
			            <p:commandButton id="filterButton" value="Apply" update="form_kt" icon="pi pi-filter"
									 widgetVar="filterButtonWV" onclick="PF('filterButtonWV').disable();PF('filterDialog').show();"
									 oncomplete="PF('filterButtonWV').enable();PF('filterDialog').hide();diableEnterBtn();"
									 actionListener="#{viewKtSumView.updateChart()}" ></p:commandButton><p:spacer width="10"/>
						<p:dialog widgetVar="filterDialog" modal="true" draggable="false" closable="false" resizable="false" showHeader="false">
					        <div>
				            	<h5>Filtering</h5>
			        			<p:progressBar id="progressBarIndeterminate" style="height:20px; width:250px;" mode="indeterminate"/>
				            </div>
					    </p:dialog>
			          </p:panel>
			          
			        <div class="card">  
					<p:panelGrid columns="2" id="board_kt" style="width: 100%" columnClasses="width-fifty-percent width-fifty-percent">
						<p:panel id="ktYrBar" header="Total No. of KT Activities">
							<p:barChart model="#{viewKtSumView.ktYrTotalBarModel}" style="width: 100%;" widgetVar="ktYrBarChart"/>
							<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_kt', 'ktYrBarChart')" style="display:none;"/>
			            </p:panel>
						<p:panel id="ktByFormYrBar" header="Total No. of #{viewKtSumView.getKtFormName()}">
							<p:barChart model="#{viewKtSumView.ktByFormYrTotalBarModel}" style="width: 100%;" widgetVar="ktByFormYrBarChart"/>
							<p:commandButton type="button" value="Export" icon="pi pi-download" onclick="exportChart('form_kt', 'ktByFormYrBarChart')" style="display:none;"/>
			            </p:panel>
		            </p:panelGrid>
		            </div>
				</h:form>
			
		</ui:composition>
</html>