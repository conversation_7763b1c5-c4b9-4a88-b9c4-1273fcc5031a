<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

	<ui:composition>		
		<div class="section">

			<div class="title">
				<p:outputPanel  styleClass="bottom-line-level1">
				 	<h2>#{formBundle['form.header.course']}</h2>
				</p:outputPanel>
			</div>

			<div class="section">
				<h:panelGrid columns="4" columnClasses="ui-grid-col-2, ui-grid-col-4, ui-grid-col-2, ui-grid-col-4" styleClass="full-width-panel infoPanel">
					<!-- row 1 -->
					<h:outputText class="dbdata-header bold" value="#{formBundle['form.header.course.semester']}" />
					<p:outputPanel  styleClass="dbdata" >
						#{selectedForm.courseHeader.academicYear} #{selectedForm.courseHeader.semester}
					</p:outputPanel>
	
					<h:outputText class="dbdata-header bold" value="#{formBundle['form.header.course.code']}" />
					<p:outputPanel  styleClass="dbdata" >
						#{selectedForm.courseHeader.subjectCode}#{selectedForm.courseHeader.courseNum}
					</p:outputPanel>
					
					<!-- row 2 -->
					<h:outputText class="dbdata-header bold" value="#{formBundle['form.header.course.title']}" />
					<p:outputPanel  styleClass="dbdata" >
						#{selectedForm.courseHeader.moduleTitle}
					</p:outputPanel>
	
					<h:outputText class="dbdata-header bold" value="#{formBundle['form.header.course.groupCode']}" />
					<p:outputPanel  styleClass="dbdata" >
						#{selectedForm.courseHeader.courseGroup}
					</p:outputPanel>					
				</h:panelGrid>
			</div>
		</div>	
		
	</ui:composition>

</html>
