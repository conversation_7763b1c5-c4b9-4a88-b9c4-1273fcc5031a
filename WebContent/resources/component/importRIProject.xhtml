<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
<cc:interface>
	<cc:attribute name="importRIProject" type="hk.eduhk.rich.view.ImportRIProject" required="true"/>
	<cc:attribute name="update" type="java.lang.String" required="true"/>
</cc:interface>
<cc:implementation>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
      	<p:overlayPanel id="projectPreviewPanel" widgetVar="projectPreviewPanelVar"
								dismissable="false"
								styleClass="supplForm-preview-panel">
					<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
						<table border="0" style="width:100%;">
							<tr>
								<td>
									#{cc.attrs.importRIProject.getProject().getTitle()}
								</td>
								<td style="text-align:right;">
									<p:commandLink oncomplete="PF('projectPreviewPanelVar').hide()">
										<i class="fas fa-times"/>
									</p:commandLink>
								</td>
							</tr>
						</table>
					</h:panelGroup>
					<div class="ui-g input-panel">
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Source ID:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().pk.source_id}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Funding Source:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().getSap_funding_source_desc()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Title of Project:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().getTitle()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Project Summary:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().getSummary()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Project Investigator:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().investigator_name}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							List of other contributor(s):
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().getOther_contributors_name()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Date From:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().from_day}/#{cc.attrs.importRIProject.getProject().from_month}/#{cc.attrs.importRIProject.getProject().from_year}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Date To:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIProject.getProject().to_day}/#{cc.attrs.importRIProject.getProject().to_month}/#{cc.attrs.importRIProject.getProject().to_year}
						</div>
					</div>
					<h:panelGroup>
						<!-- <h:outputText value="#{cc.attrs.importRIProject.getProject().apa_citation}" /> -->
						<table border="0" style="width:100%;">
							<tr>
								<td>
									<p:commandButton styleClass="default-linkButton" value="Ignore" update=":ignoreConfirmForm:ignoreProjectConfirm"
													 style="background: grey; border:grey"
													 oncomplete="PF('ignoreProjectConfirmWidget').show();"
													 actionListener="#{cc.attrs.importRIProject.setSelectedIgnoreProject()}">
									</p:commandButton>
								</td>
								<td style="text-align:right;">
									<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('projectPreviewPanelVar').hide()">
									</p:commandButton>
									<p:linkButton styleClass="default-linkButton" value="Import" outcome="/user/manageProject_edit">
										<f:param name="area_code" value="#{cc.attrs.importRIProject.getProject().pk.area_code}"/> 
										<f:param name="source_id" value="#{cc.attrs.importRIProject.getProject().pk.source_id}"/> 
			               				<f:param name="staff_number" value="#{cc.attrs.importRIProject.getProject().pk.staff_number}"/> 
										<f:param name="pid" value="#{cc.attrs.importRIProject.getProject().pid}"/>  
									</p:linkButton >
								</td>
							</tr>
						</table>
					</h:panelGroup>
								
				</p:overlayPanel>
	        	<p:dataTable id="projectDataTable"
							 value="#{cc.attrs.importRIProject.getProjectList()}" 
							 var="project"
							 styleClass="default-dataTable"
							 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
							 reflow="true"
							 paginator="true"
							 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                     rows="30"
		                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
		                 	 tableStyle="table-layout:auto;"
							 >
					
					<p:column width="10%">
						<f:facet name="header">Sources</f:facet>
						<h:outputText value="#{project.area_description}" />
					</p:column>
					
					<p:column width="10%" >
						<f:facet name="header">ID</f:facet>
						<h:outputText value="#{project.pk.source_id}" escape="false" />
					</p:column>
					
					<p:column width="60%">
						<f:facet name="header">Title</f:facet>
						<h:outputText value="#{project.getTitle()}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Actions</f:facet>
						<p:commandButton value="View" update="#{cc.attrs.update}"
										 styleClass="default-linkButton"
										 oncomplete="PF('projectPreviewPanelVar').show()"
										 actionListener="#{cc.attrs.importRIProject.setSelectedPreview(project.pk.area_code, project.pk.source_id, project.pk.staff_number)}">
						</p:commandButton>
						<p:linkButton value="Import" outcome="/user/manageProject_edit" styleClass="default-linkButton">
							<f:param name="area_code" value="#{project.pk.area_code}"/> 
							<f:param name="source_id" value="#{project.pk.source_id}"/> 
               				<f:param name="staff_number" value="#{project.pk.staff_number}"/>
               				<f:param name="pid" value="#{project.pid}"/>  
						</p:linkButton >
						<br/>
						<p:commandButton value="Ignore" update=":ignoreConfirmForm:ignoreProjectConfirm"
										 styleClass="default-linkButton"
										 style="background: grey; border:grey"
										 oncomplete="PF('ignoreProjectConfirmWidget').show();PF('projectPreviewPanelVar').hide()"
										 actionListener="#{cc.attrs.importRIProject.setSelectedIgnoreProject(project.pk.area_code, project.pk.source_id, project.pk.staff_number)}">
						</p:commandButton>
					</p:column>
					
				</p:dataTable>
</cc:implementation>

</html>
	        