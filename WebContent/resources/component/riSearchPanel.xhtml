<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >

<cc:interface>
	<cc:attribute name="searchPanel" type="hk.eduhk.rich.view.RISearchPanel" required="true"/>
	<cc:attribute name="update" type="java.lang.String"/>
	<cc:attribute name="showPersonPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showExStaffPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showRiPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="showOtherPanel" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isRdoLib" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isDeptAdmin" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isAcadStaff" type="java.lang.Boolean" default="true"/>
	<cc:attribute name="isUoaAdmin" type="java.lang.Boolean" default="true"/>
</cc:interface>
<cc:implementation>
	<p:accordionPanel multiple="true" widgetVar="multiple">
		<p:tab id="PersonPanel" title="Search by Person" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showPersonPanel and !cc.attrs.isAcadStaff}">
	
			<div class="ui-g input-panel">
	
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Staff Name
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
					<p:autoComplete id="staffName"
								 	value="#{cc.attrs.searchPanel.staffName}"
							     	completeMethod="#{cc.attrs.searchPanel.nameAutoComplete}"
							     	scrollHeight="250">
						<p:ajax event="itemSelect" process="@this" update="#{cc.attrs.update}" />
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:autoComplete>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Academic Staff Rank
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectCheckboxMenu id="rank"
										  value="#{cc.attrs.searchPanel.selectedRankList}"
										  multiple="true"
										  filter="true" 
										  filterMatchMode="contains"
										  >
						<f:selectItems value="#{cc.attrs.searchPanel.rankList}"
									   var="rk"
									   itemValue="#{rk.rank_code}"
									   itemLabel="#{rk.rank_full}"
									   />
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
					</p:selectCheckboxMenu>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Academic Staff
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="acadStaff"
									 value="#{cc.attrs.searchPanel.acadStaff}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.allYesNoEmptyList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Department(s)
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectCheckboxMenu id="department"
										  value="#{cc.attrs.searchPanel.selectedDepartmentList}"
										  multiple="true"
										  filter="true" 
										  filterMatchMode="contains"
										  >
						<f:selectItems value="#{cc.attrs.searchPanel.departmentList}"
									   var="dept"
									   itemValue="#{dept.departmentCode}"
									   itemLabel="#{dept.departmentName} (#{dept.departmentCode})"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
					</p:selectCheckboxMenu>
				</div>
			</div>
		</p:tab>
		
		
		<p:tab id="exStaffPanel" title="Search by ex-EduHK staff" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showExStaffPanel and !cc.attrs.isAcadStaff }">
	
			<div class="ui-g input-panel">
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Former Staff Name
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
					<p:autoComplete id="exStaffName"
								 	value="#{cc.attrs.searchPanel.exStaffName}"
							     	completeMethod="#{cc.attrs.searchPanel.exNameAutoComplete}"
							     	scrollHeight="250">
						<p:ajax event="itemSelect" process="@this" update="#{cc.attrs.update}" />
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}" 
								/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:autoComplete>
				</div>
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Former Staff Number
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:inputText id="formStaffNum"
									 value="#{cc.attrs.searchPanel.formStaffNum}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
			</div>
		</p:tab>
		
		
		<p:tab id="riPanel" title="Search by RI" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showRiPanel and !cc.attrs.isAcadStaff}">
	
			<h:panelGroup id="riInnerPanel" class="ui-g input-panel">
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Type of RI
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="riType" widgetVar="riTypeWV"
									 value="#{cc.attrs.searchPanel.riType}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.riTypeList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update} otherPanel nonRAESorting riInnerPanel @parent"/>
					</p:selectOneMenu>

				</div>
				
	
				<h:panelGroup rendered="#{cc.attrs.isRdoLib or cc.attrs.isDeptAdmin and !cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						RI No.
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<p:inputText id="riNo"
									 value="#{cc.attrs.searchPanel.riNo}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
				
				<h:panelGroup style="width:100%"
							  rendered="#{cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeOutput()}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Output Type
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectCheckboxMenu id="outputType"
											  value="#{cc.attrs.searchPanel.selectedOutputTypeList}"
											  multiple="true"
											  filter="true" 
											  filterMatchMode="contains"
											  >
							<f:selectItems value="#{cc.attrs.searchPanel.outputTypeList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
						</p:selectCheckboxMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true and !cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output'}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Type of View
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="viewType"
										 value="#{cc.attrs.searchPanel.viewType}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.viewTypeList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update} @parent"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup style="width:100%"
							  rendered="#{cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeProject() and cc.attrs.isRdoLib == true and !cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Keyword
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<p:inputText id="keyword"
									 value="#{cc.attrs.searchPanel.keyword}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
				
				<h:panelGroup style="width:100%" rendered="#{!cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output' }">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Smart Search
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<p:inputText id="smartSearch"
									 value="#{cc.attrs.searchPanel.smartSearch}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
				
				<h:panelGroup style="width:100%"
							  rendered="#{cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeOutput() and cc.attrs.isRdoLib == true}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Journal Name
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
						<p:inputText id="journalName"
									 value="#{cc.attrs.searchPanel.journalName}">
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:inputText>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true and !cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output'}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						CDCF Status
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="cdcfStatus"
										 value="#{cc.attrs.searchPanel.cdcfStatus}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.cdcfStatusList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Institute Declared Indicator
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="insDecIndicator"
										 value="#{cc.attrs.searchPanel.insDecIndicator}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.allYesNoEmptyList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				<h:panelGroup rendered="#{cc.attrs.searchPanel.riType eq 'RAE Output'}" >
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}" >
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						RAE Unit of Assessment (UoA)
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectCheckboxMenu id="raeUOA"
										 	  value="#{cc.attrs.searchPanel.selectedUOAList}"
											  multiple="true"
											  filter="true" 
											  filterMatchMode="contains" >
							<f:selectItems value="#{cc.attrs.searchPanel.uoaList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectCheckboxMenu>
					</div>

				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Coordination Faculty
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeFAC"
										 value="#{cc.attrs.searchPanel.raeFac}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeFacList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Research Output Type (RAE)
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeOutputType"
										 value="#{cc.attrs.searchPanel.raeOutputType}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeOutputTypeList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						RAE Selection Type
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeSelType"
										 value="#{cc.attrs.searchPanel.raeSelType}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeSelTypeList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Information Completed
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeInfoCom"
										 value="#{cc.attrs.searchPanel.raeInfoCom}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeInfoComList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Status - Research Output Info.
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeStatus"
										 value="#{cc.attrs.searchPanel.raeStatus}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeStatusList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Status - Info. for Specific Panels
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeStatusSpec"
										 value="#{cc.attrs.searchPanel.raeStatusSpec}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeStatusSpecList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Status - Full Version
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeStatusFull"
										 value="#{cc.attrs.searchPanel.raeStatusFull}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeStatusFullList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Status - Other Information
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeStatusOther"
										 value="#{cc.attrs.searchPanel.raeStatusOther}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeStatusOtherList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Status - Ineligible
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeStatusInet"
										 value="#{cc.attrs.searchPanel.raeStatusInel}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeStatusInetList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Citation Checking
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeCitation"
										 value="#{cc.attrs.searchPanel.raeCitation}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeCitationList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Copyright Clearance
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="raeCopyright"
										 value="#{cc.attrs.searchPanel.raeCopyright}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.raeCopyrightList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{!cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output'}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Consent Indicator
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="consentIndicator"
										 value="#{cc.attrs.searchPanel.consentIndicator}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.consentIndicatorList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true and !cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output'}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Change after CDCF Processed
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="chgAfterProcess"
										 value="#{cc.attrs.searchPanel.chgAfterProcess}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.allYesNoEmptyList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					RI Date Period
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					From
					<p:calendar id="riDateFrom" value="#{cc.attrs.searchPanel.riDateFrom}"
								pattern="dd/MM/yyyy" 
								mask="true"
								timeInput="true"
								showOn="button"
								locale="en"
								>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:calendar>
					/ To
					<p:calendar id="riDateTo" value="#{cc.attrs.searchPanel.riDateTo}" 
								pattern="dd/MM/yyyy" 
								mask="true"
								timeInput="true"
								showOn="button"
								locale="en"
								>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
						<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
					</p:calendar>
				</div>		
				
				<h:panelGroup rendered="#{cc.attrs.isRdoLib == true and !cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output' }">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						RI First Submit Date
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						From
						<p:calendar id="riFirstPublishFrom" value="#{cc.attrs.searchPanel.riFirstPublishFrom}"
									pattern="dd/MM/yyyy" 
									mask="true"
									timeInput="true"
									showOn="button"
									locale="en"
									>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:calendar>
						/ To
						<p:calendar id="riFirstPublishTo" value="#{cc.attrs.searchPanel.riFirstPublishTo}" 
									pattern="dd/MM/yyyy" 
									mask="true"
									timeInput="true"
									showOn="button"
									locale="en"
									>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
						</p:calendar>
					</div>
					
					<h:panelGroup style="width:100%" rendered="#{!cc.attrs.isAcadStaff and cc.attrs.searchPanel.riType != 'RAE Output' }">
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							RI Last Submit Date
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content">
							From
							<p:calendar id="riLastPublishFrom" value="#{cc.attrs.searchPanel.riLastPublishFrom}"
										pattern="dd/MM/yyyy" 
										mask="true"
										timeInput="true"
										showOn="button"
										locale="en"
										>
								<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
								<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
								<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
							</p:calendar>
							/ To
							<p:calendar id="riLastPublishTo" value="#{cc.attrs.searchPanel.riLastPublishTo}" 
										pattern="dd/MM/yyyy" 
										mask="true"
										timeInput="true"
										showOn="button"
										locale="en"
										>
								<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
								<p:ajax event="dateSelect" process="@this" update="#{cc.attrs.update}"/>
								<p:ajax event="keypress" process="@this" update="#{cc.attrs.update}"/>
							</p:calendar>
						</div>	
					</h:panelGroup>
				</h:panelGroup>
				
				<h:panelGroup style="width:100%"
							  rendered="#{cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeProject()}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Funding Source
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectCheckboxMenu id="fundingSrc"
											  value="#{cc.attrs.searchPanel.selectedFundingSrcList}"
											  multiple="true"
											  filter="true" 
											  filterMatchMode="contains"
											  >
							<f:selectItems value="#{cc.attrs.searchPanel.fundingSrcList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
						</p:selectCheckboxMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup style="width:100%"
							  rendered="#{cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeOutput() and !cc.attrs.isAcadStaff}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						Enh. T&amp;L in High Edu.
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectOneMenu id="enhTLInHighEdu"
										 value="#{cc.attrs.searchPanel.enhTLInHighEdu}"
										 style="width:auto; max-width:100%;"
										 >
							<f:selectItems value="#{cc.attrs.searchPanel.allYesNoEmptyList}"/>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
						</p:selectOneMenu>
					</div>
				</h:panelGroup>
				
				<h:panelGroup rendered="#{cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeOutput() or 
					cc.attrs.searchPanel.riType == cc.attrs.searchPanel.getRiTypeProject()}">
					<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
						SDG
					</div>
					<div class="ui-g-12 ui-md-10 ui-lg-10 content">
						<p:selectCheckboxMenu  id="sdg_info" title="SDGs Information" 
								label="-- Please select --" 
								filter = "true" filterMatchMode= "startsWith" filterNormalize = "true" multiple = "true"
								style="min-width: 15rem"
								var="c"
								value="#{cc.attrs.searchPanel.selectedSDGList}">
							<f:selectItems value="#{cc.attrs.searchPanel.sdgList}" var ="sdg" itemLabel="#{sdg.pk.lookup_code} - #{sdg.description}" 
								itemValue="#{sdg.pk.lookup_code}" >
	
							</f:selectItems>
							<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
							<p:ajax event="toggleSelect" process="@this" update="#{cc.attrs.update}"/>
						</p:selectCheckboxMenu>
					</div>
				</h:panelGroup>
				
				
				
			</h:panelGroup>
		</p:tab>	

		
		<p:tab id="otherPanel" name="otherPanel" title="Sorting" titleStyle="background:#f6f7f9"
				 rendered="#{cc.attrs.showOtherPanel and !cc.attrs.isAcadStaff}" >

				  <h:panelGroup id="nonRAESorting" style="width:100%" rendered="#{cc.attrs.searchPanel.riType != 'RAE Output' }"  >
			<div class="ui-g input-panel">
			
			
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Listing Type
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="listingType"
									 value="#{cc.attrs.searchPanel.listingType}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.listingTypeList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>
				
				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Sort by Column
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="sortCol"
									 value="#{cc.attrs.searchPanel.sortCol}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.sortColList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>

				<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
					Sort Order
				</div>
				<div class="ui-g-12 ui-md-10 ui-lg-10 content">
					<p:selectOneMenu id="sortOrder"
									 value="#{cc.attrs.searchPanel.sortOrder}"
									 style="width:auto; max-width:100%;"
									 >
						<f:selectItems value="#{cc.attrs.searchPanel.sortOrderList}"/>
						<p:ajax event="change" process="@this" update="#{cc.attrs.update}"/>
					</p:selectOneMenu>
				</div>
				
			</div>
				</h:panelGroup>
		</p:tab>
	
	</p:accordionPanel>
	
	
</cc:implementation>

</html>