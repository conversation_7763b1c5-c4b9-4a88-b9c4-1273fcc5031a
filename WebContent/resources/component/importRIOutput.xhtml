<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
<cc:interface>
	<cc:attribute name="importRIOutput" type="hk.eduhk.rich.view.ImportRIOutput" required="true"/>
	<cc:attribute name="update" type="java.lang.String" required="true"/>
</cc:interface>
<cc:implementation>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
      	<p:overlayPanel id="outputPreviewPanel" widgetVar="outputPreviewPanelVar"
								dismissable="false"
								styleClass="supplForm-preview-panel">
					<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
						<!-- <h:outputText value="#{cc.attrs.importRIOutput.getOutput().apa_citation}" /> -->
						<table border="0" style="width:100%;">
							<tr>
								<td>
									#{cc.attrs.importRIOutput.getOutput().apa_citation}
								</td>
								<td style="text-align:right;">
									<p:commandLink oncomplete="PF('outputPreviewPanelVar').hide()">
										<i class="fas fa-times"/>
									</p:commandLink>
								</td>
							</tr>
						</table>
					</h:panelGroup>
					<div class="ui-g input-panel">
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Source ID:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().pk.source_id}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Display Language:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().language}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Output Category:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().getOutput_category_desc()}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Title of Research Output:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().title_jour_book}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Name of Publication/Conference/Journal in which the output appears:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().title_paper_art}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Publisher/Conference Organiser(s)/Others:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().publisher}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							List of author(s)/contributor(s):
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().authorship_name}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Volume (Issue):
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().vol_issue}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Page No.:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().page_num}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Date:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIOutput.getOutput().from_month}/#{cc.attrs.importRIOutput.getOutput().from_year}
						</div>
					</div>
					<h:panelGroup>
						<!-- <h:outputText value="#{cc.attrs.importRIOutput.getOutput().apa_citation}" /> -->
						<table border="0" style="width:100%;">
							<tr>
								<td>
									<p:commandButton styleClass="default-linkButton" value="Ignore" update=":ignoreConfirmForm:ignoreOutputConfirm"
													 style="background: grey; border:grey"
													 oncomplete="PF('ignoreOutputConfirmWidget').show();"
													 actionListener="#{cc.attrs.importRIOutput.setSelectedIgnoreOutput()}">
									</p:commandButton>
								</td>
								<td style="text-align:right;">
									<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('outputPreviewPanelVar').hide()">
									</p:commandButton>
									<p:linkButton styleClass="default-linkButton" value="Import" outcome="/user/manageOutput_edit">
										<f:param name="area_code" value="#{cc.attrs.importRIOutput.getOutput().pk.area_code}"/> 
										<f:param name="source_id" value="#{cc.attrs.importRIOutput.getOutput().pk.source_id}"/> 
			               				<f:param name="staff_number" value="#{cc.attrs.importRIOutput.getOutput().pk.staff_number}"/>
			               				<f:param name="pid" value="#{cc.attrs.importRIOutput.getOutput().pid}"/>  
									</p:linkButton >
								</td>
							</tr>
						</table>
					</h:panelGroup>
								
				</p:overlayPanel>
	        	<p:dataTable id="outputDataTable"
							 value="#{cc.attrs.importRIOutput.getOutputList()}" 
							 var="output"
							 styleClass="default-dataTable"
							 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
							 reflow="true"
							 paginator="true"
							 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                     rows="30"
		                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
		                 	 tableStyle="table-layout:auto;"
							 >
					
					<p:column width="10%">
						<f:facet name="header">Sources</f:facet>
						<h:outputText value="#{output.area_description}" />
					</p:column>
					
					<p:column width="10%" >
						<f:facet name="header">ID</f:facet>
						<h:outputText value="#{output.pk.source_id}" escape="false" />
					</p:column>
					
					<p:column width="60%">
						<f:facet name="header">Summary</f:facet>
						<h:outputText value="#{output.apa_citation}" />
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Actions</f:facet>
						<p:commandButton value="View" update="#{cc.attrs.update}"
										 styleClass="default-linkButton"
										 oncomplete="PF('outputPreviewPanelVar').show()"
										 actionListener="#{cc.attrs.importRIOutput.setSelectedPreview(output.pk.area_code, output.pk.source_id, output.pk.staff_number)}">
						</p:commandButton>
						<p:linkButton styleClass="default-linkButton" value="Import" outcome="/user/manageOutput_edit">
							<f:param name="area_code" value="#{output.pk.area_code}"/> 
							<f:param name="source_id" value="#{output.pk.source_id}"/> 
               				<f:param name="staff_number" value="#{output.pk.staff_number}"/> 
               				<f:param name="pid" value="#{output.pid}"/> 
						</p:linkButton >
						<br/>
						<p:commandButton value="Ignore" update=":ignoreConfirmForm:ignoreOutputConfirm"
										 styleClass="default-linkButton"
										 style="background: grey; border:grey"
										 oncomplete="PF('ignoreOutputConfirmWidget').show();PF('outputPreviewPanelVar').hide()"
										 actionListener="#{cc.attrs.importRIOutput.setSelectedIgnoreOutput(output.pk.area_code, output.pk.source_id, output.pk.staff_number)}">
						</p:commandButton>
					</p:column>
					
				</p:dataTable>
</cc:implementation>

</html>
	        