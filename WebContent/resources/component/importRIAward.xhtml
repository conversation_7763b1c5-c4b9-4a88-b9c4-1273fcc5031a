<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:cc="http://java.sun.com/jsf/composite" 
	  xmlns:f="http://java.sun.com/jsf/core" 
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  >
<cc:interface>
	<cc:attribute name="importRIAward" type="hk.eduhk.rich.view.ImportRIAward" required="true"/>
	<cc:attribute name="update" type="java.lang.String" required="true"/>
</cc:interface>
<cc:implementation>
	<o:importConstants type="hk.eduhk.rich.Constant" var="const"/>
      	<p:overlayPanel id="awardPreviewPanel" widgetVar="awardPreviewPanelVar"
								dismissable="false"
								styleClass="supplForm-preview-panel">
					<h:panelGroup layout="block" style="padding-bottom:0.5em; font-weight:bold; font-size:1.2em;">
						<table border="0" style="width:100%;">
							<tr>
								<td>
									<h:outputText value="#{cc.attrs.importRIAward.getAward().award_name}" style="white-space: pre-wrap"/>
									
								</td>
								<td style="text-align:right;">
									<p:commandLink oncomplete="PF('awardPreviewPanelVar').hide()">
										<i class="fas fa-times"/>
									</p:commandLink>
								</td>
							</tr>
						</table>
					</h:panelGroup>
					<div class="ui-g input-panel">
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Source ID:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIAward.getAward().pk.source_id}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Award Recipient:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIAward.getAward().recipient_name}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Detail Description of Award:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIAward.getAward().full_desc}
						</div>
						
						<div class="ui-g-12 ui-md-2 ui-lg-2 caption">
							Date of Receipt:
						</div>
						<div class="ui-g-12 ui-md-10 ui-lg-10 content ui-fluid">
							#{cc.attrs.importRIAward.getAward().award_day}/#{cc.attrs.importRIAward.getAward().award_month}/#{cc.attrs.importRIAward.getAward().award_year}
						</div>
					</div>
					<h:panelGroup>
						<!-- <h:outputText value="#{cc.attrs.importRIAward.getAward().apa_citation}" /> -->
						<table border="0" style="width:100%;">
							<tr>
								<td>
									<p:commandButton styleClass="default-linkButton" value="Ignore" update=":ignoreConfirmForm:ignoreAwardConfirm"
													 style="background: grey; border:grey"
													 oncomplete="PF('ignoreAwardConfirmWidget').show();"
													 actionListener="#{cc.attrs.importRIAward.setSelectedIgnoreAward()}">
									</p:commandButton>
								</td>
								<td style="text-align:right;">
									<p:commandButton styleClass="default-linkButton" value="Close" oncomplete="PF('awardPreviewPanelVar').hide()">
									</p:commandButton>
									<p:linkButton styleClass="default-linkButton" value="Import" outcome="/user/manageAward_edit">
										<f:param name="area_code" value="#{cc.attrs.importRIAward.getAward().pk.area_code}"/> 
										<f:param name="source_id" value="#{cc.attrs.importRIAward.getAward().pk.source_id}"/> 
			               				<f:param name="staff_number" value="#{cc.attrs.importRIAward.getAward().pk.staff_number}"/> 
			               				<f:param name="pid" value="#{cc.attrs.importRIAward.getAward().pid}"/> 
									</p:linkButton >
								</td>
							</tr>
						</table>
					</h:panelGroup>
								
				</p:overlayPanel>
	        	<p:dataTable id="awardDataTable"
							 value="#{cc.attrs.importRIAward.getAwardList()}" 
							 var="award"
							 styleClass="default-dataTable"
							 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
							 reflow="true"
							 paginator="true"
							 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
		                     paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
		                     rows="30"
		                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE_100}"
		                 	 tableStyle="table-layout:auto;"
							 >
					
					<p:column width="10%">
						<f:facet name="header">Sources</f:facet>
						<h:outputText value="#{award.area_description}" />
					</p:column>
					
					<p:column width="10%" >
						<f:facet name="header">ID</f:facet>
						<h:outputText value="#{award.pk.source_id}" escape="false" />
					</p:column>
					
					<p:column width="60%">
						<f:facet name="header">Award Name</f:facet>
						<h:outputText value="#{award.award_name}" style="white-space: pre-wrap"/>
					</p:column>
					
					<p:column width="3em;">
						<f:facet name="header">Actions</f:facet>
						<p:commandButton value="View" update="#{cc.attrs.update}"
										 styleClass="default-linkButton"
										 oncomplete="PF('awardPreviewPanelVar').show()"
										 actionListener="#{cc.attrs.importRIAward.setSelectedPreview(award.pk.area_code, award.pk.source_id, award.pk.staff_number)}">
						</p:commandButton>
						<p:linkButton value="Import" outcome="/user/manageAward_edit" styleClass="default-linkButton">
							<f:param name="area_code" value="#{award.pk.area_code}"/> 
							<f:param name="source_id" value="#{award.pk.source_id}"/> 
               				<f:param name="staff_number" value="#{award.pk.staff_number}"/> 
               				<f:param name="pid" value="#{award.pid}"/> 
						</p:linkButton >
						<br/>
						<p:commandButton value="Ignore" update=":ignoreConfirmForm:ignoreAwardConfirm"
										 styleClass="default-linkButton"
										 style="background: grey; border:grey"
										 oncomplete="PF('ignoreAwardConfirmWidget').show();PF('awardPreviewPanelVar').hide()"
										 actionListener="#{cc.attrs.importRIAward.setSelectedIgnoreAward(award.pk.area_code, award.pk.source_id, award.pk.staff_number)}">
						</p:commandButton>
					</p:column>
					
				</p:dataTable>
</cc:implementation>

</html>
	        