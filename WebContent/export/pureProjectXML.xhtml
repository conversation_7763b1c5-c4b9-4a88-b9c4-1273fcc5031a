<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:component="http://java.sun.com/jsf/composite/component" 
	  xmlns:c="http://java.sun.com/jsp/jstl/core"
      xmlns:f="http://java.sun.com/jsf/core"      
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://java.sun.com/jsf/facelets"> 
    <h:head>
        <title>#{sysParamView.getValue('SYS_TITLE')}</title>
        <link rel="shortcut icon" type="image/x-icon" href="#{resource['image/favicon.ico']}" />
    </h:head>
    <h:body>
        <pre lang="xml" style="white-space: pre-wrap; display: block; font-family: Consolas, Courier, monospace;">
        	<h:outputText value="#{genProjectXmlView.createProjectXmlFile()}" />
    	</pre>
    </h:body>
</html>